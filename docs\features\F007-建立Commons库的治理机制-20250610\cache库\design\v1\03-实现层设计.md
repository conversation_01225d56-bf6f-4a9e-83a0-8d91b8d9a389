# F007 Cache库-实现层设计

## 文档元数据

- **文档ID**: `F007-CACHE-IMPLEMENTATION-DESIGN-003`
- **版本**: `V1.0`
- **模块**: `commons-cache-caffeine`, `commons-cache-valkey`
- **技术栈**: Java 21, Caffeine 3.1.8, Valkey 8.0, Lettuce 6.3.2, Maven 3.9
- **构建工具**: Maven 3.9.6
- **数据库技术栈**: N/A (纯缓存实现，无持久化)
复杂度等级: L2

## 核心定位

实现层是缓存库的具体技术实现中心，负责将抽象接口转化为Caffeine本地缓存和Valkey远程缓存的具体操作。它封装了底层缓存引擎的复杂性，提供高性能、异步的缓存访问能力。

## 设计哲学

本模块遵循以下设计哲学：
1. **性能至上**: 充分利用现代缓存引擎的高级特性，追求极致性能(响应时间<10ms)
2. **异步优先**: 全面拥抱异步I/O和虚拟线程，避免阻塞操作，确保并发处理能力≥10K TPS
3. **特性完整**: 不仅支持基础CRUD，还暴露高级数据结构操作，完整性≥95%
4. **故障隔离**: 本地缓存和远程缓存各自独立，互不影响，容错率≥99.9%

## 包含范围

**核心功能模块**：
- Caffeine本地缓存模板实现
- Valkey远程缓存模板实现
- 高级数据结构操作封装
- 异步I/O和管道化支持

## 排除范围

**不包含功能**：
- 缓存一致性协调逻辑
- AOP切面处理
- 监控指标收集
- Spring Boot自动配置

## 1. 分层架构设计

### 1.1 架构层次结构

```
┌─────────────────────────────────────────────────────────┐
│                    应用业务层                            │
├─────────────────────────────────────────────────────────┤
│                  抽象接口层 (API)                        │  
│  LocalCacheTemplate | ValkeyCacheTemplate              │
├─────────────────────────────────────────────────────────┤
│                   实现层 (本层)                          │
│  CaffeineCacheTemplate | ValkeyCacheTemplate           │
├─────────────────────────────────────────────────────────┤
│                  底层引擎层                              │
│      Caffeine 3.1.8     |     Valkey 8.0              │
└─────────────────────────────────────────────────────────┘
```

### 1.2 模块依赖关系

**核心依赖图**:
- `commons-cache-caffeine` → `caffeine:3.1.8` + `commons-cache-api`
- `commons-cache-valkey` → `lettuce-core:6.3.2` + `commons-cache-api`
- **边界定义**: 实现层严格依赖抽象接口，不直接依赖业务层
- **职责分离**: 本地缓存实现与远程缓存实现完全解耦

### 1.3 接口契约定义

**LocalCacheTemplate契约**:
```java
// 核心接口契约 - 同步操作，性能优先
public interface LocalCacheTemplate<K, V> {
    Optional<V> get(K key);           // 响应时间≤1ms
    void put(K key, V value);         // 响应时间≤1ms
    void evict(K key);               // 响应时间≤1ms
    CacheStats getStats();           // 提供命中率等统计信息
}
```

**ValkeyCacheTemplate契约**:
```java
// 核心接口契约 - 异步操作，可靠性优先
public interface ValkeyCacheTemplate {
    CompletableFuture<Optional<Object>> getAsync(String key);    // 响应时间≤10ms
    CompletableFuture<Void> putAsync(String key, Object value);  // 响应时间≤10ms
    CompletableFuture<Void> evictAsync(String key);             // 响应时间≤10ms
    HyperLogLogCommands hyperLogLog();                          // 高级数据结构
    JsonCommands json();                                        // JSON操作支持
}
```

## 2. 本地缓存实现 (Caffeine)

### 2.1 核心组件: `CaffeineCacheTemplate`

**架构职责**: 
- 实现`LocalCacheTemplate`接口，提供Facade模式的统一接口
- 封装`com.github.ben-manes.caffeine.cache.Cache`的复杂性
- 提供线程安全的同步操作，确保高并发下的数据一致性

**关键实现**: 
```java
// 生产级实现设计
@Component
public class CaffeineCacheTemplate<K, V> implements LocalCacheTemplate<K, V> {
    private final Cache<K, V> caffeineCache;
    private final CacheMetrics metrics;

    public CaffeineCacheTemplate(CacheProperties.L1 l1Config) {
        this.caffeineCache = Caffeine.newBuilder()
            .maximumSize(l1Config.getMaxSize())                    // 容量控制
            .expireAfterWrite(l1Config.getExpireAfterWrite())      // TTL管理
            .expireAfterAccess(l1Config.getExpireAfterAccess())    // LRU策略
            .recordStats()                                         // 统计启用
            .evictionListener(this::handleEviction)                // 驱逐监听
            .build();
        this.metrics = new CacheMetrics("caffeine");
    }

    @Override
    public Optional<V> get(K key) {
        requireNonNull(key, "缓存键不能为null");
        try {
            V value = caffeineCache.getIfPresent(key);
            metrics.recordGet(value != null);
            return Optional.ofNullable(value);
        } catch (Exception e) {
            metrics.recordError("get", e);
            throw SystemException.internalError("XCE_SYS_800", "本地缓存获取失败: " + e.getMessage(), e);
        }
    }

    private void handleEviction(K key, V value, RemovalCause cause) {
        metrics.recordEviction(cause.name());
        log.debug("缓存项被驱逐: key={}, cause={}", key, cause);
    }
}
```

### 2.2 错误处理机制

**容错策略**:
- **缓存穿透防护**: 使用`CacheLoader`实现自动回源，避免缓存击穿
- **异常隔离**: 缓存操作异常不影响业务逻辑，降级为直接调用
- **资源控制**: 自动驱逐策略防止内存溢出，保护JVM稳定性

## 3. 远程缓存实现 (Valkey)

### 3.1 核心组件: `ValkeyCacheTemplate`

**架构职责**: 
- 实现`ValkeyCacheTemplate`接口，提供异步Valkey操作能力
- 封装`io.lettuce.core.api.StatefulRedisConnection`连接管理复杂性
- 提供虚拟线程友好的异步I/O操作

**客户端选择**: Lettuce 6.3.2
- **技术决策**: 相比Jedis，Lettuce提供更优秀的异步支持和虚拟线程兼容性
- **连接池**: 利用Lettuce的连接池管理，支持集群和哨兵模式
- **性能优势**: 非阻塞I/O架构，单连接支持并发命令执行

### 3.2 连接管理与虚拟线程

**连接架构**:
```java
@Component
public class ValkeyCacheTemplate implements ValkeyCacheTemplate {
    private final StatefulRedisConnection<String, Object> connection;
    private final RedisAsyncCommands<String, Object> asyncCommands;
    private final ConnectionMetrics connectionMetrics;

    public ValkeyCacheTemplate(ValkeyProperties config) {
        // 连接配置 - 生产级参数
        RedisURI redisUri = RedisURI.builder()
            .withHost(config.getHost())
            .withPort(config.getPort())
            .withPassword(config.getPassword())
            .withDatabase(config.getDatabase())
            .withTimeout(Duration.ofSeconds(config.getTimeoutSeconds())) // 5s超时
            .build();

        RedisClient client = RedisClient.create(redisUri);
        
        // 连接选项 - 性能优化
        ClientOptions clientOptions = ClientOptions.builder()
            .autoReconnect(true)                    // 自动重连
            .disconnectedBehavior(DisconnectedBehavior.REJECT_COMMANDS)  // 断连时拒绝命令
            .pingBeforeActivateConnection(true)     // 连接前ping测试
            .build();
        client.setOptions(clientOptions);

        this.connection = client.connect(new SerializedObjectCodec()); 
        this.asyncCommands = connection.async();
        this.connectionMetrics = new ConnectionMetrics();
    }

    @Override
    public CompletableFuture<Optional<Object>> getAsync(String key) {
        requireNonNull(key, "缓存键不能为null");
        
        return asyncCommands.get(key)
            .toCompletableFuture()
            .thenApply(Optional::ofNullable)
            .whenComplete((result, throwable) -> {
                if (throwable != null) {
                    connectionMetrics.recordError("get", throwable);
                    log.error("远程缓存获取失败: key={}", key, throwable);
                } else {
                    connectionMetrics.recordGet(result.isPresent());
                }
            })
            .exceptionally(throwable -> {
                // 优雅降级 - 返回空值而不是抛异常
                return Optional.empty();
            });
    }
}
```

### 3.3 高级特性实现

**Pipelining管道化**:
```java
@Override
public CompletableFuture<Void> executePipelined(Consumer<RedisBatch> batchConsumer) {
    RedisBatch batch = new RedisBatch(asyncCommands);
    batch.setAutoFlushCommands(false);  // 禁用自动刷新
    
    try {
        batchConsumer.accept(batch);    // 执行批量命令
        batch.flushCommands();          // 手动刷新，单次网络往返
        return batch.getCompletionFuture();
    } catch (Exception e) {
        connectionMetrics.recordError("pipeline", e);
        return CompletableFuture.failedFuture(new CacheException("管道执行失败", e));
    }
}
```

**数据结构操作**:
```java
@Override
public HyperLogLogCommands hyperLogLog() {
    return new HyperLogLogCommandsImpl(asyncCommands.getStatefulConnection());
}

@Override
public JsonCommands json() {
    return new JsonCommandsImpl(asyncCommands.getStatefulConnection());
}
```

## 4. 数据流设计

### 4.1 读操作数据流

```
用户请求 → CaffeineCacheTemplate.get() 
    ↓
检查本地缓存 → [命中] 返回结果
    ↓ [未命中]
ValkeyCacheTemplate.getAsync() → 异步查询远程
    ↓
CompletableFuture处理 → 结果回调 → 更新本地缓存
    ↓
返回最终结果
```

### 4.2 写操作数据流

```
写入请求 → ValkeyCacheTemplate.putAsync() 
    ↓
异步写入远程缓存 → [成功] 
    ↓
CaffeineCacheTemplate.put() → 更新本地缓存
    ↓ 
CompletableFuture完成信号
```

## 5. 边界护栏机制

### 5.1 复杂度控制边界
- **单个缓存值大小**: ≤1MB，防止网络传输阻塞
- **批量操作数量**: ≤100条命令，控制管道处理复杂度
- **并发连接数**: ≤50个，避免连接池资源耗尽
- **超时控制**: 所有异步操作≤5s，避免无限等待

### 5.2 架构演进策略
- **版本兼容**: 保持接口向后兼容性，新版本不破坏现有功能
- **渐进式升级**: 支持Caffeine和Valkey的版本升级路径
- **降级策略**: 提供缓存不可用时的业务降级机制
- **监控埋点**: 关键操作埋点，支持架构演进的数据驱动决策

## 实施约束

### 强制性技术要求
- **Java版本**: 必须使用Java 21+，确保虚拟线程特性可用
- **Caffeine版本**: 必须使用Caffeine 3.1.8，确保性能优化和API稳定性
- **Valkey版本**: 必须使用Valkey 8.0+，确保客户端缓存等高级特性可用
- **Lettuce版本**: 必须使用Lettuce 6.3.2，确保异步I/O和虚拟线程的完美集成
- **Maven版本**: 必须使用Maven 3.9.6+，确保Java 21编译支持

### 性能指标要求
- **本地缓存响应时间**: Caffeine操作≤1ms (P95)
- **远程缓存响应时间**: Valkey操作≤10ms（P95）
- **连接管理效率**: 连接池效率≥95%，连接复用率≥90%
- **内存使用效率**: 实现层开销≤总内存的2%
- **并发处理能力**: 支持≥10K TPS的并发请求

### 兼容性要求
- **线程安全**: 所有实现必须是线程安全的，支持高并发访问
- **异步兼容**: 与虚拟线程和CompletableFuture完全兼容
- **序列化兼容**: 支持标准Java序列化和自定义序列化器

### 错误处理约束
- **异常隔离**: 缓存异常不能影响业务逻辑正常执行
- **降级策略**: 远程缓存不可用时自动降级为本地缓存
- **重试机制**: 网络异常时实现指数退避重试，最大重试3次
- **监控告警**: 错误率≥1%时触发监控告警

### 约束违规后果
- **版本不兼容**: 运行时ClassNotFoundException，功能完全不可用
- **性能不达标**: 触发监控告警，P95响应时间劣化≥50%
- **线程安全问题**: 数据不一致，缓存击穿，系统稳定性风险
- **内存泄漏**: OOM异常，应用宕机风险

### 验证锚点
- **性能基准测试**: `mvn test -Dtest=CachePerformanceTest`
- **并发安全测试**: `mvn test -Dtest=CacheConcurrencyTest`  
- **集成兼容性测试**: `mvn test -Dtest=CacheImplementationIntegrationTest`
- **错误处理测试**: `mvn test -Dtest=CacheErrorHandlingTest`
- **内存泄漏检测**: `mvn test -Dtest=CacheMemoryLeakTest`
