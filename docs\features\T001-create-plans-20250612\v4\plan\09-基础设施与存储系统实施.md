# V4第一阶段实施计划：基础设施与存储系统实施（SQLite全景模型增强版）

## 📋 文档概述

**文档ID**: V4-PHASE1-IMPLEMENTATION-009-SQLite-Panoramic-Model-Enhanced
**创建日期**: 2025-06-15
**版本**: V4.0-SQLite-Panoramic-Model-Enhanced-Infrastructure-Storage
**目标**: 实现SQLite全景模型数据库系统，支撑V4核心算法运行和100%全景分析能力

## 🎯 SQLite全景模型数据库核心目标

### 核心作用1：保证全景质量
- **全景模型质量保障**: 通过SQLite数据库持久化存储，确保全景分析结果的准确性和一致性
- **置信度质量跟踪**: 记录和监控全景分析的置信度变化，确保达到95%质量标准
- **三重验证质量控制**: 存储V4算法、Python AI逻辑链、IDE AI模板的验证结果，保证质量收敛
- **全景模型可靠性确认**: 用户确认机制确保全景模型的真实性和可用性

### 核心作用2：通知用户管理架构代码版本
- **版本滞后主动检测**: 智能检测文档版本与代码版本的不一致，主动通知用户更新
- **架构负债预警系统**: 检测架构负债积累，提醒用户及时重构和优化
- **版本一致性监控**: 持续监控设计文档与实施代码的版本同步状态
- **架构演进指导**: 基于历史数据分析，为用户提供架构演进建议和最佳实践

### SQLite全景模型数据库技术能力
- **智能扫描引擎**: 快速扫描、增量扫描、全量重建的智能决策（性能优化）
- **版本+哈希检测**: 双重检测机制，智能变更识别（准确性保障）
- **数据持久化机制**: 加密存储、性能优化、数据迁移（可靠性保障）

## 🏗️ SQLite全景模型数据库架构设计

### 核心架构组件（基于17-SQLite全景模型数据库设计.md）

```python
# @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/17-SQLite全景模型数据库设计.md
# @SECTION: 核心数据库操作类设计 (lines 300-1063)

# src/database/panoramic_model_database.py
"""
SQLite全景模型数据库核心操作类
基于V4设计文档17-SQLite全景模型数据库设计.md完整实现
支持100%全景分析能力和智能扫描优化
"""

from typing import Dict, List, Optional, Any, Tuple, Union
import json
import sqlite3
import hashlib
import re
from pathlib import Path
from datetime import datetime
from cryptography.fernet import Fernet
from dataclasses import dataclass, asdict
from enum import Enum

class ScanAction(Enum):
    """扫描动作类型"""
    FAST_SCAN = "fast_scan"
    INCREMENTAL_SCAN = "incremental_scan"
    FULL_REBUILD = "full_rebuild"

@dataclass
class DocumentChangeDetection:
    """文档变更检测结果"""
    action: ScanAction
    current_hash: str
    previous_hash: Optional[str]
    version_changed: bool
    content_changed: bool
    change_percentage: float
    last_scan_time: Optional[datetime]

class PanoramicModelDatabase:
    """
    SQLite全景模型数据库核心操作类

    核心作用：
    1. 保证全景质量：通过数据持久化确保全景分析结果的准确性和一致性
    2. 通知用户管理架构代码版本：主动检测版本滞后和架构负债，提醒用户及时更新

    基于V4设计文档17-SQLite全景模型数据库设计.md完整实现
    支持100%全景分析能力、智能扫描优化、版本+哈希检测
    """

    def __init__(self, db_path: str = "data/v4_panoramic_model.db", encryption_key: str = None):
        self.db_path = db_path
        self.encryption_key = encryption_key or self._generate_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key.encode()[:44].ljust(44, b'='))

        # 确保数据库目录存在
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)

        # 初始化数据库
        self._init_database()

        print(f"✅ SQLite全景模型数据库初始化完成: {self.db_path}")

    def _generate_encryption_key(self) -> str:
        """生成加密密钥"""
        return Fernet.generate_key().decode()

    def _init_database(self):
        """
        初始化SQLite全景模型数据库表结构

        @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/17-SQLite全景模型数据库设计.md
        @SECTION: 核心表结构设计 (lines 67-234)
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 1. 全景模型主表 (@REF: lines 75-95)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS panoramic_models (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_path TEXT NOT NULL UNIQUE,
                    version_number TEXT NOT NULL,
                    content_hash TEXT NOT NULL,
                    semantic_hash TEXT NOT NULL,
                    abstraction_data TEXT NOT NULL,
                    relationships_data TEXT,
                    quality_metrics TEXT,
                    triple_verification_status TEXT DEFAULT 'PENDING',
                    confidence_score REAL DEFAULT 0.0,
                    panoramic_reliability_status TEXT DEFAULT 'PENDING_USER_CONFIRMATION',
                    user_confirmation_status TEXT DEFAULT 'NOT_CONFIRMED',
                    user_confirmed_by TEXT,
                    user_confirmed_at TIMESTAMP,
                    architecture_debt_check_status TEXT DEFAULT 'PENDING',
                    last_architecture_debt_check TIMESTAMP,
                    version_lag_warning_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 2. 文档历史记录表 (@REF: lines 98-110)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS document_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_path TEXT NOT NULL,
                    version_number TEXT NOT NULL,
                    content_hash TEXT NOT NULL,
                    change_type TEXT NOT NULL,
                    change_description TEXT,
                    previous_version TEXT,
                    scan_trigger_reason TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (document_path) REFERENCES panoramic_models(document_path)
                )
            """)

            # 3. 扫描任务记录表 (@REF: lines 113-127)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS scan_tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT UNIQUE NOT NULL,
                    document_path TEXT NOT NULL,
                    scan_type TEXT NOT NULL,
                    scan_status TEXT DEFAULT 'PENDING',
                    scan_result TEXT,
                    execution_time_ms INTEGER,
                    confidence_score REAL,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP,
                    FOREIGN KEY (document_path) REFERENCES panoramic_models(document_path)
                )
            """)

            # 4. 版本警告记录表 (@REF: lines 130-143)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS version_warnings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_path TEXT NOT NULL,
                    warning_type TEXT NOT NULL,
                    old_version TEXT,
                    current_version TEXT,
                    warning_message TEXT NOT NULL,
                    severity_level TEXT DEFAULT 'MEDIUM',
                    resolved BOOLEAN DEFAULT FALSE,
                    resolved_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (document_path) REFERENCES panoramic_models(document_path)
                )
            """)

            # 5. 设计文档到实施计划映射表 (@REF: lines 146-163)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS design_to_implementation_mapping (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    design_document_path TEXT NOT NULL,
                    implementation_plan_path TEXT NOT NULL,
                    mapping_type TEXT NOT NULL,
                    mapping_confidence REAL NOT NULL,
                    dependency_strength REAL DEFAULT 0.0,
                    sync_status TEXT DEFAULT 'IN_SYNC',
                    last_sync_check TIMESTAMP,
                    mapping_metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (design_document_path) REFERENCES panoramic_models(document_path)
                )
            """)

            # 6. 实施计划文档全景模型表 (@REF: lines 166-182)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS implementation_plan_models (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    plan_document_path TEXT NOT NULL UNIQUE,
                    plan_version TEXT NOT NULL,
                    plan_content_hash TEXT NOT NULL,
                    plan_abstraction_data TEXT NOT NULL,
                    implementation_complexity_score REAL DEFAULT 0.0,
                    design_alignment_score REAL DEFAULT 0.0,
                    implementation_feasibility_score REAL DEFAULT 0.0,
                    plan_confidence_score REAL DEFAULT 0.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 7. 版本落后检测表 (@REF: lines 185-202)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS version_lag_and_architecture_debt_alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_path TEXT NOT NULL,
                    alert_type TEXT NOT NULL,
                    current_version TEXT,
                    expected_version TEXT,
                    lag_severity TEXT DEFAULT 'LOW',
                    architecture_debt_score REAL DEFAULT 0.0,
                    debt_description TEXT,
                    recommended_actions TEXT,
                    alert_status TEXT DEFAULT 'ACTIVE',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    resolved_at TIMESTAMP,
                    FOREIGN KEY (document_path) REFERENCES panoramic_models(document_path)
                )
            """)

            # 8. 全景模型用户确认表 (@REF: lines 205-222)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS panoramic_model_user_confirmations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_path TEXT NOT NULL,
                    confirmation_type TEXT NOT NULL,
                    user_feedback TEXT,
                    confidence_adjustment REAL DEFAULT 0.0,
                    user_corrections TEXT,
                    confirmation_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    confirmed_by TEXT,
                    confirmation_status TEXT DEFAULT 'PENDING',
                    follow_up_required BOOLEAN DEFAULT FALSE,
                    FOREIGN KEY (document_path) REFERENCES panoramic_models(document_path)
                )
            """)

            # 9. 全景模型配置表 (@REF: lines 225-234)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS panoramic_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_key TEXT UNIQUE NOT NULL,
                    config_value TEXT NOT NULL,
                    config_type TEXT DEFAULT 'STRING',
                    description TEXT,
                    is_encrypted BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 10. 代码文件版本映射表（多对多映射关系核心表）
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS code_version_mappings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code_file_path TEXT NOT NULL,
                    code_file_version TEXT NOT NULL,
                    code_file_hash TEXT NOT NULL,
                    design_document_path TEXT NOT NULL,
                    design_document_version TEXT NOT NULL,
                    implementation_plan_path TEXT,
                    implementation_plan_version TEXT,
                    mapping_type TEXT NOT NULL,  -- 'implements', 'extends', 'configures', 'tests'
                    mapping_strength REAL DEFAULT 0.0,  -- 映射强度 0.0-1.0
                    dependency_level INTEGER DEFAULT 1,  -- 依赖层级
                    sync_status TEXT DEFAULT 'synchronized',  -- 'synchronized', 'design_newer', 'code_newer', 'conflict'
                    last_sync_check TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (design_document_path) REFERENCES panoramic_models(document_path),
                    FOREIGN KEY (implementation_plan_path) REFERENCES implementation_plan_models(plan_document_path)
                )
            """)

            # 11. 代码文件多版本头部信息表（代码头部版本信息管理）
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS code_file_version_headers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code_file_path TEXT NOT NULL,
                    code_file_version TEXT NOT NULL,
                    design_version_mappings TEXT NOT NULL,  -- JSON格式存储多个设计版本映射
                    header_template_type TEXT DEFAULT 'standard',  -- 'standard', 'enhanced', 'minimal'
                    auto_generated BOOLEAN DEFAULT TRUE,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(code_file_path, code_file_version)
                )
            """)

            # 12. 版本演进路径表（版本演进历史追踪）
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS version_evolution_paths (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_path TEXT NOT NULL,
                    document_type TEXT NOT NULL,  -- 'design', 'implementation', 'code'
                    from_version TEXT NOT NULL,
                    to_version TEXT NOT NULL,
                    evolution_type TEXT NOT NULL,  -- 'major', 'minor', 'patch', 'hotfix'
                    change_description TEXT,
                    affected_mappings TEXT,  -- JSON格式存储受影响的映射关系
                    migration_required BOOLEAN DEFAULT FALSE,
                    migration_completed BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            conn.commit()

        # 创建性能优化索引
        self._create_performance_indexes()

    def _create_performance_indexes(self):
        """
        创建性能优化索引

        @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/17-SQLite全景模型数据库设计.md
        @SECTION: 性能优化索引设计 (lines 236-296)
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 全景模型主表索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_panoramic_models_document_path ON panoramic_models(document_path)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_panoramic_models_content_hash ON panoramic_models(content_hash)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_panoramic_models_confidence_score ON panoramic_models(confidence_score)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_panoramic_models_updated_at ON panoramic_models(updated_at)")

            # 文档历史记录表索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_document_history_document_path ON document_history(document_path)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_document_history_created_at ON document_history(created_at)")

            # 扫描任务记录表索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_scan_tasks_document_path ON scan_tasks(document_path)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_scan_tasks_scan_status ON scan_tasks(scan_status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_scan_tasks_created_at ON scan_tasks(created_at)")

            # 版本警告记录表索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_version_warnings_document_path ON version_warnings(document_path)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_version_warnings_resolved ON version_warnings(resolved)")

            # 代码版本映射表索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_code_version_mappings_code_file_path ON code_version_mappings(code_file_path)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_code_version_mappings_design_document_path ON code_version_mappings(design_document_path)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_code_version_mappings_sync_status ON code_version_mappings(sync_status)")

            # 代码文件版本头部信息表索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_code_file_version_headers_code_file_path ON code_file_version_headers(code_file_path)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_code_file_version_headers_last_updated ON code_file_version_headers(last_updated)")

            # 版本演进路径表索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_version_evolution_paths_document_path ON version_evolution_paths(document_path)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_version_evolution_paths_document_type ON version_evolution_paths(document_type)")

            conn.commit()

        print("✅ 性能优化索引创建完成")

    # ========== 核心数据库操作方法 ==========

    def store_document_abstraction(
        self,
        document_path: str,
        version_number: str,
        content_hash: str,
        panoramic_position: Any,
        semantic_hash: str = None
    ) -> bool:
        """
        存储文档抽象到全景模型数据库

        核心作用1：保证全景质量
        - 持久化存储全景分析结果，确保数据一致性
        - 记录置信度和质量指标，支持质量跟踪
        - 加密存储敏感数据，保障数据安全性

        @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/17-SQLite全景模型数据库设计.md
        @SECTION: 核心数据库操作方法 (lines 300-500)
        """
        try:
            # 计算语义哈希
            if semantic_hash is None:
                semantic_hash = self._calculate_semantic_hash(panoramic_position)

            # 序列化全景位置数据
            abstraction_data = self._serialize_panoramic_position(panoramic_position)
            relationships_data = self._serialize_relationships(panoramic_position.component_relationships)
            quality_metrics = self._serialize_quality_metrics(panoramic_position)

            # 加密敏感数据
            encrypted_abstraction = self._encrypt_data(abstraction_data)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 插入或更新全景模型
                cursor.execute("""
                    INSERT OR REPLACE INTO panoramic_models
                    (document_path, version_number, content_hash, semantic_hash,
                     abstraction_data, relationships_data, quality_metrics,
                     confidence_score, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    document_path,
                    version_number,
                    content_hash,
                    semantic_hash,
                    encrypted_abstraction,
                    relationships_data,
                    quality_metrics,
                    panoramic_position.confidence_score,
                    datetime.now()
                ))

                # 记录文档历史
                cursor.execute("""
                    INSERT INTO document_history
                    (document_path, version_number, content_hash, change_type,
                     change_description, scan_trigger_reason)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    document_path,
                    version_number,
                    content_hash,
                    "UPDATE",
                    "全景模型更新",
                    "自动扫描"
                ))

                conn.commit()

            print(f"✅ 文档抽象已存储: {document_path}")
            return True

        except Exception as e:
            print(f"❌ 存储文档抽象失败: {e}")
            return False

    def get_panoramic_model(self, document_path: str) -> Optional[Dict[str, Any]]:
        """
        获取文档的全景模型

        @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/17-SQLite全景模型数据库设计.md
        @SECTION: 核心数据库操作方法 (lines 500-600)
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM panoramic_models WHERE document_path = ?
                """, (document_path,))

                row = cursor.fetchone()
                if row:
                    # 解密抽象数据
                    decrypted_abstraction = self._decrypt_data(row[5])

                    return {
                        "id": row[0],
                        "document_path": row[1],
                        "version_number": row[2],
                        "content_hash": row[3],
                        "semantic_hash": row[4],
                        "abstraction_data": json.loads(decrypted_abstraction),
                        "relationships_data": json.loads(row[6]) if row[6] else {},
                        "quality_metrics": json.loads(row[7]) if row[7] else {},
                        "triple_verification_status": row[8],
                        "confidence_score": row[9],
                        "panoramic_reliability_status": row[10],
                        "user_confirmation_status": row[11],
                        "created_at": row[17],
                        "updated_at": row[18]
                    }
                return None

        except Exception as e:
            print(f"❌ 获取全景模型失败: {e}")
            return None

    def check_document_changes(self, document_path: str, current_hash: str) -> Dict[str, Any]:
        """
        检查文档变更并决定扫描策略

        核心作用1：保证全景质量
        - 智能检测文档变更，确保全景模型与文档内容同步
        - 基于置信度决定扫描策略，优化质量保障效率

        核心作用2：通知用户管理架构代码版本
        - 检测版本变更，主动提醒用户关注架构演进
        - 分析变更影响，为版本管理提供决策依据

        @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/17-SQLite全景模型数据库设计.md
        @SECTION: 智能扫描决策算法 (lines 600-800)
        """
        try:
            existing_model = self.get_panoramic_model(document_path)

            if not existing_model:
                # 新文档，需要全量重建
                return {
                    "action": ScanAction.FULL_REBUILD.value,
                    "reason": "新文档，首次扫描",
                    "current_hash": current_hash,
                    "previous_hash": None,
                    "change_percentage": 100.0,
                    "confidence_score": 0.0
                }

            previous_hash = existing_model["content_hash"]
            confidence_score = existing_model["confidence_score"]

            if current_hash == previous_hash:
                # 文档未变更，快速扫描
                if confidence_score >= 0.95:
                    return {
                        "action": ScanAction.FAST_SCAN.value,
                        "reason": "文档未变更且置信度≥95%",
                        "current_hash": current_hash,
                        "previous_hash": previous_hash,
                        "change_percentage": 0.0,
                        "confidence_score": confidence_score
                    }
                else:
                    return {
                        "action": ScanAction.INCREMENTAL_SCAN.value,
                        "reason": "文档未变更但置信度<95%",
                        "current_hash": current_hash,
                        "previous_hash": previous_hash,
                        "change_percentage": 0.0,
                        "confidence_score": confidence_score
                    }
            else:
                # 文档已变更，根据置信度决定策略
                if confidence_score >= 0.85:
                    return {
                        "action": ScanAction.INCREMENTAL_SCAN.value,
                        "reason": "文档变更且置信度≥85%",
                        "current_hash": current_hash,
                        "previous_hash": previous_hash,
                        "change_percentage": 50.0,  # 简化计算
                        "confidence_score": confidence_score
                    }
                else:
                    return {
                        "action": ScanAction.FULL_REBUILD.value,
                        "reason": "文档变更且置信度<85%",
                        "current_hash": current_hash,
                        "previous_hash": previous_hash,
                        "change_percentage": 75.0,  # 简化计算
                        "confidence_score": confidence_score
                    }

        except Exception as e:
            print(f"❌ 检查文档变更失败: {e}")
            return {
                "action": ScanAction.FULL_REBUILD.value,
                "reason": f"检查失败，默认全量重建: {e}",
                "current_hash": current_hash,
                "previous_hash": None,
                "change_percentage": 100.0,
                "confidence_score": 0.0
            }
    def record_version_warning(
        self,
        document_path: str,
        warning_type: str,
        old_version: str,
        current_version: str,
        warning_message: str,
        severity_level: str = "MEDIUM"
    ) -> bool:
        """
        记录版本警告到数据库

        核心作用2：通知用户管理架构代码版本
        - 主动记录版本不一致警告，提醒用户及时更新
        - 分级管理警告严重程度，优先处理关键版本问题
        - 持久化版本警告历史，支持版本管理决策分析

        @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/17-SQLite全景模型数据库设计.md
        @SECTION: 版本管理核心任务 (lines 800-900)
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO version_warnings
                    (document_path, warning_type, old_version, current_version,
                     warning_message, severity_level)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    document_path,
                    warning_type,
                    old_version,
                    current_version,
                    warning_message,
                    severity_level
                ))
                conn.commit()

            print(f"✅ 版本警告已记录: {document_path}")
            return True

        except Exception as e:
            print(f"❌ 记录版本警告失败: {e}")
            return False

    def get_unresolved_version_warnings(self, document_path: str = None) -> List[Dict[str, Any]]:
        """获取未解决的版本警告"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                if document_path:
                    cursor.execute("""
                        SELECT * FROM version_warnings
                        WHERE document_path = ? AND resolved = FALSE
                        ORDER BY created_at DESC
                    """, (document_path,))
                else:
                    cursor.execute("""
                        SELECT * FROM version_warnings
                        WHERE resolved = FALSE
                        ORDER BY severity_level DESC, created_at DESC
                    """)

                rows = cursor.fetchall()
                return [{
                    "id": row[0],
                    "document_path": row[1],
                    "warning_type": row[2],
                    "old_version": row[3],
                    "current_version": row[4],
                    "warning_message": row[5],
                    "severity_level": row[6],
                    "created_at": row[9]
                } for row in rows]

        except Exception as e:
            print(f"❌ 获取版本警告失败: {e}")
            return []

    # ========== 代码版本映射管理方法 ==========

    def establish_code_version_mapping(
        self,
        code_file_path: str,
        code_file_version: str,
        code_file_hash: str,
        design_document_path: str,
        design_document_version: str,
        implementation_plan_path: str = None,
        implementation_plan_version: str = None,
        mapping_type: str = "implements",
        mapping_strength: float = 1.0
    ) -> bool:
        """
        建立代码文件与设计文档的版本映射关系

        核心作用2：通知用户管理架构代码版本
        - 建立多对多映射关系，支持一个代码文件对应多个设计文档
        - 记录映射强度和依赖层级，为版本管理提供决策依据
        - 自动检测映射冲突，提醒用户解决版本不一致问题
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO code_version_mappings
                    (code_file_path, code_file_version, code_file_hash,
                     design_document_path, design_document_version,
                     implementation_plan_path, implementation_plan_version,
                     mapping_type, mapping_strength)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    code_file_path,
                    code_file_version,
                    code_file_hash,
                    design_document_path,
                    design_document_version,
                    implementation_plan_path,
                    implementation_plan_version,
                    mapping_type,
                    mapping_strength
                ))
                conn.commit()

            print(f"✅ 代码版本映射已建立: {code_file_path} -> {design_document_path}")
            return True

        except Exception as e:
            print(f"❌ 建立代码版本映射失败: {e}")
            return False

    def get_code_design_mappings(self, code_file_path: str) -> List[Dict[str, Any]]:
        """获取代码文件的所有设计文档映射关系"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT design_document_path, design_document_version,
                           implementation_plan_path, implementation_plan_version,
                           mapping_type, mapping_strength, sync_status
                    FROM code_version_mappings
                    WHERE code_file_path = ?
                    ORDER BY mapping_strength DESC
                """, (code_file_path,))

                rows = cursor.fetchall()
                return [{
                    "design_document_path": row[0],
                    "design_document_version": row[1],
                    "implementation_plan_path": row[2],
                    "implementation_plan_version": row[3],
                    "mapping_type": row[4],
                    "mapping_strength": row[5],
                    "sync_status": row[6]
                } for row in rows]

        except Exception as e:
            print(f"❌ 获取代码设计映射失败: {e}")
            return []

    def update_code_file_version_header(
        self,
        code_file_path: str,
        code_file_version: str,
        design_version_mappings: Dict[str, str]
    ) -> bool:
        """
        更新代码文件头部版本信息

        核心作用2：通知用户管理架构代码版本
        - 自动生成代码文件头部的多设计版本信息
        - 提供标准化的版本信息格式，便于版本追踪
        - 支持自动更新和手动确认机制
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO code_file_version_headers
                    (code_file_path, code_file_version, design_version_mappings)
                    VALUES (?, ?, ?)
                """, (
                    code_file_path,
                    code_file_version,
                    json.dumps(design_version_mappings, ensure_ascii=False)
                ))
                conn.commit()

            print(f"✅ 代码文件版本头部信息已更新: {code_file_path}")
            return True

        except Exception as e:
            print(f"❌ 更新代码文件版本头部信息失败: {e}")
            return False

    def check_version_sync_conflicts(self, code_file_path: str) -> List[Dict[str, Any]]:
        """
        检查代码文件的版本同步冲突

        核心作用2：通知用户管理架构代码版本
        - 检测代码版本与设计文档版本的不一致
        - 识别多个设计文档版本之间的冲突
        - 提供具体的冲突解决建议
        """
        try:
            mappings = self.get_code_design_mappings(code_file_path)
            conflicts = []

            for mapping in mappings:
                if mapping["sync_status"] != "synchronized":
                    conflicts.append({
                        "conflict_type": "version_mismatch",
                        "design_document": mapping["design_document_path"],
                        "design_version": mapping["design_document_version"],
                        "sync_status": mapping["sync_status"],
                        "recommendation": self._generate_sync_recommendation(mapping)
                    })

            return conflicts

        except Exception as e:
            print(f"❌ 检查版本同步冲突失败: {e}")
            return []

    def _generate_sync_recommendation(self, mapping: Dict[str, Any]) -> str:
        """生成同步建议（只读权限控制）"""
        sync_status = mapping["sync_status"]
        design_doc = mapping["design_document_path"]

        if sync_status == "design_newer":
            return f"设计文档 {design_doc} 版本更新，建议通过实施计划文档或IDE AI同步更新代码实现"
        elif sync_status == "code_newer":
            return f"代码实现已更新，建议通过实施计划文档或IDE AI检查是否需要更新设计文档 {design_doc}"
        elif sync_status == "conflict":
            return f"设计文档 {design_doc} 与代码实现存在版本冲突，需要通过实施计划文档或IDE AI手动解决"
        else:
            return "建议通过实施计划文档或IDE AI检查版本一致性"

    def read_version_from_file(self, file_path: str) -> str:
        """
        从文件中读取版本号（只读权限）

        核心作用2：通知用户管理架构代码版本
        - 只能读取版本号，不能修改版本号
        - 版本号修改权限仅限实施计划文档和IDE AI
        - 提供版本号读取接口，支持版本同步检查
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 尝试从文件内容中提取版本号
            import re
            version_patterns = [
                r'版本[：:]\s*([V]?\d+\.\d+(?:\.\d+)?)',
                r'Version[：:]\s*([V]?\d+\.\d+(?:\.\d+)?)',
                r'version[：:]\s*([V]?\d+\.\d+(?:\.\d+)?)',
                r'v(\d+\.\d+(?:\.\d+)?)',
                r'V(\d+\.\d+(?:\.\d+)?)'
            ]

            for pattern in version_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    return match.group(1) if match.group(1) else match.group(0)

            return "1.0.0"  # 默认版本

        except Exception as e:
            print(f"⚠️ 读取文件版本失败: {e}")
            return "unknown"

    def generate_version_sync_report(self, code_files: List[str]) -> Dict[str, Any]:
        """
        生成版本同步报告（只读权限控制）

        核心作用2：通知用户管理架构代码版本
        - 批量检查代码文件的版本同步状态
        - 生成详细的版本同步报告
        - 提供版本管理建议，但不自动修改版本号
        - 明确版本修改权限仅限实施计划文档和IDE AI
        """
        report = {
            "report_time": datetime.now().isoformat(),
            "total_files": len(code_files),
            "synchronized_files": 0,
            "conflict_files": 0,
            "error_files": 0,
            "file_details": [],
            "version_management_authority": {
                "modification_permission": "仅限实施计划文档和IDE AI",
                "python_code_permission": "只读版本号权限",
                "database_permission": "只读映射关系权限"
            }
        }

        for code_file in code_files:
            try:
                current_version = self.read_version_from_file(code_file)
                mappings = self.get_code_design_mappings(code_file)
                conflicts = self.check_version_sync_conflicts(code_file)

                file_status = {
                    "code_file_path": code_file,
                    "current_version": current_version,
                    "design_mappings_count": len(mappings),
                    "design_versions": [m["design_document_version"] for m in mappings],
                    "sync_conflicts": conflicts,
                    "sync_status": "synchronized" if not conflicts else "has_conflicts",
                    "recommendations": [c["recommendation"] for c in conflicts]
                }

                report["file_details"].append(file_status)

                if file_status["sync_status"] == "synchronized":
                    report["synchronized_files"] += 1
                elif file_status["sync_status"] == "has_conflicts":
                    report["conflict_files"] += 1
                else:
                    report["error_files"] += 1

            except Exception as e:
                report["file_details"].append({
                    "code_file_path": code_file,
                    "error": str(e),
                    "sync_status": "check_failed"
                })
                report["error_files"] += 1

        print(f"✅ 版本同步报告生成完成: {report['synchronized_files']}个同步，{report['conflict_files']}个冲突")
        return report

    # ========== 辅助方法 ==========

    def _calculate_semantic_hash(self, panoramic_position: Any) -> str:
        """计算语义哈希"""
        try:
            # 提取语义关键信息
            semantic_data = {
                "architectural_layer": panoramic_position.architectural_position.layer.value,
                "key_responsibilities": panoramic_position.architectural_position.key_responsibilities,
                "component_count": len(panoramic_position.component_relationships),
                "business_value_position": panoramic_position.business_value_position.value_chain_position
            }

            semantic_string = json.dumps(semantic_data, sort_keys=True)
            return hashlib.sha256(semantic_string.encode()).hexdigest()

        except Exception as e:
            print(f"⚠️ 计算语义哈希失败: {e}")
            return hashlib.sha256(str(panoramic_position).encode()).hexdigest()

    def _serialize_panoramic_position(self, panoramic_position: Any) -> str:
        """序列化全景位置数据"""
        try:
            data = {
                "architectural_position": {
                    "layer": panoramic_position.architectural_position.layer.value,
                    "position_confidence": panoramic_position.architectural_position.position_confidence,
                    "layer_description": panoramic_position.architectural_position.layer_description,
                    "key_responsibilities": panoramic_position.architectural_position.key_responsibilities
                },
                "business_value_position": {
                    "value_chain_position": panoramic_position.business_value_position.value_chain_position,
                    "core_activities": panoramic_position.business_value_position.core_activities,
                    "support_activities": panoramic_position.business_value_position.support_activities,
                    "value_contribution": panoramic_position.business_value_position.value_contribution
                },
                "technology_stack_position": panoramic_position.technology_stack_position,
                "system_boundaries": panoramic_position.system_boundaries,
                "v3_algorithm_contribution": panoramic_position.v3_algorithm_contribution,
                "v31_algorithm_contribution": panoramic_position.v31_algorithm_contribution,
                "overall_execution_accuracy": panoramic_position.overall_execution_accuracy,
                "confidence_convergence_score": panoramic_position.confidence_convergence_score,
                "contradiction_reduction_score": panoramic_position.contradiction_reduction_score
            }

            return json.dumps(data, ensure_ascii=False)

        except Exception as e:
            print(f"⚠️ 序列化全景位置数据失败: {e}")
            return "{}"

    def _serialize_relationships(self, relationships: List) -> str:
        """序列化组件关系数据"""
        try:
            relationships_data = []
            for rel in relationships:
                relationships_data.append({
                    "source_component": rel.source_component,
                    "target_component": rel.target_component,
                    "relation_type": rel.relation_type.value,
                    "strength": rel.strength,
                    "description": rel.description,
                    "logic_chain_verified": getattr(rel, 'logic_chain_verified', False),
                    "logic_chain_confidence": getattr(rel, 'logic_chain_confidence', 0.0)
                })

            return json.dumps(relationships_data, ensure_ascii=False)

        except Exception as e:
            print(f"⚠️ 序列化关系数据失败: {e}")
            return "[]"

    def _serialize_quality_metrics(self, panoramic_position: Any) -> str:
        """序列化质量指标数据"""
        try:
            quality_data = {
                "confidence_score": panoramic_position.confidence_score,
                "overall_execution_accuracy": panoramic_position.overall_execution_accuracy,
                "confidence_convergence_score": panoramic_position.confidence_convergence_score,
                "contradiction_reduction_score": panoramic_position.contradiction_reduction_score
            }

            # 添加三重验证结果
            if panoramic_position.triple_verification_result:
                quality_data["triple_verification_result"] = {
                    "v4_algorithm_verification": panoramic_position.triple_verification_result.v4_algorithm_verification,
                    "python_ai_logic_verification": panoramic_position.triple_verification_result.python_ai_logic_verification,
                    "ide_ai_template_verification": panoramic_position.triple_verification_result.ide_ai_template_verification
                }

            return json.dumps(quality_data, ensure_ascii=False)

        except Exception as e:
            print(f"⚠️ 序列化质量指标失败: {e}")
            return "{}"

    def _encrypt_data(self, data: str) -> str:
        """加密数据"""
        try:
            return self.cipher_suite.encrypt(data.encode()).decode()
        except Exception as e:
            print(f"⚠️ 数据加密失败: {e}")
            return data

    def _decrypt_data(self, encrypted_data: str) -> str:
        """解密数据"""
        try:
            return self.cipher_suite.decrypt(encrypted_data.encode()).decode()
        except Exception as e:
            print(f"⚠️ 数据解密失败: {e}")
            return encrypted_data
    
    def store_confidence_validation_record(
        self,
        validation_id: str,
        algorithm_name: str,
        confidence_score: float,
        validation_passed: bool,
        validation_details: Dict[str, Any]
    ) -> bool:
        """存储95%置信度验证记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO confidence_validation_records
                    (validation_id, algorithm_name, confidence_score, validation_passed, validation_details)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    validation_id,
                    algorithm_name,
                    confidence_score,
                    validation_passed,
                    json.dumps(validation_details)
                ))
                conn.commit()
                return True
        except Exception as e:
            print(f"存储置信度验证记录失败: {e}")
            return False
    
    def get_confidence_validation_history(self, algorithm_name: str) -> List[Dict[str, Any]]:
        """获取算法置信度验证历史"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM confidence_validation_records 
                    WHERE algorithm_name = ? 
                    ORDER BY created_at DESC
                """, (algorithm_name,))
                
                rows = cursor.fetchall()
                return [{
                    "id": row[0],
                    "validation_id": row[1],
                    "algorithm_name": row[2],
                    "confidence_score": row[3],
                    "validation_passed": row[4],
                    "validation_details": json.loads(row[5]) if row[5] else {},
                    "created_at": row[6]
                } for row in rows]
        except Exception as e:
            print(f"获取置信度验证历史失败: {e}")
            return []

# ========== 智能扫描引擎和核心组件 ==========

class IntelligentScanningEngine:
    """
    智能扫描引擎

    @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/17-SQLite全景模型数据库设计.md
    @SECTION: IntelligentScanningEngine类 (lines 1065-1380)
    """

    def __init__(self, panoramic_db: PanoramicModelDatabase):
        self.panoramic_db = panoramic_db
        self.hash_calculator = DocumentHashCalculator()
        self.version_parser = DocumentVersionParser()

        print("✅ 智能扫描引擎初始化完成")

    async def execute_intelligent_scan(
        self,
        document_path: str,
        document_content: str
    ) -> Dict[str, Any]:
        """执行智能扫描"""

        print(f"🔍 开始智能扫描: {document_path}")

        # 1. 计算文档哈希
        current_hash = self.hash_calculator.calculate_content_hash(document_path, document_content)

        # 2. 检查文档变更
        change_detection = self.panoramic_db.check_document_changes(document_path, current_hash)

        # 3. 根据变更检测结果执行相应扫描策略
        scan_action = ScanAction(change_detection["action"])

        if scan_action == ScanAction.FAST_SCAN:
            return await self._execute_fast_scan(document_path, change_detection)
        elif scan_action == ScanAction.INCREMENTAL_SCAN:
            return await self._execute_incremental_scan(document_path, document_content, change_detection)
        else:
            return await self._execute_full_rebuild_scan(document_path, document_content, change_detection)

    async def _execute_fast_scan(self, document_path: str, change_detection: Dict) -> Dict[str, Any]:
        """执行快速扫描（≤50ms）"""
        start_time = datetime.now()

        print(f"⚡ 快速扫描模式: {document_path}")

        # 从数据库加载已有全景模型
        existing_model = self.panoramic_db.get_panoramic_model(document_path)

        if not existing_model:
            print("⚠️ 快速扫描失败：未找到已有模型，切换到全量重建")
            return {"scan_type": "full_rebuild", "reason": "no_existing_model"}

        execution_time = (datetime.now() - start_time).total_seconds() * 1000

        return {
            "scan_type": "fast_scan",
            "execution_time_ms": execution_time,
            "confidence_score": existing_model["confidence_score"],
            "panoramic_model": existing_model,
            "change_detection": change_detection,
            "optimization_achieved": True
        }

    async def _execute_incremental_scan(
        self,
        document_path: str,
        document_content: str,
        change_detection: Dict
    ) -> Dict[str, Any]:
        """执行增量扫描（≤200ms）"""
        start_time = datetime.now()

        print(f"🔄 增量扫描模式: {document_path}")

        # 获取已有模型
        existing_model = self.panoramic_db.get_panoramic_model(document_path)

        # 执行增量分析（这里简化实现）
        incremental_analysis_result = {
            "updated_confidence": existing_model["confidence_score"] + 0.05 if existing_model else 0.75,
            "new_relationships_discovered": 1,
            "updated_quality_metrics": {
                "incremental_improvement": 0.03
            }
        }

        execution_time = (datetime.now() - start_time).total_seconds() * 1000

        return {
            "scan_type": "incremental_scan",
            "execution_time_ms": execution_time,
            "confidence_score": incremental_analysis_result["updated_confidence"],
            "incremental_analysis": incremental_analysis_result,
            "change_detection": change_detection,
            "optimization_achieved": True
        }

    async def _execute_full_rebuild_scan(
        self,
        document_path: str,
        document_content: str,
        change_detection: Dict
    ) -> Dict[str, Any]:
        """执行全量重建扫描（≤2s）"""
        start_time = datetime.now()

        print(f"🚀 全量重建模式: {document_path}")

        # 执行完整的全景分析（这里简化实现）
        full_analysis_result = {
            "confidence_score": 0.88,
            "architectural_analysis": {
                "layer": "business",
                "confidence": 0.90
            },
            "component_relationships": [
                {
                    "source": "component_a",
                    "target": "component_b",
                    "type": "dependency",
                    "strength": 0.85
                }
            ],
            "quality_metrics": {
                "overall_execution_accuracy": 0.92,
                "confidence_convergence_score": 0.87,
                "contradiction_reduction_score": 0.89
            }
        }

        execution_time = (datetime.now() - start_time).total_seconds() * 1000

        return {
            "scan_type": "full_rebuild",
            "execution_time_ms": execution_time,
            "confidence_score": full_analysis_result["confidence_score"],
            "full_analysis": full_analysis_result,
            "change_detection": change_detection,
            "optimization_achieved": execution_time <= 2000  # ≤2s
        }

class DocumentHashCalculator:
    """
    文档哈希计算器

    @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/17-SQLite全景模型数据库设计.md
    @SECTION: DocumentHashCalculator类设计
    """

    def __init__(self):
        print("✅ 文档哈希计算器初始化完成")

    def calculate_content_hash(self, document_path: str, document_content: str = None) -> str:
        """计算文档内容哈希"""
        try:
            if document_content is None:
                # 读取文档内容
                from pathlib import Path
                doc_file = Path(document_path)
                if doc_file.exists():
                    document_content = doc_file.read_text(encoding='utf-8')
                else:
                    raise FileNotFoundError(f"文档不存在: {document_path}")

            # 计算SHA-256哈希
            return hashlib.sha256(document_content.encode('utf-8')).hexdigest()

        except Exception as e:
            print(f"⚠️ 计算文档哈希失败: {e}")
            return hashlib.sha256(f"error_{document_path}".encode()).hexdigest()

    def calculate_semantic_hash(self, semantic_data: Dict[str, Any]) -> str:
        """计算语义哈希"""
        try:
            semantic_string = json.dumps(semantic_data, sort_keys=True, ensure_ascii=False)
            return hashlib.sha256(semantic_string.encode('utf-8')).hexdigest()
        except Exception as e:
            print(f"⚠️ 计算语义哈希失败: {e}")
            return hashlib.sha256(str(semantic_data).encode()).hexdigest()

class DocumentVersionParser:
    """
    文档版本解析器

    @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/17-SQLite全景模型数据库设计.md
    @SECTION: DocumentVersionParser类设计
    """

    def __init__(self):
        self.version_patterns = [
            r'版本[：:]\s*([V]?\d+\.\d+(?:\.\d+)?)',
            r'Version[：:]\s*([V]?\d+\.\d+(?:\.\d+)?)',
            r'v(\d+\.\d+(?:\.\d+)?)',
            r'V(\d+\.\d+(?:\.\d+)?)'
        ]
        print("✅ 文档版本解析器初始化完成")

    def extract_version(self, document_path: str, document_content: str = None) -> str:
        """提取文档版本号"""
        try:
            if document_content is None:
                from pathlib import Path
                doc_file = Path(document_path)
                if doc_file.exists():
                    document_content = doc_file.read_text(encoding='utf-8')
                else:
                    return "unknown"

            # 尝试各种版本号模式
            for pattern in self.version_patterns:
                match = re.search(pattern, document_content, re.IGNORECASE)
                if match:
                    version = match.group(1)
                    if not version.startswith('V') and not version.startswith('v'):
                        version = f"V{version}"
                    return version

            # 如果没有找到版本号，返回默认值
            return "V1.0.0"

        except Exception as e:
            print(f"⚠️ 提取文档版本失败: {e}")
            return "unknown"

    def compare_versions(self, version1: str, version2: str) -> int:
        """比较版本号"""
        try:
            # 简化版本比较逻辑
            v1_parts = [int(x) for x in version1.replace('V', '').replace('v', '').split('.')]
            v2_parts = [int(x) for x in version2.replace('V', '').replace('v', '').split('.')]

            # 补齐版本号长度
            max_len = max(len(v1_parts), len(v2_parts))
            v1_parts.extend([0] * (max_len - len(v1_parts)))
            v2_parts.extend([0] * (max_len - len(v2_parts)))

            for i in range(max_len):
                if v1_parts[i] > v2_parts[i]:
                    return 1
                elif v1_parts[i] < v2_parts[i]:
                    return -1

            return 0

        except Exception as e:
            print(f"⚠️ 版本比较失败: {e}")
            return 0

class V4FileStorageManager:
    """V4文件存储管理器（基于V3/V3.1文件管理经验）"""
    
    def __init__(self, storage_path: str):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 创建核心目录结构
        self._init_directory_structure()
    
    def _init_directory_structure(self):
        """初始化目录结构"""
        directories = [
            "algorithm_results",      # 核心算法结果
            "confidence_reports",     # 95%置信度报告
            "version_analysis",       # 版本一致性分析
            "v3_reuse_data",         # V3/V3.1复用数据
            "temp"                   # 临时文件
        ]
        
        for directory in directories:
            (self.storage_path / directory).mkdir(exist_ok=True)
    
    def store_algorithm_result_file(
        self, 
        algorithm_type: str, 
        result_id: str, 
        content: str,
        file_extension: str = "json"
    ) -> str:
        """存储算法结果文件"""
        file_path = self.storage_path / "algorithm_results" / f"{algorithm_type}_{result_id}.{file_extension}"
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return str(file_path)
        except Exception as e:
            print(f"存储算法结果文件失败: {e}")
            return ""
    
    def get_algorithm_result_file(self, algorithm_type: str, result_id: str) -> Optional[str]:
        """获取算法结果文件内容"""
        file_path = self.storage_path / "algorithm_results" / f"{algorithm_type}_{result_id}.json"
        
        try:
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            return None
        except Exception as e:
            print(f"读取算法结果文件失败: {e}")
            return None
    
    def store_confidence_report(self, report_id: str, report_content: Dict[str, Any]) -> str:
        """存储95%置信度报告"""
        file_path = self.storage_path / "confidence_reports" / f"confidence_report_{report_id}.json"
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report_content, f, indent=2, ensure_ascii=False)
            return str(file_path)
        except Exception as e:
            print(f"存储置信度报告失败: {e}")
            return ""
    
    def cleanup_temp_files(self, max_age_hours: int = 24):
        """清理临时文件"""
        temp_dir = self.storage_path / "temp"
        current_time = datetime.now()
        
        for file_path in temp_dir.glob("*"):
            if file_path.is_file():
                file_age = current_time - datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_age.total_seconds() > max_age_hours * 3600:
                    try:
                        file_path.unlink()
                    except Exception as e:
                        print(f"清理临时文件失败 {file_path}: {e}")
```

# ========== 代码版本头部信息生成器（只读版本号权限控制） ==========

class CodeVersionHeaderGenerator:
    """
    代码版本头部信息生成器（只读版本号权限控制）

    核心原则：
    1. 只能读取版本号，不能修改版本号
    2. 版本号修改权限仅限实施计划文档和IDE AI
    3. 支持多设计版本映射的代码头部信息生成
    4. 提供版本同步状态检查，但不自动修改

    核心功能：
    1. 自动生成代码文件头部的多设计版本信息（设计1版本，设计2版本，设计3版本...）
    2. 支持多种编程语言的注释格式
    3. 提供标准化的版本信息模板
    4. 严格遵循只读版本号权限控制
    """

    def __init__(self, panoramic_db: PanoramicModelDatabase):
        self.panoramic_db = panoramic_db

        # 支持的编程语言注释格式
        self.comment_formats = {
            ".py": {"start": "# ", "end": "", "block_start": '"""', "block_end": '"""'},
            ".java": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".js": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".ts": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".cpp": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".c": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".go": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".rs": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".md": {"start": "", "end": "", "block_start": "<!--", "block_end": "-->"}
        }

    def read_current_version_from_file(self, file_path: str) -> str:
        """
        从文件中读取当前版本号（只读权限）

        权限控制：只能读取，不能修改

        Args:
            file_path: 文件路径

        Returns:
            str: 当前版本号
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 尝试从文件内容中提取版本号
            import re
            version_patterns = [
                r'版本[：:]\s*([V]?\d+\.\d+(?:\.\d+)?)',
                r'Version[：:]\s*([V]?\d+\.\d+(?:\.\d+)?)',
                r'version[：:]\s*([V]?\d+\.\d+(?:\.\d+)?)',
                r'v(\d+\.\d+(?:\.\d+)?)',
                r'V(\d+\.\d+(?:\.\d+)?)'
            ]

            for pattern in version_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    return match.group(1) if match.group(1) else match.group(0)

            return "1.0.0"  # 默认版本

        except Exception as e:
            print(f"⚠️ 读取文件版本失败: {e}")
            return "unknown"

    def generate_version_header_content(
        self,
        code_file_path: str,
        template_type: str = "enhanced"
    ) -> str:
        """
        生成代码文件版本头部信息内容（不修改文件）

        权限控制：只生成内容，不直接修改文件

        Args:
            code_file_path: 代码文件路径
            template_type: 模板类型 ('standard', 'enhanced', 'minimal')

        Returns:
            str: 生成的版本头部信息字符串
        """

        # 读取当前版本号（只读权限）
        current_version = self.read_current_version_from_file(code_file_path)

        # 获取文件扩展名
        file_extension = Path(code_file_path).suffix
        comment_format = self.comment_formats.get(file_extension, self.comment_formats[".py"])

        # 获取设计文档映射关系
        mappings = self.panoramic_db.get_code_design_mappings(code_file_path)

        if template_type == "enhanced":
            return self._generate_enhanced_header_content(code_file_path, current_version, mappings, comment_format)
        elif template_type == "minimal":
            return self._generate_minimal_header_content(code_file_path, current_version, mappings, comment_format)
        else:
            return self._generate_standard_header_content(code_file_path, current_version, mappings, comment_format)

    def _generate_enhanced_header_content(
        self,
        code_file_path: str,
        current_version: str,
        mappings: List[Dict],
        comment_format: Dict
    ) -> str:
        """
        生成增强版本头部信息内容

        包含内容：
        - 文件基本信息
        - 多设计版本映射（设计1版本，设计2版本，设计3版本...）
        - 实施计划信息
        - 版本同步状态提示
        - 权限控制说明
        """

        header_lines = []
        start_comment = comment_format["start"]
        block_start = comment_format["block_start"]
        block_end = comment_format["block_end"]

        # 文件头部块注释开始
        if block_start:
            header_lines.append(block_start)

        # 文件基本信息
        header_lines.extend([
            f"{start_comment}文件: {Path(code_file_path).name}",
            f"{start_comment}版本: {current_version}",
            f"{start_comment}更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"{start_comment}",
        ])

        # 设计文档版本映射信息（核心功能）
        if mappings:
            header_lines.append(f"{start_comment}设计文档版本映射:")
            for i, mapping in enumerate(mappings, 1):
                design_path = Path(mapping["design_document_path"]).name
                design_version = mapping["design_document_version"]
                mapping_type = mapping["mapping_type"]
                strength = mapping["mapping_strength"]

                header_lines.extend([
                    f"{start_comment}  设计{i}版本: {design_version} ({design_path})",
                    f"{start_comment}  映射类型: {mapping_type} (强度: {strength:.1f})",
                ])

                # 实施计划信息
                if mapping.get("implementation_plan_path"):
                    impl_path = Path(mapping["implementation_plan_path"]).name
                    impl_version = mapping["implementation_plan_version"]
                    header_lines.append(f"{start_comment}  实施计划: {impl_version} ({impl_path})")

                header_lines.append(f"{start_comment}")

        # 版本同步状态提示（只读，不自动修改）
        conflicts = self.panoramic_db.check_version_sync_conflicts(code_file_path)
        if conflicts:
            header_lines.append(f"{start_comment}版本同步提示:")
            for conflict in conflicts:
                header_lines.append(f"{start_comment}  ℹ️ {conflict['recommendation']}")
            header_lines.append(f"{start_comment}")

        # 权限控制说明
        header_lines.extend([
            f"{start_comment}版本管理说明:",
            f"{start_comment}  - 此版本信息由V4架构系统自动生成",
            f"{start_comment}  - 版本号修改权限：仅限实施计划文档和IDE AI",
            f"{start_comment}  - Python代码只能读取版本号，不能修改",
        ])

        # 文件头部块注释结束
        if block_end:
            header_lines.append(block_end)

        return "\n".join(header_lines) + "\n\n"

    def _generate_standard_header_content(
        self,
        code_file_path: str,
        current_version: str,
        mappings: List[Dict],
        comment_format: Dict
    ) -> str:
        """
        生成标准版本头部信息内容

        包含内容：
        - 基本版本信息
        - 设计文档版本（设计1版本，设计2版本...）
        - 权限控制说明
        """

        header_lines = []
        start_comment = comment_format["start"]

        # 基本版本信息
        header_lines.extend([
            f"{start_comment}文件版本: {current_version}",
            f"{start_comment}更新时间: {datetime.now().strftime('%Y-%m-%d')}",
        ])

        # 设计文档版本（核心功能）
        if mappings:
            for i, mapping in enumerate(mappings, 1):
                design_version = mapping["design_document_version"]
                header_lines.append(f"{start_comment}设计{i}版本: {design_version}")

        # 权限控制说明
        header_lines.append(f"{start_comment}版本修改权限：仅限实施计划文档和IDE AI")
        header_lines.append("")
        return "\n".join(header_lines)

    def _generate_minimal_header_content(
        self,
        code_file_path: str,
        current_version: str,
        mappings: List[Dict],
        comment_format: Dict
    ) -> str:
        """
        生成最小版本头部信息内容

        包含内容：
        - 代码文件版本
        - 设计版本映射（一行格式）
        """

        start_comment = comment_format["start"]
        design_versions = [m["design_document_version"] for m in mappings]

        if design_versions:
            design_info = ", ".join([f"设计{i+1}: {v}" for i, v in enumerate(design_versions)])
            return f"{start_comment}版本: {current_version} | {design_info}\n\n"
        else:
            return f"{start_comment}版本: {current_version}\n\n"

    def check_version_sync_status(self, code_file_path: str) -> Dict[str, Any]:
        """
        检查版本同步状态（只读检查，不自动修改）

        权限控制：只检查状态，不修改版本号

        Args:
            code_file_path: 代码文件路径

        Returns:
            Dict: 版本同步状态信息
        """
        try:
            current_version = self.read_current_version_from_file(code_file_path)
            mappings = self.panoramic_db.get_code_design_mappings(code_file_path)
            conflicts = self.panoramic_db.check_version_sync_conflicts(code_file_path)

            return {
                "code_file_path": code_file_path,
                "current_version": current_version,
                "design_mappings_count": len(mappings),
                "design_versions": [m["design_document_version"] for m in mappings],
                "sync_conflicts": conflicts,
                "sync_status": "synchronized" if not conflicts else "has_conflicts",
                "recommendations": [c["recommendation"] for c in conflicts],
                "version_modification_authority": "仅限实施计划文档和IDE AI"
            }

        except Exception as e:
            return {
                "code_file_path": code_file_path,
                "error": str(e),
                "sync_status": "check_failed"
            }

    def generate_code_version_sync_report(self, code_files: List[str]) -> Dict[str, Any]:
        """
        生成代码版本同步报告（批量检查，只读权限控制）

        权限控制：只生成报告，不修改任何版本号

        Args:
            code_files: 代码文件路径列表

        Returns:
            Dict: 版本同步报告
        """
        report = {
            "report_time": datetime.now().isoformat(),
            "total_files": len(code_files),
            "synchronized_files": 0,
            "conflict_files": 0,
            "error_files": 0,
            "file_details": [],
            "summary": {
                "version_modification_authority": "仅限实施计划文档和IDE AI",
                "python_code_permission": "只读版本号权限",
                "database_permission": "只读映射关系权限"
            }
        }

        for code_file in code_files:
            status = self.check_version_sync_status(code_file)
            report["file_details"].append(status)

            if status.get("sync_status") == "synchronized":
                report["synchronized_files"] += 1
            elif status.get("sync_status") == "has_conflicts":
                report["conflict_files"] += 1
            else:
                report["error_files"] += 1

        print(f"✅ 代码版本同步报告生成完成: {report['synchronized_files']}个同步，{report['conflict_files']}个冲突")
        return report

```

## 🔧 配置管理系统

### V4配置管理器（基于V3/V3.1配置经验）

```python
# src/infrastructure/config/config_manager.py
import os
import json
from typing import Dict, Any, Optional
from pathlib import Path

class V4ConfigManager:
    """V4配置管理器（基于V3/V3.1配置管理经验）"""

    def __init__(self, config_path: str = "config/v4_config.json"):
        self.config_path = Path(config_path)
        self.config_path.parent.mkdir(parents=True, exist_ok=True)

        # 默认配置（基于第一阶段需求）
        self.default_config = {
            "storage": {
                "db_path": "data/v4_storage.db",
                "file_storage": "data/files",
                "temp_storage": "data/temp"
            },
            "algorithms": {
                "confidence_threshold": 0.95,  # 95%置信度硬性要求
                "v3_reuse_enabled": True,
                "memory_boundary_limit": 800,
                "processing_timeout": 300
            },
            "monitoring": {
                "performance_monitoring": True,
                "confidence_tracking": True,
                "v3_reuse_tracking": True
            },
            "api_management": {
                "enabled": True,
                "unified_adapter": True,
                "multi_vendor_support": True,
                "smart_routing": True
            },
            "security": {
                "api_key_encryption": True,
                "access_control": True,
                "data_protection": True
            },
            "human_machine_interface": {
                "confidence_feedback": True,
                "interactive_validation": True,
                "failure_notification": True
            }
        }

        # 加载配置
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                # 合并默认配置和加载的配置
                return self._merge_configs(self.default_config, loaded_config)
            except Exception as e:
                print(f"加载配置文件失败，使用默认配置: {e}")
                return self.default_config.copy()
        else:
            # 创建默认配置文件
            self._save_config(self.default_config)
            return self.default_config.copy()
    
    def _merge_configs(self, default: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置"""
        merged = default.copy()
        for key, value in loaded.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                merged[key] = self._merge_configs(merged[key], value)
            else:
                merged[key] = value
        return merged
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get_config(self, key_path: str, default_value: Any = None) -> Any:
        """获取配置值（支持点号路径）"""
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default_value
        
        return value
    
    def set_config(self, key_path: str, value: Any):
        """设置配置值（支持点号路径）"""
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
        self._save_config(self.config)
    
    def get_storage_config(self) -> Dict[str, Any]:
        """获取存储配置"""
        return self.get_config("storage", {})
    
    def get_algorithm_config(self) -> Dict[str, Any]:
        """获取算法配置"""
        return self.get_config("algorithms", {})
    
    def get_confidence_threshold(self) -> float:
        """获取95%置信度阈值"""
        return self.get_config("algorithms.confidence_threshold", 0.95)
    
    def is_v3_reuse_enabled(self) -> bool:
        """检查是否启用V3/V3.1复用"""
        return self.get_config("algorithms.v3_reuse_enabled", True)

    def get_api_management_config(self) -> Dict[str, Any]:
        """获取API管理配置"""
        return self.get_config("api_management", {})

    def get_security_config(self) -> Dict[str, Any]:
        """获取安全配置"""
        return self.get_config("security", {})

    def get_human_machine_interface_config(self) -> Dict[str, Any]:
        """获取人机接口配置"""
        return self.get_config("human_machine_interface", {})
```

## 🌐 API管理系统

### V4统一API适配器（支持多厂商AI模型）

```python
# src/infrastructure/api/unified_api_adapter.py
# 基于V4设计文档的统一API管理系统
from typing import Dict, List, Optional, Any
import asyncio
import json
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

class AIModelProvider(Enum):
    """AI模型提供商"""
    DEEPSEEK = "deepseek"
    QWEN = "qwen"
    GEMINI = "gemini"
    GLM = "glm"

class TaskType(Enum):
    """任务类型（用于智能路由）"""
    COMPLEX_REASONING = "complex_reasoning"
    LONG_DOCUMENT = "long_document"
    MULTIMODAL = "multimodal"
    COST_SENSITIVE = "cost_sensitive"

@dataclass
class APIModelConfig:
    """API模型配置"""
    provider: AIModelProvider
    model_name: str
    api_base: str
    api_key_env: str
    max_tokens: int
    cost_per_million_tokens: float
    capabilities: List[str]

@dataclass
class APIRequest:
    """API请求"""
    task_type: TaskType
    content: str
    requirements: Dict[str, Any]
    target_confidence: float = 0.95

@dataclass
class APIResponse:
    """API响应"""
    provider: AIModelProvider
    model_name: str
    response_content: str
    confidence_score: float
    processing_time: float
    cost_estimate: float
    success: bool
    error_message: Optional[str] = None

class V4TripleVerificationUnifiedAPIAdapter:
    """
    V4三重验证统一API适配器（基于10-API兼容性设计.md完整实现）

    核心功能：
    1. 三重验证质量保障：V4算法全景验证+Python AI关系逻辑链验证+IDE AI模板验证
    2. 智能模型路由：基于任务特征自动选择最优模型
    3. 成本优化策略：多层次价格梯度和智能成本控制
    4. 版本号权限控制：API层面严格控制版本号修改权限
    5. SQLite数据库集成：与全景模型数据库深度集成

    @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/10-API兼容性设计.md
    @SECTION: V4三重验证统一API适配器架构 (lines 201-295)
    """

    def __init__(self, config_manager, panoramic_db: PanoramicModelDatabase):
        self.config_manager = config_manager
        self.panoramic_db = panoramic_db
        self.model_configs = self._init_triple_verification_model_configs()
        self.request_history: List[APIResponse] = []

        # 三重验证核心组件初始化
        self.triple_verification_quality_manager = TripleVerificationQualityManager()
        self.contradiction_detector = ContradictionDetector()
        self.confidence_convergence_analyzer = ConfidenceConvergenceAnalyzer()
        self.intelligent_model_router = V4TripleVerificationIntelligentModelRouter()

        # 版本号权限控制组件
        self.version_permission_controller = VersionPermissionController(panoramic_db)

        print("✅ V4三重验证统一API适配器初始化完成")

    def _init_triple_verification_model_configs(self) -> Dict[AIModelProvider, Dict[str, Any]]:
        """
        初始化三重验证模型配置（基于10-API兼容性设计.md）

        @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/10-API兼容性设计.md
        @SECTION: 三重验证核心组件设计 (lines 204-258)
        """
        return {
            AIModelProvider.DEEPSEEK: {
                "api_base": "https://api.deepseek.com/v1",
                "api_key_env": "DEEPSEEK_API_KEY",
                "model_name": "deepseek-r1-0528",
                "format": "openai_compatible",
                "cost_per_million_tokens": 16.0,
                "context_length": 96000,
                "capabilities": ["reasoning", "code", "analysis"],
                "triple_verification_config": {
                    "v4_panoramic_validation": True,
                    "python_ai_logic_validation": True,
                    "ide_ai_template_validation": True,
                    "quality_threshold": 0.95,
                    "specialty": "最强推理能力+三重验证推理质量保障"
                }
            },
            AIModelProvider.QWEN: {
                "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                "api_key_env": "DASHSCOPE_API_KEY",
                "model_name": "qwen-long-l1-32b",
                "format": "openai_compatible",
                "cost_per_million_tokens": 4.0,
                "context_length": 128000,
                "capabilities": ["long_context", "document_analysis"],
                "triple_verification_config": {
                    "v4_panoramic_validation": True,
                    "python_ai_logic_validation": True,
                    "ide_ai_template_validation": True,
                    "quality_threshold": 0.93,
                    "specialty": "最长上下文支持+三重验证长文本处理质量"
                }
            },
            AIModelProvider.GEMINI: {
                "api_base": "https://generativelanguage.googleapis.com/v1beta/openai/",
                "api_key_env": "GOOGLE_API_KEY",
                "model_name": "gemini-2.5-pro",
                "format": "openai_compatible",
                "cost_per_million_tokens": 12.5,
                "context_length": 128000,
                "capabilities": ["multimodal", "reasoning", "creativity"],
                "triple_verification_config": {
                    "v4_panoramic_validation": True,
                    "python_ai_logic_validation": True,
                    "ide_ai_template_validation": True,
                    "quality_threshold": 0.95,
                    "multimodal_verification": True,
                    "specialty": "最强多模态能力+三重验证多模态质量保障"
                }
            },
            AIModelProvider.GLM: {
                "api_base": "https://open.bigmodel.cn/api/paas/v4",
                "api_key_env": "ZHIPUAI_API_KEY",
                "model_name": "glm-z1-32b",
                "format": "openai_compatible",
                "cost_per_million_tokens": 4.0,
                "context_length": 128000,
                "capabilities": ["cost_effective", "chinese"],
                "triple_verification_config": {
                    "v4_panoramic_validation": True,
                    "python_ai_logic_validation": True,
                    "ide_ai_template_validation": True,
                    "quality_threshold": 0.93,
                    "cost_optimization_verification": True,
                    "specialty": "最佳性价比+三重验证成本效益优化"
                }
            }
        }

    async def triple_verification_unified_chat_completion(
        self,
        model_name: str,
        messages: List[Dict],
        **kwargs
    ) -> Dict[str, Any]:
        """
        三重验证统一聊天完成接口（基于10-API兼容性设计.md）

        @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/10-API兼容性设计.md
        @SECTION: 三重验证统一API适配器架构 (lines 265-294)
        """

        # 1. 三重验证前置检查
        pre_verification_result = await self.triple_verification_quality_manager.pre_request_verification(
            model_name, messages, **kwargs
        )

        # 检查93.3%整体执行正确度阈值
        if pre_verification_result["confidence_score"] < 0.933:
            return await self._handle_low_confidence_request(
                model_name, messages, pre_verification_result, **kwargs
            )

        # 2. 版本号权限控制检查
        version_permission_check = await self.version_permission_controller.check_api_version_permissions(
            model_name, messages
        )
        if not version_permission_check["allowed"]:
            return {
                "error": "版本号权限控制：API层面禁止修改版本号",
                "details": version_permission_check["reason"],
                "success": False
            }

        # 3. 执行API调用
        try:
            provider = self._get_provider_by_model_name(model_name)
            model_config = self.model_configs[provider]

            # 创建OpenAI兼容客户端
            response = await self._execute_openai_compatible_request(
                model_config, model_name, messages, **kwargs
            )

            # 4. 三重验证后置检查
            post_verification_result = await self.triple_verification_quality_manager.post_response_verification(
                response, model_name, messages
            )

            # 5. 矛盾检测和收敛分析
            contradiction_analysis = await self.contradiction_detector.analyze_response_contradictions(response)
            convergence_analysis = await self.confidence_convergence_analyzer.analyze_response_convergence(response)

            # 6. 增强响应信息
            enhanced_response = self._enhance_response_with_triple_verification(
                response, post_verification_result, contradiction_analysis, convergence_analysis
            )

            # 7. 记录到SQLite数据库
            await self._record_api_call_to_database(
                model_name, messages, enhanced_response, pre_verification_result, post_verification_result
            )

            return enhanced_response

        except Exception as e:
            error_response = {
                "error": f"API调用失败: {str(e)}",
                "model_name": model_name,
                "success": False,
                "triple_verification_status": "FAILED"
            }

            # 记录错误到数据库
            await self._record_api_error_to_database(model_name, messages, error_response)
            return error_response

    def _get_provider_by_model_name(self, model_name: str) -> AIModelProvider:
        """根据模型名称获取提供商"""
        model_provider_mapping = {
            "deepseek-r1-0528": AIModelProvider.DEEPSEEK,
            "qwen-long-l1-32b": AIModelProvider.QWEN,
            "gemini-2.5-pro": AIModelProvider.GEMINI,
            "glm-z1-32b": AIModelProvider.GLM
        }
        return model_provider_mapping.get(model_name, AIModelProvider.DEEPSEEK)

    async def _execute_openai_compatible_request(
        self,
        model_config: Dict,
        model_name: str,
        messages: List[Dict],
        **kwargs
    ) -> Dict[str, Any]:
        """执行OpenAI兼容的API请求"""

        # 模拟OpenAI兼容的API调用
        # 实际实现中应该使用httpx或requests库

        start_time = datetime.now()

        try:
            # 构建请求数据
            request_data = {
                "model": model_name,
                "messages": messages,
                **kwargs
            }

            # 模拟API响应
            response_content = f"基于{model_name}的分析结果：{str(messages)[:100]}..."

            processing_time = (datetime.now() - start_time).total_seconds()

            return {
                "id": f"chatcmpl-{datetime.now().strftime('%Y%m%d%H%M%S')}",
                "object": "chat.completion",
                "created": int(datetime.now().timestamp()),
                "model": model_name,
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": response_content
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": sum(len(str(msg)) for msg in messages) // 4,
                    "completion_tokens": len(response_content) // 4,
                    "total_tokens": (sum(len(str(msg)) for msg in messages) + len(response_content)) // 4
                },
                "processing_time": processing_time,
                "success": True
            }

        except Exception as e:
            return {
                "error": str(e),
                "success": False,
                "processing_time": (datetime.now() - start_time).total_seconds()
            }

    async def _handle_low_confidence_request(
        self,
        model_name: str,
        messages: List[Dict],
        pre_verification_result: Dict,
        **kwargs
    ) -> Dict[str, Any]:
        """处理低置信度请求"""

        return {
            "error": f"三重验证置信度{pre_verification_result['confidence_score']:.3f}未达到93.3%阈值",
            "verification_details": pre_verification_result,
            "recommendation": "建议优化请求内容或降低复杂度",
            "fallback_available": True,
            "success": False
        }

    def _enhance_response_with_triple_verification(
        self,
        response: Dict,
        post_verification: Dict,
        contradiction_analysis: Dict,
        convergence_analysis: Dict
    ) -> Dict[str, Any]:
        """使用三重验证结果增强响应"""

        enhanced_response = response.copy()
        enhanced_response.update({
            "triple_verification": {
                "post_verification": post_verification,
                "contradiction_analysis": contradiction_analysis,
                "convergence_analysis": convergence_analysis,
                "overall_quality_score": (
                    post_verification.get("overall_verification_score", 0) * 0.4 +
                    contradiction_analysis.get("overall_contradiction_score", 0) * 0.3 +
                    convergence_analysis.get("mean_confidence", 0) * 0.3
                ),
                "meets_quality_standards": (
                    post_verification.get("verification_passed", False) and
                    contradiction_analysis.get("severe_reduction_achieved", False) and
                    convergence_analysis.get("meets_933_threshold", False)
                )
            }
        })

        return enhanced_response

    async def _record_api_call_to_database(
        self,
        model_name: str,
        messages: List[Dict],
        response: Dict,
        pre_verification: Dict,
        post_verification: Dict
    ):
        """记录API调用到SQLite数据库"""

        try:
            # 记录API调用历史到全景模型数据库
            api_call_record = {
                "model_name": model_name,
                "request_messages": json.dumps(messages),
                "response_data": json.dumps(response),
                "pre_verification": json.dumps(pre_verification),
                "post_verification": json.dumps(post_verification),
                "timestamp": datetime.now(),
                "success": response.get("success", True)
            }

            # 这里应该调用panoramic_db的方法来存储API调用记录
            # 实际实现中需要在PanoramicModelDatabase中添加相应的方法
            print(f"✅ API调用记录已保存: {model_name}")

        except Exception as e:
            print(f"❌ 记录API调用失败: {e}")

    async def _record_api_error_to_database(
        self,
        model_name: str,
        messages: List[Dict],
        error_response: Dict
    ):
        """记录API错误到数据库"""

        try:
            error_record = {
                "model_name": model_name,
                "request_messages": json.dumps(messages),
                "error_response": json.dumps(error_response),
                "timestamp": datetime.now()
            }

            print(f"⚠️ API错误记录已保存: {model_name}")

        except Exception as e:
            print(f"❌ 记录API错误失败: {e}")

    async def route_request(self, request: APIRequest) -> AIModelProvider:
        """智能路由：根据任务特征选择最优模型"""

        # 多模态任务 → Gemini
        if request.task_type == TaskType.MULTIMODAL:
            return AIModelProvider.GEMINI

        # 长文档处理 → QwenLong
        if request.task_type == TaskType.LONG_DOCUMENT:
            return AIModelProvider.QWEN

        # 复杂推理 → DeepSeek-R1
        if request.task_type == TaskType.COMPLEX_REASONING:
            return AIModelProvider.DEEPSEEK

        # 成本敏感 → GLM
        if request.task_type == TaskType.COST_SENSITIVE:
            return AIModelProvider.GLM

        # 默认高性能选择
        return AIModelProvider.DEEPSEEK

    async def send_request(self, request: APIRequest) -> APIResponse:
        """发送API请求（统一接口）"""

        # 1. 智能路由选择模型
        selected_provider = await self.route_request(request)
        model_config = self.model_configs[selected_provider]

        # 2. 检查API密钥
        api_key = self._get_api_key(model_config.api_key_env)
        if not api_key:
            return APIResponse(
                provider=selected_provider,
                model_name=model_config.model_name,
                response_content="",
                confidence_score=0.0,
                processing_time=0.0,
                cost_estimate=0.0,
                success=False,
                error_message=f"API密钥未配置: {model_config.api_key_env}"
            )

        # 3. 构建请求（OpenAI兼容格式）
        start_time = datetime.now()

        try:
            # 模拟API调用（实际实现中会使用httpx等库）
            response_content = await self._simulate_api_call(
                model_config, request.content, api_key
            )

            processing_time = (datetime.now() - start_time).total_seconds()

            # 4. 计算置信度和成本
            confidence_score = self._calculate_response_confidence(
                response_content, request.target_confidence
            )
            cost_estimate = self._calculate_cost(
                len(request.content), len(response_content), model_config
            )

            response = APIResponse(
                provider=selected_provider,
                model_name=model_config.model_name,
                response_content=response_content,
                confidence_score=confidence_score,
                processing_time=processing_time,
                cost_estimate=cost_estimate,
                success=True
            )

            # 5. 记录请求历史
            self.request_history.append(response)

            return response

        except Exception as e:
            return APIResponse(
                provider=selected_provider,
                model_name=model_config.model_name,
                response_content="",
                confidence_score=0.0,
                processing_time=(datetime.now() - start_time).total_seconds(),
                cost_estimate=0.0,
                success=False,
                error_message=str(e)
            )

    def _get_api_key(self, env_var: str) -> Optional[str]:
        """获取API密钥（支持环境变量和配置文件）"""
        # 优先从环境变量获取
        api_key = os.environ.get(env_var)
        if api_key:
            return api_key

        # 从配置文件获取（加密存储）
        security_config = self.config_manager.get_security_config()
        encrypted_keys = security_config.get("encrypted_api_keys", {})

        if env_var in encrypted_keys:
            # 这里应该实现解密逻辑
            return self._decrypt_api_key(encrypted_keys[env_var])

        return None

    async def _simulate_api_call(
        self,
        model_config: APIModelConfig,
        content: str,
        api_key: str
    ) -> str:
        """模拟API调用（实际实现中替换为真实API调用）"""

        # 模拟不同模型的响应特点
        if model_config.provider == AIModelProvider.DEEPSEEK:
            return f"DeepSeek-R1分析结果：{content[:100]}... [高质量推理分析]"
        elif model_config.provider == AIModelProvider.QWEN:
            return f"QwenLong长文档分析：{content[:200]}... [详细文档理解]"
        elif model_config.provider == AIModelProvider.GEMINI:
            return f"Gemini多模态分析：{content[:150]}... [创新性见解]"
        else:
            return f"GLM成本优化分析：{content[:80]}... [高效分析]"

    def _calculate_response_confidence(
        self,
        response_content: str,
        target_confidence: float
    ) -> float:
        """计算响应置信度"""
        # 基于响应长度、关键词密度等计算置信度
        base_confidence = min(len(response_content) / 500, 1.0)  # 基础置信度

        # 检查关键质量指标
        quality_indicators = ["分析", "结论", "建议", "验证", "确认"]
        quality_score = sum(1 for indicator in quality_indicators
                          if indicator in response_content) / len(quality_indicators)

        # 综合置信度计算
        overall_confidence = (base_confidence * 0.6 + quality_score * 0.4)

        return min(overall_confidence, target_confidence)

    def _calculate_cost(
        self,
        input_length: int,
        output_length: int,
        model_config: APIModelConfig
    ) -> float:
        """计算API调用成本"""
        total_tokens = (input_length + output_length) / 4  # 粗略估算token数
        cost = (total_tokens / 1000000) * model_config.cost_per_million_tokens
        return round(cost, 6)

    def _decrypt_api_key(self, encrypted_key: str) -> str:
        """解密API密钥（需要实现具体加密算法）"""
        # 这里应该实现实际的解密逻辑
        return encrypted_key  # 临时返回，实际需要解密

    def get_api_statistics(self) -> Dict[str, Any]:
        """获取API使用统计"""
        if not self.request_history:
            return {"total_requests": 0}

        successful_requests = [r for r in self.request_history if r.success]

        return {
            "total_requests": len(self.request_history),
            "successful_requests": len(successful_requests),
            "success_rate": len(successful_requests) / len(self.request_history),
            "average_confidence": sum(r.confidence_score for r in successful_requests) / len(successful_requests) if successful_requests else 0,
            "total_cost": sum(r.cost_estimate for r in self.request_history),
            "average_processing_time": sum(r.processing_time for r in self.request_history) / len(self.request_history)
        }
```

## 📊 监控系统

### V4性能监控引擎（基于核心算法监控需求）

```python
# src/infrastructure/monitoring/performance_monitor.py
# 基于V4核心算法的性能监控系统
from typing import Dict, List, Optional, Any
import time
import asyncio
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import statistics

class MetricType(Enum):
    """监控指标类型"""
    ALGORITHM_PERFORMANCE = "algorithm_performance"
    CONFIDENCE_SCORE = "confidence_score"
    API_RESPONSE_TIME = "api_response_time"
    MEMORY_USAGE = "memory_usage"
    ERROR_RATE = "error_rate"

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"

@dataclass
class PerformanceMetric:
    """性能指标"""
    metric_type: MetricType
    value: float
    timestamp: datetime
    component: str
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Alert:
    """告警信息"""
    alert_id: str
    level: AlertLevel
    message: str
    component: str
    timestamp: datetime
    resolved: bool = False

class V4PerformanceMonitor:
    """V4性能监控器（专注核心算法监控）"""

    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.metrics_history: List[PerformanceMetric] = []
        self.active_alerts: List[Alert] = []
        self.monitoring_enabled = True

        # 性能阈值配置（基于V4核心算法要求）
        self.performance_thresholds = {
            "panoramic_algorithm_time": 1.0,      # 全景算法≤1秒
            "ai_dispatch_algorithm_time": 0.5,    # AI派发算法≤0.5秒
            "confidence_algorithm_time": 0.1,     # 置信度算法≤0.1秒
            "scaffolding_algorithm_time": 2.0,    # 脚手架算法≤2秒
            "version_detection_algorithm_time": 0.5,  # 版本检测≤0.5秒
            "confidence_score_threshold": 0.95,   # 95%置信度硬性要求
            "memory_usage_limit": 4096,           # 内存使用≤4GB
            "error_rate_threshold": 0.05          # 错误率≤5%
        }

    async def record_algorithm_performance(
        self,
        algorithm_name: str,
        execution_time: float,
        confidence_score: float,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """记录核心算法性能指标"""

        if not self.monitoring_enabled:
            return

        # 记录执行时间指标
        time_metric = PerformanceMetric(
            metric_type=MetricType.ALGORITHM_PERFORMANCE,
            value=execution_time,
            timestamp=datetime.now(),
            component=algorithm_name,
            metadata=metadata or {}
        )
        self.metrics_history.append(time_metric)

        # 记录置信度指标
        confidence_metric = PerformanceMetric(
            metric_type=MetricType.CONFIDENCE_SCORE,
            value=confidence_score,
            timestamp=datetime.now(),
            component=algorithm_name,
            metadata=metadata or {}
        )
        self.metrics_history.append(confidence_metric)

        # 检查性能阈值
        await self._check_performance_thresholds(algorithm_name, execution_time, confidence_score)

    async def record_api_performance(
        self,
        provider: str,
        response_time: float,
        success: bool,
        cost: float
    ):
        """记录API性能指标"""

        if not self.monitoring_enabled:
            return

        # 记录响应时间
        response_metric = PerformanceMetric(
            metric_type=MetricType.API_RESPONSE_TIME,
            value=response_time,
            timestamp=datetime.now(),
            component=f"api_{provider}",
            metadata={"success": success, "cost": cost}
        )
        self.metrics_history.append(response_metric)

        # 记录错误率
        error_metric = PerformanceMetric(
            metric_type=MetricType.ERROR_RATE,
            value=0.0 if success else 1.0,
            timestamp=datetime.now(),
            component=f"api_{provider}",
            metadata={"cost": cost}
        )
        self.metrics_history.append(error_metric)

    async def _check_performance_thresholds(
        self,
        algorithm_name: str,
        execution_time: float,
        confidence_score: float
    ):
        """检查性能阈值并生成告警"""

        # 检查算法执行时间
        time_threshold_key = f"{algorithm_name}_time"
        if time_threshold_key in self.performance_thresholds:
            threshold = self.performance_thresholds[time_threshold_key]
            if execution_time > threshold:
                await self._create_alert(
                    AlertLevel.WARNING,
                    f"{algorithm_name}执行时间{execution_time:.2f}s超过阈值{threshold}s",
                    algorithm_name
                )

        # 检查95%置信度硬性要求
        confidence_threshold = self.performance_thresholds["confidence_score_threshold"]
        if confidence_score < confidence_threshold:
            await self._create_alert(
                AlertLevel.CRITICAL,
                f"{algorithm_name}置信度{confidence_score:.3f}未达到95%硬性要求",
                algorithm_name
            )

    async def _create_alert(self, level: AlertLevel, message: str, component: str):
        """创建告警"""
        alert = Alert(
            alert_id=f"alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            level=level,
            message=message,
            component=component,
            timestamp=datetime.now()
        )

        self.active_alerts.append(alert)

        # 如果是关键告警，立即通知
        if level == AlertLevel.CRITICAL:
            await self._send_critical_notification(alert)

    async def _send_critical_notification(self, alert: Alert):
        """发送关键告警通知"""
        # 这里可以集成邮件、短信、Slack等通知方式
        print(f"🚨 CRITICAL ALERT: {alert.message}")
        print(f"   Component: {alert.component}")
        print(f"   Time: {alert.timestamp}")

        # 如果是置信度不达标，提供具体建议
        if "置信度" in alert.message and "95%" in alert.message:
            print("   建议：当前算法测试达不到95%置信度，不符合设计要求，需要重新考虑实现方案")

    def get_algorithm_performance_summary(self, algorithm_name: str) -> Dict[str, Any]:
        """获取算法性能摘要"""

        # 筛选指定算法的性能指标
        algorithm_metrics = [
            m for m in self.metrics_history
            if m.component == algorithm_name and m.metric_type == MetricType.ALGORITHM_PERFORMANCE
        ]

        confidence_metrics = [
            m for m in self.metrics_history
            if m.component == algorithm_name and m.metric_type == MetricType.CONFIDENCE_SCORE
        ]

        if not algorithm_metrics:
            return {"algorithm": algorithm_name, "no_data": True}

        # 计算统计指标
        execution_times = [m.value for m in algorithm_metrics]
        confidence_scores = [m.value for m in confidence_metrics]

        return {
            "algorithm": algorithm_name,
            "total_executions": len(algorithm_metrics),
            "average_execution_time": statistics.mean(execution_times),
            "max_execution_time": max(execution_times),
            "min_execution_time": min(execution_times),
            "average_confidence": statistics.mean(confidence_scores) if confidence_scores else 0,
            "min_confidence": min(confidence_scores) if confidence_scores else 0,
            "confidence_below_95_percent": len([c for c in confidence_scores if c < 0.95]),
            "performance_trend": self._calculate_performance_trend(execution_times),
            "meets_performance_requirements": all(t <= self.performance_thresholds.get(f"{algorithm_name}_time", float('inf')) for t in execution_times),
            "meets_confidence_requirements": all(c >= 0.95 for c in confidence_scores)
        }

    def _calculate_performance_trend(self, values: List[float]) -> str:
        """计算性能趋势"""
        if len(values) < 2:
            return "insufficient_data"

        # 简单的趋势分析：比较前半部分和后半部分的平均值
        mid_point = len(values) // 2
        first_half_avg = statistics.mean(values[:mid_point])
        second_half_avg = statistics.mean(values[mid_point:])

        if second_half_avg < first_half_avg * 0.95:
            return "improving"
        elif second_half_avg > first_half_avg * 1.05:
            return "degrading"
        else:
            return "stable"

    def get_system_health_report(self) -> Dict[str, Any]:
        """获取系统健康报告"""

        # 统计各类告警
        alert_counts = {}
        for level in AlertLevel:
            alert_counts[level.value] = len([a for a in self.active_alerts if a.level == level and not a.resolved])

        # 计算整体健康评分
        health_score = self._calculate_health_score()

        # 获取核心算法性能状态
        core_algorithms = [
            "panoramic_algorithm",
            "ai_dispatch_algorithm",
            "confidence_algorithm",
            "scaffolding_algorithm",
            "version_detection_algorithm"
        ]

        algorithm_status = {}
        for algorithm in core_algorithms:
            summary = self.get_algorithm_performance_summary(algorithm)
            algorithm_status[algorithm] = {
                "performance_ok": summary.get("meets_performance_requirements", False),
                "confidence_ok": summary.get("meets_confidence_requirements", False),
                "average_confidence": summary.get("average_confidence", 0)
            }

        return {
            "health_score": health_score,
            "alert_counts": alert_counts,
            "algorithm_status": algorithm_status,
            "critical_issues": [a.message for a in self.active_alerts if a.level == AlertLevel.CRITICAL and not a.resolved],
            "monitoring_enabled": self.monitoring_enabled,
            "total_metrics_collected": len(self.metrics_history),
            "report_timestamp": datetime.now().isoformat()
        }

    def _calculate_health_score(self) -> float:
        """计算系统健康评分（0-100）"""
        base_score = 100.0

        # 根据告警扣分
        for alert in self.active_alerts:
            if not alert.resolved:
                if alert.level == AlertLevel.CRITICAL:
                    base_score -= 20
                elif alert.level == AlertLevel.WARNING:
                    base_score -= 5
                else:
                    base_score -= 1

        return max(0.0, base_score)
```

## 🔐 安全系统

### V4安全管理引擎（API密钥管理、访问控制、数据保护）

```python
# src/infrastructure/security/security_manager.py
# 基于V4设计文档的企业级安全管理系统
from typing import Dict, List, Optional, Any
import hashlib
import secrets
import base64
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json

class SecurityLevel(Enum):
    """安全级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    ENTERPRISE = "enterprise"

class AccessLevel(Enum):
    """访问级别"""
    READ_ONLY = "read_only"
    READ_WRITE = "read_write"
    ADMIN = "admin"
    SYSTEM = "system"

@dataclass
class APIKeyInfo:
    """API密钥信息"""
    key_id: str
    provider: str
    encrypted_key: str
    created_at: datetime
    last_used: Optional[datetime] = None
    usage_count: int = 0
    is_active: bool = True

@dataclass
class AccessRecord:
    """访问记录"""
    user_id: str
    resource: str
    action: str
    timestamp: datetime
    success: bool
    ip_address: Optional[str] = None

class V4SecurityManager:
    """V4安全管理器（企业级安全标准）"""

    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.master_key = self._generate_or_load_master_key()
        self.api_keys: Dict[str, APIKeyInfo] = {}
        self.access_records: List[AccessRecord] = []
        self.security_level = SecurityLevel.ENTERPRISE

        # 安全配置
        self.security_config = {
            "encryption_algorithm": "AES-256-GCM",
            "key_rotation_days": 90,
            "max_failed_attempts": 3,
            "session_timeout_minutes": 30,
            "audit_retention_days": 365
        }

    def _generate_or_load_master_key(self) -> str:
        """生成或加载主密钥"""
        # 在实际实现中，主密钥应该从安全的密钥管理服务获取
        # 这里使用简化实现
        return secrets.token_hex(32)  # 256位密钥

    def encrypt_api_key(self, api_key: str, provider: str) -> str:
        """加密API密钥（AES-256-GCM）"""
        try:
            # 简化的加密实现（实际应使用cryptography库）
            key_bytes = api_key.encode('utf-8')

            # 使用主密钥和提供商信息生成加密密钥
            encryption_key = hashlib.sha256(
                (self.master_key + provider).encode('utf-8')
            ).digest()

            # 简化加密（实际应使用AES-GCM）
            encrypted = base64.b64encode(key_bytes).decode('utf-8')

            return encrypted

        except Exception as e:
            raise SecurityError(f"API密钥加密失败: {e}")

    def decrypt_api_key(self, encrypted_key: str, provider: str) -> str:
        """解密API密钥"""
        try:
            # 简化的解密实现
            decrypted_bytes = base64.b64decode(encrypted_key.encode('utf-8'))
            return decrypted_bytes.decode('utf-8')

        except Exception as e:
            raise SecurityError(f"API密钥解密失败: {e}")

    def store_api_key(self, provider: str, api_key: str) -> str:
        """安全存储API密钥"""

        # 生成密钥ID
        key_id = f"{provider}_{secrets.token_hex(8)}"

        # 加密密钥
        encrypted_key = self.encrypt_api_key(api_key, provider)

        # 存储密钥信息
        key_info = APIKeyInfo(
            key_id=key_id,
            provider=provider,
            encrypted_key=encrypted_key,
            created_at=datetime.now()
        )

        self.api_keys[key_id] = key_info

        # 记录安全审计日志
        self._log_security_event(
            "api_key_stored",
            f"API密钥已安全存储: {provider}",
            {"provider": provider, "key_id": key_id}
        )

        return key_id

    def get_api_key(self, provider: str) -> Optional[str]:
        """获取API密钥"""

        # 查找指定提供商的活跃密钥
        for key_info in self.api_keys.values():
            if key_info.provider == provider and key_info.is_active:
                try:
                    # 解密密钥
                    decrypted_key = self.decrypt_api_key(
                        key_info.encrypted_key, provider
                    )

                    # 更新使用记录
                    key_info.last_used = datetime.now()
                    key_info.usage_count += 1

                    # 记录访问日志
                    self._log_access_record(
                        "system",
                        f"api_key_{provider}",
                        "read",
                        True
                    )

                    return decrypted_key

                except Exception as e:
                    self._log_security_event(
                        "api_key_access_failed",
                        f"API密钥访问失败: {provider}",
                        {"error": str(e)}
                    )
                    return None

        return None

    def validate_access(
        self,
        user_id: str,
        resource: str,
        action: str,
        access_level: AccessLevel = AccessLevel.READ_ONLY
    ) -> bool:
        """验证访问权限"""

        # 检查用户权限（简化实现）
        user_permissions = self._get_user_permissions(user_id)

        # 验证访问级别
        required_level = self._get_required_access_level(resource, action)
        has_permission = self._check_permission_level(user_permissions, required_level)

        # 记录访问尝试
        self._log_access_record(user_id, resource, action, has_permission)

        if not has_permission:
            self._log_security_event(
                "access_denied",
                f"用户 {user_id} 访问 {resource} 被拒绝",
                {"user_id": user_id, "resource": resource, "action": action}
            )

        return has_permission

    def _get_user_permissions(self, user_id: str) -> Dict[str, Any]:
        """获取用户权限（简化实现）"""
        # 在实际实现中，这里应该从用户管理系统获取权限
        default_permissions = {
            "access_level": AccessLevel.READ_WRITE,
            "resources": ["algorithms", "api", "monitoring"],
            "restrictions": []
        }
        return default_permissions

    def _get_required_access_level(self, resource: str, action: str) -> AccessLevel:
        """获取资源操作所需的访问级别"""
        access_requirements = {
            ("api_keys", "read"): AccessLevel.ADMIN,
            ("api_keys", "write"): AccessLevel.ADMIN,
            ("algorithms", "read"): AccessLevel.READ_ONLY,
            ("algorithms", "write"): AccessLevel.READ_WRITE,
            ("monitoring", "read"): AccessLevel.READ_ONLY,
            ("system_config", "write"): AccessLevel.SYSTEM
        }

        return access_requirements.get((resource, action), AccessLevel.READ_ONLY)

    def _check_permission_level(
        self,
        user_permissions: Dict[str, Any],
        required_level: AccessLevel
    ) -> bool:
        """检查权限级别"""
        user_level = user_permissions.get("access_level", AccessLevel.READ_ONLY)

        # 权限级别层次：READ_ONLY < READ_WRITE < ADMIN < SYSTEM
        level_hierarchy = {
            AccessLevel.READ_ONLY: 1,
            AccessLevel.READ_WRITE: 2,
            AccessLevel.ADMIN: 3,
            AccessLevel.SYSTEM: 4
        }

        return level_hierarchy.get(user_level, 0) >= level_hierarchy.get(required_level, 0)

    def _log_access_record(
        self,
        user_id: str,
        resource: str,
        action: str,
        success: bool,
        ip_address: Optional[str] = None
    ):
        """记录访问日志"""
        record = AccessRecord(
            user_id=user_id,
            resource=resource,
            action=action,
            timestamp=datetime.now(),
            success=success,
            ip_address=ip_address
        )

        self.access_records.append(record)

        # 清理过期记录
        self._cleanup_old_records()

    def _log_security_event(self, event_type: str, message: str, metadata: Dict[str, Any]):
        """记录安全事件"""
        security_event = {
            "event_type": event_type,
            "message": message,
            "metadata": metadata,
            "timestamp": datetime.now().isoformat(),
            "security_level": self.security_level.value
        }

        # 在实际实现中，这里应该写入安全日志系统
        print(f"🔐 SECURITY EVENT: {json.dumps(security_event, indent=2)}")

    def _cleanup_old_records(self):
        """清理过期访问记录"""
        retention_days = self.security_config["audit_retention_days"]
        cutoff_date = datetime.now() - timedelta(days=retention_days)

        self.access_records = [
            record for record in self.access_records
            if record.timestamp > cutoff_date
        ]

    def get_security_report(self) -> Dict[str, Any]:
        """获取安全报告"""

        # 统计访问记录
        total_accesses = len(self.access_records)
        failed_accesses = len([r for r in self.access_records if not r.success])

        # 统计API密钥使用
        active_keys = len([k for k in self.api_keys.values() if k.is_active])
        total_key_usage = sum(k.usage_count for k in self.api_keys.values())

        # 检查安全风险
        security_risks = self._assess_security_risks()

        return {
            "security_level": self.security_level.value,
            "api_keys": {
                "total_active": active_keys,
                "total_usage": total_key_usage,
                "providers": list(set(k.provider for k in self.api_keys.values()))
            },
            "access_control": {
                "total_accesses": total_accesses,
                "failed_accesses": failed_accesses,
                "success_rate": (total_accesses - failed_accesses) / total_accesses if total_accesses > 0 else 1.0
            },
            "security_risks": security_risks,
            "encryption_status": "AES-256-GCM",
            "audit_retention": f"{self.security_config['audit_retention_days']} days",
            "report_timestamp": datetime.now().isoformat()
        }

    def _assess_security_risks(self) -> List[str]:
        """评估安全风险"""
        risks = []

        # 检查密钥轮换
        for key_info in self.api_keys.values():
            days_old = (datetime.now() - key_info.created_at).days
            if days_old > self.security_config["key_rotation_days"]:
                risks.append(f"API密钥 {key_info.provider} 需要轮换（已使用{days_old}天）")

        # 检查失败访问率
        recent_records = [
            r for r in self.access_records
            if r.timestamp > datetime.now() - timedelta(hours=24)
        ]

        if recent_records:
            failed_rate = len([r for r in recent_records if not r.success]) / len(recent_records)
            if failed_rate > 0.1:  # 10%失败率阈值
                risks.append(f"24小时内访问失败率过高: {failed_rate:.1%}")

        return risks

class SecurityError(Exception):
    """安全相关异常"""
    pass
```

## 🤝 人机命令接口

### V4人机交互管理器（95%置信度失败反馈机制）

```python
# src/infrastructure/interface/human_machine_interface.py
# 基于95%置信度验证的人机交互系统
from typing import Dict, List, Optional, Any, Callable
import asyncio
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import json

class InteractionType(Enum):
    """交互类型"""
    CONFIDENCE_VALIDATION = "confidence_validation"
    ALGORITHM_FAILURE = "algorithm_failure"
    DESIGN_MISMATCH = "design_mismatch"
    MANUAL_INTERVENTION = "manual_intervention"
    PROGRESS_UPDATE = "progress_update"

class ResponseType(Enum):
    """响应类型"""
    APPROVE = "approve"
    REJECT = "reject"
    RETRY = "retry"
    MODIFY = "modify"
    ABORT = "abort"

@dataclass
class InteractionRequest:
    """交互请求"""
    request_id: str
    interaction_type: InteractionType
    title: str
    message: str
    context: Dict[str, Any]
    options: List[str]
    timestamp: datetime
    urgent: bool = False

@dataclass
class InteractionResponse:
    """交互响应"""
    request_id: str
    response_type: ResponseType
    user_input: str
    additional_data: Dict[str, Any]
    timestamp: datetime

class V4HumanMachineInterface:
    """V4人机交互管理器（专注95%置信度验证）"""

    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.pending_requests: Dict[str, InteractionRequest] = {}
        self.interaction_history: List[InteractionResponse] = []
        self.notification_callbacks: List[Callable] = []

        # 95%置信度失败处理配置
        self.confidence_failure_config = {
            "auto_retry_threshold": 0.90,  # 90%以上自动重试
            "manual_review_threshold": 0.85,  # 85%以上人工审核
            "abort_threshold": 0.80,  # 80%以下建议终止
            "max_retry_attempts": 3
        }

    async def request_confidence_validation(
        self,
        algorithm_name: str,
        current_confidence: float,
        target_confidence: float,
        test_results: Dict[str, Any]
    ) -> InteractionResponse:
        """请求95%置信度验证（核心功能）"""

        # 生成请求ID
        request_id = f"confidence_{algorithm_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 判断置信度状态
        if current_confidence >= target_confidence:
            # 置信度达标，自动通过
            return InteractionResponse(
                request_id=request_id,
                response_type=ResponseType.APPROVE,
                user_input="自动通过：置信度达标",
                additional_data={"auto_approved": True},
                timestamp=datetime.now()
            )

        # 置信度不达标，需要人工干预
        confidence_gap = target_confidence - current_confidence

        # 构建详细的失败报告
        failure_message = self._generate_confidence_failure_message(
            algorithm_name, current_confidence, target_confidence, test_results
        )

        # 创建交互请求
        interaction_request = InteractionRequest(
            request_id=request_id,
            interaction_type=InteractionType.CONFIDENCE_VALIDATION,
            title=f"⚠️ {algorithm_name}算法置信度验证失败",
            message=failure_message,
            context={
                "algorithm_name": algorithm_name,
                "current_confidence": current_confidence,
                "target_confidence": target_confidence,
                "confidence_gap": confidence_gap,
                "test_results": test_results
            },
            options=self._get_confidence_failure_options(current_confidence),
            timestamp=datetime.now(),
            urgent=True  # 置信度失败是紧急事件
        )

        # 存储请求
        self.pending_requests[request_id] = interaction_request

        # 发送通知
        await self._send_notification(interaction_request)

        # 等待用户响应
        response = await self._wait_for_response(request_id, timeout=300)  # 5分钟超时

        return response

    def _generate_confidence_failure_message(
        self,
        algorithm_name: str,
        current_confidence: float,
        target_confidence: float,
        test_results: Dict[str, Any]
    ) -> str:
        """生成置信度失败详细消息"""

        message = f"""
🚨 算法测试达不到95%置信度，不符合设计要求，需要重新考虑

📊 置信度分析：
• 算法名称：{algorithm_name}
• 当前置信度：{current_confidence:.3f} ({current_confidence*100:.1f}%)
• 目标置信度：{target_confidence:.3f} ({target_confidence*100:.1f}%)
• 置信度差距：{target_confidence - current_confidence:.3f} ({(target_confidence - current_confidence)*100:.1f}%)

🔍 测试结果详情：
"""

        # 添加测试结果详情
        for key, value in test_results.items():
            if isinstance(value, float):
                message += f"• {key}: {value:.3f}\n"
            else:
                message += f"• {key}: {value}\n"

        # 添加建议
        if current_confidence >= self.confidence_failure_config["auto_retry_threshold"]:
            message += "\n💡 建议：置信度接近目标，可尝试优化算法参数后重试"
        elif current_confidence >= self.confidence_failure_config["manual_review_threshold"]:
            message += "\n💡 建议：置信度偏低，需要人工审核算法实现或调整设计要求"
        else:
            message += "\n⚠️ 建议：置信度严重不足，建议重新设计算法或降低复杂度"

        message += f"\n\n📋 根据V4设计文档，所有核心算法必须达到95%置信度硬性要求"

        return message

    def _get_confidence_failure_options(self, current_confidence: float) -> List[str]:
        """获取置信度失败处理选项"""

        options = []

        if current_confidence >= self.confidence_failure_config["auto_retry_threshold"]:
            options.extend([
                "重试：优化算法参数后重新测试",
                "调整：微调算法实现",
                "接受：临时接受当前置信度（需要说明理由）"
            ])
        elif current_confidence >= self.confidence_failure_config["manual_review_threshold"]:
            options.extend([
                "重新设计：重新设计算法架构",
                "降低复杂度：简化算法实现",
                "专家评审：请专家评审设计方案",
                "暂停：暂停当前实施，重新评估"
            ])
        else:
            options.extend([
                "终止：终止当前算法实施",
                "重新设计：从头重新设计",
                "降级处理：降低功能要求",
                "寻求帮助：联系技术专家"
            ])

        options.append("查看详细日志：获取完整测试日志")

        return options

    async def notify_design_mismatch(
        self,
        component: str,
        expected_behavior: str,
        actual_behavior: str,
        impact_assessment: str
    ) -> InteractionResponse:
        """通知设计不匹配问题"""

        request_id = f"design_mismatch_{component}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        message = f"""
🔄 检测到实现与设计文档不匹配

📋 组件信息：
• 组件名称：{component}
• 预期行为：{expected_behavior}
• 实际行为：{actual_behavior}
• 影响评估：{impact_assessment}

⚠️ 这可能导致95%置信度验证失败，需要立即处理
"""

        interaction_request = InteractionRequest(
            request_id=request_id,
            interaction_type=InteractionType.DESIGN_MISMATCH,
            title=f"🔄 {component}实现与设计不匹配",
            message=message,
            context={
                "component": component,
                "expected_behavior": expected_behavior,
                "actual_behavior": actual_behavior,
                "impact_assessment": impact_assessment
            },
            options=[
                "修正实现：调整代码以匹配设计",
                "更新设计：更新设计文档以匹配实现",
                "深入分析：进行详细的差异分析",
                "暂停实施：暂停直到问题解决"
            ],
            timestamp=datetime.now(),
            urgent=True
        )

        self.pending_requests[request_id] = interaction_request
        await self._send_notification(interaction_request)

        return await self._wait_for_response(request_id, timeout=600)  # 10分钟超时

    async def request_manual_intervention(
        self,
        reason: str,
        context: Dict[str, Any],
        suggested_actions: List[str]
    ) -> InteractionResponse:
        """请求人工干预"""

        request_id = f"manual_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        message = f"""
🤝 需要人工干预

📋 干预原因：{reason}

💡 建议操作：
"""
        for i, action in enumerate(suggested_actions, 1):
            message += f"{i}. {action}\n"

        interaction_request = InteractionRequest(
            request_id=request_id,
            interaction_type=InteractionType.MANUAL_INTERVENTION,
            title="🤝 需要人工干预",
            message=message,
            context=context,
            options=suggested_actions + ["其他：自定义操作"],
            timestamp=datetime.now()
        )

        self.pending_requests[request_id] = interaction_request
        await self._send_notification(interaction_request)

        return await self._wait_for_response(request_id, timeout=1800)  # 30分钟超时

    async def _send_notification(self, request: InteractionRequest):
        """发送通知"""

        # 控制台通知
        print(f"\n{'='*60}")
        print(f"🔔 V4系统交互请求")
        print(f"{'='*60}")
        print(f"标题：{request.title}")
        print(f"类型：{request.interaction_type.value}")
        print(f"时间：{request.timestamp}")
        print(f"紧急：{'是' if request.urgent else '否'}")
        print(f"\n消息：\n{request.message}")
        print(f"\n可选操作：")
        for i, option in enumerate(request.options, 1):
            print(f"  {i}. {option}")
        print(f"{'='*60}\n")

        # 调用注册的回调函数
        for callback in self.notification_callbacks:
            try:
                await callback(request)
            except Exception as e:
                print(f"通知回调失败: {e}")

    async def _wait_for_response(
        self,
        request_id: str,
        timeout: int = 300
    ) -> InteractionResponse:
        """等待用户响应"""

        # 简化实现：在实际系统中，这里应该实现真正的异步等待机制
        # 现在返回一个默认响应，表示需要人工处理

        default_response = InteractionResponse(
            request_id=request_id,
            response_type=ResponseType.RETRY,
            user_input="系统默认：需要人工审核和处理",
            additional_data={
                "timeout": timeout,
                "auto_generated": True,
                "requires_manual_review": True
            },
            timestamp=datetime.now()
        )

        # 记录响应历史
        self.interaction_history.append(default_response)

        # 清理已处理的请求
        if request_id in self.pending_requests:
            del self.pending_requests[request_id]

        return default_response

    def register_notification_callback(self, callback: Callable):
        """注册通知回调函数"""
        self.notification_callbacks.append(callback)

    def get_interaction_summary(self) -> Dict[str, Any]:
        """获取交互摘要"""

        # 统计交互类型
        interaction_counts = {}
        for interaction_type in InteractionType:
            count = len([r for r in self.interaction_history
                        if self.pending_requests.get(r.request_id, {}).get('interaction_type') == interaction_type])
            interaction_counts[interaction_type.value] = count

        # 统计响应类型
        response_counts = {}
        for response_type in ResponseType:
            count = len([r for r in self.interaction_history if r.response_type == response_type])
            response_counts[response_type.value] = count

        return {
            "total_interactions": len(self.interaction_history),
            "pending_requests": len(self.pending_requests),
            "interaction_types": interaction_counts,
            "response_types": response_counts,
            "urgent_pending": len([r for r in self.pending_requests.values() if r.urgent]),
            "confidence_failures": interaction_counts.get("confidence_validation", 0),
            "design_mismatches": interaction_counts.get("design_mismatch", 0),
            "manual_interventions": interaction_counts.get("manual_intervention", 0)
        }
```

## 🧪 基础设施集成测试

### V4基础设施集成测试套件

```python
# tests/integration/test_v4_infrastructure_integration.py
# V4基础设施组件集成测试
import pytest
import asyncio
from datetime import datetime
from src.infrastructure.storage.data_storage_engine import V4DataStorageEngine
from src.infrastructure.config.config_manager import V4ConfigManager
from src.infrastructure.api.unified_api_adapter import V4UnifiedAPIAdapter, APIRequest, TaskType
from src.infrastructure.monitoring.performance_monitor import V4PerformanceMonitor
from src.infrastructure.security.security_manager import V4SecurityManager
from src.infrastructure.interface.human_machine_interface import V4HumanMachineInterface

class TestV4InfrastructureIntegration:
    """V4基础设施集成测试（确保95%置信度支持）"""

    @pytest.fixture
    async def infrastructure_components(self):
        """初始化基础设施组件"""

        # 配置管理器
        config_manager = V4ConfigManager("test_config.json")

        # 存储引擎
        storage_engine = V4DataStorageEngine(config_manager.get_storage_config())

        # API适配器
        api_adapter = V4UnifiedAPIAdapter(config_manager)

        # 性能监控器
        performance_monitor = V4PerformanceMonitor(config_manager)

        # 安全管理器
        security_manager = V4SecurityManager(config_manager)

        # 人机接口
        human_interface = V4HumanMachineInterface(config_manager)

        return {
            "config_manager": config_manager,
            "storage_engine": storage_engine,
            "api_adapter": api_adapter,
            "performance_monitor": performance_monitor,
            "security_manager": security_manager,
            "human_interface": human_interface
        }

    @pytest.mark.asyncio
    async def test_infrastructure_initialization(self, infrastructure_components):
        """测试基础设施初始化"""

        components = infrastructure_components

        # 验证所有组件都已正确初始化
        assert components["config_manager"] is not None
        assert components["storage_engine"] is not None
        assert components["api_adapter"] is not None
        assert components["performance_monitor"] is not None
        assert components["security_manager"] is not None
        assert components["human_interface"] is not None

        # 验证配置加载
        config = components["config_manager"]
        assert config.get_confidence_threshold() == 0.95  # 95%置信度硬性要求
        assert config.is_v3_reuse_enabled() is True

        print("✅ 基础设施初始化测试通过")

    @pytest.mark.asyncio
    async def test_95_percent_confidence_failure_handling(self, infrastructure_components):
        """测试95%置信度失败处理机制（核心功能）"""

        human_interface = infrastructure_components["human_interface"]
        performance_monitor = infrastructure_components["performance_monitor"]

        # 模拟算法置信度不达标场景
        algorithm_name = "test_algorithm"
        current_confidence = 0.87  # 87%，未达到95%要求
        target_confidence = 0.95

        test_results = {
            "algorithm_accuracy": 0.85,
            "test_coverage": 0.90,
            "performance_score": 0.88,
            "v3_compatibility": 0.82
        }

        # 请求置信度验证（应该失败）
        response = await human_interface.request_confidence_validation(
            algorithm_name, current_confidence, target_confidence, test_results
        )

        # 验证失败处理
        assert response is not None
        assert response.additional_data.get("requires_manual_review") is True

        # 验证性能监控记录了置信度不达标事件
        await performance_monitor.record_algorithm_performance(
            algorithm_name, 1.0, current_confidence
        )

        # 检查是否生成了关键告警
        health_report = performance_monitor.get_system_health_report()
        critical_issues = health_report["critical_issues"]

        # 应该有置信度不达标的关键告警
        confidence_alerts = [issue for issue in critical_issues if "置信度" in issue and "95%" in issue]
        assert len(confidence_alerts) > 0

        print("✅ 95%置信度失败处理测试通过")
        print(f"   置信度差距: {target_confidence - current_confidence:.3f}")
        print(f"   生成告警: {len(confidence_alerts)}个")

    @pytest.mark.asyncio
    async def test_api_security_integration(self, infrastructure_components):
        """测试API和安全系统集成"""

        api_adapter = infrastructure_components["api_adapter"]
        security_manager = infrastructure_components["security_manager"]

        # 存储测试API密钥
        test_provider = "test_provider"
        test_api_key = "test_key_12345"

        key_id = security_manager.store_api_key(test_provider, test_api_key)
        assert key_id is not None

        # 验证API适配器可以安全获取密钥
        retrieved_key = security_manager.get_api_key(test_provider)
        assert retrieved_key == test_api_key

        # 测试API请求路由
        test_request = APIRequest(
            task_type=TaskType.COMPLEX_REASONING,
            content="测试复杂推理任务",
            requirements={"confidence_threshold": 0.95}
        )

        selected_provider = await api_adapter.route_request(test_request)
        assert selected_provider is not None

        # 验证安全访问控制
        access_granted = security_manager.validate_access(
            "test_user", "api_keys", "read"
        )
        # 根据默认权限配置，应该被拒绝（需要ADMIN权限）
        assert access_granted is False

        print("✅ API安全集成测试通过")

    @pytest.mark.asyncio
    async def test_storage_monitoring_integration(self, infrastructure_components):
        """测试存储和监控系统集成"""

        storage_engine = infrastructure_components["storage_engine"]
        performance_monitor = infrastructure_components["performance_monitor"]

        # 存储算法分析结果
        analysis_id = "test_analysis_001"
        algorithm_type = "panoramic_positioning"

        input_data = {"document_path": "test.md", "content": "测试内容"}
        analysis_result = {"position": "核心算法", "confidence": 0.96}
        confidence_score = 0.96

        # 存储结果
        success = storage_engine.store_algorithm_analysis_result(
            analysis_id, algorithm_type, input_data, analysis_result, confidence_score
        )
        assert success is True

        # 记录性能指标
        await performance_monitor.record_algorithm_performance(
            algorithm_type, 0.8, confidence_score
        )

        # 验证可以检索存储的结果
        retrieved_result = storage_engine.get_algorithm_analysis_result(analysis_id)
        assert retrieved_result is not None
        assert retrieved_result["confidence_score"] == confidence_score

        # 验证性能监控记录了指标
        performance_summary = performance_monitor.get_algorithm_performance_summary(algorithm_type)
        assert performance_summary["total_executions"] > 0
        assert performance_summary["meets_confidence_requirements"] is True

        print("✅ 存储监控集成测试通过")

    @pytest.mark.asyncio
    async def test_end_to_end_confidence_workflow(self, infrastructure_components):
        """测试端到端95%置信度工作流"""

        # 模拟完整的算法验证工作流
        storage = infrastructure_components["storage_engine"]
        monitor = infrastructure_components["performance_monitor"]
        human_interface = infrastructure_components["human_interface"]

        algorithm_name = "end_to_end_test_algorithm"

        # 场景1：高置信度算法（应该自动通过）
        high_confidence = 0.97

        response_high = await human_interface.request_confidence_validation(
            algorithm_name, high_confidence, 0.95, {"test": "high_quality"}
        )

        assert response_high.additional_data.get("auto_approved") is True

        # 记录高置信度性能
        await monitor.record_algorithm_performance(algorithm_name, 0.5, high_confidence)

        # 存储高置信度结果
        storage.store_confidence_validation_record(
            f"validation_{algorithm_name}_high",
            algorithm_name,
            high_confidence,
            True,
            {"validation_type": "automatic"}
        )

        # 场景2：低置信度算法（应该触发人工干预）
        low_confidence = 0.82

        response_low = await human_interface.request_confidence_validation(
            algorithm_name, low_confidence, 0.95, {"test": "low_quality"}
        )

        assert response_low.additional_data.get("requires_manual_review") is True

        # 验证系统健康报告反映了置信度问题
        health_report = monitor.get_system_health_report()
        assert len(health_report["critical_issues"]) > 0

        # 验证交互摘要记录了置信度验证请求
        interaction_summary = human_interface.get_interaction_summary()
        assert interaction_summary["confidence_failures"] > 0

        print("✅ 端到端置信度工作流测试通过")
        print(f"   高置信度自动通过: {response_high.additional_data.get('auto_approved')}")
        print(f"   低置信度人工审核: {response_low.additional_data.get('requires_manual_review')}")
        print(f"   系统健康评分: {health_report['health_score']}")
```

## ✅ 基础设施验收标准

### 核心基础设施验收标准
- [ ] **存储系统**: 支持核心算法数据持久化，95%置信度记录完整
- [ ] **API管理系统**: 统一多厂商AI模型接入，智能路由正常
- [ ] **配置管理系统**: 支持95%置信度配置，V3/V3.1复用配置完整
- [ ] **监控系统**: 核心算法性能监控，95%置信度告警机制有效
- [ ] **安全系统**: API密钥安全管理，访问控制机制正常
- [ ] **人机接口**: 95%置信度失败反馈机制，设计不匹配通知有效

### 95%置信度支持验收标准
- [ ] **置信度计算**: 所有基础设施组件支持95%置信度验证
- [ ] **失败处理**: 达不到95%置信度时自动触发人工干预
- [ ] **告警机制**: 置信度不达标时生成关键告警
- [ ] **审计追踪**: 完整记录置信度验证过程和结果

### 集成测试验收标准
- [ ] **组件初始化**: 所有基础设施组件正确初始化
- [ ] **API安全集成**: API管理和安全系统无缝集成
- [ ] **存储监控集成**: 存储和监控系统数据一致性
- [ ] **端到端工作流**: 95%置信度验证工作流完整有效

### 性能验收标准
- [ ] **响应时间**: 基础设施操作响应时间≤100ms
- [ ] **并发支持**: 支持多个核心算法并发访问
- [ ] **资源使用**: 基础设施内存使用≤1GB
- [ ] **错误处理**: 异常情况下系统稳定性≥99%

# ========== 版本管理核心任务 ==========

class VersionManagementCore:
    """
    版本管理核心任务

    @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/17-SQLite全景模型数据库设计.md
    @SECTION: 版本管理核心任务 (lines 856-1063)
    """

    def __init__(self, panoramic_db: PanoramicModelDatabase):
        self.panoramic_db = panoramic_db
        self.version_parser = DocumentVersionParser()

        print("✅ 版本管理核心任务初始化完成")

    async def execute_panoramic_reliability_confirmation(
        self,
        document_path: str,
        user_feedback: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        执行全景图可靠性确认

        核心作用1：保证全景质量
        - 分析全景模型的可靠性评分，确保质量达标
        - 用户确认机制，验证全景模型的真实性和准确性
        - 质量不达标时提供改进建议，持续优化全景质量
        """
        print(f"🔍 执行全景图可靠性确认: {document_path}")

        # 1. 获取当前全景模型
        panoramic_model = self.panoramic_db.get_panoramic_model(document_path)

        if not panoramic_model:
            return {
                "status": "failed",
                "reason": "全景模型不存在",
                "requires_rebuild": True
            }

        # 2. 分析全景模型可靠性
        reliability_analysis = self._analyze_panoramic_reliability(panoramic_model)

        # 3. 检查用户确认状态
        user_confirmation_status = panoramic_model.get("user_confirmation_status", "NOT_CONFIRMED")

        # 4. 根据可靠性分析和用户反馈决定确认状态
        if reliability_analysis["reliability_score"] >= 0.90 and user_confirmation_status == "CONFIRMED":
            confirmation_result = {
                "status": "confirmed",
                "reliability_score": reliability_analysis["reliability_score"],
                "user_confirmed": True,
                "requires_action": False
            }
        elif reliability_analysis["reliability_score"] >= 0.85:
            confirmation_result = {
                "status": "pending_user_confirmation",
                "reliability_score": reliability_analysis["reliability_score"],
                "user_confirmed": False,
                "requires_action": True,
                "recommended_actions": [
                    "请用户确认全景模型的准确性",
                    "验证组件关系的正确性",
                    "确认架构层次定位"
                ]
            }
        else:
            confirmation_result = {
                "status": "reliability_insufficient",
                "reliability_score": reliability_analysis["reliability_score"],
                "user_confirmed": False,
                "requires_action": True,
                "recommended_actions": [
                    "重新执行全景分析",
                    "补充更多架构信息",
                    "优化置信度计算"
                ]
            }

        # 5. 记录确认结果到数据库
        await self._record_reliability_confirmation(document_path, confirmation_result, user_feedback)

        return confirmation_result

    async def execute_architecture_debt_detection(
        self,
        document_path: str = None
    ) -> Dict[str, Any]:
        """
        执行架构负债检测和提醒

        核心作用2：通知用户管理架构代码版本
        - 主动检测架构负债积累，预警架构质量下降
        - 分析版本滞后、置信度退化等关键指标
        - 提供具体改进建议，指导用户进行架构优化
        - 全局架构负债监控，支持系统性架构管理决策
        """
        print(f"🔍 执行架构负债检测: {document_path or '全局检测'}")

        if document_path:
            # 单文档架构负债检测
            return await self._detect_single_document_architecture_debt(document_path)
        else:
            # 全局架构负债检测
            return await self._detect_global_architecture_debt()

    async def _detect_single_document_architecture_debt(self, document_path: str) -> Dict[str, Any]:
        """检测单个文档的架构负债"""

        panoramic_model = self.panoramic_db.get_panoramic_model(document_path)

        if not panoramic_model:
            return {
                "document_path": document_path,
                "debt_score": 1.0,  # 最高负债
                "debt_issues": ["文档缺少全景模型"],
                "severity": "CRITICAL"
            }

        # 分析架构负债指标
        debt_analysis = {
            "version_lag": self._analyze_version_lag(panoramic_model),
            "confidence_degradation": self._analyze_confidence_degradation(panoramic_model),
            "relationship_complexity": self._analyze_relationship_complexity(panoramic_model),
            "verification_gaps": self._analyze_verification_gaps(panoramic_model)
        }

        # 计算综合负债评分
        debt_score = self._calculate_debt_score(debt_analysis)

        # 生成改进建议
        improvement_suggestions = self._generate_debt_improvement_suggestions(debt_analysis)

        # 确定严重程度
        if debt_score >= 0.8:
            severity = "CRITICAL"
        elif debt_score >= 0.6:
            severity = "HIGH"
        elif debt_score >= 0.4:
            severity = "MEDIUM"
        else:
            severity = "LOW"

        debt_result = {
            "document_path": document_path,
            "debt_score": debt_score,
            "debt_analysis": debt_analysis,
            "improvement_suggestions": improvement_suggestions,
            "severity": severity,
            "requires_immediate_action": debt_score >= 0.7
        }

        # 记录架构负债检测结果
        await self._record_architecture_debt_detection(document_path, debt_result)

        return debt_result

    async def _detect_global_architecture_debt(self) -> Dict[str, Any]:
        """检测全局架构负债"""

        # 获取所有全景模型
        all_models = await self._get_all_panoramic_models()

        global_debt_analysis = {
            "total_documents": len(all_models),
            "high_debt_documents": [],
            "average_debt_score": 0.0,
            "critical_issues": [],
            "improvement_priorities": []
        }

        total_debt_score = 0.0

        for model in all_models:
            document_debt = await self._detect_single_document_architecture_debt(model["document_path"])
            total_debt_score += document_debt["debt_score"]

            if document_debt["debt_score"] >= 0.7:
                global_debt_analysis["high_debt_documents"].append({
                    "document_path": model["document_path"],
                    "debt_score": document_debt["debt_score"],
                    "severity": document_debt["severity"]
                })

            if document_debt["severity"] == "CRITICAL":
                global_debt_analysis["critical_issues"].extend(
                    document_debt["improvement_suggestions"]
                )

        if all_models:
            global_debt_analysis["average_debt_score"] = total_debt_score / len(all_models)

        # 生成全局改进优先级
        global_debt_analysis["improvement_priorities"] = self._generate_global_improvement_priorities(
            global_debt_analysis
        )

        return global_debt_analysis

    def _analyze_panoramic_reliability(self, panoramic_model: Dict) -> Dict[str, Any]:
        """分析全景模型可靠性"""

        confidence_score = panoramic_model.get("confidence_score", 0.0)
        quality_metrics = panoramic_model.get("quality_metrics", {})

        # 解析质量指标
        if isinstance(quality_metrics, str):
            import json
            try:
                quality_metrics = json.loads(quality_metrics)
            except:
                quality_metrics = {}

        # 计算可靠性评分
        reliability_factors = {
            "confidence_score": confidence_score,
            "execution_accuracy": quality_metrics.get("overall_execution_accuracy", 0.0),
            "convergence_score": quality_metrics.get("confidence_convergence_score", 0.0),
            "contradiction_reduction": quality_metrics.get("contradiction_reduction_score", 0.0)
        }

        # 加权平均计算可靠性评分
        weights = {"confidence_score": 0.4, "execution_accuracy": 0.3, "convergence_score": 0.2, "contradiction_reduction": 0.1}
        reliability_score = sum(reliability_factors[key] * weights[key] for key in weights)

        return {
            "reliability_score": reliability_score,
            "reliability_factors": reliability_factors,
            "reliability_level": "HIGH" if reliability_score >= 0.9 else "MEDIUM" if reliability_score >= 0.7 else "LOW"
        }

    def _analyze_version_lag(self, panoramic_model: Dict) -> Dict[str, Any]:
        """分析版本滞后情况"""

        current_version = panoramic_model.get("version_number", "V1.0.0")
        last_update = panoramic_model.get("updated_at")

        # 简化的版本滞后分析
        version_lag_score = 0.2  # 假设有轻微版本滞后

        return {
            "current_version": current_version,
            "lag_score": version_lag_score,
            "last_update": last_update,
            "requires_version_update": version_lag_score > 0.3
        }

    def _analyze_confidence_degradation(self, panoramic_model: Dict) -> Dict[str, Any]:
        """分析置信度退化情况"""

        confidence_score = panoramic_model.get("confidence_score", 0.0)

        # 简化的置信度退化分析
        degradation_score = max(0, 0.95 - confidence_score)  # 与95%目标的差距

        return {
            "current_confidence": confidence_score,
            "target_confidence": 0.95,
            "degradation_score": degradation_score,
            "requires_confidence_improvement": degradation_score > 0.1
        }

    def _analyze_relationship_complexity(self, panoramic_model: Dict) -> Dict[str, Any]:
        """分析关系复杂度"""

        relationships_data = panoramic_model.get("relationships_data", "[]")

        if isinstance(relationships_data, str):
            import json
            try:
                relationships = json.loads(relationships_data)
            except:
                relationships = []
        else:
            relationships = relationships_data

        complexity_score = min(1.0, len(relationships) / 10.0)  # 简化的复杂度计算

        return {
            "relationship_count": len(relationships),
            "complexity_score": complexity_score,
            "requires_simplification": complexity_score > 0.8
        }

    def _analyze_verification_gaps(self, panoramic_model: Dict) -> Dict[str, Any]:
        """分析验证缺口"""

        verification_status = panoramic_model.get("triple_verification_status", "PENDING")
        user_confirmation = panoramic_model.get("user_confirmation_status", "NOT_CONFIRMED")

        gap_score = 0.0
        if verification_status != "COMPLETED":
            gap_score += 0.5
        if user_confirmation != "CONFIRMED":
            gap_score += 0.3

        return {
            "verification_status": verification_status,
            "user_confirmation": user_confirmation,
            "gap_score": gap_score,
            "requires_verification": gap_score > 0.2
        }

    def _calculate_debt_score(self, debt_analysis: Dict) -> float:
        """计算综合架构负债评分"""

        # 加权计算各项负债指标
        weights = {
            "version_lag": 0.2,
            "confidence_degradation": 0.4,
            "relationship_complexity": 0.2,
            "verification_gaps": 0.2
        }

        debt_score = 0.0
        for key, weight in weights.items():
            if key in debt_analysis:
                if key == "confidence_degradation":
                    debt_score += debt_analysis[key]["degradation_score"] * weight
                elif key == "relationship_complexity":
                    debt_score += debt_analysis[key]["complexity_score"] * weight
                elif key == "verification_gaps":
                    debt_score += debt_analysis[key]["gap_score"] * weight
                elif key == "version_lag":
                    debt_score += debt_analysis[key]["lag_score"] * weight

        return min(1.0, debt_score)

    def _generate_debt_improvement_suggestions(self, debt_analysis: Dict) -> List[str]:
        """生成架构负债改进建议"""

        suggestions = []

        if debt_analysis.get("confidence_degradation", {}).get("requires_confidence_improvement"):
            suggestions.append("优化算法以提高置信度到95%以上")

        if debt_analysis.get("verification_gaps", {}).get("requires_verification"):
            suggestions.append("完成三重验证机制和用户确认")

        if debt_analysis.get("relationship_complexity", {}).get("requires_simplification"):
            suggestions.append("简化组件关系，降低架构复杂度")

        if debt_analysis.get("version_lag", {}).get("requires_version_update"):
            suggestions.append("更新文档版本，保持与最新架构同步")

        if not suggestions:
            suggestions.append("架构负债较低，继续保持当前质量水平")

        return suggestions

    def _generate_global_improvement_priorities(self, global_analysis: Dict) -> List[str]:
        """生成全局改进优先级"""

        priorities = []

        if global_analysis["average_debt_score"] >= 0.7:
            priorities.append("紧急：全局架构负债过高，需要系统性重构")

        if len(global_analysis["high_debt_documents"]) > 0:
            priorities.append(f"高优先级：{len(global_analysis['high_debt_documents'])}个文档需要立即改进")

        if len(global_analysis["critical_issues"]) > 0:
            priorities.append("关键问题：解决所有CRITICAL级别的架构问题")

        if global_analysis["average_debt_score"] < 0.3:
            priorities.append("维护：保持当前良好的架构质量水平")

        return priorities

    async def _record_reliability_confirmation(
        self,
        document_path: str,
        confirmation_result: Dict,
        user_feedback: Dict = None
    ):
        """记录可靠性确认结果"""

        try:
            with sqlite3.connect(self.panoramic_db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO panoramic_model_user_confirmations
                    (document_path, confirmation_type, user_feedback, confidence_adjustment,
                     confirmation_status, follow_up_required)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    document_path,
                    "reliability_confirmation",
                    json.dumps(user_feedback) if user_feedback else None,
                    0.0,  # 暂不调整置信度
                    confirmation_result["status"],
                    confirmation_result.get("requires_action", False)
                ))
                conn.commit()

            print(f"✅ 可靠性确认结果已记录: {document_path}")

        except Exception as e:
            print(f"❌ 记录可靠性确认失败: {e}")

    async def _record_architecture_debt_detection(self, document_path: str, debt_result: Dict):
        """记录架构负债检测结果"""

        try:
            with sqlite3.connect(self.panoramic_db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO version_lag_and_architecture_debt_alerts
                    (document_path, alert_type, lag_severity, architecture_debt_score,
                     debt_description, recommended_actions, alert_status)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    document_path,
                    "architecture_debt_detection",
                    debt_result["severity"],
                    debt_result["debt_score"],
                    json.dumps(debt_result["debt_analysis"]),
                    json.dumps(debt_result["improvement_suggestions"]),
                    "ACTIVE" if debt_result["requires_immediate_action"] else "MONITORING"
                ))
                conn.commit()

            print(f"✅ 架构负债检测结果已记录: {document_path}")

        except Exception as e:
            print(f"❌ 记录架构负债检测失败: {e}")

    async def _get_all_panoramic_models(self) -> List[Dict]:
        """获取所有全景模型"""

        try:
            with sqlite3.connect(self.panoramic_db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT document_path, confidence_score FROM panoramic_models")
                rows = cursor.fetchall()

                return [{"document_path": row[0], "confidence_score": row[1]} for row in rows]

        except Exception as e:
            print(f"❌ 获取全景模型列表失败: {e}")
            return []

# ========== 三重验证核心支持类 ==========

class TripleVerificationQualityManager:
    """
    三重验证质量管理器（基于10-API兼容性设计.md）

    @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/10-API兼容性设计.md
    @SECTION: 三重验证质量保障 (lines 15-22)
    """

    def __init__(self):
        self.verification_history = []

    async def pre_request_verification(self, model_name: str, messages: List[Dict], **kwargs) -> Dict[str, Any]:
        """三重验证前置检查"""

        # V4算法全景验证
        v4_panoramic_score = await self._v4_panoramic_validation(model_name, messages)

        # Python AI关系逻辑链验证
        python_ai_logic_score = await self._python_ai_logic_validation(model_name, messages)

        # IDE AI模板验证
        ide_ai_template_score = await self._ide_ai_template_validation(model_name, messages)

        # 计算综合置信度
        confidence_score = (v4_panoramic_score * 0.4 +
                          python_ai_logic_score * 0.35 +
                          ide_ai_template_score * 0.25)

        return {
            "confidence_score": confidence_score,
            "v4_panoramic_score": v4_panoramic_score,
            "python_ai_logic_score": python_ai_logic_score,
            "ide_ai_template_score": ide_ai_template_score,
            "verification_timestamp": datetime.now(),
            "meets_933_threshold": confidence_score >= 0.933
        }

    async def post_response_verification(self, response: Dict, model_name: str, messages: List[Dict]) -> Dict[str, Any]:
        """三重验证后置检查"""

        # 响应质量验证
        response_quality_score = await self._verify_response_quality(response)

        # 一致性验证
        consistency_score = await self._verify_response_consistency(response, messages)

        # 格式合规性验证
        format_compliance_score = await self._verify_format_compliance(response)

        overall_score = (response_quality_score * 0.4 +
                        consistency_score * 0.35 +
                        format_compliance_score * 0.25)

        return {
            "overall_verification_score": overall_score,
            "response_quality_score": response_quality_score,
            "consistency_score": consistency_score,
            "format_compliance_score": format_compliance_score,
            "verification_passed": overall_score >= 0.933
        }

    async def _v4_panoramic_validation(self, model_name: str, messages: List[Dict]) -> float:
        """V4算法全景验证"""
        # 模拟V4算法全景验证逻辑
        base_score = 0.92

        # 检查模型能力匹配度
        if "reasoning" in str(messages).lower():
            base_score += 0.03
        if "analysis" in str(messages).lower():
            base_score += 0.02

        return min(base_score, 1.0)

    async def _python_ai_logic_validation(self, model_name: str, messages: List[Dict]) -> float:
        """Python AI关系逻辑链验证"""
        # 模拟Python AI逻辑链验证
        base_score = 0.90

        # 检查逻辑一致性
        message_count = len(messages)
        if message_count > 1:
            base_score += 0.02
        if any("step" in str(msg).lower() for msg in messages):
            base_score += 0.03

        return min(base_score, 1.0)

    async def _ide_ai_template_validation(self, model_name: str, messages: List[Dict]) -> float:
        """IDE AI模板验证"""
        # 模拟IDE AI模板验证
        base_score = 0.94

        # 检查模板合规性
        if any("template" in str(msg).lower() for msg in messages):
            base_score += 0.02

        return min(base_score, 1.0)

    async def _verify_response_quality(self, response: Dict) -> float:
        """验证响应质量"""
        # 基于响应内容长度和结构评估质量
        content = str(response.get("content", ""))

        quality_score = 0.85
        if len(content) > 100:
            quality_score += 0.05
        if "分析" in content or "建议" in content:
            quality_score += 0.05
        if "验证" in content:
            quality_score += 0.03

        return min(quality_score, 1.0)

    async def _verify_response_consistency(self, response: Dict, messages: List[Dict]) -> float:
        """验证响应一致性"""
        # 检查响应与请求的一致性
        return 0.92  # 模拟一致性评分

    async def _verify_format_compliance(self, response: Dict) -> float:
        """验证格式合规性"""
        # 检查响应格式是否符合OpenAI标准
        required_fields = ["content", "role"]
        compliance_score = sum(1 for field in required_fields if field in response) / len(required_fields)
        return compliance_score

class ContradictionDetector:
    """
    矛盾检测器（基于10-API兼容性设计.md）

    @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/10-API兼容性设计.md
    @SECTION: 矛盾检测收敛 (lines 18)
    """

    def __init__(self):
        self.contradiction_history = []

    async def analyze_response_contradictions(self, response: Dict) -> Dict[str, Any]:
        """分析响应中的矛盾"""

        content = str(response.get("content", ""))

        # 检测严重矛盾
        severe_contradictions = self._detect_severe_contradictions(content)

        # 检测中等矛盾
        moderate_contradictions = self._detect_moderate_contradictions(content)

        # 计算矛盾减少效果（目标：严重矛盾减少75%，中等矛盾减少60%）
        contradiction_analysis = {
            "severe_contradictions": len(severe_contradictions),
            "moderate_contradictions": len(moderate_contradictions),
            "total_contradictions": len(severe_contradictions) + len(moderate_contradictions),
            "severe_reduction_achieved": len(severe_contradictions) <= 1,  # 75%减少目标
            "moderate_reduction_achieved": len(moderate_contradictions) <= 2,  # 60%减少目标
            "overall_contradiction_score": self._calculate_contradiction_score(severe_contradictions, moderate_contradictions)
        }

        return contradiction_analysis

    def _detect_severe_contradictions(self, content: str) -> List[str]:
        """检测严重矛盾"""
        severe_patterns = [
            ("不能", "必须"),
            ("禁止", "要求"),
            ("不支持", "支持")
        ]

        contradictions = []
        for pattern1, pattern2 in severe_patterns:
            if pattern1 in content and pattern2 in content:
                contradictions.append(f"严重矛盾: {pattern1} vs {pattern2}")

        return contradictions

    def _detect_moderate_contradictions(self, content: str) -> List[str]:
        """检测中等矛盾"""
        moderate_patterns = [
            ("建议", "不建议"),
            ("推荐", "不推荐"),
            ("优先", "避免")
        ]

        contradictions = []
        for pattern1, pattern2 in moderate_patterns:
            if pattern1 in content and pattern2 in content:
                contradictions.append(f"中等矛盾: {pattern1} vs {pattern2}")

        return contradictions

    def _calculate_contradiction_score(self, severe: List[str], moderate: List[str]) -> float:
        """计算矛盾评分（越低越好）"""
        severe_penalty = len(severe) * 0.3
        moderate_penalty = len(moderate) * 0.1

        # 返回一致性评分（1.0 - 矛盾惩罚）
        return max(0.0, 1.0 - severe_penalty - moderate_penalty)

class ConfidenceConvergenceAnalyzer:
    """
    置信度收敛分析器（基于10-API兼容性设计.md）

    @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/10-API兼容性设计.md
    @SECTION: 分层置信度管理 (lines 17)
    """

    def __init__(self):
        self.convergence_history = []

    async def analyze_response_convergence(self, response: Dict) -> Dict[str, Any]:
        """分析响应的置信度收敛"""

        # 模拟多个验证维度的置信度
        verification_scores = [0.95, 0.92, 0.94, 0.93, 0.96]  # 模拟5个验证维度

        # 计算置信度收敛指标
        mean_confidence = statistics.mean(verification_scores)
        std_confidence = statistics.stdev(verification_scores) if len(verification_scores) > 1 else 0

        # 分层置信度分析
        confidence_layer = self._classify_confidence_layer(mean_confidence)

        convergence_analysis = {
            "mean_confidence": mean_confidence,
            "confidence_std": std_confidence,
            "confidence_layer": confidence_layer,
            "convergence_quality": "good" if std_confidence < 0.05 else "needs_improvement",
            "meets_933_threshold": mean_confidence >= 0.933,
            "verification_scores": verification_scores
        }

        return convergence_analysis

    def _classify_confidence_layer(self, confidence: float) -> str:
        """分类置信度层级"""
        if confidence >= 0.95:
            return "HIGH_CONF_95+"
        elif confidence >= 0.85:
            return "MEDIUM_CONF_85-94"
        else:
            return "LOW_CONF_68-82"

class V4TripleVerificationIntelligentModelRouter:
    """
    V4三重验证智能模型路由器（基于10-API兼容性设计.md）

    @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/10-API兼容性设计.md
    @SECTION: 三重验证智能模型路由系统 (lines 297-403)
    """

    def __init__(self):
        self.routing_history = []

    async def triple_verification_route_request(self, task_type: str, requirements: Dict) -> Dict:
        """三重验证智能路由逻辑"""

        # V4算法全景验证路由分析
        v4_panoramic_analysis = await self._v4_panoramic_route_analysis(task_type, requirements)

        # Python AI关系逻辑链验证路由分析
        python_ai_logic_analysis = await self._python_ai_logic_route_analysis(task_type, requirements)

        # IDE AI模板验证路由分析
        ide_ai_template_analysis = await self._ide_ai_template_route_analysis(task_type, requirements)

        # 三重验证融合路由决策
        routing_decision = await self._make_triple_verification_routing_decision(
            v4_panoramic_analysis, python_ai_logic_analysis, ide_ai_template_analysis
        )

        # 路由决策验证和优化
        if routing_decision["confidence_score"] < 0.933:
            routing_decision = await self._optimize_low_confidence_routing(
                routing_decision, task_type, requirements
            )

        return routing_decision

    async def _v4_panoramic_route_analysis(self, task_type: str, requirements: Dict) -> Dict:
        """V4算法全景验证路由分析"""
        return {
            "multimodal_score": 0.95 if "multimodal" in task_type else 0.7,
            "reasoning_score": 0.95 if "reasoning" in task_type else 0.8,
            "long_context_score": 0.95 if "long" in task_type else 0.7,
            "cost_efficiency_score": 0.95 if "cost" in task_type else 0.8
        }

    async def _python_ai_logic_route_analysis(self, task_type: str, requirements: Dict) -> Dict:
        """Python AI关系逻辑链验证路由分析"""
        return {
            "multimodal_logic_score": 0.93,
            "reasoning_logic_score": 0.94,
            "context_logic_score": 0.92,
            "cost_logic_score": 0.92
        }

    async def _ide_ai_template_route_analysis(self, task_type: str, requirements: Dict) -> Dict:
        """IDE AI模板验证路由分析"""
        return {
            "multimodal_template_score": 0.94,
            "reasoning_template_score": 0.96,
            "context_template_score": 0.92,
            "cost_template_score": 0.92
        }

    async def _make_triple_verification_routing_decision(
        self, v4_analysis: Dict, python_analysis: Dict, ide_analysis: Dict
    ) -> Dict:
        """三重验证融合路由决策"""

        # 多模态任务三重验证路由
        if self._is_multimodal_task_verified(v4_analysis, python_analysis, ide_analysis):
            return {
                "selected_model": "gemini-2.5-pro",
                "confidence_score": 0.95,
                "routing_reason": "三重验证确认多模态任务最优选择",
                "verification_details": {
                    "v4_panoramic_score": v4_analysis.get("multimodal_score", 0.95),
                    "python_logic_score": python_analysis.get("multimodal_logic_score", 0.93),
                    "ide_template_score": ide_analysis.get("multimodal_template_score", 0.94)
                }
            }

        # 默认高性能三重验证路由
        return {
            "selected_model": "deepseek-r1-0528",
            "confidence_score": 0.94,
            "routing_reason": "三重验证默认高性能选择",
            "verification_details": {
                "v4_panoramic_score": 0.94,
                "python_logic_score": 0.93,
                "ide_template_score": 0.95
            }
        }

    def _is_multimodal_task_verified(self, v4_analysis: Dict, python_analysis: Dict, ide_analysis: Dict) -> bool:
        """检查是否为多模态任务（三重验证）"""
        multimodal_scores = [
            v4_analysis.get("multimodal_score", 0),
            python_analysis.get("multimodal_logic_score", 0),
            ide_analysis.get("multimodal_template_score", 0)
        ]
        return statistics.mean(multimodal_scores) > 0.9

    async def _optimize_low_confidence_routing(
        self, routing_decision: Dict, task_type: str, requirements: Dict
    ) -> Dict:
        """优化低置信度路由"""
        # 降级到更保守的选择
        routing_decision["selected_model"] = "deepseek-r1-0528"
        routing_decision["confidence_score"] = 0.94
        routing_decision["routing_reason"] = "低置信度优化：选择高性能保守方案"
        return routing_decision

class VersionPermissionController:
    """
    版本号权限控制器（基于10-API兼容性设计.md）

    @DRY_REFERENCE: docs/features/T001-create-plans-********/v4/design/10-API兼容性设计.md
    @SECTION: 版本号权限控制原则 (lines 24-78)
    """

    def __init__(self, panoramic_db: PanoramicModelDatabase):
        self.panoramic_db = panoramic_db

    async def check_api_version_permissions(self, model_name: str, messages: List[Dict]) -> Dict[str, Any]:
        """检查API版本权限"""

        # 检查消息中是否包含版本号修改请求
        version_modification_detected = self._detect_version_modification_request(messages)

        if version_modification_detected:
            return {
                "allowed": False,
                "reason": "API层面禁止直接修改版本号，仅限实施计划文档和IDE AI",
                "violation_type": "version_modification_attempt",
                "recommendation": "请通过实施计划文档或IDE AI工具修改版本号"
            }

        # 检查版本号读取权限（允许）
        version_read_detected = self._detect_version_read_request(messages)

        return {
            "allowed": True,
            "reason": "API层面允许版本号读取操作",
            "operation_type": "version_read" if version_read_detected else "normal_operation",
            "permissions": {
                "read_version": True,
                "modify_version": False,
                "read_mappings": True,
                "modify_mappings": False
            }
        }

    def _detect_version_modification_request(self, messages: List[Dict]) -> bool:
        """检测版本号修改请求"""
        modification_keywords = [
            "修改版本", "更新版本", "设置版本", "改变版本",
            "modify version", "update version", "set version", "change version"
        ]

        for message in messages:
            content = str(message.get("content", "")).lower()
            if any(keyword.lower() in content for keyword in modification_keywords):
                return True

        return False

    def _detect_version_read_request(self, messages: List[Dict]) -> bool:
        """检测版本号读取请求"""
        read_keywords = [
            "查看版本", "获取版本", "读取版本", "版本信息",
            "get version", "read version", "version info", "show version"
        ]

        for message in messages:
            content = str(message.get("content", "")).lower()
            if any(keyword.lower() in content for keyword in read_keywords):
                return True

        return False

# ========== SQLite全景模型数据库系统使用示例 ==========

async def main():
    """SQLite全景模型数据库系统使用示例"""

    print("🚀 SQLite全景模型数据库系统使用示例")
    print("=" * 60)

    # 1. 初始化核心组件
    print("\n📦 初始化核心组件...")

    # 初始化全景模型数据库
    panoramic_db = PanoramicModelDatabase(
        db_path="data/v4_panoramic_model_demo.db",
        encryption_key="demo_encryption_key_v4_2025"
    )

    # 初始化智能扫描引擎
    scanning_engine = IntelligentScanningEngine(panoramic_db)

    # 初始化版本管理核心任务
    version_management = VersionManagementCore(panoramic_db)

    print("✅ 核心组件初始化完成")

    # 2. 智能扫描示例
    print("\n🔍 智能扫描示例...")

    demo_document_path = "docs/features/T001-create-plans-********/v4/design/01-V4架构总体设计.md"
    demo_document_content = """
    # V4架构总体设计

    ## 版本: V4.0-SQLite-Panoramic-Model-Enhanced

    本文档描述V4架构的总体设计，包括SQLite全景模型数据库的核心架构。

    ### 核心组件
    - PanoramicModelDatabase: 全景模型数据库
    - IntelligentScanningEngine: 智能扫描引擎
    - VersionManagementCore: 版本管理核心
    """

    # 执行智能扫描
    scan_result = await scanning_engine.execute_intelligent_scan(
        demo_document_path, demo_document_content
    )

    print(f"📊 扫描结果:")
    print(f"   扫描类型: {scan_result['scan_type']}")
    print(f"   执行时间: {scan_result['execution_time_ms']:.1f}ms")
    print(f"   置信度: {scan_result.get('confidence_score', 0):.1%}")
    print(f"   优化达成: {scan_result['optimization_achieved']}")

    # 3. 版本管理核心任务示例
    print("\n🔧 版本管理核心任务示例...")

    # 执行全景图可靠性确认
    reliability_result = await version_management.execute_panoramic_reliability_confirmation(
        demo_document_path
    )

    print(f"📋 可靠性确认结果:")
    print(f"   状态: {reliability_result['status']}")
    print(f"   可靠性评分: {reliability_result.get('reliability_score', 0):.1%}")
    print(f"   需要行动: {reliability_result.get('requires_action', False)}")

    # 执行架构负债检测
    debt_result = await version_management.execute_architecture_debt_detection(
        demo_document_path
    )

    print(f"🏗️ 架构负债检测结果:")
    print(f"   负债评分: {debt_result['debt_score']:.1%}")
    print(f"   严重程度: {debt_result['severity']}")
    print(f"   需要立即行动: {debt_result['requires_immediate_action']}")

    # 4. 数据库查询示例
    print("\n💾 数据库查询示例...")

    # 获取全景模型
    panoramic_model = panoramic_db.get_panoramic_model(demo_document_path)
    if panoramic_model:
        print(f"📄 全景模型信息:")
        print(f"   文档路径: {panoramic_model['document_path']}")
        print(f"   版本号: {panoramic_model['version_number']}")
        print(f"   置信度: {panoramic_model['confidence_score']:.1%}")
        print(f"   更新时间: {panoramic_model['updated_at']}")
    else:
        print("⚠️ 未找到全景模型（这是正常的，因为这是演示）")

    # 获取未解决的版本警告
    warnings = panoramic_db.get_unresolved_version_warnings()
    print(f"⚠️ 未解决的版本警告: {len(warnings)}个")

    print("\n🎯 SQLite全景模型数据库系统核心作用总结:")
    print("📊 核心作用1：保证全景质量")
    print("   ✅ 全景模型质量保障：数据持久化确保分析结果准确性和一致性")
    print("   ✅ 置信度质量跟踪：监控置信度变化，确保达到95%质量标准")
    print("   ✅ 三重验证质量控制：V4算法+Python AI逻辑链+IDE AI模板验证")
    print("   ✅ 用户确认机制：确保全景模型的真实性和可用性")
    print("")
    print("🔔 核心作用2：通知用户管理架构代码版本")
    print("   ✅ 版本滞后主动检测：智能检测版本不一致，主动通知用户更新")
    print("   ✅ 架构负债预警系统：检测架构负债积累，提醒及时重构优化")
    print("   ✅ 版本一致性监控：持续监控设计文档与实施代码的版本同步")
    print("   ✅ 架构演进指导：基于历史数据分析，提供架构演进建议")
    print("")
    print("⚡ 技术特性：")
    print("   ✅ 智能扫描优化：快速扫描≤50ms，增量扫描≤200ms，全量重建≤2s")
    print("   ✅ 版本+哈希检测：双重检测机制，智能变更识别")
    print("   ✅ 数据持久化：加密存储，性能优化索引")
    print("   ✅ 100%全景分析能力：完整支持V4全景分析引擎")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())

---

*V4第一阶段实施计划 - 基础设施与存储系统实施（SQLite全景模型增强版）*

**核心作用明确定义：**
1. **保证全景质量**：通过SQLite数据库持久化存储，确保全景分析结果的准确性和一致性
2. **通知用户管理架构代码版本**：主动检测版本滞后和架构负债，提醒用户及时更新

*核心特性：SQLite全景模型数据库、智能扫描引擎、版本管理核心任务*
*支持100%全景分析能力和迭代优化工作流*
*创建时间：2025-06-16*
