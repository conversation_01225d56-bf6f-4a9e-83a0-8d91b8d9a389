#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Thinking质量评估融合方案研究运行器

快速启动多维度对比研究：
- 性能评估
- 稳定性评估  
- 质量评估
- 置信度评估

基于XKongCloud Commons Nexus技术栈

使用方法：
python run_research.py

作者：AI助手
日期：2025-01-08
"""

import sys
import os
from pathlib import Path

def main():
    """主运行函数"""
    print("🔬 Thinking质量评估融合方案多维度研究")
    print("=" * 60)
    print("📋 研究内容:")
    print("   • V4.5三维融合thinking架构")
    print("   • Cognitive Ascent Protocol")  
    print("   • 两者融合方案")
    print()
    print("📊 评估维度:")
    print("   • 性能 (Performance): 响应时间、处理效率、资源利用率")
    print("   • 稳定性 (Stability): 一致性、可靠性、错误处理")
    print("   • 质量 (Quality): 准确性、完整性、逻辑性")
    print("   • 置信度 (Confidence): 确定性、可信度、验证性")
    print()
    print("🏗️ 技术栈基准:")
    print("   • XKongCloud Commons Nexus")
    print("   • Java 21 + Spring Boot 3.4.5")
    print("   • 微内核 + 服务总线架构")
    print("   • Virtual Threads + ZGC")
    print()
    
    # 检查主程序文件
    current_dir = Path(__file__).parent
    main_program = current_dir / "thinking_quality_fusion_research.py"
    
    if not main_program.exists():
        print("❌ 错误: 找不到主程序文件 thinking_quality_fusion_research.py")
        print(f"   请确保文件存在于: {main_program}")
        return False
    
    print(f"✅ 找到主程序: {main_program}")
    print()
    
    try:
        # 导入并运行主程序
        sys.path.insert(0, str(current_dir))
        from thinking_quality_fusion_research import main as research_main
        
        print("🚀 开始执行研究...")
        print("=" * 60)
        
        result = research_main()
        
        if result:
            print("\n" + "=" * 60)
            print("✅ 研究完成!")
            print("📄 详细结果已保存到JSON报告文件")
            print("📊 多维度对比分析已生成")
            print("🏗️ 技术栈兼容性评估已完成")
            return True
        else:
            print("\n❌ 研究执行失败")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请检查主程序文件是否正确")
        return False
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
    
    print("\n🎉 研究任务完成!")
    print("📋 查看生成的报告了解详细结果")
