package org.xkong.cloud.business.internal.core.test.engine.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 分析组件注解
 * 
 * 用于标记分析策略类，引擎会自动发现和注册这些组件
 * 
 * 使用示例：
 * ```java
 * @AnalysisComponent(
 *     name = "postgresql-migration-analysis",
 *     description = "PostgreSQL迁移专项分析",
 *     priority = 1,
 *     enabled = true
 * )
 * public class PostgreSQLMigrationAnalysisStrategy implements AnalysisStrategy {
 *     // 实现分析逻辑
 * }
 * ```
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface AnalysisComponent {
    
    /**
     * 分析组件名称
     * 
     * 用于配置文件中的引用和日志输出
     * 如果不指定，默认使用类名的kebab-case形式
     * 
     * @return 组件名称
     */
    String name() default "";
    
    /**
     * 分析组件描述
     * 
     * 用于文档生成和调试信息
     * 
     * @return 组件描述
     */
    String description() default "";
    
    /**
     * 执行优先级
     * 
     * 数字越小优先级越高，相同优先级的组件按名称排序执行
     * 
     * @return 优先级（默认100）
     */
    int priority() default 100;
    
    /**
     * 是否默认启用
     * 
     * 可以通过配置文件覆盖此设置
     * 
     * @return true表示默认启用，false表示默认禁用
     */
    boolean enabled() default true;
    
    /**
     * 输出文件名
     * 
     * 如果不指定，默认使用组件名称 + ".json"
     * 
     * @return 输出文件名
     */
    String outputFileName() default "";
    
    /**
     * 分析类别
     * 
     * 用于分组和过滤分析组件
     * 
     * @return 分析类别
     */
    String category() default "general";
    
    /**
     * 依赖的其他分析组件
     * 
     * 引擎会确保依赖的组件先执行
     * 
     * @return 依赖的组件名称数组
     */
    String[] dependencies() default {};
    
    /**
     * 分析组件版本
     * 
     * 用于版本控制和兼容性检查
     * 
     * @return 版本号
     */
    String version() default "1.0.0";
    
    /**
     * 分析组件作者
     * 
     * 用于标识组件的创建者
     * 
     * @return 作者信息
     */
    String author() default "AI";
    
    /**
     * 是否支持并行执行
     * 
     * 如果为true，此组件可以与其他支持并行的组件同时执行
     * 
     * @return true表示支持并行，false表示必须串行执行
     */
    boolean parallelizable() default true;
    
    /**
     * 预期执行时间（毫秒）
     * 
     * 用于执行计划和超时控制
     * 
     * @return 预期执行时间
     */
    long expectedExecutionTimeMs() default 5000;
    
    /**
     * 最大执行时间（毫秒）
     * 
     * 超过此时间将被强制终止
     * 
     * @return 最大执行时间
     */
    long maxExecutionTimeMs() default 30000;
    
    /**
     * 标签
     * 
     * 用于分类和过滤
     * 
     * @return 标签数组
     */
    String[] tags() default {};
}

/**
 * 数据收集器注解
 * 
 * 用于标记数据收集方法
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@interface DataCollector {
    
    /**
     * 数据收集器名称
     */
    String name() default "";
    
    /**
     * 输出文件名
     */
    String fileName() default "";
    
    /**
     * 是否启用
     */
    boolean enabled() default true;
    
    /**
     * 执行优先级
     */
    int priority() default 100;
    
    /**
     * 数据类型
     */
    String dataType() default "json";
    
    /**
     * 描述信息
     */
    String description() default "";
}

/**
 * 输出目标注解
 * 
 * 用于指定数据输出的目标和格式
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@interface OutputTarget {
    
    /**
     * 输出格式
     */
    String format() default "json";
    
    /**
     * 输出路径
     */
    String path() default "";
    
    /**
     * 是否压缩
     */
    boolean compressed() default false;
    
    /**
     * 是否美化输出
     */
    boolean prettyPrint() default true;
    
    /**
     * 编码格式
     */
    String encoding() default "UTF-8";
}
