---
title: PostgreSQL复杂查询实现指南(演进架构版)
document_id: C033
document_type: 共享文档
category: 中间件
scope: PostgreSQL
keywords: [PostgreSQL, 复杂查询, jOOQ, QueryDSL, 动态查询, 高级SQL, 性能优化, 演进架构, 智能查询路由, 分布式查询]
created_date: 2025-07-01
updated_date: 2025-01-15
status: 已批准
version: 2.0
authors: [AI助手]
affected_features:
  - F003 # PostgreSQL迁移
related_docs:
  - ./query-optimization-guide.md
  - ./framework-integration-best-practices.md
  - ./special-data-types-guide.md
  - ./development-standards-guide.md
  - ../architecture/patterns/postgresql-evolution-implementation-guide.md
---

# PostgreSQL复杂查询实现指南(演进架构版)

## 演进架构整合概述

本文档已升级为支持持续演进架构的复杂查询实现指南。在传统PostgreSQL复杂查询技术的基础上，融入了以下演进架构特性：

### 核心演进特性
- **智能查询路由**：根据架构模式自动选择本地或分布式查询策略
- **查询服务抽象层**：统一的查询接口，支持从单体到微服务的平滑演进
- **配置驱动查询策略**：通过配置文件控制查询实现方式和优化策略
- **分布式查询协调**：支持跨服务的复杂查询协调和结果聚合
- **渐进式查询演进**：支持查询逻辑从本地实现逐步演进到分布式实现

### 架构演进路径
1. **单体阶段**：使用本地复杂查询实现，建立查询抽象层
2. **模块化阶段**：引入查询服务抽象，支持模块间查询协调
3. **混合阶段**：部分查询保持本地，部分查询使用远程服务
4. **微服务阶段**：全面使用分布式查询协调和服务间通信

## 摘要

本文档提供了在xkongcloud项目中实现PostgreSQL复杂查询的详细指南，支持持续演进架构模式。文档重点比较了jOOQ和QueryDSL两种主流查询框架在演进架构下的应用，并针对不同的复杂查询场景提供了支持架构演进的最佳实践建议。本指南旨在帮助开发人员在面对复杂查询需求时，能够选择合适的技术方案并高效实现，同时确保查询性能、代码可维护性和架构演进能力。

## 文档关系说明

本文档是PostgreSQL相关文档体系的一部分，与其他文档的关系如下：

- [PostgreSQL查询优化指南](./query-optimization-guide.md)：提供查询优化的一般原则和技术，本文档是其在复杂查询场景下的具体应用和扩展。
- [PostgreSQL框架集成最佳实践](./framework-integration-best-practices.md)：提供各种ORM框架与PostgreSQL集成的最佳实践，本文档深入探讨了其中jOOQ和QueryDSL的应用。
- [PostgreSQL特定数据类型使用指南](./special-data-types-guide.md)：提供特殊数据类型的使用方法，本文档涵盖了如何在复杂查询中处理这些特殊类型。
- [PostgreSQL开发规范指南](./development-standards-guide.md)：提供PostgreSQL的编码规范，本文档遵循这些规范。
- [PostgreSQL演进架构实施指南](../architecture/patterns/postgresql-evolution-implementation-guide.md)：提供演进架构的通用实施模式，本文档是其在复杂查询领域的具体应用。

## 1. 演进架构下的查询框架选择

### 1.1 智能查询路由架构

在演进架构模式下，复杂查询的实现需要支持智能路由，根据当前架构模式和配置自动选择最适合的查询策略：

```java
@Component
public class QueryRoutingService {

    @Autowired
    private ServiceConfiguration config;

    @Autowired
    private LocalQueryService localQueryService;

    @Autowired
    private DistributedQueryService distributedQueryService;

    public <T> List<T> executeComplexQuery(ComplexQueryRequest request, Class<T> resultType) {
        if (config.isLocal(request.getServiceName())) {
            return localQueryService.execute(request, resultType);
        } else {
            return distributedQueryService.execute(request, resultType);
        }
    }
}
```

### 1.2 jOOQ与QueryDSL在演进架构下的比较

| 特性 | jOOQ | QueryDSL | 演进架构适配性 |
|------|------|----------|---------------|
| 定位 | SQL构建器和ORM框架 | 通用查询框架，支持多种后端 | 两者都支持 |
| 主要优势 | 完整SQL支持，类型安全，代码生成 | 简洁API，与JPA无缝集成，跨存储引擎 | jOOQ更适合复杂演进 |
| 主要劣势 | 学习曲线较陡，配置复杂 | SQL特性支持不如jOOQ全面 | QueryDSL配置更简单 |
| 适用场景 | 复杂SQL查询，报表分析，完全控制SQL | 动态条件查询，与JPA混合使用 | 混合使用最佳 |
| 代码生成 | 基于数据库schema生成 | 基于实体类生成 | 两者都支持演进 |
| 分布式支持 | 需要额外适配层 | 天然支持抽象 | QueryDSL更容易适配 |
| 远程查询支持 | 通过gRPC/REST适配 | 通过服务抽象层 | 两者都可实现 |
| 许可证 | 商业/开源双许可 | Apache 2.0 | QueryDSL更友好 |
| 社区活跃度 | 高 | 中 | jOOQ生态更丰富 |
| 最新稳定版本 | 3.20.4 (2025年) | 5.1.0 (2024年) | 两者都在积极维护 |

### 1.3 演进架构下的详细比较

#### 1.3.1 SQL表达能力与分布式适配

**jOOQ在演进架构下**:
- 支持几乎所有PostgreSQL特性，包括窗口函数、CTE、JSONB操作等
- 提供流畅的API来构建复杂SQL，便于封装为服务接口
- 支持自定义SQL函数和运算符，可以适配分布式查询需求
- 支持数据库特定的SQL方言和扩展，便于优化本地查询性能
- **演进优势**：复杂查询逻辑可以完整保留，便于从本地迁移到远程服务

**QueryDSL在演进架构下**:
- 基本SQL功能支持良好，适合大部分业务查询
- 高级特性支持有限，但抽象层设计便于扩展
- API更抽象，天然适合服务接口封装
- 跨数据库兼容性更好，便于多服务环境
- **演进优势**：抽象程度高，更容易适配分布式查询协调

#### 1.3.2 与演进架构集成

**jOOQ与演进架构**:
- 与JPA是独立的，需要额外配置，但便于独立演进
- 可以通过Spring Boot starter简化集成，支持配置驱动
- 需要单独管理事务和会话，便于分布式事务控制
- **演进策略**：通过查询服务抽象层封装jOOQ实现

**QueryDSL与演进架构**:
- 与JPA无缝集成，便于渐进式演进
- 可以直接扩展Spring Data JPA仓库，支持服务抽象
- 共享JPA的事务和会话管理，简化分布式事务
- **演进策略**：通过Repository抽象层自然演进到服务调用

#### 1.3.3 性能考量与架构演进

**jOOQ性能演进**:
- 生成高效的SQL，本地性能优异
- 提供更多优化选项，便于针对性优化
- 支持批处理和大数据集处理，适合数据密集型服务
- 可以精确控制SQL生成，便于性能调优
- **演进考虑**：本地查询性能最优，但需要额外工作适配分布式

**QueryDSL性能演进**:
- 性能通常依赖于底层实现(JPA/JDBC)，但抽象层便于优化
- 简单查询性能良好，适合大部分业务场景
- 复杂查询可能不如jOOQ优化，但便于服务间协调
- **演进考虑**：性能适中，但演进成本更低

#### 1.3.4 开发体验与架构演进

**jOOQ开发演进体验**:
- 代码更冗长但更明确，便于理解查询逻辑
- IDE支持良好，类型安全，减少演进过程中的错误
- 学习曲线较陡，但掌握后便于复杂查询演进
- 文档全面详细，支持复杂演进场景
- **演进体验**：初期投入大，但长期演进收益高

**QueryDSL开发演进体验**:
- 代码简洁直观，便于快速演进
- 与JPA开发体验一致，团队学习成本低
- 学习曲线平缓，便于团队快速适应演进
- 文档相对简单，但足够支持常见演进场景
- **演进体验**：初期投入小，演进过程平滑

## 2. 演进架构下的复杂查询场景分析

### 2.1 动态条件查询(支持演进架构)

**场景描述**：根据用户输入的多个可选条件构建查询，条件组合不固定，需要支持从本地查询演进到分布式查询。

**演进架构技术选择**：
- **首选**: QueryDSL + 查询服务抽象层
- **理由**:
  - 更简洁的条件组合API，便于服务接口封装
  - 与Spring Data JPA无缝集成，支持渐进式演进
  - 条件预定义和重用能力强，便于跨服务复用
  - 抽象程度高，便于适配分布式查询协调

**演进架构实现示例**:

```java
// 1. 查询服务抽象接口
@ServiceInterface("user-query-service")
public interface UserQueryService {
    List<User> findUsers(UserSearchCriteria criteria);
    Page<User> findUsersWithPaging(UserSearchCriteria criteria, Pageable pageable);
}

// 2. 本地实现(单体/模块化阶段)
@Service
@ConditionalOnProperty(name = "xkong.services.user-query-service.mode",
                       havingValue = "LOCAL", matchIfMissing = true)
public class LocalUserQueryService implements UserQueryService {

    @Autowired
    private JPAQueryFactory queryFactory;

    @Override
    public List<User> findUsers(UserSearchCriteria criteria) {
        QUser user = QUser.user;
        BooleanBuilder builder = buildDynamicConditions(criteria);

        return queryFactory.selectFrom(user)
            .where(builder)
            .orderBy(user.createdAt.desc())
            .fetch();
    }

    private BooleanBuilder buildDynamicConditions(UserSearchCriteria criteria) {
        QUser user = QUser.user;
        BooleanBuilder builder = new BooleanBuilder();

        if (criteria.getName() != null) {
            builder.and(user.name.containsIgnoreCase(criteria.getName()));
        }

        if (criteria.getStatus() != null) {
            builder.and(user.status.eq(criteria.getStatus()));
        }

        if (criteria.getCreatedAfter() != null) {
            builder.and(user.createdAt.after(criteria.getCreatedAfter()));
        }

        return builder;
    }
}

// 3. 分布式实现(微服务阶段)
@Service
@ConditionalOnProperty(name = "xkong.services.user-query-service.mode",
                       havingValue = "REMOTE")
public class RemoteUserQueryService implements UserQueryService {

    @Autowired
    private UserQueryServiceGrpc.UserQueryServiceBlockingStub queryStub;

    @Override
    public List<User> findUsers(UserSearchCriteria criteria) {
        UserQueryRequest request = UserQueryRequest.newBuilder()
            .setCriteria(convertToGrpcCriteria(criteria))
            .build();

        UserQueryResponse response = queryStub.findUsers(request);
        return convertFromGrpcResponse(response);
    }

    private UserSearchCriteriaProto convertToGrpcCriteria(UserSearchCriteria criteria) {
        // 转换逻辑
        return UserSearchCriteriaProto.newBuilder()
            .setName(criteria.getName() != null ? criteria.getName() : "")
            .setStatus(criteria.getStatus() != null ? criteria.getStatus().name() : "")
            .build();
    }
}

// 4. 智能路由使用
@Service
public class UserService {

    @Autowired
    private UserQueryService userQueryService; // Spring会根据配置注入合适的实现

    public List<User> searchUsers(UserSearchCriteria criteria) {
        return userQueryService.findUsers(criteria);
    }
}
```

### 2.2 复杂报表查询(支持演进架构)

**场景描述**：需要多表连接、聚合函数、窗口函数、子查询等高级SQL特性的报表查询，需要支持从本地报表演进到分布式报表协调。

**演进架构技术选择**：
- **首选**: jOOQ + 报表服务抽象层
- **理由**:
  - 完整支持高级SQL特性，便于复杂报表逻辑封装
  - 类型安全的SQL构建，减少演进过程中的错误
  - 结果映射灵活，便于适配不同的服务接口
  - 可以精确控制SQL生成，便于性能优化

**演进架构实现示例**:

```java
// 1. 报表服务抽象接口
@ServiceInterface("order-report-service")
public interface OrderReportService {
    List<OrderStatistics> getOrderStatistics(int year, int month);
    List<CustomerRankingReport> getCustomerRanking(ReportPeriod period);
    OrderSummaryReport getOrderSummary(OrderSummaryRequest request);
}

// 2. 本地实现(单体/模块化阶段)
@Service
@ConditionalOnProperty(name = "xkong.services.order-report-service.mode",
                       havingValue = "LOCAL", matchIfMissing = true)
public class LocalOrderReportService implements OrderReportService {

    @Autowired
    private DSLContext dslContext;

    @Override
    public List<OrderStatistics> getOrderStatistics(int year, int month) {
        return dslContext.select(
                CUSTOMER.NAME.as("customerName"),
                count(ORDER.ID).as("orderCount"),
                sum(ORDER.TOTAL_AMOUNT).as("totalAmount"),
                avg(ORDER.TOTAL_AMOUNT).as("averageAmount"),
                max(ORDER.CREATED_AT).as("lastOrderDate"),
                field(
                    "rank() over (order by sum(total_amount) desc)",
                    Integer.class
                ).as("customerRank")
            )
            .from(ORDER)
            .join(CUSTOMER).on(ORDER.CUSTOMER_ID.eq(CUSTOMER.ID))
            .where(
                extract(ORDER.CREATED_AT, DatePart.YEAR).eq(year)
                .and(extract(ORDER.CREATED_AT, DatePart.MONTH).eq(month))
            )
            .groupBy(CUSTOMER.ID, CUSTOMER.NAME)
            .orderBy(field("totalAmount").desc())
            .fetchInto(OrderStatistics.class);
    }

    @Override
    public List<CustomerRankingReport> getCustomerRanking(ReportPeriod period) {
        // 复杂窗口函数查询实现
        return dslContext.select(
                CUSTOMER.ID,
                CUSTOMER.NAME,
                sum(ORDER.TOTAL_AMOUNT).as("totalAmount"),
                count(ORDER.ID).as("orderCount"),
                field("dense_rank() over (order by sum(total_amount) desc)").as("rank"),
                field("lag(sum(total_amount)) over (order by sum(total_amount) desc)").as("previousAmount")
            )
            .from(CUSTOMER)
            .leftJoin(ORDER).on(CUSTOMER.ID.eq(ORDER.CUSTOMER_ID))
            .where(ORDER.CREATED_AT.between(period.getStartDate(), period.getEndDate()))
            .groupBy(CUSTOMER.ID, CUSTOMER.NAME)
            .orderBy(field("totalAmount").desc())
            .fetchInto(CustomerRankingReport.class);
    }
}

// 3. 分布式实现(微服务阶段)
@Service
@ConditionalOnProperty(name = "xkong.services.order-report-service.mode",
                       havingValue = "REMOTE")
public class RemoteOrderReportService implements OrderReportService {

    @Autowired
    private OrderReportServiceGrpc.OrderReportServiceBlockingStub reportStub;

    @Override
    public List<OrderStatistics> getOrderStatistics(int year, int month) {
        OrderStatisticsRequest request = OrderStatisticsRequest.newBuilder()
            .setYear(year)
            .setMonth(month)
            .build();

        OrderStatisticsResponse response = reportStub.getOrderStatistics(request);
        return convertFromGrpcResponse(response);
    }

    @Override
    public List<CustomerRankingReport> getCustomerRanking(ReportPeriod period) {
        // 分布式报表查询，可能需要协调多个服务
        return coordinateDistributedReport(period);
    }

    private List<CustomerRankingReport> coordinateDistributedReport(ReportPeriod period) {
        // 1. 从客户服务获取客户信息
        List<Customer> customers = customerServiceStub.getCustomers();

        // 2. 从订单服务获取订单统计
        List<OrderSummary> orderSummaries = orderServiceStub.getOrderSummaries(period);

        // 3. 在应用层进行数据聚合和排序
        return aggregateAndRank(customers, orderSummaries);
    }
}

// 4. 混合模式实现(混合阶段)
@Service
@ConditionalOnProperty(name = "xkong.services.order-report-service.mode",
                       havingValue = "HYBRID")
public class HybridOrderReportService implements OrderReportService {

    @Autowired
    private LocalOrderReportService localService;

    @Autowired
    private RemoteOrderReportService remoteService;

    @Autowired
    private ServiceConfiguration config;

    @Override
    public List<OrderStatistics> getOrderStatistics(int year, int month) {
        // 根据数据量和复杂度选择实现方式
        if (isComplexQuery(year, month)) {
            return localService.getOrderStatistics(year, month);
        } else {
            return remoteService.getOrderStatistics(year, month);
        }
    }

    private boolean isComplexQuery(int year, int month) {
        // 根据业务规则判断查询复杂度
        return config.getReportComplexityThreshold() > calculateComplexity(year, month);
    }
}
```

### 2.3 特殊数据类型查询

**场景描述**：查询包含JSONB、数组、地理信息等PostgreSQL特殊数据类型的表。

**技术选择**：
- **首选**: jOOQ
- **理由**:
  - 原生支持PostgreSQL特殊数据类型
  - 提供类型安全的特殊操作符
  - 可以充分利用PostgreSQL索引优化

**示例代码**:
```java
// jOOQ实现 - JSONB查询
public List<UserProfile> findUsersByPreferences(String theme, boolean notifications) {
    return dslContext.select()
        .from(USER_PROFILE)
        .where(
            field("{0} @> {1}::jsonb",
                USER_PROFILE.PREFERENCES,
                DSL.val(new JSONObject()
                    .put("theme", theme)
                    .put("notifications", notifications)
                    .toString())
            )
        )
        .fetchInto(UserProfile.class);
}

// jOOQ实现 - 数组查询
public List<Product> findProductsByTags(String... tags) {
    return dslContext.select()
        .from(PRODUCT)
        .where(PRODUCT.TAGS.contains(tags))
        .orderBy(PRODUCT.NAME)
        .fetchInto(Product.class);
}
```

### 2.4 全文搜索

**场景描述**：基于内容的全文搜索，需要相关性排序和高亮显示。

**技术选择**：
- **首选**: jOOQ + 原生SQL函数
- **理由**:
  - 支持PostgreSQL全文搜索函数
  - 可以处理复杂的排序和结果处理
  - 性能优化选项更多

**示例代码**:
```java
// jOOQ实现
public List<ArticleSearchResult> searchArticles(String query) {
    return dslContext.select(
            ARTICLE.ID,
            ARTICLE.TITLE,
            field("ts_headline({0}, {1}, plainto_tsquery({2}, {3}), {4})",
                inline("english"),
                ARTICLE.CONTENT,
                inline("english"),
                inline(query),
                inline("StartSel=<b>, StopSel=</b>")
            ).as("highlight")
        )
        .from(ARTICLE)
        .where(field("{0} @@ plainto_tsquery({1}, {2})",
            ARTICLE.TS_VECTOR,
            inline("english"),
            inline(query)
        ))
        .orderBy(field("ts_rank({0}, plainto_tsquery({1}, {2})) DESC",
            ARTICLE.TS_VECTOR,
            inline("english"),
            inline(query)
        ))
        .fetchInto(ArticleSearchResult.class);
}
```

## 3. 混合使用策略

在实际项目中，可以根据不同场景混合使用这些技术：

### 3.1 推荐的混合架构

1. **基础CRUD操作**: Spring Data JPA
2. **动态条件查询**: QueryDSL + Spring Data JPA
3. **复杂报表和高级SQL**: jOOQ
4. **特殊数据类型操作**: jOOQ
5. **极端性能要求场景**: 原生SQL (通过JPA的@Query或jOOQ的DSL.query())

### 3.2 混合使用示例

```java
@Service
@Transactional
public class UserService {
    private final UserRepository userRepository;          // JPA Repository
    private final JPAQueryFactory queryFactory;           // QueryDSL
    private final DSLContext dslContext;                  // jOOQ

    // 构造函数注入...

    // 使用JPA进行简单CRUD
    public User createUser(User user) {
        return userRepository.save(user);
    }

    // 使用QueryDSL进行动态条件查询
    public List<User> findUsersBySearchCriteria(UserSearchCriteria criteria) {
        QUser user = QUser.user;
        BooleanBuilder builder = new BooleanBuilder();

        // 添加条件...

        return queryFactory.selectFrom(user)
            .where(builder)
            .fetch();
    }

    // 使用jOOQ进行复杂报表查询
    public List<UserActivityReport> generateUserActivityReport(ZonedDateTime startDate, ZonedDateTime endDate) {
        return dslContext.select(
                USER.ID, USER.NAME,
                count(LOGIN_HISTORY.ID).as("loginCount"),
                max(LOGIN_HISTORY.LOGIN_TIME).as("lastLoginTime"),
                field("array_agg(distinct device_type)").as("usedDevices")
            )
            .from(USER)
            .leftJoin(LOGIN_HISTORY).on(USER.ID.eq(LOGIN_HISTORY.USER_ID))
            .where(LOGIN_HISTORY.LOGIN_TIME.between(startDate).and(endDate))
            .groupBy(USER.ID, USER.NAME)
            .orderBy(field("loginCount").desc())
            .fetchInto(UserActivityReport.class);
    }
}
```

## 4. 性能优化建议

### 4.1 jOOQ性能优化

1. **使用批处理**:
   ```java
   List<InsertValuesStep3<UserRecord, Long, String, String>> queries = new ArrayList<>();

   for (User user : users) {
       queries.add(dslContext.insertInto(USER,
           USER.ID, USER.NAME, USER.EMAIL)
           .values(user.getId(), user.getName(), user.getEmail()));
   }

   dslContext.batch(queries).execute();
   ```

2. **结果集映射优化**:
   ```java
   // 使用RecordMapper而非反射
   RecordMapper<Record, UserDTO> mapper = record ->
       new UserDTO(
           record.get(USER.ID),
           record.get(USER.NAME),
           record.get(USER.EMAIL)
       );

   return dslContext.select(USER.ID, USER.NAME, USER.EMAIL)
       .from(USER)
       .fetch(mapper);
   ```

3. **SQL重用**:
   ```java
   // 预编译和重用SQL
   private final SelectConditionStep<Record3<Long, String, String>> userBaseQuery =
       dslContext.select(USER.ID, USER.NAME, USER.EMAIL)
           .from(USER)
           .where(USER.STATUS.eq("ACTIVE"));

   public List<UserDTO> findActiveUsersByDepartment(String department) {
       return userBaseQuery
           .and(USER.DEPARTMENT.eq(department))
           .fetchInto(UserDTO.class);
   }
   ```

### 4.2 QueryDSL性能优化

1. **预定义查询部分**:
   ```java
   // 预定义常用条件
   private final BooleanExpression isActive = QUser.user.status.eq("ACTIVE");
   private final BooleanExpression isVerified = QUser.user.verified.isTrue();

   public List<User> findActiveVerifiedUsers() {
       return queryFactory.selectFrom(QUser.user)
           .where(isActive.and(isVerified))
           .fetch();
   }
   ```

2. **避免N+1问题**:
   ```java
   // 使用join fetch
   return queryFactory.selectFrom(QUser.user)
       .leftJoin(QUser.user.orders, QOrder.order).fetchJoin()
       .where(QUser.user.department.eq(department))
       .distinct()
       .fetch();
   ```

3. **分页优化**:
   ```java
   // 使用offset/limit而非Page对象
   return queryFactory.selectFrom(QUser.user)
       .where(conditions)
       .orderBy(QUser.user.id.asc())
       .offset(pageable.getOffset())
       .limit(pageable.getPageSize())
       .fetch();
   ```

## 5. 最佳实践总结

### 5.1 技术选择决策树

1. **如果查询条件是动态的且变化多样**:
   - 使用QueryDSL

2. **如果查询涉及复杂SQL特性(窗口函数、CTE、递归查询等)**:
   - 使用jOOQ

3. **如果查询需要处理PostgreSQL特殊数据类型**:
   - 使用jOOQ

4. **如果项目已经使用Spring Data JPA且查询相对简单**:
   - 使用QueryDSL扩展JPA功能

5. **如果需要完全控制生成的SQL并优化性能**:
   - 使用jOOQ

### 5.2 代码组织建议

1. **分层设计**:
   - Repository层: 基础CRUD操作(JPA)
   - QueryService层: 复杂查询逻辑(jOOQ/QueryDSL)
   - 业务Service层: 组合使用Repository和QueryService

2. **查询对象封装**:
   - 将查询条件封装为专用对象
   - 将查询结果映射为DTO对象

3. **代码复用**:
   - 提取常用查询条件为可重用组件
   - 使用组合而非继承扩展查询功能

## 6. 结论

在xkongcloud项目中实现PostgreSQL复杂查询时，应根据具体场景选择合适的技术:

- **QueryDSL**: 适合动态条件查询，与JPA集成良好，API简洁直观
- **jOOQ**: 适合复杂报表、特殊数据类型操作和高级SQL特性，提供完整SQL控制
- **混合架构**: 在大多数项目中，混合使用这些技术能够取得最佳效果

无论选择哪种技术，都应遵循本文档提供的最佳实践，确保查询性能和代码可维护性。

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 1.0 | 2025-07-01 | 初始版本 | AI助手 |
