xkongcloud-test-engine v1通用测试引擎总揽提示词
基于V2智慧继承与历史架构经验引用的统一设计指南

## 🎯 总揽设计目标

### 当前状态分析
- **V2神经可塑性引擎**：已在business-internal-core中完整实施，包含L1感知层、L2认知层、L3理解层的完整实现
- **V3架构设计**：仅存在架构设计文档，未进行代码实施，但包含宝贵的L4智慧层设计理念和架构智慧
- **缺失的L4智慧层**：V2实现了L1-L3，但缺少L4智慧层的统一决策和自动化能力

### 核心使命
构建基于V2神经可塑性智慧继承（L1-L3已实施）和V3架构经验引用（仅文档）的xkongcloud-test-engine v1统一测试引擎，补全L4智慧层，实现所有子项目测试代码的完全替换，确保智能性无损、功能等价、架构更优。

### 设计哲学
- **V2智慧深度继承**：继承已实施的V2神经可塑性分层智能（L1-L3）的核心价值，避免重复造轮子
- **V3架构经验引用**：通过引用方式继承V3设计文档的L4智慧层理念，避免重复设计
- **L4智慧层补全**：基于V2的L1-L3实现，补全缺失的L4智慧层，实现完整的神经可塑性架构
- **功能等价架构优化**：确保L1-L3输出完全一致，但架构实现可以完全不同且更优
- **渐进开发统一替换**：在xkongcloud-commons中渐进式开发，最后统一删除现有测试代码

## 📚 核心文档体系

### 主要设计文档（按执行顺序）
1. **V2神经可塑性引擎通用化抽取提示词.txt**
   - 职责：V2→通用引擎的迁移策略和实施路径
   - 核心：V2架构智慧继承、L1-L3输出一致性验证、渐进开发最后统一替换

2. **通用测试引擎提示词.txt**
   - 职责：通用引擎的整体架构设计和技术实现
   - 核心：四层神经可塑性架构、五大可选引擎、自适应配置管理

3. **xkongcloud-test-engine通用引擎使用指南.txt**
   - 职责：通用引擎的使用方法、场景配置和项目适配
   - 核心：能力组合矩阵、项目类型适配、测试执行流程

4. **字段级版本一致性检查设计提示词.txt**
   - 职责：通用引擎的版本管理机制和配置一致性保证
   - 核心：字段版本控制、跨项目通用化、严格一致性检查

### V3架构设计文档引用（仅文档，未实施）
基于V3架构设计文档的核心智慧，这些文档包含宝贵的L4智慧层设计理念：
- **V2-V3集成架构**：`docs/features/F003-PostgreSQL迁移-20250508/test/engine/v3/code-architecture/01-v2-v3-integration-architecture.md`
  - 核心价值：V3作为V2的L4智慧层实现理念，完全复用V2的L1-L3引擎
- **AI故障三环路处理**：`docs/features/F003-PostgreSQL迁移-20250508/test/engine/v3/code-architecture/02-v3-ai-failure-triple-loop-processor.md`
  - 核心价值：智能故障处理机制，第一环路80%、第二环路19%、第三环路1%的分层处理
- **自动修复执行器**：`docs/features/F003-PostgreSQL迁移-20250508/test/engine/v3/code-architecture/03-v3-auto-repair-executor-design.md`
  - 核心价值：自动化修复和执行机制设计
- **AI数据契约**：`docs/features/F003-PostgreSQL迁移-20250508/test/engine/v3/code-architecture/04-v3-code-ai-data-contract.md`
  - 核心价值：AI数据契约和版本管理机制
- **参数化推演引擎**：`docs/features/F003-PostgreSQL迁移-20250508/test/engine/v3/code-architecture/05-v3-business-simulation-engine-design.md`
  - 核心价值：零业务耦合的参数化通用引擎设计理念

## 🏗️ 统一架构设计原则

### V2核心智慧继承（已实施的L1-L3）
```java
// 继承V2已实施的神经可塑性分层智能理念
L1感知层：如人脑感官收集技术细节但不做判断（✅ 已在business-internal-core中实施）
L2认知层：如人脑模式识别发现关联和模式（✅ 已在business-internal-core中实施）
L3理解层：如人脑逻辑分析进行架构风险评估（✅ 已在business-internal-core中实施）
L4智慧层：如人脑决策中枢实现智能自动化（❌ 缺失，需要在通用引擎中补全）

// 继承V2已验证的类型安全接口设计
LayerProcessor<INPUT,OUTPUT> // 泛型接口确保编译时类型检查（✅ 已验证有效）
@NeuralUnit(layer="L1", type="PERCEPTION") // 声明式架构组件标识（✅ 已验证有效）
```

### V3架构经验引用（基于设计文档，补全L4智慧层）
```java
// xkongcloud-test-engine v1 = V2的L4智慧层实现理念（基于V3设计文档）
@Component
@NeuralUnit(layer = "L4", type = "WISDOM")
public class UniversalTestEngine implements LayerProcessor<L3ArchitecturalData, L4WisdomData> {
    @Autowired private L1PerceptionEngine l1Engine;  // 直接复用V2已实施引擎
    @Autowired private L2CognitionEngine l2Engine;   // 零代码修改
    @Autowired private L3UnderstandingEngine l3Engine; // 完全兼容

    // 补全缺失的L4智慧层能力
    public L4WisdomData process(L3ArchitecturalData input) {
        // 基于V3设计文档的L4智慧层实现
    }
}

// AI三环路智能处理机制（基于V3设计文档）
第一环路：快速诊断处理（80%问题解决）
第二环路：深度分析处理（19%问题解决）
第三环路：人工移交处理（1%问题需要人工）

// 环境感知透明度设计（基于V3设计文档）
EnvironmentAwareness awareness = environmentAwareness.getCurrentAwareness();
// 环境类型：MOCK_DIAGNOSTIC、REAL_TESTCONTAINERS、PRODUCTION_LIKE

// 参数化零业务耦合设计（基于V3设计文档）
Object executionResult = parameterInjectionManager.injectParametersToService(
    action.getTargetService(), action.getTargetMethod(), actionParameters);
```

## 🎯 统一实施策略

### 渐进开发最后统一替换路径
```
第一阶段：V2架构全面分析
├── 现有V2架构深度分析：全面分析V2代码架构、测试覆盖范围、性能基准
├── 测试场景完整梳理：梳理所有现有测试场景、业务逻辑覆盖、数据流分析
├── 性能和智能性基准建立：建立V2现有性能基准和智能分析能力的详细基准
└── 通用引擎渐进开发计划：制定通用引擎的渐进式开发计划和里程碑

第二阶段：通用引擎渐进性开发
├── 核心引擎模块渐进开发：L1-L4神经可塑性引擎按模块渐进式开发
├── 可选引擎模块渐进开发：KV模拟、持久化重建、Service推演、接口测试、数据库Mock按需渐进开发
├── core持续集成测试：每个模块开发完成后在business-internal-core中持续测试验证
└── ProjectAdapter框架渐进完善：根据测试反馈渐进完善适配器框架

第三阶段：功能完整性持续验证
├── 模块功能验证：每个开发完成的模块在core中进行功能完整性验证
├── 性能基准持续监控：持续监控通用引擎性能，确保达到或超过V2基准
├── L1-L3输出一致性验证：老代码L1-L3测试结果与新通用引擎L1-L3测试结果必须完全一致
├── 智能性增强验证：持续验证通用引擎的智能分析能力优于V2
└── 集成测试持续执行：在core中持续执行集成测试，确保各模块协同工作

第四阶段：全部完成后统一删除
├── L1-L3输出一致性最终确认：最终确认老代码L1-L3测试结果与通用引擎L1-L3测试结果完全一致
├── 通用引擎功能完整确认：确认通用引擎所有功能模块开发完成并测试通过
├── 开发完整性标准达成：以L1-L3输出一致性作为开发完整的标准
├── V2测试代码统一删除：一次性删除business-internal-core中所有现有V2测试代码
├── 依赖关系完全清理：清理所有V2相关的依赖、配置和残留代码
└── 生产环境最终验证：在生产环境中验证仅使用通用引擎的稳定性和完整性
```

### 精准继承边界控制
```
继承架构智慧：
├── V2神经可塑性分层智能理念（L1-L3已实施，L4需补全）
├── V2类型安全接口设计智慧（已验证有效）
├── V2声明式架构组件标识（已验证有效）
├── V3设计文档的L4智慧层补全理念（仅文档，需实施）
├── V3设计文档三环路处理机制（仅文档，需实施）
├── V3设计文档环境感知透明度设计（仅文档，需实施）
└── V3设计文档参数化零业务耦合设计（仅文档，需实施）

避免重复造轮子：
├── 不继承业务特定逻辑：PostgreSQL迁移等特定业务实现
├── 不继承技术栈绑定：特定Spring版本等技术栈依赖
├── 不继承过度复杂机制：批判性评估复杂设计，避免过度工程
└── 专注核心价值继承：聚焦架构智慧而非具体实现细节
```

## 🎯 开发完整性标准

### 唯一验证标准
**L1-L3输出一致性验证**：老代码和新通用引擎的L1-L3测试结果必须完全一致，但架构实现可以完全不同且更优

### 质量保证要求
- **测试覆盖率保证**：确保通用引擎覆盖现有V2的100%测试场景
- **功能完整性验证**：验证所有业务逻辑测试能力无损迁移
- **性能基准保持**：确保测试执行性能不低于现有V2实现
- **智能性增强验证**：验证通用引擎的分析能力优于V2

## 🚀 使用指导原则

### AI开发者使用指南
1. **优先阅读总揽提示词**：理解整体设计哲学和架构原则
2. **按序参考核心文档**：根据开发阶段选择对应的详细文档
3. **严格遵循继承边界**：继承架构智慧，避免业务特定逻辑
4. **持续验证输出一致性**：以L1-L3输出一致性作为开发标准
5. **引用历史架构经验**：通过引用方式使用现有设计文档，避免重复造轮子

### 关键成功因素
- **独立批判性思维**：深度理解V2已实施架构和V3设计文档的真正价值，避免为了借鉴而借鉴
- **L4智慧层补全**：基于V3设计文档，补全V2缺失的L4智慧层，实现完整的神经可塑性架构
- **渐进式开发验证**：每个模块开发完成后立即在core中验证，确保与V2 L1-L3输出一致
- **智能性无损保证**：确保通用引擎的能力不低于现有V2实现，并通过L4智慧层实现智能增强
- **完全替换目标**：最终实现所有子项目测试代码的统一替换

## 📊 当前状态总结

### 已有资产
- **V2神经可塑性引擎**：L1-L3完整实施，经过验证的架构智慧
- **V3架构设计文档**：L4智慧层设计理念，宝贵的架构经验

### 开发目标
- **xkongcloud-test-engine v1**：基于V2 L1-L3 + V3 L4设计文档，构建完整的通用测试引擎
- **L4智慧层补全**：实现V2缺失的L4智慧层，完成神经可塑性架构闭环
- **统一替换**：最终替换所有子项目的测试代码

这份总揽提示词统一了4份核心文档的设计理念，明确了当前状态和开发目标，为AI开发者提供了清晰的架构指导和实施路径。
