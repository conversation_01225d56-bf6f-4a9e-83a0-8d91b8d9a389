# 01-质量驱动API角色管理架构

## 📋 文档信息

**文档ID**: QUALITY-DRIVEN-API-ROLE-MANAGEMENT-ARCHITECTURE-V1.0  
**基于总设计**: @DRY_REF: API管理核心驱动系统架构.md  
**核心原则**: 质量第一、动态调整、固定定位、Gemini择优  
**权威保障**: 功能零损失、性能零退化(≥91.4分)、稳定性优先(100%成功率)

## 🎯 API角色定位策略

### 固定角色定位（不可改变）

```yaml
# === 固定API角色定位 ===
Fixed_API_Role_Positioning:
  
  # DeepSeek R1-0528：架构专家（固定定位）
  deepseek_r1_0528:
    role: "架构专家"
    positioning: "FIXED"  # 不可改变
    core_capabilities:
      - "算法思维日志系统"
      - "架构设计推理" 
      - "复杂逻辑分析"
    test_baseline: 92  # 基于测试数据
    token_config: "6K tokens"
    
  # DeepSeek V3-0324：复杂逻辑处理专家（固定定位）
  deepseek_v3_0324:
    role: "复杂逻辑处理专家"
    positioning: "FIXED"  # 不可改变
    core_capabilities:
      - "思维质量审查器"
      - "魔鬼审问者质询"
      - "逻辑链验证"
    test_baseline: 90  # 基于测试数据
    token_config: "8K tokens"
    
  # DeepCoder-14B：代码生成王者（固定定位）
  deepcoder_14b:
    role: "代码生成王者"
    positioning: "FIXED"  # 不可改变
    core_capabilities:
      - "代码生成"
      - "技术实现分析"
      - "集成方案设计"
    test_baseline: 85  # 推测基准
    token_config: "4K-8K tokens"
```

### 角色化配置驱动（替代动态择优）

```yaml
# === 角色化配置驱动策略 ===
Role_Based_Configuration_Strategy:

  # 基于角色的配置管理，支持Gemini通才场景
  configuration_driven:
    approach: "角色优先，配置驱动"
    gemini_positioning: "多角色通才"  # 在多个角色中可用
    comparison_targets:
      - deepseek_r1_0528
      - deepseek_v3_0324  
      - deepcoder_14b
    
    # Gemini已知缺点
    known_weaknesses:
      api_stability: "配额重置、临时Key失效"
      quota_management: "每日15:00重置，临时Key用完即失效"
      reliability_concern: "永久中断风险"
    
    # 择优判断标准
    selection_criteria:
      quality_threshold: 0.05  # 需要超出固定API 5%才择优
      stability_weight: 0.4    # 稳定性权重40%
      performance_weight: 0.6  # 性能权重60%
      
    # 择优决策逻辑
    selection_logic: |
      if (gemini_quality > fixed_api_quality + 0.05) and 
         (gemini_stability >= 0.85):
          return "SELECT_GEMINI"
      else:
          return "KEEP_FIXED_API"
```

## 🔄 质量驱动角色管理器

### 核心管理器实现

```python
# === 质量驱动API角色管理器 ===
# @DRY_REF: API管理核心驱动系统架构.md#APICoreManagementDriveSystem

from api_management.sqlite_storage.api_account_database import APIAccountDatabase
from api_management.account_management.api_failover_manager import APIFailoverManager

class QualityDrivenSelectionEngine:
    """
    质量驱动API角色管理器
    
    核心功能：
    1. 维护固定API角色定位（DeepSeek系列、DeepCoder）
    2. 动态评估Gemini与固定API的质量对比
    3. 基于质量择优选择最佳API
    4. 确保功能零损失、性能零退化、稳定性优先
    """
    
    def __init__(self):
        # @DRY_REF: 复用现有核心组件
        self.api_db = APIAccountDatabase()
        self.failover_manager = APIFailoverManager(self.api_db)
        
        # 固定角色定义（不可改变）
        self.fixed_roles = {
            'deepseek_r1_0528': {
                'role': '架构专家',
                'baseline_score': 0.92,
                'core_tasks': ['算法思维日志系统', '架构设计推理']
            },
            'deepseek_v3_0324': {
                'role': '复杂逻辑处理专家', 
                'baseline_score': 0.90,
                'core_tasks': ['思维质量审查器', '魔鬼审问者质询']
            },
            'deepcoder_14b': {
                'role': '代码生成王者',
                'baseline_score': 0.85,
                'core_tasks': ['代码生成', '技术实现分析']
            }
        }
        
        # Gemini择优配置
        self.gemini_config = {
            'api_key_pool': [],  # Gemini API Key池
            'quality_threshold': 0.05,  # 5%质量优势阈值
            'stability_requirement': 0.85,  # 85%稳定性要求
            'comparison_interval': 3600  # 1小时重新评估一次
        }
        
        # 当前最优API分配
        self.current_optimal_assignment = {}
    
    def evaluate_gemini_vs_fixed_apis(self, task_type: str) -> Dict:
        """
        评估Gemini与固定API的质量对比
        
        @DRY_REF: API管理核心驱动系统架构.md#drive_comprehensive_api_testing
        """
        comparison_results = {
            'task_type': task_type,
            'comparison_timestamp': datetime.now().isoformat(),
            'candidates': {},
            'optimal_selection': None,
            'selection_reason': ''
        }
        
        # 1. 获取任务相关的固定API
        relevant_fixed_apis = self._get_relevant_fixed_apis(task_type)
        
        # 2. 评估每个固定API的当前质量
        for api_key in relevant_fixed_apis:
            quality_score = self._evaluate_api_quality(api_key, task_type)
            comparison_results['candidates'][api_key] = {
                'type': 'fixed',
                'quality_score': quality_score,
                'baseline_score': self.fixed_roles[api_key]['baseline_score']
            }
        
        # 3. 评估Gemini质量（如果可用）
        if self._is_gemini_available():
            gemini_quality = self._evaluate_gemini_quality(task_type)
            gemini_stability = self._evaluate_gemini_stability()
            
            comparison_results['candidates']['gemini_2_5_pro'] = {
                'type': 'dynamic',
                'quality_score': gemini_quality,
                'stability_score': gemini_stability,
                'api_stability_risk': self._assess_gemini_stability_risk()
            }
        
        # 4. 择优决策
        optimal_api = self._make_optimal_selection(comparison_results['candidates'], task_type)
        comparison_results['optimal_selection'] = optimal_api
        comparison_results['selection_reason'] = self._generate_selection_reason(optimal_api, comparison_results['candidates'])
        
        return comparison_results
    
    def _evaluate_api_quality(self, api_key: str, task_type: str) -> float:
        """
        评估API质量
        
        @DRY_REF: API管理核心驱动系统架构.md#ThinkingQualityAuditorEnhanced
        """
        # 基于测试数据的质量评估
        performance_data = self.api_db.get_performance_summary(api_key)
        
        if not performance_data:
            # 使用基准分数
            return self.fixed_roles.get(api_key, {}).get('baseline_score', 0.8)
        
        # 综合质量评分
        quality_factors = {
            'thinking_quality': performance_data.get('thinking_score', 90) / 100 * 0.35,
            'confidence_convergence': performance_data.get('confidence_score', 95) / 100 * 0.25,
            'response_stability': performance_data.get('success_rate', 1.0) * 0.20,
            'task_compatibility': self._calculate_task_compatibility(api_key, task_type) * 0.15,
            'recent_performance': performance_data.get('recent_avg_score', 90) / 100 * 0.05
        }
        
        return sum(quality_factors.values())
    
    def _make_optimal_selection(self, candidates: Dict, task_type: str) -> str:
        """
        基于质量对比做出最优选择
        
        核心逻辑：
        1. 固定API优先（稳定性保障）
        2. Gemini需要显著质量优势才被选择
        3. 考虑API稳定性风险
        """
        # 分离固定API和Gemini
        fixed_apis = {k: v for k, v in candidates.items() if v['type'] == 'fixed'}
        gemini_candidate = candidates.get('gemini_2_5_pro')
        
        # 找出质量最佳的固定API
        best_fixed_api = max(fixed_apis.items(), key=lambda x: x[1]['quality_score'])
        best_fixed_quality = best_fixed_api[1]['quality_score']
        
        # 如果没有Gemini候选，直接返回最佳固定API
        if not gemini_candidate:
            return best_fixed_api[0]
        
        # Gemini择优判断
        gemini_quality = gemini_candidate['quality_score']
        gemini_stability = gemini_candidate['stability_score']
        
        # 质量优势判断
        quality_advantage = gemini_quality - best_fixed_quality
        
        # 择优决策
        if (quality_advantage > self.gemini_config['quality_threshold'] and 
            gemini_stability >= self.gemini_config['stability_requirement']):
            return 'gemini_2_5_pro'
        else:
            return best_fixed_api[0]
```

## 🧠 Gemini专用优化服务

### 架构职责说明
```
正确的调用链：
指挥官系统 → API管理器/调度引擎 → [如果选择Gemini] → GeminiOptimizationManager
                                ↓
                            [如果选择DeepSeek] → 直接调用标准API

错误的调用链：
指挥官系统 → 直接调用GeminiOptimizationManager ❌
```

### GeminiOptimizationManager实现
```python
# === 统一Gemini优化管理器 - 单例服务 ===
# @DRY_REF: 整合所有Gemini相关优化，避免重复组件

import threading
from typing import Dict, Optional
from datetime import datetime

class GeminiOptimizationProcessor:
    """
    Gemini优化处理器 - 门面内部组件

    处理器职责：仅在选择Gemini能力时提供优化处理
    1. thinking配置和提示词优化 (基于Google官方文档)
    2. 性能参数优化 (@DRY_REF: 复用现有实测配置数据)
    3. Gemini专用请求配置生成

    注意：不负责能力选择，只负责Gemini能力的优化处理
    """

    def __init__(self):
        # @DRY_REF: 复用现有质量对比组件
        self.quality_comparator = GeminiQualityComparator()

        # 基于Google官方文档的thinking配置
        self.thinking_configs = {
            "simple": {"thinkingBudget": 0, "includeThoughts": False},
            "medium": {"thinkingBudget": 1024, "includeThoughts": True},
            "complex": {"thinkingBudget": 8192, "includeThoughts": True},
            "dynamic": {"thinkingBudget": -1, "includeThoughts": True}  # 动态thinking
        }

        # 提示词优化模板
        self.thinking_templates = {
            "architecture": "请仔细分析架构设计，展示你的思考过程：",
            "code_review": "请深入思考代码质量，展示你的推理过程：",
            "problem_solving": "请逐步分析问题，展示你的解决思路："
        }

        # @DRY_REF: 复用现有实测性能配置
        self.performance_configs = {
            "high_stability": {"max_tokens": 8000, "timeout": 300},      # 99%稳定性
            "balanced": {"max_tokens": 16000, "timeout": 240},           # 95%稳定性
            "high_performance": {"max_tokens": 32000, "timeout": 420}    # 85%稳定性
        }

        # 注意：移除API选择逻辑，这应该由API管理器负责

    def optimize_gemini_request(self, prompt: str, task_type: str, complexity: str = "medium") -> Dict:
        """
        统一的Gemini请求优化
        整合提示词优化 + thinking配置 + 性能配置
        """
        # 1. 提示词优化
        if complexity != "simple":
            thinking_prefix = self.thinking_templates.get(task_type, "请仔细思考以下问题：")
            enhanced_prompt = f"""{thinking_prefix}

{prompt}

请先进行深入思考分析，然后给出最终答案。"""
        else:
            enhanced_prompt = prompt

        # 2. thinking配置
        thinking_config = self.thinking_configs[complexity]

        # 3. 性能配置
        perf_config = self.performance_configs["balanced"]  # 默认平衡配置

        # 4. 构造符合Google官方文档的API请求
        return {
            "contents": [{"parts": [{"text": enhanced_prompt}]}],
            "generationConfig": {
                "thinkingConfig": {
                    "thinkingBudget": thinking_config["thinkingBudget"],
                    "includeThoughts": thinking_config["includeThoughts"]
                },
                "maxOutputTokens": perf_config["max_tokens"],
                "temperature": 0.1
            },
            "timeout": perf_config["timeout"],
            "optimization_applied": True,
            "api_format": "gemini"
        }

    def optimize_for_gemini(self, prompt: str, task_type: str, complexity: str = "medium") -> Dict:
        """
        专门为Gemini API优化请求配置

        注意：此方法仅在确定使用Gemini时调用，不负责API选择
        """
        # 使用优化后的Gemini配置
        api_config = self.optimize_gemini_request(prompt, task_type, complexity)
        api_config["api_type"] = "gemini"
        api_config["optimization_applied"] = True
        return api_config

    def get_service_status(self) -> Dict:
        """获取服务状态"""
        return {
            'service_name': 'GeminiOptimizationManager',
            'status': 'RUNNING',
            'thinking_configs_loaded': len(self.thinking_configs),
            'performance_configs_loaded': len(self.performance_configs),
            'quality_threshold': self.selection_config['quality_threshold'],
            'stability_requirement': self.selection_config['stability_requirement']
        }

# === 单例服务访问接口 ===

_gemini_optimization_service_instance = None
_service_lock = threading.Lock()

def get_gemini_optimization_service() -> GeminiOptimizationManager:
    """
    获取Gemini优化服务单例实例

    为所有其他文档提供统一的服务访问接口
    """
    global _gemini_optimization_service_instance

    if _gemini_optimization_service_instance is None:
        with _service_lock:
            if _gemini_optimization_service_instance is None:
                _gemini_optimization_service_instance = GeminiOptimizationManager()

    return _gemini_optimization_service_instance
```

## 📊 API稳定性风险评估

### Gemini稳定性监控

```python
class GeminiStabilityMonitor:
    """
    Gemini API稳定性监控器
    
    专门监控Gemini的已知稳定性问题：
    1. 配额重置（每日15:00北京时间）
    2. 临时Key失效
    3. 永久中断风险
    """
    
    def __init__(self):
        self.stability_history = {}
        self.quota_reset_schedule = "15:00"  # 北京时间
        self.risk_thresholds = {
            'consecutive_failures': 3,
            'daily_quota_exhaustion_rate': 0.8,
            'temporary_key_failure_rate': 0.3
        }
    
    def assess_gemini_stability_risk(self) -> Dict:
        """评估Gemini当前稳定性风险"""
        
        risk_assessment = {
            'overall_risk_level': 'LOW',
            'risk_factors': {},
            'recommendations': [],
            'fallback_readiness': True
        }
        
        # 1. 配额重置风险
        quota_risk = self._assess_quota_reset_risk()
        risk_assessment['risk_factors']['quota_reset'] = quota_risk
        
        # 2. 临时Key失效风险  
        temp_key_risk = self._assess_temporary_key_risk()
        risk_assessment['risk_factors']['temporary_key'] = temp_key_risk
        
        # 3. API连续失败风险
        failure_risk = self._assess_consecutive_failure_risk()
        risk_assessment['risk_factors']['consecutive_failures'] = failure_risk
        
        # 4. 综合风险评估
        overall_risk = self._calculate_overall_risk(risk_assessment['risk_factors'])
        risk_assessment['overall_risk_level'] = overall_risk
        
        # 5. 生成建议
        risk_assessment['recommendations'] = self._generate_stability_recommendations(risk_assessment)
        
        return risk_assessment
    
    def _assess_quota_reset_risk(self) -> Dict:
        """评估配额重置风险"""
        import pytz
        from datetime import datetime, time
        
        beijing_tz = pytz.timezone('Asia/Shanghai')
        now = datetime.now(beijing_tz)
        reset_time = time(15, 0)  # 15:00
        
        # 计算距离下次重置的时间
        today_reset = datetime.combine(now.date(), reset_time)
        today_reset = beijing_tz.localize(today_reset)
        
        if now > today_reset:
            # 今天已经重置过，计算明天重置时间
            tomorrow_reset = today_reset + timedelta(days=1)
            time_to_reset = tomorrow_reset - now
        else:
            # 今天还未重置
            time_to_reset = today_reset - now
        
        hours_to_reset = time_to_reset.total_seconds() / 3600
        
        # 风险评估
        if hours_to_reset < 2:
            risk_level = 'HIGH'
            risk_message = f"距离配额重置仅剩{hours_to_reset:.1f}小时"
        elif hours_to_reset < 6:
            risk_level = 'MEDIUM'
            risk_message = f"距离配额重置{hours_to_reset:.1f}小时"
        else:
            risk_level = 'LOW'
            risk_message = f"配额重置还有{hours_to_reset:.1f}小时"
        
        return {
            'risk_level': risk_level,
            'hours_to_reset': hours_to_reset,
            'message': risk_message,
            'next_reset_time': today_reset.isoformat() if now <= today_reset else tomorrow_reset.isoformat()
        }
```

## 🎯 质量保障机制

### 权威性保障

```python
class QualityAssuranceGuard:
    """
    质量保障护栏
    
    确保优化过程中的三大权威要求：
    1. 功能零损失
    2. 性能零退化（≥91.4分基准）
    3. 稳定性优先（100%成功率）
    """
    
    def __init__(self):
        # 基于测试报告的权威基准
        self.authority_baselines = {
            'functionality_completeness': 1.0,  # 100%功能完整性
            'performance_baseline': 91.4,       # 91.4分性能基准
            'stability_baseline': 1.0,          # 100%成功率基准
            'thinking_quality_baseline': 0.95,  # 95%thinking质量基准
            'confidence_convergence_baseline': 0.98  # 98%置信度收敛基准
        }
    
    def validate_optimization_compliance(self, optimization_result: Dict) -> Dict:
        """验证优化结果是否符合权威要求"""
        
        compliance_check = {
            'overall_compliance': True,
            'compliance_details': {},
            'violations': [],
            'recommendations': []
        }
        
        # 1. 功能完整性检查
        functionality_check = self._check_functionality_completeness(optimization_result)
        compliance_check['compliance_details']['functionality'] = functionality_check
        
        if not functionality_check['compliant']:
            compliance_check['overall_compliance'] = False
            compliance_check['violations'].append('功能完整性违规')
        
        # 2. 性能基准检查
        performance_check = self._check_performance_baseline(optimization_result)
        compliance_check['compliance_details']['performance'] = performance_check
        
        if not performance_check['compliant']:
            compliance_check['overall_compliance'] = False
            compliance_check['violations'].append('性能基准违规')
        
        # 3. 稳定性基准检查
        stability_check = self._check_stability_baseline(optimization_result)
        compliance_check['compliance_details']['stability'] = stability_check
        
        if not stability_check['compliant']:
            compliance_check['overall_compliance'] = False
            compliance_check['violations'].append('稳定性基准违规')
        
        return compliance_check
```

## 📋 实施要求

### 核心实施原则

1. **固定定位不可改变** - DeepSeek R1-0528、V3-0324、DeepCoder角色固定
2. **Gemini择优对比** - 仅Gemini需要与固定API深度对比
3. **质量驱动决策** - 基于实时质量数据动态选择最优API
4. **稳定性优先** - 考虑Gemini的API稳定性风险
5. **权威基准保障** - 确保功能零损失、性能零退化、稳定性优先

## 🏗️ 基于V45容器架构的API管理器

## 📋 V45容器架构调用方式

### **V45容器架构调用原则**
根据V45容器架构要求，所有组件调用都必须通过状态容器进行：

```python
# === V45容器架构的正确调用方式 ===

# 1. 指挥官系统调用（通过容器）
result = await self.container_component_call(
    "api_manager",
    "request_ai_assistance",
    {
        "task_description": "设计一个支持V4.5三维融合的微服务架构",
        "category": "架构专家",  # 角色化参数
        "complexity_level": "complex",
        "thinking_mode": "v45_fusion",
        "devil_advocate": True,
        "ai_coordination_mode": "v45_enhanced"
    }
)

# 2. Web界面调用（通过容器）
result = await commander.container_component_call(
    "api_manager",
    "request_ai_assistance",
    {
        "task_description": prompt,
        "category": task_category,  # 角色化参数
        "complexity_level": complexity
    }
)

# 3. 因果系统调用（通过容器）
result = await commander.container_component_call(
    "api_manager",
    "request_ai_assistance",
    {
        "task_description": f"分析数据的因果关系：{data_description}",
        "category": "逻辑处理专家",  # 角色化参数
        "complexity_level": "complex"
    }
)
```

### **4AI协同架构澄清（V45容器模式）**
通过代码调查发现，4AI协同在V45容器架构下的正确架构是：

```python
# === 4AI协同的V45容器架构 ===

# 4AI协同包括：
four_ai_collaboration = {
    "IDE_AI": {
        "role": "事实验证权威",
        "weight": 0.30,
        "call_method": "MCP调用",  # ← 关键：不通过API管理器
        "description": "通过MCP连接服务器，由IDE指派任务调用"
    },
    "Python_AI_1": {
        "role": "架构推导专家",
        "weight": 0.25,
        "call_method": "容器调用API管理器",  # ← 通过容器调用API管理器
        "api_capability": "deepseek_r1_0528",
        "container_call": "container_component_call('api_manager', 'request_ai_assistance', data)"
    },
    "Python_AI_2": {
        "role": "逻辑推导专家",
        "weight": 0.25,
        "call_method": "容器调用API管理器",  # ← 通过容器调用API管理器
        "api_capability": "deepseek_v3_0324",
        "container_call": "container_component_call('api_manager', 'request_ai_assistance', data)"
    },
    "Python_AI_3": {
        "role": "质量推导专家",
        "weight": 0.20,
        "call_method": "容器调用API管理器",  # ← 通过容器调用API管理器
        "api_capability": "deepcoder_14b",
        "container_call": "container_component_call('api_manager', 'request_ai_assistance', data)"
    }
}

# API管理器的管理范围（V45容器模式）
api_manager_scope = {
    "管理": ["Python_AI_1", "Python_AI_2", "Python_AI_3", "其他API模型"],
    "不管理": ["IDE_AI"],  # IDE AI通过MCP独立调用
    "调用方式": "通过UniversalProjectContainer.component_call()路由"
}
```

### **实例管理现状分析**

#### **单例组件（全局唯一）**
```python
# 1. API管理器本身
class TaskBasedAIServiceManager:
    _instance = None  # 单例模式

# 2. 配置中心
class SimpleConfigurationCenter:
    _instances = {}  # 按配置文件路径缓存实例

# 3. 服务器启动器
class ServerLauncher:
    _instance = None  # 单例模式

# 4. 项目上下文管理器
class ProjectContextManager:
    # 全局单例服务
```

#### **多实例组件（项目级隔离）**
```python
# 1. 项目容器
self.project_containers = {}  # {client_id: UniversalProjectContainer}

# 2. 项目指挥官
self.project_commanders = {}  # {client_id: PythonHostCoreEngine}

# 3. Meeting目录服务
# 每个项目有独立的Meeting目录实例

# 4. 因果系统
# 每个项目有独立的因果分析实例
```

### V45容器架构集成设计
```python
# === 基于V45容器架构的API管理器 - 容器集成设计 ===

import threading
from typing import Dict, Optional, Any
from datetime import datetime

# 基于现有架构的导入
from api_management.sqlite_storage.api_account_database import APIAccountDatabase
from api_management.account_management.api_failover_manager import APIFailoverManager
from api_management.account_management.unified_model_pool_butler import UnifiedModelPoolButler

class TaskBasedAIServiceManager:
    """
    基于任务类别的AI服务管理器 - V45容器架构集成设计

    核心设计原则：
    1. 单例模式 - 全局唯一的AI服务管理器
    2. 高内聚 - 所有AI相关功能集中管理
    3. 任务驱动 - 基于任务类别而非具体模型
    4. 内部实现 - 模型选择和优化在内部完成
    5. 容器集成 - 与V45容器架构完全集成
    6. 容器调用 - 通过UniversalProjectContainer.component_call()调用
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            # 基于现有架构的核心组件
            self._api_db = APIAccountDatabase()
            self._failover_manager = APIFailoverManager(self._api_db)
            self._pool_butler = UnifiedModelPoolButler(self._api_db, self._failover_manager)
            self._quality_engine = QualityDrivenSelectionEngine()
            self._gemini_processor = GeminiOptimizationProcessor()

            # 角色化配置驱动架构（替代硬编码映射）
            # 通过CategoryBasedAPISelector实现配置驱动的API选择
            from api_management.core.category_based_api_selector import get_category_based_api_selector
            self._category_api_selector = get_category_based_api_selector()

            # 配置位置：common_config.json中的api_category_mappings
            # 支持角色化管理：架构专家、代码生成专家、逻辑处理专家、多模态专家、通用专家
            # 版本演进友好：新版本通过配置添加，无需修改代码

            self.initialized = True

    # === API配置管理工作流程 ===

    def store_api_configuration(self, api_key: str, config_data: dict) -> dict:
        """
        API配置存储工作流程

        基于规范名称映射的严格验证工作流程：
        1. 用户输入API + 模型名称
        2. 系统通过相似度匹配或用户指定，必须映射到规范名称
        3. 验证规范名称存在（基于common_config.json统一配置管理器）
        4. 如果没有对应规范名称则不能存入数据库
        5. 存入数据库的API-模型映射表
        6. 支持一个API对应多个不同的模型名称

        工作流程详细步骤：

        步骤1：规范名称映射验证
        ├── 输入：用户提供的API key + 模型名称
        ├── 处理：通过相似度匹配算法映射到标准规范名称
        ├── 验证：检查规范名称是否在统一配置管理器中存在
        └── 结果：映射成功 → 继续；映射失败 → 拒绝存储

        步骤2：规范配置获取
        ├── 输入：验证通过的规范名称
        ├── 处理：从统一配置管理器获取标准角色和类型信息
        ├── 合并：用户输入配置 + 规范配置
        └── 结果：生成增强配置数据

        步骤3：数据库存储（支持1对多关系）
        ├── 加密存储：使用现有加密机制存储API配置
        ├── 映射表存储：存储到api_model_mappings表（支持1对多）
        ├── 兼容性保持：同时更新原有api_configurations表
        └── 结果：返回存储成功状态和规范名称信息

        类别映射架构设计（基于JSON配置）：
        ├── 配置位置：common_config.json统一配置管理器
        ├── 映射结构：类别名称 → 标准名称列表（1对多关系）
        ├── 示例配置：
        │   "api_category_mappings": {
        │     "架构专家": ["deepseek_r1_0528", "deepseek_r2_0101"],
        │     "代码生成专家": ["deepcoder_14b", "deepcoder_v2"],
        │     "逻辑处理专家": ["deepseek_v3_0324"]
        │   }
        ├── 版本演进支持：新版本不覆盖旧版本，支持多版本共存
        └── 验证系统查询：通过类别名称获取所有可用标准名称

        相似度匹配算法：
        ├── 去除厂商前缀：gmi_、chutes_、temp_等
        ├── 模型名称特征匹配：deepseek + r1 + 0528 → deepseek_r1_0528
        ├── 提供商推断：基于模型名称模式推断提供商
        └── 优先级排序：精确匹配 > 特征匹配 > 拒绝

        数据库表结构（标准名称配对 + 1对多支持）：
        api_model_mappings表：
        ├── actual_api_key：实际API密钥名称（如：gmi_deepseek_r1_0528）
        ├── standard_name：标准名称，用于配对使用（如：deepseek_r1_0528）
        ├── model_name：实际模型名称（如：deepseek-ai/DeepSeek-R1-0528）
        ├── provider：推断的提供商（如：deepseek）
        ├── priority：优先级（支持同一标准名称多个实际API）
        ├── status：模型状态（active/failed/degraded）
        └── 设计原则：标准名称支持1对多关系，不存储类别映射

        错误处理机制：
        ├── 无法映射规范名称：抛出ValueError，拒绝存储
        ├── 规范名称不存在：抛出ValueError，拒绝存储
        ├── 数据库存储失败：回滚操作，返回错误信息
        └── 加密失败：使用备用存储机制

        示例工作流程（存储）：
        输入：api_key="gmi_deepseek_r1_0528", model_name="deepseek-ai/DeepSeek-R1-0528"
        ↓
        映射：gmi_deepseek_r1_0528 → deepseek_r1_0528（规范名称）
        ↓
        验证：deepseek_r1_0528 在配置中存在 ✓
        ↓
        获取：role="架构专家", api_type="permanent", priority=1
        ↓
        存储：双名称存储到数据库
        　├── actual_api_key: "gmi_deepseek_r1_0528"（原始名称）
        　├── standard_name: "deepseek_r1_0528"（规范名称）
        　└── model_name: "deepseek-ai/DeepSeek-R1-0528"
        ↓
        返回：{"success": True, "standard_name": "deepseek_r1_0528", "role": "架构专家"}

        验证系统查询流程（类别驱动）：
        请求：验证系统要评估"架构专家"质量
        ↓
        配置查询：从common_config.json获取 ["deepseek_r1_0528", "deepseek_r2_0101"]
        ↓
        数据库查询：通过standard_name查找所有对应的actual_api_key
        ↓
        结果：获得所有"架构专家"类别下的实际API列表
        ↓
        选择：基于质量数据选择最优API进行测试

        直接API调用流程（兼容现有）：
        请求：使用API "gmi_deepseek_r1_0528" 进行AI调用
        ↓
        查找：通过actual_api_key="gmi_deepseek_r1_0528"查找数据库记录
        ↓
        获取：standard_name="deepseek_r1_0528"
        ↓
        调用：使用原始API key进行实际调用

        关键架构优势：
        ├── 规范化管理：通过standard_name统一角色和配置管理
        ├── 调用兼容：通过actual_api_key确保原始API可调用
        ├── 1对多支持：同一standard_name可对应多个actual_api_key
        └── 向后兼容：不破坏现有API调用机制

## **角色化配置驱动架构（重构后）**

### **核心设计原则**
```
角色优先：基于"架构专家"、"代码生成专家"等角色，而非具体模型名
配置驱动：所有决策通过common_config.json配置，支持版本演进
质量对比：GeminiQualityComparator实现多维度智能择优
统一入口：CategoryBasedAPISelector提供统一的API选择接口
```

### **角色化映射架构**
```json
// common_config.json中的api_category_mappings配置
{
  "api_category_mappings": {
    "架构专家": ["deepseek_r1_0528", "gemini_2_5_pro"],
    "代码生成专家": ["deepcoder_14b", "deepseek_v3_0324"],
    "逻辑处理专家": ["deepseek_v3_0324", "deepseek_r1_0528"],
    "多模态专家": ["gemini_2_5_pro"],
    "通用专家": ["gemini_2_5_pro", "deepseek_r1_0528"]
  }
}
```

### **统一决策流程**
```
用户请求 → 角色查询 → 配置获取 → 质量对比 → 优化选择 → API调用
```

### **核心组件架构**

#### **CategoryBasedAPISelector（统一选择器）**
```python
class CategoryBasedAPISelector:
    """角色化配置驱动的API选择器"""

    async def select_api_by_category(self, category_name: str, context: Dict) -> str:
        """基于角色类别选择最优API"""
        # 1. 从配置获取角色对应的标准名称列表
        # 2. 查询数据库获取所有实际API
        # 3. 使用GeminiQualityComparator进行质量对比
        # 4. 返回最优API选择
```

#### **GeminiQualityComparator（质量对比器）**
```python
class GeminiQualityComparator:
    """Gemini与固定API的多维度质量对比"""

    # 基于角色的基准数据（非硬编码模型名）
    role_based_benchmarks = {
        '架构专家': {'quality_baseline': 0.92, 'stability_baseline': 0.95},
        '代码生成专家': {'quality_baseline': 0.89, 'stability_baseline': 0.93},
        '逻辑处理专家': {'quality_baseline': 0.87, 'stability_baseline': 0.96}
    }

    async def compare_and_select(self, candidates: List[Dict], context: Dict) -> str:
        """多维度质量对比和择优决策"""
```

#### **GeminiOptimizationManager（优化管理器）**
```python
class GeminiOptimizationManager:
    """Gemini专项优化管理"""

    def get_optimized_thinking_config(self, task_category: str, complexity_level: str) -> Dict:
        """获取针对角色优化的Thinking配置"""

    def evaluate_quality_threshold(self, task_category: str, competitor_roles: List[str]) -> Dict:
        """基于角色竞争评估质量阈值（非硬编码模型名）"""
```

### **版本演进支持**
```
新版本添加：
1. 在common_config.json中添加新的标准名称
2. 无需修改任何代码逻辑
3. 自动支持多版本共存

示例：Gemini 3.0发布
"多模态专家": ["gemini_3_0_pro", "gemini_2_5_pro"]  // 配置文件修改
```

### **Gemini通才场景支持**
```
通才配置：Gemini可在多个角色中使用
"架构专家": ["deepseek_r1_0528", "gemini_2_5_pro"],
"多模态专家": ["gemini_2_5_pro"],
"通用专家": ["gemini_2_5_pro", "deepseek_r1_0528"]

智能选择：基于质量对比在不同角色中择优使用Gemini
```
        """
        return self._api_db.store_api_configuration(api_key, config_data)

    # === V45容器架构集成方法 ===

    def register_to_container(self, container):
        """注册到V45容器中"""
        container.register_component("api_manager", {
            "service_type": "ai_service_manager",
            "singleton": True,
            "methods": ["request_ai_assistance"],
            "status": "ready"
        })
        print("✅ API管理器已注册到V45容器")

    async def request_ai_assistance(
        self,
        task_description: str,
        category: str,                  # 角色类别（架构专家、代码生成专家等）
        complexity_level: str = "medium",
        priority: str = "normal",
        context_info: Dict = None,
        # === 弹性扩展设计 ===
        advanced_config: Dict = None,  # 高级配置对象
        **kwargs                       # 完全开放的扩展参数
    ) -> Dict:
        """
        弹性AI服务接口 - V45容器架构集成版（完全向后兼容）

        === 重要说明 ===
        此方法通过V45容器架构调用：
        commander.container_component_call("api_manager", "request_ai_assistance", data)

        === 核心参数（必需） ===
        - task_description: 任务内容描述（自然语言）
        - category: 角色类别（架构专家、代码生成专家、逻辑处理专家、多模态专家、通用专家）

        === 角色化配置驱动 ===
        所有API选择基于角色类别，通过common_config.json中的api_category_mappings配置管理
        支持版本演进：新版本API通过配置添加，无需修改代码

        === 基础参数（可选） ===
        - complexity_level: 复杂度级别（simple, medium, complex）
        - priority: 优先级（low, normal, high, urgent）
        - context_info: 上下文信息（Dict格式，如{"language": "python", "framework": "django"}）

        === 弹性扩展设计 ===
        - advanced_config: 高级配置对象，支持任意复杂配置
          * thinking_mode: "standard", "enhanced", "v45_fusion"
          * ai_coordination_mode: "single", "4ai_collaboration", "v45_enhanced"
          * devil_advocate: True/False
          * confidence_target: 0.95, 0.933等
          * reasoning_depth: "standard", "deep", "twelve_layer_matrix"
          * session_context: 会话上下文信息
          * 以及任何未来的新功能配置

        - **kwargs: 完全开放的扩展参数，支持未来任何新功能
          * 任何新参数都可以直接传入，无需修改接口
          * 内部智能路由自动识别参数类型
          * 向后兼容保证

        === 返回格式 ===
        标准格式的AI协助结果：
        - success: 成功状态
        - content: AI响应内容
        - task_category: 处理的任务类别
        - capability_used: 使用的AI能力（内部选择，外部不可见具体模型）
        - advanced_features_used: 使用的高级功能列表
        - metadata: 元数据信息

        === V45容器架构兼容 ===
        - 通过容器调用：container_component_call("api_manager", "request_ai_assistance", data)
        - 状态跟踪：所有调用都记录在容器状态中
        - 调试友好：指挥官可观察所有API调用过程
        - 架构一致：与其他组件调用方式保持一致
        """
        # 生成请求追踪ID
        request_id = self._generate_request_id()
        start_time = datetime.now()

        try:
            # 0. 角色类别验证
            final_category = category

            # 1. 智能参数路由 - 弹性扩展核心
            routed_params = self._route_request_parameters(advanced_config, kwargs)

            # 2. 基于任务类别和扩展参数选择AI能力
            selected_capability = await self._select_capability_for_task_category(
                final_task_category, complexity_level, priority, routed_params
            )

            # 3. 准备API请求配置（包含扩展功能）
            request_config = await self._prepare_enhanced_api_request_config(
                task_description, selected_capability, final_task_category, complexity_level,
                context_info, routed_params
            )

            # 4. 执行增强AI服务调用
            ai_response = await self._execute_enhanced_ai_service_call(
                request_config, selected_capability, routed_params
            )

            # 5. 返回弹性格式响应
            end_time = datetime.now()
            response_data = {
                "success": True,
                "content": ai_response.get("content", ""),
                "task_category": final_task_category,
                "capability_used": "auto_selected",  # 不暴露具体模型
                "response_time_ms": (end_time - start_time).total_seconds() * 1000,
                "request_id": request_id,
                "advanced_features_used": self._extract_used_features(routed_params, ai_response),
                "metadata": {
                    "complexity_level": complexity_level,
                    "priority": priority,
                    "optimization_applied": ai_response.get("optimization_applied", False),
                    "routing_applied": True
                }
            }

            return response_data

        except Exception as e:
            # 故障处理
            return await self._handle_request_failure(request_id, task_description, final_task_category, str(e))
```

### 弹性扩展核心实现（外部不可见）
```python
    # === 智能参数路由系统 ===

    def _route_request_parameters(self, advanced_config: Dict, kwargs: Dict) -> Dict:
        """智能参数路由 - 弹性扩展核心"""

        # 合并所有扩展参数
        all_params = {**(advanced_config or {}), **kwargs}

        # 智能分类路由
        routed_params = {
            "thinking_config": {},
            "coordination_config": {},
            "quality_config": {},
            "session_config": {},
            "unknown_params": {}
        }

        # 参数路由映射（可扩展）
        param_routing_map = {
            # thinking相关
            "thinking_mode": "thinking_config",
            "thinking_depth": "thinking_config",
            "reasoning_depth": "thinking_config",

            # 协同相关
            "ai_coordination_mode": "coordination_config",
            "devil_advocate": "coordination_config",

            # 质量相关
            "confidence_target": "quality_config",
            "quality_assurance": "quality_config",

            # 会话相关
            "session_context": "session_config",
            "conversation_history": "session_config"
        }

        # 智能路由分类
        for param_name, param_value in all_params.items():
            target_category = param_routing_map.get(param_name, "unknown_params")
            routed_params[target_category][param_name] = param_value

        return routed_params

    def _validate_category(self, category: str) -> str:
        """验证角色类别有效性"""

        valid_categories = [
            "架构专家", "代码生成专家", "逻辑处理专家",
            "多模态专家", "通用专家"
        ]

        if category in valid_categories:
            return category

        # 默认使用通用专家
        return "通用专家"

    async def _select_capability_for_task_category(self, task_category: str, complexity_level: str,
                                                 priority: str, routed_params: Dict) -> str:
        """基于任务类别和扩展参数选择AI能力（角色化配置驱动）"""

        # 检查是否有协同模式要求
        coordination_config = routed_params.get("coordination_config", {})
        ai_coordination_mode = coordination_config.get("ai_coordination_mode", "single")

        # 4AI协同模式特殊处理
        if ai_coordination_mode in ["4ai_collaboration", "v45_enhanced"]:
            return await self._select_coordination_capability(ai_coordination_mode, task_category)

        # 标准情况：使用CategoryBasedAPISelector进行角色化配置驱动选择
        # 集成GeminiQualityComparator进行智能质量对比
        context = {
            'complexity_level': complexity_level,
            'priority': priority,
            'routed_params': routed_params
        }

        selected_api = await self._category_api_selector.select_api_by_category(task_category, context)
        return selected_api

    async def _select_coordination_capability(self, ai_coordination_mode: str, task_category: str) -> str:
        """选择协同模式对应的AI能力"""

        if ai_coordination_mode == "4ai_collaboration":
            # 4AI协同模式：API管理器只管理Python AI 1/2/3，IDE AI通过MCP调用
            coordination_mapping = {
                "architecture_design": "deepseek_r1_0528",  # Python AI 1 - 架构推导专家
                "code_generation": "deepcoder_14b",         # Python AI 3 - 质量推导专家
                "logic_reasoning": "deepseek_v3_0324",      # Python AI 2 - 逻辑推导专家
                "system_analysis": "deepseek_v3_0324"       # Python AI 2 - 逻辑推导专家
            }
            # 注意：IDE AI（事实验证权威）不在此处管理，通过MCP独立调用
            return coordination_mapping.get(task_category, "deepseek_v3_0324")

        elif ai_coordination_mode == "v45_enhanced":
            # V4.5增强协同：使用最强能力组合
            return "deepseek_r1_0528"  # V4.5主力模型

        return "deepseek_v3_0324"  # 默认

    async def _prepare_enhanced_api_request_config(self, task_description: str, selected_capability: str,
                                                 task_category: str, complexity_level: str,
                                                 context_info: Dict, routed_params: Dict) -> Dict:
        """准备增强API请求配置（支持扩展功能）"""

        # 基础配置
        base_config = {
            "messages": [{"role": "user", "content": task_description}],
            "stream": False,
            "temperature": 0.1,
            "max_tokens": 4000,
            "api_format": "openai",
            "context_info": context_info or {}
        }

        # 应用thinking配置
        thinking_config = routed_params.get("thinking_config", {})
        if thinking_config:
            base_config.update(self._apply_thinking_config(thinking_config, selected_capability))

        # 应用质量配置
        quality_config = routed_params.get("quality_config", {})
        if quality_config:
            base_config.update(self._apply_quality_config(quality_config, selected_capability))

        # 应用会话配置
        session_config = routed_params.get("session_config", {})
        if session_config:
            base_config.update(self._apply_session_config(session_config))

        # Gemini特殊优化
        if self._is_gemini_capability(selected_capability):
            base_config = self._gemini_processor.optimize_for_gemini_with_extensions(
                base_config, task_category, complexity_level, routed_params
            )

        return base_config

    def _apply_thinking_config(self, thinking_config: Dict, selected_capability: str) -> Dict:
        """应用thinking配置"""
        config_updates = {}

        thinking_mode = thinking_config.get("thinking_mode", "standard")
        if thinking_mode != "standard" and self._is_gemini_capability(selected_capability):
            # Gemini thinking配置
            config_updates["extra_body"] = {
                "google": {
                    "thinking_config": {
                        "include_thoughts": True,
                        "mode": thinking_mode
                    }
                }
            }

        return config_updates

    def _apply_quality_config(self, quality_config: Dict, selected_capability: str) -> Dict:
        """应用质量配置"""
        config_updates = {}

        confidence_target = quality_config.get("confidence_target", 0.95)
        if confidence_target >= 0.95:
            # 高质量要求：降低温度，增加tokens
            config_updates["temperature"] = 0.05
            config_updates["max_tokens"] = 6000

        return config_updates

    def _apply_session_config(self, session_config: Dict) -> Dict:
        """应用会话配置"""
        config_updates = {}

        session_context = session_config.get("session_context")
        if session_context:
            # 添加会话上下文到消息中
            config_updates["session_metadata"] = session_context

        return config_updates

    async def _execute_enhanced_ai_service_call(self, request_config: Dict, selected_capability: str,
                                              routed_params: Dict) -> Dict:
        """执行增强AI服务调用（支持扩展功能）"""
        try:
            # 检查是否需要协同处理
            coordination_config = routed_params.get("coordination_config", {})
            ai_coordination_mode = coordination_config.get("ai_coordination_mode", "single")

            if ai_coordination_mode == "4ai_collaboration":
                # 4AI协同处理
                result = await self._execute_4ai_collaboration(request_config, routed_params)
            elif ai_coordination_mode == "v45_enhanced":
                # V4.5增强协同处理
                result = await self._execute_v45_enhanced_collaboration(request_config, routed_params)
            else:
                # 标准单AI处理
                result = await self._pool_butler.execute_api_call(request_config, selected_capability)

            # 应用后处理增强
            if coordination_config.get("devil_advocate", False):
                result = await self._apply_devil_advocate_enhancement(result, routed_params)

            return result

        except Exception as e:
            # 故障转移到备用API
            return await self._execute_failover_call(request_config, selected_capability, str(e))

    async def _execute_failover_call(self, request_config: Dict, failed_capability: str, error: str) -> Dict:
        """执行故障转移调用"""
        try:
            # 使用failover_manager获取备用API
            backup_capability = await self._failover_manager.get_backup_api(failed_capability)
            backup_config = await self._prepare_api_request_config(
                request_config["messages"][0]["content"],
                backup_capability,
                "general_analysis",
                "simple",
                {}
            )
            result = await self._pool_butler.execute_api_call(backup_config, backup_capability)
            result["failover_applied"] = True
            result["original_error"] = error
            return result
        except Exception as backup_error:
            return {
                "success": False,
                "error": f"主API失败: {error}, 备用API失败: {str(backup_error)}",
                "timestamp": datetime.now().isoformat()
            }

    def _is_gemini_capability(self, capability: str) -> bool:
        """判断是否为Gemini能力"""
        return "gemini" in capability.lower()

    async def _handle_request_failure(self, request_id: str, task_description: str, task_category: str, error: str) -> Dict:
        """处理请求失败"""
        return {
            "success": False,
            "error": error,
            "task_category": task_category,
            "request_id": request_id,
            "timestamp": datetime.now().isoformat(),
            "fallback_suggestion": "请稍后重试或联系管理员"
        }

    def _extract_used_features(self, routed_params: Dict, ai_response: Dict) -> Dict:
        """提取使用的高级功能"""
        used_features = {}

        # 提取thinking功能
        thinking_config = routed_params.get("thinking_config", {})
        if thinking_config:
            used_features["thinking_mode"] = thinking_config.get("thinking_mode", "standard")

        # 提取协同功能
        coordination_config = routed_params.get("coordination_config", {})
        if coordination_config:
            used_features["ai_coordination_mode"] = coordination_config.get("ai_coordination_mode", "single")
            used_features["devil_advocate"] = coordination_config.get("devil_advocate", False)

        # 提取质量功能
        quality_config = routed_params.get("quality_config", {})
        if quality_config:
            used_features["confidence_target"] = quality_config.get("confidence_target", 0.95)

        # 提取会话功能
        session_config = routed_params.get("session_config", {})
        if session_config:
            used_features["session_context"] = bool(session_config.get("session_context"))

        return used_features

    async def _execute_4ai_collaboration(self, request_config: Dict, routed_params: Dict) -> Dict:
        """执行4AI协同处理（API管理器只负责Python AI 1/2/3）"""

        # 注意：4AI协同包括：
        # - IDE AI（事实验证权威，30%权重）- 通过MCP调用，不在此处管理
        # - Python AI 1（架构推导专家，25%权重）- 通过API管理器调用
        # - Python AI 2（逻辑推导专家，25%权重）- 通过API管理器调用
        # - Python AI 3（质量推导专家，20%权重）- 通过API管理器调用

        # API管理器只负责Python AI部分的协同
        python_ai_results = []

        # Python AI 1 - 架构推导专家
        python_ai_1_result = await self._pool_butler.execute_api_call(
            request_config, "deepseek_r1_0528"
        )
        python_ai_results.append({
            "ai_role": "Python_AI_1_架构推导专家",
            "weight": 0.25,
            "result": python_ai_1_result
        })

        # Python AI 2 - 逻辑推导专家
        python_ai_2_result = await self._pool_butler.execute_api_call(
            request_config, "deepseek_v3_0324"
        )
        python_ai_results.append({
            "ai_role": "Python_AI_2_逻辑推导专家",
            "weight": 0.25,
            "result": python_ai_2_result
        })

        # Python AI 3 - 质量推导专家
        python_ai_3_result = await self._pool_butler.execute_api_call(
            request_config, "deepcoder_14b"
        )
        python_ai_results.append({
            "ai_role": "Python_AI_3_质量推导专家",
            "weight": 0.20,
            "result": python_ai_3_result
        })

        return {
            "content": "Python AI协同处理完成（IDE AI通过MCP独立调用）",
            "python_ai_collaboration_results": python_ai_results,
            "collaboration_applied": True,
            "note": "IDE AI（事实验证权威，30%权重）通过MCP独立调用，不在API管理器范围内"
        }

    async def _execute_v45_enhanced_collaboration(self, request_config: Dict, routed_params: Dict) -> Dict:
        """执行V4.5增强协同处理"""
        # V4.5增强协同逻辑实现
        return {"content": "V4.5增强协同处理结果", "v45_enhanced": True}

    async def _apply_devil_advocate_enhancement(self, result: Dict, routed_params: Dict) -> Dict:
        """应用魔鬼审问者增强"""
        # 魔鬼审问者逻辑实现
        result["devil_advocate_applied"] = True
        return result

    def _generate_request_id(self) -> str:
        """生成唯一的请求追踪ID"""
        import uuid
        return f"ai_req_{uuid.uuid4().hex[:8]}_{int(datetime.now().timestamp())}"

    # === 调试和优化分析方法（状态容器集成） ===

    def _get_analytics_data(self) -> Dict:
        """获取完整的分析数据"""
        return {
            "request_statistics": self._request_tracker.get_all_requests_summary(),
            "performance_metrics": self._performance_monitor.get_performance_insights(),
            "state_container_data": self._get_state_container_summary(),
            "system_health": self._assess_system_health()
        }

    def _get_request_trace(self, request_id: str) -> Dict:
        """获取特定请求的追踪链路"""
        return self._request_tracker.get_request_trace(request_id)

    def _get_optimization_insights(self) -> Dict:
        """获取优化洞察"""
        performance_data = self._performance_monitor.get_performance_insights()
        request_data = self._request_tracker.get_all_requests_summary()

        insights = {
            "performance_optimization": [],
            "capability_optimization": [],
            "load_balancing_optimization": [],
            "failure_prevention": []
        }

        # 性能优化建议
        if performance_data["average_response_time_ms"] > 3000:
            insights["performance_optimization"].append({
                "issue": "平均响应时间过长",
                "current_value": f"{performance_data['average_response_time_ms']:.2f}ms",
                "recommendation": "考虑启用Gemini优化或增加缓存"
            })

        # 能力选择优化建议
        for capability, success_rate in performance_data["capability_success_rates"].items():
            if success_rate < 0.9:
                insights["capability_optimization"].append({
                    "capability": capability,
                    "success_rate": f"{success_rate:.2%}",
                    "recommendation": "检查该能力的配置和API稳定性"
                })

        # 负载均衡优化建议
        if request_data["total_requests"] > 100:
            insights["load_balancing_optimization"].append({
                "issue": "请求量较大",
                "total_requests": request_data["total_requests"],
                "recommendation": "考虑启用负载均衡和请求队列"
            })

        return insights

    def _get_state_container_summary(self) -> Dict:
        """获取状态容器摘要"""
        # 从状态容器获取当前状态
        current_state = self._state_container.get_current_state()

        return {
            "ai_service_facade_state": current_state.get("ai_service_facade", {}),
            "quality_engine_state": current_state.get("quality_driven_selection_engine", {}),
            "request_preparation_state": current_state.get("request_preparation_engine", {}),
            "active_components": list(current_state.keys()),
            "last_updated": datetime.now().isoformat()
        }

    def _assess_system_health(self) -> Dict:
        """评估系统健康状态"""
        request_stats = self._request_tracker.get_all_requests_summary()
        performance_stats = self._performance_monitor.get_performance_insights()

        health_score = 100
        health_issues = []

        # 成功率检查
        if request_stats["success_rate"] < 0.95:
            health_score -= 20
            health_issues.append(f"请求成功率偏低: {request_stats['success_rate']:.2%}")

        # 响应时间检查
        if performance_stats["average_response_time_ms"] > 5000:
            health_score -= 15
            health_issues.append(f"平均响应时间过长: {performance_stats['average_response_time_ms']:.2f}ms")

        # 能力健康检查
        unhealthy_capabilities = [
            cap for cap, rate in performance_stats["capability_success_rates"].items()
            if rate < 0.9
        ]
        if unhealthy_capabilities:
            health_score -= 10 * len(unhealthy_capabilities)
            health_issues.extend([f"能力 {cap} 成功率偏低" for cap in unhealthy_capabilities])

        return {
            "health_score": max(0, health_score),
            "health_status": "healthy" if health_score >= 80 else "warning" if health_score >= 60 else "critical",
            "health_issues": health_issues,
            "recommendations": performance_stats.get("optimization_recommendations", [])
        }

# === V45容器架构集成接口 ===

def get_task_based_ai_service() -> TaskBasedAIServiceManager:
    """
    获取基于任务类别的AI服务管理器

    这是外部系统访问AI能力的唯一入口
    单例模式：全局唯一的AI服务管理器
    高内聚设计：所有AI相关功能集中管理
    V45容器集成：通过容器调用，支持状态跟踪和调试
    """
    return TaskBasedAIServiceManager()

# V45容器架构集成函数
def register_api_manager_to_container(container):
    """将API管理器注册到V45容器中"""
    api_manager = get_task_based_ai_service()
    api_manager.register_to_container(container)
    return api_manager

# 向后兼容的别名
def get_ai_service() -> TaskBasedAIServiceManager:
    """向后兼容别名"""
    return get_task_based_ai_service()

def get_api_manager() -> TaskBasedAIServiceManager:
    """向后兼容别名"""
    return get_task_based_ai_service()

# === 调试和优化分析接口 ===

def get_ai_service_analytics() -> Dict:
    """
    获取AI服务分析数据 - 用于调试和优化

    返回状态容器中的完整追踪数据，包括：
    - 请求统计
    - 性能指标
    - 能力选择分析
    - 故障模式分析
    - 优化建议
    """
    ai_service = get_ai_service()
    return ai_service._get_analytics_data()

def get_request_trace(request_id: str) -> Dict:
    """
    获取特定请求的完整追踪链路

    用于调试特定请求的执行过程
    """
    ai_service = get_ai_service()
    return ai_service._get_request_trace(request_id)

def get_optimization_insights() -> Dict:
    """
    获取优化洞察

    基于状态容器数据分析，发现优化机会：
    - API选择优化建议
    - 性能瓶颈识别
    - 负载分布分析
    - 故障模式预测
    """
    ai_service = get_ai_service()
    return ai_service._get_optimization_insights()

class AIRequestTracker:
    """AI请求追踪器 - 状态容器集成"""

    def __init__(self, state_container):
        self.state_container = state_container
        self.request_history = {}

    async def track_request_start(self, request_id: str, request_state: Dict):
        """追踪请求开始"""
        self.request_history[request_id] = {
            "start": request_state,
            "timeline": [request_state]
        }

    async def track_capability_selection(self, request_id: str, selection_state: Dict):
        """追踪能力选择"""
        if request_id in self.request_history:
            self.request_history[request_id]["timeline"].append(selection_state)
            self.request_history[request_id]["capability_selection"] = selection_state

    async def track_request_preparation(self, request_id: str, preparation_state: Dict):
        """追踪请求准备"""
        if request_id in self.request_history:
            self.request_history[request_id]["timeline"].append(preparation_state)
            self.request_history[request_id]["request_preparation"] = preparation_state

    def get_request_trace(self, request_id: str) -> Dict:
        """获取请求的完整追踪链路"""
        return self.request_history.get(request_id, {})

    def get_all_requests_summary(self) -> Dict:
        """获取所有请求的汇总统计"""
        total_requests = len(self.request_history)
        successful_requests = sum(1 for req in self.request_history.values()
                                if req.get("completion", {}).get("success", False))

        return {
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "success_rate": successful_requests / total_requests if total_requests > 0 else 0,
            "recent_requests": list(self.request_history.keys())[-10:]  # 最近10个请求
        }

class AIPerformanceMonitor:
    """AI性能监控器 - 状态容器集成"""

    def __init__(self, state_container):
        self.state_container = state_container
        self.performance_metrics = {
            "response_times": [],
            "capability_usage": {},
            "optimization_effectiveness": {},
            "failure_patterns": []
        }

    async def record_response_time(self, request_id: str, response_time_ms: float):
        """记录响应时间"""
        self.performance_metrics["response_times"].append({
            "request_id": request_id,
            "response_time_ms": response_time_ms,
            "timestamp": datetime.now().isoformat()
        })

    async def record_capability_usage(self, capability: str, success: bool):
        """记录能力使用情况"""
        if capability not in self.performance_metrics["capability_usage"]:
            self.performance_metrics["capability_usage"][capability] = {
                "total_calls": 0,
                "successful_calls": 0
            }

        self.performance_metrics["capability_usage"][capability]["total_calls"] += 1
        if success:
            self.performance_metrics["capability_usage"][capability]["successful_calls"] += 1

    def get_performance_insights(self) -> Dict:
        """获取性能洞察"""
        avg_response_time = sum(m["response_time_ms"] for m in self.performance_metrics["response_times"]) / len(self.performance_metrics["response_times"]) if self.performance_metrics["response_times"] else 0

        capability_success_rates = {}
        for capability, stats in self.performance_metrics["capability_usage"].items():
            capability_success_rates[capability] = stats["successful_calls"] / stats["total_calls"] if stats["total_calls"] > 0 else 0

        return {
            "average_response_time_ms": avg_response_time,
            "capability_success_rates": capability_success_rates,
            "total_requests_processed": len(self.performance_metrics["response_times"]),
            "optimization_recommendations": self._generate_optimization_recommendations()
        }

    def _generate_optimization_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []

        # 基于响应时间分析
        if self.performance_metrics["response_times"]:
            avg_time = sum(m["response_time_ms"] for m in self.performance_metrics["response_times"]) / len(self.performance_metrics["response_times"])
            if avg_time > 5000:  # 超过5秒
                recommendations.append("考虑启用请求缓存以减少响应时间")
            if avg_time > 10000:  # 超过10秒
                recommendations.append("考虑增加负载均衡节点")

        # 基于能力使用分析
        for capability, stats in self.performance_metrics["capability_usage"].items():
            success_rate = stats["successful_calls"] / stats["total_calls"] if stats["total_calls"] > 0 else 0
            if success_rate < 0.8:  # 成功率低于80%
                recommendations.append(f"能力 {capability} 成功率较低，建议检查配置")

        return recommendations
```

### 向后兼容使用示例

```python
# === 角色化配置驱动使用示例 ===

class RoleBasedConfigurationExample:
    """角色化配置驱动使用示例"""

    def __init__(self):
        self.ai_service = get_task_based_ai_service()

    async def example_architecture_expert(self):
        """架构专家角色示例"""

        result = await self.ai_service.request_ai_assistance(
            task_description="设计一个微服务架构",
            category="架构专家",  # 角色化参数
            complexity_level="complex",
            thinking_mode="v45_fusion",
            devil_advocate=True,
            ai_coordination_mode="v45_enhanced"
        )

        print(f"架构专家调用结果: {result['content']}")

    async def example_code_generation_expert(self):
        """代码生成专家角色示例"""

        result = await self.ai_service.request_ai_assistance(
            task_description="生成用户认证代码",
            category="代码生成专家",  # 角色化参数
            complexity_level="medium",
            advanced_config={
                "thinking_mode": "enhanced",
                "ai_coordination_mode": "4ai_collaboration"
            }
        )

        print(f"代码生成专家调用结果: {result['content']}")

    async def example_multimodal_expert(self):
        """多模态专家角色示例"""

        result = await self.ai_service.request_ai_assistance(
            task_description="分析图像中的架构设计",
            category="多模态专家",  # 角色化参数
            complexity_level="complex"
        )

        print(f"多模态专家调用结果: {result['content']}")
        result = await self.ai_service.request_ai_assistance(
            task_description="分析因果关系",
            category="逻辑处理专家",  # 角色化参数
            complexity_level="complex"
        )

        print(f"逻辑处理专家调用结果: {result['content']}")

## 📋 角色化配置驱动架构优势

### **核心优势**

#### **配置驱动优先**
- 所有API选择通过common_config.json配置管理
- 版本演进只需修改配置文件，无需修改代码
- 支持多版本API共存和动态切换

#### **角色化管理**
- 基于"架构专家"、"代码生成专家"等角色，而非具体模型名
- 脱离硬编码模型依赖，支持灵活的角色定义
- 通过角色映射实现智能API选择

#### **质量对比机制**
- GeminiQualityComparator实现多维度质量对比
- 支持Gemini与固定API的智能择优
- 基于角色基准数据进行质量评估
### **实例管理兼容性**

#### **单例组件（保持不变）**
- `TaskBasedAIServiceManager`：全局单例，所有项目共享
- `SimpleConfigurationCenter`：按配置文件路径单例
- `ServerLauncher`：全局单例服务器启动器
- `ProjectContextManager`：全局单例项目上下文管理

#### **多实例组件（项目级隔离）**
- 项目容器：每个项目独立的`UniversalProjectContainer`
- 项目指挥官：每个项目独立的`PythonHostCoreEngine`
- Meeting目录服务：每个项目独立的Meeting实例
- 因果系统：每个项目独立的因果分析实例

### **角色化API调用模式**

| 角色类别 | 配置映射 | 质量对比 | 版本演进 |
|---------|---------|--------|--------|
| `category="架构专家"` | ✅ 配置驱动 | ✅ 智能择优 | ✅ 配置升级 |
| `category="代码生成专家"` | ✅ 配置驱动 | ✅ 智能择优 | ✅ 配置升级 |
| `category="逻辑处理专家"` | ✅ 配置驱动 | ✅ 智能择优 | ✅ 配置升级 |
| `category="多模态专家"` | ✅ 配置驱动 | ✅ 智能择优 | ✅ 配置升级 |
| `category="通用专家"` | ✅ 配置驱动 | ✅ 智能择优 | ✅ 配置升级 |

## 📋 总结

### **角色化配置驱动架构成果**

#### **核心成就**
- ✅ 删除了所有硬编码的API映射逻辑
- ✅ 实现了基于角色的配置驱动架构
- ✅ 建立了统一的CategoryBasedAPISelector选择器
- ✅ 集成了GeminiQualityComparator智能质量对比
- ✅ 支持Gemini通才场景和版本演进

#### **技术优势**
- **配置优先**：版本升级只需修改JSON配置
- **角色化管理**：脱离具体模型名，基于角色定义
- **智能择优**：多维度质量对比和动态选择
- **统一入口**：CategoryBasedAPISelector提供统一接口
- **版本友好**：支持多版本API共存和平滑升级

#### **决策流程**
```
用户请求 → 角色查询 → 配置获取 → 质量对比 → 优化选择 → API调用
```

### **兼容性保证**
- **角色化重构**：基于角色类别的配置驱动架构
- **配置管理**：通过common_config.json统一管理
- **质量对比**：GeminiQualityComparator智能择优
- **V45容器集成**：通过容器调用，支持状态跟踪和调试

通过这样的设计，我们实现了完全的角色化配置驱动架构，消除了硬编码冲突，支持版本演进和Gemini通才场景。
### 下一步文档

- **02-核心业务功能验证系统** - thinking质量、魔鬼审问者、置信度收敛
- **03-API池智能调度引擎** - 角色化配置驱动、负载均衡、故障转移
- **04-Web API接口设计** - RESTful API、实时状态、监控接口
- **05-人工管理交互界面** - 用户界面、配置管理、状态监控
- **06-系统集成与部署** - 12步集成、测试验证、生产部署




## 📋 角色化配置驱动架构优势

### **核心优势**

#### **配置驱动优先**
- 所有API选择通过common_config.json配置管理
- 版本演进只需修改配置文件，无需修改代码
- 支持多版本API共存和动态切换

#### **角色化管理**
- 基于"架构专家"、"代码生成专家"等角色，而非具体模型名
- 脱离硬编码模型依赖，支持灵活的角色定义
- 通过角色映射实现智能API选择

#### **质量对比机制**
- GeminiQualityComparator实现多维度质量对比
- 支持Gemini与固定API的智能择优
- 基于角色基准数据进行质量评估

### **实例管理兼容性**

#### **单例组件（保持不变）**
- `TaskBasedAIServiceManager`：全局单例，所有项目共享
- `SimpleConfigurationCenter`：按配置文件路径单例
- `ServerLauncher`：全局单例服务器启动器
- `ProjectContextManager`：全局单例项目上下文管理

#### **多实例组件（项目级隔离）**
- 项目容器：每个项目独立的`UniversalProjectContainer`
- 项目指挥官：每个项目独立的`PythonHostCoreEngine`
- Meeting目录服务：每个项目独立的Meeting实例
- 因果系统：每个项目独立的因果分析实例

### **角色化API调用模式**

| 角色类别 | 配置映射 | 质量对比 | 版本演进 |
|---------|---------|--------|--------|
| `category="架构专家"` | ✅ 配置驱动 | ✅ 智能择优 | ✅ 配置升级 |
| `category="代码生成专家"` | ✅ 配置驱动 | ✅ 智能择优 | ✅ 配置升级 |
| `category="逻辑处理专家"` | ✅ 配置驱动 | ✅ 智能择优 | ✅ 配置升级 |
| `category="多模态专家"` | ✅ 配置驱动 | ✅ 智能择优 | ✅ 配置升级 |
| `category="通用专家"` | ✅ 配置驱动 | ✅ 智能择优 | ✅ 配置升级 |

## 📋 总结

### **角色化配置驱动架构成果**

#### **核心成就**
- ✅ 删除了所有硬编码的API映射逻辑
- ✅ 实现了基于角色的配置驱动架构
- ✅ 建立了统一的CategoryBasedAPISelector选择器
- ✅ 集成了GeminiQualityComparator智能质量对比
- ✅ 支持Gemini通才场景和版本演进

#### **技术优势**
- **配置优先**：版本升级只需修改JSON配置
- **角色化管理**：脱离具体模型名，基于角色定义
- **智能择优**：多维度质量对比和动态选择
- **统一入口**：CategoryBasedAPISelector提供统一接口
- **版本友好**：支持多版本API共存和平滑升级

#### **决策流程**
```
用户请求 → 角色查询 → 配置获取 → 质量对比 → 优化选择 → API调用
```


### 下一步文档

- **02-核心业务功能验证系统** - thinking质量、魔鬼审问者、置信度收敛
- **03-API池智能调度引擎** - 角色化配置驱动、负载均衡、故障转移
- **04-Web API接口设计** - RESTful API、实时状态、监控接口
- **05-人工管理交互界面** - 用户界面、配置管理、状态监控
- **06-系统集成与部署** - 12步集成、测试验证、生产部署


### 下一步文档

- **02-核心业务功能验证系统** - thinking质量、魔鬼审问者、置信度收敛
- **03-API池智能调度引擎** - 动态选择、负载均衡、故障转移
- **04-Web API接口设计** - RESTful API、实时状态、监控接口
- **05-人工管理交互界面** - 用户界面、配置管理、状态监控
- **06-系统集成与部署** - 12步集成、测试验证、生产部署
