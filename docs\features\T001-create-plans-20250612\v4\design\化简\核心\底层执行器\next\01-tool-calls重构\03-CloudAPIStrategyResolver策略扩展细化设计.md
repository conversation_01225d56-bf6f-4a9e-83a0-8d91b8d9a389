# 03-CloudAPIStrategyResolver策略扩展细化设计（伪代码级）

## 1. 现有实现溯源
- 文件：无专门CloudAPIStrategyResolver类，相关策略分散在AI调用参数构建逻辑中
- 现状：
  - logit_bias等高级API参数支持有限，策略分散，难以扩展
  - 缺乏统一的策略解析与优先级管理机制

## 2. 目标结构与接口（伪代码级）

### 2.1 抽象策略接口
```python
# cloud_api_strategy_resolver.py
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod

class CloudDecodingStrategy(ABC):
    @abstractmethod
    def resolve(self, constraints: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        pass
```

### 2.2 具体策略实现
```python
class LogitBiasStrategy(CloudDecodingStrategy):
    def resolve(self, constraints: Dict) -> Optional[Dict]:
        if "output_choices" in constraints and isinstance(constraints["output_choices"], list):
            logit_bias = {choice: 100 for choice in constraints["output_choices"]}
            return {"logit_bias": logit_bias, "max_tokens": 5}
        return None

class MaxTokensStrategy(CloudDecodingStrategy):
    def resolve(self, constraints: Dict) -> Optional[Dict]:
        if "max_tokens" in constraints:
            return {"max_tokens": constraints["max_tokens"]}
        return None
```

### 2.3 策略注册与优先级
```python
class CloudAPIStrategyResolver:
    def __init__(self):
        self._strategies = [
            LogitBiasStrategy(),
            MaxTokensStrategy(),
            # 可扩展更多策略
        ]

    def resolve_api_params(self, constraints: Dict) -> Dict[str, Any]:
        for strategy in self._strategies:
            params = strategy.resolve(constraints)
            if params:
                return params
        return {}
```

### 2.4 用法示例
```python
resolver = CloudAPIStrategyResolver()
constraints = {"output_choices": ["A", "B"]}
api_params = resolver.resolve_api_params(constraints)
# api_params: {"logit_bias": {"A": 100, "B": 100}, "max_tokens": 5}
```

### 2.5 单元测试建议
```python
def test_logit_bias_strategy():
    s = LogitBiasStrategy()
    c = {"output_choices": ["yes", "no"]}
    assert s.resolve(c)["logit_bias"] == {"yes": 100, "no": 100}

def test_resolver_priority():
    resolver = CloudAPIStrategyResolver()
    c = {"max_tokens": 10}
    assert resolver.resolve_api_params(c)["max_tokens"] == 10
```

## 3. 需变更/新建/删除内容（精确到代码）
- 新建：
  - `tools/ace/src/executors/cloud_api_strategy_resolver.py`（CloudAPIStrategyResolver、策略类）
- 修改：
  - ValidationDrivenExecutor等调用点，集成CloudAPIStrategyResolver统一处理API参数
- 单元测试：
  - `test_cloud_api_strategy_resolver.py`，覆盖所有策略分支

## 4. 兼容性与测试建议
- CloudAPIStrategyResolver为新增组件，不影响旧有流程
- 建议：
  - 单元测试：各策略分支的参数输出
  - 集成测试：与AI服务管理器的集成调用

## 5. 代码引用与路径
- 新增：`tools/ace/src/executors/cloud_api_strategy_resolver.py`（建议）
- 相关调用：ValidationDrivenExecutor等 