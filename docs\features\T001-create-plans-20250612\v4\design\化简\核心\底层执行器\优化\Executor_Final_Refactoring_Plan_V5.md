# 执行器最终重构方案 V5.1：引入智能恢复机制的契约式AI调用框架

**文档版本**: 2.1 (最终版)
**日期**: 2025-07-25
**作者**: <PERSON><PERSON> (AI Assistant)

## 1. 最终确定的核心思想

本方案旨在解决AI输出的可靠性问题，其核心是**用流程的智慧，驾驭AI的强大能力，同时规避其不可靠性**。我们通过一个两次调用的工作流，强制AI进行**角色分离**，并建立一个**包含智能恢复能力的、端到端可审计的信任链**。

- **第一次调用 (生成契约)**: AI扮演“质检总监”，审阅全部任务信息，生成一份客观、高质量的“算法验证链”作为“质量契约”。
- **第二次调用 (履行契约)**: AI扮演“工程师”，拿着这份“质量契约”作为硬性验收标准，去生成最终内容。
- **后端审计 (执行契约)**: 我们用确定性算法，严格审计“工程师”的产出是否100%履行了“质检总监”制定的“契约”。
- **智能恢复 (Resilience)**: 基于失败发生的具体阶段和原因，执行最高效的恢复路径，而不是粗暴地全盘重试。

## 2. 智能恢复策略：基于失败点的精准恢复

这是V5.1版本最重要的升级。我们为流程中的每个关键失败点设计了专属的、最高效的恢复策略。

```mermaid
graph TD
    A[开始] --> B{Call 1: 生成验证链};
    B -- 成功 --> C{Call 2: 生成内容};
    B -- 失败 --> D[只重试Call 1 (最多N次)];
    D -- 成功 --> C;
    D -- 失败 --> E[终止: 无法生成契约];

    C -- 成功 --> F{后端审计};
    C -- 失败(如JSON格式错误) --> G[只重试Call 2 (最多N次)];
    G -- 成功 --> F;
    G -- 失败 --> H[终止: 无法生成内容];

    F -- 成功 --> I[任务成功];
    F -- 失败 --> J{携带审计反馈, 重试Call 2 (最多N次)};
    J -- 成功 --> F;
    J -- 失败 --> K[终止: 审计最终失败];
```

## 3. `validation_driven_executor.py` 精确修改伪代码 (覆盖率 > 80%)

### 3.1. 数据结构定义 (Dataclasses)

```python
# location: top of validation_driven_executor.py

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional

# --- 无变化 ---
@dataclass
class ValidationChainPoint:
    point_id: str
    validation_type: str
    target: str
    expected_result: str
    description: str = ""
    is_critical: bool = True

# --- 无变化 ---
@dataclass
class AIContract:
    confidence_score: float
    validation_chain: List[ValidationChainPoint]
    context_signature: str

# --- 无变化 ---
@dataclass
class ValidationResult:
    passed: bool
    confidence_score: float
    audited_points: List[Dict] = field(default_factory=list)
    issues: List[str] = field(default_factory=list)

# --- 无变化 ---
@dataclass
class ExecutionResult:
    success: bool
    # ...
```

### 3.2. `ValidationDrivenExecutor` 类的修改

#### **`execute_with_validation` 方法 (引入重试逻辑)**

```python
# location: class ValidationDrivenExecutor
import hashlib
import json
import asyncio

async def execute_with_validation(self,
                                  # ... (参数不变) ...
                                  max_retries: int = 2) -> ExecutionResult:
    execution_id = f"{self.task_id}_{len(self.execution_history)}"
    start_time = datetime.now()

    # --- 准备阶段: 生成上下文签名 ---
    full_input_str = json.dumps({
        "content": original_content, "guardrails": guardrails, "constraints": constraints, "context": context
    }, sort_keys=True)
    context_signature = hashlib.sha256(full_input_str.encode('utf-8')).hexdigest()

    # --- 阶段 0: 生成“验证链契约” (带重试) ---
    contract = None
    for attempt in range(max_retries):
        try:
            contract = await self._phase_0_get_validation_contract(full_input_str, context_signature)
            if contract.confidence_score >= confidence_threshold and contract.context_signature == context_signature:
                break # 成功，跳出重试循环
            # 如果置信度或签名有问题，也算一种失败，将继续重试
            contract = None # 重置，以便最后检查
        except Exception as e:
            logger.warning(f"Phase 0, Attempt {attempt+1}/{max_retries} failed: {e}")
            if attempt + 1 == max_retries: # 最后一次尝试仍然失败
                return self._record_and_return(execution_id, ExecutionResult(success=False, error_message=f"Phase 0 failed after {max_retries} attempts: {e}"), start_time)
            await asyncio.sleep(1) # 简单等待后重试
    
    if not contract:
        msg = f"Phase 0 failed to produce a valid contract after {max_retries} attempts."
        return self._record_and_return(execution_id, ExecutionResult(success=False, error_message=msg), start_time)

    # --- 阶段 1 & 2: 生产与审计循环 (带反馈重试) ---
    audit_feedback = None
    for attempt in range(max_retries):
        try:
            # --- 阶段 1: 遵循“契约”进行生产 ---
            ai_output_json = await self._phase_1_generate_with_contract(
                full_input_str, contract.validation_chain, context_signature, audit_feedback
            )
            ai_output_data = json.loads(ai_output_json)
            generated_content_str = ai_output_data.get("generated_content", {}).get("content", "")
            purified_content = self._purify_content(generated_content_str)

            # --- 阶段 2: 后端审计 ---
            validation_result = await self.validation_loop.audit_with_chain(
                purified_content, contract.validation_chain, context
            )
            validation_result.confidence_score = contract.confidence_score

            if validation_result.passed:
                # 审计通过，执行最终操作并成功返回
                return await self._execute_after_validation(
                    purified_content, pycrud_operations, validation_result, execution_id, start_time
                )
            else:
                # 审计失败，准备反馈信息用于下一次重试
                audit_feedback = {"audit_issues": validation_result.issues}
                logger.warning(f"Audit, Attempt {attempt+1}/{max_retries} failed. Issues: {validation_result.issues}")

        except Exception as e:
            # 捕获生成或解析阶段的错误
            logger.warning(f"Phase 1/2, Attempt {attempt+1}/{max_retries} failed: {e}")
            audit_feedback = {"system_error": str(e)} # 将系统错误也作为反馈

        if attempt + 1 == max_retries:
            msg = f"Process failed after {max_retries} attempts. Last issues: {audit_feedback}"
            return self._record_and_return(execution_id, ExecutionResult(success=False, error_message=msg, validation_result=validation_result), start_time)
        
        await asyncio.sleep(1) # 等待后重试
    
    # 理论上不会执行到这里，但作为兜底
    return self._record_and_return(execution_id, ExecutionResult(success=False, error_message="Unknown error in retry loop."), start_time)
```

#### **修改的辅助方法**

```python
# location: class ValidationDrivenExecutor

# --- _phase_0_get_validation_contract 方法保持不变 ---

# --- 修改 _phase_1_generate_with_contract 方法，增加 feedback 参数 ---
async def _phase_1_generate_with_contract(self, 
                                          full_input_str: str, 
                                          validation_chain: List[ValidationChainPoint], 
                                          context_signature: str,
                                          feedback: Optional[Dict] = None) -> str:
    """第二次调用: 让AI扮演“工程师”，如果提供了feedback，则进行修正"""
    chain_str = json.dumps([p.__dict__ for p in validation_chain], indent=2)
    
    feedback_prompt_part = ""
    if feedback:
        feedback_str = json.dumps(feedback, indent=2)
        feedback_prompt_part = f"""
        **Previous Attempt Feedback**:
        Your last generated content failed our audit. Please correct the following specific issues and regenerate the content.
        
        **Failure Details**:
        {feedback_str}
        """

    prompt = f"""
    **Role**: You are a world-class Principal Engineer AI.
    **Task**: Generate the final content based on the user request below.
    {feedback_prompt_part}
    **User Request**:
    {full_input_str}

    **Mandatory Acceptance Criteria (Your Contract)**:
    Your output MUST pass the audit using the following "validation_chain". This is a hard requirement.
    {chain_str}

    **Mandatory Output**:
    Respond with a single JSON object ONLY. It must contain a "generated_content" object, which has a "content" field with your final code or document.
    """
    return await self.ai_service_manager.call_ai(
        model_name=self._select_model_by_role(self.executor_role),
        content=prompt,
        json_output=True
    )

# --- _purify_content 方法保持不变 ---
```

### 3.3. `ValidationLoop` 类的修改

此类在此次优化中无需修改，其`audit_with_chain`方法已经能够满足要求，因为它只负责单次的、无状态的审计。

## 4. 总结

V5.1版本在V5的基础上，引入了**基于失败点的精准恢复机制**。通过在主流程中增加带有反馈的重试循环，极大地提升了系统的健壮性和最终的成功率，同时保持了架构的清晰和职责的分离。这是一个在不显著增加架构复杂度的情况下，有效提升系统韧性的务实方案。
