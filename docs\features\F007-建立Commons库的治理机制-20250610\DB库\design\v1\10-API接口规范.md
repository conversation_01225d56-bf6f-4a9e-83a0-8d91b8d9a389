# F007 DB库API接口规范详细设计

## 文档信息
- **文档ID**: F007-DB-API-SPEC-010
- **创建日期**: 2025-01-17
- **版本**: v1.0
- **模块**: 全模块API规范
- **依赖**: 所有Commons DB模块
- **状态**: 设计阶段
- **复杂度等级**: L2-中等复杂度（4-7概念，多组件协调）

## 核心定位
API接口规范是Commons DB的**标准化接口定义**，确保：
- 统一的API设计风格
- 一致的命名约定
- 标准化的异常处理
- 完整的文档规范
- 向后兼容性保证
- **🔑 现代技术特性API集成**：
  - **Java 21特性API**：虚拟线程异步API、Record类型、Pattern Matching、Sealed Classes
  - **PostgreSQL 17特性API**：JSON_TABLE查询、并行处理、流式I/O接口
  - **Spring Boot 3.4 API**：观测性API、容器化健康检查、AOT编译支持
  - **技术特性组合API**：智能场景识别、性能优化建议、自动配置接口
  - **云原生API**：Kubernetes集成、多云部署、容器化优化接口

## 设计哲学

本项目遵循以下设计哲学，专注解决API接口设计的核心难点：

1. **微内核架构精准实现**：构建可插拔的API接口体系，支持动态扩展和版本演进
   - **插件接口定义难点**：如何设计统一而灵活的API插件接口，支持不同模块的差异化需求
   - **生命周期管理难点**：如何管理API接口的版本升级、兼容性维护和废弃策略
   - **插件发现机制难点**：如何实现API接口的自动发现和注册，保持系统的动态扩展性

2. **分层架构精准实现**：建立清晰的API分层体系，确保职责明确和依赖合理
   - **层次划分难点**：如何正确划分API的抽象层次，避免层次混乱和职责模糊
   - **职责定义难点**：如何明确定义每层API的职责边界，避免功能重复和耦合
   - **依赖方向难点**：如何控制API层间的依赖方向，确保架构的稳定性和可维护性
   - **接口契约难点**：如何设计严格的接口契约，保证API的一致性和兼容性

3. **复杂性边界精确控制**：明确定义AI认知边界，确保API设计复杂度可控
   - **模块划分原则**：按照功能职责和技术特性进行清晰的API模块划分
   - **职责分离策略**：每个API接口专注单一职责，避免接口膨胀和功能混乱
   - **边界定义方法**：通过接口契约和文档规范明确API的使用边界

4. **一致性与简洁性统一**：在API设计的一致性和简洁性之间找到最佳平衡
5. **可扩展性与类型安全**：确保API的可扩展性同时保证类型安全
6. **异步优先与性能导向**：优先考虑异步设计，以性能优化为核心考量
7. **现代技术深度融合**：充分利用Java 21、Spring Boot 3.4等现代技术特性
8. **云原生标准兼容**：遵循云原生API设计最佳实践和标准

## 包含范围

### 功能范围
- 统一数据访问接口定义
- API命名和设计约定规范
- 异常处理和错误码标准
- 请求响应格式规范
- 异步API和响应式编程支持
- 分页和查询接口标准
- 批量操作API规范
- 现代技术特性API集成

### 技术范围
- Java 21 Record和Sealed Classes
- CompletableFuture异步编程
- Spring Boot 3.4观测性API
- 虚拟线程API支持
- PostgreSQL 17特性API
- OpenAPI 3.0文档生成

### 微内核架构组件
- **API核心接口**：标准化的基础API接口定义
- **插件API体系**：可插拔的API扩展接口
- **版本管理机制**：API版本演进和兼容性管理
- **接口注册中心**：API接口的自动发现和注册

### 分层架构组件
- **抽象接口层**：通用数据访问接口抽象
- **技术适配层**：特定技术栈的API适配
- **业务接口层**：业务功能的API接口定义
- **集成接口层**：外部系统集成的API接口

## 排除范围

### 功能排除
- 具体业务逻辑实现（由业务模块负责）
- 数据库连接管理（由连接模块负责）
- 安全认证和授权（由安全模块负责）
- 缓存策略实现（由缓存模块负责）
- 监控和日志记录（由监控模块负责）

### 技术排除
- 非Java语言的API支持
- 自定义序列化协议开发
- 复杂的API网关功能
- 实时消息推送API

### 复杂性边界
- 不支持动态API生成（避免运行时复杂性）
- 不支持复杂的API组合（保持接口简洁）
- 不支持跨模块事务API（避免分布式事务复杂性）

## 1. 规范概述

### 1.2 现代技术栈组合优势 🔮
- **现代Java API**：充分利用Java 21特性，API设计更加简洁、类型安全、性能优越
- **异步优先API**：虚拟线程原生支持，CompletableFuture、Reactive Streams无缝集成
- **数据库特性感知API**：API自动适配数据库版本和特性，无缝利用最新数据库能力
- **云原生API标准**：遵循云原生最佳实践，支持微服务、容器化、Kubernetes部署

### 1.3 设计原则
- **一致性**：所有API遵循统一的设计模式
- **简洁性**：API设计简洁明了，易于理解
- **可扩展性**：支持未来功能的扩展
- **类型安全**：充分利用Java类型系统
- **🔑 现代化架构**：
  - **异步优先**：API设计优先考虑异步和响应式编程
  - **特性感知**：API能够感知和利用底层技术特性
  - **云原生标准**：遵循云原生API设计最佳实践
  - **性能导向**：API设计以性能优化为核心考量

## 2. 核心API规范

### 2.1 DataAccessTemplate 标准接口

```java
/**
 * 统一数据访问模板接口
 * 
 * @param <T> 实体类型
 * @param <ID> 主键类型
 * @since 1.0.0
 */
public interface DataAccessTemplate<T, ID> {
    
    // ============ 基础CRUD操作 ============
    
    /**
     * 保存实体
     * 
     * @param entity 实体对象，不能为null
     * @return 保存后的实体（可能包含生成的ID）
     * @throws IllegalArgumentException 当entity为null时
     * @throws DataAccessException 数据访问异常
     * @since 1.0.0
     */
    @NonNull
    T save(@NonNull T entity);
    
    /**
     * 批量保存实体
     * 
     * @param entities 实体集合，不能为null或空
     * @return 保存后的实体列表
     * @throws IllegalArgumentException 当entities为null或空时
     * @throws DataAccessException 数据访问异常
     * @since 1.0.0
     */
    @NonNull
    List<T> saveAll(@NonNull Iterable<T> entities);
    
    /**
     * 根据ID查找实体
     * 
     * @param id 主键ID，不能为null
     * @return Optional包装的实体
     * @throws IllegalArgumentException 当id为null时
     * @throws DataAccessException 数据访问异常
     * @since 1.0.0
     */
    @NonNull
    Optional<T> findById(@NonNull ID id);
    
    /**
     * 查询所有实体
     * 
     * @return 实体列表，永不为null
     * @throws DataAccessException 数据访问异常
     * @since 1.0.0
     */
    @NonNull
    List<T> findAll();
    
    // ============ 🔑 现代技术特性API ============
    
    /**
     * 虚拟线程异步查询所有实体
     * 基于Java 21虚拟线程，支持高并发无阻塞查询
     * 
     * @return 异步实体列表
     * @throws DataAccessException 数据访问异常
     * @since 3.0.0
     */
    @NonNull
    CompletableFuture<List<T>> findAllAsync();
    
    /**
     * 流式查询大结果集
     * 基于PostgreSQL 17流式I/O，支持TB级数据处理
     * 
     * @param fetchSize 批次大小
     * @return 实体流，需要在try-with-resources中使用
     * @throws DataAccessException 数据访问异常
     * @since 3.0.0
     */
    @NonNull
    Stream<T> findAllAsStream(int fetchSize);
    
    /**
     * 技术特性组合优化查询
     * 智能识别查询场景，自动选择最优技术特性组合
     * 
     * @param spec 查询规范
     * @param optimizationHint 优化提示
     * @param <R> 结果类型
     * @return 优化查询结果
     * @throws DataAccessException 数据访问异常
     * @since 3.0.0
     */
    @NonNull
    <R> CompletableFuture<List<R>> queryOptimized(
        @NonNull QuerySpec<R> spec, 
        @NonNull OptimizationHint optimizationHint);
    
    /**
     * 分页查询所有实体
     * 
     * @param pageable 分页参数，不能为null
     * @return 分页结果，永不为null
     * @throws IllegalArgumentException 当pageable为null时
     * @throws DataAccessException 数据访问异常
     * @since 1.0.0
     */
    @NonNull
    Page<T> findAll(@NonNull Pageable pageable);
    
    // ============ 查询操作 ============
    
    /**
     * 根据查询规范查询
     * 
     * @param spec 查询规范，不能为null
     * @param <R> 结果类型
     * @return 查询结果列表，永不为null
     * @throws IllegalArgumentException 当spec为null时
     * @throws QuerySyntaxException 查询语法错误时
     * @throws DataAccessException 数据访问异常
     * @since 1.0.0
     */
    @NonNull
    <R> List<R> query(@NonNull QuerySpec<R> spec);
    
    /**
     * 根据查询规范分页查询
     * 
     * @param spec 查询规范，不能为null
     * @param pageable 分页参数，不能为null
     * @param <R> 结果类型
     * @return 分页查询结果，永不为null
     * @throws IllegalArgumentException 当参数为null时
     * @throws QuerySyntaxException 查询语法错误时
     * @throws DataAccessException 数据访问异常
     * @since 1.0.0
     */
    @NonNull
    <R> Page<R> queryWithPaging(@NonNull QuerySpec<R> spec, @NonNull Pageable pageable);
    
    // ============ 批量操作 ============
    
    /**
     * 批量插入（高性能）
     * 
     * @param entities 实体列表，不能为null或空
     * @throws IllegalArgumentException 当entities为null或空时
     * @throws DataAccessException 数据访问异常
     * @since 1.0.0
     */
    void batchInsert(@NonNull List<T> entities);
    
    /**
     * 批量更新（高性能）
     * 
     * @param entities 实体列表，不能为null或空
     * @throws IllegalArgumentException 当entities为null或空时
     * @throws DataAccessException 数据访问异常
     * @since 1.0.0
     */
    void batchUpdate(@NonNull List<T> entities);
    
    // ============ 元数据操作 ============
    
    /**
     * 获取实体类型
     * 
     * @return 实体类型，永不为null
     * @since 1.0.0
     */
    @NonNull
    Class<T> getEntityType();
    
    /**
     * 获取主键类型
     * 
     * @return 主键类型，永不为null
     * @since 1.0.0
     */
    @NonNull
    Class<ID> getIdType();
}
```

### 2.2 QuerySpec 查询规范接口

```java
/**
 * 查询规范接口
 * 定义查询的抽象规范
 * 
 * @param <R> 查询结果类型
 * @since 1.0.0
 */
public interface QuerySpec<R> {
    
    /**
     * 获取查询结果类型
     * 
     * @return 结果类型的Class对象，永不为null
     * @since 1.0.0
     */
    @NonNull
    Class<R> getResultType();
    
    /**
     * 获取查询语句
     * 
     * @return 查询语句，永不为null
     * @since 1.0.0
     */
    @NonNull
    String getQuery();
    
    /**
     * 获取查询参数
     * 
     * @return 参数映射，永不为null（可能为空Map）
     * @since 1.0.0
     */
    @NonNull
    Map<String, Object> getParameters();
    
    /**
     * 获取查询提示
     * 
     * @return 查询提示信息，可能为null
     * @since 1.0.0
     */
    @Nullable
    QueryHint getHint();
    
    /**
     * 获取查询类型
     * 
     * @return 查询类型枚举，永不为null
     * @since 1.0.0
     */
    @NonNull
    QueryType getQueryType();
    
    /**
     * 构建器模式创建QuerySpec
     * 
     * @param <R> 结果类型
     * @return QuerySpec构建器
     * @since 1.0.0
     */
    static <R> QuerySpecBuilder<R> builder() {
        return new QuerySpecBuilder<>();
    }
}
```

### 2.3 DataAccessProvider SPI接口

```java
/**
 * 数据访问提供者SPI接口
 * 实现此接口以提供特定的数据访问实现
 * 
 * @since 1.0.0
 */
public interface DataAccessProvider {
    
    /**
     * 获取提供者名称
     * 
     * @return 提供者唯一标识名称，永不为null
     * @since 1.0.0
     */
    @NonNull
    String getName();
    
    /**
     * 获取提供者优先级
     * 数值越大优先级越高
     * 
     * @return 优先级，默认为0
     * @since 1.0.0
     */
    default int getOrder() {
        return 0;
    }
    
    /**
     * 获取提供者版本
     * 
     * @return 版本号，永不为null
     * @since 1.0.0
     */
    @NonNull
    String getVersion();
    
    /**
     * 检查是否支持指定的实体类型
     * 
     * @param entityType 实体类型，不能为null
     * @return 是否支持
     * @throws IllegalArgumentException 当entityType为null时
     * @since 1.0.0
     */
    boolean supports(@NonNull Class<?> entityType);
    
    /**
     * 创建数据访问模板实例
     * 
     * @param entityType 实体类型，不能为null
     * @param idType 主键类型，不能为null
     * @param config 数据源配置，不能为null
     * @param <T> 实体类型泛型
     * @param <ID> 主键类型泛型
     * @return 数据访问模板实例，永不为null
     * @throws IllegalArgumentException 当参数为null时
     * @throws ProviderException 创建失败时
     * @since 1.0.0
     */
    @NonNull
    <T, ID> DataAccessTemplate<T, ID> createTemplate(
        @NonNull Class<T> entityType, 
        @NonNull Class<ID> idType,
        @NonNull DataSourceConfig config
    );
}
```

## 3. XCE异常处理规范

### 3.1 XCE异常层次结构

```java
/**
 * Commons DB数据库异常基类
 * 基于XCE异常分类标准的数据库类异常
 * 错误码段：650-699
 *
 * @since 1.0.0
 */
public class DatabaseSystemException extends SystemException {

    // 🔑 实施关键点：XCE数据库异常错误码
    public static final String CONNECTION_POOL_EXHAUSTED = "XCE_DB_650";
    public static final String TRANSACTION_TIMEOUT = "XCE_DB_651";
    public static final String QUERY_TIMEOUT = "XCE_DB_652";
    public static final String CONNECTION_FAILED = "XCE_DB_653";
    public static final String DEADLOCK_DETECTED = "XCE_DB_654";
    public static final String CONSTRAINT_VIOLATION = "XCE_DB_655";

    public DatabaseSystemException(@NonNull String errorCode, @NonNull String message) {
        super(errorCode, message);
    }

    public DatabaseSystemException(@NonNull String errorCode, @NonNull String message, @Nullable Throwable cause) {
        super(errorCode, message, cause);
    }

    // 🔑 实施关键点：便捷的异常创建方法
    public static DatabaseSystemException connectionPoolExhausted() {
        return new DatabaseSystemException(CONNECTION_POOL_EXHAUSTED, "数据库连接池已耗尽");
    }

    public static DatabaseSystemException queryTimeout(String query, long timeoutMs) {
        return new DatabaseSystemException(QUERY_TIMEOUT,
            String.format("查询超时: %dms, SQL: %s", timeoutMs, query));
    }

    public static DatabaseSystemException transactionTimeout(long timeoutMs) {
        return new DatabaseSystemException(TRANSACTION_TIMEOUT,
            String.format("事务超时: %dms", timeoutMs));
    }
}

/**
 * Commons DB验证异常
 * 基于XCE异常分类标准的验证类异常
 * 错误码段：750-799
 *
 * @since 1.0.0
 */
public class ValidationBusinessException extends BusinessException {

    // 🔑 实施关键点：XCE验证异常错误码
    public static final String ENTITY_NOT_FOUND = "XCE_VAL_750";
    public static final String INVALID_QUERY_SYNTAX = "XCE_VAL_751";
    public static final String INVALID_PARAMETER = "XCE_VAL_752";
    public static final String ENTITY_STATE_INVALID = "XCE_VAL_753";

    public ValidationBusinessException(@NonNull String errorCode, @NonNull String message) {
        super(errorCode, message);
    }

    // 🔑 实施关键点：便捷的异常创建方法
    public static ValidationBusinessException entityNotFound(@NonNull String entityType, @NonNull Object id) {
        return new ValidationBusinessException(ENTITY_NOT_FOUND,
            String.format("实体未找到: %s[id=%s]", entityType, id));
    }

    public static ValidationBusinessException invalidQuerySyntax(@NonNull String query, @NonNull String reason) {
        return new ValidationBusinessException(INVALID_QUERY_SYNTAX,
            String.format("查询语法错误: %s, 原因: %s", query, reason));
    }

    public static ValidationBusinessException invalidParameter(@NonNull String paramName, @NonNull Object value) {
        return new ValidationBusinessException(INVALID_PARAMETER,
            String.format("参数验证失败: %s=%s", paramName, value));
    }
}
```

### 3.2 XCE异常处理最佳实践

```java
// ✅ 正确的XCE异常处理
public Optional<User> findUser(Long id) {
    try {
        return userTemplate.findById(id);
    } catch (ValidationBusinessException e) {
        // 验证异常（如实体未找到）是正常业务场景
        if (ValidationBusinessException.ENTITY_NOT_FOUND.equals(e.getErrorInfo().getCode())) {
            return Optional.empty();
        }
        // 其他验证异常需要重新抛出
        throw e;
    } catch (DatabaseSystemException e) {
        // 数据库系统异常需要记录日志并重新抛出
        log.error("数据库访问失败: userId={}, errorCode={}", id, e.getErrorInfo().getCode(), e);
        throw e;
    }
}

// ✅ 正确的XCE异常转换
@Component
public class CommonsDbExceptionTranslator {

    public void translateAndThrow(Exception originalException, String operation) {
        if (originalException instanceof DataAccessException) {
            // Spring DataAccessException转换为XCE异常
            if (originalException instanceof QueryTimeoutException) {
                throw DatabaseSystemException.queryTimeout("unknown", 30000);
            } else if (originalException instanceof DataIntegrityViolationException) {
                throw new DatabaseSystemException(DatabaseSystemException.CONSTRAINT_VIOLATION,
                    "数据完整性约束违规", originalException);
            }
        }

        // 其他异常转换为通用系统异常
        throw SystemException.internalError("数据访问操作失败: " + operation, originalException);
    }
}

// ❌ 错误的异常处理
public User findUser(Long id) {
    try {
        return userTemplate.findById(id).orElse(null);
    } catch (Exception e) {
        // 过于宽泛的异常捕获，违反XCE分类原则
        return null; // 丢失了异常信息和错误码
    }
}
```

## 4. 命名约定规范

### 4.1 接口命名规范

| 类型 | 命名模式 | 示例 |
|------|----------|------|
| 核心接口 | `XxxTemplate` | `DataAccessTemplate` |
| SPI接口 | `XxxProvider` | `DataAccessProvider` |
| 配置类 | `XxxConfig` | `DataSourceConfig` |
| 异常类 | `XxxException` | `DataAccessException` |
| 枚举类 | `XxxType/XxxStatus` | `QueryType`, `HealthStatus` |

### 4.2 方法命名规范

| 操作类型 | 命名模式 | 示例 |
|----------|----------|------|
| 查询操作 | `find*`, `query*`, `get*` | `findById`, `queryBySpec` |
| 保存操作 | `save*`, `create*`, `insert*` | `save`, `batchInsert` |
| 更新操作 | `update*`, `modify*` | `update`, `batchUpdate` |
| 删除操作 | `delete*`, `remove*` | `deleteById`, `removeAll` |
| 统计操作 | `count*`, `exists*` | `count`, `existsById` |
| 批量操作 | `batch*` | `batchInsert`, `batchUpdate` |

### 4.3 参数命名规范

```java
// ✅ 正确的参数命名
public List<User> findUsers(
    @NonNull UserQueryCriteria criteria,    // 查询条件
    @NonNull Pageable pageable,             // 分页参数
    @Nullable QueryHint hint                 // 查询提示
);

// ❌ 错误的参数命名
public List<User> findUsers(
    Object c,           // 不明确的参数名
    Pageable p,         // 缩写不清晰
    QueryHint qh        // 不必要的缩写
);
```

## 5. 注解规范

### 5.1 核心注解定义

```java
/**
 * 指定使用的数据访问提供者
 * 
 * @since 1.0.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface UseProvider {
    
    /**
     * 提供者名称
     * 
     * @return 提供者名称
     */
    @NonNull
    String value();
}

/**
 * 指定使用的数据源
 * 
 * @since 1.0.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface UseDataSource {
    
    /**
     * 数据源名称
     * 
     * @return 数据源名称
     */
    @NonNull
    String value() default "primary";
}

/**
 * 标记实体支持批量操作优化
 * 
 * @since 1.0.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BatchOptimized {
    
    /**
     * 批量大小
     * 
     * @return 批量大小
     */
    int batchSize() default 100;
}
```

### 5.2 注解使用示例

```java
// 实体类注解使用
@Entity
@UseProvider("querydsl")
@UseDataSource("primary")
@BatchOptimized(batchSize = 500)
public class User {
    @Id
    private Long id;
    private String username;
    // ...
}

// Repository注解使用
@Repository
@UseProvider("jpa")
public interface UserRepository extends JpaRepository<User, Long> {
    // ...
}
```

## 6. 版本兼容性规范

### 6.1 API版本策略

- **主版本号**：不兼容的API变更
- **次版本号**：向后兼容的功能新增
- **修订版本号**：向后兼容的问题修复

### 6.2 废弃API处理

```java
/**
 * 查询所有实体（已废弃）
 * 
 * @return 实体列表
 * @deprecated 自1.2.0起废弃，请使用 {@link #findAll()} 替代
 * @since 1.0.0
 */
@Deprecated(since = "1.2.0", forRemoval = true)
@NonNull
List<T> queryAll();
```

## 7. 文档规范

### 7.1 JavaDoc规范

```java
/**
 * 数据访问模板接口的简短描述
 * 
 * <p>详细描述数据访问模板的功能和用途。
 * 可以包含多个段落来详细说明。</p>
 * 
 * <p>使用示例：</p>
 * <pre>{@code
 * DataAccessTemplate<User, Long> userTemplate = ...;
 * User user = userTemplate.findById(1L).orElse(null);
 * }</pre>
 * 
 * @param <T> 实体类型
 * @param <ID> 主键类型
 * <AUTHOR> DB Team
 * @since 1.0.0
 * @see DataAccessProvider
 * @see QuerySpec
 */
public interface DataAccessTemplate<T, ID> {
    // ...
}
```

### 7.2 代码示例规范

```java
/**
 * 批量保存实体
 * 
 * <p>示例用法：</p>
 * <pre>{@code
 * List<User> users = Arrays.asList(
 *     new User("alice"),
 *     new User("bob")
 * );
 * List<User> savedUsers = userTemplate.saveAll(users);
 * }</pre>
 * 
 * @param entities 实体集合
 * @return 保存后的实体列表
 * @throws IllegalArgumentException 当entities为null或空时
 * @since 1.0.0
 */
@NonNull
List<T> saveAll(@NonNull Iterable<T> entities);
```

## 8. 实施检查清单

### 8.1 API设计检查
- [ ] 接口命名符合规范
- [ ] 方法签名一致性
- [ ] 参数验证完整性
- [ ] 异常处理规范性
- [ ] 返回值类型合理性

### 8.2 文档检查
- [ ] JavaDoc完整性
- [ ] 代码示例准确性
- [ ] 参数说明清晰性
- [ ] 异常说明完整性
- [ ] 版本信息正确性

### 8.3 兼容性检查
- [ ] 向后兼容性保证
- [ ] 废弃API标记
- [ ] 版本号正确性
- [ ] 迁移指南完整性

---

**实施提示**: 此文档为API接口规范的完整定义，重点关注一致性、类型安全和文档完整性。后续实施时需要严格遵循这些规范，确保API的专业性和易用性。
