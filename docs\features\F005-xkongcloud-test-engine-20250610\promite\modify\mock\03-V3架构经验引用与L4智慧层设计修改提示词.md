# 03-V3架构经验引用与L4智慧层设计修改提示词

**文档版本**: MODIFY-V3-WISDOM-REFERENCE  
**创建时间**: 2025年6月10日  
**修改目标**: 在V3架构经验引用中增加Mock环境的详细分类和gRPC接口模拟机制

---

## 🎯 修改目标

在V3架构经验引用中增加Mock环境的详细分类，并在参数化零业务耦合设计中增加gRPC接口Mock机制。

## 📝 具体修改内容

### **修改位置1：环境感知透明度设计 - 增加Mock环境详细分类**

**在UniversalEnvironmentAwarenessProvider类中修改**：
```java
/**
 * 检测环境类型
 * 基于V3设计文档的环境分类，增加Mock环境的详细分类
 */
private EnvironmentType detectEnvironmentType() {
    if (isTestContainersEnvironment()) {
        return EnvironmentType.REAL_TESTCONTAINERS;      // 真实容器环境，高可靠性
    } else if (isMockDevelopmentMode()) {
        return EnvironmentType.MOCK_DEVELOPMENT;         // 开发阶段快速验证
    } else if (isMockDiagnosticMode()) {
        return EnvironmentType.MOCK_DIAGNOSTIC;          // 故障诊断分析
    } else if (isMockProtectionMode()) {
        return EnvironmentType.MOCK_PROTECTION;          // TestContainers失败保护
    } else if (isMockInterfaceMode()) {
        return EnvironmentType.MOCK_INTERFACE;           // gRPC接口模拟
    } else if (isProductionLikeEnvironment()) {
        return EnvironmentType.PRODUCTION_LIKE;          // 生产类似环境，最高可靠性
    } else {
        return EnvironmentType.UNKNOWN;                  // 未知环境，保守处理
    }
}

/**
 * 基于环境感知调整处理策略
 * 引用V3设计文档的环境适应机制，增加Mock环境的详细策略
 */
public UniversalProcessingStrategy adaptProcessingStrategy(
        UniversalEnvironmentAwareness awareness,
        UniversalTestConfiguration config) {
    
    UniversalProcessingStrategy strategy = new UniversalProcessingStrategy();
    
    switch (awareness.getEnvironmentType()) {
        case MOCK_DEVELOPMENT:
            // 开发阶段Mock：快速验证，宽松精度要求
            strategy.setProcessingLevel(ProcessingLevel.FAST_DEVELOPMENT);
            strategy.setAccuracyRequirement(AccuracyLevel.DEVELOPMENT_FRIENDLY);
            strategy.setTimeoutSeconds(10);
            strategy.setPurpose("开发阶段快速验证程序逻辑");
            break;
            
        case MOCK_DIAGNOSTIC:
            // 故障诊断Mock：标准处理，确保诊断准确性
            strategy.setProcessingLevel(ProcessingLevel.DIAGNOSTIC_ANALYSIS);
            strategy.setAccuracyRequirement(AccuracyLevel.DIAGNOSTIC_PRECISE);
            strategy.setTimeoutSeconds(60);
            strategy.setPurpose("故障诊断分析，精确区分环境问题与代码问题");
            break;
            
        case MOCK_PROTECTION:
            // 保护模式Mock：保守处理，确保系统连续性
            strategy.setProcessingLevel(ProcessingLevel.PROTECTION_MODE);
            strategy.setAccuracyRequirement(AccuracyLevel.CONSERVATIVE);
            strategy.setTimeoutSeconds(30);
            strategy.setPurpose("TestContainers失败保护，维持基础分析能力");
            break;
            
        case MOCK_INTERFACE:
            // 接口模拟Mock：严格处理，确保接口一致性
            strategy.setProcessingLevel(ProcessingLevel.INTERFACE_SIMULATION);
            strategy.setAccuracyRequirement(AccuracyLevel.INTERFACE_STRICT);
            strategy.setTimeoutSeconds(45);
            strategy.setPurpose("gRPC接口模拟，验证接口调用逻辑");
            break;
            
        case REAL_TESTCONTAINERS:
            // TestContainers环境：标准处理，平衡精度和性能
            strategy.setProcessingLevel(ProcessingLevel.STANDARD_PROCESSING);
            strategy.setAccuracyRequirement(AccuracyLevel.STANDARD);
            strategy.setTimeoutSeconds(300);
            strategy.setPurpose("真实环境完整验证");
            break;
            
        case PRODUCTION_LIKE:
            // 生产类似环境：严格处理，最高精度要求
            strategy.setProcessingLevel(ProcessingLevel.STRICT_VALIDATION);
            strategy.setAccuracyRequirement(AccuracyLevel.ULTRA_STRICT);
            strategy.setTimeoutSeconds(600);
            strategy.setPurpose("生产级别严格验证");
            break;
            
        default:
            // 未知环境：保守处理
            strategy.setProcessingLevel(ProcessingLevel.CONSERVATIVE);
            strategy.setAccuracyRequirement(AccuracyLevel.STANDARD);
            strategy.setTimeoutSeconds(120);
            strategy.setPurpose("未知环境保守处理");
            break;
    }
    
    return strategy;
}
```

### **修改位置2：参数化零业务耦合设计 - 增加gRPC接口Mock机制**

**在UniversalParametricExecutionEngine类中增加**：
```java
@Autowired
private GrpcInterfaceMockManager grpcMockManager;

@Autowired
private DatabaseQueryMockMapper queryMockMapper;

/**
 * gRPC接口Mock执行引擎
 * 引用V3设计文档的参数化通用引擎设计，增加gRPC接口模拟能力
 */
@Component
public class GrpcInterfaceMockExecutionEngine {
    
    /**
     * 执行gRPC接口Mock测试
     * 模拟外部gRPC服务，验证接口调用逻辑和数据一致性
     */
    public GrpcMockExecutionResult executeGrpcMockTest(GrpcMockTestConfig config) {
        
        GrpcMockExecutionResult result = new GrpcMockExecutionResult();
        result.setConfigId(config.getConfigId());
        result.setStartTime(LocalDateTime.now());
        
        try {
            // Step 1: 启动gRPC Mock服务
            GrpcMockServer mockServer = grpcMockManager.startMockServer(config.getGrpcConfig());
            
            // Step 2: 配置Mock响应规则
            configureMockResponses(mockServer, config.getMockResponseRules());
            
            // Step 3: 执行gRPC调用测试
            for (GrpcCallTestCase testCase : config.getTestCases()) {
                GrpcCallResult callResult = executeGrpcCall(testCase, mockServer);
                result.addCallResult(callResult);
            }
            
            // Step 4: 验证数据一致性
            DataConsistencyResult consistencyResult = verifyDataConsistency(result.getCallResults(), config);
            result.setDataConsistencyResult(consistencyResult);
            
            result.setSuccessful(true);
            result.setEndTime(LocalDateTime.now());
            
        } catch (Exception e) {
            log.error("gRPC接口Mock测试执行失败", e);
            result.setSuccessful(false);
            result.setErrorMessage(e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 配置Mock响应规则
     * 将gRPC请求参数映射为Mock响应数据
     */
    private void configureMockResponses(GrpcMockServer mockServer, List<MockResponseRule> rules) {
        for (MockResponseRule rule : rules) {
            mockServer.when(rule.getRequestMatcher())
                     .thenReturn(rule.getResponseData());
        }
    }
}

/**
 * 数据库查询Mock映射器
 * 将gRPC请求参数映射为数据库查询，确保数据一致性
 */
@Component
public class DatabaseQueryMockMapper {
    
    /**
     * 将gRPC请求参数映射为数据库查询
     * 引用V3设计文档的零业务耦合设计理念
     */
    public DatabaseQueryMockResult mapGrpcRequestToQuery(
            GrpcRequest grpcRequest, 
            DatabaseMockConfig mockConfig) {
        
        try {
            // 解析gRPC请求参数
            Map<String, Object> requestParams = parseGrpcRequestParams(grpcRequest);
            
            // 映射为数据库查询
            DatabaseQuery query = buildDatabaseQuery(requestParams, mockConfig.getQueryTemplate());
            
            // 执行Mock查询
            MockQueryResult queryResult = executeMockQuery(query, mockConfig.getMockDataSource());
            
            // 转换为gRPC响应格式
            GrpcResponse grpcResponse = convertToGrpcResponse(queryResult, mockConfig.getResponseMapping());
            
            return DatabaseQueryMockResult.builder()
                .grpcRequest(grpcRequest)
                .databaseQuery(query)
                .queryResult(queryResult)
                .grpcResponse(grpcResponse)
                .mappingSuccessful(true)
                .build();
                
        } catch (Exception e) {
            log.error("gRPC请求到数据库查询映射失败", e);
            return DatabaseQueryMockResult.failure(grpcRequest, e.getMessage());
        }
    }
    
    /**
     * 验证接口一致性
     * 确保Mock接口与真实接口的一致性
     */
    public InterfaceConsistencyResult verifyInterfaceConsistency(
            GrpcMockExecutionResult mockResult,
            GrpcRealExecutionResult realResult) {
        
        InterfaceConsistencyResult result = new InterfaceConsistencyResult();
        
        // 验证响应数据结构一致性
        boolean structureConsistent = verifyResponseStructure(mockResult, realResult);
        result.setStructureConsistent(structureConsistent);
        
        // 验证数据类型一致性
        boolean typeConsistent = verifyDataTypes(mockResult, realResult);
        result.setTypeConsistent(typeConsistent);
        
        // 验证业务逻辑一致性
        boolean logicConsistent = verifyBusinessLogic(mockResult, realResult);
        result.setLogicConsistent(logicConsistent);
        
        result.setOverallConsistent(structureConsistent && typeConsistent && logicConsistent);
        
        return result;
    }
}
```

## 🎯 修改原则

1. **详细Mock环境分类**：明确区分不同Mock环境的使用场景和策略
2. **gRPC接口Mock机制**：提供完整的gRPC接口模拟和验证能力
3. **数据一致性保证**：确保Mock数据与真实数据的一致性
4. **接口契约验证**：验证Mock接口与真实接口的一致性

## 📋 验证要点

修改完成后，文档应该能够让AI清晰理解：
- Mock环境的详细分类和使用场景
- gRPC接口Mock的实现机制和验证策略
- 数据库查询Mock映射的设计理念
- 接口一致性验证的重要性和实现方式
