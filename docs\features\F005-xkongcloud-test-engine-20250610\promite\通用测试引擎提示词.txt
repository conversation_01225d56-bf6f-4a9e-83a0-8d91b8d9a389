xkongcloud-test-engine统一架构设计提示词（V2最佳实践继承版）
架构定位与目标
核心定位
V2神经可塑性核心智慧继承：基于独立批判性分析，深度继承V2神经可塑性分层智能的核心价值
避免重复造轮子原则：不重新发明V2已经成熟的架构智慧，专注现代化重构和通用化扩展
功能等价架构优化：确保L1-L3输出完全一致，但架构实现可以完全不同且更优
设计目标
V2核心价值精准继承：继承神经可塑性分层智能、类型安全接口、声明式架构等核心价值，避免业务特定逻辑
智能决策机制现代化：基于V2的全知覆盖+选择性注意力理念，现代化实现智能资源协调和能力调度
自适应学习能力增强：继承V2的历史对比分析和自认知评估智慧，集成现代AI技术增强学习能力
通用化架构重构：在V2核心智慧基础上，设计支持所有xkongcloud子项目的通用测试引擎架构

## 🏗️ 历史架构经验继承（基于现有设计文档）

### 核心设计理念引用
基于以下历史架构设计文档的核心智慧，避免重复造轮子：
- **V2-V3集成架构**：`docs/features/F003-PostgreSQL迁移-20250508/test/engine/v3/code-architecture/01-v2-v3-integration-architecture.md`
- **AI故障三环路处理**：`docs/features/F003-PostgreSQL迁移-20250508/test/engine/v3/code-architecture/02-v3-ai-failure-triple-loop-processor.md`
- **参数化推演引擎**：`docs/features/F003-PostgreSQL迁移-20250508/test/engine/v3/code-architecture/05-v3-business-simulation-engine-design.md`

### 历史架构核心智慧继承要点

#### 1. xkongcloud-test-engine v1 = V2的L4智慧层实现理念
```java
// 继承历史架构的完整性设计思想
@Component
@NeuralUnit(layer = "L4", type = "WISDOM")
public class UniversalTestEngine implements LayerProcessor<L3ArchitecturalData, L4WisdomData> {
    // 直接注入V2引擎（零代码修改）
    @Autowired private L1PerceptionEngine l1Engine;
    @Autowired private L2CognitionEngine l2Engine;
    @Autowired private L3UnderstandingEngine l3Engine;
}
```

#### 2. AI三环路智能处理机制
```java
// 继承历史架构的智能故障处理设计
第一环路：快速诊断处理（80%问题解决）
第二环路：深度分析处理（19%问题解决）
第三环路：人工移交处理（1%问题需要人工）
```

#### 3. 环境感知透明度设计
```java
// 继承历史架构的环境感知智慧
EnvironmentAwareness awareness = environmentAwareness.getCurrentAwareness();
// 明确环境类型：MOCK_DIAGNOSTIC、REAL_TESTCONTAINERS、PRODUCTION_LIKE
```

#### 4. 参数化通用引擎设计
```java
// 继承历史架构的零业务耦合设计理念
// 通过反射调用任意真实业务Service，实现最大通用性
Object executionResult = parameterInjectionManager.injectParametersToService(
    action.getTargetService(), action.getTargetMethod(), actionParameters);
```
项目类型分类与适配策略
项目类型矩阵

项目分类维度：
- KV参数依赖：有/无
- 持久化存储：有/无  
- Service层：有/无
- 对外接口：有/无

典型项目类型：
1. 完整微服务：KV + 持久化 + Service + 接口（如business-internal-core）
2. 轻量服务：KV + Service + 接口（如配置服务、网关服务）
3. 数据处理服务：持久化 + Service（如ETL服务、报表服务）
4. 纯计算服务：仅Service（如算法服务、工具服务）
5. 静态服务：仅接口（如文档服务、健康检查服务）
6. 最小化服务：无外部依赖（如纯函数库、工具类）

智能架构分析与替换机制
项目架构深度扫描器：全面分析现有项目架构、测试代码结构、业务逻辑覆盖范围
测试覆盖率分析器：分析现有测试的覆盖率和测试场景，确保通用引擎100%覆盖
智能能力映射器：基于架构分析结果，自动映射到最优的引擎能力组合
配置智能生成器：基于现有项目特征和测试需求，自动生成最优配置模板
替换策略规划器：制定灰度替换计划，支持按功能模块渐进式替换现有测试代码
核心架构设计
四层神经可塑性架构（V2精髓继承与现代化重构）
L1感知层（继承V2感知智慧，避免重复造轮子）

神经感知理念继承：继承V2的L1如人脑感官收集技术细节但不做判断的感知层设计理念
类型安全数据流：继承V2的LayerProcessor<RawTestData, L1AbstractedData>类型安全接口设计
技术深度抽象智慧：继承V2的技术细节感知、数据抽象、指标生成等核心抽象能力
现代化技术实现：采用现代监控技术重新实现，但保持V2的感知层核心智慧
L2认知层（继承V2模式识别智慧，现代化算法）

神经认知理念继承：继承V2的L2如人脑模式识别发现关联和模式的认知层设计理念
智能模式识别：继承V2的analyzeIssuePatterns、generateRecommendations、calculateConfidence核心算法智慧
关联分析能力：继承V2的性能关联分析和业务流程模式识别增强能力
现代化算法增强：在V2智慧基础上，集成现代机器学习算法提升识别精度
L3理解层（继承V2架构分析智慧，现代化升级）

神经理解理念继承：继承V2的L3如人脑逻辑分析进行架构风险评估的理解层设计理念
架构分析框架：继承V2的架构风险评估和业务影响分析方法论
风险评估智慧：继承V2的风险识别分析、影响范围分析、演进建议生成能力
现代化架构支持：扩展支持云原生、微服务、事件驱动等现代架构模式
L4智慧层（继承V2决策智慧，实现完整神经可塑性）

神经智慧理念继承：继承V2的L4如人脑决策中枢的智慧层设计理念，实现完整的神经可塑性架构
全知覆盖确认：继承V2的顶层全知原则，通过各层历史报告掌握所有细节覆盖情况
选择性注意力：继承V2的L4选择性关注特定层级细节的智能注意力控制机制
按需调动能力：继承V2的智能资源协调和能力调度理念，现代化实现自动化决策

### 历史架构经验在通用引擎中的应用

#### 1. 完全复用现有架构模式（继承历史V2集成智慧）
```java
/**
 * 通用测试引擎协调器
 * 基于历史V2集成架构设计，复用现有V2分层架构和统一管理系统
 */
@Component
public class UniversalTestEngineCoordinator {
    // 直接注入V2引擎（零代码修改）
    @Autowired private L1PerceptionEngine l1Engine;
    @Autowired private L2CognitionEngine l2Engine;
    @Autowired private L3UnderstandingEngine l3Engine;

    // 注入V2统一管理系统（复用V2基础设施）
    @Autowired private UniversalReportOutputInterface reportOutput;
    @Autowired private UniversalVersionManager versionManager;
    @Autowired private UniversalDirectoryManager directoryManager;
    @Autowired private AIIndexSystemManager aiIndexManager;
}
```

#### 2. 智能故障处理机制（继承历史三环路处理智慧）
```java
/**
 * 通用引擎智能故障处理器
 * 基于历史AI故障三环路处理机制设计
 */
@Component
public class UniversalFailureProcessor {
    // 第一环路：快速诊断处理（80%故障在此解决）
    private UniversalQuickDiagnosisResult executeFirstLoop();

    // 第二环路：深度分析处理（19%故障在此解决）
    private UniversalDeepAnalysisResult executeSecondLoop();

    // 第三环路：人工移交处理（1%故障需要人工）
    private UniversalHumanEscalationResult executeThirdLoop();
}
```

#### 3. 环境感知透明度（继承历史环境感知设计）
```java
/**
 * 通用引擎环境感知器
 * 基于历史环境感知透明度设计，明确当前处理能力边界
 */
@Component
public class UniversalEnvironmentAwarenessProvider {
    public UniversalEnvironmentAwareness getCurrentAwareness() {
        // 明确环境类型和可靠性评分
        // MOCK_DIAGNOSTIC、REAL_TESTCONTAINERS、PRODUCTION_LIKE
    }
}
```

#### 4. 参数化通用设计（继承历史零业务耦合智慧）
```java
/**
 * 通用参数化执行引擎
 * 基于历史参数化业务推演引擎设计，实现零业务耦合
 */
@Component
public class UniversalParametricExecutionEngine {
    // 通过反射调用任意真实业务Service
    public Object executeParametricTest(String serviceName, String methodName, ParameterSet parameters) {
        Object serviceBean = applicationContext.getBean(serviceName);
        Method targetMethod = findTargetMethod(serviceBean, methodName, parameters);
        return targetMethod.invoke(serviceBean, buildMethodArguments(targetMethod, parameters));
    }
}
```
五大可选引擎（按需激活）
环境自主启动引擎（KV参数项目专用）

KV参数检测与模拟：自动检测项目的KV参数依赖，提供Mock实现
服务发现模拟：模拟微服务注册中心，支持服务间调用测试
配置中心模拟：模拟配置中心服务，提供动态配置能力
依赖服务Mock：模拟项目依赖的外部服务，实现独立测试
统一持久化重建引擎（持久化项目专用）

多数据源自动检测：自动识别项目使用的数据库、缓存、消息队列类型
数据模式分析：分析数据库表结构、索引、约束等数据模式
测试数据生成：基于数据模式和业务规则生成符合要求的测试数据
环境隔离管理：通过TestContainers实现完全隔离的测试环境
参数化推演引擎（Service项目专用）

Service自动发现：扫描Spring容器中的Service Bean，自动构建调用映射
业务流程编排：支持复杂业务流程的多步骤、多分支推演
参数化测试执行：基于JSON配置的参数化测试数据注入和执行
结果验证框架：自动验证Service调用结果的正确性和业务规则符合性
数据库驱动Mock引擎（持久化+Service项目专用）

数据一致性保证：确保Mock返回数据与数据库中数据完全一致
智能查询映射：将gRPC/HTTP请求参数映射为数据库查询
动态数据更新：支持测试过程中数据的动态变更和Mock响应同步
事务模拟支持：模拟数据库事务行为，支持回滚和一致性测试
选择性接口测试引擎（接口项目专用）

接口自动发现：扫描Controller类和gRPC服务，自动构建接口映射
协议自适应：支持REST API、gRPC、WebSocket等多种协议的统一测试
测试数据自动生成：基于接口定义和业务规则自动生成测试数据
契约测试支持：支持API契约测试和向后兼容性验证
自适应配置管理架构
分层配置体系（按项目类型简化）
项目配置目录结构：
${PROJECT_ROOT}/xkongcloud-test-engine-configs/
├── project-profile.json           # 项目画像（自动生成或手动配置）
├── global-config.json            # 全局配置（基于项目类型模板）
├── [可选] kv-config.json         # KV参数配置（仅KV依赖项目）
├── [可选] persistence-config.json # 持久化配置（仅持久化项目）
├── [可选] service-config.json    # Service配置（仅Service项目）
├── [可选] interface-config.json  # 接口配置（仅接口项目）
└── [可选] business/              # 业务配置目录（复杂项目）
    ├── roles/                    # 角色和权限配置
    ├── scenarios/                # 业务场景配置
    └── data-templates/           # 数据模板配置

项目画像自动生成
代码结构分析：扫描项目目录结构，识别典型的Spring Boot项目模式
依赖关系分析：分析pom.xml或build.gradle，识别使用的技术栈
配置文件分析：分析application.properties/yml，识别外部依赖配置
注解扫描分析：扫描Spring注解，识别Service、Controller、Repository等组件
配置模板智能匹配
项目类型识别：基于项目画像自动识别最匹配的项目类型
模板自动选择：从模板库中选择最适合的配置模板
配置自动生成：基于模板和项目特征自动生成初始配置
增量配置支持：支持在基础配置上增加项目特定的配置
ProjectAdapter接口设计（可选实现）
基础适配器接口
项目基本信息：项目名称、版本、类型等基础信息（必须实现）
能力声明接口：声明项目具备的能力类型（必须实现）
KV参数适配：提供KV参数映射（可选实现，默认返回空）
持久化适配：提供持久化配置（可选实现，默认返回空）
Service适配：提供Service映射（可选实现，默认返回空）
接口适配：提供接口映射（可选实现，默认返回空）
默认适配器实现
自动扫描适配器：基于项目扫描结果自动生成适配器实现
配置驱动适配器：基于配置文件驱动的通用适配器实现
最小化适配器：仅提供基础信息的最简适配器实现
模板化适配器：基于项目类型模板的标准适配器实现
引擎执行流程架构
自适应执行链路
项目扫描与画像生成 → 能力检测与映射 → 配置模板匹配 → 
适配器自动生成 → 引擎能力激活 → 版本一致性检查 → 
[条件]KV参数模拟启动 → [条件]持久化环境重建 → 
[条件]数据库驱动Mock配置 → L1感知数据收集 → 
L2认知模式分析 → L3理解架构评估 → L4智慧决策执行 → 
[条件]参数化业务推演 → [条件]接口测试执行 → 
结果聚合分析 → 智能报告生成
能力激活策略
最小能力集：所有项目都激活L1-L4神经可塑性分析能力
增量能力激活：根据项目特征逐步激活额外的引擎能力
性能优化激活：根据项目规模和复杂度调整引擎执行策略
故障降级机制：某个引擎能力失效时的优雅降级和继续执行
替换复杂度分级（智能性无损）
零配置完全替换（最简单项目）
依赖完全替换：删除现有测试依赖，引入xkongcloud-test-engine-spring-boot-starter
代码完全删除：删除所有现有测试代码，使用通用引擎API
智能自动配置：引擎基于项目架构分析，自动生成最优配置和测试策略
最小配置完全替换（简单项目）
架构分析配置：基于现有项目架构分析，配置项目类型和特征
能力智能激活：基于架构分析结果，智能激活最优引擎能力组合
测试覆盖率保证：确保通用引擎测试覆盖率不低于现有实现
标准配置完全替换（中等复杂项目）
适配器智能实现：基于架构分析，自动生成ProjectAdapter实现
配置模板定制：基于现有测试场景分析，定制项目特定配置模板
业务逻辑完全覆盖：确保所有现有业务逻辑测试场景完全覆盖
完整配置完全替换（复杂项目）
深度架构分析：全面分析现有复杂项目架构和测试体系
智能性增强实现：在保持现有智能性基础上，增强分析和测试能力
高级功能无损迁移：将现有高级测试功能无损迁移到通用引擎

### 历史架构经验继承的实施原则

#### 1. 避免重复造轮子原则
- **不重新实现历史已验证的架构模式**：直接引用历史的V2集成架构设计
- **不重新设计历史已成熟的处理机制**：继承三环路故障处理、环境感知透明度等核心机制
- **不重新发明历史已优化的通用设计**：采用参数化零业务耦合的设计理念

#### 2. 精准继承边界控制
- **继承架构智慧**：历史架构的L4智慧层补全理念、三环路处理机制、环境感知设计
- **继承设计模式**：V2完全复用模式、参数化通用引擎模式、智能故障处理模式
- **避免业务特定逻辑**：不继承历史架构中PostgreSQL迁移等特定业务实现
- **避免过度复杂性**：批判性评估历史架构的复杂设计，避免过度工程

#### 3. 引用文档驱动开发
- **架构设计阶段**：参考历史架构文档的设计理念和模式
- **接口设计阶段**：继承历史架构的LayerProcessor接口设计和NeuralUnit注解模式
- **实现开发阶段**：采用历史架构的环境感知、参数化执行等核心机制
- **测试验证阶段**：应用历史架构的三环路处理机制进行智能故障处理

#### 4. 现代化重构增强
- **技术栈现代化**：在历史架构智慧基础上，采用最新技术栈重新实现
- **云原生架构升级**：将历史架构的设计理念融入云原生架构，提升可扩展性
- **AI能力增强**：基于历史架构的智能处理机制，集成现代AI技术提升自动化水平
- **通用性扩展**：在历史架构零业务耦合基础上，进一步提升通用引擎的适配能力
技术实现架构
核心技术栈
Spring Boot 3.x：基础框架，提供依赖注入和自动配置
Spring Data：统一数据访问，支持多种数据源的抽象
TestContainers：容器化测试环境，提供真实的外部依赖
Micrometer：指标收集，提供统一的监控和度量能力
扩展性架构
插件化引擎：支持自定义引擎组件的插件化扩展
SPI机制：通过Java SPI机制支持第三方扩展
事件驱动：基于Spring Events的松耦合组件通信
异步执行：支持长时间测试的异步执行和进度跟踪
性能优化架构
懒加载机制：按需加载引擎组件，减少启动时间和内存占用
缓存策略：智能缓存分析结果，避免重复计算
并行执行：支持多引擎并行执行，提高测试效率
资源池管理：统一管理测试资源，避免资源竞争和浪费
部署与运维架构
Maven模块结构
xkongcloud-commons/xkongcloud-test-engine/
├── engine-core/                    # 核心引擎实现
├── engine-neural/                  # 神经可塑性分析引擎
├── engine-adapters/                # 项目适配器框架
├── engine-templates/               # 配置模板库
├── engine-spring-boot-starter/     # Spring Boot集成
├── engine-plugins/                 # 扩展插件框架
└── engine-examples/                # 使用示例和最佳实践
版本管理策略
语义化版本：采用语义化版本控制，确保向后兼容性
配置版本管理：配置文件版本与引擎版本的兼容性管理
迁移工具支持：提供配置和适配器的自动迁移工具
兼容性矩阵：维护引擎版本与项目类型的兼容性矩阵
监控与运维
健康检查：集成Spring Boot Actuator，提供引擎健康状态监控
性能监控：详细的测试执行性能监控和分析
日志管理：统一的日志格式和分级，支持日志聚合和分析
告警机制：基于测试结果和系统状态的智能告警