# 实施计划：搭建项目准入审查架构框架

- **文档版本**: 4.0 (已与最终实现对齐)
- **更新日期**: 2025-08-05
- **核心目标**: 搭建一个功能完整的架构框架，以支持项目经理工作台的“项目准入审查”功能。此框架将严格遵循V4.2设计文档，实现一个基于**HTTP轮询**的、支持状态持久化的、采用四阶段阻塞式验证策略的简洁、可复用的服务。
- **架构演进**: 从原计划的WebSocket实时通信**演进为HTTP轮询机制**，显著降低了架构复杂度，提高了系统的稳定性和可调试性。

---

## 1. 核心业务流程 (最终实现)

本框架最终实现了以下三个核心用户流程：

1.  **流程一：创建工作区 (用户首次交互)**
    *   用户在前端界面，通过一个**动态生成的弹窗**输入目标设计文档的目录路径。
    *   前端通过HTTP `POST`请求 `/api/pm_v2/get_and_create` 发送路径信息。
    *   后端创建唯一的`task_id`，为该任务创建项目经理实例，并在目标目录下生成 `_pm_workspace` 工作区和初始状态。
    *   后端返回 `{task_id: "uuid", redirect_url: "/pm_v2/{task_id}"}` 响应。
    *   前端自动跳转到专用页面：`http://localhost:25526/pm_v2/{task_id}`。

2.  **流程二：状态轮询与恢复 (自动)**
    *   用户访问 `/pm_v2/{task_id}` 页面（无论是首次跳转还是刷新页面）。
    *   前端初始化脚本从URL中获取 `task_id`。
    *   前端 `DataManager` 启动一个**定时器**，周期性地向后端发送 `GET` 请求 `/api/pm_v2/status/{task_id}`。
    *   后端从 `StatusQueueManager` 中查询并返回该任务的最新状态。
    *   前端根据返回的状态数据，实时更新UI（如进度条、状态信息等）。此机制天然支持断线重连和状态恢复。

3.  **流程三：执行项目准入审查 (核心功能)**
    *   用户在任务页面点击“开始审查”按钮。
    *   前端通过HTTP `POST`请求 `/api/pm_v2/start-review/{task_id}` 发送启动指令。
    *   后端项目经理在一个**后台线程**中，启动一个严格的、可复用的**四阶段阻塞式**验证流程。
    *   在验证的每个关键节点，后端都会将最新的进度和状态更新到 `StatusQueueManager` 中。
    *   前端的轮询机制会自动获取到这些状态变更，并更新UI，从而实现了**单向数据流的实时反馈**。
    *   任何一个阻塞性阶段验证失败，流程将**立即中止**，并将失败状态更新到队列中，前端轮询到后即可展示给用户。

---

## 2. 架构设计原理 (Architectural Rationale)

当前设计基于以下核心原则，确保了系统的健壮性、可维护性和可扩展性。

1.  **服务复用性 (Reusability)**
    *   “项目准入审查”被设计成一个独立、可复用的核心服务。未来V4.2主治理引擎可以**直接调用**此审查服务作为其流程的第一步，以确保输入的`01号`文档质量达标，保证了全系统验证**标准的统一**。

2.  **关注点分离 (Separation of Concerns)**
    *   **`bp_admission_review.py` (审查蓝图)**: 只负责定义审查**“做什么”**以及**“按什么顺序做”**。
    *   **`ProjectManager` (项目经理)**: 负责**“何时”**触发审查，以及管理任务的生命周期。
    *   **`StatusQueueManager` (状态中心)**: 作为一个线程安全的单例，负责**维护所有异步任务的状态**，是前后端解耦的核心。
    *   **`app.py` (API服务)**: 只负责定义HTTP API端点，并将请求调度到相应的服务，不包含任何业务逻辑。

3.  **健壮性与用户体验 (Robustness & UX)**
    *   采用**HTTP轮询**替代WebSocket，简化了连接管理和状态同步逻辑，避免了复杂的断线重连和心跳机制，使系统更加稳定可靠。
    *   **任务ID (`task_id`)** 机制，结合URL路由，实现了天然的状态持久化和恢复能力。
    *   **阻塞式分阶段审查**的策略，实现了“快速失败”，确保了系统不会在明显的基础问题上浪费计算资源，并能在第一时间将最关键的错误反馈给用户。

---

## 3. 可复用的项目准入审查服务 (Reusable Project Admission Review Service)

为了确保任何提交给V4.2治理引擎的设计文档都是**正确的、安全的、可信赖的和完备的**，我们设计了这个可被系统复用的“项目准入审查”服务。该服务遵循下述严格的、四阶段阻塞式验证策略。

### **审查阶段详细定义**

| 阶段 | 名称 | 核心目标 | 输入 | 核心处理 | 输出/成功标准 | 失败后果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **1** | **基础资产盘点 (Asset Inventory)** | 确认项目具备执行审查所需的最基础物理资产。 | 目标目录路径。 | 1. 检查目录及 `01号-*.md` 文件是否存在且可读。<br>2. 检查 `01号` 文档是否包含**必需的结构锚点**：` ```mermaid ` 和 `### CONSTRAINT-`。 | 所有资产均存在且可读。 | **立即中止**。通知：“项目核心资产缺失，无法启动审查。” |
| **2** | **核心模块语法验证 (Module Syntax Validation)** | 确保核心设计模块的语法正确，为逻辑分析提供可靠的输入。 | 通过第一阶段验证的`01号`文档内容。 | 1. **解析核心模块**: 提取Mermaid图和所有YAML代码块。<br>2. **语法校验**: 使用对应的解析器验证其语法。<br>3. **自动修复**: 对可100%确定的格式问题进行修复。 | 所有核心模块均语法正确或可被成功自动修复。 | **立即中止**。通知：“设计文档核心模块存在致命语法错误。” |
| **3** | **宏观架构健康度评估 (Holistic Architecture Assessment)** | 由AI扮演首席架构师，对设计的整体健全性和逻辑自洽性进行评估。 | 通过第二阶段验证的、语法正确的`01号`文档内容。 | 1. **AI上下文构建**: 准备包含完整文档和设计原则的Prompt。<br>2. **生成健康度报告**: AI分析并产出一个结构化的`DocumentHealthReport`对象（与V4.2主流程定义一致）。<br>3. **致命风险检查**: 检查报告中是否存在`CRITICAL`级别的风险、**循环依赖**或**分层架构违规**。 | `DocumentHealthReport`成功生成，且不包含任何已定义的致命风险。 | **立即中止**。通知：“项目在宏观架构设计上存在致命缺陷。” |
| **4** | **设计完备性与一致性审计 (Design Completeness & Consistency Audit)** | 在宏观设计通过后，审计所有微观设计细节是否完备且一致。 | 通过第三阶段验证的`01号`文档内容。 | 1. **微观约束冲突分析**: 检查不同约束间的逻辑矛盾。<br>2. **架构-实现映射审计**: 验证架构图中的核心组件是否都在代码清单中有定义。<br>3. **职责重叠分析**: 此阶段**复用**V4.2主流程的“阶段零：标准化与预验证”能力，但不生成`AtomicConstraint`，只进行冲突检测。 | 所有微观设计细节均通过一致性和完备性检查。 | **立即中止**。通知：“项目设计细节存在不一致或缺失。” |

---

## 4. 最终实现的技术组件

### **后端核心组件**

1.  **`app.py`**:
    *   定义了三个核心API端点: `POST /api/pm_v2/get_and_create`, `GET /api/pm_v2/status/{task_id}`, `POST /api/pm_v2/start-review/{task_id}`。
    *   将所有业务逻辑委托给 `ProjectManagerService`。
2.  **`ProjectManagerService` (单例服务)**:
    *   作为所有项目管理任务的唯一入口和工厂。
    *   `get_or_create_manager` 方法负责创建或获取与特定项目路径绑定的 `ProjectManager` 实例。
3.  **`ProjectManager` (实例类)**:
    *   与一个 `task_id` 绑定，管理一个完整的审查任务生命周期。
    *   `start_review()` 方法在一个新线程中启动四阶段审查流程。
    *   在审查过程中，通过回调将进度和状态更新到 `StatusQueueManager`。
4.  **`StatusQueueManager` (单例状态中心)**:
    *   一个线程安全的字典，用于存储 `task_id` 到其最新状态的映射。
    *   为后端异步任务和前端API请求提供了一个解耦的、可靠的状态交换中心。

### **前端核心组件**

1.  **`pm_v2_unified_init.js` (初始化脚本)**:
    *   从URL中提取 `task_id`。
    *   调用 `initializeApp`，并将 `task_id` 传递给 `AppManager`。
    *   遵循 `AppManager` 的设计，由 `AppManager` 内部创建和管理 `DataManager`。
2.  **`AppManager` (应用管理器)**:
    *   在 `init()` 流程中，根据传入的 `task_id` (可以为null) 初始化 `DataManager`。
3.  **`DataManager` (数据管理器)**:
    *   其核心职责是**HTTP轮询**。
    *   如果构造时获得了有效的 `task_id`，则启动一个定时器，周期性地调用 `/api/pm_v2/status/{task_id}`。
    *   获取到新状态后，通过发布/订阅模式通知所有相关的UI组件进行更新。
4.  **UI交互 (动态弹窗)**:
    *   用户创建工作区的交互由一个**动态生成的弹窗**完成。
    *   该弹窗通过**自定义事件** (`confirmDirectory`) 与上层逻辑通信，实现了UI与业务逻辑的解耦。
    *   原 `HumanInputComponent` 中的静态输入框和按钮已被移除。

---

## 5. 结论

本框架成功地搭建了一套基于HTTP轮询的、健壮的、可复用的项目准入审查服务。它通过清晰的分层、单向的数据流和解耦的组件设计，为V4.2主治理引擎提供了一个可靠的质量保证前置步骤，并为用户提供了流畅、实时的交互体验。设计文档与最终实现已完全对齐。
