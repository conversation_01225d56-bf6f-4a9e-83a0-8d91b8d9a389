#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ValidationDrivenExecutor - 验证驱动执行器

版本: V2.0-Zen-Integrated
创建日期: 2025-01-14
修订日期: 2025-01-14
作者: AI Architecture Team
描述: 基于完备信息封装的验证驱动执行器，实现AI认知完整性驱动的高质量执行
核心理念: 完备信息 → 正确思考 → 优质输出 → 验证优化循环
"""

import asyncio
import json
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# ==================== PyCRUD操作枚举系统 ====================

class PyCRUDOperation(Enum):
    """预定义的PyCRUD操作枚举，避免协议脱钩"""

    # 文件操作
    FILE_CREATE = "file_manager.create_file(path: str, content: str) -> bool"
    FILE_READ = "file_manager.read_file(path: str) -> str"
    FILE_UPDATE = "file_manager.update_file(path: str, content: str) -> bool"
    FILE_DELETE = "file_manager.delete_file(path: str) -> bool"
    FILE_LIST = "file_manager.list_files(directory: str) -> List[str]"

    # 代码操作
    CODE_GENERATE_CLASS = "code_generator.generate_class(class_name: str, methods: List[str]) -> str"
    CODE_ANALYZE_QUALITY = "code_analyzer.analyze_quality(code: str) -> QualityReport"
    CODE_FORMAT = "code_formatter.format_code(code: str, style: str) -> str"
    CODE_VALIDATE_SYNTAX = "code_validator.validate_syntax(code: str) -> ValidationResult"

    # 文档操作
    DOC_GENERATE = "doc_generator.generate_doc(template: str, data: Dict) -> str"
    DOC_UPDATE_SECTION = "doc_updater.update_section(doc: str, section: str, content: str) -> str"
    DOC_VALIDATE_FORMAT = "doc_validator.validate_format(doc: str, format: str) -> bool"

    # 数据操作
    DATA_PROCESS_JSON = "data_processor.process_json(data: Dict, schema: Dict) -> Dict"
    DATA_VALIDATE = "data_validator.validate_data(data: Any, constraints: Dict) -> bool"
    DATA_TRANSFORM = "data_transformer.transform(data: Any, rules: List[str]) -> Any"


class PyCRUDOperationGroups:
    """预定义的操作组合，供调用者快速选择"""

    FILE_OPERATIONS = [
        PyCRUDOperation.FILE_CREATE,
        PyCRUDOperation.FILE_READ,
        PyCRUDOperation.FILE_UPDATE,
        PyCRUDOperation.FILE_DELETE,
        PyCRUDOperation.FILE_LIST
    ]

    CODE_OPERATIONS = [
        PyCRUDOperation.CODE_GENERATE_CLASS,
        PyCRUDOperation.CODE_ANALYZE_QUALITY,
        PyCRUDOperation.CODE_FORMAT,
        PyCRUDOperation.CODE_VALIDATE_SYNTAX
    ]

    DOC_OPERATIONS = [
        PyCRUDOperation.DOC_GENERATE,
        PyCRUDOperation.DOC_UPDATE_SECTION,
        PyCRUDOperation.DOC_VALIDATE_FORMAT
    ]

    # 常用组合
    CODE_GENERATION_COMBO = [
        PyCRUDOperation.CODE_GENERATE_CLASS,
        PyCRUDOperation.FILE_CREATE,
        PyCRUDOperation.CODE_VALIDATE_SYNTAX
    ]

    DOCUMENT_WRITING_COMBO = [
        PyCRUDOperation.DOC_GENERATE,
        PyCRUDOperation.FILE_CREATE,
        PyCRUDOperation.DOC_VALIDATE_FORMAT
    ]


# ==================== 核心数据结构 ====================

@dataclass
class ExecutionResult:
    """执行结果"""
    success: bool
    generated_content: str = ""
    execution_result: Any = None
    validation_result: Any = None
    confidence: float = 0.0
    error_message: str = ""
    execution_time: float = 0.0
    ai_manager_status: Dict = None

    @classmethod
    def generate_only(cls, generated_content: str, validation_result: Any = None):
        """只生成模式的成功结果"""
        return cls(
            success=True,
            generated_content=generated_content,
            validation_result=validation_result,
            confidence=validation_result.confidence if validation_result else 0.0
        )

    @classmethod
    def generate_and_execute(cls, execution_result: Any, validation_result: Any = None):
        """生成并执行模式的成功结果"""
        return cls(
            success=True,
            execution_result=execution_result,
            validation_result=validation_result,
            confidence=validation_result.confidence if validation_result else 0.0
        )

    @classmethod
    def validation_failed(cls, validation_result: Any, max_iterations: int):
        """验证失败结果"""
        return cls(
            success=False,
            error_message=f"Validation failed after {max_iterations} iterations",
            validation_result=validation_result,
            confidence=validation_result.confidence if validation_result else 0.0
        )


@dataclass
class ValidationResult:
    """验证结果"""
    confidence: float
    py_results: List[Any]
    ai_results: List[Any]
    issues: List[str]
    passed: bool
    context_error: bool = False  # 标记是否为上下文错误
    error_message: str = ""      # 错误消息（用于上下文错误）


@dataclass
class OptimizationResult:
    """参数优化结果"""
    原始内容: str
    护栏: Dict
    约束: Dict
    上下文: Dict
    优化说明: str
    预期改进: str
    quality_score: float
    convergence_iteration: int = 0
    convergence_status: str = "正常收敛"
    is_forced_convergence: bool = False
    is_emergency_convergence: bool = False


@dataclass
class AnalysisQualityCheck:
    """AI分析质量检查结果"""
    completeness: float = 0.0
    consistency: float = 0.0
    depth: float = 0.0
    hallucination_check: float = 0.0
    is_reliable: bool = False


@dataclass
class StrategyQualityCheck:
    """AI策略质量检查结果"""
    targeting: float = 0.0
    feasibility: float = 0.0
    consistency: float = 0.0
    over_optimization: float = 0.0
    is_reasonable: bool = False


@dataclass
class OptimizationResultCheck:
    """AI优化结果检查"""
    completeness: float = 0.0
    validity: float = 0.0
    optimization_magnitude: float = 0.0
    problem_solving: float = 0.0
    is_valid: bool = False


@dataclass
class AdvancedOptimizationConfig:
    """高级优化配置"""
    cap_methods: Optional[Dict] = None
    model_selection: Optional[Dict] = None
    confidence_layering: Optional[Dict] = None
    reasoning_algorithms: Optional[Dict] = None
    data_anchors: Optional[Dict] = None
    contradiction_detection: Optional[Dict] = None
    thinking_audit: Optional[Dict] = None
    tag_system: Optional[Dict] = None


# ==================== JSON协议管理器 ====================

class JSONProtocolManager:
    """JSON协议封装管理器"""

    @staticmethod
    def 封装完备信息(original_content: str,
                   pycrud_operations: Optional[List[PyCRUDOperation]],
                   guardrails: Dict, constraints: Dict, context: Dict) -> Dict:
        """
        按照ai_json_protocol_specification.md标准封装完备信息

        职责：纯粹的组装器，不添加、修改或验证护栏和约束内容

        Args:
            original_content: 任务目标(包含输出要求)
            pycrud_operations: 预定义的操作枚举列表，None表示只生成不执行
            guardrails: 护栏规则(不能做什么) - 调用者提供，原样传递
            constraints: 约束条件(必须做什么) - 调用者提供，原样传递
            context: 上下文信息(环境背景) - 调用者提供，原样传递
        """
        # 将枚举转换为字符串列表，供AI理解
        operation_commands = None
        if pycrud_operations is not None:
            operation_commands = [op.value for op in pycrud_operations]

        return {
            "protocol_version": "1.0",
            "protocol_type": "ai_validation_driven_execution",
            "operation": {
                "pycrud_commands": operation_commands,   # None或操作命令列表
                "execution_mode": "generate_only" if operation_commands is None else "generate_and_execute",
                "original_content": original_content,    # 任务目标(包含输出要求)
            },
            "context": context,                          # 环境背景信息 (原样传递)
            "constraints": constraints,                  # 必须做什么 (原样传递)
            "guardrails": guardrails,                   # 不能做什么 (原样传递)
            "validation": {
                "confidence_calculation": {"method": "weighted_average"}
            }
        }


# ==================== 统一配置管理器接口 ====================

class ValidationDrivenExecutorConfig:
    """ValidationDrivenExecutor配置管理"""

    @staticmethod
    def get_role_config(executor_role: str) -> Dict:
        """从common_config.json读取角色配置"""
        try:
            # 导入统一配置管理器
            from tools.ace.src.unified_config_manager import UnifiedConfigManager

            # 初始化统一配置管理器
            UnifiedConfigManager.initialize()

            # 读取executor_roles配置
            role_config = UnifiedConfigManager.get_config(
                f"executor_roles.{executor_role}",
                default={}
            )

            # 如果没有配置，使用默认配置
            if not role_config:
                role_config = ValidationDrivenExecutorConfig._get_default_role_config(executor_role)

            return role_config
        except Exception as e:
            logger.warning(f"Failed to load role config for {executor_role}: {e}")
            return ValidationDrivenExecutorConfig._get_default_role_config(executor_role)

    @staticmethod
    def _get_default_role_config(executor_role: str) -> Dict:
        """获取默认角色配置"""
        default_configs = {
            "code_generator": {
                "task_category": "code_generation",
                "model_preferences": ["qwen_3_235b_a22b", "deepseek_v3_0324", "deepseek_r1_0528"],
                "default_confidence_threshold": 0.85,
                "priority_weight": 0.8,
                "default_constraints": {
                    "max_lines": 200,
                    "compilation_required": True,
                    "code_style": "google_java_style"
                },
                "validation_protocols": ["code_quality", "architectural_thinking"]
            },
            "document_writer": {
                "task_category": "documentation",
                "model_preferences": ["deepseek_r1_0528", "gemini_2_5_pro", "qwen_3_235b_a22b"],
                "default_confidence_threshold": 0.80,
                "priority_weight": 0.7,
                "default_constraints": {
                    "max_sections": 10,
                    "format": "markdown",
                    "documentation_required": True
                },
                "validation_protocols": ["content_quality", "structure_validation"]
            },
            "architecture_designer": {
                "task_category": "architecture_design",
                "model_preferences": ["deepseek_r1_0528", "gemini_2_5_pro", "deepseek_v3_0324"],
                "default_confidence_threshold": 0.90,
                "priority_weight": 0.9,
                "default_constraints": {
                    "complexity_level": "high",
                    "design_patterns_required": True,
                    "scalability_consideration": True
                },
                "validation_protocols": ["architectural_thinking", "expert_review"]
            }
        }
        return default_configs.get(executor_role, {
            "task_category": "general",
            "model_preferences": ["gemini_2_5_pro"],
            "default_confidence_threshold": 0.85,
            "priority_weight": 0.5,
            "default_constraints": {},
            "validation_protocols": ["basic_validation"]
        })


# ==================== 主要的ValidationDrivenExecutor类 ====================

class ValidationDrivenExecutor:
    """基于完备信息封装的验证驱动执行器"""

    def __init__(self, task_id: str, executor_role: str, task_context: Dict,
                 validation_ai_config: Optional[Dict] = None):
        """
        初始化ValidationDrivenExecutor

        Args:
            task_id: 任务ID
            executor_role: 执行器角色
            task_context: 任务上下文
            validation_ai_config: 验证AI配置 (可选)
        """
        self.task_id = task_id
        self.executor_role = executor_role
        self.task_context = task_context
        self.validation_ai_config = validation_ai_config

        # 使用全局单例AI服务管理器 (不需要注入)
        try:
            from tools.ace.src.api_management.core.task_based_ai_service_manager import get_simplified_ai_service_manager
            self.ai_service_manager = get_simplified_ai_service_manager()
        except ImportError as e:
            logger.error(f"Failed to import AI service manager: {e}")
            self.ai_service_manager = None

        # 核心组件
        self.json_protocol_manager = JSONProtocolManager()
        self.validation_loop = ValidationLoop(self.ai_service_manager, validation_ai_config)
        self.pycrud_executor = PyCRUDExecutor()

        # 任务级状态
        self.execution_history = []
        self.current_confidence = 0.0

        logger.info(f"ValidationDrivenExecutor initialized: {task_id}, role: {executor_role}, validation_ai: {validation_ai_config.get('model') if validation_ai_config else 'None'}")

    async def execute_with_validation(self,
                                    original_content: str,
                                    pycrud_operations: Optional[List[PyCRUDOperation]], # 📋 操作命令列表 (None=只生成不执行)
                                    guardrails: Dict,                           # 🔍 护栏规则(不能做什么)
                                    constraints: Dict,                          # 🎯 约束条件(必须做什么)
                                    context: Dict,                              # 🌍 上下文信息(环境背景)
                                    confidence_threshold: float = 0.85,        # 🎯 质量标准
                                    advanced_optimization: Optional[AdvancedOptimizationConfig] = None) -> ExecutionResult:
        """
        带验证的执行 - 基于完备信息封装的验证驱动执行器

        核心流程：完备封装 → AI执行 → 验证循环 → CRUD执行
        """
        execution_id = f"{self.task_id}_{len(self.execution_history)}"
        start_time = datetime.now()

        logger.info(f"Starting ValidationDrivenExecutor execution {execution_id}")

        try:
            # 第1步：从统一配置管理读取角色配置
            role_config = ValidationDrivenExecutorConfig.get_role_config(self.executor_role)

            # 第2步：JSON协议完备封装 (纯粹组装，不修改调用者内容)
            完整JSON请求 = self.json_protocol_manager.封装完备信息(
                original_content=original_content,      # 📄 任务目标 (调用者提供)
                pycrud_operations=pycrud_operations,    # 📋 操作枚举列表 (调用者提供，None=只生成不执行)
                guardrails=guardrails,                  # 🔍 护栏规则 (调用者提供，原样传递)
                constraints=constraints,                # 🎯 约束条件 (调用者提供，原样传递)
                context=context                         # 🌍 上下文信息 (调用者提供，原样传递)
            )

            # 第3步：通过AI管理器调用AI API (使用真实接口)
            if self.ai_service_manager:
                complete_output = await self.ai_service_manager.call_ai(
                    model_id=None,  # None = AI管理器智能选择最佳模型
                    prompt=json.dumps(完整JSON请求, ensure_ascii=False, indent=2),
                    task_category=role_config.get("task_category", "general"),
                    # AI管理器自动处理：智能选择、质量评估、故障转移、追踪
                )
            else:
                # 模拟AI输出（当AI管理器不可用时）
                complete_output = f"模拟AI输出：{original_content}"
                logger.warning("AI service manager not available, using mock output")

            # 第4步：验证-优化循环
            max_iterations = 3  # 最大优化迭代次数
            for iteration in range(max_iterations):
                validation_result = await self.validation_loop.多维度验证(
                    complete_output, guardrails, constraints, context
                )

                # 检查是否为上下文错误
                if validation_result.context_error:
                    logger.error("❌ 上下文设计有问题，停止执行，返回错误给调用者")
                    context_error_result = ExecutionResult(
                        success=False,
                        error_message=f"上下文设计不完整，需要重新设计。{validation_result.error_message}",
                        validation_result=validation_result,
                        confidence=0.0
                    )
                    return self._record_and_return(execution_id, context_error_result, start_time)

                if validation_result.confidence >= confidence_threshold:
                    # 验证通过，执行或返回结果
                    return await self._execute_after_validation(
                        complete_output, pycrud_operations, validation_result, execution_id, start_time
                    )
                else:
                    # 分析失败原因并优化参数（只有非上下文错误才进行优化）
                    if iteration < max_iterations - 1:  # 不是最后一次迭代
                        logger.info(f"🔄 第{iteration+1}次验证失败，开始参数优化...")
                        logger.info(f"📋 具体问题: {validation_result.issues}")

                        优化结果 = await self.validation_loop.分析并优化参数(
                            validation_result.issues,  # 验证失败的具体问题（精准输入）
                            original_content, pycrud_operations, guardrails, constraints, context
                        )

                        # 递归调用execute_with_validation，使用优化后的参数
                        logger.info(f"🎯 使用优化后的参数进行第{iteration+2}次尝试...")
                        return await self.execute_with_validation(
                            original_content=优化结果.原始内容,      # 可能优化的原始内容
                            pycrud_operations=pycrud_operations,    # 保持不变！遵从调用者意图
                            guardrails=优化结果.护栏,               # 优化后的护栏
                            constraints=优化结果.约束,              # 优化后的约束
                            context=优化结果.上下文,                # 优化后的上下文
                            confidence_threshold=confidence_threshold,
                            advanced_optimization=advanced_optimization
                        )

            # 达到最大迭代次数仍未通过验证
            final_result = ExecutionResult.validation_failed(validation_result, max_iterations)
            return self._record_and_return(execution_id, final_result, start_time)

        except Exception as e:
            logger.error(f"Execution {execution_id} error: {str(e)}")
            error_result = ExecutionResult(success=False, error_message=str(e))
            return self._record_and_return(execution_id, error_result, start_time)

    async def _execute_after_validation(self, complete_output: str, pycrud_operations: Optional[List[PyCRUDOperation]],
                                      validation_result: ValidationResult, execution_id: str, start_time: datetime) -> ExecutionResult:
        """验证通过后执行PyCRUD操作或返回结果"""
        try:
            if pycrud_operations is None:
                # 只生成模式：直接返回AI生成的内容
                final_result = ExecutionResult.generate_only(complete_output, validation_result)
                logger.info(f"Execution {execution_id} completed (generate only) with confidence {validation_result.confidence:.3f}")
            else:
                # 生成并执行模式：执行pycrud操作命令
                execution_result = await self.pycrud_executor.执行(complete_output, pycrud_operations)
                final_result = ExecutionResult.generate_and_execute(execution_result, validation_result)
                logger.info(f"Execution {execution_id} completed (generate and execute) with confidence {validation_result.confidence:.3f}")

            return self._record_and_return(execution_id, final_result, start_time)

        except Exception as e:
            logger.error(f"Post-validation execution {execution_id} error: {str(e)}")
            error_result = ExecutionResult(success=False, error_message=str(e))
            return self._record_and_return(execution_id, error_result, start_time)

    def _record_and_return(self, execution_id: str, result: ExecutionResult, start_time: datetime) -> ExecutionResult:
        """记录执行历史并返回结果"""

        execution_time = (datetime.now() - start_time).total_seconds()
        result.execution_time = execution_time

        # 获取AI管理器状态
        if self.ai_service_manager:
            try:
                result.ai_manager_status = self.ai_service_manager.get_system_status()
            except Exception as e:
                logger.warning(f"Failed to get AI manager status: {e}")
                result.ai_manager_status = {"error": str(e)}

        self.execution_history.append({
            "execution_id": execution_id,
            "timestamp": datetime.now().isoformat(),
            "result": result,
            "confidence": result.confidence,
            "execution_time": execution_time
        })

        # 更新当前置信度
        self.current_confidence = result.confidence

        return result

    def get_task_status(self) -> Dict:
        """获取任务执行状态"""
        return {
            "task_id": self.task_id,
            "executor_role": self.executor_role,
            "execution_count": len(self.execution_history),
            "current_confidence": self.current_confidence,
            "last_execution": self.execution_history[-1] if self.execution_history else None,
            "average_execution_time": sum(h["execution_time"] for h in self.execution_history) / len(self.execution_history) if self.execution_history else 0,
            "ai_manager_available": self.ai_service_manager is not None
        }

    def get_ai_manager_status(self) -> Dict:
        """获取AI管理器状态"""
        if self.ai_service_manager:
            try:
                return self.ai_service_manager.get_system_status()
            except Exception as e:
                return {"error": str(e), "available": False}
        return {"available": False, "reason": "AI service manager not initialized"}

    def get_ai_tracking_stats(self) -> Dict:
        """获取AI调用追踪统计"""
        if self.ai_service_manager:
            try:
                return self.ai_service_manager.get_tracking_statistics()
            except Exception as e:
                return {"error": str(e), "available": False}
        return {"available": False, "reason": "AI service manager not initialized"}


# ==================== 验证循环引擎 ====================

class ValidationLoop:
    """验证-优化循环引擎"""

    def __init__(self, ai_service_manager, validation_ai_config: Optional[Dict] = None):
        self.ai_service_manager = ai_service_manager
        self.validation_ai_config = validation_ai_config
        self.ai_validation_enabled = validation_ai_config is not None

        # Python算法验证器（已集成到_python_algorithm_validation中）
        # 不再需要单独的验证器，统一使用多维度验证

    async def 多维度验证(self, complete_output: str, guardrails: Dict, constraints: Dict, context: Dict) -> ValidationResult:
        """
        多维度验证：基于护栏和约束规模的智能验证策略

        核心策略：
        1. 如果护栏和约束很多很大：分批验证（提高AI注意力）
        2. 如果护栏和约束适中：整体验证
        3. Python算法验证：护栏+约束+内容质量
        4. AI验证：分批或整体检查，记录不通过的地方
        """
        py_results = []
        ai_results = []
        all_failed_checks = []  # 记录所有不通过的检查，用于后续优化

        # 第1步：Python算法验证（快速基础检查）
        logger.info("🔍 执行Python算法验证...")
        py_result = await self._python_algorithm_validation(complete_output, guardrails, constraints, context)
        py_results.append(py_result)

        # 检查是否有上下文错误
        if py_result.get("context_error", False):
            logger.error("❌ 上下文设计有问题，需要调用者重新设计")
            return ValidationResult(
                confidence=0.0,
                py_results=py_results,
                ai_results=[],
                issues=py_result.get("context_issues", []),
                passed=False,
                context_error=True,
                error_message=py_result.get("error_message", "上下文设计不完整")
            )

        if py_result.get("issues"):
            all_failed_checks.extend(py_result["issues"])

        # 第2步：AI验证（仅当配置了验证AI时）
        if self.ai_validation_enabled and self.ai_service_manager:
            logger.info("🤖 执行AI多维度验证...")

            # 评估护栏和约束的复杂度
            guardrail_complexity = self._evaluate_complexity(guardrails)
            constraint_complexity = self._evaluate_complexity(constraints)

            logger.info(f"📊 复杂度评估 - 护栏: {guardrail_complexity}, 约束: {constraint_complexity}")

            # 根据复杂度选择验证策略
            if guardrail_complexity >= 3 or constraint_complexity >= 3:
                # 策略1：分批验证（提高注意力）
                logger.info("📋 采用分批验证策略（护栏和约束复杂度高）")
                ai_batch_results = await self._ai_batch_validation(complete_output, guardrails, constraints, context)
                ai_results.extend(ai_batch_results)
            else:
                # 策略2：整体验证
                logger.info("🎯 采用整体验证策略（护栏和约束复杂度适中）")
                ai_overall_result = await self._ai_overall_validation(complete_output, guardrails, constraints, context)
                ai_results.append(ai_overall_result)

            # 收集AI验证的失败项
            for ai_result in ai_results:
                if ai_result.get("issues"):
                    all_failed_checks.extend(ai_result["issues"])

        # 第3步：计算综合置信度（加权平均）
        py_confidence = py_result.get("confidence", 0.0)
        ai_confidence = self._calculate_ai_confidence(ai_results) if ai_results else 0.0

        # 根据是否有AI验证调整权重
        if self.ai_validation_enabled and ai_results:
            combined_confidence = py_confidence * 0.6 + ai_confidence * 0.4
        else:
            combined_confidence = py_confidence

        logger.info(f"📈 置信度计算 - Python: {py_confidence:.3f}, AI: {ai_confidence:.3f}, 综合: {combined_confidence:.3f}")

        return ValidationResult(
            confidence=combined_confidence,
            py_results=py_results,
            ai_results=ai_results,
            issues=all_failed_checks,  # 所有不通过的检查，用于后续优化
            passed=combined_confidence >= 0.7
        )

    def _evaluate_complexity(self, data: Dict) -> int:
        """
        评估护栏或约束的复杂度

        复杂度评分：
        - 1: 简单（1-2个主要项目）
        - 2: 中等（3-5个主要项目）
        - 3: 复杂（6-10个主要项目）
        - 4: 非常复杂（10+个主要项目）
        """
        if not data:
            return 0

        total_items = 0

        # 计算所有嵌套项目的总数
        def count_items(obj):
            if isinstance(obj, dict):
                count = len(obj)
                for value in obj.values():
                    if isinstance(value, (dict, list)):
                        count += count_items(value)
                return count
            elif isinstance(obj, list):
                return len(obj) + sum(count_items(item) for item in obj if isinstance(item, (dict, list)))
            else:
                return 1

        total_items = count_items(data)

        if total_items <= 2:
            return 1
        elif total_items <= 5:
            return 2
        elif total_items <= 10:
            return 3
        else:
            return 4

    async def _python_algorithm_validation(self, output: str, guardrails: Dict, constraints: Dict, context: Dict) -> Dict:
        """
        Python算法验证：护栏+约束+内容质量+上下文检查

        验证维度：
        1. 护栏合规检查（权重0.4）
        2. 约束满足检查（权重0.35）
        3. 内容质量检查（权重0.25）
        4. 上下文完整性检查（当护栏+约束都不通过时触发）
        """
        issues = []
        context_issues = []  # 专门记录上下文问题

        # 1. 护栏合规检查
        guardrail_score = await self._check_guardrail_compliance(output, guardrails, issues)

        # 2. 约束满足检查
        constraint_score = await self._check_constraint_satisfaction(output, constraints, issues)

        # 3. 内容质量检查
        quality_score = await self._check_content_quality(output, context, issues)

        # 4. 第三层检查：如果护栏+约束都有问题，检查上下文
        if guardrail_score < 0.7 and constraint_score < 0.7:
            logger.info("🔍 护栏和约束都不通过，开始检查上下文问题...")
            context_score = await self._check_context_completeness(output, context, context_issues)

            # 如果上下文有问题，这是调用者的责任，需要重新设计
            if context_issues:
                logger.warning(f"❌ 发现上下文问题，需要调用者重新设计: {context_issues}")
                return {
                    "confidence": 0.0,
                    "issues": issues,
                    "context_issues": context_issues,  # 上下文问题单独记录
                    "validator": "python_algorithm",
                    "context_error": True,  # 标记为上下文错误
                    "error_message": f"上下文设计不完整，需要重新设计。问题：{'; '.join(context_issues)}",
                    "details": {
                        "guardrail_score": guardrail_score,
                        "constraint_score": constraint_score,
                        "quality_score": quality_score,
                        "context_score": context_score
                    }
                }

        # 加权计算综合置信度
        综合置信度 = (guardrail_score * 0.4 + constraint_score * 0.35 + quality_score * 0.25)

        return {
            "confidence": 综合置信度,
            "issues": issues,
            "context_issues": context_issues,
            "validator": "python_algorithm",
            "context_error": False,
            "details": {
                "guardrail_score": guardrail_score,
                "constraint_score": constraint_score,
                "quality_score": quality_score
            }
        }

    async def _check_guardrail_compliance(self, output: str, guardrails: Dict, issues: List[str]) -> float:
        """检查护栏合规性"""
        if not guardrails:
            return 1.0

        violations = 0
        total_checks = 0

        # 检查禁止项
        if "禁止项" in guardrails:
            禁止项 = guardrails["禁止项"]

            # 检查不能使用的项目
            if "不能使用" in 禁止项:
                for forbidden_item in 禁止项["不能使用"]:
                    total_checks += 1
                    if forbidden_item.lower() in output.lower():
                        violations += 1
                        issues.append(f"护栏违规：使用了禁止项 '{forbidden_item}'")

            # 检查不能访问的项目
            if "不能访问" in 禁止项:
                for forbidden_access in 禁止项["不能访问"]:
                    total_checks += 1
                    if forbidden_access.lower() in output.lower():
                        violations += 1
                        issues.append(f"护栏违规：访问了禁止资源 '{forbidden_access}'")

        # 检查安全边界
        if "安全边界" in guardrails:
            安全边界 = guardrails["安全边界"]
            for boundary_key, boundary_desc in 安全边界.items():
                total_checks += 1
                # 简单的关键词检查（实际应用中可以更复杂）
                if "恶意" in output.lower() or "删除" in output.lower() or "格式化" in output.lower():
                    violations += 1
                    issues.append(f"护栏违规：违反安全边界 '{boundary_key}': {boundary_desc}")

        # 计算合规分数
        if total_checks == 0:
            return 1.0

        compliance_score = max(0.0, 1.0 - (violations / total_checks))
        return compliance_score

    async def _check_constraint_satisfaction(self, output: str, constraints: Dict, issues: List[str]) -> float:
        """检查约束满足度"""
        if not constraints:
            return 1.0

        satisfied = 0
        total_checks = 0

        # 检查依赖条件
        if "依赖条件" in constraints:
            依赖条件 = constraints["依赖条件"]

            # 检查必须集成的项目
            if "必须集成" in 依赖条件:
                for required_item in 依赖条件["必须集成"]:
                    total_checks += 1
                    if required_item.lower() in output.lower():
                        satisfied += 1
                    else:
                        issues.append(f"约束未满足：缺少必需集成 '{required_item}'")

            # 检查必须兼容的项目
            if "必须兼容" in 依赖条件:
                for compatible_item in 依赖条件["必须兼容"]:
                    total_checks += 1
                    if compatible_item.lower() in output.lower():
                        satisfied += 1
                    else:
                        issues.append(f"约束未满足：缺少兼容性说明 '{compatible_item}'")

        # 检查架构要求
        if "架构要求" in constraints:
            架构要求 = constraints["架构要求"]

            if "必须采用" in 架构要求:
                required_arch = 架构要求["必须采用"]
                total_checks += 1
                if required_arch.lower() in output.lower():
                    satisfied += 1
                else:
                    issues.append(f"约束未满足：未采用必需架构 '{required_arch}'")

            if "必须支持" in 架构要求:
                for support_item in 架构要求["必须支持"]:
                    total_checks += 1
                    if support_item.lower() in output.lower():
                        satisfied += 1
                    else:
                        issues.append(f"约束未满足：缺少必需支持 '{support_item}'")

        # 计算满足分数
        if total_checks == 0:
            return 1.0

        satisfaction_score = satisfied / total_checks
        return satisfaction_score

    async def _check_content_quality(self, output: str, context: Dict, issues: List[str]) -> float:
        """检查内容质量"""
        quality_score = 0.8  # 基础分数

        # 检查内容长度
        if len(output.strip()) < 10:
            issues.append("内容质量问题：输出内容过短")
            quality_score -= 0.3

        # 检查是否包含基本结构
        if context.get("项目背景") and "项目" not in output.lower():
            issues.append("内容质量问题：未体现项目背景")
            quality_score -= 0.1

        # 检查是否有明显的错误标识
        error_indicators = ["错误", "失败", "无法", "不能", "异常"]
        for indicator in error_indicators:
            if indicator in output:
                issues.append(f"内容质量问题：包含错误标识 '{indicator}'")
                quality_score -= 0.1
                break

        return max(0.0, quality_score)

    async def _check_context_completeness(self, output: str, context: Dict, context_issues: List[str]) -> float:
        """
        检查上下文完整性和清晰度 - 1对1精确映射问题

        当护栏+约束都不通过时，问题可能出在上下文设计上。

        核心原则：
        1. 只列出有问题的字段，没问题的不说
        2. 每个问题字段都要1对1说明具体问题
        3. 调用者可以根据问题列表精准改进
        """
        context_score = 1.0
        field_problems = {}  # 记录每个字段的具体问题

        # 定义标准上下文字段和检查规则
        context_field_rules = {
            "项目背景": {
                "required": True,
                "min_length": 20,
                "description": "项目的详细背景描述"
            },
            "业务场景": {
                "required": True,
                "type": dict,
                "required_subfields": ["峰值流量", "用户规模"],
                "description": "具体的业务场景和性能指标"
            },
            "技术栈": {
                "required": True,
                "version_required": True,
                "description": "技术栈及具体版本信息"
            },
            "当前环境": {
                "required": True,
                "type": dict,
                "required_subfields": ["部署方式", "服务器配置", "网络环境"],
                "description": "当前部署和运行环境"
            },
            "目标受众": {
                "required": False,
                "min_length": 10,
                "description": "目标用户群体描述"
            },
            "质量要求": {
                "required": False,
                "type": dict,
                "description": "质量标准和要求"
            },
            "时间约束": {
                "required": False,
                "description": "时间限制和里程碑"
            },
            "资源限制": {
                "required": False,
                "description": "资源约束条件"
            }
        }

        # 1对1检查每个字段
        for field_name, rules in context_field_rules.items():
            field_issues = []

            # 检查字段是否存在
            if field_name not in context:
                if rules.get("required", False):
                    field_issues.append(f"缺少必需字段")
                continue  # 字段不存在就跳过后续检查

            field_value = context[field_name]

            # 检查字段值是否为空
            if not field_value:
                field_issues.append("字段值为空")
                field_problems[field_name] = field_issues
                continue

            # 检查字符串类型字段
            if isinstance(field_value, str):
                # 检查最小长度
                min_length = rules.get("min_length", 5)
                if len(field_value.strip()) < min_length:
                    field_issues.append(f"内容过于简单（少于{min_length}个字符）")

                # 检查模糊词汇
                vague_words = ["可能", "大概", "应该", "或许", "待定", "未确定", "暂时", "临时"]
                found_vague = [word for word in vague_words if word in field_value]
                if found_vague:
                    field_issues.append(f"包含模糊表述: {', '.join(found_vague)}")

                # 检查版本信息（针对技术栈）
                if rules.get("version_required", False):
                    if "版本" not in field_value and "v" not in field_value.lower() and not any(char.isdigit() for char in field_value):
                        field_issues.append("缺少具体版本号信息")

            # 检查字典类型字段
            elif isinstance(field_value, dict):
                if rules.get("type") == dict:
                    # 检查必需的子字段
                    required_subfields = rules.get("required_subfields", [])
                    missing_subfields = [sub for sub in required_subfields if sub not in field_value]
                    if missing_subfields:
                        field_issues.append(f"缺少必需子字段: {', '.join(missing_subfields)}")

                    # 检查子字段值是否为空
                    empty_subfields = [sub for sub, val in field_value.items() if not val]
                    if empty_subfields:
                        field_issues.append(f"子字段值为空: {', '.join(empty_subfields)}")

            # 检查类型不匹配
            elif rules.get("type") and not isinstance(field_value, rules["type"]):
                expected_type = rules["type"].__name__
                actual_type = type(field_value).__name__
                field_issues.append(f"类型错误：期望{expected_type}，实际{actual_type}")

            # 如果有问题，记录到field_problems中
            if field_issues:
                field_problems[field_name] = field_issues

        # 生成1对1的问题描述
        for field_name, issues in field_problems.items():
            for issue in issues:
                context_issues.append(f"上下文字段 '{field_name}': {issue}")
                context_score -= 0.1

        return max(0.0, context_score)

    async def _ai_batch_validation(self, output: str, guardrails: Dict, constraints: Dict, context: Dict) -> List[Dict]:
        """
        AI分批验证策略：护栏和约束复杂度高时使用

        策略：
        1. 如果护栏和约束很多很大：分批验证（提高AI注意力）
        2. 分别检查护栏和约束，每次只关注少量项目
        """
        results = []

        # 分批检查护栏
        if guardrails:
            logger.info("🔍 分批检查护栏...")
            guardrail_batches = self._split_into_batches(guardrails, max_batch_size=3)

            for i, batch in enumerate(guardrail_batches):
                logger.info(f"📋 检查护栏批次 {i+1}/{len(guardrail_batches)}")
                result = await self._ai_check_guardrails_batch(output, batch, context)
                results.append(result)

        # 分批检查约束
        if constraints:
            logger.info("🎯 分批检查约束...")
            constraint_batches = self._split_into_batches(constraints, max_batch_size=3)

            for i, batch in enumerate(constraint_batches):
                logger.info(f"📋 检查约束批次 {i+1}/{len(constraint_batches)}")
                result = await self._ai_check_constraints_batch(output, batch, context)
                results.append(result)

        return results

    async def _ai_overall_validation(self, output: str, guardrails: Dict, constraints: Dict, context: Dict) -> Dict:
        """
        AI整体验证策略：护栏和约束复杂度适中时使用

        策略：
        1. 一次性检查所有护栏和约束
        2. 适用于复杂度不高的情况
        """
        logger.info("🎯 执行AI整体验证...")

        validation_prompt = f"""
请对以下AI输出进行全面验证：

输出内容：
{output}

护栏规则（不能做什么）：
{json.dumps(guardrails, ensure_ascii=False, indent=2)}

约束条件（必须做什么）：
{json.dumps(constraints, ensure_ascii=False, indent=2)}

上下文信息：
{json.dumps(context, ensure_ascii=False, indent=2)}

请检查：
1. 是否违反了任何护栏规则
2. 是否满足了所有约束条件
3. 输出质量如何

请以JSON格式返回：
{{
    "confidence": 0.85,
    "issues": ["具体问题1", "具体问题2"],
    "guardrail_violations": ["护栏违规1", "护栏违规2"],
    "constraint_failures": ["约束未满足1", "约束未满足2"],
    "assessment": "详细评估说明"
}}
"""

        try:
            validation_result = await self.ai_service_manager.call_ai(
                model_id=self.validation_ai_config.get("model"),
                prompt=validation_prompt,
                task_category="validation"
            )

            # 尝试解析AI返回的JSON
            try:
                result_data = json.loads(validation_result)
                return {
                    "confidence": result_data.get("confidence", 0.8),
                    "issues": result_data.get("issues", []),
                    "validator": "ai_overall_validation",
                    "details": {
                        "guardrail_violations": result_data.get("guardrail_violations", []),
                        "constraint_failures": result_data.get("constraint_failures", []),
                        "assessment": result_data.get("assessment", "")
                    }
                }
            except json.JSONDecodeError:
                # 如果不是JSON格式，解析文本内容
                issues = self._parse_validation_text(validation_result)
                return {
                    "confidence": 0.7,
                    "issues": issues,
                    "validator": "ai_overall_validation",
                    "raw_output": validation_result
                }

        except Exception as e:
            logger.error(f"AI整体验证失败: {e}")
            return {
                "confidence": 0.0,
                "issues": [f"AI验证异常: {str(e)}"],
                "validator": "ai_overall_validation"
            }

    def _split_into_batches(self, data: Dict, max_batch_size: int = 3) -> List[Dict]:
        """将复杂的护栏或约束分割成小批次"""
        batches = []
        current_batch = {}
        current_size = 0

        for key, value in data.items():
            current_batch[key] = value
            current_size += 1

            if current_size >= max_batch_size:
                batches.append(current_batch)
                current_batch = {}
                current_size = 0

        # 添加最后一个批次
        if current_batch:
            batches.append(current_batch)

        return batches if batches else [data]

    def _calculate_ai_confidence(self, ai_results: List[Dict]) -> float:
        """计算AI验证的综合置信度"""
        if not ai_results:
            return 0.0

        confidences = [result.get("confidence", 0.0) for result in ai_results]
        return sum(confidences) / len(confidences)

    def _parse_validation_text(self, text: str) -> List[str]:
        """解析AI验证文本，提取问题列表"""
        issues = []
        lines = text.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['问题', '错误', '违反', '缺少', '未满足']):
                issues.append(line)

        return issues if issues else ["AI验证文本解析失败"]

    async def _ai_check_guardrails_batch(self, output: str, guardrail_batch: Dict, context: Dict) -> Dict:
        """AI检查护栏批次"""
        validation_prompt = f"""
请检查以下AI输出是否违反了护栏规则：

输出内容：
{output}

护栏规则（不能做什么）：
{json.dumps(guardrail_batch, ensure_ascii=False, indent=2)}

上下文信息：
{json.dumps(context, ensure_ascii=False, indent=2)}

请仔细检查输出是否违反了任何护栏规则。

请以JSON格式返回：
{{
    "confidence": 0.9,
    "issues": ["具体违规问题"],
    "violations": ["详细违规说明"]
}}
"""

        try:
            result = await self.ai_service_manager.call_ai(
                model_id=self.validation_ai_config.get("model"),
                prompt=validation_prompt,
                task_category="validation"
            )

            try:
                result_data = json.loads(result)
                return {
                    "confidence": result_data.get("confidence", 0.8),
                    "issues": result_data.get("issues", []),
                    "validator": "ai_guardrail_batch",
                    "batch_data": guardrail_batch
                }
            except json.JSONDecodeError:
                issues = self._parse_validation_text(result)
                return {
                    "confidence": 0.7,
                    "issues": issues,
                    "validator": "ai_guardrail_batch",
                    "batch_data": guardrail_batch
                }

        except Exception as e:
            logger.error(f"AI护栏批次验证失败: {e}")
            return {
                "confidence": 0.0,
                "issues": [f"AI护栏验证异常: {str(e)}"],
                "validator": "ai_guardrail_batch"
            }

    async def _ai_check_constraints_batch(self, output: str, constraint_batch: Dict, context: Dict) -> Dict:
        """AI检查约束批次"""
        validation_prompt = f"""
请检查以下AI输出是否满足了约束条件：

输出内容：
{output}

约束条件（必须做什么）：
{json.dumps(constraint_batch, ensure_ascii=False, indent=2)}

上下文信息：
{json.dumps(context, ensure_ascii=False, indent=2)}

请仔细检查输出是否满足了所有约束条件。

请以JSON格式返回：
{{
    "confidence": 0.9,
    "issues": ["未满足的约束"],
    "failures": ["详细说明"]
}}
"""

        try:
            result = await self.ai_service_manager.call_ai(
                model_id=self.validation_ai_config.get("model"),
                prompt=validation_prompt,
                task_category="validation"
            )

            try:
                result_data = json.loads(result)
                return {
                    "confidence": result_data.get("confidence", 0.8),
                    "issues": result_data.get("issues", []),
                    "validator": "ai_constraint_batch",
                    "batch_data": constraint_batch
                }
            except json.JSONDecodeError:
                issues = self._parse_validation_text(result)
                return {
                    "confidence": 0.7,
                    "issues": issues,
                    "validator": "ai_constraint_batch",
                    "batch_data": constraint_batch
                }

        except Exception as e:
            logger.error(f"AI约束批次验证失败: {e}")
            return {
                "confidence": 0.0,
                "issues": [f"AI约束验证异常: {str(e)}"],
                "validator": "ai_constraint_batch"
            }

    async def 分析并优化参数(self, validation_issues: List[str], 原始内容: str, crud操作列表: Optional[List[PyCRUDOperation]],
                         护栏: Dict, 约束: Dict, 上下文: Dict) -> OptimizationResult:
        """
        基于收敛控制的AI参数优化算法

        核心理念：
        - 算法控制验证AI的收敛，确保在有限步骤内得到足够好的结果
        - 通过质量递进、迭代改进、强制收敛三重机制保证算法收敛性
        - 目标是"足够好"而非"完美"，重点是稳定收敛而非极致优化
        """

        # 收敛控制参数
        max_convergence_iterations = 3      # 最大收敛迭代次数
        min_improvement_threshold = 0.05    # 最小改进阈值（5%）
        convergence_target = 0.85           # 收敛目标质量（85%）
        force_convergence_threshold = 0.75  # 强制收敛最低质量（75%）

        current_quality = 0.0  # 当前质量分数
        best_result = None     # 最佳结果缓存

        # 收敛迭代循环
        for iteration in range(max_convergence_iterations):
            logger.info(f"🔄 参数优化收敛迭代 {iteration + 1}/{max_convergence_iterations}")

            try:
                # 第1步：AI分析 + Python质量评估
                问题分析结果, 分析质量分数 = await self._ai_analyze_with_convergence_scoring(
                    validation_issues, 原始内容, 护栏, 约束, 上下文, iteration
                )

                # 第2步：收敛性检查
                if iteration > 0:
                    improvement = 分析质量分数 - current_quality
                    logger.info(f"📊 质量改进: {current_quality:.3f} → {分析质量分数:.3f} (改进: {improvement:.3f})")

                    if improvement < min_improvement_threshold:
                        logger.warning(f"⚠️  改进不足({improvement:.3f} < {min_improvement_threshold})，触发强制收敛")
                        return self._force_convergence_optimization(
                            best_result, validation_issues, 护栏, 约束, 上下文
                        )

                current_quality = 分析质量分数

                # 第3步：质量达标检查
                if 分析质量分数 >= convergence_target:
                    logger.info(f"✅ 达到收敛目标({分析质量分数:.3f} >= {convergence_target})")
                    break

                # 第4步：继续优化流程
                优化策略, 策略质量分数 = await self._ai_strategy_with_convergence_scoring(
                    问题分析结果, 护栏, 约束, 上下文, iteration
                )

                优化后参数, 结果质量分数 = await self._ai_optimization_with_convergence_scoring(
                    优化策略, 原始内容, 护栏, 约束, 上下文, iteration
                )

                # 计算综合质量分数
                综合质量分数 = (分析质量分数 * 0.4 + 策略质量分数 * 0.3 + 结果质量分数 * 0.3)

                # 更新最佳结果
                if best_result is None or 综合质量分数 > best_result.quality_score:
                    best_result = OptimizationResult(
                        原始内容=优化后参数.get("优化后原始内容", 原始内容),
                        护栏=优化后参数["优化后护栏"],
                        约束=优化后参数["优化后约束"],
                        上下文=优化后参数["优化后上下文"],
                        优化说明=f"第{iteration+1}次迭代优化结果",
                        预期改进=优化后参数.get("预期改进", ""),
                        quality_score=综合质量分数,
                        convergence_iteration=iteration + 1
                    )

            except Exception as e:
                logger.error(f"❌ 第{iteration+1}次迭代出现异常: {e}")
                if best_result is not None:
                    return best_result
                else:
                    return self._emergency_convergence(validation_issues, 护栏, 约束, 上下文)

        # 最终结果检查
        if best_result is None:
            return self._emergency_convergence(validation_issues, 护栏, 约束, 上下文)

        if best_result.quality_score < force_convergence_threshold:
            logger.warning(f"⚠️  最终质量不足({best_result.quality_score:.3f} < {force_convergence_threshold})，强制收敛")
            return self._force_convergence_optimization(
                best_result, validation_issues, 护栏, 约束, 上下文
            )

        logger.info(f"🎯 收敛成功！最终质量: {best_result.quality_score:.3f}")
        return best_result

    # 旧的AI验证方法（已被新的分批验证替代）
    async def _ai_validation_legacy(self, output: str, guardrails: Dict, constraints: Dict, context: Dict) -> Dict:
        """AI验证"""
        if not self.ai_validation_enabled or not self.ai_service_manager:
            return {"confidence": 0.0, "issues": ["AI validation disabled"], "validator": "ai_validation"}

        try:
            validation_prompt = f"""
请对以下AI输出进行质量验证：

输出内容：
{output}

护栏规则：
{json.dumps(guardrails, ensure_ascii=False, indent=2)}

约束条件：
{json.dumps(constraints, ensure_ascii=False, indent=2)}

上下文信息：
{json.dumps(context, ensure_ascii=False, indent=2)}

请评估：
1. 是否违反了护栏规则
2. 是否满足了约束条件
3. 输出质量如何
4. 给出0-1之间的置信度分数

请以JSON格式返回：
{{
    "confidence": 0.85,
    "issues": ["问题1", "问题2"],
    "assessment": "详细评估"
}}
"""

            validation_result = await self.ai_service_manager.call_ai(
                model_id=self.validation_ai_config.get("model"),
                prompt=validation_prompt,
                task_category="validation"
            )

            # 尝试解析AI返回的JSON
            try:
                result_data = json.loads(validation_result)
                return {
                    "confidence": result_data.get("confidence", 0.8),
                    "issues": result_data.get("issues", []),
                    "validator": "ai_validation",
                    "assessment": result_data.get("assessment", "")
                }
            except json.JSONDecodeError:
                # 如果不是JSON格式，使用默认值
                return {"confidence": 0.8, "issues": [], "validator": "ai_validation", "raw_output": validation_result}

        except Exception as e:
            logger.error(f"AI validation failed: {e}")
            return {"confidence": 0.0, "issues": [str(e)], "validator": "ai_validation"}

    async def _ai_analyze_with_convergence_scoring(self, validation_issues: List[str], 原始内容: str,
                                                 护栏: Dict, 约束: Dict, 上下文: Dict, iteration: int) -> tuple:
        """AI分析 + Python质量评估"""
        # 模拟AI分析
        分析质量分数 = 0.7 + (iteration * 0.05)  # 模拟迭代改进
        问题分析结果 = f"第{iteration+1}次迭代分析：{validation_issues}"

        return 问题分析结果, min(1.0, 分析质量分数)

    async def _ai_strategy_with_convergence_scoring(self, 问题分析结果: str, 护栏: Dict, 约束: Dict, 上下文: Dict, iteration: int) -> tuple:
        """AI策略设计 + Python质量评估"""
        # 模拟AI策略设计
        策略质量分数 = 0.75 + (iteration * 0.03)  # 模拟迭代改进
        优化策略 = f"第{iteration+1}次迭代策略：基于{问题分析结果}的优化方案"

        return 优化策略, min(1.0, 策略质量分数)

    async def _ai_optimization_with_convergence_scoring(self, 优化策略: str, 原始内容: str, 护栏: Dict, 约束: Dict, 上下文: Dict, iteration: int) -> tuple:
        """AI参数优化 + Python质量评估"""
        # 模拟AI参数优化
        结果质量分数 = 0.8 + (iteration * 0.02)  # 模拟迭代改进

        优化后参数 = {
            "优化后护栏": 护栏.copy(),
            "优化后约束": 约束.copy(),
            "优化后上下文": 上下文.copy(),
            "优化后原始内容": 原始内容,
            "预期改进": f"第{iteration+1}次迭代优化，预期提升质量"
        }

        return 优化后参数, min(1.0, 结果质量分数)

    def _force_convergence_optimization(self, best_result: OptimizationResult, validation_issues: List[str],
                                      护栏: Dict, 约束: Dict, 上下文: Dict) -> OptimizationResult:
        """强制收敛机制"""
        logger.info("🛡️  启动强制收敛机制")

        return OptimizationResult(
            原始内容=best_result.原始内容 if best_result else "",
            护栏=护栏,
            约束=约束,
            上下文=上下文,
            优化说明="强制收敛：使用保守优化策略确保系统稳定",
            预期改进="保守改进，重点保证收敛性和稳定性",
            quality_score=0.75,
            convergence_status="强制收敛",
            is_forced_convergence=True
        )

    def _emergency_convergence(self, validation_issues: List[str], 护栏: Dict, 约束: Dict, 上下文: Dict) -> OptimizationResult:
        """紧急收敛机制"""
        logger.info("🚨 启动紧急收敛机制")

        return OptimizationResult(
            原始内容="",
            护栏=护栏,
            约束=约束,
            上下文=上下文,
            优化说明="紧急收敛：保持原始参数，确保系统不崩溃",
            预期改进="无改进，但保证系统稳定运行",
            quality_score=0.60,
            convergence_status="紧急收敛",
            is_emergency_convergence=True
        )


# ==================== PyCRUD执行器 ====================

class PyCRUDExecutor:
    """Python CRUD操作执行器"""

    async def 执行(self, complete_output: str, pycrud_operations: List[PyCRUDOperation]) -> Dict:
        """执行PyCRUD操作命令"""
        results = []

        for operation in pycrud_operations:
            try:
                # 模拟执行CRUD操作
                if operation == PyCRUDOperation.FILE_CREATE:
                    result = f"Created file based on: {complete_output[:100]}..."
                elif operation == PyCRUDOperation.CODE_GENERATE_CLASS:
                    result = f"Generated class from: {complete_output[:100]}..."
                elif operation == PyCRUDOperation.DOC_GENERATE:
                    result = f"Generated documentation from: {complete_output[:100]}..."
                else:
                    result = f"Executed {operation.value}"

                results.append({
                    "operation": operation.value,
                    "success": True,
                    "result": result
                })

            except Exception as e:
                results.append({
                    "operation": operation.value,
                    "success": False,
                    "error": str(e)
                })

        return {
            "total_operations": len(pycrud_operations),
            "successful_operations": sum(1 for r in results if r["success"]),
            "results": results
        }


# ==================== 旧代码清理（删除不需要的类） ====================

# ==================== 使用示例 ====================

async def main():
    """使用示例"""

    # 初始化统一配置管理器
    try:
        from tools.ace.src.unified_config_manager import UnifiedConfigManager
        UnifiedConfigManager.initialize()
    except ImportError:
        logger.warning("UnifiedConfigManager not available, using defaults")

    # 创建ValidationDrivenExecutor
    executor = ValidationDrivenExecutor(
        task_id="task_001_code_generation",
        executor_role="code_generator",
        task_context={"project": "nexus", "module": "plugin-manager"},
        validation_ai_config={
            "model": "gemini_2_5_pro",           # 验证AI模型
            "confidence_threshold": 0.85,       # 验证AI的置信度门槛
            "timeout": 30,                      # 验证超时时间
            "max_retries": 2                    # 验证最大重试次数
        }
    )

    # 示例1：生成并执行代码
    result = await executor.execute_with_validation(
        original_content="实现支持热插拔的插件管理器，输出Java类代码",  # 📄 任务目标(包含输出要求)
        pycrud_operations=[                              # 📋 操作枚举列表 (调用者选择)
            PyCRUDOperation.CODE_GENERATE_CLASS,
            PyCRUDOperation.FILE_CREATE,
            PyCRUDOperation.CODE_VALIDATE_SYNTAX
        ],
        guardrails={                                     # 🔍 护栏规则(不能做什么)
            "禁止项": {
                "不能使用": ["过时的技术栈", "未授权的第三方库"],
                "不能访问": ["生产数据库", "敏感配置文件"]
            },
            "安全边界": {
                "禁止恶意代码": "不得生成任何恶意代码",
                "禁止敏感操作": "不得执行删除、格式化等危险操作"
            }
        },
        constraints={                                    # 🎯 约束条件(必须做什么)
            "依赖条件": {
                "必须集成": ["Spring Boot 3.2.x", "统一认证中心"],
                "必须兼容": ["MySQL 8.0", "Redis 6.x"]
            },
            "架构要求": {
                "必须采用": "微服务架构",
                "必须支持": ["热插拔", "动态加载", "版本管理"]
            },
            "质量要求": {
                "必须通过": ["单元测试", "代码审查", "安全扫描"],
                "必须达到": "95%测试覆盖率"
            }
        },
        context={                                        # 🌍 上下文信息(环境背景)
            "项目背景": "电商平台插件系统重构",
            "当前环境": {
                "现有技术栈": "Spring Boot 2.x + MySQL + Redis",
                "团队规模": "15人开发团队",
                "部署环境": "阿里云K8s集群"
            },
            "业务场景": {
                "峰值流量": "双11期间100万QPS",
                "插件数量": "预计50+个业务插件"
            }
        },
        confidence_threshold=0.85
    )

    print("执行结果:")
    print(f"成功: {result.success}")
    print(f"置信度: {result.confidence:.3f}")
    if result.success:
        if result.generated_content:
            print(f"生成内容: {result.generated_content[:200]}...")
        if result.execution_result:
            print(f"执行结果: {result.execution_result}")
    else:
        print(f"错误信息: {result.error_message}")

        # 检查是否为上下文错误
        if result.validation_result and hasattr(result.validation_result, 'context_error') and result.validation_result.context_error:
            print("⚠️  这是上下文设计问题，需要重新设计上下文！")
            print("📋 上下文问题详情:")
            for issue in result.validation_result.issues:
                print(f"   - {issue}")
            print("\n💡 建议：请根据上述问题重新设计上下文参数")

    # 示例2：只生成不执行 (用于设计、分析等场景)
    design_result = await executor.execute_with_validation(
        original_content="分析插件管理器的架构设计方案，输出markdown格式的分析报告",
        pycrud_operations=None,                          # None = 只生成不执行
        guardrails={
            "内容边界": {
                "不能泄露": ["商业机密", "技术细节"],
                "不能偏离": "客观分析原则"
            }
        },
        constraints={
            "分析要求": {
                "必须包含": ["架构优势", "潜在风险", "实施建议"],
                "必须遵循": "技术中立原则"
            }
        },
        context={
            "分析背景": "为技术选型提供决策依据",
            "目标受众": "技术委员会"
        },
        confidence_threshold=0.80
    )

    print(f"\n设计分析结果:")
    print(f"成功: {design_result.success}")
    print(f"置信度: {design_result.confidence:.3f}")
    if design_result.success and design_result.generated_content:
        print(f"分析报告: {design_result.generated_content[:200]}...")

    # 示例3：演示上下文错误的情况
    print(f"\n" + "="*50)
    print("示例3：上下文设计不完整的情况")
    print("="*50)

    context_error_result = await executor.execute_with_validation(
        original_content="实现高性能的数据处理系统",
        pycrud_operations=[PyCRUDOperation.CODE_GENERATE_CLASS],
        guardrails={
            "禁止项": {
                "不能使用": ["过时技术", "不安全的库"],
                "不能访问": ["生产数据库"]
            }
        },
        constraints={
            "依赖条件": {
                "必须集成": ["高性能框架", "分布式存储"],
                "必须兼容": ["现有系统"]
            },
            "架构要求": {
                "必须采用": "微服务架构",
                "必须支持": ["高并发", "容错"]
            }
        },
        context={
            # 故意设计不完整的上下文，演示1对1问题映射
            "项目背景": "数据处理",  # 过于简单（少于20个字符）
            "技术栈": "Java",       # 缺少版本信息
            "业务场景": {},         # 字段值为空
            "目标受众": "可能是企业用户",  # 包含模糊表述
            # 缺少必需字段：当前环境
        },
        confidence_threshold=0.85
    )

    print(f"上下文错误示例结果:")
    print(f"成功: {context_error_result.success}")
    if not context_error_result.success:
        print(f"错误类型: {'上下文设计问题' if context_error_result.validation_result and hasattr(context_error_result.validation_result, 'context_error') and context_error_result.validation_result.context_error else '其他错误'}")
        print(f"错误信息: {context_error_result.error_message}")
        if context_error_result.validation_result and hasattr(context_error_result.validation_result, 'issues'):
            print("\n📋 1对1上下文问题映射:")
            for issue in context_error_result.validation_result.issues:
                print(f"  - {issue}")

            print("\n💡 基于1对1映射的改进建议:")
            print("  根据上述具体问题，调用者需要：")
            print("  1. '项目背景': 补充详细描述（至少20个字符）")
            print("  2. '技术栈': 添加具体版本号信息")
            print("  3. '业务场景': 提供具体的性能指标")
            print("  4. '目标受众': 移除模糊表述，使用明确描述")
            print("  5. '当前环境': 添加缺少的必需字段")

    # 查看任务状态
    status = executor.get_task_status()
    print(f"\n任务状态:")
    print(f"任务ID: {status['task_id']}")
    print(f"执行器角色: {status['executor_role']}")
    print(f"执行次数: {status['execution_count']}")
    print(f"当前置信度: {status['current_confidence']:.3f}")
    print(f"平均执行时间: {status['average_execution_time']:.3f}s")
    print(f"AI管理器可用: {status['ai_manager_available']}")


if __name__ == "__main__":
    asyncio.run(main())
