# XKongCloud Commons Nexus代码修改模板

## 文档信息
- **文档ID**: XKONGCLOUD COMMONS NEXUS-CODE-TEMPLATES
- **创建日期**: 2025-06-14
- **版本**: v3.1-enhanced
- **关联主计划**: 01-XKongCloud Commons Nexus主实施计划.md

## 模板使用说明

### 模板结构
每个代码模板包含以下部分：
- **文件路径**: 目标文件的完整路径
- **包声明**: Java包声明
- **导入语句**: 必要的import语句
- **类/接口定义**: 完整的类或接口结构
- **方法实现**: 具体的方法实现代码

### 使用原则
- 严格按照模板结构实现
- 保持代码风格一致性
- 遵循Java编码规范
- 确保所有依赖正确导入

## 核心组件模板

### 注解配置模板

#### @EnableNexus注解模板
**文件路径**: `org/xkong/cloud/commons/nexus/config/EnableNexus.java`

```java
package org.xkong.cloud.commons.nexus.config;

import org.springframework.context.annotation.Import;
import java.lang.annotation.*;

/**
 * 启用Nexus功能的注解
 * 自动配置Nexus相关组件
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(NexusAutoConfiguration.class)
public @interface EnableNexus {

    /**
     * 插件扫描包路径
     */
    String[] scanPackages() default {};

    /**
     * 是否启用自动配置
     */
    boolean autoConfiguration() default true;

    /**
     * 事件总线配置
     */
    String eventBusConfig() default "default";
}
```

#### @ExtensionPoint注解模板
**文件路径**: `org/xkong/cloud/commons/nexus/api/ExtensionPoint.java`

```java
package org.xkong.cloud.commons.nexus.api;

import java.lang.annotation.*;

/**
 * 扩展点标记注解
 * 标识可以被插件扩展的接口
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExtensionPoint {

    /**
     * 扩展点名称
     */
    String value() default "";

    /**
     * 扩展点描述
     */
    String description() default "";

    /**
     * 是否允许多个实现
     */
    boolean multiple() default true;
}
```

### 核心接口模板

#### ServiceBus接口模板
**文件路径**: `org/xkong/cloud/commons/nexus/api/ServiceBus.java`

```java
package org.xkong.cloud.commons.nexus.api;

import java.util.concurrent.CompletableFuture;

/**
 * 服务总线接口
 * 提供事件发布和服务注册功能
 */
@ExtensionPoint("service-bus")
public interface ServiceBus {

    /**
     * 发布事件
     */
    <T extends Event> CompletableFuture<Void> publish(T event);

    /**
     * 异步发布事件
     */
    <T extends Event> CompletableFuture<Void> publishAsync(T event);

    /**
     * 注册服务
     */
    <T> void registerService(Class<T> serviceType, T serviceInstance);

    /**
     * 获取服务
     */
    <T> T getService(Class<T> serviceType);
}
```

### 实现类模板

#### PluginActivator实现模板
**文件路径**: `org/xkong/cloud/commons/nexus/core/PluginActivator.java`

```java
package org.xkong.cloud.commons.nexus.core;

import org.xkong.cloud.commons.nexus.api.*;
import org.springframework.stereotype.Component;

/**
 * 插件激活器实现
 * 负责插件的生命周期管理
 */
@Component
public class PluginActivator {

    private final PluginContext pluginContext;
    private final ServiceBus serviceBus;

    public PluginActivator(PluginContext pluginContext, ServiceBus serviceBus) {
        this.pluginContext = pluginContext;
        this.serviceBus = serviceBus;
    }

    /**
     * 启动插件
     */
    public void start(Plugin plugin) {
        try {
            plugin.start(pluginContext);
            serviceBus.publish(new PluginStartedEvent(plugin));
        } catch (Exception e) {
            throw new PluginStartException("Failed to start plugin: " + plugin.getName(), e);
        }
    }

    /**
     * 停止插件
     */
    public void stop(Plugin plugin) {
        try {
            plugin.stop(pluginContext);
            serviceBus.publish(new PluginStoppedEvent(plugin));
        } catch (Exception e) {
            // 记录错误但不抛出异常
            System.err.println("Error stopping plugin: " + plugin.getName());
        }
    }
}
```

## 配置类模板

### Spring Boot自动配置模板
**文件路径**: `org/xkong/cloud/commons/nexus/config/NexusAutoConfiguration.java`

```java
package org.xkong.cloud.commons.nexus.config;

import org.xkong.cloud.commons.nexus.core.*;
import org.xkong.cloud.commons.nexus.api.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Nexus自动配置类
 */
@Configuration
public class NexusAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public ServiceBus serviceBus() {
        return new DefaultServiceBus();
    }

    @Bean
    @ConditionalOnMissingBean
    public PluginContext pluginContext() {
        return new DefaultPluginContext();
    }

    @Bean
    @ConditionalOnMissingBean
    public PluginActivator pluginActivator(PluginContext pluginContext, ServiceBus serviceBus) {
        return new PluginActivator(pluginContext, serviceBus);
    }
}
```

## 测试模板

### 单元测试模板
**文件路径**: `C:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-nexus\src\test\java\org\xkong\cloud\commons\nexus\core\PluginActivatorTest.java`

```java
package org.xkong.cloud.commons.nexus.core;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * PluginActivator单元测试
 */
class PluginActivatorTest {

    @Mock
    private PluginContext pluginContext;

    @Mock
    private ServiceBus serviceBus;

    @Mock
    private Plugin plugin;

    private PluginActivator pluginActivator;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        pluginActivator = new PluginActivator(pluginContext, serviceBus);
    }

    @Test
    void testStartPlugin() {
        // Given
        when(plugin.getName()).thenReturn("test-plugin");

        // When
        pluginActivator.start(plugin);

        // Then
        verify(plugin).start(pluginContext);
        verify(serviceBus).publish(any(PluginStartedEvent.class));
    }
}
```

## 使用指南

### 实施步骤
1. **创建目录结构**: 按照包名创建对应的目录
2. **复制模板代码**: 将模板代码复制到对应文件
3. **调整包名**: 确保包名与实际项目一致
4. **补充实现**: 根据具体需求补充方法实现
5. **编译验证**: 确保代码能够正常编译
6. **测试验证**: 运行单元测试确保功能正确

### 注意事项
- 保持代码风格一致
- 遵循命名规范
- 添加必要的注释
- 确保异常处理完整
- 编写对应的单元测试

---
**模板版本**: v3.1-enhanced
**最后更新**: 2025-06-14 20:41:34
