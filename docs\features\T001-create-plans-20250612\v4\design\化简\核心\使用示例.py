#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用智能代码生成系统 - 使用示例
Universal Intelligent Code Generation System - Usage Examples

文档ID: T001-UNIVERSAL-CODE-GENERATION-EXAMPLES-001
版本: V1.0
创建日期: 2025-01-16
作者: AI Code Generation System
适用范围: 演示如何使用通用代码生成系统
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

try:
    from 算法 import UniversalCodeGenerationSystem
    from 检查 import DesignDocumentPreChecker
    from 安全生成 import SafeCodeGenerationSystem
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保以下文件在同一目录下：")
    print("- 算法.py")
    print("- 检查.py")
    print("- 安全生成.py")
    print("并安装必要的依赖包：")
    print("pip install networkx javalang openai pyyaml")
    sys.exit(1)

def example_0_pre_check_only():
    """示例0：设计文档预检查（新增）"""
    print("=" * 60)
    print("示例0：设计文档预检查")
    print("=" * 60)

    # 创建预检查器
    checker = DesignDocumentPreChecker()

    # 设置文档路径
    design_doc_path = "通用智能代码生成系统设计方案.md"

    print(f"📋 检查文档: {design_doc_path}")

    # 检查文档是否存在
    if not os.path.exists(design_doc_path):
        print(f"❌ 设计文档不存在: {design_doc_path}")
        print("💡 请确保设计文档在当前目录下")
        return

    try:
        # 执行预检查
        print("🔍 正在执行完整性检查...")
        report = checker.check_document_completeness(design_doc_path)

        # 打印详细报告
        checker.print_report(report)

        # 给出建议
        if report.is_ready_for_generation:
            print("\n✅ 建议: 可以直接使用示例1进行代码生成")
        elif report.human_intervention_required:
            print("\n🚨 建议: 请先解决关键问题，或使用示例2的安全生成模式")
        else:
            print("\n⚠️ 建议: 改进文档质量后重新检查")

        return report

    except Exception as e:
        print(f"❌ 预检查失败: {e}")
        return None

def example_0_5_safe_generation():
    """示例0.5：安全代码生成（预检查+生成）"""
    print("=" * 60)
    print("示例0.5：安全代码生成")
    print("=" * 60)

    # 设置路径
    design_doc_path = "通用智能代码生成系统设计方案.md"
    output_dir = "output/safe-generated-code"

    print(f"📋 设计文档: {design_doc_path}")
    print(f"📁 输出目录: {output_dir}")

    # 检查文档是否存在
    if not os.path.exists(design_doc_path):
        print(f"❌ 设计文档不存在: {design_doc_path}")
        return

    try:
        # 创建安全生成系统
        safe_generator = SafeCodeGenerationSystem()

        # 执行安全生成（包含预检查）
        print("🛡️ 正在执行安全代码生成...")
        result = safe_generator.safe_generate_code(
            design_doc_path=design_doc_path,
            output_dir=output_dir,
            force_generate=False  # 遵循安全检查
        )

        # 分析结果
        if result["success"]:
            print("\n🎉 安全生成成功!")
            print(f"📊 预检查分数: {result['pre_check']['score']:.2f}")
            print(f"📊 总体质量: {result.get('overall_quality', 0.0):.2f}")
            print(f"⏱️ 总耗时: {result['generation'].get('total_time', 0.0):.2f}秒")
        else:
            print(f"\n❌ 安全生成失败: {result.get('reason', 'Unknown')}")
            if "next_steps" in result:
                print("📝 建议的下一步:")
                for step in result["next_steps"]:
                    print(f"  - {step}")

        return result

    except Exception as e:
        print(f"❌ 安全生成异常: {e}")
        return None

def example_1_basic_usage():
    """示例1：基本使用方法"""
    print("=" * 60)
    print("示例1：基本使用方法")
    print("=" * 60)
    
    # 创建代码生成系统
    system = UniversalCodeGenerationSystem()
    
    # 设置输入和输出路径
    design_doc_path = "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/00-护栏约束上下文总览.md"
    output_dir = "output/generated-code-basic"
    
    print(f"📋 设计文档路径: {design_doc_path}")
    print(f"📁 输出目录: {output_dir}")
    
    # 检查设计文档是否存在
    if not os.path.exists(design_doc_path):
        print(f"❌ 设计文档不存在: {design_doc_path}")
        print("请确保设计文档路径正确")
        return
    
    try:
        # 生成生产级代码
        print("🚀 开始生成代码...")
        result = system.generate_production_code(design_doc_path, output_dir)
        
        # 显示结果
        if result["success"]:
            print("✅ 代码生成成功！")
            print(f"⏱️  总耗时: {result['total_time']:.2f}秒")
            print(f"📊 组件数量: {result['components_generated']}")
            print(f"🎯 质量分数: {result['quality_score']:.2%}")
            print(f"📁 输出目录: {result['output_directory']}")
            
            # 显示生成的组件详情
            print("\n📋 生成的组件详情:")
            for i, gen_result in enumerate(result['generation_results'][:5], 1):
                status = "✅" if gen_result.is_production_ready else "⚠️"
                print(f"  {i}. {status} {gen_result.component_id} (质量: {gen_result.quality_score:.2%})")
            
            if len(result['generation_results']) > 5:
                print(f"  ... 还有{len(result['generation_results']) - 5}个组件")
        else:
            print("❌ 代码生成失败！")
            print(f"错误信息: {result['error']}")
            
    except Exception as e:
        print(f"❌ 系统异常: {e}")

def example_2_custom_configuration():
    """示例2：自定义配置"""
    print("=" * 60)
    print("示例2：自定义配置")
    print("=" * 60)
    
    # 使用自定义OpenAI API密钥
    custom_api_key = os.getenv("CUSTOM_OPENAI_API_KEY", "your-api-key-here")
    system = UniversalCodeGenerationSystem(openai_api_key=custom_api_key)
    
    print(f"🔑 使用自定义API密钥: {custom_api_key[:10]}...")
    print("📋 可以通过环境变量CUSTOM_OPENAI_API_KEY设置")

def example_3_batch_processing():
    """示例3：批量处理多个设计文档"""
    print("=" * 60)
    print("示例3：批量处理多个设计文档")
    print("=" * 60)
    
    # 模拟多个设计文档
    design_docs = [
        {
            "name": "Nexus万用插座",
            "path": "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/00-护栏约束上下文总览.md",
            "output": "output/generated-code-nexus"
        },
        # 可以添加更多设计文档
    ]
    
    system = UniversalCodeGenerationSystem()
    
    results = []
    for doc_info in design_docs:
        print(f"\n🔄 处理文档: {doc_info['name']}")
        
        if not os.path.exists(doc_info['path']):
            print(f"⚠️  跳过不存在的文档: {doc_info['path']}")
            continue
        
        try:
            result = system.generate_production_code(doc_info['path'], doc_info['output'])
            results.append({
                "name": doc_info['name'],
                "result": result
            })
            
            status = "✅" if result["success"] else "❌"
            print(f"{status} {doc_info['name']}: {result.get('components_generated', 0)}个组件")
            
        except Exception as e:
            print(f"❌ {doc_info['name']} 处理失败: {e}")
    
    # 汇总结果
    print("\n📊 批量处理汇总:")
    total_components = sum(r["result"].get("components_generated", 0) for r in results if r["result"]["success"])
    success_count = sum(1 for r in results if r["result"]["success"])
    
    print(f"  成功文档: {success_count}/{len(results)}")
    print(f"  总组件数: {total_components}")

def example_4_quality_analysis():
    """示例4：质量分析"""
    print("=" * 60)
    print("示例4：质量分析")
    print("=" * 60)
    
    system = UniversalCodeGenerationSystem()
    
    design_doc_path = "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/00-护栏约束上下文总览.md"
    output_dir = "output/generated-code-quality"
    
    if not os.path.exists(design_doc_path):
        print(f"❌ 设计文档不存在: {design_doc_path}")
        return
    
    try:
        result = system.generate_production_code(design_doc_path, output_dir)
        
        if result["success"]:
            print("📊 质量分析报告:")
            print(f"  整体质量分数: {result['quality_score']:.2%}")
            
            # 分析各组件质量
            generation_results = result['generation_results']
            
            # 按质量分数排序
            sorted_results = sorted(generation_results, key=lambda x: x.quality_score, reverse=True)
            
            print("\n🏆 质量排行榜 (前10名):")
            for i, gen_result in enumerate(sorted_results[:10], 1):
                status = "🟢" if gen_result.quality_score >= 0.9 else "🟡" if gen_result.quality_score >= 0.7 else "🔴"
                print(f"  {i:2d}. {status} {gen_result.component_id:<30} {gen_result.quality_score:.2%}")
            
            # 统计质量分布
            high_quality = sum(1 for r in generation_results if r.quality_score >= 0.9)
            medium_quality = sum(1 for r in generation_results if 0.7 <= r.quality_score < 0.9)
            low_quality = sum(1 for r in generation_results if r.quality_score < 0.7)
            
            print(f"\n📈 质量分布:")
            print(f"  🟢 高质量 (≥90%): {high_quality} 个")
            print(f"  🟡 中等质量 (70-89%): {medium_quality} 个")
            print(f"  🔴 低质量 (<70%): {low_quality} 个")
            
            # 显示问题组件
            if low_quality > 0:
                print(f"\n⚠️  需要关注的低质量组件:")
                for gen_result in sorted_results:
                    if gen_result.quality_score < 0.7:
                        print(f"  - {gen_result.component_id}: {gen_result.quality_score:.2%}")
                        for violation in gen_result.violations[:3]:
                            print(f"    • {violation}")
                        if len(gen_result.violations) > 3:
                            print(f"    • ... 还有{len(gen_result.violations) - 3}个问题")
        
    except Exception as e:
        print(f"❌ 质量分析失败: {e}")

def example_5_performance_monitoring():
    """示例5：性能监控"""
    print("=" * 60)
    print("示例5：性能监控")
    print("=" * 60)
    
    import time
    
    system = UniversalCodeGenerationSystem()
    
    design_doc_path = "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/00-护栏约束上下文总览.md"
    output_dir = "output/generated-code-perf"
    
    if not os.path.exists(design_doc_path):
        print(f"❌ 设计文档不存在: {design_doc_path}")
        return
    
    try:
        # 监控各阶段性能
        start_time = time.time()
        
        print("⏱️  性能监控开始...")
        result = system.generate_production_code(design_doc_path, output_dir)
        
        if result["success"]:
            print(f"\n📊 性能报告:")
            print(f"  总耗时: {result['total_time']:.2f}秒")
            print(f"  组件数量: {result['components_generated']}")
            print(f"  平均每组件: {result['total_time'] / max(result['components_generated'], 1):.2f}秒")
            
            # 分析各组件生成时间
            generation_results = result['generation_results']
            generation_times = [r.generation_time for r in generation_results if r.generation_time > 0]
            
            if generation_times:
                avg_time = sum(generation_times) / len(generation_times)
                max_time = max(generation_times)
                min_time = min(generation_times)
                
                print(f"\n⏱️  组件生成时间分析:")
                print(f"  平均时间: {avg_time:.2f}秒")
                print(f"  最长时间: {max_time:.2f}秒")
                print(f"  最短时间: {min_time:.2f}秒")
                
                # 找出最慢的组件
                slowest = max(generation_results, key=lambda x: x.generation_time)
                print(f"  最慢组件: {slowest.component_id} ({slowest.generation_time:.2f}秒)")
        
    except Exception as e:
        print(f"❌ 性能监控失败: {e}")

def main():
    """主函数 - 运行所有示例"""
    print("通用智能代码生成系统 - 使用示例")
    print("=" * 80)
    
    examples = [
        ("🔍 设计文档预检查", example_0_pre_check_only),
        ("🛡️ 安全代码生成（推荐）", example_0_5_safe_generation),
        ("📝 基本使用方法", example_1_basic_usage),
        ("⚙️ 自定义配置", example_2_custom_configuration),
        ("📦 批量处理", example_3_batch_processing),
        ("📊 质量分析", example_4_quality_analysis),
        ("⏱️ 性能监控", example_5_performance_monitoring)
    ]

    print("📋 可用示例:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"  {i}. {name}")

    print("\n💡 推荐流程:")
    print("  1️⃣ 先运行示例1进行预检查")
    print("  2️⃣ 再运行示例2进行安全生成")
    print("  3️⃣ 或直接运行示例3进行传统生成")

    print("\n请选择要运行的示例 (1-7, 或按回车运行示例2):")
    choice = input().strip()
    
    if not choice:
        choice = "2"  # 默认运行安全生成示例
    
    try:
        index = int(choice) - 1
        if 0 <= index < len(examples):
            name, func = examples[index]
            print(f"\n🚀 运行示例: {name}")
            func()
        else:
            print("❌ 无效选择")
    except ValueError:
        print("❌ 请输入有效数字")
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")

if __name__ == "__main__":
    main()
