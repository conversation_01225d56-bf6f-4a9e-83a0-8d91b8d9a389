#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复验证测试脚本

验证数据提取逻辑修复效果

作者：AI助手
日期：2025-01-09
"""

import sys
import os
from pathlib import Path

def main():
    """主测试函数"""
    print("🔧 数据提取逻辑修复验证测试")
    print("=" * 60)
    print("📋 修复内容:")
    print("   ✅ 修复reasoning_content提取逻辑")
    print("   ✅ 增强thinking模式检测算法")
    print("   ✅ 适配不同模型的输出格式")
    print("   ✅ 改进技术文档分析能力")
    print()
    
    # 检查主程序文件
    current_dir = Path(__file__).parent
    main_program = current_dir / "thinking_quality_fusion_research.py"
    
    if not main_program.exists():
        print("❌ 错误: 找不到主程序文件")
        return False
    
    print(f"✅ 找到修复后的主程序: {main_program}")
    print()
    
    try:
        # 导入并运行修复后的程序
        sys.path.insert(0, str(current_dir))
        from thinking_quality_fusion_research import main as research_main
        
        print("🚀 开始执行修复后的研究...")
        print("🎯 预期改进:")
        print("   • R1模型thinking质量显著提升")
        print("   • 融合效果从负值变为正值")
        print("   • 技术栈兼容性改善")
        print("   • 整体评估结果更合理")
        print()
        print("=" * 60)
        
        result = research_main()
        
        if result:
            print("\n" + "=" * 60)
            print("✅ 修复验证完成!")
            print("📄 请对比新旧报告查看修复效果")
            print("🔍 重点关注:")
            print("   • R1模型的thinking质量评分")
            print("   • 融合方案的增强效果")
            print("   • 技术栈兼容性指标")
            return True
        else:
            print("\n❌ 修复验证失败")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 修复验证成功!")
        print("📊 请查看新生成的报告了解修复效果")
    else:
        print("\n💥 修复验证失败")
        sys.exit(1)
