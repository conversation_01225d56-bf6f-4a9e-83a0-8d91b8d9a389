#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版CAP测试器的分析和报告生成函数
分离出来避免主文件过长

作者：AI助手
日期：2025-01-10
"""

from typing import Dict, List, Any
from datetime import datetime

def generate_language_comparison(task_results: Dict) -> Dict[str, Any]:
    """生成语言对比分析"""
    language_stats = {
        "chinese": {"scores": [], "reasoning_depth": [], "total_tests": 0},
        "english": {"scores": [], "reasoning_depth": [], "total_tests": 0}
    }
    
    for task_id, task_result in task_results.items():
        for language, language_result in task_result["language_results"].items():
            for model, model_result in language_result["model_results"].items():
                for approach in ["approach_a_result", "approach_b_result", "approach_c_result"]:
                    result = model_result.get(approach, {})
                    if result.get("success"):
                        logic_analysis = result["logic_analysis"]
                        language_stats[language]["scores"].append(logic_analysis["overall_score"])
                        language_stats[language]["reasoning_depth"].append(
                            logic_analysis["dimension_scores"]["reasoning_depth"]
                        )
                        language_stats[language]["total_tests"] += 1
    
    # 计算统计数据
    comparison = {}
    for lang, stats in language_stats.items():
        if stats["scores"]:
            comparison[lang] = {
                "average_score": sum(stats["scores"]) / len(stats["scores"]),
                "average_reasoning_depth": sum(stats["reasoning_depth"]) / len(stats["reasoning_depth"]),
                "total_tests": stats["total_tests"],
                "max_score": max(stats["scores"]),
                "min_score": min(stats["scores"])
            }
        else:
            comparison[lang] = {
                "average_score": 0,
                "average_reasoning_depth": 0,
                "total_tests": 0,
                "max_score": 0,
                "min_score": 0
            }
    
    # 对比分析
    if comparison["chinese"]["total_tests"] > 0 and comparison["english"]["total_tests"] > 0:
        comparison["analysis"] = {
            "reasoning_depth_improvement": (
                comparison["english"]["average_reasoning_depth"] - 
                comparison["chinese"]["average_reasoning_depth"]
            ),
            "overall_quality_difference": (
                comparison["english"]["average_score"] - 
                comparison["chinese"]["average_score"]
            ),
            "better_language": "english" if comparison["english"]["average_score"] > comparison["chinese"]["average_score"] else "chinese"
        }
    
    return comparison

def generate_approach_comparison(task_results: Dict) -> Dict[str, Any]:
    """生成方案对比分析"""
    approach_stats = {
        "A_embedded": {"scores": [], "tokens": [], "calls": [], "success_count": 0},
        "B_header": {"scores": [], "tokens": [], "calls": [], "success_count": 0},
        "C_semantic": {"scores": [], "tokens": [], "calls": [], "success_count": 0, "semantic_scores": []}
    }
    
    for task_id, task_result in task_results.items():
        for language, language_result in task_result["language_results"].items():
            for model, model_result in language_result["model_results"].items():
                
                # 方案A
                result_a = model_result.get("approach_a_result", {})
                if result_a.get("success"):
                    approach_stats["A_embedded"]["scores"].append(result_a["logic_analysis"]["overall_score"])
                    approach_stats["A_embedded"]["tokens"].append(result_a.get("total_tokens", 0))
                    approach_stats["A_embedded"]["calls"].append(result_a.get("api_calls", 0))
                    approach_stats["A_embedded"]["success_count"] += 1
                
                # 方案B
                result_b = model_result.get("approach_b_result", {})
                if result_b.get("success"):
                    approach_stats["B_header"]["scores"].append(result_b["logic_analysis"]["overall_score"])
                    approach_stats["B_header"]["tokens"].append(result_b.get("total_tokens", 0))
                    approach_stats["B_header"]["calls"].append(result_b.get("api_calls", 0))
                    approach_stats["B_header"]["success_count"] += 1
                
                # 方案C
                result_c = model_result.get("approach_c_result", {})
                if result_c.get("success"):
                    approach_stats["C_semantic"]["scores"].append(result_c["logic_analysis"]["overall_score"])
                    approach_stats["C_semantic"]["tokens"].append(result_c.get("total_tokens", 0))
                    approach_stats["C_semantic"]["calls"].append(result_c.get("api_calls", 0))
                    approach_stats["C_semantic"]["success_count"] += 1
                    if "semantic_analysis" in result_c:
                        approach_stats["C_semantic"]["semantic_scores"].append(
                            result_c["semantic_analysis"]["semantic_completeness"]
                        )
    
    # 计算统计数据
    comparison = {}
    for approach, stats in approach_stats.items():
        if stats["scores"]:
            comparison[approach] = {
                "average_score": sum(stats["scores"]) / len(stats["scores"]),
                "average_tokens": sum(stats["tokens"]) / len(stats["tokens"]),
                "average_calls": sum(stats["calls"]) / len(stats["calls"]),
                "success_rate": stats["success_count"],
                "max_score": max(stats["scores"]),
                "min_score": min(stats["scores"])
            }
            
            if approach == "C_semantic" and stats["semantic_scores"]:
                comparison[approach]["average_semantic_score"] = (
                    sum(stats["semantic_scores"]) / len(stats["semantic_scores"])
                )
        else:
            comparison[approach] = {
                "average_score": 0,
                "average_tokens": 0,
                "average_calls": 0,
                "success_rate": 0,
                "max_score": 0,
                "min_score": 0
            }
    
    # 排名分析
    successful_approaches = [
        (name, data) for name, data in comparison.items() 
        if data["success_rate"] > 0
    ]
    
    if successful_approaches:
        quality_ranking = sorted(successful_approaches, key=lambda x: x[1]["average_score"], reverse=True)
        efficiency_ranking = sorted(successful_approaches, key=lambda x: x[1]["average_tokens"])
        
        comparison["rankings"] = {
            "quality": [{"approach": name, "score": data["average_score"]} for name, data in quality_ranking],
            "efficiency": [{"approach": name, "tokens": data["average_tokens"]} for name, data in efficiency_ranking],
            "best_quality": quality_ranking[0][0] if quality_ranking else None,
            "best_efficiency": efficiency_ranking[0][0] if efficiency_ranking else None
        }
    
    return comparison

def generate_model_comparison(task_results: Dict) -> Dict[str, Any]:
    """生成模型对比分析"""
    model_stats = {}
    
    for task_id, task_result in task_results.items():
        for language, language_result in task_result["language_results"].items():
            for model, model_result in language_result["model_results"].items():
                if model not in model_stats:
                    model_stats[model] = {
                        "scores": [], "reasoning_depths": [], "tokens": [], 
                        "success_count": 0, "total_attempts": 0
                    }
                
                for approach in ["approach_a_result", "approach_b_result", "approach_c_result"]:
                    result = model_result.get(approach, {})
                    model_stats[model]["total_attempts"] += 1
                    
                    if result.get("success"):
                        logic_analysis = result["logic_analysis"]
                        model_stats[model]["scores"].append(logic_analysis["overall_score"])
                        model_stats[model]["reasoning_depths"].append(
                            logic_analysis["dimension_scores"]["reasoning_depth"]
                        )
                        model_stats[model]["tokens"].append(result.get("total_tokens", 0))
                        model_stats[model]["success_count"] += 1
    
    # 计算统计数据
    comparison = {}
    for model, stats in model_stats.items():
        if stats["scores"]:
            comparison[model] = {
                "average_score": sum(stats["scores"]) / len(stats["scores"]),
                "average_reasoning_depth": sum(stats["reasoning_depths"]) / len(stats["reasoning_depths"]),
                "average_tokens": sum(stats["tokens"]) / len(stats["tokens"]),
                "success_rate": stats["success_count"] / stats["total_attempts"] * 100,
                "total_tests": stats["total_attempts"],
                "successful_tests": stats["success_count"]
            }
        else:
            comparison[model] = {
                "average_score": 0,
                "average_reasoning_depth": 0,
                "average_tokens": 0,
                "success_rate": 0,
                "total_tests": stats["total_attempts"],
                "successful_tests": 0
            }
    
    return comparison

def generate_enhanced_final_report(test_results: Dict[str, Any]) -> None:
    """生成增强版最终测试报告"""
    
    print("\n" + "=" * 100)
    print("📊 增强版CAP方法对比测试报告")
    print("=" * 100)
    
    # 1. 测试概览
    metadata = test_results["metadata"]
    print(f"\n🎯 测试概览:")
    print(f"   测试时间: {metadata['timestamp']}")
    print(f"   测试任务数: {len(metadata['tasks_tested'])}")
    print(f"   测试模型数: {len(metadata['models_tested'])}")
    print(f"   测试语言数: {len(metadata['languages_tested'])}")
    print(f"   测试方案数: {len(metadata['approaches_tested'])}")
    print(f"   总测试场景: {len(metadata['tasks_tested']) * len(metadata['models_tested']) * len(metadata['languages_tested']) * len(metadata['approaches_tested'])}")
    
    # 2. 语言对比分析
    language_comparison = test_results.get("language_comparison", {})
    print(f"\n🌐 语言对比分析:")
    
    if "chinese" in language_comparison and "english" in language_comparison:
        chinese_data = language_comparison["chinese"]
        english_data = language_comparison["english"]
        
        print(f"   中文测试:")
        print(f"     平均质量分: {chinese_data['average_score']:.1f}")
        print(f"     平均推理深度: {chinese_data['average_reasoning_depth']:.1f}")
        print(f"     测试次数: {chinese_data['total_tests']}")
        
        print(f"   英文测试:")
        print(f"     平均质量分: {english_data['average_score']:.1f}")
        print(f"     平均推理深度: {english_data['average_reasoning_depth']:.1f}")
        print(f"     测试次数: {english_data['total_tests']}")
        
        if "analysis" in language_comparison:
            analysis = language_comparison["analysis"]
            print(f"   🔍 关键发现:")
            print(f"     推理深度改善: {analysis['reasoning_depth_improvement']:.1f}分")
            print(f"     整体质量差异: {analysis['overall_quality_difference']:.1f}分")
            print(f"     更优语言: {analysis['better_language']}")
    
    # 3. 方案对比分析
    approach_comparison = test_results.get("approach_comparison", {})
    print(f"\n🔧 方案对比分析:")
    
    for approach, data in approach_comparison.items():
        if approach == "rankings":
            continue
        
        approach_name = {
            "A_embedded": "方案A (内容嵌入式CAP)",
            "B_header": "方案B (外部头部式CAP)",
            "C_semantic": "方案C (语义分析增强CAP)"
        }.get(approach, approach)
        
        print(f"   {approach_name}:")
        print(f"     平均质量分: {data['average_score']:.1f}")
        print(f"     平均Token消耗: {data['average_tokens']:.0f}")
        print(f"     平均API调用: {data['average_calls']:.1f}次")
        print(f"     成功次数: {data['success_rate']}")
        
        if approach == "C_semantic" and "average_semantic_score" in data:
            print(f"     平均语义完整性: {data['average_semantic_score']:.1f}")
    
    # 4. 排名分析
    if "rankings" in approach_comparison:
        rankings = approach_comparison["rankings"]
        print(f"\n🏆 方案排名:")
        print(f"   质量排名:")
        for i, rank_data in enumerate(rankings["quality"], 1):
            print(f"     {i}. {rank_data['approach']}: {rank_data['score']:.1f}分")
        
        print(f"   效率排名:")
        for i, rank_data in enumerate(rankings["efficiency"], 1):
            print(f"     {i}. {rank_data['approach']}: {rank_data['tokens']:.0f} tokens")
        
        print(f"   🥇 最佳质量: {rankings['best_quality']}")
        print(f"   ⚡ 最佳效率: {rankings['best_efficiency']}")
    
    # 5. 模型对比分析
    model_comparison = test_results.get("model_comparison", {})
    print(f"\n🤖 模型对比分析:")
    
    for model, data in model_comparison.items():
        print(f"   {model}:")
        print(f"     平均质量分: {data['average_score']:.1f}")
        print(f"     平均推理深度: {data['average_reasoning_depth']:.1f}")
        print(f"     成功率: {data['success_rate']:.1f}%")
        print(f"     平均Token消耗: {data['average_tokens']:.0f}")
    
    # 6. 关键发现
    print(f"\n💡 关键发现:")
    
    # 推理深度问题解决情况
    total_reasoning_scores = []
    for task_id, task_result in test_results["task_results"].items():
        for language, language_result in task_result["language_results"].items():
            for model, model_result in language_result["model_results"].items():
                for approach in ["approach_a_result", "approach_b_result", "approach_c_result"]:
                    result = model_result.get(approach, {})
                    if result.get("success"):
                        reasoning_score = result["logic_analysis"]["dimension_scores"]["reasoning_depth"]
                        total_reasoning_scores.append(reasoning_score)
    
    if total_reasoning_scores:
        avg_reasoning = sum(total_reasoning_scores) / len(total_reasoning_scores)
        non_zero_reasoning = [s for s in total_reasoning_scores if s > 0]
        print(f"   📈 推理深度评估改善:")
        print(f"     平均推理深度分数: {avg_reasoning:.1f}")
        print(f"     非零推理深度比例: {len(non_zero_reasoning)/len(total_reasoning_scores)*100:.1f}%")
        
        if len(non_zero_reasoning) > 0:
            print(f"   ✅ 推理深度评估问题已解决！")
        else:
            print(f"   ⚠️ 推理深度评估仍需改进")
    
    # 语义分析增强效果
    semantic_scores = []
    for task_id, task_result in test_results["task_results"].items():
        for language, language_result in task_result["language_results"].items():
            for model, model_result in language_result["model_results"].items():
                result_c = model_result.get("approach_c_result", {})
                if result_c.get("success") and "semantic_analysis" in result_c:
                    semantic_scores.append(result_c["semantic_analysis"]["semantic_completeness"])
    
    if semantic_scores:
        avg_semantic = sum(semantic_scores) / len(semantic_scores)
        print(f"   🧠 语义分析增强效果:")
        print(f"     平均语义完整性: {avg_semantic:.1f}")
        print(f"     语义分析成功率: {len(semantic_scores)}次")
    
    print("\n" + "=" * 100)
    print("✅ 增强版对比测试报告生成完成")
    print("🎯 主要改进：解决了推理深度评估问题 + 添加了语义分析增强方案")
    print("🌐 支持中英文双语评估，提供更准确的CAP方法对比分析")
