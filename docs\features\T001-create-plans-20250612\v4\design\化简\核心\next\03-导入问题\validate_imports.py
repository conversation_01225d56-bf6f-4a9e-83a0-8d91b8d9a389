#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入验证脚本
Import Validation Script

验证修复后的Python模块是否能够正常导入
确保相对导入修复没有引入新的问题

作者：AI架构修复团队
日期：2025-01-18
版本：v1.0
"""

import os
import sys
import importlib
import traceback
from datetime import datetime
from typing import List, Dict, Any

class ImportValidator:
    """导入验证器"""
    
    def __init__(self, base_dir: str = "tools/ace/src"):
        self.base_dir = base_dir
        self.validation_results = []
        
        # 核心模块列表
        self.core_modules = [
            'python_host',
            'algorithms',
            'v4_algorithms',
            'api_management',
            'api_management.core',
            'api_management.core.task_based_ai_service_manager',
            'api_management.sqlite_storage',
            'api_management.sqlite_storage.api_account_database',
            'database',
            'database.panoramic_model_database',
            'configuration_center',
            'task_interfaces',
            'project_container',
            'meeting_directory',
            'four_layer_meeting_system',
            'web_interface'
        ]
        
        # 关键功能测试
        self.functional_tests = [
            'test_api_service_manager',
            'test_database_connection',
            'test_config_loading'
        ]
    
    def setup_environment(self):
        """设置测试环境"""
        # 确保base_dir在Python路径中
        abs_base_dir = os.path.abspath(self.base_dir)
        if abs_base_dir not in sys.path:
            sys.path.insert(0, abs_base_dir)
            print(f"✅ 已添加到Python路径: {abs_base_dir}")
    
    def validate_module_import(self, module_name: str) -> Dict[str, Any]:
        """验证单个模块导入"""
        result = {
            'module_name': module_name,
            'import_success': False,
            'import_time': 0.0,
            'error_message': None,
            'module_attributes': 0,
            'module_path': None
        }
        
        try:
            start_time = datetime.now()
            
            # 尝试导入模块
            module = importlib.import_module(module_name)
            
            end_time = datetime.now()
            import_time = (end_time - start_time).total_seconds()
            
            result.update({
                'import_success': True,
                'import_time': import_time,
                'module_attributes': len(dir(module)),
                'module_path': getattr(module, '__file__', 'Unknown')
            })
            
            print(f"✅ {module_name} - 导入成功 ({import_time:.3f}s)")
            
        except ImportError as e:
            result['error_message'] = f"ImportError: {str(e)}"
            print(f"❌ {module_name} - 导入失败: {e}")
            
        except Exception as e:
            result['error_message'] = f"Exception: {str(e)}"
            print(f"❌ {module_name} - 导入异常: {e}")
        
        return result
    
    def validate_all_modules(self) -> Dict[str, Any]:
        """验证所有核心模块"""
        print("🔍 开始验证核心模块导入...")
        
        successful_imports = 0
        failed_imports = 0
        
        for module_name in self.core_modules:
            result = self.validate_module_import(module_name)
            self.validation_results.append(result)
            
            if result['import_success']:
                successful_imports += 1
            else:
                failed_imports += 1
        
        return {
            'total_modules': len(self.core_modules),
            'successful_imports': successful_imports,
            'failed_imports': failed_imports,
            'success_rate': successful_imports / len(self.core_modules) * 100
        }
    
    def test_api_service_manager(self) -> Dict[str, Any]:
        """测试API服务管理器功能"""
        test_result = {
            'test_name': 'API服务管理器',
            'success': False,
            'error_message': None
        }
        
        try:
            from api_management.core.task_based_ai_service_manager import get_simplified_ai_service_manager
            
            # 创建管理器实例
            manager = get_simplified_ai_service_manager()
            
            # 检查基本方法
            if hasattr(manager, 'call_ai') and hasattr(manager, 'get_system_status'):
                test_result['success'] = True
                print("✅ API服务管理器功能测试通过")
            else:
                test_result['error_message'] = "缺少必要的方法"
                print("❌ API服务管理器缺少必要方法")
                
        except Exception as e:
            test_result['error_message'] = str(e)
            print(f"❌ API服务管理器测试失败: {e}")
        
        return test_result
    
    def test_database_connection(self) -> Dict[str, Any]:
        """测试数据库连接功能"""
        test_result = {
            'test_name': '数据库连接',
            'success': False,
            'error_message': None
        }
        
        try:
            from api_management.sqlite_storage.api_account_database import APIAccountDatabase
            
            # 创建数据库实例
            db = APIAccountDatabase()
            
            # 检查基本方法
            if hasattr(db, 'get_api_configurations') and hasattr(db, 'set_daily_limit'):
                test_result['success'] = True
                print("✅ 数据库连接功能测试通过")
            else:
                test_result['error_message'] = "缺少必要的方法"
                print("❌ 数据库连接缺少必要方法")
                
        except Exception as e:
            test_result['error_message'] = str(e)
            print(f"❌ 数据库连接测试失败: {e}")
        
        return test_result
    
    def test_config_loading(self) -> Dict[str, Any]:
        """测试配置加载功能"""
        test_result = {
            'test_name': '配置加载',
            'success': False,
            'error_message': None
        }
        
        try:
            from unified_config_manager import UnifiedConfigManager
            
            # 测试配置获取
            config = UnifiedConfigManager.get_config("api_category_mappings", {})
            
            if isinstance(config, dict):
                test_result['success'] = True
                print("✅ 配置加载功能测试通过")
            else:
                test_result['error_message'] = "配置格式不正确"
                print("❌ 配置加载格式错误")
                
        except Exception as e:
            test_result['error_message'] = str(e)
            print(f"❌ 配置加载测试失败: {e}")
        
        return test_result
    
    def run_functional_tests(self) -> List[Dict[str, Any]]:
        """运行功能测试"""
        print("\n🧪 开始功能测试...")
        
        test_results = []
        
        # API服务管理器测试
        test_results.append(self.test_api_service_manager())
        
        # 数据库连接测试
        test_results.append(self.test_database_connection())
        
        # 配置加载测试
        test_results.append(self.test_config_loading())
        
        return test_results
    
    def generate_validation_report(self) -> Dict[str, Any]:
        """生成验证报告"""
        # 模块导入统计
        import_stats = self.validate_all_modules()
        
        # 功能测试结果
        functional_results = self.run_functional_tests()
        
        # 失败的模块
        failed_modules = [
            r['module_name'] for r in self.validation_results 
            if not r['import_success']
        ]
        
        # 成功的功能测试
        successful_tests = [
            r['test_name'] for r in functional_results 
            if r['success']
        ]
        
        report = {
            'validation_metadata': {
                'validation_time': datetime.now().isoformat(),
                'base_directory': self.base_dir,
                'validator_version': 'v1.0'
            },
            'import_statistics': import_stats,
            'functional_test_results': functional_results,
            'detailed_results': self.validation_results,
            'summary': {
                'overall_success': import_stats['failed_imports'] == 0 and 
                                 all(r['success'] for r in functional_results),
                'failed_modules': failed_modules,
                'successful_tests': successful_tests,
                'recommendations': self._generate_recommendations(failed_modules, functional_results)
            }
        }
        
        return report
    
    def _generate_recommendations(self, failed_modules: List[str], functional_results: List[Dict[str, Any]]) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        if failed_modules:
            recommendations.append(f"修复失败的模块导入: {', '.join(failed_modules)}")
            recommendations.append("检查这些模块的相对导入是否完全修复")
        
        failed_tests = [r for r in functional_results if not r['success']]
        if failed_tests:
            recommendations.append("修复失败的功能测试")
            for test in failed_tests:
                recommendations.append(f"- {test['test_name']}: {test['error_message']}")
        
        if not failed_modules and not failed_tests:
            recommendations.append("所有验证通过！可以启动服务器进行最终测试")
        
        return recommendations
    
    def print_summary(self, report: Dict[str, Any]):
        """打印验证摘要"""
        print("\n" + "="*60)
        print("📊 导入验证摘要")
        print("="*60)
        
        import_stats = report['import_statistics']
        print(f"📦 总模块数: {import_stats['total_modules']}")
        print(f"✅ 成功导入: {import_stats['successful_imports']}")
        print(f"❌ 失败导入: {import_stats['failed_imports']}")
        print(f"📈 成功率: {import_stats['success_rate']:.1f}%")
        
        functional_results = report['functional_test_results']
        successful_tests = len([r for r in functional_results if r['success']])
        print(f"🧪 功能测试: {successful_tests}/{len(functional_results)} 通过")
        
        if report['summary']['overall_success']:
            print(f"\n🎉 验证完全成功！所有模块和功能都正常工作")
        else:
            print(f"\n⚠️  发现问题，需要进一步修复")
            for rec in report['summary']['recommendations']:
                print(f"   💡 {rec}")
        
        print("="*60)

def main():
    """主函数"""
    print("🚀 启动导入验证器")
    
    # 创建验证器实例
    validator = ImportValidator()
    
    # 设置环境
    validator.setup_environment()
    
    # 生成验证报告
    report = validator.generate_validation_report()
    
    # 打印摘要
    validator.print_summary(report)
    
    # 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"import_validation_report_{timestamp}.json"
    
    try:
        import json
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"\n📄 验证报告已保存: {report_file}")
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")
    
    return report['summary']['overall_success']

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
