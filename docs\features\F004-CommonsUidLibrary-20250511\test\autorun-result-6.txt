/home/<USER>/apps/jdk-21.0.5/bin/java -javaagent:/home/<USER>/apps/idea-IU-243.23654.189/lib/idea_rt.jar=33665:/home/<USER>/apps/idea-IU-243.23654.189/bin -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -classpath /media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/test-classes:/media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/classes:/home/<USER>/works/project/mvnRepository/com/xfvape/uid/uid-generator/0.0.4-RELEASE/uid-generator-0.0.4-RELEASE.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-core/6.2.6/spring-core-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-jcl/6.2.6/spring-jcl-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-beans/6.2.6/spring-beans-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/mybatis/mybatis/3.2.3/mybatis-3.2.3.jar:/home/<USER>/works/project/mvnRepository/org/mybatis/mybatis-spring/1.2.4/mybatis-spring-1.2.4.jar:/home/<USER>/works/project/mvnRepository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/home/<USER>/works/project/mvnRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/log4j-over-slf4j/2.0.17/log4j-over-slf4j-2.0.17.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-context/6.2.6/spring-context-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-aop/6.2.6/spring-aop-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-expression/6.2.6/spring-expression-6.2.6.jar:/home/<USER>/works/project/mvnRepository/io/micrometer/micrometer-observation/1.14.6/micrometer-observation-1.14.6.jar:/home/<USER>/works/project/mvnRepository/io/micrometer/micrometer-commons/1.14.6/micrometer-commons-1.14.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-jdbc/6.2.6/spring-jdbc-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-tx/6.2.6/spring-tx-6.2.6.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-annotations/2.18.3/jackson-annotations-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-core/2.18.3/jackson-core-2.18.3.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter-test/3.4.5/spring-boot-starter-test-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter/3.4.5/spring-boot-starter-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot/3.4.5/spring-boot-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-autoconfigure/3.4.5/spring-boot-autoconfigure-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter-logging/3.4.5/spring-boot-starter-logging-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/home/<USER>/works/project/mvnRepository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/home/<USER>/works/project/mvnRepository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/home/<USER>/works/project/mvnRepository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-test/3.4.5/spring-boot-test-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-test-autoconfigure/3.4.5/spring-boot-test-autoconfigure-3.4.5.jar:/home/<USER>/works/project/mvnRepository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/home/<USER>/works/project/mvnRepository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/home/<USER>/works/project/mvnRepository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/home/<USER>/works/project/mvnRepository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/home/<USER>/works/project/mvnRepository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/home/<USER>/works/project/mvnRepository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/home/<USER>/works/project/mvnRepository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/home/<USER>/works/project/mvnRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-test/6.2.6/spring-test-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/xmlunit/xmlunit-core/2.10.0/xmlunit-core-2.10.0.jar:/home/<USER>/works/project/mvnRepository/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar:/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar:/home/<USER>/works/project/mvnRepository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/home/<USER>/works/project/mvnRepository/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-launcher/1.11.4/junit-platform-launcher-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/testcontainers/1.19.7/testcontainers-1.19.7.jar:/home/<USER>/works/project/mvnRepository/junit/junit/4.13.2/junit-4.13.2.jar:/home/<USER>/works/project/mvnRepository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/home/<USER>/works/project/mvnRepository/org/apache/commons/commons-compress/1.24.0/commons-compress-1.24.0.jar:/home/<USER>/works/project/mvnRepository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/home/<USER>/works/project/mvnRepository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-api/3.3.6/docker-java-api-3.3.6.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-transport-zerodep/3.3.6/docker-java-transport-zerodep-3.3.6.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-transport/3.3.6/docker-java-transport-3.3.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/postgresql/1.19.7/postgresql-1.19.7.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/jdbc/1.20.6/jdbc-1.20.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/database-commons/1.20.6/database-commons-1.20.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/junit-jupiter/1.19.7/junit-jupiter-1.19.7.jar:/home/<USER>/works/project/mvnRepository/org/openjdk/jmh/jmh-core/1.37/jmh-core-1.37.jar:/home/<USER>/works/project/mvnRepository/net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar:/home/<USER>/works/project/mvnRepository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/home/<USER>/works/project/mvnRepository/org/openjdk/jmh/jmh-generator-annprocess/1.37/jmh-generator-annprocess-1.37.jar:/home/<USER>/works/project/mvnRepository/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/home/<USER>/works/project/mvnRepository/org/postgresql/postgresql/42.7.5/postgresql-42.7.5.jar:/home/<USER>/works/project/mvnRepository/org/checkerframework/checker-qual/3.48.3/checker-qual-3.48.3.jar:/home/<USER>/works/project/mvnRepository/com/github/oshi/oshi-core/6.5.0/oshi-core-6.5.0.jar:/home/<USER>/works/project/mvnRepository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/home/<USER>/works/project/mvnRepository/net/java/dev/jna/jna-platform/5.14.0/jna-platform-5.14.0.jar:/home/<USER>/works/project/mvnRepository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar:/home/<USER>/works/project/mvnRepository/ch/qos/logback/logback-classic/1.5.16/logback-classic-1.5.16.jar:/home/<USER>/works/project/mvnRepository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/home/<USER>/works/project/mvnRepository/org/xkongkit/xkongkit-core/1.0.0-SNAPSHOT/xkongkit-core-1.0.0-SNAPSHOT.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/kryo/5.5.0/kryo-5.5.0.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/home/<USER>/works/project/mvnRepository/org/lmdbjava/lmdbjava/0.9.1/lmdbjava-0.9.1.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-constants/0.10.4/jnr-constants-0.10.4.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-ffi/2.2.17/jnr-ffi-2.2.17.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jffi/1.3.13/jffi-1.3.13.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jffi/1.3.13/jffi-1.3.13-native.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-commons/9.7.1/asm-commons-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-a64asm/1.0.0/jnr-a64asm-1.0.0.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-x86asm/1.0.2/jnr-x86asm-1.0.2.jar:/home/<USER>/works/project/mvnRepository/com/tokyocabinet/tokyocabinet/1.24/tokyocabinet-1.24.jar:/home/<USER>/works/project/mvnRepository/org/jackson/databind/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.3/jackson-datatype-jsr310-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/github/luben/zstd-jni/1.5.5-3/zstd-jni-1.5.5-3.jar:/home/<USER>/works/project/mvnRepository/com/github/ben-manes/caffeine/caffeine/3.1.8/caffeine-3.1.8.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/home/<USER>/works/project/mvnRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/home/<USER>/works/project/mvnRepository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-context/1.70.0/grpc-context-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-impl/0.31.1/opencensus-impl-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-impl-core/0.31.1/opencensus-impl-core-0.31.1.jar:/home/<USER>/works/project/mvnRepository/com/lmax/disruptor/3.4.2/disruptor-3.4.2.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-census/1.71.0/grpc-census-1.71.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-api/1.70.0/grpc-api-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-contrib-grpc-metrics/0.31.1/opencensus-contrib-grpc-metrics-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-services/1.71.0/grpc-services-1.71.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-stub/1.70.0/grpc-stub-1.70.0.jar:/home/<USER>/works/project/mvnRepository/org/codehaus/mojo/animal-sniffer-annotations/1.24/animal-sniffer-annotations-1.24.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-core/1.70.0/grpc-core-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar:/home/<USER>/works/project/mvnRepository/io/perfmark/perfmark-api/0.27.0/perfmark-api-0.27.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-protobuf/1.70.0/grpc-protobuf-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/protobuf/protobuf-java/3.25.6/protobuf-java-3.25.6.jar:/home/<USER>/works/project/mvnRepository/com/google/api/grpc/proto-google-common-protos/2.46.0/proto-google-common-protos-2.46.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-protobuf-lite/1.70.0/grpc-protobuf-lite-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-util/1.70.0/grpc-util-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/protobuf/protobuf-java-util/3.25.6/protobuf-java-util-3.25.6.jar:/home/<USER>/works/project/mvnRepository/com/google/errorprone/error_prone_annotations/2.30.0/error_prone_annotations-2.30.0.jar:/home/<USER>/works/project/mvnRepository/com/google/code/gson/gson/2.11.0/gson-2.11.0.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-x86_64.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-common/4.1.119.Final/netty-common-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-buffer/4.1.119.Final/netty-buffer-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport/4.1.119.Final/netty-transport-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-resolver/4.1.119.Final/netty-resolver-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-native-unix-common/4.1.119.Final/netty-transport-native-unix-common-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-classes-epoll/4.1.119.Final/netty-transport-classes-epoll-4.1.119.Final.jar org.xkong.cloud.commons.uid.TestRunner
===== 运行单元测试 =====
20:11:20,916 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-classic version 1.5.16
20:11:20,916 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-core version 1.5.18
20:11:20,916 |-WARN in ch.qos.logback.classic.LoggerContext[default] - Versions of logback-core and logback-classic are different!
20:11:20,925 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7d900ecf - Here is a list of configurators discovered as a service, by rank: 
20:11:20,925 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7d900ecf -   org.springframework.boot.logging.logback.RootLogLevelConfigurator
20:11:20,925 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7d900ecf - They will be invoked in order until ExecutionStatus.DO_NOT_INVOKE_NEXT_IF_ANY is returned.
20:11:20,925 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7d900ecf - Constructed configurator of type class org.springframework.boot.logging.logback.RootLogLevelConfigurator
20:11:21,181 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7d900ecf - org.springframework.boot.logging.logback.RootLogLevelConfigurator.configure() call lasted 1 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
20:11:21,181 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7d900ecf - Trying to configure with ch.qos.logback.classic.joran.SerializedModelConfigurator
20:11:21,190 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7d900ecf - Constructed configurator of type class ch.qos.logback.classic.joran.SerializedModelConfigurator
20:11:21,192 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.scmo]
20:11:21,194 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback.scmo]
20:11:21,194 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7d900ecf - ch.qos.logback.classic.joran.SerializedModelConfigurator.configure() call lasted 4 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
20:11:21,194 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7d900ecf - Trying to configure with ch.qos.logback.classic.util.DefaultJoranConfigurator
20:11:21,203 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7d900ecf - Constructed configurator of type class ch.qos.logback.classic.util.DefaultJoranConfigurator
20:11:21,204 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-test.xml] at [file:/media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/test-classes/logback-test.xml]
20:11:22,070 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [CONSOLE]
20:11:22,070 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
20:11:22,077 |-INFO in ch.qos.logback.core.model.processor.ImplicitModelHandler - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
20:11:22,133 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - BEWARE: Writing to the console can be very slow. Avoid logging to the 
20:11:22,133 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - console in production environments, especially in high volume systems.
20:11:22,133 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - See also https://logback.qos.ch/codes.html#slowConsole
20:11:22,133 |-INFO in ch.qos.logback.classic.model.processor.RootLoggerModelHandler - Setting level of ROOT logger to INFO
20:11:22,133 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[ROOT]
20:11:22,136 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [org.xkong.cloud.commons.uid] to DEBUG
20:11:22,136 |-INFO in ch.qos.logback.core.model.processor.DefaultProcessor@6f01b95f - End of configuration.
20:11:22,137 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@4007f65e - Registering current configuration as safe fallback point
20:11:22,137 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7d900ecf - ch.qos.logback.classic.util.DefaultJoranConfigurator.configure() call lasted 934 milliseconds. ExecutionStatus=DO_NOT_INVOKE_NEXT_IF_ANY

2025-05-22 20:11:22.797 [main] INFO  o.x.c.c.u.f.MachineFingerprints - 使用OSHI库成功获取系统信息
2025-05-22 20:11:26.118 [main] INFO  o.x.c.c.u.f.MachineFingerprints - 已收集机器特征码: {"os_arch":"amd64","os_name":"Linux","fingerprint_hash":"a786543839e216899233d14607b55f87785e42729661e39640f0eed8954da167","hostname":"long-VirtualBox","mac_addresses":["2A:7****:EE:59","08:0****:3A:87"],"os_version":"5.4.0-91-generic"}
Mockito is currently self-attaching to enable the inline-mock-maker. This will no longer work in future releases of the JDK. Please add Mockito as an agent to your build what is described in Mockito's documentation: https://javadoc.io/doc/org.mockito/mockito-core/latest/org/mockito/Mockito.html#0.3
WARNING: A Java agent has been loaded dynamically (/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
Java HotSpot(TM) 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classpath has been appended
2025-05-22 20:11:28.738 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 20:11:28.831 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 20:11:28.843 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 20:11:29.090 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 20:11:29.091 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 20:11:29.097 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 20:11:29.102 [main] DEBUG o.x.c.c.u.c.KeyManagementService - 使用NativePRNGNonBlocking算法的SecureRandom
2025-05-22 20:11:29.112 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已为应用 test-app 环境 test-env 创建新的 test-key-type 类型密钥
2025-05-22 20:11:29.118 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 20:11:29.125 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 20:11:29.132 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 20:11:29.135 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已清除密钥缓存
2025-05-22 20:11:29.156 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 20:11:29.157 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-22 20:11:29.158 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 20:11:29.158 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-22 20:11:29.160 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 20:11:29.160 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-22 20:11:29.161 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-22 20:11:29.161 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-22 20:11:29.369 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 20:11:29.369 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-22 20:11:29.374 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 20:11:29.375 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-22 20:11:29.375 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-22 20:11:29.376 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-22 20:11:29.377 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 已清除所有验证缓存
2025-05-22 20:11:29.584 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 20:11:29.585 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 20:11:29.585 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 20:11:29.585 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 20:11:29.586 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:29.589 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 20:11:29.590 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 20:11:29.590 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 20:11:29.591 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: main
2025-05-22 20:11:29.592 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: main
2025-05-22 20:11:29.593 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: main
2025-05-22 20:11:29.593 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: main
2025-05-22 20:11:29.593 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: main
2025-05-22 20:11:29.593 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: main
2025-05-22 20:11:29.593 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: main
2025-05-22 20:11:29.626 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:29.698 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 20:11:29.699 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 20:11:29.699 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 20:11:29.699 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 20:11:29.699 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:29.699 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 20:11:29.706 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-22 20:11:29.707 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: main
2025-05-22 20:11:29.707 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 20:11:29.707 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 20:11:29.724 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 20:11:29.724 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 20:11:29.725 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 20:11:29.725 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 20:11:29.725 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:29.725 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 20:11:29.725 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 无法为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 20:11:29.726 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 20:11:29.726 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取工作机器ID时发生异常，当前线程: main, 异常: 无法为实例 12345 分配工作机器ID
java.lang.IllegalStateException: 无法为实例 12345 分配工作机器ID
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.getWorkerId(PersistentInstanceWorkerIdAssigner.java:161)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.lambda$2(PersistentInstanceWorkerIdAssignerTest.java:248)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3128)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.testGetWorkerId_MaxReached(PersistentInstanceWorkerIdAssignerTest.java:248)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:767)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$8(TestMethodTestDescriptor.java:217)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:213)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:138)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.SessionPerRequestLauncher.execute(SessionPerRequestLauncher.java:63)
	at org.xkong.cloud.commons.uid.TestRunner.runTests(TestRunner.java:88)
	at org.xkong.cloud.commons.uid.TestRunner.runUnitTests(TestRunner.java:53)
	at org.xkong.cloud.commons.uid.TestRunner.main(TestRunner.java:27)
2025-05-22 20:11:29.739 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 20:11:29.739 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 20:11:29.739 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 20:11:29.739 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 20:11:29.739 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:29.740 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 20:11:29.741 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 插入新的工作机器ID失败，可能存在并发冲突，工作机器ID: 42
2025-05-22 20:11:29.743 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，实例ID: null
2025-05-22 20:11:29.743 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 无法为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 20:11:29.743 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 20:11:29.744 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取工作机器ID时发生异常，当前线程: main, 异常: 无法为实例 12345 分配工作机器ID
java.lang.IllegalStateException: 无法为实例 12345 分配工作机器ID
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.getWorkerId(PersistentInstanceWorkerIdAssigner.java:161)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.testGetWorkerId_AllocateNew(PersistentInstanceWorkerIdAssignerTest.java:185)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:767)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$8(TestMethodTestDescriptor.java:217)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:213)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:138)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.SessionPerRequestLauncher.execute(SessionPerRequestLauncher.java:63)
	at org.xkong.cloud.commons.uid.TestRunner.runTests(TestRunner.java:88)
	at org.xkong.cloud.commons.uid.TestRunner.runUnitTests(TestRunner.java:53)
	at org.xkong.cloud.commons.uid.TestRunner.main(TestRunner.java:27)
2025-05-22 20:11:29.752 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: main
2025-05-22 20:11:29.752 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: false -> false，当前线程: main
2025-05-22 20:11:29.752 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: main
2025-05-22 20:11:29.752 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: main
2025-05-22 20:11:29.752 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 20:11:29.752 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 20:11:29.752 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 20:11:29.753 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 20:11:29.753 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:29.753 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 20:11:29.753 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 20:11:29.753 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 20:11:29.753 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: main
2025-05-22 20:11:29.764 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 20:11:29.764 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 20:11:29.764 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 20:11:29.765 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 20:11:29.765 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:29.765 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 20:11:29.765 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 20:11:29.765 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 20:11:29.767 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 20:11:29.769 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 20:11:29.774 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 20:11:29.774 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 20:11:29.774 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 20:11:29.776 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 20:11:29.776 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:29.776 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 20:11:29.776 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 20:11:29.776 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 20:11:29.782 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 20:11:29.782 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 20:11:29.782 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 20:11:29.782 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 20:11:29.782 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:29.783 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 20:11:29.783 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 20:11:29.783 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 20:11:29.785 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放成功
2025-05-22 20:11:29.792 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 20:11:29.792 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 20:11:29.792 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 20:11:29.792 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 20:11:29.792 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:29.792 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 20:11:29.792 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 20:11:29.792 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 20:11:29.793 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: main
2025-05-22 20:11:29.793 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: main
2025-05-22 20:11:29.793 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: main
2025-05-22 20:11:29.793 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: main
2025-05-22 20:11:29.793 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: main
2025-05-22 20:11:29.793 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: main
2025-05-22 20:11:29.793 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: main
2025-05-22 20:11:29.908 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign

Test run finished after 11800 ms
[         5 containers found      ]
[         0 containers skipped    ]
[         5 containers started    ]
[         0 containers aborted    ]
[         5 containers successful ]
[         0 containers failed     ]
[        25 tests found           ]
[         0 tests skipped         ]
[        25 tests started         ]
[         0 tests aborted         ]
[        22 tests successful      ]
[         3 tests failed          ]


===== 运行集成测试 =====
2025-05-22 20:11:30.127 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:30.127 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:11:30.127 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:11:30.127 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:30.127 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:30.127 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:11:30.127 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:11:30.158 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:30.159 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:30.159 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:11:30.159 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:11:30.159 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:11:30.413 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:30.413 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:11:30.413 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:11:30.413 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:30.413 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:30.413 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:11:30.413 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:11:30.414 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:30.415 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:30.415 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:11:30.415 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:11:30.415 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:11:30.567 [main] INFO  o.testcontainers.images.PullPolicy - Image pull policy will be performed by: DefaultPullPolicy()
2025-05-22 20:11:30.570 [main] INFO  o.t.utility.ImageNameSubstitutor - Image name substitution will be performed by: DefaultImageNameSubstitutor (composite of 'ConfigurationFileImageNameSubstitutor' and 'PrefixingImageNameSubstitutor')
2025-05-22 20:11:31.066 [main] INFO  o.t.d.DockerClientProviderStrategy - Loaded org.testcontainers.dockerclient.UnixSocketClientProviderStrategy from ~/.testcontainers.properties, will try it first
2025-05-22 20:11:31.778 [main] INFO  o.t.d.DockerClientProviderStrategy - Found Docker environment with local Unix socket (unix:///var/run/docker.sock)
2025-05-22 20:11:31.780 [main] INFO  o.testcontainers.DockerClientFactory - Docker host IP address is localhost
2025-05-22 20:11:31.819 [main] INFO  o.testcontainers.DockerClientFactory - Connected to docker: 
  Server Version: 28.1.1
  API Version: 1.49
  Operating System: Linux Mint 20.3
  Total Memory: 7960 MB
2025-05-22 20:11:31.898 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Creating container for image: testcontainers/ryuk:0.6.0
2025-05-22 20:11:31.927 [main] INFO  o.t.utility.RegistryAuthLocator - Failure when attempting to lookup auth config. Please ignore if you don't have images in an authenticated registry. Details: (dockerImageName: testcontainers/ryuk:0.6.0, configFile: /home/<USER>/.docker/config.json, configEnv: DOCKER_AUTH_CONFIG). Falling back to docker-java default behaviour. Exception message: Status 404: No config supplied. Checked in order: /home/<USER>/.docker/config.json (file not found), DOCKER_AUTH_CONFIG (not set)
2025-05-22 20:11:32.469 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Container testcontainers/ryuk:0.6.0 is starting: c39c821582f4c85c7461484f2eda2e0af7460895fe06718ff8b1954c1d505c12
2025-05-22 20:11:34.842 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Container testcontainers/ryuk:0.6.0 started in PT2.943609382S
2025-05-22 20:11:34.848 [main] INFO  o.t.utility.RyukResourceReaper - Ryuk started - will monitor and terminate Testcontainers containers on JVM exit
2025-05-22 20:11:34.848 [main] INFO  o.testcontainers.DockerClientFactory - Checking the system...
2025-05-22 20:11:34.849 [main] INFO  o.testcontainers.DockerClientFactory - ✔︎ Docker server version should be at least 1.6.0
2025-05-22 20:11:34.850 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 20:11:36.185 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 1ca5760e91a8b05a4c359728504c64ec83e6d18bb16c04cdb46b6ebee11117b9
2025-05-22 20:11:45.217 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT10.366972813S
2025-05-22 20:11:45.217 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 20:11:45.415 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-22 20:11:45.881 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@260d48f5
2025-05-22 20:11:45.883 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-22 20:11:45.942 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 20:11:45.946 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 20:11:45.947 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 20:11:45.949 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 20:11:45.950 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 20:11:45.952 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-22 20:11:45.988 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 20:11:45.988 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-22 20:11:45.991 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-22 20:11:46.040 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 20:11:46.041 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-22 20:11:46.048 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 20:11:46.063 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 20:11:46.063 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 20:11:46.063 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 20:11:47.806 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 20:11:48.992 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 6ef4a728b6541d3ed3e08833a2d6eee54a4bf57f669e42c7810ea177b01da68b
2025-05-22 20:11:49.469 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.471 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.472 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.472 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.472 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.472 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.472 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.473 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:49.696 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.698 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.698 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.698 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.698 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.698 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.698 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.699 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:49.721 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.734 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.750 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.751 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.752 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.752 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.752 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.752 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.752 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.752 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:49.772 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.774 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.775 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.775 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.775 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.775 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.775 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.776 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:49.789 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.790 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.791 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.791 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.791 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.791 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.791 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:11:49.791 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:49.976 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.051 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:11:50.051 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:11:50.051 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.051 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.051 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.051 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.061 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.061 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:50.062 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:11:50.062 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.062 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:11:50.200 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.200 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:11:50.200 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:11:50.200 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.200 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.200 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.200 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.202 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.202 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:50.202 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.203 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-22 20:11:50.203 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:11:50.203 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.203 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:11:50.253 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.253 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:11:50.253 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:11:50.253 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.253 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.253 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.253 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.254 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.255 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:50.255 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:11:50.255 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.255 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:11:50.276 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.276 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:11:50.277 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:11:50.277 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.277 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.277 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.277 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.279 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.279 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:50.279 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:11:50.279 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.279 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:11:50.292 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.292 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:11:50.292 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:11:50.292 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.292 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.292 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.292 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.294 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:11:50.294 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:11:50.295 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:11:50.295 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:11:50.295 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:11:54.582 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT6.775771381S
2025-05-22 20:11:54.582 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 20:11:54.616 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-05-22 20:11:54.643 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@e97f115
2025-05-22 20:11:54.643 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-05-22 20:11:54.643 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 20:11:54.644 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 20:11:54.644 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 20:11:54.647 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 不存在，正在自动创建
2025-05-22 20:11:54.648 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 已成功创建
2025-05-22 20:11:54.648 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 20:11:54.650 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-22 20:11:54.663 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 20:11:54.663 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-22 20:11:54.664 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-22 20:11:54.673 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 20:11:54.673 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-22 20:11:54.674 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 20:11:54.682 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 20:11:54.682 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 20:11:54.682 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 20:11:54.691 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Starting...
2025-05-22 20:11:54.710 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-3 - Added connection org.postgresql.jdbc.PgConnection@43755e2f
2025-05-22 20:11:54.710 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Start completed.
2025-05-22 20:11:54.710 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 20:11:54.711 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 20:11:54.711 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 20:11:54.712 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 20:11:54.712 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 20:11:54.714 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 20:11:54.714 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:11:54.715 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 20:11:54.715 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:11:54.717 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 20:11:54.717 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 20:11:54.718 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 20:11:54.718 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:11:54.719 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 20:11:54.719 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:11:54.720 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 20:11:54.721 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 20:11:54.722 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 20:11:54.722 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:11:54.722 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 20:11:54.722 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:11:54.723 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 20:11:54.724 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 20:11:54.724 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 20:11:54.727 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Starting...
2025-05-22 20:11:54.743 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-4 - Added connection org.postgresql.jdbc.PgConnection@45c423b3
2025-05-22 20:11:54.744 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Start completed.
2025-05-22 20:11:54.744 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 20:11:54.744 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 20:11:54.745 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 20:11:54.746 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 20:11:54.746 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 20:11:54.748 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 20:11:54.748 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:11:54.748 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 20:11:54.748 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:11:54.750 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 20:11:54.750 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 20:11:54.751 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 20:11:54.751 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:11:54.753 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 20:11:54.753 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:11:54.754 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 20:11:54.754 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 20:11:54.754 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 20:11:54.755 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:11:54.755 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 20:11:54.755 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:11:54.756 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 20:11:54.756 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 20:11:54.756 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 20:11:56.174 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 20:11:56.942 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 14123df5ffe5e77dc600cd16d42430f381c4a1b2e17de83ec79f24038a336e21
2025-05-22 20:12:01.244 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.069432501S
2025-05-22 20:12:01.244 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 20:12:01.250 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-5 - Starting...
2025-05-22 20:12:01.276 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-5 - Added connection org.postgresql.jdbc.PgConnection@779228dc
2025-05-22 20:12:01.277 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-5 - Start completed.
2025-05-22 20:12:01.277 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 20:12:01.277 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 20:12:01.277 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 20:12:01.279 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 不存在，正在自动创建
2025-05-22 20:12:01.280 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 已成功创建
2025-05-22 20:12:01.280 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 20:12:01.282 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-22 20:12:01.293 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 20:12:01.293 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-22 20:12:01.294 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-22 20:12:01.302 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 20:12:01.302 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-22 20:12:01.303 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 20:12:01.324 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 20:12:01.324 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 20:12:01.324 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 20:12:01.329 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件清理关闭钩子
2025-05-22 20:12:01.329 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/expiry-test6229425462796610656, 描述: 创建的临时目录
2025-05-22 20:12:01.330 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/expiry-test-211113628847561150464, 描述: 创建的临时目录
2025-05-22 20:12:01.330 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/expiry-test6229425462796610656/instance-id
2025-05-22 20:12:01.330 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 20:12:01.333 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 20:12:01.333 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 20:12:01.354 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 1
2025-05-22 20:12:01.389 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/expiry-test6229425462796610656/instance-id
2025-05-22 20:12:01.392 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-6 - Starting...
2025-05-22 20:12:01.410 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-6 - Added connection org.postgresql.jdbc.PgConnection@632d4cf2
2025-05-22 20:12:01.411 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-6 - Start completed.
2025-05-22 20:12:01.411 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 20:12:01.411 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 20:12:01.411 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 20:12:01.413 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 20:12:01.413 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 20:12:01.414 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 20:12:01.414 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:01.415 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 20:12:01.415 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:01.417 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 20:12:01.417 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 20:12:01.418 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 20:12:01.418 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:01.419 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 20:12:01.419 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:01.420 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 20:12:01.421 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 20:12:01.421 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 20:12:01.421 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:01.422 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 20:12:01.422 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:01.425 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 20:12:01.425 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 20:12:01.425 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 20:12:01.426 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/lease-test366851117260239309, 描述: 创建的临时目录
2025-05-22 20:12:01.426 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/lease-test366851117260239309/instance-id
2025-05-22 20:12:01.426 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 20:12:01.429 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 20:12:01.429 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 20:12:01.431 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 2
2025-05-22 20:12:01.432 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/lease-test366851117260239309/instance-id
2025-05-22 20:12:01.433 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 20:12:01.433 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 20:12:01.434 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 20:12:01.434 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 2 分配工作机器ID，当前线程: main
2025-05-22 20:12:01.434 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 2 是否已分配工作机器ID
2025-05-22 20:12:01.435 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 20:12:01.441 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 0，实例ID: 2
2025-05-22 20:12:01.442 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 2 分配了新的工作机器ID: 0，当前线程: main
2025-05-22 20:12:01.443 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 20:12:01.444 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 20:12:02.433 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: worker-id-lease-renewal
2025-05-22 20:12:02.434 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 2，schemaName: infra_uid，当前线程: worker-id-lease-renewal
2025-05-22 20:12:02.436 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:12:02.440 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '5 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 2]，当前线程: worker-id-lease-renewal
2025-05-22 20:12:02.442 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: worker-id-lease-renewal
2025-05-22 20:12:02.443 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: worker-id-lease-renewal
2025-05-22 20:12:02.450 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-22 20:12:02.454 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-7 - Starting...
2025-05-22 20:12:02.473 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-7 - Added connection org.postgresql.jdbc.PgConnection@1de85972
2025-05-22 20:12:02.473 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-7 - Start completed.
2025-05-22 20:12:02.473 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 20:12:02.474 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 20:12:02.474 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 20:12:02.476 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 20:12:02.476 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 20:12:02.477 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 20:12:02.477 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:02.478 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 20:12:02.478 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:02.480 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 20:12:02.480 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 20:12:02.481 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 20:12:02.481 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:02.482 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 20:12:02.482 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:02.483 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 20:12:02.483 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 20:12:02.484 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 20:12:02.484 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:02.485 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 20:12:02.485 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:02.486 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 20:12:02.486 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 20:12:02.486 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 20:12:02.487 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test4425452840772122052, 描述: 创建的临时目录
2025-05-22 20:12:02.487 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test4425452840772122052/instance-id
2025-05-22 20:12:02.487 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 20:12:02.489 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 20:12:02.489 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 20:12:02.491 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 3
2025-05-22 20:12:02.492 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test4425452840772122052/instance-id
2025-05-22 20:12:02.492 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 20:12:02.493 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 20:12:02.493 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 20:12:02.493 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 3 分配工作机器ID，当前线程: main
2025-05-22 20:12:02.493 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 3 是否已分配工作机器ID
2025-05-22 20:12:02.494 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 20:12:02.496 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 3
2025-05-22 20:12:02.497 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 3 分配了新的工作机器ID: 0，当前线程: main
2025-05-22 20:12:02.497 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 20:12:02.497 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 20:12:02.498 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-22 20:12:02.501 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-8 - Starting...
2025-05-22 20:12:02.519 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-8 - Added connection org.postgresql.jdbc.PgConnection@6181bc4a
2025-05-22 20:12:02.520 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-8 - Start completed.
2025-05-22 20:12:02.520 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 20:12:02.521 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 20:12:02.521 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 20:12:02.523 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 20:12:02.523 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 20:12:02.525 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 20:12:02.525 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:02.526 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 20:12:02.526 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:02.528 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 20:12:02.528 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 20:12:02.529 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 20:12:02.529 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:02.530 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 20:12:02.530 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:02.531 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 20:12:02.531 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 20:12:02.533 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 20:12:02.533 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:02.533 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 20:12:02.534 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:02.535 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 20:12:02.535 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 20:12:02.535 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 20:12:02.536 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test17788280237907805450, 描述: 创建的临时目录
2025-05-22 20:12:02.536 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test17788280237907805450, 描述: 并发测试基础目录
2025-05-22 20:12:02.538 [pool-1-thread-1] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test17788280237907805450/instance-id-0, 描述: 并发测试实例 0 的ID文件
2025-05-22 20:12:02.539 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test17788280237907805450/instance-id-0
2025-05-22 20:12:02.539 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 20:12:02.541 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 20:12:02.541 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 20:12:02.542 [pool-1-thread-2] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test17788280237907805450/instance-id-1, 描述: 并发测试实例 1 的ID文件
2025-05-22 20:12:02.542 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test17788280237907805450/instance-id-1
2025-05-22 20:12:02.542 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 20:12:02.543 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 4
2025-05-22 20:12:02.543 [pool-1-thread-3] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test17788280237907805450/instance-id-2, 描述: 并发测试实例 2 的ID文件
2025-05-22 20:12:02.543 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test17788280237907805450/instance-id-2
2025-05-22 20:12:02.543 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 20:12:02.543 [pool-1-thread-4] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test17788280237907805450/instance-id-3, 描述: 并发测试实例 3 的ID文件
2025-05-22 20:12:02.544 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test17788280237907805450/instance-id-3
2025-05-22 20:12:02.544 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 20:12:02.544 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test17788280237907805450/instance-id-0
2025-05-22 20:12:02.544 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-1
2025-05-22 20:12:02.544 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-1
2025-05-22 20:12:02.544 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-1
2025-05-22 20:12:02.544 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 4 分配工作机器ID，当前线程: pool-1-thread-1
2025-05-22 20:12:02.544 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 4 是否已分配工作机器ID
2025-05-22 20:12:02.545 [pool-1-thread-5] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test17788280237907805450/instance-id-4, 描述: 并发测试实例 4 的ID文件
2025-05-22 20:12:02.545 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test17788280237907805450/instance-id-4
2025-05-22 20:12:02.545 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 20:12:02.546 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 20:12:02.546 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 20:12:02.548 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-1
2025-05-22 20:12:02.548 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 20:12:02.548 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 20:12:02.550 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 5
2025-05-22 20:12:02.550 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 20:12:02.550 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 20:12:02.551 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test17788280237907805450/instance-id-2
2025-05-22 20:12:02.551 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-3
2025-05-22 20:12:02.551 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-3
2025-05-22 20:12:02.552 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-3
2025-05-22 20:12:02.552 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 5 分配工作机器ID，当前线程: pool-1-thread-3
2025-05-22 20:12:02.552 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 5 是否已分配工作机器ID
2025-05-22 20:12:02.552 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 4
2025-05-22 20:12:02.553 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 4 分配了新的工作机器ID: 0，当前线程: pool-1-thread-1
2025-05-22 20:12:02.553 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 20:12:02.553 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-1
2025-05-22 20:12:02.553 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: pool-1-thread-1
2025-05-22 20:12:02.553 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 4，schemaName: infra_uid，当前线程: pool-1-thread-1
2025-05-22 20:12:02.554 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 6
2025-05-22 20:12:02.555 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test17788280237907805450/instance-id-1
2025-05-22 20:12:02.555 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-2
2025-05-22 20:12:02.555 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-2
2025-05-22 20:12:02.555 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-2
2025-05-22 20:12:02.555 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 6 分配工作机器ID，当前线程: pool-1-thread-2
2025-05-22 20:12:02.555 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 6 是否已分配工作机器ID
2025-05-22 20:12:02.556 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 20:12:02.556 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 20:12:02.557 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-3
2025-05-22 20:12:02.561 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 1，实例ID: 5
2025-05-22 20:12:02.562 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 5 分配了新的工作机器ID: 1，当前线程: pool-1-thread-3
2025-05-22 20:12:02.562 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 1
2025-05-22 20:12:02.562 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-3
2025-05-22 20:12:02.562 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 1，当前线程: pool-1-thread-3
2025-05-22 20:12:02.562 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 5，schemaName: infra_uid，当前线程: pool-1-thread-3
2025-05-22 20:12:02.563 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-2
2025-05-22 20:12:02.563 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 7
2025-05-22 20:12:02.565 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 8
2025-05-22 20:12:02.565 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: pool-1-thread-1
2025-05-22 20:12:02.565 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 4]，当前线程: pool-1-thread-1
2025-05-22 20:12:02.565 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test17788280237907805450/instance-id-4
2025-05-22 20:12:02.566 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-1
2025-05-22 20:12:02.566 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test17788280237907805450/instance-id-3
2025-05-22 20:12:02.568 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-4
2025-05-22 20:12:02.568 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-4
2025-05-22 20:12:02.568 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-4
2025-05-22 20:12:02.568 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 8 分配工作机器ID，当前线程: pool-1-thread-4
2025-05-22 20:12:02.568 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 8 是否已分配工作机器ID
2025-05-22 20:12:02.568 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: pool-1-thread-1
2025-05-22 20:12:02.569 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-5
2025-05-22 20:12:02.569 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-5
2025-05-22 20:12:02.569 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-5
2025-05-22 20:12:02.569 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 7 分配工作机器ID，当前线程: pool-1-thread-5
2025-05-22 20:12:02.569 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 7 是否已分配工作机器ID
2025-05-22 20:12:02.569 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-4
2025-05-22 20:12:02.570 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 当前状态: ACTIVE，当前线程: pool-1-thread-3
2025-05-22 20:12:02.570 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [1, 5]，当前线程: pool-1-thread-3
2025-05-22 20:12:02.570 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-3
2025-05-22 20:12:02.571 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 租约续约成功，当前线程: pool-1-thread-3
2025-05-22 20:12:02.571 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-5
2025-05-22 20:12:02.576 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 2，实例ID: 7
2025-05-22 20:12:02.576 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 7 分配了新的工作机器ID: 2，当前线程: pool-1-thread-5
2025-05-22 20:12:02.576 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 2
2025-05-22 20:12:02.576 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-5
2025-05-22 20:12:02.576 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 2，当前线程: pool-1-thread-5
2025-05-22 20:12:02.576 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 7，schemaName: infra_uid，当前线程: pool-1-thread-5
2025-05-22 20:12:02.577 [pool-1-thread-2] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 检测到并发插入，最大工作机器ID已从 1 变为 2，重新计算工作机器ID
2025-05-22 20:12:02.577 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 当前状态: ACTIVE，当前线程: pool-1-thread-5
2025-05-22 20:12:02.577 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [2, 7]，当前线程: pool-1-thread-5
2025-05-22 20:12:02.578 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 3，实例ID: 6
2025-05-22 20:12:02.578 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-5
2025-05-22 20:12:02.578 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 6 分配了新的工作机器ID: 3，当前线程: pool-1-thread-2
2025-05-22 20:12:02.578 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 3
2025-05-22 20:12:02.579 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-2
2025-05-22 20:12:02.579 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 3，当前线程: pool-1-thread-2
2025-05-22 20:12:02.579 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 6，schemaName: infra_uid，当前线程: pool-1-thread-2
2025-05-22 20:12:02.579 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 租约续约成功，当前线程: pool-1-thread-5
2025-05-22 20:12:02.579 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 当前状态: ACTIVE，当前线程: pool-1-thread-2
2025-05-22 20:12:02.579 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [3, 6]，当前线程: pool-1-thread-2
2025-05-22 20:12:02.580 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-2
2025-05-22 20:12:02.580 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 释放成功
2025-05-22 20:12:02.580 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 租约续约成功，当前线程: pool-1-thread-2
2025-05-22 20:12:02.581 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 释放成功
2025-05-22 20:12:02.581 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-22 20:12:02.582 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 释放成功
2025-05-22 20:12:02.582 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 8
2025-05-22 20:12:02.583 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 8 分配了新的工作机器ID: 0，当前线程: pool-1-thread-4
2025-05-22 20:12:02.583 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 20:12:02.583 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-4
2025-05-22 20:12:02.583 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: pool-1-thread-4
2025-05-22 20:12:02.583 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 8，schemaName: infra_uid，当前线程: pool-1-thread-4
2025-05-22 20:12:02.583 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: pool-1-thread-4
2025-05-22 20:12:02.583 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 8]，当前线程: pool-1-thread-4
2025-05-22 20:12:02.584 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-4
2025-05-22 20:12:02.584 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: pool-1-thread-4
2025-05-22 20:12:02.585 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-22 20:12:02.589 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 开始清理临时文件，当前注册的临时文件数量: 0
2025-05-22 20:12:04.350 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 20:12:04.901 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 3c7d7319360b53fb4b8a6629ae10cae984af85f278c39c3c45b5b45beeea6dc2
2025-05-22 20:12:09.469 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.470 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.470 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.470 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.470 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.470 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.470 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.471 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:09.696 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.696 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.696 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.696 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.696 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.696 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.696 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.697 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:09.718 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.734 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.750 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.750 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.750 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.750 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.750 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.750 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.750 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.751 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:09.773 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.773 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.773 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.773 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.773 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.773 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.773 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.774 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:09.790 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.790 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.790 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.790 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.790 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.790 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.790 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:12:09.790 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:09.972 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:09.972 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:12:09.972 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:12:09.974 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:09.974 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:09.974 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:09.974 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:09.976 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:09.976 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:12:09.977 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:12:09.977 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:12:09.977 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:12:10.198 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.198 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:12:10.198 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:12:10.198 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.198 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.198 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:10.198 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:10.200 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.200 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:12:10.200 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.201 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-22 20:12:10.201 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:12:10.201 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:12:10.201 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:12:10.252 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.252 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:12:10.252 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:12:10.252 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.252 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.252 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:10.252 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:10.253 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.254 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:12:10.257 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:12:10.257 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:12:10.257 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:12:10.274 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.274 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:12:10.274 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:12:10.274 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.274 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.274 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:10.274 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:10.275 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.275 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:12:10.275 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:12:10.275 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:12:10.275 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:12:10.291 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.291 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:12:10.291 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:12:10.291 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.291 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.291 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:10.291 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:10.292 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:10.292 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:12:10.293 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:12:10.293 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:12:10.293 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:12:10.596 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT6.24648878S
2025-05-22 20:12:10.596 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 20:12:10.598 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-9 - Starting...
2025-05-22 20:12:10.617 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-9 - Added connection org.postgresql.jdbc.PgConnection@5613247e
2025-05-22 20:12:10.617 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-9 - Start completed.
2025-05-22 20:12:10.617 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 20:12:10.618 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 20:12:10.618 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 20:12:10.620 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 不存在，正在自动创建
2025-05-22 20:12:10.620 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 已成功创建
2025-05-22 20:12:10.620 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 20:12:10.622 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-22 20:12:10.631 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 20:12:10.631 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-22 20:12:10.632 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-22 20:12:10.639 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 20:12:10.639 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-22 20:12:10.640 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 20:12:10.646 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 20:12:10.646 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 20:12:10.646 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 20:12:10.646 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: key-test, 环境: test, Schema: infra_uid
2025-05-22 20:12:10.648 [main] DEBUG o.x.c.c.u.c.KeyManagementService - 使用NativePRNGNonBlocking算法的SecureRandom
2025-05-22 20:12:10.649 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已为应用 key-test 环境 test 创建新的 test-key-type 类型密钥
2025-05-22 20:12:12.171 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 20:12:12.868 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 1e833672b21d9683201e4a00bee0a7903fa44013278ab5e0263ea15465a96e85
2025-05-22 20:12:18.416 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT6.245046757S
2025-05-22 20:12:18.416 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 20:12:18.418 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-10 - Starting...
2025-05-22 20:12:18.438 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-10 - Added connection org.postgresql.jdbc.PgConnection@147c00aa
2025-05-22 20:12:18.438 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-10 - Start completed.
2025-05-22 20:12:18.445 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-22 20:12:18.447 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: test_schema
2025-05-22 20:12:18.460 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: non_existent_schema
2025-05-22 20:12:18.463 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-11 - Starting...
2025-05-22 20:12:18.479 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-11 - Added connection org.postgresql.jdbc.PgConnection@794cb26b
2025-05-22 20:12:18.479 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-11 - Start completed.
2025-05-22 20:12:18.479 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-22 20:12:18.483 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-22 20:12:18.487 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.non_existent_table
2025-05-22 20:12:18.490 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-12 - Starting...
2025-05-22 20:12:18.505 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-12 - Added connection org.postgresql.jdbc.PgConnection@750e2d33
2025-05-22 20:12:18.505 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-12 - Start completed.
2025-05-22 20:12:18.505 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-22 20:12:18.506 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 验证表结构是否包含所需的列: test_schema.test_table
2025-05-22 20:12:18.506 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-22 20:12:18.510 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table
2025-05-22 20:12:18.514 [main] WARN  o.x.c.c.u.m.PostgreSQLMetadataService - 表 test_schema.test_table 缺少必需的列: name
2025-05-22 20:12:18.517 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-13 - Starting...
2025-05-22 20:12:18.532 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-13 - Added connection org.postgresql.jdbc.PgConnection@100eeedc
2025-05-22 20:12:18.532 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-13 - Start completed.
2025-05-22 20:12:18.532 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-22 20:12:18.534 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-22 20:12:18.534 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: test_schema
2025-05-22 20:12:18.535 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - Schema test_schema 验证通过
2025-05-22 20:12:18.535 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-22 20:12:18.538 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 test_schema.test_table 验证通过
2025-05-22 20:12:18.538 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-22 20:12:18.539 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 test_schema.test_table 验证通过
2025-05-22 20:12:18.539 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table
2025-05-22 20:12:18.543 [main] ERROR o.x.c.c.u.s.i.UidValidationServiceImpl - 表结构验证失败: 表 test_schema.test_table 缺少必需的列 name
2025-05-22 20:12:18.545 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-14 - Starting...
2025-05-22 20:12:18.560 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-14 - Added connection org.postgresql.jdbc.PgConnection@9f1ca74
2025-05-22 20:12:18.561 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-14 - Start completed.
2025-05-22 20:12:18.561 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-22 20:12:18.562 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table

Test run finished after 49910 ms
[         6 containers found      ]
[         0 containers skipped    ]
[         6 containers started    ]
[         0 containers aborted    ]
[         6 containers successful ]
[         0 containers failed     ]
[        14 tests found           ]
[         0 tests skipped         ]
[        14 tests started         ]
[         0 tests aborted         ]
[         6 tests successful      ]
[         8 tests failed          ]


===== 运行简化测试 =====

1. 基本功能测试
2025-05-22 20:12:20.050 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 20:12:20.912 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 2ee91408b2d7f1878f97f456bd424b8c827035487936fe52521acfe1fe9585e8
2025-05-22 20:12:26.004 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.953463743S
2025-05-22 20:12:26.004 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 20:12:26.005 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-15 - Starting...
2025-05-22 20:12:26.024 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-15 - Added connection org.postgresql.jdbc.PgConnection@35e75f7a
2025-05-22 20:12:26.025 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-15 - Start completed.
2025-05-22 20:12:26.025 [main] INFO  o.x.c.c.u.test.PostgresTestContainer - 正在执行数据库初始化脚本: init-schema.sql
2025-05-22 20:12:26.082 [main] INFO  o.x.c.c.u.test.PostgresTestContainer - 数据库初始化脚本执行完成
2025-05-22 20:12:26.083 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-16 - Starting...
2025-05-22 20:12:26.099 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-16 - Added connection org.postgresql.jdbc.PgConnection@*************-05-22 20:12:26.100 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-16 - Start completed.
2025-05-22 20:12:26.100 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 20:12:26.101 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 20:12:26.101 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 20:12:26.102 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 20:12:26.102 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 20:12:26.104 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 20:12:26.104 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:26.105 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 20:12:26.105 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:26.106 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 20:12:26.106 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 20:12:26.107 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 20:12:26.107 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:26.108 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 20:12:26.108 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:26.109 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 20:12:26.109 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 20:12:26.110 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 20:12:26.118 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 20:12:26.118 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 20:12:26.118 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 20:12:26.140 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 1
2025-05-22 20:12:26.142 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 1 在数据库中不存在
2025-05-22 20:12:26.142 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 20:12:26.145 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 20:12:26.145 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 20:12:26.146 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 1
2025-05-22 20:12:26.148 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/test-instance-id.dat
实例ID: 1
2025-05-22 20:12:26.149 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 20:12:26.149 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 20:12:26.149 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 20:12:26.149 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 1 分配工作机器ID，当前线程: main
2025-05-22 20:12:26.149 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 1 是否已分配工作机器ID
2025-05-22 20:12:26.150 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 20:12:26.154 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 0，实例ID: 1
2025-05-22 20:12:26.155 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配了新的工作机器ID: 0，当前线程: main
2025-05-22 20:12:26.155 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 20:12:26.155 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
工作机器ID: 0
2025-05-22 20:12:26.156 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功

2. 实例恢复测试
2025-05-22 20:12:26.158 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-17 - Starting...
2025-05-22 20:12:26.175 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-17 - Added connection org.postgresql.jdbc.PgConnection@3a70575
2025-05-22 20:12:26.175 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-17 - Start completed.
2025-05-22 20:12:26.175 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 20:12:26.176 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 20:12:26.176 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 20:12:26.177 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 20:12:26.177 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 20:12:26.179 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 20:12:26.179 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:26.179 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 20:12:26.179 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:26.181 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 20:12:26.181 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 20:12:26.182 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 20:12:26.182 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:26.183 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 20:12:26.183 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:26.184 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 20:12:26.184 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 20:12:26.184 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 20:12:26.184 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:26.185 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 20:12:26.185 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:26.186 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 20:12:26.186 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 20:12:26.186 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
===== 第一次运行 =====
2025-05-22 20:12:26.188 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 2
2025-05-22 20:12:26.189 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 2 在数据库中不存在
2025-05-22 20:12:26.189 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 20:12:26.190 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 20:12:26.190 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 20:12:26.191 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 2
2025-05-22 20:12:26.193 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
第一次运行 - 实例ID: 2

===== 第二次运行 (从文件恢复) =====
2025-05-22 20:12:26.195 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 2
2025-05-22 20:12:26.196 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 2 在数据库中存在，更新实例信息
2025-05-22 20:12:26.196 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 2 的最后活动时间
第二次运行 - 实例ID: 2
从文件恢复结果: 成功

===== 删除文件后运行 (从特征码恢复) =====
2025-05-22 20:12:26.197 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
2025-05-22 20:12:26.197 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 20:12:26.198 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 通过指纹哈希精确匹配找到实例ID: 2
2025-05-22 20:12:26.198 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 成功恢复实例ID: 2
2025-05-22 20:12:26.198 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 2 的最后活动时间
2025-05-22 20:12:26.200 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
删除文件后运行 - 实例ID: 2
从特征码恢复结果: 成功

3. 租约管理测试
2025-05-22 20:12:26.202 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - ===== 租约管理测试开始 =====
2025-05-22 20:12:26.202 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1. 准备测试环境 - 初始化数据源
2025-05-22 20:12:26.202 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.1 检查Docker是否正常运行
2025-05-22 20:12:29.470 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.471 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.471 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.471 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.471 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.471 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.471 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.472 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:29.695 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.695 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.696 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.696 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.696 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.696 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.696 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.696 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:29.718 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.734 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.749 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.750 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.750 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.750 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.750 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.750 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.750 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.751 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:29.772 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.773 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.773 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.773 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.773 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.773 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.773 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.773 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:29.791 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.792 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.792 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.792 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.792 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.792 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.792 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 20:12:29.792 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:29.973 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:29.973 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:12:29.973 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:12:29.973 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:29.973 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:29.973 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:29.973 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:29.976 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:29.976 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:12:29.976 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:12:29.977 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:12:29.977 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:12:30.198 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.198 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:12:30.198 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:12:30.198 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.198 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.198 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:30.198 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:30.204 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.204 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:12:30.205 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.206 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-22 20:12:30.206 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:12:30.206 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:12:30.206 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:12:30.252 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.252 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:12:30.252 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:12:30.252 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.252 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.252 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:30.252 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:30.253 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.253 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:12:30.253 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:12:30.253 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:12:30.253 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:12:30.274 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.274 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:12:30.274 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:12:30.274 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.274 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.274 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:30.274 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:30.275 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.275 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:12:30.275 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:12:30.275 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:12:30.275 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:12:30.293 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.293 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 20:12:30.294 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 20:12:30.294 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.294 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.294 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:30.294 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 20:12:30.294 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 20:12:30.294 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 20:12:30.295 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 20:12:30.295 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 20:12:30.295 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 20:12:38.123 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - Docker正常运行
2025-05-22 20:12:38.123 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.2 创建PostgresTestContainer实例
2025-05-22 20:12:38.124 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - PostgresTestContainer实例创建成功
2025-05-22 20:12:38.124 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.3 检查容器是否正在运行
2025-05-22 20:12:38.130 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 容器运行状态: true
2025-05-22 20:12:38.130 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.4 获取JDBC连接信息
2025-05-22 20:12:38.130 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - JDBC URL: ******************************************************
2025-05-22 20:12:38.130 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 用户名: test
2025-05-22 20:12:38.130 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 密码: test
2025-05-22 20:12:38.130 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.5 获取数据源
2025-05-22 20:12:38.130 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-18 - Starting...
2025-05-22 20:12:38.149 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-18 - Added connection org.postgresql.jdbc.PgConnection@67ceeffd
2025-05-22 20:12:38.150 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-18 - Start completed.
2025-05-22 20:12:38.150 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据源获取成功
2025-05-22 20:12:38.150 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.6 测试数据库连接
2025-05-22 20:12:38.150 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库连接成功
2025-05-22 20:12:38.150 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库产品名称: PostgreSQL
2025-05-22 20:12:38.150 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库产品版本: 17.4 (Debian 17.4-1.pgdg120+2)
2025-05-22 20:12:38.150 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库驱动名称: PostgreSQL JDBC Driver
2025-05-22 20:12:38.150 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库驱动版本: 42.7.5
2025-05-22 20:12:38.150 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.7 创建JdbcTemplate和TransactionTemplate
2025-05-22 20:12:38.150 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - JdbcTemplate和TransactionTemplate创建成功
2025-05-22 20:12:38.150 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.8 初始化表结构
2025-05-22 20:12:38.150 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 20:12:38.151 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 20:12:38.151 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 20:12:38.153 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 20:12:38.153 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 20:12:38.154 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 20:12:38.154 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:38.155 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 20:12:38.155 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:38.157 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 20:12:38.157 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 20:12:38.158 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 20:12:38.158 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:38.159 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 20:12:38.159 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:38.160 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 20:12:38.160 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 20:12:38.161 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 20:12:38.161 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 20:12:38.162 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 20:12:38.162 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 20:12:38.163 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 20:12:38.163 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 20:12:38.163 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 20:12:38.163 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.2 验证表结构是否创建成功
2025-05-22 20:12:38.170 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 已创建的表: [{table_name=instance_registry}, {table_name=worker_id_assignment}, {table_name=encryption_key}]
2025-05-22 20:12:38.170 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 2. 创建 KeyManagementService
2025-05-22 20:12:38.170 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: lease-test, 环境: test, Schema: infra_uid
2025-05-22 20:12:38.170 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - KeyManagementService 创建成功，加密状态: false
2025-05-22 20:12:38.171 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 3. 创建实例管理器
2025-05-22 20:12:38.172 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 3
2025-05-22 20:12:38.174 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 3 在数据库中不存在
2025-05-22 20:12:38.174 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 20:12:38.175 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到精确匹配，且恢复策略为创建新实例，跳过模糊匹配
2025-05-22 20:12:38.175 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 20:12:38.177 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 3
2025-05-22 20:12:38.180 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/lease-test-instance-id.dat
2025-05-22 20:12:38.181 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 2.1 实例管理器创建成功，实例ID: 3
2025-05-22 20:12:38.188 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 已注册的实例: [{instance_unique_id=3, application_name=lease-test, environment=test, instance_group=default, status=ACTIVE, first_registered_at=2025-05-22 20:12:38.176015, last_seen_at=2025-05-22 20:12:38.176015, custom_metadata={"os_arch": "amd64", "os_name": "Linux", "hostname": "long-VirtualBox", "os_version": "5.4.0-91-generic", "mac_addresses": ["2A:76:62:37:EE:59", "08:00:27:D6:3A:87"], "fingerprint_hash": "a786543839e216899233d14607b55f87785e42729661e39640f0eed8954da167"}}]
2025-05-22 20:12:38.188 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 3. 创建工作机器ID分配器
2025-05-22 20:12:38.189 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 4. 开始分配工作机器ID
2025-05-22 20:12:38.189 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 20:12:38.189 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 20:12:38.189 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 20:12:38.189 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 3 分配工作机器ID，当前线程: main
2025-05-22 20:12:38.189 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 3 是否已分配工作机器ID
2025-05-22 20:12:38.190 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 20:12:38.192 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 3
2025-05-22 20:12:38.192 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 3 分配了新的工作机器ID: 0，当前线程: main
2025-05-22 20:12:38.192 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 20:12:38.192 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 20:12:38.192 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 分配的工作机器ID: 0
2025-05-22 20:12:38.194 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 工作机器ID分配记录: [{worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:38.190808, lease_expires_at=2025-05-22 20:13:38.190808, released_at=2025-05-22 20:12:26.156002}]
2025-05-22 20:12:38.194 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5. 测试租约续约 - 通过心跳线程自动续约
2025-05-22 20:12:38.194 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.1 等待心跳线程自动续约前，当前状态
2025-05-22 20:12:38.195 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:38.190808, lease_expires_at=2025-05-22 20:13:38.190808, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:38.195 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-22 20:12:39.200 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.1 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:38.190808, lease_expires_at=2025-05-22 20:13:38.190808, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:39.200 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.2 等待心跳线程自动续约前，当前状态
2025-05-22 20:12:39.202 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:38.190808, lease_expires_at=2025-05-22 20:13:38.190808, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:39.203 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-22 20:12:40.212 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.2 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:38.190808, lease_expires_at=2025-05-22 20:13:38.190808, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:40.212 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.3 等待心跳线程自动续约前，当前状态
2025-05-22 20:12:40.221 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:38.190808, lease_expires_at=2025-05-22 20:13:38.190808, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:40.222 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-22 20:12:41.225 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.3 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:38.190808, lease_expires_at=2025-05-22 20:13:38.190808, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:41.226 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6. 测试手动续约
2025-05-22 20:12:41.226 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.1 手动续约前，当前状态
2025-05-22 20:12:41.227 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:38.190808, lease_expires_at=2025-05-22 20:13:38.190808, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:41.227 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-22 20:12:41.227 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-22 20:12:41.227 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-22 20:12:41.229 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-22 20:12:41.229 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-22 20:12:41.231 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-22 20:12:41.232 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-22 20:12:41.234 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.1 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:41.230304, lease_expires_at=2025-05-22 20:13:41.230304, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:41.234 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-22 20:12:42.235 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.2 手动续约前，当前状态
2025-05-22 20:12:42.238 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:41.230304, lease_expires_at=2025-05-22 20:13:41.230304, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:42.238 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-22 20:12:42.238 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-22 20:12:42.238 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-22 20:12:42.239 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-22 20:12:42.240 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-22 20:12:42.241 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-22 20:12:42.242 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-22 20:12:42.243 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.2 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:42.240763, lease_expires_at=2025-05-22 20:13:42.240763, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:42.244 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-22 20:12:43.245 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.3 手动续约前，当前状态
2025-05-22 20:12:43.248 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:42.240763, lease_expires_at=2025-05-22 20:13:42.240763, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:43.248 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-22 20:12:43.248 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-22 20:12:43.248 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-22 20:12:43.250 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-22 20:12:43.250 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-22 20:12:43.251 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-22 20:12:43.252 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-22 20:12:43.253 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.3 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:43.25075, lease_expires_at=2025-05-22 20:13:43.25075, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:43.253 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-22 20:12:44.256 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7. 测试租约失效后的重新分配
2025-05-22 20:12:44.256 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.1 模拟租约失效 - 将工作机器ID状态设置为AVAILABLE
2025-05-22 20:12:44.260 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 更新结果: 影响行数 = 1
2025-05-22 20:12:44.261 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 更新后状态: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:43.25075, lease_expires_at=2025-05-22 20:13:43.25075, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:44.262 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.2 等待2秒，让租约续约线程发现租约失效
2025-05-22 20:12:46.262 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.3 尝试重新获取工作机器ID
2025-05-22 20:12:46.262 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 直接返回已缓存的工作机器ID: 0
2025-05-22 20:12:46.262 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 重新分配的工作机器ID: 0
2025-05-22 20:12:46.265 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 新工作机器ID记录: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:43.25075, lease_expires_at=2025-05-22 20:13:43.25075, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:46.265 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 8. 关闭心跳线程
2025-05-22 20:12:46.267 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放失败
2025-05-22 20:12:46.268 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 关闭后状态: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-22 20:12:38.190808, last_renewed_at=2025-05-22 20:12:43.25075, lease_expires_at=2025-05-22 20:13:43.25075, released_at=2025-05-22 20:12:26.156002}
2025-05-22 20:12:46.269 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - ===== 租约管理测试完成 =====
2025-05-22 20:12:48.344 [Thread-7] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 20:12:48.346 [Thread-15] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 20:12:48.348 [Thread-8] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 20:12:48.348 [Thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 20:12:48.348 [Thread-8] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 20:12:48.348 [Thread-10] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 20:12:48.349 [Thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 20:12:48.349 [Thread-6] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 20:12:48.349 [Thread-6] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 20:12:48.361 [temp-file-cleanup-hook] DEBUG o.x.c.commons.uid.util.TempFileUtils - 执行临时文件清理关闭钩子
2025-05-22 20:12:48.361 [temp-file-cleanup-hook] DEBUG o.x.c.commons.uid.util.TempFileUtils - 开始清理临时文件，当前注册的临时文件数量: 0
2025-05-22 20:12:48.362 [Thread-0] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 20:12:48.362 [Thread-18] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 20:12:48.362 [Thread-0] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 20:12:48.362 [Thread-16] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 20:12:48.362 [Thread-12] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 20:12:48.363 [Thread-1] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 20:12:48.363 [Thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 20:12:48.364 [Thread-5] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 20:12:48.364 [Thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 20:12:48.364 [Thread-14] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 20:12:48.364 [Thread-11] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 20:12:48.365 [Thread-13] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 20:12:48.366 [Thread-19] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-18 - Failed to validate connection org.postgresql.jdbc.PgConnection@67ceeffd (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-22 20:12:48.368 [Thread-19] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-18 - Failed to validate connection org.postgresql.jdbc.PgConnection@2d645ae7 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-22 20:12:48.390 [Thread-4] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 20:12:48.390 [Thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 20:13:18.397 [Thread-19] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放失败: Failed to obtain JDBC Connection
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:653)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:972)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1016)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1026)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.releaseWorkerId(PersistentInstanceWorkerIdAssigner.java:525)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.lambda$2(PersistentInstanceWorkerIdAssigner.java:85)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.sql.SQLTransientConnectionException: HikariPool-18 - Connection is not available, request timed out after 30023ms (total=0, active=0, idle=0, waiting=0)
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:686)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:179)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:144)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:99)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 7 common frames omitted
Caused by: org.postgresql.util.PSQLException: Connection to localhost:32774 refused. Check that the hostname and port are correct and that the postmaster is accepting TCP/IP connections.
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:352)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273)
	at org.postgresql.Driver.makeConnection(Driver.java:446)
	at org.postgresql.Driver.connect(Driver.java:298)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:724)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:703)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	... 1 common frames omitted
Caused by: java.net.ConnectException: 拒绝连接
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260)
	at org.postgresql.core.PGStream.<init>(PGStream.java:121)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268)
	... 14 common frames omitted
2025-05-22 20:13:18.398 [Thread-19] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 0

进程已结束，退出代码为 0
