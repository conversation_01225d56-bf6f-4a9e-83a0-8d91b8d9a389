# 项目经理九宫格交互界面设计 - 架构风险检测专用界面

## 📋 文档信息
- **文档版本**: V1.0
- **创建日期**: 2025-01-16
- **设计目标**: 基于ace现有九宫格模板，定制项目经理架构风险检测与代码生成的专用交互界面
- **复用基础**: `tools/ace/src/web_interface/templates/nine_grid.html`

## 🎨 界面设计概览

### **九宫格布局定制**

```
┌─────────────┬─────────────┬─────────────┐
│   区域1-2   │             │    区域3    │
│ 项目状态监控 │             │ 项目风险分类 │
│  + 进度条   │             │ + 修复建议  │
├─────────────┤    区域5    ├─────────────┤
│   区域4     │             │    区域6    │
│ AI协同状态  │ 项目管理决策日志 │ 项目监控详细报告│
│ + 认知负荷  │ + 实时输出  │ + 证据链   │
├─────────────┼─────────────┼─────────────┤
│   区域7     │   区域8     │    区域9    │
│ 项目架构可视化  │ 项目经理工作台  │ 项目文档生成状态│
│ + 依赖图   │ 核心控制台  │ + 质量评估 │
└─────────────┴─────────────┴─────────────┘
```

## 🎯 区域8核心交互设计

### **布局结构**
```html
<div class="grid-area grid-area-8">
    <!-- 项目文档目录输入区 -->
    <div class="work-directory-section">
        <label>📁 项目文档目录</label>
        <div class="input-group">
            <input type="text" id="work-directory" placeholder="输入项目文档目录路径..." />
            <button id="browse-btn">📂</button>
        </div>
        <div class="directory-status" id="directory-status">
            <span class="status-indicator"></span>
            <span class="status-text">请选择项目文档目录</span>
        </div>
    </div>

    <!-- 文档矛盾检测输入区 -->
    <div class="document-contradiction-section">
        <label>📄 文档矛盾检测</label>
        <div class="input-group">
            <input type="text" id="document-paths" placeholder="输入00-xx格式文档路径，多个用逗号分隔..." />
            <button id="browse-docs-btn">📂</button>
        </div>
        <div class="document-status" id="document-status">
            <span class="status-indicator"></span>
            <span class="status-text">可选：检测文档间矛盾</span>
        </div>
    </div>

    <!-- 详细区域 -->
    <div class="detail-area" id="detail-area">
        <div class="detail-header">
            <span class="detail-title">检测详情</span>
            <div class="detail-controls">
                <button class="detail-btn" data-view="risks">风险</button>
                <button class="detail-btn" data-view="suggestions">建议</button>
                <button class="detail-btn" data-view="code">代码</button>
                <button class="detail-btn" data-view="documents">文档矛盾</button>
            </div>
        </div>
        <div class="detail-content" id="detail-content">
            <div class="placeholder">选择工作目录后开始检测</div>
        </div>
    </div>

    <!-- 核心控制按钮 -->
    <div class="control-buttons">
        <button id="check-btn" class="primary-btn" disabled>
            <span class="btn-icon">🔍</span>
            <span class="btn-text">项目检测</span>
            <div class="btn-progress" style="display: none;"></div>
        </button>
        <button id="generate-btn" class="secondary-btn" disabled>
            <span class="btn-icon">⚡</span>
            <span class="btn-text">项目生成</span>
            <div class="btn-progress" style="display: none;"></div>
        </button>
    </div>
</div>
```

### **交互流程设计**

**1. 项目文档目录选择**
```javascript
// 目录输入和验证
document.getElementById('work-directory').addEventListener('input', function(e) {
    const path = e.target.value;
    validateWorkDirectory(path);
});

function validateWorkDirectory(path) {
    // 发送验证请求到后端
    fetch('/api/architecture-risk/validate-directory', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({directory: path})
    })
    .then(response => response.json())
    .then(data => {
        updateDirectoryStatus(data);
        toggleCheckButton(data.valid);
    });
}
```

**2. 项目检测按钮交互**
```javascript
document.getElementById('check-btn').addEventListener('click', function() {
    const workDir = document.getElementById('work-directory').value;
    
    // 开始检测
    startRiskDetection(workDir);
});

function startRiskDetection(directory) {
    // 禁用按钮，显示进度
    const checkBtn = document.getElementById('check-btn');
    checkBtn.disabled = true;
    checkBtn.querySelector('.btn-progress').style.display = 'block';
    
    // 建立WebSocket连接接收实时进度
    const ws = new WebSocket('ws://localhost:5000/ws/risk-detection');
    
    ws.onmessage = function(event) {
        const data = JSON.parse(event.data);
        updateDetectionProgress(data);
    };
    
    // 发送检测请求
    fetch('/api/architecture-risk/detect', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({directory: directory})
    });
}
```

**3. 项目生成按钮交互**
```javascript
document.getElementById('generate-btn').addEventListener('click', function() {
    const detectionResults = getDetectionResults();
    
    // 开始代码生成
    startCodeGeneration(detectionResults);
});

function startCodeGeneration(results) {
    // 显示生成选项对话框
    showGenerationOptions(results, function(options) {
        // 执行代码生成
        executeCodeGeneration(options);
    });
}
```

## 🔍 其他区域定制设计

### **区域1-2: 项目状态监控**

**功能**: 显示项目检测进度和整体状态
```html
<div class="detection-status-section">
    <div class="status-overview">
        <div class="status-item">
            <span class="status-label">检测状态</span>
            <span class="status-value" id="detection-status">待开始</span>
        </div>
        <div class="status-item">
            <span class="status-label">进度</span>
            <div class="progress-bar">
                <div class="progress-fill" id="detection-progress" style="width: 0%"></div>
            </div>
        </div>
    </div>
    
    <div class="risk-summary" id="risk-summary" style="display: none;">
        <div class="risk-count critical">
            <span class="count" id="critical-count">0</span>
            <span class="label">致命</span>
        </div>
        <div class="risk-count high">
            <span class="count" id="high-count">0</span>
            <span class="label">严重</span>
        </div>
        <div class="risk-count medium">
            <span class="count" id="medium-count">0</span>
            <span class="label">重要</span>
        </div>
        <div class="risk-count low">
            <span class="count" id="low-count">0</span>
            <span class="label">隐蔽</span>
        </div>
    </div>
</div>
```

### **区域3: 项目风险分类展示**

**功能**: 按风险等级分类显示项目检测结果
```html
<div class="risk-classification-section">
    <div class="risk-category" data-level="critical">
        <div class="category-header">
            <span class="category-icon">🚨</span>
            <span class="category-title">致命级风险</span>
            <span class="category-count" id="critical-risks-count">0</span>
        </div>
        <div class="category-content" id="critical-risks-list">
            <!-- 动态填充风险项 -->
        </div>
    </div>
    
    <div class="risk-category" data-level="high">
        <div class="category-header">
            <span class="category-icon">⚠️</span>
            <span class="category-title">严重级风险</span>
            <span class="category-count" id="high-risks-count">0</span>
        </div>
        <div class="category-content" id="high-risks-list">
            <!-- 动态填充风险项 -->
        </div>
    </div>
    
    <!-- 重要级和隐蔽级风险类似结构 -->
</div>
```

### **区域5: 项目管理决策日志**

**功能**: 实时显示项目管理算法的思维过程
```html
<div class="algorithm-thinking-section">
    <div class="thinking-header">
        <span class="thinking-title">算法思维过程</span>
        <div class="thinking-controls">
            <button class="thinking-btn" data-filter="all">全部</button>
            <button class="thinking-btn" data-filter="detection">检测</button>
            <button class="thinking-btn" data-filter="analysis">分析</button>
        </div>
    </div>
    
    <div class="thinking-log" id="thinking-log">
        <!-- 实时日志输出 -->
        <div class="log-entry" data-type="detection">
            <span class="log-time">[14:17:30]</span>
            <span class="log-content">开始解析架构文档...</span>
        </div>
    </div>
</div>
```

### **区域6: 检测详细报告**

**功能**: 显示详细的检测报告和证据链
```html
<div class="detailed-report-section">
    <div class="report-tabs">
        <button class="tab-btn active" data-tab="overview">概览</button>
        <button class="tab-btn" data-tab="dependencies">依赖</button>
        <button class="tab-btn" data-tab="security">安全</button>
        <button class="tab-btn" data-tab="performance">性能</button>
    </div>
    
    <div class="report-content">
        <div class="tab-panel active" id="overview-panel">
            <!-- 检测概览 -->
        </div>
        <div class="tab-panel" id="dependencies-panel">
            <!-- 依赖分析详情 -->
        </div>
        <!-- 其他面板 -->
    </div>
</div>
```

### **区域7: 架构可视化**

**功能**: 显示架构依赖图和风险热点
```html
<div class="architecture-visualization-section">
    <div class="visualization-header">
        <span class="viz-title">架构依赖图</span>
        <div class="viz-controls">
            <button class="viz-btn" data-view="dependencies">依赖</button>
            <button class="viz-btn" data-view="risks">风险</button>
            <button class="viz-btn" data-view="layers">分层</button>
        </div>
    </div>
    
    <div class="visualization-canvas" id="architecture-canvas">
        <!-- 使用D3.js或类似库渲染架构图 -->
    </div>
    
    <div class="visualization-legend">
        <div class="legend-item">
            <span class="legend-color critical"></span>
            <span class="legend-text">致命风险</span>
        </div>
        <!-- 其他图例项 -->
    </div>
</div>
```

### **区域9: 代码生成状态**

**功能**: 显示代码生成进度和质量评估
```html
<div class="code-generation-section">
    <div class="generation-status">
        <div class="status-item">
            <span class="status-label">生成状态</span>
            <span class="status-value" id="generation-status">未开始</span>
        </div>
        <div class="status-item">
            <span class="status-label">进度</span>
            <div class="progress-bar">
                <div class="progress-fill" id="generation-progress" style="width: 0%"></div>
            </div>
        </div>
    </div>
    
    <div class="generation-results" id="generation-results" style="display: none;">
        <div class="result-item">
            <span class="result-label">生成文件</span>
            <span class="result-value" id="generated-files-count">0</span>
        </div>
        <div class="result-item">
            <span class="result-label">代码质量</span>
            <span class="result-value" id="code-quality-score">-</span>
        </div>
    </div>
</div>
```

## 🎨 样式设计

### **主题色彩方案**
```css
:root {
    /* 基础色彩 */
    --bg-primary: #1E1F22;
    --bg-secondary: #2A2D30;
    --bg-tertiary: #393B40;
    --border-color: #3C3F41;
    --text-primary: #BBBBBB;
    --text-secondary: #888888;
    
    /* 功能色彩 */
    --accent-blue: #0078D4;
    --success-green: #4CAF50;
    --warning-orange: #FF9800;
    --danger-red: #F44336;
    --info-cyan: #00BCD4;
    
    /* 风险等级色彩 */
    --risk-critical: #F44336;
    --risk-high: #FF9800;
    --risk-medium: #2196F3;
    --risk-low: #4CAF50;
}
```

### **响应式设计**
```css
/* 九宫格响应式布局 */
.nine-grid-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    grid-gap: 2px;
    height: 100vh;
    width: 100vw;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .nine-grid-container {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(9, auto);
        height: auto;
        overflow-y: auto;
    }
}
```

## 🔄 实时更新机制

### **WebSocket通信**
```javascript
class ArchitectureRiskWebSocket {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
    }
    
    connect() {
        this.ws = new WebSocket('ws://localhost:5000/ws/architecture-risk');
        
        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        };
        
        this.ws.onclose = () => {
            this.handleReconnect();
        };
    }
    
    handleMessage(data) {
        switch(data.type) {
            case 'detection_progress':
                this.updateDetectionProgress(data);
                break;
            case 'risk_found':
                this.addRiskToDisplay(data);
                break;
            case 'generation_progress':
                this.updateGenerationProgress(data);
                break;
        }
    }
}
```

## 📱 用户体验优化

### **交互反馈**
- **按钮状态**: 禁用/启用状态清晰可见
- **进度指示**: 实时进度条和百分比显示
- **状态提示**: 清晰的状态文字和图标指示
- **错误处理**: 友好的错误提示和恢复建议

### **性能优化**
- **懒加载**: 大量数据分页加载
- **虚拟滚动**: 长列表性能优化
- **缓存策略**: 检测结果本地缓存
- **防抖处理**: 输入框防抖优化

---

**设计原则**: 复用ace现有资产 + 专业化定制 + 优秀用户体验
**核心价值**: 提供直观、高效的架构风险检测和代码生成交互界面
