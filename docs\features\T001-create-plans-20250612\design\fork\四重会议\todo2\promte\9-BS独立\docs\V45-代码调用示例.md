# V4.5 MCP API代码调用示例

## 🔧 Web服务器内部调用方式

### Python调用示例

```python
# 在Web服务器中调用MCP API
import asyncio
from mcp_server.task_dispatcher import TaskDispatcher

class WebServerAPI:
    def __init__(self):
        self.task_dispatcher = TaskDispatcher()
    
    async def create_document(self, file_path: str, content: str):
        """创建新文档"""
        task = {
            "task_type": "document_edit",
            "command": {
                "file_path": file_path,
                "operation": "insert_line",
                "parameters": {
                    "line_number": 1,
                    "content": content,
                    "position": "after"
                }
            },
            "metadata": {
                "priority": "normal",
                "timeout": 300
            }
        }
        
        result = await self.task_dispatcher.send_task(task)
        return result
    
    async def update_document(self, file_path: str, line_number: int, new_content: str):
        """更新文档指定行"""
        task = {
            "task_type": "document_edit",
            "command": {
                "file_path": file_path,
                "operation": "update_line",
                "parameters": {
                    "line_number": line_number,
                    "content": new_content,
                    "merge_mode": "replace"
                }
            }
        }
        
        return await self.task_dispatcher.send_task(task)
    
    async def search_files(self, directory: str, pattern: str):
        """搜索文件"""
        task = {
            "task_type": "directory_operation",
            "command": {
                "operation": "search_files",
                "parameters": {
                    "directory_path": directory,
                    "pattern": pattern,
                    "recursive": True,
                    "max_results": 100
                }
            }
        }
        
        return await self.task_dispatcher.send_task(task)
```

### Flask Web API封装示例

```python
from flask import Flask, request, jsonify
import asyncio

app = Flask(__name__)
web_api = WebServerAPI()

@app.route('/api/documents', methods=['POST'])
def create_document():
    """创建新文档的Web API"""
    data = request.json
    file_path = data.get('file_path')
    content = data.get('content')
    
    # 异步调用MCP API
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    result = loop.run_until_complete(
        web_api.create_document(file_path, content)
    )
    
    return jsonify(result)

@app.route('/api/documents/<path:file_path>', methods=['PUT'])
def update_document(file_path):
    """更新文档的Web API"""
    data = request.json
    line_number = data.get('line_number')
    content = data.get('content')
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    result = loop.run_until_complete(
        web_api.update_document(file_path, line_number, content)
    )
    
    return jsonify(result)

@app.route('/api/files/search', methods=['GET'])
def search_files():
    """搜索文件的Web API"""
    directory = request.args.get('directory', '.')
    pattern = request.args.get('pattern', '*')
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    result = loop.run_until_complete(
        web_api.search_files(directory, pattern)
    )
    
    return jsonify(result)
```

## 📝 实际使用场景示例

### 场景1：创建项目文档

```python
async def create_project_docs(project_name: str):
    """创建项目文档结构"""
    web_api = WebServerAPI()
    
    # 创建README.md
    readme_content = f"""# {project_name}
    
## 项目描述
这是{project_name}项目的说明文档。

## 安装方法
```bash
npm install {project_name}
```

## 使用方法
详细使用方法请参考API文档。
"""
    
    await web_api.create_document(f"{project_name}/README.md", readme_content)
    
    # 创建API文档
    api_content = f"""# {project_name} API文档

## 接口列表

### GET /api/status
获取系统状态

### POST /api/data
提交数据
"""
    
    await web_api.create_document(f"{project_name}/docs/API.md", api_content)
    
    print(f"项目 {project_name} 文档创建完成")
```

### 场景2：批量文档处理

```python
async def batch_update_headers(directory: str):
    """批量更新文档标题"""
    web_api = WebServerAPI()
    
    # 搜索所有Markdown文件
    search_result = await web_api.search_files(directory, "*.md")
    
    if search_result["status"] == "success":
        files = search_result["result"]["files"]
        
        for file_info in files:
            file_path = file_info["path"]
            
            # 更新每个文件的第一行为标准标题格式
            new_header = f"# {file_info['name'].replace('.md', '').replace('_', ' ').title()}"
            
            await web_api.update_document(file_path, 1, new_header)
            print(f"已更新文件: {file_path}")
```

### 场景3：文档内容替换

```python
async def update_copyright_info(directory: str, old_year: str, new_year: str):
    """批量更新版权信息"""
    web_api = WebServerAPI()
    
    # 搜索包含版权信息的文件
    search_result = await web_api.search_files(directory, "*.md")
    
    if search_result["status"] == "success":
        files = search_result["result"]["files"]
        
        for file_info in files:
            file_path = file_info["path"]
            
            # 全局替换版权年份
            task = {
                "task_type": "document_edit",
                "command": {
                    "file_path": file_path,
                    "operation": "replace_all",
                    "parameters": {
                        "search_pattern": f"© {old_year}",
                        "replace_with": f"© {new_year}",
                        "regex": False,
                        "case_sensitive": True
                    }
                }
            }
            
            result = await web_api.task_dispatcher.send_task(task)
            if result["status"] == "success":
                print(f"已更新版权信息: {file_path}")
```

## 🔄 错误处理示例

```python
async def safe_document_operation(file_path: str, operation_func):
    """安全的文档操作，包含错误处理"""
    try:
        result = await operation_func()
        
        if result["status"] == "success":
            print(f"操作成功: {file_path}")
            return result
        else:
            print(f"操作失败: {file_path}, 错误: {result.get('error', '未知错误')}")
            return None
            
    except Exception as e:
        print(f"操作异常: {file_path}, 异常: {str(e)}")
        return None

# 使用示例
async def create_document_safely(file_path: str, content: str):
    web_api = WebServerAPI()
    
    async def create_operation():
        return await web_api.create_document(file_path, content)
    
    return await safe_document_operation(file_path, create_operation)
```

## 📊 性能监控示例

```python
import time
from typing import Dict, Any

class PerformanceMonitor:
    def __init__(self):
        self.operation_stats = {}
    
    async def monitored_operation(self, operation_name: str, operation_func):
        """监控操作性能"""
        start_time = time.time()
        
        try:
            result = await operation_func()
            end_time = time.time()
            duration = end_time - start_time
            
            # 记录性能统计
            if operation_name not in self.operation_stats:
                self.operation_stats[operation_name] = {
                    "total_calls": 0,
                    "total_time": 0,
                    "success_count": 0,
                    "error_count": 0
                }
            
            stats = self.operation_stats[operation_name]
            stats["total_calls"] += 1
            stats["total_time"] += duration
            
            if result and result.get("status") == "success":
                stats["success_count"] += 1
            else:
                stats["error_count"] += 1
            
            print(f"操作 {operation_name} 耗时: {duration:.3f}s")
            return result
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            if operation_name in self.operation_stats:
                self.operation_stats[operation_name]["error_count"] += 1
            
            print(f"操作 {operation_name} 异常，耗时: {duration:.3f}s, 错误: {str(e)}")
            raise
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        report = {}
        for operation, stats in self.operation_stats.items():
            if stats["total_calls"] > 0:
                avg_time = stats["total_time"] / stats["total_calls"]
                success_rate = stats["success_count"] / stats["total_calls"] * 100
                
                report[operation] = {
                    "平均耗时": f"{avg_time:.3f}s",
                    "成功率": f"{success_rate:.1f}%",
                    "总调用次数": stats["total_calls"],
                    "成功次数": stats["success_count"],
                    "失败次数": stats["error_count"]
                }
        
        return report

# 使用示例
monitor = PerformanceMonitor()

async def monitored_create_document(file_path: str, content: str):
    web_api = WebServerAPI()
    
    async def operation():
        return await web_api.create_document(file_path, content)
    
    return await monitor.monitored_operation("create_document", operation)
```

## 🚀 快速集成模板

```python
# 完整的集成模板
class DocumentManager:
    def __init__(self):
        self.web_api = WebServerAPI()
        self.monitor = PerformanceMonitor()
    
    async def create(self, file_path: str, content: str):
        """创建文档"""
        async def operation():
            return await self.web_api.create_document(file_path, content)
        return await self.monitor.monitored_operation("create", operation)
    
    async def read(self, file_path: str, line_number: int = None):
        """读取文档"""
        # 实现读取逻辑
        pass
    
    async def update(self, file_path: str, line_number: int, content: str):
        """更新文档"""
        async def operation():
            return await self.web_api.update_document(file_path, line_number, content)
        return await self.monitor.monitored_operation("update", operation)
    
    async def delete(self, file_path: str):
        """删除文档"""
        # 实现删除逻辑
        pass
    
    def get_stats(self):
        """获取统计信息"""
        return self.monitor.get_performance_report()

# 使用示例
async def main():
    doc_manager = DocumentManager()
    
    # 创建文档
    await doc_manager.create("test.md", "# 测试文档\n\n这是测试内容。")
    
    # 更新文档
    await doc_manager.update("test.md", 1, "# 更新后的标题")
    
    # 查看性能统计
    print(doc_manager.get_stats())

# 运行示例
if __name__ == "__main__":
    asyncio.run(main())
```

---
**版本**：V4.5 | **更新**：2025-06-27 | **状态**：✅ 生产就绪
