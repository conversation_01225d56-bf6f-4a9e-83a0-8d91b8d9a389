# V4.5 MCP分离架构快速测试指令

## 💡 重要概念：MCP客户端"挂起"
- **挂起**：IDE调用MCP客户端后，MCP客户端连接到Web服务器并等待任务指令
- **不是**：终端执行任务时的挂起状态
- **双重连接**：MCP客户端同时连接IDE（MCP协议）和Web服务器（WebSocket）

## 🚀 快速启动

### 1. 重启Web服务器
```bash
# 停止当前服务器（Ctrl+C），然后：
cd C:\ExchangeWorks\xkong\xkongcloud
python tools\ace\src\four_layer_meeting_server\server_launcher.py
```

### 2. 重启MCP客户端（在IDE中）
**重要**：MCP客户端通过IDE的MCP配置启动，不是终端运行！
- 在IDE中重新加载MCP配置或重启IDE
- 确保MCP客户端连接到Web服务器并进入挂起等待状态

### 3. 验证连接
```bash
curl http://localhost:25526/api/connected_clients
```

## 📋 核心测试指令

### 测试1：读取设计文档
```bash
curl -X POST http://localhost:25526/api/send_task \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "file_operation",
    "command": {
      "operation": "read",
      "file_path": "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/fork/四重会议/todo2/promte/9-BS独立/V45-MCP分离架构设计要求.md"
    }
  }'
```

### 测试2：写入测试文件
```bash
curl -X POST http://localhost:25526/api/send_task \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "file_operation",
    "command": {
      "operation": "write",
      "file_path": "mcp_test_success.txt",
      "content": "V4.5 MCP分离架构测试成功！\n时间: 2025-06-27\n执行方式: MCP客户端挂起模式"
    }
  }'
```

### 测试3：代码分析
```bash
curl -X POST http://localhost:25526/api/send_task \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "code_analysis",
    "command": {
      "file_path": "tools/ace/src/four_layer_meeting_system/mcp_server/simple_ascii_launcher.py",
      "analysis_type": "structure"
    }
  }'
```

### 测试4：MCP工具执行
```bash
curl -X POST http://localhost:25526/api/execute_mcp_tool \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "get_system_status",
    "arguments": {}
  }'
```

## 🔍 浏览器测试（开发者工具）

```javascript
// 测试文件读取
fetch('http://localhost:25526/api/send_task', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({
    "task_type": "file_operation",
    "command": {
      "operation": "read",
      "file_path": "README.md"
    }
  })
}).then(r => r.json()).then(console.log);

// 测试文件写入
fetch('http://localhost:25526/api/send_task', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({
    "task_type": "file_operation",
    "command": {
      "operation": "write",
      "file_path": "browser_test.txt",
      "content": "浏览器测试成功"
    }
  })
}).then(r => r.json()).then(console.log);
```

## ✅ 预期结果

### MCP客户端终端输出：
```
⏳ 挂起等待任务指令...
📥 收到任务指令: file_operation (ID: task_xxx)
🔄 开始执行任务（保持挂起状态）...
⚙️ 执行方式: mcp_client_direct_python
📤 任务执行完成，继续挂起等待下一个任务...
```

### API响应：
```json
{
  "status": "success",
  "task_id": "task_20250627_001234_0"
}
```

### Web服务器终端输出：
```
🌐 Web界面请求任务: file_operation
📊 任务结果: {"status": "success", "task_id": "task_xxx"}
```

## 🎯 成功标准

1. ✅ **MCP客户端双重连接**：同时连接IDE和Web服务器
2. ✅ **挂起等待状态**：连接Web服务器后进入挂起等待
3. ✅ **Web服务器任务下发**：能通过API发送任务
4. ✅ **文件读取功能**：返回正确文件内容
5. ✅ **文件写入功能**：创建新文件成功
6. ✅ **代码分析功能**：返回结构信息
7. ✅ **MCP工具执行**：现有工具正常工作
8. ✅ **持续挂起机制**：任务执行后继续挂起等待

## 🚨 快速故障排除

- **MCP客户端未进入挂起状态**：重启IDE，确保加载新代码
- **API 404错误**：重启Web服务器
- **连接失败**：检查端口25527
- **权限错误**：检查文件路径权限
