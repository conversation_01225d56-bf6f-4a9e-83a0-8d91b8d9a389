---
title: UID生成时序图
description: 展示xkongcloud-commons-uid库生成唯一ID的时序图
created_date: 2025-05-18
updated_date: 2025-05-18
version: 1.0
status: 草稿
author: AI助手
---

# UID生成时序图

此时序图展示了xkongcloud-commons-uid库在应用运行时生成唯一ID的过程，包括租约续约和ID生成。

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant UG as UidGenerator
    participant PIWA as PersistentInstanceWorkerIdAssigner
    participant DB as 数据库(PostgreSQL)
    
    App->>UG: 请求生成唯一ID
    UG->>PIWA: 获取WorkerID
    
    alt 首次获取或租约已过期
        PIWA->>DB: 获取/续约WorkerID
        DB-->>PIWA: 返回WorkerID和租约信息
    else 租约有效
        PIWA->>PIWA: 使用缓存的WorkerID
    end
    
    PIWA-->>UG: 返回WorkerID
    UG->>UG: 生成时间戳部分
    UG->>UG: 生成序列号部分
    UG->>UG: 组合生成最终UID
    UG-->>App: 返回唯一ID
    
    loop 定期租约续约
        PIWA->>DB: 续约WorkerID租约
        DB-->>PIWA: 返回续约结果
    end
```
