# V4.3方案：九宫格交互界面设计 (以“富报告”核心)

## 1. 文档信息

- **文档版本**: V4.3
- **创建日期**: 2025-08-13
- **目的**: 明确V4.3理引擎如何复用现有九宫格UI，并将其改造为以“富报告”为核心的架构工作台，指导人机交互流程。

## 2. 九宫格UI的复用与改造原则

V4.3方案的核心原则是**不投入精力大改界面**，而是最大限度地复用现有九宫格UI，并将其核心交互聚焦于“富报告”的展示与处理。

-   **核心理念**: 九宫格不再是简单的功能入口，而是**架构治理的“驾驶舱”**，所有关键信息和操作都围绕“富报告”展开。
-   **信息流**: 所有的信息输入和输出都通过“富报告”进行标准化，简化UI的复杂性。
-   **人机协同**: 人类通过UI进行决策和“物理总线”操作（复/粘贴），AI和算法则在后台进行智能分析和生成。

## 3. 九宫格区域功能映射

我们将九宫格的9个区域，按照V4.3的核心工作流进行功能映射和职责义：

| 区域编号 | 区域名称 (V2) | V4.3功能映射 | 职责描述 |
| :------- | :------------ | :----------- | :------- |
| **区域1** | **文档列表** | **设计文档浏览器** | 展示`docs/`目录下所有`01~xx`号设计文档的列表。用户可在此选择文档启动审计。 |
| 区域2 | 待定 | 辅助信息/禁用 | 可用于显示全局状态、通知等，或暂时禁用。 |
| 区域3 | 待定 | 辅助信息/禁用 | 可用于显示全局状态、通知等，或暂时禁用。 |
| 区域4 | 待定 | 辅助信息/禁用 | 可用于显示全局状态、通知等，或暂时禁用。 |
| **区域5** | **核心操作区** | **审计结果与报告导航** | **核心区域**。用于展示审计任务的最终状态、核心摘要，并提供指向新生成的 **`rich_report.yaml`** 报告文件的直接链接。用户在此获取审计结果，并导航到报告文件进行详细审查。 |
| 区域6 | 待定 | 辅助信息/禁用 | 可用于显示全局状态、通知等，或暂时禁用。 |
| 区域7 | 待定 | 辅助信息/禁用 | 可用于显示全局状态、通知等，或暂时禁用。 |
| **区域8** | **输入区** | **RooCode修订内容输入区** | 用于人类开发者将RooCode返回的、修改后的设计文档内容粘贴到此区域。系统会在此区域展示diff，并提供“批准并写入”按钮。 |
| 区域9 | 待定 | 辅助信息/禁用 | 可用于显示全局状态、通知等，或暂时禁用。 |

## 4. 核心交互流程 (UI视)

以下是从UI角度看，V4.3治理引擎的核心交互流程：

1.  **启动审计**:
    *   用户在**区域1**（设计文档浏览器）中，选择一个或多个设计文档（例如 `docs/features/F009/design.md`）。
    *   点击“开审计”按钮（按钮位置待定，可在区域1下方或区域5上方）。
    *   V4.3治理引擎后台开始执行算法扫描和Py AI分析。

2.  **获取审计报告**:
    *   审计完成后，V4.3引擎将生成的“**富报告**” (`rich_report.yaml`) 保存至被审计文档目录下的 `_pm_workspace` 工作区内。
    *   **区域5**（审计结果与报告导航）会更新，显示审计的总体状态（如“审计完成，发现X个问题”），并提供一个清晰的、可点击的链接，指向新生成的 `rich_report.yaml` 文件。
    *   UI不再负责渲染整个报告的内容。

3.  **人类决策与委托**:
    *   人类开发者点击**区域5**中的链接，在IDE或文本编辑器中打开 `rich_report.yaml` 文件进行详细审查。
    *   开发者从YAML文件中复制需要的 `recommended_task` 指令。
    *   开发者将复制的指令粘贴到IDE（如VSCode终端）中的RooCode编排器并执行。

4.  **RooCode修订与智能迭代 (第一阶段)**:
    *   在**第一阶段（设计文档自我完善）**中，RooCode会直接根据指令修改本地的 `.md` 文件。
    *   **人类开发者在IDE中确认RooCode的修改已保存后**，无需任何复制粘贴操作。
    *   开发者可以直接回到我们的UI界面，**再次点击“开始审计”按钮**，触发智能迭代流程：
        1.  V4.3引擎会对**已更新**的设计文档发起新一轮的合规性检查。
        2.  **Py AI会对比新旧两份`rich_report.yaml`**，执行“收敛性检查”，判断问题是否正在减少。
        3.  如果问题未能收敛，Py AI会**优化**下一次给RooCode的提示词。
        4.  系统生成一份包含“收敛性分析”和“优化后提示词”的**新版`rich_report.yaml`**，并在区域5更新链接。
    *   这个流程形成了一个快速的“**审计 -> 委托RooCode修订 -> 再次审计（含收敛性检查）**”闭环，直到设计文档100%完备。

5.  **接收代码修订与审批 (第二阶段)**:
    *   在**第二阶段（代码生成与对齐）**中，当RooCode生成或修改的是**代码**时，为了安全起见，我们仍然保留审批流程。
    *   RooCode完成**代码**任务后，会将修订后的**代码内容**作为文本返回。
    *   开发者将**代码**内容粘贴到我们UI的**区域8**，审查`diff`后，点击“批准并写入”。写入成功后，系统同样会自动触发包含“收敛性检查”的再次审计。

## 5. 关键UI实现考量

-   **响应性**: 区域5的报告展示应具备良好的可读性和响应性，即使报告内容较长也能流畅滚动。
-   **可复制性**: `recommended_task`的“一键复制”功能必须稳定可靠。
-   **Diff视图**: 区域8的diff视图应清晰直，帮助用户快速识别RooCode的修改。
-   **状态反馈**: UI应清晰地显示当前审计状态（例如“正在审计中…”、“审计完成，发现X个问题”、“设计质量达标”）。

## 6. 报告与附件（文件系统交付）
- 审计完成后，所有产出物将直接生成在工作区目录中，UI仅提供链接：
  - `rich_report.yaml`: 包含所有核心发现和建议的主报告。
  - `assertion_results.yaml`: 详细的断言与证据。
  - `micro_graph.yaml`: （可选）用于调试的微观图。
- 在区域8展示审批前后的 `diff`，并在通过后归档以上制品的路径。

## 7. 门禁与状态文案
- 设计侧未通过：显示 `DESIGN-NOT-COMPLIANT`，引导回到阶段一修订（提示 Schema/覆盖率/一致性原因）。
- 代码侧未通过：显示 `CODE-NOT-COMPLIANT`，高亮 `MISSING/CONFLICT/LEGACY` 分类与证据。
- 全量通过：显示 `COMPLIANT`，提供“写入/合并并重审”按钮。

## 8. 一键复制与粘贴
- 在每条 `recommended_task` 行尾提供“一键复制”按钮；复制内容可直接发送至 RooCode 编排器。
- RooCode 返回文本在区域8粘贴后自动生成 `diff`，并提示“批准并写入”。

## 9. Serena 请求失败呈现（INDETERMINATE）
- 对于 Serena 超时/错误的断言，按 `INDETERMINATE` 显示灰色块与重试建议，不放宽门禁。
- 提供“重新获取代码事实”按钮以触发重试或调整 `project.yml` 的扫描范围。
