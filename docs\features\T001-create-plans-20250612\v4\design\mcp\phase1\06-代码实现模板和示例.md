# V4 MCP Server第一阶段代码实现模板和示例

## 📋 文档概述

**文档ID**: V4-MCP-PHASE1-CODE-TEMPLATES-006
**创建日期**: 2025-06-18
**版本**: F007-mcp-phase1-v1.0.L1.5.0
**目标**: 提供详细的代码实现模板和示例，基于DRY原则复用V4设计
**模板引用**: @TEMPLATE_REF:../核心/V4架构信息AI填充模板.md#代码实现模板

## 🏗️ 核心组件代码模板

### 1. MCP Server主服务实现
```python
# tools/ace/mcp/v4_context_guidance_server/core/v4_mcp_server.py
"""
V4上下文引导MCP Server主服务
基于 @REF:docs/features/T001-create-plans-20250612/v4/design/01-V4架构总体设计.md
复用V4服务架构模式，实现精准的IDE AI控制
"""

import os
import json
import logging
import sys
from typing import Dict, List, Optional
from datetime import datetime
from pathlib import Path

# 导入现有ACE算法模块（已验证的生产级别代码）
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "ace" / "src"))
from algorithms.v4_multi_dimensional_analyzer import V4MultiDimensionalAnalyzer
from algorithms.semantic_tag_analyzer import SemanticTagAnalyzer
from algorithms.confidence_95_filter import Confidence95Filter
from algorithms.enhanced_standards_detector import EnhancedStandardsDetector
from algorithms.smart_batch_modifier import SmartBatchModifier
from algorithms.detection_report_generator import DetectionReportGenerator

from .task_context_manager import TaskContextManager
from .progress_tracker import ProgressTracker
from ..checkresult_processing.v4_checkresult_parser import V4CheckResultParser
from ..ide_control.modification_controller import ModificationController


class V4ContextGuidanceServer:
    """
    V4上下文引导MCP Server主服务

    基于现有ACE算法的DRY复用，提供精准的IDE AI修改控制
    支持断线重连和批次处理
    """

    def __init__(self):
        # 初始化日志
        self.logger = logging.getLogger(__name__)

        # 直接复用现有ACE算法（已验证）
        self.v4_analyzer = V4MultiDimensionalAnalyzer()
        self.semantic_analyzer = SemanticTagAnalyzer()
        self.confidence_filter = Confidence95Filter()
        self.standards_detector = EnhancedStandardsDetector()
        self.smart_batch_modifier = SmartBatchModifier()
        self.report_generator = DetectionReportGenerator()

        # 基于ACE算法的组件
        self.task_context_manager = TaskContextManager()
        self.checkresult_parser = V4CheckResultParser()
        self.modification_controller = ModificationController()

        # 进度跟踪器（支持断线重连）
        self.progress_tracker = None

        # 服务状态
        self.server_state = {
            "initialized": False,
            "current_task": None,
            "active_sessions": {},
            "ace_algorithms_loaded": True
        }
    
    def initialize_modification_task(self, checkresult_path: str) -> Dict:
        """
        初始化修改任务

        基于V4设计的智能任务识别和策略选择

        Args:
            checkresult_path: checkresult-v4目录的完整路径

        Returns:
            Dict: 任务初始化结果

        Raises:
            ValueError: 当checkresult_path无效时
            FileNotFoundError: 当目录不存在时
        """

        try:
            # 验证输入参数
            if not checkresult_path or not os.path.isdir(checkresult_path):
                raise ValueError(f"无效的checkresult路径: {checkresult_path}")

            self.logger.info(f"开始初始化修改任务: {checkresult_path}")

            # 1. 基于现有ACE算法的任务类型识别
            task_context = self.task_context_manager.analyze_checkresult_context(checkresult_path)

            # 2. 使用ACE Confidence95Filter验证置信度
            confidence_validation = self.confidence_filter.apply_confidence_filter(
                task_context, threshold=0.95
            )

            if not confidence_validation.get("passed", False):
                raise ValueError(f"任务识别置信度不足: {confidence_validation.get('confidence', 0.0)}")

            # 3. 解析checkresult-v4目录（基于ACE格式）
            checkresult_data = self.checkresult_parser.parse_checkresult_directory(checkresult_path)

            # 4. 使用ACE SmartBatchModifier生成修改队列
            modification_queue = self._generate_ace_based_modification_queue(
                checkresult_data, task_context
            )

            # 5. 使用ACE算法验证修改队列
            ace_validation = self._validate_with_ace_algorithms(modification_queue)

            if not ace_validation["validation_passed"]:
                raise ValueError(f"ACE算法验证失败: {ace_validation['errors']}")

            # 6. 初始化进度跟踪器
            progress_file = os.path.join(checkresult_path, "mcp_progress_tracker.json")
            self.progress_tracker = ProgressTracker()
            self.progress_tracker.initialize_or_load(progress_file, modification_queue)

            # 7. 更新服务状态
            self.server_state.update({
                "initialized": True,
                "current_task": {
                    "checkresult_path": checkresult_path,
                    "task_context": task_context,
                    "total_modifications": len(modification_queue),
                    "ace_validation": ace_validation
                }
            })
            
            # 6. 生成初始化结果
            progress_summary = self.progress_tracker.get_progress_summary()
            
            result = {
                "status": "task_initialized",
                "task_id": progress_summary["task_id"],
                "checkresult_path": checkresult_path,
                "total_modifications": progress_summary["overall_progress"]["total_modifications"],
                "completed_modifications": progress_summary["overall_progress"]["completed_count"],
                "remaining_modifications": progress_summary["overall_progress"]["pending_count"],
                "progress_file": progress_file,
                "initialization_time": datetime.now().isoformat()
            }
            
            self.logger.info(f"任务初始化完成: {result['task_id']}")
            return result
            
        except Exception as e:
            self.logger.error(f"任务初始化失败: {str(e)}")
            raise
    
    def get_next_modification_batch(self, batch_size: int = 3) -> Dict:
        """
        获取下一批修改指令
        
        Args:
            batch_size: 批次大小，默认3个修改
            
        Returns:
            Dict: 下一批修改指令或完成状态
            
        Raises:
            RuntimeError: 当任务未初始化时
        """
        
        if not self.server_state["initialized"] or not self.progress_tracker:
            raise RuntimeError("任务未初始化，请先调用 initialize_modification_task")
        
        try:
            # 获取下一批修改
            next_batch = self.progress_tracker.get_next_batch(batch_size)
            
            if not next_batch:
                # 所有修改已完成
                final_summary = self.progress_tracker.get_progress_summary()
                return {
                    "status": "all_completed",
                    "message": "所有修改已完成",
                    "final_summary": final_summary
                }
            
            # 使用ACE SmartBatchModifier生成精确的修改指令
            precise_instructions = self.smart_batch_modifier.generate_smart_batch_instructions(
                next_batch, output_format="ide_ai_friendly"
            )

            return {
                "status": "batch_ready",
                "batch_info": {
                    "batch_number": self.progress_tracker.progress_data["modification_queue"]["current_batch"],
                    "batch_size": len(next_batch),
                    "modifications": next_batch
                },
                "ide_ai_instructions": precise_instructions,
                "progress_summary": self.progress_tracker.get_progress_summary(),
                "ace_algorithm_used": "SmartBatchModifier"
            }
            
        except Exception as e:
            self.logger.error(f"获取修改批次失败: {str(e)}")
            raise
    
    def validate_and_continue(self, completed_modifications: List[Dict]) -> Dict:
        """
        验证修改结果并继续下一批
        
        Args:
            completed_modifications: 已完成的修改列表
            
        Returns:
            Dict: 验证结果和下一批修改指令
        """
        
        if not self.server_state["initialized"] or not self.progress_tracker:
            raise RuntimeError("任务未初始化")
        
        try:
            # 使用ACE算法验证修改结果
            validation_results = self._validate_modifications_with_ace(completed_modifications)

            # 更新进度
            self.progress_tracker.mark_batch_completed(completed_modifications, validation_results)
            
            # 统计验证结果
            successful_count = sum(1 for r in validation_results if r.get("success", False))
            failed_count = len(validation_results) - successful_count
            
            # 获取下一批
            next_batch = self.progress_tracker.get_next_batch()
            
            if not next_batch:
                # 任务完成
                return {
                    "status": "task_completed",
                    "message": "所有修改已完成",
                    "validation_summary": {
                        "total_validated": len(validation_results),
                        "successful": successful_count,
                        "failed": failed_count
                    },
                    "final_summary": self.progress_tracker.get_progress_summary()
                }
            
            # 使用ACE SmartBatchModifier继续下一批
            precise_instructions = self.smart_batch_modifier.generate_smart_batch_instructions(
                next_batch, output_format="ide_ai_friendly"
            )

            return {
                "status": "batch_completed_continue",
                "validation_summary": {
                    "total_validated": len(validation_results),
                    "successful": successful_count,
                    "failed": failed_count
                },
                "next_batch": {
                    "modifications": next_batch,
                    "batch_number": self.progress_tracker.progress_data["modification_queue"]["current_batch"]
                },
                "ide_ai_instructions": precise_instructions,
                "progress_summary": self.progress_tracker.get_progress_summary(),
                "ace_algorithm_used": "SmartBatchModifier"
            }
            
        except Exception as e:
            self.logger.error(f"验证和继续失败: {str(e)}")
            raise
    
    def _generate_modification_queue(self, checkresult_data: Dict) -> List[Dict]:
        """
        基于检查报告生成修改队列
        
        Args:
            checkresult_data: 解析后的检查报告数据
            
        Returns:
            List[Dict]: 修改队列
        """
        
        modification_queue = []
        
        for report in checkresult_data.get("reports", []):
            file_path = report.get("target_file_path")
            
            # 处理重要问题（高优先级）
            for issue in report.get("important_issues", []):
                modification_queue.append({
                    "id": f"{report['file_name']}_{issue.get('line_number', 0)}_{len(modification_queue)}",
                    "priority": "high",
                    "file_path": file_path,
                    "line_number": issue.get("line_number"),
                    "action": "replace",
                    "find_text": issue.get("find_text", ""),
                    "replace_text": issue.get("replace_text", ""),
                    "description": issue.get("description", ""),
                    "confidence": issue.get("confidence", 0.9),
                    "context": issue.get("context", ""),
                    "status": "pending",
                    "checkresult_path": self.server_state["current_task"]["checkresult_path"]
                })
            
            # 处理改进建议（中优先级）
            for suggestion in report.get("improvement_suggestions", []):
                modification_queue.append({
                    "id": f"{report['file_name']}_{suggestion.get('line_number', 0)}_{len(modification_queue)}",
                    "priority": "medium",
                    "file_path": file_path,
                    "line_number": suggestion.get("line_number"),
                    "action": "replace",
                    "find_text": suggestion.get("find_text", ""),
                    "replace_text": suggestion.get("replace_text", ""),
                    "description": suggestion.get("description", ""),
                    "confidence": suggestion.get("confidence", 0.8),
                    "context": suggestion.get("context", ""),
                    "status": "pending",
                    "checkresult_path": self.server_state["current_task"]["checkresult_path"]
                })
        
        # 按优先级和行号排序
        modification_queue.sort(key=lambda x: (
            0 if x["priority"] == "high" else 1,
            x["file_path"],
            x.get("line_number", 0)
        ))
        
        return modification_queue

    def _generate_ace_based_modification_queue(self, checkresult_data: Dict, task_context: Dict) -> List[Dict]:
        """
        基于现有ACE算法生成修改队列
        """

        modification_queue = []

        for report in checkresult_data.get("reports", []):
            file_path = report.get("target_file_path")

            # 处理重要问题（高优先级）
            for issue in report.get("important_issues", []):
                modification_queue.append({
                    "id": f"{report['file_name']}_{issue.get('line_number', 0)}_{len(modification_queue)}",
                    "priority": "high",
                    "file_path": file_path,
                    "line_number": issue.get("line_number"),
                    "action": "replace",
                    "find_text": issue.get("find_text", ""),
                    "replace_text": issue.get("replace_text", ""),
                    "description": issue.get("description", ""),
                    "confidence": issue.get("confidence", 0.95),
                    "context": issue.get("context", ""),
                    "status": "pending",
                    "ace_compatible": True
                })

            # 处理改进建议（中优先级）
            for suggestion in report.get("improvement_suggestions", []):
                modification_queue.append({
                    "id": f"{report['file_name']}_{suggestion.get('line_number', 0)}_{len(modification_queue)}",
                    "priority": "medium",
                    "file_path": file_path,
                    "line_number": suggestion.get("line_number"),
                    "action": "replace",
                    "find_text": suggestion.get("find_text", ""),
                    "replace_text": suggestion.get("replace_text", ""),
                    "description": suggestion.get("description", ""),
                    "confidence": suggestion.get("confidence", 0.85),
                    "context": suggestion.get("context", ""),
                    "status": "pending",
                    "ace_compatible": True
                })

        # 按优先级和行号排序
        modification_queue.sort(key=lambda x: (
            0 if x["priority"] == "high" else 1,
            x["file_path"],
            x.get("line_number", 0)
        ))

        return modification_queue

    def _validate_with_ace_algorithms(self, modifications: List[Dict]) -> Dict:
        """
        使用现有ACE算法验证修改队列
        """

        # 使用ACE Confidence95Filter验证
        confidence_validation = self.confidence_filter.apply_confidence_filter(
            {"modifications": modifications}, threshold=0.95
        )

        # 使用ACE EnhancedStandardsDetector验证格式
        format_validation = True
        for mod in modifications:
            if not mod.get("ace_compatible", False):
                format_validation = False
                break

        return {
            "validation_passed": confidence_validation.get("passed", False) and format_validation,
            "confidence_validation": confidence_validation,
            "format_validation": format_validation,
            "errors": [] if confidence_validation.get("passed", False) and format_validation else ["验证失败"]
        }

    def _validate_modifications_with_ace(self, completed_modifications: List[Dict]) -> List[Dict]:
        """
        使用ACE算法验证修改结果
        """

        validation_results = []

        for modification in completed_modifications:
            # 使用ACE算法验证
            ace_validation = self.confidence_filter.apply_confidence_filter(
                modification, threshold=0.90
            )

            validation_results.append({
                "modification_id": modification.get("id", "unknown"),
                "success": ace_validation.get("passed", False),
                "confidence": ace_validation.get("confidence", 0.0),
                "ace_validation": ace_validation
            })

        return validation_results
```

### 2. V4检查报告解析器实现
```python
# tools/ace/mcp/v4_context_guidance_server/checkresult_processing/v4_checkresult_parser.py
"""
V4检查报告解析器
基于 @REF:docs/features/T001-create-plans-20250612/v4/design/02-扫描阶段设计.md
复用V4扫描报告格式和数据结构
"""

import os
import re
import glob
import logging
from typing import Dict, List, Optional


class V4CheckResultParser:
    """
    V4检查报告解析器
    
    解析checkresult-v4目录中的检查报告，提取修改指令
    支持多种报告格式和版本兼容
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 报告格式模式（基于V4报告格式规范）
        self.report_patterns = {
            "important_issue": r"### 重要问题 \d+.*?(?=###|\Z)",
            "improvement_suggestion": r"### 改进建议 \d+.*?(?=###|\Z)",
            "line_number": r"第(\d+)行",
            "find_replace": r"`([^`]+)`.*?替换为.*?`([^`]+)`",
            "arrow_replacement": r"`(->)`.*?替换为.*?`(→)`"
        }
    
    def parse_checkresult_directory(self, checkresult_path: str) -> Dict:
        """
        解析整个checkresult-v4目录
        
        Args:
            checkresult_path: checkresult-v4目录路径
            
        Returns:
            Dict: 解析后的检查报告数据
        """
        
        self.logger.info(f"开始解析checkresult目录: {checkresult_path}")
        
        checkresult_data = {
            "reports": [],
            "batch_improvement": None,
            "quality_overview": None,
            "total_issues": 0,
            "files_to_modify": []
        }
        
        try:
            # 解析所有检查报告文件
            report_files = glob.glob(os.path.join(checkresult_path, "*_检查报告.md"))
            
            for report_file in report_files:
                self.logger.debug(f"解析报告文件: {report_file}")
                report_data = self.parse_individual_report(report_file)
                
                if report_data:
                    checkresult_data["reports"].append(report_data)
                    checkresult_data["total_issues"] += report_data.get("total_issues", 0)
                    
                    # 提取目标文件路径
                    target_file = report_data.get("target_file_path")
                    if target_file and target_file not in checkresult_data["files_to_modify"]:
                        checkresult_data["files_to_modify"].append(target_file)
            
            # 解析批量改进指令（如果存在）
            batch_improvement_file = os.path.join(checkresult_path, "ai-prompt-batch-improvement.md")
            if os.path.exists(batch_improvement_file):
                checkresult_data["batch_improvement"] = self._parse_batch_improvement(batch_improvement_file)
            
            self.logger.info(f"解析完成，共发现 {len(checkresult_data['reports'])} 个报告，{checkresult_data['total_issues']} 个问题")
            
            return checkresult_data
            
        except Exception as e:
            self.logger.error(f"解析checkresult目录失败: {str(e)}")
            raise
    
    def parse_individual_report(self, report_file_path: str) -> Optional[Dict]:
        """
        解析单个检查报告文件
        
        Args:
            report_file_path: 报告文件路径
            
        Returns:
            Optional[Dict]: 解析后的报告数据，失败时返回None
        """
        
        try:
            with open(report_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取基本信息
            file_name = os.path.basename(report_file_path)
            target_file_path = self._extract_target_file_path(content, report_file_path)
            
            # 解析重要问题
            important_issues = self._extract_important_issues(content)
            
            # 解析改进建议
            improvement_suggestions = self._extract_improvement_suggestions(content)
            
            # 构建报告数据
            report_data = {
                "file_name": file_name,
                "report_file_path": report_file_path,
                "target_file_path": target_file_path,
                "total_issues": len(important_issues) + len(improvement_suggestions),
                "important_issues": important_issues,
                "improvement_suggestions": improvement_suggestions,
                "parsing_time": datetime.now().isoformat()
            }
            
            self.logger.debug(f"成功解析报告: {file_name}, 发现 {report_data['total_issues']} 个问题")
            
            return report_data
            
        except Exception as e:
            self.logger.error(f"解析报告文件失败 {report_file_path}: {str(e)}")
            return None
    
    def _extract_target_file_path(self, content: str, report_file_path: str) -> str:
        """
        提取目标文件路径
        
        基于报告文件名和内容推断目标文件路径
        """
        
        # 从报告文件名推断目标文件
        report_name = os.path.basename(report_file_path)
        
        if "_检查报告.md" in report_name:
            target_name = report_name.replace("_检查报告.md", ".md")
            
            # 构建目标文件的相对路径
            checkresult_dir = os.path.dirname(report_file_path)
            parent_dir = os.path.dirname(checkresult_dir)  # 上级目录
            target_file_path = os.path.join(parent_dir, target_name)
            
            return target_file_path
        
        return ""
    
    def _extract_important_issues(self, content: str) -> List[Dict]:
        """提取重要问题"""
        
        issues = []
        
        # 查找所有重要问题段落
        important_sections = re.findall(self.report_patterns["important_issue"], content, re.DOTALL)
        
        for section in important_sections:
            issue = self._parse_issue_section(section, "high")
            if issue:
                issues.append(issue)
        
        return issues
    
    def _extract_improvement_suggestions(self, content: str) -> List[Dict]:
        """提取改进建议"""
        
        suggestions = []
        
        # 查找所有改进建议段落
        suggestion_sections = re.findall(self.report_patterns["improvement_suggestion"], content, re.DOTALL)
        
        for section in suggestion_sections:
            suggestion = self._parse_issue_section(section, "medium")
            if suggestion:
                suggestions.append(suggestion)
        
        return suggestions
    
    def _parse_issue_section(self, section: str, priority: str) -> Optional[Dict]:
        """
        解析问题段落
        
        Args:
            section: 问题段落文本
            priority: 优先级（high/medium）
            
        Returns:
            Optional[Dict]: 解析后的问题数据
        """
        
        try:
            # 提取行号
            line_match = re.search(self.report_patterns["line_number"], section)
            line_number = int(line_match.group(1)) if line_match else None
            
            # 提取查找和替换文本
            find_text = ""
            replace_text = ""
            
            # 尝试箭头替换模式
            arrow_match = re.search(self.report_patterns["arrow_replacement"], section)
            if arrow_match:
                find_text = arrow_match.group(1)
                replace_text = arrow_match.group(2)
            else:
                # 尝试通用查找替换模式
                general_match = re.search(self.report_patterns["find_replace"], section)
                if general_match:
                    find_text = general_match.group(1)
                    replace_text = general_match.group(2)
            
            # 提取描述
            description = self._extract_description(section)
            
            # 提取上下文
            context = self._extract_context(section)
            
            return {
                "line_number": line_number,
                "find_text": find_text,
                "replace_text": replace_text,
                "description": description,
                "context": context,
                "confidence": 0.95 if priority == "high" else 0.85,
                "priority": priority
            }
            
        except Exception as e:
            self.logger.warning(f"解析问题段落失败: {str(e)}")
            return None
    
    def _extract_description(self, section: str) -> str:
        """提取问题描述"""
        
        # 提取第一行作为描述
        lines = section.strip().split('\n')
        if lines:
            # 移除Markdown标记
            description = lines[0].replace('#', '').strip()
            return description
        
        return "未知问题"
    
    def _extract_context(self, section: str) -> str:
        """提取上下文信息"""
        
        # 查找包含上下文的行
        lines = section.split('\n')
        for line in lines:
            if '上下文' in line or 'context' in line.lower():
                return line.strip()
        
        return ""
```

### 3. MCP工具接口实现示例
```python
# tools/ace/mcp/v4_context_guidance_server/tools/phase1_tools.py
"""
第一阶段MCP工具接口实现
基于标准MCP协议和V4设计模式
"""

from typing import Dict, List
from ..core.v4_mcp_server import V4ContextGuidanceServer


# 全局服务实例
_server_instance = None


def get_server_instance() -> V4ContextGuidanceServer:
    """获取服务实例（单例模式）"""
    global _server_instance
    if _server_instance is None:
        _server_instance = V4ContextGuidanceServer()
    return _server_instance


def execute_checkresult_v4_modification_task(checkresult_path: str) -> Dict:
    """
    执行checkresult-v4修改任务

    这是第一阶段的核心工具，IDE AI通过此工具启动修改任务
    支持灵活的路径输入格式，自动处理路径标准化和验证

    Args:
        checkresult_path: checkresult-v4目录路径（支持多种格式）

    Returns:
        Dict: 任务初始化结果和第一批修改指令

    Examples:
        支持的输入格式:
        - "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/checkresult-v4"
        - "（docs\\features\\F007-建立Commons库的治理机制-20250610\\nexus万用插座\\design\\v1\\checkresult-v4）"
        - 相对路径或绝对路径
    """

    server = get_server_instance()

    try:
        # 1. 路径预处理和验证
        normalized_path = _normalize_and_validate_checkresult_path(checkresult_path)

        if "error" in normalized_path:
            return {
                "status": "path_validation_error",
                "message": f"❌ 路径验证失败: {normalized_path['error']}",
                "suggestion": normalized_path.get("suggestion", ""),
                "original_input": checkresult_path
            }

        # 2. 初始化任务
        init_result = server.initialize_modification_task(normalized_path["validated_path"])

        if init_result["remaining_modifications"] == 0:
            return {
                "status": "task_already_completed",
                "message": "✅ 所有修改已完成",
                "summary": init_result
            }

        # 获取第一批修改指令
        first_batch = server.get_next_modification_batch()

        return {
            "status": "task_initialized",
            "message": f"📋 任务已初始化，准备执行 {len(first_batch['batch_info']['modifications'])} 个修改",
            "task_summary": init_result,
            "current_batch": first_batch,
            "ide_ai_instructions": first_batch["ide_ai_instructions"]
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"❌ 任务初始化失败: {str(e)}",
            "error_type": type(e).__name__
        }


def get_next_modification_batch(checkresult_path: str, batch_size: int = 3) -> Dict:
    """
    获取下一批修改指令

    支持MCP断线重连，IDE AI可以随时调用此工具继续任务
    """

    server = get_server_instance()

    try:
        # 如果服务未初始化，尝试重新初始化（断线重连场景）
        if not server.server_state["initialized"]:
            init_result = server.initialize_modification_task(checkresult_path)
            if "error" in init_result:
                return init_result

        # 获取下一批修改
        next_batch = server.get_next_modification_batch(batch_size)

        return next_batch

    except Exception as e:
        return {
            "status": "error",
            "message": f"❌ 获取修改批次失败: {str(e)}",
            "suggestion": "请检查checkresult_path是否正确，或尝试重新初始化任务"
        }


def validate_and_continue_modifications(
    checkresult_path: str,
    completed_modifications: List[Dict],
    session_id: str = None
) -> Dict:
    """
    验证修改结果并继续下一批

    IDE AI完成修改后调用此工具进行验证和获取下一批指令
    """

    server = get_server_instance()

    try:
        # 验证修改结果并获取下一批
        result = server.validate_and_continue(completed_modifications)

        # 添加会话信息
        if session_id:
            result["session_id"] = session_id

        return result

    except Exception as e:
        return {
            "status": "error",
            "message": f"❌ 验证和继续失败: {str(e)}",
            "suggestion": "请检查completed_modifications格式是否正确"
        }


# MCP工具注册（示例）
MCP_TOOLS = [
    {
        "name": "execute_checkresult_v4_modification_task",
        "description": "执行checkresult-v4修改任务",
        "function": execute_checkresult_v4_modification_task,
        "parameters": {
            "checkresult_path": {
                "type": "string",
                "description": "checkresult-v4目录的完整路径",
                "required": True
            }
        }
    },
    {
        "name": "get_next_modification_batch",
        "description": "获取下一批修改指令",
        "function": get_next_modification_batch,
        "parameters": {
            "checkresult_path": {
                "type": "string",
                "description": "checkresult-v4目录路径",
                "required": True
            },
            "batch_size": {
                "type": "integer",
                "description": "批次大小，默认3",
                "required": False,
                "default": 3
            }
        }
    },
    {
        "name": "validate_and_continue_modifications",
        "description": "验证修改结果并继续下一批",
        "function": validate_and_continue_modifications,
        "parameters": {
            "checkresult_path": {
                "type": "string",
                "description": "checkresult-v4目录路径",
                "required": True
            },
            "completed_modifications": {
                "type": "array",
                "description": "已完成的修改列表",
                "required": True
            },
            "session_id": {
                "type": "string",
                "description": "会话ID（可选）",
                "required": False
            }
        }
    }
]
```

### 5. 路径处理工具函数
```python
def _normalize_and_validate_checkresult_path(checkresult_path: str) -> Dict:
    """
    标准化和验证checkresult路径

    支持多种输入格式，自动处理路径清理和验证
    处理用户可能输入的各种路径格式
    """

    import os
    import re
    import glob

    try:
        # 1. 清理路径字符串
        cleaned_path = checkresult_path.strip()

        # 移除可能的中文括号和英文括号
        if cleaned_path.startswith('（') and cleaned_path.endswith('）'):
            cleaned_path = cleaned_path[1:-1]
        elif cleaned_path.startswith('(') and cleaned_path.endswith(')'):
            cleaned_path = cleaned_path[1:-1]

        # 移除引号
        cleaned_path = cleaned_path.strip('"\'')

        # 2. 路径格式标准化
        # 统一使用正斜杠（跨平台兼容）
        normalized_path = cleaned_path.replace('\\', '/')

        # 3. 路径存在性验证
        if not os.path.exists(normalized_path):
            # 尝试相对于当前工作目录
            if not os.path.isabs(normalized_path):
                abs_path = os.path.abspath(normalized_path)
                if os.path.exists(abs_path):
                    normalized_path = abs_path
                else:
                    return {
                        "error": f"路径不存在: {normalized_path}",
                        "suggestion": "请检查路径是否正确，确保checkresult-v4目录存在",
                        "original_input": checkresult_path
                    }

        # 4. 验证是否为checkresult目录
        if not ('checkresult' in normalized_path.lower()):
            return {
                "error": f"路径似乎不是checkresult目录: {normalized_path}",
                "suggestion": "请确保路径指向checkresult-v4目录",
                "original_input": checkresult_path
            }

        # 5. 验证目录内容
        if not os.path.isdir(normalized_path):
            return {
                "error": f"路径不是目录: {normalized_path}",
                "suggestion": "请确保路径指向一个目录而不是文件",
                "original_input": checkresult_path
            }

        # 检查是否包含检查报告文件
        report_files = glob.glob(os.path.join(normalized_path, "*_检查报告.md"))

        if not report_files:
            return {
                "error": f"目录中没有找到检查报告文件: {normalized_path}",
                "suggestion": "请确保目录中包含*_检查报告.md文件",
                "original_input": checkresult_path,
                "found_files": os.listdir(normalized_path) if os.path.exists(normalized_path) else []
            }

        return {
            "validated_path": normalized_path,
            "original_input": checkresult_path,
            "found_reports": len(report_files),
            "report_files": [os.path.basename(f) for f in report_files],
            "normalization_applied": cleaned_path != checkresult_path
        }

    except Exception as e:
        return {
            "error": f"路径验证失败: {str(e)}",
            "suggestion": "请检查路径格式是否正确",
            "original_input": checkresult_path
        }
```

---

**创建时间**: 2025-06-18
**维护说明**: 基于V4设计文档和DRY原则，提供可直接使用的代码实现模板，支持灵活的路径输入格式
