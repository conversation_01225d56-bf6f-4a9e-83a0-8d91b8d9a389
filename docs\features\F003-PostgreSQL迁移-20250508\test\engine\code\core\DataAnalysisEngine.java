package org.xkong.cloud.business.internal.core.test.engine.core;

import org.xkong.cloud.business.internal.core.test.engine.annotations.AnalysisComponent;
import org.xkong.cloud.business.internal.core.test.engine.interfaces.AnalysisStrategy;
import org.xkong.cloud.business.internal.core.test.engine.interfaces.AnalysisContext;
import org.xkong.cloud.business.internal.core.test.engine.interfaces.AnalysisResult;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Constructor;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 数据分析引擎
 * 
 * 核心引擎，负责：
 * 1. 自动发现分析策略组件
 * 2. 管理分析执行流程
 * 3. 协调数据收集和输出
 * 4. 提供配置驱动的行为控制
 */
public class DataAnalysisEngine {
    
    private static final Logger log = LoggerFactory.getLogger(DataAnalysisEngine.class);
    
    private final CollectionEngine collectionEngine;
    private final AnalysisEngine analysisEngine;
    private final OutputEngine outputEngine;
    private final ConfigurationEngine configurationEngine;
    
    private final ObjectMapper jsonMapper;
    private final ExecutorService executorService;
    
    public DataAnalysisEngine() {
        this.collectionEngine = new CollectionEngine();
        this.analysisEngine = new AnalysisEngine();
        this.outputEngine = new OutputEngine();
        this.configurationEngine = new ConfigurationEngine();
        
        this.jsonMapper = createJsonMapper();
        this.executorService = Executors.newCachedThreadPool();
    }
    
    /**
     * 执行完整的数据分析流程
     * 
     * @param baseOutputPath 输出基础路径
     * @param contextData 上下文数据
     * @return 执行结果摘要
     */
    public AnalysisExecutionSummary executeFullAnalysis(String baseOutputPath, Object... contextData) {
        log.info("开始执行完整数据分析流程");
        
        long startTime = System.currentTimeMillis();
        String timestamp = getCurrentTimestamp();
        String outputDirectory = baseOutputPath + "/" + timestamp;
        
        try {
            // 1. 创建输出目录
            createOutputDirectory(outputDirectory);
            
            // 2. 构建分析上下文
            AnalysisContext context = buildAnalysisContext(outputDirectory, timestamp, contextData);
            
            // 3. 发现分析策略
            List<AnalysisStrategy> strategies = discoverAnalysisStrategies();
            log.info("发现 {} 个分析策略", strategies.size());
            
            // 4. 执行分析策略
            List<AnalysisResult> results = executeAnalysisStrategies(strategies, context);
            
            // 5. 输出分析结果
            outputEngine.outputResults(results, outputDirectory);
            
            // 6. 创建latest软链接
            createLatestSymlink(baseOutputPath, timestamp);
            
            // 7. 生成执行摘要
            AnalysisExecutionSummary summary = createExecutionSummary(
                strategies, results, startTime, outputDirectory
            );
            
            log.info("数据分析流程完成，耗时: {}ms", summary.getTotalExecutionTimeMs());
            return summary;
            
        } catch (Exception e) {
            log.error("数据分析流程执行失败", e);
            throw new RuntimeException("Analysis execution failed", e);
        }
    }
    
    /**
     * 自动发现分析策略
     */
    private List<AnalysisStrategy> discoverAnalysisStrategies() {
        List<AnalysisStrategy> strategies = new ArrayList<>();
        
        // 扫描classpath中所有标记了@AnalysisComponent的类
        Set<Class<?>> componentClasses = scanForAnalysisComponents();
        
        for (Class<?> componentClass : componentClasses) {
            try {
                if (AnalysisStrategy.class.isAssignableFrom(componentClass)) {
                    AnalysisStrategy strategy = createStrategyInstance(componentClass);
                    
                    // 检查是否启用
                    if (isStrategyEnabled(strategy, componentClass)) {
                        strategies.add(strategy);
                        log.debug("注册分析策略: {}", strategy.getStrategyName());
                    }
                }
            } catch (Exception e) {
                log.warn("无法创建分析策略实例: {}", componentClass.getName(), e);
            }
        }
        
        // 按优先级排序
        strategies.sort(Comparator.comparingInt(AnalysisStrategy::getPriority));
        
        return strategies;
    }
    
    /**
     * 执行分析策略
     */
    private List<AnalysisResult> executeAnalysisStrategies(
            List<AnalysisStrategy> strategies, 
            AnalysisContext context) {
        
        List<AnalysisResult> results = new ArrayList<>();
        Map<String, AnalysisResult> completedResults = new ConcurrentHashMap<>();
        
        // 构建依赖图
        Map<String, Set<String>> dependencyGraph = buildDependencyGraph(strategies);
        
        // 按依赖顺序执行
        Set<String> executed = new HashSet<>();
        Queue<AnalysisStrategy> readyToExecute = new LinkedList<>();
        
        // 找出没有依赖的策略
        for (AnalysisStrategy strategy : strategies) {
            if (dependencyGraph.get(strategy.getStrategyName()).isEmpty()) {
                readyToExecute.offer(strategy);
            }
        }
        
        while (!readyToExecute.isEmpty()) {
            AnalysisStrategy strategy = readyToExecute.poll();
            
            if (!strategy.canExecute(context)) {
                log.info("跳过分析策略: {} (执行条件不满足)", strategy.getStrategyName());
                continue;
            }
            
            try {
                log.info("执行分析策略: {}", strategy.getStrategyName());
                long strategyStartTime = System.currentTimeMillis();
                
                AnalysisResult result = strategy.analyze(context);
                
                long executionTime = System.currentTimeMillis() - strategyStartTime;
                log.info("分析策略 {} 执行完成，耗时: {}ms", strategy.getStrategyName(), executionTime);
                
                results.add(result);
                completedResults.put(strategy.getStrategyName(), result);
                executed.add(strategy.getStrategyName());
                
                // 检查是否有新的策略可以执行
                for (AnalysisStrategy pendingStrategy : strategies) {
                    String pendingName = pendingStrategy.getStrategyName();
                    if (!executed.contains(pendingName) && 
                        !readyToExecute.contains(pendingStrategy)) {
                        
                        Set<String> dependencies = dependencyGraph.get(pendingName);
                        if (executed.containsAll(dependencies)) {
                            readyToExecute.offer(pendingStrategy);
                        }
                    }
                }
                
            } catch (Exception e) {
                log.error("分析策略执行失败: {}", strategy.getStrategyName(), e);
                // 创建失败结果
                AnalysisResult failedResult = createFailedResult(strategy, e);
                results.add(failedResult);
            }
        }
        
        return results;
    }
    
    /**
     * 构建分析上下文
     */
    private AnalysisContext buildAnalysisContext(String outputDirectory, String timestamp, Object... contextData) {
        return new DefaultAnalysisContext(outputDirectory, timestamp, contextData);
    }
    
    /**
     * 扫描分析组件
     */
    private Set<Class<?>> scanForAnalysisComponents() {
        // 这里应该实现classpath扫描逻辑
        // 为了简化，这里返回一个预定义的列表
        Set<Class<?>> components = new HashSet<>();
        
        // 可以通过反射扫描特定包下的所有类
        // 或者通过配置文件指定要加载的策略类
        
        return components;
    }
    
    /**
     * 创建策略实例
     */
    private AnalysisStrategy createStrategyInstance(Class<?> strategyClass) throws Exception {
        Constructor<?> constructor = strategyClass.getDeclaredConstructor();
        return (AnalysisStrategy) constructor.newInstance();
    }
    
    /**
     * 检查策略是否启用
     */
    private boolean isStrategyEnabled(AnalysisStrategy strategy, Class<?> strategyClass) {
        AnalysisComponent annotation = strategyClass.getAnnotation(AnalysisComponent.class);
        if (annotation != null) {
            // 首先检查注解中的默认设置
            boolean defaultEnabled = annotation.enabled();
            
            // 然后检查配置文件中的设置（如果有的话）
            String strategyName = annotation.name().isEmpty() ? 
                                 strategy.getStrategyName() : 
                                 annotation.name();
            
            return configurationEngine.isStrategyEnabled(strategyName, defaultEnabled);
        }
        
        return strategy.isEnabled();
    }
    
    // 其他辅助方法...
    
    private ObjectMapper createJsonMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        return mapper;
    }
    
    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss"));
    }
    
    private void createOutputDirectory(String outputDirectory) throws IOException {
        Files.createDirectories(Paths.get(outputDirectory));
    }
    
    private void createLatestSymlink(String baseOutputPath, String timestamp) {
        // 实现软链接创建逻辑
    }
}
