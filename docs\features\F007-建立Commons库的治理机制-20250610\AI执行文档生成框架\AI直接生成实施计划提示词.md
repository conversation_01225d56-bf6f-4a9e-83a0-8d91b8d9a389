# AI严格生成实施计划文档的提示词

## 使用说明
这是用于指导AI严格按照模板生成实施计划文档的提示词。配合用户提供的设计文档链接和输出目录使用。

---

## 🎯 标准执行提示词

```
请严格按照以下流程为项目生成完整的实施计划文档：

## 📋 执行任务
1. 学习设计文档内容，理解项目需求和架构设计
2. 调研当前项目的真实代码架构  
3. 基于设计文档和架构调研，在指定目录生成完整的实施计划文档

## 🔍 第一步：深度代码架构调研（必须执行）

在生成任何文档前，必须先深度调研当前项目的真实架构：

### 1.1 项目整体结构扫描
```bash
# 使用 codebase_search 工具
# 目标：理解项目整体结构、模块分布、技术栈
```

### 1.2 核心模块依赖分析  
```bash
# 使用 grep_search 工具
# 目标：分析核心模块间的依赖关系、接口设计、数据流
```

### 1.3 配置和接口详查
```bash
# 使用 read_file 工具  
# 目标：详细了解配置文件、核心接口、数据模型
```

### 1.4 关键代码逻辑理解
```bash
# 使用 file_search 工具
# 目标：定位关键业务逻辑、设计模式、代码组织方式
```

## 📊 第二步：项目复杂度智能评估

基于架构调研结果和设计文档，自动评估项目复杂度：

### 评估维度
- **模块数量**: 统计核心业务模块数量
- **依赖复杂度**: 分析模块间依赖关系（线性/树状/网状）
- **代码修改规模**: 基于设计文档估算代码变更量
- **技术栈复杂度**: 评估涉及的技术栈和框架复杂度
- **集成复杂度**: 评估与现有系统的集成复杂度

### 复杂度判定标准
```
简单项目（L1）: ≤5个模块，线性依赖，≤1000行代码修改
中等项目（L2）: 6-10个模块，树状依赖，1000-5000行代码修改  
复杂项目（L3）: >10个模块，网状依赖，>5000行代码修改
```

## 📝 第三步：严格按照模板生成实施计划文档

根据复杂度评估结果，生成对应的实施计划文档：

### L1简单项目 - 生成单一实施计划文档

**文件**: `{输出目录}/01-{项目名称}-实施计划.md`

```markdown
# {项目名称} - AI执行实施计划

## 📋 项目概览

- **项目名称**: [从设计文档提取]
- **技术栈**: [基于架构调研的实际技术栈]
- **核心目标**: [从设计文档提取核心功能目标]
- **复杂度评估**: L1简单项目 (评估详情: [具体评估分析])
- **预估工期**: [基于复杂度的合理时间估算]
- **风险级别**: [基于技术栈和团队经验评估]

## 🔍 现有架构调研结果

### 项目结构分析
[基于 codebase_search 的实际项目结构]
```
[实际的目录树结构]
```

### 核心模块识别
[基于架构调研识别的关键模块]
- **[模块1名称]**: [功能职责] - 位置: `[实际路径]`
- **[模块2名称]**: [功能职责] - 位置: `[实际路径]`

### 技术架构栈
[基于实际代码分析的技术栈]
- **后端框架**: [实际使用的框架及版本]
- **数据存储**: [实际的数据库和缓存方案]
- **配置管理**: [实际的配置文件组织方式]

### 接口设计现状
[基于代码调研的API接口分析]
- **现有接口**: [实际存在的关键接口]
- **数据模型**: [实际的数据结构定义]
- **依赖关系**: [实际的模块依赖关系图]

## 🎯 具体实施目标

### 功能目标
[基于设计文档提取的具体功能要求]
1. [功能点1]: [详细描述]
2. [功能点2]: [详细描述]  
3. [功能点3]: [详细描述]

### 技术目标
[基于设计文档和架构分析的技术实现目标]
- [技术实现点1]
- [技术实现点2]

### 验收标准
[明确的可验证标准]
- [验收标准1]
- [验收标准2]
- [性能指标要求]

## 📅 详细实施步骤

### 阶段1: 环境准备和基础设施 (预估1天)

#### 1.1 开发环境检查
- [ ] **环境版本验证**
  ```bash
  # 验证Java版本 (基于项目实际需求)
  java -version
  # 验证构建工具版本
  [实际构建工具] --version
  # 验证其他必要工具
  [基于项目实际需要的工具检查]
  ```

- [ ] **项目代码准备**
  ```bash
  # 创建功能分支
  git checkout -b feature/[基于设计文档的功能名称]
  # 验证基础编译
  [实际的编译命令]
  # 验证现有测试
  [实际的测试命令]
  ```

#### 1.2 依赖配置更新
- [ ] **依赖管理** (修改 `[实际的依赖配置文件]`)
  ```[实际的配置格式]
  # 基于设计文档需求添加的具体依赖
  [具体的依赖配置]
  ```

- [ ] **配置文件更新** (修改 `[实际的配置文件路径]`)
  ```[实际的配置格式]
  # 基于功能需求的具体配置
  [具体的配置内容]
  ```

### 阶段2: 核心功能实施 (预估[基于复杂度的天数])

#### 2.1 [功能模块1名称] 实施 (≤50行代码)
- **实施目标**: [从设计文档提取的具体目标]
- **修改文件**: `[基于架构调研的实际文件路径]`
- **具体实施内容**:

  **现有代码** (基于 read_file 调研):
  ```[实际代码语言]
  [基于架构调研的实际现有代码片段]
  ```

  **修改为**:
  ```[实际代码语言]
  // 添加必要的导入语句 (基于实际技术栈)
  [实际需要的导入语句]
  
  [基于设计文档的具体实现代码]
  // 包含完整的异常处理和日志记录
  ```

- **验证步骤**:
  ```bash
  # 编译验证 (使用实际编译命令)
  [实际编译命令]
  # 单元测试 (基于实际测试框架)
  [实际测试命令]
  # 功能验证 (基于具体功能的验证方法)
  [具体验证步骤]
  ```

#### 2.2 [功能模块2名称] 实施 (≤50行代码)
[按照相同格式，基于设计文档和架构调研的具体实施内容]

### 阶段3: 集成测试与验证 (预估1天)

#### 3.1 完整功能测试
- [ ] **单元测试完善**
  ```[实际测试代码语言]
  // 基于实际测试框架的测试用例
  [具体的测试实现]
  ```

- [ ] **集成测试验证**
  ```bash
  # 启动应用 (使用实际启动命令)
  [实际启动命令]
  # 功能验证 (基于设计文档的验收标准)
  [具体验证步骤]
  ```

#### 3.2 性能与稳定性验证
- [ ] **性能指标验证**: [基于设计文档的性能要求]
- [ ] **稳定性测试**: [基于系统特点的稳定性验证]

## 🚧 操作边界控制

### ✅ 允许修改的文件范围
[基于架构调研的实际可修改文件]
- `[实际业务代码路径]` - 业务逻辑实现
- `[实际配置文件路径]` - 功能配置
- `[实际测试代码路径]` - 测试用例
- **修改限制**: 单次修改 ≤ 50行代码

### ❌ 严格禁止修改  
[基于架构分析的禁止修改范围]
- `[实际核心模块路径]` - 其他模块的核心接口
- `[实际生产配置路径]` - 生产环境配置
- `[实际第三方依赖]` - 第三方库源码
- **禁止操作**: 跨模块全局性修改

## ✅ 验证锚点系统

### 每步验证标准
1. **编译验证**: `[实际编译命令]` 必须成功
2. **测试验证**: `[实际测试命令]` 所有测试通过  
3. **功能验证**: [基于设计文档的功能验证方法]
4. **性能验证**: [基于设计文档的性能要求]

### 质量门禁
- 编译成功率: 100%
- 测试通过率: 100%  
- 代码覆盖率: >80%
- 功能验收: 符合设计文档要求

## ⚠️ 风险评估与应对方案

### 🔴 P0级别风险 (立即回滚)
| 风险场景 | 触发条件 | 立即回滚操作 |
|---------|----------|-------------|
| 编译失败 | 编译命令执行失败且10分钟无法修复 | `git checkout -f [上一稳定commit]` |
| 服务启动失败 | 应用无法正常启动 | 立即回滚代码并验证服务恢复 |
| 核心功能异常 | 影响主要业务流程 | 停止实施，回滚到稳定版本 |

### 🟡 P1级别风险 (1小时内决策)  
| 风险场景 | 触发条件 | 处理策略 |
|---------|----------|----------|
| 测试失败 | 测试通过率 < 80% | 分析失败原因，修复或回滚 |
| 性能下降 | 响应时间增加 > 20% | 性能调优或功能回滚 |
| 集成异常 | 与其他模块集成出现问题 | 修复集成问题或隔离变更 |

### 🟢 P2级别风险 (计划处理)
| 风险场景 | 触发条件 | 处理策略 |
|---------|----------|----------|
| 代码规范问题 | 静态检查发现问题 | 规范修复，不阻塞主流程 |
| 文档不同步 | 代码与文档不一致 | 更新文档，保持同步 |

### 回滚执行命令
```bash
# P0级别：立即回滚（5分钟内）
git log --oneline -5
git checkout -f [稳定commit hash]  
[实际的重新构建命令]
[实际的服务重启命令]

# P1级别：计划回滚（1小时内）
git stash push -m "备份当前修改"
git checkout [稳定分支]
[实际的验证命令]
# 分析问题后决定是修复还是回滚
```

## 📋 执行检查清单

### 实施前检查
- [ ] 设计文档已理解
- [ ] 代码架构已调研  
- [ ] 开发环境已验证
- [ ] Git分支已创建: `feature/[功能名称]`

### 每步实施检查
- [ ] 代码修改 ≤ 50行
- [ ] 导入语句完整
- [ ] 异常处理完善  
- [ ] 编译验证通过: `[实际编译命令]`
- [ ] 单元测试通过
- [ ] 功能验证符合预期

### 完成验收检查
- [ ] 所有单元测试通过: `[实际测试命令]`
- [ ] 集成测试验证完成
- [ ] 功能验收符合设计文档
- [ ] 性能指标达到要求
- [ ] 代码审核完成

## 📦 部署与配置管理

### 构建配置
```[实际构建配置格式]
# 基于项目实际需要的构建配置
[具体构建配置内容]
```

### 运行配置  
```[实际配置格式]
# 基于功能需求的运行时配置
[具体运行配置内容]
```

### 数据库变更 (如需要)
```sql
-- 基于设计文档的数据库变更
[具体的DDL语句]
```

---

## 📊 执行状态跟踪

### 实施进度
- [ ] 阶段1完成: 环境准备 ✓/✗
- [ ] 阶段2完成: 核心功能开发 ✓/✗  
- [ ] 阶段3完成: 集成测试验证 ✓/✗
- [ ] 整体验收: 通过设计文档要求 ✓/✗

### 质量指标监控
- 代码覆盖率: ____% (目标: >80%)
- 测试通过率: ____% (目标: 100%)
- 功能完成度: ____% (基于设计文档)
- 性能达标率: ____% (基于设计文档要求)

---

**严格执行原则**:
1. **架构优先**: 基于真实架构调研制定实施方案
2. **设计驱动**: 严格按照设计文档要求实施
3. **边界控制**: 每步修改≤50行，禁止跨模块修改
4. **渐进验证**: 每步完成立即验证，确保质量
5. **风险优先**: 遇到P0风险立即回滚，不犹豫

**请严格按照上述实施计划执行，每完成一个步骤立即进行验证！**
```

### L2中等项目 - 生成扩展文档体系

当评估为中等项目时，额外生成以下文档：

**文件**: `{输出目录}/02-{项目名称}-架构设计方案.md`
**文件**: `{输出目录}/03-{项目名称}-模块集成计划.md`
**文件**: `{输出目录}/04-{项目名称}-风险控制方案.md`

### L3复杂项目 - 生成完整文档矩阵

当评估为复杂项目时，按模块分别生成：

**总体规划文档**:
- `{输出目录}/10-{项目名称}-总体架构方案.md`
- `{输出目录}/11-{项目名称}-实施策略规划.md`

**模块实施文档** (按实际模块生成):
- `{输出目录}/20-{模块1名称}-详细实施计划.md`
- `{输出目录}/21-{模块1名称}-测试验证方案.md`
- `{输出目录}/22-{模块2名称}-详细实施计划.md`
- `{输出目录}/23-{模块2名称}-测试验证方案.md`

**集成验证文档**:
- `{输出目录}/40-{项目名称}-系统集成测试.md`
- `{输出目录}/41-{项目名称}-端到端验证.md`

## 🎯 严格执行要求

1. **必须先调研**: 使用工具深度调研项目架构，不能依赖文档描述
2. **基于真实架构**: 所有实施计划必须基于实际代码结构制定  
3. **设计文档驱动**: 功能目标和验收标准严格基于设计文档
4. **输出目录准确**: 所有文档必须生成到用户指定的输出目录
5. **格式严格一致**: 严格按照上述模板格式生成文档内容
6. **内容具体可执行**: 所有步骤都必须包含具体的命令、代码、验证方法

**执行顺序: 架构调研 → 复杂度评估 → 按模板生成文档 → 保存到指定目录**
```

---

## 🎯 用户使用方式

根据您的使用习惯，标准工作流程是：

1. **提供设计文档链接**: "设计文档: [链接]"
2. **提供提示词链接**: "提示词: [本文件链接]"  
3. **指定输出目录**: "输出目录: docs/xxx/实施计划/"
4. **AI自动执行**: 调研架构 → 学习设计 → 严格按模板生成文档

## 🌟 提示词特点

✅ **配合您的习惯**: 专为"设计文档+提示词+输出目录"的工作流设计
✅ **强制架构调研**: 要求AI先深度调研真实代码架构
✅ **设计文档驱动**: 基于设计文档内容生成功能目标和验收标准  
✅ **严格模板格式**: 确保生成的文档格式统一、内容完整
✅ **智能复杂度适配**: 根据项目复杂度自动选择文档生成策略
✅ **输出目录精确**: 严格按照指定目录生成对应文档文件

**这样AI就会严格按照您的提示词，在您指定的目录中生成完整的实施计划文档！** 