# {feature_name}

## 文档元数据

- **功能ID**: {feature_id}
- **功能名称**: {feature_name}
- **项目代码**: {project_code}
- **创建日期**: {created_date}
- **状态**: {status} (planned/active_development/stable_maintenance/completed_archived)
- **版本**: 1.0.0
- **作者**: {authors}
- **关键词**: {keywords}
- **相关特征**: {related_features}

## 概述

{feature_description}

## 目录结构

```
{feature_id}-{feature_name}-{date}/
├── README.md                    # 本文件 - 功能概述和索引
├── requirements/               # 需求文档
│   ├── requirements.md         # 功能需求说明
│   ├── user-stories.md         # 用户故事
│   └── acceptance-criteria.md  # 验收标准
├── design/                     # 设计文档
│   ├── architecture.md         # 架构设计
│   ├── database-schema.md      # 数据库设计
│   ├── interface-design.md     # 接口设计
│   └── diagrams/              # 设计图表
├── plan/                       # 实施计划
│   ├── implementation-plan.md  # 实施计划
│   ├── milestone-plan.md       # 里程碑计划
│   └── risk-analysis.md        # 风险分析
├── api/                        # API文档
│   ├── api-specification.md    # API规范
│   ├── endpoints.md           # 端点文档
│   └── examples/              # API示例
├── test/                       # 测试文档
│   ├── test-strategy.md        # 测试策略
│   ├── test-cases.md          # 测试用例
│   └── test-results/          # 测试结果
├── guide/                      # 用户指南
│   ├── user-guide.md          # 用户指南
│   ├── developer-guide.md     # 开发者指南
│   └── deployment-guide.md    # 部署指南
└── code/                       # 代码示例和脚本
    ├── examples/              # 代码示例
    ├── scripts/               # 脚本文件
    └── configurations/        # 配置文件
```

## 快速导航

### 需求文档
- [功能需求](requirements/requirements.md) - 详细的功能需求说明
- [用户故事](requirements/user-stories.md) - 用户使用场景
- [验收标准](requirements/acceptance-criteria.md) - 功能验收条件

### 设计文档
- [架构设计](design/architecture.md) - 整体架构设计
- [数据库设计](design/database-schema.md) - 数据库Schema设计
- [接口设计](design/interface-design.md) - API接口设计

### 实施文档
- [实施计划](plan/implementation-plan.md) - 详细实施步骤
- [里程碑计划](plan/milestone-plan.md) - 开发里程碑
- [风险分析](plan/risk-analysis.md) - 风险评估和应对

### API文档
- [API规范](api/api-specification.md) - 完整API规范
- [端点文档](api/endpoints.md) - 具体端点说明
- [API示例](api/examples/) - 使用示例

### 测试文档
- [测试策略](test/test-strategy.md) - 测试方法和策略
- [测试用例](test/test-cases.md) - 详细测试用例
- [测试结果](test/test-results/) - 测试执行结果

### 用户指南
- [用户指南](guide/user-guide.md) - 最终用户使用指南
- [开发者指南](guide/developer-guide.md) - 开发者集成指南
- [部署指南](guide/deployment-guide.md) - 部署和配置指南

## 功能状态

- **开发状态**: {status}
- **完成度**: {completion_percentage}%
- **最后更新**: {last_updated}

## 依赖关系

### 前置依赖
{prerequisite_dependencies}

### 关联功能
{related_features_details}

### 技术依赖
{technical_dependencies}

## 变更历史

| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| 1.0.0 | {created_date} | 初始创建 | {authors} |

## AI记忆系统集成

- **L3-Index路径**: `docs/ai-memory/L3-index/feature-index/by-project/{project_code}/feature-{feature_id}.md`
- **注意力命令**: `@L3:feature:{feature_id}`
- **记忆激活**: `@L2:project:{project_code}`

## 注意事项

1. **文档更新**: 任何功能变更都应该同步更新相关文档
2. **状态同步**: 开发状态变更时需要更新`docs/feature-status.json`
3. **记忆系统**: 重要变更后需要同步AI记忆索引(`@sync:feature:{feature_id}`)

## 联系信息

- **负责人**: {project_owner}
- **开发团队**: {development_team}
- **技术支持**: {technical_support} 