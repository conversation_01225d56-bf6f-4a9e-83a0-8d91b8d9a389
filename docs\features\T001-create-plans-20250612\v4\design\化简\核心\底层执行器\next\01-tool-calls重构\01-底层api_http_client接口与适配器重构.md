# 01-底层api_http_client接口与适配器重构细化设计（伪代码级）

## 1. 现有实现溯源
- 文件：`tools/ace/src/api_management/core/api_http_client.py`
- 主要方法：
  - `def execute_api_request(self, ...)`（第118行）
  - `def _build_interface_specific_payload(self, ...)`（第228行）
- 现状：
  - execute_api_request参数签名固定，无法透传tools/tool_choice/logit_bias等高级API参数。
  - _build_interface_specific_payload采用大量if/elif分支，耦合不同API厂商逻辑。
  - 未采用适配器模式，扩展性和维护性差。

## 2. 目标结构与接口（伪代码级）

### 2.1 目录结构
```
tools/ace/src/api_management/core/api_adapters/
    base_adapter.py
    openai_adapter.py
    gemini_adapter.py
    anthropic_adapter.py
    ...
```

### 2.2 BaseAPIAdapter抽象类
```python
# base_adapter.py
class BaseAPIAdapter:
    def build_payload(self, model_name, content, role=None, json_output=False, json_schema=None, **kwargs):
        raise NotImplementedError
```

### 2.3 具体适配器实现（以OpenAI为例）
```python
# openai_adapter.py
from .base_adapter import BaseAPIAdapter
class OpenAIAdapter(BaseAPIAdapter):
    def build_payload(self, model_name, content, role=None, json_output=False, json_schema=None, **kwargs):
        payload = {
            'model': model_name,
            'messages': [{'role': role or 'user', 'content': content}],
            'max_tokens': kwargs.get('max_tokens', 2048),
            'temperature': kwargs.get('temperature', 0.7),
            'top_p': kwargs.get('top_p', 1.0)
        }
        # Tool Calling支持
        if kwargs.get('tools'):
            payload['tools'] = kwargs['tools']
            payload['tool_choice'] = kwargs.get('tool_choice', 'auto')
        # logit_bias等高级参数
        if kwargs.get('logit_bias'):
            payload['logit_bias'] = kwargs['logit_bias']
        return payload
```

### 2.4 APIHttpClient重构核心
```python
# api_http_client.py
from .api_adapters import OpenAIAdapter, GeminiAdapter, AnthropicAdapter
class APIHttpClient:
    ...
    def execute_api_request(self, api_key, api_url, model_name, interface_type, content=None, role=None, json_output=False, json_schema=None, **kwargs):
        # 1. 选择适配器
        adapter = self._get_adapter(interface_type)
        # 2. 构建payload
        payload = adapter.build_payload(model_name, content, role, json_output, json_schema, **kwargs)
        # 3. 构建headers
        headers = self._build_headers(api_key, interface_type)
        # 4. 发送请求
        response = requests.post(api_url, headers=headers, json=payload, timeout=self._get_timeout_for_model(model_name))
        # 5. 处理响应
        return self._process_response(response, ...)

    def _get_adapter(self, interface_type):
        if interface_type == 'openai':
            return OpenAIAdapter()
        elif interface_type == 'gemini':
            return GeminiAdapter()
        elif interface_type == 'anthropic':
            return AnthropicAdapter()
        else:
            return OpenAIAdapter()  # 默认兜底
```

### 2.5 兼容性兜底与参数透传
- execute_api_request保留所有原有参数，新增**kwargs，旧代码不受影响。
- 未实现的API类型自动回退到OpenAIAdapter或默认适配器。
- 适配器build_payload方法支持所有主流API参数，未来可扩展。

## 3. 需变更/新建/删除内容（精确到代码）
- 修改：
  - `api_http_client.py`：execute_api_request签名、主流程、_build_interface_specific_payload拆分
- 新建：
  - `api_adapters/`目录及BaseAPIAdapter、OpenAIAdapter、GeminiAdapter等
- 删除：
  - `_build_interface_specific_payload`方法中的所有API分支实现，迁移到各自适配器
- 相关调用点：
  - 所有调用execute_api_request的地方需适配新参数传递方式

## 4. 兼容性与测试建议
- execute_api_request保留原有参数，**kwargs为可选，旧代码不受影响。
- 适配器模式下，未实现的API类型自动回退到默认处理。
- 建议：
  - 单元测试：各适配器build_payload的参数覆盖。
  - 集成测试：execute_api_request全路径回归。
  - 回归测试：所有主流API厂商的调用兼容性。

## 5. 代码引用与路径
- `tools/ace/src/api_management/core/api_http_client.py`
- 新增：`tools/ace/src/api_management/core/api_adapters/`
- 相关调用：所有AI服务管理器、执行器调用execute_api_request的地方 