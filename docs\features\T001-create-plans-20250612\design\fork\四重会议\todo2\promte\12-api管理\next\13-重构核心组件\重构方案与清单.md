# ApiMemoryManager主权重构方案（全景梳理与对接）

## 一、现有架构全景与数据流

### 1. 主要核心类/模块及职责
| 类/模块 | 主要职责 | 典型数据/状态 |
|---------|----------|--------------|
| ApiMemoryManager | API Key/配置/质量/队列/异常/用量的唯一主权管理 | cachemap, rolemap, modelmap, quality_score, status_boolean |
| APIAccountDatabase | API Key/配置/用量/质量的持久化 | SQLite表 |
| GlobalAPIConnectionPool | API连接池、负载均衡、健康监控 | 连接状态、健康分数 |
| APILifecycleManager | API Key生命周期、失效检测、删除/恢复 | 状态、失效标记 |
| DailyUsageLimitManager | 用量统计、用量重置 | 用量计数 |
| QualityAssuranceGuard | 质量评测、分数计算 | 质量分数、历史 |
| URLQualityTracker | URL级别质量跟踪 | 质量分数、API Key |
| WebInterfaceApp/Controller | 前端API Key录入/变更/管理 | API Key、配置 |

### 2. 现有数据流与交互关系
```mermaid
flowchart TD
    subgraph 业务/服务/前端/脚本
        A1[API Key调度/录入/变更/用量/质量/异常]
    end
    subgraph 现有实现
        B1[APIAccountDatabase]
        B2[GlobalAPIConnectionPool]
        B3[APILifecycleManager]
        B4[DailyUsageLimitManager]
        B5[QualityAssuranceGuard]
        B6[URLQualityTracker]
    end
    A1 -- 直连/调用 --> B1
    A1 -- 直连/调用 --> B2
    A1 -- 直连/调用 --> B3
    A1 -- 直连/调用 --> B4
    A1 -- 直连/调用 --> B5
    A1 -- 直连/调用 --> B6

    subgraph 重构后
        C1[ApiMemoryManager]
        C2[APIAccountDatabase(仅持久化)]
    end
    A1 -- 只通过接口 --> C1
    C1 -- 持久化/恢复 --> C2
```

---

## 二、切入点与主权边界
- **唯一主权中心**：ApiMemoryManager，所有API Key/配置/质量/队列/用量/异常/恢复等操作，只能通过其接口实现。
- **APIAccountDatabase**：仅作持久化后端，只允许被ApiMemoryManager内部调用。
- **所有业务/服务/工具/脚本/前端**：全部切换为调用ApiMemoryManager接口，禁止越权。

---

## 三、逐类分析与对接/迁移方案

### 1. ApiMemoryManager
- 职能：唯一主权中心，所有API Key/配置/质量/队列/异常/用量/恢复等操作。
- 新增接口（如list_problematic_apis、用量统计/重置/状态、URL维度、质量历史等）已覆盖所有业务需求。
- 对接方案：所有业务/服务/工具/脚本**只允许通过其接口**进行API Key相关操作。

### 2. APIAccountDatabase
- 职能：持久化API Key/配置/用量/质量等。
- 对接方案：只允许被ApiMemoryManager内部调用，业务层/服务层/工具层/脚本/管理工具**禁止直接调用**。

### 3. GlobalAPIConnectionPool
- 职能：API连接池、负载均衡、健康监控。
- 对接方案：API Key调度、优先级、异常切换等全部通过ApiMemoryManager接口，连接池只负责连接生命周期。

### 4. APILifecycleManager
- 职能：API Key生命周期、失效检测、删除/恢复。
- 对接方案：所有API Key状态、失效、恢复、删除等操作全部通过ApiMemoryManager接口。

### 5. DailyUsageLimitManager
- 职能：用量统计、用量重置。
- 对接方案：所有用量数据的读写必须通过ApiMemoryManager接口。

### 6. QualityAssuranceGuard
- 职能：质量评测、分数计算。
- 对接方案：只保留质量评测算法，所有分数/状态的存取必须通过ApiMemoryManager接口。

### 7. URLQualityTracker
- 职能：URL级别质量跟踪。
- 对接方案：只保留URL统计/分析，API Key/质量分数/状态等全部通过ApiMemoryManager接口。

### 8. WebInterfaceApp/Controller
- 职能：前端API Key录入/变更/管理。
- 对接方案：所有API Key相关操作全部通过ApiMemoryManager接口。

---

## 四、接口适配性与功能无损迁移

### 1. ApiMemoryManager现有对外接口
- get_next_available_api(role, model)：API Key调度/轮询/优先级
- add_api_config(api_config)：API Key录入/变更
- set_quality_score(api_key, score)：质量分数写入
- mark_abnormal(api_key) / mark_normal(api_key)：异常/恢复
- get_api_config(api_key)：API Key配置读取
- list_problematic_apis()：批量获取异常/不达标API Key
- increment_usage(api_key, count=1)：用量统计
- reset_usage(api_key=None)：用量重置
- get_usage_status(api_key)：用量状态查询
- list_apis_by_url(url)：URL维度API Key及状态
- get_url_quality_status(url)：URL维度质量分数/状态
- get_quality_history(api_key)：质量历史
- list_quality_scores(filter_params)：批量质量分数查询

### 2. 业务场景与接口适配性矩阵
| 业务场景 | 适配接口 | 适配性 | 说明 |
|----------|----------|--------|------|
| API Key调度/优先级/轮询 | get_next_available_api | ✅ | 支持按role/model调度，自动轮询/优先级 |
| API Key录入/变更 | add_api_config | ✅ | 字段覆盖所有业务必需项 |
| 质量分数评测/写入 | set_quality_score | ✅ | 支持分数写入，自动重排队列 |
| 异常标记/恢复 | mark_abnormal / mark_normal | ✅ | 支持异常/恢复，自动调整队列 |
| API Key配置读取 | get_api_config | ✅ | 支持按key读取全部配置 |
| 用量统计 | increment_usage | ✅ | 支持用量计数 |
| 用量重置 | reset_usage | ✅ | 支持单个/全部重置 |
| 用量状态查询 | get_usage_status | ✅ | 支持用量状态字典返回 |
| 批量获取异常/不达标API | list_problematic_apis | ✅ | 支持后台质量跟踪/批量还原 |
| URL级别API Key/状态 | list_apis_by_url | ✅ | 支持URL维度API Key及状态 |
| URL级别质量分数/状态 | get_url_quality_status | ✅ | 支持URL维度质量分数/状态 |
| 质量历史 | get_quality_history | ✅ | 支持单Key历史查询 |
| 批量质量分数查询 | list_quality_scores | ✅ | 支持多Key/过滤参数批量查询 |

---

## 五、迁移落地建议与后续步骤
1. 全局代码审计，定位所有API Key/配置/质量/队列/用量相关的数据库/本地状态/影子实现，全部迁移到ApiMemoryManager接口。
2. 逐步替换和删除所有越权/冗余/影子实现，统一到ApiMemoryManager接口。
3. 编写边界守护测试，持续监控接口调用，防止后续越权。
4. 完善接口文档和集成测试，确保所有业务流程闭环。
5. 如有新业务需求，优先扩展ApiMemoryManager接口，保持主权边界不变。 

---

## 六、详细类级重构方案（方法/逻辑链级）

### 1. APIAccountDatabase
- **保留内容**：
  - 仅作为持久化后端，保留底层数据库读写、加解密、表结构管理等方法。
  - 只允许被ApiMemoryManager内部调用。
- **需废弃/禁用**：
  - 所有对外API Key/配置/用量/质量等操作接口（如get_all_api_configurations、store_api_configuration、increment_daily_usage、reset_daily_usage、get_api_configuration等），业务/服务/前端/工具层**不再直接调用**。
  - 任何业务逻辑、状态判断、队列/分数/异常/用量等管理全部移除。
- **逻辑链**：
  - 只负责数据的持久化存取，所有业务流、状态流、质量流、用量流等全部由ApiMemoryManager主权管理。

### 2. GlobalAPIConnectionPool
- **保留内容**：
  - 仅保留连接池/底层连接管理相关方法（如连接复用、连接生命周期、底层健康检查等）。
- **需废弃/禁用**：
  - API Key调度、优先级、异常切换、健康监控、API Key状态/分数/用量/异常等管理方法全部废弃。
  - 任何与API Key主权相关的业务逻辑全部移除。
- **逻辑链**：
  - 连接池只负责连接生命周期，所有API Key调度/切换/异常/优先级等全部通过ApiMemoryManager接口获取和反馈。

### 3. APILifecycleManager
- **保留内容**：
  - 仅保留生命周期管理的非API Key主权相关功能（如外部通知、日志、非主权状态变更等）。
- **需废弃/禁用**：
  - API Key状态、失效、恢复、删除等相关方法全部废弃，全部通过ApiMemoryManager接口实现。
  - 任何直接数据库操作、API Key本地状态维护、队列/分数/用量/异常等管理全部移除。
- **逻辑链**：
  - 只负责生命周期相关的外围通知/日志，所有主权相关操作全部由ApiMemoryManager主权管理。

### 4. DailyUsageLimitManager
- **保留内容**：
  - 无需保留，所有用量统计/重置/状态全部由ApiMemoryManager接口实现。
- **需废弃/禁用**：
  - 用量相关的本地缓存、数据库直连、API Key状态维护、用量统计/重置/状态等全部废弃。
- **逻辑链**：
  - 业务/服务/前端/工具层全部通过ApiMemoryManager.increment_usage、reset_usage、get_usage_status等接口实现用量管理。

### 5. QualityAssuranceGuard
- **保留内容**：
  - 仅保留质量评测算法/策略相关方法（如分数计算、评测流程、策略配置等）。
- **需废弃/禁用**：
  - 所有分数/状态的存取、API Key状态维护、数据库直连、质量历史/异常/用量等管理全部废弃。
  - 只通过ApiMemoryManager接口读写分数/状态。
- **逻辑链**：
  - 只负责算法/策略，所有分数/状态的存取全部通过ApiMemoryManager.set_quality_score、get_quality_history、list_quality_scores等接口实现。

### 6. URLQualityTracker
- **保留内容**：
  - 仅保留URL级别统计/分析算法（如URL聚合、统计分析等）。
- **需废弃/禁用**：
  - API Key/质量分数/状态等本地缓存、数据库直连、API Key状态/分数/用量/异常等管理全部废弃。
  - 只通过ApiMemoryManager.list_apis_by_url、get_url_quality_status等接口获取数据。
- **逻辑链**：
  - 只负责URL分析算法，所有数据获取/反馈全部通过ApiMemoryManager接口。

### 7. WebInterfaceApp/Controller
- **保留内容**：
  - 作为前端/接口层，负责用户交互、API Key录入/变更/管理、状态查询、质量跟踪等。
- **需废弃/禁用**：
  - 任何API Key/配置/用量/质量/异常等操作的本地实现、数据库直连、API Key状态/分数/用量/异常等管理全部废弃。
  - 只通过ApiMemoryManager接口实现所有API Key相关操作。
- **逻辑链**：
  - 前端/接口层所有API Key相关操作全部通过ApiMemoryManager.add_api_config、get_api_config、get_usage_status、set_quality_score、list_problematic_apis等接口实现。

---

> **系统架构大幅简化，只需围绕ApiMemoryManager接口实现所有业务流，其他管理器/服务/工具只保留必要的独立职责。** 