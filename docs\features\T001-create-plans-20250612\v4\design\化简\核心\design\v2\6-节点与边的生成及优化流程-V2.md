# V4.2方案：节点与边的生成及优化流程-V2

**【注意：文档内容已过时】**
**本文档描述的AI交互和产出生成流程，是基于一个尚未实现的、宏大的四阶段治理引擎。当前的V4.2实现核心是一个异步任务框架，尚未包含如此复杂的AI交互和节点/边生成逻辑。本文档可作为未来增强AI治理能力时的重要设计参考。**

## 1. 文档信息

- **文档版本**: V2.0
- **创建日期**: 2025-07-31
- **文档性质**: **核心架构文档** - 定义V2系统的节点与边的生成及优化流程
- **界面交互**: 界面交互设计已在《03-九宫格交互界面设计-V2.md》中单独定义

**注意**: 本文档专注于核心架构和流程设计，不涉及界面交互设计。界面交互部分请参考《03-九宫格交互界面设计-V2.md》文档。

## 2. 核心理念：AI从“决策者”到“语义理解与应用专家”

在V4.2架构中，AI的角色进一步进化。它不仅是基于可靠输入的“决策者”，更是能够深刻**理解不同语义类型 (`category`)**、并为其**应用最恰当的治理模式**（直接引用、分叉细化）的专家。

- **AI的新职责**:
    1.  **理解语义**: 分析`AtomicConstraint`的`category`，明白这是一个通用约束、一个边界条件，还是一个状态机。
    2.  **上下文决策**: 结合当前局部设计目标，决定如何应用这个知识单元。
    3.  **精确输出**: 生成的本地知识单元，必须保持统一模型的结构，并清晰地通过`parent_id`建立血统。

这种转变要求AI具备更强的“技术领域知识”，能够理解不同`category`背后的工程实践差异。

---

## 3. 分层AI角色与“统一语义模型”交互体系

本章节将为核心AI角色提供生产级的、微调后的提示词，展示其全新的、基于“统一语义模型”的思考模式。

### 3.1. 角色一：首席架构师AI (Chief Architect AI)

- **触发时机**: 阶段零，全局知识库生成。
- **核心任务**: 分析`01号`文档，将设计思想**分类**并**结构化**为统一的`AtomicConstraint`对象。
- **提示词微调要点**: 强调**精确的`category`分类**和**严格的`params`结构化提取**。

*(注：其提示词主要是引导AI进行分类和提取，此处从略，重点展示消费层的提示词。)*

### 3.2. 角色二：领域专家AI (Domain Expert AI) - **V4.2核心交互范例**

- **触发时机**: 阶段二，为逻辑层节点（如`UserService`）生成局部知识单元。
- **核心任务**: 基于全局知识库，为当前服务制定一套精确的、上下文感知的局部`AtomicConstraint`列表。

#### **生产级“统一语义模型”交互式提示词 (渲染结果示例)**
```
**Role**: You are a **Domain Expert AI**, a precise and detail-oriented specialist. Your task is to define the specific knowledge units (`AtomicConstraint`) for the `UserService`.

**Your Core Task**:
You must make decisions based on the provided Global Knowledge Base. For each global knowledge unit, you must **understand its `category`** and decide whether to **inherit** it directly or **fork and refine** it for the specific needs of the `UserService`.

---
### **1. Available Global Knowledge Base (Your Decision Basis)**
*This library is pre-validated, conflict-free, and uses a unified model.*

```json
[
  {
    "id": "GC001",
    "category": "constraint",
    "type": "authentication_requirement",
    "params": {},
    "description": "All public APIs must be authenticated."
  },
  {
    "id": "GB001",
    "category": "boundary_condition",
    "type": "response_time",
    "params": { "max_ms": 1000 },
    "description": "All user-facing APIs must respond within 1000ms."
  },
  {
    "id": "GS001",
    "category": "state_machine",
    "type": "user_account_lifecycle",
    "params": { "diagram_source": "stateDiagram-v2\n [*] --> UNVERIFIED\n UNVERIFIED --> ACTIVE\n ACTIVE --> SUSPENDED\n SUSPENDED --> ACTIVE\n" },
    "description": "Defines the lifecycle of a user account."
  }
]
```

---
### **2. Your Design Context**
The `UserService` is a core component for user registration and login. Login is a critical, high-frequency operation requiring maximum performance. The registration process for this service also requires an additional `DEACTIVATED` state before an account can be permanently deleted.

---
### **3. Your Decision-Making Process & Mandatory Output**

Based on the library and context, produce a single JSON object with two keys: `inherited_global_knowledge` and `local_knowledge`.

-   **`inherited_global_knowledge`**: A list of string IDs for the global units you are inheriting **directly without change**.
-   **`local_knowledge`**: A list of new `AtomicConstraint` objects for units that are either **brand new** or are a **fork/refinement** of a global unit.
    -   **For forks, you MUST set the `parent_id`** to the ID of the global unit you are refining.

**Your Thought Process Example**:
-   "Does `UserService` need authentication? Yes. Is the generic constraint `GC001` sufficient? Yes. -> Add `'GC001'` to `inherited_global_knowledge`."
-   "What about response time? The global boundary `GB001` is 1000ms, but login is critical. I need a stricter local boundary. -> Create a new `AtomicConstraint` with `category: 'boundary_condition'`, `max_ms: 200`, and set its `parent_id` to `'GB001'`."
-   "What about the user lifecycle? The global state machine `GS001` is a good baseline, but my context requires an extra `DEACTIVATED` state. I must fork it. -> Create a new `AtomicConstraint` with `category: 'state_machine'`, add the new state and transitions, and set its `parent_id` to `'GS001'`."

---
**Mandatory Output**:
Respond with a single JSON object ONLY.

```json
{
  "inherited_global_knowledge": [
    "GC001"
  ],
  "local_knowledge": [
    {
      "parent_id": "GB001",
      "category": "boundary_condition",
      "type": "response_time",
      "params": { "max_ms": 200 },
      "description": "The critical login API for UserService must respond within 200ms."
    },
    {
      "parent_id": "GS001",
      "category": "state_machine",
      "type": "user_account_lifecycle_extended",
      "params": { "diagram_source": "stateDiagram-v2\n [*] --> UNVERIFIED\n UNVERIFIED --> ACTIVE\n ACTIVE --> SUSPENDED\n SUSPENDED --> ACTIVE\n ACTIVE --> DEACTIVATED\n" },
      "description": "Extends the global user lifecycle with a DEACTIVATED state for UserService."
    }
  ]
}
```
```

**设计说明**: 这个提示词完美地体现了V4.2的思想。它指导AI将所有不同类型的“知识”都视为统一的`AtomicConstraint`对象进行处理，并通过`category`来理解其内在语义，从而正确地应用“引用”或“分叉”的治理模式。这确保了系统的核心逻辑在处理日益丰富的语义类型时，依然保持高度的一致性和稳定性。
