---
id: F004-D005
title: 实例ID文件加密存储设计
description: 本文档描述了实例ID文件加密存储的设计方案，包括加密算法选择、密钥管理策略和与PersistentInstanceManager的集成方案
author: AI助手
created_date: 2025-06-07
updated_date: 2025-06-25
version: 1.4
keywords: [实例ID, 加密存储, AES-256-GCM, 密钥管理, 安全性, Schema命名规范, 构建器模式, 解耦, 并发控制, 线程安全]
related_docs:
  - ../design/commons-uid-library-design.md
  - ../plan/uid-library-refactoring-plan.md
---

# 实例ID文件加密存储设计

## 1. 背景与目的

在分布式ID生成系统中，实例ID（`instance_unique_id`）是应用实例的持久化身份标识，通常存储在本地文件系统中。在当前的实现中，这个文件以明文形式存储，这在局域网环境中风险相对可控，但在云环境中可能带来安全隐患。

本文档提出了一种实例ID文件加密存储的设计方案，旨在提高系统在云环境中的安全性，防止因实例ID泄露或篡改导致的安全问题。

### 1.1 安全风险分析

明文存储实例ID文件在云环境中可能面临以下风险：

1. **身份冒充**：攻击者获取实例ID后可能冒充合法实例
2. **ID冲突**：恶意修改实例ID可能导致多个实例使用相同的Worker ID，进而生成重复的分布式ID
3. **资源耗尽**：通过不断修改实例ID文件，可能导致系统不断申请新的Worker ID，最终耗尽有限的Worker ID资源池
4. **系统拓扑信息泄露**：实例ID可能与特定环境和应用关联，泄露系统环境信息

## 2. 加密方案概述

### 2.1 加密技术选择

本方案选择使用**AES-256-GCM**算法对实例ID文件进行加密：

- **AES (Advanced Encryption Standard)**：对称加密算法，加解密速度快，安全性高
- **256位密钥**：提供足够的加密强度，抵抗暴力破解
- **GCM (Galois/Counter Mode)**：提供认证加密，同时确保数据机密性和完整性

### 2.2 加密内容

加密的内容包括：
- 实例唯一ID (`instance_unique_id`)
- 可能的元数据（如获取时间、应用名称等）

### 2.3 密钥管理策略

密钥管理采用以下策略：

1. **密钥存储**：
   - 在PostgreSQL数据库的`infra_uid.encryption_key`表中存储加密密钥
   - 密钥与应用名称(`application_name`)和环境(`environment`)关联

2. **密钥生成**：
   - 应用首次启动时自动生成随机密钥
   - 使用安全的随机数生成器（如`SecureRandom`）生成密钥

3. **密钥获取**：
   - 应用启动时从数据库获取对应的加密密钥
   - 实现密钥缓存机制，减少数据库访问

4. **密钥保护**：
   - 可选择使用环境变量或配置文件提供的主密钥来加密存储在数据库中的子密钥
   - 实现密钥版本管理，支持密钥轮换

## 3. 使用外部加密工具包

本方案将使用已经实现的外部加密工具包`org.xkong.xkongkit.utils.EncryptionUtils`，该工具类提供了AES-256-GCM加密和解密功能，可直接用于实例ID文件的加密存储。

### 3.1 工具包概述

`EncryptionUtils`类是一个独立的工具类，位于`org.xkong.xkongkit.utils`包中，提供以下核心功能：

- 使用AES-256-GCM算法进行加密和解密
- 提供密钥生成功能
- 支持Base64编码的密钥和加密数据
- 确保数据机密性和完整性

该工具类已经实现了所有必要的加密功能，包括：
- 安全的密钥生成
- IV（初始化向量）的随机生成和管理
- 加密数据的格式化和编码
- 解密过程中的数据完整性验证


### 3.2 使用示例

```java
// 导入外部工具包
import org.xkong.xkongkit.utils.EncryptionUtils;

try {
    // 生成新密钥
    String key = EncryptionUtils.generateKey();

    // 加密数据
    String instanceId = "123456789";
    String encrypted = EncryptionUtils.encrypt(instanceId, key);

    // 解密数据
    String decrypted = EncryptionUtils.decrypt(encrypted, key);

    System.out.println("原始数据: " + instanceId);
    System.out.println("加密数据: " + encrypted);
    System.out.println("解密数据: " + decrypted);
} catch (Exception e) {
    e.printStackTrace();
}
```

## 4. 密钥管理实现

### 4.1 密钥存储表设计

在PostgreSQL数据库中创建`infra_uid.encryption_key`表用于存储加密密钥：

```sql
-- 创建基础设施Schema并指定所有者
CREATE SCHEMA IF NOT EXISTS infra_uid AUTHORIZATION xkong_user;

-- 在infra_uid Schema中创建表（与其他表保持一致的命名风格）
CREATE TABLE infra_uid.encryption_key (
    id BIGSERIAL PRIMARY KEY,
    application_name VARCHAR(255) NOT NULL,
    environment VARCHAR(100) NOT NULL,
    key_type VARCHAR(50) NOT NULL,
    encryption_key TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    CONSTRAINT uk_encryption_key_app_env_type UNIQUE(application_name, environment, key_type)
);

-- 创建索引
CREATE INDEX idx_encryption_key_app_env ON infra_uid.encryption_key (application_name, environment);
CREATE INDEX idx_encryption_key_active ON infra_uid.encryption_key (is_active);
```

### 4.2 密钥管理服务

创建`KeyManagementService`类负责密钥的生成、存储和获取：

```java
/**
 * 密钥管理服务，负责加密密钥的生成、存储和获取
 */
public class KeyManagementService {

    private static final Logger log = LoggerFactory.getLogger(KeyManagementService.class);

    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;
    private final String applicationName;
    private final String environment;
    private final boolean encryptionEnabled;
    private final String schemaName;

    // 密钥缓存
    private String cachedKey;

    /**
     * 构造函数
     *
     * @param jdbcTemplate 数据库操作模板
     * @param transactionTemplate 事务模板
     * @param applicationName 应用名称，通常使用clusterId
     * @param environment 环境名称
     * @param encryptionEnabled 是否启用加密
     * @param schemaName Schema名称，默认为"infra_uid"
     */
    public KeyManagementService(JdbcTemplate jdbcTemplate,
                               TransactionTemplate transactionTemplate,
                               String applicationName,
                               String environment,
                               boolean encryptionEnabled,
                               String schemaName) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
        this.applicationName = applicationName;
        this.environment = environment;
        this.encryptionEnabled = encryptionEnabled;
        this.schemaName = schemaName;

        log.info("初始化KeyManagementService，应用：{}，环境：{}，加密启用状态：{}，Schema：{}",
                applicationName, environment, encryptionEnabled, schemaName);
    }

    /**
     * 获取当前应用和环境的加密密钥
     * 如果密钥不存在，则生成新密钥
     *
     * @return Base64编码的密钥，如果加密未启用则返回null
     */
    public String getOrCreateKey() {
        // 如果未启用加密，返回null
        if (!encryptionEnabled) {
            return null;
        }

        // 如果缓存中有密钥，直接返回
        if (cachedKey != null) {
            return cachedKey;
        }

        // 从数据库获取密钥
        String key = transactionTemplate.execute(status -> {
            try {
                // 查询当前活跃的密钥
                String sql = String.format("SELECT encryption_key FROM %s.encryption_key " +
                             "WHERE application_name = ? AND environment = ? AND is_active = TRUE " +
                             "ORDER BY version DESC LIMIT 1", schemaName);

                List<String> keys = jdbcTemplate.query(sql,
                    new Object[]{applicationName, environment},
                    (rs, rowNum) -> rs.getString("encryption_key"));

                if (!keys.isEmpty()) {
                    log.debug("从数据库获取到现有密钥");
                    return keys.get(0);
                }

                // 如果没有密钥，生成新密钥并存储
                log.info("未找到现有密钥，为应用 {} 环境 {} 生成新密钥", applicationName, environment);
                String newKey = EncryptionUtils.generateKey();

                String insertSql = String.format("INSERT INTO %s.encryption_key " +
                                  "(application_name, environment, key_type, encryption_key, updated_at) " +
                                  "VALUES (?, ?, 'INSTANCE_ID', ?, CURRENT_TIMESTAMP)", schemaName);

                jdbcTemplate.update(insertSql, applicationName, environment, newKey);

                return newKey;
            } catch (Exception e) {
                log.error("获取或创建加密密钥失败", e);
                throw new RuntimeException("获取或创建加密密钥失败", e);
            }
        });

        // 更新缓存
        cachedKey = key;

        return key;
    }

    /**
     * 检查加密是否已启用
     *
     * @return 加密是否已启用
     */
    public boolean isEncryptionEnabled() {
        return encryptionEnabled;
    }

    /**
     * 获取Schema名称
     *
     * @return Schema名称
     */
    public String getSchemaName() {
        return schemaName;
    }
}
```

## 5. 与PersistentInstanceManager的集成

### 5.1 使用构建器模式创建PersistentInstanceManager

使用构建器模式创建`PersistentInstanceManager`实例，并注入`KeyManagementService`：

```java
/**
 * 持久化实例管理器构建器
 * 用于构建PersistentInstanceManager实例
 */
public class PersistentInstanceManagerBuilder {
    private JdbcTemplate jdbcTemplate;
    private TransactionTemplate transactionTemplate;
    private String applicationName;
    private String environment = "default";
    private String instanceGroup;
    private String localStoragePath = ".instance_id";
    private boolean recoveryEnabled = true;
    private int highConfidenceThreshold = 150;
    private int minimumAcceptableScore = 70;
    private String recoveryStrategy = "ALERT_AUTO_WITH_TIMEOUT";
    private int recoveryTimeoutSeconds = 300;
    private Long instanceIdOverride;
    private boolean encryptionEnabled = false;
    private String schemaName = "infra_uid";
    private KeyManagementService keyManagementService;

    // 设置方法，返回this以支持链式调用
    public PersistentInstanceManagerBuilder withJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        return this;
    }

    public PersistentInstanceManagerBuilder withTransactionTemplate(TransactionTemplate transactionTemplate) {
        this.transactionTemplate = transactionTemplate;
        return this;
    }

    public PersistentInstanceManagerBuilder withApplicationName(String applicationName) {
        this.applicationName = applicationName;
        return this;
    }

    public PersistentInstanceManagerBuilder withEnvironment(String environment) {
        this.environment = environment;
        return this;
    }

    public PersistentInstanceManagerBuilder withInstanceGroup(String instanceGroup) {
        this.instanceGroup = instanceGroup;
        return this;
    }

    public PersistentInstanceManagerBuilder withLocalStoragePath(String localStoragePath) {
        this.localStoragePath = localStoragePath;
        return this;
    }

    public PersistentInstanceManagerBuilder withRecoveryEnabled(boolean recoveryEnabled) {
        this.recoveryEnabled = recoveryEnabled;
        return this;
    }

    public PersistentInstanceManagerBuilder withHighConfidenceThreshold(int highConfidenceThreshold) {
        this.highConfidenceThreshold = highConfidenceThreshold;
        return this;
    }

    public PersistentInstanceManagerBuilder withMinimumAcceptableScore(int minimumAcceptableScore) {
        this.minimumAcceptableScore = minimumAcceptableScore;
        return this;
    }

    public PersistentInstanceManagerBuilder withRecoveryStrategy(String recoveryStrategy) {
        this.recoveryStrategy = recoveryStrategy;
        return this;
    }

    public PersistentInstanceManagerBuilder withRecoveryTimeoutSeconds(int recoveryTimeoutSeconds) {
        this.recoveryTimeoutSeconds = recoveryTimeoutSeconds;
        return this;
    }

    public PersistentInstanceManagerBuilder withInstanceIdOverride(Long instanceIdOverride) {
        this.instanceIdOverride = instanceIdOverride;
        return this;
    }

    public PersistentInstanceManagerBuilder withEncryptionEnabled(boolean encryptionEnabled) {
        this.encryptionEnabled = encryptionEnabled;
        return this;
    }

    public PersistentInstanceManagerBuilder withSchemaName(String schemaName) {
        this.schemaName = schemaName;
        return this;
    }

    /**
     * 设置密钥管理服务
     * 推荐作为独立Bean管理，而不是在PersistentInstanceManager内部实例化
     */
    public PersistentInstanceManagerBuilder withKeyManagementService(KeyManagementService keyManagementService) {
        this.keyManagementService = keyManagementService;
        return this;
    }

    /**
     * 构建PersistentInstanceManager实例
     * 在构建前验证必要参数
     */
    public PersistentInstanceManager build() {
        // 验证必要参数
        if (jdbcTemplate == null) {
            throw new IllegalStateException("jdbcTemplate不能为空");
        }
        if (applicationName == null || applicationName.trim().isEmpty()) {
            throw new IllegalStateException("applicationName不能为空");
        }
        if (encryptionEnabled && keyManagementService == null) {
            throw new IllegalStateException("启用加密时keyManagementService不能为空");
        }
        // 其他验证...

        return new PersistentInstanceManager(
            jdbcTemplate,
            transactionTemplate,
            applicationName,
            environment,
            instanceGroup,
            localStoragePath,
            recoveryEnabled,
            highConfidenceThreshold,
            minimumAcceptableScore,
            recoveryStrategy,
            recoveryTimeoutSeconds,
            instanceIdOverride,
            encryptionEnabled,
            schemaName,
            keyManagementService
        );
    }
}

    /**
     * 从本地文件读取实例ID
     */
    private Long readInstanceIdFromLocalStorage() {
        File file = new File(localStoragePath);
        if (!file.exists()) {
            log.debug("本地实例ID文件不存在: {}", localStoragePath);
            return null;
        }

        try {
            String content = Files.readString(file.toPath());

            // 如果加密已启用，尝试解密内容
            if (encryptionEnabled && keyManagementService.isEncryptionEnabled()) {
                try {
                    // 尝试直接解析为Long（处理未加密的旧文件）
                    try {
                        return Long.parseLong(content.trim());
                    } catch (NumberFormatException e) {
                        // 不是有效的Long，尝试解密
                        log.debug("本地文件内容不是有效的Long，尝试解密");
                        String key = keyManagementService.getOrCreateKey();
                        if (key != null) {
                            String decryptedContent = EncryptionUtils.decrypt(content, key);
                            return Long.parseLong(decryptedContent.trim());
                        } else {
                            log.warn("加密已启用但无法获取密钥，无法解密实例ID文件");
                            return null;
                        }
                    }
                } catch (Exception e) {
                    log.error("解密实例ID文件失败", e);
                    return null;
                }
            } else {
                // 加密未启用，直接解析
                return Long.parseLong(content.trim());
            }
        } catch (Exception e) {
            log.error("读取本地实例ID文件失败", e);
            return null;
        }
    }

    /**
     * 将实例ID写入本地文件
     */
    private void writeInstanceIdToLocalStorage(Long instanceId) {
        try {
            String content;

            // 如果加密已启用，加密内容
            if (encryptionEnabled && keyManagementService.isEncryptionEnabled()) {
                String key = keyManagementService.getOrCreateKey();
                if (key != null) {
                    content = EncryptionUtils.encrypt(instanceId.toString(), key);
                    log.debug("实例ID已加密并将写入本地存储");
                } else {
                    // 如果无法获取密钥，则以明文存储并记录警告
                    content = instanceId.toString();
                    log.warn("加密已启用但无法获取密钥，实例ID将以明文存储");
                }
            } else {
                // 加密未启用，以明文存储
                content = instanceId.toString();
            }

            File file = new File(localStoragePath);
            Files.writeString(file.toPath(), content);

            // 设置文件权限（仅所有者可读写）
            file.setReadable(false, false);
            file.setReadable(true, true);
            file.setWritable(false, false);
            file.setWritable(true, true);

            log.info("实例ID已成功写入本地存储: {}", localStoragePath);
        } catch (Exception e) {
            log.error("写入实例ID到本地存储失败", e);
            throw new RuntimeException("持久化实例ID失败", e);
        }
    }

    // 其他方法保持不变...
}
```

## 6. 安全考虑

### 6.1 密钥保护

虽然本方案将加密密钥存储在PostgreSQL数据库中，但仍需注意以下安全考虑：

1. **数据库访问控制**：
   - 严格限制对`infra_uid. encryption_keyS`表的访问权限
   - 只允许必要的应用账户访问此表

2. **密钥传输安全**：
   - 确保数据库连接使用TLS/SSL加密
   - 避免在日志中打印密钥信息

3. **密钥轮换**：
   - 定期更换加密密钥
   - 实现密钥版本管理，支持平滑过渡

### 6.2 文件权限

加密后的实例ID文件仍需设置适当的文件系统权限：

1. **最小权限原则**：
   - 设置文件为仅所有者可读写
   - 在Linux系统上使用`chmod 600`限制权限

2. **存储位置选择**：
   - 将文件存储在受限制的目录中
   - 避免存储在应用程序的公共目录

## 7. 配置参数

为了支持实例ID文件加密功能，需要添加以下配置参数：

| 参数名 | 描述 | 默认值 | 示例 |
|-------|------|-------|------|
| `uid.instance.encryption.enabled` | 是否启用实例ID文件加密 | `false` | `true` |
| `uid.instance.encryption.algorithm` | 加密算法 | `AES-256-GCM` | `AES-256-GCM` |
| `postgresql.uid.schema` | UID生成器表所在的Schema名称 | `infra_uid` | `infra_uid` |

这些参数可以通过KVParamService获取，然后使用构建器模式创建PersistentInstanceManager，示例代码如下：

```java
// 在UidGeneratorConfigApplicationSpecific.java中
@Bean
public KeyManagementService keyManagementService() {
    // 获取必需参数（无默认值，缺少时应用无法启动）
    String schemaName = kvParamService.getParam("postgresql.uid.schema");
    if (schemaName == null || schemaName.trim().isEmpty()) {
        throw new IllegalStateException("必需的参数'postgresql.uid.schema'未配置");
    }

    String environment = kvParamService.getParam("uid.instance.environment");
    if (environment == null || environment.trim().isEmpty()) {
        throw new IllegalStateException("必需的参数'uid.instance.environment'未配置");
    }

    String encryptionEnabledStr = kvParamService.getParam("uid.instance.encryption.enabled");
    if (encryptionEnabledStr == null || encryptionEnabledStr.trim().isEmpty()) {
        throw new IllegalStateException("必需的参数'uid.instance.encryption.enabled'未配置");
    }
    boolean encryptionEnabled = Boolean.parseBoolean(encryptionEnabledStr);

    // 创建KeyManagementService实例
    return new KeyManagementService(
        jdbcTemplate,
        transactionTemplate,
        clusterId, // 使用@Value("${xkong.kv.cluster-id}")注入的clusterId变量
        environment,
        encryptionEnabled,
        schemaName
    );
}

@Bean
public PersistentInstanceManager persistentInstanceManager(KeyManagementService keyManagementService) {
    // 获取必需参数（无默认值，缺少时应用无法启动）
    String schemaName = kvParamService.getParam("postgresql.uid.schema");
    if (schemaName == null || schemaName.trim().isEmpty()) {
        throw new IllegalStateException("必需的参数'postgresql.uid.schema'未配置");
    }

    String environment = kvParamService.getParam("uid.instance.environment");
    if (environment == null || environment.trim().isEmpty()) {
        throw new IllegalStateException("必需的参数'uid.instance.environment'未配置");
    }

    String instanceGroup = kvParamService.getParam("uid.instance.group");
    if (instanceGroup == null || instanceGroup.trim().isEmpty()) {
        throw new IllegalStateException("必需的参数'uid.instance.group'未配置");
    }

    String localStoragePath = kvParamService.getParam("uid.instance.local-storage-path");
    if (localStoragePath == null || localStoragePath.trim().isEmpty()) {
        throw new IllegalStateException("必需的参数'uid.instance.local-storage-path'未配置");
    }

    String recoveryEnabledStr = kvParamService.getParam("uid.instance.recovery.enabled");
    if (recoveryEnabledStr == null || recoveryEnabledStr.trim().isEmpty()) {
        throw new IllegalStateException("必需的参数'uid.instance.recovery.enabled'未配置");
    }
    boolean recoveryEnabled = Boolean.parseBoolean(recoveryEnabledStr);

    String highConfThresholdStr = kvParamService.getParam("uid.instance.recovery.high-confidence-threshold");
    if (highConfThresholdStr == null || highConfThresholdStr.trim().isEmpty()) {
        throw new IllegalStateException("必需的参数'uid.instance.recovery.high-confidence-threshold'未配置");
    }
    int highConfThreshold = Integer.parseInt(highConfThresholdStr);

    String minAcceptScoreStr = kvParamService.getParam("uid.instance.recovery.minimum-acceptable-score");
    if (minAcceptScoreStr == null || minAcceptScoreStr.trim().isEmpty()) {
        throw new IllegalStateException("必需的参数'uid.instance.recovery.minimum-acceptable-score'未配置");
    }
    int minAcceptScore = Integer.parseInt(minAcceptScoreStr);

    String recoveryStrategy = kvParamService.getParam("uid.instance.recovery.strategy");
    if (recoveryStrategy == null || recoveryStrategy.trim().isEmpty()) {
        throw new IllegalStateException("必需的参数'uid.instance.recovery.strategy'未配置");
    }

    String recoveryTimeoutSecondsStr = kvParamService.getParam("uid.instance.recovery.timeout-seconds");
    if (recoveryTimeoutSecondsStr == null || recoveryTimeoutSecondsStr.trim().isEmpty()) {
        throw new IllegalStateException("必需的参数'uid.instance.recovery.timeout-seconds'未配置");
    }
    int recoveryTimeoutSeconds = Integer.parseInt(recoveryTimeoutSecondsStr);

    String encryptionEnabledStr = kvParamService.getParam("uid.instance.encryption.enabled");
    if (encryptionEnabledStr == null || encryptionEnabledStr.trim().isEmpty()) {
        throw new IllegalStateException("必需的参数'uid.instance.encryption.enabled'未配置");
    }
    boolean encryptionEnabled = Boolean.parseBoolean(encryptionEnabledStr);

    // 使用构建器模式创建PersistentInstanceManager
    return new PersistentInstanceManagerBuilder()
        .withJdbcTemplate(jdbcTemplate)
        .withTransactionTemplate(transactionTemplate)
        .withApplicationName(clusterId)
        .withEnvironment(environment)
        .withInstanceGroup(instanceGroup)
        .withLocalStoragePath(localStoragePath)
        .withRecoveryEnabled(recoveryEnabled)
        .withHighConfidenceThreshold(highConfThreshold)
        .withMinimumAcceptableScore(minAcceptScore)
        .withRecoveryStrategy(recoveryStrategy)
        .withRecoveryTimeoutSeconds(recoveryTimeoutSeconds)
        .withEncryptionEnabled(encryptionEnabled)
        .withSchemaName(schemaName)
        .withKeyManagementService(keyManagementService)
        .build();
}
```

## 8. 实施建议

### 8.1 分阶段实施

建议分阶段实施本加密方案：

1. **阶段一**：开发和测试
   - 使用外部工具包`org.xkong.xkongkit.utils.EncryptionUtils`
   - 实现`KeyManagementService`类
   - 在测试环境中验证加密解密功能

2. **阶段二**：集成到`PersistentInstanceManager`
   - 修改`PersistentInstanceManager`的构造函数，添加加密参数
   - 修改文件读写方法，支持加密和解密
   - 添加向后兼容性支持，能够读取未加密的旧文件

3. **阶段三**：生产部署
   - 初始阶段设置`uid.instance.encryption.enabled=false`，保持明文存储
   - 在测试环境中验证功能后，逐步在生产环境中启用加密
   - 监控系统运行情况，确保无异常

### 8.2 兼容性考虑

为了确保平滑过渡，`PersistentInstanceManager`的实现已经包含了处理未加密旧文件的逻辑：

1. 读取文件时，首先尝试直接解析为Long（处理未加密的旧文件）
2. 如果解析失败，则尝试解密内容
3. 写入文件时，根据配置决定是否加密

这种实现方式确保了即使在启用加密后，系统仍然能够正确读取之前以明文形式存储的实例ID文件，实现平滑过渡。

## 9. 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|------|---------|-------|
| 1.4 | 2025-06-25 | 增强并发安全性：添加文件操作的并发控制详细说明 | AI助手 |
| 1.3 | 2025-06-20 | 重构设计：添加构建器模式、更新KeyManagementService添加Schema参数、完全解耦KV参数服务 | AI助手 |
| 1.2 | 2025-06-13 | 更新恢复策略为ALERT_AUTO_WITH_TIMEOUT并添加超时参数 | AI助手 |
| 1.1 | 2025-06-09 | 修正表名和约束命名风格，调整与其他文档的一致性，完善配置参数和集成说明 | AI助手 |
| 1.0 | 2025-06-07 | 初始版本 | AI助手 |
