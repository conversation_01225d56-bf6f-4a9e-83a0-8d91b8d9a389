# 04-五大可选引擎架构设计修改提示词

**文档版本**: MODIFY-FIVE-OPTIONAL-ENGINES  
**创建时间**: 2025年6月10日  
**修改目标**: 重新设计五大可选引擎，体现正确的Mock哲学和双阶段开发模式

---

## 🎯 修改目标

重新设计五大可选引擎的架构，体现Mock的四重价值定位，强调Mock与TestContainers的协作关系。

## 📝 具体修改内容

### **修改位置1：引擎1 - KV参数模拟引擎重新设计**

**完全重写KVParameterSimulationEngine类**：
```java
/**
 * KV参数模拟引擎（重新设计）
 * Mock配置中心，支持开发阶段快速验证和生产阶段故障诊断
 */
@Component
@ConditionalOnProperty(name = "universal.engine.kv-simulation.enabled", havingValue = "true")
public class KVParameterSimulationEngine implements OptionalEngine {
    
    @Autowired
    private MockConfigCenterManager mockConfigCenterManager;
    
    @Autowired
    private RealConfigCenterConnector realConfigCenterConnector;
    
    @Autowired
    private ConfigCenterDiagnosticAnalyzer diagnosticAnalyzer;
    
    /**
     * 启动KV参数模拟
     * 支持开发模式、诊断模式、保护模式
     */
    public KVSimulationResult startKVSimulation(KVSimulationConfig config) {
        log.info("启动KV参数模拟引擎，模式: {}", config.getSimulationMode());
        
        try {
            switch (config.getSimulationMode()) {
                case DEVELOPMENT:
                    return startDevelopmentMockMode(config);
                case DIAGNOSTIC:
                    return startDiagnosticMockMode(config);
                case PROTECTION:
                    return startProtectionMockMode(config);
                default:
                    throw new UnsupportedSimulationModeException(config.getSimulationMode());
            }
            
        } catch (Exception e) {
            log.error("KV参数模拟启动失败", e);
            return KVSimulationResult.failure(e.getMessage());
        }
    }
    
    /**
     * 开发模式：Mock配置中心快速验证参数注入逻辑
     */
    private KVSimulationResult startDevelopmentMockMode(KVSimulationConfig config) {
        // 启动轻量级Mock配置中心
        MockConfigCenter mockCenter = mockConfigCenterManager.startLightweightMockCenter(
            config.getDevelopmentConfig());
        
        // 快速验证参数注入逻辑
        ParameterInjectionResult injectionResult = verifyParameterInjectionLogic(
            mockCenter, config.getParameterInjectionTests());
        
        return KVSimulationResult.builder()
            .simulationMode(SimulationMode.DEVELOPMENT)
            .mockConfigCenter(mockCenter)
            .injectionResult(injectionResult)
            .purpose("开发阶段快速验证参数注入逻辑")
            .startupTime(Duration.ofSeconds(3))
            .build();
    }
    
    /**
     * 诊断模式：当真实配置中心失败时，用Mock诊断问题
     */
    private KVSimulationResult startDiagnosticMockMode(KVSimulationConfig config) {
        // 尝试连接真实配置中心
        ConfigCenterConnectionResult realConnectionResult = realConfigCenterConnector.testConnection(
            config.getRealConfigCenterConfig());
        
        // 启动诊断Mock配置中心
        MockConfigCenter diagnosticMockCenter = mockConfigCenterManager.startDiagnosticMockCenter(
            config.getDiagnosticConfig());
        
        // 对比分析诊断问题
        DiagnosticAnalysisResult analysisResult = diagnosticAnalyzer.analyzeProblem(
            realConnectionResult, diagnosticMockCenter, config);
        
        return KVSimulationResult.builder()
            .simulationMode(SimulationMode.DIAGNOSTIC)
            .realConnectionResult(realConnectionResult)
            .mockConfigCenter(diagnosticMockCenter)
            .diagnosticAnalysis(analysisResult)
            .purpose("故障诊断分析，精确区分配置中心问题与代码问题")
            .build();
    }
    
    /**
     * 保护模式：TestContainers失败时提供基础配置支持
     */
    private KVSimulationResult startProtectionMockMode(KVSimulationConfig config) {
        // 启动保护模式Mock配置中心
        MockConfigCenter protectionMockCenter = mockConfigCenterManager.startProtectionMockCenter(
            config.getProtectionConfig());
        
        // 提供基础配置支持，维持系统运行
        BasicConfigurationSupport basicSupport = provideBasicConfigurationSupport(
            protectionMockCenter, config.getEssentialConfigurations());
        
        return KVSimulationResult.builder()
            .simulationMode(SimulationMode.PROTECTION)
            .mockConfigCenter(protectionMockCenter)
            .basicSupport(basicSupport)
            .purpose("TestContainers失败保护，维持基础配置功能")
            .reliabilityNote("保护模式下的配置功能，真实性受限")
            .build();
    }
}
```

### **修改位置2：引擎2 - 持久化重建引擎重新设计**

**在PersistenceReconstructionEngine类中增加Mock降级机制**：
```java
/**
 * 持久化重建引擎（增加Mock降级机制）
 * TestContainers主导真实环境，Mock提供降级保护
 */
@Component
@ConditionalOnProperty(name = "universal.engine.persistence-reconstruction.enabled", havingValue = "true")
public class PersistenceReconstructionEngine implements OptionalEngine {
    
    @Autowired
    private TestContainersManager testContainersManager;
    
    @Autowired
    private MockPersistenceManager mockPersistenceManager;
    
    @Autowired
    private PersistenceFailoverManager failoverManager;
    
    /**
     * 重建持久化环境
     * 主要模式：TestContainers，降级模式：Mock
     */
    public PersistenceReconstructionResult reconstructPersistenceEnvironment(
            PersistenceReconstructionConfig config) {
        
        log.info("启动持久化重建引擎，配置: {}", config.getConfigId());
        
        try {
            // Step 1: 尝试启动TestContainers环境
            TestContainersResult containersResult = testContainersManager.startContainers(
                config.getRequiredContainers());
            
            if (containersResult.isSuccessful()) {
                // TestContainers成功，使用真实环境
                return buildRealEnvironmentResult(containersResult, config);
            } else {
                // TestContainers失败，降级到Mock环境
                log.warn("TestContainers启动失败，降级到Mock持久化环境: {}", 
                    containersResult.getFailureReason());
                return buildMockEnvironmentResult(containersResult, config);
            }
            
        } catch (Exception e) {
            log.error("持久化环境重建失败", e);
            return PersistenceReconstructionResult.failure(e.getMessage());
        }
    }
    
    /**
     * 构建Mock环境结果
     * TestContainers失败时的降级保护
     */
    private PersistenceReconstructionResult buildMockEnvironmentResult(
            TestContainersResult failedContainersResult,
            PersistenceReconstructionConfig config) {
        
        // 启动Mock持久化环境
        MockPersistenceResult mockResult = mockPersistenceManager.startMockPersistence(
            config.getMockPersistenceConfig());
        
        // 配置故障转移
        FailoverConfiguration failoverConfig = failoverManager.configureMockFailover(
            failedContainersResult, mockResult);
        
        return PersistenceReconstructionResult.builder()
            .environmentType(EnvironmentType.MOCK_PROTECTION)
            .testContainersResult(failedContainersResult)
            .mockPersistenceResult(mockResult)
            .failoverConfiguration(failoverConfig)
            .reconstructionStatus(ReconstructionStatus.MOCK_FALLBACK)
            .protectionNote("TestContainers失败，已降级到Mock持久化环境")
            .build();
    }
}
```

### **修改位置3：引擎4 - 接口自适应测试引擎重新设计**

**重新设计InterfaceAdaptiveTestingEngine，突出gRPC接口Mock**：
```java
/**
 * 接口自适应测试引擎（重新设计）
 * 多协议接口测试，Mock外部接口验证gRPC调用逻辑
 */
@Component
@ConditionalOnProperty(name = "universal.engine.interface-adaptive.enabled", havingValue = "true")
public class InterfaceAdaptiveTestingEngine implements OptionalEngine {
    
    @Autowired
    private GrpcInterfaceMockManager grpcMockManager;
    
    @Autowired
    private RestInterfaceMockManager restMockManager;
    
    @Autowired
    private InterfaceContractValidator contractValidator;
    
    /**
     * 执行接口自适应测试
     * 重点支持gRPC接口Mock和多协议适配
     */
    public InterfaceAdaptiveTestingResult executeInterfaceAdaptiveTest(
            InterfaceAdaptiveTestingConfig config) {
        
        log.info("启动接口自适应测试引擎，配置: {}", config.getConfigId());
        
        try {
            // Step 1: gRPC接口Mock测试
            GrpcMockTestingResult grpcResult = executeGrpcMockTesting(config.getGrpcConfig());
            
            // Step 2: REST接口Mock测试
            RestMockTestingResult restResult = executeRestMockTesting(config.getRestConfig());
            
            // Step 3: 协议适配验证
            ProtocolAdaptationResult adaptationResult = verifyProtocolAdaptation(grpcResult, restResult);
            
            // Step 4: 接口契约验证
            ContractValidationResult contractResult = contractValidator.validateContracts(
                grpcResult, restResult, config.getContractConfig());
            
            return InterfaceAdaptiveTestingResult.builder()
                .grpcMockResult(grpcResult)
                .restMockResult(restResult)
                .protocolAdaptationResult(adaptationResult)
                .contractValidationResult(contractResult)
                .testingStatus(TestingStatus.COMPLETED)
                .build();
                
        } catch (Exception e) {
            log.error("接口自适应测试失败", e);
            return InterfaceAdaptiveTestingResult.failure(e.getMessage());
        }
    }
    
    /**
     * 执行gRPC接口Mock测试
     * 模拟外部gRPC服务，验证接口调用逻辑
     */
    private GrpcMockTestingResult executeGrpcMockTesting(GrpcTestingConfig config) {
        // 启动gRPC Mock服务器
        GrpcMockServer mockServer = grpcMockManager.startMockServer(config);
        
        // 配置Mock响应规则
        grpcMockManager.configureMockResponses(mockServer, config.getMockRules());
        
        // 执行gRPC调用测试
        List<GrpcCallResult> callResults = new ArrayList<>();
        for (GrpcTestCase testCase : config.getTestCases()) {
            GrpcCallResult result = executeGrpcCall(testCase, mockServer);
            callResults.add(result);
        }
        
        // 验证接口调用逻辑
        InterfaceLogicValidationResult logicValidation = validateGrpcCallLogic(callResults);
        
        return GrpcMockTestingResult.builder()
            .mockServer(mockServer)
            .callResults(callResults)
            .logicValidation(logicValidation)
            .purpose("验证gRPC接口调用逻辑和数据一致性")
            .build();
    }
}
```

### **修改位置4：引擎5 - 重命名为"数据一致性验证引擎"**

**完全重新设计，解决命名矛盾**：
```java
/**
 * 数据一致性验证引擎（原数据库驱动Mock引擎重新设计）
 * gRPC接口模拟和数据库查询映射，确保数据一致性
 */
@Component
@ConditionalOnProperty(name = "universal.engine.data-consistency.enabled", havingValue = "true")
public class DataConsistencyVerificationEngine implements OptionalEngine {
    
    @Autowired
    private GrpcDatabaseMappingManager mappingManager;
    
    @Autowired
    private DataConsistencyValidator consistencyValidator;
    
    @Autowired
    private QueryLogicVerifier queryLogicVerifier;
    
    /**
     * 启动数据一致性验证
     * gRPC参数映射为数据库查询，验证数据一致性
     */
    public DataConsistencyVerificationResult startDataConsistencyVerification(
            DataConsistencyVerificationConfig config) {
        
        log.info("启动数据一致性验证引擎，配置: {}", config.getConfigId());
        
        try {
            // Step 1: gRPC参数到数据库查询映射
            GrpcDatabaseMappingResult mappingResult = mappingManager.mapGrpcToDatabase(
                config.getGrpcMappingConfig());
            
            // Step 2: 数据一致性验证
            DataConsistencyResult consistencyResult = consistencyValidator.verifyDataConsistency(
                mappingResult, config.getConsistencyConfig());
            
            // Step 3: 查询逻辑验证
            QueryLogicVerificationResult queryLogicResult = queryLogicVerifier.verifyQueryLogic(
                mappingResult, config.getQueryLogicConfig());
            
            return DataConsistencyVerificationResult.builder()
                .mappingResult(mappingResult)
                .consistencyResult(consistencyResult)
                .queryLogicResult(queryLogicResult)
                .verificationStatus(VerificationStatus.COMPLETED)
                .purpose("确保gRPC接口与数据库查询的数据一致性")
                .build();
                
        } catch (Exception e) {
            log.error("数据一致性验证失败", e);
            return DataConsistencyVerificationResult.failure(e.getMessage());
        }
    }
    
    /**
     * gRPC参数映射为数据库查询
     * 验证数据库查询逻辑的正确性
     */
    private GrpcDatabaseMappingResult mapGrpcParametersToQueries(
            List<GrpcRequest> grpcRequests,
            DatabaseMappingConfig mappingConfig) {
        
        List<QueryMappingResult> mappingResults = new ArrayList<>();
        
        for (GrpcRequest request : grpcRequests) {
            // 解析gRPC请求参数
            Map<String, Object> parameters = parseGrpcParameters(request);
            
            // 映射为数据库查询
            DatabaseQuery query = buildDatabaseQuery(parameters, mappingConfig);
            
            // 验证查询逻辑
            QueryLogicValidationResult validation = validateQueryLogic(query, request);
            
            mappingResults.add(QueryMappingResult.builder()
                .grpcRequest(request)
                .databaseQuery(query)
                .logicValidation(validation)
                .build());
        }
        
        return GrpcDatabaseMappingResult.builder()
            .mappingResults(mappingResults)
            .overallMappingSuccessful(mappingResults.stream().allMatch(r -> r.getLogicValidation().isValid()))
            .build();
    }
}
```

## 🎯 修改原则

1. **体现Mock四重价值**：开发加速器、故障诊断器、接口模拟器、神经保护器
2. **强调双阶段开发**：Mock先行验证 → TestContainers完整验证
3. **突出gRPC接口Mock**：重点支持gRPC接口模拟和验证
4. **解决命名矛盾**：将"数据库驱动Mock"重命名为"数据一致性验证"

## 📋 验证要点

修改完成后，文档应该能够让AI清晰理解：
- 五大引擎中Mock的正确定位和价值
- Mock与TestContainers的协作关系
- gRPC接口Mock的重要性和实现方式
- 数据一致性验证的核心目标和机制
