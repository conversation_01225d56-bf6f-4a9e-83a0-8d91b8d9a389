# V4全景拼图数据结构扩展设计方案

## 📋 文档概述

**文档ID**: V4-PANORAMIC-DATA-STRUCTURE-EXTENSION-002
**创建日期**: 2025-06-24
**版本**: V4.5-Enhanced-Data-Structure-Extension
**目标**: 扩展现有数据结构以支持V4全景拼图与因果推理系统集成
**依赖文档**: 01-V4全景拼图功能实施计划总览.md

## 🎯 数据结构扩展需求分析

### 现有数据结构分析
基于现有V4.5因果推理系统，当前数据结构主要包括：
- 基础因果关系数据
- 策略路线数据
- 认知突破检测数据

### 扩展需求识别
为支持全景拼图功能，需要扩展以下数据结构：
- **PanoramicPosition**: 支持策略路线数据和复杂度评估
- **ArchitecturalContext**: 执行上下文数据结构
- **CausalMappingData**: 因果推理映射数据
- **QualityMetrics**: 质量评估和收敛数据

## 🏗️ 核心数据结构设计

### 1. PanoramicPosition扩展设计

```python
# C:\ExchangeWorks\xkong\xkongcloud\tools\ace\src\python_host\panoramic\data_structures.py

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

class ComplexityLevel(Enum):
    """复杂度级别枚举"""
    LOW = "low"           # 简单：≤3个概念，单一组件
    MEDIUM = "medium"     # 中等：4-7个概念，多组件协调
    HIGH = "high"         # 复杂：≥8个概念，架构性变更

class StrategyType(Enum):
    """策略类型枚举"""
    BREAKTHROUGH = "breakthrough"     # 策略自我突破
    COGNITIVE = "cognitive"          # 认知突破
    OPTIMIZATION = "optimization"    # 优化策略
    INTEGRATION = "integration"      # 集成策略

@dataclass
class StrategyRouteData:
    """策略路线数据结构"""
    strategy_id: str
    strategy_type: StrategyType
    route_path: List[str]
    confidence_score: float
    complexity_assessment: ComplexityLevel
    execution_priority: int
    dependencies: List[str] = field(default_factory=list)
    estimated_execution_time: int = 0  # 分钟
    risk_factors: List[str] = field(default_factory=list)
    success_criteria: List[str] = field(default_factory=list)

@dataclass
class ComplexityAssessment:
    """复杂度评估数据结构"""
    concept_count: int                    # 概念数量
    dependency_layers: int                # 依赖层级
    memory_pressure: float               # 记忆压力 0.0-1.0
    hallucination_risk: float            # 幻觉风险 0.0-1.0
    context_switch_cost: float           # 上下文切换成本
    verification_anchor_density: float   # 验证锚点密度
    overall_complexity: ComplexityLevel  # 综合复杂度评估
    
    def calculate_ai_cognitive_load(self) -> float:
        """计算AI认知负载"""
        load_factors = [
            self.memory_pressure * 0.3,
            self.hallucination_risk * 0.25,
            self.context_switch_cost * 0.2,
            (1.0 - self.verification_anchor_density) * 0.25
        ]
        return min(1.0, sum(load_factors))

@dataclass
class PanoramicPositionExtended:
    """扩展的全景拼图位置数据结构"""
    # 原有字段
    position_id: str
    document_path: str
    architectural_layer: str
    component_type: str
    
    # 新增策略路线支持
    strategy_routes: List[StrategyRouteData] = field(default_factory=list)
    
    # 新增复杂度评估
    complexity_assessment: Optional[ComplexityAssessment] = None
    
    # 新增执行上下文
    execution_context: Dict[str, Any] = field(default_factory=dict)
    
    # 新增质量指标
    quality_metrics: Dict[str, float] = field(default_factory=dict)
    
    # 新增因果关系数据
    causal_relationships: List[Dict[str, Any]] = field(default_factory=list)
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    version: str = "1.0.0"
```

### 2. 执行上下文数据结构

```python
@dataclass
class ExecutionContext:
    """执行上下文数据结构"""
    context_id: str
    execution_phase: str              # 执行阶段
    current_step: int                 # 当前步骤
    total_steps: int                  # 总步骤数
    
    # 环境信息
    environment_info: Dict[str, Any] = field(default_factory=dict)
    
    # 资源状态
    resource_status: Dict[str, Any] = field(default_factory=dict)
    
    # 依赖状态
    dependency_status: Dict[str, str] = field(default_factory=dict)
    
    # 错误历史
    error_history: List[Dict[str, Any]] = field(default_factory=list)
    
    # 性能指标
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    
    def add_error(self, error_type: str, error_message: str, timestamp: datetime = None):
        """添加错误记录"""
        if timestamp is None:
            timestamp = datetime.now()
        
        self.error_history.append({
            "error_type": error_type,
            "error_message": error_message,
            "timestamp": timestamp.isoformat(),
            "step": self.current_step
        })
    
    def update_performance_metric(self, metric_name: str, value: float):
        """更新性能指标"""
        self.performance_metrics[metric_name] = value
        
    def get_execution_progress(self) -> float:
        """获取执行进度"""
        if self.total_steps == 0:
            return 0.0
        return min(1.0, self.current_step / self.total_steps)
```

### 3. 因果映射数据结构

```python
@dataclass
class CausalRelationship:
    """因果关系数据结构"""
    relationship_id: str
    cause_component: str              # 原因组件
    effect_component: str             # 结果组件
    relationship_type: str            # 关系类型
    strength: float                   # 关系强度 0.0-1.0
    confidence: float                 # 置信度 0.0-1.0
    evidence_sources: List[str] = field(default_factory=list)
    
@dataclass
class CausalMappingData:
    """因果推理映射数据结构"""
    mapping_id: str
    panoramic_position_id: str
    
    # 因果关系网络
    causal_relationships: List[CausalRelationship] = field(default_factory=list)
    
    # 推理路径
    inference_paths: List[List[str]] = field(default_factory=list)
    
    # 反事实分析
    counterfactual_scenarios: List[Dict[str, Any]] = field(default_factory=list)
    
    # 干预效果预测
    intervention_predictions: List[Dict[str, Any]] = field(default_factory=list)
    
    # 根因分析结果
    root_cause_analysis: Dict[str, Any] = field(default_factory=dict)
    
    # 质量评估
    mapping_quality_score: float = 0.0
    validation_status: str = "pending"
    
    def add_causal_relationship(self, cause: str, effect: str, 
                              relationship_type: str, strength: float, 
                              confidence: float, evidence: List[str] = None):
        """添加因果关系"""
        relationship = CausalRelationship(
            relationship_id=f"{cause}_{effect}_{len(self.causal_relationships)}",
            cause_component=cause,
            effect_component=effect,
            relationship_type=relationship_type,
            strength=strength,
            confidence=confidence,
            evidence_sources=evidence or []
        )
        self.causal_relationships.append(relationship)
        
    def calculate_network_complexity(self) -> float:
        """计算因果网络复杂度"""
        if not self.causal_relationships:
            return 0.0
        
        # 基于关系数量和路径复杂度计算
        relationship_count = len(self.causal_relationships)
        path_complexity = sum(len(path) for path in self.inference_paths)
        
        return min(1.0, (relationship_count * 0.1 + path_complexity * 0.05))
```

### 4. 质量评估数据结构

```python
@dataclass
class QualityMetrics:
    """质量评估指标数据结构"""
    metrics_id: str
    
    # 核心质量指标
    execution_correctness: float = 0.0      # 执行正确度
    confidence_score: float = 0.0           # 置信度评分
    consistency_score: float = 0.0          # 一致性评分
    completeness_score: float = 0.0         # 完整性评分
    
    # 三重验证结果
    v4_algorithm_verification: float = 0.0   # V4算法验证
    python_ai_verification: float = 0.0      # Python AI验证
    ide_ai_verification: float = 0.0         # IDE AI验证
    
    # 收敛指标
    convergence_rate: float = 0.0            # 收敛速率
    stability_score: float = 0.0             # 稳定性评分
    
    # 性能指标
    execution_time_ms: int = 0               # 执行时间（毫秒）
    memory_usage_mb: float = 0.0             # 内存使用（MB）
    cpu_usage_percent: float = 0.0           # CPU使用率
    
    # 错误统计
    error_count: int = 0                     # 错误数量
    warning_count: int = 0                   # 警告数量
    
    # 时间戳
    measurement_timestamp: datetime = field(default_factory=datetime.now)
    
    def calculate_overall_quality(self) -> float:
        """计算综合质量评分"""
        core_metrics = [
            self.execution_correctness * 0.3,
            self.confidence_score * 0.25,
            self.consistency_score * 0.2,
            self.completeness_score * 0.25
        ]
        return sum(core_metrics)
    
    def calculate_triple_verification_score(self) -> float:
        """计算三重验证综合评分"""
        verification_scores = [
            self.v4_algorithm_verification,
            self.python_ai_verification,
            self.ide_ai_verification
        ]
        return sum(verification_scores) / len(verification_scores)
    
    def is_quality_target_met(self, target: float = 93.3) -> bool:
        """检查是否达到质量目标"""
        overall_quality = self.calculate_overall_quality()
        return overall_quality >= target
```

## 🔗 数据结构关系映射

### 数据流关系图
```
PanoramicPositionExtended
    ├── StrategyRouteData (1:N)
    ├── ComplexityAssessment (1:1)
    ├── ExecutionContext (1:1)
    ├── CausalMappingData (1:1)
    └── QualityMetrics (1:1)

CausalMappingData
    ├── CausalRelationship (1:N)
    └── QualityMetrics (1:1)
```

### 数据转换接口设计
```python
class PanoramicToCausalDataMapper:
    """全景拼图到因果推理数据映射器"""
    
    def map_panoramic_to_causal(self, panoramic_data: PanoramicPositionExtended) -> CausalMappingData:
        """将全景拼图数据映射为因果推理数据"""
        pass
    
    def extract_strategy_routes(self, panoramic_data: PanoramicPositionExtended) -> List[StrategyRouteData]:
        """提取策略路线数据"""
        pass
    
    def assess_complexity(self, panoramic_data: PanoramicPositionExtended) -> ComplexityAssessment:
        """评估复杂度"""
        pass
```

## 📊 数据库表结构扩展

### 新增表结构
```sql
-- 策略路线数据表
CREATE TABLE strategy_routes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    strategy_id TEXT NOT NULL,
    panoramic_position_id TEXT NOT NULL,
    strategy_type TEXT NOT NULL,
    route_path TEXT NOT NULL,  -- JSON格式
    confidence_score REAL DEFAULT 0.0,
    complexity_level TEXT DEFAULT 'medium',
    execution_priority INTEGER DEFAULT 1,
    dependencies TEXT,  -- JSON格式
    estimated_execution_time INTEGER DEFAULT 0,
    risk_factors TEXT,  -- JSON格式
    success_criteria TEXT,  -- JSON格式
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (panoramic_position_id) REFERENCES panoramic_models(id)
);

-- 复杂度评估表
CREATE TABLE complexity_assessments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    panoramic_position_id TEXT NOT NULL,
    concept_count INTEGER DEFAULT 0,
    dependency_layers INTEGER DEFAULT 0,
    memory_pressure REAL DEFAULT 0.0,
    hallucination_risk REAL DEFAULT 0.0,
    context_switch_cost REAL DEFAULT 0.0,
    verification_anchor_density REAL DEFAULT 0.0,
    overall_complexity TEXT DEFAULT 'medium',
    ai_cognitive_load REAL DEFAULT 0.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (panoramic_position_id) REFERENCES panoramic_models(id)
);

-- 因果关系映射表
CREATE TABLE causal_relationships (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    relationship_id TEXT NOT NULL UNIQUE,
    mapping_id TEXT NOT NULL,
    cause_component TEXT NOT NULL,
    effect_component TEXT NOT NULL,
    relationship_type TEXT NOT NULL,
    strength REAL DEFAULT 0.0,
    confidence REAL DEFAULT 0.0,
    evidence_sources TEXT,  -- JSON格式
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## ⚠️ 实施注意事项

### 目录创建提醒
- 创建目录：`C:\ExchangeWorks\xkong\xkongcloud\tools\ace\src\python_host\panoramic\`
- 创建文件：`C:\ExchangeWorks\xkong\xkongcloud\tools\ace\src\python_host\panoramic\data_structures.py`
- 创建文件：`C:\ExchangeWorks\xkong\xkongcloud\tools\ace\src\python_host\panoramic\__init__.py`

### 依赖关系
- 依赖现有SQLite数据库设计
- 依赖V4.5因果推理系统接口
- 需要与三重验证机制兼容

### 质量控制
- 所有数据结构必须支持序列化/反序列化
- 必须包含完整的类型注解
- 必须包含数据验证逻辑

---

*V4全景拼图数据结构扩展设计方案*
*支持因果推理系统深度集成*
*创建时间：2025-06-24*
