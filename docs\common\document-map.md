# 共享文档映射表

本文档提供了`docs/common`目录下所有共享文档的中央索引，便于快速查找和访问相关文档。

## 架构文档

### 架构模式
| 文档ID | 文档名称 | 路径 | 描述 |
|-------|---------|------|------|
| C007 | PostgreSQL演进架构实施指南 | [patterns/postgresql-evolution-implementation-guide.md](./architecture/patterns/postgresql-evolution-implementation-guide.md) | PostgreSQL数据库迁移项目中实施持续演进架构的通用指南和可复用模式 |
| C044 | 分布式租约管理模式 | [patterns/distributed-lease-management-pattern.md](./architecture/patterns/distributed-lease-management-pattern.md) | 分布式系统中有限资源租约管理的设计模式，包含防拥堵机制、故障重试和指数退避策略 |
| C045 | 实例身份识别与恢复模式 | [patterns/instance-identity-recovery-pattern.md](./architecture/patterns/instance-identity-recovery-pattern.md) | 分布式系统中实例持久化身份管理的设计模式，通过特征码匹配实现身份恢复 |

| 文档ID | 文档标题 | 类别 | 路径 | 版本 | 状态 | 更新日期 |
|-------|---------|-----|------|------|------|---------|
| C001 | 系统架构原则 | 架构 | [架构原则](./architecture/principles/architecture-principles.md) | 1.0 | 计划中 | - |
| C002 | 设计模式指南 | 架构 | [设计模式](./architecture/patterns/design-patterns-guide.md) | 1.0 | 计划中 | - |
| C003 | 系统架构图 | 架构 | [架构图](./architecture/diagrams/system-architecture.md) | 1.0 | 计划中 | - |
| C004 | 持续演进架构设计原则 | 架构 | [持续演进架构](./architecture/principles/continuous-evolution-architecture.md) | 1.0 | 草稿 | 2025-01-15 |
| C005 | 服务演进模式指南 | 架构 | [演进模式](./architecture/patterns/service-evolution-patterns.md) | 1.0 | 草稿 | 2025-01-15 |
| C006 | 架构演进路线图 | 架构 | [演进路线图](./architecture/diagrams/architecture-evolution-roadmap.md) | 1.0 | 草稿 | 2025-01-15 |

## 最佳实践文档

| 文档ID | 文档标题 | 类别 | 路径 | 版本 | 状态 | 更新日期 |
|-------|---------|-----|------|------|------|---------|
| C004 | 编码规范 | 最佳实践 | [编码标准](./best-practices/coding-standards/coding-guidelines.md) | 1.0 | 计划中 | - |
| C005 | 异常处理规范 | 最佳实践 | [异常处理](./best-practices/error-handling/exception-handling-guide.md) | 1.0 | 计划中 | - |
| C006 | 日志记录规范 | 最佳实践 | [日志记录](./best-practices/logging/logging-guidelines.md) | 1.0 | 计划中 | - |
| C007 | 测试规范 | 最佳实践 | [测试](./best-practices/testing/testing-guidelines.md) | 1.0 | 计划中 | - |
| C033 | 可扩展测试运行器架构设计 | 最佳实践 | [测试运行器架构](./best-practices/testing/scalable-test-runner-architecture.md) | 1.0 | 草稿 | 2025-01-15 |
| C034 | 测试运行器实现指南 | 最佳实践 | [测试运行器实现](./best-practices/testing/test-runner-implementation-guide.md) | 1.0 | 计划中 | - |
| C035 | 测试运行器使用示例 | 最佳实践 | [测试运行器示例](./best-practices/testing/test-runner-usage-examples.md) | 1.0 | 计划中 | - |
| C036 | 测试运行器最佳实践 | 最佳实践 | [测试运行器最佳实践](./best-practices/testing/test-runner-best-practices.md) | 1.0 | 计划中 | - |
| C043 | AI测试策略制定指导 | 最佳实践 | [AI策略指导](./best-practices/testing/ai-testing-strategy-guide.md) | 1.0 | 生效 | 2025-01-15 |
| C044 | 混合分层参数测试架构 | 最佳实践 | [混合架构设计](./best-practices/testing/hybrid-parameter-testing-architecture.md) | 1.0 | 生效 | 2025-01-15 |
| C046 | 基础设施工具类设计指南 | 最佳实践 | [基础设施工具类](./best-practices/coding-standards/infrastructure-utilities-design-guide.md) | 1.0 | 生效 | 2025-01-15 |
| C047 | 零停机数据库结构演进指南 | 最佳实践 | [数据库结构演进](./best-practices/database/zero-downtime-schema-evolution-guide.md) | 1.0 | 生效 | 2025-01-15 |
| C048 | 分布式系统监控指标设计指南 | 最佳实践 | [监控指标设计](./best-practices/monitoring/distributed-system-metrics-design-guide.md) | 1.0 | 生效 | 2025-01-15 |
| C049 | 高覆盖率测试策略指南 | 最佳实践 | [高覆盖率测试策略](./best-practices/testing/high-coverage-testing-strategy-guide.md) | 1.0 | 生效 | 2025-01-15 |

## 中间件文档

| 文档ID | 文档标题 | 类别 | 路径 | 版本 | 状态 | 更新日期 |
|-------|---------|-----|------|------|------|---------|
| C008 | Cassandra数据模型设计 | 中间件 | [Cassandra](./middleware/cassandra/data-model-design.md) | 1.0 | 计划中 | - |
| C009 | PostgreSQL集成指南 | 中间件 | [PostgreSQL](./middleware/postgresql/integration-guide.md) | 1.0 | 计划中 | - |
| C010 | RabbitMQ配置指南 | 中间件 | [RabbitMQ](./middleware/rabbitmq/configuration-guide.md) | 1.0 | 计划中 | - |
| C011 | Valkey使用指南 | 中间件 | [Valkey](./middleware/valkey/usage-guide.md) | 1.0 | 计划中 | - |
| C024 | Snowflake ID生成机制的高可用设计方案 | 中间件 | [Snowflake ID](./middleware/integration/snowflake-id-high-availability-design.md) | 1.0 | 草稿 | 2025-05-9 |
| C025 | PostgreSQL、RabbitMQ和Valkey配合使用最佳实践 | 中间件 | [中间件集成](./middleware/integration/postgresql-rabbitmq-valkey-best-practices.md) | 1.0 | 已批准 | 2025-05-9 |
| C032 | PostgreSQL文档索引 | 中间件 | [PostgreSQL索引](./middleware/postgresql/postgresql-index.json) | 1.0 | 已批准 | 2025-05-12 |

## 协议文档

| 文档ID | 文档标题 | 类别 | 路径 | 版本 | 状态 | 更新日期 |
|-------|---------|-----|------|------|------|---------|
| C012 | gRPC接口设计规范 | 协议 | [gRPC](./protocols/grpc/interface-design-guide.md) | 1.0 | 计划中 | - |
| C013 | REST API设计规范 | 协议 | [REST](./protocols/rest/api-design-guide.md) | 1.0 | 计划中 | - |
| C014 | 内部服务通信规范 | 协议 | [内部通信](./protocols/internal/communication-guide.md) | 1.0 | 计划中 | - |

## 安全文档

| 文档ID | 文档标题 | 类别 | 路径 | 版本 | 状态 | 更新日期 |
|-------|---------|-----|------|------|------|---------|
| C015 | 认证机制规范 | 安全 | [认证](./security/authentication/authentication-guide.md) | 1.0 | 计划中 | - |
| C016 | 授权控制规范 | 安全 | [授权](./security/authorization/authorization-guide.md) | 1.0 | 计划中 | - |
| C017 | 数据保护规范 | 安全 | [数据保护](./security/data-protection/data-protection-guide.md) | 1.0 | 计划中 | - |

## 故障排除文档

| 文档ID | 文档标题 | 类别 | 路径 | 版本 | 状态 | 更新日期 |
|-------|---------|-----|------|------|------|---------|
| C018 | 常见问题解决指南 | 故障排除 | [常见问题](./troubleshooting/common-issues/common-issues-guide.md) | 1.0 | 计划中 | - |
| C019 | 调试技巧指南 | 故障排除 | [调试](./troubleshooting/debugging/debugging-guide.md) | 1.0 | 计划中 | - |
| C020 | 性能优化指南 | 故障排除 | [性能优化](./troubleshooting/performance/performance-optimization-guide.md) | 1.0 | 计划中 | - |

## 文档模板

| 文档ID | 文档标题 | 类别 | 路径 | 版本 | 状态 | 更新日期 |
|-------|---------|-----|------|------|------|---------|
| C021 | 设计文档模板 | 模板 | [设计文档](./templates/design-doc.md) | 1.0 | 计划中 | - |
| C022 | API文档模板 | 模板 | [API文档](./templates/api-doc.md) | 1.0 | 计划中 | - |
| C023 | 实现文档模板 | 模板 | [实现文档](./templates/implementation-doc.md) | 1.0 | 计划中 | - |
| T003 | 任务文件模板（含文档更新） | 模板 | [任务文件](./templates/task-file-template-with-doc-updates.md) | 1.0 | 已批准 | 2025-05-08 |
| T004 | AI推演分析与人工决策记录模板 | 模板 | [分析决策记录模板](./templates/ai-analysis-human-decision-record-template.md) | 1.0 | 生效 | 2025-01-15 |

## 关键词索引

| 关键词 | 相关文档 |
|-------|---------|
| 架构 | C001, C002, C003 |
| 设计模式 | C002, C044, C045 |
| 编码规范 | C004 |
| 异常处理 | C005 |
| 日志记录 | C006 |
| 测试 | C007, C033, C034, C035, C036, C043, C044, C049 |
| 测试运行器 | C033, C034, C035, C036 |
| AI测试策略 | C043 |
| 参数化测试 | C044 |
| 系统性思维 | C043 |
| STRIDE威胁建模 | C043 |
| FMEA分析 | C043 |
| 攻击树分析 | C043 |
| 可扩展架构 | C033 |
| 分布式租约 | C044 |
| 实例身份恢复 | C045 |
| 基础设施工具类 | C046 |
| 数据库演进 | C047 |
| 监控指标 | C048 |
| 高覆盖率测试 | C049 |
| 防拥堵机制 | C044 |
| 故障重试 | C044 |
| 特征码匹配 | C045 |
| 置信度决策 | C045 |
| 零停机部署 | C047 |
| 渐进式部署 | C047 |
| Cassandra | C008 |
| PostgreSQL | C009, C025, C032 |
| RabbitMQ | C010, C025 |
| Valkey | C011, C025 |
| gRPC | C012 |
| REST | C013 |
| 认证 | C015 |
| 授权 | C016 |
| 数据保护 | C017 |
| 故障排除 | C018, C019, C020 |
| 性能优化 | C020 |
| 文档模板 | C021, C022, C023, T003, T004 |
| AI推演分析 | T004 |
| 人工决策记录 | T004 |
| 任务文件 | T003 |
| RIPER-5 | T003 |
| Snowflake ID | C024 |
| 高可用 | C024, C025 |
| 分布式ID | C024 |
| 中间件集成 | C024, C025 |
| 最佳实践 | C025 |

## 使用指南

1. 通过文档ID直接查找特定文档
2. 通过类别浏览相关领域的文档
3. 通过关键词索引查找特定主题的文档
4. 点击路径链接直接访问文档

## 文档状态说明

- **计划中**：文档尚未创建，但已在计划中
- **草稿**：文档已创建但尚未完成
- **已审核**：文档已完成并经过审核
- **已批准**：文档已批准并正式发布
- **已过时**：文档已被新版本替代

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 1.6 | 2025-01-15 | 添加PostgreSQL迁移重构提取的6个通用标准库文档(C044-C049)：分布式租约管理模式、实例身份识别与恢复模式、基础设施工具类设计指南、零停机数据库结构演进指南、分布式系统监控指标设计指南、高覆盖率测试策略指南 | AI助手 |
| 1.5 | 2025-01-15 | 添加AI测试策略指导和混合分层参数测试架构文档(C043-C044)，新增AI推演分析与人工决策记录模板(T004) | AI助手 |
| 1.4 | 2025-01-15 | 添加可扩展测试运行器架构设计文档(C033-C036) | AI助手 |
| 1.3 | 2025-05-12 | 添加PostgreSQL文档索引(C032) | AI助手 |
| 1.2 | 2025-05-15 | 添加PostgreSQL、RabbitMQ和Valkey配合使用最佳实践(C025) | AI助手 |
| 1.1 | 2025-05-15 | 添加Snowflake ID生成机制的高可用设计方案(C024) | AI助手 |
| 1.0 | 2025-05-08 | 初始版本 | AI助手 |
