# PostgreSQL傻瓜式入门教程 - 第三部分：Repository接口设计与高级查询

## 前言

在前两部分中，我们学习了PostgreSQL的基础知识、Spring Boot集成方法、数据模型设计和JPA实体类映射。本部分将深入探讨Repository接口设计和高级查询技术，这是开发高效PostgreSQL应用的关键。

## 1. Spring Data JPA Repository接口设计

### 1.1 Repository接口基础

**Spring Data JPA**提供了一套强大的**Repository接口体系**，简化了数据访问层的开发。主要接口包括：

> **通俗解释**：Repository接口体系就像是一套预先设计好的数据访问工具箱，让你不必从零开始编写数据库操作代码，大大提高开发效率。

1. **Repository**：标记接口，不包含任何方法
   > **通俗解释**：最基础的接口，就像一个空的工具箱，只是一个标记，表明"这是一个数据访问组件"。

2. **CrudRepository**：提供基本的CRUD操作
   > **通俗解释**：包含基本的增删改查功能，就像一个带有基础工具的工具箱，能满足日常维修需求。

3. **PagingAndSortingRepository**：在CrudRepository基础上增加分页和排序功能
   > **通俗解释**：在基础工具箱上增加了分页和排序功能，就像增加了目录和索引功能的工具箱，能更有条理地处理大量数据。

4. **JpaRepository**：在PagingAndSortingRepository基础上增加JPA特定功能
   > **通俗解释**：最全功能的工具箱，包含了所有基础功能，还增加了JPA特有的高级工具，能满足更复杂的需求。

通常，我们会选择继承JpaRepository接口，因为它提供了最全面的功能。

### 1.2 创建Repository接口

创建Repository接口非常简单，只需定义一个接口并继承JpaRepository，指定实体类和主键类型：

```java
// 导入Spring Data JPA的JpaRepository接口
import org.springframework.data.jpa.repository.JpaRepository;
// 导入Spring的Repository注解
import org.springframework.stereotype.Repository;

/**
 * 用户数据访问接口
 * 继承JpaRepository接口，提供基本的CRUD操作和分页排序功能
 * 泛型参数：User - 实体类类型，Long - 主键类型
 */
@Repository  // 标记为Spring的Repository组件，可以被自动扫描和注入
public interface UserRepository extends JpaRepository<User, Long> {
    // 自定义查询方法...
    // 这里可以添加特定的查询方法，Spring Data JPA会根据方法名自动生成实现
}
```

> **通俗解释**：
> - **@Repository**：标记这个接口是一个数据访问组件，就像给工具箱贴上"数据访问工具"的标签。
> - **JpaRepository<User, Long>**：继承JPA仓库接口，指定操作的是User实体，主键类型是Long，就像告诉工具箱"你要处理的是用户数据，用户ID是长整型"。
> - **泛型参数**：尖括号中的类型说明，就像告诉工具"你要处理什么类型的物品"，这里是User实体和Long类型的ID。

### 1.3 基于方法名的查询

Spring Data JPA支持**基于方法名的查询**，它会根据方法名自动生成查询语句。常用的方法名前缀包括：

> **通俗解释**：基于方法名的查询就像是一种魔法，你只需要按照特定规则命名方法，Spring Data JPA就能自动理解你想要什么数据，并生成相应的SQL语句，不需要你手写SQL。

- **find...By**：查找匹配条件的对象
  > **通俗解释**：就像说"找出所有符合某条件的记录"，例如"找出所有姓张的人"。
- **get...By**：与find...By同义
  > **通俗解释**：功能与find...By相同，只是名称不同，就像"获取"和"查找"的区别。
- **count...By**：计算匹配条件的对象数量
  > **通俗解释**：就像说"数一数有多少符合条件的记录"，例如"数一数有多少人年龄超过30岁"。
- **exists...By**：判断是否存在匹配条件的对象
  > **通俗解释**：就像问"是否存在符合条件的记录"，返回是或否，例如"是否存在用户名为admin的用户"。
- **delete...By**：删除匹配条件的对象
  > **通俗解释**：就像说"删除所有符合条件的记录"，例如"删除所有过期的订单"。

方法名中可以使用的关键字包括：

| 关键字 | 示例 | 对应的SQL | 通俗解释 |
|-------|------|----------|---------|
| And | findByUsernameAndEmail | ... WHERE username = ? AND email = ? | 同时满足两个条件，就像"找出既姓张又住在北京的人" |
| Or | findByUsernameOrEmail | ... WHERE username = ? OR email = ? | 满足任一条件，就像"找出姓张的人或住在北京的人" |
| Equals | findByUsername | ... WHERE username = ? | 精确匹配，就像"找出名字叫张三的人" |
| Like | findByUsernameLike | ... WHERE username LIKE ? | 模糊匹配，就像"找出名字中包含'张'的人" |
| NotLike | findByUsernameNotLike | ... WHERE username NOT LIKE ? | 不匹配，就像"找出名字中不包含'张'的人" |
| StartingWith | findByUsernameStartingWith | ... WHERE username LIKE '?%' | 以某字符开头，就像"找出名字以'张'开头的人" |
| EndingWith | findByUsernameEndingWith | ... WHERE username LIKE '%?' | 以某字符结尾，就像"找出名字以'三'结尾的人" |
| Containing | findByUsernameContaining | ... WHERE username LIKE '%?%' | 包含某字符，就像"找出名字中包含'三'的人" |
| GreaterThan | findByAgeGreaterThan | ... WHERE age > ? | 大于某值，就像"找出年龄大于30岁的人" |
| LessThan | findByAgeLessThan | ... WHERE age < ? | 小于某值，就像"找出年龄小于30岁的人" |
| Between | findByAgeBetween | ... WHERE age BETWEEN ? AND ? | 在两值之间，就像"找出年龄在20到30岁之间的人" |
| In | findByAgeIn | ... WHERE age IN (?, ?, ...) | 在列表中的值，就像"找出年龄是18、20或25岁的人" |
| IsNull | findByAddressIsNull | ... WHERE address IS NULL | 值为空，就像"找出没有填写地址的人" |
| IsNotNull | findByAddressIsNotNull | ... WHERE address IS NOT NULL | 值不为空，就像"找出已填写地址的人" |
| OrderBy | findByAgeOrderByUsernameDesc | ... WHERE age = ? ORDER BY username DESC | 排序，就像"找出30岁的人，按名字倒序排列" |

**示例**：

```java
/**
 * 用户数据访问接口
 * 演示基于方法名的查询功能
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    /**
     * 根据用户名查找用户
     * 生成SQL: SELECT * FROM user_management.user WHERE username = ?
     *
     * @param username 用户名
     * @return 匹配的用户，如果不存在则返回null
     */
    User findByUsername(String username);

    /**
     * 根据邮箱查找用户
     * 生成SQL: SELECT * FROM user_management.user WHERE email = ?
     *
     * @param email 电子邮箱
     * @return 匹配的用户，如果不存在则返回null
     */
    User findByEmail(String email);

    /**
     * 根据用户名或邮箱查找用户
     * 生成SQL: SELECT * FROM user_management.user WHERE username = ? OR email = ?
     *
     * @param username 用户名
     * @param email 电子邮箱
     * @return 匹配的用户，如果不存在则返回null
     */
    User findByUsernameOrEmail(String username, String email);

    /**
     * 查找包含指定名称的用户，不区分大小写
     * 生成SQL: SELECT * FROM user_management.user WHERE LOWER(username) LIKE LOWER(CONCAT('%', ?, '%'))
     *
     * @param name 用户名的部分内容
     * @return 匹配的用户列表
     */
    List<User> findByUsernameContainingIgnoreCase(String name);

    /**
     * 查找指定状态的用户，并按创建时间降序排序
     * 生成SQL: SELECT * FROM user_management.user WHERE status = ? ORDER BY created_at DESC
     *
     * @param status 用户状态
     * @return 匹配的用户列表，按创建时间降序排序
     */
    List<User> findByStatusOrderByCreatedAtDesc(String status);

    /**
     * 查找最近登录的前10个用户
     * 生成SQL: SELECT * FROM user_management.user ORDER BY last_login_at DESC LIMIT 10
     *
     * @return 最近登录的10个用户列表
     */
    List<User> findTop10ByOrderByLastLoginAtDesc();

    /**
     * 统计指定状态的用户数量
     * 生成SQL: SELECT COUNT(*) FROM user_management.user WHERE status = ?
     *
     * @param status 用户状态
     * @return 匹配的用户数量
     */
    long countByStatus(String status);

    /**
     * 判断用户名是否存在
     * 生成SQL: SELECT COUNT(*) > 0 FROM user_management.user WHERE username = ?
     *
     * @param username 用户名
     * @return 如果存在则返回true，否则返回false
     */
    boolean existsByUsername(String username);

    /**
     * 删除指定邮箱的用户
     * 生成SQL: DELETE FROM user_management.user WHERE email = ?
     *
     * @param email 电子邮箱
     */
    void deleteByEmail(String email);
}
```

### 1.4 使用@Query注解

对于复杂查询，可以使用**@Query注解**自定义JPQL或原生SQL查询：

> **通俗解释**：@Query注解就像是一个特殊标记，告诉Spring Data JPA"不要根据方法名生成查询，而是使用我明确提供的查询语句"，适用于复杂查询场景。

```java
/**
 * 用户数据访问接口
 * 演示使用@Query注解自定义查询
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    /**
     * 使用JPQL查询活跃用户
     * JPQL是JPA查询语言，类似SQL但操作的是对象而非表
     *
     * @param status 用户状态参数
     * @param date 日期参数，查询在此日期之后创建的用户
     * @return 匹配条件的用户列表
     */
    // 使用JPQL查询
    @Query("SELECT u FROM User u WHERE u.status = :status AND u.createdAt > :date")
    List<User> findActiveUsersCreatedAfter(@Param("status") String status, @Param("date") LocalDateTime date);

    /**
     * 使用原生SQL查询活跃用户
     * nativeQuery=true表示使用原生SQL而非JPQL
     * 使用位置参数?1, ?2代替命名参数
     *
     * @param status 用户状态参数
     * @param date 日期参数，查询在此日期之后创建的用户
     * @return 匹配条件的用户列表
     */
    // 使用原生SQL查询
    @Query(value = "SELECT * FROM user_management.user WHERE status = ?1 AND created_at > ?2", nativeQuery = true)
    List<User> findActiveUsersCreatedAfterNative(String status, LocalDateTime date);

    /**
     * 根据邮箱域名查询用户
     * 使用LIKE操作符进行模糊匹配
     *
     * @param domain 邮箱域名
     * @return 匹配域名的用户列表
     */
    // 使用命名参数
    @Query("SELECT u FROM User u WHERE u.email LIKE %:domain%")
    List<User> findByEmailDomain(@Param("domain") String domain);

    /**
     * 更新不活跃用户的状态
     * 使用@Modifying注解标记为修改操作
     * 使用@Transactional注解确保事务性
     *
     * @param status 新的用户状态
     * @param date 日期参数，更新在此日期之前最后登录的用户
     * @return 更新的记录数
     */
    // 更新查询
    @Modifying  // 标记为修改操作
    @Transactional  // 确保事务性
    @Query("UPDATE User u SET u.status = :status WHERE u.lastLoginAt < :date")
    int updateStatusForInactiveUsers(@Param("status") String status, @Param("date") LocalDateTime date);

    /**
     * 根据用户偏好查询用户
     * 演示关联查询，JOIN连接User和UserPreference表
     *
     * @param key 偏好键名
     * @param value 偏好值
     * @return 匹配偏好的用户列表
     */
    // 复杂查询，关联多个表
    @Query("SELECT u FROM User u JOIN u.preferences p WHERE p.key = :key AND p.value = :value")
    List<User> findByPreference(@Param("key") String key, @Param("value") String value);
}
```

> **通俗解释**：
> - **JPQL(JPA查询语言)**：一种类似SQL但操作的是对象而非表的查询语言，就像用Java的思维方式来描述数据库查询。
> - **@Param**：参数标记，将方法参数与查询语句中的命名参数关联起来，就像给参数贴上标签，便于在查询中引用。
> - **nativeQuery=true**：表示使用原生SQL而非JPQL，就像告诉JPA"这是原始的SQL语句，不要翻译它"。
> - **@Modifying**：标记查询为修改操作（如UPDATE、DELETE），就像告诉JPA"这不是查询，而是要修改数据"。
> - **@Transactional**：确保操作的事务性，要么全部成功，要么全部失败，就像银行转账，确保钱从一个账户转出并成功转入另一个账户。
```

### 1.5 分页和排序

Spring Data JPA提供了强大的**分页和排序**功能：

> **通俗解释**：
> - **分页(Pagination)**：将查询结果分成多页返回的技术，就像图书馆的书分成多页，一次只看一页，避免一次加载过多数据。
> - **排序(Sorting)**：按照指定字段对结果进行排序，就像按照书名、出版日期或作者名字对书籍进行排序。

```java
/**
 * 用户数据访问接口
 * 演示分页和排序功能
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    /**
     * 分页查询指定状态的用户
     *
     * @param status 用户状态
     * @param pageable 分页参数，包含页码、每页大小和排序信息
     * @return 分页结果，包含匹配的用户和分页元数据
     */
    // 分页查询
    Page<User> findByStatus(String status, Pageable pageable);

    /**
     * 排序查询指定状态的用户
     * 方法重载，与上面的方法同名但参数不同
     *
     * @param status 用户状态
     * @param sort 排序参数，指定排序字段和方向
     * @return 排序后的用户列表
     */
    // 排序查询
    List<User> findByStatus(String status, Sort sort);

    /**
     * 分页和排序组合查询
     * 查询指定状态且在指定日期之后创建的用户
     *
     * @param status 用户状态
     * @param date 日期参数，查询在此日期之后创建的用户
     * @param pageable 分页参数，包含页码、每页大小和排序信息
     * @return 分页结果，包含匹配的用户和分页元数据
     */
    // 分页和排序组合查询
    Page<User> findByStatusAndCreatedAtAfter(String status, LocalDateTime date, Pageable pageable);
}
```

使用示例：

```java
/**
 * 分页和排序功能的使用示例
 */
// 创建分页请求，第1页（索引从0开始），每页10条，按创建时间降序排序
Pageable pageable = PageRequest.of(0, 10, Sort.by("createdAt").descending());

// 查询激活状态的用户，分页显示
// 返回Page对象，包含当前页的数据和分页元数据
Page<User> userPage = userRepository.findByStatus("ACTIVE", pageable);

// 获取分页信息
int totalPages = userPage.getTotalPages();  // 总页数
long totalElements = userPage.getTotalElements();  // 总记录数
List<User> users = userPage.getContent();  // 当前页的数据列表

// 创建排序请求，按用户名升序排序
// Sort对象可以独立使用，也可以作为Pageable的一部分
Sort sort = Sort.by("username").ascending();

// 查询激活状态的用户，按用户名排序
// 返回排序后的列表，不包含分页信息
List<User> sortedUsers = userRepository.findByStatus("ACTIVE", sort);

// 其他常用的分页和排序操作
// 多字段排序：先按状态升序，再按创建时间降序
Sort multiSort = Sort.by("status").ascending().and(Sort.by("createdAt").descending());

// 创建带多字段排序的分页请求
Pageable sortedPageable = PageRequest.of(0, 20, multiSort);

// 检查是否有下一页
boolean hasNext = userPage.hasNext();

// 获取下一页的分页请求
Pageable nextPageable = userPage.nextPageable();
```

> **通俗解释**：
> - **Page**：分页结果对象，包含当前页的数据和分页元数据，就像一本书的某一页，既有内容，又有页码信息。
> - **Pageable**：分页请求参数，包含页码、每页大小和排序信息，就像告诉图书管理员"我要看第几页，每页要看多少内容"。
> - **PageRequest.of()**：创建分页请求的工厂方法，就像填写一张"借阅申请单"，指定要看哪一页。
> - **Sort**：排序参数，指定排序字段和方向，就像告诉图书管理员"按照什么顺序排列这些书"。

## 2. 高级查询技术

### 2.1 Specification查询

对于复杂的动态查询，可以使用JPA Criteria API和**Specification接口**：

> **通俗解释**：Specification查询就像是一种动态拼装查询条件的技术，可以根据用户输入的不同条件灵活构建查询，就像根据客户不断变化的需求灵活调整查询条件。

```java
/**
 * 用户数据访问接口
 * 演示Specification查询功能
 * 继承JpaSpecificationExecutor接口，支持动态条件查询
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {
    // 继承JpaSpecificationExecutor接口，支持Specification查询
    // 该接口提供了findAll(Specification)、findOne(Specification)等方法
    // 不需要额外定义方法，直接使用接口提供的方法即可
}
```

使用示例：

```java
/**
 * Specification查询的使用示例
 * 演示如何创建动态查询条件
 */
// 创建Specification对象，定义查询条件
// root: 查询的根对象，对应实体类
// query: 查询对象，用于设置查询类型、分组、排序等
// criteriaBuilder: 条件构造器，用于创建条件表达式
Specification<User> spec = (root, query, criteriaBuilder) -> {
    // 创建条件列表，用于存储所有查询条件
    List<Predicate> predicates = new ArrayList<>();

    // 动态添加查询条件，根据实际参数值决定是否添加条件
    // 模糊查询用户名
    if (username != null) {
        // 创建LIKE条件：username LIKE '%value%'
        predicates.add(criteriaBuilder.like(root.get("username"), "%" + username + "%"));
    }

    // 精确匹配状态
    if (status != null) {
        // 创建等值条件：status = value
        predicates.add(criteriaBuilder.equal(root.get("status"), status));
    }

    // 日期范围查询
    if (startDate != null && endDate != null) {
        // 创建BETWEEN条件：createdAt BETWEEN startDate AND endDate
        predicates.add(criteriaBuilder.between(root.get("createdAt"), startDate, endDate));
    }

    // 将所有条件用AND连接，返回最终的条件表达式
    // 如果predicates为空，则返回null，表示无条件查询
    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
};

// 使用Specification查询，返回所有匹配条件的用户
// 如果spec返回null，则查询所有用户
List<User> users = userRepository.findAll(spec);

// 结合分页和排序，返回分页结果
// 第1页（索引从0开始），每页10条，按创建时间降序排序
Page<User> userPage = userRepository.findAll(spec, PageRequest.of(0, 10, Sort.by("createdAt").descending()));

// 其他常用操作
// 查询单个结果
Optional<User> singleUser = userRepository.findOne(spec);

// 统计匹配条件的记录数
long count = userRepository.count(spec);

// 检查是否存在匹配条件的记录
boolean exists = userRepository.exists(spec);
```

> **通俗解释**：
> - **JpaSpecificationExecutor**：提供基于Specification的查询功能的接口，就像一个专门处理复杂查询的助手。
> - **Specification**：查询规范，定义查询条件的接口，就像一张详细的查询表单。
> - **Predicate**：条件表达式，表示一个查询条件，就像查询表单中的一个筛选条件。
> - **CriteriaBuilder**：条件构造器，用于创建各种条件表达式，就像一个条件生成工具。
> - **Root**：查询的根对象，对应实体类，就像查询的起点，指定从哪个表开始查询。

### 2.2 命名查询

JPA支持在实体类上使用**@NamedQuery**和**@NamedNativeQuery**定义命名查询：

> **通俗解释**：命名查询就像是预先定义好的查询模板，有了名字的查询，可以在多个地方重复使用，就像餐厅的固定菜单，点菜时只需要说菜名，不需要每次都详细描述菜的做法。

```java
/**
 * 用户实体类
 * 演示命名查询的定义
 */
@Entity  // 标记为JPA实体类
@Table(name = "user", schema = "user_management")  // 指定表名和Schema
// 定义JPQL命名查询，可以定义多个查询
@NamedQueries({
    // 查询所有活跃用户
    // name格式：实体类名.方法名，与Repository中的方法名对应
    @NamedQuery(
        name = "User.findActiveUsers",  // 查询名称
        query = "SELECT u FROM User u WHERE u.status = 'ACTIVE'"  // JPQL查询语句
    ),
    // 根据邮箱域名查询用户
    @NamedQuery(
        name = "User.findByDomain",  // 查询名称
        query = "SELECT u FROM User u WHERE u.email LIKE CONCAT('%@', :domain)"  // 带参数的JPQL查询语句
    )
})
// 定义原生SQL命名查询，可以定义多个查询
@NamedNativeQueries({
    // 使用原生SQL查询活跃用户
    @NamedNativeQuery(
        name = "User.findActiveUsersNative",  // 查询名称
        query = "SELECT * FROM user_management.user WHERE status = 'ACTIVE'",  // 原生SQL查询语句
        resultClass = User.class  // 结果映射的实体类
    )
})
public class User {
    // 实体类定义...
    // 这里省略了实体类的属性和方法
}
```

在Repository中使用命名查询：

```java
/**
 * 用户数据访问接口
 * 演示命名查询的使用
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    /**
     * 使用命名查询查找活跃用户
     * 方法名必须与@NamedQuery中定义的name属性的后缀部分一致
     * 自动使用User.findActiveUsers命名查询
     *
     * @return 活跃用户列表
     */
    // 使用命名查询
    List<User> findActiveUsers();

    /**
     * 使用带参数的命名查询
     * 方法名必须与@NamedQuery中定义的name属性的后缀部分一致
     * 自动使用User.findByDomain命名查询
     *
     * @param domain 邮箱域名参数，对应查询中的:domain参数
     * @return 匹配域名的用户列表
     */
    // 使用带参数的命名查询
    List<User> findByDomain(@Param("domain") String domain);

    /**
     * 使用原生SQL命名查询
     * 方法名必须与@NamedNativeQuery中定义的name属性的后缀部分一致
     * 自动使用User.findActiveUsersNative命名查询
     *
     * @return 活跃用户列表
     */
    // 使用原生命名查询
    List<User> findActiveUsersNative();
}
```

> **通俗解释**：
> - **@NamedQuery**：定义一个命名的JPQL查询，就像给一个常用的查询起个名字，方便重复使用。
> - **@NamedQueries**：定义多个命名查询的集合，就像一个查询菜单，包含多个预定义的查询。
> - **@NamedNativeQuery**：定义一个命名的原生SQL查询，就像给一个SQL语句起个名字，适用于JPQL无法满足的复杂查询。
> - **@NamedNativeQueries**：定义多个原生SQL命名查询的集合，就像一个SQL语句菜单。
```

### 2.3 投影查询

对于只需要返回部分字段的查询，可以使用**投影查询**：

> **通俗解释**：投影查询就像是只选择需要的字段而不是整个实体，就像在图书馆只借阅书中的某几章而不是整本书，减少数据传输量，提高效率。

```java
/**
 * 用户摘要投影接口
 * 定义需要返回的字段，只返回部分字段而非整个实体
 * 接口方法名必须与实体类的属性名对应（get + 属性名首字母大写）
 */
// 定义投影接口
public interface UserSummary {
    /**
     * 获取用户ID
     * 方法名必须与实体类的属性对应（get + 属性名首字母大写）
     *
     * @return 用户ID
     */
    Long getUserId();

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    String getUsername();

    /**
     * 获取电子邮箱
     *
     * @return 电子邮箱
     */
    String getEmail();

    /**
     * 获取用户全名（计算属性）
     * 使用SpEL表达式计算属性值
     * target指向当前实体对象
     *
     * @return 用户全名
     */
    // 可以添加计算属性
    @Value("#{target.firstName + ' ' + target.lastName}")
    String getFullName();
}

/**
 * 用户数据访问接口
 * 演示投影查询的使用
 */
// 在Repository中使用投影
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    /**
     * 根据状态查询用户摘要信息
     * 返回UserSummary接口类型，只包含接口定义的字段
     * Spring Data JPA会自动创建接口的实现类
     *
     * @param status 用户状态
     * @return 用户摘要信息列表
     */
    List<UserSummary> findByStatus(String status);

    /**
     * 使用@Query注解自定义投影查询
     * 查询字段别名必须与接口方法名对应
     *
     * @param status 用户状态
     * @return 用户摘要信息列表
     */
    @Query("SELECT u.userId as userId, u.username as username, u.email as email FROM User u WHERE u.status = :status")
    List<UserSummary> findSummaryByStatus(@Param("status") String status);

    /**
     * 使用原生SQL进行投影查询
     *
     * @param status 用户状态
     * @return 用户摘要信息列表
     */
    @Query(value = "SELECT user_id as userId, username, email FROM user_management.user WHERE status = :status",
           nativeQuery = true)
    List<UserSummary> findSummaryByStatusNative(@Param("status") String status);
}
```

> **通俗解释**：
> - **投影接口**：定义需要返回的字段集合的接口，就像一个数据筛选器，只保留需要的字段。
> - **接口方法**：每个方法对应实体类的一个属性，方法名必须遵循特定规则，就像告诉JPA"我需要这些字段"。
> - **计算属性**：通过表达式计算得到的属性，不直接对应数据库字段，就像根据已有数据计算出新的信息。
> - **@Value**：使用SpEL表达式定义计算属性，就像一个小型计算器，可以组合多个字段生成新值。
```

### 2.4 使用QueryDSL

**QueryDSL**提供了类型安全的查询API，可以与Spring Data JPA集成：

> **通俗解释**：QueryDSL就像是一种特殊的积木，按照规则组合就能构建出正确的查询，避免拼写错误和类型错误，提供了比字符串拼接更安全、更灵活的查询构建方式。

```java
/**
 * 用户数据访问接口
 * 演示QueryDSL查询功能
 * 继承QuerydslPredicateExecutor接口，支持类型安全的查询
 */
// 添加QueryDSL支持
@Repository
public interface UserRepository extends JpaRepository<User, Long>, QuerydslPredicateExecutor<User> {
    // 继承QuerydslPredicateExecutor接口，支持QueryDSL查询
    // 该接口提供了findAll(Predicate)、findOne(Predicate)等方法
    // 不需要额外定义方法，直接使用接口提供的方法即可
}
```

使用示例：

```java
/**
 * QueryDSL查询的使用示例
 * 演示类型安全的查询构建
 */
// 创建查询
// QUser是由QueryDSL自动生成的查询类，对应User实体
QUser user = QUser.user;  // 获取User实体的查询对象

// 构建查询条件
// 使用类型安全的方式构建查询条件，避免字符串拼接和类型错误
Predicate predicate = user.status.eq("ACTIVE")  // 状态等于"ACTIVE"
                         .and(user.createdAt.after(startDate))  // 创建时间在startDate之后
                         .and(user.email.like("%@example.com"));  // 邮箱包含"@example.com"

// 执行查询
// 使用QuerydslPredicateExecutor接口提供的findAll方法
Iterable<User> users = userRepository.findAll(predicate);

// 结合排序
// 按用户名升序排序
Iterable<User> sortedUsers = userRepository.findAll(predicate, user.username.asc());

// 结合分页
// 第1页（索引从0开始），每页10条
Page<User> userPage = userRepository.findAll(predicate, PageRequest.of(0, 10));

// 其他常用操作
// 复杂条件组合
Predicate complexPredicate = user.status.eq("ACTIVE")
    .and(
        user.email.like("%@example.com")
            .or(user.email.like("%@gmail.com"))
    )
    .and(user.createdAt.between(startDate, endDate));

// 使用BooleanBuilder构建动态条件
BooleanBuilder builder = new BooleanBuilder();
if (username != null) {
    builder.and(user.username.containsIgnoreCase(username));
}
if (status != null) {
    builder.and(user.status.eq(status));
}
if (startDate != null && endDate != null) {
    builder.and(user.createdAt.between(startDate, endDate));
}

// 使用动态构建的条件查询
Iterable<User> dynamicUsers = userRepository.findAll(builder.getValue());
```

> **通俗解释**：
> - **QuerydslPredicateExecutor**：提供基于QueryDSL的查询功能的接口，就像一个专门处理QueryDSL查询的助手。
> - **QUser**：QueryDSL自动生成的查询类，对应User实体，就像一个实体类的查询模板。
> - **Predicate**：查询条件表达式，就像一个条件组合器，可以组合多个条件。
> - **BooleanBuilder**：动态条件构建器，就像一个条件收集器，可以根据需要动态添加条件。
> - **类型安全**：在编译时就能发现错误，而不是在运行时，就像提前检查拼写错误，避免运行时才发现问题。

## 3. 下一步学习计划

在掌握了Repository接口设计和高级查询技术后，你可以继续学习以下内容：

1. **Schema设计与管理**：如何规划和管理多Schema环境
2. **高级特性应用**：如何使用PostgreSQL的高级特性，如JSONB、数组等
3. **性能优化**：如何优化PostgreSQL查询性能
4. **事务管理**：如何在Spring Boot中管理事务

这些内容将在后续教程中详细介绍。

## 语法规则总结

为了帮助你快速掌握Spring Data JPA Repository接口设计和高级查询技术的语法，以下是本章涉及的主要语法规则总结：

### 1. Repository接口定义语法

#### 1.1 基本Repository接口定义

```java
// 基本语法
public interface EntityNameRepository extends JpaRepository<EntityType, IdType> {
    // 自定义查询方法...
}

// 示例
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    // 自定义查询方法...
}
```

> **语法说明**：
> - `@Repository`：标记接口为Spring的Repository组件，可选但推荐使用
> - `EntityType`：实体类类型，如User
> - `IdType`：主键类型，如Long
> - 继承`JpaRepository`获取完整的CRUD、分页和排序功能
> - 也可以继承其他Repository接口：
>   - `Repository`：标记接口，不包含任何方法
>   - `CrudRepository`：提供基本CRUD操作
>   - `PagingAndSortingRepository`：提供分页和排序功能
>   - `JpaSpecificationExecutor`：提供Specification查询功能

### 2. 基于方法名的查询语法

#### 2.1 查询方法命名规则

```java
// 基本语法
ReturnType findBy<PropertyName>[<Operator>][<Condition>](<ParameterType> <parameterName>);

// 示例
User findByUsername(String username);
List<User> findByEmailContaining(String emailPart);
List<User> findByStatusAndCreatedAtAfter(String status, LocalDateTime date);
List<User> findTop10ByOrderByLastLoginAtDesc();
long countByStatus(String status);
boolean existsByUsername(String username);
void deleteByEmail(String email);
```

> **语法说明**：
> - 方法名以动词开头，如`find`、`get`、`count`、`exists`、`delete`
> - 后跟`By`关键字（除了`findAll`等特殊方法）
> - 然后是属性名（首字母大写）
> - 可选的操作符和条件
> - 参数类型和参数名
> - 返回类型可以是实体、集合、基本类型等

#### 2.2 常用方法名关键字

| 关键字 | 示例 | 生成的SQL | 语法规则 |
|-------|------|----------|---------|
| And | findByUsernameAndEmail | ... WHERE username = ? AND email = ? | 属性名1And属性名2 |
| Or | findByUsernameOrEmail | ... WHERE username = ? OR email = ? | 属性名1Or属性名2 |
| Equals | findByUsername | ... WHERE username = ? | 默认，可省略 |
| Like | findByUsernameLike | ... WHERE username LIKE ? | 属性名Like |
| NotLike | findByUsernameNotLike | ... WHERE username NOT LIKE ? | 属性名NotLike |
| StartingWith | findByUsernameStartingWith | ... WHERE username LIKE '?%' | 属性名StartingWith |
| EndingWith | findByUsernameEndingWith | ... WHERE username LIKE '%?' | 属性名EndingWith |
| Containing | findByUsernameContaining | ... WHERE username LIKE '%?%' | 属性名Containing |
| GreaterThan | findByAgeGreaterThan | ... WHERE age > ? | 属性名GreaterThan |
| LessThan | findByAgeLessThan | ... WHERE age < ? | 属性名LessThan |
| Between | findByAgeBetween | ... WHERE age BETWEEN ? AND ? | 属性名Between |
| In | findByAgeIn | ... WHERE age IN (?, ?, ...) | 属性名In |
| IsNull | findByAddressIsNull | ... WHERE address IS NULL | 属性名IsNull |
| IsNotNull | findByAddressIsNotNull | ... WHERE address IS NOT NULL | 属性名IsNotNull |
| OrderBy | findByAgeOrderByUsernameDesc | ... WHERE age = ? ORDER BY username DESC | 属性名OrderBy属性名Asc/Desc |

### 3. @Query注解语法

#### 3.1 JPQL查询语法

```java
// 基本语法
@Query("SELECT e FROM EntityName e WHERE e.property = :paramName")
ReturnType methodName(@Param("paramName") ParameterType paramValue);

// 示例
@Query("SELECT u FROM User u WHERE u.status = :status AND u.createdAt > :date")
List<User> findActiveUsersCreatedAfter(@Param("status") String status, @Param("date") LocalDateTime date);
```

> **语法说明**：
> - `@Query`：定义查询语句
> - JPQL查询语句使用实体名和属性名，而不是表名和列名
> - `:paramName`：命名参数，需要使用`@Param`注解绑定
> - 返回类型可以是实体、集合、基本类型等

#### 3.2 原生SQL查询语法

```java
// 基本语法
@Query(value = "SELECT * FROM table_name WHERE column_name = ?1", nativeQuery = true)
ReturnType methodName(ParameterType param);

// 示例
@Query(value = "SELECT * FROM user_management.user WHERE status = ?1 AND created_at > ?2", nativeQuery = true)
List<User> findActiveUsersCreatedAfterNative(String status, LocalDateTime date);
```

> **语法说明**：
> - `value`：SQL查询语句
> - `nativeQuery = true`：指定使用原生SQL而非JPQL
> - `?1`, `?2`：位置参数，1表示第一个参数，2表示第二个参数
> - 也可以使用命名参数：`WHERE column_name = :paramName`

#### 3.3 修改查询语法

```java
// 基本语法
@Modifying
@Transactional
@Query("UPDATE EntityName e SET e.property = :value WHERE e.condition = :condition")
int methodName(@Param("value") ValueType value, @Param("condition") ConditionType condition);

// 示例
@Modifying
@Transactional
@Query("UPDATE User u SET u.status = :status WHERE u.lastLoginAt < :date")
int updateStatusForInactiveUsers(@Param("status") String status, @Param("date") LocalDateTime date);
```

> **语法说明**：
> - `@Modifying`：标记为修改操作（UPDATE或DELETE）
> - `@Transactional`：确保事务性
> - 返回值为int类型，表示受影响的行数

### 4. 分页和排序语法

#### 4.1 分页查询语法

```java
// Repository接口方法
Page<EntityType> findByProperty(PropertyType property, Pageable pageable);

// 使用示例
// 创建分页请求，第0页（从0开始），每页10条，按createdAt降序排序
Pageable pageable = PageRequest.of(0, 10, Sort.by("createdAt").descending());
// 执行分页查询
Page<User> userPage = userRepository.findByStatus("ACTIVE", pageable);
// 获取分页信息
int totalPages = userPage.getTotalPages();  // 总页数
long totalElements = userPage.getTotalElements();  // 总记录数
List<User> users = userPage.getContent();  // 当前页的数据
boolean hasNext = userPage.hasNext();  // 是否有下一页
```

> **语法说明**：
> - `Page<EntityType>`：分页结果，包含数据和分页信息
> - `Pageable`：分页请求参数
> - `PageRequest.of()`：创建分页请求
>   - 第一个参数：页码（从0开始）
>   - 第二个参数：每页大小
>   - 第三个参数：排序信息（可选）

#### 4.2 排序查询语法

```java
// Repository接口方法
List<EntityType> findByProperty(PropertyType property, Sort sort);

// 使用示例
// 创建排序，按username升序
Sort sort = Sort.by("username").ascending();
// 执行排序查询
List<User> sortedUsers = userRepository.findByStatus("ACTIVE", sort);

// 多字段排序
Sort multiSort = Sort.by("status").ascending().and(Sort.by("createdAt").descending());
```

> **语法说明**：
> - `Sort`：排序参数
> - `Sort.by()`：创建排序
>   - 参数：排序字段
> - `ascending()`/`descending()`：指定排序方向
> - `and()`：组合多个排序条件

### 5. Specification查询语法

#### 5.1 定义Repository接口

```java
// 基本语法
public interface EntityRepository extends JpaRepository<Entity, IdType>, JpaSpecificationExecutor<Entity> {
    // 自定义方法...
}

// 示例
@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {
    // 自定义方法...
}
```

> **语法说明**：
> - 继承`JpaSpecificationExecutor<Entity>`接口，获取Specification查询功能
> - 该接口提供了`findAll(Specification)`、`findOne(Specification)`等方法

#### 5.2 创建Specification

```java
// 基本语法
Specification<Entity> spec = (root, query, criteriaBuilder) -> {
    // 创建条件...
    return criteriaBuilder.predicate;
};

// 示例
Specification<User> spec = (root, query, criteriaBuilder) -> {
    List<Predicate> predicates = new ArrayList<>();

    if (username != null) {
        predicates.add(criteriaBuilder.like(root.get("username"), "%" + username + "%"));
    }

    if (status != null) {
        predicates.add(criteriaBuilder.equal(root.get("status"), status));
    }

    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
};

// 使用Specification查询
List<User> users = userRepository.findAll(spec);
```

> **语法说明**：
> - `Specification<Entity>`：查询规范，定义查询条件
> - `root`：查询的根对象，对应实体类
> - `query`：查询对象，用于设置查询类型、分组、排序等
> - `criteriaBuilder`：条件构造器，用于创建条件表达式
> - `Predicate`：条件表达式，表示一个查询条件
> - 常用条件构造方法：
>   - `equal`：等于
>   - `notEqual`：不等于
>   - `like`：模糊匹配
>   - `greaterThan`/`lessThan`：大于/小于
>   - `between`：在两值之间
>   - `in`：在列表中
>   - `isNull`/`isNotNull`：为空/不为空
>   - `and`/`or`：条件组合

### 6. 命名查询语法

#### 6.1 在实体类上定义命名查询

```java
// 基本语法
@Entity
@NamedQueries({
    @NamedQuery(name = "EntityName.methodName", query = "JPQL查询语句")
})
@NamedNativeQueries({
    @NamedNativeQuery(name = "EntityName.methodName", query = "SQL查询语句", resultClass = Entity.class)
})
public class EntityName {
    // 实体类定义...
}

// 示例
@Entity
@Table(name = "user", schema = "user_management")
@NamedQueries({
    @NamedQuery(
        name = "User.findActiveUsers",
        query = "SELECT u FROM User u WHERE u.status = 'ACTIVE'"
    ),
    @NamedQuery(
        name = "User.findByDomain",
        query = "SELECT u FROM User u WHERE u.email LIKE CONCAT('%@', :domain)"
    )
})
@NamedNativeQueries({
    @NamedNativeQuery(
        name = "User.findActiveUsersNative",
        query = "SELECT * FROM user_management.user WHERE status = 'ACTIVE'",
        resultClass = User.class
    )
})
public class User {
    // 实体类定义...
}
```

> **语法说明**：
> - `@NamedQuery`：定义JPQL命名查询
>   - `name`：查询名称，通常使用`实体类名.方法名`格式
>   - `query`：JPQL查询语句
> - `@NamedNativeQuery`：定义原生SQL命名查询
>   - `name`：查询名称
>   - `query`：SQL查询语句
>   - `resultClass`：结果映射的实体类

#### 6.2 在Repository中使用命名查询

```java
// 基本语法
// 方法名必须与@NamedQuery中定义的name属性的后缀部分一致
ReturnType methodName(Parameters...);

// 示例
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    // 使用命名查询，方法名必须与@NamedQuery中定义的name属性的后缀部分一致
    List<User> findActiveUsers();

    // 使用带参数的命名查询
    List<User> findByDomain(@Param("domain") String domain);

    // 使用原生SQL命名查询
    List<User> findActiveUsersNative();
}
```

> **语法说明**：
> - 方法名必须与`@NamedQuery`或`@NamedNativeQuery`中定义的`name`属性的后缀部分一致
> - 如果命名查询中使用了命名参数，需要使用`@Param`注解绑定参数

### 7. 投影查询语法

#### 7.1 定义投影接口

```java
// 基本语法
public interface ProjectionName {
    // getter方法，方法名必须与实体类的属性对应
    PropertyType getPropertyName();

    // 计算属性
    @Value("#{表达式}")
    ComputedType getComputedPropertyName();
}

// 示例
public interface UserSummary {
    Long getUserId();
    String getUsername();
    String getEmail();

    // 计算属性
    @Value("#{target.firstName + ' ' + target.lastName}")
    String getFullName();
}
```

> **语法说明**：
> - 定义一个接口，包含需要返回的属性的getter方法
> - 方法名必须与实体类的属性对应（get + 属性名首字母大写）
> - `@Value`：使用SpEL表达式定义计算属性
>   - `target`：指向当前实体对象

#### 7.2 在Repository中使用投影

```java
// 基本语法
List<ProjectionName> findByProperty(PropertyType property);

// 示例
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    // 使用投影接口作为返回类型
    List<UserSummary> findByStatus(String status);

    // 使用@Query注解自定义投影查询
    @Query("SELECT u.userId as userId, u.username as username, u.email as email FROM User u WHERE u.status = :status")
    List<UserSummary> findSummaryByStatus(@Param("status") String status);
}
```

> **语法说明**：
> - 使用投影接口作为返回类型
> - 可以与`@Query`注解结合使用
> - 查询字段别名必须与接口方法名对应

### 8. QueryDSL查询语法

#### 8.1 定义Repository接口

```java
// 基本语法
public interface EntityRepository extends JpaRepository<Entity, IdType>, QuerydslPredicateExecutor<Entity> {
    // 自定义方法...
}

// 示例
@Repository
public interface UserRepository extends JpaRepository<User, Long>, QuerydslPredicateExecutor<User> {
    // 自定义方法...
}
```

> **语法说明**：
> - 继承`QuerydslPredicateExecutor<Entity>`接口，获取QueryDSL查询功能
> - 该接口提供了`findAll(Predicate)`、`findOne(Predicate)`等方法

#### 8.2 使用QueryDSL查询

```java
// 基本语法
QEntity entity = QEntity.entity;
Predicate predicate = entity.property.operation(value);
Iterable<Entity> results = repository.findAll(predicate);

// 示例
QUser user = QUser.user;
Predicate predicate = user.status.eq("ACTIVE")
                         .and(user.createdAt.after(startDate))
                         .and(user.email.like("%@example.com"));
Iterable<User> users = userRepository.findAll(predicate);

// 动态条件
BooleanBuilder builder = new BooleanBuilder();
if (username != null) {
    builder.and(user.username.containsIgnoreCase(username));
}
if (status != null) {
    builder.and(user.status.eq(status));
}
Iterable<User> dynamicUsers = userRepository.findAll(builder.getValue());
```

> **语法说明**：
> - `QEntity`：QueryDSL自动生成的查询类，对应实体类
> - `Predicate`：查询条件表达式
> - 常用操作符：
>   - `eq`：等于
>   - `ne`：不等于
>   - `like`/`contains`/`startsWith`/`endsWith`：模糊匹配
>   - `gt`/`lt`/`goe`/`loe`：大于/小于/大于等于/小于等于
>   - `between`：在两值之间
>   - `in`：在列表中
>   - `isNull`/`isNotNull`：为空/不为空
>   - `and`/`or`：条件组合
> - `BooleanBuilder`：动态条件构建器

### 9. 常见错误和注意事项

1. **方法名拼写错误**：基于方法名的查询要求方法名严格遵循命名规则，拼写错误会导致启动失败。

2. **属性名不匹配**：方法名中的属性名必须与实体类的属性名匹配（区分大小写）。

3. **返回类型不匹配**：返回类型必须与查询结果兼容，如查询单个结果时使用`List`会导致类型转换错误。

4. **忘记添加@Param注解**：使用命名参数时，必须使用`@Param`注解绑定参数。

5. **忘记添加@Modifying和@Transactional注解**：执行修改操作时，必须添加这两个注解。

6. **N+1查询问题**：关联查询时，如果没有使用适当的抓取策略，可能导致N+1查询问题，影响性能。

### 10. 最佳实践

1. **使用合适的Repository接口**：根据需求选择合适的Repository接口，如只需要基本CRUD操作，可以使用`CrudRepository`。

2. **合理命名查询方法**：方法名应该清晰表达查询意图，遵循命名规范。

3. **复杂查询使用@Query注解**：对于复杂查询，使用`@Query`注解比基于方法名的查询更清晰、更灵活。

4. **使用分页查询处理大量数据**：处理大量数据时，使用分页查询避免一次加载过多数据。

5. **使用投影查询优化性能**：只查询需要的字段，减少数据传输量。

6. **使用QueryDSL处理动态查询**：对于条件复杂且动态变化的查询，使用QueryDSL比Specification更简洁、更类型安全。

7. **使用命名查询集中管理SQL**：对于频繁使用的查询，使用命名查询集中管理，便于维护。

## 语法规则总结

为了帮助你快速掌握Spring Data JPA Repository接口设计和高级查询技术的语法，以下是本章涉及的主要语法规则总结：

### 1. Repository接口定义语法

#### 1.1 基本Repository接口定义

```java
// 基本语法
public interface EntityNameRepository extends JpaRepository<EntityType, IdType> {
    // 自定义查询方法...
}

// 示例
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    // 自定义查询方法...
}
```

> **语法说明**：
> - `@Repository`：标记接口为Spring的Repository组件，可选但推荐使用
> - `EntityType`：实体类类型，如User
> - `IdType`：主键类型，如Long
> - 继承`JpaRepository`获取完整的CRUD、分页和排序功能
> - 也可以继承其他Repository接口：
>   - `Repository`：标记接口，不包含任何方法
>   - `CrudRepository`：提供基本CRUD操作
>   - `PagingAndSortingRepository`：提供分页和排序功能
>   - `JpaSpecificationExecutor`：提供Specification查询功能

### 2. 基于方法名的查询语法

#### 2.1 查询方法命名规则

```java
// 基本语法
ReturnType findBy<PropertyName>[<Operator>][<Condition>](<ParameterType> <parameterName>);

// 示例
User findByUsername(String username);
List<User> findByEmailContaining(String emailPart);
List<User> findByStatusAndCreatedAtAfter(String status, LocalDateTime date);
List<User> findTop10ByOrderByLastLoginAtDesc();
long countByStatus(String status);
boolean existsByUsername(String username);
void deleteByEmail(String email);
```

> **语法说明**：
> - 方法名以动词开头，如`find`、`get`、`count`、`exists`、`delete`
> - 后跟`By`关键字（除了`findAll`等特殊方法）
> - 然后是属性名（首字母大写）
> - 可选的操作符和条件
> - 参数类型和参数名
> - 返回类型可以是实体、集合、基本类型等

#### 2.2 常用方法名关键字

| 关键字 | 示例 | 生成的SQL | 语法规则 |
|-------|------|----------|---------|
| And | findByUsernameAndEmail | ... WHERE username = ? AND email = ? | 属性名1And属性名2 |
| Or | findByUsernameOrEmail | ... WHERE username = ? OR email = ? | 属性名1Or属性名2 |
| Equals | findByUsername | ... WHERE username = ? | 默认，可省略 |
| Like | findByUsernameLike | ... WHERE username LIKE ? | 属性名Like |
| NotLike | findByUsernameNotLike | ... WHERE username NOT LIKE ? | 属性名NotLike |
| StartingWith | findByUsernameStartingWith | ... WHERE username LIKE '?%' | 属性名StartingWith |
| EndingWith | findByUsernameEndingWith | ... WHERE username LIKE '%?' | 属性名EndingWith |
| Containing | findByUsernameContaining | ... WHERE username LIKE '%?%' | 属性名Containing |
| GreaterThan | findByAgeGreaterThan | ... WHERE age > ? | 属性名GreaterThan |
| LessThan | findByAgeLessThan | ... WHERE age < ? | 属性名LessThan |
| Between | findByAgeBetween | ... WHERE age BETWEEN ? AND ? | 属性名Between |
| In | findByAgeIn | ... WHERE age IN (?, ?, ...) | 属性名In |
| IsNull | findByAddressIsNull | ... WHERE address IS NULL | 属性名IsNull |
| IsNotNull | findByAddressIsNotNull | ... WHERE address IS NOT NULL | 属性名IsNotNull |
| OrderBy | findByAgeOrderByUsernameDesc | ... WHERE age = ? ORDER BY username DESC | 属性名OrderBy属性名Asc/Desc |

### 3. @Query注解语法

#### 3.1 JPQL查询语法

```java
// 基本语法
@Query("SELECT e FROM EntityName e WHERE e.property = :paramName")
ReturnType methodName(@Param("paramName") ParameterType paramValue);

// 示例
@Query("SELECT u FROM User u WHERE u.status = :status AND u.createdAt > :date")
List<User> findActiveUsersCreatedAfter(@Param("status") String status, @Param("date") LocalDateTime date);
```

> **语法说明**：
> - `@Query`：定义查询语句
> - JPQL查询语句使用实体名和属性名，而不是表名和列名
> - `:paramName`：命名参数，需要使用`@Param`注解绑定
> - 返回类型可以是实体、集合、基本类型等

#### 3.2 原生SQL查询语法

```java
// 基本语法
@Query(value = "SELECT * FROM table_name WHERE column_name = ?1", nativeQuery = true)
ReturnType methodName(ParameterType param);

// 示例
@Query(value = "SELECT * FROM user_management.user WHERE status = ?1 AND created_at > ?2", nativeQuery = true)
List<User> findActiveUsersCreatedAfterNative(String status, LocalDateTime date);
```

> **语法说明**：
> - `value`：SQL查询语句
> - `nativeQuery = true`：指定使用原生SQL而非JPQL
> - `?1`, `?2`：位置参数，1表示第一个参数，2表示第二个参数
> - 也可以使用命名参数：`WHERE column_name = :paramName`

#### 3.3 修改查询语法

```java
// 基本语法
@Modifying
@Transactional
@Query("UPDATE EntityName e SET e.property = :value WHERE e.condition = :condition")
int methodName(@Param("value") ValueType value, @Param("condition") ConditionType condition);

// 示例
@Modifying
@Transactional
@Query("UPDATE User u SET u.status = :status WHERE u.lastLoginAt < :date")
int updateStatusForInactiveUsers(@Param("status") String status, @Param("date") LocalDateTime date);
```

> **语法说明**：
> - `@Modifying`：标记为修改操作（UPDATE或DELETE）
> - `@Transactional`：确保事务性
> - 返回值为int类型，表示受影响的行数

### 4. 分页和排序语法

#### 4.1 分页查询语法

```java
// Repository接口方法
Page<EntityType> findByProperty(PropertyType property, Pageable pageable);

// 使用示例
// 创建分页请求，第0页（从0开始），每页10条，按createdAt降序排序
Pageable pageable = PageRequest.of(0, 10, Sort.by("createdAt").descending());
// 执行分页查询
Page<User> userPage = userRepository.findByStatus("ACTIVE", pageable);
// 获取分页信息
int totalPages = userPage.getTotalPages();  // 总页数
long totalElements = userPage.getTotalElements();  // 总记录数
List<User> users = userPage.getContent();  // 当前页的数据
boolean hasNext = userPage.hasNext();  // 是否有下一页
```

> **语法说明**：
> - `Page<EntityType>`：分页结果，包含数据和分页信息
> - `Pageable`：分页请求参数
> - `PageRequest.of()`：创建分页请求
>   - 第一个参数：页码（从0开始）
>   - 第二个参数：每页大小
>   - 第三个参数：排序信息（可选）

#### 4.2 排序查询语法

```java
// Repository接口方法
List<EntityType> findByProperty(PropertyType property, Sort sort);

// 使用示例
// 创建排序，按username升序
Sort sort = Sort.by("username").ascending();
// 执行排序查询
List<User> sortedUsers = userRepository.findByStatus("ACTIVE", sort);

// 多字段排序
Sort multiSort = Sort.by("status").ascending().and(Sort.by("createdAt").descending());
```

> **语法说明**：
> - `Sort`：排序参数
> - `Sort.by()`：创建排序
>   - 参数：排序字段
> - `ascending()`/`descending()`：指定排序方向
> - `and()`：组合多个排序条件

### 5. Specification查询语法

#### 5.1 定义Repository接口

```java
// 基本语法
public interface EntityRepository extends JpaRepository<Entity, IdType>, JpaSpecificationExecutor<Entity> {
    // 自定义方法...
}

// 示例
@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {
    // 自定义方法...
}
```

> **语法说明**：
> - 继承`JpaSpecificationExecutor<Entity>`接口，获取Specification查询功能
> - 该接口提供了`findAll(Specification)`、`findOne(Specification)`等方法

#### 5.2 创建Specification

```java
// 基本语法
Specification<Entity> spec = (root, query, criteriaBuilder) -> {
    // 创建条件...
    return criteriaBuilder.predicate;
};

// 示例
Specification<User> spec = (root, query, criteriaBuilder) -> {
    List<Predicate> predicates = new ArrayList<>();

    if (username != null) {
        predicates.add(criteriaBuilder.like(root.get("username"), "%" + username + "%"));
    }

    if (status != null) {
        predicates.add(criteriaBuilder.equal(root.get("status"), status));
    }

    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
};

// 使用Specification查询
List<User> users = userRepository.findAll(spec);
```

> **语法说明**：
> - `Specification<Entity>`：查询规范，定义查询条件
> - `root`：查询的根对象，对应实体类
> - `query`：查询对象，用于设置查询类型、分组、排序等
> - `criteriaBuilder`：条件构造器，用于创建条件表达式
> - `Predicate`：条件表达式，表示一个查询条件
> - 常用条件构造方法：
>   - `equal`：等于
>   - `notEqual`：不等于
>   - `like`：模糊匹配
>   - `greaterThan`/`lessThan`：大于/小于
>   - `between`：在两值之间
>   - `in`：在列表中
>   - `isNull`/`isNotNull`：为空/不为空
>   - `and`/`or`：条件组合

### 6. 命名查询语法

#### 6.1 在实体类上定义命名查询

```java
// 基本语法
@Entity
@NamedQueries({
    @NamedQuery(name = "EntityName.methodName", query = "JPQL查询语句")
})
@NamedNativeQueries({
    @NamedNativeQuery(name = "EntityName.methodName", query = "SQL查询语句", resultClass = Entity.class)
})
public class EntityName {
    // 实体类定义...
}

// 示例
@Entity
@Table(name = "user", schema = "user_management")
@NamedQueries({
    @NamedQuery(
        name = "User.findActiveUsers",
        query = "SELECT u FROM User u WHERE u.status = 'ACTIVE'"
    ),
    @NamedQuery(
        name = "User.findByDomain",
        query = "SELECT u FROM User u WHERE u.email LIKE CONCAT('%@', :domain)"
    )
})
@NamedNativeQueries({
    @NamedNativeQuery(
        name = "User.findActiveUsersNative",
        query = "SELECT * FROM user_management.user WHERE status = 'ACTIVE'",
        resultClass = User.class
    )
})
public class User {
    // 实体类定义...
}
```

> **语法说明**：
> - `@NamedQuery`：定义JPQL命名查询
>   - `name`：查询名称，通常使用`实体类名.方法名`格式
>   - `query`：JPQL查询语句
> - `@NamedNativeQuery`：定义原生SQL命名查询
>   - `name`：查询名称
>   - `query`：SQL查询语句
>   - `resultClass`：结果映射的实体类

#### 6.2 在Repository中使用命名查询

```java
// 基本语法
// 方法名必须与@NamedQuery中定义的name属性的后缀部分一致
ReturnType methodName(Parameters...);

// 示例
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    // 使用命名查询，方法名必须与@NamedQuery中定义的name属性的后缀部分一致
    List<User> findActiveUsers();

    // 使用带参数的命名查询
    List<User> findByDomain(@Param("domain") String domain);

    // 使用原生SQL命名查询
    List<User> findActiveUsersNative();
}
```

> **语法说明**：
> - 方法名必须与`@NamedQuery`或`@NamedNativeQuery`中定义的`name`属性的后缀部分一致
> - 如果命名查询中使用了命名参数，需要使用`@Param`注解绑定参数

### 7. 投影查询语法

#### 7.1 定义投影接口

```java
// 基本语法
public interface ProjectionName {
    // getter方法，方法名必须与实体类的属性对应
    PropertyType getPropertyName();

    // 计算属性
    @Value("#{表达式}")
    ComputedType getComputedPropertyName();
}

// 示例
public interface UserSummary {
    Long getUserId();
    String getUsername();
    String getEmail();

    // 计算属性
    @Value("#{target.firstName + ' ' + target.lastName}")
    String getFullName();
}
```

> **语法说明**：
> - 定义一个接口，包含需要返回的属性的getter方法
> - 方法名必须与实体类的属性对应（get + 属性名首字母大写）
> - `@Value`：使用SpEL表达式定义计算属性
>   - `target`：指向当前实体对象

#### 7.2 在Repository中使用投影

```java
// 基本语法
List<ProjectionName> findByProperty(PropertyType property);

// 示例
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    // 使用投影接口作为返回类型
    List<UserSummary> findByStatus(String status);

    // 使用@Query注解自定义投影查询
    @Query("SELECT u.userId as userId, u.username as username, u.email as email FROM User u WHERE u.status = :status")
    List<UserSummary> findSummaryByStatus(@Param("status") String status);
}
```

> **语法说明**：
> - 使用投影接口作为返回类型
> - 可以与`@Query`注解结合使用
> - 查询字段别名必须与接口方法名对应

### 8. QueryDSL查询语法

#### 8.1 定义Repository接口

```java
// 基本语法
public interface EntityRepository extends JpaRepository<Entity, IdType>, QuerydslPredicateExecutor<Entity> {
    // 自定义方法...
}

// 示例
@Repository
public interface UserRepository extends JpaRepository<User, Long>, QuerydslPredicateExecutor<User> {
    // 自定义方法...
}
```

> **语法说明**：
> - 继承`QuerydslPredicateExecutor<Entity>`接口，获取QueryDSL查询功能
> - 该接口提供了`findAll(Predicate)`、`findOne(Predicate)`等方法

#### 8.2 使用QueryDSL查询

```java
// 基本语法
QEntity entity = QEntity.entity;
Predicate predicate = entity.property.operation(value);
Iterable<Entity> results = repository.findAll(predicate);

// 示例
QUser user = QUser.user;
Predicate predicate = user.status.eq("ACTIVE")
                         .and(user.createdAt.after(startDate))
                         .and(user.email.like("%@example.com"));
Iterable<User> users = userRepository.findAll(predicate);

// 动态条件
BooleanBuilder builder = new BooleanBuilder();
if (username != null) {
    builder.and(user.username.containsIgnoreCase(username));
}
if (status != null) {
    builder.and(user.status.eq(status));
}
Iterable<User> dynamicUsers = userRepository.findAll(builder.getValue());
```

> **语法说明**：
> - `QEntity`：QueryDSL自动生成的查询类，对应实体类
> - `Predicate`：查询条件表达式
> - 常用操作符：
>   - `eq`：等于
>   - `ne`：不等于
>   - `like`/`contains`/`startsWith`/`endsWith`：模糊匹配
>   - `gt`/`lt`/`goe`/`loe`：大于/小于/大于等于/小于等于
>   - `between`：在两值之间
>   - `in`：在列表中
>   - `isNull`/`isNotNull`：为空/不为空
>   - `and`/`or`：条件组合
> - `BooleanBuilder`：动态条件构建器

### 9. 常见错误和注意事项

1. **方法名拼写错误**：基于方法名的查询要求方法名严格遵循命名规则，拼写错误会导致启动失败。

2. **属性名不匹配**：方法名中的属性名必须与实体类的属性名匹配（区分大小写）。

3. **返回类型不匹配**：返回类型必须与查询结果兼容，如查询单个结果时使用`List`会导致类型转换错误。

4. **忘记添加@Param注解**：使用命名参数时，必须使用`@Param`注解绑定参数。

5. **忘记添加@Modifying和@Transactional注解**：执行修改操作时，必须添加这两个注解。

6. **N+1查询问题**：关联查询时，如果没有使用适当的抓取策略，可能导致N+1查询问题，影响性能。

### 10. 最佳实践

1. **使用合适的Repository接口**：根据需求选择合适的Repository接口，如只需要基本CRUD操作，可以使用`CrudRepository`。

2. **合理命名查询方法**：方法名应该清晰表达查询意图，遵循命名规范。

3. **复杂查询使用@Query注解**：对于复杂查询，使用`@Query`注解比基于方法名的查询更清晰、更灵活。

4. **使用分页查询处理大量数据**：处理大量数据时，使用分页查询避免一次加载过多数据。

5. **使用投影查询优化性能**：只查询需要的字段，减少数据传输量。

6. **使用QueryDSL处理动态查询**：对于条件复杂且动态变化的查询，使用QueryDSL比Specification更简洁、更类型安全。

7. **使用命名查询集中管理SQL**：对于频繁使用的查询，使用命名查询集中管理，便于维护。

## 小结

本教程介绍了Spring Data JPA Repository接口设计和高级查询技术，包括：

- Repository接口基础和创建方法
- 基于方法名的查询
- 使用@Query注解自定义查询
- 分页和排序
- Specification动态查询
- 命名查询
- 投影查询
- QueryDSL类型安全查询

通过本教程，你应该已经掌握了如何设计Repository接口和如何使用各种高级查询技术。在下一部分教程中，我们将深入探讨Schema设计与管理以及PostgreSQL高级特性应用。

> **专业名词总结**：
>
> 1. **Repository接口**：数据访问层的接口，提供基本的数据库操作功能
> 2. **CrudRepository**：提供基本增删改查操作的接口
> 3. **PagingAndSortingRepository**：在CrudRepository基础上增加分页和排序功能
> 4. **JpaRepository**：Spring Data JPA提供的最全功能接口
> 5. **基于方法名的查询**：通过方法名自动生成查询语句的技术
> 6. **@Query注解**：自定义查询语句的注解
> 7. **JPQL**：JPA查询语言，类似SQL但操作的是对象而非表
> 8. **分页(Pagination)**：将查询结果分成多页返回的技术
> 9. **排序(Sorting)**：按照指定字段对结果进行排序
> 10. **Specification查询**：动态构建查询条件的技术
> 11. **命名查询**：在实体类上预定义的查询
> 12. **投影查询**：只返回部分字段的查询
> 13. **QueryDSL**：提供类型安全的查询API
> 14. **类型安全**：在编译时就能发现错误，而不是在运行时
