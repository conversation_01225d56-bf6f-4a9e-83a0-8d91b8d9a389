# V4.2方案：宏观语义地图构建流程-V2

## 1. 文档信息

- **文档版本**: V2.0
- **创建日期**: 2025-07-31
- **文档性质**: **核心架构文档** - 定义V2系统的宏观语义地图构建流程
- **界面交互**: 界面交互设计已在《03-九宫格交互界面设计-V2.md》中单独定义

**【注意：未来架构愿景】**
**本章节描述的“宏观语义地图构建”是一个设想中的、用于大规模文档预分析的先进流程。在当前V4.2的实现中，系统采用的是一个更直接的异步任务模型，用户指定的任务由`ProjectManager`实例直接执行，并未包含此独立的预处理阶段。本文档内容可作为未来增强系统文档分析能力的参考。**

**注意**: 本文档专注于核心架构和流程设计，不涉及界面交互设计。界面交互部分请参考《03-九宫格交互界面设计-V2.md》文档。

## 2. 设计哲学：为分层治理提供精确靶点

在V4架构的“分层治理”模型中，我们需要将宏大的设计文档，拆解成一个个独立的、有明确上下文的“治理单元”。宏观语义地图构建流程的核心任务，就是完成这个拆解工作。它将非结构化的文本转化为结构化的“语义块”，为后续的“领域架构师AI”在继承全局约束时，提供精确的、聚焦的分析靶点。

“语义地图”就是我们架构文档的区域地图。它将所有文档内容分解为有意义的“语义块”，并为每个块标注其在整体架构中的**宏观定位**（属于哪个层级）和**核心意图**（`guideline_mapping`）。

这个宏观预处理阶段在V4架构中至关重要，因为它：
- **为局部契约生成提供输入**: 为“阶段二”的“领域架构师AI”提供了结构化的、聚焦的局部上下文（即单个语义块），这是AI执行“约束继承与具体化”推理的基础。
- **并行处理以提升效率**: 此流程可以与“阶段零”和“阶段一”并行处理，提前准备好所有局部文档的结构化数据。
- **降低后续任务的复杂度**：将庞大、非结构化的文档，转化为一系列独立的、有边界的、有分类的“语义块”，极大地降低了后续AI处理的复杂度。

## 3. 核心流程：三步构建语义地图

我们将通过一个算法和AI协同的三步流程来构建语义地图。

```mermaid
flowchart TD
    A[输入: 所有设计文档] --> B["第一步：文档分块 (算法)"]
    B --> C["第二步：语义分类 (AI)"]
    C --> D["第三步：关系预识别 (算法+AI)"]
    D --> E[输出: 语义地图]
```

### 3.1. 第一步：文档分块 (算法主导)

此步骤的目标是将非结构化的文档文本，切分为一系列离散的、有边界的、有初步类型的“语义块”。

- **核心工具**: `RuleBasedBlocker` (基于规则的文档分块器)
- **输入**: 文档路径列表
- **输出**: `List<DocumentBlock>`

#### **实现逻辑**

1.  **模式定义**：`RuleBasedBlocker` 会内置一套基于正则表达式的模式，用于识别Markdown文档中的结构化元素，如各级标题 (`#`, `##`, `###`)、代码块 (```)、列表 (`-`, `*`) 等。这些模式是可配置的，可以针对不同的文档风格进行调整。
2.  **内容遍历**：算法会逐行读取文档内容。
3.  **边界识别**：当一行内容匹配到一个高级别的标题模式（如`##`）时，算法就认为这是一个新的语义块的开始，并将之前收集的内容保存为一个块。
4.  **初步分类**：根据匹配到的标题内容，算法可以对块进行初步的、基于关键词的分类（如标题中包含“架构”、“组件”、“接口”等）。
5.  **元数据附加**：每个生成的`DocumentBlock`对象都会包含其内容、初步类型、在原始文档中的起止行号、来源文档路径以及一个确定性的高置信度分数（如0.9）。

#### **AI的辅助角色**

虽然此步骤由算法主导，但我们可以引入AI进行**验证和优化**：
- **AI验证**：在算法分块后，可以随机抽取一些块，让AI判断其“语义内聚性”，即这个块的内容是否都围绕一个核心主题。
- **AI建议**：如果AI认为一个块的内容过于发散，它可以建议将其拆分为多个更小的块。

这个“算法为主，AI验证”的混合策略，确保了分块过程既快速、确定，又不失语义上的灵活性。

### 3.2. 第二步：语义分类与意图分析 (AI主导)

此步骤是构建地图的核心，目标是为每个语义块赋予其在锥形结构中的**宏观定位 (`layer`)** 和其在《黄金准则》中的**核心意图 (`guideline_mapping`)**。

- **核心工具**: `LayerAndIntentClassifier` (层级与意图分类器)
- **输入**: `List<DocumentBlock>`
- **输出**: `List<ClassifiedBlock>`
