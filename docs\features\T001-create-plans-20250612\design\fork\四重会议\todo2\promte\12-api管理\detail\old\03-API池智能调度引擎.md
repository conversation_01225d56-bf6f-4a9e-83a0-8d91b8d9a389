# 03-API池智能调度引擎

## 📋 文档信息

**文档ID**: API-POOL-INTELLIGENT-SCHEDULING-ENGINE-V1.0  
**基于总设计**: @DRY_REF: API管理核心驱动系统架构.md#UnifiedModelPoolButler  
**核心功能**: 动态选择、负载均衡、故障转移、质量最大化利用  
**权威保障**: 功能零损失、性能零退化(≥91.4分)、稳定性优先(100%成功率)

## 🎯 智能调度架构

### API池调度策略

```yaml
# === API池智能调度策略 ===
API_Pool_Intelligent_Scheduling_Strategy:
  
  # 固定API池（角色不变）
  fixed_api_pool:
    deepseek_r1_0528:
      role: "架构专家"
      priority: "HIGH"
      allocation_weight: 0.35
      specialization: ["算法思维日志", "架构设计推理"]
      
    deepseek_v3_0324:
      role: "复杂逻辑处理专家"
      priority: "HIGH" 
      allocation_weight: 0.35
      specialization: ["思维质量审查", "魔鬼审问者质询"]
      
    deepcoder_14b:
      role: "代码生成王者"
      priority: "MEDIUM"
      allocation_weight: 0.20
      specialization: ["代码生成", "技术实现分析"]
  
  # 角色化配置驱动池（替代动态择优）
  role_based_api_pool:
    configuration_driven:
      approach: "基于角色类别的配置驱动选择"
      priority: "ROLE_BASED"  # 角色优先级
      allocation_weight: "CONFIG_DRIVEN"  # 配置驱动权重
      quality_comparison: "GeminiQualityComparator"
      version_evolution: "配置文件管理"
  
  # 临时API池（应急使用）
  temporary_api_pool:
    user_temporary_keys:
      role: "应急保障"
      priority: "EMERGENCY"
      allocation_weight: 0.10
      usage_condition: "主力和备用都不可用时"
```

## 🚀 智能调度引擎实现

### UnifiedModelPoolButlerEnhanced

```python
# === API池智能调度引擎 ===
# @DRY_REF: API管理核心驱动系统架构.md#UnifiedModelPoolButler

from api_management.account_management.unified_model_pool_butler import UnifiedModelPoolButler
from api_management.account_management.api_failover_manager import APIFailoverManager

class UnifiedModelPoolButlerEnhanced(UnifiedModelPoolButler):
    """
    统一模型池管家增强版
    
    基于质量驱动的智能调度功能：
    1. 质量最大化API选择
    2. 动态负载均衡
    3. 智能故障转移
    4. Gemini择优集成
    """
    
    def __init__(self, api_db: APIAccountDatabase, failover_manager: APIFailoverManager):
        # @DRY_REF: 继承现有组件
        super().__init__(api_db, failover_manager)
        
        # 智能调度核心组件
        self.quality_evaluator = APIQualityEvaluator(api_db)
        self.load_balancer = DynamicLoadBalancer()
        self.gemini_comparator = GeminiQualityComparator(api_db)
        self.scheduling_optimizer = SchedulingOptimizer()
        
        # 调度配置
        self.scheduling_config = {
            'quality_weight': 0.6,           # 质量权重60%
            'load_weight': 0.25,             # 负载权重25%
            'stability_weight': 0.15,        # 稳定性权重15%
            'gemini_quality_threshold': 0.05, # Gemini质量优势阈值5%
            'emergency_fallback_enabled': True
        }
        
        # 当前最优分配缓存
        self.current_optimal_allocation = {}
        self.last_optimization_time = None
        self.optimization_interval = 300  # 5分钟重新优化
    
    async def get_optimal_api_for_task(self, task_type: str, quality_requirements: Dict = None) -> str:
        """
        为任务获取最优API
        
        核心调度逻辑：质量第一、动态调整、智能故障转移
        """
        scheduling_request = {
            'task_type': task_type,
            'quality_requirements': quality_requirements or {},
            'request_timestamp': datetime.now().isoformat(),
            'scheduling_context': self._build_scheduling_context()
        }
        
        try:
            # 1. 检查是否需要重新优化调度
            if self._should_reoptimize_scheduling():
                await self._perform_scheduling_optimization()
            
            # 2. 获取任务相关的候选API
            candidate_apis = await self._get_task_relevant_candidates(task_type)
            
            # 3. 评估每个候选API的当前适用性
            api_suitability_scores = {}
            for api_key in candidate_apis:
                suitability_score = await self._evaluate_api_suitability(api_key, task_type, quality_requirements)
                if suitability_score > 0:  # 只考虑可用的API
                    api_suitability_scores[api_key] = suitability_score
            
            # 4. 应用负载均衡
            load_balanced_selection = self.load_balancer.apply_load_balancing(api_suitability_scores)
            
            # 5. 最终验证和故障转移
            optimal_api = await self._finalize_api_selection(load_balanced_selection, task_type)
            
            # 6. 记录调度决策
            await self._log_scheduling_decision(scheduling_request, optimal_api, api_suitability_scores)
            
            return optimal_api
            
        except Exception as e:
            # 异常情况下的应急故障转移
            logger.error(f"智能调度失败，启用应急故障转移: {e}")
            return await self._emergency_fallback_selection(task_type)
    
    async def _get_task_relevant_candidates(self, task_type: str) -> List[str]:
        """获取任务相关的候选API"""
        
        # 角色化任务映射（配置驱动）
        task_role_mapping = {
            '算法思维日志系统': '架构专家',
            '架构设计推理': '架构专家',
            '思维质量审查器': '逻辑处理专家',
            '魔鬼审问者质询': '逻辑处理专家',
            '代码生成': '代码生成专家',
            '技术实现分析': '代码生成专家',
            '逻辑链验证': '逻辑处理专家',
            '复杂逻辑分析': '架构专家'
        }

        # 通过角色获取相关API（配置驱动）
        task_role = task_role_mapping.get(task_type, '通用专家')
        relevant_apis = await self._get_apis_by_role(task_role)
        
        # 如果没有特定映射，使用所有可用API
        if not relevant_apis:
            relevant_apis = await self._get_all_available_apis()
        
        # 验证API可用性
        available_candidates = []
        for api_key in relevant_apis:
            if await self._verify_api_availability(api_key):
                available_candidates.append(api_key)
        
        # 如果主要候选都不可用，添加临时API
        if not available_candidates:
            temporary_apis = await self._get_available_temporary_apis()
            available_candidates.extend(temporary_apis)
        
        return available_candidates
    
    async def _evaluate_api_suitability(self, api_key: str, task_type: str, quality_requirements: Dict) -> float:
        """评估API对特定任务的适用性"""
        
        suitability_factors = {}
        
        # 1. 质量评估（权重60%）
        quality_score = await self.quality_evaluator.evaluate_api_quality(api_key, task_type)
        suitability_factors['quality'] = quality_score * self.scheduling_config['quality_weight']
        
        # 2. 负载评估（权重25%）
        load_score = self.load_balancer.evaluate_api_load(api_key)
        suitability_factors['load'] = (1.0 - load_score) * self.scheduling_config['load_weight']  # 负载越低越好
        
        # 3. 稳定性评估（权重15%）
        stability_score = await self._evaluate_api_stability(api_key)
        suitability_factors['stability'] = stability_score * self.scheduling_config['stability_weight']
        
        # 4. 角色化质量调整（替代Gemini特殊处理）
        role_adjustment = await self._apply_role_based_quality_adjustment(api_key, task_type)
        suitability_factors['role_adjustment'] = role_adjustment
        
        # 5. 质量要求匹配度
        requirement_match = self._calculate_requirement_match(api_key, quality_requirements)
        suitability_factors['requirement_match'] = requirement_match * 0.1  # 权重10%
        
        # 综合适用性评分
        total_suitability = sum(suitability_factors.values())
        
        return min(1.0, max(0.0, total_suitability))  # 限制在0-1范围内
    
    async def _apply_role_based_quality_adjustment(self, api_key: str, task_type: str) -> float:
        """应用基于角色的质量调整（替代硬编码Gemini逻辑）"""

        # 获取API的角色
        api_role = await self._get_api_role_from_config(api_key)
        task_role = await self._get_task_required_role(task_type)

        # 角色匹配度调整
        if api_role == task_role:
            return 0.1  # 角色完全匹配，给予正调整
        elif api_role == '通用专家':
            return 0.05  # 通用专家适中调整
        else:
            return 0.0  # 其他情况无调整

    async def _get_apis_by_role(self, role: str) -> List[str]:
        """通过角色获取API列表（配置驱动）"""
        # 从CategoryBasedAPISelector获取角色对应的API
        from api_management.core.category_based_api_selector import get_category_based_api_selector
        selector = get_category_based_api_selector()
        return selector.get_category_apis(role)
```

## ⚖️ 动态负载均衡器

### DynamicLoadBalancer实现

```python
class DynamicLoadBalancer:
    """
    动态负载均衡器
    
    功能：
    1. 实时监控API负载
    2. 智能分配请求
    3. 避免单点过载
    4. 保障服务质量
    """
    
    def __init__(self):
        self.load_metrics = {}
        self.load_thresholds = {
            'requests_per_minute': 50,        # 每分钟最大请求数
            'concurrent_requests': 5,         # 最大并发请求数
            'average_response_time': 2.0,     # 平均响应时间阈值（秒）
            'error_rate_threshold': 0.05      # 5%错误率阈值
        }
        self.load_history = {}
    
    def evaluate_api_load(self, api_key: str) -> float:
        """评估API当前负载水平"""
        
        if api_key not in self.load_metrics:
            return 0.0  # 无负载数据时返回0负载
        
        current_metrics = self.load_metrics[api_key]
        
        # 计算各项负载指标
        load_factors = {}
        
        # 1. 请求频率负载
        rpm = current_metrics.get('requests_per_minute', 0)
        load_factors['request_frequency'] = min(1.0, rpm / self.load_thresholds['requests_per_minute'])
        
        # 2. 并发请求负载
        concurrent = current_metrics.get('concurrent_requests', 0)
        load_factors['concurrency'] = min(1.0, concurrent / self.load_thresholds['concurrent_requests'])
        
        # 3. 响应时间负载
        avg_response_time = current_metrics.get('average_response_time', 0.5)
        load_factors['response_time'] = min(1.0, avg_response_time / self.load_thresholds['average_response_time'])
        
        # 4. 错误率负载
        error_rate = current_metrics.get('error_rate', 0.0)
        load_factors['error_rate'] = min(1.0, error_rate / self.load_thresholds['error_rate_threshold'])
        
        # 综合负载评分（取最大值，因为任一指标过高都表示高负载）
        overall_load = max(load_factors.values())
        
        return overall_load
    
    def apply_load_balancing(self, api_suitability_scores: Dict[str, float]) -> str:
        """应用负载均衡选择最优API"""
        
        if not api_suitability_scores:
            raise Exception("没有可用的API候选")
        
        # 计算负载调整后的分数
        load_adjusted_scores = {}
        
        for api_key, suitability_score in api_suitability_scores.items():
            current_load = self.evaluate_api_load(api_key)
            
            # 负载调整：负载越高，分数越低
            load_penalty = current_load * 0.3  # 最大30%的负载惩罚
            adjusted_score = suitability_score * (1.0 - load_penalty)
            
            load_adjusted_scores[api_key] = adjusted_score
        
        # 选择调整后分数最高的API
        best_api = max(load_adjusted_scores.items(), key=lambda x: x[1])
        
        # 更新负载统计
        self._update_load_allocation(best_api[0])
        
        return best_api[0]
    
    def update_api_metrics(self, api_key: str, request_metrics: Dict):
        """更新API负载指标"""
        
        if api_key not in self.load_metrics:
            self.load_metrics[api_key] = {
                'requests_per_minute': 0,
                'concurrent_requests': 0,
                'average_response_time': 0.0,
                'error_rate': 0.0,
                'total_requests': 0,
                'last_update': datetime.now()
            }
        
        metrics = self.load_metrics[api_key]
        
        # 更新请求计数
        metrics['total_requests'] += 1
        
        # 更新响应时间（移动平均）
        response_time = request_metrics.get('response_time', 0.0)
        if metrics['average_response_time'] == 0.0:
            metrics['average_response_time'] = response_time
        else:
            # 指数移动平均
            alpha = 0.1
            metrics['average_response_time'] = (alpha * response_time + 
                                              (1 - alpha) * metrics['average_response_time'])
        
        # 更新错误率
        if request_metrics.get('success', True):
            # 成功请求，降低错误率
            metrics['error_rate'] *= 0.95
        else:
            # 失败请求，提高错误率
            metrics['error_rate'] = min(1.0, metrics['error_rate'] + 0.05)
        
        # 更新时间戳
        metrics['last_update'] = datetime.now()
        
        # 计算每分钟请求数（基于最近的请求历史）
        self._update_requests_per_minute(api_key)
    
    def _update_requests_per_minute(self, api_key: str):
        """更新每分钟请求数统计"""
        
        if api_key not in self.load_history:
            self.load_history[api_key] = []
        
        now = datetime.now()
        self.load_history[api_key].append(now)
        
        # 保留最近1分钟的请求记录
        one_minute_ago = now - timedelta(minutes=1)
        self.load_history[api_key] = [
            timestamp for timestamp in self.load_history[api_key] 
            if timestamp > one_minute_ago
        ]
        
        # 更新每分钟请求数
        self.load_metrics[api_key]['requests_per_minute'] = len(self.load_history[api_key])
```

## 🔄 智能故障转移

### EnhancedFailoverManager

```python
# === 增强故障转移管理器 ===
# @DRY_REF: API管理核心驱动系统架构.md#APIFailoverManager

class EnhancedFailoverManager(APIFailoverManager):
    """
    增强故障转移管理器
    
    基于质量驱动的智能故障转移：
    1. 质量感知故障转移
    2. 多层级故障保护
    3. 自动恢复检测
    4. 故障学习优化
    """
    
    def __init__(self, api_db: APIAccountDatabase):
        # @DRY_REF: 继承现有故障转移管理器
        super().__init__(api_db)
        
        # 增强故障转移配置
        self.enhanced_config = {
            'quality_aware_failover': True,
            'automatic_recovery_detection': True,
            'failover_learning_enabled': True,
            'max_failover_attempts': 3,
            'recovery_check_interval': 300  # 5分钟检查一次恢复
        }
        
        # 故障历史和学习
        self.failure_history = {}
        self.recovery_patterns = {}
        self.failover_performance = {}
    
    async def execute_intelligent_failover(self, failed_api: str, task_type: str, failure_reason: str) -> str:
        """
        执行智能故障转移
        
        @DRY_REF: API管理核心驱动系统架构.md#execute_api_failover
        """
        failover_request = {
            'failed_api': failed_api,
            'task_type': task_type,
            'failure_reason': failure_reason,
            'failover_timestamp': datetime.now().isoformat(),
            'failover_attempts': 0
        }
        
        try:
            # 1. 记录故障信息
            await self._record_failure_event(failed_api, failure_reason)
            
            # 2. 分析故障模式
            failure_pattern = self._analyze_failure_pattern(failed_api, failure_reason)
            
            # 3. 基于质量选择故障转移目标
            failover_candidates = await self._get_quality_aware_failover_candidates(failed_api, task_type)
            
            # 4. 执行分层故障转移
            for attempt in range(self.enhanced_config['max_failover_attempts']):
                failover_request['failover_attempts'] = attempt + 1
                
                if not failover_candidates:
                    break
                
                # 选择最佳故障转移目标
                failover_target = failover_candidates.pop(0)
                
                # 验证故障转移目标可用性
                if await self._verify_failover_target(failover_target, task_type):
                    # 执行故障转移
                    await self._execute_failover_to_target(failed_api, failover_target)
                    
                    # 记录成功的故障转移
                    await self._record_successful_failover(failover_request, failover_target)
                    
                    return failover_target
            
            # 所有故障转移尝试都失败
            raise Exception(f"所有故障转移尝试都失败，原始故障API: {failed_api}")
            
        except Exception as e:
            # 记录故障转移失败
            await self._record_failover_failure(failover_request, str(e))
            raise
    
    async def _get_quality_aware_failover_candidates(self, failed_api: str, task_type: str) -> List[str]:
        """获取基于质量的故障转移候选"""
        
        # 获取所有可用API
        all_available_apis = await self._get_all_available_apis()
        
        # 排除已故障的API
        available_candidates = [api for api in all_available_apis if api != failed_api]
        
        # 基于质量和任务适配性排序
        candidate_scores = {}
        
        for api_key in available_candidates:
            # 计算候选API的综合分数
            quality_score = await self._get_api_quality_score(api_key, task_type)
            task_compatibility = self._get_task_compatibility_score(api_key, task_type)
            historical_reliability = self._get_historical_reliability(api_key)
            
            # 综合评分
            total_score = (quality_score * 0.5 + 
                          task_compatibility * 0.3 + 
                          historical_reliability * 0.2)
            
            candidate_scores[api_key] = total_score
        
        # 按分数排序，返回排序后的候选列表
        sorted_candidates = sorted(candidate_scores.items(), key=lambda x: x[1], reverse=True)
        
        return [api_key for api_key, score in sorted_candidates]
    
    async def monitor_api_recovery(self):
        """监控API恢复状态"""
        
        if not self.enhanced_config['automatic_recovery_detection']:
            return
        
        # 获取当前故障的API列表
        failed_apis = await self._get_currently_failed_apis()
        
        recovery_results = {}
        
        for api_key in failed_apis:
            # 检查API是否已恢复
            recovery_status = await self._check_api_recovery(api_key)
            
            if recovery_status['recovered']:
                # API已恢复，更新状态
                await self._mark_api_recovered(api_key)
                
                # 记录恢复模式
                await self._record_recovery_pattern(api_key, recovery_status)
                
                recovery_results[api_key] = 'RECOVERED'
            else:
                recovery_results[api_key] = 'STILL_FAILED'
        
        return recovery_results
    
    async def _check_api_recovery(self, api_key: str) -> Dict:
        """检查API恢复状态"""
        
        recovery_check = {
            'api_key': api_key,
            'check_timestamp': datetime.now().isoformat(),
            'recovered': False,
            'recovery_quality': 0.0,
            'recovery_tests': {}
        }
        
        try:
            # 1. 基础连通性测试
            connectivity_test = await self._test_api_connectivity(api_key)
            recovery_check['recovery_tests']['connectivity'] = connectivity_test
            
            if not connectivity_test['success']:
                return recovery_check
            
            # 2. 功能性测试
            functionality_test = await self._test_api_functionality(api_key)
            recovery_check['recovery_tests']['functionality'] = functionality_test
            
            if not functionality_test['success']:
                return recovery_check
            
            # 3. 质量测试
            quality_test = await self._test_api_quality(api_key)
            recovery_check['recovery_tests']['quality'] = quality_test
            
            # 判断是否完全恢复
            if (connectivity_test['success'] and 
                functionality_test['success'] and 
                quality_test['quality_score'] >= 0.8):
                
                recovery_check['recovered'] = True
                recovery_check['recovery_quality'] = quality_test['quality_score']
            
            return recovery_check
            
        except Exception as e:
            recovery_check['error'] = str(e)
            return recovery_check
```

## 📊 调度性能监控

### SchedulingPerformanceMonitor

```python
class SchedulingPerformanceMonitor:
    """
    调度性能监控器
    
    监控指标：
    1. 调度决策准确性
    2. 负载均衡效果
    3. 故障转移成功率
    4. 整体系统性能
    """
    
    def __init__(self):
        self.performance_metrics = {}
        self.monitoring_config = {
            'metrics_retention_days': 30,
            'performance_alert_threshold': 0.85,
            'load_balance_target': 0.8,
            'failover_success_target': 0.95
        }
    
    def monitor_scheduling_performance(self) -> Dict:
        """监控调度性能"""
        
        performance_report = {
            'monitoring_timestamp': datetime.now().isoformat(),
            'overall_performance': {},
            'detailed_metrics': {},
            'performance_alerts': [],
            'optimization_recommendations': []
        }
        
        # 1. 调度决策准确性
        decision_accuracy = self._calculate_decision_accuracy()
        performance_report['detailed_metrics']['decision_accuracy'] = decision_accuracy
        
        # 2. 负载均衡效果
        load_balance_effectiveness = self._calculate_load_balance_effectiveness()
        performance_report['detailed_metrics']['load_balance'] = load_balance_effectiveness
        
        # 3. 故障转移成功率
        failover_success_rate = self._calculate_failover_success_rate()
        performance_report['detailed_metrics']['failover_success'] = failover_success_rate
        
        # 4. API利用率
        api_utilization = self._calculate_api_utilization()
        performance_report['detailed_metrics']['api_utilization'] = api_utilization
        
        # 5. 综合性能评分
        overall_score = self._calculate_overall_performance_score(performance_report['detailed_metrics'])
        performance_report['overall_performance']['score'] = overall_score
        performance_report['overall_performance']['grade'] = self._get_performance_grade(overall_score)
        
        # 6. 生成性能告警
        alerts = self._generate_performance_alerts(performance_report['detailed_metrics'])
        performance_report['performance_alerts'] = alerts
        
        # 7. 生成优化建议
        recommendations = self._generate_optimization_recommendations(performance_report['detailed_metrics'])
        performance_report['optimization_recommendations'] = recommendations
        
        return performance_report
    
    def _calculate_decision_accuracy(self) -> Dict:
        """计算调度决策准确性"""
        
        # 基于历史调度决策和实际性能结果计算准确性
        recent_decisions = self._get_recent_scheduling_decisions()
        
        if not recent_decisions:
            return {'accuracy_rate': 0.0, 'sample_size': 0}
        
        correct_decisions = 0
        total_decisions = len(recent_decisions)
        
        for decision in recent_decisions:
            # 评估决策是否正确（基于后续性能表现）
            if self._evaluate_decision_correctness(decision):
                correct_decisions += 1
        
        accuracy_rate = correct_decisions / total_decisions if total_decisions > 0 else 0.0
        
        return {
            'accuracy_rate': accuracy_rate,
            'correct_decisions': correct_decisions,
            'total_decisions': total_decisions,
            'sample_period': '24_hours'
        }
```

## 📋 实施要求

### 12步系统集成接口

```python
# === API管理器内部的智能调度引擎组件 ===
# 注意：此组件是API管理器的内部实现，不直接对外暴露

class IntelligentSchedulingEngine:
    """
    API管理器内部的智能调度引擎组件

    注意：此组件是API管理器的内部实现，不直接对外暴露。
    外部系统通过API管理器的统一接口访问，API管理器内部复用此调度引擎。

    @DRY_REF: 不重新造轮子，API管理器内部复用现有调度系统
    """

    def __init__(self, api_db, failover_manager):
        # API管理器内部组件初始化
        self.api_db = api_db
        self.failover_manager = failover_manager

    async def execute_intelligent_scheduling(self, task_context: Dict) -> Dict:
        """
        执行智能调度（API管理器内部调用）

        此方法由API管理器内部调用，不直接暴露给外部
        """
        # 内部调度逻辑
        scheduling_result = {
            "selected_api": self._select_optimal_api(task_context),
            "load_balancing_applied": True,
            "failover_protection": True,
            "scheduling_metadata": self._generate_scheduling_metadata(task_context)
        }

        return scheduling_result

async def _execute_intelligent_scheduling(self, api_config: Dict) -> Dict:
    """执行智能调度 - 本文档的核心职责"""
    # 应用负载均衡
    balanced_config = await self._apply_load_balancing(api_config)

    # 应用故障转移
    failover_config = await self._apply_intelligent_failover(balanced_config)

    return failover_config
```

### 核心调度原则

1. **质量最大化** - 始终选择质量最佳的可用API
2. **动态负载均衡** - 避免单点过载，保障服务质量
3. **智能故障转移** - 基于质量感知的多层级故障保护
4. **角色化配置驱动** - 基于角色类别的智能API选择和质量对比
5. **版本演进友好** - 通过配置管理支持API版本升级
6. **权威基准保障** - 确保功能零损失、性能零退化、稳定性优先

### 下一步文档

- **04-Web API接口设计** - RESTful API、实时状态、监控接口
- **05-人工管理交互界面** - 用户界面、配置管理、状态监控
- **06-系统集成与部署** - 12步集成、测试验证、生产部署
