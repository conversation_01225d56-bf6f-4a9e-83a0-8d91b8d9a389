#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DocumentContradictionPreprocessor V2.0 - 架构级文档矛盾预处理器
基于V4架构信息AI填充模板思维，实现架构级文档抽象和矛盾推理
"""

import asyncio
import re
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from pathlib import Path
from enum import Enum
import logging
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class ArchitecturalDomain(Enum):
    """架构域分类 - 基于V4模板的架构思维分层"""
    CORE_ARCHITECTURE = "core_architecture"      # 核心架构设计
    INTEGRATION_LAYER = "integration_layer"      # 集成层设计
    SERVICE_BOUNDARY = "service_boundary"        # 服务边界定义
    DATA_FLOW = "data_flow"                     # 数据流设计
    CONSTRAINT_SYSTEM = "constraint_system"      # 约束系统
    QUALITY_ATTRIBUTES = "quality_attributes"    # 质量属性


class ConfidenceLevel(Enum):
    """置信度分层 - 对应V4模板的三重验证机制"""
    HIGH_CONFIDENCE_95_PLUS = "95+"             # 95%+高置信度域
    MEDIUM_CONFIDENCE_85_94 = "85-94"           # 85-94%中等置信度域  
    LOW_CONFIDENCE_68_82 = "68-82"              # 68-82%挑战域


@dataclass
class ArchitecturalConcept:
    """架构概念抽象 - 架构级思维的基本单元"""
    concept_id: str
    concept_name: str
    domain: ArchitecturalDomain
    confidence_level: ConfidenceLevel
    
    # 架构语义信息
    semantic_keywords: Set[str]
    design_patterns: List[str]
    quality_attributes: List[str]
    
    # 上下文关联
    dependencies: List[str] = field(default_factory=list)
    constraints: List[str] = field(default_factory=list)
    
    # V4模板标记信息
    v4_template_tags: Dict[str, str] = field(default_factory=dict)
    
    # 矛盾检测元数据
    contradiction_indicators: List[str] = field(default_factory=list)


@dataclass
class ArchitecturalDocument:
    """架构文档抽象模型 - 基于V4模板的文档架构化表示"""
    doc_id: str
    doc_type: str
    file_path: str
    
    # 架构概念集合
    architectural_concepts: List[ArchitecturalConcept]
    
    # 架构域映射
    domain_coverage: Dict[ArchitecturalDomain, float]
    
    # 置信度分布
    confidence_distribution: Dict[ConfidenceLevel, float]
    
    # V4模板填充状态
    v4_template_completeness: float
    
    # 矛盾检测准备状态
    contradiction_detection_readiness: float
    
    # 原始内容（保留用于深度分析）
    raw_content: str
    processing_timestamp: str


class ArchitecturalSemanticAnalyzer(ABC):
    """架构语义分析器抽象基类"""
    
    @abstractmethod
    async def extract_architectural_concepts(self, content: str, doc_type: str) -> List[ArchitecturalConcept]:
        """提取架构概念"""
        pass
    
    @abstractmethod
    async def analyze_design_patterns(self, content: str) -> List[str]:
        """分析设计模式"""
        pass
    
    @abstractmethod
    async def assess_confidence_level(self, concept: ArchitecturalConcept, context: str) -> ConfidenceLevel:
        """评估置信度级别"""
        pass


class MicrokernelSemanticAnalyzer(ArchitecturalSemanticAnalyzer):
    """微内核架构语义分析器 - 专门处理微内核模式的架构文档"""
    
    def __init__(self):
        # 微内核架构的核心语义模式
        self.microkernel_patterns = {
            "plugin_management": {
                "keywords": {"plugin", "插件", "扩展", "extension", "module"},
                "design_patterns": ["Plugin Pattern", "Registry Pattern"],
                "confidence_indicators": ["生命周期", "lifecycle", "加载", "卸载"]
            },
            "service_bus": {
                "keywords": {"service bus", "服务总线", "事件", "event", "消息"},
                "design_patterns": ["Event Bus Pattern", "Mediator Pattern"],
                "confidence_indicators": ["异步", "async", "发布", "订阅"]
            },
            "kernel_core": {
                "keywords": {"kernel", "内核", "core", "核心"},
                "design_patterns": ["Microkernel Pattern", "Facade Pattern"],
                "confidence_indicators": ["启动", "初始化", "bootstrap"]
            }
        }
    
    async def extract_architectural_concepts(self, content: str, doc_type: str) -> List[ArchitecturalConcept]:
        """提取微内核架构概念"""
        concepts = []
        
        for pattern_name, pattern_info in self.microkernel_patterns.items():
            if self._matches_pattern(content, pattern_info["keywords"]):
                concept = ArchitecturalConcept(
                    concept_id=f"microkernel_{pattern_name}",
                    concept_name=pattern_name,
                    domain=self._determine_domain(pattern_name),
                    confidence_level=await self._assess_pattern_confidence(content, pattern_info),
                    semantic_keywords=pattern_info["keywords"],
                    design_patterns=pattern_info["design_patterns"],
                    quality_attributes=self._extract_quality_attributes(content, pattern_name),
                    v4_template_tags=self._generate_v4_tags(pattern_name, content)
                )
                concepts.append(concept)
        
        return concepts
    
    def _matches_pattern(self, content: str, keywords: Set[str]) -> bool:
        """检查内容是否匹配特定模式"""
        content_lower = content.lower()
        return any(keyword.lower() in content_lower for keyword in keywords)
    
    def _determine_domain(self, pattern_name: str) -> ArchitecturalDomain:
        """确定架构域"""
        domain_mapping = {
            "plugin_management": ArchitecturalDomain.CORE_ARCHITECTURE,
            "service_bus": ArchitecturalDomain.INTEGRATION_LAYER,
            "kernel_core": ArchitecturalDomain.CORE_ARCHITECTURE
        }
        return domain_mapping.get(pattern_name, ArchitecturalDomain.CORE_ARCHITECTURE)
    
    async def _assess_pattern_confidence(self, content: str, pattern_info: Dict) -> ConfidenceLevel:
        """评估模式置信度"""
        confidence_score = 0
        
        # 关键词匹配度
        keyword_matches = sum(1 for kw in pattern_info["keywords"] if kw.lower() in content.lower())
        confidence_score += (keyword_matches / len(pattern_info["keywords"])) * 40
        
        # 置信度指示器匹配度
        indicator_matches = sum(1 for ind in pattern_info["confidence_indicators"] if ind.lower() in content.lower())
        confidence_score += (indicator_matches / len(pattern_info["confidence_indicators"])) * 30
        
        # 设计模式明确性
        pattern_mentions = sum(1 for pattern in pattern_info["design_patterns"] if pattern.lower() in content.lower())
        confidence_score += (pattern_mentions / len(pattern_info["design_patterns"])) * 30
        
        if confidence_score >= 95:
            return ConfidenceLevel.HIGH_CONFIDENCE_95_PLUS
        elif confidence_score >= 85:
            return ConfidenceLevel.MEDIUM_CONFIDENCE_85_94
        else:
            return ConfidenceLevel.LOW_CONFIDENCE_68_82
    
    def _extract_quality_attributes(self, content: str, pattern_name: str) -> List[str]:
        """提取质量属性"""
        quality_patterns = {
            "performance": ["性能", "performance", "延迟", "latency", "吞吐", "throughput"],
            "scalability": ["扩展", "scalability", "伸缩", "scale"],
            "maintainability": ["维护", "maintainability", "可维护", "modular"],
            "reliability": ["可靠", "reliability", "稳定", "stable"]
        }
        
        found_attributes = []
        content_lower = content.lower()
        
        for attr, keywords in quality_patterns.items():
            if any(kw in content_lower for kw in keywords):
                found_attributes.append(attr)
        
        return found_attributes
    
    def _generate_v4_tags(self, pattern_name: str, content: str) -> Dict[str, str]:
        """生成V4模板标记"""
        tags = {}
        
        # 基于内容生成置信度标记
        if "明确" in content or "clear" in content.lower():
            tags["confidence_tag"] = "@HIGH_CONF_95+:明确架构定义"
        elif "可能" in content or "might" in content.lower():
            tags["confidence_tag"] = "@MEDIUM_CONF_85-94:推理架构设计"
        else:
            tags["confidence_tag"] = "@LOW_CONF_68-82:需要专家评审"
        
        # 实施方向标记
        if "新增" in content or "new" in content.lower():
            tags["implementation_tag"] = "@NEW_CREATE"
        elif "修改" in content or "modify" in content.lower():
            tags["implementation_tag"] = "@MODIFY"
        elif "重构" in content or "refactor" in content.lower():
            tags["implementation_tag"] = "@REFACTOR"
        
        return tags
    
    async def analyze_design_patterns(self, content: str) -> List[str]:
        """分析设计模式"""
        patterns = []
        for pattern_info in self.microkernel_patterns.values():
            patterns.extend(pattern_info["design_patterns"])
        return list(set(patterns))
    
    async def assess_confidence_level(self, concept: ArchitecturalConcept, context: str) -> ConfidenceLevel:
        """评估置信度级别"""
        return concept.confidence_level


class ArchitecturalContradictionDetector:
    """架构级矛盾检测器 - 基于架构概念的矛盾推理"""
    
    def __init__(self):
        # 架构矛盾规则库
        self.contradiction_rules = {
            "pattern_conflict": {
                "description": "设计模式冲突",
                "severity": "high",
                "detection_logic": self._detect_pattern_conflicts
            },
            "domain_boundary_violation": {
                "description": "架构域边界违反",
                "severity": "medium", 
                "detection_logic": self._detect_domain_violations
            },
            "confidence_divergence": {
                "description": "置信度发散",
                "severity": "medium",
                "detection_logic": self._detect_confidence_divergence
            }
        }
    
    async def detect_contradictions(self, doc1: ArchitecturalDocument, doc2: ArchitecturalDocument) -> List[Dict[str, Any]]:
        """检测两个架构文档间的矛盾"""
        contradictions = []
        
        for rule_name, rule_info in self.contradiction_rules.items():
            detected = await rule_info["detection_logic"](doc1, doc2)
            if detected:
                contradictions.extend(detected)
        
        return contradictions
    
    async def _detect_pattern_conflicts(self, doc1: ArchitecturalDocument, doc2: ArchitecturalDocument) -> List[Dict[str, Any]]:
        """检测设计模式冲突"""
        conflicts = []
        
        # 获取两个文档的设计模式
        patterns1 = set()
        patterns2 = set()
        
        for concept in doc1.architectural_concepts:
            patterns1.update(concept.design_patterns)
        
        for concept in doc2.architectural_concepts:
            patterns2.update(concept.design_patterns)
        
        # 检测互斥模式
        conflicting_pairs = [
            ("Singleton Pattern", "Factory Pattern"),
            ("Synchronous Pattern", "Asynchronous Pattern")
        ]
        
        for pattern1, pattern2 in conflicting_pairs:
            if pattern1 in patterns1 and pattern2 in patterns2:
                conflicts.append({
                    "type": "pattern_conflict",
                    "severity": "high",
                    "description": f"设计模式冲突: {pattern1} vs {pattern2}",
                    "doc1_id": doc1.doc_id,
                    "doc2_id": doc2.doc_id,
                    "confidence": 0.9
                })
        
        return conflicts
    
    async def _detect_domain_violations(self, doc1: ArchitecturalDocument, doc2: ArchitecturalDocument) -> List[Dict[str, Any]]:
        """检测架构域边界违反"""
        violations = []
        
        # 检查是否有跨域的不当依赖
        for concept1 in doc1.architectural_concepts:
            for concept2 in doc2.architectural_concepts:
                if self._is_domain_violation(concept1, concept2):
                    violations.append({
                        "type": "domain_boundary_violation",
                        "severity": "medium",
                        "description": f"架构域边界违反: {concept1.domain} -> {concept2.domain}",
                        "doc1_id": doc1.doc_id,
                        "doc2_id": doc2.doc_id,
                        "confidence": 0.8
                    })
        
        return violations
    
    def _is_domain_violation(self, concept1: ArchitecturalConcept, concept2: ArchitecturalConcept) -> bool:
        """判断是否存在域边界违反"""
        # 简化的违反规则：核心架构不应直接依赖集成层
        if (concept1.domain == ArchitecturalDomain.CORE_ARCHITECTURE and 
            concept2.domain == ArchitecturalDomain.INTEGRATION_LAYER and
            concept2.concept_name in concept1.dependencies):
            return True
        return False
    
    async def _detect_confidence_divergence(self, doc1: ArchitecturalDocument, doc2: ArchitecturalDocument) -> List[Dict[str, Any]]:
        """检测置信度发散"""
        divergences = []
        
        # 计算置信度差异
        for level in ConfidenceLevel:
            diff = abs(doc1.confidence_distribution.get(level, 0) - doc2.confidence_distribution.get(level, 0))
            if diff > 0.25:  # V4模板目标：收敛差距≤25
                divergences.append({
                    "type": "confidence_divergence",
                    "severity": "medium",
                    "description": f"置信度发散: {level.value} 差距 {diff:.2f}",
                    "doc1_id": doc1.doc_id,
                    "doc2_id": doc2.doc_id,
                    "confidence": 0.85
                })
        
        return divergences


class DocumentContradictionPreprocessorV2:
    """
    架构级文档矛盾预处理器 V2.0
    
    核心改进：
    1. 基于V4架构模板的架构级抽象思维
    2. 语义分析器模式，支持不同架构风格
    3. 三重验证机制集成
    4. 架构概念级矛盾检测
    """
    
    def __init__(self):
        # 语义分析器注册表
        self.semantic_analyzers = {
            "microkernel": MicrokernelSemanticAnalyzer(),
            # 可扩展其他架构风格分析器
        }
        
        # 架构矛盾检测器
        self.contradiction_detector = ArchitecturalContradictionDetector()
        
        # V4模板集成配置
        self.v4_template_config = {
            "enable_triple_verification": True,
            "confidence_convergence_threshold": 0.25,
            "contradiction_reduction_target": 0.75
        }
    
    async def preprocess_documents(self, doc_paths: List[str]) -> List[ArchitecturalDocument]:
        """
        架构级文档预处理
        
        Args:
            doc_paths: 文档路径列表
            
        Returns:
            List[ArchitecturalDocument]: 架构化文档列表
        """
        architectural_docs = []
        
        for doc_path in doc_paths:
            try:
                arch_doc = await self._preprocess_single_document(doc_path)
                architectural_docs.append(arch_doc)
                logger.info(f"成功架构化预处理文档: {doc_path}")
            except Exception as e:
                logger.error(f"架构化预处理文档失败 {doc_path}: {e}")
                continue
        
        # 执行三重验证
        if self.v4_template_config["enable_triple_verification"]:
            architectural_docs = await self._apply_triple_verification(architectural_docs)
        
        return architectural_docs
    
    async def _preprocess_single_document(self, doc_path: str) -> ArchitecturalDocument:
        """架构级单文档预处理"""
        
        # 1. 读取文档内容
        raw_content = self._read_document(doc_path)
        
        # 2. 识别架构风格
        architecture_style = self._identify_architecture_style(raw_content)
        
        # 3. 选择对应的语义分析器
        analyzer = self.semantic_analyzers.get(architecture_style, self.semantic_analyzers["microkernel"])
        
        # 4. 提取架构概念
        architectural_concepts = await analyzer.extract_architectural_concepts(raw_content, self._get_doc_type(doc_path))
        
        # 5. 计算架构域覆盖度
        domain_coverage = self._calculate_domain_coverage(architectural_concepts)
        
        # 6. 计算置信度分布
        confidence_distribution = self._calculate_confidence_distribution(architectural_concepts)
        
        # 7. 评估V4模板完整性
        v4_completeness = self._assess_v4_template_completeness(architectural_concepts)
        
        # 8. 评估矛盾检测准备状态
        contradiction_readiness = self._assess_contradiction_detection_readiness(architectural_concepts)
        
        return ArchitecturalDocument(
            doc_id=self._generate_doc_id(doc_path),
            doc_type=self._get_doc_type(doc_path),
            file_path=doc_path,
            architectural_concepts=architectural_concepts,
            domain_coverage=domain_coverage,
            confidence_distribution=confidence_distribution,
            v4_template_completeness=v4_completeness,
            contradiction_detection_readiness=contradiction_readiness,
            raw_content=raw_content,
            processing_timestamp=self._get_current_timestamp()
        )
    
    def _identify_architecture_style(self, content: str) -> str:
        """识别架构风格"""
        content_lower = content.lower()
        
        # 微内核架构特征
        if any(keyword in content_lower for keyword in ["plugin", "插件", "microkernel", "微内核"]):
            return "microkernel"
        
        # 默认使用微内核分析器
        return "microkernel"
    
    def _calculate_domain_coverage(self, concepts: List[ArchitecturalConcept]) -> Dict[ArchitecturalDomain, float]:
        """计算架构域覆盖度"""
        domain_counts = {}
        total_concepts = len(concepts)
        
        if total_concepts == 0:
            return {domain: 0.0 for domain in ArchitecturalDomain}
        
        for concept in concepts:
            domain_counts[concept.domain] = domain_counts.get(concept.domain, 0) + 1
        
        return {domain: count / total_concepts for domain, count in domain_counts.items()}
    
    def _calculate_confidence_distribution(self, concepts: List[ArchitecturalConcept]) -> Dict[ConfidenceLevel, float]:
        """计算置信度分布"""
        confidence_counts = {}
        total_concepts = len(concepts)
        
        if total_concepts == 0:
            return {level: 0.0 for level in ConfidenceLevel}
        
        for concept in concepts:
            confidence_counts[concept.confidence_level] = confidence_counts.get(concept.confidence_level, 0) + 1
        
        return {level: count / total_concepts for level, count in confidence_counts.items()}
    
    def _assess_v4_template_completeness(self, concepts: List[ArchitecturalConcept]) -> float:
        """评估V4模板完整性"""
        if not concepts:
            return 0.0
        
        # 检查V4标记完整性
        tagged_concepts = sum(1 for concept in concepts if concept.v4_template_tags)
        return tagged_concepts / len(concepts)
    
    def _assess_contradiction_detection_readiness(self, concepts: List[ArchitecturalConcept]) -> float:
        """评估矛盾检测准备状态"""
        if not concepts:
            return 0.0
        
        # 检查矛盾检测元数据完整性
        ready_concepts = sum(1 for concept in concepts if concept.contradiction_indicators)
        return ready_concepts / len(concepts)
    
    async def _apply_triple_verification(self, docs: List[ArchitecturalDocument]) -> List[ArchitecturalDocument]:
        """应用三重验证机制"""
        
        # 1. 置信度收敛验证
        await self._verify_confidence_convergence(docs)
        
        # 2. 矛盾检测验证
        await self._verify_contradiction_detection(docs)
        
        # 3. 架构一致性验证
        await self._verify_architecture_consistency(docs)
        
        return docs
    
    async def _verify_confidence_convergence(self, docs: List[ArchitecturalDocument]):
        """验证置信度收敛"""
        if len(docs) < 2:
            return
        
        # 计算置信度方差
        for level in ConfidenceLevel:
            values = [doc.confidence_distribution.get(level, 0) for doc in docs]
            variance = sum((x - sum(values)/len(values))**2 for x in values) / len(values)
            
            if variance > self.v4_template_config["confidence_convergence_threshold"]:
                logger.warning(f"置信度发散检测: {level.value} 方差 {variance:.3f}")
    
    async def _verify_contradiction_detection(self, docs: List[ArchitecturalDocument]):
        """验证矛盾检测"""
        contradictions = []
        
        for i, doc1 in enumerate(docs):
            for doc2 in docs[i+1:]:
                doc_contradictions = await self.contradiction_detector.detect_contradictions(doc1, doc2)
                contradictions.extend(doc_contradictions)
        
        if contradictions:
            logger.info(f"检测到 {len(contradictions)} 个架构矛盾")
    
    async def _verify_architecture_consistency(self, docs: List[ArchitecturalDocument]):
        """验证架构一致性"""
        # 检查架构域覆盖一致性
        domain_coverages = [doc.domain_coverage for doc in docs]
        
        for domain in ArchitecturalDomain:
            values = [coverage.get(domain, 0) for coverage in domain_coverages]
            if max(values) - min(values) > 0.3:  # 30%差异阈值
                logger.warning(f"架构域覆盖不一致: {domain.value}")
    
    def _read_document(self, doc_path: str) -> str:
        """读取文档内容"""
        try:
            with open(doc_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取文档失败 {doc_path}: {e}")
            raise
    
    def _get_doc_type(self, doc_path: str) -> str:
        """获取文档类型"""
        file_name = Path(doc_path).name.lower()
        
        if "00" in file_name and ("overview" in file_name or "总览" in file_name):
            return "00_overview"
        elif "01" in file_name and ("architecture" in file_name or "架构" in file_name):
            return "01_architecture"
        # ... 其他类型判断
        
        return "unknown"
    
    def _generate_doc_id(self, doc_path: str) -> str:
        """生成文档ID"""
        return f"arch_doc_{hash(doc_path) % 10000:04d}"
    
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()


# 使用示例
async def main():
    """架构级预处理器使用示例"""
    preprocessor = DocumentContradictionPreprocessorV2()
    
    doc_paths = [
        "docs/00-护栏约束上下文总览.md",
        "docs/01-系统架构设计文档.md"
    ]
    
    architectural_docs = await preprocessor.preprocess_documents(doc_paths)
    
    for doc in architectural_docs:
        print(f"架构文档ID: {doc.doc_id}")
        print(f"架构概念数量: {len(doc.architectural_concepts)}")
        print(f"V4模板完整性: {doc.v4_template_completeness:.2f}")
        print(f"矛盾检测准备度: {doc.contradiction_detection_readiness:.2f}")
        print("---")


if __name__ == "__main__":
    asyncio.run(main())