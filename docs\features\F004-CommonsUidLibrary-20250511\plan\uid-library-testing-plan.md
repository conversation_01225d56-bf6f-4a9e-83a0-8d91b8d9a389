---
title: xkongcloud-commons-uid库测试方案
document_id: F004-PLAN-003
document_type: 测试方案
category: 公共库开发
scope: XKC-CORE
keywords: [UID生成器, 测试方案, 单元测试, 集成测试, 性能测试]
created_date: 2025-05-18
updated_date: 2025-05-18
status: 草稿
version: 1.0
authors: [AI助手]
---

# xkongcloud-commons-uid库测试方案

## 1. 概述

本文档提供了对xkongcloud-commons-uid库的全面测试方案，包括单元测试、集成测试和性能测试。测试方案旨在确保库的功能正确性、稳定性和性能满足需求。

## 2. 测试环境

| 环境类型 | 配置说明 |
|---------|---------|
| 开发测试环境 | JDK 21, PostgreSQL 17.4, Spring Boot 3.2 |
| 集成测试环境 | 使用TestContainers自动管理PostgreSQL容器 |
| 性能测试环境 | 8核16GB内存服务器，PostgreSQL独立部署 |

## 3. 单元测试

### 3.1 WorkerNodeType枚举测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| testEnumValues | 测试枚举值的正确性 | CONTAINER值为1，ACTUAL值为2 |
| testOfMethodWithValidValues | 测试of方法处理有效值 | 正确返回对应枚举值 |
| testOfMethodWithInvalidValues | 测试of方法处理无效值 | 抛出IllegalArgumentException异常 |

### 3.2 MachineFingerprints类测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| testGetFingerprints | 测试获取特征码的基本功能 | 返回包含基本信息的特征码Map |
| testFingerprintsCaching | 测试特征码缓存功能 | 多次调用返回相同对象引用 |
| testFingerprintHashConsistency | 测试特征码哈希的一致性 | 多次获取的哈希值相同 |
| testMaskedFingerprints | 测试敏感信息掩码功能 | 敏感信息被正确掩码 |

### 3.3 KeyManagementService类测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| testIsEncryptionEnabled | 测试加密启用状态检查 | 返回正确的加密启用状态 |
| testGetSchemaName | 测试获取Schema名称 | 返回正确的Schema名称 |
| testGetEncryptionKey_EncryptionDisabled | 测试禁用加密时获取密钥 | 抛出IllegalStateException异常 |
| testGetEncryptionKey_KeyExists | 测试密钥已存在时获取密钥 | 返回已存在的密钥 |
| testGetEncryptionKey_KeyNotExists | 测试密钥不存在时获取密钥 | 创建并返回新密钥 |
| testEncryptAndDecrypt | 测试加密和解密功能 | 解密后的文本与原文相同 |

### 3.4 PersistentInstanceManagerBuilder类测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| testBuilderWithRequiredParams | 测试使用必要参数构建 | 成功创建PersistentInstanceManager实例 |
| testBuilderWithAllParams | 测试使用所有参数构建 | 成功创建PersistentInstanceManager实例 |
| testBuilderWithMissingRequiredParams | 测试缺少必要参数 | 抛出IllegalStateException异常 |
| testBuilderWithInvalidParams | 测试使用无效参数 | 抛出IllegalArgumentException异常 |

### 3.5 PersistentInstanceManager类测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| testWithInstanceIdOverride_Exists | 测试使用已存在的实例ID覆盖值 | 使用指定的实例ID |
| testWithInstanceIdOverride_NotExists | 测试使用不存在的实例ID覆盖值 | 注册新实例并使用指定的实例ID |
| testLoadInstanceIdFromFile | 测试从文件加载实例ID | 成功加载并使用文件中的实例ID |
| testRecoverInstanceId_HighConfidence | 测试高置信度特征码恢复 | 成功恢复实例ID |
| testRecoverInstanceId_LowConfidence | 测试低置信度特征码恢复 | 根据恢复策略处理 |
| testRegisterNewInstance | 测试注册新实例 | 成功注册并返回新实例ID |
| testSaveInstanceIdToFile | 测试保存实例ID到文件 | 成功保存实例ID到文件 |

### 3.6 ValidationResultCache类测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| testSchemaValidation | 测试Schema验证状态缓存 | 正确记录和查询Schema验证状态 |
| testTableValidation | 测试表验证状态缓存 | 正确记录和查询表验证状态 |
| testParamValidation | 测试参数验证状态缓存 | 正确记录和查询参数验证状态 |
| testClearAll | 测试清除所有缓存 | 所有缓存被清除 |
| testCacheExpiry | 测试缓存过期 | 过期时间后缓存被清除 |

### 3.7 UidValidationUtils类测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| testValidateDatabaseConnection | 测试数据库连接验证 | 连接正常时不抛异常，异常时抛出RuntimeException |
| testValidateSchemaExists | 测试Schema存在验证 | Schema存在时不抛异常，不存在时抛出RuntimeException |
| testValidateTableExists | 测试表存在验证 | 表存在时不抛异常，不存在时抛出RuntimeException |
| testValidateTableStructure | 测试表结构验证 | 结构正确时不抛异常，不正确时抛出RuntimeException |

### 3.8 PersistentInstanceWorkerIdAssigner类测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| testGetWorkerId_AlreadyAssigned | 测试获取已分配的工作机器ID | 返回已分配的工作机器ID |
| testGetWorkerId_AssignUnused | 测试分配未使用的工作机器ID | 成功分配并返回新工作机器ID |
| testGetWorkerId_NoAvailable | 测试无可用工作机器ID | 抛出RuntimeException异常 |
| testRenewLease | 测试续约工作机器ID | 成功续约并更新租约时间 |
| testReleaseWorkerId | 测试释放工作机器ID | 成功释放工作机器ID |

### 3.9 UidTableManager类测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| testInitializeTables_TablesNotExist | 测试初始化不存在的表 | 成功创建所有必要的表 |
| testInitializeTables_TablesExist | 测试初始化已存在的表 | 不执行创建操作 |
| testEnsureSchemaExists | 测试确保Schema存在 | Schema不存在时创建，存在时不操作 |
| testValidateAllTables | 测试验证所有表 | 表结构正确时返回true，不正确时返回false |
| testIsTableExists | 测试检查表是否存在 | 表存在时返回true，不存在时返回false |

## 4. 集成测试

### 4.1 数据库交互测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| testSchemaAndTableCreation | 测试Schema和表的创建 | 成功创建Schema和所有必要的表 |
| testInstanceRegistration | 测试实例注册 | 成功注册实例并返回实例ID |
| testWorkerIdAssignment | 测试工作机器ID分配 | 成功分配工作机器ID |
| testKeyManagement | 测试密钥管理 | 成功创建和获取密钥 |

### 4.2 实例恢复测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| testInstanceRecoveryFromFile | 测试从文件恢复实例ID | 成功从文件恢复实例ID |
| testInstanceRecoveryFromFingerprint | 测试从特征码恢复实例ID | 成功从特征码恢复实例ID |
| testInstanceRecoveryWithDifferentStrategies | 测试不同恢复策略 | 各策略按预期工作 |

### 4.3 租约管理测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| testLeaseRenewal | 测试租约续约 | 成功续约并更新租约时间 |
| testLeaseExpiration | 测试租约过期 | 过期后工作机器ID可被重新分配 |
| testConcurrentLeaseManagement | 测试并发租约管理 | 并发操作下租约管理正常工作 |

## 5. 性能测试

### 5.1 基准测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| benchmarkUidGeneration | 测试UID生成性能 | 单线程下每秒生成>10000个UID |
| benchmarkWorkerIdAssignment | 测试工作机器ID分配性能 | 分配时间<100ms |
| benchmarkInstanceRecovery | 测试实例恢复性能 | 恢复时间<200ms |

### 5.2 并发测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| testConcurrentUidGeneration | 测试并发UID生成 | 10个线程并发下每秒生成>50000个UID |
| testConcurrentWorkerIdAssignment | 测试并发工作机器ID分配 | 并发分配不会导致重复分配 |
| testConcurrentInstanceRegistration | 测试并发实例注册 | 并发注册不会导致数据不一致 |

### 5.3 压力测试

| 测试用例 | 测试内容 | 预期结果 |
|---------|---------|---------|
| stressTestUidGeneration | 长时间高并发UID生成 | 1小时内稳定生成UID，无性能下降 |
| stressTestDatabaseOperations | 长时间高频数据库操作 | 数据库连接池稳定，无连接泄漏 |
| stressTestLeaseManagement | 长时间租约管理 | 租约管理稳定，无资源耗尽 |

## 6. 主要测试方法

### 6.1 单元测试方法

使用JUnit 5和Mockito框架进行单元测试，主要测试各个组件的独立功能。

```java
@ExtendWith(MockitoExtension.class)
public class ComponentTest {
    @Mock
    private Dependency dependency;
    
    private Component component;
    
    @BeforeEach
    public void setUp() {
        component = new Component(dependency);
    }
    
    @Test
    public void testFunction() {
        // 准备测试数据
        when(dependency.method()).thenReturn(expectedValue);
        
        // 执行测试
        Result result = component.function();
        
        // 验证结果
        assertEquals(expectedValue, result);
        verify(dependency).method();
    }
}
```

### 6.2 集成测试方法

使用TestContainers和Spring Boot Test框架进行集成测试，测试多个组件的协同工作。

```java
@Testcontainers
@SpringBootTest
public class IntegrationTest {
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:17.4")
        .withDatabaseName("test")
        .withUsername("test")
        .withPassword("test");
    
    @Autowired
    private UidGenerator uidGenerator;
    
    @Test
    public void testUidGeneration() {
        // 执行测试
        long uid = uidGenerator.getUID();
        
        // 验证结果
        assertTrue(uid > 0);
    }
}
```

### 6.3 性能测试方法

使用JMH框架进行性能测试，测试关键操作的性能指标。

```java
@State(Scope.Benchmark)
public class PerformanceTest {
    private UidGenerator uidGenerator;
    
    @Setup
    public void setUp() {
        // 初始化测试环境
        uidGenerator = createUidGenerator();
    }
    
    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OutputTimeUnit(TimeUnit.SECONDS)
    public long benchmarkUidGeneration() {
        return uidGenerator.getUID();
    }
}
```

## 7. 简化测试方法

为了快速验证功能，可以使用以下简化的main方法测试：

### 7.1 基本功能测试

```java
public class BasicFunctionalityTest {
    public static void main(String[] args) {
        // 1. 准备测试环境
        DataSource dataSource = setupDataSource();
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        TransactionTemplate transactionTemplate = new TransactionTemplate(
            new DataSourceTransactionManager(dataSource));
        
        // 2. 初始化表结构
        UidTableManager tableManager = new UidTableManager(
            jdbcTemplate, transactionTemplate, "infra_uid");
        tableManager.initializeTables();
        
        // 3. 创建实例管理器
        PersistentInstanceManager instanceManager = new PersistentInstanceManagerBuilder()
            .withJdbcTemplate(jdbcTemplate)
            .withTransactionTemplate(transactionTemplate)
            .withApplicationName("test-app")
            .withEnvironment("test")
            .withGroup("default")
            .build();
        
        // 4. 获取实例ID
        long instanceId = instanceManager.getInstanceId();
        System.out.println("实例ID: " + instanceId);
        
        // 5. 创建工作机器ID分配器
        PersistentInstanceWorkerIdAssigner assigner = new PersistentInstanceWorkerIdAssigner(
            jdbcTemplate, transactionTemplate, instanceManager, "infra_uid");
        
        // 6. 分配工作机器ID
        long workerId = assigner.assignWorkerId();
        System.out.println("工作机器ID: " + workerId);
    }
    
    private static DataSource setupDataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("*******************************************");
        config.setUsername("postgres");
        config.setPassword("postgres");
        return new HikariDataSource(config);
    }
}
```

### 7.2 实例恢复测试

```java
public class InstanceRecoveryTest {
    public static void main(String[] args) {
        // 1. 准备测试环境
        DataSource dataSource = setupDataSource();
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        TransactionTemplate transactionTemplate = new TransactionTemplate(
            new DataSourceTransactionManager(dataSource));
        
        // 2. 第一次运行 - 创建实例
        System.out.println("===== 第一次运行 =====");
        PersistentInstanceManager instanceManager1 = createInstanceManager(
            jdbcTemplate, transactionTemplate, "./data/uid/instance-id.dat");
        
        long instanceId1 = instanceManager1.getInstanceId();
        System.out.println("第一次运行 - 实例ID: " + instanceId1);
        
        // 3. 第二次运行 - 从文件恢复
        System.out.println("\n===== 第二次运行 (从文件恢复) =====");
        PersistentInstanceManager instanceManager2 = createInstanceManager(
            jdbcTemplate, transactionTemplate, "./data/uid/instance-id.dat");
        
        long instanceId2 = instanceManager2.getInstanceId();
        System.out.println("第二次运行 - 实例ID: " + instanceId2);
        System.out.println("从文件恢复结果: " + (instanceId1 == instanceId2 ? "成功" : "失败"));
        
        // 4. 删除文件后运行 - 从特征码恢复
        try {
            Files.deleteIfExists(Paths.get("./data/uid/instance-id.dat"));
            System.out.println("\n===== 删除文件后运行 (从特征码恢复) =====");
            PersistentInstanceManager instanceManager3 = createInstanceManager(
                jdbcTemplate, transactionTemplate, "./data/uid/instance-id.dat");
            
            long instanceId3 = instanceManager3.getInstanceId();
            System.out.println("删除文件后运行 - 实例ID: " + instanceId3);
            System.out.println("从特征码恢复结果: " + (instanceId1 == instanceId3 ? "成功" : "失败"));
        } catch (Exception e) {
            System.err.println("文件操作失败: " + e.getMessage());
        }
    }
    
    private static PersistentInstanceManager createInstanceManager(
            JdbcTemplate jdbcTemplate, 
            TransactionTemplate transactionTemplate,
            String localStoragePath) {
        return new PersistentInstanceManagerBuilder()
            .withJdbcTemplate(jdbcTemplate)
            .withTransactionTemplate(transactionTemplate)
            .withApplicationName("recovery-test")
            .withEnvironment("test")
            .withGroup("default")
            .withLocalStoragePath(localStoragePath)
            .withRecoveryEnabled(true)
            .build();
    }
    
    private static DataSource setupDataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("*******************************************");
        config.setUsername("postgres");
        config.setPassword("postgres");
        return new HikariDataSource(config);
    }
}
```

### 7.3 租约管理测试

```java
public class LeaseManagementTest {
    public static void main(String[] args) throws Exception {
        // 1. 准备测试环境
        DataSource dataSource = setupDataSource();
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        TransactionTemplate transactionTemplate = new TransactionTemplate(
            new DataSourceTransactionManager(dataSource));
        
        // 2. 创建实例管理器
        PersistentInstanceManager instanceManager = new PersistentInstanceManagerBuilder()
            .withJdbcTemplate(jdbcTemplate)
            .withTransactionTemplate(transactionTemplate)
            .withApplicationName("lease-test")
            .withEnvironment("test")
            .withGroup("default")
            .build();
        
        // 3. 创建工作机器ID分配器
        PersistentInstanceWorkerIdAssigner assigner = new PersistentInstanceWorkerIdAssigner(
            jdbcTemplate, transactionTemplate, instanceManager, "infra_uid");
        
        // 4. 分配工作机器ID
        long workerId = assigner.assignWorkerId();
        System.out.println("分配的工作机器ID: " + workerId);
        
        // 5. 测试租约续约
        System.out.println("\n测试租约续约...");
        for (int i = 0; i < 3; i++) {
            Thread.sleep(1000);
            assigner.renewLease();
            System.out.println("第" + (i+1) + "次续约成功");
        }
        
        // 6. 释放工作机器ID
        assigner.releaseWorkerId();
        System.out.println("\n工作机器ID已释放");
    }
    
    private static DataSource setupDataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("*******************************************");
        config.setUsername("postgres");
        config.setPassword("postgres");
        return new HikariDataSource(config);
    }
}
```

## 8. 测试覆盖率目标

| 组件 | 行覆盖率目标 | 分支覆盖率目标 |
|------|------------|--------------|
| WorkerNodeType | 100% | 100% |
| MachineFingerprints | 85% | 80% |
| KeyManagementService | 90% | 85% |
| PersistentInstanceManagerBuilder | 95% | 90% |
| PersistentInstanceManager | 90% | 85% |
| ValidationResultCache | 95% | 90% |
| UidValidationUtils | 90% | 85% |
| PersistentInstanceWorkerIdAssigner | 90% | 85% |
| UidTableManager | 90% | 85% |
| 整体覆盖率 | ≥90% | ≥85% |

## 9. 测试执行计划

| 阶段 | 时间 | 负责人 |
|------|------|-------|
| 单元测试 | 3天 | 开发人员 |
| 集成测试 | 2天 | 开发人员 |
| 性能测试 | 2天 | 测试人员 |
| 测试报告 | 1天 | 测试人员 |
| **总计** | **8天** | |

## 10. 测试结果报告模板

```
# xkongcloud-commons-uid库测试报告

## 测试概述
- 测试时间：[日期]
- 测试环境：[环境描述]
- 测试版本：[版本号]

## 测试覆盖率
- 行覆盖率：[百分比]
- 分支覆盖率：[百分比]
- 方法覆盖率：[百分比]

## 测试结果摘要
- 单元测试：[通过/失败/总数]
- 集成测试：[通过/失败/总数]
- 性能测试：[通过/失败/总数]

## 详细测试结果
[详细测试结果]

## 发现的问题
[问题列表]

## 结论和建议
[结论和建议]
```

## 11. 总结

本测试方案提供了对xkongcloud-commons-uid库的全面测试策略，包括单元测试、集成测试和性能测试。通过执行这些测试，可以确保库的功能正确性、稳定性和性能满足需求。测试方案中的简化测试方法可以在开发过程中快速验证功能，而完整的测试套件则确保库的质量和可靠性。
