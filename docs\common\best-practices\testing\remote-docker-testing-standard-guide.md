# 远程Docker测试标准指南

## 概述

本文档提供在Windows开发环境中通过SSH连接到Linux Docker环境进行集成测试的标准方法。该方案实现了完全自动化的测试环境，AI可以像本地开发一样直接执行测试。

## 核心优势

- **零Docker依赖**：Windows环境无需安装Docker
- **完全自动化**：AI一键执行，无需人工干预
- **环境隔离**：每次测试都是全新的Docker容器环境
- **多数据库支持**：支持PostgreSQL、Valkey、RabbitMQ等完整数据库栈
- **TestContainers兼容**：保持现有测试代码不变

## 技术架构

```
Windows开发环境          SSH隧道           Linux Docker环境
┌─────────────────┐    ┌─────────────┐    ┌─────────────────┐
│   TestRunner    │───▶│ SSH Tunnel  │───▶│  Docker API     │
│   (AI执行)      │    │ :2375       │    │  :2375          │
└─────────────────┘    └─────────────┘    └─────────────────┘
         │                                          │
         ▼                                          ▼
┌─────────────────┐                        ┌─────────────────┐
│  TestContainers │                        │ PostgreSQL      │
│  Framework      │                        │ Valkey          │
│                 │                        │ RabbitMQ        │
└─────────────────┘                        └─────────────────┘
```

## 标准工作流程

### 1. 环境检测
- 自动检查SSH隧道状态：`netstat -an | findstr :2375`
- 验证SSH连接：`ssh user@remote-host "echo test"`

### 2. 连接建立
- 建立SSH隧道：`ssh -N -L 2375:localhost:2375 user@remote-host`
- 设置环境变量：
  - `DOCKER_HOST=tcp://localhost:2375`
  - `TESTCONTAINERS_RYUK_DISABLED=true`

### 3. 编码配置
- 解决Windows编码问题：`chcp 65001`
- 设置Java编码：`JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8`

### 4. 测试执行
- TestContainers自动管理远程Docker容器
- 根据项目类型选择合适的测试命令

### 5. 资源清理
- 测试完成后自动清理所有Docker资源
- 关闭SSH隧道连接

## 通用脚本模式

### Windows批处理脚本模板
```batch
@echo off
chcp 65001 > nul
set DOCKER_HOST=tcp://localhost:2375
set TESTCONTAINERS_RYUK_DISABLED=true
set JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8

echo 检查SSH隧道状态...
netstat -an | findstr :2375 > nul
if %errorlevel% neq 0 (
    echo 建立SSH隧道...
    start /b ssh -N -L 2375:localhost:2375 user@remote-host
    timeout /t 3 > nul
)

echo 执行测试...
cd /d "%PROJECT_PATH%"
call mvn test

echo 测试完成
```

### Linux配置脚本模板
```bash
#!/bin/bash
# 配置Docker API监听
sudo systemctl stop docker
sudo dockerd -H unix:///var/run/docker.sock -H tcp://0.0.0.0:2375 &
echo "Docker API configured on port 2375"
```

## 时序优化策略

### 容器启动等待
```java
// 使用Wait策略确保容器完全启动
container.waitingFor(
    Wait.forLogMessage(".*ready to accept connections.*", 1)
        .withStartupTimeout(Duration.ofMinutes(3))
);

// 添加连接重试机制
@Retryable(value = {SQLException.class}, maxAttempts = 5, backoff = @Backoff(delay = 2000))
public Connection getConnection() {
    return dataSource.getConnection();
}
```

### 连接池配置
```yaml
spring:
  datasource:
    hikari:
      connection-timeout: 30000
      initialization-fail-timeout: 60000
      maximum-pool-size: 5
      minimum-idle: 1
```

## 故障排除指南

| 问题类型 | 检查命令 | 解决方案 |
|---------|---------|---------|
| SSH连接失败 | `ssh user@host "echo test"` | 检查网络和SSH配置 |
| 端口占用 | `netstat -an \| findstr :2375` | `taskkill /f /im ssh.exe` |
| Docker连接失败 | `curl http://localhost:2375/version` | 检查远程Docker服务 |
| 编码问题 | 检查中文字符显示 | 运行编码配置脚本 |
| 连接拒绝 | 容器启动后立即连接失败 | 添加等待策略和重试机制 |

## 最佳实践

### 安全配置
- 使用SSH密钥认证，避免密码输入
- Docker API仅监听localhost，通过SSH隧道加密传输
- 定期更新SSH密钥和Docker配置

### 性能优化
- 使用连接池减少连接开销
- 实施容器预热策略
- 合理设置超时时间

### 资源管理
- 定期清理过期的Docker镜像和容器
- 监控远程服务器资源使用情况
- 实施测试并发控制

## 适用场景

### 推荐使用
- Windows开发环境 + Linux生产环境
- 需要完整数据库栈的集成测试
- CI/CD流水线中的自动化测试
- 多项目共享测试环境

### 不推荐使用
- 网络延迟敏感的性能测试
- 需要本地文件系统访问的测试
- 简单的单元测试

## 扩展支持

### 多环境支持
- 开发环境：sb.sn.cn
- 测试环境：test.sn.cn
- 预生产环境：pre.sn.cn

### 多数据库支持
- PostgreSQL：主要关系型数据库
- Valkey：缓存和会话存储
- RabbitMQ：消息队列
- Elasticsearch：搜索引擎（可选）

## 相关技术

- TestContainers
- Docker API
- SSH隧道
- Maven/Gradle
- Spring Boot Test

---
**文档版本**: 1.0  
**创建日期**: 2025-06-04  
**适用环境**: Windows + Linux Docker  
**维护状态**: 活跃维护
