# 执行器重构方案 V3：基于“承诺-自证-审计”模型的高可信验证框架

**文档版本**: 1.0
**日期**: 2025-07-25
**作者**: <PERSON><PERSON> (AI Assistant)

## 1. 核心思想与目标

经过深入讨论，我们确定了当前验证机制的核心缺陷：基于关键词的文本匹配无法理解上下文，导致验证不可靠。

本方案旨在用一套全新的、工业级的验证框架取而代之。该框架基于 **“承诺-自证-审计 (Commitment-Self-Proof-Audit)”** 模型，目标是建立一个既能利用AI高级认知能力，又能保证结果确定性和可靠性的高可信内容生成与验证体系。

- **承诺 (Commitment)**: AI在执行前，必须对输入的质量和任务可行性进行评估，并做出可量化的置信度承诺。
- **自证 (Self-Proof)**: AI在生成内容的同时，必须提供一份结构化的“证据清单”（`validation_points`），列出所有可供确定性算法验证的关键点。
- **审计 (Audit)**: 后端系统扮演严格的审计角色，基于确定性算法（如AST解析）逐一核实AI提交的“证据”，并拥有最终裁决权。

## 2. 对 `tools/ace/src/executors/validation_driven_executor.py` 的详细修改设计

以下是对现有代码的逐一修改点分析。

### 2.1. 新增与修改的数据结构 (Dataclasses)

为了支持新框架，我们需要引入新的数据结构。

**新增 `ValidationPoint`**:
```python
@dataclass
class ValidationPoint:
    """由AI提供的、可供确定性算法验证的单个检查点"""
    point_id: str          # 验证点唯一ID, e.g., "GUARDRAIL_NO_SYSTEM_EXIT"
    description: str       # 对该点的自然语言描述
    validation_type: str   # 验证类型, e.g., "AST_CHECK", "REGEX_MATCH", "JSON_SCHEMA"
    target: str            # 验证目标, e.g., "System.exit", "Plugin", "^(error|fail)"
    expected_result: str   # 期望结果, e.g., "NOT_FOUND", "EXISTS", "NOT_MATCH"
    is_critical: bool = True # 标记该点是否为关键验证点
```

**新增 `AICommitment`**:
```python
@dataclass
class AICommitment:
    """AI在执行主任务前返回的可行性承诺"""
    status: str  # "CONFIDENT" or "REJECTED"
    confidence_score: float = 0.0
    analysis_report: str = "" # 如果CONFIDENT，提供分析报告
    issue_report: str = ""    # 如果REJECTED，提供问题报告
```

**修改 `ValidationResult`**:
将移除旧的`py_results`, `ai_results`，改为更清晰的结构。
```python
# 修改后的 ValidationResult
@dataclass
class ValidationResult:
    """验证结果"""
    passed: bool
    confidence: float # 这里的置信度来源于AI的承诺
    audited_points: List[Dict] # 记录每个验证点的审计结果
    issues: List[str]
    error_message: str = ""
```

### 2.2. `ValidationDrivenExecutor` 类的重构

这是修改的核心，主执行流程将完全改变。

**`execute_with_validation` 方法 (完全重写)**:

此方法将拆分为两个阶段的AI调用。

```python
# 伪代码
async def execute_with_validation(self, ...):
    # --- 阶段 0: 可行性分析与承诺获取 ---
    try:
        commitment = await self._phase_0_get_commitment(guardrails, constraints, context)
        if commitment.status == "REJECTED":
            return ExecutionResult(success=False, error_message=f"AI rejected task: {commitment.issue_report}")
        if commitment.confidence_score < confidence_threshold:
            return ExecutionResult(success=False, error_message=f"AI confidence ({commitment.confidence_score}) is below threshold.")
    except Exception as e:
        return ExecutionResult(success=False, error_message=f"Phase 0 (Commitment) failed: {e}")

    # --- 阶段 1: 核心任务执行与自证信息生成 ---
    try:
        ai_output_json = await self._phase_1_generate_with_self_proof(original_content, ...)
    except Exception as e:
        return ExecutionResult(success=False, error_message=f"Phase 1 (Generation) failed: {e}")

    # --- 阶段 2: 净化、审计与执行 ---
    try:
        # 解析JSON
        try:
            ai_output_data = json.loads(ai_output_json)
            validation_points_data = ai_output_data.get("validation_points", [])
            generated_content_str = ai_output_data.get("generated_content", {}).get("content", "")
        except json.JSONDecodeError as e:
            return ExecutionResult(success=False, error_message=f"Failed to decode AI JSON output: {e}")

        # 净化产物
        purified_content = self._purify_content(generated_content_str)

        # 审计
        validation_result = await self.validation_loop.audit_driven_validation(
            purified_content,
            validation_points_data,
            context  # 传递上下文，用于判断内容类型
        )

        if not validation_result.passed:
            return ExecutionResult.validation_failed(...) # 返回审计失败的结果

        # (如果审计通过) 执行后续PyCRUD操作或返回结果
        return await self._execute_after_validation(...)

    except Exception as e:
        # ... 错误处理
```

**新增 `_phase_0_get_commitment` 方法**:

```python
# 新增方法
async def _phase_0_get_commitment(self, guardrails, constraints, context) -> AICommitment:
    # 1. 构建预分析Prompt，要求AI以指定JSON格式返回承诺或拒绝理由
    prompt = f"""
    [Pre-analysis Stage] Review the following inputs:
    Guardrails: {guardrails}
    Constraints: {constraints}
    Context: {context}
    Is it feasible to generate a high-quality output? Respond in one of the following JSON formats ONLY:
    - If feasible: {{"status": "CONFIDENT", "confidence_score": <float>, "analysis_report": "<text>"}}
    - If not: {{"status": "REJECTED", "issue_report": "<text>"}}
    """
    # 2. 调用AI服务
    response_str = await self.ai_service_manager.call_ai(...)
    # 3. 解析返回的JSON，并构造成 AICommitment 对象返回
    response_data = json.loads(response_str)
    return AICommitment(**response_data)
```

**新增 `_phase_1_generate_with_self_proof` 方法**:

```python
# 新增方法
async def _phase_1_generate_with_self_proof(self, original_content, ...):
    # 1. 构建核心任务Prompt，强力注入 "self-proof" 要求
    #    明确要求AI输出包含 `validation_points` 数组
    prompt = f"""
    {original_content}
    ---
    **Mandatory Output Requirement**:
    Your entire output MUST be a single JSON object.
    In this JSON, you MUST include a key "validation_points" which is an array.
    Each object in the array must represent a verifiable point and contain:
    "point_id", "description", "validation_type", "target", "expected_result".
    """
    # 2. 调用AI服务，返回原始JSON字符串
    return await self.ai_service_manager.call_ai(...)
```

**新增 `_purify_content` 方法**:

```python
# 新增方法
def _purify_content(self, content: str) -> str:
    # 简单的反转义实现，可以使用更健壮的库
    return content.encode('utf-8').decode('unicode_escape')
```

### 2.3. `ValidationLoop` 类的重构

此类是审计逻辑的核心。

**`multi_dimensional_validation` 方法 (重命名/重写)**:

将此方法重写为 `audit_driven_validation`。

```python
# 重写后的方法
async def audit_driven_validation(self,
                                purified_content: str,
                                validation_points_data: List[Dict],
                                context: Dict) -> ValidationResult:
    if not validation_points_data:
        return ValidationResult(passed=False, issues=["AI did not provide any validation_points."])

    audited_points_results = []
    all_issues = []

    # 1. 创建验证器分发器实例
    dispatcher = ValidatorDispatcher()

    # 2. 遍历AI提供的每个验证点
    for point_data in validation_points_data:
        try:
            validation_point = ValidationPoint(**point_data)
            # 3. 使用分发器进行审计
            is_passed, details = await dispatcher.dispatch(
                validation_point,
                purified_content,
                context
            )

            audited_points_results.append({
                "point_id": validation_point.point_id,
                "passed": is_passed,
                "details": details
            })

            if not is_passed:
                all_issues.append(f"Audit failed for point '{validation_point.point_id}': {details}")
                # 可配置为快速失败
                # return ValidationResult(passed=False, issues=all_issues, ...)

        except TypeError as e:
            # validation_point_data 格式错误
            issue = f"Invalid validation_point format: {point_data}. Error: {e}"
            all_issues.append(issue)

    # 4. 最终裁决
    final_passed = not all_issues
    return ValidationResult(
        passed=final_passed,
        issues=all_issues,
        audited_points=audited_points_results,
        ...
    )
```

**删除旧的验证方法**:

- `_python_algorithm_validation`
- `_check_guardrail_compliance`
- `_check_constraint_satisfaction`
- `_check_content_quality`
- `_check_context_completeness`
- `_ai_batch_validation`
- `_ai_overall_validation`

这些方法将被全新的、由`ValidatorDispatcher`驱动的审计逻辑所取代。

### 2.4. 新增验证器模块 (`validators.py`)

为了保持代码整洁，建议将具体的验证器逻辑放到一个新文件中 `tools/ace/src/executors/validators.py`。

**`ValidatorDispatcher` 类**:

```python
# in validators.py
class ValidatorDispatcher:
    def __init__(self):
        self.validators = {
            "AST_CHECK_JAVA": JavaAstValidator(),
            "AST_CHECK_PYTHON": PythonAstValidator(),
            "REGEX_MATCH": RegexValidator(),
            # ... 可扩展
        }

    async def dispatch(self, point: ValidationPoint, content: str, context: Dict):
        # 根据 point.validation_type 和 context（例如，代码语言）选择验证器
        validator_key = self._get_validator_key(point.validation_type, context)
        validator = self.validators.get(validator_key)
        if not validator:
            return False, f"No validator found for type '{validator_key}'"
        return await validator.validate(point, content)

    def _get_validator_key(self, validation_type, context):
        # ... 实现选择逻辑, e.g., if validation_type == "AST_CHECK" and context.get('language') == 'java': return "AST_CHECK_JAVA"
        pass
```

**具体的验证器实现 (`JavaAstValidator`, etc.)**:

```python
# in validators.py
class BaseValidator:
    async def validate(self, point: ValidationPoint, content: str):
        raise NotImplementedError

class JavaAstValidator(BaseValidator):
    async def validate(self, point: ValidationPoint, content: str):
        try:
            # 1. 引入 javalang 库
            import javalang
            tree = javalang.parse.parse(content)
            # 2. 遍历AST，寻找 point.target
            #    例如，寻找一个 AnnotationDeclaration 节点，其 name 为 "Plugin"
            found = False
            for path, node in tree:
                if isinstance(node, javalang.tree.AnnotationDeclaration) and node.name == point.target:
                    found = True
                    break
            # 3. 将实际结果与 point.expected_result 比较
            actual_result = "EXISTS" if found else "NOT_FOUND"
            is_passed = (actual_result == point.expected_result)
            return is_passed, f"Expected {point.expected_result}, but was {actual_result}"
        except javalang.tokenizer.LexerError as e:
            return False, f"Java code parsing failed: {e}"

# ... 实现其他验证器
```

## 3. 依赖项变更

- 需要在项目中添加 `javalang` 库用于Java AST解析。

---

这份详尽的方案覆盖了从顶层流程到底层实现的所有修改点，确保了新框架的完整性和可操作性。
