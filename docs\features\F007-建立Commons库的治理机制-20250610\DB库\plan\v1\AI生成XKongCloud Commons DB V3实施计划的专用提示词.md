# AI生成XKongCloud Commons DB V3实施计划的专用提示词

## 文档元数据
- **文档ID**: `commons-db-v3-plan-prompt-20250612`
- **生成日期**: `2025-06-12`
- **适用项目**: `XKongCloud Commons DB V3: 务实的企业级数据访问架构`
- **项目版本**: `V3.0 - 务实企业级架构`
- **复杂度等级**: `L2-中等复杂度（4-7概念，多组件协调）`
- **技术栈**: `Java 21 + Spring Boot 3.4 + PostgreSQL 17 + HikariCP + Querydsl + Flyway`

## 📋 使用说明

这是专门为生成XKongCloud Commons DB V3数据访问库实施计划而定制的AI提示词。基于完整的设计文档分析，此提示词将引导AI生成高质量、可执行的实施计划，确保:

1. **架构完整性**: 完全符合分层架构设计和现代技术栈要求
2. **技术特性深度集成**: 正确实现Java 21虚拟线程、PostgreSQL 17特性、HikariCP优化等现代技术特性的组合优势
3. **精确的实施步骤**: 提供详细、清晰、可执行的实施路径
4. **风险预警与规避**: 预先识别关键风险点，提供缓解策略
5. **验证标准**: 明确的功能验收和性能验证标准

---

## 🧠 AI认知约束强制激活

```bash
@L1:global-constraints                    # 全局约束检查
@L1:ai-implementation-design-principles   # AI实施设计原则
@AI_COGNITIVE_CONSTRAINTS                 # AI认知约束激活
@MEMORY_BOUNDARY_CHECK                    # 记忆边界检查
@HALLUCINATION_PREVENTION                 # 幻觉防护激活
@ATOMIC_OPERATION_VALIDATION              # 原子操作验证
@COGNITIVE_GRANULARITY_CONTROL            # 认知粒度控制
@BOUNDARY_GUARD_ACTIVATION                # 边界护栏激活
```

## 🎯 项目概览

### 核心定位与价值

XKongCloud Commons DB V3是一个强大、灵活、可扩展的、与Spring生态深度集成的数据访问基础库，旨在统一各子项目的数据访问方式，提高开发效率，并为未来的架构演进提供支持。该库采用务实的企业级架构设计，结合现代技术特性，实现以下关键价值：

1. **统一数据访问标准**: 提供多层次的数据访问抽象和实现
2. **深度Spring生态集成**: 无缝整合Spring Data JPA、事务管理等
3. **多数据源支持**: 兼容PostgreSQL、MySQL、Redis等多种数据源
4. **Schema管理自动化**: 基于Flyway的数据库迁移管理
5. **性能与可观测性**: 通过Micrometer实现全面监控
6. **渐进式演进支持**: 降低架构演进的复杂度和风险
7. **现代技术特性倍增**: 通过技术组合优化实现性能突破

### 分层架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                Commons DB 统一数据访问层                      │
├─────────────────────────────────────────────────────────────┤
│  L1: JPA层     │  L2: 查询构建层  │  L3: JDBC层  │  监控层   │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   核心抽象层 (Core Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  统一接口定义  │  SPI机制  │  方言系统  │  连接管理          │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│              缓存适配器层 (Cache Adapter Layer) 🔮未来规划     │
├─────────────────────────────────────────────────────────────┤
│  DB-Cache桥接  │  缓存策略  │  一致性管理  │  失效处理        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   数据驱动层 (Driver Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  PostgreSQL    │  MySQL     │  Redis     │  Valkey(未来)     │
└─────────────────────────────────────────────────────────────┘
```

### 模块结构

```
commons-db/
├── commons-db-core/                 # 核心抽象层
│   ├── api/                        # 统一数据访问接口
│   ├── spi/                        # 服务提供者接口
│   └── dialect/                    # 数据库方言基础
├── commons-db-jpa/                 # L1: Spring Data JPA实现
│   ├── repository/                 # Repository增强
│   ├── entity/                     # 实体管理
│   └── transaction/                # 事务协调
├── commons-db-querydsl/            # L2: Querydsl集成
│   ├── builder/                    # 查询构建器
│   ├── processor/                  # 查询处理器
│   └── optimizer/                  # 查询优化
├── commons-db-jdbc/                # L3: JdbcTemplate封装
│   ├── executor/                   # JDBC执行器
│   ├── mapper/                     # 结果映射器
│   └── batch/                      # 批处理支持
├── commons-db-dialect/             # 数据库方言实现
├── commons-db-monitoring/          # 监控集成
├── commons-db-migration/           # Schema管理
└── commons-db-starter/             # Spring Boot自动配置
```

## 📏 架构约束与边界

### 架构强制约束清单

| 约束ID | 约束说明 | 严重等级 | 验证方法 |
|-------|---------|----------|---------|
| AC001 | 必须遵循分层架构设计，保持L1-L2-L3层次清晰 | P0 | 代码静态分析+依赖检查 |
| AC002 | 核心抽象层不能依赖具体实现层 | P0 | 依赖倒置原则验证 |
| AC003 | 所有功能必须通过SPI机制提供可插拔实现 | P1 | SPI注册验证 |
| AC004 | 每个模块必须有完整的单元测试，覆盖率≥80% | P1 | 测试覆盖率报告 |
| AC005 | 必须兼容Java 21虚拟线程特性 | P0 | 线程模型兼容性测试 |
| AC006 | HikariCP配置必须优化支持虚拟线程 | P1 | 连接池性能测试 |
| AC007 | 必须提供标准监控接入点 | P1 | Micrometer指标验证 |
| AC008 | Schema管理必须基于Flyway实现版本控制 | P0 | 迁移脚本验证 |

### 包含与排除范围

**包含范围:**
- 统一数据访问接口和实现（JPA、Querydsl、JDBC）
- Spring生态集成（Spring Data、Spring Boot、Spring TX）
- 多数据源支持（PostgreSQL、MySQL、Redis）
- Schema版本管理（Flyway集成）
- 性能监控（Micrometer集成）
- 现代技术特性组合优化

**排除范围:**
- 大数据处理（万亿级数据需专门技术栈）
- AI/ML功能（不属于数据访问范畴）
- 具体业务逻辑实现
- 零停机独立保证（需系统工程支持）
- 缓存主实现（仅提供适配器，缓存主体由commons-cache实现）

### 技术栈约束

**强制技术选型:**
- JDK: Java 21（虚拟线程支持）
- 框架: Spring Boot 3.4
- ORM: Hibernate (JPA实现)
- 连接池: HikariCP（Spring Boot 3.4默认）
- 类型安全查询: Querydsl 5.0+
- Schema管理: Flyway 9.0+
- 监控: Micrometer + Spring Boot Actuator

**技术版本兼容性矩阵:**
| 组件 | 最低版本 | 推荐版本 | 兼容性测试必要性 |
|-----|---------|---------|---------------|
| Java | 21 | 21 LTS | 必要 |
| Spring Boot | 3.2 | 3.4 | 必要 |
| HikariCP | 5.0 | 5.1 | 必要 |
| Hibernate | 6.2 | 6.4 | 必要 |
| Querydsl | 5.0.0 | 5.0.0 | 必要 |
| Flyway | 9.0 | 9.8 | 必要 |
| PostgreSQL | 15 | 17 | 必要 |

## 🚀 现代技术特性组合优化

### 技术特性组合效应

Commons DB V3最重要的创新在于**技术特性组合优化**，即通过组合多项技术特性产生性能倍增效应而非简单叠加。AI在实施计划中必须特别关注这些组合优化：

#### 核心组合策略

**组合1：高并发查询优化**
- 核心技术组合：HikariCP虚拟线程友好连接池 + PostgreSQL 17并行查询 + Spring Boot 3.4异步处理 + Java 21 Virtual Threads
- 性能价值：查询吞吐量提升300%（vs 单独优化150%）
- 实施关键点：必须确保所有组件配置协同工作，尤其是连接池与虚拟线程配置

**组合2：JSON数据处理优化**
- 核心技术组合：PostgreSQL 17 JSON增强 + HikariCP预编译缓存优化 + Spring Boot 3.4响应式 + Java 21 Pattern Matching
- 性能价值：JSON查询性能提升500%（vs 单独优化200%）
- 实施关键点：需特别优化JSON查询路径，确保数据库特性与Java代码完美协作

**组合3：批量操作优化**
- 核心技术组合：PostgreSQL 17批量插入 + HikariCP批量重写优化 + Spring Boot 3.4事务管理 + Java 21 Record类
- 性能价值：批量操作性能提升800%（vs 单独优化300%）
- 实施关键点：必须实现智能批量策略，根据数据量动态调整批次大小

**组合4：虚拟线程无锁优化**
- 核心技术组合：HikariCP无synchronized设计 + Java 21虚拟线程 + PostgreSQL 17异步I/O + Spring Boot 3.4优雅关闭
- 性能价值：高并发场景性能提升1000%+（消除虚拟线程固定问题）
- 实施关键点：避免任何可能导致虚拟线程固定的同步块和锁

### 关键技术特性实现要点

#### Java 21虚拟线程集成

```java
// 核心配置类示例
@Configuration
public class VirtualThreadConfiguration {
    
    @Bean
    public Executor virtualThreadExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }
    
    // 虚拟线程固定检测器
    @Bean
    public VirtualThreadPinningDetector pinningDetector() {
        return new VirtualThreadPinningDetector();
    }
}

// 异步数据访问示例
@Component
public class VirtualThreadDataAccess<T, ID> {
    
    private final Executor virtualThreadExecutor;
    private final JpaRepository<T, ID> repository;
    
    // 异步保存方法
    public CompletableFuture<T> saveAsync(T entity) {
        return CompletableFuture.supplyAsync(() -> repository.save(entity), virtualThreadExecutor);
    }
    
    // 异步批量查询
    public CompletableFuture<List<T>> findAllByIdsAsync(List<ID> ids) {
        return CompletableFuture.supplyAsync(() -> repository.findAllById(ids), virtualThreadExecutor);
    }
}
```

#### PostgreSQL 17特性集成

```java
// PostgreSQL 17 JSON特性集成示例
@Component
public class PostgreSQL17JsonSupport {
    
    private final JdbcTemplate jdbcTemplate;
    
    // JSON_TABLE支持（PostgreSQL 17新特性）
    public <T> List<T> queryWithJsonTable(String jsonData, Class<T> resultType) {
        String sql = 
            "SELECT * FROM json_table(?, '$.items[*]' COLUMNS (" +
            "  id VARCHAR(50) PATH '$.id'," +
            "  name VARCHAR(100) PATH '$.name'," +
            "  value DECIMAL PATH '$.value'" +
            ")) AS jt";
        
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(resultType), jsonData);
    }
    
    // 并行查询支持
    public <T> List<T> parallelQuery(String sql, RowMapper<T> mapper) {
        // 启用并行查询
        jdbcTemplate.execute("SET max_parallel_workers_per_gather = 4");
        return jdbcTemplate.query(sql, mapper);
    }
}
```

#### HikariCP虚拟线程优化

```java
// HikariCP虚拟线程优化配置
@Configuration
public class HikariCPConfiguration {
    
    @Bean
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.hikari")
    public HikariConfig hikariConfig() {
        HikariConfig config = new HikariConfig();
        
        // 虚拟线程优化设置
        config.setMaximumPoolSize(20);        // 较小的连接池，因为虚拟线程成本低
        config.setMinimumIdle(5);             // 保持最小连接数
        config.setIdleTimeout(30000);         // 空闲连接超时
        config.setConnectionTimeout(10000);   // 连接获取超时
        config.setMaxLifetime(1800000);       // 连接最大生命周期
        
        // 关键：设置事务隔离级别为READ_COMMITTED，避免长事务
        config.setTransactionIsolation("TRANSACTION_READ_COMMITTED");
        
        // 关键：禁用JMX，减少虚拟线程固定风险
        config.setRegisterMbeans(false);
        
        // 预编译语句缓存
        config.setPreparationThreshold(3);
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        
        return config;
    }
}
```

## 📝 实施关键点

### 核心组件实施优先级和依赖顺序

**实施模块优先级矩阵**：

| 模块 | 优先级 | 依赖模块 | 关键特性 | 验证方式 |
|-----|------|---------|---------|--------|
| commons-db-core | P0 | 无 | SPI机制、接口定义 | 单元测试 |
| commons-db-dialect | P0 | commons-db-core | 数据库方言实现 | 集成测试 |
| commons-db-starter | P1 | commons-db-core | 自动配置 | Spring Boot测试 |
| commons-db-jpa | P1 | commons-db-core, commons-db-dialect | JPA增强实现 | 单元测试+集成测试 |
| commons-db-querydsl | P1 | commons-db-core, commons-db-jpa | Querydsl集成 | 功能测试+性能测试 |
| commons-db-jdbc | P1 | commons-db-core, commons-db-dialect | JDBC封装 | 功能测试+性能测试 |
| commons-db-monitoring | P2 | 所有模块 | 监控指标 | 监控测试 |
| commons-db-migration | P2 | commons-db-core | Schema管理 | 迁移测试 |

### 关键接口及抽象实现

**必须实现的核心抽象接口**：

1. **数据访问模板接口**
```java
public interface DataAccessTemplate<T, ID> {
    // 基础CRUD操作
    T save(T entity);
    Optional<T> findById(ID id);
    List<T> findAll();
    void delete(T entity);
    
    // 批量操作
    <S extends T> List<S> saveAll(Iterable<S> entities);
    void deleteAll(Iterable<? extends T> entities);
    
    // 查询操作
    List<T> findBySpec(QuerySpec spec);
    Page<T> findBySpec(QuerySpec spec, Pageable pageable);
    
    // 异步操作（虚拟线程支持）
    CompletableFuture<T> saveAsync(T entity);
    CompletableFuture<Optional<T>> findByIdAsync(ID id);
    CompletableFuture<List<T>> findAllAsync();
    
    // 事务支持
    <R> R executeInTransaction(Function<DataAccessTemplate<T, ID>, R> action);
    
    // 监控支持
    DataAccessStats getStats();
    
    // 提供者信息
    DataAccessProvider getProvider();
}
```
