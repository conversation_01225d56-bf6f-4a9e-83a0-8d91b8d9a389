#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAP方法对比测试运行器
快速运行CAP对比测试的简化脚本

使用方法：
python run_cap_comparison_test.py

作者：AI助手
日期：2025-01-10
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from cap_approach_comparison_tester import CAPApproachTester, REAL_WORLD_TASKS, API_CONFIG
    import json
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保 cap_approach_comparison_tester.py 文件在同一目录下")
    sys.exit(1)

def run_quick_test():
    """运行快速测试（只测试一个任务和一个模型）"""
    print("🚀 CAP方法对比快速测试")
    print("=" * 50)
    
    # 只测试第一个任务和第一个模型
    test_task = REAL_WORLD_TASKS[0]
    test_model = API_CONFIG["models"][0]
    
    print(f"📋 测试任务: {test_task['name']}")
    print(f"🤖 测试模型: {test_model}")
    print()
    
    tester = CAPApproachTester()
    
    try:
        print("🔄 开始测试方案A（内容嵌入式CAP）...")
        result_a = tester.test_approach_a_embedded_cap(test_task, test_model)
        
        if result_a["success"]:
            print(f"✅ 方案A完成 - 质量分数: {result_a['logic_analysis']['overall_score']:.1f}")
            print(f"   API调用次数: {result_a['api_calls']}")
            print(f"   Token消耗: {result_a['total_tokens']}")
        else:
            print(f"❌ 方案A失败: {result_a['error']}")
            return
        
        print()
        print("🔄 开始测试方案B（外部头部式CAP）...")
        result_b = tester.test_approach_b_header_cap(test_task, test_model)
        
        if result_b["success"]:
            print(f"✅ 方案B完成 - 质量分数: {result_b['logic_analysis']['overall_score']:.1f}")
            print(f"   API调用次数: {result_b['api_calls']}")
            print(f"   Token消耗: {result_b['total_tokens']}")
        else:
            print(f"❌ 方案B失败: {result_b['error']}")
            return
        
        # 简单对比
        print()
        print("📊 快速对比结果:")
        print("-" * 30)
        
        score_a = result_a['logic_analysis']['overall_score']
        score_b = result_b['logic_analysis']['overall_score']
        
        print(f"质量对比:")
        print(f"  方案A: {score_a:.1f}分")
        print(f"  方案B: {score_b:.1f}分")
        if score_a > score_b:
            print(f"  🏆 质量优胜: 方案A，领先 {score_a - score_b:.1f}分")
        elif score_b > score_a:
            print(f"  🏆 质量优胜: 方案B，领先 {score_b - score_a:.1f}分")
        else:
            print(f"  🤝 质量结果: 平局")
        
        print(f"\n效率对比:")
        print(f"  方案A: {result_a['api_calls']}次调用, {result_a['total_tokens']}tokens")
        print(f"  方案B: {result_b['api_calls']}次调用, {result_b['total_tokens']}tokens")
        
        if result_a['total_tokens'] < result_b['total_tokens']:
            savings = (result_b['total_tokens'] - result_a['total_tokens']) / result_b['total_tokens'] * 100
            print(f"  💰 效率优胜: 方案A，节省 {savings:.1f}% tokens")
        elif result_b['total_tokens'] < result_a['total_tokens']:
            savings = (result_a['total_tokens'] - result_b['total_tokens']) / result_a['total_tokens'] * 100
            print(f"  💰 效率优胜: 方案B，节省 {savings:.1f}% tokens")
        else:
            print(f"  🤝 效率结果: 相当")
        
        # 保存快速测试结果
        quick_result = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "quick_test",
            "task": test_task,
            "model": test_model,
            "approach_a_result": result_a,
            "approach_b_result": result_b,
            "quick_comparison": {
                "quality_winner": "A" if score_a > score_b else "B" if score_b > score_a else "Tie",
                "efficiency_winner": "A" if result_a['total_tokens'] < result_b['total_tokens'] else "B" if result_b['total_tokens'] < result_a['total_tokens'] else "Tie",
                "quality_difference": abs(score_a - score_b),
                "token_difference": abs(result_a['total_tokens'] - result_b['total_tokens'])
            }
        }
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"cap_quick_test_result_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(quick_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 快速测试结果已保存: {filename}")
        print("🎉 快速测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

def run_full_test():
    """运行完整测试"""
    print("🚀 CAP方法对比完整测试")
    print("=" * 50)
    print("⚠️ 注意：完整测试将调用大量API，可能需要较长时间和费用")
    
    confirm = input("是否继续？(y/N): ").strip().lower()
    if confirm != 'y':
        print("测试已取消")
        return
    
    from cap_approach_comparison_tester import main
    main()

def show_menu():
    """显示菜单"""
    print("🎯 CAP方法对比测试器")
    print("=" * 30)
    print("1. 快速测试（1个任务，1个模型）")
    print("2. 完整测试（所有任务，所有模型）")
    print("3. 查看测试配置")
    print("4. 退出")
    print()

def show_config():
    """显示测试配置"""
    print("📋 当前测试配置:")
    print("-" * 20)
    print(f"API地址: {API_CONFIG['url']}")
    print(f"测试模型: {', '.join(API_CONFIG['models'])}")
    print(f"测试任务数: {len(REAL_WORLD_TASKS)}")
    print("\n测试任务列表:")
    for i, task in enumerate(REAL_WORLD_TASKS, 1):
        print(f"  {i}. {task['name']} ({task['id']})")
    print()

def main():
    """主函数"""
    while True:
        show_menu()
        choice = input("请选择操作 (1-4): ").strip()
        
        if choice == '1':
            print()
            run_quick_test()
            print()
        elif choice == '2':
            print()
            run_full_test()
            print()
        elif choice == '3':
            print()
            show_config()
        elif choice == '4':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")
            print()

if __name__ == "__main__":
    main()
