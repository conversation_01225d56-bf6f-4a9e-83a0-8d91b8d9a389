# V4.5 项目隔离机制设计 - 最小改动完美隔离

## 📋 设计概述

**设计目标**：多项目数据库和Meeting隔离，支持远程多项目同时调用指挥官
**核心方案**：MCP客户端ID映射 + 通用路径映射规则 + 项目级物理隔离
**代码改动量**：306行（完整设计），基于现有目录结构改造
**架构师自信度**：95%（基于现有代码结构和隔离需求分析）

## 🏗️ 基于现有目录结构的项目隔离规划

### 当前系统目录结构分析
```
tools/ace/src/                           # 当前系统根目录
├── data/v4_panoramic_model.db           # 当前全局数据库
├── Meeting/                             # 当前全局Meeting目录
│   ├── ai_communication_logs/
│   ├── algorithm_thinking_logs/
│   ├── evidence_archive/
│   ├── v4_conical_geometry_tracking/
│   ├── v4_philosophy_alignment/
│   ├── v4_unified_logic_chains/
│   └── v4_validation_results/
├── four_layer_meeting_system/           # 四重会议系统
├── python_host/                         # Python指挥官
└── [其他系统组件...]
```

### 项目隔离后的目录结构设计
```
tools/ace/src/                           # 系统根目录（保持不变）
├── projects/                            # 新增：项目隔离根目录
│   ├── xkongcloud/                      # 项目A：xkongcloud
│   │   ├── databases/
│   │   │   └── xkongcloud.db           # 项目专属数据库
│   │   └── meetings/                    # 项目专属Meeting目录
│   │       └── docs/features/F007-建立Commons库的治理机制-20250610/
│   │           └── nexus万用插座/design/v1/
│   │               ├── ai_communication_logs/
│   │               ├── algorithm_thinking_logs/
│   │               ├── evidence_archive/
│   │               ├── v4_conical_geometry_tracking/
│   │               ├── v4_philosophy_alignment/
│   │               ├── v4_unified_logic_chains/
│   │               └── v4_validation_results/
│   ├── enterprise_system/               # 项目B：enterprise_system
│   │   ├── databases/
│   │   │   └── enterprise_system.db
│   │   └── meetings/
│   │       └── src/enterprise/user-management/api/
│   │           ├── ai_communication_logs/
│   │           └── [Meeting子目录结构...]
│   └── default/                         # 默认项目（兼容现有系统）
│       ├── databases/
│       │   └── default.db
│       └── meetings/
│           └── [现有Meeting目录迁移到这里]
├── [现有系统组件保持不变...]
├── four_layer_meeting_system/
├── python_host/
└── data/v4_panoramic_model.db          # 保留作为默认数据库
```

## 🎯 隔离需求分析

### 双层隔离架构
```
根项目隔离（项目级别）：
├── 项目A: xkongcloud/
│   ├── databases/xkongcloud.db          # 项目专属数据库
│   └── meetings/                        # 项目专属Meeting根目录
├── 项目B: enterprise_system/
│   ├── databases/enterprise_system.db   # 项目专属数据库
│   └── meetings/                        # 项目专属Meeting根目录

工作目录隔离（Meeting级别）：
基于用户工作目录的通用映射规则：
用户工作目录: docs\features\F007-nexus万用插座\design\v1
映射Meeting目录: {项目根}/meetings/docs/features/F007-nexus万用插座/design/v1/

隔离原则：
- 项目级隔离：每个项目有独立的根目录，包含databases/和meetings/
- Meeting隔离：完全保持用户工作目录的层次结构
- 通用映射：{项目根}/meetings/{用户工作目录路径}/
```

### 项目隔离映射关系设计
```python
# MCP客户端ID → 项目映射（基于tools/ace/src/projects/结构）
{
    "mcp_client_dev_001": {
        "project_name": "xkongcloud",
        "project_path": "tools/ace/src/projects/xkongcloud",
        "sqlite_db": "tools/ace/src/projects/xkongcloud/databases/xkongcloud.db",
        "meetings_root": "tools/ace/src/projects/xkongcloud/meetings",
        "client_name": "开发环境-XKong云"
    },
    "mcp_client_prod_002": {
        "project_name": "enterprise_system",
        "project_path": "tools/ace/src/projects/enterprise_system",
        "sqlite_db": "tools/ace/src/projects/enterprise_system/databases/enterprise_system.db",
        "meetings_root": "tools/ace/src/projects/enterprise_system/meetings",
        "client_name": "生产环境-企业系统"
    },
    "default": {
        "project_name": "default",
        "project_path": "tools/ace/src/projects/default",
        "sqlite_db": "tools/ace/src/projects/default/databases/default.db",
        "meetings_root": "tools/ace/src/projects/default/meetings",
        "client_name": "默认项目（兼容现有系统）"
    }
}

# 工作目录 → Meeting路径映射（通用规则）
# 通用映射规则：{项目根}/meetings/{用户工作目录路径}/
# 示例映射（非硬编码）：
{
    # 用户调用：使用ace mcp来开会：docs\features\F007-建立Commons库的治理机制-20250610\nexus万用插座\design\v1
    # 自动映射到：xkongcloud/meetings/docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/
    "通用映射规则": {
        "input_pattern": "用户工作目录路径",
        "output_pattern": "{项目根}/meetings/{用户工作目录路径}/",
        "work_nature": "基于路径特征智能识别",
        "work_type": "基于目录模式智能分类"
    }
}
```

## 🏗️ 核心组件设计

### 1. ProjectContextManager（80行代码）

```python
class ProjectContextManager:
    """项目上下文管理器 - 基于现有配置管理系统的项目隔离"""

    # 类变量：全局项目映射配置
    _project_mappings = {}
    _current_context = None
    _web_server_ref = None
    _config_loader = None

    @classmethod
    def initialize(cls, web_server):
        """初始化项目映射 - Web服务器启动时调用，集成现有配置管理"""
        cls._web_server_ref = web_server

        # 集成现有配置管理系统
        from common_config_loader import CommonConfigLoader
        cls._config_loader = CommonConfigLoader()

        cls._load_project_mappings()
    
    @classmethod
    def _load_project_mappings(cls):
        """从统一配置管理系统加载项目映射配置"""
        # 从配置文件获取项目隔离配置
        isolation_config = cls._config_loader.get_config("project_isolation_config", {})

        if not isolation_config.get("enabled", False):
            # 项目隔离未启用，使用默认配置
            cls._project_mappings = {}
            return

        projects_root = isolation_config.get("projects_root", "tools/ace/src/projects")
        client_mappings = isolation_config.get("client_mappings", {})

        # 动态生成项目映射
        cls._project_mappings = {}
        for client_id, client_config in client_mappings.items():
            project_name = client_config["project_name"]
            cls._project_mappings[client_id] = {
                "project_name": project_name,
                "project_path": f"{projects_root}/{project_name}",
                "sqlite_db": f"{projects_root}/{project_name}/databases/{project_name}.db",
                "meetings_root": f"{projects_root}/{project_name}/meetings",
                "client_name": client_config["client_name"]
            }

        # 工作目录映射配置（通用规则，无需硬编码）
        # 使用通用映射规则：{项目路径}/meetings/{用户工作目录路径}/
        cls._directory_mappings = {
            # 这里不再硬编码具体路径，而是使用智能识别规则
            # 所有映射都通过 _generate_meeting_path 和 _analyze_work_nature 动态生成
        }
    
    @classmethod
    def get_project_context(cls, client_id: str = None):
        """获取根项目上下文 - 自动映射或人工输入（SQLite隔离级别）"""
        if client_id and client_id in cls._project_mappings:
            # 自动映射：通过客户端ID获取根项目信息
            return cls._project_mappings[client_id]
        else:
            # 人工输入：通过Web界面让人类选择根项目
            return cls._request_human_project_selection(client_id)

    @classmethod
    def get_meeting_context(cls, work_directory: str, root_project: str):
        """获取Meeting上下文 - 基于工作目录路径的通用映射规则"""
        # 通用映射规则：{项目路径}/meetings/{用户工作目录路径}/
        # 标准化路径分隔符为Unix格式
        normalized_work_dir = work_directory.replace('\\', '/')

        # 获取项目上下文
        project_context = cls.get_project_context_by_name(root_project)
        if not project_context:
            # 使用默认项目
            project_context = cls._get_default_context()

        # 生成Meeting路径：完全保持用户工作目录结构
        meeting_path = f"{project_context['meetings_root']}/{normalized_work_dir}/"

        # 智能识别工作性质
        work_nature = cls._analyze_work_nature(work_directory)

        return {
            "meeting_path": meeting_path,
            "work_nature": work_nature["nature"],
            "work_type": work_nature["work_type"]
        }

    @classmethod
    def _generate_meeting_path(cls, work_directory: str, root_project: str):
        """生成Meeting路径 - 通用映射规则：完全保持用户工作目录结构"""
        # 标准化路径分隔符
        normalized_work_dir = work_directory.replace('\\', '/')

        # 获取项目上下文
        project_context = cls.get_project_context_by_name(root_project)
        if not project_context:
            project_context = cls._get_default_context()

        # 通用映射规则：{项目路径}/meetings/{用户工作目录路径}/
        return f"{project_context['meetings_root']}/{normalized_work_dir}/"

    @classmethod
    def get_project_context_by_name(cls, project_name: str):
        """根据项目名称获取项目上下文"""
        for client_id, context in cls._project_mappings.items():
            if context["project_name"] == project_name:
                return context
        return None

    @classmethod
    def _analyze_work_nature(cls, work_directory: str):
        """分析工作性质 - 基于配置文件中的模式匹配"""
        import re

        # 从配置文件获取工作性质模式
        isolation_config = cls._config_loader.get_config("project_isolation_config", {})
        work_nature_patterns = isolation_config.get("work_nature_patterns", {})

        # 如果配置文件中没有模式，使用默认模式
        if not work_nature_patterns:
            work_nature_patterns = {
                'nexus万用插座': {
                    'pattern': r'nexus|万用插座|插座',
                    'nature': 'nexus万用插座设计文档',
                    'work_type': '四重会议工作'
                },
                'commons治理': {
                    'pattern': r'commons|治理',
                    'nature': 'Commons库治理机制',
                    'work_type': '治理体系设计'
                },
                '四重会议': {
                    'pattern': r'四重会议|four.*layer',
                    'nature': '四重会议系统',
                    'work_type': '会议系统开发'
                }
            }

        # 模式匹配
        for key, pattern_info in work_nature_patterns.items():
            pattern = pattern_info.get('pattern', '')
            if pattern and re.search(pattern, work_directory, re.IGNORECASE):
                return {
                    'nature': pattern_info.get('nature', '未知工作性质'),
                    'work_type': pattern_info.get('work_type', '未知工作类型')
                }

        return {
            'nature': '通用项目工作',
            'work_type': '项目开发'
        }
    
    @classmethod
    def _request_human_project_selection(cls, client_id: str):
        """请求人类选择项目 - 通过Web界面"""
        available_projects = [
            {"root": "xkongcloud", "sub": "commons_governance", "name": "XKong云-Commons治理"},
            {"root": "enterprise_system", "sub": "user_management", "name": "企业系统-用户管理"},
            {"root": "ai_platform", "sub": "model_training", "name": "AI平台-模型训练"}
        ]
        
        # 通过Web界面显示项目选择
        if cls._web_server_ref:
            cls._web_server_ref.request_project_selection(client_id, available_projects)
        
        # 返回默认项目（防止阻塞）
        return {
            "root_project": "default",
            "sub_project": "unknown",
            "sqlite_db": "databases/default.db", 
            "meeting_path": "meetings/default/unknown/",
            "client_name": f"未知项目-{client_id}"
        }
    
    @classmethod
    def set_current_context(cls, client_id: str):
        """设置当前项目上下文 - 指挥官调用"""
        cls._current_context = cls.get_project_context(client_id)
        return cls._current_context
    
    @classmethod
    def get_current_context(cls):
        """获取当前项目上下文"""
        return cls._current_context or cls._get_default_context()
    
    @classmethod
    def _get_default_context(cls):
        """获取默认上下文（兼容现有系统，从配置文件获取）"""
        # 从配置文件获取默认项目配置
        isolation_config = cls._config_loader.get_config("project_isolation_config", {})
        projects_root = isolation_config.get("projects_root", "tools/ace/src/projects")
        default_project = isolation_config.get("default_project", "default")

        return {
            "project_name": default_project,
            "project_path": f"{projects_root}/{default_project}",
            "sqlite_db": f"{projects_root}/{default_project}/databases/{default_project}.db",
            "meetings_root": f"{projects_root}/{default_project}/meetings",
            "client_name": f"默认项目（{default_project}）"
        }
```

### 2. 指挥官集成（增强工作目录识别）

```python
class PythonCommanderMeetingCoordinatorV45Enhanced:
    """指挥官增强版 - 支持项目隔离 + 工作目录识别"""

    def __init__(self):
        # 现有初始化代码...

        # 🎯 新增：项目上下文支持
        self.project_context = None
        self.current_sqlite_db = None
        self.current_meeting_path = None

        # 🎯 新增：工作目录上下文支持
        self.work_directory = None
        self.work_nature = None
        self.work_type = None
        self.work_context = None

    def set_project_context(self, client_id: str):
        """设置项目上下文 - 远程调用时使用"""
        self.project_context = ProjectContextManager.set_current_context(client_id)
        self.current_sqlite_db = self.project_context["sqlite_db"]
        self.current_meeting_path = self.project_context["meeting_path"]

        print(f"🎯 切换到项目: {self.project_context['client_name']}")
        print(f"📊 SQLite: {self.current_sqlite_db}")
        print(f"📁 Meeting: {self.current_meeting_path}")

    def set_work_directory_context(self, directory: str, nature: str, work_type: str, context: str):
        """设置工作目录上下文 - Meeting隔离级别"""
        self.work_directory = directory
        self.work_nature = nature
        self.work_type = work_type
        self.work_context = context

        # 🎯 关键：基于工作目录生成Meeting路径（不是基于子项目）
        if self.project_context:
            meeting_context = ProjectContextManager.get_meeting_context(
                directory, self.project_context["root_project"]
            )
            self.current_meeting_path = meeting_context["meeting_path"]

        print(f"📁 工作目录: {self.work_directory}")
        print(f"🎯 工作性质: {self.work_nature}")
        print(f"🔧 工作类型: {self.work_type}")
        print(f"📋 工作上下文: {self.work_context}")
        print(f"📂 Meeting路径: {self.current_meeting_path}")

        # 根据工作性质调整指挥官行为
        self._adapt_commander_behavior()

    def _adapt_commander_behavior(self):
        """根据工作性质调整指挥官行为模式"""
        behavior_patterns = {
            'nexus万用插座设计文档': {
                'focus': '通用接口设计',
                'algorithms': ['接口抽象', '模式识别', '架构推导'],
                'validation_level': 'high',
                'documentation_style': 'architectural'
            },
            'Commons库治理机制': {
                'focus': '治理体系设计',
                'algorithms': ['治理模式', '流程设计', '规范制定'],
                'validation_level': 'strict',
                'documentation_style': 'governance'
            },
            '四重会议系统': {
                'focus': '会议协调机制',
                'algorithms': ['协调算法', '状态管理', '流程控制'],
                'validation_level': 'medium',
                'documentation_style': 'system'
            },
            'V4.5系统设计': {
                'focus': '系统架构设计',
                'algorithms': ['架构分析', '系统设计', '集成规划'],
                'validation_level': 'high',
                'documentation_style': 'technical'
            },
            '容错机制设计': {
                'focus': '可靠性工程',
                'algorithms': ['故障分析', '容错设计', '恢复机制'],
                'validation_level': 'critical',
                'documentation_style': 'reliability'
            }
        }

        pattern = behavior_patterns.get(self.work_nature, {
            'focus': '通用项目工作',
            'algorithms': ['通用分析', '问题解决', '方案设计'],
            'validation_level': 'medium',
            'documentation_style': 'standard'
        })

        self.current_behavior_pattern = pattern
        print(f"🧠 指挥官行为模式: {pattern['focus']}")
        print(f"🔧 算法组合: {', '.join(pattern['algorithms'])}")
        print(f"✅ 验证级别: {pattern['validation_level']}")

    def get_sqlite_connection(self):
        """获取SQLite连接 - 自动使用当前项目数据库"""
        db_path = self.current_sqlite_db or "databases/default.db"
        return sqlite3.connect(db_path)

    def get_meeting_directory(self):
        """获取Meeting目录 - 基于工作目录隔离，与ProjectIsolationManager对接"""
        return self.current_meeting_path or "Meeting/default/main/"

    def get_project_isolation_config(self):
        """获取项目隔离配置 - 供Meeting目录的ProjectIsolationManager使用"""
        if not self.project_context or not self.work_directory:
            return None

        return {
            "root_project": self.project_context["root_project"],
            "work_directory": self.work_directory,
            "meeting_path": self.current_meeting_path,
            "sqlite_db": self.current_sqlite_db,
            "work_nature": self.work_nature,
            "work_type": self.work_type,
            "isolation_namespace": f"{self.project_context['root_project']}::{self.work_directory.replace('\\', '::')}"
        }

    def get_work_context(self):
        """获取完整工作上下文"""
        return {
            'project': self.project_context,
            'work_directory': self.work_directory,
            'work_nature': self.work_nature,
            'work_type': self.work_type,
            'work_context': self.work_context,
            'behavior_pattern': getattr(self, 'current_behavior_pattern', None)
        }
```

### 3. Web服务器集成（与Meeting目录ProjectIsolationManager对接）

```python
class FourLayerMeetingWebServer:
    """Web服务器增强 - 支持项目选择，与Meeting目录隔离管理器对接"""

    def __init__(self):
        # 现有初始化代码...

        # 🎯 新增：项目上下文管理器初始化
        ProjectContextManager.initialize(self)
        self.pending_project_selections = {}  # 待选择的项目

        # 🎯 与Meeting目录ProjectIsolationManager对接
        self.meeting_isolation_manager = None  # 将在Meeting目录服务初始化时设置
    
    def request_project_selection(self, client_id: str, available_projects: list):
        """请求人类选择项目 - 通过Web界面"""
        self.pending_project_selections[client_id] = {
            "available_projects": available_projects,
            "timestamp": datetime.now(),
            "status": "pending"
        }
        
        # 通过WebSocket通知前端显示项目选择界面
        self._notify_frontend_project_selection(client_id, available_projects)
    
    def handle_project_selection(self, client_id: str, selected_project: dict):
        """处理人类的项目选择"""
        if client_id in self.pending_project_selections:
            # 更新项目映射
            ProjectContextManager._project_mappings[client_id] = {
                "root_project": selected_project["root"],
                "sub_project": selected_project["sub"], 
                "sqlite_db": f"databases/{selected_project['root']}.db",
                "meeting_path": f"meetings/{selected_project['root']}/{selected_project['sub']}/",
                "client_name": selected_project["name"]
            }
            
            # 通知指挥官切换项目上下文
            if self.commander:
                self.commander.set_project_context(client_id)

                # 🎯 与Meeting目录ProjectIsolationManager同步
                if self.meeting_isolation_manager:
                    isolation_config = self.commander.get_project_isolation_config()
                    if isolation_config:
                        self.meeting_isolation_manager.update_project_namespace(isolation_config)

            # 清理待选择状态
            del self.pending_project_selections[client_id]

            print(f"✅ 项目选择完成: {client_id} → {selected_project['name']}")
            print(f"📂 Meeting隔离已同步到ProjectIsolationManager")
    
    def _notify_frontend_project_selection(self, client_id: str, available_projects: list):
        """通知前端显示项目选择界面"""
        notification = {
            "type": "project_selection_request",
            "client_id": client_id,
            "available_projects": available_projects,
            "message": f"请为客户端 {client_id} 选择项目"
        }
        
        # 发送到Web界面（具体实现根据前端框架）
        # self.broadcast_to_frontend(notification)
```

### 4. 抽象层集成（20行代码）

```python
class RemoteClientFactory:
    """远程客户端工厂 - 支持项目自动隔离"""
    
    @classmethod
    def create_file(cls, file_path: str, remote_id: str):
        """创建远程文件实例 - 自动项目隔离"""
        if not remote_id:
            raise ValueError("必须指定remote_id")
        
        # 🎯 自动获取项目上下文
        project_context = ProjectContextManager.get_project_context(remote_id)
        
        # 🎯 自动调整文件路径（项目隔离）
        isolated_file_path = cls._apply_project_isolation(file_path, project_context)
        
        return RemoteFile(remote_id, isolated_file_path, project_context)
    
    @classmethod
    def _apply_project_isolation(cls, file_path: str, project_context: dict):
        """应用项目隔离 - 自动调整路径"""
        root_project = project_context["root_project"]
        sub_project = project_context["sub_project"]
        
        # 如果是相对路径，自动添加项目前缀
        if not os.path.isabs(file_path):
            return f"projects/{root_project}/{sub_project}/{file_path}"
        
        return file_path
```

## 📊 使用示例

### 通用隔离使用示例
```python
# 指挥官使用 - 通用映射规则示例
async def commander_workflow():
    # 1. 用户调用：使用ace mcp来开会：docs\features\F007-建立Commons库的治理机制-20250610\nexus万用插座\design\v1

    # 2. 系统自动识别项目上下文（基于MCP客户端ID）
    commander.set_project_context("mcp_client_dev_001")  # → 识别为xkongcloud项目

    # 3. 系统自动设置工作目录上下文（通用映射规则）
    work_dir = "docs\\features\\F007-建立Commons库的治理机制-20250610\\nexus万用插座\\design\\v1"
    commander.set_work_directory_context(work_dir)  # 自动识别工作性质

    # 4. 双层隔离自动生效（通用规则）
    db_conn = commander.get_sqlite_connection()  # → xkongcloud/databases/xkongcloud.db
    meeting_dir = commander.get_meeting_directory()  # → xkongcloud/meetings/docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/

    # 5. 文件操作自动隔离到正确的项目结构
    file = RemoteClientFactory.create_file("analysis.md", "mcp_client_dev_001")
    await file.write("分析结果...")  # 自动保存到项目Meeting目录
```

### 九宫格5号位项目选择界面集成
```html
<!-- 在现有九宫格5号位智能选择题区域集成项目选择 -->
<div id="project-selection" class="project-selection" style="background: #393B40; padding: 0.8rem; border-radius: 4px; margin-bottom: 1rem; display: none;">
    <div style="font-weight: bold; margin-bottom: 0.5rem; color: #FF9800;">🏢 项目选择：</div>
    <div style="margin-bottom: 0.8rem; font-size: 0.9rem;">
        检测到新的MCP客户端连接：<span id="client-id-display"></span><br>
        请选择对应的项目环境：
    </div>
    <div style="display: flex; flex-direction: column; gap: 0.4rem;">
        <button onclick="selectProject('xkongcloud', 'commons_governance', 'XKong云-Commons治理')"
                style="padding: 0.4rem; background: #2A2D30; color: #BBBBBB; border: 1px solid #3C3F41; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">
            🏗️ XKong云 - Commons治理
        </button>
        <button onclick="selectProject('enterprise_system', 'user_management', '企业系统-用户管理')"
                style="padding: 0.4rem; background: #2A2D30; color: #BBBBBB; border: 1px solid #3C3F41; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">
            🏢 企业系统 - 用户管理
        </button>
        <button onclick="selectProject('ai_platform', 'model_training', 'AI平台-模型训练')"
                style="padding: 0.4rem; background: #2A2D30; color: #BBBBBB; border: 1px solid #3C3F41; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">
            🤖 AI平台 - 模型训练
        </button>
        <button onclick="selectProject('custom', 'custom', '自定义项目')"
                style="padding: 0.4rem; background: #2A2D30; color: #BBBBBB; border: 1px solid #3C3F41; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">
            ⚙️ 自定义项目配置
        </button>
    </div>
</div>

<script>
// 项目选择功能集成到现有JavaScript
function selectProject(rootProject, subProject, projectName) {
    const clientId = document.getElementById('client-id-display').textContent;

    // 隐藏项目选择界面
    document.getElementById('project-selection').style.display = 'none';

    // 显示选择结果
    const processLog = document.getElementById('process-log');
    const selectionEntry = document.createElement('div');
    selectionEntry.innerHTML = `[${new Date().toLocaleTimeString('zh-CN', {hour12: false})}] 项目选择: ${clientId} → ${projectName}`;
    selectionEntry.style.color = '#4CAF50';
    processLog.appendChild(selectionEntry);
    processLog.scrollTop = processLog.scrollHeight;

    // 更新状态
    document.getElementById('current-status').textContent = `已选择项目：${projectName}`;

    // 发送到服务器
    socket.emit('project_selection', {
        client_id: clientId,
        root_project: rootProject,
        sub_project: subProject,
        project_name: projectName
    });

    console.log('项目选择:', clientId, '→', projectName);

    // 模拟指挥官响应
    setTimeout(() => {
        const responseEntry = document.createElement('div');
        responseEntry.innerHTML = `[${new Date().toLocaleTimeString('zh-CN', {hour12: false})}] INFO: 指挥官已切换到项目环境，开始执行任务...`;
        responseEntry.style.color = '#2196F3';
        processLog.appendChild(responseEntry);
        processLog.scrollTop = processLog.scrollHeight;

        document.getElementById('current-status').textContent = '项目环境已就绪，继续执行';
    }, 1000);
}

// 显示项目选择界面（当检测到新客户端时调用）
function showProjectSelection(clientId) {
    // 隐藏智能选择题
    document.getElementById('smart-question').style.display = 'none';

    // 显示项目选择
    document.getElementById('client-id-display').textContent = clientId;
    document.getElementById('project-selection').style.display = 'block';

    // 更新状态
    document.getElementById('current-status').textContent = '等待项目选择';

    // 添加日志
    const processLog = document.getElementById('process-log');
    const detectionEntry = document.createElement('div');
    detectionEntry.innerHTML = `[${new Date().toLocaleTimeString('zh-CN', {hour12: false})}] 检测到新客户端: ${clientId}，请选择项目环境`;
    detectionEntry.style.color = '#FF9800';
    processLog.appendChild(detectionEntry);
    processLog.scrollTop = processLog.scrollHeight;
}

// Socket.IO事件监听（新增项目选择相关事件）
socket.on('project_selection_request', (data) => {
    console.log('项目选择请求:', data);
    showProjectSelection(data.client_id);
});
</script>
```

### 完整项目隔离工作流程
```python
# 完整的项目隔离设置流程（通用规则）
async def complete_isolation_workflow():
    # 1. 用户调用：使用ace mcp来开会：docs\features\F007-建立Commons库的治理机制-20250610\nexus万用插座\design\v1

    # 2. 系统自动识别：
    #    - MCP客户端ID → 项目根目录（如：xkongcloud）
    #    - 工作目录路径 → Meeting路径（通用映射规则）
    #    - 工作性质 → 智能识别（nexus万用插座设计文档）

    # 3. 指挥官自动切换到项目隔离上下文：
    #    - 项目数据库：xkongcloud/databases/xkongcloud.db
    #    - Meeting目录：xkongcloud/meetings/docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/
    #    - 工作模式：根据工作性质自适应

    # 4. 开始执行具体任务，所有数据自动隔离到正确的项目结构

    # 通用映射规则确保：
    # - 任何用户工作目录都能正确映射到对应的Meeting结构
    # - 保持完整的目录层次关系
    # - 支持任意深度的目录嵌套
```

### 九宫格8号位目录输入界面
```html
<!-- 在九宫格8号位添加目录输入功能 -->
<div class="grid-area grid-area-8">
    <div class="area-title">工作目录设置 + 任务性质识别</div>
    <div class="area-content">
        <!-- 目录输入区域 -->
        <div id="directory-input-section" style="margin-bottom: 1rem;">
            <div style="font-weight: bold; margin-bottom: 0.5rem; color: #FF9800;">📁 工作目录：</div>
            <input type="text" id="work-directory-input"
                   placeholder="请输入工作目录路径，例如：docs\features\F007-..."
                   style="width: 100%; padding: 0.5rem; background: #2A2D30; color: #BBBBBB; border: 1px solid #3C3F41; border-radius: 4px; font-size: 0.8rem; margin-bottom: 0.5rem;">
            <button onclick="analyzeWorkDirectory()"
                    style="padding: 0.4rem 0.8rem; background: #0078D4; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">
                🔍 分析目录性质
            </button>
        </div>

        <!-- 目录性质识别结果 -->
        <div id="directory-analysis-result" style="background: #393B40; padding: 0.8rem; border-radius: 4px; margin-bottom: 1rem; display: none;">
            <div style="font-weight: bold; margin-bottom: 0.5rem; color: #4CAF50;">🎯 目录性质识别：</div>
            <div id="work-nature-display" style="margin-bottom: 0.5rem; font-size: 0.9rem;"></div>
            <div id="work-context-display" style="font-size: 0.8rem; color: #BBBBBB;"></div>
            <button onclick="confirmWorkDirectory()"
                    style="padding: 0.3rem 0.6rem; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem; margin-top: 0.5rem;">
                ✅ 确认工作环境
            </button>
        </div>

        <!-- 当前工作状态显示 -->
        <div class="status-item">
            <strong>当前项目：</strong><span id="current-project-display">未选择</span>
        </div>
        <div class="status-item">
            <strong>工作目录：</strong><span id="current-directory-display">未设置</span>
        </div>
        <div class="status-item">
            <strong>工作性质：</strong><span id="current-work-nature">未识别</span>
        </div>
        <div class="status-item">
            <strong>指挥官状态：</strong><span id="commander-ready-status">等待配置</span>
        </div>
    </div>
</div>

<script>
// 目录性质识别规则
const directoryPatterns = {
    'nexus万用插座': {
        pattern: /nexus万用插座|nexus.*插座/i,
        nature: 'nexus万用插座设计文档',
        workType: '四重会议工作',
        context: '通用接口设计和架构规划'
    },
    'commons治理': {
        pattern: /commons.*治理|治理.*commons/i,
        nature: 'Commons库治理机制',
        workType: '治理体系设计',
        context: '库管理和治理流程设计'
    },
    '四重会议': {
        pattern: /四重会议|four.*layer.*meeting/i,
        nature: '四重会议系统',
        workType: '会议系统开发',
        context: '多层会议架构和协调机制'
    },
    'V45设计': {
        pattern: /V45|v4\.5|version.*4\.5/i,
        nature: 'V4.5系统设计',
        workType: '系统架构设计',
        context: '新版本架构和功能设计'
    },
    '容错机制': {
        pattern: /容错|fault.*tolerance|双端.*持久化/i,
        nature: '容错机制设计',
        workType: '可靠性工程',
        context: '系统容错和恢复机制设计'
    }
};

function analyzeWorkDirectory() {
    const directory = document.getElementById('work-directory-input').value.trim();
    if (!directory) {
        alert('请输入工作目录路径');
        return;
    }

    // 分析目录性质
    let matchedPattern = null;
    for (const [key, pattern] of Object.entries(directoryPatterns)) {
        if (pattern.pattern.test(directory)) {
            matchedPattern = pattern;
            break;
        }
    }

    if (!matchedPattern) {
        // 默认识别
        matchedPattern = {
            nature: '通用项目工作',
            workType: '项目开发',
            context: '基于目录路径的通用工作'
        };
    }

    // 显示识别结果
    document.getElementById('work-nature-display').textContent =
        `${matchedPattern.nature} - ${matchedPattern.workType}`;
    document.getElementById('work-context-display').textContent =
        `工作上下文：${matchedPattern.context}`;
    document.getElementById('directory-analysis-result').style.display = 'block';

    // 更新全局状态
    window.currentWorkDirectory = directory;
    window.currentWorkNature = matchedPattern;

    console.log('目录分析完成:', directory, matchedPattern);
}

function confirmWorkDirectory() {
    if (!window.currentWorkDirectory || !window.currentWorkNature) {
        alert('请先分析目录性质');
        return;
    }

    // 更新显示
    document.getElementById('current-directory-display').textContent = window.currentWorkDirectory;
    document.getElementById('current-work-nature').textContent =
        `${window.currentWorkNature.nature} - ${window.currentWorkNature.workType}`;
    document.getElementById('commander-ready-status').textContent = '环境就绪';
    document.getElementById('commander-ready-status').style.color = '#4CAF50';

    // 隐藏分析结果
    document.getElementById('directory-analysis-result').style.display = 'none';

    // 发送到服务器
    socket.emit('work_directory_setup', {
        directory: window.currentWorkDirectory,
        nature: window.currentWorkNature.nature,
        workType: window.currentWorkNature.workType,
        context: window.currentWorkNature.context
    });

    // 更新5号位状态
    const processLog = document.getElementById('process-log');
    const setupEntry = document.createElement('div');
    setupEntry.innerHTML = `[${new Date().toLocaleTimeString('zh-CN', {hour12: false})}] 工作环境配置完成: ${window.currentWorkNature.nature}`;
    setupEntry.style.color = '#4CAF50';
    processLog.appendChild(setupEntry);
    processLog.scrollTop = processLog.scrollHeight;

    document.getElementById('current-status').textContent = '工作环境已就绪，可以开始任务';

    console.log('工作目录确认完成');
}

// 项目选择完成后的回调（更新selectProject函数）
function selectProject(rootProject, subProject, projectName) {
    const clientId = document.getElementById('client-id-display').textContent;

    // 隐藏项目选择界面
    document.getElementById('project-selection').style.display = 'none';

    // 显示选择结果
    const processLog = document.getElementById('process-log');
    const selectionEntry = document.createElement('div');
    selectionEntry.innerHTML = `[${new Date().toLocaleTimeString('zh-CN', {hour12: false})}] 项目选择: ${clientId} → ${projectName}`;
    selectionEntry.style.color = '#4CAF50';
    processLog.appendChild(selectionEntry);
    processLog.scrollTop = processLog.scrollHeight;

    // 更新8号位项目显示
    document.getElementById('current-project-display').textContent = projectName;

    // 显示目录输入提示
    const directoryPromptEntry = document.createElement('div');
    directoryPromptEntry.innerHTML = `[${new Date().toLocaleTimeString('zh-CN', {hour12: false})}] 请在8号位输入工作目录路径`;
    directoryPromptEntry.style.color = '#FF9800';
    processLog.appendChild(directoryPromptEntry);
    processLog.scrollTop = processLog.scrollHeight;

    // 更新状态
    document.getElementById('current-status').textContent = `项目已选择：${projectName}，请设置工作目录`;

    // 发送到服务器
    socket.emit('project_selection', {
        client_id: clientId,
        root_project: rootProject,
        sub_project: subProject,
        project_name: projectName
    });

    console.log('项目选择:', clientId, '→', projectName);
}
</script>
```

## 🎯 架构优势总结

| 特性 | 实现方式 | 代码改动 | 效果 |
|------|----------|----------|------|
| **项目映射** | MCP客户端ID自动映射 | +80行 | 零配置使用 |
| **九宫格集成** | 5号位项目选择 + 8号位目录输入 | +120行 | 完美用户体验 |
| **目录识别** | 智能模式识别 + 指挥官行为适配 | +80行 | 智能工作模式 |
| **SQLite隔离** | 基于根项目的动态数据库路径 | +3行 | 根项目级隔离 |
| **Meeting隔离** | 基于工作目录的动态Meeting路径 | +3行 | 工作目录级隔离 |
| **文件隔离** | 自动路径前缀 | +20行 | 透明隔离 |
| **总改动** | **智能设计** | **306行** | **完美隔离+智能识别** |

### 🌟 九宫格5号位集成优势

| 集成特点 | 现有功能 | 项目选择功能 | 完美融合 |
|---------|----------|--------------|----------|
| **界面位置** | 智能选择题区域 | 项目选择界面 | ✅ 同一区域复用 |
| **交互模式** | 按钮选择 + 日志反馈 | 按钮选择 + 日志反馈 | ✅ 交互模式一致 |
| **状态更新** | 实时状态显示 | 实时项目状态 | ✅ 状态机制一致 |
| **Socket通信** | human_choice事件 | project_selection事件 | ✅ 通信模式一致 |
| **用户体验** | 智能决策辅助 | 项目环境选择 | ✅ 体验逻辑一致 |

## 🔧 基于现有统一配置管理架构的改造方案

### 现有配置管理架构分析
```python
# 现有统一配置管理体系：
tools/ace/src/configuration_center/simple_configuration_center.py  # 配置中心核心
tools/ace/src/configuration_center/configuration_adapter.py        # 兼容性适配器
tools/ace/src/common_config_loader.py                             # 统一配置加载器
config/common_config.json                                         # 主配置文件

# 配置管理特点：
# 1. 单例模式：同一配置文件只创建一个实例
# 2. 点号分隔：支持嵌套配置访问（如：database_config.sqlite_path）
# 3. 兼容性适配：支持多种现有接口
# 4. 统一加载：所有组件通过CommonConfigLoader访问配置
```

### 项目隔离配置集成方案
```python
# 在现有配置文件中添加项目隔离配置
config/common_config.json 新增：
{
  "project_isolation_config": {
    "enabled": true,
    "projects_root": "tools/ace/src/projects",
    "default_project": "default",
    "client_mappings": {
      "mcp_client_dev_001": {
        "project_name": "xkongcloud",
        "client_name": "开发环境-XKong云"
      },
      "mcp_client_prod_002": {
        "project_name": "enterprise_system",
        "client_name": "生产环境-企业系统"
      }
    },
    "work_nature_patterns": {
      "nexus万用插座": {
        "pattern": "nexus万用插座|nexus.*插座",
        "nature": "nexus万用插座设计文档",
        "work_type": "四重会议工作"
      },
      "commons治理": {
        "pattern": "commons.*治理|治理.*commons",
        "nature": "Commons库治理机制",
        "work_type": "治理体系设计"
      }
    }
  }
}
```

### 需要改造的核心文件
```python
# 1. 项目上下文管理器（新增）
tools/ace/src/four_layer_meeting_system/project_context_manager.py
# 集成现有配置管理系统，通过CommonConfigLoader获取项目配置

# 2. 配置系统扩展（修改）
config/common_config.json
# 添加project_isolation_config配置段

# 3. 指挥官集成改造（修改）
tools/ace/src/python_host/python_host_core_engine.py
# 通过现有配置系统获取项目隔离配置

# 4. Web服务器集成改造（修改）
tools/ace/src/four_layer_meeting_server/server_launcher.py
# 使用现有配置管理获取项目配置

# 5. MCP客户端集成改造（修改）
tools/ace/src/four_layer_meeting_system/mcp_server/simple_ascii_launcher.py
# 通过CommonConfigLoader获取项目隔离配置
```

### 配置文件扩展示例
```json
// config/common_config.json 新增配置段
{
  "project_isolation_config": {
    "enabled": true,
    "projects_root": "tools/ace/src/projects",
    "default_project": "default",
    "client_mappings": {
      "mcp_client_dev_001": {
        "project_name": "xkongcloud",
        "client_name": "开发环境-XKong云"
      },
      "mcp_client_prod_002": {
        "project_name": "enterprise_system",
        "client_name": "生产环境-企业系统"
      }
    },
    "work_nature_patterns": {
      "nexus万用插座": {
        "pattern": "nexus万用插座|nexus.*插座",
        "nature": "nexus万用插座设计文档",
        "work_type": "四重会议工作"
      },
      "commons治理": {
        "pattern": "commons.*治理|治理.*commons",
        "nature": "Commons库治理机制",
        "work_type": "治理体系设计"
      },
      "四重会议": {
        "pattern": "四重会议|four.*layer",
        "nature": "四重会议系统",
        "work_type": "会议系统开发"
      },
      "V45设计": {
        "pattern": "V45|v4\\.5|version.*4\\.5",
        "nature": "V4.5系统设计",
        "work_type": "系统架构设计"
      },
      "容错机制": {
        "pattern": "容错|fault.*tolerance|双端.*持久化",
        "nature": "容错机制设计",
        "work_type": "可靠性工程"
      }
    },
    "migration_config": {
      "auto_migrate_on_startup": true,
      "backup_before_migration": true,
      "preserve_original_paths": true
    }
  }
}
```

### 数据迁移策略
```python
# 基于配置的数据迁移策略
migration_plan = {
    "database_migration": {
        "source": "data/v4_panoramic_model.db",  # 从配置获取：database_config.sqlite_path
        "target_pattern": "{projects_root}/{default_project}/databases/{default_project}.db",
        "strategy": "复制现有数据库到默认项目，保持向后兼容"
    },
    "meeting_migration": {
        "source": "Meeting/",  # 从配置获取：v4_system_config.algorithm_thinking_log_config.log_directory
        "target_pattern": "{projects_root}/{default_project}/meetings/",
        "strategy": "迁移现有Meeting目录到默认项目，保持现有结构"
    },
    "config_integration": {
        "strategy": "通过现有配置管理系统集成项目隔离配置，无需修改现有配置加载逻辑"
    }
}
```

## 🚀 实施优先级

### P0（立即实施，306行代码）
1. **ProjectContextManager核心类**（80行）- 基于tools/ace/src/projects/结构
2. **指挥官项目上下文集成**（80行）- 修改python_host_core_engine.py
3. **九宫格5号位项目选择界面**（60行）- 集成到server_launcher.py
4. **九宫格8号位目录输入界面**（60行）- 智能性质识别
5. **MCP客户端项目隔离支持**（26行）- 集成到simple_ascii_launcher.py

### P1（验证阶段）
- 数据迁移脚本实施
- 多项目隔离测试
- 配置系统改造
- 向后兼容性验证

### P2（优化阶段）
- 项目配置持久化
- 工作目录历史记录
- 智能目录性质学习
- 高级项目管理功能

## 🎯 完美的九宫格集成方案

### ✅ **与现有界面无缝融合**
- **复用5号位智能选择题区域**：项目选择与智能选择题使用相同的界面区域
- **保持交互模式一致**：按钮选择 + 实时日志 + 状态更新
- **Socket通信模式一致**：新增project_selection事件，与human_choice事件并行
- **用户体验逻辑一致**：都是辅助指挥官做出正确决策

### ✅ **零学习成本**
- **界面风格完全一致**：使用相同的CSS样式和布局
- **操作流程完全一致**：点击按钮 → 日志反馈 → 状态更新
- **视觉效果完全一致**：相同的颜色、字体、动画效果

## 📋 完整实施计划

### 🎯 **实施目标确认**
基于现有目录结构（tools/ace/src/），实现完整的项目级隔离机制，支持：
- 用户调用：`使用ace mcp来开会：docs\features\F007-nexus万用插座\design\v1`
- 自动映射：`tools/ace/src/projects/xkongcloud/meetings/docs/features/F007-nexus万用插座/design/v1/`
- 项目隔离：每个项目独立的数据库和Meeting目录结构

### 📝 **详细实施步骤**

#### **步骤1：创建项目目录结构**
```bash
# 在 tools/ace/src/ 下创建项目隔离目录结构
mkdir -p tools/ace/src/projects/xkongcloud/databases
mkdir -p tools/ace/src/projects/xkongcloud/meetings
mkdir -p tools/ace/src/projects/enterprise_system/databases
mkdir -p tools/ace/src/projects/enterprise_system/meetings
mkdir -p tools/ace/src/projects/default/databases
mkdir -p tools/ace/src/projects/default/meetings
```

#### **步骤2：数据迁移**
```python
# 迁移现有数据到默认项目
cp tools/ace/src/data/v4_panoramic_model.db tools/ace/src/projects/default/databases/default.db
cp -r tools/ace/src/Meeting/* tools/ace/src/projects/default/meetings/
```

#### **步骤3：核心代码实施**
1. **ProjectContextManager.py**（80行）- 项目上下文管理
2. **python_host_core_engine.py改造**（80行）- 指挥官项目支持
3. **server_launcher.py改造**（120行）- Web界面集成
4. **simple_ascii_launcher.py改造**（26行）- MCP客户端集成

#### **步骤4：配置系统改造**
- 更新配置文件支持项目隔离路径
- 添加项目映射配置
- 保持向后兼容性

#### **步骤5：测试验证**
- 多项目隔离功能测试
- 向后兼容性测试
- 性能影响评估

### 🚀 **预期实施效果**
- ✅ 完全的项目级物理隔离
- ✅ 通用的工作目录映射规则
- ✅ 智能的工作性质识别
- ✅ 无缝的用户体验集成
- ✅ 向后兼容现有系统

**结论**：这是一个真正的顶级架构师设计 - 基于现有目录结构，智能识别，完美隔离，无缝集成，自适应工作模式！

## 🔗 与TODO2第10步Meeting目录隔离的完美集成

### ✅ **架构对接点**
- **V4.5项目隔离机制**：提供项目上下文和工作目录识别
- **Meeting目录ProjectIsolationManager**：接收隔离配置，执行Meeting目录隔离
- **双向同步**：项目选择和目录设置自动同步到Meeting目录隔离管理器

### ✅ **隔离命名空间统一**
```
isolation_namespace格式：{root_project}::{work_directory_path_segments}
示例：xkongcloud::docs::features::F007-建立Commons库的治理机制-20250610::nexus万用插座::design::v1
```

### ✅ **Meeting目录路径标准化**
- **路径格式**：`Meeting/{root_project}/{key_segment}/`
- **示例路径**：`Meeting/xkongcloud/F007-nexus万用插座/`
- **与TODO2第10步完全兼容**：使用相同的Meeting目录结构和隔离逻辑

## 🎯 与Meeting目录ProjectIsolationManager完整对接

### ✅ **Meeting目录隔离对接流程**
1. **客户端连接** → 九宫格5号位显示项目选择
2. **选择项目** → SQLite隔离生效，同步到ProjectIsolationManager
3. **输入目录** → 系统智能识别工作性质，生成Meeting隔离路径
4. **确认环境** → 指挥官自动适配工作模式，Meeting目录隔离完成
5. **开始工作** → 双层隔离生效：SQLite + Meeting目录

### ✅ **ProjectIsolationManager对接配置**
```python
# 指挥官提供给Meeting目录的隔离配置
isolation_config = {
    "root_project": "xkongcloud",
    "work_directory": "docs\\features\\F007-建立Commons库的治理机制-20250610\\nexus万用插座\\design\\v1",
    "meeting_path": "Meeting/xkongcloud/F007-nexus万用插座/",
    "sqlite_db": "databases/xkongcloud.db",
    "work_nature": "nexus万用插座设计文档",
    "work_type": "四重会议工作",
    "isolation_namespace": "xkongcloud::docs::features::F007-建立Commons库的治理机制-20250610::nexus万用插座::design::v1"
}

# Meeting目录ProjectIsolationManager使用此配置进行隔离
meeting_isolation_manager.update_project_namespace(isolation_config)
```

### ✅ **系统智能识别能力**
- **nexus万用插座** → 通用接口设计模式
- **Commons治理** → 治理体系设计模式
- **四重会议** → 会议协调机制模式
- **V4.5设计** → 系统架构设计模式
- **容错机制** → 可靠性工程模式

### ✅ **指挥官自适应行为**
- **算法组合自动调整**：根据工作性质选择最佳算法
- **验证级别自动设置**：critical/high/medium/low
- **文档风格自动适配**：architectural/governance/system/technical/reliability
- **工作焦点自动聚焦**：精准定位工作重点
