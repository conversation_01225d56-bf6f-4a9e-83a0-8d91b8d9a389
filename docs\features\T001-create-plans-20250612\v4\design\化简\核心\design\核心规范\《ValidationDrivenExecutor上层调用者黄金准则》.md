# 《ValidationDrivenExecutor 上层调用者黄金准则》

## 引言：理念与哲学

本准则旨在为 `ValidationDrivenExecutor` 的上层调用者提供一套清晰、健壮、高效的最佳实践。遵循本准则，将帮助您最大化AI的效能，同时将风险控制在最低水平。

### 核心思想：GIGO (Garbage In, Garbage Out / 垃圾进，垃圾出)

您必须深刻理解，底层执行器是一个忠实但“盲目”的仆人。它不会猜测您的意图，也不会美化您的输入。**您输入的质量，直接决定了输出的质量。**

### 责任边界：上层负责“思考”，底层负责“执行”

- **上层调用者 (您)**: 您的核心责任是**清晰地思考**和**精确地翻译**。您必须将复杂的业务需求，转化为机器可以理解的、无歧义的指令。**所有智能都必须体现在上层。**
- **底层执行器 (本系统)**: 它的核心责任是**忠实地执行**和**可靠地断言**。它会无条件地执行您提供的、格式正确的指令，并报告结果。

---

## 第一章：正文 (`original_content`) 编写准则

`original_content` 是您与AI进行自然语言交互的核心区域，它负责**启发和引导**AI。

### 准则1.1：明确核心任务

正文应清晰、直接地描述您希望AI完成的**核心目标**。

- **正确示例**:
  ```
  "请为XKongCloud Commons Nexus项目创建一个@Plugin注解，用于微内核的插件发现。"
  ```
- **错误示例**:
  ```
  "我需要一个注解。" (过于模糊，缺少上下文和目的)
  ```

### 准则1.2：将“软引导”融入正文

对于那些关乎代码风格、质量、最佳实践但没有绝对对错的“软性”要求，应将其作为“友好建议”融入正文中，而不是作为硬性约束。

- **正确示例 (作为软引导)**:
  ```json
  "original_content": "请创建一个Java的Plugin注解。\n\n**编码风格与最佳实践 (软引导)**:\n- 请确保代码可读性高，并为所有公共成员添加完整的JavaDoc注释。\n- 一个高质量的注解定义，其代码长度通常建议保持在50行以内，以确保其聚焦和简洁。"
  ```
  **分析**: 这样做是告诉AI：“我信任你的专业判断，这是我们推荐的风格，请尽力遵守。” AI会将其作为提升代码质量的**启发**，但不会因为生成了51行代码而导致硬性失败。

- **错误示例 (作为硬约束)**:
  ```json
  "constraints": {
    "length_rule": "代码行数必须小于等于50行"
  }
  ```
  **分析**: 这样做会将一个“品味”问题上升为“对错”问题。AI的输出长度天生具有弹性，用刚性的算法去验证一个弹性的、非核心的指标，会大大增加流程的脆弱性，导致不必要的失败。

---

## 第二章：硬约束 (`constraints` & `guardrails`) 定义的艺术

`constraints` 和 `guardrails` 是系统的“安全带”和“法律”，负责**约束和验证**AI的行为。这里的规则必须是**非黑即白、不容置疑**的。

### 准则2.1：逻辑一致性是最高圣经

`constraints` 只能包含**要求性**规则 (必须做什么)，`guardrails` 只能包含**禁止性**规则 (不能做什么)。

- **正确示例**:
  ```json
  "constraints": { "feature": "必须实现登录功能" },
  "guardrails": { "security": "不能使用硬编码密码" }
  ```
- **错误示例 (INTENT_MISMATCH)**:
  ```json
  "constraints": { "security": "确保没有硬编码密码" }
  ```
  **分析**: “没有硬编码密码”是一个**禁止性**的描述，但它被错误地放在了**要求性**的`constraints`区域。这将100%被系统的`INTENT_MISMATCH`审计机制拒绝。

### 准则2.2：明确性优先原则 (The Principle of Clarity First)

在定义规则时，请选择能够**最明确、最简洁地定义“成功状态”**的那一方。

#### 场景一：当“允许”的集合有限时，使用 `constraints`

- **需求**: 文件必须生成在路径 `A/B/C` 下。
- **分析**: “正确”的路径只有1个，而“错误”的路径有无限个。
- **正确做法 (使用 `constraints`)**:
  ```json
  "constraints": { "path_rule": "文件路径必须是 A/B/C" }
  ```
- **错误做法 (使用 `guardrails`)**:
  ```json
  "guardrails": { "path_rule": "文件路径不能是 A/B/D, 不能是 X/Y/Z, ..." }
  ```
  **分析**: 无法穷举所有错误路径，规则无效且愚蠢。

#### 场景二：当“禁止”的集合有限时，使用 `guardrails`

- **需求**: 代码中不能使用`eval()`或`exec()`这两个危险函数。
- **分析**: “错误”的函数只有2个，而“正确”的函数有成千上万个。
- **正确做法 (使用 `guardrails`)**:
  ```json
  "guardrails": { "security_rule": "不能使用 eval() 或 exec() 函数" }
  ```
- **错误做法 (使用 `constraints`)**:
  ```json
  "constraints": { "security_rule": "只能使用函数A, 只能使用函数B, ..." }
  ```
  **分析**: 无法穷举所有安全函数，规则无效。

### 准则2.3：规则的原子性与可溯源性

尽量让每个规则只描述一件事情，这能帮助AI更精确地溯源，降低`KEYWORD_NOT_FOUND`的风险。

- **错误示例 (多个规则混写)**:
  ```json
  "constraints": { "rule": "必须实现分页，每页10条，并按时间倒序排列" }
  ```
- **正确示例 (拆分成原子规则)**:
  ```json
  "constraints": {
    "pagination": "必须实现分页功能",
    "page_size": "每页必须为10条",
    "sorting": "必须按时间倒序排列"
  }
  ```

---

## 第三章：上下文 (`context`) 的正确使用姿势

`context` 负责提供必要的**背景事实**，它不应包含任何指令或要求。

### 准则3.1：只提供“背景事实”，而非“指令”

- **正确示例**:
  ```json
  "context": {
    "项目ID": "F007",
    "技术栈": "Java 21 + Spring Boot 3.4.5"
  }
  ```
- **错误示例**:
  ```json
  "context": {
    "style_guide": "代码风格要好，请使用驼峰命名法"
  }
  ```
  **分析**: 风格要求是“软引导”，应放在`original_content`中。`context`只应包含客观信息。

### 准则3.2：为“局部任务”提供必要支撑

如果您的任务是修改或补充现有内容，您**有责任**在`context`中提供足够的原始上下文。

- **场景**: 为一个已有的类增加一个方法。
- **正确示例**:
  ```json
  "original_content": "请为下面的 MyClass 类增加一个名为 'calculateTotal' 的公共方法。",
  "context": {
    "original_class_code": "public class MyClass { ... (完整的原始类代码) ... }"
  }
  ```
- **错误示例**:
  ```json
  "original_content": "请为一个名为 MyClass 的类增加一个 'calculateTotal' 方法。"
  // context 为空或不提供原始代码
  ```
  **分析**: 缺少原始代码，AI无法知道新方法应该放在哪里、是否会与现有成员冲突，也使得后续的AST验证等无法进行。

---

## 附录：一个黄金标准的请求示例

这是一个遵循了所有准则的、高质量的请求示例。

```json
{
  "original_content": "请为XKongCloud的Nexus微内核框架，创建一个核心的@Plugin注解。\n\n**核心功能**:\n该注解是插件被内核识别和加载的唯一入口，因此必须包含id, name, version, description四个核心元数据字段，以支持插件的生命周期管理。\n\n**编码风格与最佳实践 (软引导)**:\n- 请为所有公开的元数据字段，编写清晰、完整的JavaDoc注释，解释其用途。\n- 作为框架的核心API，请确保注解定义本身高度简洁，建议总行数不要超过60行。",

  "constraints": {
    "file_path": "C:\\ExchangeWorks\\xkong\\xkongcloud\\xkongcloud-commons\\xkongcloud-commons-nexus\\src\\main\\java\\org\\xkong\\cloud\\commons\\nexus\\api\\Plugin.java",
    "package_declaration": "包路径必须是 org.xkong.cloud.commons.nexus.api",
    "dependency_policy": "只能依赖标准的Java注解API (java.lang.annotation)"
  },

  "guardrails": {
    "external_dependencies": "不能引入除java.lang.annotation之外的任何外部依赖",
    "complex_logic": "注解定义中不能包含任何复杂的逻辑或默认实现"
  },
  
  "context": {
    "project_name": "XKongCloud Commons Nexus",
    "project_id": "F007-NEXUS",
    "target_jdk": "Java 21"
  }
}
