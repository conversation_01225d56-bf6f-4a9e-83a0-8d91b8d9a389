# 项目上下文传递指令

## 背景说明

当前对话已达到较长长度，需要开启新对话继续项目开发。新对话中的AI助手将无法访问本次对话的历史记录，因此需要完整的项目上下文传递。

## 核心任务

API管理系统的质量评估显示问题修复 - 实现DRY原则下的三者一致性：录入测试质量、生产使用质量、跟踪记录质量。

## 1. 项目概览

### 项目名称
API管理系统质量评估一致性修复

### 项目目标
解决前端API测试显示硬编码质量分数的问题，实现DRY原则下的三者一致性：
1. **录入测试的质量评估** - 前端API管理界面测试时显示的质量
2. **生产使用的质量评估** - TaskBasedAIServiceManager在实际AI请求中的质量评估  
3. **跟踪记录的质量评估** - AIRequestTracker持续跟踪的质量指标

### 当前阶段
问题诊断和架构分析阶段，已识别出根本问题：**临时API的边界抽象缺失**

### 技术栈
- **后端**: Python Flask
- **前端**: JavaScript + HTML
- **数据库**: SQLite (api_account_database)
- **架构模式**: DRY原则、单例模式、配置驱动

### 主要功能模块
- **QualityAssuranceGuard**: 质量保障护栏系统
- **TaskBasedAIServiceManager**: 基于任务的AI服务管理器
- **APIClassificationManager**: API分类管理器
- **CategoryBasedAPISelector**: 基于类别的API选择器

## 2. 文件清单

### 已修改的文件

#### A. `tools/ace/src/api_management/core/api_classification_manager.py`
**修改内容**: 修复模块导入失败问题
- **第657-668行**: 修改`_get_apis_by_standard_name`方法，使用已初始化的数据库连接避免重复导入
- **问题**: 解决了`❌ 标准名称查询失败: No module named 'tools.ace.src.api_management'`错误
- **状态**: 部分有效，解决了导入问题但没有解决根本的边界抽象问题

#### B. `tools/ace/src/configuration_center/web_api.py`  
**修改内容**: 修复前后端专家类别映射不一致
- **第1507行**: `return "代码生成和逻辑优化"` → `return "代码生成专家"`
- **第1514行**: 默认返回值同样修改
- **问题**: 解决了前端返回的类别名与后端配置不匹配的问题
- **状态**: 有效且必要的修改

### 参考文件

#### 设计文档
- `docs/features/T001-create-plans-20250612/design/fork/四重会议/todo2/promte/12-api管理/detail/02-核心业务功能验证系统-v2.md`
  - 定义了质量评估的标准和基准值
  - 功能完整性: 87.0%, 性能: 94.5%, 稳定性: 98.2%, thinking质量: 100.0%

#### 核心业务文件
- `tools/ace/src/api_management/core/quality_assurance_guard.py`
  - 质量保障护栏系统，负责质量评估和降级处理
- `tools/ace/src/api_management/core/task_based_ai_service_manager.py`  
  - 生产级AI服务管理器，包含完整的质量评估流程
- `tools/ace/src/configuration_center/config/common_config.json`
  - API类别映射配置：架构专家、代码生成专家、逻辑处理专家等

## 3. 当前进度状态

### 已完成的功能点
1. ✅ **问题诊断完成**: 识别出前端显示硬编码质量分数的根本原因
2. ✅ **架构分析完成**: 理解了DRY原则下三者一致性的要求
3. ✅ **部分修复完成**: 修复了模块导入和类别映射不一致问题

### 正在进行的任务
**核心问题**: 临时API的边界抽象缺失

**当前状态**:
```
录入的临时API → 前端简单测试 → 硬编码的质量分数 ❌
数据库中的永久API → 完整的质量评估流程 → 真实的质量分数 ✅
```

### 待解决的问题
1. **临时API注册机制缺失**: 录入测试的API无法被系统识别和评估
2. **边界抽象不统一**: 两种API来源（临时vs永久）没有统一的抽象层
3. **质量评估流程不一致**: 临时API使用简化测试，永久API使用完整评估

### 下一步计划
创建**临时API注册和抽象机制**，实现：
```
录入的临时API → 临时注册机制 → 统一的API抽象层 → 相同的质量评估流程
数据库中的永久API → 直接访问 → 统一的API抽象层 → 相同的质量评估流程
```

## 4. 关键决策记录

### 重要的技术选型决策
1. **DRY原则强制执行**: 三个质量评估必须完全一致
2. **配置驱动架构**: 所有角色映射通过配置文件管理
3. **边界抽象设计**: 需要统一处理临时API和永久API

### 架构设计要点
1. **质量评估的三个层次**:
   - API选择质量验证（有真实差异化评估）
   - 结果质量保障检查（被强制降级到0.72）  
   - 前端显示的质量评估（显示硬编码分数）

2. **降级机制的作用**:
   - 质量保护机制：当AI响应质量不达标时强制降级
   - 统一标准：所有不达标响应都降级到相同分数
   - 风险控制：防止低质量响应被误认为高质量

### 需要特别注意的约束条件
1. **不能修改底层质量评估方法**: 会影响生产系统和跟踪系统
2. **必须保持前后端配置一致性**: 类别名称必须完全匹配
3. **必须基于架构理解进行修改**: 不能盲目修改代码

## 5. 环境和依赖

### 开发环境配置
- **工作目录**: `c:\ExchangeWorks\xkong\xkongcloud`
- **启动命令**: `cd tools/ace && python src/configuration_center/app.py`
- **端口**: 25526

### 关键依赖
- **数据库**: `tools/ace/src/api_management/sqlite_storage/api_account_database.py`
- **配置管理**: `tools/ace/src/configuration_center/simple_configuration_center.py`
- **质量评估**: `tools/ace/src/api_management/core/quality_assurance_guard.py`

### 数据库结构
- **api_model_mappings表**: 存储API与模型的映射关系
- **api_performance表**: 存储API性能数据
- **加密存储机制**: 所有API密钥都经过加密存储

## 6. 关键日志信息

### 问题表现
```
前端显示：
🔧 功能性: 50.0%  ← 硬编码值
⚡ 性能: 100.0%   ← 硬编码值  
🛡️ 稳定性: 80.0%  ← 硬编码值
🧠 思维质量: 64.8% ← 硬编码值

后端实际评估：
DeepSeek-V3: 功能完整性=0.784, 综合=0.894
DeepSeek-R1: 功能完整性=0.976, 综合=0.962
```

### 错误信息
```
❌ 标准名称查询失败: No module named 'tools.ace.src.api_management'
📊 类别 架构专家 共找到 0 个实际API
⚠️ 类别 架构专家 下没有可用的实际API，使用第一个标准名称
```

## 7. 立即行动指南

新对话中的AI助手应该：

1. **首先验证修改效果**: 重启服务器测试已修改的两个文件是否解决了基础问题
2. **重点关注**: 创建临时API注册机制，实现统一的API抽象层
3. **避免**: 修改底层质量评估方法，这会破坏DRY原则
4. **目标**: 让录入测试的API能够使用与永久API相同的质量评估流程

**成功标准**: 前端显示的质量分数应该与后端实际评估的分数一致，且不同模型应该显示不同的差异化分数。
