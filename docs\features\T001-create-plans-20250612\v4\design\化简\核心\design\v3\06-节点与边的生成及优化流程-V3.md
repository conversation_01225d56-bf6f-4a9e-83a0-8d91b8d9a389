# V4.3方案：节点与边的生成及优化流程 (图谱精炼)

## 1. 文档信息

- **文档版本**: V4.3
- **创建日期**: 2025-08-13
- **目的**: 详细阐述V4.3治理引擎中，如何从原始设计文档和代码中提取实体（节点）和关系（边），并对其进行标准化、去重和优化的确定性算法流程，以构建高质量的宏观语义地图和微观图。

## 2. 节点与边的核心概念

在V4.3方案中，所有架构信息和代码信息都将被抽象为图谱中的**节点 (Nodes)** 和**边 (Edges)**。

-   **节点**: 代表独立的实体，如：
    *   `AtomicConstraint` (原子约束)
    *   模块 (Module)
    *   接口 (Interface)
    *   类 (Class)
    *   方法 (Method)
    *   配置项 (Configuration Item)
    *   数据结构 (Data Structure)
    *   设计文档 (Design Document)
-   **边**: 代表实体之间的关系，如：
    *   `CONSTRAINS` (约束)
    *   `DEPENDS_ON` (依赖于)
    *   `IMPLEMENTS` (实现)
    *   `CALLS` (调用)
    *   `INHERITS` (继承)
    *   `CONTAINS` (包含)
    *   `REFERENCES` (引用)
    *   `DEFINED_IN` (定义于)

## 3. 节点与边的生成流程概览

节点与边的生成是一个多阶段的确定性算法过程，旨在从非结构化和半结构化数据中提取结构化知识。

```mermaid
graph TD
    A[1. 原始数据输入<br/>(设计文档, 代码仓)] --> B[2. 文档实体识别<br/>(AST标记: AC/伪代码/Mermaid)]
    A --> C[2'. 代码实体获取<br/>(Serena: LSP 符号/引用/实现)]
    B & C --> D[3. 实体标准化与去重<br/>(命名解析, ID生成: symbolId/file_path)]
    D --> E[4. 关系识别<br/>(Doc: 伪代码/Mermaid; Code: Serena 引用/实现)]
    E --> F[5. 关系标准化与去重]
    F --> G[6. 图谱构建<br/>(节点与边入库)]
    G --> H[7. 图谱优化与精炼<br/>(冗余移除, 属性丰富, 证据挂载)]
    H --> I[8. 高质量知识图谱输出]
```

## 4. 各阶段详细描述

### 4.1. 阶段1: 原始数据输入

-   **输入**:
    *   设计文档（Markdown格式，包含`AtomicConstraint` JSON、伪代码、Mermaid图）。
    *   源代码文件（Java, Python, Go等）。
-   **来源**: 文件系统扫描。

### 4.2. 阶段2: 初始实体识别

此阶段的目标是识别出潜在的节点（严格遵循文档AST标记与 Serena 代码事实）。

-   **设计文档**:
    *   **`AtomicConstraint`**: 仅从 `<!-- ATOMIC_CONSTRAINT_START --> ... END -->` 标记的 JSON 代码块提取，每个对象即为一个 `AtomicConstraint` 节点。
    *   **模块/接口**: 仅从明确章节/清单或 Mermaid 图中的节点生成；名称必须可在文档中回指，禁止依赖 NLP 模糊识别。
    *   **伪代码提示实体（可选）**: 从 `pseudocode` 代码块提取过程名等，作为 `DocHint` 辅助节点用于后续断言，不参与强制映射。
-   **源代码（Serena/LSP）**:
    *   **类/接口/方法/字段**: 通过 Serena 工具（如 `get_symbols_overview`, `find_symbol`, `get_outline`）获取，使用 `symbolId` + `file_path` 标识。
    *   **实现/继承**: 通过 `find_implementations` 与类型信息识别 `IMPLEMENTS`/`EXTENDS` 相关实体。
    *   **配置项**: 配置文件（`.properties`, `.yaml`）采用静态扫描生成 `ConfigKey` 节点，证据类型标注为 `CONFIG_VALUE`。
-   **输出**: 标准化的实体列表，含类型、ID（或 `symbolId`）、来源文件/行号与证据摘要。

### 4.3. 阶段3: 实体标准化与去重 (确定性算法)

此阶段旨在为每个实体生成唯一的、标准化的ID，并消除重复。

-   **操作**:
    *   **ID生成策略**:
        *   **`AtomicConstraint`**: 使用其JSON中定义的`id`字段作为唯一ID。
        *   **代码实体 (类、方法等)**: 采用“全限定名 + 版本号/哈希”的策略生成ID，例如 `com.xkong.project.UserService.createUser(String, int)@v1.0`。
        *   **设计实体 (模块、接口)**: 采用“文档路径 + 实体名称 + 版本号/哈希”的策略，确保唯一性。
    *   **命名解析与归一化**:
        *   处理命名变体（如“用户服务” vs “UserService”），通过预定义的映射表或模糊匹配算法进行归一化。
        *   将中文概念映射到英文技术术语（如果需要）。
    *   **去重**: 基于生成的标准化ID进行去重，确保每个逻辑实体在图谱中只有一个节点。
-   **输出**: 标准化、去重后的节点列表，每个节点包含唯一ID、类型、名称、原始来源信息等属性。

### 4.4. 阶段4: 关系识别

此阶段的目标是识别实体之间的连接（边）。

-   **设计文档**:
    *   **`AtomicConstraint`关系**:
        *   `parent_id`字段直接定义了`DERIVES_FROM`（派生自）关系。
        *   `target_entity`和`target_type`定义了`CONSTRAINS`（约束）关系。
    *   **伪代码**: 通过对伪代码进行控制流和数据流分析，识别出`CALLS`（调用）、`MODIFIES`（修改）等关系。
    *   **Mermaid图**: 直接从Mermaid的连接语法中提取`DEPENDS_ON`、`CONTAINS`等关系。
-   **源代码（Serena/LSP）**:
    *   **调用关系**: 通过 `find_referencing_symbols(method.symbolId)` 识别调用点，生成 `CALLS` 边。
    *   **继承/实现**: 通过类型信息与 `find_implementations` 生成 `EXTENDS`/`IMPLEMENTS` 边。
    *   **引用关系**: 通过符号引用生成 `REFERENCES`/`READS`/`WRITES` 边（据引用类型细分）。
    *   **依赖管理**: 解析 `pom.xml`、`requirements.txt` 等构建文件，识别模块间 `DEPENDS_ON` 关系（非 Serena）。
-   **输出**: 包含源节点ID、目标节点ID、关系类型、来源信息（文件、行号）的初步关系列表。

### 4.5. 阶段5: 关系标准化与去重 (确定性算法)

-   **操作**:
    *   **关系类型归一化**: 将不同来源（设计、代码）识别出的语义相似关系归一化为统一的边类型（如“调用”可能在伪代码中是“invoke”，在代码中是“call”，统一为`CALLS`）。
    *   **去重**: 基于源节点ID、目标节点ID和关系类型进行去重。
-   **输出**: 标准化、去重后的边列表，每条边包含源节点ID、目标节点ID、关系类型、以及其他属性。

### 4.6. 阶段6: 图谱构建

-   **工具**: 使用图数据库（如`Neo4j`）或内存图库（如Python的`networkx`）进行图谱的物理构建。
-   **操作**: 将标准化后的节点和边插入到图谱中。

### 4.7. 阶段7: 图谱优化与精炼 (确定性算法)

此阶段旨在提升图谱的质量和可用性。

-   **操作**:
    *   **冗余信息移除**: 移除在图谱构建过程中产生的临时或冗余属性。
    *   **属性丰富**:
        *   从原始文档和代码中提取更多有用的属性（如：`AtomicConstraint`的`description`、`severity`；方法的`visibility`、`return_type`；类的`is_abstract`等），作为节点或边的属性。
        *   计算并添加派生属性（如：模块的“入度/出度”表示依赖复杂度）。
    *   **一致性校正**: 在此阶段进行最终的确定性校正，例如：
        *   如果一个模块在设计文档和代码中都被识别，确保它们映射到同一个节点。
        *   解决因命名不一致导致的实体分裂问题。
-   **输出**: 最终的、高质量的、可用于后续分析的宏观语义地图和微观图。

## 5. 交付与后续流程

生成并优化后的节点与边将共同构成宏观语义地图和微观图，它们是V4.3治理引擎进行“双重验证”和“富报告”生成的基石。这些图谱将直接交付给双重验证引擎和Py AI进行深度分析。
