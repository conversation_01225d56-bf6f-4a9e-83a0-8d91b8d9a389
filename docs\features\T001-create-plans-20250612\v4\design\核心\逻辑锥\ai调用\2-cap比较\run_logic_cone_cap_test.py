#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逻辑锥CAP方法对比测试运行器
提供交互式菜单和不同测试模式

使用方法：
python run_logic_cone_cap_test.py

作者：AI专家团队
日期：2025-01-10
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from logic_cone_cap_method_comparator import (
        LogicConeCAPMethodComparator, 
        LOGIC_CONE_TASKS,
        API_CONFIG
    )
    import json
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保 logic_cone_cap_method_comparator.py 文件在同一目录下")
    sys.exit(1)

def run_quick_test():
    """运行快速测试（1个层级，2种CAP方法）"""
    print("🚀 逻辑锥CAP方法快速对比测试")
    print("=" * 60)
    
    # 只测试L1层级的2种CAP方法
    print("📋 测试范围: L1原则层")
    print("🔧 测试方法: 逻辑审议者协议 vs 语义分析增强CAP")
    print("🤖 测试模型: R1模型")
    print()
    
    comparator = LogicConeCAPMethodComparator()
    
    try:
        # 手动测试L1层级的一个任务
        l1_task = LOGIC_CONE_TASKS["L1_principle"][0]
        print(f"🎯 测试任务: {l1_task['name']}")
        print(f"📝 任务描述: {l1_task['base_task']}")
        print()
        
        # 测试逻辑审议者协议
        print("🔄 测试逻辑审议者协议...")
        result_logic = comparator._test_cap_method_on_task(
            "logic_inquisitor", "R1", l1_task, "L1_principle"
        )
        
        if result_logic["success"]:
            print(f"✅ 逻辑审议者协议完成")
            print(f"   质量分数: {result_logic['evaluation']['overall_score']:.1f}")
            print(f"   质量等级: {result_logic['evaluation']['quality_grade']}")
            print(f"   推理长度: {result_logic['reasoning_length']}字符")
        else:
            print(f"❌ 逻辑审议者协议失败: {result_logic['error']}")
            return
        
        print()
        print("🔄 测试语义分析增强CAP...")
        result_semantic = comparator._test_cap_method_on_task(
            "semantic_enhanced", "R1", l1_task, "L1_principle"
        )
        
        if result_semantic["success"]:
            print(f"✅ 语义分析增强CAP完成")
            print(f"   质量分数: {result_semantic['evaluation']['overall_score']:.1f}")
            print(f"   质量等级: {result_semantic['evaluation']['quality_grade']}")
            print(f"   推理长度: {result_semantic['reasoning_length']}字符")
        else:
            print(f"❌ 语义分析增强CAP失败: {result_semantic['error']}")
            return
        
        # 快速对比
        print()
        print("📊 快速对比结果:")
        print("-" * 40)
        
        logic_score = result_logic['evaluation']['overall_score']
        semantic_score = result_semantic['evaluation']['overall_score']
        
        print(f"逻辑审议者协议: {logic_score:.1f}分")
        print(f"语义分析增强CAP: {semantic_score:.1f}分")
        print()
        
        if logic_score > semantic_score:
            advantage = logic_score - semantic_score
            print(f"🏆 逻辑审议者协议胜出，优势: +{advantage:.1f}分")
            print("💡 理论CAP在L1原则层表现更优")
        elif semantic_score > logic_score:
            advantage = semantic_score - logic_score
            print(f"🏆 语义分析增强CAP胜出，优势: +{advantage:.1f}分")
            print("💡 实用CAP在L1原则层表现更优")
        else:
            print("🤝 两种方法表现相当")
        
        # 保存快速测试结果
        quick_result = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "logic_cone_quick_test",
            "task": l1_task,
            "logic_inquisitor_result": result_logic,
            "semantic_enhanced_result": result_semantic,
            "comparison": {
                "logic_score": logic_score,
                "semantic_score": semantic_score,
                "winner": "logic_inquisitor" if logic_score > semantic_score else "semantic_enhanced" if semantic_score > logic_score else "tie",
                "advantage": abs(logic_score - semantic_score)
            }
        }
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"logic_cone_quick_test_result_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(quick_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 快速测试结果已保存: {filename}")
        print("🎉 快速测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

def run_layer_specific_test():
    """运行层级专用测试（选择特定层级进行深度测试）"""
    print("🚀 逻辑锥层级专用CAP测试")
    print("=" * 50)
    
    print("请选择要测试的层级:")
    print("1. L0 哲学思想层")
    print("2. L1 原则层")
    print("3. L2 业务层")
    
    choice = input("请选择 (1-3): ").strip()
    
    layer_mapping = {
        "1": ("L0_philosophy", "哲学思想层"),
        "2": ("L1_principle", "原则层"),
        "3": ("L2_business", "业务层")
    }
    
    if choice not in layer_mapping:
        print("❌ 无效选择")
        return
    
    layer_id, layer_name = layer_mapping[choice]
    
    print(f"\n📋 测试层级: {layer_name}")
    print(f"🎯 测试任务数: {len(LOGIC_CONE_TASKS[layer_id])}")
    
    comparator = LogicConeCAPMethodComparator()
    
    try:
        # 获取该层级的所有CAP方法
        applicable_caps = []
        for cap_id, cap_config in comparator.cap_methods.items():
            if layer_id in cap_config["target_layers"]:
                applicable_caps.append((cap_id, cap_config["name"]))
        
        print(f"🔧 适用CAP方法: {len(applicable_caps)}种")
        for cap_id, cap_name in applicable_caps:
            print(f"   - {cap_name}")
        print()
        
        layer_results = {}
        
        # 测试该层级的所有任务
        for task in LOGIC_CONE_TASKS[layer_id]:
            print(f"🎯 测试任务: {task['name']}")
            
            task_results = {}
            
            for cap_id, cap_name in applicable_caps:
                print(f"  🔧 测试: {cap_name}")
                
                # 获取推荐模型
                best_model = comparator.cap_methods[cap_id]["best_model"]
                
                result = comparator._test_cap_method_on_task(
                    cap_id, best_model, task, layer_id
                )
                
                task_results[cap_id] = result
                
                if result["success"]:
                    print(f"    ✅ 完成: {result['evaluation']['overall_score']:.1f}分")
                else:
                    print(f"    ❌ 失败: {result['error']}")
            
            layer_results[task["id"]] = {
                "task": task,
                "results": task_results
            }
            print()
        
        # 生成层级专用分析
        print("📊 层级专用分析:")
        print("-" * 30)
        
        cap_scores = {}
        for task_id, task_data in layer_results.items():
            for cap_id, result in task_data["results"].items():
                if result.get("success"):
                    if cap_id not in cap_scores:
                        cap_scores[cap_id] = []
                    cap_scores[cap_id].append(result["evaluation"]["overall_score"])
        
        for cap_id, scores in cap_scores.items():
            cap_name = comparator.cap_methods[cap_id]["name"]
            avg_score = sum(scores) / len(scores)
            max_score = max(scores)
            print(f"{cap_name}: 平均{avg_score:.1f}分, 最高{max_score:.1f}分")
        
        if cap_scores:
            best_cap = max(cap_scores.items(), key=lambda x: sum(x[1])/len(x[1]))
            best_cap_name = comparator.cap_methods[best_cap[0]]["name"]
            print(f"\n🏆 {layer_name}最佳CAP方法: {best_cap_name}")
        
        print(f"\n🎉 {layer_name}专用测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

def run_full_test():
    """运行完整测试"""
    print("🚀 逻辑锥CAP方法完整对比测试")
    print("=" * 60)
    print("⚠️ 注意：完整测试将调用大量API，可能需要较长时间和费用")
    
    # 计算预估API调用次数
    total_calls = 0
    comparator = LogicConeCAPMethodComparator()
    
    for layer, tasks in LOGIC_CONE_TASKS.items():
        for task in tasks:
            applicable_caps = [cap_id for cap_id, cap_config in comparator.cap_methods.items() 
                             if layer in cap_config["target_layers"]]
            total_calls += len(applicable_caps)
    
    print(f"📊 预估API调用次数: {total_calls}次")
    
    confirm = input("是否继续？(y/N): ").strip().lower()
    if confirm != 'y':
        print("测试已取消")
        return
    
    from logic_cone_cap_method_comparator import main
    main()

def show_config():
    """显示测试配置"""
    print("📋 逻辑锥CAP方法对比测试配置:")
    print("-" * 35)
    print(f"API地址: {API_CONFIG['url']}")
    print(f"测试模型: {', '.join(API_CONFIG['models'])}")
    
    print("\n🔧 CAP方法库:")
    comparator = LogicConeCAPMethodComparator()
    for cap_id, cap_config in comparator.cap_methods.items():
        print(f"  {cap_config['name']} ({cap_config['type']})")
        print(f"    推荐模型: {cap_config['best_model']}")
        print(f"    适用层级: {', '.join(cap_config['target_layers'])}")
    
    print("\n📋 逻辑锥任务集:")
    for layer, tasks in LOGIC_CONE_TASKS.items():
        layer_name = {
            "L0_philosophy": "哲学思想层",
            "L1_principle": "原则层", 
            "L2_business": "业务层"
        }.get(layer, layer)
        print(f"  {layer_name}: {len(tasks)}个任务")
        for task in tasks:
            print(f"    - {task['name']}: 复杂度{task['complexity']}/10")
    print()

def show_menu():
    """显示菜单"""
    print("🎯 逻辑锥CAP方法深度对比测试器")
    print("=" * 40)
    print("1. 快速测试（L1层级，2种CAP方法对比）")
    print("2. 层级专用测试（选择特定层级深度测试）")
    print("3. 完整测试（所有层级，所有CAP方法）")
    print("4. 查看测试配置")
    print("5. 退出")
    print()

def main():
    """主函数"""
    while True:
        show_menu()
        choice = input("请选择操作 (1-5): ").strip()
        
        if choice == '1':
            print()
            run_quick_test()
            print()
        elif choice == '2':
            print()
            run_layer_specific_test()
            print()
        elif choice == '3':
            print()
            run_full_test()
            print()
        elif choice == '4':
            print()
            show_config()
        elif choice == '5':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")
            print()

if __name__ == "__main__":
    main()
