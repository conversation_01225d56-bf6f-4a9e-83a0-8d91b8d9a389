# F007 Cache库-自动配置设计

## 文档元数据

- **文档ID**: `F007-CACHE-AUTOCONFIG-DESIGN-006`
- **版本**: `V1.0`
- **模块**: `commons-cache-starter`
- **技术栈**: Spring Boot 3.4.5, Spring Framework 6.1.3, Bean Validation 3.0.2, Maven 3.9
- **构建工具**: Maven 3.9.6
- **数据库技术栈**: N/A (配置管理层，无数据存储)
复杂度等级: L2

## 核心定位

自动配置模块是缓存库的"零配置"体验中心，负责根据项目依赖和配置自动装配缓存组件，提供开箱即用的Spring Boot集成体验。它是实现"batteries-included"承诺的关键模块。

## 设计哲学

本模块遵循以下设计哲学：
1. **约定优于配置**: 提供智能的默认配置，减少样板代码，配置项减少≥80%
2. **按需装配**: 根据依赖和配置条件智能装配组件，装配精确度≥95%
3. **可覆盖性**: 允许用户完全自定义和覆盖默认配置，覆盖灵活度≥90%
4. **故障友好**: 配置错误时提供清晰的错误信息，错误定位准确度≥95%

## 包含范围

**核心功能模块**：
- Spring Boot自动配置类
- 配置属性映射和验证
- 条件化Bean装配
- Starter依赖管理

## 排除范围

**不包含功能**：
- 具体的缓存实现逻辑
- 业务功能实现
- 监控和可观测性配置
- 运行时动态配置变更

## 1. 分层架构设计

### 1.1 架构层次结构

```
┌─────────────────────────────────────────────────────────────────┐
│                    用户应用层                                    │
│               application.yml配置                              │
├─────────────────────────────────────────────────────────────────┤
│                自动配置暴露层 (本层)                             │
│              XkCacheAutoConfiguration                          │
├─────────────────────────────────────────────────────────────────┤
│                配置属性处理层 (本层)                             │
│              CacheProperties + Validation                     │
├─────────────────────────────────────────────────────────────────┤
│                条件装配决策层 (本层)                             │
│           ConditionalOnProperty + ConditionalOnClass          │
├─────────────────────────────────────────────────────────────────┤
│                  Spring Boot框架层                              │
│                  ApplicationContext                            │
├─────────────────────────────────────────────────────────────────┤
│                    缓存业务层                                    │
│         CacheTemplates | CacheManager | Aspects              │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 模块依赖关系

**核心依赖图**:
- `XkCacheAutoConfiguration` → `CacheProperties` + `ConditionalAnnotations`
- `CacheProperties` → `Bean Validation` + `Configuration Metadata`
- **边界定义**: 自动配置层只负责Bean装配，不处理具体业务逻辑
- **职责分离**: 属性映射、条件判断、Bean创建各自独立，便于测试和扩展

### 1.3 接口契约定义

**AutoConfiguration契约**:
```java
// 核心接口契约 - 自动配置类
@Configuration
@EnableConfigurationProperties(CacheProperties.class)
@ConditionalOnProperty(prefix = "xkong.cache", name = "enabled", havingValue = "true", matchIfMissing = true)
public class XkCacheAutoConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "xkong.cache.l1", name = "enabled", havingValue = "true")
    LocalCacheTemplate localCacheTemplate(CacheProperties properties);     // L1缓存模板
    
    @Bean  
    @ConditionalOnMissingBean
    @ConditionalOnClass(RedisClient.class)
    ValkeyCacheTemplate valkeyCacheTemplate(CacheProperties properties);   // L2缓存模板
    
    @Bean
    @ConditionalOnBean({LocalCacheTemplate.class, ValkeyCacheTemplate.class})
    DualLayerCacheManager dualLayerCacheManager();                         // 双层缓存管理器
}
```

**CacheProperties契约**:
```java
// 核心接口契约 - 配置属性类
@ConfigurationProperties(prefix = "xkong.cache")
@Validated
public class CacheProperties {
    
    @NotNull
    Boolean enabled = true;                                                 // 主开关
    
    @Valid  
    L1Properties l1 = new L1Properties();                                  // L1配置
    
    @Valid
    L2Properties l2 = new L2Properties();                                  // L2配置
    
    @DurationMin(seconds = 1)
    Duration defaultTtl = Duration.ofMinutes(10);                          // 默认TTL
}
```

## 2. 核心职责

`commons-cache-starter`是实现"开箱即用"体验的关键。它负责根据项目的依赖和配置，自动装配所有必要的缓存组件Bean，并提供类型安全的配置属性。

**核心能力**:
- **智能检测**: 自动检测项目中的缓存依赖和配置
- **条件装配**: 基于条件注解进行精确的Bean装配
- **配置验证**: 在启动时验证配置参数的有效性
- **错误诊断**: 提供详细的配置错误信息和修复建议

## 3. 配置属性 (`CacheProperties`)

我们将提供一个`@ConfigurationProperties("xkong.cache")`注解的类来映射所有配置。

### 3.1 完整配置结构

```yaml
# application.yml 完整示例
xkong:
  cache:
    enabled: true # 主开关
    default-ttl: 10m # 全局默认TTL
    
    # L1 本地缓存 (Caffeine) 配置
    l1:
      enabled: true
      max-size: 1000
      expire-after-write: 5m
      expire-after-access: 2m
      initial-capacity: 100
      refresh-after-write: 30s
      
    # L2 远程缓存 (Valkey) 配置  
    l2:
      valkey:
        mode: standalone # or cluster, sentinel
        host: localhost
        port: 6379
        password: ""
        database: 0
        timeout: 5s
        connection-pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 3s
          
    # 监控配置
    monitoring:
      enabled: true
      metrics-interval: 30s
      tracing-enabled: true
      
    # 序列化配置
    serialization:
      type: jdk # or json, protobuf
      compress: false
```

### 3.2 配置属性实现

```java
@ConfigurationProperties(prefix = "xkong.cache")
@Validated
@Data
public class CacheProperties {
    
    /**
     * 是否启用缓存功能
     */
    @NotNull
    private Boolean enabled = true;
    
    /**
     * 默认TTL时间
     */
    @NotNull
    @DurationMin(seconds = 1)
    @DurationMax(hours = 24)
    private Duration defaultTtl = Duration.ofMinutes(10);
    
    /**
     * L1本地缓存配置
     */
    @Valid
    private L1Properties l1 = new L1Properties();
    
    /**
     * L2远程缓存配置
     */
    @Valid
    private L2Properties l2 = new L2Properties();
    
    /**
     * 监控配置
     */
    @Valid
    private MonitoringProperties monitoring = new MonitoringProperties();
    
    /**
     * 序列化配置
     */
    @Valid
    private SerializationProperties serialization = new SerializationProperties();
    
    @Data
    public static class L1Properties {
        private Boolean enabled = true;
        
        @Min(100)
        @Max(1000000)
        private Integer maxSize = 1000;
        
        @DurationMin(seconds = 10)
        private Duration expireAfterWrite = Duration.ofMinutes(5);
        
        @DurationMin(seconds = 5)
        private Duration expireAfterAccess = Duration.ofMinutes(2);
        
        @Min(10)
        private Integer initialCapacity = 100;
    }
    
    @Data
    public static class L2Properties {
        @Valid
        private ValkeyProperties valkey = new ValkeyProperties();
        
        @Data
        public static class ValkeyProperties {
            @NotBlank
            private String mode = "standalone";
            
            @NotBlank
            private String host = "localhost";
            
            @Min(1)
            @Max(65535)
            private Integer port = 6379;
            
            private String password = "";
            
            @Min(0)
            @Max(15)
            private Integer database = 0;
            
            @DurationMin(seconds = 1)
            @DurationMax(seconds = 30)
            private Duration timeout = Duration.ofSeconds(5);
            
            @Valid
            private ConnectionPoolProperties connectionPool = new ConnectionPoolProperties();
        }
    }
}
```

## 4. 自动配置类 (`XkCacheAutoConfiguration`)

这是自动配置的核心，包含一系列使用`@Bean`和条件注解的方法。

### 4.1 主配置类结构

```java
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(CacheProperties.class)
@ConditionalOnProperty(prefix = "xkong.cache", name = "enabled", havingValue = "true", matchIfMissing = true)
@AutoConfigureAfter({RedisAutoConfiguration.class})
@AutoConfigureBefore({CacheAutoConfiguration.class})
public class XkCacheAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(XkCacheAutoConfiguration.class);
    
    @Bean
    @ConditionalOnMissingBean
    public CachePropertiesValidator cachePropertiesValidator() {
        return new CachePropertiesValidator();
    }
    
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnProperty(prefix = "xkong.cache.l1", name = "enabled", havingValue = "true", matchIfMissing = true)
    static class L1CacheConfiguration {
        
        @Bean
        @ConditionalOnMissingBean
        public LocalCacheTemplate localCacheTemplate(CacheProperties properties, 
                                                     CachePropertiesValidator validator) {
            validator.validateL1Properties(properties.getL1());
            log.info("自动配置L1本地缓存: maxSize={}, expireAfterWrite={}", 
                    properties.getL1().getMaxSize(), properties.getL1().getExpireAfterWrite());
            return new CaffeineCacheTemplate(properties.getL1());
        }
    }
    
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass(RedisClient.class)
    @ConditionalOnProperty(prefix = "xkong.cache.l2.valkey", name = "host")
    static class L2CacheConfiguration {
        
        @Bean
        @ConditionalOnMissingBean
        public ValkeyCacheTemplate valkeyCacheTemplate(CacheProperties properties,
                                                       CachePropertiesValidator validator) {
            validator.validateL2Properties(properties.getL2());
            log.info("自动配置L2远程缓存: host={}, port={}", 
                    properties.getL2().getValkey().getHost(), properties.getL2().getValkey().getPort());
            return new ValkeyCacheTemplate(properties.getL2().getValkey());
        }
    }
    
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnBean({LocalCacheTemplate.class, ValkeyCacheTemplate.class})
    static class DualLayerCacheConfiguration {
        
        @Bean
        @ConditionalOnMissingBean
        public DualLayerCacheManager dualLayerCacheManager(LocalCacheTemplate localCache,
                                                           ValkeyCacheTemplate remoteCache,
                                                           CacheProperties properties) {
            log.info("自动配置双层缓存管理器");
            return new DualLayerCacheManager(localCache, remoteCache, properties);
        }
    }
    
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnBean(DualLayerCacheManager.class)
    @ConditionalOnProperty(prefix = "xkong.cache", name = "aop-enabled", havingValue = "true", matchIfMissing = true)
    static class CacheAspectConfiguration {
        
        @Bean
        @ConditionalOnMissingBean
        public CacheAspect cacheAspect(DualLayerCacheManager cacheManager) {
            log.info("自动配置缓存AOP切面");
            return new CacheAspect(cacheManager);
        }
    }
}
```

### 4.2 Bean装配逻辑详述

**装配决策树**:
```
Application启动
    ↓
检查xkong.cache.enabled (默认true)
    ↓ [enabled=true]
检查L1配置条件 → 创建LocalCacheTemplate
    ↓
检查L2配置条件 → 创建ValkeyCacheTemplate  
    ↓
检查双层条件 → 创建DualLayerCacheManager
    ↓
检查AOP条件 → 创建CacheAspect
    ↓
自动配置完成
```

**条件注解说明**:
- **`@ConditionalOnProperty`**: 基于配置属性的条件装配
- **`@ConditionalOnClass`**: 基于类路径中类存在性的条件装配
- **`@ConditionalOnBean`**: 基于其他Bean存在性的条件装配
- **`@ConditionalOnMissingBean`**: 避免重复创建，支持用户自定义覆盖

## 5. 配置验证和错误处理

### 5.1 配置验证器

```java
@Component
public class CachePropertiesValidator {
    
    public void validateL1Properties(CacheProperties.L1Properties l1) {
        if (l1.getMaxSize() <= 0) {
            throw ValidationBusinessException.invalidArgument("XCE_VAL_800", "L1缓存maxSize必须大于0, 当前值: " + l1.getMaxSize());
        }
        
        if (l1.getExpireAfterWrite().compareTo(Duration.ofSeconds(1)) < 0) {
            throw ValidationBusinessException.invalidArgument("XCE_VAL_801", "L1缓存expireAfterWrite不能小于1秒");
        }
        
        // 逻辑一致性检查
        if (l1.getExpireAfterAccess().compareTo(l1.getExpireAfterWrite()) > 0) {
            throw ValidationBusinessException.invalidArgument("XCE_VAL_802", 
                "L1缓存expireAfterAccess不能大于expireAfterWrite: access=" + l1.getExpireAfterAccess() + ", write=" + l1.getExpireAfterWrite());
        }
    }
    
    public void validateL2Properties(CacheProperties.L2Properties l2) {
        ValkeyProperties valkey = l2.getValkey();
        
        // 连接参数验证
        if (!isValidHost(valkey.getHost())) {
            throw ValidationBusinessException.invalidArgument("XCE_VAL_803", "无效的Valkey主机地址: " + valkey.getHost());
        }
        
        if (!isPortAvailable(valkey.getHost(), valkey.getPort())) {
            throw SystemException.internalError("XCE_SYS_805", "无法连接到Valkey服务: " + 
                valkey.getHost() + ":" + valkey.getPort());
        }
    }
    
    private boolean isValidHost(String host) {
        try {
            InetAddress.getByName(host);
            return true;
        } catch (UnknownHostException e) {
            return false;
        }
    }
    
    private boolean isPortAvailable(String host, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), 3000);
            return true;
        } catch (IOException e) {
            return false;
        }
    }
}
```

### 5.2 启用自动配置

在`resources/META-INF/spring/`目录下创建`org.springframework.boot.autoconfigure.AutoConfiguration.imports`文件：

```
org.xkong.cloud.commons.cache.starter.XkCacheAutoConfiguration
```

### 5.3 配置元数据

在`resources/META-INF/`目录下创建`spring-configuration-metadata.json`文件：

```json
{
  "groups": [
    {
      "name": "xkong.cache",
      "type": "org.xkong.cloud.commons.cache.starter.CacheProperties",
      "description": "XKong缓存库配置属性"
    }
  ],
  "properties": [
    {
      "name": "xkong.cache.enabled",
      "type": "java.lang.Boolean",
      "description": "是否启用缓存功能",
      "defaultValue": true
    },
    {
      "name": "xkong.cache.default-ttl",
      "type": "java.time.Duration",
      "description": "默认TTL时间",
      "defaultValue": "10m"
    },
    {
      "name": "xkong.cache.l1.enabled",
      "type": "java.lang.Boolean", 
      "description": "是否启用L1本地缓存",
      "defaultValue": true
    }
  ],
  "hints": [
    {
      "name": "xkong.cache.l2.valkey.mode",
      "values": [
        {"value": "standalone", "description": "单机模式"},
        {"value": "cluster", "description": "集群模式"},
        {"value": "sentinel", "description": "哨兵模式"}
      ]
    }
  ]
}
```

## 6. 灵活性与覆盖

### 6.1 默认行为

- **默认启用所有**: 如果用户只引入starter并进行基本配置，默认会启用完整的双层缓存+AOP功能
- **智能降级**: 如果L2配置缺失，自动降级为仅L1模式，不影响应用启动

### 6.2 用户自定义覆盖

```java
@Configuration
public class CustomCacheConfiguration {
    
    // 用户自定义LocalCacheTemplate会覆盖自动配置
    @Bean
    @Primary
    public LocalCacheTemplate customLocalCacheTemplate() {
        return new CustomCaffeineCacheTemplate();
    }
    
    // 用户自定义CacheManager会覆盖自动配置
    @Bean
    @Primary
    public DualLayerCacheManager customCacheManager() {
        return new CustomDualLayerCacheManager();
    }
}
```

### 6.3 按需禁用

```yaml
xkong:
  cache:
    l1:
      enabled: false # 禁用L1本地缓存
    l2:
      valkey:
        host: # 留空会禁用L2远程缓存
    aop-enabled: false # 禁用AOP切面
```

## 7. 边界护栏机制

### 7.1 复杂度控制边界
- **配置项数量**: 限制顶级配置项≤20个，避免配置复杂度过高
- **嵌套层级**: 配置属性嵌套层级≤3层，保持配置结构清晰
- **依赖检查深度**: Bean依赖检查深度≤5层，避免循环依赖
- **启动时间控制**: 自动配置处理时间≤应用启动时间的5%

### 7.2 架构演进策略
- **配置向后兼容**: 在同一主版本内保持配置格式向后兼容
- **渐进式迁移**: 支持旧配置到新配置的自动迁移和警告
- **扩展点设计**: 提供ConfigurationCustomizer接口支持配置扩展
- **版本检测**: 自动检测依赖版本兼容性，提供升级建议

## 实施约束

### 强制性技术要求
- **Spring Boot版本**: 必须使用Spring Boot 3.4.5，确保自动配置机制的兼容性
- **Spring Framework版本**: 必须使用Spring Framework 6.1.3，确保条件注解的完整支持
- **Bean Validation版本**: 必须使用Bean Validation 3.0.2进行配置参数验证
- **Java版本**: 必须使用Java 21+，确保配置处理的现代语法支持
- **Maven版本**: 必须使用Maven 3.9.6+，确保依赖管理和编译

### 配置约束
- **默认配置**: 必须提供生产可用的默认配置，覆盖率≥90%
- **配置验证**: 所有配置参数必须进行格式和范围验证，验证覆盖率100%
- **向后兼容**: 配置格式在同一主版本内保持向后兼容
- **环境隔离**: 支持不同环境的配置隔离(dev/test/prod)

### 性能指标要求
- **启动时间**: 自动配置处理时间≤应用启动时间的5%
- **内存开销**: 配置对象内存占用≤总内存的1%
- **配置加载**: 配置文件解析时间≤100ms
- **Bean创建**: Bean装配时间≤500ms
- **验证性能**: 配置验证时间≤50ms

### 兼容性要求
- **Spring生态**: 与Spring Boot Actuator、Security、Data等完全兼容(≥95%)
- **配置格式**: 支持YAML、Properties等标准格式
- **外部化配置**: 支持环境变量、命令行参数等外部化配置
- **IDE支持**: 提供配置提示和自动补全支持

### 错误处理约束
- **配置错误**: 提供清晰的错误信息和修复建议，错误定位准确度≥95%
- **启动失败**: 配置错误时快速失败，失败原因明确
- **降级策略**: 部分配置错误时自动降级，不影响核心功能
- **日志输出**: 配置加载过程提供详细的日志输出

### 约束违规后果
- **配置错误**: 应用启动失败，组件不可用
- **性能不达标**: 影响应用启动速度，启动时间增加≥20%
- **兼容性问题**: 与现有Spring应用集成冲突，功能异常
- **验证失败**: 运行时配置错误，系统行为不可预期

### 验证锚点
- **自动配置测试**: `mvn test -Dtest=AutoConfigurationTest`
- **配置验证测试**: `mvn test -Dtest=ConfigurationValidationTest`
- **集成兼容性测试**: `mvn test -Dtest=SpringBootIntegrationTest`
- **性能基准测试**: `mvn test -Dtest=AutoConfigurationPerformanceTest`
- **错误处理测试**: `mvn test -Dtest=ConfigurationErrorHandlingTest`
