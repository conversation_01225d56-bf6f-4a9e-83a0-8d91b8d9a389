# V4.3方案：微观图构建与双重验证引擎 (代码一致性)

## 1. 文档信息

- **文档版本**: V4.3
- **创建日期**: 2025-08-13
- **目的**: 详细阐述V4.3治理引擎如何从代码层面构建“微观图”，并结合“宏观语义地图”实现设计与代码的“双重验证”，确保最终的架构一致性。

## 2. 微观图的定义与重要性

**微观图 (Micro Graph)** 是指从实际代码库中提取的、细粒度的、机器可读的图谱。它包含了函数调用、类继承、接口实现、数据结构定义、配置项使用等代码层面的具体信息。

-   **重要性**:
    *   **代码级事实**: 微观图是代码的真实反映，是进行代码与设计一致性验证的“事实层”。
    *   **双重验证基石**: 与“宏观语义地图”形成对比和互补，共同构成双重验证的基础。
    *   **问题定位**: 能够精确地将设计问题或不一致性定位到具体的代码行。

## 3. 双重验证引擎的核心思想

V4.3的双重验证引擎，其核心思想是：**将“宏观语义地图”（设计意图）与“微观图”（代码实现）进行比对和融合，通过确定性算法识别两者之间的差异和不一致性。**

-   **宏观语义地图**: 描述了“应该是什么” (What should be) 的架构设计。
-   **微观图**: 描述了“实际是什么” (What is) 的代码实现。
-   **双重验证**: 旨在弥合“应该”与“实际”之间的差距。

## 4. 在 `DesignValidationWorkflow` 中获取代码事实（基于 Serena）

`ProjectManager` 在其 `DesignValidationWorkflow` 的执行过程中，明确采用开源 `serena` 作为“代码事实”的唯一来源。`serena` 通过 MCP/LSP 调用语言服务器（如 JDT LS 等）提供编译器级的符号/引用/实现信息，确保“微观图”具备工业级确定性与稳定性。

```mermaid
graph TD
    A[1. 触发代码扫描] --> B[2. 加载 project.yml 范围/忽略规则]
    B --> C[3. 启动 Serena (绑定 workspaceFolder)]
    C --> D{4. 工具调用<br/>get_symbols_overview / find_symbol / find_referencing_symbols / find_implementations / get_outline}
    D --> E[5. 反序列化响应并装配微观图<br/>节点: 类/接口/方法/字段/配置; 边: 调用/实现/继承/引用]
    E --> F[6. 确定性校验与快取<br/>超时/重试/证据记录]
    F --> G[7. 交付双重验证引擎]
```

### 4.1. Serena 配置与运行边界
- `project.yml`: 使用 `ignored_paths`、`languages`、`entry_dirs` 限定扫描范围，避免无关目录；绑定 `${workspaceFolder}` 保证跨项目可复用。
- 语言服务器：采用稳定版本（如 JDT LS for Java）；Serena 负责进程生命周期与崩溃重启。
- 超时与重试：单请求超时（如 15s），指数退避重试≤3 次；失败请求写入证据并标记为 `INDETERMINATE`。

### 4.2. Serena 工具调用规范（稳定接口）
- `get_symbols_overview(path)`：列出文件/目录下顶层符号（类/接口/方法）。
- `find_symbol(fq_name)`：按 FQN 精确定位符号，返回唯一 `symbolId` 与源位置。
- `find_referencing_symbols(symbolId)`：返回引用该符号的所有位置（调用/读取/写入）。
- `find_implementations(symbolId)`：接口/抽象方法的实现列表。
- `get_outline(path)`：文件结构大纲（用于方法/字段枚举）。
- 约束：所有请求参数与响应结构版本固定；响应包含 `request_id` 以便在报告中回溯。

### 4.3. 微观图装配（内存图）
- 节点类型：`Class`、`Interface`、`Enum`、`Method`、`Field`、`ConfigKey`。
- 边类型：`CALLS`、`IMPLEMENTS`、`EXTENDS`、`REFERENCES`、`READS`、`WRITES`。
- 主键：`symbolId`（语言服务器返回）+ `file_path`；防抖去重并记录首次证据。
- 产物：可选持久化 `micro_graph.json`（调试），默认内存供后续比对。

### 4.4. 确定性与错误恢复
- 决策不依赖 AI；仅依赖 Serena 响应与规则引擎。
- 快取：对稳定文件的查询结果落盘缓存（含 `request_id`），哈希命中直接复用。
- 证据记录：每条图边携带 `evidence`（文件/行号/请求与响应摘要）。
- 失败隔离：个别文件/请求失败不阻断整体；相关断言标记 `INDETERMINATE` 并在报告中呈现。

## 5. 双重验证引擎流程（断言驱动）

双重验证引擎是 `DesignValidationWorkflow` 内部的核心步骤，其流程如下：

```mermaid
graph TD
    A[宏观语义地图 (设计意图)] --> C[断言映射与对齐]
    B[微观图 (Serena 代码事实)] --> C
    C --> D[差异与违规识别 (确定性规则)]
    D --> E[分类: MISSING/CONFLICT/LEGACY/OK]
    E --> F[生成验证结果与证据]
```

### 5.1. 输入
- 宏观图：来自 `04-宏观语义地图构建流程-V3.md`（已通过 Schema 与一致性校验）。
- 微观图：按 4.x 章节由 Serena 获取并装配完成。

### 5.2. 设计 → 代码 断言映射（示例）
- 性能/韧性：`resilience.circuit_breaker=true` → 存在熔断构造/策略引用（如 resilience4j 或等价实现），并对目标调用点生效。
- HTTP 客户端策略：`http_client_policy.timeout_ms=X` → 客户端构造点存在超时= X±允许容差；缺失则 `MISSING`。
- 异常规范：`coding_standard.exception_wrapping` → 出口层存在统一异常包装与错误码记录；直抛内部异常则 `LEGACY`。
- 集成分层：`integration_pattern.repository_only` → 应用层直用 `JdbcTemplate`/裸 SQL 记为 `LEGACY`；经 `Repository` 为 `OK`。

### 5.3. 差异分类与门槛
- `MISSING`：设计有，代码无。
- `CONFLICT`：设计与实现不一致（值/关系/约束不符）。
- `LEGACY`：命中旧架构反模式（不允许的做法）。
- `OK`：满足断言。
- 验收门槛：`MISSING=0`、`CONFLICT=0`、`LEGACY=0`。否则进入整改循环。

## 6. 交付与证据
- `assertion_results.yaml`：逐断言判定与证据（含 `serena.request_id`）。
- `RichReport v2`（以 `rich_report_v2.yaml` 格式产出）：`summary/findings/overall_status`，并附 `recommended_task`（由 Py AI 编排）。
- 可选 `micro_graph.yaml`（调试用途）。

## 7. 后续流程
- 人类审阅 `RichReport v2` → 委托 RooCode 执行整改 → 粘贴结果至 UI 区域8 → V4.3 终检与写入 → 自动重审，直至 `overall_status=COMPLIANT`。
