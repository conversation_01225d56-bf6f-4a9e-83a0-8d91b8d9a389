# Web界面配置管理设计

## 📋 设计概述

**设计目标**：为SimpleConfigurationCenter提供Web界面管理功能
**核心功能**：配置查看、修改、重启确认、状态监控
**设计原则**：简单可靠、重启更新、用户友好
**技术栈**：HTML + CSS + JavaScript + Flask API + WebSocket
**实现状态**：已完成并集成到现有Web系统

## 🎯 功能需求

### **核心功能**
```yaml
配置查看:
  - 显示当前所有配置项
  - 按分类组织配置（数据库、Web、性能等）
  - 配置值的实时显示

配置修改:
  - 在线编辑配置值
  - 实时保存到配置文件
  - 配置变更后询问用户是否重启应用

状态监控:
  - 配置文件状态
  - 最后修改时间
  - 配置同步状态
```

### **用户体验**
```yaml
界面友好:
  - 清晰的配置分类
  - 直观的输入控件
  - 实时的状态反馈

操作简单:
  - 点击即可编辑
  - 自动保存配置
  - 错误提示明确
```

## 🌐 Web界面设计

### **独立配置中心页面**
**设计原则**：配置管理作为独立页面，不占用九宫格核心会议功能区域
**访问方式**：通过左侧导航菜单"⚙️ 九宫格配置"链接访问
**页面路由**：`/config` - 独立的配置中心页面

```html
<!-- 独立配置中心页面 (config_center.html) -->
<div class="config-container">
    <div class="config-header">
        <h1 class="config-title">⚙️ 九宫格配置中心</h1>
        <p class="config-subtitle">统一配置管理 - 双模式编辑支持</p>
    </div>

    <div class="config-sections">
        <!-- API密钥配置区域 -->
        <div class="config-section">
            <h2 class="section-title">🔑 API密钥配置</h2>

            <div class="config-item">
                <label class="config-label">GMI DeepSeek V3-0324 API密钥:</label>
                <input type="password" id="gmi-deepseek-v3-key" class="config-input api-key-input"
                       placeholder="输入GMI API密钥">
                <span id="gmi-deepseek-v3-status" class="api-status unknown">未知</span>
                <button class="save-btn" onclick="updateAPIKey('gmi_deepseek_v3', 'gmi-deepseek-v3-key')">
                    保存
                </button>
            </div>

            <div class="config-item">
                <label class="config-label">GMI DeepSeek R1-0528 API密钥:</label>
                <input type="password" id="gmi-deepseek-r1-key" class="config-input api-key-input"
                       placeholder="输入GMI API密钥">
                <span id="gmi-deepseek-r1-status" class="api-status unknown">未知</span>
                <button class="save-btn" onclick="updateAPIKey('gmi_deepseek_r1', 'gmi-deepseek-r1-key')">
                    保存
                </button>
            </div>

            <div class="config-item">
                <label class="config-label">Chutes DeepSeek R1 API密钥:</label>
                <input type="password" id="chutes-deepseek-r1-key" class="config-input api-key-input"
                       placeholder="输入Chutes API密钥">
                <span id="chutes-deepseek-r1-status" class="api-status unknown">未知</span>
                <button class="save-btn" onclick="updateAPIKey('chutes_deepseek_r1', 'chutes-deepseek-r1-key')">
                    保存
                </button>
            </div>

            <div class="config-item">
                <label class="config-label">Chutes DeepCoder-14B API密钥:</label>
                <input type="password" id="chutes-deepcoder-14b-key" class="config-input api-key-input"
                       placeholder="输入Chutes API密钥">
                <span id="chutes-deepcoder-14b-status" class="api-status unknown">未知</span>
                <button class="save-btn" onclick="updateAPIKey('chutes_deepcoder_14b', 'chutes-deepcoder-14b-key')">
                    保存
                </button>
            </div>

            <div class="api-actions">
                <button class="action-btn test-btn" onclick="testAllAPIKeys()">
                    🧪 测试所有密钥
                </button>
                <button class="action-btn refresh-btn" onclick="loadAPIKeyStatus()">
                    🔄 刷新状态
                </button>
            </div>
        </div>

        <!-- 配置分类区域 -->
        <div class="config-tabs">
            <button class="tab-btn active" data-tab="database">数据库</button>
            <button class="tab-btn" data-tab="web">Web界面</button>
            <button class="tab-btn" data-tab="performance">性能</button>
            <button class="tab-btn" data-tab="debug">调试</button>
        </div>

        <!-- 数据库配置面板 -->
        <div class="config-panel" id="database-panel">
            <div class="config-item">
                <label>数据库路径:</label>
                <input type="text" id="db-path" class="config-input"
                       data-config-key="database_config.sqlite_path">
                <button class="save-btn" onclick="saveConfig('database_config.sqlite_path', 'db-path')">
                    保存
                </button>
            </div>

            <div class="config-item">
                <label>加密密钥长度:</label>
                <input type="number" id="encryption-length" class="config-input"
                       data-config-key="database_config.encryption_key_length">
                <button class="save-btn" onclick="saveConfig('database_config.encryption_key_length', 'encryption-length')">
                    保存
                </button>
            </div>

            <div class="config-item">
                <label>查询超时:</label>
                <input type="text" id="query-timeout" class="config-input"
                       data-config-key="database_config.query_timeout">
                <button class="save-btn" onclick="saveConfig('database_config.query_timeout', 'query-timeout')">
                    保存
                </button>
            </div>
        </div>

        <!-- Web界面配置面板 -->
        <div class="config-panel" id="web-panel" style="display: none;">
            <div class="config-item">
                <label>监听地址:</label>
                <input type="text" id="web-host" class="config-input"
                       data-config-key="web_interface_config.host">
                <button class="save-btn" onclick="saveConfig('web_interface_config.host', 'web-host')">
                    保存
                </button>
            </div>

            <div class="config-item">
                <label>监听端口:</label>
                <input type="number" id="web-port" class="config-input"
                       data-config-key="web_interface_config.port">
                <button class="save-btn" onclick="saveConfig('web_interface_config.port', 'web-port')">
                    保存
                </button>
            </div>

            <div class="config-item">
                <label>调试URL:</label>
                <input type="text" id="debug-url" class="config-input"
                       data-config-key="web_interface_config.debug_url">
                <button class="save-btn" onclick="saveConfig('web_interface_config.debug_url', 'debug-url')">
                    保存
                </button>
            </div>
        </div>

        <!-- 性能配置面板 -->
        <div class="config-panel" id="performance-panel" style="display: none;">
            <div class="config-item">
                <label>最大并发适配:</label>
                <input type="number" id="max-concurrent" class="config-input"
                       data-config-key="performance.max_concurrent_adaptations">
                <button class="save-btn" onclick="saveConfig('performance.max_concurrent_adaptations', 'max-concurrent')">
                    保存
                </button>
            </div>

            <div class="config-item">
                <label>缓存TTL (秒):</label>
                <input type="number" id="cache-ttl" class="config-input"
                       data-config-key="performance.cache_ttl">
                <button class="save-btn" onclick="saveConfig('performance.cache_ttl', 'cache-ttl')">
                    保存
                </button>
            </div>
        </div>

        <!-- 调试配置面板 -->
        <div class="config-panel" id="debug-panel" style="display: none;">
            <div class="config-item">
                <label>启用调试:</label>
                <input type="checkbox" id="debug-enabled" class="config-checkbox"
                       data-config-key="debug.enabled">
                <button class="save-btn" onclick="saveConfigCheckbox('debug.enabled', 'debug-enabled')">
                    保存
                </button>
            </div>

            <div class="config-item">
                <label>日志级别:</label>
                <select id="log-level" class="config-select"
                        data-config-key="debug.log_level">
                    <option value="DEBUG">DEBUG</option>
                    <option value="INFO">INFO</option>
                    <option value="WARNING">WARNING</option>
                    <option value="ERROR">ERROR</option>
                </select>
                <button class="save-btn" onclick="saveConfig('debug.log_level', 'log-level')">
                    保存
                </button>
            </div>
        </div>

        <!-- 配置状态监控 -->
        <div class="config-status">
            <div class="status-row">
                <span class="status-label">配置文件:</span>
                <span id="config-file-status" class="status-value">正常</span>
            </div>
            <div class="status-row">
                <span class="status-label">最后更新:</span>
                <span id="last-update-time" class="status-value">从未更新</span>
            </div>
            <div class="status-row">
                <span class="status-label">同步状态:</span>
                <span id="sync-status" class="status-value">已同步</span>
            </div>
        </div>

        <!-- 配置操作按钮 -->
        <div class="config-actions">
            <button class="action-btn reload-btn" onclick="reloadConfig()">
                🔄 重新加载
            </button>
            <button class="action-btn export-btn" onclick="exportConfig()">
                📤 导出配置
            </button>
            <button class="action-btn import-btn" onclick="importConfig()">
                📥 导入配置
            </button>
        </div>
    </div>
</div>

### **左侧导航菜单集成**
```html
<!-- 在九宫格页面的左侧导航菜单中添加配置入口 -->
<nav class="left-menu" id="leftMenu">
    <div class="menu-title">🏠 四重验证会议系统</div>
    <a href="/" class="menu-item" data-target="home">🏠 主页</a>
    <a href="/modules" class="menu-item" data-target="modules">🔧 模块管理</a>
    <a href="/debug" class="menu-item" data-target="debug">🐛 调试中心</a>
    <a href="/api/status" class="menu-item" data-target="status" target="_blank">📊 系统状态</a>
    <a href="/api/health" class="menu-item" data-target="health" target="_blank">❤️ 健康检查</a>
    <div class="menu-item" onclick="openConfigCenter()">⚙️ 九宫格配置</div>
</nav>

<script>
function openConfigCenter() {
    // 打开独立的配置中心页面
    window.open('/config', '_blank');
}
</script>
```

### **CSS样式设计**
```css
/* 独立配置中心页面样式 */
.config-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    background-color: #1E1F22;
    color: #BBBBBB;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    min-height: 100vh;
}

.config-header {
    text-align: center;
    margin-bottom: 2rem;
    border-bottom: 1px solid #3C3F41;
    padding-bottom: 1rem;
}

.config-title {
    color: #0078D4;
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.config-subtitle {
    color: #BBBBBB;
    font-size: 1rem;
}

.config-sections {
    background-color: #2A2D30;
    border: 1px solid #3C3F41;
    border-radius: 8px;
    padding: 1.5rem;
    overflow-y: auto;
}

/* API密钥配置特殊样式 */
.api-key-input {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
}

.api-status {
    display: inline-block;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: bold;
    margin-left: 0.5rem;
}

.api-status.active {
    background-color: #4CAF50;
    color: white;
}

.api-status.inactive {
    background-color: #F44336;
    color: white;
}

.api-status.unknown {
    background-color: #FF9800;
    color: white;
}

.api-actions {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #3C3F41;
    display: flex;
    gap: 0.5rem;
}

.test-btn:hover {
    background-color: #2196F3;
    border-color: #2196F3;
}

.refresh-btn:hover {
    background-color: #FF9800;
    border-color: #FF9800;
}

.config-tabs {
    display: flex;
    margin-bottom: 1rem;
    border-bottom: 1px solid #3C3F41;
}

.tab-btn {
    background: none;
    border: none;
    color: #BBBBBB;
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    color: #0078D4;
}

.tab-btn.active {
    color: #0078D4;
    border-bottom-color: #0078D4;
}

.config-panel {
    max-height: 300px;
    overflow-y: auto;
}

.config-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background-color: #1E1F22;
    border-radius: 4px;
    gap: 0.5rem;
}

.config-item label {
    min-width: 120px;
    font-weight: bold;
    color: #BBBBBB;
}

.config-input,
.config-select {
    flex: 1;
    padding: 0.4rem;
    background-color: #2A2D30;
    color: #BBBBBB;
    border: 1px solid #3C3F41;
    border-radius: 3px;
    font-size: 0.9rem;
}

.config-input:focus,
.config-select:focus {
    outline: none;
    border-color: #0078D4;
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
}

.config-checkbox {
    width: auto;
    margin-right: 0.5rem;
}

.save-btn {
    padding: 0.4rem 0.8rem;
    background-color: #0078D4;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: background-color 0.2s ease;
}

.save-btn:hover {
    background-color: #106EBE;
}

.save-btn:disabled {
    background-color: #3C3F41;
    cursor: not-allowed;
}

.config-status {
    background-color: #1E1F22;
    padding: 0.75rem;
    border-radius: 4px;
    margin: 1rem 0;
}

.status-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
}

.status-label {
    font-weight: bold;
}

.status-value {
    color: #0078D4;
}

.config-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.action-btn {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #3C3F41;
    border-radius: 4px;
    background-color: #2A2D30;
    color: #BBBBBB;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: #3C3F41;
    border-color: #0078D4;
}

.reload-btn:hover {
    color: #FFA500;
    border-color: #FFA500;
}

.export-btn:hover {
    color: #28A745;
    border-color: #28A745;
}

.import-btn:hover {
    color: #17A2B8;
    border-color: #17A2B8;
}

/* 重启确认对话框样式 */
.restart-confirmation-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.restart-confirmation-modal .modal-content {
    background-color: #2A2D30;
    border: 1px solid #3C3F41;
    border-radius: 8px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    color: #BBBBBB;
}

.restart-confirmation-modal .modal-header h3 {
    margin: 0 0 1rem 0;
    color: #FFA500;
    font-size: 1.2rem;
}

.restart-confirmation-modal .modal-body {
    margin-bottom: 2rem;
    line-height: 1.5;
}

.restart-confirmation-modal .modal-body p {
    margin-bottom: 1rem;
}

.restart-confirmation-modal .modal-footer {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.restart-confirmation-modal .btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.restart-confirmation-modal .btn-primary {
    background-color: #0078D4;
    color: white;
}

.restart-confirmation-modal .btn-primary:hover {
    background-color: #106EBE;
}

.restart-confirmation-modal .btn-secondary {
    background-color: #3C3F41;
    color: #BBBBBB;
}

.restart-confirmation-modal .btn-secondary:hover {
    background-color: #4C4F51;
}

.restart-confirmation-modal .btn-danger {
    background-color: #D13438;
    color: white;
}

.restart-confirmation-modal .btn-danger:hover {
    background-color: #B12328;
}

/* 通知样式 */
.config-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem;
    border-radius: 4px;
    color: white;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.config-notification.success {
    background-color: #28A745;
}

.config-notification.error {
    background-color: #DC3545;
}

.config-notification.warning {
    background-color: #FFC107;
    color: #000;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
```

### **JavaScript功能实现**
```javascript
class ConfigurationManager {
    constructor() {
        this.currentConfig = {};
        this.socket = null;
        
        this.initializeEventListeners();
        this.initializeWebSocket();
        this.loadCurrentConfig();
    }
    
    initializeEventListeners() {
        // 标签切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });
        
        // 输入框实时验证
        document.querySelectorAll('.config-input, .config-select').forEach(input => {
            input.addEventListener('input', (e) => {
                this.validateInput(e.target);
            });
        });
    }
    
    initializeWebSocket() {
        // 连接WebSocket以接收配置变更通知
        this.socket = io('/config');
        
        this.socket.on('connect', () => {
            console.log('配置WebSocket连接成功');
            this.updateSyncStatus('已连接');
        });
        
        this.socket.on('disconnect', () => {
            console.log('配置WebSocket连接断开');
            this.updateSyncStatus('连接断开');
        });
        
        this.socket.on('config_changed', (data) => {
            console.log('收到配置变更通知:', data);
            this.handleConfigChange(data);
        });
        
        this.socket.on('config_notification', (data) => {
            console.log('收到配置通知:', data);
            if (data.type === 'file_change') {
                this.showNotification('配置文件已被外部修改，正在重新加载...', 'warning');
                this.loadCurrentConfig();
            }
        });
    }
    
    switchTab(tabName) {
        // 切换标签
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // 切换面板
        document.querySelectorAll('.config-panel').forEach(panel => {
            panel.style.display = 'none';
        });
        document.getElementById(`${tabName}-panel`).style.display = 'block';
    }
    
    async loadCurrentConfig() {
        try {
            const response = await fetch('/api/config');
            const result = await response.json();
            
            if (result.success) {
                this.currentConfig = result.config;
                this.populateConfigFields();
                this.updateLastUpdateTime();
                this.updateSyncStatus('已同步');
            } else {
                this.showNotification('加载配置失败', 'error');
            }
        } catch (error) {
            this.showNotification(`加载配置异常: ${error.message}`, 'error');
        }
    }
    
    populateConfigFields() {
        // 填充配置字段
        document.querySelectorAll('[data-config-key]').forEach(element => {
            const configKey = element.dataset.configKey;
            const value = this.getNestedValue(this.currentConfig, configKey);
            
            if (value !== undefined) {
                if (element.type === 'checkbox') {
                    element.checked = Boolean(value);
                } else {
                    element.value = value;
                }
            }
        });
    }
    
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }
    
    validateInput(input) {
        const configKey = input.dataset.configKey;
        const value = input.value;
        
        // 基本验证
        let isValid = true;
        let errorMessage = '';
        
        if (configKey.includes('port')) {
            const port = parseInt(value);
            if (isNaN(port) || port < 1024 || port > 65535) {
                isValid = false;
                errorMessage = '端口号必须在1024-65535之间';
            }
        }
        
        if (configKey.includes('path') && value && !value.trim()) {
            isValid = false;
            errorMessage = '路径不能为空';
        }
        
        // 更新输入框样式
        if (isValid) {
            input.style.borderColor = '#3C3F41';
            input.title = '';
        } else {
            input.style.borderColor = '#DC3545';
            input.title = errorMessage;
        }
        
        return isValid;
    }
    
    async saveConfig(configKey, inputId) {
        const input = document.getElementById(inputId);
        const value = input.value;

        // 验证输入
        if (!this.validateInput(input)) {
            this.showNotification('配置值无效，请检查输入', 'error');
            return;
        }

        try {
            // 类型转换
            let processedValue = value;
            if (configKey.includes('port') || configKey.includes('length') || configKey.includes('ttl') || configKey.includes('concurrent')) {
                processedValue = parseInt(value);
            }

            const response = await fetch(`/api/config/${configKey}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ value: processedValue })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('配置保存成功', 'success');
                this.updateLastUpdateTime();

                // 更新本地配置
                this.setNestedValue(this.currentConfig, configKey, processedValue);

                // 如果需要重启，询问用户
                if (result.restart_required) {
                    this.showRestartConfirmation(configKey, processedValue);
                }
            } else {
                this.showNotification(`配置保存失败: ${result.error}`, 'error');
            }

        } catch (error) {
            this.showNotification(`配置保存异常: ${error.message}`, 'error');
        }
    }

    async updateAPIKey(provider, inputId) {
        const apiKey = document.getElementById(inputId).value;
        if (!apiKey || apiKey.trim() === '') {
            this.showNotification('请输入有效的API密钥', 'error');
            return;
        }

        try {
            const response = await fetch('/api/keys/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    provider: provider,
                    api_key: apiKey
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(`${provider} API密钥已更新`, 'success');
                // 清空输入框（安全考虑）
                document.getElementById(inputId).value = '';
                // 更新状态显示
                this.updateAPIKeyStatus(provider, 'active');
            } else {
                this.showNotification(`API密钥更新失败: ${result.error}`, 'error');
            }

        } catch (error) {
            this.showNotification(`API密钥更新异常: ${error.message}`, 'error');
        }
    }

    async testAllAPIKeys() {
        this.showNotification('正在测试所有API密钥...', 'info');

        try {
            const response = await fetch('/api/keys/test-all', {
                method: 'POST'
            });

            const result = await response.json();

            if (result.success) {
                // 更新所有API密钥状态
                Object.entries(result.results).forEach(([provider, testResult]) => {
                    this.updateAPIKeyStatus(provider, testResult.status ? 'active' : 'inactive');
                });
                this.showNotification('API密钥测试完成', 'success');
            } else {
                this.showNotification(`API密钥测试失败: ${result.error}`, 'error');
            }

        } catch (error) {
            this.showNotification(`API密钥测试异常: ${error.message}`, 'error');
        }
    }

    async loadAPIKeyStatus() {
        try {
            const response = await fetch('/api/keys/status');
            const result = await response.json();

            if (result.success) {
                Object.entries(result.status).forEach(([provider, status]) => {
                    this.updateAPIKeyStatus(provider, status);
                });
            } else {
                console.error('加载API密钥状态失败:', result.error);
            }

        } catch (error) {
            console.error('加载API密钥状态异常:', error);
        }
    }

    updateAPIKeyStatus(provider, status) {
        const statusElement = document.getElementById(`${provider.replace('_', '-')}-status`);
        if (statusElement) {
            statusElement.className = `api-status ${status}`;
            statusElement.textContent = status === 'active' ? '正常' :
                                      status === 'inactive' ? '失效' : '未知';
        }
    }
    
    async saveConfigCheckbox(configKey, inputId) {
        const input = document.getElementById(inputId);
        const value = input.checked;
        
        try {
            const response = await fetch(`/api/config/${configKey}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ value: value })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('配置保存成功', 'success');
                this.updateLastUpdateTime();
                
                // 更新本地配置
                this.setNestedValue(this.currentConfig, configKey, value);
            } else {
                this.showNotification(`配置保存失败: ${result.error}`, 'error');
            }
            
        } catch (error) {
            this.showNotification(`配置保存异常: ${error.message}`, 'error');
        }
    }
    
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((current, key) => {
            if (!current[key]) current[key] = {};
            return current[key];
        }, obj);
        target[lastKey] = value;
    }
    
    async reloadConfig() {
        try {
            const response = await fetch('/api/config/reload', {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('配置重新加载成功', 'success');
                this.loadCurrentConfig();
            } else {
                this.showNotification('配置重新加载失败', 'error');
            }
            
        } catch (error) {
            this.showNotification(`配置重新加载异常: ${error.message}`, 'error');
        }
    }
    
    exportConfig() {
        // 导出配置为JSON文件
        const configJson = JSON.stringify(this.currentConfig, null, 2);
        const blob = new Blob([configJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `config_export_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification('配置导出成功', 'success');
    }
    
    importConfig() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const importedConfig = JSON.parse(e.target.result);
                        this.handleConfigImport(importedConfig);
                    } catch (error) {
                        this.showNotification('配置文件格式错误', 'error');
                    }
                };
                reader.readAsText(file);
            }
        };
        
        input.click();
    }
    
    async handleConfigImport(importedConfig) {
        if (confirm('确定要导入新配置吗？这将覆盖当前配置。')) {
            try {
                // 这里可以实现批量配置更新
                this.showNotification('配置导入功能开发中...', 'warning');
            } catch (error) {
                this.showNotification(`配置导入失败: ${error.message}`, 'error');
            }
        }
    }
    
    showRestartConfirmation(configKey, value) {
        // 创建重启确认对话框
        const modal = document.createElement('div');
        modal.className = 'restart-confirmation-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>⚠️ 需要重启应用</h3>
                </div>
                <div class="modal-body">
                    <p>配置项 <strong>${configKey}</strong> 已更新为 <strong>${value}</strong></p>
                    <p>需要重启应用以使配置生效。您希望：</p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="this.restartApplication()">立即重启</button>
                    <button class="btn btn-secondary" onclick="this.closeRestartModal()">稍后手动重启</button>
                    <button class="btn btn-danger" onclick="this.closeRestartModal()">取消</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定事件
        modal.querySelector('.btn-primary').onclick = () => this.restartApplication();
        modal.querySelector('.btn-secondary').onclick = () => this.closeRestartModal();
        modal.querySelector('.btn-danger').onclick = () => this.closeRestartModal();

        this.currentRestartModal = modal;
    }

    async restartApplication() {
        try {
            this.showNotification('正在重启应用...', 'info');

            const response = await fetch('/api/restart_server', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ confirm: true })
            });

            const result = await response.json();

            if (result.status === 'success') {
                this.showNotification('重启请求已发送，页面将在几秒后刷新', 'success');
                this.closeRestartModal();

                // 等待服务器重启后刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 5000);
            } else {
                this.showNotification(`重启失败: ${result.message}`, 'error');
            }

        } catch (error) {
            // 服务器关闭是正常的重启过程
            this.showNotification('应用正在重启，页面将自动刷新', 'info');
            this.closeRestartModal();

            // 等待服务器重启后刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 5000);
        }
    }

    closeRestartModal() {
        if (this.currentRestartModal) {
            document.body.removeChild(this.currentRestartModal);
            this.currentRestartModal = null;
        }
    }

    handleConfigChange(data) {
        // 处理配置变更通知
        this.updateLastUpdateTime();
        this.updateSyncStatus('已同步');

        // 如果变更的配置项在当前界面，更新显示
        const element = document.querySelector(`[data-config-key="${data.key}"]`);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = Boolean(data.value);
            } else {
                element.value = data.value;
            }
        }
    }
    
    updateLastUpdateTime() {
        document.getElementById('last-update-time').textContent = new Date().toLocaleString();
    }
    
    updateSyncStatus(status) {
        document.getElementById('sync-status').textContent = status;
    }
    
    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `config-notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 3000);
    }
}

// 全局函数（供HTML调用）
let configManager;

function saveConfig(configKey, inputId) {
    configManager.saveConfig(configKey, inputId);
}

function saveConfigCheckbox(configKey, inputId) {
    configManager.saveConfigCheckbox(configKey, inputId);
}

function reloadConfig() {
    configManager.reloadConfig();
}

function exportConfig() {
    configManager.exportConfig();
}

function importConfig() {
    configManager.importConfig();
}

// 初始化配置管理器
document.addEventListener('DOMContentLoaded', () => {
    configManager = new ConfigurationManager();
});
```

## 🔌 后端API实现

### **Flask配置管理API**
```python
from flask import Blueprint, request, jsonify
from flask_socketio import emit
from datetime import datetime

config_api = Blueprint('config_api', __name__, url_prefix='/api/config')

@config_api.route('/<path:key>', methods=['GET'])
def get_config_value(key):
    """获取指定配置值"""
    try:
        value = config_center.get_config(key)
        return jsonify({
            "success": True,
            "key": key,
            "value": value
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@config_api.route('/<path:key>', methods=['PUT'])
def set_config_value(key):
    """设置指定配置值"""
    try:
        data = request.get_json()
        value = data.get('value')

        success = config_center.set_config(key, value)

        if success:
            # 通过WebSocket通知所有客户端
            emit('config_changed', {
                'key': key,
                'value': value,
                'timestamp': datetime.now().isoformat()
            }, broadcast=True, namespace='/config')

            return jsonify({
                "success": True,
                "key": key,
                "value": value
            })
        else:
            return jsonify({
                "success": False,
                "error": "配置设置失败"
            }), 500

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@config_api.route('', methods=['GET'])
def get_all_config():
    """获取所有配置"""
    try:
        config = config_center.get_all_config()
        return jsonify({
            "success": True,
            "config": config
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@config_api.route('/reload', methods=['POST'])
def reload_config():
    """重新加载配置文件"""
    try:
        success = config_center.reload_config()
        return jsonify({
            "success": success,
            "message": "配置重载成功" if success else "配置重载失败"
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

### **API密钥管理API**
```python
@app.route('/api/keys/update', methods=['POST'])
def update_api_key():
    """更新API密钥"""
    try:
        if not hasattr(self, 'api_db'):
            return jsonify({"success": False, "error": "API管理模块不可用"})

        data = request.get_json()
        provider = data.get('provider')
        api_key = data.get('api_key')

        if not provider or not api_key:
            return jsonify({"success": False, "error": "缺少必要参数"})

        # 存储API密钥到数据库（加密存储）
        config_data = {
            'model_name': provider,
            'role': 'primary',
            'api_type': 'primary',
            'provider': provider,
            'updated_at': datetime.now().isoformat()
        }

        self.api_db.store_api_configuration(api_key, config_data)

        return jsonify({
            "success": True,
            "message": f"API密钥已更新: {provider}"
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/keys/status')
def get_api_key_status():
    """获取API密钥状态"""
    try:
        if not hasattr(self, 'api_db'):
            return jsonify({"success": False, "error": "API管理模块不可用"})

        # 获取所有API配置状态
        status = {
            'gmi_deepseek_v3': 'unknown',
            'gmi_deepseek_r1': 'unknown',
            'chutes_deepseek_r1': 'unknown',
            'chutes_deepcoder_14b': 'unknown'
        }

        # TODO: 实现真实的状态检查逻辑
        # 这里可以调用api_db的方法来检查密钥是否存在和有效

        return jsonify({
            "success": True,
            "status": status
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/keys/test-all', methods=['POST'])
def test_all_api_keys():
    """测试所有API密钥"""
    try:
        if not hasattr(self, 'api_db'):
            return jsonify({"success": False, "error": "API管理模块不可用"})

        # TODO: 实现API密钥测试逻辑
        # 这里应该调用实际的API来测试密钥有效性
        results = {
            'gmi_deepseek_v3': {'status': True, 'message': '测试成功'},
            'gmi_deepseek_r1': {'status': True, 'message': '测试成功'},
            'chutes_deepseek_r1': {'status': True, 'message': '测试成功'},
            'chutes_deepcoder_14b': {'status': True, 'message': '测试成功'}
        }

        return jsonify({
            "success": True,
            "results": results
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)})
```
```

---

**本设计提供了完整的Web界面配置管理解决方案，支持实时配置修改和双向同步。**
