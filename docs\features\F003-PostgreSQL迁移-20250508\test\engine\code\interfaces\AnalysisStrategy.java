package org.xkong.cloud.business.internal.core.test.engine.interfaces;

import java.util.Map;

/**
 * 分析策略接口
 * 
 * AI实现此接口来定义新的分析策略
 * 引擎会自动发现和执行所有实现了此接口的策略
 */
public interface AnalysisStrategy {
    
    /**
     * 执行分析
     * 
     * @param context 分析上下文，包含所有可用的数据源
     * @return 分析结果
     */
    AnalysisResult analyze(AnalysisContext context);
    
    /**
     * 获取输出文件名
     * 
     * @return 输出文件名（不包含路径）
     */
    String getOutputFileName();
    
    /**
     * 获取分析策略名称
     * 
     * @return 策略名称，用于配置和日志
     */
    default String getStrategyName() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * 获取分析优先级
     * 
     * @return 优先级（数字越小优先级越高）
     */
    default int getPriority() {
        return 100;
    }
    
    /**
     * 是否启用此分析策略
     * 
     * @return true表示启用，false表示禁用
     */
    default boolean isEnabled() {
        return true;
    }
    
    /**
     * 获取分析策略的依赖
     * 
     * @return 依赖的其他策略名称列表
     */
    default String[] getDependencies() {
        return new String[0];
    }
    
    /**
     * 验证分析上下文是否满足执行条件
     * 
     * @param context 分析上下文
     * @return true表示可以执行，false表示跳过
     */
    default boolean canExecute(AnalysisContext context) {
        return true;
    }
    
    /**
     * 获取分析策略的配置参数
     * 
     * @return 配置参数映射
     */
    default Map<String, Object> getDefaultParameters() {
        return Map.of();
    }
}

/**
 * 分析上下文
 * 
 * 包含所有可用的数据源和配置信息
 */
interface AnalysisContext {
    
    /**
     * 获取AI测试结果
     */
    <T> T getTestResult(Class<T> type);
    
    /**
     * 获取项目结构信息
     */
    <T> T getProjectStructure(Class<T> type);
    
    /**
     * 获取配置信息
     */
    <T> T getConfiguration(String key, Class<T> type);
    
    /**
     * 获取历史数据
     */
    <T> T getHistoricalData(String dataType, Class<T> type);
    
    /**
     * 获取分析参数
     */
    Map<String, Object> getParameters();
    
    /**
     * 获取输出目录
     */
    String getOutputDirectory();
    
    /**
     * 获取时间戳
     */
    String getTimestamp();
}

/**
 * 分析结果
 * 
 * 包含分析产生的数据和元信息
 */
interface AnalysisResult {
    
    /**
     * 获取分析数据
     */
    Object getData();
    
    /**
     * 获取分析元信息
     */
    AnalysisMetadata getMetadata();
    
    /**
     * 是否成功
     */
    boolean isSuccess();
    
    /**
     * 获取错误信息（如果失败）
     */
    String getErrorMessage();
    
    /**
     * 获取警告信息
     */
    String[] getWarnings();
}

/**
 * 分析元信息
 */
interface AnalysisMetadata {
    
    /**
     * 分析策略名称
     */
    String getStrategyName();
    
    /**
     * 执行时间（毫秒）
     */
    long getExecutionTimeMs();
    
    /**
     * 数据大小（字节）
     */
    long getDataSizeBytes();
    
    /**
     * 分析置信度
     */
    double getConfidence();
    
    /**
     * 分析版本
     */
    String getVersion();
    
    /**
     * 额外属性
     */
    Map<String, Object> getAttributes();
}
