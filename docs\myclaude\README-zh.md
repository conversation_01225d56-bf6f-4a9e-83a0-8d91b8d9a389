# Claude Code 多智能体工作流系统

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Claude Code](https://img.shields.io/badge/Claude-Code-blue)](https://claude.ai/code)

> 将开发流程从手动命令链升级为自动化专家团队，95%质量保证。

## 🚀 从手工作坊到自动化工厂

**传统方式**: 手动命令链，需要持续监督
```bash
/ask → /code → /test → /review → /optimize
# 1-2小时手动操作，上下文污染，质量不确定
```

**现在**: 一键自动化专家工作流
```bash
/requirements-pilot "实现JWT用户认证系统"
# 30分钟自动执行，90%质量门控，零人工干预
```

## 🎯 核心价值主张

本仓库提供了一个**Claude Code元框架**，实现：

- **🤖 多智能体协调**: 专业AI团队并行工作
- **⚡ 质量门控自动化**: 95%阈值自动优化循环
- **🔄 工作流自动化**: 从需求到生产就绪代码
- **📊 上下文隔离**: 每个智能体保持专注专业性，无污染

## 📋 两种主要使用模式

### 1. 🏭 Requirements-Driven 工作流（自动化专家团队）

**架构**: 需求导向工作流与质量门控
```
requirements-generate → requirements-code → requirements-review → (≥90%?) → requirements-testing
         ↑                                              ↓ (<90%)
         ←←←←←← 自动优化循环 ←←←←←←
```

**使用方法**:
```bash
# 一条命令完成完整开发工作流
/requirements-pilot "构建用户管理系统，支持RBAC权限控制"

# 高级多阶段工作流
先使用 requirements-generate，然后 requirements-code，再用 requirements-review，
如果评分 ≥90% 则使用 requirements-testing
```

**质量评分体系** (总分100%):
- 功能性 (40%)
- 集成性 (25%) 
- 代码质量 (20%)
- 性能 (15%)

### 2. 🎛️ 自定义命令（手动编排）

**架构**: 针对性专业技能的独立斜杠命令
```bash
/ask     # 技术咨询和架构指导
/code    # 功能实现，带约束条件
/debug   # 使用UltraThink方法论的系统化问题分析
/test    # 全面测试策略
/review  # 多维度代码验证
/optimize # 性能优化协调
/bugfix  # 错误解决工作流
/refactor # 代码重构协调
/docs    # 文档生成
/think   # 高级思考和分析
```

**渐进式示例**:
```bash
# 逐步开发，手动控制每个环节
/ask "帮我理解微服务架构需求"
/code "实现带限流功能的网关"
/test "创建负载测试套件"
/review "验证安全性和性能"
/optimize "为生产环境优化性能"
```

## 🚀 快速开始

### 1. 配置设置

克隆或复制配置结构:
```bash
# 你的项目目录
├── commands/          # 11个专业斜杠命令
├── agents/           # 9个专家智能体配置  
└── CLAUDE.md         # 项目特定指导原则
```

### 2. 基本使用

**完整功能开发**:
```bash
/requirements-pilot "实现OAuth2认证，支持刷新令牌"
```

**手动开发流程**:
```bash
/ask "可扩展微服务的设计原则"
/code "实现OAuth2，遵循安全最佳实践"
/test "创建全面测试套件"
/review "验证实现质量"
```

### 3. 预期输出

**自动化工作流结果**:
- ✅ 需求确认，90+质量评分
- ✅ 实现就绪技术规格
- ✅ 生产就绪代码，遵循最佳实践
- ✅ 全面测试套件 (单元 + 集成 + 功能测试)
- ✅ 90%+ 质量验证评分

## 🏗️ 架构概览

### 核心组件

#### **Commands 目录** (`/commands/`)
- **咨询服务**: `/ask` - 架构指导（不修改代码）
- **实现工具**: `/code` - 带约束的功能开发
- **质量保证**: `/test`, `/review`, `/debug`
- **优化工具**: `/optimize`, `/refactor`
- **错误解决**: `/bugfix` - 系统化错误修复工作流
- **文档工具**: `/docs` - 文档生成
- **分析工具**: `/think` - 高级思考和分析
- **需求工具**: `/requirements-pilot` - 完整需求驱动工作流

#### **Agents 目录** (`/agents/`)
- **requirements-generate**: 为代码生成优化的技术规格生成
- **requirements-code**: 最小架构开销的直接实现智能体
- **requirements-review**: 专注功能性和可维护性的实用代码审查
- **requirements-testing**: 专注功能验证的实用测试智能体
- **bugfix**: 分析和修复软件缺陷的错误解决专家
- **bugfix-verify**: 客观评估的修复验证专家
- **code**: 直接实现的开发协调器
- **debug**: UltraThink系统化问题分析
- **optimize**: 性能优化协调

### 多智能体协调系统

**4个核心专家**:
1. **需求生成专家** - 实现就绪的技术规格
2. **代码实现专家** - 直接、实用的代码实现
3. **质量审查专家** - 实用质量审查与评分
4. **测试协调专家** - 功能验证和测试

**关键特性**:
- **独立上下文**: 专家间无上下文污染
- **质量门控**: 90%阈值自动进展判断
- **迭代改进**: 自动优化循环
- **可追溯性**: 完整规格 → 代码 → 测试追溯链

## 📚 工作流示例

### 企业用户认证系统

**输入**:
```bash
/requirements-pilot "企业JWT认证系统，支持RBAC，500并发用户，集成现有LDAP"
```

**自动化执行过程**:
1. **需求确认** (质量: 92/100) - 交互式澄清
   - 功能清晰度、技术特定性、实现完整性
   - **决策**: ≥90%，进入实现阶段

2. **第1轮** (质量: 83/100) - 基础实现
   - 问题: 错误处理不完整，集成问题
   - **决策**: <90%，重新开始并改进

3. **第2轮** (质量: 93/100) - 生产就绪
   - **决策**: ≥90%，进入功能测试

**最终交付物**:
- 质量评估的需求确认
- 实现就绪技术规格
- 带RBAC的实用JWT实现
- 带正确错误处理的LDAP集成
- 专注关键路径的功能测试套件

### API网关开发

**输入**:
```bash
/ask "高性能API网关的设计考虑"
# (交互式咨询阶段)

/code "实现微服务API网关，支持限流和熔断器"
# (实现阶段)

/test "为网关创建全面测试套件"
# (测试阶段)
```

**结果**:
- 性能模式的架构咨询
- 带负载均衡策略的详细规格
- 带监控的生产就绪实现

## 🔧 高级使用模式

### 自定义工作流组合

```bash
# 调试 → 修复 → 验证工作流
先使用 debug 分析 [性能问题]，
然后用 code 实现修复，
再用 review 确保质量

# 完整开发 + 优化流水线  
先使用 requirements-pilot 处理 [功能开发]，
然后用 review 进行质量验证，
如果评分 ≥95% 则使用 test 进行全面测试，
最后用 optimize 确保生产就绪
```

### 质量驱动开发

```bash
# 迭代质量改进
先使用 review 评估 [现有代码]，
如果评分 <95% 则用 code 基于反馈改进，
重复直到达到质量阈值
```

## 🎯 效益与影响

| 维度 | 手动命令 | Requirements-Driven工作流 |
|------|-------------|------------------|
| **复杂度** | 手动触发每个步骤 | 一键启动完整流水线 |
| **质量** | 主观评估 | 90%客观评分 |
| **上下文** | 污染，需要/clear | 隔离，无污染 |
| **专业性** | AI角色切换 | 专注的专家 |
| **错误处理** | 手动发现/修复 | 自动优化 |
| **时间投入** | 1-2小时手动工作 | 30分钟自动化 |

## 🔮 关键创新

### 1. **专家深度 > 通才广度**
每个智能体在独立上下文中专注各自领域专业知识，避免角色切换导致的质量下降。

### 2. **智能质量门控**  
90%客观评分，自动决策工作流进展或优化循环。

### 3. **完全自动化**
一条命令触发端到端开发工作流，最少人工干预。

### 4. **持续改进**
质量反馈驱动自动规格优化，创建智能改进循环。

## 🛠️ 配置说明

### 设置Sub-Agents

1. **创建智能体配置**: 将智能体文件复制到Claude Code配置中
2. **配置命令**: 设置工作流触发命令
3. **自定义质量门控**: 根据需要调整评分阈值

### 工作流定制

```bash
# 带特定质量要求的自定义工作流
先使用 requirements-pilot 处理 [严格安全要求和性能约束]，
然后用 review 验证并设置 [90%最低阈值]，
继续优化直到达到阈值
```

## 📖 命令参考

### Requirements 工作流
- `/requirements-pilot` - 完整的需求驱动开发工作流
- 交互式需求确认 → 技术规格 → 实现 → 测试

### 开发命令  
- `/ask` - 架构咨询（不修改代码）
- `/code` - 带约束的功能实现
- `/debug` - 系统化问题分析
- `/test` - 全面测试策略
- `/review` - 多维度代码验证

### 优化命令
- `/optimize` - 性能优化协调
- `/refactor` - 带质量门控的代码重构

### 其他命令
- `/bugfix` - 错误解决工作流
- `/docs` - 文档生成
- `/think` - 高级思考和分析

## 🤝 贡献

这是一个Claude Code配置框架。欢迎贡献：

1. **新智能体配置**: 特定领域的专业专家
2. **工作流模式**: 新的自动化序列  
3. **质量指标**: 增强的评分维度
4. **命令扩展**: 额外的开发阶段覆盖

## 📄 许可证

MIT许可证 - 详见[LICENSE](LICENSE)文件。

## 🙋 支持

- **文档**: 查看`/commands/`和`/agents/`获取详细规格
- **问题**: 使用GitHub issues报告bug和功能请求
- **讨论**: 分享工作流模式和定制化方案

---

## 🎉 开始使用

准备好转换你的开发工作流了吗？从这里开始：

```bash
/requirements-pilot "在这里描述你的第一个功能"
```

看着你的一行请求变成完整、经过测试、生产就绪的实现，90%质量保证。

**记住**: 专业软件来自专业流程。需求驱动工作流为您提供一个永不疲倦、始终专业的虚拟开发团队。

*让专业的AI做专业的事 - 开发从此变得优雅而高效。*

---

## 🌟 实战案例

### 用户管理系统开发

**需求**: 构建企业内部用户管理系统，500人规模，RBAC权限控制，集成OA系统

**传统方式** (1-2小时):
```bash
1. /ask 用户认证需求 → 手动澄清需求
2. /code 实现认证逻辑 → 手动编写代码  
3. /test 生成测试用例 → 手动测试
4. /review 代码审查 → 手动修复问题
5. /optimize 性能优化 → 手动优化
```

**Requirements-Driven方式** (30分钟自动):
```bash
/requirements-pilot "企业用户管理系统，500人规模，RBAC权限，OA系统集成"
```

**自动化执行结果**:
- 📋 **完整规格文档**: 需求分析、架构设计、实现计划
- 💻 **生产级代码**: JWT最佳实践、完善异常处理、性能优化
- 🧪 **全面测试覆盖**: 单元测试、集成测试、安全测试
- ✅ **质量保证**: 97/100评分，所有维度均达标

### 微服务API网关

**场景**: 高并发微服务架构，需要API网关进行流量管理

**Step 1 - 需求理解**:
```bash
/ask "设计高性能微服务API网关，需要考虑哪些方面？"
```

AI会深入询问：
- 预期QPS和并发量
- 路由策略和负载均衡
- 限流和熔断机制
- 监控和日志需求

**Step 2 - 需求驱动开发**:
```bash
/requirements-pilot "基于讨论，实现API网关完整功能"
```

自动化执行：
- **需求确认** - 交互式澄清和质量评估
- **技术规格** - 考虑高并发的架构设计
- **代码实现** - 详细的功能实现
- **质量验证** - 多维度质量评估
- **测试套件** - 功能和性能测试

## 💡 最佳实践

### 1. 需求澄清优先

**不要急于实现，先用/ask充分交流**

```bash
# 错误做法：直接开始
/requirements-pilot "用户管理系统"

# 正确做法：先理解需求  
/ask "企业用户管理系统需要考虑哪些方面？"
# 经过3-5轮对话澄清需求后
/requirements-pilot "基于讨论，实现企业用户管理系统"
```

### 2. 渐进式复杂度

从简单功能开始，逐步增加复杂性：

```bash
# 第一阶段：基础功能
/requirements-pilot "用户注册登录基础功能"

# 第二阶段：权限管理
/requirements-pilot "在现有基础上添加RBAC权限系统"

# 第三阶段：系统集成
/requirements-pilot "集成LDAP和SSO单点登录"
```

### 3. 质量优先策略

利用质量门控确保每个阶段的代码质量：

```bash
# 设置更高质量要求
先使用 requirements-pilot 实现功能，
然后用 review 验证质量，
如果评分 <98% 则继续优化，
达标后用 test 和 optimize 完善
```

## 🔍 深度解析：为什么这样更有效？

### 传统问题分析

**上下文污染**: 单一AI在不同角色间切换，质量逐步下降
```
AI扮演产品经理 → 架构师 → 开发者 → 测试工程师 → 优化专家
随着对话长度增加，AI的专业度和准确性下降
```

**手动管理开销**: 每个环节都需要人工判断和干预
```
是否需求已完整？ → 设计是否合理？ → 代码是否正确？ → 测试是否充分？
每个决策点都可能中断，需要重新组织思路
```

### Requirements-Driven解决方案

**专业化隔离**: 每个专家在独立上下文中工作
```
规格专家(独立) + 实现专家(独立) + 质量专家(独立) + 测试专家(独立)
专业深度最大化，角色混淆最小化
```

**自动化决策**: 基于客观指标的自动流程控制
```
质量评分 ≥90% → 自动进入下一阶段
质量评分 <90% → 自动返回优化，无需人工判断
```

## 🚀 开始你的AI工厂

从手工作坊升级到自动化工厂，只需要：

1. **配置一次**: 设置Requirements-Driven智能体和自定义命令
2. **使用一生**: 每个项目都能享受专业AI团队服务
3. **持续改进**: 工作流模式不断优化，开发效率持续提升

**记住**: 在AI时代，Requirements-Driven工作流让你拥有了一个永不疲倦、始终专业的虚拟开发团队。

*让专业的AI做专业的事，开发从此变得优雅而高效。*