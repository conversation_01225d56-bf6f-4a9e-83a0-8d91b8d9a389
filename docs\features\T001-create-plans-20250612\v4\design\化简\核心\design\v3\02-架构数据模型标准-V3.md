# V4.3方案：架构数据模型标准 (核心模型定义)

## 1. 文档信息

- **文档版本**: V4.3
- **创建日期**: 2025-08-13
- **目的**: 明确V4.3治理引擎内部及与外部AI（RooCode）交互所使用的核心数据模型，确保数据的一致性、可追溯性和可扩展性。本文档定义的所有数据模型，其标准持久化格式均为 YAML。为确保定义的精确性，本文将使用 JSON Schema 进行结构描述。

## 2. 核心数据模型概览

V4.3方案的核心在于将架构知识和治理过程标准化为机器可读的数据结构。以下是三个最关键的数据模型：

1.  **`AtomicConstraint` (原子约束)**: 架构“法典”的最小单元，描述一个具体的、可验证的架构规则。
2.  **`RichReport` (富报告)**: 治理引擎审计结果的唯一输出，包含详细的发现、建议和委托指令。
3.  **`SuperTaskInstruction` (超级任务指令)**: 引擎委托给RooCode编排器的标准化任务描述。

## 3. `AtomicConstraint` (原子约束)

`AtomicConstraint` 是V4.3“法典”的基石，它以结构化的方式定义了所有架构规则。

```json
{
  "type": "object",
  "title": "AtomicConstraint",
  "description": "架构治理的原子级约束，机器可读、可验证。",
  "properties": {
    "id": {
      "type": "string",
      "description": "唯一且不可变的约束ID，例如 'GC-PERF-001', 'AC-LPS-001'。遵循特定命名规范。",
      "pattern": "^(GC|AC)-[A-Z]{3}-\\d{3}$"
    },
    "parent_id": {
      "type": "string",
      "description": "如果此约束是派生或细化自另一个约束，则指向其父约束ID。",
      "nullable": true
    },
    "status": {
      "type": "string",
      "description": "约束的当前状态。",
      "enum": ["active", "deprecated", "draft", "violated"],
      "default": "draft"
    },
    "category": {
      "type": "string",
      "description": "约束所属的架构领域（如性能、安全、韧性、业务规则、依赖管理等）。",
      "enum": ["performance", "security", "resilience", "business_rule", "dependency_management", "coding_standard", "documentation_standard", "integration_pattern", "data_model"]
    },
    "type": {
      "type": "string",
      "description": "该约束在特定类别下的具体类型（如 thread_pool_config, exception_wrapping, http_client_policy, idempotency, api_version_mandate, data_contract_schema等）。"
    },
    "params": {
      "type": "object",
      "description": "与约束相关的参数，具体结构取决于 'type' 字段。",
      "additionalProperties": true
    },
    "description": {
      "type": "string",
      "description": "对约束的自然语言描述，清晰说明其目的和要求。"
    },
    "evidence_path": {
      "type": "array",
      "items": { "type": "string" },
      "description": "指向支持此约束的原始证据路径（如代码文件、旧文档URL、会议纪要等）。",
      "nullable": true
    },
    "created_at": {
      "type": "string",
      "format": "date-time",
      "description": "约束创建时间。"
    },
    "last_updated_at": {
      "type": "string",
      "format": "date-time",
      "description": "约束最后更新时间。"
    }
  },
  "required": ["id", "status", "category", "type", "params", "description"]
}
```

## 4. `RichReport` (富报告)

`RichReport` 是V4.3治理引擎审计结果的唯一输出，它以结构化的方式向人类和后续流程提供全面的信息。此报告的标准化持久化格式为 YAML。

**YAML 示例:**
```yaml
report_id: "report-20250813-1"
generated_at: "2025-08-13T12:00:00Z"
target_documents:
  - "docs/features/F009/design.md"
summary: "审计发现一个关键性能约束缺失。"
overall_status: "NON_COMPLIANT"
findings:
  - finding_id: "finding-001"
    description: "UserService 未实现异步处理机制。"
    severity: "CRITICAL"
    # ... 其他字段
```

**JSON Schema 定义:**
```json
{
  "type": "object",
  "title": "RichReport",
  "description": "V4.3治理引擎审计结果的标准化富报告。",
  "properties": {
    "report_id": {
      "type": "string",
      "description": "报告的唯一ID。"
    },
    "generated_at": {
      "type": "string",
      "format": "date-time",
      "description": "报告生成时间。"
    },
    "target_documents": {
      "type": "array",
      "items": { "type": "string" },
      "description": "本次审计所针对的设计文档路径列表（如 'docs/features/F009/design.md'）。"
    },
    "summary": {
      "type": "string",
      "description": "对本次审计核心发现和建议的自然语言总结。"
    },
    "overall_status": {
      "type": "string",
      "description": "本次审计的总体状态。",
      "enum": ["COMPLIANT", "NON_COMPLIANT", "INCOMPLETE_DESIGN", "PENDING_INVESTIGATION"]
    },
    "convergence_analysis": {
      "type": "object",
      "description": "与上一次报告的收敛性分析结果。在第二次及以后的审计运行时，此字段为必填项 (required)，是实现智能迭代的核心数据。",
      "properties": {
        "previous_report_id": { "type": "string" },
        "issues_resolved_count": { "type": "integer" },
        "issues_introduced_count": { "type": "integer" },
        "total_issues_current": { "type": "integer" },
        "convergence_status": { 
          "type": "string", 
          "enum": ["CONVERGING", "DIVERGING", "STABLE", "INITIAL"],
          "description": "CONVERGING: 问题正在减少; DIVERGING: 问题增多，可能由错误的修订引入; STABLE: 问题无变化。"
        },
        "analysis_summary": { "type": "string" }
      }
    },
    "findings": {
      "type": "array",
      "description": "结构化的发现点列表。",
      "items": {
        "type": "object",
        "properties": {
          "finding_id": {
            "type": "string",
            "description": "发现点的唯一ID。"
          },
          "description": {
            "type": "string",
            "description": "对问题的详细描述。"
          },
          "severity": {
            "type": "string",
            "description": "严重等级。",
            "enum": ["CRITICAL", "HIGH", "MEDIUM", "LOW"]
          },
          "source_location": {
            "type": "object",
            "description": "问题在设计文档中的位置。",
            "properties": {
              "file_path": { "type": "string" },
              "section_title": { "type": "string", "nullable": true },
              "line_numbers": { "type": "array", "items": { "type": "integer" }, "nullable": true }
            }
          },
          "atomic_constraint_draft": {
            "$ref": "#/definitions/AtomicConstraint",
            "description": "由Py AI生成的、与此发现点相关的AtomicConstraint草案。"
          },
          "pseudocode_block": {
            "type": "string",
            "description": "由Py AI生成的伪代码块，展示实施逻辑。",
            "format": "markdown"
          },
          "pseudocode_coverage_score": {
            "type": "number",
            "description": "伪代码块的逻辑覆盖率（0-100），由算法校验。",
            "minimum": 0,
            "maximum": 100
          },
          "recommended_task": {
            "type": "string",
            "description": "给RooCode编排器的`new_task`提示词，明确指示其修订任务。",
            "format": "markdown"
          }
        },
        "required": ["finding_id", "description", "severity", "atomic_constraint_draft", "pseudocode_block", "pseudocode_coverage_score", "recommended_task"]
      }
    }
  },
  "required": ["report_id", "generated_at", "target_documents", "summary", "overall_status", "findings"],
  "definitions": {
    "AtomicConstraint": { "$ref": "#/components/schemas/AtomicConstraint" }
  }
}
```

## 5. `SuperTaskInstruction` (超级任务指令)

`SuperTaskInstruction` 是我们引擎委托给RooCode编排器的标准化任务描述，它封装在`new_task`指令的`message`参数中。

```json
{
  "type": "object",
  "title": "SuperTaskInstruction",
  "description": "V4.3治理引擎委托给RooCode编排器的标准化任务指令。",
  "properties": {
    "parent_task": {
      "type": "string",
      "description": "此任务所属的父级任务描述，例如 'Audit Compliance with Architectural Constraint GC-ERR-001'。"
    },
    "overall_goal": {
      "type": "string",
      "description": "本次任务的总体目标。"
    },
    "subtasks": {
      "type": "array",
      "description": "子任务列表，RooCode将按顺序执行。",
      "items": {
        "type": "object",
        "properties": {
          "subtask_id": {
            "type": "string",
            "description": "子任务的唯一ID。"
          },
          "delegate_to_mode": {
            "type": "string",
            "description": "指示RooCode应该使用哪种模式来执行此子任务（例如 'code_analysis_mode', 'documentation_and_diagram_mode', 'build_tool_analysis_mode', 'code_implementation_mode'）。"
          },
          "instructions": {
            "type": "string",
            "description": "给RooCode的具体指令，详细说明任务内容、目标和预期输出格式。",
            "format": "markdown"
          },
          "target_file_path": {
            "type": "string",
            "description": "如果任务涉及修改特定文件，则指定文件路径。",
            "nullable": true
          },
          "expected_output_format": {
            "type": "string",
            "description": "RooCode完成任务后，预期返回的输出格式（如 'Markdown Report', 'JSON List', 'Code Diff'）。"
          },
          "context_data": {
            "type": "object",
            "description": "提供给RooCode的额外上下文数据，具体内容取决于任务类型。",
            "additionalProperties": true,
            "nullable": true
          }
        },
        "required": ["subtask_id", "delegate_to_mode", "instructions", "expected_output_format"]
      }
    },
    "completion_criteria": {
      "type": "string",
      "description": "RooCode何时可以调用`attempt_completion`的条件描述。",
      "format": "markdown"
    }
  },
  "required": ["parent_task", "overall_goal", "subtasks", "completion_criteria"]
}

```

## 6. 断言与证据数据模型（AssertionResult, Evidence）

为实现“确定性门禁”，比对引擎对每条设计约束产出一条或多条断言结果，并附可追溯的证据与 Serena 调用元数据。断言结果将作为一个独立的 `assertion_results.yaml` 文件输出。

```json
{
  "type": "object",
  "title": "AssertionResult",
  "description": "设计→代码对齐断言的判定结果与证据。",
  "properties": {
    "assertion_id": { "type": "string", "description": "断言唯一ID，例如 'AR-HTTP-001'。" },
    "constraint_id": { "type": "string", "description": "对应的 AtomicConstraint.id。" },
    "rule_id": { "type": "string", "description": "规则库条目ID，用于规则生命周期管理与审计。" },
    "classification": {
      "type": "string",
      "enum": ["OK", "MISSING", "CONFLICT", "LEGACY", "INDETERMINATE"],
      "description": "判定类别；INDETERMINATE 表示外部依赖失败或证据不足暂不可判。"
    },
    "severity": { "type": "string", "enum": ["CRITICAL", "HIGH", "MEDIUM", "LOW"], "description": "问题严重度（对 OK 可省略）。" },
    "subject": {
      "type": "object",
      "description": "被断言的目标实体（代码或设计）。",
      "properties": {
        "entity_type": { "type": "string", "enum": ["Class", "Interface", "Enum", "Method", "Field", "ConfigKey", "Module", "Constraint"] },
        "fqn": { "type": "string", "nullable": true },
        "file_path": { "type": "string", "nullable": true },
        "line": { "type": "integer", "nullable": true }
      }
    },
    "message": { "type": "string", "description": "人类可读的精简描述（确定性措辞）。" },
    "evidence": {
      "type": "array",
      "items": { "$ref": "#/definitions/Evidence" }
    },
    "serena_request": {
      "type": "object",
      "description": "与关键证据相关联的一次 Serena 工具调用摘要。",
      "properties": {
        "request_id": { "type": "string" },
        "tool_name": { "type": "string" },
        "params_hash": { "type": "string", "description": "请求参数规范化后的哈希，用于可复现性校验。" },
        "duration_ms": { "type": "integer" },
        "status": { "type": "string", "enum": ["OK", "TIMEOUT", "ERROR"] }
      },
      "nullable": true
    },
    "gating_effect": { "type": "boolean", "description": "是否触发门禁（非 OK 即 true；INDETERMINATE 可按策略决定）。" },
    "recommended_task_ref": { "type": "string", "nullable": true, "description": "若对应 RichReport.findings[*]，可填 finding_id。" },
    "timestamp": { "type": "string", "format": "date-time" }
  },
  "required": ["assertion_id", "constraint_id", "classification", "message", "gating_effect", "timestamp"],
  "definitions": {
    "Evidence": {
      "type": "object",
      "title": "Evidence",
      "properties": {
        "type": { "type": "string", "enum": ["SERENA_REFERENCE", "TEXT_DIFF", "CONFIG_VALUE", "STATIC_ANALYSIS"] },
        "description": { "type": "string" },
        "source_location": {
          "type": "object",
          "properties": {
            "file_path": { "type": "string" },
            "line": { "type": "integer", "nullable": true }
          },
          "nullable": true
        },
        "serena_request_id": { "type": "string", "nullable": true },
        "snippet": { "type": "string", "nullable": true, "description": "相关代码/配置摘录（可选）。" }
      },
      "required": ["type", "description"]
    }
  }
}
```

## 7. 微观图数据模型（可选持久化格式）

微观图通常驻留内存以支持断言计算。为调试或审计，可选择落盘如下结构，并持久化为 `micro_graph.yaml` 文件。

```json
{
  "type": "object",
  "title": "MicroGraph",
  "properties": {
    "nodes": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "node_id": { "type": "string" },
          "kind": { "type": "string", "enum": ["Class", "Interface", "Enum", "Method", "Field", "ConfigKey"] },
          "name": { "type": "string" },
          "fqn": { "type": "string", "nullable": true },
          "file_path": { "type": "string", "nullable": true },
          "span": { "type": "object", "properties": { "start_line": { "type": "integer" }, "end_line": { "type": "integer" } }, "nullable": true }
        },
        "required": ["node_id", "kind", "name"]
      }
    },
    "edges": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "src_node_id": { "type": "string" },
          "dst_node_id": { "type": "string" },
          "type": { "type": "string", "enum": ["CALLS", "IMPLEMENTS", "EXTENDS", "REFERENCES", "READS", "WRITES"] }
        },
        "required": ["src_node_id", "dst_node_id", "type"]
      }
    }
  },
  "required": ["nodes", "edges"]
}
```

## 8. RichReport 附件与制品引用（可选）

为保持 `RichReport` 精简，断言结果与微观图可作为附件制品引用：

```json
{
  "type": "object",
  "title": "ValidationArtifactsRef",
  "properties": {
    "assertion_results_path": { "type": "string", "description": "相对工作区路径，如 'out/assertion_results.json'" },
    "micro_graph_path": { "type": "string", "nullable": true }
  },
  "required": ["assertion_results_path"]
}
```

在实现上，`RichReport` 可通过 UI 元数据携带 `ValidationArtifactsRef`，不强制并入 `RichReport` 主体 JSON Schema，以避免报告体积过大与重复序列化。
