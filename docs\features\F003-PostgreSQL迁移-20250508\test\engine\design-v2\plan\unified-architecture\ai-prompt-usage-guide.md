# AI提示词使用指南

**文档更新时间**: 2025年1月15日 15:00:00（中国标准时间）
**用途**: 指导AI如何正确使用统一架构重构文档
**目标**: 确保AI能够按照文档准确执行重构，避免错误和遗漏

## 🚨 AI执行目录位置提醒（必读）

**⚠️ 重要：AI执行验证步骤和编译命令时，必须明确当前所处的目录位置，避免"找不到文件"错误**

### 🚨 代码类型声明
**重要**: 本文档中的所有代码都是**测试代码**，应放置在以下目录结构中：
```
xkongcloud-business-internal-core/
└── src/test/java/org/xkong/cloud/business/internal/core/
    ├── neural/ (现有神经可塑性测试系统)
    └── unified/ (新的统一架构测试组件)
```
**禁止**: 将任何代码放置到 src/main/java/ 目录下

### 当前文档位置
```
文档路径: docs/features/F003-PostgreSQL迁移-20250508/test/engine/design-v2/plan/unified-architecture/ai-prompt-usage-guide.md
工作目录: c:\ExchangeWorks\xkong\xkongcloud (项目根目录)
相对路径: docs\features\F003-PostgreSQL迁移-20250508\test\engine\design-v2\plan\unified-architecture\
```

### AI执行验证和编译时的目录要求
- **编译Java文件时**: 必须在项目根目录 `c:\ExchangeWorks\xkong\xkongcloud` 执行
- **运行测试时**: 必须在项目根目录执行，或在 `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-business-internal-core` 执行
- **查找源文件时**: 源文件位于 `src/main/java/` 或 `src/test/java/` 下
- **执行Maven命令时**: 必须在包含pom.xml的目录中执行

### 目录验证检查点
在执行任何编译或验证命令前，AI必须：
1. 确认当前工作目录位置
2. 验证目标文件路径是否正确
3. 检查依赖文件是否存在
4. 确保编译环境路径配置正确

## 🚨 使用前必读

### 文档使用原则
1. **严格按序执行**: 必须按照阶段→步骤→子步骤的顺序执行
2. **状态验证优先**: 每个步骤开始前必须验证前置状态
3. **检查点强制**: 每个步骤完成后必须执行检查点验证
4. **回滚准备**: 任何失败都必须立即执行回滚方案

### 文档关系图
```
unified-architecture-refactoring-plan.md (主计划)
    ↓ 引用
implementation-checklist.md (执行检查)
    ↓ 引用  
component-migration-guide.md (迁移指导)
    ↓ 引用
ai-memory-management-strategy.md (记忆管理)
```

## 📋 阶段性提示词模板

### 阶段开始提示词

```
我需要开始统一架构重构的[阶段名称]。

请按照以下步骤执行：

1. 首先阅读 docs/features/F003-PostgreSQL迁移-20250508/test/engine/design-v2/plan/unified-architecture/unified-architecture-refactoring-plan.md 中的[阶段名称]部分

2. 然后查看 docs/features/F003-PostgreSQL迁移-20250508/test/engine/design-v2/plan/unified-architecture/implementation-checklist.md 中对应的检查清单

3. 如果涉及组件迁移，参考 docs/features/F003-PostgreSQL迁移-20250508/test/engine/design-v2/plan/unified-architecture/component-migration-guide.md

4. 执行前请确认：
   - [ ] 前一阶段所有步骤已完成
   - [ ] 当前工作目录正确
   - [ ] 所有依赖文件存在
   - [ ] 编译环境正常

5. 开始执行第一个步骤，严格按照文档中的"前置状态"→"执行内容"→"验证标准"→"目标状态"流程

请确认你已理解上述要求，然后开始执行。
```

### 步骤执行提示词

```
我需要执行步骤[X.Y]：[步骤名称]

请严格按照以下流程执行：

**第1步：状态确认**
根据 unified-architecture-refactoring-plan.md 中步骤[X.Y]的"前置状态"，验证当前系统状态是否满足要求。

**第2步：执行内容**
严格按照文档中"执行内容"部分的代码和说明执行，不要偏离或添加额外功能。

**第3步：验证检查**
执行文档中"验证标准"部分的所有验证命令，确保结果符合预期。

**第4步：状态确认**
确认已达到"目标状态"中描述的所有要求。

**第5步：检查清单**
更新 implementation-checklist.md 中对应步骤的完成状态。

如果任何步骤失败，立即执行"回滚方案"。

请逐步执行并报告每个步骤的结果。
```

### 组件迁移提示词

```
我需要迁移组件：[现有组件名] → [新组件名]

请按照以下流程执行：

**第1步：迁移分析**
阅读 component-migration-guide.md 中对应组件的迁移指南，特别关注：
- 需要保留的正确逻辑
- 需要改进的部分
- 迁移实施步骤

**第2步：现有组件分析**
使用 codebase-retrieval 工具分析现有组件的实现，确认：
- 正确逻辑的具体代码位置
- 需要复用的方法和类
- 现有的测试用例

**第3步：新组件实现**
严格按照迁移指南中的步骤实现新组件：
- 复用现有的正确逻辑（不要重写）
- 实现统一接口规范
- 添加必要的新功能

**第4步：迁移验证**
执行迁移指南中的验证策略：
- 功能一致性验证
- 性能对比验证
- 集成测试验证

**第5步：文档更新**
更新相关文档和检查清单。

请确认你理解迁移要求，然后开始执行。
```

## 🧠 AI记忆管理提示词

### 工作开始提示词

```
我需要开始/继续统一架构重构工作。

请首先执行记忆管理协议：

**第1步：状态重建**
阅读 ai-memory-management-strategy.md 中的"中断恢复机制"，确认：
- 当前进度状态
- 已完成的组件
- 正在进行的任务
- 关键上下文信息

**第2步：上下文压缩**
使用文档中的"上下文压缩策略"，生成当前工作的压缩上下文，包括：
- 已完成组件列表
- 当前任务描述
- 关键代码片段
- 下一步骤计划

**第3步：检查点验证**
执行"检查点机制"中的验证项目：
- 微检查点（代码编译、逻辑正确等）
- 宏检查点（功能完整、测试通过等）

**第4步：工作准备**
确认工作环境和依赖关系正确，然后开始执行具体任务。

请按照上述步骤重建工作上下文，然后报告当前状态。
```

### 中断保存提示词

```
我需要暂停当前工作并保存状态。

请按照 ai-memory-management-strategy.md 中的"中断状态保存"协议执行：

**第1步：进度记录**
记录当前的：
- 阶段和步骤进度
- 正在编辑的文件
- 已完成的任务
- 待完成的任务

**第2步：状态保存**
保存关键的：
- 工作上下文
- 复用逻辑来源
- 接口要求
- 依赖关系

**第3步：恢复指令**
生成下次恢复工作时需要的：
- 检查命令
- 恢复命令
- 继续命令

请生成完整的中断状态保存报告。
```

## 🔍 错误处理提示词

### 编译错误处理

```
遇到编译错误，请按照以下步骤处理：

**第1步：错误分析**
分析编译错误信息，确定：
- 错误类型（语法、依赖、接口等）
- 错误位置（文件、行号）
- 可能原因

**第2步：文档检查**
检查相关文档中是否有：
- 类似错误的处理方案
- 正确的代码示例
- 依赖关系说明

**第3步：修复尝试**
按照文档指导修复错误，如果文档中没有相关信息，则：
- 检查是否偏离了文档要求
- 确认是否正确复用了现有逻辑
- 验证接口实现是否正确

**第4步：回滚决策**
如果无法快速修复（超过30分钟），执行回滚方案：
- 恢复到上一个稳定状态
- 重新分析问题
- 寻求替代方案

请报告错误详情和修复计划。
```

### 测试失败处理

```
遇到测试失败，请按照以下步骤处理：

**第1步：失败分析**
分析测试失败原因：
- 功能逻辑错误
- 接口不匹配
- 数据格式问题
- 环境配置问题

**第2步：对比验证**
如果是迁移组件，对比新旧组件：
- 输入参数是否一致
- 输出结果是否一致
- 异常处理是否一致

**第3步：修复策略**
根据失败类型选择修复策略：
- 逻辑错误：检查是否正确复用现有逻辑
- 接口问题：检查接口实现是否符合规范
- 格式问题：检查是否遵循JSON格式规范

**第4步：回归测试**
修复后执行完整的回归测试，确保：
- 当前测试通过
- 相关测试不受影响
- 集成测试正常

请报告测试失败详情和修复结果。
```

## 📚 常用查询提示词

### 查找现有逻辑

```
我需要查找[功能描述]的现有实现逻辑。

请使用 codebase-retrieval 工具查找：
- 相关的类和方法
- 具体的实现代码
- 使用示例
- 测试用例

然后对照 component-migration-guide.md 确认：
- 哪些逻辑需要保留
- 哪些逻辑需要改进
- 如何在新组件中复用

请提供详细的查找结果和复用建议。
```

### 验证接口规范

```
我需要验证[组件名]的接口实现是否符合规范。

请检查：
1. 是否实现了正确的接口
2. 方法签名是否正确
3. 返回值类型是否匹配
4. 异常处理是否完整

参考文档：
- unified-architecture-refactoring-plan.md 中的接口定义
- implementation-checklist.md 中的检查标准

请提供详细的验证结果。
```

### 检查依赖关系

```
我需要检查[组件名]的依赖关系是否正确。

请验证：
1. Spring依赖注入配置
2. 组件间调用关系
3. 配置参数引用
4. 循环依赖检查

参考 ai-memory-management-strategy.md 中的依赖关系卡片。

请报告依赖关系检查结果。
```

## ⚠️ 关键注意事项

### 绝对禁止的操作
1. **跳过验证步骤**: 每个步骤的验证都是必须的
2. **偏离文档要求**: 不要添加文档中没有要求的功能
3. **重写现有正确逻辑**: 必须复用现有的正确实现
4. **忽略回滚方案**: 失败时必须执行回滚

### 强制执行的操作
1. **状态确认**: 每个步骤开始前确认前置状态
2. **验证执行**: 每个步骤完成后执行验证命令
3. **文档更新**: 每个步骤完成后更新检查清单
4. **错误报告**: 任何异常都必须立即报告

### 质量保证要求
1. **编译通过**: 每次代码修改后必须编译通过
2. **测试通过**: 每个组件完成后必须测试通过
3. **接口一致**: 所有组件必须实现统一接口
4. **文档同步**: 代码和文档必须保持同步

通过严格遵循这些提示词模板和注意事项，确保AI能够准确、安全地完成统一架构重构任务。

## 🎯 实际使用示例

### 示例1：开始阶段一执行

**用户提示词**:
```
我需要开始统一架构重构的阶段一：核心组件设计与接口定义。

请按照以下步骤执行：

1. 首先阅读 docs/features/F003-PostgreSQL迁移-20250508/test/engine/design-v2/plan/unified-architecture/unified-architecture-refactoring-plan.md 中的阶段一部分

2. 然后查看 docs/features/F003-PostgreSQL迁移-20250508/test/engine/design-v2/plan/unified-architecture/implementation-checklist.md 中对应的检查清单

3. 执行前请确认：
   - [ ] 当前工作目录为 c:\ExchangeWorks\xkong\xkongcloud
   - [ ] 现有组件VersionCombinationManager、UniversalNamingStrategy、ReportDirectoryManager存在
   - [ ] Java编译环境正常

4. 开始执行第一个步骤：步骤1.1创建核心接口
   执行目录：c:\ExchangeWorks\xkong\xkongcloud (项目根目录)
   目标文件：xkongcloud-business-internal-core/src/test/java/org/xkong/cloud/business/internal/core/unified/UniversalReportOutputInterface.java
   严格按照文档中的"前置状态"→"执行内容"→"验证标准"→"目标状态"流程

请确认你已理解上述要求，然后开始执行。
```

**AI应该的响应流程**:
1. 使用view工具阅读unified-architecture-refactoring-plan.md的阶段一部分
2. 使用view工具查看implementation-checklist.md的对应检查清单
3. 确认前置条件（使用codebase-retrieval查找现有组件）
4. 开始执行步骤1.1，创建UniversalReportOutputInterface.java
5. 执行验证命令确认编译通过
6. 更新implementation-checklist.md的完成状态

### 示例2：组件迁移执行

**用户提示词**:
```
我需要迁移组件：VersionCombinationManager → UniversalVersionManager

请按照以下流程执行：

1. 阅读 component-migration-guide.md 中VersionCombinationManager的迁移指南
2. 使用 codebase-retrieval 分析现有VersionCombinationManager的实现
3. 严格按照迁移指南实现UniversalVersionManager，特别注意保留convertVersionToFormat方法的正确逻辑
4. 执行迁移验证确保功能一致性

请确认你理解迁移要求，然后开始执行。
```

**AI应该的响应流程**:
1. 使用view工具阅读component-migration-guide.md中的VersionCombinationManager迁移部分
2. 使用codebase-retrieval查找VersionCombinationManager的具体实现
3. 创建UniversalVersionManager.java，复用现有的正确逻辑
4. 实现UniversalVersionManagerInterface接口
5. 创建单元测试验证功能一致性
6. 更新相关文档

### 示例3：错误处理执行

**用户提示词**:
```
我在实现UniversalVersionManager时遇到编译错误：
"cannot find symbol: class UniversalVersionManagerInterface"

请按照错误处理流程解决这个问题。
```

**AI应该的响应流程**:
1. 分析错误：缺少接口定义
2. 检查文档：确认是否应该先创建接口
3. 查看implementation-checklist.md确认步骤顺序
4. 如果接口应该已存在但缺失，先创建接口
5. 如果步骤顺序错误，执行回滚方案
6. 重新按正确顺序执行

### 示例4：工作恢复执行

**用户提示词**:
```
我需要恢复之前中断的统一架构重构工作。

请首先执行记忆管理协议重建工作上下文，然后继续执行。
```

**AI应该的响应流程**:
1. 使用view工具阅读ai-memory-management-strategy.md的中断恢复机制
2. 检查当前工作目录状态
3. 查看implementation-checklist.md确认已完成的步骤
4. 使用codebase-retrieval确认已完成组件的状态
5. 生成当前工作上下文压缩报告
6. 确认下一步应该执行的任务
7. 继续执行具体步骤

## 📋 快速启动检查清单

### 开始重构前检查
- [ ] 确认工作目录：c:\ExchangeWorks\xkong\xkongcloud
- [ ] 确认文档存在：unified-architecture-refactoring-plan.md
- [ ] 确认文档存在：implementation-checklist.md
- [ ] 确认文档存在：component-migration-guide.md
- [ ] 确认文档存在：ai-memory-management-strategy.md
- [ ] 确认现有组件存在：VersionCombinationManager
- [ ] 确认现有组件存在：UniversalNamingStrategy
- [ ] 确认现有组件存在：ReportDirectoryManager
- [ ] 确认Java编译环境正常

### 每个步骤执行检查
- [ ] 阅读步骤文档完成
- [ ] 前置状态验证通过
- [ ] 执行内容严格按文档
- [ ] 验证命令执行成功
- [ ] 目标状态确认达成
- [ ] 检查清单状态更新

### 阶段完成检查
- [ ] 所有步骤检查清单完成
- [ ] 单元测试全部通过
- [ ] 集成测试全部通过
- [ ] 文档更新完成
- [ ] 下一阶段前置条件满足

## 🚨 紧急情况处理

### 如果AI偏离文档执行
**立即停止并执行**:
```
停止当前操作！

请重新阅读相关文档，确认：
1. 当前操作是否在文档要求范围内
2. 是否遗漏了必要的前置步骤
3. 是否正确理解了执行要求

然后重新开始正确的执行流程。
```

### 如果出现不可恢复错误
**立即执行回滚**:
```
执行紧急回滚！

1. 停止所有当前操作
2. 执行文档中的回滚方案
3. 恢复到上一个稳定状态
4. 重新分析问题原因
5. 制定新的执行计划

请报告回滚结果和问题分析。
```

通过这套完整的提示词使用指南，确保AI能够准确、安全、高效地完成统一架构重构任务。
