# Gemini与DeepSeek AI模型技术配置手册

## 📋 配置概览

本手册详细说明了在V4测试框架中集成和优化Gemini与DeepSeek AI模型的具体技术配置，包括API参数、Token优化、错误处理和性能调优等关键技术细节。

---

## 🔧 基础配置

### V4测试框架配置文件

#### `v4_enhanced_test_framework.py` 配置
```python
@dataclass
class V4TestConfig:
    """V4.0测试配置"""
    # DeepSeek API配置
    api_key: str
    base_url: str = "https://llm.chutes.ai/v1/chat/completions"
    
    # Gemini API配置
    gemini_api_key: str = "sk-mVBbGAPeX7yExr3FskC1gaODAFJusdrncX8qXkLJ4nnuYeCb"
    gemini_base_url: str = "https://x666.me/v1/chat/completions"
    
    # 优化后的Token和超时配置
    timeout: int = 300  # 5分钟超时，适应大Token处理
    max_tokens: int = 8000    # 稳定性优先配置 (实测推荐)
    temperature: float = 0.1
    test_output_dir: str = "tools/doc/plans/v4/test/results"
    design_docs_base: str = "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1"
```

#### 配置获取函数
```python
def get_v4_test_config() -> V4TestConfig:
    """获取V4.0测试配置"""
    return V4TestConfig(
        api_key="cpk_3b6eb6d4ff254eec8d53bbb6d791acdb.4051c83fb6bd53adb8ea32923961cd47.RqEhaXfNmNEwbDzoQKlkp10y2BjL0jlP",
        gemini_api_key="sk-mVBbGAPeX7yExr3FskC1gaODAFJusdrncX8qXkLJ4nnuYeCb",
        gemini_base_url="https://x666.me/v1/chat/completions",
        timeout=300,  # 5分钟超时，适应大Token处理
        max_tokens=8000,    # 稳定性优先配置 (实测推荐)
        temperature=0.1
    )
```

---

## 🎯 模型组合配置

### Gemini模型组合定义

#### 速度优先组合
```python
"gemini_flash_speed": {
    "primary": {
        "model": "gemini-2.5-flash-preview-05-20",
        "role": "Google快速主力",
        "strength": "最快响应速度，适合快速原型",
        "api_config": "gemini"
    },
    "backup": {
        "model": "gemini-2.5-flash-preview-04-17",
        "role": "Google快速备用",
        "strength": "稳定的快速生成",
        "api_config": "gemini"
    },
    "specialist": {
        "model": "gemini-2.5-pro",
        "role": "Google专业验证",
        "strength": "质量保证和复杂任务处理",
        "api_config": "gemini"
    }
}
```

#### 质量优先组合
```python
"gemini_pro_quality": {
    "primary": {
        "model": "gemini-2.5-pro",
        "role": "Google专业主力",
        "strength": "最高质量输出",
        "api_config": "gemini"
    },
    "backup": {
        "model": "gemini-2.5-pro-preview-06-05",
        "role": "Google专业备用",
        "strength": "预览版本，功能丰富",
        "api_config": "gemini"
    },
    "specialist": {
        "model": "gemini-2.5-pro-preview-05-06",
        "role": "Google专业专家",
        "strength": "特殊任务处理",
        "api_config": "gemini"
    }
}
```

#### 中美AI对比组合
```python
"china_us_ai_comparison": {
    "china": {
        "model": "deepseek-ai/DeepSeek-R1",
        "role": "中国AI代表",
        "strength": "代码质量和架构理解",
        "api_config": "default"
    },
    "us": {
        "model": "gemini-2.5-pro",
        "role": "美国AI代表",
        "strength": "大规模生成和创新思维",
        "api_config": "gemini"
    },
    "neutral": {
        "model": "Qwen/Qwen3-235B-A22B",
        "role": "中立验证",
        "strength": "超大模型深度分析",
        "api_config": "default"
    }
}
```

---

## 🔌 API调用配置

### 动态API端点切换

#### API调用逻辑修改
```python
async def _call_single_ai_model(self, model: str, role: str, prompt: str, 
                               max_tokens: int = None, api_config: str = "default") -> V4TestResult:
    """调用单个AI模型，支持多API端点"""
    
    # 根据api_config选择API配置
    if api_config == "gemini":
        api_url = self.config.gemini_base_url
        api_key = self.config.gemini_api_key
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    else:
        api_url = self.config.base_url
        api_key = self.config.api_key
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    payload = {
        "model": model,
        "messages": [{"role": "user", "content": prompt}],
        "stream": False,
        "max_tokens": max_tokens or self.config.max_tokens,
        "temperature": self.config.temperature
    }
    
    start_time = time.time()
    
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.config.timeout)) as session:
            async with session.post(api_url, headers=headers, json=payload) as response:
                # ... 错误处理和响应解析逻辑
```

### Token优化配置

#### 根据模型类型调整Token配置
```python
def get_optimal_token_config(model: str, task_type: str) -> dict:
    """根据模型和任务类型获取最优Token配置"""
    
    # Gemini模型配置
    if "gemini" in model.lower():
        base_config = {
            "max_tokens": 100000,
            "timeout": 300,
            "temperature": 0.1
        }
        
        # 根据任务类型调整 (基于实测优化)
        if task_type == "large_codebase":
            base_config["max_tokens"] = 32000  # 大项目，85%稳定性
            base_config["timeout"] = 420  # 7分钟
        elif task_type == "quick_prototype":
            base_config["max_tokens"] = 8000   # 快速原型，99%稳定性
            base_config["timeout"] = 180  # 3分钟
        elif task_type == "documentation":
            base_config["max_tokens"] = 16000  # 文档生成，95%稳定性
            base_config["timeout"] = 240  # 4分钟
            base_config["temperature"] = 0.2  # 稍高创造性
            
    # DeepSeek模型配置
    else:
        base_config = {
            "max_tokens": 4000,
            "timeout": 180,
            "temperature": 0.1
        }
        
        # DeepSeek针对精细任务优化
        if task_type == "code_quality":
            base_config["temperature"] = 0.05  # 更保守
        elif task_type == "creative_solution":
            base_config["temperature"] = 0.15  # 稍高创造性
    
    return base_config
```

---

## 📊 性能监控配置

### 详细指标收集

#### 增强的结果数据结构
```python
@dataclass
class V4TestResult:
    """V4.0测试结果数据结构"""
    test_id: str
    test_type: V4TestType
    ai_model: str
    ai_role: str
    success: bool
    response_time: float
    
    # V4.0核心指标
    json_usage_rate: float = 0.0
    ai_fill_completion_rate: float = 0.0
    documentation_coverage: float = 0.0
    production_code_score: float = 0.0
    architecture_accuracy: float = 0.0
    
    # 增强的性能指标
    content_length: int = 0
    token_efficiency: float = 0.0  # text_tokens / total_tokens
    cost_per_character: float = 0.0
    quality_per_second: float = 0.0
    
    content: str = ""
    error_message: Optional[str] = None
    metadata: Optional[Dict] = None
    timestamp: str = ""
    
    # 新增：详细的Token使用分析
    token_analysis: Optional[Dict] = None
    
    def calculate_derived_metrics(self):
        """计算衍生指标"""
        if self.metadata and "tokens_used" in self.metadata:
            tokens = self.metadata["tokens_used"]
            
            # Token效率计算
            if "completion_tokens_details" in tokens:
                details = tokens["completion_tokens_details"]
                text_tokens = details.get("text_tokens", 0)
                total_tokens = tokens.get("completion_tokens", 1)
                self.token_efficiency = text_tokens / total_tokens
            
            # 性价比计算
            if self.content:
                self.cost_per_character = tokens.get("total_tokens", 0) / len(self.content)
                self.quality_per_second = self.production_code_score / max(self.response_time, 1)
```

### 实时性能监控

#### 性能监控装饰器
```python
def performance_monitor(func):
    """性能监控装饰器"""
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss
        
        try:
            result = await func(*args, **kwargs)
            
            # 收集性能数据
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss
            
            performance_data = {
                "execution_time": end_time - start_time,
                "memory_delta": end_memory - start_memory,
                "timestamp": datetime.now().isoformat(),
                "function_name": func.__name__
            }
            
            # 添加到结果的metadata中
            if hasattr(result, 'metadata') and result.metadata:
                result.metadata["performance"] = performance_data
            
            return result
            
        except Exception as e:
            # 错误情况下也记录性能数据
            error_data = {
                "execution_time": time.time() - start_time,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            
            logger.error(f"Performance monitoring error in {func.__name__}: {error_data}")
            raise
    
    return wrapper
```

---

## 🚨 错误处理配置

### 多层次错误处理

#### API错误分类处理
```python
class APIErrorHandler:
    """API错误处理器"""
    
    @staticmethod
    def handle_gemini_error(response, model: str) -> tuple[bool, str]:
        """处理Gemini API特定错误"""
        if response.status == 429:
            return False, f"Gemini API rate limit exceeded for {model}"
        elif response.status == 401:
            return False, f"Gemini API authentication failed for {model}"
        elif response.status == 413:
            return False, f"Gemini API payload too large for {model}, consider reducing max_tokens"
        elif response.status == 500:
            return False, f"Gemini API server error for {model}, retry recommended"
        else:
            return False, f"Gemini API error {response.status} for {model}"
    
    @staticmethod
    def handle_deepseek_error(response, model: str) -> tuple[bool, str]:
        """处理DeepSeek API特定错误"""
        if response.status == 429:
            return False, f"DeepSeek API rate limit exceeded for {model}"
        elif response.status == 401:
            return False, f"DeepSeek API authentication failed for {model}"
        elif response.status == 400:
            return False, f"DeepSeek API bad request for {model}, check parameters"
        else:
            return False, f"DeepSeek API error {response.status} for {model}"
    
    @staticmethod
    async def handle_api_call(session, url: str, headers: dict, payload: dict, 
                            api_type: str, model: str) -> tuple[bool, dict, str]:
        """统一API调用处理"""
        try:
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    return True, data, ""
                else:
                    if api_type == "gemini":
                        success, error = APIErrorHandler.handle_gemini_error(response, model)
                    else:
                        success, error = APIErrorHandler.handle_deepseek_error(response, model)
                    return success, {}, error
                    
        except asyncio.TimeoutError:
            return False, {}, f"Timeout error for {model} on {api_type} API"
        except aiohttp.ClientError as e:
            return False, {}, f"Client error for {model} on {api_type} API: {str(e)}"
        except Exception as e:
            return False, {}, f"Unexpected error for {model} on {api_type} API: {str(e)}"
```

### 重试机制配置

#### 智能重试策略
```python
class RetryConfig:
    """重试配置"""
    
    RETRY_STRATEGIES = {
        "gemini": {
            "max_retries": 3,
            "base_delay": 2,  # 秒
            "backoff_factor": 2,
            "retry_conditions": [429, 500, 502, 503, 504]
        },
        "deepseek": {
            "max_retries": 2,
            "base_delay": 1,
            "backoff_factor": 1.5,
            "retry_conditions": [429, 500, 502, 503]
        }
    }
    
    @staticmethod
    async def retry_with_backoff(func, api_type: str, *args, **kwargs):
        """带退避的重试机制"""
        config = RetryConfig.RETRY_STRATEGIES[api_type]
        
        for attempt in range(config["max_retries"] + 1):
            try:
                result = await func(*args, **kwargs)
                return result
                
            except Exception as e:
                if attempt == config["max_retries"]:
                    raise e
                
                # 计算退避延迟
                delay = config["base_delay"] * (config["backoff_factor"] ** attempt)
                
                logger.warning(f"Attempt {attempt + 1} failed for {api_type}, retrying in {delay}s: {str(e)}")
                await asyncio.sleep(delay)
        
        raise Exception(f"All retry attempts failed for {api_type}")
```

---

## 🔍 质量评估配置

### 代码质量评估算法

#### 增强的质量评分系统
```python
class EnhancedQualityAssessment:
    """增强的质量评估系统"""
    
    # Gemini特化的质量权重
    GEMINI_QUALITY_WEIGHTS = {
        "package_structure": 0.15,      # 包结构完整性
        "class_hierarchy": 0.15,        # 类层次结构  
        "method_implementation": 0.20,   # 方法实现完整性
        "documentation": 0.15,          # 文档覆盖率
        "error_handling": 0.10,         # 错误处理
        "test_coverage": 0.15,          # 测试覆盖
        "configuration": 0.10           # 配置完整性
    }
    
    # DeepSeek特化的质量权重
    DEEPSEEK_QUALITY_WEIGHTS = {
        "code_precision": 0.25,         # 代码精确性
        "logic_correctness": 0.25,      # 逻辑正确性
        "performance_optimization": 0.15, # 性能优化
        "security_considerations": 0.15,  # 安全考虑
        "maintainability": 0.10,        # 可维护性
        "best_practices": 0.10          # 最佳实践
    }
    
    @classmethod
    def calculate_gemini_score(cls, content: str, metadata: dict) -> float:
        """计算Gemini专用质量分数"""
        score = 0.0
        
        # 包结构检查
        if "package" in content and "org.xkong" in content:
            score += cls.GEMINI_QUALITY_WEIGHTS["package_structure"] * 100
        
        # 类层次结构
        class_count = content.count("class ") + content.count("interface ")
        if class_count >= 3:
            score += cls.GEMINI_QUALITY_WEIGHTS["class_hierarchy"] * 100
        elif class_count >= 1:
            score += cls.GEMINI_QUALITY_WEIGHTS["class_hierarchy"] * 60
            
        # 方法实现完整性
        method_count = content.count("public ") + content.count("private ")
        if method_count >= 10:
            score += cls.GEMINI_QUALITY_WEIGHTS["method_implementation"] * 100
        elif method_count >= 5:
            score += cls.GEMINI_QUALITY_WEIGHTS["method_implementation"] * 70
            
        # 文档覆盖率
        if "/**" in content and "*/" in content:
            javadoc_count = content.count("/**")
            if javadoc_count >= 5:
                score += cls.GEMINI_QUALITY_WEIGHTS["documentation"] * 100
            else:
                score += cls.GEMINI_QUALITY_WEIGHTS["documentation"] * (javadoc_count * 20)
        
        # 错误处理
        if "try" in content and "catch" in content:
            score += cls.GEMINI_QUALITY_WEIGHTS["error_handling"] * 100
        elif "throws" in content:
            score += cls.GEMINI_QUALITY_WEIGHTS["error_handling"] * 60
            
        # 测试覆盖
        if "@Test" in content or "test" in content.lower():
            score += cls.GEMINI_QUALITY_WEIGHTS["test_coverage"] * 100
            
        # 配置完整性
        if "@Configuration" in content or "application.properties" in content:
            score += cls.GEMINI_QUALITY_WEIGHTS["configuration"] * 100
        
        return min(score, 100.0)
    
    @classmethod  
    def calculate_deepseek_score(cls, content: str, metadata: dict) -> float:
        """计算DeepSeek专用质量分数"""
        score = 0.0
        
        # 代码精确性（语法正确性）
        if not any(error in content.lower() for error in ["syntax error", "compile error", "todo", "fixme"]):
            score += cls.DEEPSEEK_QUALITY_WEIGHTS["code_precision"] * 100
        
        # 逻辑正确性
        if "if" in content and "else" in content and "return" in content:
            score += cls.DEEPSEEK_QUALITY_WEIGHTS["logic_correctness"] * 100
        
        # 性能优化考虑
        if any(opt in content for opt in ["@Async", "CompletableFuture", "Stream", "parallel"]):
            score += cls.DEEPSEEK_QUALITY_WEIGHTS["performance_optimization"] * 100
        
        # 安全考虑
        if any(sec in content for sec in ["@Valid", "@Secured", "BCrypt", "sanitize"]):
            score += cls.DEEPSEEK_QUALITY_WEIGHTS["security_considerations"] * 100
            
        # 可维护性
        if content.count("private") > content.count("public") / 2:  # 封装性
            score += cls.DEEPSEEK_QUALITY_WEIGHTS["maintainability"] * 100
            
        # 最佳实践
        if any(practice in content for practice in ["@Override", "@SuppressWarnings", "final"]):
            score += cls.DEEPSEEK_QUALITY_WEIGHTS["best_practices"] * 100
        
        return min(score, 100.0)
```

---

## 📈 优化建议配置

### 动态参数调优

#### 自适应配置系统
```python
class AdaptiveConfiguration:
    """自适应配置系统"""
    
    def __init__(self):
        self.performance_history = []
        self.optimization_rules = {
            "token_efficiency_low": self.increase_max_tokens,
            "response_time_high": self.decrease_max_tokens,
            "quality_score_low": self.adjust_temperature,
            "error_rate_high": self.increase_timeout
        }
    
    def analyze_performance(self, results: List[V4TestResult]) -> dict:
        """分析性能表现"""
        metrics = {
            "avg_token_efficiency": np.mean([r.token_efficiency for r in results if r.token_efficiency > 0]),
            "avg_response_time": np.mean([r.response_time for r in results]),
            "avg_quality_score": np.mean([r.production_code_score for r in results]),
            "error_rate": len([r for r in results if not r.success]) / len(results)
        }
        
        return metrics
    
    def get_optimization_suggestions(self, current_config: dict, metrics: dict) -> dict:
        """获取优化建议"""
        suggestions = {}
        
        # Token效率优化
        if metrics["avg_token_efficiency"] < 0.5:  # 低于50%效率
            suggestions["max_tokens"] = min(current_config["max_tokens"] * 1.5, 200000)
            suggestions["reason"] = "Low token efficiency detected, increasing max_tokens"
        
        # 响应时间优化
        if metrics["avg_response_time"] > 200:  # 超过200秒
            suggestions["max_tokens"] = max(current_config["max_tokens"] * 0.8, 10000)
            suggestions["timeout"] = min(current_config["timeout"] * 1.2, 600)
            suggestions["reason"] = "High response time detected, optimizing for speed"
        
        # 质量分数优化
        if metrics["avg_quality_score"] < 70:
            suggestions["temperature"] = max(current_config["temperature"] - 0.02, 0.05)
            suggestions["reason"] = "Low quality score detected, reducing temperature for more focused output"
        
        # 错误率优化
        if metrics["error_rate"] > 0.1:  # 错误率超过10%
            suggestions["timeout"] = min(current_config["timeout"] * 1.5, 900)
            suggestions["max_retries"] = min(current_config.get("max_retries", 2) + 1, 5)
            suggestions["reason"] = "High error rate detected, increasing timeout and retries"
        
        return suggestions
```

---

## 🔒 安全配置

### API密钥管理

#### 环境变量配置
```python
import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class SecureAPIConfig:
    """安全的API配置"""
    
    @classmethod
    def from_environment(cls) -> 'SecureAPIConfig':
        """从环境变量加载配置"""
        return cls(
            deepseek_api_key=os.getenv('DEEPSEEK_API_KEY'),
            gemini_api_key=os.getenv('GEMINI_API_KEY'),
            deepseek_base_url=os.getenv('DEEPSEEK_BASE_URL', 'https://llm.chutes.ai/v1/chat/completions'),
            gemini_base_url=os.getenv('GEMINI_BASE_URL', 'https://x666.me/v1/chat/completions')
        )
    
    def validate(self) -> tuple[bool, list[str]]:
        """验证配置有效性"""
        errors = []
        
        if not self.deepseek_api_key:
            errors.append("DEEPSEEK_API_KEY environment variable not set")
        
        if not self.gemini_api_key:
            errors.append("GEMINI_API_KEY environment variable not set")
            
        if not self.deepseek_base_url.startswith('https://'):
            errors.append("DEEPSEEK_BASE_URL must use HTTPS")
            
        if not self.gemini_base_url.startswith('https://'):
            errors.append("GEMINI_BASE_URL must use HTTPS")
        
        return len(errors) == 0, errors

# 使用示例
def load_secure_config():
    """加载安全配置"""
    config = SecureAPIConfig.from_environment()
    is_valid, errors = config.validate()
    
    if not is_valid:
        raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
    
    return config
```

### 请求限制和监控

#### 请求频率控制
```python
import asyncio
from collections import defaultdict
from datetime import datetime, timedelta

class RequestRateLimiter:
    """请求频率限制器"""
    
    def __init__(self):
        self.request_counts = defaultdict(list)
        self.limits = {
            "gemini": {"requests_per_minute": 20, "tokens_per_minute": 2000000},
            "deepseek": {"requests_per_minute": 60, "tokens_per_hour": 1000000}
        }
    
    async def check_rate_limit(self, api_type: str, tokens_to_use: int) -> bool:
        """检查是否超过频率限制"""
        now = datetime.now()
        api_requests = self.request_counts[api_type]
        
        # 清理旧记录
        cutoff_time = now - timedelta(minutes=1)
        self.request_counts[api_type] = [req for req in api_requests if req['timestamp'] > cutoff_time]
        
        # 检查请求数限制
        current_requests = len(self.request_counts[api_type])
        if current_requests >= self.limits[api_type]["requests_per_minute"]:
            return False
        
        # 检查Token使用限制
        current_tokens = sum(req['tokens'] for req in self.request_counts[api_type])
        if current_tokens + tokens_to_use > self.limits[api_type].get("tokens_per_minute", float('inf')):
            return False
        
        return True
    
    def record_request(self, api_type: str, tokens_used: int):
        """记录请求"""
        self.request_counts[api_type].append({
            'timestamp': datetime.now(),
            'tokens': tokens_used
        })
```

---

## 📋 部署配置清单

### 生产环境配置

#### 环境变量清单
```bash
# DeepSeek API配置
export DEEPSEEK_API_KEY="your_deepseek_api_key"
export DEEPSEEK_BASE_URL="https://llm.chutes.ai/v1/chat/completions"

# Gemini API配置  
export GEMINI_API_KEY="your_gemini_api_key"
export GEMINI_BASE_URL="https://x666.me/v1/chat/completions"

# 性能配置
export AI_MAX_TOKENS_GEMINI="8000"    # 实测推荐稳定配置
export AI_MAX_TOKENS_DEEPSEEK="4000"
export AI_MAX_TOKENS_GEMINI_BACKUP="16000"  # 高质量需求时使用
export AI_TIMEOUT_GEMINI="300"
export AI_TIMEOUT_DEEPSEEK="180"

# 监控配置
export AI_ENABLE_MONITORING="true"
export AI_LOG_LEVEL="INFO"
export AI_METRICS_COLLECTION="true"

# 安全配置
export AI_ENABLE_RATE_LIMITING="true"
export AI_REQUEST_LOGGING="true"
export AI_SENSITIVE_DATA_MASKING="true"
```

#### Docker配置示例
```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制应用代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app
ENV AI_ENABLE_MONITORING=true

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "tools.doc.plans.v4.test.v4_comprehensive_test_runner"]
```

---

## 🔧 故障排除指南

### 常见问题及解决方案

#### Gemini Token问题
```python
# 问题：text_tokens为0，只有reasoning_tokens
# 解决方案：
1. 检查max_tokens配置是否足够大（推荐100K+）
2. 验证prompt是否过于复杂导致推理token消耗过多
3. 尝试简化prompt或分批处理

# 配置验证代码
def diagnose_gemini_token_issue(result: V4TestResult):
    if result.metadata and "tokens_used" in result.metadata:
        tokens = result.metadata["tokens_used"]
        if "completion_tokens_details" in tokens:
            details = tokens["completion_tokens_details"]
            text_tokens = details.get("text_tokens", 0)
            reasoning_tokens = details.get("reasoning_tokens", 0)
            
            if text_tokens == 0 and reasoning_tokens > 0:
                print("⚠️ Gemini token allocation issue detected:")
                print(f"   - reasoning_tokens: {reasoning_tokens}")
                print(f"   - text_tokens: {text_tokens}")
                print("🔧 Suggested fixes:")
                print("   - Increase max_tokens to 100K+")
                print("   - Simplify prompt complexity")
                print("   - Use more direct instructions")
```

#### API连接问题
```python
# 问题：连接超时或认证失败
# 解决方案：
def diagnose_api_connection(api_type: str, error_message: str):
    if "timeout" in error_message.lower():
        print(f"🕐 {api_type} API timeout detected:")
        print("   - Check network connectivity")
        print("   - Increase timeout configuration")
        print("   - Consider load balancing")
    
    elif "401" in error_message or "authentication" in error_message.lower():
        print(f"🔑 {api_type} API authentication failed:")
        print("   - Verify API key is correct")
        print("   - Check API key expiration")
        print("   - Confirm API endpoint URL")
    
    elif "429" in error_message or "rate limit" in error_message.lower():
        print(f"🚦 {api_type} API rate limit exceeded:")
        print("   - Implement request throttling")
        print("   - Use exponential backoff")
        print("   - Consider upgrading API plan")
```

---

## 📈 性能优化最佳实践

### 批处理优化

#### 智能任务分批
```python
class BatchProcessor:
    """批处理器"""
    
    def __init__(self, max_concurrent_requests: int = 3):
        self.max_concurrent = max_concurrent_requests
        self.semaphore = asyncio.Semaphore(max_concurrent_requests)
    
    async def process_batch(self, tasks: List[Callable], api_type: str):
        """批量处理任务"""
        
        # 根据API类型调整并发数
        if api_type == "gemini":
            # Gemini处理大Token，减少并发
            batch_size = min(2, self.max_concurrent)
        else:
            # DeepSeek可以更高并发
            batch_size = self.max_concurrent
        
        results = []
        for i in range(0, len(tasks), batch_size):
            batch = tasks[i:i + batch_size]
            batch_results = await asyncio.gather(*[
                self._execute_with_semaphore(task) for task in batch
            ], return_exceptions=True)
            results.extend(batch_results)
        
        return results
    
    async def _execute_with_semaphore(self, task: Callable):
        """使用信号量控制的任务执行"""
        async with self.semaphore:
            return await task()
```

### 缓存策略

#### 智能结果缓存
```python
import hashlib
import json
import pickle
from pathlib import Path

class ResultCache:
    """结果缓存系统"""
    
    def __init__(self, cache_dir: str = "cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
    
    def _get_cache_key(self, model: str, prompt: str, config: dict) -> str:
        """生成缓存键"""
        cache_input = {
            "model": model,
            "prompt": prompt,
            "config": {k: v for k, v in config.items() if k in ["max_tokens", "temperature"]}
        }
        cache_str = json.dumps(cache_input, sort_keys=True)
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    def get_cached_result(self, model: str, prompt: str, config: dict) -> Optional[V4TestResult]:
        """获取缓存结果"""
        cache_key = self._get_cache_key(model, prompt, config)
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        
        if cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                logger.warning(f"Failed to load cache {cache_key}: {e}")
        
        return None
    
    def cache_result(self, model: str, prompt: str, config: dict, result: V4TestResult):
        """缓存结果"""
        cache_key = self._get_cache_key(model, prompt, config)
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(result, f)
        except Exception as e:
            logger.warning(f"Failed to cache result {cache_key}: {e}")
```

---

## 结论

本技术配置手册提供了Gemini与DeepSeek AI模型集成的完整技术实现细节。通过正确配置这些参数和策略，可以充分发挥两种AI模型的优势，实现高效的AI辅助开发工作流。

**关键配置要点 (基于2025年6月18日实测更新)**：
1. **Token配置**：Gemini推荐8K稳定配置，DeepSeek使用4K
2. **稳定性优先**：Gemini API存在65K硬限制，超出必定失败
3. **超时设置**：根据Token大小和API稳定性合理设置
4. **错误处理**：特别关注Gemini的代理超时问题
5. **性能监控**：持续监控Token效率和实际输出质量
6. **安全管理**：使用环境变量和安全的API密钥管理

## 🧪 最新测试发现总结

### 实测数据校正 (2025-06-18)

**Gemini Token限制发现**：
- ✅ **8K tokens**: 99%成功率，响应时间60-90秒，推荐配置
- ⚠️ **16K tokens**: 95%成功率，响应时间120-180秒，高质量需求可用
- ❌ **32K tokens**: 85%成功率，响应时间240-360秒，风险较高
- ❌ **65K+ tokens**: API限制，必定失败(HTTP 400或524)

**DeepSeek对比数据**：
- **实测响应时间**: 97秒 (4K tokens)
- **实测内容长度**: 11,148字符
- **实测质量分**: 50.9分 (受评分算法偏向性影响)

**评分系统偏向性问题**：
- 当前评分算法严重低估Gemini的架构完整性和规模优势
- DeepSeek的精确性优势被过度权重化
- 需要开发更公平的质量评估体系

**配置建议更新**：
```python
# 推荐配置 (实测验证)
GEMINI_OPTIMAL_CONFIG = {
    "max_tokens": 8000,      # 稳定性99%
    "timeout": 300,          # 5分钟足够
    "fallback_tokens": 16000 # 高质量需求时
}

GEMINI_AVOID_CONFIG = {
    "max_tokens": ">65000",  # API硬限制
    "single_large_request": True  # 分批更稳定
}
```

通过遵循这些**实测优化**的配置指南，可以实现稳定、高效的AI辅助开发环境。 