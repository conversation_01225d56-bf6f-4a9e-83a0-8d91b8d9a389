/home/<USER>/apps/jdk-21.0.5/bin/java -javaagent:/home/<USER>/apps/idea-IU-243.23654.189/lib/idea_rt.jar=37743:/home/<USER>/apps/idea-IU-243.23654.189/bin -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -classpath /media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/test-classes:/media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/classes:/home/<USER>/works/project/mvnRepository/com/xfvape/uid/uid-generator/0.0.4-RELEASE/uid-generator-0.0.4-RELEASE.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-core/6.2.6/spring-core-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-jcl/6.2.6/spring-jcl-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-beans/6.2.6/spring-beans-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/mybatis/mybatis/3.2.3/mybatis-3.2.3.jar:/home/<USER>/works/project/mvnRepository/org/mybatis/mybatis-spring/1.2.4/mybatis-spring-1.2.4.jar:/home/<USER>/works/project/mvnRepository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/home/<USER>/works/project/mvnRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/log4j-over-slf4j/2.0.17/log4j-over-slf4j-2.0.17.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-context/6.2.6/spring-context-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-aop/6.2.6/spring-aop-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-expression/6.2.6/spring-expression-6.2.6.jar:/home/<USER>/works/project/mvnRepository/io/micrometer/micrometer-observation/1.14.6/micrometer-observation-1.14.6.jar:/home/<USER>/works/project/mvnRepository/io/micrometer/micrometer-commons/1.14.6/micrometer-commons-1.14.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-jdbc/6.2.6/spring-jdbc-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-tx/6.2.6/spring-tx-6.2.6.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-annotations/2.18.3/jackson-annotations-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-core/2.18.3/jackson-core-2.18.3.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter-test/3.4.5/spring-boot-starter-test-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter/3.4.5/spring-boot-starter-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot/3.4.5/spring-boot-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-autoconfigure/3.4.5/spring-boot-autoconfigure-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter-logging/3.4.5/spring-boot-starter-logging-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/home/<USER>/works/project/mvnRepository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/home/<USER>/works/project/mvnRepository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-test/3.4.5/spring-boot-test-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-test-autoconfigure/3.4.5/spring-boot-test-autoconfigure-3.4.5.jar:/home/<USER>/works/project/mvnRepository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/home/<USER>/works/project/mvnRepository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/home/<USER>/works/project/mvnRepository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/home/<USER>/works/project/mvnRepository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/home/<USER>/works/project/mvnRepository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/home/<USER>/works/project/mvnRepository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/home/<USER>/works/project/mvnRepository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/home/<USER>/works/project/mvnRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-test/6.2.6/spring-test-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/xmlunit/xmlunit-core/2.10.0/xmlunit-core-2.10.0.jar:/home/<USER>/works/project/mvnRepository/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar:/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar:/home/<USER>/works/project/mvnRepository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/home/<USER>/works/project/mvnRepository/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-launcher/1.11.4/junit-platform-launcher-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/testcontainers/1.19.7/testcontainers-1.19.7.jar:/home/<USER>/works/project/mvnRepository/junit/junit/4.13.2/junit-4.13.2.jar:/home/<USER>/works/project/mvnRepository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/home/<USER>/works/project/mvnRepository/org/apache/commons/commons-compress/1.24.0/commons-compress-1.24.0.jar:/home/<USER>/works/project/mvnRepository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/home/<USER>/works/project/mvnRepository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-api/3.3.6/docker-java-api-3.3.6.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-transport-zerodep/3.3.6/docker-java-transport-zerodep-3.3.6.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-transport/3.3.6/docker-java-transport-3.3.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/postgresql/1.19.7/postgresql-1.19.7.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/jdbc/1.20.6/jdbc-1.20.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/database-commons/1.20.6/database-commons-1.20.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/junit-jupiter/1.19.7/junit-jupiter-1.19.7.jar:/home/<USER>/works/project/mvnRepository/org/openjdk/jmh/jmh-core/1.37/jmh-core-1.37.jar:/home/<USER>/works/project/mvnRepository/net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar:/home/<USER>/works/project/mvnRepository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/home/<USER>/works/project/mvnRepository/org/openjdk/jmh/jmh-generator-annprocess/1.37/jmh-generator-annprocess-1.37.jar:/home/<USER>/works/project/mvnRepository/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/home/<USER>/works/project/mvnRepository/org/postgresql/postgresql/42.7.5/postgresql-42.7.5.jar:/home/<USER>/works/project/mvnRepository/org/checkerframework/checker-qual/3.48.3/checker-qual-3.48.3.jar:/home/<USER>/works/project/mvnRepository/com/github/oshi/oshi-core/6.5.0/oshi-core-6.5.0.jar:/home/<USER>/works/project/mvnRepository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/home/<USER>/works/project/mvnRepository/net/java/dev/jna/jna-platform/5.14.0/jna-platform-5.14.0.jar:/home/<USER>/works/project/mvnRepository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/home/<USER>/works/project/mvnRepository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar:/home/<USER>/works/project/mvnRepository/ch/qos/logback/logback-classic/1.5.16/logback-classic-1.5.16.jar:/home/<USER>/works/project/mvnRepository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/home/<USER>/works/project/mvnRepository/org/xkongkit/xkongkit-core/1.0.0-SNAPSHOT/xkongkit-core-1.0.0-SNAPSHOT.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/kryo/5.5.0/kryo-5.5.0.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/home/<USER>/works/project/mvnRepository/org/lmdbjava/lmdbjava/0.9.1/lmdbjava-0.9.1.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-constants/0.10.4/jnr-constants-0.10.4.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-ffi/2.2.17/jnr-ffi-2.2.17.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jffi/1.3.13/jffi-1.3.13.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jffi/1.3.13/jffi-1.3.13-native.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-commons/9.7.1/asm-commons-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-a64asm/1.0.0/jnr-a64asm-1.0.0.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-x86asm/1.0.2/jnr-x86asm-1.0.2.jar:/home/<USER>/works/project/mvnRepository/com/tokyocabinet/tokyocabinet/1.24/tokyocabinet-1.24.jar:/home/<USER>/works/project/mvnRepository/org/jackson/databind/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.3/jackson-datatype-jsr310-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/github/luben/zstd-jni/1.5.5-3/zstd-jni-1.5.5-3.jar:/home/<USER>/works/project/mvnRepository/com/github/ben-manes/caffeine/caffeine/3.1.8/caffeine-3.1.8.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/home/<USER>/works/project/mvnRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/home/<USER>/works/project/mvnRepository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-context/1.70.0/grpc-context-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-impl/0.31.1/opencensus-impl-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-impl-core/0.31.1/opencensus-impl-core-0.31.1.jar:/home/<USER>/works/project/mvnRepository/com/lmax/disruptor/3.4.2/disruptor-3.4.2.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-census/1.71.0/grpc-census-1.71.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-api/1.70.0/grpc-api-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-contrib-grpc-metrics/0.31.1/opencensus-contrib-grpc-metrics-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-services/1.71.0/grpc-services-1.71.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-stub/1.70.0/grpc-stub-1.70.0.jar:/home/<USER>/works/project/mvnRepository/org/codehaus/mojo/animal-sniffer-annotations/1.24/animal-sniffer-annotations-1.24.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-core/1.70.0/grpc-core-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar:/home/<USER>/works/project/mvnRepository/io/perfmark/perfmark-api/0.27.0/perfmark-api-0.27.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-protobuf/1.70.0/grpc-protobuf-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/protobuf/protobuf-java/3.25.6/protobuf-java-3.25.6.jar:/home/<USER>/works/project/mvnRepository/com/google/api/grpc/proto-google-common-protos/2.46.0/proto-google-common-protos-2.46.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-protobuf-lite/1.70.0/grpc-protobuf-lite-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-util/1.70.0/grpc-util-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/protobuf/protobuf-java-util/3.25.6/protobuf-java-util-3.25.6.jar:/home/<USER>/works/project/mvnRepository/com/google/errorprone/error_prone_annotations/2.30.0/error_prone_annotations-2.30.0.jar:/home/<USER>/works/project/mvnRepository/com/google/code/gson/gson/2.11.0/gson-2.11.0.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-x86_64.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-common/4.1.119.Final/netty-common-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-buffer/4.1.119.Final/netty-buffer-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport/4.1.119.Final/netty-transport-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-resolver/4.1.119.Final/netty-resolver-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-native-unix-common/4.1.119.Final/netty-transport-native-unix-common-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-classes-epoll/4.1.119.Final/netty-transport-classes-epoll-4.1.119.Final.jar org.xkong.cloud.commons.uid.TestRunner
===== 运行单元测试 =====
02:13:01,441 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-classic version 1.5.16
02:13:01,441 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-core version 1.5.18
02:13:01,441 |-WARN in ch.qos.logback.classic.LoggerContext[default] - Versions of logback-core and logback-classic are different!
02:13:01,444 |-INFO in ch.qos.logback.classic.util.ContextInitializer@76494737 - Here is a list of configurators discovered as a service, by rank: 
02:13:01,444 |-INFO in ch.qos.logback.classic.util.ContextInitializer@76494737 -   org.springframework.boot.logging.logback.RootLogLevelConfigurator
02:13:01,444 |-INFO in ch.qos.logback.classic.util.ContextInitializer@76494737 - They will be invoked in order until ExecutionStatus.DO_NOT_INVOKE_NEXT_IF_ANY is returned.
02:13:01,444 |-INFO in ch.qos.logback.classic.util.ContextInitializer@76494737 - Constructed configurator of type class org.springframework.boot.logging.logback.RootLogLevelConfigurator
02:13:01,452 |-INFO in ch.qos.logback.classic.util.ContextInitializer@76494737 - org.springframework.boot.logging.logback.RootLogLevelConfigurator.configure() call lasted 1 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
02:13:01,452 |-INFO in ch.qos.logback.classic.util.ContextInitializer@76494737 - Trying to configure with ch.qos.logback.classic.joran.SerializedModelConfigurator
02:13:01,453 |-INFO in ch.qos.logback.classic.util.ContextInitializer@76494737 - Constructed configurator of type class ch.qos.logback.classic.joran.SerializedModelConfigurator
02:13:01,453 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.scmo]
02:13:01,454 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback.scmo]
02:13:01,454 |-INFO in ch.qos.logback.classic.util.ContextInitializer@76494737 - ch.qos.logback.classic.joran.SerializedModelConfigurator.configure() call lasted 1 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
02:13:01,454 |-INFO in ch.qos.logback.classic.util.ContextInitializer@76494737 - Trying to configure with ch.qos.logback.classic.util.DefaultJoranConfigurator
02:13:01,455 |-INFO in ch.qos.logback.classic.util.ContextInitializer@76494737 - Constructed configurator of type class ch.qos.logback.classic.util.DefaultJoranConfigurator
02:13:01,455 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-test.xml] at [file:/media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/test-classes/logback-test.xml]
02:13:01,623 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [CONSOLE]
02:13:01,623 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
02:13:01,631 |-INFO in ch.qos.logback.core.model.processor.ImplicitModelHandler - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
02:13:01,726 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - BEWARE: Writing to the console can be very slow. Avoid logging to the 
02:13:01,726 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - console in production environments, especially in high volume systems.
02:13:01,726 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - See also https://logback.qos.ch/codes.html#slowConsole
02:13:01,727 |-INFO in ch.qos.logback.classic.model.processor.RootLoggerModelHandler - Setting level of ROOT logger to INFO
02:13:01,727 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[ROOT]
02:13:01,730 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [org.xkong.cloud.commons.uid] to DEBUG
02:13:01,730 |-INFO in ch.qos.logback.core.model.processor.DefaultProcessor@4a003cbe - End of configuration.
02:13:01,731 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@4082ba93 - Registering current configuration as safe fallback point
02:13:01,731 |-INFO in ch.qos.logback.classic.util.ContextInitializer@76494737 - ch.qos.logback.classic.util.DefaultJoranConfigurator.configure() call lasted 276 milliseconds. ExecutionStatus=DO_NOT_INVOKE_NEXT_IF_ANY

2025-05-24 02:13:02.577 [main] INFO  o.x.c.c.u.f.MachineFingerprints - 使用OSHI库成功获取系统信息
2025-05-24 02:13:04.919 [main] INFO  o.x.c.c.u.f.MachineFingerprints - 已收集机器特征码: {"os_arch":"amd64","os_name":"Linux","fingerprint_hash":"0cd7e71f226e917911f5e1251a5665812c4065753531534bd12cbdfeaab5c60d","hostname":"long-VirtualBox","mac_addresses":["CE:A****:73:FD","08:0****:3A:87"],"os_version":"5.4.0-91-generic"}
Mockito is currently self-attaching to enable the inline-mock-maker. This will no longer work in future releases of the JDK. Please add Mockito as an agent to your build what is described in Mockito's documentation: https://javadoc.io/doc/org.mockito/mockito-core/latest/org/mockito/Mockito.html#0.3
WARNING: A Java agent has been loaded dynamically (/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
Java HotSpot(TM) 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classpath has been appended
2025-05-24 02:13:06.530 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-24 02:13:06.609 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-24 02:13:06.661 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-24 02:13:06.983 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-24 02:13:06.984 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-24 02:13:06.990 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-24 02:13:06.995 [main] DEBUG o.x.c.c.u.c.KeyManagementService - 使用NativePRNGNonBlocking算法的SecureRandom
2025-05-24 02:13:06.997 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已为应用 test-app 环境 test-env 创建新的 test-key-type 类型密钥
2025-05-24 02:13:07.003 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-24 02:13:07.010 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-24 02:13:07.015 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-24 02:13:07.018 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已清除密钥缓存
2025-05-24 02:13:07.042 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-24 02:13:07.043 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-24 02:13:07.047 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-24 02:13:07.047 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-24 02:13:07.050 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-24 02:13:07.051 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-24 02:13:07.052 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-24 02:13:07.052 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-24 02:13:07.283 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-24 02:13:07.286 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-24 02:13:07.302 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-24 02:13:07.304 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-24 02:13:07.304 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-24 02:13:07.304 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-24 02:13:07.304 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 已清除所有验证缓存
2025-05-24 02:13:07.428 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:13:07.430 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:07.434 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:07.434 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:07.439 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:13:07.446 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 续约工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42，实例: 2066533285
2025-05-24 02:13:07.448 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始重新分配工作机器ID
2025-05-24 02:13:07.449 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:07.450 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:13:07.450 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配工作机器ID成功，新的工作机器ID: 42
2025-05-24 02:13:07.521 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:13:07.550 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:07.558 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:07.558 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:07.560 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:07.571 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-24 02:13:07.572 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42
2025-05-24 02:13:07.588 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:13:07.590 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:07.593 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:07.594 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:07.594 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:07.601 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:13:07.603 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:07.610 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:07.610 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:07.611 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:07.612 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 插入新的工作机器ID失败，可能存在并发冲突，工作机器ID: 42
2025-05-24 02:13:07.618 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:13:07.629 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:07.630 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:07.631 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:07.631 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:13:07.631 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功分配工作机器ID: 42，重试次数: 0
2025-05-24 02:13:07.635 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:13:07.640 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:07.643 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:07.643 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:07.643 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:13:07.644 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 2102562039
2025-05-24 02:13:07.644 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 2102562039
2025-05-24 02:13:07.646 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 2102562039
2025-05-24 02:13:07.646 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 2102562039
2025-05-24 02:13:07.647 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 2102562039
2025-05-24 02:13:07.647 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42
2025-05-24 02:13:07.647 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 2102562039
2025-05-24 02:13:07.654 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:13:07.668 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:07.671 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:07.671 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:07.672 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:13:07.677 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:13:07.678 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:07.680 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:07.680 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:07.681 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:13:07.681 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 1890097328
2025-05-24 02:13:07.681 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 1890097328
2025-05-24 02:13:07.682 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 1890097328
2025-05-24 02:13:07.682 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 1890097328
2025-05-24 02:13:07.682 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 1890097328
2025-05-24 02:13:07.682 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42
2025-05-24 02:13:07.682 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 1890097328
2025-05-24 02:13:07.690 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:13:07.691 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:07.692 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:07.693 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:07.693 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:13:07.694 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功续约工作机器ID: 42，实例: 104136534
2025-05-24 02:13:07.787 [main] INFO  o.testcontainers.images.PullPolicy - Image pull policy will be performed by: DefaultPullPolicy()
2025-05-24 02:13:07.790 [main] INFO  o.t.utility.ImageNameSubstitutor - Image name substitution will be performed by: DefaultImageNameSubstitutor (composite of 'ConfigurationFileImageNameSubstitutor' and 'PrefixingImageNameSubstitutor')
2025-05-24 02:13:08.070 [main] INFO  o.t.d.DockerClientProviderStrategy - Loaded org.testcontainers.dockerclient.UnixSocketClientProviderStrategy from ~/.testcontainers.properties, will try it first
2025-05-24 02:13:08.602 [main] INFO  o.t.d.DockerClientProviderStrategy - Found Docker environment with local Unix socket (unix:///var/run/docker.sock)
2025-05-24 02:13:08.604 [main] INFO  o.testcontainers.DockerClientFactory - Docker host IP address is localhost
2025-05-24 02:13:08.622 [main] INFO  o.testcontainers.DockerClientFactory - Connected to docker: 
  Server Version: 28.1.1
  API Version: 1.49
  Operating System: Linux Mint 20.3
  Total Memory: 7960 MB
2025-05-24 02:13:08.651 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Creating container for image: testcontainers/ryuk:0.6.0
2025-05-24 02:13:08.656 [main] INFO  o.t.utility.RegistryAuthLocator - Failure when attempting to lookup auth config. Please ignore if you don't have images in an authenticated registry. Details: (dockerImageName: testcontainers/ryuk:0.6.0, configFile: /home/<USER>/.docker/config.json, configEnv: DOCKER_AUTH_CONFIG). Falling back to docker-java default behaviour. Exception message: Status 404: No config supplied. Checked in order: /home/<USER>/.docker/config.json (file not found), DOCKER_AUTH_CONFIG (not set)
2025-05-24 02:13:09.427 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Container testcontainers/ryuk:0.6.0 is starting: 2d9b2391091ad0428c94c23a7eeec469d8f04fe607b901f5ca4aa8f7bd7c7838
2025-05-24 02:13:09.864 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Container testcontainers/ryuk:0.6.0 started in PT1.213478887S
2025-05-24 02:13:09.871 [main] INFO  o.t.utility.RyukResourceReaper - Ryuk started - will monitor and terminate Testcontainers containers on JVM exit
2025-05-24 02:13:09.871 [main] INFO  o.testcontainers.DockerClientFactory - Checking the system...
2025-05-24 02:13:09.872 [main] INFO  o.testcontainers.DockerClientFactory - ✔︎ Docker server version should be at least 1.6.0
2025-05-24 02:13:09.872 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-24 02:13:10.328 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 8bd7a97f7e48478f9b14c6cf71c2fc754e19ab9743e4f152adcc9f5a2a7a8104
2025-05-24 02:13:15.923 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT6.050219397S
2025-05-24 02:13:15.924 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-24 02:13:15.946 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-24 02:13:16.166 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3c3e363
2025-05-24 02:13:16.168 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-24 02:13:16.170 [main] INFO  o.x.c.c.u.test.PostgresTestContainer - 正在执行数据库初始化脚本: init-schema.sql
2025-05-24 02:13:16.233 [main] INFO  o.x.c.c.u.test.PostgresTestContainer - 数据库初始化脚本执行完成
2025-05-24 02:13:16.241 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-05-24 02:13:16.348 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@1214d23c
2025-05-24 02:13:16.349 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-05-24 02:13:16.355 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 构建UidGeneratorFacade
2025-05-24 02:13:16.359 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Starting...
2025-05-24 02:13:16.383 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-3 - Added connection org.postgresql.jdbc.PgConnection@714f1a44
2025-05-24 02:13:16.384 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Start completed.
2025-05-24 02:13:16.384 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 为测试环境构建UidGeneratorFacade
2025-05-24 02:13:16.384 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 构建UidGeneratorFacade
2025-05-24 02:13:16.389 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 初始化UidGeneratorFacade
2025-05-24 02:13:16.389 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建JdbcTemplate
2025-05-24 02:13:16.390 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建TransactionTemplate
2025-05-24 02:13:16.397 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建DatabaseMetadataService
2025-05-24 02:13:16.445 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:13:16.446 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidValidationService
2025-05-24 02:13:16.447 [main] INFO  o.x.c.c.u.config.UidValidationConfig - 创建UidValidationService实例
2025-05-24 02:13:16.448 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidTableManager
2025-05-24 02:13:16.448 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:13:16.535 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:13:16.535 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:13:16.560 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:13:16.561 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:16.568 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-24 02:13:16.569 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:16.571 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-24 02:13:16.572 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-24 02:13:16.586 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-24 02:13:16.587 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:13:16.587 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:16.589 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-24 02:13:16.589 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:16.591 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-24 02:13:16.591 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-24 02:13:16.596 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-24 02:13:16.598 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:13:16.598 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:16.601 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-24 02:13:16.680 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:13:16.680 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-24 02:13:16.680 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:13:16.686 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceManager
2025-05-24 02:13:16.700 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:16.700 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:13:16.700 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: test-app, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:13:16.734 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 1
2025-05-24 02:13:16.759 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 1 在数据库中不存在
2025-05-24 02:13:16.759 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-24 02:13:16.767 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-24 02:13:16.767 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-24 02:13:16.794 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 1
2025-05-24 02:13:16.808 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/test-instance-id.dat
2025-05-24 02:13:16.810 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:13:16.810 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceWorkerIdAssigner
2025-05-24 02:13:16.810 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:13:16.811 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:16.811 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建CachedUidGenerator
2025-05-24 02:13:16.825 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - CachedUidGenerator配置完成，准备初始化RingBuffer
2025-05-24 02:13:16.826 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 调用CachedUidGenerator.afterPropertiesSet()方法
2025-05-24 02:13:16.826 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:16.826 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配工作机器ID
2025-05-24 02:13:16.829 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:16.838 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 0，实例ID: 1
2025-05-24 02:13:16.839 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配了新的工作机器ID: 0
2025-05-24 02:13:16.839 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功分配工作机器ID: 0，重试次数: 0
2025-05-24 02:13:16.840 [main] INFO  c.x.uid.impl.DefaultUidGenerator - Initialized bits(1, 18, 18, 14) for workerID:0
2025-05-24 02:13:16.852 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized ring buffer size:131072, paddingFactor:50
2025-05-24 02:13:16.858 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized BufferPaddingExecutor. Using schdule:true, interval:60
2025-05-24 02:13:16.860 [main] INFO  com.xfvape.uid.buffer.RingBuffer - Ready to padding buffer lastSecond:1748023996. RingBuffer [bufferSize=131072, tail=-1, cursor=-1, paddingThreshold=65536]
2025-05-24 02:13:16.892 [main] WARN  com.xfvape.uid.buffer.RingBuffer - Rejected putting buffer for uid:324362823616430080. RingBuffer [bufferSize=131072, tail=131071, cursor=-1, paddingThreshold=65536]
2025-05-24 02:13:16.892 [main] INFO  com.xfvape.uid.buffer.RingBuffer - End to padding buffer lastSecond:1748024005. RingBuffer [bufferSize=131072, tail=131071, cursor=-1, paddingThreshold=65536]
2025-05-24 02:13:16.903 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized RingBuffer successfully.
2025-05-24 02:13:16.903 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - CachedUidGenerator.afterPropertiesSet()方法调用成功
2025-05-24 02:13:16.904 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - RingBuffer初始化成功
2025-05-24 02:13:16.904 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade初始化完成
2025-05-24 02:13:16.904 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 关闭UidGeneratorFacade资源
2025-05-24 02:13:16.904 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 开始关闭CachedUidGenerator的BufferPaddingExecutor
2025-05-24 02:13:16.904 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - BufferPaddingExecutor关闭成功
2025-05-24 02:13:16.904 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 2146473561
2025-05-24 02:13:16.904 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 2146473561
2025-05-24 02:13:16.905 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 2146473561
2025-05-24 02:13:16.905 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 2146473561
2025-05-24 02:13:16.905 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 2146473561
2025-05-24 02:13:16.907 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 0
2025-05-24 02:13:16.907 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 2146473561
2025-05-24 02:13:16.907 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade资源已关闭
2025-05-24 02:13:16.911 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Starting...
2025-05-24 02:13:16.935 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-4 - Added connection org.postgresql.jdbc.PgConnection@70139a81
2025-05-24 02:13:16.936 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Start completed.
2025-05-24 02:13:16.936 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 为测试环境构建UidGeneratorFacade
2025-05-24 02:13:16.936 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 构建UidGeneratorFacade
2025-05-24 02:13:16.936 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 初始化UidGeneratorFacade
2025-05-24 02:13:16.936 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建JdbcTemplate
2025-05-24 02:13:16.936 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建TransactionTemplate
2025-05-24 02:13:16.936 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建DatabaseMetadataService
2025-05-24 02:13:16.936 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:13:16.936 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidValidationService
2025-05-24 02:13:16.936 [main] INFO  o.x.c.c.u.config.UidValidationConfig - 创建UidValidationService实例
2025-05-24 02:13:16.936 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidTableManager
2025-05-24 02:13:16.936 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:13:16.937 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:13:16.939 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:13:16.946 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:13:16.947 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:16.950 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-24 02:13:16.950 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:16.952 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-24 02:13:16.953 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-24 02:13:16.961 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-24 02:13:16.961 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:13:16.961 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:16.964 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-24 02:13:16.964 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:16.966 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-24 02:13:16.966 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-24 02:13:16.971 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-24 02:13:16.971 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:13:16.971 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:16.973 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-24 02:13:16.973 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:16.975 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-24 02:13:16.975 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-24 02:13:16.980 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-24 02:13:16.980 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:13:16.980 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:13:16.980 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceManager
2025-05-24 02:13:16.980 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:16.980 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:13:16.980 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: test-app, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:13:16.981 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 1
2025-05-24 02:13:16.983 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 1 在数据库中存在，更新实例信息
2025-05-24 02:13:16.984 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 1 的最后活动时间
2025-05-24 02:13:16.984 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:13:16.984 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceWorkerIdAssigner
2025-05-24 02:13:16.984 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:13:16.985 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:16.985 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建CachedUidGenerator
2025-05-24 02:13:16.985 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - CachedUidGenerator配置完成，准备初始化RingBuffer
2025-05-24 02:13:16.985 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 调用CachedUidGenerator.afterPropertiesSet()方法
2025-05-24 02:13:16.985 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:16.985 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配工作机器ID
2025-05-24 02:13:16.986 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:16.988 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 1
2025-05-24 02:13:16.989 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配了新的工作机器ID: 0
2025-05-24 02:13:16.989 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功分配工作机器ID: 0，重试次数: 0
2025-05-24 02:13:16.989 [main] INFO  c.x.uid.impl.DefaultUidGenerator - Initialized bits(1, 18, 18, 14) for workerID:0
2025-05-24 02:13:17.013 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized ring buffer size:131072, paddingFactor:50
2025-05-24 02:13:17.013 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized BufferPaddingExecutor. Using schdule:true, interval:60
2025-05-24 02:13:17.014 [main] INFO  com.xfvape.uid.buffer.RingBuffer - Ready to padding buffer lastSecond:1748023997. RingBuffer [bufferSize=131072, tail=-1, cursor=-1, paddingThreshold=65536]
2025-05-24 02:13:17.045 [main] WARN  com.xfvape.uid.buffer.RingBuffer - Rejected putting buffer for uid:324362827911397376. RingBuffer [bufferSize=131072, tail=131071, cursor=-1, paddingThreshold=65536]
2025-05-24 02:13:17.045 [main] INFO  com.xfvape.uid.buffer.RingBuffer - End to padding buffer lastSecond:1748024006. RingBuffer [bufferSize=131072, tail=131071, cursor=-1, paddingThreshold=65536]
2025-05-24 02:13:17.045 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized RingBuffer successfully.
2025-05-24 02:13:17.045 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - CachedUidGenerator.afterPropertiesSet()方法调用成功
2025-05-24 02:13:17.046 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - RingBuffer初始化成功
2025-05-24 02:13:17.046 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade初始化完成
2025-05-24 02:13:17.046 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 关闭UidGeneratorFacade资源
2025-05-24 02:13:17.047 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 开始关闭CachedUidGenerator的BufferPaddingExecutor
2025-05-24 02:13:17.047 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - BufferPaddingExecutor关闭成功
2025-05-24 02:13:17.047 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 580507907
2025-05-24 02:13:17.047 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 580507907
2025-05-24 02:13:17.047 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 580507907
2025-05-24 02:13:17.047 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 580507907
2025-05-24 02:13:17.047 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 580507907
2025-05-24 02:13:17.049 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 0
2025-05-24 02:13:17.049 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 580507907
2025-05-24 02:13:17.049 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade资源已关闭
2025-05-24 02:13:17.053 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-5 - Starting...
2025-05-24 02:13:17.097 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-5 - Added connection org.postgresql.jdbc.PgConnection@58fbcb71
2025-05-24 02:13:17.097 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-5 - Start completed.
2025-05-24 02:13:17.098 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 构建UidGeneratorFacade
2025-05-24 02:13:17.100 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-6 - Starting...
2025-05-24 02:13:17.149 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-6 - Added connection org.postgresql.jdbc.PgConnection@3c27f72
2025-05-24 02:13:17.149 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-6 - Start completed.
2025-05-24 02:13:17.150 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 构建UidGeneratorFacade
2025-05-24 02:13:17.150 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 初始化UidGeneratorFacade
2025-05-24 02:13:17.150 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建JdbcTemplate
2025-05-24 02:13:17.150 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建TransactionTemplate
2025-05-24 02:13:17.150 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建DatabaseMetadataService
2025-05-24 02:13:17.150 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:13:17.150 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidValidationService
2025-05-24 02:13:17.150 [main] INFO  o.x.c.c.u.config.UidValidationConfig - 创建UidValidationService实例
2025-05-24 02:13:17.150 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidTableManager
2025-05-24 02:13:17.150 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:13:17.152 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:13:17.152 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:13:17.154 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:13:17.154 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:17.158 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-24 02:13:17.158 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:17.160 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-24 02:13:17.160 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-24 02:13:17.169 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-24 02:13:17.169 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:13:17.169 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:17.171 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-24 02:13:17.172 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:17.173 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-24 02:13:17.174 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-24 02:13:17.180 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-24 02:13:17.180 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:13:17.180 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:17.183 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-24 02:13:17.184 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:17.186 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-24 02:13:17.186 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-24 02:13:17.189 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-24 02:13:17.189 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:13:17.189 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:13:17.189 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceManager
2025-05-24 02:13:17.189 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:17.189 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:13:17.189 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: test-app, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:13:17.191 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 1
2025-05-24 02:13:17.193 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 1 在数据库中存在，更新实例信息
2025-05-24 02:13:17.196 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 1 的最后活动时间
2025-05-24 02:13:17.197 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:13:17.197 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceWorkerIdAssigner
2025-05-24 02:13:17.197 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:13:17.197 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:17.197 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建CachedUidGenerator
2025-05-24 02:13:17.198 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - CachedUidGenerator配置完成，准备初始化RingBuffer
2025-05-24 02:13:17.198 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 调用CachedUidGenerator.afterPropertiesSet()方法
2025-05-24 02:13:17.198 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:17.198 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配工作机器ID
2025-05-24 02:13:17.208 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:17.214 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 1
2025-05-24 02:13:17.215 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配了新的工作机器ID: 0
2025-05-24 02:13:17.216 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功分配工作机器ID: 0，重试次数: 0
2025-05-24 02:13:17.216 [main] INFO  c.x.uid.impl.DefaultUidGenerator - Initialized bits(1, 18, 18, 14) for workerID:0
2025-05-24 02:13:17.219 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized ring buffer size:131072, paddingFactor:50
2025-05-24 02:13:17.219 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized BufferPaddingExecutor. Using schdule:true, interval:60
2025-05-24 02:13:17.219 [main] INFO  com.xfvape.uid.buffer.RingBuffer - Ready to padding buffer lastSecond:1748023997. RingBuffer [bufferSize=131072, tail=-1, cursor=-1, paddingThreshold=65536]
2025-05-24 02:13:17.225 [main] WARN  com.xfvape.uid.buffer.RingBuffer - Rejected putting buffer for uid:324362827911397376. RingBuffer [bufferSize=131072, tail=131071, cursor=-1, paddingThreshold=65536]
2025-05-24 02:13:17.226 [main] INFO  com.xfvape.uid.buffer.RingBuffer - End to padding buffer lastSecond:1748024006. RingBuffer [bufferSize=131072, tail=131071, cursor=-1, paddingThreshold=65536]
2025-05-24 02:13:17.226 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized RingBuffer successfully.
2025-05-24 02:13:17.226 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - CachedUidGenerator.afterPropertiesSet()方法调用成功
2025-05-24 02:13:17.227 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - RingBuffer初始化成功
2025-05-24 02:13:17.227 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade初始化完成
2025-05-24 02:13:17.227 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 关闭UidGeneratorFacade资源
2025-05-24 02:13:17.227 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 开始关闭CachedUidGenerator的BufferPaddingExecutor
2025-05-24 02:13:17.227 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - BufferPaddingExecutor关闭成功
2025-05-24 02:13:17.227 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 368384503
2025-05-24 02:13:17.227 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 368384503
2025-05-24 02:13:17.229 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 368384503
2025-05-24 02:13:17.230 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 368384503
2025-05-24 02:13:17.230 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 368384503
2025-05-24 02:13:17.232 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 0
2025-05-24 02:13:17.232 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 368384503
2025-05-24 02:13:17.232 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade资源已关闭

Test run finished after 16462 ms
[         6 containers found      ]
[         0 containers skipped    ]
[         6 containers started    ]
[         0 containers aborted    ]
[         6 containers successful ]
[         0 containers failed     ]
[        30 tests found           ]
[         0 tests skipped         ]
[        30 tests started         ]
[         0 tests aborted         ]
[        27 tests successful      ]
[         3 tests failed          ]


===== 运行集成测试 =====
2025-05-24 02:13:17.358 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-24 02:13:18.883 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 59051acb70dfa5267fd5572243b4f493b75a72e9b17727fdf0bfe34e35a11cc1
2025-05-24 02:13:24.409 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT7.051305903S
2025-05-24 02:13:24.410 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-24 02:13:24.417 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:13:24.417 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-7 - Starting...
2025-05-24 02:13:24.443 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-7 - Added connection org.postgresql.jdbc.PgConnection@70a24f9
2025-05-24 02:13:24.443 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-7 - Start completed.
2025-05-24 02:13:24.450 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:13:24.451 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:13:24.451 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:13:24.453 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:13:24.453 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:24.458 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-24 02:13:24.477 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:13:24.477 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-24 02:13:24.477 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:24.479 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-24 02:13:24.491 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:13:24.491 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-24 02:13:24.491 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:24.493 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-24 02:13:24.502 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:13:24.502 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-24 02:13:24.502 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:13:27.243 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-24 02:13:27.432 [WorkerIdAssigner-LeaseRenewal-2066533285] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 续约工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42，实例: 2066533285
2025-05-24 02:13:27.435 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始重新分配工作机器ID
2025-05-24 02:13:27.436 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:27.436 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:13:27.436 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配工作机器ID成功，新的工作机器ID: 42
2025-05-24 02:13:27.549 [WorkerIdAssigner-LeaseRenewal-1576416089] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 续约工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42，实例: 1576416089
2025-05-24 02:13:27.553 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始重新分配工作机器ID
2025-05-24 02:13:27.554 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:27.555 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:27.556 [WorkerIdAssigner-Reassignment-1576416089] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-24 02:13:27.556 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42
2025-05-24 02:13:27.556 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配工作机器ID成功，新的工作机器ID: 42
2025-05-24 02:13:27.584 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 58bc9efcca3e4a01315bcef4afce911d81634d61905905e782a7c013daee8fd1
2025-05-24 02:13:27.620 [WorkerIdAssigner-LeaseRenewal-1994984869] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 续约工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42，实例: 1994984869
2025-05-24 02:13:27.621 [WorkerIdAssigner-Reassignment-1994984869] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始重新分配工作机器ID
2025-05-24 02:13:27.621 [WorkerIdAssigner-Reassignment-1994984869] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:27.622 [WorkerIdAssigner-Reassignment-1994984869] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:13:27.622 [WorkerIdAssigner-Reassignment-1994984869] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配工作机器ID成功，新的工作机器ID: 42
2025-05-24 02:13:27.671 [WorkerIdAssigner-LeaseRenewal-325897214] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 续约工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42，实例: 325897214
2025-05-24 02:13:27.673 [WorkerIdAssigner-Reassignment-325897214] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始重新分配工作机器ID
2025-05-24 02:13:27.673 [WorkerIdAssigner-Reassignment-325897214] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:27.674 [WorkerIdAssigner-Reassignment-325897214] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:13:27.694 [WorkerIdAssigner-Reassignment-325897214] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配工作机器ID成功，新的工作机器ID: 42
2025-05-24 02:13:27.765 [WorkerIdAssigner-LeaseRenewal-104136534] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功续约工作机器ID: 42，实例: 104136534
2025-05-24 02:13:33.821 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT6.578564547S
2025-05-24 02:13:33.822 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-24 02:13:33.833 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-8 - Starting...
2025-05-24 02:13:33.854 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-8 - Added connection org.postgresql.jdbc.PgConnection@2ffb0d10
2025-05-24 02:13:33.855 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-8 - Start completed.
2025-05-24 02:13:33.855 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:13:33.855 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:13:33.856 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:13:33.856 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:13:33.858 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 不存在，正在自动创建
2025-05-24 02:13:33.859 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 创建Schema（如果不存在）: infra_uid
2025-05-24 02:13:33.860 [main] DEBUG o.x.c.c.u.m.s.JdbcMetadataStrategy - 执行SQL: CREATE SCHEMA IF NOT EXISTS "infra_uid"
2025-05-24 02:13:33.861 [main] INFO  o.x.c.c.u.m.s.JdbcMetadataStrategy - 已创建Schema: infra_uid
2025-05-24 02:13:33.861 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 已成功创建
2025-05-24 02:13:33.861 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:13:33.861 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:33.865 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-24 02:13:33.876 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:13:33.877 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-24 02:13:33.877 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:33.878 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-24 02:13:33.888 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:13:33.888 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-24 02:13:33.888 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:33.890 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-24 02:13:33.897 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:13:33.897 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-24 02:13:33.898 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:13:33.898 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:33.898 [main] ERROR o.x.c.c.u.i.PersistentInstanceManagerBuilder - 构建失败: recoveryEnabled不能为空
2025-05-24 02:13:33.904 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-9 - Starting...
2025-05-24 02:13:33.921 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-9 - Added connection org.postgresql.jdbc.PgConnection@5529522f
2025-05-24 02:13:33.921 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-9 - Start completed.
2025-05-24 02:13:33.921 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:13:33.921 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:13:33.922 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:13:33.922 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:13:33.924 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:13:33.924 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:33.927 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-24 02:13:33.927 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:33.929 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-24 02:13:33.929 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-24 02:13:33.941 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-24 02:13:33.941 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:13:33.941 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:33.943 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-24 02:13:33.943 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:33.945 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-24 02:13:33.945 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-24 02:13:33.950 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-24 02:13:33.951 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:13:33.951 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:33.953 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-24 02:13:33.953 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:33.955 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-24 02:13:33.955 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-24 02:13:33.961 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-24 02:13:33.961 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:13:33.961 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:13:33.962 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:33.962 [main] ERROR o.x.c.c.u.i.PersistentInstanceManagerBuilder - 构建失败: localStoragePath不能为空
2025-05-24 02:13:33.965 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-10 - Starting...
2025-05-24 02:13:33.986 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-10 - Added connection org.postgresql.jdbc.PgConnection@7fe87c0e
2025-05-24 02:13:33.986 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-10 - Start completed.
2025-05-24 02:13:33.986 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:13:33.986 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:13:33.988 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:13:33.988 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:13:33.990 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:13:33.991 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:33.994 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-24 02:13:33.994 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:33.997 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-24 02:13:33.997 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-24 02:13:34.006 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-24 02:13:34.006 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:13:34.006 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:34.010 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-24 02:13:34.011 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:34.014 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-24 02:13:34.014 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-24 02:13:34.023 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-24 02:13:34.025 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:13:34.029 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:34.033 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-24 02:13:34.033 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:34.038 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-24 02:13:34.038 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-24 02:13:34.043 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-24 02:13:34.043 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:13:34.043 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:13:34.044 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:34.044 [main] ERROR o.x.c.c.u.i.PersistentInstanceManagerBuilder - 构建失败: localStoragePath不能为空
2025-05-24 02:13:35.953 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-24 02:13:36.487 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 5fce0a5605bdf13ad56adc3a6739a781b1dabd8f2137729726a38e3d3332c240
2025-05-24 02:13:41.016 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.063443302S
2025-05-24 02:13:41.016 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-24 02:13:41.018 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-11 - Starting...
2025-05-24 02:13:41.043 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-11 - Added connection org.postgresql.jdbc.PgConnection@29c244e
2025-05-24 02:13:41.043 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-11 - Start completed.
2025-05-24 02:13:41.043 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:13:41.043 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:13:41.044 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:13:41.044 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:13:41.047 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 不存在，正在自动创建
2025-05-24 02:13:41.047 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 创建Schema（如果不存在）: infra_uid
2025-05-24 02:13:41.049 [main] DEBUG o.x.c.c.u.m.s.JdbcMetadataStrategy - 执行SQL: CREATE SCHEMA IF NOT EXISTS "infra_uid"
2025-05-24 02:13:41.050 [main] INFO  o.x.c.c.u.m.s.JdbcMetadataStrategy - 已创建Schema: infra_uid
2025-05-24 02:13:41.050 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 已成功创建
2025-05-24 02:13:41.050 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:13:41.050 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:41.054 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-24 02:13:41.088 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:13:41.088 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-24 02:13:41.088 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:41.092 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-24 02:13:41.113 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:13:41.114 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-24 02:13:41.114 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:41.117 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-24 02:13:41.124 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:13:41.125 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-24 02:13:41.125 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:13:41.130 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件清理关闭钩子
2025-05-24 02:13:41.131 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/expiry-test9706226876675336049, 描述: 创建的临时目录
2025-05-24 02:13:41.131 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/expiry-test-28450970830401079781, 描述: 创建的临时目录
2025-05-24 02:13:41.131 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:41.131 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:13:41.131 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: expiry-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:13:41.131 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/expiry-test9706226876675336049/instance-id
2025-05-24 02:13:41.131 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-24 02:13:41.136 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-24 02:13:41.136 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-24 02:13:41.139 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 1
2025-05-24 02:13:41.140 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/expiry-test9706226876675336049/instance-id
2025-05-24 02:13:41.140 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:13:41.140 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 1秒，续约间隔: 1秒
2025-05-24 02:13:41.141 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:41.141 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:41.141 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配工作机器ID
2025-05-24 02:13:41.142 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:41.150 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 0，实例ID: 1
2025-05-24 02:13:41.154 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配了新的工作机器ID: 0
2025-05-24 02:13:41.155 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 647170252
2025-05-24 02:13:41.155 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 647170252
2025-05-24 02:13:41.158 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 647170252
2025-05-24 02:13:41.159 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 647170252
2025-05-24 02:13:41.159 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 647170252
2025-05-24 02:13:41.161 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 0
2025-05-24 02:13:41.161 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 647170252
2025-05-24 02:13:43.161 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:43.162 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:13:43.162 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: expiry-test-2, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:13:43.162 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/expiry-test-28450970830401079781/instance-id
2025-05-24 02:13:43.162 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-24 02:13:43.164 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-24 02:13:43.164 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-24 02:13:43.167 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 2
2025-05-24 02:13:43.168 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/expiry-test-28450970830401079781/instance-id
2025-05-24 02:13:43.168 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:13:43.168 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 1秒，续约间隔: 1秒
2025-05-24 02:13:43.169 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:43.169 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:43.169 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 2 分配工作机器ID
2025-05-24 02:13:43.170 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:43.173 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 2
2025-05-24 02:13:43.174 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 2 分配了新的工作机器ID: 0
2025-05-24 02:13:43.174 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 1638740214
2025-05-24 02:13:43.174 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 1638740214
2025-05-24 02:13:43.174 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 1638740214
2025-05-24 02:13:43.174 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 1638740214
2025-05-24 02:13:43.174 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 1638740214
2025-05-24 02:13:43.175 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 0
2025-05-24 02:13:43.175 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 1638740214
2025-05-24 02:13:43.179 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-12 - Starting...
2025-05-24 02:13:43.198 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-12 - Added connection org.postgresql.jdbc.PgConnection@54daf8f9
2025-05-24 02:13:43.199 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-12 - Start completed.
2025-05-24 02:13:43.199 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:13:43.199 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:13:43.200 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:13:43.200 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:13:43.202 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:13:43.203 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:43.206 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-24 02:13:43.206 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:43.208 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-24 02:13:43.208 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-24 02:13:43.219 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-24 02:13:43.220 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:13:43.220 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:43.222 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-24 02:13:43.222 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:43.224 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-24 02:13:43.224 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-24 02:13:43.228 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-24 02:13:43.228 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:13:43.228 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:43.231 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-24 02:13:43.231 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:43.233 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-24 02:13:43.234 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-24 02:13:43.238 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-24 02:13:43.238 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:13:43.238 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:13:43.238 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/lease-test1369069111976296197, 描述: 创建的临时目录
2025-05-24 02:13:43.238 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:43.239 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:13:43.239 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: lease-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:13:43.239 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/lease-test1369069111976296197/instance-id
2025-05-24 02:13:43.239 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-24 02:13:43.249 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-24 02:13:43.249 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-24 02:13:43.252 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 3
2025-05-24 02:13:43.253 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/lease-test1369069111976296197/instance-id
2025-05-24 02:13:43.253 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:13:43.253 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 5秒，续约间隔: 1秒
2025-05-24 02:13:43.254 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:43.254 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:43.254 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 3 分配工作机器ID
2025-05-24 02:13:43.264 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:43.269 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 3
2025-05-24 02:13:43.272 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 3 分配了新的工作机器ID: 0
2025-05-24 02:13:44.256 [WorkerIdAssigner-LeaseRenewal-2048777201] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功续约工作机器ID: 0，实例: 2048777201
2025-05-24 02:13:44.278 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 2048777201
2025-05-24 02:13:44.278 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 2048777201
2025-05-24 02:13:44.279 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 2048777201
2025-05-24 02:13:44.279 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 2048777201
2025-05-24 02:13:44.279 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 2048777201
2025-05-24 02:13:44.280 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 0
2025-05-24 02:13:44.280 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 2048777201
2025-05-24 02:13:44.282 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-13 - Starting...
2025-05-24 02:13:44.315 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-13 - Added connection org.postgresql.jdbc.PgConnection@70c205bf
2025-05-24 02:13:44.315 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-13 - Start completed.
2025-05-24 02:13:44.315 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:13:44.315 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:13:44.316 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:13:44.316 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:13:44.318 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:13:44.318 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:44.322 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-24 02:13:44.322 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:44.324 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-24 02:13:44.324 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-24 02:13:44.334 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-24 02:13:44.334 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:13:44.334 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:44.336 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-24 02:13:44.337 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:44.340 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-24 02:13:44.340 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-24 02:13:44.345 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-24 02:13:44.345 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:13:44.345 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:44.348 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-24 02:13:44.348 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:44.350 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-24 02:13:44.350 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-24 02:13:44.354 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-24 02:13:44.354 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:13:44.354 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:13:44.355 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test5421860714076101249, 描述: 创建的临时目录
2025-05-24 02:13:44.355 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:44.355 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:13:44.355 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: worker-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:13:44.355 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test5421860714076101249/instance-id
2025-05-24 02:13:44.355 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-24 02:13:44.359 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-24 02:13:44.359 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-24 02:13:44.360 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 4
2025-05-24 02:13:44.362 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test5421860714076101249/instance-id
2025-05-24 02:13:44.362 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:13:44.362 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 3600秒，续约间隔: 1200秒
2025-05-24 02:13:44.362 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:44.363 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:13:44.363 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 4 分配工作机器ID
2025-05-24 02:13:44.364 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:44.366 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 4
2025-05-24 02:13:44.367 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 4 分配了新的工作机器ID: 0
2025-05-24 02:13:44.367 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 54030040
2025-05-24 02:13:44.368 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 54030040
2025-05-24 02:13:44.368 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 54030040
2025-05-24 02:13:44.368 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 54030040
2025-05-24 02:13:44.368 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 54030040
2025-05-24 02:13:44.369 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 0
2025-05-24 02:13:44.369 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 54030040
2025-05-24 02:13:44.371 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-14 - Starting...
2025-05-24 02:13:44.413 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-14 - Added connection org.postgresql.jdbc.PgConnection@704c3bdf
2025-05-24 02:13:44.413 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-14 - Start completed.
2025-05-24 02:13:44.413 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:13:44.413 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:13:44.414 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:13:44.415 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:13:44.418 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:13:44.418 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:44.422 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-24 02:13:44.422 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:44.435 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-24 02:13:44.435 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-24 02:13:44.450 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-24 02:13:44.450 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:13:44.450 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:44.452 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-24 02:13:44.452 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:44.454 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-24 02:13:44.454 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-24 02:13:44.466 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-24 02:13:44.466 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:13:44.466 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:44.479 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-24 02:13:44.480 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:44.491 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-24 02:13:44.492 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-24 02:13:44.502 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-24 02:13:44.503 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:13:44.503 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:13:44.504 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test15191181955671297672, 描述: 创建的临时目录
2025-05-24 02:13:44.504 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test15191181955671297672, 描述: 并发测试基础目录
2025-05-24 02:13:44.510 [pool-1-thread-1] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test15191181955671297672/instance-id-0, 描述: 并发测试实例 0 的ID文件
2025-05-24 02:13:44.510 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:44.510 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:13:44.510 [pool-1-thread-1] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-0, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:13:44.510 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test15191181955671297672/instance-id-0
2025-05-24 02:13:44.510 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-24 02:13:44.512 [pool-1-thread-3] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test15191181955671297672/instance-id-2, 描述: 并发测试实例 2 的ID文件
2025-05-24 02:13:44.513 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:44.513 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:13:44.513 [pool-1-thread-3] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-2, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:13:44.513 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test15191181955671297672/instance-id-2
2025-05-24 02:13:44.513 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-24 02:13:44.518 [pool-1-thread-2] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test15191181955671297672/instance-id-1, 描述: 并发测试实例 1 的ID文件
2025-05-24 02:13:44.518 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:44.519 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:13:44.519 [pool-1-thread-2] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-1, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:13:44.519 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test15191181955671297672/instance-id-1
2025-05-24 02:13:44.519 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-24 02:13:44.528 [pool-1-thread-4] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test15191181955671297672/instance-id-3, 描述: 并发测试实例 3 的ID文件
2025-05-24 02:13:44.529 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:44.529 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:13:44.529 [pool-1-thread-4] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-3, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:13:44.529 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test15191181955671297672/instance-id-3
2025-05-24 02:13:44.529 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-24 02:13:44.533 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-24 02:13:44.533 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-24 02:13:44.538 [pool-1-thread-5] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test15191181955671297672/instance-id-4, 描述: 并发测试实例 4 的ID文件
2025-05-24 02:13:44.538 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:13:44.538 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:13:44.538 [pool-1-thread-5] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-4, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:13:44.537 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 5
2025-05-24 02:13:44.538 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test15191181955671297672/instance-id-4
2025-05-24 02:13:44.538 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-24 02:13:44.540 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test15191181955671297672/instance-id-0
2025-05-24 02:13:44.540 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:13:44.540 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 3600秒，续约间隔: 1200秒
2025-05-24 02:13:44.542 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-24 02:13:44.542 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-24 02:13:44.544 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 6
2025-05-24 02:13:44.546 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test15191181955671297672/instance-id-3
2025-05-24 02:13:44.546 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:13:44.546 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 3600秒，续约间隔: 1200秒
2025-05-24 02:13:44.552 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-24 02:13:44.552 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-24 02:13:44.562 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 7
2025-05-24 02:13:44.573 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test15191181955671297672/instance-id-1
2025-05-24 02:13:44.573 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:13:44.574 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 3600秒，续约间隔: 1200秒
2025-05-24 02:13:44.583 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:44.583 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: pool-1-thread-1
2025-05-24 02:13:44.583 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 5 分配工作机器ID
2025-05-24 02:13:44.584 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:44.584 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: pool-1-thread-4
2025-05-24 02:13:44.584 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 6 分配工作机器ID
2025-05-24 02:13:44.585 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-24 02:13:44.585 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-24 02:13:44.585 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:44.585 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: pool-1-thread-2
2025-05-24 02:13:44.585 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 7 分配工作机器ID
2025-05-24 02:13:44.593 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 8
2025-05-24 02:13:44.597 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test15191181955671297672/instance-id-4
2025-05-24 02:13:44.597 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:13:44.597 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 3600秒，续约间隔: 1200秒
2025-05-24 02:13:44.598 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:44.598 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: pool-1-thread-5
2025-05-24 02:13:44.598 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 8 分配工作机器ID
2025-05-24 02:13:44.602 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:44.607 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 6
2025-05-24 02:13:44.607 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 6 分配了新的工作机器ID: 0
2025-05-24 02:13:44.608 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:44.609 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:44.610 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:44.612 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 1，实例ID: 8
2025-05-24 02:13:44.613 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 8 分配了新的工作机器ID: 1
2025-05-24 02:13:44.614 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功续约工作机器ID: 1，实例: 1419208112
2025-05-24 02:13:44.614 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 1419208112
2025-05-24 02:13:44.614 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 1419208112
2025-05-24 02:13:44.614 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 1419208112
2025-05-24 02:13:44.614 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 1419208112
2025-05-24 02:13:44.614 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 1419208112
2025-05-24 02:13:44.618 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 2，实例ID: 5
2025-05-24 02:13:44.618 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 5 分配了新的工作机器ID: 2
2025-05-24 02:13:44.620 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功续约工作机器ID: 2，实例: 1787058948
2025-05-24 02:13:44.620 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 1787058948
2025-05-24 02:13:44.620 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 1787058948
2025-05-24 02:13:44.620 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-24 02:13:44.620 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-24 02:13:44.620 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 1787058948
2025-05-24 02:13:44.620 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 1787058948
2025-05-24 02:13:44.620 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 1787058948
2025-05-24 02:13:44.621 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 9
2025-05-24 02:13:44.623 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 3，实例ID: 7
2025-05-24 02:13:44.624 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test15191181955671297672/instance-id-2
2025-05-24 02:13:44.624 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:13:44.624 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 3600秒，续约间隔: 1200秒
2025-05-24 02:13:44.638 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 7 分配了新的工作机器ID: 3
2025-05-24 02:13:44.639 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:13:44.639 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: pool-1-thread-3
2025-05-24 02:13:44.639 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 9 分配工作机器ID
2025-05-24 02:13:44.639 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功续约工作机器ID: 0，实例: 449652155
2025-05-24 02:13:44.639 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 449652155
2025-05-24 02:13:44.639 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 449652155
2025-05-24 02:13:44.640 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 449652155
2025-05-24 02:13:44.640 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 449652155
2025-05-24 02:13:44.640 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 449652155
2025-05-24 02:13:44.641 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:44.643 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功续约工作机器ID: 3，实例: 1411696677
2025-05-24 02:13:44.657 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 1411696677
2025-05-24 02:13:44.657 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 1411696677
2025-05-24 02:13:44.658 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 0
2025-05-24 02:13:44.658 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 449652155
2025-05-24 02:13:44.659 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 1411696677
2025-05-24 02:13:44.659 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 1411696677
2025-05-24 02:13:44.659 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 4，实例ID: 9
2025-05-24 02:13:44.659 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 1411696677
2025-05-24 02:13:44.660 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 9 分配了新的工作机器ID: 4
2025-05-24 02:13:44.661 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 1
2025-05-24 02:13:44.661 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 1419208112
2025-05-24 02:13:44.662 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 3
2025-05-24 02:13:44.662 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 1411696677
2025-05-24 02:13:44.664 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功续约工作机器ID: 4，实例: 1583560162
2025-05-24 02:13:44.664 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 1583560162
2025-05-24 02:13:44.664 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 1583560162
2025-05-24 02:13:44.664 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 1583560162
2025-05-24 02:13:44.664 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 1583560162
2025-05-24 02:13:44.665 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 1583560162
2025-05-24 02:13:44.666 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 4
2025-05-24 02:13:44.667 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 1583560162
2025-05-24 02:13:44.670 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 2
2025-05-24 02:13:44.670 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 1787058948
2025-05-24 02:13:44.671 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 开始清理临时文件，当前注册的临时文件数量: 0
2025-05-24 02:13:46.346 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-24 02:13:47.216 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 9332b400a8194bbad0f88fbf16dedb17bc4ae1415485693dd141dc09984af418
2025-05-24 02:13:47.433 [WorkerIdAssigner-LeaseRenewal-2066533285] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 续约工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42，实例: 2066533285
2025-05-24 02:13:47.434 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始重新分配工作机器ID
2025-05-24 02:13:47.434 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:47.434 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:13:47.434 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配工作机器ID成功，新的工作机器ID: 42
2025-05-24 02:13:47.523 [WorkerIdAssigner-LeaseRenewal-1576416089] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 续约工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42，实例: 1576416089
2025-05-24 02:13:47.523 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始重新分配工作机器ID
2025-05-24 02:13:47.523 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:47.523 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:13:47.524 [WorkerIdAssigner-Reassignment-1576416089] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-24 02:13:47.524 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42
2025-05-24 02:13:47.524 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配工作机器ID成功，新的工作机器ID: 42
2025-05-24 02:13:47.620 [WorkerIdAssigner-LeaseRenewal-1994984869] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 续约工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42，实例: 1994984869
2025-05-24 02:13:47.621 [WorkerIdAssigner-Reassignment-1994984869] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始重新分配工作机器ID
2025-05-24 02:13:47.621 [WorkerIdAssigner-Reassignment-1994984869] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:47.621 [WorkerIdAssigner-Reassignment-1994984869] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:13:47.621 [WorkerIdAssigner-Reassignment-1994984869] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配工作机器ID成功，新的工作机器ID: 42
2025-05-24 02:13:47.655 [WorkerIdAssigner-LeaseRenewal-325897214] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 续约工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42，实例: 325897214
2025-05-24 02:13:47.655 [WorkerIdAssigner-Reassignment-325897214] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始重新分配工作机器ID
2025-05-24 02:13:47.655 [WorkerIdAssigner-Reassignment-325897214] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:13:47.656 [WorkerIdAssigner-Reassignment-325897214] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:13:47.656 [WorkerIdAssigner-Reassignment-325897214] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配工作机器ID成功，新的工作机器ID: 42
2025-05-24 02:13:47.693 [WorkerIdAssigner-LeaseRenewal-104136534] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功续约工作机器ID: 42，实例: 104136534
2025-05-24 02:13:52.861 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT6.515338041S
2025-05-24 02:13:52.862 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-24 02:13:52.864 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-15 - Starting...
2025-05-24 02:13:52.895 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-15 - Added connection org.postgresql.jdbc.PgConnection@15be68b
2025-05-24 02:13:52.896 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-15 - Start completed.
2025-05-24 02:13:52.896 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:13:52.896 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:13:52.897 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:13:52.897 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:13:52.899 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 不存在，正在自动创建
2025-05-24 02:13:52.899 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 创建Schema（如果不存在）: infra_uid
2025-05-24 02:13:52.900 [main] DEBUG o.x.c.c.u.m.s.JdbcMetadataStrategy - 执行SQL: CREATE SCHEMA IF NOT EXISTS "infra_uid"
2025-05-24 02:13:52.902 [main] INFO  o.x.c.c.u.m.s.JdbcMetadataStrategy - 已创建Schema: infra_uid
2025-05-24 02:13:52.902 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 已成功创建
2025-05-24 02:13:52.902 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:13:52.902 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:13:52.905 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-24 02:13:52.917 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:13:52.917 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-24 02:13:52.917 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:13:52.919 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-24 02:13:52.928 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:13:52.928 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-24 02:13:52.928 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:13:52.929 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-24 02:13:52.936 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:13:52.936 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-24 02:13:52.936 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:13:52.937 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: key-test, 环境: test, Schema: infra_uid
2025-05-24 02:13:52.938 [main] DEBUG o.x.c.c.u.c.KeyManagementService - 使用NativePRNGNonBlocking算法的SecureRandom
2025-05-24 02:13:52.939 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已为应用 key-test 环境 test 创建新的 test-key-type 类型密钥
2025-05-24 02:13:54.706 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-24 02:13:56.090 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 34e630f66023951de2c53c9482bd9bb3d2c7185225f5d937cfe67d6a26f0287a
2025-05-24 02:14:00.532 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.825839768S
2025-05-24 02:14:00.532 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-24 02:14:00.534 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-16 - Starting...
2025-05-24 02:14:00.559 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-16 - Added connection org.postgresql.jdbc.PgConnection@6dc2279c
2025-05-24 02:14:00.560 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-16 - Start completed.
2025-05-24 02:14:00.560 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:14:00.561 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: test_schema
2025-05-24 02:14:00.564 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: non_existent_schema
2025-05-24 02:14:00.570 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-17 - Starting...
2025-05-24 02:14:00.589 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-17 - Added connection org.postgresql.jdbc.PgConnection@72001c71
2025-05-24 02:14:00.590 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-17 - Start completed.
2025-05-24 02:14:00.590 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:14:00.598 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-24 02:14:00.603 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.non_existent_table
2025-05-24 02:14:00.614 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-18 - Starting...
2025-05-24 02:14:00.632 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-18 - Added connection org.postgresql.jdbc.PgConnection@2e159116
2025-05-24 02:14:00.633 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-18 - Start completed.
2025-05-24 02:14:00.633 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:14:00.636 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 验证表结构是否包含所需的列: test_schema.test_table
2025-05-24 02:14:00.636 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-24 02:14:00.640 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table
2025-05-24 02:14:00.646 [main] WARN  o.x.c.c.u.m.PostgreSQLMetadataService - 表 test_schema.test_table 缺少必需的列: name
2025-05-24 02:14:00.652 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-19 - Starting...
2025-05-24 02:14:00.674 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-19 - Added connection org.postgresql.jdbc.PgConnection@2e4ecdf8
2025-05-24 02:14:00.675 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-19 - Start completed.
2025-05-24 02:14:00.675 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:14:00.683 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:14:00.683 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: test_schema
2025-05-24 02:14:00.693 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - Schema test_schema 验证通过
2025-05-24 02:14:00.693 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-24 02:14:00.707 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 test_schema.test_table 验证通过
2025-05-24 02:14:00.707 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-24 02:14:00.710 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 test_schema.test_table 验证通过
2025-05-24 02:14:00.710 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table
2025-05-24 02:14:00.715 [main] ERROR o.x.c.c.u.s.i.UidValidationServiceImpl - 表结构验证失败: 表 test_schema.test_table 缺少必需的列 name
2025-05-24 02:14:00.717 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-20 - Starting...
2025-05-24 02:14:00.753 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-20 - Added connection org.postgresql.jdbc.PgConnection@21a9f95b
2025-05-24 02:14:00.753 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-20 - Start completed.
2025-05-24 02:14:00.754 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:14:00.757 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table

Test run finished after 45158 ms
[         6 containers found      ]
[         0 containers skipped    ]
[         6 containers started    ]
[         0 containers aborted    ]
[         6 containers successful ]
[         0 containers failed     ]
[        14 tests found           ]
[         0 tests skipped         ]
[        14 tests started         ]
[         0 tests aborted         ]
[         8 tests successful      ]
[         6 tests failed          ]


===== 运行简化测试 =====

1. 基本功能测试
2025-05-24 02:14:02.497 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-21 - Starting...
2025-05-24 02:14:02.513 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-21 - Added connection org.postgresql.jdbc.PgConnection@527d48db
2025-05-24 02:14:02.514 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-21 - Start completed.
2025-05-24 02:14:02.514 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 为测试环境构建UidGeneratorFacade
2025-05-24 02:14:02.514 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 构建UidGeneratorFacade
2025-05-24 02:14:02.514 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 初始化UidGeneratorFacade
2025-05-24 02:14:02.514 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建JdbcTemplate
2025-05-24 02:14:02.514 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建TransactionTemplate
2025-05-24 02:14:02.514 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建DatabaseMetadataService
2025-05-24 02:14:02.514 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:14:02.514 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidValidationService
2025-05-24 02:14:02.514 [main] INFO  o.x.c.c.u.config.UidValidationConfig - 创建UidValidationService实例
2025-05-24 02:14:02.514 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidTableManager
2025-05-24 02:14:02.514 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:14:02.515 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:14:02.515 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:14:02.517 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:14:02.517 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:14:02.521 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-24 02:14:02.521 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:14:02.523 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-24 02:14:02.523 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-24 02:14:02.530 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-24 02:14:02.531 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:14:02.531 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:14:02.536 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-24 02:14:02.536 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:14:02.538 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-24 02:14:02.538 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-24 02:14:02.545 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-24 02:14:02.545 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:14:02.545 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:14:02.547 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-24 02:14:02.547 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:14:02.549 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-24 02:14:02.549 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-24 02:14:02.553 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-24 02:14:02.553 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:14:02.553 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:14:02.553 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceManager
2025-05-24 02:14:02.553 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:14:02.553 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:14:02.553 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: test-app, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:14:02.555 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 1
2025-05-24 02:14:02.557 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 1 在数据库中存在，更新实例信息
2025-05-24 02:14:02.558 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 1 的最后活动时间
2025-05-24 02:14:02.558 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:14:02.558 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceWorkerIdAssigner
2025-05-24 02:14:02.559 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:14:02.559 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:14:02.559 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建CachedUidGenerator
2025-05-24 02:14:02.559 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - CachedUidGenerator配置完成，准备初始化RingBuffer
2025-05-24 02:14:02.559 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 调用CachedUidGenerator.afterPropertiesSet()方法
2025-05-24 02:14:02.559 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:14:02.559 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配工作机器ID
2025-05-24 02:14:02.560 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:14:02.562 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 1
2025-05-24 02:14:02.563 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配了新的工作机器ID: 0
2025-05-24 02:14:02.563 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功分配工作机器ID: 0，重试次数: 0
2025-05-24 02:14:02.563 [main] INFO  c.x.uid.impl.DefaultUidGenerator - Initialized bits(1, 18, 18, 14) for workerID:0
2025-05-24 02:14:02.566 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized ring buffer size:131072, paddingFactor:50
2025-05-24 02:14:02.566 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized BufferPaddingExecutor. Using schdule:true, interval:60
2025-05-24 02:14:02.566 [main] INFO  com.xfvape.uid.buffer.RingBuffer - Ready to padding buffer lastSecond:1748024042. RingBuffer [bufferSize=131072, tail=-1, cursor=-1, paddingThreshold=65536]
2025-05-24 02:14:02.572 [main] WARN  com.xfvape.uid.buffer.RingBuffer - Rejected putting buffer for uid:324363021184925696. RingBuffer [bufferSize=131072, tail=131071, cursor=-1, paddingThreshold=65536]
2025-05-24 02:14:02.572 [main] INFO  com.xfvape.uid.buffer.RingBuffer - End to padding buffer lastSecond:1748024051. RingBuffer [bufferSize=131072, tail=131071, cursor=-1, paddingThreshold=65536]
2025-05-24 02:14:02.573 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized RingBuffer successfully.
2025-05-24 02:14:02.573 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - CachedUidGenerator.afterPropertiesSet()方法调用成功
2025-05-24 02:14:02.573 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - RingBuffer初始化成功
2025-05-24 02:14:02.573 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade初始化完成
生成的UID示例:
  UID 1: 324362986825187328
  UID 2: 324362986825187329
  UID 3: 324362986825187330
  UID 4: 324362986825187331
  UID 5: 324362986825187332
批量生成的UID:
  批量UID 1: 324362986825187333
  批量UID 2: 324362986825187334
  批量UID 3: 324362986825187335
基本功能测试完成
2025-05-24 02:14:02.578 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 关闭UidGeneratorFacade资源
2025-05-24 02:14:02.578 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 开始关闭CachedUidGenerator的BufferPaddingExecutor
2025-05-24 02:14:02.578 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - BufferPaddingExecutor关闭成功
2025-05-24 02:14:02.578 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 138329541
2025-05-24 02:14:02.578 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 138329541
2025-05-24 02:14:02.578 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 138329541
2025-05-24 02:14:02.578 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 138329541
2025-05-24 02:14:02.578 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 138329541
2025-05-24 02:14:02.579 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 0
2025-05-24 02:14:02.579 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 138329541
2025-05-24 02:14:02.579 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade资源已关闭

2. 实例恢复测试
2025-05-24 02:14:02.581 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-22 - Starting...
2025-05-24 02:14:02.598 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-22 - Added connection org.postgresql.jdbc.PgConnection@3d132bb6
2025-05-24 02:14:02.598 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-22 - Start completed.
2025-05-24 02:14:02.598 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:14:02.598 [main] INFO  o.x.c.c.u.config.UidValidationConfig - 创建UidValidationService实例
2025-05-24 02:14:02.598 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:14:02.599 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:14:02.599 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:14:02.601 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:14:02.601 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:14:02.605 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-24 02:14:02.605 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:14:02.606 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-24 02:14:02.606 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-24 02:14:02.613 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-24 02:14:02.613 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:14:02.613 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:14:02.616 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-24 02:14:02.616 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:14:02.617 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-24 02:14:02.617 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-24 02:14:02.621 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-24 02:14:02.621 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:14:02.621 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:14:02.623 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-24 02:14:02.623 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:14:02.624 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-24 02:14:02.625 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-24 02:14:02.628 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-24 02:14:02.628 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:14:02.628 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
===== 第一次运行 =====
2025-05-24 02:14:02.629 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:14:02.629 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:14:02.629 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: recovery-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:14:02.630 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 2
2025-05-24 02:14:02.632 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 2 在数据库中不存在
2025-05-24 02:14:02.632 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-24 02:14:02.633 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-24 02:14:02.633 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-24 02:14:02.635 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 2
2025-05-24 02:14:02.638 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
2025-05-24 02:14:02.638 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
第一次运行 - 实例ID: 2

===== 第二次运行 (从文件恢复) =====
2025-05-24 02:14:02.639 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:14:02.639 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:14:02.639 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: recovery-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:14:02.641 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 2
2025-05-24 02:14:02.642 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 2 在数据库中存在，更新实例信息
2025-05-24 02:14:02.643 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 2 的最后活动时间
2025-05-24 02:14:02.643 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
第二次运行 - 实例ID: 2
从文件恢复结果: 成功

===== 删除文件后运行 (从特征码恢复) =====
2025-05-24 02:14:02.644 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:14:02.644 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:14:02.644 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: recovery-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:14:02.644 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
2025-05-24 02:14:02.645 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-24 02:14:02.646 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 通过指纹哈希精确匹配找到实例ID: 2
2025-05-24 02:14:02.646 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 成功恢复实例ID: 2
2025-05-24 02:14:02.647 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 2 的最后活动时间
2025-05-24 02:14:02.649 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
2025-05-24 02:14:02.649 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
删除文件后运行 - 实例ID: 2
从特征码恢复结果: 成功

3. 租约管理测试
2025-05-24 02:14:02.651 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - ===== 租约管理测试开始 =====
2025-05-24 02:14:02.651 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1. 准备测试环境 - 初始化数据源
2025-05-24 02:14:02.651 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.1 检查Docker是否正常运行
2025-05-24 02:14:02.827 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - Docker正常运行
2025-05-24 02:14:02.828 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.2 创建PostgresTestContainer实例
2025-05-24 02:14:02.828 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - PostgresTestContainer实例创建成功
2025-05-24 02:14:02.828 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.3 检查容器是否正在运行
2025-05-24 02:14:02.831 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 容器运行状态: true
2025-05-24 02:14:02.831 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.4 获取JDBC连接信息
2025-05-24 02:14:02.831 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - JDBC URL: ******************************************************
2025-05-24 02:14:02.831 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 用户名: test
2025-05-24 02:14:02.831 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 密码: test
2025-05-24 02:14:02.831 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.5 获取数据源
2025-05-24 02:14:02.832 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-23 - Starting...
2025-05-24 02:14:02.849 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-23 - Added connection org.postgresql.jdbc.PgConnection@7bce9ce4
2025-05-24 02:14:02.849 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-23 - Start completed.
2025-05-24 02:14:02.849 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据源获取成功
2025-05-24 02:14:02.849 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.6 测试数据库连接
2025-05-24 02:14:02.850 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库连接成功
2025-05-24 02:14:02.850 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库产品名称: PostgreSQL
2025-05-24 02:14:02.850 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库产品版本: 17.4 (Debian 17.4-1.pgdg120+2)
2025-05-24 02:14:02.850 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库驱动名称: PostgreSQL JDBC Driver
2025-05-24 02:14:02.850 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库驱动版本: 42.7.5
2025-05-24 02:14:02.850 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.7 创建JdbcTemplate和TransactionTemplate
2025-05-24 02:14:02.850 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - JdbcTemplate和TransactionTemplate创建成功
2025-05-24 02:14:02.850 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.8 创建元数据服务和验证服务
2025-05-24 02:14:02.850 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:14:02.850 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.9 初始化表结构
2025-05-24 02:14:02.850 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:14:02.851 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:14:02.851 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:14:02.853 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:14:02.853 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:14:02.857 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-24 02:14:02.857 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:14:02.860 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-24 02:14:02.860 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-24 02:14:02.867 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-24 02:14:02.867 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:14:02.867 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:14:02.869 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-24 02:14:02.869 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:14:02.871 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-24 02:14:02.871 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-24 02:14:02.875 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-24 02:14:02.875 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:14:02.875 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:14:02.876 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-24 02:14:02.876 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:14:02.878 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-24 02:14:02.879 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-24 02:14:02.882 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-24 02:14:02.883 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:14:02.883 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:14:02.883 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.2 验证表结构是否创建成功
2025-05-24 02:14:02.891 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 已创建的表: [{table_name=instance_registry}, {table_name=worker_id_assignment}, {table_name=encryption_key}]
2025-05-24 02:14:02.891 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 2. 创建 KeyManagementService
2025-05-24 02:14:02.891 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: lease-test, 环境: test, Schema: infra_uid
2025-05-24 02:14:02.891 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - KeyManagementService 创建成功，加密状态: false
2025-05-24 02:14:02.891 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 3. 创建实例管理器
2025-05-24 02:14:02.891 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:14:02.891 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:14:02.891 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: lease-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AND_NEW, 加密启用: false
2025-05-24 02:14:02.893 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 3
2025-05-24 02:14:02.894 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 3 在数据库中不存在
2025-05-24 02:14:02.894 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-24 02:14:02.895 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到精确匹配，且恢复策略为创建新实例，跳过模糊匹配
2025-05-24 02:14:02.895 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-24 02:14:02.896 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 3
2025-05-24 02:14:02.898 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/lease-test-instance-id.dat
2025-05-24 02:14:02.898 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:14:02.898 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 2.1 实例管理器创建成功，实例ID: 3
2025-05-24 02:14:02.901 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 已注册的实例: [{instance_unique_id=3, application_name=lease-test, environment=test, instance_group=default, status=ACTIVE, first_registered_at=2025-05-24 02:14:02.896269, last_seen_at=2025-05-24 02:14:02.896269, custom_metadata={"os_arch": "amd64", "os_name": "Linux", "hostname": "long-VirtualBox", "os_version": "5.4.0-91-generic", "mac_addresses": ["CE:A9:04:3F:73:FD", "08:00:27:D6:3A:87"], "fingerprint_hash": "0cd7e71f226e917911f5e1251a5665812c4065753531534bd12cbdfeaab5c60d"}}]
2025-05-24 02:14:02.901 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 3. 创建工作机器ID分配器
2025-05-24 02:14:02.901 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:14:02.901 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:14:02.901 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 4. 开始分配工作机器ID
2025-05-24 02:14:02.901 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:14:02.901 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 3 分配工作机器ID
2025-05-24 02:14:02.903 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:14:02.905 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 3
2025-05-24 02:14:02.905 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 3 分配了新的工作机器ID: 0
2025-05-24 02:14:02.905 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 分配的工作机器ID: 0
2025-05-24 02:14:02.907 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 工作机器ID分配记录: [{worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:02.903711, lease_expires_at=2025-05-24 02:15:02.903711, released_at=2025-05-24 02:14:02.579236}]
2025-05-24 02:14:02.907 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5. 测试租约续约 - 通过心跳线程自动续约
2025-05-24 02:14:02.907 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.1 等待心跳线程自动续约前，当前状态
2025-05-24 02:14:02.908 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:02.903711, lease_expires_at=2025-05-24 02:15:02.903711, released_at=2025-05-24 02:14:02.579236}
2025-05-24 02:14:02.908 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-24 02:14:04.016 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.1 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:02.903711, lease_expires_at=2025-05-24 02:15:02.903711, released_at=2025-05-24 02:14:02.579236}
2025-05-24 02:14:04.016 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.2 等待心跳线程自动续约前，当前状态
2025-05-24 02:14:04.017 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:02.903711, lease_expires_at=2025-05-24 02:15:02.903711, released_at=2025-05-24 02:14:02.579236}
2025-05-24 02:14:04.017 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-24 02:14:05.025 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.2 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:02.903711, lease_expires_at=2025-05-24 02:15:02.903711, released_at=2025-05-24 02:14:02.579236}
2025-05-24 02:14:05.025 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.3 等待心跳线程自动续约前，当前状态
2025-05-24 02:14:05.029 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:02.903711, lease_expires_at=2025-05-24 02:15:02.903711, released_at=2025-05-24 02:14:02.579236}
2025-05-24 02:14:05.029 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-24 02:14:06.037 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.3 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:02.903711, lease_expires_at=2025-05-24 02:15:02.903711, released_at=2025-05-24 02:14:02.579236}
2025-05-24 02:14:06.038 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6. 测试手动续约
2025-05-24 02:14:06.038 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.1 手动续约前，当前状态
2025-05-24 02:14:06.039 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:02.903711, lease_expires_at=2025-05-24 02:15:02.903711, released_at=2025-05-24 02:14:02.579236}
2025-05-24 02:14:06.039 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-24 02:14:06.040 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功续约工作机器ID: 0，实例: 2038405091
2025-05-24 02:14:06.041 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.1 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:06.039749, lease_expires_at=2025-05-24 02:15:06.039749, released_at=2025-05-24 02:14:02.579236}
2025-05-24 02:14:06.041 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-24 02:14:07.044 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.2 手动续约前，当前状态
2025-05-24 02:14:07.045 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:06.039749, lease_expires_at=2025-05-24 02:15:06.039749, released_at=2025-05-24 02:14:02.579236}
2025-05-24 02:14:07.046 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-24 02:14:07.047 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功续约工作机器ID: 0，实例: 2038405091
2025-05-24 02:14:07.048 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.2 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:07.04639, lease_expires_at=2025-05-24 02:15:07.04639, released_at=2025-05-24 02:14:02.579236}
2025-05-24 02:14:07.050 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-24 02:14:07.431 [WorkerIdAssigner-LeaseRenewal-2066533285] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 续约工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42，实例: 2066533285
2025-05-24 02:14:07.431 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始重新分配工作机器ID
2025-05-24 02:14:07.431 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:14:07.431 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:14:07.431 [WorkerIdAssigner-Reassignment-2066533285] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配工作机器ID成功，新的工作机器ID: 42
2025-05-24 02:14:07.525 [WorkerIdAssigner-LeaseRenewal-1576416089] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 续约工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42，实例: 1576416089
2025-05-24 02:14:07.525 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始重新分配工作机器ID
2025-05-24 02:14:07.526 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:14:07.526 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:14:07.527 [WorkerIdAssigner-Reassignment-1576416089] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-24 02:14:07.527 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42
2025-05-24 02:14:07.527 [WorkerIdAssigner-Reassignment-1576416089] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配工作机器ID成功，新的工作机器ID: 42
2025-05-24 02:14:07.620 [WorkerIdAssigner-LeaseRenewal-1994984869] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 续约工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42，实例: 1994984869
2025-05-24 02:14:07.620 [WorkerIdAssigner-Reassignment-1994984869] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始重新分配工作机器ID
2025-05-24 02:14:07.620 [WorkerIdAssigner-Reassignment-1994984869] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:14:07.621 [WorkerIdAssigner-Reassignment-1994984869] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:14:07.621 [WorkerIdAssigner-Reassignment-1994984869] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配工作机器ID成功，新的工作机器ID: 42
2025-05-24 02:14:07.655 [WorkerIdAssigner-LeaseRenewal-325897214] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 续约工作机器ID失败，可能已过期或被其他实例占用，工作机器ID: 42，实例: 325897214
2025-05-24 02:14:07.655 [WorkerIdAssigner-Reassignment-325897214] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始重新分配工作机器ID
2025-05-24 02:14:07.656 [WorkerIdAssigner-Reassignment-325897214] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配工作机器ID
2025-05-24 02:14:07.656 [WorkerIdAssigner-Reassignment-325897214] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42
2025-05-24 02:14:07.656 [WorkerIdAssigner-Reassignment-325897214] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配工作机器ID成功，新的工作机器ID: 42
2025-05-24 02:14:07.692 [WorkerIdAssigner-LeaseRenewal-104136534] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功续约工作机器ID: 42，实例: 104136534
2025-05-24 02:14:08.052 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.3 手动续约前，当前状态
2025-05-24 02:14:08.053 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:07.04639, lease_expires_at=2025-05-24 02:15:07.04639, released_at=2025-05-24 02:14:02.579236}
2025-05-24 02:14:08.054 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-24 02:14:08.055 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功续约工作机器ID: 0，实例: 2038405091
2025-05-24 02:14:08.055 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.3 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:08.054494, lease_expires_at=2025-05-24 02:15:08.054494, released_at=2025-05-24 02:14:02.579236}
2025-05-24 02:14:08.056 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-24 02:14:09.056 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7. 测试租约失效后的重新分配
2025-05-24 02:14:09.056 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.1 模拟租约失效 - 将工作机器ID状态设置为AVAILABLE
2025-05-24 02:14:09.058 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 更新结果: 影响行数 = 1
2025-05-24 02:14:09.059 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 更新后状态: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:08.054494, lease_expires_at=2025-05-24 02:15:08.054494, released_at=2025-05-24 02:14:02.579236}
2025-05-24 02:14:09.059 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.2 等待2秒，让租约续约线程发现租约失效
2025-05-24 02:14:11.060 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.3 尝试重新获取工作机器ID
2025-05-24 02:14:11.061 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 重新分配的工作机器ID: 0
2025-05-24 02:14:11.063 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 新工作机器ID记录: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:08.054494, lease_expires_at=2025-05-24 02:15:08.054494, released_at=2025-05-24 02:14:02.579236}
2025-05-24 02:14:11.063 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 8. 关闭心跳线程
2025-05-24 02:14:11.063 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 2038405091
2025-05-24 02:14:11.063 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 2038405091
2025-05-24 02:14:11.064 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 2038405091
2025-05-24 02:14:11.064 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 2038405091
2025-05-24 02:14:11.064 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 2038405091
2025-05-24 02:14:11.066 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 0
2025-05-24 02:14:11.066 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 2038405091
2025-05-24 02:14:11.067 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 关闭后状态: {worker_id=0, instance_id=null, status=AVAILABLE, assigned_at=2025-05-24 02:14:02.903711, last_renewed_at=2025-05-24 02:14:08.054494, lease_expires_at=2025-05-24 02:15:08.054494, released_at=2025-05-24 02:14:11.065114}
2025-05-24 02:14:11.067 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - ===== 租约管理测试完成 =====

4. 门面模式测试
开始运行门面模式基本功能测试
初始化PostgreSQL测试容器
2025-05-24 02:14:11.074 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-24 - Starting...
2025-05-24 02:14:11.101 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-24 - Added connection org.postgresql.jdbc.PgConnection@7f53fc38
2025-05-24 02:14:11.101 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-24 - Start completed.
PostgreSQL测试容器初始化完成
创建UidGeneratorFacade
2025-05-24 02:14:11.101 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 构建UidGeneratorFacade
2025-05-24 02:14:11.101 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 初始化UidGeneratorFacade
2025-05-24 02:14:11.101 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建JdbcTemplate
2025-05-24 02:14:11.101 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建TransactionTemplate
2025-05-24 02:14:11.101 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建DatabaseMetadataService
2025-05-24 02:14:11.101 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:14:11.101 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidValidationService
2025-05-24 02:14:11.101 [main] INFO  o.x.c.c.u.config.UidValidationConfig - 创建UidValidationService实例
2025-05-24 02:14:11.101 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidTableManager
2025-05-24 02:14:11.101 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:14:11.102 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:14:11.102 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:14:11.105 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:14:11.105 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:14:11.109 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-24 02:14:11.109 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:14:11.110 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-24 02:14:11.110 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-24 02:14:11.127 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-24 02:14:11.127 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:14:11.127 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:14:11.129 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-24 02:14:11.129 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:14:11.130 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-24 02:14:11.130 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-24 02:14:11.133 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-24 02:14:11.133 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:14:11.133 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:14:11.134 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-24 02:14:11.134 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:14:11.142 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-24 02:14:11.142 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-24 02:14:11.145 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-24 02:14:11.145 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:14:11.145 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:14:11.145 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceManager
2025-05-24 02:14:11.145 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:14:11.146 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:14:11.146 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: facade-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:14:11.148 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 4
2025-05-24 02:14:11.149 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 4 在数据库中不存在
2025-05-24 02:14:11.149 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-24 02:14:11.151 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-24 02:14:11.151 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-24 02:14:11.152 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 4
2025-05-24 02:14:11.154 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/facade-test-instance-id.dat
2025-05-24 02:14:11.155 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:14:11.155 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceWorkerIdAssigner
2025-05-24 02:14:11.155 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:14:11.155 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:14:11.155 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建CachedUidGenerator
2025-05-24 02:14:11.155 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - CachedUidGenerator配置完成，准备初始化RingBuffer
2025-05-24 02:14:11.155 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 调用CachedUidGenerator.afterPropertiesSet()方法
2025-05-24 02:14:11.156 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:14:11.156 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 4 分配工作机器ID
2025-05-24 02:14:11.157 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:14:11.159 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 4
2025-05-24 02:14:11.160 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 4 分配了新的工作机器ID: 0
2025-05-24 02:14:11.160 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功分配工作机器ID: 0，重试次数: 0
2025-05-24 02:14:11.160 [main] INFO  c.x.uid.impl.DefaultUidGenerator - Initialized bits(1, 18, 18, 14) for workerID:0
2025-05-24 02:14:11.183 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized ring buffer size:131072, paddingFactor:50
2025-05-24 02:14:11.185 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized BufferPaddingExecutor. Using schdule:true, interval:60
2025-05-24 02:14:11.185 [main] INFO  com.xfvape.uid.buffer.RingBuffer - Ready to padding buffer lastSecond:1748024051. RingBuffer [bufferSize=131072, tail=-1, cursor=-1, paddingThreshold=65536]
2025-05-24 02:14:11.199 [main] WARN  com.xfvape.uid.buffer.RingBuffer - Rejected putting buffer for uid:324363059839631360. RingBuffer [bufferSize=131072, tail=131071, cursor=-1, paddingThreshold=65536]
2025-05-24 02:14:11.199 [main] INFO  com.xfvape.uid.buffer.RingBuffer - End to padding buffer lastSecond:1748024060. RingBuffer [bufferSize=131072, tail=131071, cursor=-1, paddingThreshold=65536]
2025-05-24 02:14:11.200 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized RingBuffer successfully.
2025-05-24 02:14:11.200 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - CachedUidGenerator.afterPropertiesSet()方法调用成功
2025-05-24 02:14:11.200 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - RingBuffer初始化成功
2025-05-24 02:14:11.200 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade初始化完成
UidGeneratorFacade创建完成
尝试获取UID
生成的UID: 324363025479892992
尝试批量获取10个UID
批量生成的UID数量: 10
批量UID示例:
  - 324363025479892993
  - 324363025479892994
  - 324363025479892995
  - 324363025479892996
  - 324363025479892997

使用便捷方法创建测试环境的门面:
2025-05-24 02:14:11.202 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 为测试环境构建UidGeneratorFacade
2025-05-24 02:14:11.202 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 构建UidGeneratorFacade
2025-05-24 02:14:11.202 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 初始化UidGeneratorFacade
2025-05-24 02:14:11.202 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建JdbcTemplate
2025-05-24 02:14:11.202 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建TransactionTemplate
2025-05-24 02:14:11.202 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建DatabaseMetadataService
2025-05-24 02:14:11.202 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-24 02:14:11.202 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidValidationService
2025-05-24 02:14:11.203 [main] INFO  o.x.c.c.u.config.UidValidationConfig - 创建UidValidationService实例
2025-05-24 02:14:11.203 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidTableManager
2025-05-24 02:14:11.203 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-24 02:14:11.206 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-24 02:14:11.206 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-24 02:14:11.208 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-24 02:14:11.208 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:14:11.210 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-24 02:14:11.210 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-24 02:14:11.212 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-24 02:14:11.212 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-24 02:14:11.215 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-24 02:14:11.215 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-24 02:14:11.215 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:14:11.218 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-24 02:14:11.218 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-24 02:14:11.222 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-24 02:14:11.222 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-24 02:14:11.226 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-24 02:14:11.226 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-24 02:14:11.226 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:14:11.227 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-24 02:14:11.228 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-24 02:14:11.229 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-24 02:14:11.229 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-24 02:14:11.232 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-24 02:14:11.232 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-24 02:14:11.232 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-24 02:14:11.232 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceManager
2025-05-24 02:14:11.232 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-24 02:14:11.233 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-24 02:14:11.233 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: test-app, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-24 02:14:11.234 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 1
2025-05-24 02:14:11.235 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 1 在数据库中存在，更新实例信息
2025-05-24 02:14:11.237 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 1 的最后活动时间
2025-05-24 02:14:11.237 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-24 02:14:11.237 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceWorkerIdAssigner
2025-05-24 02:14:11.237 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 正在初始化WorkerIdAssigner，租约时长: 60秒，续约间隔: 20秒
2025-05-24 02:14:11.238 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner初始化完成，状态: RUNNING
2025-05-24 02:14:11.238 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建CachedUidGenerator
2025-05-24 02:14:11.238 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - CachedUidGenerator配置完成，准备初始化RingBuffer
2025-05-24 02:14:11.238 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 调用CachedUidGenerator.afterPropertiesSet()方法
2025-05-24 02:14:11.238 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，线程: main
2025-05-24 02:14:11.238 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配工作机器ID
2025-05-24 02:14:11.239 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID
2025-05-24 02:14:11.242 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 1，实例ID: 1
2025-05-24 02:14:11.243 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配了新的工作机器ID: 1
2025-05-24 02:14:11.243 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功分配工作机器ID: 1，重试次数: 0
2025-05-24 02:14:11.243 [main] INFO  c.x.uid.impl.DefaultUidGenerator - Initialized bits(1, 18, 18, 14) for workerID:1
2025-05-24 02:14:11.254 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized ring buffer size:131072, paddingFactor:50
2025-05-24 02:14:11.255 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized BufferPaddingExecutor. Using schdule:true, interval:60
2025-05-24 02:14:11.255 [main] INFO  com.xfvape.uid.buffer.RingBuffer - Ready to padding buffer lastSecond:1748024051. RingBuffer [bufferSize=131072, tail=-1, cursor=-1, paddingThreshold=65536]
2025-05-24 02:14:11.261 [main] WARN  com.xfvape.uid.buffer.RingBuffer - Rejected putting buffer for uid:324363059839647744. RingBuffer [bufferSize=131072, tail=131071, cursor=-1, paddingThreshold=65536]
2025-05-24 02:14:11.262 [main] INFO  com.xfvape.uid.buffer.RingBuffer - End to padding buffer lastSecond:1748024060. RingBuffer [bufferSize=131072, tail=131071, cursor=-1, paddingThreshold=65536]
2025-05-24 02:14:11.262 [main] INFO  c.xfvape.uid.impl.CachedUidGenerator - Initialized RingBuffer successfully.
2025-05-24 02:14:11.262 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - CachedUidGenerator.afterPropertiesSet()方法调用成功
2025-05-24 02:14:11.263 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - RingBuffer初始化成功
2025-05-24 02:14:11.263 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade初始化完成
测试环境生成的UID: 324363025479909376
关闭testFacade
2025-05-24 02:14:11.263 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 关闭UidGeneratorFacade资源
2025-05-24 02:14:11.263 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 开始关闭CachedUidGenerator的BufferPaddingExecutor
2025-05-24 02:14:11.263 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - BufferPaddingExecutor关闭成功
2025-05-24 02:14:11.263 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 715027790
2025-05-24 02:14:11.263 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 715027790
2025-05-24 02:14:11.264 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 715027790
2025-05-24 02:14:11.264 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 715027790
2025-05-24 02:14:11.264 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 715027790
2025-05-24 02:14:11.272 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功释放工作机器ID: 1
2025-05-24 02:14:11.272 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 715027790
2025-05-24 02:14:11.272 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade资源已关闭
关闭facade
2025-05-24 02:14:11.273 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 关闭UidGeneratorFacade资源
2025-05-24 02:14:11.273 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 开始关闭CachedUidGenerator的BufferPaddingExecutor
2025-05-24 02:14:11.273 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - BufferPaddingExecutor关闭成功
2025-05-24 02:14:11.273 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭WorkerIdAssigner，实例: 2107079200
2025-05-24 02:14:11.273 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭租约续约调度器，实例: 2107079200
2025-05-24 02:14:11.277 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 租约续约调度器关闭完成，实例: 2107079200
2025-05-24 02:14:11.277 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始关闭重新分配线程池，实例: 2107079200
2025-05-24 02:14:11.277 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重新分配线程池关闭完成，实例: 2107079200
2025-05-24 02:14:11.284 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - WorkerIdAssigner关闭完成，实例: 2107079200
2025-05-24 02:14:11.284 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade资源已关闭
关闭PostgreSQL容器
测试完成
JVM关闭钩子被触发，确保关闭所有资源
2025-05-24 02:14:12.418 [temp-file-cleanup-hook] DEBUG o.x.c.commons.uid.util.TempFileUtils - 执行临时文件清理关闭钩子
2025-05-24 02:14:12.418 [temp-file-cleanup-hook] DEBUG o.x.c.commons.uid.util.TempFileUtils - 开始清理临时文件，当前注册的临时文件数量: 0

进程已结束，退出代码为 0
