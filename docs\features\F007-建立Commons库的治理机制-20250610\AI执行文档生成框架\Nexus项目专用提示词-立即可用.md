# Nexus万用插座项目专用提示词 - 立即可用

## 使用说明
直接复制以下提示词，即可为Nexus万用插座项目生成完整的AI精确执行文档体系。

---

## 完整提示词

```
学习 docs/features/F007-建立Commons库的治理机制-20250610/将UID库切换到XCE异常库/plans/ 的文档体系结构，为 Nexus万用插座框架 项目生成一套完整的AI精确执行文档。

### 项目背景
- **项目名称**: Nexus万用插座框架
- **技术栈**: Spring Boot, Maven, Java SPI, 微内核架构, 插件化系统
- **核心目标**: 建立插件化可扩展的微内核架构框架，支持Commons库的插件化治理
- **复杂度**: 中等（6个模块：nexus-api, nexus-kernel, nexus-service-bus, nexus-starter, nexus-plugin-db, nexus-plugin-cache）
- **代码修改量**: 约3000行代码（新建框架）
- **AI认知复杂度**: 中 - AI需要分层理解微内核架构，注意插件生命周期管理，适度记忆管理
- **风险控制重点**: 强化模块间协调，增加集成验证，防止插件接口不匹配
- **AI执行注意事项**: 分阶段验证，每完成一个模块立即测试集成，特别关注插件加载机制

### AI认知复杂度判断结果: 中认知复杂度项目
根据AI认知复杂度判断规则，Nexus万用插座项目属于**中认知复杂度项目**，使用扩展文档序列号：[01-09] + [10, 11, 40, 41]

⚠️ **AI执行特别提醒**：
- 微内核架构需要分层理解：API契约层 → 内核层 → 服务总线层 → 自动配置层 → 插件层
- 插件生命周期管理复杂，需要严格控制启动顺序和依赖关系
- Spring Boot集成点较多，需要特别注意自动配置的边界和冲突
- 服务总线的异步消息处理需要重点验证性能和可靠性

### 必须生成的文档清单 (中等项目扩展序列号)

#### 标准核心文档 (01-09)
1. **01-nexus-impl-plan.md**
   - 总体目标：建立微内核可扩展架构的完整实施计划
   - 分阶段实施步骤：API契约层 → 微内核层 → 服务总线层 → 自动配置层 → 插件适配层
   - 操作边界：每阶段仅修改指定模块，单次修改限制在50行代码以内
   - 验证锚点：每个模块完成后立即编译验证，确保框架层次清晰
   - 执行原则：奠基石模式，自底向上逐层构建，确保每层都建立在坚实基础上

2. **02-nexus-execution-checklist.md**
   - API契约层检查：Plugin接口、@ExtensionPoint注解、Event基类的完整定义
   - 微内核层检查：PluginLoader、LifecycleManager的SPI实现和依赖管理
   - 服务总线层检查：ServiceBus接口和InMemoryServiceBus的异步消息处理
   - 自动配置层检查：NexusAutoConfiguration和NexusProperties的Spring集成
   - 插件适配层检查：现有Commons库的插件化适配验证
   - 质量门禁：每阶段编译成功、单元测试通过、集成测试验证

3. **03-nexus-code-templates.md**
   - Plugin接口定义模板：生命周期方法（start、stop、getName）的标准实现
   - @ExtensionPoint注解模板：插件服务暴露的标准注解定义
   - ServiceBus消息发布模板：Event发布和订阅的标准代码模式
   - 自动配置模板：Spring Boot集成的标准配置类模板
   - 插件适配模板：现有Commons库转换为插件的标准模式
   - 禁止模糊匹配：所有代码示例必须精确可用，包含完整包名和导入语句

4. **04-nexus-risk-assessment.md**
   - 高风险项：微内核架构复杂度、插件生命周期管理、服务总线性能
   - 中风险项：Spring Boot集成兼容性、现有Commons库适配影响
   - 低风险项：文档同步、配置复杂度
   - 回滚触发条件：P0（框架无法启动）、P1（插件加载失败）、P2（性能下降）
   - 具体缓解措施：分层验证、渐进式集成、完整回滚方案

5. **05-nexus-integration-guide.md**
   - Core项目集成指导：如何在现有项目中启用Nexus框架
   - Commons库适配指导：DB库和Cache库的插件化改造步骤
   - 配置参数指导：application.yml中Nexus相关配置的完整说明
   - 测试验证指导：框架集成后的功能验证和性能测试方案

8. **08-nexus-dependency-map.json**
   - nexus_framework模块定义：nexus-api、nexus-kernel、nexus-service-bus、nexus-starter
   - nexus_plugins模块定义：nexus-plugin-db、nexus-plugin-cache
   - 依赖关系映射：严格的模块依赖顺序和编译验证要求
   - 关键文件路径：每个模块的核心Java文件和配置文件完整路径
   - 修改类型和风险级别：新建、接口定义、实现类、配置类的风险评估

9. **09-nexus-config-map.json**
   - plugin_lifecycle_mapping：插件生命周期状态和转换规则
   - service_bus_configuration：服务总线的事件类型和处理器配置
   - spring_integration_mapping：Spring Boot自动配置的参数映射
   - commons_adaptation_rules：现有Commons库适配为插件的转换规则
   - performance_benchmarks：框架性能基准和监控指标定义

#### 中等项目扩展文档 (10-11, 40-41)
10. **10-nexus-architecture-overview.md**
    - 微内核架构整体设计：核心层、插件层、服务层的职责边界
    - 模块关系图表：依赖关系和通信模式的可视化表示
    - 技术选型决策：Java SPI、Spring Boot、Event Bus的选型理由
    - 扩展点设计：插件接口、服务总线、配置管理的扩展能力

11. **11-nexus-implementation-strategy.md**
    - 跨模块协调策略：并行开发nexus-kernel和nexus-service-bus的协调机制
    - 渐进式集成策略：从框架核心到插件适配的分层集成方法
    - 质量保证策略：代码审查、单元测试、集成测试的全流程质量控制
    - 里程碑管理：关键节点的交付物和验收标准

40. **40-nexus-module-integration-test.md**
    - 模块间集成测试：nexus-kernel与nexus-service-bus的协同工作验证
    - 插件加载测试：多插件同时加载和生命周期管理的集成验证
    - Spring Boot集成测试：nexus-starter在实际Spring Boot应用中的集成验证
    - 性能基准测试：插件化后的性能对比和优化策略

41. **41-nexus-end-to-end-test.md**
    - 完整流程测试：从框架启动到插件工作的端到端功能验证
    - 用户场景测试：模拟实际使用场景的完整业务流程测试
    - 兼容性测试：与现有Commons库和Core项目的兼容性验证
    - 稳定性测试：长时间运行和高并发情况下的框架稳定性验证

### 关键要求

#### A. 操作边界控制
- 每个阶段明确定义模块创建和文件修改范围
- 严格按照微内核架构的分层原则，禁止跨层直接依赖
- API契约层：仅定义接口、注解、基类，不包含任何实现
- 微内核层：仅依赖API层，实现插件管理和生命周期控制
- 服务总线层：仅依赖API层，提供插件间通信能力
- 自动配置层：依赖微内核和服务总线，提供Spring Boot集成
- 插件适配层：依赖API层和对应Commons库，实现具体插件

#### B. 验证锚点系统
- API契约层验证：接口定义完整，注解可正常使用，基类继承正常
- 微内核层验证：插件发现机制工作，生命周期管理正确，依赖解析准确
- 服务总线层验证：事件发布订阅功能正常，异步处理性能达标
- 自动配置层验证：Spring Boot应用正常启动，Nexus组件自动注册
- 插件适配层验证：DB和Cache功能作为插件正常工作，性能无下降
- 整体框架验证：多插件协同工作，框架稳定性和扩展性达标

#### C. 风险评估机制
- 架构复杂度风险：微内核模式的学习曲线和维护复杂度
- 性能影响风险：插件化可能带来的性能开销和内存消耗
- 兼容性风险：与现有Spring Boot项目的集成兼容性
- 生态风险：插件开发和维护的生态建设需求
- 每个风险提供技术选型、实施策略、监控手段的具体缓解措施

#### D. 代码修改模板
- Plugin接口模板：包含start()、stop()、getName()、getDependencies()等标准方法
- ExtensionPoint注解模板：支持服务接口标记和元数据定义
- ServiceBus事件模板：Event基类扩展和具体事件类定义
- 自动配置模板：@Configuration、@ConditionalOnClass等Spring注解的标准用法
- 插件实现模板：DatabasePlugin和CachePlugin的完整实现示例

#### E. JSON结构化映射
- dependency_map：nexus-api → nexus-kernel/nexus-service-bus → nexus-starter → nexus-plugins
- module_structure：每个模块的包结构、核心类、配置文件的完整定义
- integration_points：与Spring Boot、Maven、现有Commons库的集成点
- validation_checkpoints：每个阶段的编译验证、测试验证、集成验证要求

#### F. 执行序列控制
- 严格的奠基石模式：API契约层 → 微内核层 → 服务总线层 → 自动配置层 → 插件适配层
- 每阶段内部的执行顺序：pom.xml → 核心接口 → 实现类 → 测试验证
- 并行开发策略：微内核层和服务总线层可在API层完成后并行开发
- 集成验证节点：每两个阶段完成后进行集成测试验证

#### G. 回滚方案设计
- P0级别：框架无法启动、核心插件加载失败、严重性能下降超过50%
- P1级别：部分插件功能异常、Spring Boot集成问题、测试失败率超过20%
- P2级别：文档不完整、配置复杂度过高、开发体验不佳
- 分阶段回滚：可以回滚到任意已完成的阶段，保持已验证功能的可用性

### 输出标准

1. **精确性**: 所有Java代码、Maven配置、Spring配置必须可直接使用
2. **完整性**: 从框架设计到插件实现的全流程覆盖
3. **可验证性**: 每个组件和集成点都有明确的验证标准
4. **可扩展性**: 框架设计支持未来新插件的无缝接入
5. **AI友好**: 文档结构便于AI理解微内核架构和插件化模式

### 特殊指令

- 基于UID库切换XCE异常库案例的文档结构和细节密度
- 保持相同的精确度和可执行性
- 适应微内核架构和插件化系统的技术特点
- 确保生成的文档可直接用于AI执行Nexus框架的完整构建

请按照上述要求，生成完整的7个文档，确保Nexus万用插座框架能够被AI精确实施。
```

---

## 验证清单

使用此提示词前，请确认：

- [ ] 已准备好参考案例路径：`docs/features/F007-建立Commons库的治理机制-20250610/将UID库切换到XCE异常库/plans/`
- [ ] 理解Nexus项目的技术背景：微内核架构、插件化系统、Spring Boot集成
- [ ] 确认输出目录：`docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/plan/v2/`
- [ ] 准备验证生成的7个文档的完整性和可执行性

## 预期输出文件 (中等项目扩展序列号)

#### 标准核心文档 (01-09)
1. `01-nexus-impl-plan.md` - 奠基石模式实施计划
2. `02-nexus-execution-checklist.md` - 分阶段执行检查清单  
3. `03-nexus-code-templates.md` - 微内核和插件代码模板
4. `04-nexus-risk-assessment.md` - 架构风险评估与回滚方案
5. `05-nexus-integration-guide.md` - 现有项目集成指导
8. `08-nexus-dependency-map.json` - 模块依赖关系映射
9. `09-nexus-config-map.json` - 插件配置参数映射

#### 中等项目扩展文档 (10-11, 40-41)
10. `10-nexus-architecture-overview.md` - 微内核架构整体设计
11. `11-nexus-implementation-strategy.md` - 跨模块协调与集成策略
40. `40-nexus-module-integration-test.md` - 模块间集成测试方案
41. `41-nexus-end-to-end-test.md` - 端到端完整流程测试

**总计：11个文档** (标准7个 + 扩展4个)

---

## 序列号规则应用示例

### 如果项目更复杂 (>10个模块)
将自动使用完整扩展序列号系统：
- **B区 (20-29)**: 每个核心模块独立的计划和检查清单
- **C区 (30-39)**: 外围模块的专项文档
- **D区 (40-49)**: 全面的集成验证文档
- **配置区 (80-89)**: 分模块的依赖和配置映射

### 如果项目更简单 (低认知复杂度)
将使用基础序列号：
- 仅生成7个标准文档 (01-05, 08-09)
- AI执行策略：直接处理模式，可整体理解
- 重点关注边界控制、验证锚点、代码模板

复制上述提示词，即可生成完整的Nexus万用插座框架AI执行文档体系！ 