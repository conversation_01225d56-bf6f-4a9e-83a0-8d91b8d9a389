# V3.1生成器统一JSON结构定义标准

## 文档信息
- **文档ID**: T001-V3.1-UNIFIED-JSON-STRUCTURE-STANDARD
- **创建日期**: 2025-06-14
- **版本**: v3.1
- **目标**: 统一所有设计文档中的JSON结构定义，消除不一致性
- **权威性**: 本文档为V3.1项目中JSON结构的唯一权威定义

## 统一JSON结构定义

### 1. design-analysis-complete.json标准结构

#### 1.1 元数据结构 (metadata) - 权威定义
```json
{
  "metadata": {
    "document_id": "{{AI_FILL_REQUIRED}}",
    "version": "v1.0",
    "generated_at": "{{SCANNER_AUTO_FILL}}",
    "source_design_docs": ["{{SCANNER_AUTO_FILL}}"],
    "ai_validation_status": "{{AI_FILL_REQUIRED}}",
    "completeness_score": "{{AI_CALCULATE_REQUIRED}}",
    "fill_status": {
      "scanner_filled": ["generated_at", "source_design_docs"],
      "ai_required": ["document_id", "ai_validation_status", "completeness_score"]
    }
  }
}
```

#### 1.2 项目身份信息 (project_identity) - 权威定义
```json
{
  "project_identity": {
    "project_name": "{{AI_FILL_REQUIRED}}",
    "base_package": "{{AI_FILL_REQUIRED}}",
    "maven_artifact_id": "{{AI_FILL_REQUIRED}}",
    "java_version": "{{AI_FILL_REQUIRED}}",
    "spring_boot_version": "{{AI_FILL_REQUIRED}}"
  }
}
```

#### 1.3 架构规范 (architecture_specification) - 权威定义
```json
{
  "architecture_specification": {
    "pattern_type": "{{AI_FILL_REQUIRED}}",
    "microkernel_config": {
      "kernel_interfaces": ["{{AI_FILL_REQUIRED}}"],
      "plugin_system_enabled": "{{AI_FILL_REQUIRED}}"
    },
    "service_bus_config": {
      "async_event_processing": "{{AI_FILL_REQUIRED}}",
      "performance_targets": {
        "events_per_second": "{{AI_FILL_REQUIRED}}",
        "max_latency_ms": "{{AI_FILL_REQUIRED}}"
      }
    },
    "virtual_threads_config": {
      "enabled": "{{AI_FILL_REQUIRED}}",
      "jvm_parameters": "{{AI_FILL_REQUIRED}}"
    }
  }
}
```

#### 1.4 接口系统 (interface_system) - 权威定义
```json
{
  "interface_system": {
    "core_interfaces": [
      {
        "name": "{{AI_FILL_REQUIRED}}",
        "package": "{{AI_FILL_REQUIRED}}",
        "methods": "{{AI_FILL_REQUIRED}}",
        "method_signature_full": {
          "method_name": "{{AI_FILL_REQUIRED}}",
          "return_type": "{{AI_FILL_REQUIRED}}",
          "parameters": [
            {
              "name": "{{AI_FILL_REQUIRED}}",
              "type": "{{AI_FILL_REQUIRED}}"
            }
          ],
          "access_modifiers": "{{AI_FILL_REQUIRED}}",
          "throws_declarations": "{{AI_FILL_REQUIRED}}",
          "method_annotations": "{{AI_FILL_REQUIRED}}"
        }
      }
    ],
    "field_definitions": [
      {
        "field_name": "{{AI_FILL_REQUIRED}}",
        "field_type": "{{AI_FILL_REQUIRED}}",
        "access_modifier": "{{AI_FILL_REQUIRED}}",
        "annotations": "{{AI_FILL_REQUIRED}}"
      }
    ]
  }
}
```

### 2. 统一模块命名标准

#### 2.1 核心模块命名 - 权威定义
- **主生成器**: `V3JsonEnhancedGenerator`
- **AI负载计算器**: `JsonLoadCalculator`
- **代码占位符生成器**: `CodePlaceholderGenerator`
- **DRY引用引擎**: `DryReferenceEngine`
- **实施计划模板**: `ImplementationPlanTemplate`
- **质量门禁管理器**: `QualityGateManager`
- **AI约束模板**: `AIConstraintTemplates`

#### 2.2 数据模型命名 - 权威定义
- **AI负载指标**: `AILoadMetrics`
- **计划生成模型**: `PlanGenerationModel`
- **质量验证器**: `QualityValidator`
- **边界护栏管理器**: `BoundaryGuardManager`
- **上下文管理器**: `ContextManager`
- **错误恢复管理器**: `ErrorRecoveryManager`

### 3. 统一依赖关系定义

#### 3.1 模块依赖关系图 - 权威定义
```
V3JsonEnhancedGenerator (主控制器)
├── JsonLoadCalculator (AI负载计算器)
│   ├── AILoadMetrics (数据模型)
│   └── QualityGateManager (质量门禁)
├── CodePlaceholderGenerator (代码占位符生成器)
│   ├── AIConstraintTemplates (AI约束模板)
│   └── ImplementationPlanTemplate (实施计划模板)
├── DryReferenceEngine (DRY引用引擎)
│   └── PlanGenerationModel (计划生成模型)
└── QualityValidator (质量验证器)
    ├── BoundaryGuardManager (边界护栏管理器)
    ├── ContextManager (上下文管理器)
    └── ErrorRecoveryManager (错误恢复管理器)
```

#### 3.2 接口依赖关系 - 权威定义
- `JsonLoadCalculator` → `AILoadMetrics`
- `CodePlaceholderGenerator` → `AILoadMetrics`, `AIConstraintTemplates`
- `DryReferenceEngine` → `PlanGenerationModel`
- `ImplementationPlanTemplate` → `CodePlaceholderGenerator`, `DryReferenceEngine`
- `QualityValidator` → `QualityGateManager`, `BoundaryGuardManager`

### 4. 统一文件路径标准

#### 4.1 项目文件结构 - 权威定义
```
tools/doc/plans/v3.1/
├── v3_json_enhanced_generator.py          # 主生成器
├── analyzers/
│   ├── __init__.py
│   ├── json_load_calculator.py            # AI负载计算器
│   ├── code_placeholder_generator.py      # 代码占位符生成器
│   └── dry_reference_engine.py            # DRY引用引擎
├── templates/
│   ├── __init__.py
│   ├── implementation_plan_template.py    # 实施计划模板
│   └── ai_constraint_templates.py         # AI约束模板
├── models/
│   ├── __init__.py
│   ├── ai_load_model.py                   # AI负载模型
│   └── plan_generation_model.py          # 计划生成模型
├── validators/
│   ├── __init__.py
│   ├── quality_validator.py               # 质量验证器
│   ├── boundary_guard_manager.py          # 边界护栏管理器
│   ├── context_manager.py                 # 上下文管理器
│   └── error_recovery_manager.py          # 错误恢复管理器
└── tests/
    ├── __init__.py
    └── test_v3_json_enhanced.py          # 测试文件
```

## 一致性检查清单

### JSON结构一致性
- [ ] 所有文档使用统一的metadata结构
- [ ] 所有文档使用统一的project_identity结构
- [ ] 所有文档使用统一的architecture_specification结构
- [ ] 所有文档使用统一的interface_system结构

### 模块命名一致性
- [ ] 所有文档使用统一的核心模块命名
- [ ] 所有文档使用统一的数据模型命名
- [ ] 所有文档使用统一的接口命名

### 依赖关系一致性
- [ ] 所有文档使用统一的模块依赖关系图
- [ ] 所有文档使用统一的接口依赖关系
- [ ] 所有文档使用统一的文件路径结构

## 强制执行规则

### 1. 权威性原则
- 本文档为V3.1项目中JSON结构和模块命名的**唯一权威定义**
- 所有其他设计文档必须与本文档保持100%一致
- 任何修改必须先更新本文档，再同步到其他文档

### 2. 一致性验证
- 每次文档更新后必须进行一致性检查
- 发现不一致时必须立即修正
- 实施前必须通过完整的一致性验证

### 3. 变更管理
- JSON结构变更必须在本文档中先行定义
- 模块命名变更必须经过一致性影响评估
- 依赖关系变更必须更新完整的依赖关系图
