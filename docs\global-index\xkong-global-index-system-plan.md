---
title: XKong全局索引系统设计方案
document_id: XGIS-PLAN
document_type: 设计方案
category: 索引系统
scope: 全局
keywords: [索引系统, 全局索引, 架构查询, API查询, 数据库查询]
created_date: 2025-05-15
updated_date: 2025-05-15
status: 草稿
version: 1.0
authors: [AI助手]
---

# XKong全局索引系统设计方案

## 1. 概述

XKong全局索引系统（XKong Global Index System，简称XGIS）旨在提供一个高效、全面的索引机制，使AI能够快速查询xkongcloud工程的当前开发状态，包括架构、流程、API、运行参数以及数据库表和Schema等信息。该系统采用JSON格式组织索引数据，按照不同的领域和功能模块分类，并通过父子关系体现系统的层次结构。

### 1.1 设计目标

- 提供高效的查询机制，支持快速定位系统中的各种信息
- 体现系统中的父子关系，使索引结构更加清晰
- 支持大规模数据（如500+表）的索引和查询
- 为未来从单体结构扩展为微服务架构预留扩展空间
- 简化索引文件的维护，减少冗余信息
- 提供统一的索引规则和路径解析机制

### 1.2 适用范围

本方案适用于xkongcloud工程的全局索引系统的设计和实施，包括但不限于以下方面：

- 系统架构索引
- API索引（REST和gRPC）
- 数据库索引（Schema和表）
- 流程索引
- 配置索引
- 组件索引
- 文档索引

## 2. 目录结构设计

XKong全局索引系统采用以下目录结构：

```
docs/
├── global-index/
│   ├── README.md                    # 索引系统说明文档
│   ├── global-index.json            # 全局主索引（轻量级，主要包含引用）
│   ├── domains/                     # 按领域划分的索引
│   │   ├── architecture/            # 架构索引
│   │   │   └── index.json           # 架构主索引
│   │   ├── api/                     # API索引
│   │   │   ├── index.json           # API总索引
│   │   │   ├── rest/                # REST API索引（按项目分）
│   │   │   │   ├── XKC-CORE.json    # XKC-CORE项目的REST API
│   │   │   │   └── XKC-CENTER.json  # XKC-CENTER项目的REST API
│   │   │   └── grpc/                # gRPC API索引
│   │   │       └── index.json       # gRPC服务索引
│   │   ├── database/                # 数据库索引
│   │   │   ├── index.json           # 数据库总索引
│   │   │   └── schemas/             # Schema索引（包含表）
│   │   │       ├── core_business.json  # 核心业务Schema
│   │   │       ├── infra_uid.json      # UID基础设施Schema
│   │   │       └── common_audit.json   # 审计日志Schema
│   │   ├── workflow/                # 流程索引
│   │   │   └── index.json           # 流程主索引
│   │   ├── configuration/           # 配置索引
│   │   │   └── index.json           # 配置主索引
│   │   ├── component/               # 组件索引
│   │   │   └── index.json           # 组件主索引
│   │   └── documentation/           # 文档索引
│   │       └── index.json           # 文档主索引
│   ├── projects/                    # 按项目划分的索引
│   │   ├── XKC-CORE/                # XKC-CORE项目索引
│   │   │   └── index.json           # XKC-CORE项目主索引
│   │   ├── XKC-CENTER/              # XKC-CENTER项目索引
│   │   │   └── index.json           # XKC-CENTER项目主索引
│   │   └── XKC-COMMON/              # XKC-COMMON项目索引
│   │       └── index.json           # XKC-COMMON项目主索引
│   └── features/                    # 按功能划分的索引（新增）
│       ├── F003-PostgreSQL-migration.json  # F003功能全局索引
│       ├── F004-CommonsUidLibrary.json     # F004功能全局索引
│       └── index.json               # 功能索引总览
```

## 3. 索引规则设计

### 3.1 文件命名规则

```json
{
  "fileNaming": {
    "mainIndex": "index.json",       // 默认主索引文件名
    "projectIndex": "${projectCode}.json",  // 项目索引文件命名
    "schemaIndex": "${schemaName}.json",    // Schema索引文件命名
    "featureIndex": "${featureId}-${featureName}.json",  // 功能索引文件命名（新增）
    "featureKnowledgeBaseIndex": "knowledge-base-index.json"  // 功能知识库索引文件名（新增）
  }
}
```

### 3.2 路径解析规则

```json
{
  "pathResolution": {
    "defaultFile": "index.json",     // 当路径指向目录时，默认查找此文件
    "implicitIndex": true,           // 是否在路径中省略index.json
    "separator": "#",                // 路径与内部引用的分隔符
    "arrayNotation": "[]"            // 数组引用符号
  }
}
```

路径解析示例：
- `domains/database` → `domains/database/index.json`
- `domains/database/schemas/core_business` → `domains/database/schemas/core_business.json`
- `domains/database#tableNameIndex` → `domains/database/index.json`中的`tableNameIndex`属性
- `domains/api/grpc#services.KVService` → `domains/api/grpc/index.json`中的`services.KVService`属性
- `features/F003-PostgreSQL-migration` → `features/F003-PostgreSQL-migration.json`（新增）
- `features/F003-PostgreSQL-migration#知识库标准库映射` → `features/F003-PostgreSQL-migration.json`中的对应属性（新增）

### 3.3 Schema命名规则

```json
{
  "schemaNamePattern": {
    "business": "<业务领域>_<可选子域>",      // 如：core_business
    "infrastructure": "infra_<组件类型>",    // 如：infra_uid
    "common": "common_<功能类型>"            // 如：common_audit
  }
}
```

## 4. 索引文件结构设计

### 4.1 全局主索引

```json
{
  "metadata": {
    "indexId": "XGIS-MAIN",
    "title": "XKong全局索引系统",
    "version": "1.0",
    "createdDate": "2025-05-15",
    "updatedDate": "2025-05-15"
  },
  "overview": "XKong全局索引系统(XGIS)提供对xkongcloud工程当前开发状态的全面索引，采用分层结构组织信息，支持高效查询。",
  "rules": "rules",  // 指向rules/index.json
  "domains": {
    "architecture": {
      "name": "架构索引",
      "path": "domains/architecture",  // 指向domains/architecture/index.json
      "description": "系统架构、组件关系、设计模式等信息索引"
    },
    "api": {
      "name": "API索引",
      "path": "domains/api",  // 指向domains/api/index.json
      "description": "所有服务API、接口定义、参数说明等信息索引",
      "subDomains": {
        "rest": {"path": "domains/api/rest"},  // 指向domains/api/rest/index.json
        "grpc": {"path": "domains/api/grpc"}   // 指向domains/api/grpc/index.json
      }
    },
    "database": {
      "name": "数据库索引",
      "path": "domains/database",  // 指向domains/database/index.json
      "description": "数据库、Schema、表结构等信息索引"
    },
    "workflow": {
      "name": "流程索引",
      "path": "domains/workflow",  // 指向domains/workflow/index.json
      "description": "业务流程、开发工作流等信息索引"
    },
    "configuration": {
      "name": "配置索引",
      "path": "domains/configuration",  // 指向domains/configuration/index.json
      "description": "系统配置参数、环境配置等信息索引"
    },
    "component": {
      "name": "组件索引",
      "path": "domains/component",  // 指向domains/component/index.json
      "description": "系统组件、中间件、第三方库等信息索引"
    },
    "documentation": {
      "name": "文档索引",
      "path": "domains/documentation",  // 指向domains/documentation/index.json
      "description": "项目文档、技术文档等信息索引"
    }
  },
  "projects": {
    "name": "项目索引",
    "path": "projects",  // 指向projects/index.json
    "description": "按项目代码划分的索引，包含XKC-CORE、XKC-CENTER等项目"
  },
  "features": {
    "name": "功能索引",
    "path": "features",  // 指向features/index.json
    "description": "按功能划分的索引，提供功能导向的文档组织和知识库映射"
  }
}
```

### 4.2 数据库索引（体现父子关系）

```json
{
  "metadata": {
    "indexId": "XGIS-DB",
    "title": "XKong数据库索引",
    "version": "1.0",
    "createdDate": "2025-05-15",
    "updatedDate": "2025-05-15"
  },
  "overview": "XKong数据库索引提供对系统中所有数据库、Schema和表的索引，支持快速查询表结构、字段定义和关系。",
  "databases": [
    {
      "name": "xkong_main_db",
      "type": "PostgreSQL",
      "version": "17",
      "description": "主数据库，包含所有业务数据和基础设施数据",
      "schemas": [
        {
          "name": "core_business",
          "path": "domains/database/schemas/core_business",  // 指向core_business.json
          "description": "核心业务Schema",
          "tableCount": 2
        },
        // 其他Schema...
      ]
    }
  ],
  "schemaNameIndex": {
    "core_business": {
      "description": "核心业务Schema",
      "pattern": "<业务领域>_<可选子域>",
      "path": "domains/database/schemas/core_business"  // 指向core_business.json
    },
    // 其他Schema...
  },
  "tableNameIndex": {
    "users": {
      "schema": "core_business",
      "path": "domains/database/schemas/core_business#tables.users"
    },
    // 其他表...
  },
  // 其他内容...
}
```

### 4.3 Schema索引（包含表）

```json
{
  "metadata": {
    "indexId": "XGIS-DB-SCHEMA-CORE-BUSINESS",
    "title": "核心业务Schema索引",
    "version": "1.0",
    "createdDate": "2025-05-15",
    "updatedDate": "2025-05-15"
  },
  "overview": "核心业务Schema，包含核心业务相关的表",
  "name": "core_business",
  "description": "核心业务Schema，包含核心业务相关的表",
  "pattern": "<业务领域>_<可选子域>",
  "owner": "业务开发团队",
  "database": "xkong_main_db",
  "tables": {
    "users": {
      "name": "users",
      "description": "用户表，存储系统用户信息",
      "entityClass": "org.xkong.cloud.business.internal.core.entity.User",
      "primaryKey": "id",
      "fields": [
        {"name": "id", "type": "UUID", "description": "用户ID"},
        {"name": "username", "type": "VARCHAR(50)", "description": "用户名"},
        {"name": "email", "type": "VARCHAR(100)", "description": "电子邮件"}
      ],
      "indexes": [
        {"name": "idx_users_username", "fields": ["username"], "unique": true},
        {"name": "idx_users_email", "fields": ["email"], "unique": true}
      ],
      "relations": [
        {"table": "user_roles", "type": "one-to-many", "field": "user_id"}
      ]
    },
    // 其他表...
  },
  "tableCount": 2,
  "keywordIndex": {
    "用户": ["users", "user_roles"],
    "角色": ["user_roles"]
  },
  // 其他内容...
}
```

### 4.4 API索引（体现父子关系）

```json
{
  "metadata": {
    "indexId": "XGIS-API-GRPC",
    "title": "XKong gRPC API索引",
    "version": "1.0",
    "createdDate": "2025-05-15",
    "updatedDate": "2025-05-15"
  },
  "overview": "XKong gRPC API索引提供对系统中所有gRPC服务的索引。",
  "services": {
    "KVService": {
      "name": "KVService",
      "package": "org.xkong.cloud.proto.internal.kv",
      "project": "XKC-CENTER",
      "protoFile": "kv_service.proto",
      "description": "KV参数服务，提供配置参数的获取和动态更新功能",
      "methods": {
        "GetKVParam": {
          "name": "GetKVParam",
          "description": "根据Key获取Value",
          "requestType": "GetKVParamRequest",
          "responseType": "GetKVParamResponse",
          "requestFields": [
            {"name": "key", "type": "string", "description": "参数Key"}
          ],
          "responseFields": [
            {"name": "value", "type": "string", "description": "参数Value"}
          ]
        },
        // 其他方法...
      }
    }
  },
  // 其他内容...
}
```

### 4.5 配置索引（体现父子关系）

```json
{
  "metadata": {
    "indexId": "XGIS-CONF",
    "title": "XKong配置索引",
    "version": "1.0",
    "createdDate": "2025-05-15",
    "updatedDate": "2025-05-15"
  },
  "overview": "XKong配置索引提供对系统中所有配置参数的索引。",
  "configurations": {
    "application": {
      "name": "应用配置",
      "description": "应用级别的配置参数",
      "projects": {
        "XKC-CORE": {
          "file": "application.properties",
          "parameters": {
            "spring.application.name": {
              "value": "xkongcloud-business-internal-core",
              "description": "应用名称"
            },
            // 其他参数...
          }
        }
      }
    },
    // 其他配置类别...
  },
  // 其他内容...
}
```

### 4.6 功能索引（新增）

#### 4.6.1 功能全局索引

```json
{
  "metadata": {
    "index_id": "XGIS-FEATURE-${FEATURE_ID}",
    "title": "${功能名称}功能全局索引",
    "version": "1.0",
    "created_date": "2025-01-15",
    "updated_date": "2025-01-15",
    "index_type": "feature_global_index",
    "parent_system": "XKong全局索引系统(XGIS)"
  },
  "功能基本信息": {
    "feature_id": "${FEATURE_ID}",
    "feature_name": "${功能名称}",
    "project_code": "${项目代码}",
    "status": "active_development|stable_maintenance|completed_archived",
    "current_phase": "${当前阶段}",
    "description": "${功能描述}"
  },
  "功能文档索引": {
    "feature_documents": {
      "base_path": "docs/features/${FEATURE_ID}-${功能名称}-${日期}/",
      "structure": {
        "requirements": "requirements/",
        "design": "design/",
        "plan": "plan/",
        "api": "api/",
        "code": "code/",
        "test": "test/",
        "guide": "guide/",
        "log": "log/",
        "index": "index/"
      }
    },
    "knowledge_base_index": {
      "path": "docs/features/${FEATURE_ID}-${功能名称}-${日期}/index/knowledge-base-index.json",
      "description": "功能导向的知识库标准库索引"
    }
  },
  "知识库标准库映射": {
    // 映射到docs/common/相关文档
  },
  "AI记忆系统集成": {
    // 与AI记忆系统的集成配置
  }
}
```

#### 4.6.2 功能知识库索引

```json
{
  "metadata": {
    "feature_id": "${FEATURE_ID}",
    "feature_name": "${功能名称}",
    "index_type": "knowledge_base_index",
    "version": "1.0",
    "description": "从功能角度组织相关的docs/common标准库内容"
  },
  "功能概述": {
    "description": "${功能描述}",
    "phases": ["${阶段1}", "${阶段2}", "..."],
    "current_phase": "${当前阶段}"
  },
  "知识库标准库索引": {
    "${分类1}": {
      "category": "${分类名称}",
      "priority": "critical|high|medium|low",
      "description": "${分类描述}",
      "documents": [
        {
          "id": "${文档ID}",
          "path": "docs/common/${相对路径}",
          "title": "${文档标题}",
          "relevance": "critical|high|medium|low",
          "phase_mapping": ["${相关阶段}"],
          "key_topics": ["${关键主题}"],
          "usage_scenarios": ["${使用场景}"]
        }
      ]
    }
  },
  "功能阶段映射": {
    "${阶段名称}": {
      "description": "${阶段描述}",
      "critical_documents": ["${关键文档路径}"],
      "supporting_documents": ["${支撑文档路径}"]
    }
  },
  "使用指南": {
    "ai_access_pattern": "使用@L3:feature:${FEATURE_ID}命令激活此索引",
    "priority_levels": {
      "critical": "核心功能实现必需的文档",
      "high": "重要的支撑文档",
      "medium": "有用的参考文档",
      "low": "可选的扩展文档"
    }
  }
}
```

## 5. 大规模数据处理策略

### 5.1 大型Schema处理

对于包含大量表（如100+）的大型Schema，采用以下策略：

```json
{
  "tableGroups": [
    {
      "name": "用户管理",
      "description": "用户相关表",
      "path": "domains/database/schemas/large_business/user_management",
      "tableCount": 15
    },
    // 其他表组...
  ],
  "tableNameIndex": {
    "users": {"group": "用户管理", "path": "domains/database/schemas/large_business/user_management#tables.users"},
    // 其他表...
  }
}
```

### 5.2 微服务扩展支持

为支持未来从单体结构扩展为微服务架构，在全局索引中添加微服务映射：

```json
{
  "microservices": {
    "overview": "系统中的所有微服务定义",
    "services": [
      {
        "name": "core-service",
        "projects": ["XKC-CORE"],
        "description": "核心业务服务",
        "path": "domains/microservice/services/core-service",
        "databases": [
          {
            "name": "xkong_core_db",
            "schemas": ["core_business"]
          }
        ]
      },
      // 其他微服务...
    ],
    "discovery": {
      "path": "domains/microservice/discovery",
      "description": "服务发现索引"
    },
    "gateway": {
      "path": "domains/microservice/gateway",
      "description": "API网关索引"
    },
    "tracing": {
      "path": "domains/microservice/tracing",
      "description": "分布式追踪索引"
    }
  }
}
```


## 8. 开发视角文档系统理解机制（新增）

### 8.1 背景与问题

传统的文档索引系统按技术领域（domains）和项目代码（projects）组织，但缺少从"正在开发功能"视角理解整个文档系统的机制。这导致AI无法从开发者的实际工作视角智能地导航和理解文档关联性。

### 8.2 开发视角机制设计

#### 8.2.1 核心原理

**功能中心视角**：以正在开发的功能为中心，重新组织整个文档系统的理解和导航，而非从技术分类出发。

**开发阶段感知**：根据功能当前开发阶段动态调整文档重要性和访问优先级。

**智能关联分析**：自动识别功能需求与docs/common标准库文档的关联强度。

#### 8.2.2 实现机制

```json
{
  "active_development_context": {
    "auto_detection": "自动识别active_development状态的功能",
    "context_activation": "激活开发视角文档系统理解",
    "intelligent_mapping": "重新映射功能与标准库文档的关联",
    "phase_aware_navigation": "根据开发阶段提供智能导航"
  }
}
```

#### 8.2.3 集成点

- **AI记忆系统**：与L2-context层深度集成
- **注意力命令**：新增开发视角相关命令
- **功能索引**：增强现有功能索引的开发导向能力
- **RIPER-5协议**：在所有开发阶段提供开发视角支持

### 8.3 使用场景

**场景1**：用户询问F003功能问题时，AI自动从F003开发视角理解PostgreSQL相关文档的重要性。

**场景2**：用户需要了解最佳实践时，AI根据当前开发功能的阶段推荐最相关的实践指南。

## 9. 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 1.0 | 2025-05-15 | 初始版本 | AI助手 |
| 1.1 | 2025-01-15 | 新增功能索引体系，包含features/目录、功能全局索引和功能知识库索引规范 | AI助手 |
| 1.2 | 2025-01-15 | 新增开发视角文档系统理解机制，解决从正在开发功能视角理解整个文档系统的核心缺陷 | AI助手 |
