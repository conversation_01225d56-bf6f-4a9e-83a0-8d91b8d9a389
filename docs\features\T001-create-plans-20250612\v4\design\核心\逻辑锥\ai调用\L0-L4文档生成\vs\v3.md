# 新一代电商订单系统架构设计文档

## 1. 架构演进与设计方案

### 1.1 从单体到微服务的演进路径

**演进阶段规划：**
1. **解耦阶段**（1-2个月）：
   - 引入API网关分离前后端
   - 将核心模块(订单、支付、库存)代码分层隔离
   - 建立独立的数据访问层

2. **服务化阶段**（3-6个月）：
   - 按业务域拆分服务（订单服务、支付服务、库存服务等）
   - 引入服务注册与发现机制
   - 实现服务间基本通信

3. **优化阶段**（6-12个月）：
   - 完善服务治理（熔断、限流、降级）
   - 实施CI/CD流水线
   - 建立完善的监控体系

### 1.2 架构方案对比分析

#### 方案1：事件驱动架构

**优点：**
- 高松耦合，服务间通过事件通信
- 天然支持异步处理，适合高并发场景
- 易于扩展新功能（只需订阅事件）
- 良好的最终一致性支持

**缺点：**
- 事件溯源实现复杂
- 调试和问题追踪困难
- 需要完善的事件处理监控
- 学习曲线较陡

**适用场景：**
- 需要高扩展性的实时系统
- 业务逻辑复杂且变化频繁
- 需要与多个外部系统集成

#### 方案2：CQRS架构

**优点：**
- 读写分离，优化性能
- 可独立扩展读写服务
- 查询模型可针对不同场景优化
- 降低复杂业务逻辑的耦合度

**缺点：**
- 数据一致性维护复杂
- 需要处理命令和查询的同步延迟
- 系统复杂度增加
- 开发成本较高

**适用场景：**
- 读写负载差异大的系统
- 需要复杂查询报表的场景
- 需要高性能读操作的业务

#### 方案3：六边形架构

**优点：**
- 核心业务与技术实现完全解耦
- 易于替换技术组件
- 测试友好，可单元测试业务逻辑
- 长期维护成本低

**缺点：**
- 前期设计成本高
- 需要严格遵循架构规范
- 可能引入不必要的抽象层

**适用场景：**
- 需要长期演进的核心系统
- 技术栈可能变化的场景
- 对测试覆盖率要求高的项目

**推荐方案：** 采用事件驱动为主，CQRS为辅的混合架构，核心业务采用六边形架构设计

## 2. 技术选型决策

### 2.1 数据库选型

**方案对比：**

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|---------|
| 分库分表(ShardingSphere) | 成熟稳定，社区支持好<br>兼容现有MySQL技术栈 | 需要处理分布式事务<br>跨库查询复杂 | 已有MySQL投资<br>需要渐进式改造 |
| NewSQL(TiDB) | 自动分片，强一致性<br>水平扩展能力强 | 运维复杂度高<br>部分SQL功能受限 | 需要强一致性<br>海量数据场景 |
| 分布式数据库(Aurora) | 全托管服务<br>高性能读写 | 厂商锁定风险<br>成本较高 | 云原生部署<br>快速上线需求 |

**决策：** 核心订单数据采用TiDB，历史订单归档到S3，使用分库分表作为过渡方案

### 2.2 缓存设计

**多级缓存方案：**
1. **客户端缓存**：HTTP缓存控制，ETag机制
2. **CDN缓存**：静态资源和非敏感API响应
3. **应用缓存**：本地Caffeine缓存(高频访问数据)
4. **分布式缓存**：Redis集群(共享业务数据)
5. **数据库缓存**：MySQL查询缓存

**Redis优化措施：**
- 热点数据预加载
- 多级Key过期策略
- 内存淘汰策略优化
- 集群分片部署

### 2.3 消息队列选型

| 特性 | Kafka | RocketMQ | Pulsar |
|------|-------|----------|--------|
| 吞吐量 | 极高 | 高 | 高 |
| 延迟 | 中等 | 低 | 极低 |
| 功能丰富度 | 中等 | 高 | 极高 |
| 云原生支持 | 中等 | 弱 | 强 |
| 运维复杂度 | 高 | 中等 | 低 |

**决策：** 核心订单业务使用RocketMQ(阿里云支持好)，实时推荐使用Pulsar

### 2.4 服务治理选型

| 特性 | Spring Cloud | Istio | Dapr |
|------|-------------|-------|------|
| 语言支持 | Java为主 | 多语言 | 多语言 |
| 学习曲线 | 平缓 | 陡峭 | 中等 |
| 功能完整性 | 需集成组件 | 完整 | 完整 |
| 云原生支持 | 中等 | 强 | 极强 |
| 性能开销 | 低 | 高 | 中等 |

**决策：** 采用Spring Cloud Alibaba作为过渡，逐步迁移到Istio+Dapr混合架构

## 3. 非功能性需求设计

### 3.1 性能设计

**千万级订单处理方案：**
1. **流量削峰**：
   - 订单预生成+异步确认
   - 排队系统设计
   - 支付结果回调

2. **性能优化**：
   ```java
   // 订单创建伪代码
   @DistributedLock(key = "order:create:#{userId}")
   @Idempotent(key = "order:req:#{requestId}")
   public Order createOrder(OrderRequest request) {
       // 1. 预检查(库存、风控等)
       preCheck(request);
       
       // 2. 生成订单号(分片键)
       String orderId = snowflake.nextId();
       
       // 3. 异步持久化
       mqProducer.send(new OrderEvent(orderId, "CREATED"));
       
       // 4. 返回订单摘要
       return buildOrderSummary(orderId);
   }
   ```

3. **关键指标保障**：
   - 订单创建：<200ms(P99<1s)
   - 订单查询：<50ms(P99<300ms)
   - 系统吞吐：>15,000 TPS

### 3.2 高可用设计

**99.99%可用性方案：**
1. **多活部署**：
   - 同城双活+异地灾备
   - 单元化路由策略

2. **容灾设计**：
   - 服务降级方案
   - 限流熔断策略
   - 自动故障转移

3. **混沌工程**：
   - 定期故障演练
   - 自动恢复测试

### 3.3 一致性设计

**分布式事务策略：**
1. **强一致性场景**：
   - TCC模式(库存扣减)
   - 代码示例：
   ```java
   @TccAction(confirmMethod = "confirm", cancelMethod = "cancel")
   public boolean reserveInventory(Order order) {
       // Try阶段预留资源
   }
   ```

2. **最终一致性场景**：
   - 可靠事件模式+事务日志
   - 最大努力通知

3. **补偿机制**：
   - 对账系统设计
   - 异常订单处理流程

### 3.4 安全设计

**安全防护体系：**
1. **数据安全**：
   - 字段级加密(支付信息)
   - 动态脱敏(查询接口)
   - 数据水印

2. **访问控制**：
   - RBAC+ABAC混合模型
   - 服务网格mTLS
   - 细粒度权限控制

3. **审计追踪**：
   - 全链路操作日志
   - 敏感操作二次验证

## 4. 架构图示

### 4.1 逻辑架构图
```
[用户端] -> [CDN]
         -> [API Gateway] -> [Auth Service]
                          -> [Order Service] <-> [Redis]
                                               <-> [MQ]
                                               <-> [TiDB]
                          -> [Payment Service]
                          -> [Inventory Service]
```

### 4.2 部署架构图
```
[Region A]
  ├─ [AZ1] 订单服务集群
  ├─ [AZ2] 订单服务集群
  └─ [AZ3] 数据库主节点

[Region B]
  ├─ [AZ1] 订单服务备用集群
  └─ [AZ2] 数据库从节点
```

## 5. 实施路线图

1. **准备阶段**（1-2个月）：
   - 搭建基础平台
   - 技术预研验证
   - 团队培训

2. **试点阶段**（3-4个月）：
   - 拆分订单查询服务
   - 实现核心链路
   - 性能压测优化

3. **推广阶段**（5-8个月）：
   - 全量服务拆分
   - 数据迁移
   - 监控体系完善

4. **优化阶段**（9-12个月）：
   - 多活部署
   - 混沌工程
   - 持续调优

---

# 订单系统API设计文档

## 1. 订单创建API

### POST /api/v1/orders

**请求参数：**
```json
{
  "requestId": "uuidv4",
  "userId": 123456,
  "items": [
    {
      "skuId": "P1001",
      "quantity": 2,
      "price": 99.99
    }
  ],
  "shippingAddress": {
    "recipient": "张三",
    "phone": "13800138000",
    "address": "北京市海淀区"
  },
  "paymentMethod": "ALIPAY",
  "source": "APP"
}
```

**成功响应：**
```json
{
  "code": 200,
  "data": {
    "orderId": "O20231101123456",
    "status": "CREATED",
    "createdAt": "2023-11-01T12:34:56Z",
    "totalAmount": 199.98,
    "payableAmount": 199.98,
    "expireTime": "2023-11-01T13:34:56Z"
  }
}
```

**错误响应：**
```json
{
  "code": 429,
  "message": "请求过于频繁",
  "detail": {
    "retryAfter": 30,
    "limit": 100,
    "window": "1m"
  }
}
```

**设计要点：**
1. 幂等性：基于requestId实现
2. 限流：用户维度+全局维度
3. 安全：敏感字段加密传输

## 2. 订单查询API

### GET /api/v1/orders/{orderId}

**参数说明：**
- `fields`: 可选，指定返回字段(如`fields=id,status,amount`)

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "orderId": "O20231101123456",
    "status": "PAID",
    "items": [
      {
        "skuId": "P1001",
        "name": "智能手机",
        "quantity": 2,
        "price": 99.99
      }
    ],
    "paymentInfo": {
      "method": "ALIPAY",
      "transactionId": "T20231101123500",
      "amount": 199.98
    },
    "shippingInfo": {
      "address": "北京市海淀区****",
      "logisticsStatus": "PREPARING"
    }
  }
}
```

**缓存策略：**
- HTTP缓存：Cache-Control: max-age=60
- CDN缓存：根据用户地域缓存
- Redis缓存：多级Key设计

## 3. 订单状态更新API

### PUT /api/v1/orders/{orderId}/status

**请求示例：**
```json
{
  "action": "FULFILL",
  "operator": "system",
  "reason": "inventory_ready",
  "extInfo": {
    "warehouse": "BJ-01"
  }
}
```

**状态机设计：**
```
CREATED -> PAID -> FULFILLED -> SHIPPED -> COMPLETED
           \-> CANCELLED
```

## 4. 订单列表API

### GET /api/v1/orders

**查询参数：**
- `status`: 订单状态过滤
- `startTime/endTime`: 时间范围
- `sort`: 排序字段(-表示DESC)
- `page`: 页码
- `size`: 每页大小

**分页响应：**
```json
{
  "code": 200,
  "data": {
    "items": [...],
    "pagination": {
      "total": 1024,
      "page": 1,
      "size": 20,
      "hasMore": true
    }
  }
}
```

**性能优化：**
- 读写分离
- 分页优化：`WHERE id > ? LIMIT ?`
- 索引优化：组合索引(status, create_time)

---

# 性能优化方案

## 1. 性能问题分析

### 1.1 订单创建接口分析
**瓶颈点：**
1. 同步库存检查(RPC调用)
2. 订单表主键竞争
3. 支付预创建阻塞

**优化方向：**
- 异步库存预留
- 分布式ID生成优化
- 支付流程解耦

### 1.2 数据库连接池问题
**根本原因：**
- 事务时间过长
- 连接泄漏
- 不合理的事务隔离级别

## 2. 优化措施

### 2.1 代码层优化
```java
// 优化前
@Transactional
public Order createOrder() {
    // 1. 检查库存(同步)
    // 2. 创建订单
    // 3. 创建支付
}

// 优化后
public Order createOrder() {
    // 1. 异步库存预留(MQ)
    // 2. 快速生成订单(无事务)
    // 3. 异步支付创建
}
```

### 2.2 数据库优化
| 优化项 | 当前值 | 目标值 | 措施 |
|-------|-------|-------|------|
| 连接池使用率 | 95% | <70% | 调整连接数+引入HikariCP |
| 查询响应时间 | 120ms | <50ms | 增加从库+优化索引 |
| 锁等待时间 | 300ms | <100ms | 拆解大事务 |

### 2.3 Redis优化
1. **热点数据**：本地缓存+Redis多副本
2. **大Key问题**：拆分为多个Hash存储
3. **缓存穿透**：布隆过滤器+空值缓存

## 3. 监控体系

**关键指标：**
- 应用层：QPS、响应时间、错误率
- 中间件：Redis命中率、MQ堆积
- 数据库：慢查询、锁等待

**告警策略：**
- 分级告警(P1-P4)
- 智能基线告警
- 关联分析告警

---

# 安全威胁分析

## 1. 威胁识别

### 1.1 主要威胁
1. **注入攻击**：SQL/NoSQL注入
2. **业务逻辑漏洞**：订单金额篡改
3. **数据泄露**：敏感信息暴露
4. **DDoS攻击**：API洪水攻击

## 2. 防护方案

### 2.1 防御措施
1. **输入验证**：
   ```java
   @Validated
   public Order createOrder(@Valid OrderRequest request) {
       // 自动参数校验
   }
   ```

2. **安全加固**：
   - API网关层WAF
   - 服务网格mTLS
   - 细粒度RBAC

### 2.2 数据安全
1. **加密方案**：
   - 传输层：TLS 1.3
   - 存储层：AES-256字段加密
   - 密钥管理：HSM硬件模块

## 3. 合规性设计

**GDPR合规措施：**
1. 数据主体权利实现
2. DPO(数据保护官)任命
3. 隐私影响评估

---

# 系统重构计划

## 1. 重构策略

### 1.1 服务拆分
1. **垂直拆分**：
   - 订单核心服务
   - 订单查询服务
   - 订单作业服务

2. **数据迁移**：
   - 双写过渡方案
   - 数据一致性校验
   - 灰度迁移策略

## 2. 风险控制

**主要风险：**
1. 数据不一致
2. 性能下降
3. 服务不可用

**应对措施：**
- 完善回滚方案
- 流量逐步切换
- 7*24小时值班

## 3. 实施计划

**阶段里程碑：**
1. **M1-2**：搭建基础平台
2. **M3-4**：试点服务上线
3. **M5-6**：全量迁移
4. **M7-8**：优化调优

**团队分工：**
- 架构组：技术方案设计
- 核心组：关键服务开发
- QA组：自动化测试覆盖
- SRE组：稳定性保障 