#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人类架构师思维模拟算法：AI代码质量检查引擎
Architectural Thinking Engine for AI Code Quality Assessment

版本: V1.0-Architectural-Thinking-Engine
创建日期: 2025-01-13
作者: AI Architecture Team
描述: 模拟世界级架构师的思维过程，从架构视角检查AI生成代码的质量
"""

import ast
import re
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class QualityDimension(Enum):
    """质量评估维度枚举"""
    MAINTAINABILITY = "maintainability"    # 可维护性
    EXTENSIBILITY = "extensibility"        # 可扩展性
    CONSISTENCY = "consistency"            # 一致性
    ELEGANCE = "elegance"                  # 优雅性
    ROBUSTNESS = "robustness"              # 健壮性


class ArchitecturalRole(Enum):
    """架构角色枚举"""
    CONTROLLER = "controller"              # 控制器
    SERVICE = "service"                    # 服务层
    REPOSITORY = "repository"              # 数据访问层
    MODEL = "model"                        # 模型层
    UTILITY = "utility"                    # 工具类
    CONFIGURATION = "configuration"        # 配置类


@dataclass
class CodeEssence:
    """代码本质信息"""
    primary_purpose: str                   # 主要目的
    architectural_role: ArchitecturalRole  # 架构角色
    complexity_level: int                  # 复杂度级别 (1-10)
    design_patterns: List[str]             # 设计模式
    quality_indicators: Dict[str, Any]     # 质量指标


@dataclass
class QualityAssessment:
    """质量评估结果"""
    maintainability_score: float          # 可维护性评分
    extensibility_score: float            # 可扩展性评分
    consistency_score: float              # 一致性评分
    elegance_score: float                 # 优雅性评分
    robustness_score: float               # 健壮性评分
    overall_score: float                  # 综合评分
    confidence: float                     # 置信度
    quality_issues: List[Dict[str, Any]]  # 质量问题
    strength_points: List[Dict[str, Any]] # 优势点


@dataclass
class RiskPoint:
    """代码风险点"""
    risk_type: str                    # 风险类型
    risk_category: str                # 风险分类
    location: str                     # 代码位置
    description: str                  # 风险描述
    severity: str                     # 严重程度 (high/medium/low)
    check_points: List[str]           # 需要检查的要点
    code_snippet: str                 # 相关代码片段
    confidence: float                 # 识别置信度


class CodeRiskDetector:
    """代码风险点检测器"""

    def __init__(self):
        """初始化风险检测器"""
        self.risk_patterns = self._initialize_risk_patterns()
        logger.info("代码风险检测器初始化完成")

    def _initialize_risk_patterns(self) -> Dict[str, Dict]:
        """初始化风险模式库"""
        return {
            # 并发安全风险
            "concurrent_access_risk": {
                "patterns": [
                    r"\.send\s*\(",  # 消息发送
                    r"\.put\s*\(",   # 数据写入
                    r"\.add\s*\(",   # 集合操作
                    r"\.remove\s*\(", # 删除操作
                ],
                "context_patterns": [
                    r"parallelStream\s*\(\)",
                    r"CompletableFuture",
                    r"ExecutorService",
                    r"@Async",
                    r"virtualThreadExecutor"
                ],
                "category": "concurrency",
                "severity": "high",
                "description": "共享对象的并发访问风险"
            },

            # 锁管理风险
            "lock_management_risk": {
                "patterns": [
                    r"\.lock\s*\(",
                    r"\.tryLock\s*\(",
                    r"\.unlock\s*\(",
                    r"synchronized\s*\(",
                ],
                "context_patterns": [
                    r"try\s*\{",
                    r"finally\s*\{",
                    r"catch\s*\(",
                    r"throw\s+",
                ],
                "category": "resource_management",
                "severity": "high",
                "description": "锁获取和释放管理风险"
            },

            # 资源管理风险
            "resource_management_risk": {
                "patterns": [
                    r"new\s+.*Connection",
                    r"new\s+.*Stream",
                    r"new\s+.*Reader",
                    r"new\s+.*Writer",
                    r"\.getConnection\s*\(",
                ],
                "context_patterns": [
                    r"try\s*\(",  # try-with-resources
                    r"finally\s*\{",
                    r"\.close\s*\(",
                    r"AutoCloseable",
                ],
                "category": "resource_management",
                "severity": "medium",
                "description": "资源获取和释放管理风险"
            },

            # API使用风险
            "api_usage_risk": {
                "patterns": [
                    r"\.tryLock\s*\([^)]*\)",  # tryLock参数
                    r"\.setTimeout\s*\(",      # 超时设置
                    r"\.setMaxConnections\s*\(", # 连接池配置
                    r"\.setRetryPolicy\s*\(",  # 重试策略
                ],
                "context_patterns": [
                    r"import\s+.*kafka",
                    r"import\s+.*redisson",
                    r"import\s+.*spring",
                    r"@Configuration",
                ],
                "category": "api_usage",
                "severity": "medium",
                "description": "第三方API使用配置风险"
            },

            # 异常处理风险
            "exception_handling_risk": {
                "patterns": [
                    r"catch\s*\(\s*Exception",
                    r"catch\s*\(\s*Throwable",
                    r"catch\s*\([^)]*\)\s*\{\s*\}",  # 空catch块
                    r"throw\s+new\s+RuntimeException",
                ],
                "context_patterns": [
                    r"try\s*\{",
                    r"finally\s*\{",
                    r"log\.",
                    r"logger\.",
                ],
                "category": "error_handling",
                "severity": "medium",
                "description": "异常处理不当风险"
            },

            # 性能风险
            "performance_risk": {
                "patterns": [
                    r"for\s*\([^)]*:\s*.*\)\s*\{[^}]*\..*\(",  # 循环中的方法调用
                    r"while\s*\([^)]*\)\s*\{[^}]*new\s+",     # 循环中的对象创建
                    r"Stream\s*\.[^.]*\.collect\s*\(",        # Stream操作
                    r"parallelStream\s*\(\)",                 # 并行流
                ],
                "context_patterns": [
                    r"@Transactional",
                    r"@Service",
                    r"@Controller",
                ],
                "category": "performance",
                "severity": "low",
                "description": "潜在性能问题风险"
            }
        }

    def detect_risk_points(self, code_content: str, context: Optional[Dict] = None) -> List[RiskPoint]:
        """检测代码中的风险点"""

        risk_points = []
        lines = code_content.split('\n')

        for risk_type, risk_config in self.risk_patterns.items():
            detected_risks = self._detect_specific_risk(
                code_content, lines, risk_type, risk_config, context
            )
            risk_points.extend(detected_risks)

        # 按严重程度和置信度排序
        risk_points.sort(key=lambda x: (
            {"high": 3, "medium": 2, "low": 1}[x.severity],
            x.confidence
        ), reverse=True)

        logger.info(f"检测到 {len(risk_points)} 个风险点")
        return risk_points

    def _detect_specific_risk(self, code_content: str, lines: List[str],
                            risk_type: str, risk_config: Dict,
                            context: Optional[Dict]) -> List[RiskPoint]:
        """检测特定类型的风险"""

        risk_points = []

        # 检查主要模式
        for pattern in risk_config["patterns"]:
            matches = list(re.finditer(pattern, code_content, re.MULTILINE))

            for match in matches:
                # 计算行号
                line_num = code_content[:match.start()].count('\n') + 1

                # 检查上下文模式
                context_confidence = self._check_context_patterns(
                    code_content, match, risk_config.get("context_patterns", [])
                )

                if context_confidence > 0.3:  # 置信度阈值
                    # 提取代码片段
                    code_snippet = self._extract_code_snippet(lines, line_num)

                    # 生成检查要点
                    check_points = self._generate_check_points(risk_type, match.group(), code_snippet)

                    risk_point = RiskPoint(
                        risk_type=risk_type,
                        risk_category=risk_config["category"],
                        location=f"Line {line_num}",
                        description=risk_config["description"],
                        severity=risk_config["severity"],
                        check_points=check_points,
                        code_snippet=code_snippet,
                        confidence=context_confidence
                    )

                    risk_points.append(risk_point)

        return risk_points

    def _check_context_patterns(self, code_content: str, match, context_patterns: List[str]) -> float:
        """检查上下文模式匹配度"""

        if not context_patterns:
            return 0.5  # 基础置信度

        # 获取匹配位置前后的上下文
        start = max(0, match.start() - 500)
        end = min(len(code_content), match.end() + 500)
        context_text = code_content[start:end]

        matched_patterns = 0
        for pattern in context_patterns:
            if re.search(pattern, context_text, re.MULTILINE):
                matched_patterns += 1

        # 计算置信度
        confidence = 0.3 + (matched_patterns / len(context_patterns)) * 0.7
        return min(1.0, confidence)

    def _extract_code_snippet(self, lines: List[str], line_num: int, context_lines: int = 3) -> str:
        """提取代码片段"""

        start_line = max(0, line_num - context_lines - 1)
        end_line = min(len(lines), line_num + context_lines)

        snippet_lines = []
        for i in range(start_line, end_line):
            prefix = ">>> " if i == line_num - 1 else "    "
            snippet_lines.append(f"{prefix}{i+1:3d}: {lines[i]}")

        return '\n'.join(snippet_lines)

    def _generate_check_points(self, risk_type: str, matched_text: str, code_snippet: str) -> List[str]:
        """生成检查要点"""

        check_points_map = {
            "concurrent_access_risk": [
                "检查对象是否为线程安全的",
                "确认是否需要同步机制",
                "验证并发访问的正确性",
                "检查是否存在竞态条件"
            ],
            "lock_management_risk": [
                "确认锁获取后是否正确释放",
                "检查异常情况下的锁释放",
                "验证锁超时参数的合理性",
                "确认锁的粒度是否合适"
            ],
            "resource_management_risk": [
                "确认资源是否正确关闭",
                "检查异常情况下的资源释放",
                "验证是否使用try-with-resources",
                "确认资源池配置的合理性"
            ],
            "api_usage_risk": [
                "查阅官方API文档确认用法",
                "检查参数配置的正确性",
                "验证版本兼容性",
                "确认最佳实践的遵循"
            ],
            "exception_handling_risk": [
                "检查异常处理的完整性",
                "确认异常信息的记录",
                "验证异常恢复机制",
                "检查是否遵循异常处理最佳实践"
            ],
            "performance_risk": [
                "评估性能影响",
                "检查是否存在优化空间",
                "验证算法复杂度",
                "确认资源使用效率"
            ]
        }

        return check_points_map.get(risk_type, ["需要进一步分析此风险点"])


class ArchitecturalThinkingEngine:
    """人类架构师思维模拟引擎"""

    def __init__(self):
        """初始化架构师思维引擎"""
        self.quality_weights = {
            QualityDimension.MAINTAINABILITY: 0.30,
            QualityDimension.EXTENSIBILITY: 0.25,
            QualityDimension.CONSISTENCY: 0.20,
            QualityDimension.ELEGANCE: 0.15,
            QualityDimension.ROBUSTNESS: 0.10
        }

        self.design_patterns = [
            "Singleton", "Factory", "Observer", "Strategy", "Command",
            "Decorator", "Adapter", "Facade", "Template Method", "Builder"
        ]

        # 初始化风险检测器
        self.risk_detector = CodeRiskDetector()

        logger.info("架构师思维引擎初始化完成")
    
    def analyze_code_quality(self, code_content: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        架构师级代码质量分析（增强版：包含风险点检测）

        Args:
            code_content: 代码内容
            context: 上下文信息

        Returns:
            完整的质量分析结果（包含风险点）
        """
        logger.info("开始架构师级代码质量分析（增强版）")

        if context is None:
            context = {}

        try:
            # 阶段1：检测代码风险点（新增：风险识别优先）
            risk_points = self.risk_detector.detect_risk_points(code_content, context)
            logger.info(f"风险点检测完成：发现 {len(risk_points)} 个风险点")

            # 阶段2：理解代码本质（架构师的第一直觉）
            code_essence = self._understand_code_essence(code_content, context)
            logger.info(f"代码本质分析完成：{code_essence.primary_purpose}")

            # 阶段3：构建代码关系图（架构师的系统思维）
            relationship_graph = self._build_code_relationship_graph(code_essence)
            logger.info("代码关系图构建完成")

            # 阶段4：推导设计意图（架构师的设计洞察）
            design_intent = self._derive_design_intent(relationship_graph)
            logger.info("设计意图推导完成")

            # 阶段5：评估架构质量（架构师的质量标准，结合风险点）
            quality_assessment = self._assess_architectural_quality(
                code_essence, relationship_graph, design_intent, risk_points
            )
            logger.info(f"质量评估完成，综合评分：{quality_assessment.overall_score:.2f}")

            # 阶段6：生成改进建议（架构师的指导意见，包含风险缓解）
            improvement_suggestions = self._generate_architectural_suggestions(
                quality_assessment, risk_points
            )
            logger.info("改进建议生成完成")

            # 阶段7：生成风险评估报告（新增）
            risk_assessment_report = self._generate_risk_assessment_report(risk_points)
            logger.info("风险评估报告生成完成")

            return {
                "code_essence": code_essence.__dict__,
                "relationship_graph": relationship_graph,
                "design_intent": design_intent,
                "quality_assessment": quality_assessment.__dict__,
                "improvement_suggestions": improvement_suggestions,
                "risk_points": [rp.__dict__ for rp in risk_points],  # 新增
                "risk_assessment_report": risk_assessment_report,     # 新增
                "overall_quality_score": quality_assessment.overall_score,
                "architectural_confidence": quality_assessment.confidence,
                "risk_coverage": len(risk_points)  # 新增：风险覆盖度
            }

        except Exception as e:
            logger.error(f"代码质量分析失败：{str(e)}")
            return {
                "error": str(e),
                "overall_quality_score": 0.0,
                "architectural_confidence": 0.0,
                "risk_coverage": 0
            }

    def _generate_risk_assessment_report(self, risk_points: List[RiskPoint]) -> Dict[str, Any]:
        """生成风险评估报告"""

        if not risk_points:
            return {
                "summary": "未发现明显的代码风险点",
                "total_risks": 0,
                "risk_distribution": {},
                "priority_risks": [],
                "recommendations": ["代码结构良好，建议继续保持"]
            }

        # 按严重程度分类
        risk_distribution = {"high": 0, "medium": 0, "low": 0}
        for rp in risk_points:
            risk_distribution[rp.severity] += 1

        # 获取高优先级风险
        priority_risks = [rp for rp in risk_points if rp.severity == "high"][:5]

        # 生成总结
        total_risks = len(risk_points)
        high_risks = risk_distribution["high"]

        if high_risks > 0:
            summary = f"发现 {total_risks} 个风险点，其中 {high_risks} 个高风险需要立即关注"
        elif risk_distribution["medium"] > 0:
            summary = f"发现 {total_risks} 个风险点，主要为中等风险，建议优化"
        else:
            summary = f"发现 {total_risks} 个低风险点，整体代码质量良好"

        # 生成建议
        recommendations = []
        if high_risks > 0:
            recommendations.append("立即处理高风险项，特别关注并发安全和资源管理")
        if risk_distribution["medium"] > 3:
            recommendations.append("系统性优化中等风险项，提升代码健壮性")
        if total_risks > 10:
            recommendations.append("考虑重构以降低整体复杂度和风险密度")

        recommendations.append("建议结合单元测试验证风险点的实际影响")

        return {
            "summary": summary,
            "total_risks": total_risks,
            "risk_distribution": risk_distribution,
            "priority_risks": [rp.__dict__ for rp in priority_risks],
            "recommendations": recommendations
        }
    
    def _understand_code_essence(self, code_content: str, context: Dict) -> CodeEssence:
        """理解代码本质（模拟架构师的直觉）"""
        
        # 架构师思维：快速识别代码的主要目的
        primary_purpose = self._identify_primary_purpose(code_content)
        
        # 架构师思维：判断代码在架构中的角色
        architectural_role = self._determine_architectural_role(code_content, context)
        
        # 架构师思维：评估复杂度级别
        complexity_level = self._assess_complexity_level(code_content)
        
        # 架构师思维：识别使用的设计模式
        design_patterns = self._identify_design_patterns(code_content)
        
        # 架构师思维：提取质量指标
        quality_indicators = self._extract_quality_indicators(code_content)
        
        return CodeEssence(
            primary_purpose=primary_purpose,
            architectural_role=architectural_role,
            complexity_level=complexity_level,
            design_patterns=design_patterns,
            quality_indicators=quality_indicators
        )
    
    def _identify_primary_purpose(self, code_content: str) -> str:
        """识别代码的主要目的"""
        
        # 分析类名和方法名
        if "class " in code_content:
            class_match = re.search(r'class\s+(\w+)', code_content)
            if class_match:
                class_name = class_match.group(1)
                
                # 基于命名模式推断目的
                if "Controller" in class_name:
                    return "处理HTTP请求和响应"
                elif "Service" in class_name:
                    return "实现业务逻辑"
                elif "Repository" in class_name:
                    return "数据访问和持久化"
                elif "Model" in class_name:
                    return "数据模型定义"
                elif "Util" in class_name or "Helper" in class_name:
                    return "提供工具方法"
                elif "Config" in class_name:
                    return "系统配置管理"
        
        # 分析函数定义
        function_matches = re.findall(r'def\s+(\w+)', code_content)
        if function_matches:
            if any("process" in func for func in function_matches):
                return "数据处理和转换"
            elif any("validate" in func for func in function_matches):
                return "数据验证和校验"
            elif any("calculate" in func for func in function_matches):
                return "计算和算法实现"
        
        return "通用功能实现"
    
    def _determine_architectural_role(self, code_content: str, context: Dict) -> ArchitecturalRole:
        """判断代码在架构中的角色"""
        
        # 基于文件路径判断
        file_path = context.get("file_path", "")
        if "controller" in file_path.lower():
            return ArchitecturalRole.CONTROLLER
        elif "service" in file_path.lower():
            return ArchitecturalRole.SERVICE
        elif "repository" in file_path.lower() or "dao" in file_path.lower():
            return ArchitecturalRole.REPOSITORY
        elif "model" in file_path.lower() or "entity" in file_path.lower():
            return ArchitecturalRole.MODEL
        elif "util" in file_path.lower() or "helper" in file_path.lower():
            return ArchitecturalRole.UTILITY
        elif "config" in file_path.lower():
            return ArchitecturalRole.CONFIGURATION
        
        # 基于代码内容判断
        if "@RestController" in code_content or "@Controller" in code_content:
            return ArchitecturalRole.CONTROLLER
        elif "@Service" in code_content:
            return ArchitecturalRole.SERVICE
        elif "@Repository" in code_content:
            return ArchitecturalRole.REPOSITORY
        elif "@Entity" in code_content or "@Table" in code_content:
            return ArchitecturalRole.MODEL
        elif "@Configuration" in code_content:
            return ArchitecturalRole.CONFIGURATION
        
        return ArchitecturalRole.UTILITY
    
    def _assess_complexity_level(self, code_content: str) -> int:
        """评估复杂度级别 (1-10)"""
        
        complexity_score = 1
        
        # 基于代码行数
        lines = code_content.split('\n')
        line_count = len([line for line in lines if line.strip()])
        if line_count > 100:
            complexity_score += 2
        elif line_count > 50:
            complexity_score += 1
        
        # 基于方法数量
        method_count = len(re.findall(r'def\s+\w+', code_content))
        if method_count > 10:
            complexity_score += 2
        elif method_count > 5:
            complexity_score += 1
        
        # 基于嵌套层级
        max_indent = 0
        for line in lines:
            if line.strip():
                indent = len(line) - len(line.lstrip())
                max_indent = max(max_indent, indent // 4)
        
        if max_indent > 4:
            complexity_score += 2
        elif max_indent > 2:
            complexity_score += 1
        
        # 基于循环和条件语句
        control_structures = len(re.findall(r'\b(if|for|while|try|except)\b', code_content))
        if control_structures > 20:
            complexity_score += 2
        elif control_structures > 10:
            complexity_score += 1
        
        return min(complexity_score, 10)
    
    def _identify_design_patterns(self, code_content: str) -> List[str]:
        """识别使用的设计模式"""
        
        patterns = []
        
        # 单例模式
        if "__new__" in code_content and "_instance" in code_content:
            patterns.append("Singleton")
        
        # 工厂模式
        if "create" in code_content.lower() and "factory" in code_content.lower():
            patterns.append("Factory")
        
        # 观察者模式
        if "observer" in code_content.lower() or "notify" in code_content.lower():
            patterns.append("Observer")
        
        # 策略模式
        if "strategy" in code_content.lower() or "algorithm" in code_content.lower():
            patterns.append("Strategy")
        
        # 装饰器模式
        if "@" in code_content and "decorator" in code_content.lower():
            patterns.append("Decorator")
        
        # 适配器模式
        if "adapter" in code_content.lower() or "adapt" in code_content.lower():
            patterns.append("Adapter")
        
        # 外观模式
        if "facade" in code_content.lower():
            patterns.append("Facade")
        
        # 建造者模式
        if "builder" in code_content.lower() and "build" in code_content.lower():
            patterns.append("Builder")
        
        return patterns
    
    def _extract_quality_indicators(self, code_content: str) -> Dict[str, Any]:
        """提取质量指标"""
        
        lines = code_content.split('\n')
        total_lines = len([line for line in lines if line.strip()])
        
        # 注释率
        comment_lines = len([line for line in lines if line.strip().startswith('#')])
        comment_ratio = comment_lines / total_lines if total_lines > 0 else 0
        
        # 空行率
        empty_lines = len([line for line in lines if not line.strip()])
        empty_ratio = empty_lines / len(lines) if len(lines) > 0 else 0
        
        # 方法平均长度
        methods = re.findall(r'def\s+\w+.*?(?=def|\Z)', code_content, re.DOTALL)
        avg_method_length = sum(len(method.split('\n')) for method in methods) / len(methods) if methods else 0
        
        # 命名质量（基于长度和可读性）
        identifiers = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', code_content)
        avg_name_length = sum(len(name) for name in identifiers) / len(identifiers) if identifiers else 0
        
        return {
            "total_lines": total_lines,
            "comment_ratio": comment_ratio,
            "empty_ratio": empty_ratio,
            "avg_method_length": avg_method_length,
            "avg_name_length": avg_name_length,
            "method_count": len(methods),
            "identifier_count": len(set(identifiers))
        }

    def _build_code_relationship_graph(self, code_essence: CodeEssence) -> Dict[str, Any]:
        """构建代码关系图（模拟架构师的系统思维）"""

        return {
            "internal_structure": {
                "complexity_level": code_essence.complexity_level,
                "design_patterns": code_essence.design_patterns,
                "architectural_role": code_essence.architectural_role.value
            },
            "external_dependencies": self._analyze_dependencies(code_essence),
            "interaction_patterns": self._analyze_interactions(code_essence),
            "data_flow": {"flow_complexity": "medium"},
            "control_flow": {"flow_type": "sequential"},
            "architectural_layers": self._identify_layers(code_essence)
        }

    def _analyze_dependencies(self, code_essence: CodeEssence) -> List[str]:
        """分析外部依赖"""
        dependencies = []

        if code_essence.architectural_role == ArchitecturalRole.CONTROLLER:
            dependencies.extend(["Service Layer", "Model Layer"])
        elif code_essence.architectural_role == ArchitecturalRole.SERVICE:
            dependencies.extend(["Repository Layer", "Model Layer"])
        elif code_essence.architectural_role == ArchitecturalRole.REPOSITORY:
            dependencies.extend(["Database", "Model Layer"])

        return dependencies

    def _analyze_interactions(self, code_essence: CodeEssence) -> List[str]:
        """分析交互模式"""
        interactions = []

        if "Singleton" in code_essence.design_patterns:
            interactions.append("Global Access Pattern")
        if "Observer" in code_essence.design_patterns:
            interactions.append("Event-Driven Pattern")
        if "Factory" in code_essence.design_patterns:
            interactions.append("Creation Pattern")

        return interactions

    def _identify_layers(self, code_essence: CodeEssence) -> List[str]:
        """识别架构分层"""
        layers = []

        role_to_layer = {
            ArchitecturalRole.CONTROLLER: "Presentation Layer",
            ArchitecturalRole.SERVICE: "Business Layer",
            ArchitecturalRole.REPOSITORY: "Data Access Layer",
            ArchitecturalRole.MODEL: "Domain Layer",
            ArchitecturalRole.UTILITY: "Infrastructure Layer",
            ArchitecturalRole.CONFIGURATION: "Configuration Layer"
        }

        layers.append(role_to_layer.get(code_essence.architectural_role, "Unknown Layer"))
        return layers

    def _derive_design_intent(self, relationship_graph: Dict[str, Any]) -> Dict[str, Any]:
        """推导设计意图（模拟架构师的设计洞察）"""

        return {
            "design_goals": self._infer_design_goals(relationship_graph),
            "architectural_principles": self._identify_principles(relationship_graph),
            "design_trade_offs": self._analyze_trade_offs(relationship_graph),
            "evolution_strategy": {"scalability": "horizontal", "maintainability": "high"},
            "quality_attributes": {"performance": "medium", "security": "high"}
        }

    def _infer_design_goals(self, relationship_graph: Dict[str, Any]) -> List[str]:
        """推导设计目标"""
        goals = []

        internal_structure = relationship_graph.get("internal_structure", {})
        complexity = internal_structure.get("complexity_level", 0)

        if complexity <= 3:
            goals.append("Simplicity and Clarity")
        elif complexity <= 6:
            goals.append("Balanced Complexity")
        else:
            goals.append("Feature Richness")

        patterns = internal_structure.get("design_patterns", [])
        if "Singleton" in patterns:
            goals.append("Resource Management")
        if "Observer" in patterns:
            goals.append("Loose Coupling")
        if "Factory" in patterns:
            goals.append("Flexible Creation")

        return goals

    def _identify_principles(self, relationship_graph: Dict[str, Any]) -> List[str]:
        """识别架构原则"""
        principles = []

        layers = relationship_graph.get("architectural_layers", [])
        if len(layers) > 1:
            principles.append("Separation of Concerns")

        dependencies = relationship_graph.get("external_dependencies", [])
        if len(dependencies) <= 2:
            principles.append("Minimal Dependencies")

        interactions = relationship_graph.get("interaction_patterns", [])
        if "Event-Driven Pattern" in interactions:
            principles.append("Loose Coupling")

        return principles

    def _analyze_trade_offs(self, relationship_graph: Dict[str, Any]) -> List[str]:
        """分析设计权衡"""
        trade_offs = []

        complexity = relationship_graph.get("internal_structure", {}).get("complexity_level", 0)

        if complexity > 5:
            trade_offs.append("Complexity vs Functionality")

        dependencies = relationship_graph.get("external_dependencies", [])
        if len(dependencies) > 3:
            trade_offs.append("Coupling vs Reusability")

        return trade_offs

    def _assess_architectural_quality(self, code_essence: CodeEssence,
                                    relationship_graph: Dict[str, Any],
                                    design_intent: Dict[str, Any],
                                    risk_points: Optional[List[RiskPoint]] = None) -> QualityAssessment:
        """架构质量评估（模拟架构师的质量判断）"""

        # 评估各个维度
        maintainability = self._evaluate_maintainability(code_essence, relationship_graph)
        extensibility = self._evaluate_extensibility(design_intent, relationship_graph)
        consistency = self._evaluate_consistency(code_essence, design_intent)
        elegance = self._evaluate_elegance(code_essence, relationship_graph, design_intent)
        robustness = self._evaluate_robustness(code_essence, relationship_graph)

        # 计算综合评分
        overall_score = (
            maintainability * self.quality_weights[QualityDimension.MAINTAINABILITY] +
            extensibility * self.quality_weights[QualityDimension.EXTENSIBILITY] +
            consistency * self.quality_weights[QualityDimension.CONSISTENCY] +
            elegance * self.quality_weights[QualityDimension.ELEGANCE] +
            robustness * self.quality_weights[QualityDimension.ROBUSTNESS]
        )

        # 计算置信度
        confidence = min(0.9, overall_score / 100.0 + 0.1)

        # 识别问题和优势
        quality_issues = self._identify_quality_issues(
            maintainability, extensibility, consistency, elegance, robustness
        )
        strength_points = self._identify_strength_points(
            maintainability, extensibility, consistency, elegance, robustness
        )

        return QualityAssessment(
            maintainability_score=maintainability,
            extensibility_score=extensibility,
            consistency_score=consistency,
            elegance_score=elegance,
            robustness_score=robustness,
            overall_score=overall_score,
            confidence=confidence,
            quality_issues=quality_issues,
            strength_points=strength_points
        )

    def _evaluate_maintainability(self, code_essence: CodeEssence, relationship_graph: Dict[str, Any]) -> float:
        """评估可维护性"""
        score = 70.0  # 基础分

        # 基于复杂度调整
        if code_essence.complexity_level <= 3:
            score += 20
        elif code_essence.complexity_level <= 6:
            score += 10
        else:
            score -= 10

        # 基于注释率调整
        comment_ratio = code_essence.quality_indicators.get("comment_ratio", 0)
        if comment_ratio > 0.2:
            score += 10
        elif comment_ratio > 0.1:
            score += 5

        # 基于方法长度调整
        avg_method_length = code_essence.quality_indicators.get("avg_method_length", 0)
        if avg_method_length < 20:
            score += 10
        elif avg_method_length > 50:
            score -= 10

        return min(100.0, max(0.0, score))

    def _evaluate_extensibility(self, design_intent: Dict[str, Any], relationship_graph: Dict[str, Any]) -> float:
        """评估可扩展性"""
        score = 70.0  # 基础分

        # 基于设计模式调整
        patterns = relationship_graph.get("internal_structure", {}).get("design_patterns", [])
        if "Strategy" in patterns or "Factory" in patterns:
            score += 15
        if "Observer" in patterns:
            score += 10

        # 基于架构原则调整
        principles = design_intent.get("architectural_principles", [])
        if "Separation of Concerns" in principles:
            score += 10
        if "Loose Coupling" in principles:
            score += 10

        return min(100.0, max(0.0, score))

    def _evaluate_consistency(self, code_essence: CodeEssence, design_intent: Dict[str, Any]) -> float:
        """评估一致性"""
        score = 80.0  # 基础分

        # 基于命名一致性调整
        avg_name_length = code_essence.quality_indicators.get("avg_name_length", 0)
        if 5 <= avg_name_length <= 15:
            score += 10
        else:
            score -= 5

        # 基于架构角色一致性调整
        if code_essence.architectural_role != ArchitecturalRole.UTILITY:
            score += 10

        return min(100.0, max(0.0, score))

    def _evaluate_elegance(self, code_essence: CodeEssence, relationship_graph: Dict[str, Any],
                          design_intent: Dict[str, Any]) -> float:
        """评估优雅性"""
        score = 75.0  # 基础分

        # 基于设计目标调整
        goals = design_intent.get("design_goals", [])
        if "Simplicity and Clarity" in goals:
            score += 15
        if "Balanced Complexity" in goals:
            score += 10

        # 基于设计模式使用调整
        patterns = code_essence.design_patterns
        if len(patterns) == 1:
            score += 10  # 适度使用设计模式
        elif len(patterns) > 3:
            score -= 10  # 过度设计

        return min(100.0, max(0.0, score))

    def _evaluate_robustness(self, code_essence: CodeEssence, relationship_graph: Dict[str, Any]) -> float:
        """评估健壮性"""
        score = 70.0  # 基础分

        # 基于错误处理（简化评估）
        if code_essence.complexity_level > 5:
            score += 10  # 复杂代码通常有更多错误处理

        # 基于架构角色调整
        if code_essence.architectural_role in [ArchitecturalRole.SERVICE, ArchitecturalRole.REPOSITORY]:
            score += 15  # 核心层需要更高健壮性

        return min(100.0, max(0.0, score))

    def _identify_quality_issues(self, maintainability: float, extensibility: float,
                               consistency: float, elegance: float, robustness: float) -> List[Dict[str, Any]]:
        """识别质量问题"""
        issues = []

        if maintainability < 60:
            issues.append({
                "type": "maintainability",
                "severity": "high",
                "description": "代码可维护性较低，建议简化复杂度和增加注释"
            })

        if extensibility < 60:
            issues.append({
                "type": "extensibility",
                "severity": "medium",
                "description": "代码扩展性不足，建议使用更多设计模式"
            })

        if consistency < 70:
            issues.append({
                "type": "consistency",
                "severity": "medium",
                "description": "代码一致性有待提升，建议统一命名规范"
            })

        return issues

    def _identify_strength_points(self, maintainability: float, extensibility: float,
                                consistency: float, elegance: float, robustness: float) -> List[Dict[str, Any]]:
        """识别优势点"""
        strengths = []

        if maintainability >= 80:
            strengths.append({
                "type": "maintainability",
                "description": "代码具有良好的可维护性"
            })

        if extensibility >= 80:
            strengths.append({
                "type": "extensibility",
                "description": "代码具有良好的可扩展性"
            })

        if elegance >= 85:
            strengths.append({
                "type": "elegance",
                "description": "代码设计优雅简洁"
            })

        return strengths

    def _generate_architectural_suggestions(self, quality_assessment: QualityAssessment,
                                          risk_points: Optional[List[RiskPoint]] = None) -> Dict[str, Any]:
        """生成架构师级改进建议"""

        suggestions = {
            "critical_improvements": [],
            "architectural_refactoring": [],
            "design_optimizations": [],
            "best_practice_recommendations": [],
            "evolution_guidance": {}
        }

        # 基于质量问题生成建议
        for issue in quality_assessment.quality_issues:
            if issue["severity"] == "high":
                suggestions["critical_improvements"].append({
                    "issue": issue["description"],
                    "suggestion": self._generate_improvement_suggestion(issue)
                })
            else:
                suggestions["design_optimizations"].append({
                    "issue": issue["description"],
                    "suggestion": self._generate_improvement_suggestion(issue)
                })

        # 生成最佳实践建议
        if quality_assessment.overall_score < 70:
            suggestions["best_practice_recommendations"].extend([
                "考虑重构复杂方法，将其拆分为更小的函数",
                "增加单元测试覆盖率",
                "改善代码注释和文档",
                "统一命名规范和代码风格"
            ])

        # 生成演进指导
        suggestions["evolution_guidance"] = {
            "next_steps": "基于当前质量评估，建议优先改善可维护性",
            "long_term_goals": "建立完整的架构治理体系",
            "monitoring": "定期进行代码质量评估和重构"
        }

        return suggestions

    def _generate_improvement_suggestion(self, issue: Dict[str, Any]) -> str:
        """为特定问题生成改进建议"""

        issue_type = issue.get("type", "")

        if issue_type == "maintainability":
            return "建议拆分复杂方法，增加代码注释，使用更清晰的命名"
        elif issue_type == "extensibility":
            return "建议引入策略模式或工厂模式，提高代码的可扩展性"
        elif issue_type == "consistency":
            return "建议制定并遵循统一的编码规范和命名约定"
        else:
            return "建议进行代码重构，提升整体质量"


# 演示用法
def demonstrate_architectural_thinking():
    """演示架构师思维引擎的使用（增强版：包含风险检测）"""

    # 示例代码（包含潜在风险点）
    sample_code = '''
import java.util.concurrent.*;
import org.apache.kafka.clients.producer.*;
import org.redisson.api.*;

@Service
public class OrderService {
    private final Producer<String, OrderEvent> kafkaProducer;
    private final RedissonClient redissonClient;

    public void processOrdersBatch(List<Order> orders) {
        // 风险点1：并发访问共享Producer
        orders.parallelStream().forEach(order -> {
            kafkaProducer.send(new ProducerRecord<>("orders", order));
        });
    }

    public boolean reserveInventory(String productId, int quantity) {
        RLock lock = redissonClient.getLock("inventory:" + productId);
        // 风险点2：锁管理
        try {
            if (lock.tryLock(30, TimeUnit.SECONDS)) {
                // 业务逻辑
                return updateInventory(productId, quantity);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        // 风险点3：锁可能未释放
        return false;
    }

    private boolean updateInventory(String productId, int quantity) {
        // 风险点4：资源管理
        Connection conn = dataSource.getConnection();
        PreparedStatement stmt = conn.prepareStatement("UPDATE inventory SET qty = qty - ? WHERE product_id = ?");
        stmt.setInt(1, quantity);
        stmt.setString(2, productId);
        return stmt.executeUpdate() > 0;
    }
}
    '''

    # 创建引擎实例
    engine = ArchitecturalThinkingEngine()

    # 分析代码质量
    context = {"file_path": "service/OrderService.java"}
    result = engine.analyze_code_quality(sample_code, context)

    # 输出结果
    print("=== 架构师思维分析结果（增强版） ===")
    print(f"综合质量评分: {result['overall_quality_score']:.2f}")
    print(f"分析置信度: {result['architectural_confidence']:.2f}")
    print(f"风险覆盖度: {result['risk_coverage']} 个风险点")

    print("\n=== 代码本质 ===")
    essence = result['code_essence']
    print(f"主要目的: {essence['primary_purpose']}")
    print(f"架构角色: {essence['architectural_role']}")
    print(f"复杂度级别: {essence['complexity_level']}")

    print("\n=== 风险评估报告 ===")
    risk_report = result['risk_assessment_report']
    print(f"风险总结: {risk_report['summary']}")
    print(f"风险分布: {risk_report['risk_distribution']}")

    print("\n=== 检测到的风险点 ===")
    for i, risk_point in enumerate(result['risk_points'][:3], 1):  # 显示前3个
        print(f"\n风险点 {i}:")
        print(f"  类型: {risk_point['risk_type']}")
        print(f"  严重程度: {risk_point['severity']}")
        print(f"  位置: {risk_point['location']}")
        print(f"  描述: {risk_point['description']}")
        print(f"  检查要点:")
        for check_point in risk_point['check_points'][:2]:  # 显示前2个检查要点
            print(f"    - {check_point}")

    print("\n=== 质量评估 ===")
    assessment = result['quality_assessment']
    print(f"可维护性: {assessment['maintainability_score']:.1f}")
    print(f"可扩展性: {assessment['extensibility_score']:.1f}")
    print(f"一致性: {assessment['consistency_score']:.1f}")
    print(f"优雅性: {assessment['elegance_score']:.1f}")
    print(f"健壮性: {assessment['robustness_score']:.1f}")

    print("\n=== 风险缓解建议 ===")
    for recommendation in risk_report['recommendations']:
        print(f"- {recommendation}")


def demonstrate_risk_detection_only():
    """单独演示风险检测功能"""

    # 包含多种风险的代码示例
    risky_code = '''
@Service
public class PaymentService {
    private KafkaProducer producer;

    public void processPayments(List<Payment> payments) {
        // 并发风险
        payments.parallelStream().forEach(payment -> {
            producer.send(new ProducerRecord<>("payments", payment));
        });

        // 锁管理风险
        Lock lock = getLock();
        lock.lock();
        try {
            processPayment();
        } catch (Exception e) {
            // 空异常处理
        }
        // 可能忘记unlock

        // 资源管理风险
        Connection conn = getConnection();
        Statement stmt = conn.createStatement();
        stmt.execute("UPDATE accounts SET balance = balance - 100");
        // 资源未关闭
    }
}
    '''

    # 创建风险检测器
    detector = CodeRiskDetector()

    # 检测风险点
    risk_points = detector.detect_risk_points(risky_code)

    print("=== 风险检测专项分析 ===")
    print(f"检测到 {len(risk_points)} 个风险点\n")

    for i, rp in enumerate(risk_points, 1):
        print(f"风险点 {i}: {rp.risk_type}")
        print(f"  分类: {rp.risk_category}")
        print(f"  严重程度: {rp.severity}")
        print(f"  置信度: {rp.confidence:.2f}")
        print(f"  描述: {rp.description}")
        print(f"  位置: {rp.location}")
        print(f"  检查要点:")
        for check_point in rp.check_points:
            print(f"    ✓ {check_point}")
        print(f"  代码片段:")
        print(f"{rp.code_snippet}")
        print("-" * 50)


if __name__ == "__main__":
    print("=== 演示1：完整的架构师思维分析（包含风险检测） ===")
    demonstrate_architectural_thinking()

    print("\n\n=== 演示2：专项风险检测分析 ===")
    demonstrate_risk_detection_only()
