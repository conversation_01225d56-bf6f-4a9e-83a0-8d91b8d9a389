/home/<USER>/apps/jdk-21.0.5/bin/java -javaagent:/home/<USER>/apps/idea-IU-243.23654.189/lib/idea_rt.jar=46041:/home/<USER>/apps/idea-IU-243.23654.189/bin -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -classpath /media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/test-classes:/media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/classes:/home/<USER>/works/project/mvnRepository/com/xfvape/uid/uid-generator/0.0.4-RELEASE/uid-generator-0.0.4-RELEASE.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-core/6.2.6/spring-core-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-jcl/6.2.6/spring-jcl-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-beans/6.2.6/spring-beans-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/mybatis/mybatis/3.2.3/mybatis-3.2.3.jar:/home/<USER>/works/project/mvnRepository/org/mybatis/mybatis-spring/1.2.4/mybatis-spring-1.2.4.jar:/home/<USER>/works/project/mvnRepository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/home/<USER>/works/project/mvnRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/log4j-over-slf4j/2.0.17/log4j-over-slf4j-2.0.17.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-context/6.2.6/spring-context-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-aop/6.2.6/spring-aop-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-expression/6.2.6/spring-expression-6.2.6.jar:/home/<USER>/works/project/mvnRepository/io/micrometer/micrometer-observation/1.14.6/micrometer-observation-1.14.6.jar:/home/<USER>/works/project/mvnRepository/io/micrometer/micrometer-commons/1.14.6/micrometer-commons-1.14.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-jdbc/6.2.6/spring-jdbc-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-tx/6.2.6/spring-tx-6.2.6.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-annotations/2.18.3/jackson-annotations-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-core/2.18.3/jackson-core-2.18.3.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter-test/3.4.5/spring-boot-starter-test-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter/3.4.5/spring-boot-starter-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot/3.4.5/spring-boot-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-autoconfigure/3.4.5/spring-boot-autoconfigure-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter-logging/3.4.5/spring-boot-starter-logging-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/home/<USER>/works/project/mvnRepository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/home/<USER>/works/project/mvnRepository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/home/<USER>/works/project/mvnRepository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-test/3.4.5/spring-boot-test-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-test-autoconfigure/3.4.5/spring-boot-test-autoconfigure-3.4.5.jar:/home/<USER>/works/project/mvnRepository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/home/<USER>/works/project/mvnRepository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/home/<USER>/works/project/mvnRepository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/home/<USER>/works/project/mvnRepository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/home/<USER>/works/project/mvnRepository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/home/<USER>/works/project/mvnRepository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/home/<USER>/works/project/mvnRepository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/home/<USER>/works/project/mvnRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-test/6.2.6/spring-test-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/xmlunit/xmlunit-core/2.10.0/xmlunit-core-2.10.0.jar:/home/<USER>/works/project/mvnRepository/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar:/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar:/home/<USER>/works/project/mvnRepository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/home/<USER>/works/project/mvnRepository/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-launcher/1.11.4/junit-platform-launcher-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/testcontainers/1.19.7/testcontainers-1.19.7.jar:/home/<USER>/works/project/mvnRepository/junit/junit/4.13.2/junit-4.13.2.jar:/home/<USER>/works/project/mvnRepository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/home/<USER>/works/project/mvnRepository/org/apache/commons/commons-compress/1.24.0/commons-compress-1.24.0.jar:/home/<USER>/works/project/mvnRepository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/home/<USER>/works/project/mvnRepository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-api/3.3.6/docker-java-api-3.3.6.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-transport-zerodep/3.3.6/docker-java-transport-zerodep-3.3.6.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-transport/3.3.6/docker-java-transport-3.3.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/postgresql/1.19.7/postgresql-1.19.7.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/jdbc/1.20.6/jdbc-1.20.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/database-commons/1.20.6/database-commons-1.20.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/junit-jupiter/1.19.7/junit-jupiter-1.19.7.jar:/home/<USER>/works/project/mvnRepository/org/openjdk/jmh/jmh-core/1.37/jmh-core-1.37.jar:/home/<USER>/works/project/mvnRepository/net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar:/home/<USER>/works/project/mvnRepository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/home/<USER>/works/project/mvnRepository/org/openjdk/jmh/jmh-generator-annprocess/1.37/jmh-generator-annprocess-1.37.jar:/home/<USER>/works/project/mvnRepository/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/home/<USER>/works/project/mvnRepository/org/postgresql/postgresql/42.7.5/postgresql-42.7.5.jar:/home/<USER>/works/project/mvnRepository/org/checkerframework/checker-qual/3.48.3/checker-qual-3.48.3.jar:/home/<USER>/works/project/mvnRepository/com/github/oshi/oshi-core/6.5.0/oshi-core-6.5.0.jar:/home/<USER>/works/project/mvnRepository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/home/<USER>/works/project/mvnRepository/net/java/dev/jna/jna-platform/5.14.0/jna-platform-5.14.0.jar:/home/<USER>/works/project/mvnRepository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar:/home/<USER>/works/project/mvnRepository/ch/qos/logback/logback-classic/1.5.16/logback-classic-1.5.16.jar:/home/<USER>/works/project/mvnRepository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/home/<USER>/works/project/mvnRepository/org/xkongkit/xkongkit-core/1.0.0-SNAPSHOT/xkongkit-core-1.0.0-SNAPSHOT.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/kryo/5.5.0/kryo-5.5.0.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/home/<USER>/works/project/mvnRepository/org/lmdbjava/lmdbjava/0.9.1/lmdbjava-0.9.1.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-constants/0.10.4/jnr-constants-0.10.4.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-ffi/2.2.17/jnr-ffi-2.2.17.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jffi/1.3.13/jffi-1.3.13.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jffi/1.3.13/jffi-1.3.13-native.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-commons/9.7.1/asm-commons-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-a64asm/1.0.0/jnr-a64asm-1.0.0.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-x86asm/1.0.2/jnr-x86asm-1.0.2.jar:/home/<USER>/works/project/mvnRepository/com/tokyocabinet/tokyocabinet/1.24/tokyocabinet-1.24.jar:/home/<USER>/works/project/mvnRepository/org/jackson/databind/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.3/jackson-datatype-jsr310-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/github/luben/zstd-jni/1.5.5-3/zstd-jni-1.5.5-3.jar:/home/<USER>/works/project/mvnRepository/com/github/ben-manes/caffeine/caffeine/3.1.8/caffeine-3.1.8.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/home/<USER>/works/project/mvnRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/home/<USER>/works/project/mvnRepository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-context/1.70.0/grpc-context-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-impl/0.31.1/opencensus-impl-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-impl-core/0.31.1/opencensus-impl-core-0.31.1.jar:/home/<USER>/works/project/mvnRepository/com/lmax/disruptor/3.4.2/disruptor-3.4.2.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-census/1.71.0/grpc-census-1.71.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-api/1.70.0/grpc-api-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-contrib-grpc-metrics/0.31.1/opencensus-contrib-grpc-metrics-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-services/1.71.0/grpc-services-1.71.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-stub/1.70.0/grpc-stub-1.70.0.jar:/home/<USER>/works/project/mvnRepository/org/codehaus/mojo/animal-sniffer-annotations/1.24/animal-sniffer-annotations-1.24.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-core/1.70.0/grpc-core-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar:/home/<USER>/works/project/mvnRepository/io/perfmark/perfmark-api/0.27.0/perfmark-api-0.27.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-protobuf/1.70.0/grpc-protobuf-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/protobuf/protobuf-java/3.25.6/protobuf-java-3.25.6.jar:/home/<USER>/works/project/mvnRepository/com/google/api/grpc/proto-google-common-protos/2.46.0/proto-google-common-protos-2.46.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-protobuf-lite/1.70.0/grpc-protobuf-lite-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-util/1.70.0/grpc-util-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/protobuf/protobuf-java-util/3.25.6/protobuf-java-util-3.25.6.jar:/home/<USER>/works/project/mvnRepository/com/google/errorprone/error_prone_annotations/2.30.0/error_prone_annotations-2.30.0.jar:/home/<USER>/works/project/mvnRepository/com/google/code/gson/gson/2.11.0/gson-2.11.0.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-x86_64.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-common/4.1.119.Final/netty-common-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-buffer/4.1.119.Final/netty-buffer-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport/4.1.119.Final/netty-transport-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-resolver/4.1.119.Final/netty-resolver-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-native-unix-common/4.1.119.Final/netty-transport-native-unix-common-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-classes-epoll/4.1.119.Final/netty-transport-classes-epoll-4.1.119.Final.jar org.xkong.cloud.commons.uid.TestRunner
===== 运行单元测试 =====
01:40:23,301 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-classic version 1.5.16
01:40:23,301 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-core version 1.5.18
01:40:23,301 |-WARN in ch.qos.logback.classic.LoggerContext[default] - Versions of logback-core and logback-classic are different!
01:40:23,316 |-INFO in ch.qos.logback.classic.util.ContextInitializer@3f191845 - Here is a list of configurators discovered as a service, by rank: 
01:40:23,326 |-INFO in ch.qos.logback.classic.util.ContextInitializer@3f191845 -   org.springframework.boot.logging.logback.RootLogLevelConfigurator
01:40:23,326 |-INFO in ch.qos.logback.classic.util.ContextInitializer@3f191845 - They will be invoked in order until ExecutionStatus.DO_NOT_INVOKE_NEXT_IF_ANY is returned.
01:40:23,326 |-INFO in ch.qos.logback.classic.util.ContextInitializer@3f191845 - Constructed configurator of type class org.springframework.boot.logging.logback.RootLogLevelConfigurator
01:40:23,339 |-INFO in ch.qos.logback.classic.util.ContextInitializer@3f191845 - org.springframework.boot.logging.logback.RootLogLevelConfigurator.configure() call lasted 3 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
01:40:23,340 |-INFO in ch.qos.logback.classic.util.ContextInitializer@3f191845 - Trying to configure with ch.qos.logback.classic.joran.SerializedModelConfigurator
01:40:23,361 |-INFO in ch.qos.logback.classic.util.ContextInitializer@3f191845 - Constructed configurator of type class ch.qos.logback.classic.joran.SerializedModelConfigurator
01:40:23,364 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.scmo]
01:40:23,364 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback.scmo]
01:40:23,364 |-INFO in ch.qos.logback.classic.util.ContextInitializer@3f191845 - ch.qos.logback.classic.joran.SerializedModelConfigurator.configure() call lasted 3 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
01:40:23,364 |-INFO in ch.qos.logback.classic.util.ContextInitializer@3f191845 - Trying to configure with ch.qos.logback.classic.util.DefaultJoranConfigurator
01:40:23,367 |-INFO in ch.qos.logback.classic.util.ContextInitializer@3f191845 - Constructed configurator of type class ch.qos.logback.classic.util.DefaultJoranConfigurator
01:40:23,399 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-test.xml] at [file:/media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/test-classes/logback-test.xml]
01:40:23,744 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [CONSOLE]
01:40:23,744 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
01:40:23,760 |-INFO in ch.qos.logback.core.model.processor.ImplicitModelHandler - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
01:40:23,816 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - BEWARE: Writing to the console can be very slow. Avoid logging to the 
01:40:23,816 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - console in production environments, especially in high volume systems.
01:40:23,816 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - See also https://logback.qos.ch/codes.html#slowConsole
01:40:23,816 |-INFO in ch.qos.logback.classic.model.processor.RootLoggerModelHandler - Setting level of ROOT logger to INFO
01:40:23,817 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[ROOT]
01:40:23,818 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [org.xkong.cloud.commons.uid] to DEBUG
01:40:23,818 |-INFO in ch.qos.logback.core.model.processor.DefaultProcessor@5f049ea1 - End of configuration.
01:40:23,818 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@72cc7e6f - Registering current configuration as safe fallback point
01:40:23,818 |-INFO in ch.qos.logback.classic.util.ContextInitializer@3f191845 - ch.qos.logback.classic.util.DefaultJoranConfigurator.configure() call lasted 451 milliseconds. ExecutionStatus=DO_NOT_INVOKE_NEXT_IF_ANY

2025-05-23 01:40:24.624 [main] INFO  o.x.c.c.u.f.MachineFingerprints - 使用OSHI库成功获取系统信息
2025-05-23 01:40:27.030 [main] INFO  o.x.c.c.u.f.MachineFingerprints - 已收集机器特征码: {"os_arch":"amd64","os_name":"Linux","fingerprint_hash":"579ce699d22f0350bfc9f8da764f44ffce04ec103bc79545876e86249dd7c46d","hostname":"long-VirtualBox","mac_addresses":["BE:C****:D3:AE","08:0****:3A:87"],"os_version":"5.4.0-91-generic"}
Mockito is currently self-attaching to enable the inline-mock-maker. This will no longer work in future releases of the JDK. Please add Mockito as an agent to your build what is described in Mockito's documentation: https://javadoc.io/doc/org.mockito/mockito-core/latest/org/mockito/Mockito.html#0.3
WARNING: A Java agent has been loaded dynamically (/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
Java HotSpot(TM) 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classpath has been appended
2025-05-23 01:40:29.340 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 01:40:29.755 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 01:40:29.787 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 01:40:30.338 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 01:40:30.339 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 01:40:30.355 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 01:40:30.363 [main] DEBUG o.x.c.c.u.c.KeyManagementService - 使用NativePRNGNonBlocking算法的SecureRandom
2025-05-23 01:40:30.365 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已为应用 test-app 环境 test-env 创建新的 test-key-type 类型密钥
2025-05-23 01:40:30.375 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 01:40:30.447 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 01:40:30.461 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 01:40:30.463 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已清除密钥缓存
2025-05-23 01:40:30.482 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-23 01:40:30.483 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-23 01:40:30.486 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-23 01:40:30.486 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-23 01:40:30.496 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-23 01:40:30.498 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-23 01:40:30.498 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-23 01:40:30.498 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-23 01:40:30.706 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-23 01:40:30.707 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-23 01:40:30.710 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-23 01:40:30.711 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-23 01:40:30.711 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-23 01:40:30.711 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-23 01:40:30.711 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 已清除所有验证缓存
2025-05-23 01:40:30.809 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 01:40:30.809 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 01:40:30.809 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 01:40:30.810 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 01:40:30.810 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:30.814 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-23 01:40:30.814 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-23 01:40:30.814 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 01:40:30.816 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: main
2025-05-23 01:40:30.816 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: main
2025-05-23 01:40:30.817 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: main
2025-05-23 01:40:30.817 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: main
2025-05-23 01:40:30.817 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: main
2025-05-23 01:40:30.817 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: main
2025-05-23 01:40:30.819 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: main
2025-05-23 01:40:30.827 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:31.033 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 01:40:31.048 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 01:40:31.048 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 01:40:31.049 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 01:40:31.049 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:31.050 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-23 01:40:31.077 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-23 01:40:31.077 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: main
2025-05-23 01:40:31.077 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-23 01:40:31.077 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 01:40:31.103 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 01:40:31.104 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 01:40:31.104 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 01:40:31.104 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 01:40:31.104 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:31.104 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-23 01:40:31.104 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 无法为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 01:40:31.105 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 01:40:31.105 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取工作机器ID时发生异常，当前线程: main, 异常: 无法为实例 12345 分配工作机器ID
java.lang.IllegalStateException: 无法为实例 12345 分配工作机器ID
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.getWorkerId(PersistentInstanceWorkerIdAssigner.java:166)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.lambda$2(PersistentInstanceWorkerIdAssignerTest.java:248)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3128)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.testGetWorkerId_MaxReached(PersistentInstanceWorkerIdAssignerTest.java:248)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:767)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$8(TestMethodTestDescriptor.java:217)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:213)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:138)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.SessionPerRequestLauncher.execute(SessionPerRequestLauncher.java:63)
	at org.xkong.cloud.commons.uid.TestRunner.runTests(TestRunner.java:88)
	at org.xkong.cloud.commons.uid.TestRunner.runUnitTests(TestRunner.java:53)
	at org.xkong.cloud.commons.uid.TestRunner.main(TestRunner.java:27)
2025-05-23 01:40:31.124 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 01:40:31.124 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 01:40:31.124 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 01:40:31.125 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 01:40:31.125 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:31.125 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-23 01:40:31.128 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 插入新的工作机器ID失败，可能存在并发冲突，工作机器ID: 42
2025-05-23 01:40:31.130 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，实例ID: null
2025-05-23 01:40:31.130 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 无法为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 01:40:31.130 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 01:40:31.130 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取工作机器ID时发生异常，当前线程: main, 异常: 无法为实例 12345 分配工作机器ID
java.lang.IllegalStateException: 无法为实例 12345 分配工作机器ID
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.getWorkerId(PersistentInstanceWorkerIdAssigner.java:166)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.testGetWorkerId_AllocateNew(PersistentInstanceWorkerIdAssignerTest.java:185)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:767)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$8(TestMethodTestDescriptor.java:217)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:213)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:138)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.SessionPerRequestLauncher.execute(SessionPerRequestLauncher.java:63)
	at org.xkong.cloud.commons.uid.TestRunner.runTests(TestRunner.java:88)
	at org.xkong.cloud.commons.uid.TestRunner.runUnitTests(TestRunner.java:53)
	at org.xkong.cloud.commons.uid.TestRunner.main(TestRunner.java:27)
2025-05-23 01:40:31.148 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: main
2025-05-23 01:40:31.148 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: false -> false，当前线程: main
2025-05-23 01:40:31.148 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: main
2025-05-23 01:40:31.148 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: main
2025-05-23 01:40:31.149 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 01:40:31.149 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 01:40:31.149 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 01:40:31.149 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 01:40:31.149 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:31.149 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-23 01:40:31.149 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-23 01:40:31.149 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 01:40:31.150 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: main
2025-05-23 01:40:31.163 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 01:40:31.163 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 01:40:31.163 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 01:40:31.163 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 01:40:31.164 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:31.170 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-23 01:40:31.171 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-23 01:40:31.171 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 01:40:31.179 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 01:40:31.183 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 01:40:31.189 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 01:40:31.189 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 01:40:31.189 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 01:40:31.191 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 01:40:31.191 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:31.191 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-23 01:40:31.191 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-23 01:40:31.191 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 01:40:31.198 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 01:40:31.198 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 01:40:31.198 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 01:40:31.199 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 01:40:31.199 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:31.200 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-23 01:40:31.200 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-23 01:40:31.200 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 01:40:31.201 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放成功
2025-05-23 01:40:31.266 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 01:40:31.267 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 01:40:31.267 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 01:40:31.267 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 01:40:31.267 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:31.268 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-23 01:40:31.268 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-23 01:40:31.268 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 01:40:31.268 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: main
2025-05-23 01:40:31.269 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: main
2025-05-23 01:40:31.269 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: main
2025-05-23 01:40:31.269 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: main
2025-05-23 01:40:31.269 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: main
2025-05-23 01:40:31.269 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: main
2025-05-23 01:40:31.269 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: main
2025-05-23 01:40:31.273 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign

Test run finished after 8452 ms
[         5 containers found      ]
[         0 containers skipped    ]
[         5 containers started    ]
[         0 containers aborted    ]
[         5 containers successful ]
[         0 containers failed     ]
[        25 tests found           ]
[         0 tests skipped         ]
[        25 tests started         ]
[         0 tests aborted         ]
[        22 tests successful      ]
[         3 tests failed          ]


===== 运行集成测试 =====
2025-05-23 01:40:31.383 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:31.391 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:40:31.391 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:40:31.391 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:31.391 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:31.391 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:40:31.391 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:40:31.394 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:31.397 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:31.397 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:40:31.397 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:40:31.398 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:40:31.558 [main] INFO  o.testcontainers.images.PullPolicy - Image pull policy will be performed by: DefaultPullPolicy()
2025-05-23 01:40:31.561 [main] INFO  o.t.utility.ImageNameSubstitutor - Image name substitution will be performed by: DefaultImageNameSubstitutor (composite of 'ConfigurationFileImageNameSubstitutor' and 'PrefixingImageNameSubstitutor')
2025-05-23 01:40:31.817 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:31.817 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:40:31.817 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:40:31.817 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:31.817 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:31.817 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:40:31.817 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:40:31.818 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:31.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:31.818 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:40:31.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:40:31.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:40:31.899 [main] INFO  o.t.d.DockerClientProviderStrategy - Loaded org.testcontainers.dockerclient.UnixSocketClientProviderStrategy from ~/.testcontainers.properties, will try it first
2025-05-23 01:40:32.717 [main] INFO  o.t.d.DockerClientProviderStrategy - Found Docker environment with local Unix socket (unix:///var/run/docker.sock)
2025-05-23 01:40:32.723 [main] INFO  o.testcontainers.DockerClientFactory - Docker host IP address is localhost
2025-05-23 01:40:32.743 [main] INFO  o.testcontainers.DockerClientFactory - Connected to docker: 
  Server Version: 28.1.1
  API Version: 1.49
  Operating System: Linux Mint 20.3
  Total Memory: 7960 MB
2025-05-23 01:40:32.775 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Creating container for image: testcontainers/ryuk:0.6.0
2025-05-23 01:40:32.781 [main] INFO  o.t.utility.RegistryAuthLocator - Failure when attempting to lookup auth config. Please ignore if you don't have images in an authenticated registry. Details: (dockerImageName: testcontainers/ryuk:0.6.0, configFile: /home/<USER>/.docker/config.json, configEnv: DOCKER_AUTH_CONFIG). Falling back to docker-java default behaviour. Exception message: Status 404: No config supplied. Checked in order: /home/<USER>/.docker/config.json (file not found), DOCKER_AUTH_CONFIG (not set)
2025-05-23 01:40:33.644 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Container testcontainers/ryuk:0.6.0 is starting: 1b10abbc7853ab8482655ce44391aa340cfadc02fcf44316ffb5e93f220bb376
2025-05-23 01:40:34.109 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Container testcontainers/ryuk:0.6.0 started in PT1.33434034S
2025-05-23 01:40:34.114 [main] INFO  o.t.utility.RyukResourceReaper - Ryuk started - will monitor and terminate Testcontainers containers on JVM exit
2025-05-23 01:40:34.115 [main] INFO  o.testcontainers.DockerClientFactory - Checking the system...
2025-05-23 01:40:34.118 [main] INFO  o.testcontainers.DockerClientFactory - ✔︎ Docker server version should be at least 1.6.0
2025-05-23 01:40:34.119 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-23 01:40:34.419 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 3f429fdcb212309af9e27f41a8988342a320245ac391fc333f31794744a31b3c
2025-05-23 01:40:38.907 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT4.788358256S
2025-05-23 01:40:38.908 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-23 01:40:38.949 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:40:38.987 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-23 01:40:39.240 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4b3ad7ca
2025-05-23 01:40:39.242 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-23 01:40:39.296 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 01:40:39.298 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 01:40:39.299 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 01:40:39.345 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 01:40:39.346 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:40:39.370 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-23 01:40:39.418 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 01:40:39.418 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-23 01:40:39.418 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:40:39.421 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-23 01:40:39.437 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 01:40:39.437 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-23 01:40:39.437 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:40:39.439 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-23 01:40:39.503 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 01:40:39.504 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-23 01:40:39.504 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 01:40:41.345 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-23 01:40:41.788 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: c53d8e9acc7f805b8df2265f4edf12de2cf72bc6fc5f1b58d51a84e72005debf
2025-05-23 01:40:47.072 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.726768982S
2025-05-23 01:40:47.072 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-23 01:40:47.091 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-05-23 01:40:47.112 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@13a268cd
2025-05-23 01:40:47.112 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-05-23 01:40:47.112 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:40:47.113 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 01:40:47.113 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 01:40:47.113 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 01:40:47.116 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 不存在，正在自动创建
2025-05-23 01:40:47.116 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 创建Schema（如果不存在）: infra_uid
2025-05-23 01:40:47.117 [main] DEBUG o.x.c.c.u.m.s.JdbcMetadataStrategy - 执行SQL: CREATE SCHEMA IF NOT EXISTS "infra_uid"
2025-05-23 01:40:47.119 [main] INFO  o.x.c.c.u.m.s.JdbcMetadataStrategy - 已创建Schema: infra_uid
2025-05-23 01:40:47.119 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 已成功创建
2025-05-23 01:40:47.119 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 01:40:47.119 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:40:47.122 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-23 01:40:47.134 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 01:40:47.134 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-23 01:40:47.134 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:40:47.136 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-23 01:40:47.145 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 01:40:47.146 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-23 01:40:47.146 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:40:47.148 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-23 01:40:47.155 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 01:40:47.155 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-23 01:40:47.155 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 01:40:47.158 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:40:47.158 [main] ERROR o.x.c.c.u.i.PersistentInstanceManagerBuilder - 构建失败: recoveryEnabled不能为空
2025-05-23 01:40:47.163 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Starting...
2025-05-23 01:40:47.180 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-3 - Added connection org.postgresql.jdbc.PgConnection@a69f9d
2025-05-23 01:40:47.180 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Start completed.
2025-05-23 01:40:47.180 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:40:47.180 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 01:40:47.181 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 01:40:47.181 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 01:40:47.182 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 01:40:47.183 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:40:47.185 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 01:40:47.186 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:40:47.189 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 01:40:47.189 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 01:40:47.198 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 01:40:47.198 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 01:40:47.198 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:40:47.199 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 01:40:47.200 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:40:47.201 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 01:40:47.201 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 01:40:47.204 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 01:40:47.205 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 01:40:47.205 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:40:47.206 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 01:40:47.206 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:40:47.208 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 01:40:47.208 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 01:40:47.211 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 01:40:47.212 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 01:40:47.212 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 01:40:47.212 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:40:47.212 [main] ERROR o.x.c.c.u.i.PersistentInstanceManagerBuilder - 构建失败: localStoragePath不能为空
2025-05-23 01:40:47.215 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Starting...
2025-05-23 01:40:47.231 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-4 - Added connection org.postgresql.jdbc.PgConnection@3348c987
2025-05-23 01:40:47.231 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Start completed.
2025-05-23 01:40:47.232 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:40:47.232 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 01:40:47.232 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 01:40:47.232 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 01:40:47.234 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 01:40:47.234 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:40:47.237 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 01:40:47.237 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:40:47.239 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 01:40:47.239 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 01:40:47.246 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 01:40:47.246 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 01:40:47.246 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:40:47.247 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 01:40:47.248 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:40:47.249 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 01:40:47.249 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 01:40:47.252 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 01:40:47.252 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 01:40:47.252 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:40:47.254 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 01:40:47.254 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:40:47.255 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 01:40:47.255 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 01:40:47.259 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 01:40:47.259 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 01:40:47.259 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 01:40:47.259 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:40:47.259 [main] ERROR o.x.c.c.u.i.PersistentInstanceManagerBuilder - 构建失败: localStoragePath不能为空
2025-05-23 01:40:48.807 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-23 01:40:49.501 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 17fe7a1cf5b06694b6e3298687c941e7c08002dfc36ba016b095c8ec4a32f75c
2025-05-23 01:40:50.802 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:40:50.807 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:40:50.807 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-23 01:40:50.808 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:40:50.808 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:40:50.808 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:40:50.808 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:40:50.809 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:50.916 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:40:50.918 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:40:50.918 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 01:40:50.918 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:40:50.918 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:40:50.918 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:40:50.918 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:40:50.919 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.092 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.120 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.139 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.142 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.143 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.143 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.143 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.143 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.143 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.143 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.189 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.191 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.191 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.191 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.191 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.191 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.191 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.192 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.258 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.260 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.261 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.261 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.261 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.261 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.261 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:40:51.262 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.309 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.309 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:40:51.310 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:40:51.310 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.310 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.310 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.310 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.312 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.312 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:51.313 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:40:51.313 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.313 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:40:51.423 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.423 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:40:51.423 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:40:51.423 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.423 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.423 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.423 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.425 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.426 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:51.426 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.427 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-23 01:40:51.427 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:40:51.427 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.427 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:40:51.644 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.644 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:40:51.644 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:40:51.644 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.644 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.644 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.644 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.646 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.646 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:51.647 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:40:51.647 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.647 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:40:51.693 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.693 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:40:51.693 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:40:51.693 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.693 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.693 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.693 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.694 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.694 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:51.694 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:40:51.695 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.695 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:40:51.763 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.763 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:40:51.763 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:40:51.763 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.763 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.763 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.763 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.764 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:40:51.764 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:40:51.765 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:40:51.765 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:40:51.765 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:40:54.906 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT6.099201544S
2025-05-23 01:40:54.907 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-23 01:40:54.908 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-5 - Starting...
2025-05-23 01:40:54.951 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-5 - Added connection org.postgresql.jdbc.PgConnection@31d4b3e8
2025-05-23 01:40:54.952 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-5 - Start completed.
2025-05-23 01:40:54.952 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:40:54.952 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 01:40:54.953 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 01:40:54.953 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 01:40:54.955 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 不存在，正在自动创建
2025-05-23 01:40:54.955 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 创建Schema（如果不存在）: infra_uid
2025-05-23 01:40:54.956 [main] DEBUG o.x.c.c.u.m.s.JdbcMetadataStrategy - 执行SQL: CREATE SCHEMA IF NOT EXISTS "infra_uid"
2025-05-23 01:40:54.957 [main] INFO  o.x.c.c.u.m.s.JdbcMetadataStrategy - 已创建Schema: infra_uid
2025-05-23 01:40:54.957 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 已成功创建
2025-05-23 01:40:54.957 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 01:40:54.957 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:40:54.960 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-23 01:40:54.970 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 01:40:54.970 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-23 01:40:54.970 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:40:54.972 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-23 01:40:54.983 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 01:40:54.983 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-23 01:40:54.983 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:40:54.985 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-23 01:40:54.992 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 01:40:54.992 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-23 01:40:54.992 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 01:40:54.997 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件清理关闭钩子
2025-05-23 01:40:54.997 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/expiry-test2028485505715305614, 描述: 创建的临时目录
2025-05-23 01:40:54.997 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/expiry-test-23668925100268561436, 描述: 创建的临时目录
2025-05-23 01:40:54.998 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:40:54.998 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 01:40:54.998 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: expiry-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 01:40:54.998 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/expiry-test2028485505715305614/instance-id
2025-05-23 01:40:54.998 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 01:40:55.002 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 01:40:55.003 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 01:40:55.027 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 1
2025-05-23 01:40:55.033 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/expiry-test2028485505715305614/instance-id
2025-05-23 01:40:55.033 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 01:40:55.036 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-6 - Starting...
2025-05-23 01:40:55.052 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-6 - Added connection org.postgresql.jdbc.PgConnection@38e00b47
2025-05-23 01:40:55.052 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-6 - Start completed.
2025-05-23 01:40:55.053 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:40:55.053 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 01:40:55.054 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 01:40:55.054 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 01:40:55.056 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 01:40:55.056 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:40:55.060 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 01:40:55.060 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:40:55.062 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 01:40:55.062 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 01:40:55.069 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 01:40:55.069 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 01:40:55.069 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:40:55.071 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 01:40:55.071 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:40:55.073 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 01:40:55.073 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 01:40:55.077 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 01:40:55.077 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 01:40:55.077 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:40:55.079 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 01:40:55.079 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:40:55.080 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 01:40:55.080 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 01:40:55.083 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 01:40:55.084 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 01:40:55.084 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 01:40:55.084 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/lease-test7328917772236258591, 描述: 创建的临时目录
2025-05-23 01:40:55.084 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:40:55.084 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 01:40:55.084 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: lease-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 01:40:55.084 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/lease-test7328917772236258591/instance-id
2025-05-23 01:40:55.084 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 01:40:55.087 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 01:40:55.087 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 01:40:55.089 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 2
2025-05-23 01:40:55.090 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/lease-test7328917772236258591/instance-id
2025-05-23 01:40:55.090 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 01:40:55.091 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 01:40:55.091 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 01:40:55.091 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 01:40:55.091 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 2 分配工作机器ID，当前线程: main
2025-05-23 01:40:55.091 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 2 是否已分配工作机器ID
2025-05-23 01:40:55.092 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-23 01:40:55.098 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 0，实例ID: 2
2025-05-23 01:40:55.098 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 2 分配了新的工作机器ID: 0，当前线程: main
2025-05-23 01:40:55.098 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-23 01:40:55.098 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 01:40:56.093 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: worker-id-lease-renewal
2025-05-23 01:40:56.094 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 2，schemaName: infra_uid，当前线程: worker-id-lease-renewal
2025-05-23 01:40:56.097 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:40:56.100 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '5 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 2]，当前线程: worker-id-lease-renewal
2025-05-23 01:40:56.102 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: worker-id-lease-renewal
2025-05-23 01:40:56.104 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: worker-id-lease-renewal
2025-05-23 01:40:56.107 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-23 01:40:56.112 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-7 - Starting...
2025-05-23 01:40:56.135 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-7 - Added connection org.postgresql.jdbc.PgConnection@5a6f6cac
2025-05-23 01:40:56.135 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-7 - Start completed.
2025-05-23 01:40:56.135 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:40:56.135 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 01:40:56.136 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 01:40:56.136 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 01:40:56.138 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 01:40:56.139 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:40:56.142 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 01:40:56.142 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:40:56.144 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 01:40:56.145 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 01:40:56.151 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 01:40:56.152 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 01:40:56.152 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:40:56.153 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 01:40:56.153 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:40:56.156 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 01:40:56.156 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 01:40:56.159 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 01:40:56.160 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 01:40:56.160 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:40:56.161 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 01:40:56.162 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:40:56.163 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 01:40:56.163 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 01:40:56.166 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 01:40:56.167 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 01:40:56.167 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 01:40:56.167 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test6900377404988165893, 描述: 创建的临时目录
2025-05-23 01:40:56.167 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:40:56.167 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 01:40:56.167 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: worker-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 01:40:56.167 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test6900377404988165893/instance-id
2025-05-23 01:40:56.167 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 01:40:56.170 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 01:40:56.170 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 01:40:56.171 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 3
2025-05-23 01:40:56.172 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test6900377404988165893/instance-id
2025-05-23 01:40:56.172 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 01:40:56.172 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 01:40:56.172 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 01:40:56.172 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 01:40:56.172 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 3 分配工作机器ID，当前线程: main
2025-05-23 01:40:56.172 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 3 是否已分配工作机器ID
2025-05-23 01:40:56.174 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-23 01:40:56.176 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 3
2025-05-23 01:40:56.177 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 3 分配了新的工作机器ID: 0，当前线程: main
2025-05-23 01:40:56.177 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-23 01:40:56.177 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 01:40:56.179 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-23 01:40:56.181 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-8 - Starting...
2025-05-23 01:40:56.198 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-8 - Added connection org.postgresql.jdbc.PgConnection@621dc63d
2025-05-23 01:40:56.212 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-8 - Start completed.
2025-05-23 01:40:56.212 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:40:56.212 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 01:40:56.213 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 01:40:56.213 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 01:40:56.215 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 01:40:56.215 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:40:56.217 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 01:40:56.218 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:40:56.219 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 01:40:56.219 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 01:40:56.228 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 01:40:56.229 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 01:40:56.229 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:40:56.230 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 01:40:56.230 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:40:56.232 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 01:40:56.232 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 01:40:56.235 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 01:40:56.235 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 01:40:56.235 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:40:56.237 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 01:40:56.237 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:40:56.238 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 01:40:56.239 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 01:40:56.242 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 01:40:56.243 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 01:40:56.243 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 01:40:56.243 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test4446943498298753483, 描述: 创建的临时目录
2025-05-23 01:40:56.243 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test4446943498298753483, 描述: 并发测试基础目录
2025-05-23 01:40:56.246 [pool-1-thread-1] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test4446943498298753483/instance-id-0, 描述: 并发测试实例 0 的ID文件
2025-05-23 01:40:56.247 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:40:56.247 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 01:40:56.247 [pool-1-thread-1] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-0, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 01:40:56.247 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test4446943498298753483/instance-id-0
2025-05-23 01:40:56.247 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 01:40:56.249 [pool-1-thread-3] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test4446943498298753483/instance-id-2, 描述: 并发测试实例 2 的ID文件
2025-05-23 01:40:56.249 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:40:56.249 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 01:40:56.249 [pool-1-thread-3] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-2, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 01:40:56.249 [pool-1-thread-2] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test4446943498298753483/instance-id-1, 描述: 并发测试实例 1 的ID文件
2025-05-23 01:40:56.249 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:40:56.249 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 01:40:56.249 [pool-1-thread-2] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-1, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 01:40:56.249 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test4446943498298753483/instance-id-1
2025-05-23 01:40:56.249 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 01:40:56.249 [pool-1-thread-5] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test4446943498298753483/instance-id-4, 描述: 并发测试实例 4 的ID文件
2025-05-23 01:40:56.249 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:40:56.249 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 01:40:56.249 [pool-1-thread-4] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test4446943498298753483/instance-id-3, 描述: 并发测试实例 3 的ID文件
2025-05-23 01:40:56.249 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:40:56.249 [pool-1-thread-5] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-4, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 01:40:56.250 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test4446943498298753483/instance-id-2
2025-05-23 01:40:56.250 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test4446943498298753483/instance-id-4
2025-05-23 01:40:56.250 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 01:40:56.250 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 01:40:56.249 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 01:40:56.251 [pool-1-thread-4] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-3, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 01:40:56.252 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test4446943498298753483/instance-id-3
2025-05-23 01:40:56.252 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 01:40:56.255 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 01:40:56.255 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 01:40:56.257 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 4
2025-05-23 01:40:56.261 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test4446943498298753483/instance-id-0
2025-05-23 01:40:56.261 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 01:40:56.261 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-1
2025-05-23 01:40:56.261 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-1
2025-05-23 01:40:56.261 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-1
2025-05-23 01:40:56.261 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 4 分配工作机器ID，当前线程: pool-1-thread-1
2025-05-23 01:40:56.261 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 4 是否已分配工作机器ID
2025-05-23 01:40:56.264 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 01:40:56.264 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 01:40:56.265 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 5
2025-05-23 01:40:56.266 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test4446943498298753483/instance-id-4
2025-05-23 01:40:56.266 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 01:40:56.269 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-5
2025-05-23 01:40:56.269 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-5
2025-05-23 01:40:56.269 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-5
2025-05-23 01:40:56.269 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 5 分配工作机器ID，当前线程: pool-1-thread-5
2025-05-23 01:40:56.269 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 5 是否已分配工作机器ID
2025-05-23 01:40:56.269 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 01:40:56.269 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 01:40:56.271 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-5
2025-05-23 01:40:56.273 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 5
2025-05-23 01:40:56.274 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 5 分配了新的工作机器ID: 0，当前线程: pool-1-thread-5
2025-05-23 01:40:56.274 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-23 01:40:56.274 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-5
2025-05-23 01:40:56.274 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: pool-1-thread-5
2025-05-23 01:40:56.274 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 5，schemaName: infra_uid，当前线程: pool-1-thread-5
2025-05-23 01:40:56.276 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 6
2025-05-23 01:40:56.278 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test4446943498298753483/instance-id-2
2025-05-23 01:40:56.278 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-1
2025-05-23 01:40:56.278 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 01:40:56.281 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-3
2025-05-23 01:40:56.281 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-3
2025-05-23 01:40:56.281 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-3
2025-05-23 01:40:56.281 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 6 分配工作机器ID，当前线程: pool-1-thread-3
2025-05-23 01:40:56.281 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 6 是否已分配工作机器ID
2025-05-23 01:40:56.281 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: pool-1-thread-5
2025-05-23 01:40:56.281 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 5]，当前线程: pool-1-thread-5
2025-05-23 01:40:56.284 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-5
2025-05-23 01:40:56.285 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: pool-1-thread-5
2025-05-23 01:40:56.285 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 1，实例ID: 4
2025-05-23 01:40:56.286 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 4 分配了新的工作机器ID: 1，当前线程: pool-1-thread-1
2025-05-23 01:40:56.286 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 1
2025-05-23 01:40:56.286 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-1
2025-05-23 01:40:56.286 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 1，当前线程: pool-1-thread-1
2025-05-23 01:40:56.286 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 4，schemaName: infra_uid，当前线程: pool-1-thread-1
2025-05-23 01:40:56.287 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 当前状态: ACTIVE，当前线程: pool-1-thread-1
2025-05-23 01:40:56.288 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [1, 4]，当前线程: pool-1-thread-1
2025-05-23 01:40:56.289 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 01:40:56.289 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 01:40:56.290 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-1
2025-05-23 01:40:56.292 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 租约续约成功，当前线程: pool-1-thread-1
2025-05-23 01:40:56.293 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 7
2025-05-23 01:40:56.295 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-3
2025-05-23 01:40:56.297 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test4446943498298753483/instance-id-1
2025-05-23 01:40:56.298 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 01:40:56.299 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-2
2025-05-23 01:40:56.299 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-2
2025-05-23 01:40:56.299 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-2
2025-05-23 01:40:56.299 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 7 分配工作机器ID，当前线程: pool-1-thread-2
2025-05-23 01:40:56.299 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 7 是否已分配工作机器ID
2025-05-23 01:40:56.299 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 01:40:56.299 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 01:40:56.303 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 8
2025-05-23 01:40:56.303 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 2，实例ID: 6
2025-05-23 01:40:56.308 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 6 分配了新的工作机器ID: 2，当前线程: pool-1-thread-3
2025-05-23 01:40:56.308 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 2
2025-05-23 01:40:56.309 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-3
2025-05-23 01:40:56.312 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 2，当前线程: pool-1-thread-3
2025-05-23 01:40:56.312 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 6，schemaName: infra_uid，当前线程: pool-1-thread-3
2025-05-23 01:40:56.316 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-2
2025-05-23 01:40:56.318 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-23 01:40:56.320 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 当前状态: ACTIVE，当前线程: pool-1-thread-3
2025-05-23 01:40:56.320 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [2, 6]，当前线程: pool-1-thread-3
2025-05-23 01:40:56.324 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 7
2025-05-23 01:40:56.326 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 7 分配了新的工作机器ID: 0，当前线程: pool-1-thread-2
2025-05-23 01:40:56.326 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-23 01:40:56.326 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-2
2025-05-23 01:40:56.326 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: pool-1-thread-2
2025-05-23 01:40:56.326 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 7，schemaName: infra_uid，当前线程: pool-1-thread-2
2025-05-23 01:40:56.325 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-3
2025-05-23 01:40:56.327 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: pool-1-thread-2
2025-05-23 01:40:56.327 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 7]，当前线程: pool-1-thread-2
2025-05-23 01:40:56.330 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test4446943498298753483/instance-id-3
2025-05-23 01:40:56.330 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 01:40:56.330 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-2
2025-05-23 01:40:56.330 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-4
2025-05-23 01:40:56.330 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-4
2025-05-23 01:40:56.330 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-4
2025-05-23 01:40:56.330 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 8 分配工作机器ID，当前线程: pool-1-thread-4
2025-05-23 01:40:56.330 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 8 是否已分配工作机器ID
2025-05-23 01:40:56.330 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 租约续约成功，当前线程: pool-1-thread-3
2025-05-23 01:40:56.333 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: pool-1-thread-2
2025-05-23 01:40:56.334 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-4
2025-05-23 01:40:56.347 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 释放成功
2025-05-23 01:40:56.352 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-23 01:40:56.353 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 释放成功
2025-05-23 01:40:56.369 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 3，实例ID: 8
2025-05-23 01:40:56.370 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 8 分配了新的工作机器ID: 3，当前线程: pool-1-thread-4
2025-05-23 01:40:56.370 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 3
2025-05-23 01:40:56.370 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-4
2025-05-23 01:40:56.370 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 3，当前线程: pool-1-thread-4
2025-05-23 01:40:56.371 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 8，schemaName: infra_uid，当前线程: pool-1-thread-4
2025-05-23 01:40:56.371 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 当前状态: ACTIVE，当前线程: pool-1-thread-4
2025-05-23 01:40:56.371 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [3, 8]，当前线程: pool-1-thread-4
2025-05-23 01:40:56.373 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-4
2025-05-23 01:40:56.374 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 租约续约成功，当前线程: pool-1-thread-4
2025-05-23 01:40:56.391 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 释放成功
2025-05-23 01:40:56.399 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 开始清理临时文件，当前注册的临时文件数量: 0
2025-05-23 01:40:57.899 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-23 01:40:58.519 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 4469780df65f0d8298b068064afb8bf51ee2f10a56dae9a0273404298a41c0f9
2025-05-23 01:41:04.436 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT6.536836159S
2025-05-23 01:41:04.436 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-23 01:41:04.437 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-9 - Starting...
2025-05-23 01:41:04.463 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-9 - Added connection org.postgresql.jdbc.PgConnection@503556cb
2025-05-23 01:41:04.464 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-9 - Start completed.
2025-05-23 01:41:04.464 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:41:04.464 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 01:41:04.465 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 01:41:04.465 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 01:41:04.467 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 不存在，正在自动创建
2025-05-23 01:41:04.467 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 创建Schema（如果不存在）: infra_uid
2025-05-23 01:41:04.468 [main] DEBUG o.x.c.c.u.m.s.JdbcMetadataStrategy - 执行SQL: CREATE SCHEMA IF NOT EXISTS "infra_uid"
2025-05-23 01:41:04.469 [main] INFO  o.x.c.c.u.m.s.JdbcMetadataStrategy - 已创建Schema: infra_uid
2025-05-23 01:41:04.469 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 已成功创建
2025-05-23 01:41:04.469 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 01:41:04.469 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:41:04.479 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-23 01:41:04.490 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 01:41:04.490 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-23 01:41:04.491 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:41:04.492 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-23 01:41:04.504 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 01:41:04.504 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-23 01:41:04.504 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:41:04.507 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-23 01:41:04.514 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 01:41:04.515 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-23 01:41:04.515 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 01:41:04.515 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: key-test, 环境: test, Schema: infra_uid
2025-05-23 01:41:04.518 [main] DEBUG o.x.c.c.u.c.KeyManagementService - 使用NativePRNGNonBlocking算法的SecureRandom
2025-05-23 01:41:04.519 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已为应用 key-test 环境 test 创建新的 test-key-type 类型密钥
2025-05-23 01:41:06.129 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-23 01:41:06.869 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 27a6824415cc3dc6bc410b18c57e65c902dcdad547e6b180b9023f37ac34d77e
2025-05-23 01:41:10.795 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:41:10.796 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:41:10.796 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-23 01:41:10.796 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:41:10.796 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:41:10.796 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:41:10.796 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:41:10.797 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:10.920 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:41:10.920 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:41:10.920 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 01:41:10.920 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:41:10.920 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:41:10.920 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:41:10.920 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:41:10.921 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.092 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.120 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.139 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.139 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.140 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.140 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.140 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.140 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.140 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.140 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.188 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.189 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.189 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.189 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.189 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.189 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.189 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.190 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.258 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.259 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.260 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.260 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.260 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.260 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.260 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:41:11.260 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.297 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.297 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:41:11.297 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:41:11.297 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.298 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.298 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.298 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.303 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.303 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:41:11.303 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:41:11.303 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.303 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:41:11.422 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.422 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:41:11.422 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:41:11.422 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.422 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.422 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.422 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.423 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.423 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:41:11.423 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.424 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-23 01:41:11.424 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:41:11.424 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.424 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:41:11.640 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.640 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:41:11.640 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:41:11.640 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.640 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.640 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.640 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.642 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.642 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:41:11.643 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:41:11.643 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.643 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:41:11.690 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.690 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:41:11.690 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:41:11.690 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.690 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.691 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.691 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.692 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.692 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:41:11.692 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:41:11.692 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.692 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:41:11.761 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.761 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:41:11.761 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:41:11.761 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.761 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.761 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.761 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.762 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:11.762 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:41:11.762 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:41:11.762 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:41:11.762 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:41:12.414 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT6.284560284S
2025-05-23 01:41:12.414 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-23 01:41:12.416 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-10 - Starting...
2025-05-23 01:41:12.443 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-10 - Added connection org.postgresql.jdbc.PgConnection@6b9a1205
2025-05-23 01:41:12.443 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-10 - Start completed.
2025-05-23 01:41:12.443 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:41:12.445 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: test_schema
2025-05-23 01:41:12.448 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: non_existent_schema
2025-05-23 01:41:12.453 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-11 - Starting...
2025-05-23 01:41:12.472 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-11 - Added connection org.postgresql.jdbc.PgConnection@46524ebe
2025-05-23 01:41:12.472 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-11 - Start completed.
2025-05-23 01:41:12.472 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:41:12.479 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-23 01:41:12.483 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.non_existent_table
2025-05-23 01:41:12.486 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-12 - Starting...
2025-05-23 01:41:12.503 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-12 - Added connection org.postgresql.jdbc.PgConnection@65af05b2
2025-05-23 01:41:12.504 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-12 - Start completed.
2025-05-23 01:41:12.504 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:41:12.505 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 验证表结构是否包含所需的列: test_schema.test_table
2025-05-23 01:41:12.505 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-23 01:41:12.510 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table
2025-05-23 01:41:12.515 [main] WARN  o.x.c.c.u.m.PostgreSQLMetadataService - 表 test_schema.test_table 缺少必需的列: name
2025-05-23 01:41:12.518 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-13 - Starting...
2025-05-23 01:41:12.540 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-13 - Added connection org.postgresql.jdbc.PgConnection@74bbc273
2025-05-23 01:41:12.540 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-13 - Start completed.
2025-05-23 01:41:12.540 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:41:12.548 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 01:41:12.548 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: test_schema
2025-05-23 01:41:12.553 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - Schema test_schema 验证通过
2025-05-23 01:41:12.553 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-23 01:41:12.561 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 test_schema.test_table 验证通过
2025-05-23 01:41:12.562 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-23 01:41:12.566 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 test_schema.test_table 验证通过
2025-05-23 01:41:12.566 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table
2025-05-23 01:41:12.571 [main] ERROR o.x.c.c.u.s.i.UidValidationServiceImpl - 表结构验证失败: 表 test_schema.test_table 缺少必需的列 name
2025-05-23 01:41:12.576 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-14 - Starting...
2025-05-23 01:41:12.629 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-14 - Added connection org.postgresql.jdbc.PgConnection@401926df
2025-05-23 01:41:12.629 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-14 - Start completed.
2025-05-23 01:41:12.629 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:41:12.632 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table

Test run finished after 43514 ms
[         6 containers found      ]
[         0 containers skipped    ]
[         6 containers started    ]
[         0 containers aborted    ]
[         6 containers successful ]
[         0 containers failed     ]
[        14 tests found           ]
[         0 tests skipped         ]
[        14 tests started         ]
[         0 tests aborted         ]
[         6 tests successful      ]
[         8 tests failed          ]


===== 运行简化测试 =====

1. 基本功能测试
2025-05-23 01:41:14.988 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-23 01:41:15.669 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 6025ecf9c0892cb9d4ec522a124d37a5cb8dc9c250a68bfc15884233e7bf9306
2025-05-23 01:41:20.586 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.597684444S
2025-05-23 01:41:20.587 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-23 01:41:20.587 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-15 - Starting...
2025-05-23 01:41:20.612 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-15 - Added connection org.postgresql.jdbc.PgConnection@5368e981
2025-05-23 01:41:20.612 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-15 - Start completed.
2025-05-23 01:41:20.613 [main] INFO  o.x.c.c.u.test.PostgresTestContainer - 正在执行数据库初始化脚本: init-schema.sql
2025-05-23 01:41:20.658 [main] INFO  o.x.c.c.u.test.PostgresTestContainer - 数据库初始化脚本执行完成
2025-05-23 01:41:20.659 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-16 - Starting...
2025-05-23 01:41:20.677 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-16 - Added connection org.postgresql.jdbc.PgConnection@1224e1b6
2025-05-23 01:41:20.677 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-16 - Start completed.
2025-05-23 01:41:20.677 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:41:20.677 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 01:41:20.678 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 01:41:20.678 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 01:41:20.680 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 01:41:20.680 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:41:20.683 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 01:41:20.683 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:41:20.685 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 01:41:20.685 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 01:41:20.691 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 01:41:20.692 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 01:41:20.692 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:41:20.693 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 01:41:20.693 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:41:20.695 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 01:41:20.695 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 01:41:20.698 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 01:41:20.698 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 01:41:20.698 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:41:20.699 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-23 01:41:20.707 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 01:41:20.707 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-23 01:41:20.707 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 01:41:20.707 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:41:20.707 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 01:41:20.707 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: test-app, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 01:41:20.733 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 1
2025-05-23 01:41:20.734 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 1 在数据库中不存在
2025-05-23 01:41:20.735 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 01:41:20.736 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 01:41:20.736 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 01:41:20.738 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 1
2025-05-23 01:41:20.744 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/test-instance-id.dat
2025-05-23 01:41:20.744 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
实例ID: 1
2025-05-23 01:41:20.745 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 01:41:20.745 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 01:41:20.745 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 01:41:20.745 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 1 分配工作机器ID，当前线程: main
2025-05-23 01:41:20.745 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 1 是否已分配工作机器ID
2025-05-23 01:41:20.747 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-23 01:41:20.750 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 0，实例ID: 1
2025-05-23 01:41:20.751 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配了新的工作机器ID: 0，当前线程: main
2025-05-23 01:41:20.751 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-23 01:41:20.751 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
工作机器ID: 0
2025-05-23 01:41:20.752 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功

2. 实例恢复测试
2025-05-23 01:41:20.755 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-17 - Starting...
2025-05-23 01:41:20.771 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-17 - Added connection org.postgresql.jdbc.PgConnection@603d25db
2025-05-23 01:41:20.771 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-17 - Start completed.
2025-05-23 01:41:20.772 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:41:20.772 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 01:41:20.772 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 01:41:20.772 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 01:41:20.774 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 01:41:20.774 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:41:20.777 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 01:41:20.777 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:41:20.779 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 01:41:20.779 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 01:41:20.786 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 01:41:20.786 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 01:41:20.786 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:41:20.787 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 01:41:20.787 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:41:20.788 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 01:41:20.789 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 01:41:20.792 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 01:41:20.792 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 01:41:20.792 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:41:20.794 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 01:41:20.794 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:41:20.796 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 01:41:20.796 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 01:41:20.799 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 01:41:20.799 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 01:41:20.800 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
===== 第一次运行 =====
2025-05-23 01:41:20.800 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:41:20.800 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 01:41:20.800 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: recovery-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 01:41:20.801 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 2
2025-05-23 01:41:20.803 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 2 在数据库中不存在
2025-05-23 01:41:20.803 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 01:41:20.805 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 01:41:20.805 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 01:41:20.806 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 2
2025-05-23 01:41:20.808 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
2025-05-23 01:41:20.809 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
第一次运行 - 实例ID: 2

===== 第二次运行 (从文件恢复) =====
2025-05-23 01:41:20.809 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:41:20.809 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 01:41:20.809 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: recovery-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 01:41:20.810 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 2
2025-05-23 01:41:20.811 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 2 在数据库中存在，更新实例信息
2025-05-23 01:41:20.812 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 2 的最后活动时间
2025-05-23 01:41:20.812 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
第二次运行 - 实例ID: 2
从文件恢复结果: 成功

===== 删除文件后运行 (从特征码恢复) =====
2025-05-23 01:41:20.813 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:41:20.813 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 01:41:20.813 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: recovery-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 01:41:20.813 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
2025-05-23 01:41:20.813 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 01:41:20.814 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 通过指纹哈希精确匹配找到实例ID: 2
2025-05-23 01:41:20.814 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 成功恢复实例ID: 2
2025-05-23 01:41:20.815 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 2 的最后活动时间
2025-05-23 01:41:20.816 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
2025-05-23 01:41:20.816 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
删除文件后运行 - 实例ID: 2
从特征码恢复结果: 成功

3. 租约管理测试
2025-05-23 01:41:20.818 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - ===== 租约管理测试开始 =====
2025-05-23 01:41:20.818 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1. 准备测试环境 - 初始化数据源
2025-05-23 01:41:20.818 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.1 检查Docker是否正常运行
2025-05-23 01:41:20.912 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - Docker正常运行
2025-05-23 01:41:20.912 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.2 创建PostgresTestContainer实例
2025-05-23 01:41:20.912 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - PostgresTestContainer实例创建成功
2025-05-23 01:41:20.912 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.3 检查容器是否正在运行
2025-05-23 01:41:20.915 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 容器运行状态: true
2025-05-23 01:41:20.915 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.4 获取JDBC连接信息
2025-05-23 01:41:20.915 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - JDBC URL: ******************************************************
2025-05-23 01:41:20.915 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 用户名: test
2025-05-23 01:41:20.915 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 密码: test
2025-05-23 01:41:20.915 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.5 获取数据源
2025-05-23 01:41:20.916 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-18 - Starting...
2025-05-23 01:41:20.931 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-18 - Added connection org.postgresql.jdbc.PgConnection@44aeae34
2025-05-23 01:41:20.931 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-18 - Start completed.
2025-05-23 01:41:20.931 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据源获取成功
2025-05-23 01:41:20.931 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.6 测试数据库连接
2025-05-23 01:41:20.931 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库连接成功
2025-05-23 01:41:20.932 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库产品名称: PostgreSQL
2025-05-23 01:41:20.932 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库产品版本: 17.4 (Debian 17.4-1.pgdg120+2)
2025-05-23 01:41:20.932 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库驱动名称: PostgreSQL JDBC Driver
2025-05-23 01:41:20.932 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库驱动版本: 42.7.5
2025-05-23 01:41:20.932 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.7 创建JdbcTemplate和TransactionTemplate
2025-05-23 01:41:20.932 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - JdbcTemplate和TransactionTemplate创建成功
2025-05-23 01:41:20.932 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.8 创建元数据服务和验证服务
2025-05-23 01:41:20.932 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 01:41:20.932 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.9 初始化表结构
2025-05-23 01:41:20.932 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 01:41:20.933 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 01:41:20.933 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 01:41:20.934 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 01:41:20.934 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:41:20.937 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 01:41:20.937 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 01:41:20.939 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 01:41:20.939 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 01:41:20.945 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 01:41:20.945 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 01:41:20.945 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:41:20.947 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 01:41:20.947 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 01:41:20.948 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 01:41:20.948 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 01:41:20.951 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 01:41:20.951 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 01:41:20.951 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:41:20.952 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 01:41:20.952 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 01:41:20.954 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 01:41:20.954 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 01:41:20.961 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 01:41:20.961 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 01:41:20.961 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 01:41:20.961 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.2 验证表结构是否创建成功
2025-05-23 01:41:20.967 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 已创建的表: [{table_name=instance_registry}, {table_name=worker_id_assignment}, {table_name=encryption_key}]
2025-05-23 01:41:20.968 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 2. 创建 KeyManagementService
2025-05-23 01:41:20.968 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: lease-test, 环境: test, Schema: infra_uid
2025-05-23 01:41:20.968 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - KeyManagementService 创建成功，加密状态: false
2025-05-23 01:41:20.968 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 3. 创建实例管理器
2025-05-23 01:41:20.968 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 01:41:20.968 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 01:41:20.968 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: lease-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AND_NEW, 加密启用: false
2025-05-23 01:41:20.969 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 3
2025-05-23 01:41:20.970 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 3 在数据库中不存在
2025-05-23 01:41:20.970 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 01:41:20.971 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到精确匹配，且恢复策略为创建新实例，跳过模糊匹配
2025-05-23 01:41:20.971 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 01:41:20.972 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 3
2025-05-23 01:41:20.974 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/lease-test-instance-id.dat
2025-05-23 01:41:20.975 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 01:41:20.975 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 2.1 实例管理器创建成功，实例ID: 3
2025-05-23 01:41:20.977 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 已注册的实例: [{instance_unique_id=3, application_name=lease-test, environment=test, instance_group=default, status=ACTIVE, first_registered_at=2025-05-23 01:41:20.972413, last_seen_at=2025-05-23 01:41:20.972413, custom_metadata={"os_arch": "amd64", "os_name": "Linux", "hostname": "long-VirtualBox", "os_version": "5.4.0-91-generic", "mac_addresses": ["BE:CE:01:47:D3:AE", "08:00:27:D6:3A:87"], "fingerprint_hash": "579ce699d22f0350bfc9f8da764f44ffce04ec103bc79545876e86249dd7c46d"}}]
2025-05-23 01:41:20.977 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 3. 创建工作机器ID分配器
2025-05-23 01:41:20.977 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 4. 开始分配工作机器ID
2025-05-23 01:41:20.977 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 01:41:20.977 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 01:41:20.978 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 01:41:20.978 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 3 分配工作机器ID，当前线程: main
2025-05-23 01:41:20.978 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 3 是否已分配工作机器ID
2025-05-23 01:41:20.979 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-23 01:41:20.980 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 3
2025-05-23 01:41:20.981 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 3 分配了新的工作机器ID: 0，当前线程: main
2025-05-23 01:41:20.981 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-23 01:41:20.981 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 01:41:20.981 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 分配的工作机器ID: 0
2025-05-23 01:41:20.982 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 工作机器ID分配记录: [{worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:20.979489, lease_expires_at=2025-05-23 01:42:20.979489, released_at=2025-05-23 01:41:20.75228}]
2025-05-23 01:41:20.982 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5. 测试租约续约 - 通过心跳线程自动续约
2025-05-23 01:41:20.982 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.1 等待心跳线程自动续约前，当前状态
2025-05-23 01:41:20.983 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:20.979489, lease_expires_at=2025-05-23 01:42:20.979489, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:20.983 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-23 01:41:21.989 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.1 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:20.979489, lease_expires_at=2025-05-23 01:42:20.979489, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:21.989 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.2 等待心跳线程自动续约前，当前状态
2025-05-23 01:41:21.991 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:20.979489, lease_expires_at=2025-05-23 01:42:20.979489, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:21.991 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-23 01:41:22.994 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.2 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:20.979489, lease_expires_at=2025-05-23 01:42:20.979489, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:22.994 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.3 等待心跳线程自动续约前，当前状态
2025-05-23 01:41:22.997 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:20.979489, lease_expires_at=2025-05-23 01:42:20.979489, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:22.998 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-23 01:41:23.999 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.3 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:20.979489, lease_expires_at=2025-05-23 01:42:20.979489, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:23.999 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6. 测试手动续约
2025-05-23 01:41:23.999 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.1 手动续约前，当前状态
2025-05-23 01:41:24.000 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:20.979489, lease_expires_at=2025-05-23 01:42:20.979489, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:24.000 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-23 01:41:24.000 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-23 01:41:24.000 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-23 01:41:24.001 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-23 01:41:24.001 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-23 01:41:24.002 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-23 01:41:24.003 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-23 01:41:24.003 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.1 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:24.002008, lease_expires_at=2025-05-23 01:42:24.002008, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:24.004 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-23 01:41:25.016 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.2 手动续约前，当前状态
2025-05-23 01:41:25.032 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:24.002008, lease_expires_at=2025-05-23 01:42:24.002008, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:25.032 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-23 01:41:25.032 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-23 01:41:25.032 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-23 01:41:25.034 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-23 01:41:25.034 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-23 01:41:25.037 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-23 01:41:25.040 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-23 01:41:25.047 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.2 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:25.035586, lease_expires_at=2025-05-23 01:42:25.035586, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:25.047 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-23 01:41:26.048 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.3 手动续约前，当前状态
2025-05-23 01:41:26.064 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:25.035586, lease_expires_at=2025-05-23 01:42:25.035586, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:26.064 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-23 01:41:26.064 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-23 01:41:26.064 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-23 01:41:26.070 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-23 01:41:26.072 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-23 01:41:26.088 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-23 01:41:26.090 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-23 01:41:26.091 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.3 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:26.078098, lease_expires_at=2025-05-23 01:42:26.078098, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:26.091 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-23 01:41:27.091 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7. 测试租约失效后的重新分配
2025-05-23 01:41:27.091 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.1 模拟租约失效 - 将工作机器ID状态设置为AVAILABLE
2025-05-23 01:41:27.094 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 更新结果: 影响行数 = 1
2025-05-23 01:41:27.096 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 更新后状态: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:26.078098, lease_expires_at=2025-05-23 01:42:26.078098, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:27.097 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.2 等待2秒，让租约续约线程发现租约失效
2025-05-23 01:41:29.097 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.3 尝试重新获取工作机器ID
2025-05-23 01:41:29.097 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 直接返回已缓存的工作机器ID: 0
2025-05-23 01:41:29.097 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 重新分配的工作机器ID: 0
2025-05-23 01:41:29.099 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 新工作机器ID记录: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:26.078098, lease_expires_at=2025-05-23 01:42:26.078098, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:29.099 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 8. 关闭心跳线程
2025-05-23 01:41:29.101 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放失败
2025-05-23 01:41:29.101 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 关闭后状态: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-23 01:41:20.979489, last_renewed_at=2025-05-23 01:41:26.078098, lease_expires_at=2025-05-23 01:42:26.078098, released_at=2025-05-23 01:41:20.75228}
2025-05-23 01:41:29.101 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - ===== 租约管理测试完成 =====
2025-05-23 01:41:30.796 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:41:30.796 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:41:30.797 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-23 01:41:30.797 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:41:30.797 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:41:30.797 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:41:30.797 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:41:30.797 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:30.919 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:41:30.920 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:41:30.921 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 01:41:30.921 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:41:30.921 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:41:30.921 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:41:30.921 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:41:30.921 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.093 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.120 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.139 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.139 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.139 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.139 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.139 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.139 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.139 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.140 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.203 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.204 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.204 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.204 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.204 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.204 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.204 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.205 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.255 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.256 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.256 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.256 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.256 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.256 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.256 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 01:41:31.256 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.298 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.298 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:41:31.298 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:41:31.298 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.298 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.298 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.298 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.300 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.300 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:41:31.300 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:41:31.300 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.301 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:41:31.423 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.423 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:41:31.423 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:41:31.423 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.423 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.423 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.423 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.425 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.425 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:41:31.425 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.428 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-23 01:41:31.428 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:41:31.428 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.428 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:41:31.640 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.640 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:41:31.640 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:41:31.640 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.640 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.640 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.641 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.645 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.645 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:41:31.646 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:41:31.646 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.646 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:41:31.705 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.705 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:41:31.705 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:41:31.705 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.705 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.705 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.705 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.706 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.707 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:41:31.707 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:41:31.707 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.707 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:41:31.759 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.759 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 01:41:31.760 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 01:41:31.760 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.760 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.760 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.760 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.761 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 01:41:31.761 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 01:41:31.762 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 01:41:31.762 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 01:41:31.762 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 01:41:32.194 [temp-file-cleanup-hook] DEBUG o.x.c.commons.uid.util.TempFileUtils - 执行临时文件清理关闭钩子
2025-05-23 01:41:32.194 [temp-file-cleanup-hook] DEBUG o.x.c.commons.uid.util.TempFileUtils - 开始清理临时文件，当前注册的临时文件数量: 0
2025-05-23 01:41:32.194 [Thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 01:41:32.195 [Thread-11] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 01:41:32.195 [Thread-13] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 01:41:32.196 [Thread-7] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 01:41:32.197 [Thread-19] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-18 - Failed to validate connection org.postgresql.jdbc.PgConnection@44aeae34 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-23 01:41:32.197 [Thread-16] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 01:41:32.200 [Thread-5] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 01:41:32.200 [Thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-23 01:41:32.201 [Thread-0] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 01:41:32.201 [Thread-0] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-23 01:41:32.201 [Thread-8] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 01:41:32.201 [Thread-8] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-23 01:41:32.201 [Thread-4] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 01:41:32.201 [Thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-23 01:41:32.201 [Thread-6] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 01:41:32.201 [Thread-6] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-23 01:41:32.202 [Thread-10] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 01:41:32.202 [Thread-12] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 01:41:32.202 [Thread-15] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 01:41:32.202 [Thread-14] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 01:41:32.207 [Thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 01:41:32.207 [Thread-18] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 01:41:32.209 [Thread-19] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-18 - Failed to validate connection org.postgresql.jdbc.PgConnection@470901ac (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-23 01:41:32.210 [Thread-1] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 01:41:32.210 [Thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
