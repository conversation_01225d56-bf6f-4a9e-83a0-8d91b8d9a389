# JavaScript组件复用实现方案

## 核心思路：统一的数据绑定和更新机制

基于现有的project_manager_v2.html和nine_grid.html，设计一套JavaScript复用方案，让所有组件都能统一地与后端对接。

---

## 1. 统一数据管理器 (DataManager)

### 核心实现
```javascript
// data-manager.js - 统一数据管理
class DataManager {
    constructor(projectPath = null) {
        this.projectPath = projectPath;
        this.data = new Map();
        this.subscribers = new Map();
        this.wsClient = null;
        this.httpClient = new HttpClient();

        this.setupWebSocket();

        // 如果有项目路径，立即绑定；否则等待后续绑定
        if (projectPath) {
            this.bindProjectPath(projectPath);
        } else {
            // 尝试从localStorage恢复项目路径
            const savedPath = localStorage.getItem('pm_v2_current_project_path');
            if (savedPath) {
                this.setProjectPath(savedPath);
            }
        }
    }

    // 设置项目路径（支持延迟绑定）
    setProjectPath(projectPath) {
        this.projectPath = projectPath;
        this.bindProjectPath(projectPath);
        localStorage.setItem('pm_v2_current_project_path', projectPath);

        // 通知所有订阅者项目路径已变化
        this.notifySubscribers('project_path_changed', projectPath);
    }

    // 绑定项目路径到WebSocket
    bindProjectPath(projectPath) {
        if (this.wsClient && this.wsClient.readyState === WebSocket.OPEN) {
            this.wsClient.send(JSON.stringify({
                type: 'bind_project_path',
                data: { project_path: projectPath }
            }));
        }
    }
    
    // 统一的数据获取方法
    async fetchData(dataType, params = {}) {
        const endpoint = this.getEndpoint(dataType);
        const requestParams = this.getRequestParams(params);
        const cacheKey = `${dataType}_${JSON.stringify(requestParams)}`;

        try {
            // 使用POST方式传递项目路径
            const response = await this.httpClient.post(endpoint, requestParams);
            this.setData(cacheKey, response.data);
            return response.data;
        } catch (error) {
            console.error(`Failed to fetch ${dataType}:`, error);
            throw error;
        }
    }
    
    // 统一的数据设置方法
    setData(key, value) {
        const oldValue = this.data.get(key);
        this.data.set(key, value);
        
        // 通知所有订阅者
        const subscribers = this.subscribers.get(key) || [];
        subscribers.forEach(callback => {
            try {
                callback(value, oldValue);
            } catch (error) {
                console.error('Subscriber error:', error);
            }
        });
    }
    
    // 统一的数据订阅方法
    subscribe(key, callback) {
        if (!this.subscribers.has(key)) {
            this.subscribers.set(key, []);
        }
        this.subscribers.get(key).push(callback);
        
        // 如果已有数据，立即调用回调
        if (this.data.has(key)) {
            callback(this.data.get(key));
        }
        
        // 返回取消订阅函数
        return () => {
            const subscribers = this.subscribers.get(key);
            const index = subscribers.indexOf(callback);
            if (index > -1) {
                subscribers.splice(index, 1);
            }
        };
    }
    
    // WebSocket事件处理
    setupWebSocket() {
        this.wsClient = new WebSocket(`ws://localhost:25526/ws/pm-v2`);

        this.wsClient.onopen = () => {
            // 连接成功后，如果有项目路径则绑定
            if (this.projectPath) {
                this.bindProjectPath(this.projectPath);
            }
        };

        this.wsClient.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleWebSocketMessage(message);
        };
    }
    
    handleWebSocketMessage(message) {
        const { type, data } = message;
        
        // 根据事件类型更新对应的数据
        switch (type) {
            case 'stage_progress_update':
                this.setData('progress', data);
                break;
            case 'constraint_created':
                this.updateConstraintsList(data);
                break;
            case 'reliability_score_update':
                this.setData('risk_assessment', data);
                break;
            // ... 其他事件类型
        }
    }
    
    // 获取API端点
    getEndpoint(dataType) {
        if (!this.projectPath) {
            throw new Error('Project path not set. Call setProjectPath() first.');
        }

        // 使用POST方式传递项目路径，避免URL编码问题
        const endpoints = {
            'progress': `/workspace/progress`,
            'risk_assessment': `/workspace/risk-assessment`,
            'constraints': `/workspace/constraints`,
            'knowledge_graph': `/workspace/knowledge-graph`,
            'manager_status': `/workspace/manager-status`,
            'algorithm_logs': `/workspace/algorithm-logs`,
            'deliverables': `/workspace/deliverables`
        };
        return endpoints[dataType];
    }

    // 获取API请求参数（包含项目路径）
    getRequestParams(params = {}) {
        return {
            ...params,
            project_path: this.projectPath
        };
    }
}
```

---

## 2. 统一组件基类 (BaseComponent)

### 核心实现
```javascript
// base-component.js - 组件基类
class BaseComponent {
    constructor(containerId, dataManager, config = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.dataManager = dataManager;
        this.config = config;
        this.subscriptions = [];
        this.isDestroyed = false;
        this.isProjectBound = false;

        if (!this.container) {
            throw new Error(`Container ${containerId} not found`);
        }

        // 监听项目路径变化
        this.dataManager.subscribe('project_path_changed', (projectPath) => {
            this.isProjectBound = !!projectPath;
            this.onProjectBound(projectPath);
        });
    }

    // 项目绑定时的回调（子类可重写）
    onProjectBound(projectPath) {
        // 项目绑定后重新渲染
        this.render();
    }
    
    // 初始化组件
    async init() {
        try {
            await this.loadData();
            this.setupSubscriptions();
            this.render();
            this.bindEvents();
        } catch (error) {
            this.handleError(error);
        }
    }
    
    // 加载初始数据 - 子类重写
    async loadData() {
        const dataTypes = this.getDataTypes();
        const promises = dataTypes.map(type => 
            this.dataManager.fetchData(type, this.getDataParams(type))
        );
        await Promise.all(promises);
    }
    
    // 设置数据订阅
    setupSubscriptions() {
        const dataTypes = this.getDataTypes();
        
        dataTypes.forEach(type => {
            const unsubscribe = this.dataManager.subscribe(type, (data, oldData) => {
                if (!this.isDestroyed) {
                    this.onDataUpdate(type, data, oldData);
                }
            });
            this.subscriptions.push(unsubscribe);
        });
    }
    
    // 数据更新处理 - 子类重写
    onDataUpdate(dataType, data, oldData) {
        this.render();
    }
    
    // 渲染组件 - 子类必须实现
    render() {
        throw new Error('render method must be implemented');
    }
    
    // 绑定事件 - 子类重写
    bindEvents() {
        // 默认实现为空
    }
    
    // 错误处理
    handleError(error) {
        console.error(`Error in component ${this.containerId}:`, error);
        this.renderError(error.message);
    }
    
    // 渲染错误状态
    renderError(message) {
        this.container.innerHTML = `
            <div style="color: #F44336; text-align: center; padding: 1rem;">
                <div>⚠️ 加载失败</div>
                <div style="font-size: 0.8rem; margin-top: 0.5rem;">${message}</div>
            </div>
        `;
    }
    
    // 销毁组件
    destroy() {
        this.isDestroyed = true;
        this.subscriptions.forEach(unsubscribe => unsubscribe());
        this.subscriptions = [];
    }
    
    // 抽象方法 - 子类必须实现
    getDataTypes() {
        throw new Error('getDataTypes method must be implemented');
    }
    
    getDataParams(type) {
        return {};
    }
}
```

---

## 3. 具体组件实现示例

### 进度监控组件
```javascript
// progress-component.js
class ProgressComponent extends BaseComponent {
    getDataTypes() {
        return ['progress'];
    }
    
    onDataUpdate(dataType, data) {
        if (dataType === 'progress') {
            this.updateStageProgress(data.current_stage);
            this.updateStageZeroMetrics(data.stage_zero_metrics);
            this.updateKeyMetrics(data.key_metrics);
        }
    }
    
    render() {
        const progressData = this.dataManager.data.get('progress');
        if (!progressData) return;
        
        this.container.innerHTML = `
            <div class="progress-container">
                <div id="stage-progress"></div>
                <div id="stage-zero-metrics"></div>
                <div id="key-metrics"></div>
            </div>
        `;
        
        this.updateStageProgress(progressData.current_stage);
        this.updateStageZeroMetrics(progressData.stage_zero_metrics);
        this.updateKeyMetrics(progressData.key_metrics);
    }
    
    updateStageProgress(stageData) {
        const container = document.getElementById('stage-progress');
        if (!container) return;
        
        // 复用现有的updateStageProgress逻辑
        const stages = ['阶段零：标准化与预验证', '阶段一：全局契约生成', ...];
        container.innerHTML = stages.map((stage, index) => `
            <div class="stage-item ${index === stageData.stage_number ? 'current' : 'pending'}">
                <span class="status-indicator ${stageData.status_indicator}"></span>
                <span>${stage}</span>
            </div>
        `).join('');
    }
    
    updateStageZeroMetrics(metricsData) {
        const container = document.getElementById('stage-zero-metrics');
        if (!container) return;
        
        container.innerHTML = `
            <div class="metrics-title">🛡️ 阶段零预验证指标</div>
            <div class="metric-item">
                <span class="metric-label">预验证通过率</span>
                <span class="metric-value success">${metricsData.pre_validation_pass_rate}%</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">冲突预防数量</span>
                <span class="metric-value info">${metricsData.conflict_prevention_count}</span>
            </div>
        `;
    }
    
    updateKeyMetrics(keyMetrics) {
        const container = document.getElementById('key-metrics');
        if (!container) return;
        
        container.innerHTML = `
            <div class="key-metrics">
                <div class="metric">约束发现: ${keyMetrics.atomic_constraints_discovered}</div>
                <div class="metric">契约生成: ${keyMetrics.global_contracts_generated}</div>
                <div class="metric">可靠性评分: ${keyMetrics.current_reliability_score}%</div>
            </div>
        `;
    }
}
```

### 约束审查组件
```javascript
// constraint-component.js
class ConstraintComponent extends BaseComponent {
    constructor(containerId, dataManager, config) {
        super(containerId, dataManager, config);
        this.selectedConstraintId = null;
    }
    
    getDataTypes() {
        return ['constraints'];
    }
    
    onDataUpdate(dataType, data) {
        if (dataType === 'constraints') {
            this.renderConstraintsList(data);
        } else if (dataType === 'selected_constraint') {
            this.renderConstraintDetail(data);
        }
    }
    
    render() {
        this.container.innerHTML = `
            <div class="constraint-container">
                <div id="constraints-list"></div>
                <div id="constraint-detail"></div>
            </div>
        `;
        
        const constraintsData = this.dataManager.data.get('constraints');
        if (constraintsData) {
            this.renderConstraintsList(constraintsData);
        }
    }
    
    renderConstraintsList(constraintsData) {
        const container = document.getElementById('constraints-list');
        if (!container) return;
        
        container.innerHTML = constraintsData.constraints.map(constraint => `
            <div class="constraint-item" onclick="window.selectConstraint('${constraint.id}')">
                <div class="constraint-id">${constraint.id}</div>
                <div class="constraint-category">${constraint.category}</div>
                <div class="constraint-description">${constraint.description}</div>
            </div>
        `).join('');
    }
    
    async selectConstraint(constraintId) {
        this.selectedConstraintId = constraintId;
        try {
            const constraintData = await this.dataManager.fetchData(
                'constraint_detail', 
                { constraintId }
            );
            this.renderConstraintDetail(constraintData);
        } catch (error) {
            this.handleError(error);
        }
    }
    
    renderConstraintDetail(constraintData) {
        const container = document.getElementById('constraint-detail');
        if (!container) return;
        
        // 复用现有的约束详情渲染逻辑
        const paramsHtml = Object.entries(constraintData.params).map(([key, value]) => 
            `<div class="param-node">
                <span class="param-key">${key}</span>
                <span class="param-value">${value}</span>
            </div>`
        ).join('');
        
        container.innerHTML = `
            <div class="constraint-detail-enhanced">
                <div class="constraint-header">
                    <span class="constraint-id">${constraintData.id}</span>
                    <span class="constraint-category">${constraintData.category}</span>
                </div>
                <div class="constraint-section">
                    <div class="section-title">📦 参数结构</div>
                    <div class="params-tree">${paramsHtml}</div>
                </div>
                <div class="constraint-section">
                    <div class="section-title">📝 描述</div>
                    <div class="description">${constraintData.description}</div>
                </div>
            </div>
        `;
    }
    
    bindEvents() {
        // 将selectConstraint方法绑定到全局，供HTML onclick调用
        window.selectConstraint = (constraintId) => {
            this.selectConstraint(constraintId);
        };
    }
}
```

---

## 4. 组件工厂和应用初始化

### 组件工厂
```javascript
// component-factory.js
class ComponentFactory {
    static createComponent(type, containerId, dataManager, config) {
        const components = {
            'progress': ProgressComponent,
            'risk': RiskAssessmentComponent,
            'manager': ManagerStatusComponent,
            'algorithm': AlgorithmThinkingComponent,
            'constraint': ConstraintComponent,
            'knowledge': KnowledgeGraphComponent,
            'control': ControlPanelComponent,
            'deliverables': DeliverablesComponent
        };
        
        const ComponentClass = components[type];
        if (!ComponentClass) {
            throw new Error(`Unknown component type: ${type}`);
        }
        
        return new ComponentClass(containerId, dataManager, config);
    }
}
```

### 应用初始化
```javascript
// app-init.js
class ProjectManagerApp {
    constructor(projectId) {
        this.projectId = projectId;
        this.dataManager = new DataManager(projectId);
        this.components = new Map();
    }
    
    async init() {
        // 定义组件配置
        const componentConfigs = [
            { type: 'progress', containerId: 'progress-area' },
            { type: 'risk', containerId: 'risk-area' },
            { type: 'manager', containerId: 'manager-area' },
            { type: 'algorithm', containerId: 'algorithm-area' },
            { type: 'constraint', containerId: 'constraint-area' },
            { type: 'knowledge', containerId: 'knowledge-area' },
            { type: 'control', containerId: 'control-area' },
            { type: 'deliverables', containerId: 'deliverables-area' }
        ];
        
        // 创建并初始化所有组件
        for (const config of componentConfigs) {
            try {
                const component = ComponentFactory.createComponent(
                    config.type,
                    config.containerId,
                    this.dataManager,
                    config
                );
                
                await component.init();
                this.components.set(config.type, component);
                
                console.log(`Component ${config.type} initialized successfully`);
            } catch (error) {
                console.error(`Failed to initialize component ${config.type}:`, error);
            }
        }
    }
    
    destroy() {
        this.components.forEach(component => component.destroy());
        this.components.clear();
        
        if (this.dataManager.wsClient) {
            this.dataManager.wsClient.close();
        }
    }
}

// 全局初始化
let app;

document.addEventListener('DOMContentLoaded', async () => {
    const projectId = getCurrentProjectId(); // 从URL或其他地方获取
    app = new ProjectManagerApp(projectId);
    
    try {
        await app.init();
        console.log('Project Manager V2 initialized successfully');
    } catch (error) {
        console.error('Failed to initialize Project Manager V2:', error);
    }
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (app) {
        app.destroy();
    }
});
```

---

## 5. HTTP客户端实现

### 简化的HTTP客户端
```javascript
// http-client.js
class HttpClient {
    constructor(baseURL = 'http://localhost:25526/api/v2') {
        this.baseURL = baseURL;
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }
    
    async request(method, url, data = null, headers = {}) {
        const fullURL = url.startsWith('http') ? url : `${this.baseURL}${url}`;
        const config = {
            method,
            headers: { ...this.defaultHeaders, ...headers },
        };
        
        if (data) {
            config.body = JSON.stringify(data);
        }
        
        try {
            const response = await fetch(fullURL, config);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error(`HTTP request failed: ${method} ${url}`, error);
            throw error;
        }
    }
    
    get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullURL = queryString ? `${url}?${queryString}` : url;
        return this.request('GET', fullURL);
    }
    
    post(url, data) {
        return this.request('POST', url, data);
    }
    
    put(url, data) {
        return this.request('PUT', url, data);
    }
    
    delete(url) {
        return this.request('DELETE', url);
    }
}
```

---

## 6. 使用方式

### HTML中的使用
```html
<!-- 在project_manager_v2.html中 -->
<script src="static/js/http-client.js"></script>
<script src="static/js/data-manager.js"></script>
<script src="static/js/base-component.js"></script>
<script src="static/js/components/progress-component.js"></script>
<script src="static/js/components/constraint-component.js"></script>
<!-- ... 其他组件 -->
<script src="static/js/component-factory.js"></script>
<script src="static/js/app-init.js"></script>
```

### 现有代码的迁移
```javascript
// 将现有的函数包装成组件方法
class ExistingFunctionsWrapper {
    static wrapFunction(originalFunction, component) {
        return function(...args) {
            try {
                return originalFunction.apply(component, args);
            } catch (error) {
                component.handleError(error);
            }
        };
    }
}

// 在组件中使用现有函数
class ProgressComponent extends BaseComponent {
    constructor(containerId, dataManager, config) {
        super(containerId, dataManager, config);
        
        // 包装现有函数
        this.updateStageProgress = ExistingFunctionsWrapper.wrapFunction(
            window.updateStageProgress, 
            this
        );
    }
}
```

这套方案的核心优势：
1. **最小化改动** - 复用现有的HTML结构和CSS样式
2. **统一数据流** - 所有组件都通过DataManager获取和更新数据
3. **简单易懂** - 基于类继承的简单模式，容易理解和维护
4. **渐进迁移** - 可以逐步将现有函数迁移到组件中
5. **错误隔离** - 单个组件的错误不会影响其他组件
