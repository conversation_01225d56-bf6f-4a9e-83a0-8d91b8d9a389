#!/usr/bin/env python3
"""
CAP vs Thinking 对比测试
验证V3模型使用CAP是否比强制thinking更有效
"""

import json
import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# 添加父目录到路径以导入现有模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from thinking_quality_fusion_research import SimpleAPIClient, V4_5_ThinkingAnalyzer

class CAPTestFramework:
    """CAP测试框架"""
    
    def __init__(self):
        self.api_client = SimpleAPIClient()
        self.analyzer = V4_5_ThinkingAnalyzer()
        
    def create_cap_prompt(self, base_prompt: str) -> str:
        """创建CAP增强提示"""
        cap_template = """请按以下认知增强步骤(CAP)完成任务：

🔍 **步骤1：需求解构分析**
- 识别核心问题和关键约束
- 分析技术背景和业务目标
- 明确成功标准和评估维度

🧠 **步骤2：多角度探索**
- 从架构师、性能工程师、质量保障等专家视角分析
- 对比不同技术方案的优劣
- 识别潜在风险和机会点

⚖️ **步骤3：批判性评估**
- 分析现有方案的局限性
- 识别可能的技术债务和瓶颈
- 评估方案的可扩展性和维护性

🚀 **步骤4：创新综合设计**
- 结合前面分析，设计创新解决方案
- 提供具体的技术实现细节
- 包含性能指标和验证方法

📊 **步骤5：方案验证总结**
- 总结关键技术决策和权衡
- 提供量化的性能预期
- 说明实施路径和风险控制

请在每个步骤中详细展示你的分析推理过程，确保逻辑清晰、论证充分。

**原始任务：**
{base_prompt}
"""
        return cap_template.format(base_prompt=base_prompt)
    
    def run_comparison_test(self) -> Dict[str, Any]:
        """运行CAP vs 原始提示对比测试"""
        
        # 测试用例
        base_prompt = """设计一个基于XKongCloud Commons Nexus微内核架构的Thinking质量评估系统。

要求：
1. 基于第一性原理分析微内核架构的优势
2. 从多个专家视角进行设计考量
3. 批判性分析现有方案的局限性
4. 提出创新的插件化评估方案
5. 确保系统性能达到以下指标：
   - 启动时间 ≤ 1000ms
   - 事件处理能力 ≥ 10000 events/s
   - 内存使用 ≤ 512MB基准"""
        
        # 创建CAP增强提示
        cap_prompt = self.create_cap_prompt(base_prompt)
        
        results = {
            "test_timestamp": datetime.now().isoformat(),
            "base_prompt_test": {},
            "cap_prompt_test": {},
            "comparison_analysis": {}
        }
        
        # 测试V3模型
        model = "deepseek-ai/DeepSeek-V3-0324"
        
        print("🔬 CAP vs Thinking 对比测试")
        print("=" * 80)
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🤖 测试模型: {model}")
        print()
        
        # 测试1：原始提示
        print("📋 测试1：原始提示（当前方式）")
        print("-" * 60)
        
        base_result = self.api_client.call_api(model, base_prompt)
        if base_result["success"]:
            base_content = base_result.get("content", "")
            base_analysis = self.analyzer.comprehensive_analysis(base_content)
            
            results["base_prompt_test"] = {
                "api_response": base_result,
                "analysis": base_analysis,
                "content_length": len(base_content),
                "reasoning_length": len(base_result.get("reasoning_content", "") or "")
            }
            
            print(f"📊 原始提示得分: {base_analysis['overall_score']:.1f}分")
            print(f"📝 内容长度: {len(base_content)}字符")
            print(f"🧠 推理长度: {len(base_result.get('reasoning_content', '') or '')}字符")
        
        print("\n" + "="*60 + "\n")
        
        # 测试2：CAP增强提示
        print("📋 测试2：CAP增强提示（优化方式）")
        print("-" * 60)
        
        cap_result = self.api_client.call_api(model, cap_prompt)
        if cap_result["success"]:
            cap_content = cap_result.get("content", "")
            cap_analysis = self.analyzer.comprehensive_analysis(cap_content)
            
            results["cap_prompt_test"] = {
                "api_response": cap_result,
                "analysis": cap_analysis,
                "content_length": len(cap_content),
                "reasoning_length": len(cap_result.get("reasoning_content", "") or "")
            }
            
            print(f"📊 CAP提示得分: {cap_analysis['overall_score']:.1f}分")
            print(f"📝 内容长度: {len(cap_content)}字符")
            print(f"🧠 推理长度: {len(cap_result.get('reasoning_content', '') or '')}字符")
        
        # 对比分析
        if base_result["success"] and cap_result["success"]:
            self.generate_comparison_analysis(results)
        
        return results
    
    def generate_comparison_analysis(self, results: Dict[str, Any]):
        """生成对比分析"""
        base_score = results["base_prompt_test"]["analysis"]["overall_score"]
        cap_score = results["cap_prompt_test"]["analysis"]["overall_score"]
        
        improvement = cap_score - base_score
        improvement_pct = (improvement / base_score) * 100 if base_score > 0 else 0
        
        print("\n" + "="*60)
        print("📈 对比分析结果")
        print("="*60)
        print(f"📊 得分对比:")
        print(f"   原始提示: {base_score:.1f}分")
        print(f"   CAP提示:  {cap_score:.1f}分")
        print(f"   提升幅度: {improvement:+.1f}分 ({improvement_pct:+.1f}%)")
        
        # 多维度对比
        base_multi = results["base_prompt_test"]["analysis"]["multi_dimensional_scores"]
        cap_multi = results["cap_prompt_test"]["analysis"]["multi_dimensional_scores"]
        
        print(f"\n📈 多维度对比:")
        for dimension in base_multi.keys():
            base_dim = base_multi[dimension]
            cap_dim = cap_multi[dimension]
            dim_improvement = cap_dim - base_dim
            dim_improvement_pct = (dim_improvement / base_dim) * 100 if base_dim > 0 else 0
            print(f"   {dimension.upper():<12} | 原始: {base_dim:.1f} | CAP: {cap_dim:.1f} | 提升: {dim_improvement:+.1f} ({dim_improvement_pct:+.1f}%)")
        
        # 结论
        if improvement > 0:
            print(f"\n✅ 结论: CAP方法对V3模型有正优化效果")
            print(f"   建议: V3模型应使用CAP而非强制thinking")
        else:
            print(f"\n❌ 结论: CAP方法未显示明显优势")
            print(f"   建议: 需要进一步优化CAP策略")
        
        results["comparison_analysis"] = {
            "score_improvement": improvement,
            "improvement_percentage": improvement_pct,
            "dimension_improvements": {
                dim: cap_multi[dim] - base_multi[dim] 
                for dim in base_multi.keys()
            },
            "conclusion": "positive" if improvement > 0 else "negative"
        }

def main():
    """主函数"""
    tester = CAPTestFramework()
    results = tester.run_comparison_test()
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"cap_vs_thinking_test_report_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试报告已保存: {filename}")

if __name__ == "__main__":
    main()
