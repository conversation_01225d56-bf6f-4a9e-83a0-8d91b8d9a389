#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相对导入修复脚本
Relative Import Fixer Script

基于扫描结果，自动修复Python文件中的相对导入语句
将相对导入转换为绝对导入

作者：AI架构修复团队
日期：2025-01-18
版本：v1.0
"""

import os
import re
import json
import shutil
from datetime import datetime
from typing import List, Dict, Any, Tuple

class RelativeImportFixer:
    """相对导入修复器"""
    
    def __init__(self, base_dir: str = "tools/ace/src"):
        self.base_dir = base_dir
        self.backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.fix_results = []
        
        # 包路径映射表
        self.package_mapping = {
            'python_host': 'python_host',
            'algorithms': 'algorithms',
            'v4_algorithms': 'v4_algorithms',
            'api_management': 'api_management',
            'database': 'database',
            'configuration_center': 'configuration_center',
            'task_interfaces': 'task_interfaces',
            'project_container': 'project_container',
            'meeting_directory': 'meeting_directory',
            'four_layer_meeting_system': 'four_layer_meeting_system',
            'web_interface': 'web_interface',
            'tests': 'tests'
        }
    
    def create_backup(self, file_path: str) -> str:
        """创建文件备份"""
        try:
            # 确保备份目录存在
            backup_full_dir = os.path.join(os.path.dirname(file_path), self.backup_dir)
            os.makedirs(backup_full_dir, exist_ok=True)
            
            # 创建备份文件
            backup_file = os.path.join(backup_full_dir, os.path.basename(file_path))
            shutil.copy2(file_path, backup_file)
            
            return backup_file
        except Exception as e:
            print(f"❌ 创建备份失败: {file_path}, 错误: {e}")
            return ""
    
    def get_absolute_import(self, relative_import: str, current_package: str) -> str:
        """将相对导入转换为绝对导入"""
        
        # 解析相对导入语句
        patterns = [
            (r'from\s+\.([a-zA-Z_][a-zA-Z0-9_]*)\s+import\s+(.+)', 1),      # from .module import ...
            (r'from\s+\.\.([a-zA-Z_][a-zA-Z0-9_]*)\s+import\s+(.+)', 2),    # from ..module import ...
            (r'from\s+\.\.\.\s*([a-zA-Z_][a-zA-Z0-9_]*)\s+import\s+(.+)', 3), # from ...module import ...
            (r'from\s+\.\s+import\s+(.+)', 0),                               # from . import ...
            (r'from\s+\.\.\s+import\s+(.+)', 1),                             # from .. import ...
        ]
        
        for pattern, level in patterns:
            match = re.match(pattern, relative_import.strip())
            if match:
                if level == 0:  # from . import ...
                    imports = match.group(1)
                    if current_package:
                        return f"from {current_package} import {imports}"
                    else:
                        return relative_import  # 根级别，保持不变
                
                elif level == 1:  # from .module import ... 或 from .. import ...
                    if len(match.groups()) == 2:  # from .module import ...
                        module, imports = match.groups()
                        if current_package:
                            return f"from {current_package}.{module} import {imports}"
                        else:
                            return f"from {module} import {imports}"
                    else:  # from .. import ...
                        imports = match.group(1)
                        package_parts = current_package.split('.') if current_package else []
                        if len(package_parts) > 0:
                            parent_package = '.'.join(package_parts[:-1])
                            if parent_package:
                                return f"from {parent_package} import {imports}"
                        return relative_import  # 无法确定父包，保持不变
                
                elif level == 2:  # from ..module import ...
                    module, imports = match.groups()
                    package_parts = current_package.split('.') if current_package else []
                    if len(package_parts) > 0:
                        parent_package = '.'.join(package_parts[:-1])
                        if parent_package:
                            return f"from {parent_package}.{module} import {imports}"
                    return relative_import  # 无法确定父包，保持不变
        
        return relative_import  # 无法解析，保持不变
    
    def fix_file(self, file_path: str, relative_imports: List[Dict[str, Any]]) -> Dict[str, Any]:
        """修复单个文件的相对导入"""
        fix_result = {
            'file_path': file_path,
            'backup_created': False,
            'fixes_applied': [],
            'errors': [],
            'success': False
        }
        
        try:
            # 创建备份
            backup_file = self.create_backup(file_path)
            if backup_file:
                fix_result['backup_created'] = True
                fix_result['backup_path'] = backup_file
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # 确定当前包路径
            rel_path = os.path.relpath(file_path, self.base_dir)
            package_parts = rel_path.replace('\\', '/').split('/')[:-1]  # 去掉文件名
            current_package = '.'.join(package_parts) if package_parts else ''
            
            # 应用修复
            modified = False
            for import_info in relative_imports:
                line_num = import_info['line_number'] - 1  # 转换为0基索引
                original_line = lines[line_num]
                
                # 获取绝对导入
                absolute_import = self.get_absolute_import(
                    import_info['original_line'], 
                    current_package
                )
                
                if absolute_import != import_info['original_line']:
                    # 保持原有的缩进
                    indent = len(original_line) - len(original_line.lstrip())
                    new_line = ' ' * indent + absolute_import
                    
                    lines[line_num] = new_line
                    modified = True
                    
                    fix_result['fixes_applied'].append({
                        'line_number': import_info['line_number'],
                        'original': import_info['original_line'],
                        'fixed': absolute_import
                    })
            
            # 保存修复后的文件
            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(lines))
                
                fix_result['success'] = True
                print(f"✅ 已修复文件: {file_path} ({len(fix_result['fixes_applied'])}个导入)")
            else:
                fix_result['success'] = True
                print(f"ℹ️  文件无需修复: {file_path}")
        
        except Exception as e:
            error_msg = f"修复文件失败: {e}"
            fix_result['errors'].append(error_msg)
            print(f"❌ {file_path}: {error_msg}")
        
        return fix_result
    
    def fix_from_scan_report(self, scan_report_file: str) -> Dict[str, Any]:
        """基于扫描报告修复相对导入"""
        print(f"🔧 开始基于扫描报告修复相对导入: {scan_report_file}")
        
        try:
            # 读取扫描报告
            with open(scan_report_file, 'r', encoding='utf-8') as f:
                scan_report = json.load(f)
            
            scan_results = scan_report.get('scan_results', [])
            
            if not scan_results:
                print("ℹ️  扫描报告中没有发现相对导入问题")
                return {'success': True, 'fixes_applied': 0}
            
            print(f"📋 发现 {len(scan_results)} 个文件需要修复")
            
            # 按优先级排序（__init__.py文件优先）
            scan_results.sort(key=lambda x: (
                0 if x['relative_path'].endswith('__init__.py') else 1,
                x['relative_path']
            ))
            
            # 逐个修复文件
            total_fixes = 0
            for result in scan_results:
                file_path = result['file_path']
                relative_imports = result['relative_imports']
                
                fix_result = self.fix_file(file_path, relative_imports)
                self.fix_results.append(fix_result)
                
                if fix_result['success']:
                    total_fixes += len(fix_result['fixes_applied'])
            
            # 生成修复报告
            fix_report = self._generate_fix_report()
            
            print(f"🎉 修复完成！总共修复了 {total_fixes} 个相对导入")
            return fix_report
        
        except Exception as e:
            print(f"❌ 修复过程失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _generate_fix_report(self) -> Dict[str, Any]:
        """生成修复报告"""
        successful_fixes = [r for r in self.fix_results if r['success']]
        failed_fixes = [r for r in self.fix_results if not r['success']]
        
        total_imports_fixed = sum(len(r['fixes_applied']) for r in successful_fixes)
        
        report = {
            'fix_metadata': {
                'fix_time': datetime.now().isoformat(),
                'base_directory': self.base_dir,
                'fixer_version': 'v1.0'
            },
            'statistics': {
                'total_files_processed': len(self.fix_results),
                'successful_fixes': len(successful_fixes),
                'failed_fixes': len(failed_fixes),
                'total_imports_fixed': total_imports_fixed
            },
            'fix_results': self.fix_results,
            'summary': {
                'most_fixed_files': sorted(
                    successful_fixes,
                    key=lambda x: len(x['fixes_applied']),
                    reverse=True
                )[:5],
                'failed_files': [r['file_path'] for r in failed_fixes]
            }
        }
        
        return report
    
    def save_fix_report(self, output_file: str = None) -> str:
        """保存修复报告"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"relative_import_fix_report_{timestamp}.json"
        
        fix_report = self._generate_fix_report()
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(fix_report, f, indent=2, ensure_ascii=False)
            
            print(f"📄 修复报告已保存: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"❌ 保存修复报告失败: {e}")
            return ""

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("❌ 使用方法: python fix_relative_imports.py <scan_report_file>")
        print("   示例: python fix_relative_imports.py relative_import_scan_report_20250118_143022.json")
        return
    
    scan_report_file = sys.argv[1]
    
    if not os.path.exists(scan_report_file):
        print(f"❌ 扫描报告文件不存在: {scan_report_file}")
        return
    
    print("🚀 启动相对导入修复器")
    
    # 创建修复器实例
    fixer = RelativeImportFixer()
    
    # 执行修复
    fix_report = fixer.fix_from_scan_report(scan_report_file)
    
    # 保存修复报告
    if fix_report.get('success', False):
        fixer.save_fix_report()
        
        print(f"\n💡 后续建议:")
        print(f"1. 运行验证脚本确认修复效果")
        print(f"2. 启动服务器测试功能是否正常")
        print(f"3. 如有问题，可从备份文件恢复")
    else:
        print(f"\n❌ 修复失败，请检查错误信息")

if __name__ == "__main__":
    main()
