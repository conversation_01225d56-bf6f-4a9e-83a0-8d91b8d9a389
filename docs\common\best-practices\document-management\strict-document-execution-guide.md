# 严格文档驱动执行指南

## 概述

本指南定义了XKongCloud项目中严格文档驱动执行的标准和要求，确保所有AI实现严格遵循文档规范，防止出现文档外的参数、逻辑或配置。

## 核心原则

### 1. 零容忍文档边界政策
- **绝对禁止**实现任何文档中未明确定义的参数
- **绝对禁止**使用任何文档中未指定的方法或逻辑
- **绝对禁止**参考历史代码或.bak文件进行实现

### 2. 文档完整性要求
- 执行前必须验证文档的完整性和清晰度
- 文档不完整或模糊时必须拒绝执行
- 要求人类完善文档后再继续执行

### 3. 备份文件合规性
- 所有备份文件必须符合当前文档要求
- 备份文件不得包含任何文档外的内容
- 创建备份前必须进行文档合规性验证

## 执行流程

### 阶段1：文档验证
1. **完整性检查**
   - 验证执行计划文档是否完整
   - 确认所有必要的参数都有明确定义
   - 检查文档逻辑一致性

2. **边界确认**
   - 明确文档定义的执行范围
   - 识别文档外的任何内容
   - 确认执行边界

### 阶段2：实现验证
1. **参数验证**
   - 每个参数必须在文档中明确定义
   - 参数名称必须与文档完全一致
   - 参数用法必须符合文档规范

2. **方法验证**
   - 每个方法必须在实施计划中指定
   - 方法签名必须与文档一致
   - 方法逻辑必须符合文档描述

3. **配置验证**
   - 每个配置项必须在设计规范中记录
   - 配置值必须符合文档要求
   - 配置结构必须与文档一致

### 阶段3：备份验证
1. **内容检查**
   - 验证备份文件内容符合文档规范
   - 移除任何未记录的参数或逻辑
   - 确保备份文件的合规性

2. **历史代码隔离**
   - 禁止在备份中包含文档外的历史模式
   - 移除所有未记录的遗留代码
   - 确保备份文件的纯净性

## 违规处理

### 立即停止条件
- 发现任何未记录的参数实现
- 检测到文档外的历史代码使用
- 备份文件包含未记录的内容

### 违规报告格式
```
违规类型: [参数违规/方法违规/配置违规/备份违规]
违规位置: [文件名:行号]
未记录内容: [具体的违规内容]
所需文档: [应该记录该内容的文档位置]
修复措施: [解决违规的具体行动]
```

### 修复流程
1. 立即停止当前执行
2. 生成详细违规报告
3. 上报人类决策者
4. 要求文档更新或执行范围调整
5. 重新验证后继续执行

## 最佳实践

### 执行前检查清单
- [ ] 文档完整性已验证
- [ ] 所有参数都在文档中明确定义
- [ ] 所有方法都在实施计划中指定
- [ ] 所有配置都在设计规范中记录
- [ ] 没有引用任何历史代码或.bak文件
- [ ] 执行范围严格限制在文档定义内

### 备份文件检查清单
- [ ] 备份内容已与文档规范对比验证
- [ ] 所有未记录的参数已移除
- [ ] 所有未记录的方法已移除
- [ ] 所有历史代码模式已清理
- [ ] 备份文件合规性已确认

## 工具和命令

### 自动验证命令
- `@STRICT_DOCUMENT_BOUNDARY_CHECK`: 严格文档边界检查
- `@BACKUP_FILE_COMPLIANCE_CHECK`: 备份文件合规性检查
- `@PARAMETER_DOCUMENTATION_VALIDATION`: 参数文档验证
- `@HISTORICAL_CODE_ISOLATION_CHECK`: 历史代码隔离检查

### 手动验证步骤
1. 逐行对比实现代码与文档规范
2. 验证每个参数的文档来源
3. 确认每个方法的文档支持
4. 检查备份文件的合规性

## 案例分析

### 典型违规案例：app.environment参数
**问题**: AI在PostgreSQL迁移过程中实现了文档中未定义的`app.environment`参数
**违规原因**: 
- 参数未在执行文档中明确定义
- 参考了.bak文件中的历史代码
- 违反了严格文档边界原则

**正确做法**:
- 只实现文档中明确要求的`uid.instance.environment`参数
- 完全忽略.bak文件中的历史代码
- 严格按照文档规范执行

## 持续改进

### 监控指标
- 文档边界违规率 = 0%
- 未记录参数实现率 = 0%
- 历史代码引用率 = 0%
- 备份文件合规率 = 100%

### 定期审查
- 每月审查文档驱动执行的合规性
- 分析违规案例并改进约束机制
- 更新最佳实践指南
- 强化AI记忆系统的约束能力
