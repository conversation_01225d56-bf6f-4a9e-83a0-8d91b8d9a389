# V4 - 版本一致性检测引擎

## 📋 实施概述
**文档ID**: V4-PLAN-006  
**阶段**: 版本一致性检测引擎实现  
**置信度**: 95%  

## 🎯 核心目标
实现V4版本一致性检测引擎，确保设计文档、代码实现、测试用例和配置文件之间的版本一致性，提供自动检测、冲突识别和解决建议功能。

## 🏗️ 引擎架构设计

### 核心组件结构
```
engines/version_consistency/
├── __init__.py
├── main_detector.py             # 主检测引擎
├── version_tracker.py           # 版本跟踪器
├── consistency_checker.py       # 一致性检查器
├── conflict_resolver.py         # 冲突解决器
├── dependency_analyzer.py       # 依赖分析器
├── sync_manager.py             # 同步管理器
└── report_generator.py         # 报告生成器
```

## 🔧 核心实施代码

### 主检测引擎 - src/v4_scaffolding/engines/version_consistency.py
```python
"""V4版本一致性检测引擎"""

from __future__ import annotations
import asyncio
import hashlib
from pathlib import Path
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import logging

from ..core.config import config
from ..core.exceptions import VersionConflictError, ConsistencyCheckError
from ..models.base import AnalysisStatus, ProcessingResult
from ..models.version_models import (
    VersionInfo, ConsistencyReport, ConflictDetection, 
    DependencyGraph, SyncRecommendation
)

from .version_tracker import VersionTracker
from .consistency_checker import ConsistencyChecker
from .conflict_resolver import ConflictResolver
from .dependency_analyzer import DependencyAnalyzer
from .sync_manager import SyncManager
from .report_generator import ReportGenerator


class FileType(Enum):
    """文件类型枚举"""
    DESIGN_DOCUMENT = "design_document"
    SOURCE_CODE = "source_code"
    TEST_CODE = "test_code"
    CONFIG_FILE = "config_file"
    DEPENDENCY_FILE = "dependency_file"


@dataclass
class VersionedFile:
    """版本化文件信息"""
    path: Path
    file_type: FileType
    version: str
    checksum: str
    last_modified: datetime
    dependencies: List[str] = field(default_factory=list)
    related_files: List[Path] = field(default_factory=list)


@dataclass
class ConsistencyCheckContext:
    """一致性检查上下文"""
    project_root: Path
    include_patterns: List[str]
    exclude_patterns: List[str]
    check_dependencies: bool = True
    deep_analysis: bool = False


class VersionConsistencyDetector:
    """版本一致性检测引擎"""
    
    def __init__(self):
        self.version_tracker = VersionTracker()
        self.consistency_checker = ConsistencyChecker()
        self.conflict_resolver = ConflictResolver()
        self.dependency_analyzer = DependencyAnalyzer()
        self.sync_manager = SyncManager()
        self.report_generator = ReportGenerator()
        
        self.logger = logging.getLogger(__name__)
        
        # 版本检测配置
        self.detection_config = {
            'supported_file_types': {
                '.md': FileType.DESIGN_DOCUMENT,
                '.py': FileType.SOURCE_CODE,
                '.yml': FileType.CONFIG_FILE,
                '.yaml': FileType.CONFIG_FILE,
                '.json': FileType.CONFIG_FILE,
                '.toml': FileType.CONFIG_FILE,
                '.txt': FileType.DEPENDENCY_FILE
            },
            'version_patterns': {
                'semver': r'v?(\d+)\.(\d+)\.(\d+)(?:-([a-zA-Z0-9\.-]+))?',
                'date_version': r'(\d{4})\.(\d{2})\.(\d{2})',
                'hash_version': r'([a-f0-9]{7,40})'
            },
            'consistency_threshold': 0.85,
            'conflict_tolerance': 0.1
        }
    
    async def perform_consistency_check(
        self,
        context: ConsistencyCheckContext
    ) -> ConsistencyReport:
        """执行一致性检查"""
        
        self.logger.info(f"Starting consistency check for {context.project_root}")
        
        # 第一步：扫描和分类文件
        versioned_files = await self._scan_versioned_files(context)
        
        # 第二步：构建依赖图
        dependency_graph = await self.dependency_analyzer.build_dependency_graph(
            versioned_files, context
        )
        
        # 第三步：执行版本跟踪
        version_tracking_result = await self.version_tracker.track_versions(
            versioned_files, dependency_graph
        )
        
        # 第四步：检查一致性
        consistency_issues = await self.consistency_checker.check_consistency(
            versioned_files, dependency_graph, context
        )
        
        # 第五步：检测冲突
        conflicts = await self._detect_version_conflicts(
            versioned_files, consistency_issues
        )
        
        # 第六步：生成解决建议
        resolution_recommendations = await self.conflict_resolver.generate_recommendations(
            conflicts, dependency_graph, context
        )
        
        # 第七步：创建一致性报告
        consistency_report = ConsistencyReport(
            check_timestamp=datetime.now(),
            project_root=context.project_root,
            total_files_checked=len(versioned_files),
            consistency_score=await self._calculate_consistency_score(consistency_issues),
            version_tracking_result=version_tracking_result,
            consistency_issues=consistency_issues,
            detected_conflicts=conflicts,
            resolution_recommendations=resolution_recommendations,
            dependency_graph=dependency_graph
        )
        
        self.logger.info(
            f"Consistency check completed. Score: {consistency_report.consistency_score}"
        )
        
        return consistency_report
    
    async def resolve_version_conflicts(
        self,
        conflicts: List[ConflictDetection],
        resolution_strategy: str = "auto"
    ) -> Dict[str, Any]:
        """解决版本冲突"""
        
        resolution_results = {}
        
        for conflict in conflicts:
            try:
                if resolution_strategy == "auto":
                    result = await self.conflict_resolver.auto_resolve_conflict(conflict)
                elif resolution_strategy == "manual":
                    result = await self.conflict_resolver.suggest_manual_resolution(conflict)
                else:
                    result = await self.conflict_resolver.custom_resolve_conflict(
                        conflict, resolution_strategy
                    )
                
                resolution_results[conflict.conflict_id] = result
                
            except Exception as e:
                self.logger.error(f"Failed to resolve conflict {conflict.conflict_id}: {e}")
                resolution_results[conflict.conflict_id] = {
                    'status': 'failed',
                    'error': str(e)
                }
        
        return resolution_results
    
    async def sync_versions_across_files(
        self,
        target_files: List[VersionedFile],
        sync_strategy: str = "latest"
    ) -> Dict[str, Any]:
        """跨文件版本同步"""
        
        return await self.sync_manager.sync_versions(target_files, sync_strategy)
    
    async def _scan_versioned_files(
        self, 
        context: ConsistencyCheckContext
    ) -> List[VersionedFile]:
        """扫描版本化文件"""
        
        versioned_files = []
        
        # 遍历项目目录
        for file_path in context.project_root.rglob("*"):
            if not file_path.is_file():
                continue
            
            # 检查文件类型
            file_type = self._determine_file_type(file_path)
            if not file_type:
                continue
            
            # 检查包含/排除模式
            if not self._should_include_file(file_path, context):
                continue
            
            # 提取版本信息
            version_info = await self._extract_version_info(file_path, file_type)
            
            # 计算文件校验和
            checksum = await self._calculate_file_checksum(file_path)
            
            # 创建版本化文件对象
            versioned_file = VersionedFile(
                path=file_path,
                file_type=file_type,
                version=version_info.version if version_info else "unknown",
                checksum=checksum,
                last_modified=datetime.fromtimestamp(file_path.stat().st_mtime),
                dependencies=await self._extract_dependencies(file_path, file_type),
                related_files=await self._find_related_files(file_path, context)
            )
            
            versioned_files.append(versioned_file)
        
        return versioned_files
    
    def _determine_file_type(self, file_path: Path) -> Optional[FileType]:
        """确定文件类型"""
        suffix = file_path.suffix.lower()
        
        # 特殊处理测试文件
        if 'test' in str(file_path).lower() and suffix == '.py':
            return FileType.TEST_CODE
        
        return self.detection_config['supported_file_types'].get(suffix)
    
    def _should_include_file(
        self, 
        file_path: Path, 
        context: ConsistencyCheckContext
    ) -> bool:
        """判断是否应该包含文件"""
        file_str = str(file_path)
        
        # 检查排除模式
        for pattern in context.exclude_patterns:
            if pattern in file_str:
                return False
        
        # 检查包含模式
        if context.include_patterns:
            return any(pattern in file_str for pattern in context.include_patterns)
        
        return True
    
    async def _extract_version_info(
        self, 
        file_path: Path, 
        file_type: FileType
    ) -> Optional[VersionInfo]:
        """提取版本信息"""
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # 基于文件类型使用不同的版本提取策略
            if file_type == FileType.DESIGN_DOCUMENT:
                return await self._extract_document_version(content)
            elif file_type in [FileType.SOURCE_CODE, FileType.TEST_CODE]:
                return await self._extract_code_version(content)
            elif file_type == FileType.CONFIG_FILE:
                return await self._extract_config_version(content, file_path.suffix)
            elif file_type == FileType.DEPENDENCY_FILE:
                return await self._extract_dependency_version(content)
                
        except Exception as e:
            self.logger.warning(f"Failed to extract version from {file_path}: {e}")
            
        return None
    
    async def _extract_document_version(self, content: str) -> Optional[VersionInfo]:
        """从设计文档提取版本"""
        import re
        
        # 查找文档版本标记
        version_patterns = [
            r'version\s*[:\-]\s*([v]?\d+\.\d+\.\d+)',
            r'版本\s*[：:]\s*([v]?\d+\.\d+\.\d+)',
            r'V(\d+\.\d+\.\d+)',
            r'v(\d+\.\d+\.\d+)'
        ]
        
        for pattern in version_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                version = match.group(1)
                return VersionInfo(
                    version=version,
                    source="document_header",
                    extraction_confidence=0.9
                )
        
        return None
    
    async def _extract_code_version(self, content: str) -> Optional[VersionInfo]:
        """从代码文件提取版本"""
        import re
        
        # 查找代码中的版本定义
        version_patterns = [
            r'__version__\s*=\s*["\']([^"\']+)["\']',
            r'VERSION\s*=\s*["\']([^"\']+)["\']',
            r'version\s*=\s*["\']([^"\']+)["\']'
        ]
        
        for pattern in version_patterns:
            match = re.search(pattern, content)
            if match:
                version = match.group(1)
                return VersionInfo(
                    version=version,
                    source="code_constant",
                    extraction_confidence=0.95
                )
        
        return None
    
    async def _extract_config_version(
        self, 
        content: str, 
        file_suffix: str
    ) -> Optional[VersionInfo]:
        """从配置文件提取版本"""
        
        if file_suffix in ['.yml', '.yaml']:
            return await self._extract_yaml_version(content)
        elif file_suffix == '.json':
            return await self._extract_json_version(content)
        elif file_suffix == '.toml':
            return await self._extract_toml_version(content)
        
        return None
    
    async def _extract_yaml_version(self, content: str) -> Optional[VersionInfo]:
        """从YAML文件提取版本"""
        import re
        
        version_match = re.search(r'version\s*:\s*["\']?([^"\']+)["\']?', content)
        if version_match:
            return VersionInfo(
                version=version_match.group(1),
                source="yaml_config",
                extraction_confidence=0.85
            )
        
        return None
    
    async def _extract_json_version(self, content: str) -> Optional[VersionInfo]:
        """从JSON文件提取版本"""
        try:
            import json
            data = json.loads(content)
            
            if 'version' in data:
                return VersionInfo(
                    version=str(data['version']),
                    source="json_config",
                    extraction_confidence=0.9
                )
                
        except Exception:
            pass
        
        return None
    
    async def _extract_toml_version(self, content: str) -> Optional[VersionInfo]:
        """从TOML文件提取版本"""
        import re
        
        version_match = re.search(r'version\s*=\s*["\']([^"\']+)["\']', content)
        if version_match:
            return VersionInfo(
                version=version_match.group(1),
                source="toml_config",
                extraction_confidence=0.9
            )
        
        return None
    
    async def _extract_dependency_version(self, content: str) -> Optional[VersionInfo]:
        """从依赖文件提取版本"""
        # 简化的依赖版本提取（如requirements.txt）
        import re
        
        # 查找版本要求
        version_match = re.search(r'==(\d+\.\d+\.\d+)', content)
        if version_match:
            return VersionInfo(
                version=version_match.group(1),
                source="dependency_spec",
                extraction_confidence=0.8
            )
        
        return None
    
    async def _calculate_file_checksum(self, file_path: Path) -> str:
        """计算文件校验和"""
        try:
            content = file_path.read_bytes()
            return hashlib.md5(content).hexdigest()
        except Exception:
            return "unknown"
    
    async def _extract_dependencies(
        self, 
        file_path: Path, 
        file_type: FileType
    ) -> List[str]:
        """提取文件依赖"""
        dependencies = []
        
        try:
            content = file_path.read_text(encoding='utf-8')
            
            if file_type in [FileType.SOURCE_CODE, FileType.TEST_CODE]:
                # Python导入分析
                import re
                imports = re.findall(r'(?:from|import)\s+([a-zA-Z_][a-zA-Z0-9_\.]*)', content)
                dependencies.extend(imports)
            
            elif file_type == FileType.DESIGN_DOCUMENT:
                # 文档引用分析
                import re
                refs = re.findall(r'\[([^\]]+)\]', content)
                dependencies.extend(refs)
                
        except Exception as e:
            self.logger.warning(f"Failed to extract dependencies from {file_path}: {e}")
        
        return dependencies
    
    async def _find_related_files(
        self, 
        file_path: Path, 
        context: ConsistencyCheckContext
    ) -> List[Path]:
        """查找相关文件"""
        related_files = []
        file_stem = file_path.stem
        
        # 查找同名但不同扩展名的文件
        for related_path in file_path.parent.glob(f"{file_stem}.*"):
            if related_path != file_path:
                related_files.append(related_path)
        
        # 查找测试文件
        if not 'test' in str(file_path).lower():
            test_patterns = [
                f"test_{file_stem}.py",
                f"{file_stem}_test.py",
                f"tests/test_{file_stem}.py"
            ]
            
            for pattern in test_patterns:
                test_path = context.project_root / pattern
                if test_path.exists():
                    related_files.append(test_path)
        
        return related_files
    
    async def _detect_version_conflicts(
        self,
        versioned_files: List[VersionedFile],
        consistency_issues: List[Any]
    ) -> List[ConflictDetection]:
        """检测版本冲突"""
        conflicts = []
        
        # 按文件组分组（相关文件）
        file_groups = self._group_related_files(versioned_files)
        
        for group_id, files in file_groups.items():
            group_versions = [f.version for f in files if f.version != "unknown"]
            
            if len(set(group_versions)) > 1:
                # 发现版本不一致
                conflict = ConflictDetection(
                    conflict_id=f"version_mismatch_{group_id}",
                    conflict_type="version_mismatch",
                    affected_files=[f.path for f in files],
                    conflicting_versions=group_versions,
                    severity="medium",
                    description=f"Version mismatch in related files: {group_versions}"
                )
                conflicts.append(conflict)
        
        return conflicts
    
    def _group_related_files(
        self, 
        versioned_files: List[VersionedFile]
    ) -> Dict[str, List[VersionedFile]]:
        """将相关文件分组"""
        groups = {}
        
        for file in versioned_files:
            # 使用文件名基础部分作为分组键
            group_key = file.path.stem
            
            if group_key not in groups:
                groups[group_key] = []
            groups[group_key].append(file)
        
        return groups
    
    async def _calculate_consistency_score(
        self, 
        consistency_issues: List[Any]
    ) -> float:
        """计算一致性得分"""
        if not consistency_issues:
            return 1.0
        
        # 简化的得分计算
        total_issues = len(consistency_issues)
        max_allowed_issues = 10  # 假设的最大允许问题数
        
        score = max(0.0, 1.0 - (total_issues / max_allowed_issues))
        return score
```

### 一致性检查器 - src/v4_scaffolding/engines/consistency_checker.py
```python
"""一致性检查器"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import asyncio

from ..models.version_models import ConsistencyIssue, IssueType, IssueSeverity


@dataclass
class CheckRule:
    """检查规则"""
    rule_id: str
    rule_name: str
    check_function: str
    severity: IssueSeverity
    enabled: bool = True


class ConsistencyChecker:
    """一致性检查器"""
    
    def __init__(self):
        self.check_rules = self._initialize_check_rules()
    
    def _initialize_check_rules(self) -> List[CheckRule]:
        """初始化检查规则"""
        return [
            CheckRule(
                rule_id="version_mismatch",
                rule_name="Version Mismatch Check",
                check_function="check_version_mismatch",
                severity=IssueSeverity.HIGH
            ),
            CheckRule(
                rule_id="dependency_consistency",
                rule_name="Dependency Consistency Check", 
                check_function="check_dependency_consistency",
                severity=IssueSeverity.MEDIUM
            ),
            CheckRule(
                rule_id="file_modification_sync",
                rule_name="File Modification Sync Check",
                check_function="check_modification_sync",
                severity=IssueSeverity.LOW
            ),
            CheckRule(
                rule_id="checksum_integrity",
                rule_name="Checksum Integrity Check",
                check_function="check_checksum_integrity",
                severity=IssueSeverity.HIGH
            )
        ]
    
    async def check_consistency(
        self,
        versioned_files: List['VersionedFile'],
        dependency_graph: 'DependencyGraph',
        context: 'ConsistencyCheckContext'
    ) -> List[ConsistencyIssue]:
        """执行一致性检查"""
        
        issues = []
        
        for rule in self.check_rules:
            if not rule.enabled:
                continue
            
            try:
                rule_issues = await self._execute_check_rule(
                    rule, versioned_files, dependency_graph, context
                )
                issues.extend(rule_issues)
                
            except Exception as e:
                # 记录检查规则执行失败
                error_issue = ConsistencyIssue(
                    issue_id=f"rule_error_{rule.rule_id}",
                    issue_type=IssueType.SYSTEM_ERROR,
                    severity=IssueSeverity.HIGH,
                    description=f"Check rule {rule.rule_name} failed: {str(e)}",
                    affected_files=[],
                    recommendation="Review check rule implementation"
                )
                issues.append(error_issue)
        
        return issues
    
    async def _execute_check_rule(
        self,
        rule: CheckRule,
        versioned_files: List['VersionedFile'],
        dependency_graph: 'DependencyGraph',
        context: 'ConsistencyCheckContext'
    ) -> List[ConsistencyIssue]:
        """执行检查规则"""
        
        check_method = getattr(self, rule.check_function, None)
        if not check_method:
            return []
        
        return await check_method(versioned_files, dependency_graph, context)
    
    async def check_version_mismatch(
        self,
        versioned_files: List['VersionedFile'],
        dependency_graph: 'DependencyGraph',
        context: 'ConsistencyCheckContext'
    ) -> List[ConsistencyIssue]:
        """检查版本不匹配"""
        issues = []
        
        # 按文件组检查版本一致性
        file_groups = self._group_files_by_relationship(versioned_files)
        
        for group_name, files in file_groups.items():
            versions = set(f.version for f in files if f.version != "unknown")
            
            if len(versions) > 1:
                issue = ConsistencyIssue(
                    issue_id=f"version_mismatch_{group_name}",
                    issue_type=IssueType.VERSION_MISMATCH,
                    severity=IssueSeverity.HIGH,
                    description=f"Version mismatch in {group_name}: {versions}",
                    affected_files=[f.path for f in files],
                    recommendation="Sync versions across related files"
                )
                issues.append(issue)
        
        return issues
    
    async def check_dependency_consistency(
        self,
        versioned_files: List['VersionedFile'],
        dependency_graph: 'DependencyGraph',
        context: 'ConsistencyCheckContext'
    ) -> List[ConsistencyIssue]:
        """检查依赖一致性"""
        issues = []
        
        for file in versioned_files:
            for dependency in file.dependencies:
                # 检查依赖是否存在对应的文件
                dependency_files = [
                    f for f in versioned_files 
                    if dependency in str(f.path) or dependency in f.dependencies
                ]
                
                if not dependency_files:
                    issue = ConsistencyIssue(
                        issue_id=f"missing_dependency_{dependency}",
                        issue_type=IssueType.MISSING_DEPENDENCY,
                        severity=IssueSeverity.MEDIUM,
                        description=f"Missing dependency: {dependency}",
                        affected_files=[file.path],
                        recommendation=f"Add or verify dependency: {dependency}"
                    )
                    issues.append(issue)
        
        return issues
    
    async def check_modification_sync(
        self,
        versioned_files: List['VersionedFile'],
        dependency_graph: 'DependencyGraph',
        context: 'ConsistencyCheckContext'
    ) -> List[ConsistencyIssue]:
        """检查修改同步"""
        issues = []
        
        # 检查相关文件的修改时间是否合理
        for file in versioned_files:
            for related_path in file.related_files:
                related_file = next(
                    (f for f in versioned_files if f.path == related_path), 
                    None
                )
                
                if related_file:
                    time_diff = abs(
                        (file.last_modified - related_file.last_modified).total_seconds()
                    )
                    
                    # 如果相关文件修改时间差超过24小时，标记为潜在问题
                    if time_diff > 86400:  # 24小时
                        issue = ConsistencyIssue(
                            issue_id=f"sync_issue_{file.path.name}_{related_file.path.name}",
                            issue_type=IssueType.SYNC_ISSUE,
                            severity=IssueSeverity.LOW,
                            description=f"Related files have significant modification time difference",
                            affected_files=[file.path, related_file.path],
                            recommendation="Review if files should be synchronized"
                        )
                        issues.append(issue)
        
        return issues
    
    async def check_checksum_integrity(
        self,
        versioned_files: List['VersionedFile'],
        dependency_graph: 'DependencyGraph',
        context: 'ConsistencyCheckContext'
    ) -> List[ConsistencyIssue]:
        """检查校验和完整性"""
        issues = []
        
        for file in versioned_files:
            if file.checksum == "unknown":
                issue = ConsistencyIssue(
                    issue_id=f"checksum_unknown_{file.path.name}",
                    issue_type=IssueType.INTEGRITY_CHECK,
                    severity=IssueSeverity.MEDIUM,
                    description=f"Cannot calculate checksum for file: {file.path}",
                    affected_files=[file.path],
                    recommendation="Verify file accessibility and permissions"
                )
                issues.append(issue)
        
        return issues
    
    def _group_files_by_relationship(
        self, 
        versioned_files: List['VersionedFile']
    ) -> Dict[str, List['VersionedFile']]:
        """按关系分组文件"""
        groups = {}
        
        for file in versioned_files:
            # 使用文件名基础部分作为分组键
            group_key = file.path.stem
            
            if group_key not in groups:
                groups[group_key] = []
            groups[group_key].append(file)
            
            # 添加相关文件到同一组
            for related_file in versioned_files:
                if related_file.path in file.related_files:
                    if related_file not in groups[group_key]:
                        groups[group_key].append(related_file)
        
        return groups
```

## 🧪 核心测试用例

### tests/unit/test_version_consistency.py
```python
"""版本一致性检测引擎测试"""

import pytest
import asyncio
from pathlib import Path
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

from v4_scaffolding.engines.version_consistency import (
    VersionConsistencyDetector, FileType, VersionedFile, ConsistencyCheckContext
)


class TestVersionConsistencyDetector:
    """版本一致性检测引擎测试"""
    
    @pytest.fixture
    def detector(self):
        return VersionConsistencyDetector()
    
    @pytest.fixture
    def sample_project(self, temp_dir):
        """示例项目结构"""
        # 创建项目文件
        (temp_dir / "setup.py").write_text('version = "1.0.0"')
        (temp_dir / "pyproject.toml").write_text('[project]\nversion = "1.0.1"')
        (temp_dir / "src" / "main.py").write_text('__version__ = "1.0.0"')
        (temp_dir / "docs" / "design.md").write_text('# Design v1.0.2')
        (temp_dir / "tests" / "test_main.py").write_text('# Test for main.py')
        
        return temp_dir
    
    @pytest.fixture
    def check_context(self, sample_project):
        return ConsistencyCheckContext(
            project_root=sample_project,
            include_patterns=["*.py", "*.md", "*.toml"],
            exclude_patterns=["__pycache__", ".git"],
            check_dependencies=True,
            deep_analysis=False
        )
    
    @pytest.mark.asyncio
    async def test_perform_consistency_check(self, detector, check_context):
        """测试一致性检查执行"""
        report = await detector.perform_consistency_check(check_context)
        
        assert report.total_files_checked > 0
        assert 0.0 <= report.consistency_score <= 1.0
        assert report.detected_conflicts is not None
        
        # 应该检测到版本不一致问题
        assert len(report.detected_conflicts) > 0
    
    @pytest.mark.asyncio
    async def test_scan_versioned_files(self, detector, check_context):
        """测试版本化文件扫描"""
        files = await detector._scan_versioned_files(check_context)
        
        assert len(files) > 0
        
        # 验证文件类型识别
        file_types = {f.file_type for f in files}
        assert FileType.SOURCE_CODE in file_types
        assert FileType.CONFIG_FILE in file_types
        assert FileType.DESIGN_DOCUMENT in file_types
    
    @pytest.mark.asyncio
    async def test_extract_version_info(self, detector):
        """测试版本信息提取"""
        # 测试Python代码版本提取
        test_content = '__version__ = "2.1.0"'
        temp_file = Path("test.py")
        temp_file.write_text(test_content)
        
        try:
            version_info = await detector._extract_version_info(temp_file, FileType.SOURCE_CODE)
            assert version_info is not None
            assert version_info.version == "2.1.0"
        finally:
            temp_file.unlink(missing_ok=True)
    
    @pytest.mark.asyncio
    async def test_detect_version_conflicts(self, detector):
        """测试版本冲突检测"""
        versioned_files = [
            VersionedFile(
                path=Path("file1.py"),
                file_type=FileType.SOURCE_CODE,
                version="1.0.0",
                checksum="abc123",
                last_modified=datetime.now()
            ),
            VersionedFile(
                path=Path("file1.md"),
                file_type=FileType.DESIGN_DOCUMENT,
                version="1.0.1",
                checksum="def456",
                last_modified=datetime.now()
            )
        ]
        
        conflicts = await detector._detect_version_conflicts(versioned_files, [])
        
        assert len(conflicts) > 0
        assert conflicts[0].conflict_type == "version_mismatch"
    
    @pytest.mark.asyncio
    async def test_resolve_version_conflicts(self, detector):
        """测试版本冲突解决"""
        from v4_scaffolding.models.version_models import ConflictDetection
        
        conflict = ConflictDetection(
            conflict_id="test_conflict",
            conflict_type="version_mismatch",
            affected_files=[Path("file1.py"), Path("file2.py")],
            conflicting_versions=["1.0.0", "1.0.1"],
            severity="medium",
            description="Test conflict"
        )
        
        resolution_results = await detector.resolve_version_conflicts([conflict])
        
        assert "test_conflict" in resolution_results
        assert "status" in resolution_results["test_conflict"]


class TestConsistencyChecker:
    """一致性检查器测试"""
    
    @pytest.fixture
    def checker(self):
        from v4_scaffolding.engines.consistency_checker import ConsistencyChecker
        return ConsistencyChecker()
    
    @pytest.fixture
    def sample_versioned_files(self):
        """示例版本化文件"""
        return [
            VersionedFile(
                path=Path("main.py"),
                file_type=FileType.SOURCE_CODE,
                version="1.0.0",
                checksum="abc123",
                last_modified=datetime.now(),
                dependencies=["requests", "numpy"]
            ),
            VersionedFile(
                path=Path("test_main.py"),
                file_type=FileType.TEST_CODE,
                version="1.0.1",
                checksum="def456",
                last_modified=datetime.now(),
                related_files=[Path("main.py")]
            )
        ]
    
    @pytest.mark.asyncio
    async def test_check_consistency(self, checker, sample_versioned_files):
        """测试一致性检查"""
        from v4_scaffolding.models.version_models import DependencyGraph
        
        dependency_graph = DependencyGraph(nodes=[], edges=[])
        context = ConsistencyCheckContext(
            project_root=Path("."),
            include_patterns=[],
            exclude_patterns=[]
        )
        
        issues = await checker.check_consistency(
            sample_versioned_files, dependency_graph, context
        )
        
        assert len(issues) > 0
        # 应该检测到版本不匹配问题
        version_issues = [i for i in issues if "version" in i.description.lower()]
        assert len(version_issues) > 0
    
    @pytest.mark.asyncio
    async def test_check_version_mismatch(self, checker, sample_versioned_files):
        """测试版本不匹配检查"""
        from v4_scaffolding.models.version_models import DependencyGraph
        
        issues = await checker.check_version_mismatch(
            sample_versioned_files, 
            DependencyGraph(nodes=[], edges=[]),
            ConsistencyCheckContext(Path("."), [], [])
        )
        
        assert len(issues) > 0
        assert issues[0].issue_type.value == "version_mismatch"
```

## 📋 验收标准

### 功能验收
- [ ] 版本一致性检测功能 (100%)
- [ ] 多文件类型版本提取 (100%)
- [ ] 冲突检测和解决 (100%)
- [ ] 依赖分析功能 (100%)
- [ ] 同步管理功能 (100%)

### 质量验收
- [ ] 单元测试覆盖率 ≥ 95%
- [ ] 版本提取准确率 ≥ 90%
- [ ] 冲突检测准确率 ≥ 85%
- [ ] 一致性检查覆盖率 ≥ 90%

### 性能验收
- [ ] 项目扫描时间 ≤ 10秒 (1000文件)
- [ ] 一致性检查时间 ≤ 15秒
- [ ] 冲突解决时间 ≤ 5秒
- [ ] 内存占用 ≤ 400MB

## 🚀 下一步骤
1. **07-测试驱动验证框架.md** - pytest框架和测试策略
2. **08-CLI接口和集成测试.md** - 命令行接口和端到端测试 