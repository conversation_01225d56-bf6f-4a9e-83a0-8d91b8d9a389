# V4.5 MCP底层API快速参考

## 🚀 新建文档（3种方式）

### 方式1：insert_line（推荐）
```python
{
    "task_type": "document_edit",
    "command": {
        "file_path": "new_doc.md",
        "operation": "insert_line",
        "parameters": {
            "line_number": 1,
            "content": "# 标题\n\n内容...",
            "position": "after"
        }
    }
}
```

### 方式2：update_line
```python
{
    "task_type": "document_edit",
    "command": {
        "file_path": "new_doc.md",
        "operation": "update_line",
        "parameters": {
            "line_number": 1,
            "content": "文档内容",
            "merge_mode": "replace"
        }
    }
}
```

### 方式3：Web调试命令
```bash
# 在 http://localhost:25526/debug 执行
mcp_file_write
```

## 📝 文档编辑API

### insert_line - 插入行
```python
{
    "task_type": "document_edit",
    "command": {
        "file_path": "doc.md",
        "operation": "insert_line",
        "parameters": {
            "line_number": 2,
            "content": "新内容",
            "position": "after"  # before/after/replace
        }
    }
}
```

### read_line - 读取行
```python
{
    "task_type": "document_edit",
    "command": {
        "file_path": "doc.md",
        "operation": "read_line",
        "parameters": {
            "line_number": 1,
            "range": [1, 5]  # 可选
        }
    }
}
```

### update_line - 更新行
```python
{
    "task_type": "document_edit",
    "command": {
        "file_path": "doc.md",
        "operation": "update_line",
        "parameters": {
            "line_number": 1,
            "content": "新内容",
            "merge_mode": "replace"  # replace/append/prepend
        }
    }
}
```

### delete_line - 删除行
```python
{
    "task_type": "document_edit",
    "command": {
        "file_path": "doc.md",
        "operation": "delete_line",
        "parameters": {
            "line_number": 1,
            "count": 1  # 删除行数
        }
    }
}
```

### replace_all - 全局替换
```python
{
    "task_type": "document_edit",
    "command": {
        "file_path": "doc.md",
        "operation": "replace_all",
        "parameters": {
            "search_pattern": "旧内容",
            "replace_with": "新内容",
            "regex": False,
            "case_sensitive": True
        }
    }
}
```

## 📁 目录操作API

### list_directory - 列出目录
```python
{
    "task_type": "directory_operation",
    "command": {
        "operation": "list_directory",
        "parameters": {
            "directory_path": "docs/",
            "recursive": True,
            "include_files": True,
            "include_dirs": True,
            "max_depth": 3
        }
    }
}
```

### search_files - 搜索文件
```python
{
    "task_type": "directory_operation",
    "command": {
        "operation": "search_files",
        "parameters": {
            "directory_path": "docs/",
            "pattern": "*.md",
            "recursive": True,
            "max_results": 100,
            "content_search": "关键词"  # 可选
        }
    }
}
```

### delete_directory - 删除目录
```python
{
    "task_type": "directory_operation",
    "command": {
        "operation": "delete_directory",
        "parameters": {
            "directory_path": "temp/",
            "recursive": True,
            "force": False
        }
    }
}
```

### delete_file - 删除文件
```python
{
    "task_type": "directory_operation",
    "command": {
        "operation": "delete_file",
        "parameters": {
            "file_path": "temp.txt",
            "backup": True
        }
    }
}
```

## 🧪 Web调试测试命令

```bash
# 访问：http://localhost:25526/debug

# 文档编辑测试
document_edit_test      # insert_line测试
read_line_test         # read_line测试  
update_line_test       # update_line测试
delete_line_test       # delete_line测试
replace_all_test       # replace_all测试

# 目录操作测试
directory_operation_test  # list_directory测试
search_files_test        # search_files测试
delete_test             # delete操作测试

# 文件创建测试
mcp_file_write          # 新建文件测试

# 系统状态
client_states          # 客户端状态
status                 # 系统状态
health                 # 健康检查
```

## 📊 返回结果格式

### 成功响应
```json
{
    "status": "success",
    "result": {
        "operation_specific_data": "...",
        "lines_affected": 1,
        "total_lines": 10
    },
    "backup_id": "backup_20250627_123456",
    "operation": "insert_line"
}
```

### 错误响应
```json
{
    "status": "error",
    "error": "错误描述",
    "error_type": "FileNotFoundError",
    "backup_id": "backup_20250627_123456"
}
```

## ⚡ 核心特性

- ✅ **自动新建文档**：文件不存在时自动创建
- ✅ **自动备份**：操作前自动备份，成功后清理
- ✅ **路径处理**：自动创建目录结构
- ✅ **编码支持**：UTF-8，支持中文
- ✅ **错误恢复**：失败时自动回滚
- ✅ **跨平台**：Windows/Linux兼容

## 📍 关键路径

```bash
# 代码位置
tools/ace/src/four_layer_meeting_system/mcp_server/editors/
├── document_commander.py    # 文档编辑API
└── directory_commander.py   # 目录操作API

# 日志位置  
tools/ace/src/tests/mcp-client-logs/

# Web调试界面
http://localhost:25526/debug
```

## 🎯 验证状态

- **总API数**：9个
- **验证通过**：9个  
- **成功率**：100%
- **三维度验证**：✅ Web界面+实地结果+日志确认

---
**版本**：V4.5 | **更新**：2025-06-27 | **状态**：✅ 生产就绪
