# XCE异常分类标准

## 文档信息
- **文档ID**: F007-XCE-CLASSIFICATION-STANDARD
- **创建日期**: 2025-01-15
- **版本**: v1.0
- **适用范围**: 整个xkongcloud平台
- **权威级别**: 平台级标准

## 分类原则

### 1. 技术类别优先原则
**定义**: 异常按技术操作类型分类，而非按项目或业务领域分类

**优势**:
- **真实复用性**: 相同技术操作的异常可被多个项目复用
- **处理一致性**: 相同技术类别使用统一的处理策略
- **开发直观性**: 开发者按操作类型思考异常

**示例**:
```java
// 正确：按技术类别分类
DatabaseSystemException.queryTimeout()    // 数据库查询超时
NetworkSystemException.connectionFailed() // 网络连接失败
FileSystemException.accessDenied()        // 文件访问被拒绝

// 错误：按项目分类
UidException.databaseQueryTimeout()       // 应该归类到数据库异常
UserException.networkConnectionFailed()   // 应该归类到网络异常
```

### 2. 主要诱发因素分类原则
**定义**: 按异常的主要诱发因素分类，次要因素通过metadata保留

**分类规则**:
- **网络异常**: 主要由网络连接、超时、服务不可用引起
- **数据库异常**: 主要由数据库连接、查询、事务问题引起
- **文件异常**: 主要由文件读写、权限、路径问题引起
- **验证异常**: 主要由参数验证、状态验证、格式验证引起
- **安全异常**: 主要由认证、授权、加密问题引起
- **业务异常**: 主要由业务规则、逻辑冲突引起

**示例**:
```java
// UID实例恢复失败 - 主要因素是数据库查询
DatabaseSystemException.instanceRecoveryFailed()
    .addMetadata("component", "uid")
    .addMetadata("operation", "instance_recovery");

// UID机器特征码收集失败 - 主要因素是文件访问
FileSystemException.fingerprintCollectionFailed()
    .addMetadata("component", "uid")
    .addMetadata("operation", "fingerprint_collection");
```

## 技术类别定义

### 1. 核心异常 (core/)
**错误码段**: 100-199 (业务), 500-599 (系统)
**包路径**: `org.xkong.cloud.commons.exception.core`

**包含异常**:
- `ServiceException`: 所有异常的基类
- `BusinessException`: 业务逻辑异常
- `SystemException`: 系统内部异常

### 2. 网络类异常 (network/)
**错误码段**: 600-649
**包路径**: `org.xkong.cloud.commons.exception.network`
**命名前缀**: `XCE_NET_xxx`

**典型场景**:
- 网络连接超时
- 服务不可用
- 网络通信失败
- Mock环境创建失败

### 3. 数据库类异常 (database/)
**错误码段**: 650-699
**包路径**: `org.xkong.cloud.commons.exception.database`
**命名前缀**: `XCE_DB_xxx`

**典型场景**:
- 数据库连接失败
- 查询超时
- 事务冲突
- 连接池耗尽

### 4. 文件类异常 (file/)
**错误码段**: 700-749
**包路径**: `org.xkong.cloud.commons.exception.file`
**命名前缀**: `XCE_FILE_xxx`

**典型场景**:
- 文件不存在
- 权限被拒绝
- 磁盘空间不足
- 文件格式错误

### 5. 验证类异常 (validation/)
**错误码段**: 750-799
**包路径**: `org.xkong.cloud.commons.exception.validation`
**命名前缀**: `XCE_VAL_xxx`

**典型场景**:
- 参数验证失败
- 状态验证失败
- 格式验证失败
- 业务规则验证失败

### 6. 安全类异常 (security/)
**错误码段**: 800-849
**包路径**: `org.xkong.cloud.commons.exception.security`
**命名前缀**: `XCE_SEC_xxx`

**典型场景**:
- 认证失败
- 授权被拒绝
- 加密解密失败
- 安全策略违规

### 7. 算法处理异常 (algorithm/)
**错误码段**: 850-899
**包路径**: `org.xkong.cloud.commons.exception.algorithm`
**命名前缀**: `XCE_ALG_xxx`

**典型场景**:
- L2认知层算法失败
- L3理解层算法失败
- L4智慧层算法失败
- 算法参数错误

## 错误码分配规范

### 1. 分段分配原则
```java
// 核心异常
100-199: 通用业务异常
500-599: 通用系统异常

// 技术类别异常
600-649: 网络类异常 (50个)
650-699: 数据库类异常 (50个)
700-749: 文件类异常 (50个)
750-799: 验证类异常 (50个)
800-849: 安全类异常 (50个)
850-899: 算法处理异常 (50个)

// 预留扩展
900-999: 未来技术类别扩展
```

### 2. 命名规范
**格式**: `XCE_[技术类别]_[编号]`

**示例**:
```java
// 网络类异常
public static final String NETWORK_CONNECTION_TIMEOUT = "XCE_NET_600";
public static final String NETWORK_SERVICE_UNAVAILABLE = "XCE_NET_601";

// 数据库类异常
public static final String DATABASE_CONNECTION_POOL_EXHAUSTED = "XCE_DB_650";
public static final String DATABASE_TRANSACTION_TIMEOUT = "XCE_DB_651";

// 文件类异常
public static final String FILE_NOT_FOUND = "XCE_FILE_700";
public static final String FILE_PERMISSION_DENIED = "XCE_FILE_701";
```

### 3. 编号分配策略
- **连续分配**: 同一技术类别内连续分配编号
- **预留空间**: 每个类别预留扩展空间
- **避免冲突**: 严格按分段分配，避免重叠

## 项目异常归类指导

### 1. UID库异常归类示例

**原有异常分析**:
```java
// 原有错误码 (存在冲突)
XCE_UID_800: UID生成失败
XCE_UID_801: Worker ID分配失败
XCE_UID_802: 实例注册失败
XCE_UID_803: 实例恢复失败
XCE_UID_804: 租约续约失败
XCE_UID_805: 机器特征码收集失败
```

**重新归类结果**:
```java
// 业务逻辑类异常
XCE_BIZ_180: UID生成失败
XCE_BIZ_181: Worker ID分配失败
XCE_BIZ_182: 实例注册失败

// 数据库类异常
XCE_DB_680: 实例恢复失败 (数据库查询)
XCE_DB_681: 租约续约失败 (数据库更新)

// 文件类异常
XCE_FILE_720: 机器特征码收集失败 (文件访问)
```

### 2. 其他项目归类指导

**用户管理项目**:
```java
// 验证类异常
XCE_VAL_760: 用户名格式验证失败
XCE_VAL_761: 密码强度验证失败

// 安全类异常
XCE_SEC_810: 用户认证失败
XCE_SEC_811: 用户授权被拒绝

// 数据库类异常
XCE_DB_660: 用户查询失败
XCE_DB_661: 用户更新失败
```

## 扩展指导

### 1. 新技术类别扩展
**步骤**:
1. 评估是否属于现有技术类别
2. 如需新类别，从900段开始分配
3. 创建对应包和异常类
4. 更新分类标准文档

**示例**:
```java
// 消息队列类异常 (新增)
900-949: 消息队列类异常
XCE_MQ_900: 消息发送失败
XCE_MQ_901: 消息消费失败
```

### 2. 现有类别扩展
**原则**:
- 在对应技术类别段内分配
- 保持命名规范一致
- 更新相关文档

## 质量保证

### 1. 分类检查清单
- [ ] 按主要诱发因素分类
- [ ] 错误码无冲突
- [ ] 命名规范一致
- [ ] 包路径正确
- [ ] 文档更新完整

### 2. 代码审查要点
- 异常分类是否正确
- 错误码是否符合规范
- 命名是否一致
- metadata是否合理使用

### 3. 持续改进
- 定期评估分类合理性
- 收集开发者反馈
- 优化分类标准
- 更新最佳实践

## 实施建议

### 1. 渐进式迁移
- 新项目严格按标准实施
- 现有项目逐步迁移
- 保持向后兼容

### 2. 工具支持
- 开发IDE插件辅助分类
- 静态分析工具检查规范
- 自动化测试验证

### 3. 培训推广
- 团队培训分类标准
- 提供最佳实践示例
- 建立问题反馈机制

## 总结

XCE异常分类标准建立了基于技术类别的异常分类体系，通过统一的错误码分配和命名规范，为xkongcloud平台提供了一致、可扩展、易维护的异常处理框架。

**核心价值**:
1. **统一性**: 平台级统一异常分类
2. **复用性**: 技术类别异常跨项目复用
3. **可维护性**: 清晰的分类和命名规范
4. **扩展性**: 为未来发展预留空间
