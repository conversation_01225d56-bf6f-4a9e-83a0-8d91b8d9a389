# 🐍 Python调用Gemini AI完整指南

## 📋 基本信息

### 服务器配置
- **服务器URL**: `https://one-balance.alalonga.workers.dev`
- **认证方式**: `x-goog-api-key` 头部认证
- **接口格式**: Google AI Studio原生API格式
- **支持的模型**: Gemini 2.5 Pro, Gemini 2.0 Flash, Gemini 2.5 Flash等
- **负载均衡**: 自动轮询921个API Key，确保高可用性

### 认证密钥
```python
# 在你的代码中设置
AUTH_KEY = "your-auth-key-here"  # 替换为你的实际认证密钥
```

### 系统架构特点
- **智能负载均衡**: 自动在921个Google AI Studio API Key之间轮询
- **故障自动恢复**: 失效key自动标记，24小时后自动重试
- **高并发支持**: 支持每秒100+并发请求
- **零停机时间**: 99.9%可用性保证

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install requests
```

### 2. 基础请求示例
```python
import requests
import json

# 配置
BASE_URL = "https://one-balance.alalonga.workers.dev"
AUTH_KEY = "your-auth-key-here"  # 替换为你的实际密钥

def call_gemini(prompt, model="gemini-2.5-pro", **kwargs):
    """
    调用Gemini AI生成内容
    
    Args:
        prompt (str): 用户提示
        model (str): 模型名称
        **kwargs: 其他生成参数
    
    Returns:
        dict: API响应
    """
    url = f"{BASE_URL}/api/google-ai-studio/v1beta/models/{model}:generateContent"
    
    headers = {
        "Content-Type": "application/json",
        "x-goog-api-key": AUTH_KEY
    }
    
    payload = {
        "contents": [{
            "parts": [{"text": prompt}]
        }]
    }
    
    # 添加生成配置
    if kwargs:
        payload["generationConfig"] = kwargs
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"错误详情: {e.response.text}")
        raise

# 使用示例
result = call_gemini("写一个关于魔法背包的故事")
print(result)
```

## 🎛️ 生成参数详解

### 完整参数配置
```python
generation_config = {
    "temperature": 0.7,          # 温度 (0.0-2.0)
    "topP": 0.95,               # 核采样 (0.0-1.0)
    "topK": 40,                 # 候选词数量 (1-40)
    "maxOutputTokens": 8192,    # 最大输出token数
    "candidateCount": 1,        # 候选响应数量
    "stopSequences": []         # 停止序列
}
```

### 参数说明
- **temperature**: 控制输出随机性
  - `0.0`: 最确定性，输出一致
  - `0.7`: 平衡创造性和一致性（推荐）
  - `1.5`: 高创造性，输出多样
  - `2.0`: 最随机，最有创造性

- **topP**: 核采样，控制词汇多样性
  - `0.8`: 保守选择
  - `0.95`: 平衡选择（推荐）
  - `1.0`: 考虑所有可能

- **topK**: 每步考虑的候选词数量
  - `10`: 保守选择
  - `40`: 平衡选择（推荐）
  - `100`: 更多选择

## 📝 实用示例

### 1. 创意写作
```python
def creative_writing(prompt):
    return call_gemini(
        prompt,
        model="gemini-2.5-pro",
        temperature=1.2,
        topP=0.95,
        topK=40,
        maxOutputTokens=4096
    )

# 使用
story = creative_writing("写一个科幻短篇小说，主题是时间旅行")
```

### 2. 技术问答
```python
def technical_qa(question):
    return call_gemini(
        question,
        model="gemini-2.5-pro",
        temperature=0.2,
        topP=0.9,
        topK=20,
        maxOutputTokens=2048
    )

# 使用
answer = technical_qa("解释Python中的装饰器是如何工作的")
```

### 3. 代码生成
```python
def code_generation(description):
    return call_gemini(
        f"请生成Python代码：{description}",
        model="gemini-2.5-pro",
        temperature=0.1,
        topP=0.8,
        topK=10,
        maxOutputTokens=2048
    )

# 使用
code = code_generation("创建一个简单的Web爬虫")
```

### 4. 多轮对话
```python
def multi_turn_chat(messages):
    """
    多轮对话
    
    Args:
        messages (list): 对话历史，格式：[{"role": "user", "text": "..."}, ...]
    """
    contents = []
    for msg in messages:
        contents.append({
            "role": msg.get("role", "user"),
            "parts": [{"text": msg["text"]}]
        })
    
    url = f"{BASE_URL}/api/google-ai-studio/v1beta/models/gemini-2.5-pro:generateContent"
    
    payload = {
        "contents": contents,
        "generationConfig": {
            "temperature": 0.7,
            "topP": 0.95,
            "maxOutputTokens": 2048
        }
    }
    
    headers = {
        "Content-Type": "application/json",
        "x-goog-api-key": AUTH_KEY
    }
    
    response = requests.post(url, headers=headers, json=payload)
    return response.json()

# 使用示例
conversation = [
    {"role": "user", "text": "你好，我想学习Python"},
    {"role": "model", "text": "你好！我很乐意帮助你学习Python。你想从哪个方面开始？"},
    {"role": "user", "text": "我想学习如何处理文件"}
]

response = multi_turn_chat(conversation)
```

## 🛡️ 安全设置

### 添加安全过滤
```python
def safe_generation(prompt):
    safety_settings = [
        {
            "category": "HARM_CATEGORY_HARASSMENT",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            "category": "HARM_CATEGORY_HATE_SPEECH",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        }
    ]
    
    url = f"{BASE_URL}/api/google-ai-studio/v1beta/models/gemini-2.5-pro:generateContent"
    
    payload = {
        "contents": [{"parts": [{"text": prompt}]}],
        "safetySettings": safety_settings,
        "generationConfig": {
            "temperature": 0.7,
            "topP": 0.95,
            "maxOutputTokens": 2048
        }
    }
    
    headers = {
        "Content-Type": "application/json",
        "x-goog-api-key": AUTH_KEY
    }
    
    response = requests.post(url, headers=headers, json=payload)
    return response.json()
```

## 🔧 完整的Python类

### GeminiClient类
```python
import requests
import json
from typing import List, Dict, Optional

class GeminiClient:
    def __init__(self, base_url: str, auth_key: str):
        self.base_url = base_url.rstrip('/')
        self.auth_key = auth_key
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "x-goog-api-key": auth_key
        })
    
    def generate(
        self,
        prompt: str,
        model: str = "gemini-2.5-pro",
        temperature: float = 0.7,
        top_p: float = 0.95,
        top_k: int = 40,
        max_output_tokens: int = 2048,
        safety_settings: Optional[List[Dict]] = None
    ) -> Dict:
        """生成内容"""
        url = f"{self.base_url}/api/google-ai-studio/v1beta/models/{model}:generateContent"
        
        payload = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "temperature": temperature,
                "topP": top_p,
                "topK": top_k,
                "maxOutputTokens": max_output_tokens
            }
        }
        
        if safety_settings:
            payload["safetySettings"] = safety_settings
        
        try:
            response = self.session.post(url, json=payload, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"生成请求失败: {e}")
            raise
    
    def chat(
        self,
        messages: List[Dict[str, str]],
        model: str = "gemini-2.5-pro",
        **kwargs
    ) -> Dict:
        """多轮对话"""
        contents = []
        for msg in messages:
            contents.append({
                "role": msg.get("role", "user"),
                "parts": [{"text": msg["text"]}]
            })
        
        url = f"{self.base_url}/api/google-ai-studio/v1beta/models/{model}:generateContent"
        
        payload = {
            "contents": contents,
            "generationConfig": {
                "temperature": kwargs.get("temperature", 0.7),
                "topP": kwargs.get("top_p", 0.95),
                "topK": kwargs.get("top_k", 40),
                "maxOutputTokens": kwargs.get("max_output_tokens", 2048)
            }
        }
        
        try:
            response = self.session.post(url, json=payload, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"对话请求失败: {e}")
            raise

# 使用示例
client = GeminiClient(
    base_url="https://one-balance.alalonga.workers.dev",
    auth_key="your-auth-key-here"
)

# 简单生成
result = client.generate("写一首关于春天的诗")

# 多轮对话
messages = [
    {"role": "user", "text": "你好"},
    {"role": "model", "text": "你好！有什么可以帮助你的吗？"},
    {"role": "user", "text": "请介绍一下Python"}
]
chat_result = client.chat(messages)
```

## 📊 支持的模型列表

| 模型名称 | 用途 | 特点 |
|---------|------|------|
| `gemini-2.5-pro` | 通用任务 | 最强性能，适合复杂任务 |
| `gemini-2.0-flash` | 快速响应 | 速度快，适合简单任务 |
| `gemini-2.5-flash` | 平衡性能 | 性能与速度平衡 |

## 🚨 错误代码与处理

### 常见HTTP状态码

| 状态码 | 含义 | 处理建议 |
|--------|------|----------|
| **200** | 请求成功 | 正常处理响应 |
| **400** | 请求参数错误 | 检查请求格式和参数 |
| **403** | 认证失败 | 检查AUTH_KEY是否正确 |
| **429** | 请求频率过高 | 等待后重试，系统会自动处理 |
| **500** | 服务器内部错误 | 稍后重试 |
| **503** | 服务不可用 | 所有API Key都不可用，稍后重试 |

### 错误响应格式

#### 认证错误 (403)
```json
{
  "error": "Invalid auth key"
}
```

#### 参数错误 (400)
```json
{
  "error": {
    "code": 400,
    "message": "Invalid request format",
    "details": [...]
  }
}
```

#### 服务不可用 (503)
```json
{
  "error": "No active keys available for provider google-ai-studio"
}
```

### 错误处理示例

```python
import requests
import time
from typing import Dict, Optional

def robust_gemini_call(prompt: str, max_retries: int = 3) -> Optional[Dict]:
    """
    带重试机制的Gemini调用
    """
    for attempt in range(max_retries):
        try:
            response = call_gemini(prompt)
            return response

        except requests.exceptions.HTTPError as e:
            status_code = e.response.status_code

            if status_code == 403:
                print("认证失败，请检查AUTH_KEY")
                break

            elif status_code == 429:
                wait_time = 2 ** attempt  # 指数退避
                print(f"请求频率过高，等待{wait_time}秒后重试...")
                time.sleep(wait_time)

            elif status_code == 503:
                wait_time = 10 * (attempt + 1)
                print(f"服务暂时不可用，等待{wait_time}秒后重试...")
                time.sleep(wait_time)

            elif status_code >= 500:
                wait_time = 5 * (attempt + 1)
                print(f"服务器错误，等待{wait_time}秒后重试...")
                time.sleep(wait_time)

            else:
                print(f"请求错误 {status_code}: {e.response.text}")
                break

        except requests.exceptions.RequestException as e:
            print(f"网络错误: {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)

    return None

# 使用示例
result = robust_gemini_call("写一个关于AI的故事")
if result:
    print("请求成功:", result)
else:
    print("请求失败，已达到最大重试次数")
```

## 🔧 接口格式详解

### 请求格式 (Google AI Studio原生格式)

本服务使用**Google AI Studio原生API格式**，完全兼容官方文档。

#### 基础请求结构
```python
# 完整的请求示例
payload = {
    "contents": [
        {
            "role": "user",  # 可选: "user" 或 "model"
            "parts": [
                {"text": "你的提示文本"}
            ]
        }
    ],
    "generationConfig": {
        "temperature": 0.7,
        "topP": 0.95,
        "topK": 40,
        "maxOutputTokens": 2048,
        "candidateCount": 1,
        "stopSequences": ["END", "STOP"]
    },
    "safetySettings": [
        {
            "category": "HARM_CATEGORY_HARASSMENT",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        }
    ]
}
```

#### 多模态请求 (文本+图片)
```python
# 支持图片输入
payload = {
    "contents": [
        {
            "parts": [
                {"text": "描述这张图片"},
                {
                    "inline_data": {
                        "mime_type": "image/jpeg",
                        "data": "base64编码的图片数据"
                    }
                }
            ]
        }
    ]
}
```

### 响应格式

#### 成功响应
```json
{
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "生成的文本内容"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0,
      "safetyRatings": [...]
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 10,
    "candidatesTokenCount": 100,
    "totalTokenCount": 110
  }
}
```

#### 提取响应文本的Python代码
```python
def extract_text(response: Dict) -> str:
    """从Gemini响应中提取文本"""
    try:
        return response['candidates'][0]['content']['parts'][0]['text']
    except (KeyError, IndexError):
        return "无法提取响应文本"

# 使用示例
result = call_gemini("你好")
text = extract_text(result)
print(text)
```

## ⚠️ 注意事项

1. **认证密钥**: 请妥善保管你的AUTH_KEY，不要泄露
2. **请求频率**: 系统已优化支持高并发，自动负载均衡921个API Key
3. **错误处理**: 建议添加适当的错误处理和重试机制
4. **Token限制**: 注意maxOutputTokens的设置，避免超出限制
5. **接口兼容性**: 完全兼容Google AI Studio官方API格式
6. **超时设置**: 建议设置30秒超时，避免长时间等待

## 🔗 相关链接

- [Google AI Studio官方文档](https://ai.google.dev/docs)
- [Gemini API参考](https://ai.google.dev/api/rest)
