# AI修改指南

## 概述

本指南为AI提供了修改数据分析引擎的最佳实践和规则，确保AI能够以最小的代码改动实现新的分析功能。

## 核心原则

### 1. 最小改动原则
- **新增分析** = 实现新的Strategy接口
- **修改分析** = 修改Strategy类的analyze方法
- **控制行为** = 修改JSON配置文件
- **禁用功能** = 修改注解参数或配置文件

### 2. 约定优于配置
- 遵循标准的命名约定
- 使用预定义的数据结构
- 遵循接口契约

### 3. 自动发现机制
- 使用@AnalysisComponent注解标记策略类
- 引擎自动发现和注册组件
- 无需手动注册或配置

## AI修改操作指南

### 操作1：添加新的分析策略

**步骤1：创建策略类**
```java
@AnalysisComponent(
    name = "new-analysis",
    description = "新的分析功能",
    priority = 10,
    enabled = true
)
public class NewAnalysisStrategy implements AnalysisStrategy {
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        // AI实现分析逻辑
        NewAnalysisData data = new NewAnalysisData();
        // ... 收集和分析数据
        return new DefaultAnalysisResult(data, createMetadata());
    }
    
    @Override
    public String getOutputFileName() {
        return "new-analysis.json";
    }
}
```

**步骤2：定义数据模型（如果需要）**
```java
public class NewAnalysisData {
    private String analysisType;
    private List<String> findings;
    private double confidenceScore;
    
    // getters and setters
}
```

**步骤3：配置启用（可选）**
```json
// 在 config/analysis-engine-config.json 中添加
"new-analysis": {
    "enabled": true,
    "priority": 10,
    "timeout_ms": 15000,
    "parameters": {
        "detail_level": "medium"
    }
}
```

### 操作2：修改现有分析策略

**只需要修改Strategy类的analyze方法：**
```java
@Override
public AnalysisResult analyze(AnalysisContext context) {
    // AI修改这里的分析逻辑
    
    // 获取数据
    AITestResult testResult = context.getTestResult(AITestResult.class);
    
    // 修改分析逻辑
    PostgreSQLMigrationAnalysisData data = new PostgreSQLMigrationAnalysisData();
    
    // AI可以添加新的分析维度
    data.setNewAnalysisDimension(analyzeNewDimension(testResult));
    
    // AI可以修改现有分析逻辑
    data.setRiskAssessment(performEnhancedRiskAssessment(testResult));
    
    return new DefaultAnalysisResult(data, createMetadata());
}
```

### 操作3：禁用或启用分析策略

**方法1：修改注解**
```java
@AnalysisComponent(
    name = "some-analysis",
    enabled = false  // 禁用此策略
)
```

**方法2：修改配置文件**
```json
"some-analysis": {
    "enabled": false  // 禁用此策略
}
```

### 操作4：调整分析优先级

**方法1：修改注解**
```java
@AnalysisComponent(
    priority = 1  // 最高优先级
)
```

**方法2：修改配置文件**
```json
"some-analysis": {
    "priority": 1  // 最高优先级
}
```

### 操作5：自定义输出文件名

**方法1：修改注解**
```java
@AnalysisComponent(
    outputFileName = "custom-report.json"
)
```

**方法2：重写方法**
```java
@Override
public String getOutputFileName() {
    return "custom-report.json";
}
```

## 数据访问模式

### 获取测试结果数据
```java
AITestResult testResult = context.getTestResult(AITestResult.class);

// 基础数据
int totalTests = testResult.getTotalTestCount();
int passedTests = testResult.getPassedTestCount();
double passRate = testResult.getOverallPassRate();

// 失败测试详情
List<String> failedTests = testResult.getFailedTests();
```

### 获取项目结构数据
```java
ProjectStructure structure = context.getProjectStructure(ProjectStructure.class);

// 模块信息
List<String> modules = structure.getModules();
Map<String, Integer> packageCounts = structure.getPackageCounts();
```

### 获取配置信息
```java
String configValue = context.getConfiguration("some.config.key", String.class);
Map<String, Object> allConfigs = context.getConfiguration("", Map.class);
```

### 获取分析参数
```java
Map<String, Object> parameters = context.getParameters();
String detailLevel = (String) parameters.get("detail_level");
boolean includeMetrics = (Boolean) parameters.getOrDefault("include_metrics", false);
```

## 输出数据结构规范

### 标准分析结果结构
```java
public class StandardAnalysisData {
    private String analysisType;           // 分析类型
    private LocalDateTime timestamp;       // 分析时间戳
    private String version;               // 分析版本
    private double confidenceScore;       // 置信度评分
    private List<Finding> findings;       // 分析发现
    private List<Recommendation> recommendations; // 建议
    private Map<String, Object> metadata; // 元数据
    
    // getters and setters
}
```

### 风险评估数据结构
```java
public class RiskAssessmentData {
    private List<Risk> risks;
    
    public void addRisk(String name, double probability, String severity, String description) {
        risks.add(new Risk(name, probability, severity, description));
    }
}
```

### 建议数据结构
```java
public class Recommendation {
    private String title;
    private String priority;      // HIGH, MEDIUM, LOW
    private String description;
    private List<String> actions;
    private double confidence;
    
    // getters and setters
}
```

## 常见分析模式

### 模式1：基于测试结果的分析
```java
@Override
public AnalysisResult analyze(AnalysisContext context) {
    AITestResult testResult = context.getTestResult(AITestResult.class);
    
    // 分析测试通过率
    double passRate = testResult.getOverallPassRate();
    
    // 分析失败模式
    List<String> failurePatterns = analyzeFailurePatterns(testResult);
    
    // 生成建议
    List<Recommendation> recommendations = generateRecommendations(passRate, failurePatterns);
    
    return createResult(passRate, failurePatterns, recommendations);
}
```

### 模式2：基于配置的分析
```java
@Override
public AnalysisResult analyze(AnalysisContext context) {
    // 获取Spring Boot配置
    Map<String, Object> springConfig = context.getConfiguration("spring", Map.class);
    
    // 分析配置冲突
    List<ConfigConflict> conflicts = detectConfigConflicts(springConfig);
    
    // 生成解决方案
    List<Solution> solutions = generateSolutions(conflicts);
    
    return createResult(conflicts, solutions);
}
```

### 模式3：基于架构的分析
```java
@Override
public AnalysisResult analyze(AnalysisContext context) {
    ProjectStructure structure = context.getProjectStructure(ProjectStructure.class);
    
    // 分析模块依赖
    Map<String, List<String>> dependencies = analyzeDependencies(structure);
    
    // 计算复杂度指标
    double complexity = calculateComplexity(dependencies);
    
    // 识别架构风险
    List<ArchitecturalRisk> risks = identifyArchitecturalRisks(complexity, dependencies);
    
    return createResult(complexity, risks);
}
```

## 错误处理最佳实践

### 1. 优雅的错误处理
```java
@Override
public AnalysisResult analyze(AnalysisContext context) {
    try {
        // 分析逻辑
        return successResult;
    } catch (DataNotFoundException e) {
        return new FailedAnalysisResult("所需数据不可用", e);
    } catch (AnalysisException e) {
        return new FailedAnalysisResult("分析执行失败", e);
    } catch (Exception e) {
        return new FailedAnalysisResult("未知错误", e);
    }
}
```

### 2. 条件检查
```java
@Override
public boolean canExecute(AnalysisContext context) {
    try {
        AITestResult testResult = context.getTestResult(AITestResult.class);
        return testResult != null && testResult.getTotalTestCount() > 0;
    } catch (Exception e) {
        return false;
    }
}
```

## 性能优化建议

### 1. 避免重复计算
```java
// 缓存计算结果
private static final Map<String, Object> cache = new ConcurrentHashMap<>();

private Object expensiveCalculation(String key) {
    return cache.computeIfAbsent(key, k -> doExpensiveCalculation(k));
}
```

### 2. 流式处理大数据
```java
// 使用Stream API处理大量数据
List<Result> results = largeDataSet.stream()
    .filter(this::isRelevant)
    .map(this::transform)
    .collect(Collectors.toList());
```

### 3. 设置合理的超时时间
```java
@AnalysisComponent(
    name = "heavy-analysis",
    expectedExecutionTimeMs = 30000,  // 预期30秒
    maxExecutionTimeMs = 60000        // 最大60秒
)
```

## 调试和测试

### 1. 启用调试模式
```json
// 在配置文件中启用调试
"feature_flags": {
    "enable_debug_mode": true
}
```

### 2. 单独测试策略
```java
// 创建测试用的上下文
AnalysisContext testContext = createTestContext();

// 执行策略
AnalysisStrategy strategy = new YourAnalysisStrategy();
AnalysisResult result = strategy.analyze(testContext);

// 验证结果
assert result.isSuccess();
```

## 总结

遵循这些指南，AI可以：
- 以最小的代码改动添加新的分析功能
- 安全地修改现有分析逻辑
- 通过配置文件灵活控制行为
- 确保代码质量和性能
