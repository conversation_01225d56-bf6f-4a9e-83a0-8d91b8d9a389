---
title: PostgreSQL迁移 Phase 2[3] 实施计划 - F004 UID库完整重构（测试与配置）
document_id: F003-PHASE2-003
document_type: 实施计划
category: 数据库迁移
scope: F004 UID库重构
keywords: [PostgreSQL, F004, UID库, 测试验证, 配置管理, 监控指标]
created_date: 2025-01-15
updated_date: 2025-06-01
status: 已完成
version: 1.0
authors: [AI助手]
affected_features:
  - F003 # PostgreSQL迁移
  - F004 # Commons UID Library
related_docs:
  - ./phase2[1]-implementation-plan.md
  - ./phase2[2]-implementation-plan.md
  - ../../../features/F004-CommonsUidLibrary-20250511/design/postgresql-persistent-id-fingerprint-recovery.md
---

# PostgreSQL迁移 Phase 2[3] 实施计划 - F004 UID库完整重构（测试与配置）

> 本文档是 phase2[1] 和 phase2[2] 的续篇，包含完整的测试验证方案、配置管理和监控指标实现。

## 阶段4：测试验证方案

### 4.1 单元测试重构

**完整的测试工具类**：

```java
/**
 * 测试用机器特征码创建工具
 */
public class TestFingerprintUtils {
    
    /**
     * 创建测试用的机器特征码
     */
    public static Map<String, Object> createTestFingerprints(String biosUuid, String... macAddresses) {
        Map<String, Object> fingerprints = new HashMap<>();
        
        // 基本信息
        fingerprints.put("hostname", "test-host-" + System.currentTimeMillis());
        fingerprints.put("os_name", "Linux");
        fingerprints.put("os_version", "5.4.0");
        
        // 核心特征码
        if (biosUuid != null) {
            fingerprints.put("bios_uuid", biosUuid);
        }
        
        if (macAddresses != null && macAddresses.length > 0) {
            fingerprints.put("mac_addresses", Arrays.asList(macAddresses));
        }
        
        // 系统序列号
        fingerprints.put("system_serial", "TEST-SERIAL-" + biosUuid);
        
        // 云元数据（可选）
        Map<String, String> cloudMetadata = new HashMap<>();
        cloudMetadata.put("aws_instance_id", "i-test" + biosUuid);
        fingerprints.put("cloud_metadata", cloudMetadata);
        
        return fingerprints;
    }
    
    /**
     * 创建高匹配度的特征码对
     */
    public static Pair<Map<String, Object>, Map<String, Object>> createHighMatchPair() {
        String biosUuid = "test-bios-123";
        String[] macs = {"00:11:22:33:44:55", "00:11:22:33:44:66"};
        
        Map<String, Object> current = createTestFingerprints(biosUuid, macs);
        Map<String, Object> stored = createTestFingerprints(biosUuid, macs);
        
        return Pair.of(current, stored);
    }
    
    /**
     * 创建低匹配度的特征码对
     */
    public static Pair<Map<String, Object>, Map<String, Object>> createLowMatchPair() {
        Map<String, Object> current = createTestFingerprints("bios-123", "00:11:22:33:44:55");
        Map<String, Object> stored = createTestFingerprints("bios-456", "00:11:22:33:44:66");
        
        // 只保留主机名匹配
        current.put("hostname", "same-host");
        stored.put("hostname", "same-host");
        
        return Pair.of(current, stored);
    }
}

/**
 * 机器特征码匹配测试
 */
@Test
public void testFingerprintMatching() {
    // 测试高置信度匹配
    Pair<Map<String, Object>, Map<String, Object>> highMatch = TestFingerprintUtils.createHighMatchPair();
    int highScore = persistentInstanceManager.calculateMatchScore(
        highMatch.getLeft(), highMatch.getRight());

    assertTrue("高置信度匹配分数应该 >= 150", highScore >= 150);
    log.info("高置信度匹配分数: {}", highScore);

    // 测试低置信度匹配
    Pair<Map<String, Object>, Map<String, Object>> lowMatch = TestFingerprintUtils.createLowMatchPair();
    int lowScore = persistentInstanceManager.calculateMatchScore(
        lowMatch.getLeft(), lowMatch.getRight());

    assertTrue("低置信度匹配分数应该在70-149之间", lowScore >= 70 && lowScore < 150);
    log.info("低置信度匹配分数: {}", lowScore);

    // 测试无匹配
    Map<String, Object> current = TestFingerprintUtils.createTestFingerprints("bios-123");
    Map<String, Object> stored = TestFingerprintUtils.createTestFingerprints("bios-456");
    int noMatchScore = persistentInstanceManager.calculateMatchScore(current, stored);

    assertTrue("无匹配分数应该 < 70", noMatchScore < 70);
    log.info("无匹配分数: {}", noMatchScore);
}

/**
 * 续约重试机制测试
 */
@Test
public void testRenewalRetryMechanism() {
    // 模拟网络故障
    when(jdbcTemplate.update(any(), any(), any()))
        .thenThrow(new ConnectException("Network error"))
        .thenReturn(1); // 第二次成功

    // 执行续约
    workerIdAssigner.renewLeaseTask();

    // 验证重试逻辑
    verify(jdbcTemplate, times(2)).update(any(), any(), any());
    
    // 验证成功续约
    assertEquals("续约应该成功", 1, workerIdAssigner.getLastRenewalResult());
}

/**
 * 防拥堵机制测试
 */
@Test
public void testAntiCongestionMechanism() {
    // 测试多实例的续约时间分散
    List<Long> intervals = new ArrayList<>();
    
    // 模拟100个实例
    for (int i = 0; i < 100; i++) {
        // 模拟不同的实例ID
        when(instanceManager.getInstanceId()).thenReturn((long) i);
        intervals.add(workerIdAssigner.calculateAntiCongestionInterval());
    }

    // 验证时间分散性
    long variance = calculateVariance(intervals);
    assertTrue("续约时间应该有足够的分散性", variance > 0);
    
    // 验证时间范围合理性
    long minInterval = Collections.min(intervals);
    long maxInterval = Collections.max(intervals);
    assertTrue("最小间隔应该 >= 10秒", minInterval >= 10);
    assertTrue("最大间隔应该合理", maxInterval <= 600); // 假设基础间隔为300秒
}

/**
 * 计算方差的辅助方法
 */
private long calculateVariance(List<Long> values) {
    double mean = values.stream().mapToLong(Long::longValue).average().orElse(0.0);
    double variance = values.stream()
        .mapToDouble(v -> Math.pow(v - mean, 2))
        .average()
        .orElse(0.0);
    return (long) variance;
}
```

### 4.2 集成测试方案

```java
/**
 * 并发实例启动测试
 */
@Test
public void testConcurrentInstanceStartup() throws InterruptedException {
    int instanceCount = 10;
    CountDownLatch latch = new CountDownLatch(instanceCount);
    List<Future<Long>> futures = new ArrayList<>();
    ExecutorService executor = Executors.newFixedThreadPool(instanceCount);

    try {
        for (int i = 0; i < instanceCount; i++) {
            final int instanceIndex = i;
            futures.add(executor.submit(() -> {
                try {
                    // 创建独立的实例管理器
                    PersistentInstanceManager manager = createTestManager("test-app-" + instanceIndex);
                    manager.initializeInstanceId();
                    return manager.getInstanceId();
                } finally {
                    latch.countDown();
                }
            }));
        }

        // 等待所有实例完成
        assertTrue("所有实例应该在30秒内完成初始化", latch.await(30, TimeUnit.SECONDS));

        // 验证所有实例都获得了唯一的ID
        Set<Long> instanceIds = futures.stream()
            .map(f -> {
                try {
                    return f.get();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            })
            .collect(Collectors.toSet());

        assertEquals("所有实例应该获得唯一ID", instanceCount, instanceIds.size());
        log.info("成功创建 {} 个唯一实例ID: {}", instanceCount, instanceIds);
        
    } finally {
        executor.shutdown();
    }
}

/**
 * 网络故障恢复测试
 */
@Test
public void testNetworkFailureRecovery() {
    // 模拟网络中断
    when(jdbcTemplate.update(any(), any(), any()))
        .thenThrow(new ConnectException("Connection refused"))
        .thenThrow(new SocketTimeoutException("Read timeout"))
        .thenReturn(1); // 第三次成功

    // 启动续约任务
    workerIdAssigner.startRenewalScheduler();
    
    // 等待重试完成
    await().atMost(10, TimeUnit.SECONDS)
           .until(() -> workerIdAssigner.getLastRenewalResult() == 1);

    // 验证重试次数
    verify(jdbcTemplate, times(3)).update(any(), any(), any());
}
```

## 阶段5：配置管理和监控

### 5.1 配置文件更新

**application.yml 新增配置**：

```yaml
# UID库配置
uid:
  instance:
    # 实例恢复配置
    recovery:
      enabled: true
      strategy: ALERT_AUTO_WITH_TIMEOUT
      timeout-seconds: 300
      accept-low-confidence: false

    # 实例ID加密配置
    encryption:
      enabled: false
      algorithm: AES-256-GCM

    # 文件存储配置
    storage:
      path: ${user.home}/.xkong/instance.id
      backup-enabled: true
      backup-count: 3

  worker:
    # Worker ID分配配置
    assignment:
      max-worker-id: 262143
      lease-duration-seconds: 300
      renewal-interval-ratio: 0.33

    # 续约重试配置
    renewal:
      retry:
        network:
          max-retries: 3
          base-delay-ms: 1000
        database:
          max-retries: 5
          base-delay-ms: 2000
        resource:
          max-retries: 2
          base-delay-ms: 10000

      # 防拥堵配置
      anti-congestion:
        enabled: true
        random-offset-ratio: 0.2
        min-interval-seconds: 10
        max-interval-ratio: 2.0

  # 数据库配置
  database:
    schema: infra_uid
    connection-timeout: 30000
    query-timeout: 10000

# 监控配置
management:
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: ${spring.application.name}
      environment: ${spring.profiles.active}
```

**配置属性类**：

```java
/**
 * UID库配置属性
 */
@ConfigurationProperties(prefix = "uid")
@Data
public class UidProperties {

    private Instance instance = new Instance();
    private Worker worker = new Worker();
    private Database database = new Database();

    @Data
    public static class Instance {
        private Recovery recovery = new Recovery();
        private Encryption encryption = new Encryption();
        private Storage storage = new Storage();

        @Data
        public static class Recovery {
            private boolean enabled = true;
            private String strategy = "ALERT_AUTO_WITH_TIMEOUT";
            private int timeoutSeconds = 300;
            private boolean acceptLowConfidence = false;
        }

        @Data
        public static class Encryption {
            private boolean enabled = false;
            private String algorithm = "AES-256-GCM";
        }

        @Data
        public static class Storage {
            private String path = "${user.home}/.xkong/instance.id";
            private boolean backupEnabled = true;
            private int backupCount = 3;
        }
    }

    @Data
    public static class Worker {
        private Assignment assignment = new Assignment();
        private Renewal renewal = new Renewal();

        @Data
        public static class Assignment {
            private int maxWorkerId = 262143;
            private int leaseDurationSeconds = 300;
            private double renewalIntervalRatio = 0.33;
        }

        @Data
        public static class Renewal {
            private Retry retry = new Retry();
            private AntiCongestion antiCongestion = new AntiCongestion();

            @Data
            public static class Retry {
                private RetryConfig network = new RetryConfig(3, 1000L);
                private RetryConfig database = new RetryConfig(5, 2000L);
                private RetryConfig resource = new RetryConfig(2, 10000L);

                @Data
                @AllArgsConstructor
                public static class RetryConfig {
                    private int maxRetries;
                    private long baseDelayMs;
                }
            }

            @Data
            public static class AntiCongestion {
                private boolean enabled = true;
                private double randomOffsetRatio = 0.2;
                private int minIntervalSeconds = 10;
                private double maxIntervalRatio = 2.0;
            }
        }
    }

    @Data
    public static class Database {
        private String schema = "infra_uid";
        private int connectionTimeout = 30000;
        private int queryTimeout = 10000;
    }
}
```
