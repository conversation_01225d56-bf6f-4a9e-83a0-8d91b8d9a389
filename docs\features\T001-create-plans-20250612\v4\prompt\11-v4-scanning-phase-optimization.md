# V4.0扫描阶段任务优化设计

## 🎯 V4扫描阶段核心目标

基于V4一体化架构，设计专门的扫描阶段任务，实现：
- **设计文档完备度**: ≥90%（确保后续阶段质量）
- **JSON完整度**: ≥90%（支持精准实施计划生成）
- **扫描精准度**: ≥95%（结构化信息提取准确性）
- **AI认知边界合规性**: 100%（防止幻觉和记忆溢出）

## 🔄 V4扫描阶段任务流程

### 📋 阶段A：扫描阶段任务详细设计

```yaml
v4_scanning_phase:
  input: "设计文档目录（如design/v1/）"
  
  execution_modes:
    default_mode:
      name: "Python AI智能扫描"
      description: "使用AI进行深度语义分析和结构化提取"
      performance: "高精度，较慢"
      use_case: "复杂架构文档，需要深度理解"
      
    algorithm_mode:
      name: "算法扫描模式"
      description: "使用V3扫描器算法进行快速扫描"
      performance: "中等精度，快速"
      use_case: "简单文档，节约时间"
      command_parameter: "--use-algorithm-scanning"
  
  output_structure:
    checkresult_directory:
      - "详细检查报告（每个文档一个）"
      - "ai-prompt-batch-improvement.md（批量修改指令）"
      - "quality-overview-report.md（质量汇总报告）"
      - "扫描结果数据.json（结构化数据）"
      - "整体扫描汇总报告.md（总体分析）"
  
  quality_standards:
    document_completeness: "≥90%"
    json_completeness: "≥90%"
    extraction_accuracy: "≥95%"
    cognitive_compliance: "100%"
  
  iterative_optimization:
    process: "扫描→分析→修改→重新扫描→验证→达标"
    termination_condition: "设计文档完备度≥90% AND JSON完整度≥90%"
    max_iterations: 5
```

## 🔧 V4扫描阶段核心组件

### 1. V4智能扫描控制器
```python
class V4ScanningPhaseController:
    """V4扫描阶段任务控制器"""
    
    def __init__(self, scanning_mode: str = "ai_intelligent"):
        self.scanning_mode = scanning_mode
        self.v3_scanner = AdvancedDesignDocScanner()
        self.ai_scanner = V4AIIntelligentScanner()
        self.quality_assessor = V4ScanQualityAssessor()
        
    def execute_scanning_phase(self, design_doc_directory: str) -> Dict:
        """执行V4扫描阶段任务"""
        
        iteration = 0
        max_iterations = 5
        
        while iteration < max_iterations:
            print(f"🔍 V4扫描阶段 - 第{iteration + 1}轮扫描")
            
            # 1. 执行扫描
            if self.scanning_mode == "algorithm":
                scan_result = self._execute_algorithm_scanning(design_doc_directory)
            else:
                scan_result = self._execute_ai_intelligent_scanning(design_doc_directory)
            
            # 2. 质量评估
            quality_assessment = self.quality_assessor.assess_scan_quality(scan_result)
            
            # 3. 生成checkresult目录内容
            checkresult_output = self._generate_checkresult_directory(
                scan_result, quality_assessment, design_doc_directory)
            
            # 4. 检查是否达到质量标准
            if self._meets_quality_standards(quality_assessment):
                print("✅ V4扫描阶段完成，质量标准达标")
                return {
                    "status": "completed",
                    "iterations": iteration + 1,
                    "quality_assessment": quality_assessment,
                    "checkresult_path": checkresult_output["directory_path"]
                }
            
            # 5. 生成改进指令
            improvement_instructions = self._generate_improvement_instructions(
                quality_assessment, checkresult_output)
            
            print(f"⚠️ 质量标准未达标，生成改进指令: {improvement_instructions['file_path']}")
            print("请根据ai-prompt-batch-improvement.md修改设计文档后重新扫描")
            
            # 等待用户修改（在实际实现中可能需要交互机制）
            user_input = input("设计文档已修改完成？(y/n): ")
            if user_input.lower() != 'y':
                break
                
            iteration += 1
        
        return {
            "status": "max_iterations_reached",
            "iterations": iteration,
            "final_quality": quality_assessment
        }
```

### 2. V4 AI智能扫描器（集成记忆库最佳实践）
```python
class V4AIIntelligentScanner:
    """V4 AI智能扫描器 - 基于记忆库和标准实施文档的AI控制最佳实践"""

    def __init__(self):
        # 借鉴记忆库L1-core的AI认知约束管理
        self.cognitive_constraint_manager = CognitiveConstraintManager()

        # 借鉴标准实施文档的验证锚点机制
        self.verification_anchor_manager = VerificationAnchorManager()

        # 借鉴V3.1的质量门禁系统
        self.quality_gate_manager = QualityGateManager()

        # V4新增：持续优化能力
        self.continuous_optimization_engine = ContinuousOptimizationEngine()

    def scan_with_cognitive_constraints(self, design_doc_directory: str) -> Dict:
        """基于AI认知约束的智能扫描（集成最佳实践）"""

        # 1. 激活AI认知约束（基于记忆库L1-core约束）
        constraint_activation_result = self._activate_comprehensive_cognitive_constraints()

        # 2. 智能文档发现和分组（基于认知边界管理）
        document_groups = self._discover_and_group_documents_with_cognitive_awareness(design_doc_directory)

        # 3. 基于认知边界的批次处理（借鉴标准实施文档的批次策略）
        scan_results = {}
        for group in document_groups:
            # 认知负载评估（基于记忆库约束标准）
            cognitive_load = self._assess_cognitive_load_with_memory_constraints(group)

            if cognitive_load <= 0.6:  # 60%认知负载阈值（借鉴标准实施文档）
                # 执行批次扫描（集成验证锚点）
                batch_result = self._scan_document_batch_with_verification_anchors(group)
                scan_results.update(batch_result)
            else:
                # 超过认知边界，基于AI认知约束进一步分割
                sub_groups = self._split_by_cognitive_boundary_with_constraints(group)
                for sub_group in sub_groups:
                    sub_result = self._scan_document_batch_with_verification_anchors(sub_group)
                    scan_results.update(sub_result)

        # 4. 结果整合和验证（基于质量门禁机制）
        integrated_result = self._integrate_scan_results_with_quality_gates(scan_results)

        # 5. 持续优化学习（基于执行历史优化）
        optimization_data = self.continuous_optimization_engine.extract_learning_data(integrated_result)

        return {
            **integrated_result,
            "cognitive_constraints_applied": constraint_activation_result,
            "optimization_learning_data": optimization_data
        }

    def _activate_comprehensive_cognitive_constraints(self) -> Dict:
        """激活综合AI认知约束（基于记忆库最佳实践）"""

        # 基于记忆库L1-core的约束配置
        constraints = {
            "@L1:global-constraints": "全局约束激活",
            "@AI_COGNITIVE_CONSTRAINTS": "AI认知约束激活",
            "@BOUNDARY_GUARD_ACTIVATION": "边界护栏激活",
            "@AI_MEMORY_800_LINES_VALIDATION": "800行记忆边界验证",
            "@HALLUCINATION_PREVENTION": "幻觉防护激活",
            "@MEMORY_BOUNDARY_CHECK": "记忆边界检查",
            "@ATOMIC_OPERATION_VALIDATION": "原子操作验证",
            "@COGNITIVE_GRANULARITY_CONTROL": "认知粒度控制"
        }

        # 激活约束并记录结果
        activation_results = {}
        for constraint, description in constraints.items():
            activation_result = self.cognitive_constraint_manager.activate_constraint(constraint)
            activation_results[constraint] = {
                "description": description,
                "activated": activation_result["success"],
                "parameters": activation_result.get("parameters", {})
            }
            print(f"🛡️ {description}: {'✅ 已激活' if activation_result['success'] else '❌ 激活失败'}")

        return activation_results

    def _assess_cognitive_load_with_memory_constraints(self, document_group: List) -> float:
        """基于记忆库约束评估认知负载"""

        # 基于记忆库L1-core的认知负载计算
        concept_count = sum(doc.get('concept_count', 0) for doc in document_group)
        line_count = sum(doc.get('line_count', 0) for doc in document_group)
        dependency_depth = max(doc.get('dependency_depth', 0) for doc in document_group)

        # 认知负载计算（基于记忆库标准）
        concept_load = concept_count / 5.0  # 最大5个概念
        memory_load = line_count / 800.0    # 最大800行记忆边界
        dependency_load = dependency_depth / 2.0  # 最大2层依赖

        # 综合认知负载（基于标准实施文档的权重分配）
        cognitive_load = (concept_load * 0.4 + memory_load * 0.4 + dependency_load * 0.2)

        return min(cognitive_load, 1.0)  # 限制在0-1范围内
```

### 3. V4扫描质量评估器
```python
class V4ScanQualityAssessor:
    """V4扫描质量评估器"""
    
    def assess_scan_quality(self, scan_result: Dict) -> Dict:
        """评估扫描质量"""
        
        quality_metrics = {
            "document_completeness": self._assess_document_completeness(scan_result),
            "json_completeness": self._assess_json_completeness(scan_result),
            "extraction_accuracy": self._assess_extraction_accuracy(scan_result),
            "cognitive_compliance": self._assess_cognitive_compliance(scan_result),
            "anti_pattern_detection": self._assess_anti_patterns(scan_result)
        }
        
        # 计算综合质量分数
        overall_quality = self._calculate_overall_quality(quality_metrics)
        
        quality_assessment = {
            "metrics": quality_metrics,
            "overall_quality": overall_quality,
            "meets_standards": self._check_quality_standards(quality_metrics),
            "improvement_areas": self._identify_improvement_areas(quality_metrics)
        }
        
        return quality_assessment
    
    def _check_quality_standards(self, metrics: Dict) -> bool:
        """检查是否达到质量标准"""
        standards = {
            "document_completeness": 0.90,
            "json_completeness": 0.90,
            "extraction_accuracy": 0.95,
            "cognitive_compliance": 1.00
        }
        
        for metric, threshold in standards.items():
            if metrics.get(metric, 0) < threshold:
                return False
        
        return True
```

## 📊 V4扫描阶段输出规范

### checkresult目录结构
```
checkresult/
├── 01-architecture-overview_检查报告.md
├── 02-kernel-and-plugin-lifecycle_检查报告.md
├── ...（每个设计文档对应一个检查报告）
├── ai-prompt-batch-improvement.md          # 核心：批量修改指令
├── quality-overview-report.md              # 质量汇总报告
├── 扫描结果数据.json                       # 结构化扫描数据
└── 整体扫描汇总报告.md                     # 总体分析报告
```

### ai-prompt-batch-improvement.md核心内容
```markdown
# V4扫描阶段批量文档修改指令

## 项目概况
- 检查报告目录: [具体路径]
- 文档总数: X个
- 当前平均得分: X/100
- 目标完备度: ≥90%

## 需要修改的文档列表
[按优先级排序的文档列表和具体问题]

## 批量修改策略
### 阶段1：高优先级修改（完备度<80%）
### 阶段2：中优先级修改（完备度80-89%）
### 阶段3：优化细节（完备度≥90%）

## JSON完整度检查提醒
[具体的JSON填充指导和验证要求]

## 设计文档反模式修改指导
[反模式检测结果和修复建议]

## 验证命令
```bash
python tools/doc/design/v3/advanced-doc-scanner.py "[文档路径]"
```
```

## 🚨 V4扫描阶段AI认知约束（基于记忆库最佳实践）

### 强制性约束激活（借鉴记忆库L1-core约束）
```yaml
cognitive_constraints:
  memory_boundary_management:
    max_context_elements: 5              # 最大5个概念（记忆库标准）
    single_concept_rule: true            # 每个操作只涉及一个核心概念
    information_chunking: true           # 复杂信息分解为独立认知块
    context_refresh_triggers:
      - "概念数量超过5个"
      - "操作步骤超过3个"
      - "依赖关系超过2层"

  hallucination_prevention:
    concrete_anchoring: true             # 每个步骤必须有具体验证锚点
    reality_check_mandatory: true        # 定期对照实际代码状态验证
    assumption_explicit_marking: true    # 明确区分已知事实和AI推测
    code_state_verification: true        # 每个操作前必须查看实际代码状态
    mandatory_architecture_analysis: true # 必须首先分析真实项目架构

  cognitive_granularity_control:
    atomic_operation_definition: "单个文档、单个概念、立即验证"
    immediate_feedback_loop: "操作→验证→确认→下一步"
    context_isolation: true              # 每个操作在独立上下文中完成
    progressive_assembly: true           # 从简单组件逐步组装复杂理解
```

### 扫描处理边界（基于标准实施文档经验）
```yaml
processing_boundaries:
  single_document_limit: "≤200行/次"     # 单文档处理限制
  batch_processing_limit: "≤3个文档/批次" # 批次处理限制
  cognitive_load_threshold: "≤60%"       # 认知负载阈值（标准实施文档标准）
  memory_refresh_trigger: "每5个文档重置上下文"
  step_limitation: "50行代码以内"         # 每步骤代码行数限制
  verification_pass_rate: "100%"         # 验证通过率要求
```

### V4扫描阶段质量门禁（借鉴标准实施文档质量门禁）
```yaml
quality_gates:
  cognitive_complexity_gate:
    threshold: 0.7                       # 认知复杂度阈值
    measurement: "概念数量、依赖深度、操作复杂度综合评估"
    failure_action: "分解任务或降级处理"

  memory_pressure_gate:
    threshold: 0.6                       # 记忆压力阈值
    measurement: "上下文大小、信息密度、处理时长"
    failure_action: "上下文刷新或分批处理"

  hallucination_risk_gate:
    threshold: 0.3                       # 幻觉风险阈值
    measurement: "现实锚点密度、假设标记率、验证覆盖率"
    failure_action: "增加验证锚点或人工介入"

  verification_anchor_gate:
    threshold: 1.0                       # 验证锚点覆盖率100%
    measurement: "每个分析步骤的验证锚点覆盖情况"
    failure_action: "补充验证锚点或回滚操作"
```

### V4扫描阶段持续优化机制
```python
class V4ScanningContinuousOptimization:
    """V4扫描阶段持续优化 - 基于执行历史的深度迭代开发"""

    def optimize_scanning_strategy(self, execution_history: List[Dict]) -> Dict:
        """基于执行历史优化扫描策略"""

        # 1. 分析扫描性能模式
        performance_patterns = self._analyze_scanning_performance_patterns(execution_history)

        # 2. 识别认知约束优化机会
        constraint_optimization_opportunities = self._identify_constraint_optimization_opportunities(performance_patterns)

        # 3. 优化文档切割策略
        chunking_strategy_improvements = self._optimize_document_chunking_strategy(performance_patterns)

        # 4. 优化质量门禁参数
        quality_gate_optimizations = self._optimize_quality_gate_parameters(performance_patterns)

        return {
            "performance_analysis": performance_patterns,
            "constraint_optimizations": constraint_optimization_opportunities,
            "chunking_improvements": chunking_strategy_improvements,
            "quality_gate_optimizations": quality_gate_optimizations,
            "optimization_timestamp": datetime.now().isoformat()
        }
```

---

*基于V4.0一体化架构、扫描阶段任务设计和记忆库最佳实践制定*
*集成标准实施文档和记忆库实施文档的AI控制经验*
*具备持续优化和深度迭代开发能力*
*专家置信度评估：95%*
*创建时间：2025-06-14*
