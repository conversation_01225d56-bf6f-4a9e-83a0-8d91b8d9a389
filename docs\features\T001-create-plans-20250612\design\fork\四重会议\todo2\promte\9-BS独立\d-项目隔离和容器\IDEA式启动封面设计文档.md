# IDEA式启动封面设计文档

## 📋 设计概述

基于IntelliJ IDEA启动界面的设计理念，创建四重会议系统的项目选择封面，实现无默认指挥官的按需启动架构。

## 🎯 设计目标

- **无默认指挥官**: 服务器启动后不创建任何项目指挥官
- **按需启动**: 用户选择项目时才启动对应的指挥官
- **状态透明**: 清晰显示服务器和项目状态
- **用户友好**: 类似IDEA的熟悉体验

## 🖼️ 界面布局设计

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                        四重会议系统 V4.5 (全屏宽度)                                              │
├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                                                   │
│  📂 项目 (可滚动)                    ✏️ 操作                    💻 系统状态                                      │
│  ┌─────────────────────────────┐     ┌─────────────────────────┐ ┌─────────────────────────┐                     │
│  │ 🟢 AI平台开发               │     │ 🆕 新建项目              │ │ 🧠 内存: 19.9/31.9 GB   │                     │
│  │ /path/to/ai-platform       │     │                         │ │ ████████░░ 62.4%        │                     │
│  │ 最后打开：2分钟前           │     │ 📁 打开现有项目          │ │                         │                     │
│  │ 状态：算法分析中...         │     │                         │ │ 💾 硬盘: 92/112 GB      │                     │
│  │ 指挥官：运行中              │     │ ⚙️ 系统设置              │ │ ████████▓▓ 81.9%        │                     │
│  └─────────────────────────────┘     │                         │ │                         │                     │
│                                      │ 📖 帮助文档              │ │ ⚡ CPU: 4.2% (4核)      │                     │
│  ┌─────────────────────────────┐     └─────────────────────────┘ │ █░░░░░░░░░ 4.2%         │                     │
│  │ 🟡 企业系统重构             │                                 │                         │                     │
│  │ /path/to/enterprise-sys     │                                 │ 📊 负载: 0.0/4.0        │                     │
│  │ 最后打开：1小时前           │                                 │ ░░░░░░░░░░ 0.0%         │                     │
│  │ 状态：暂停中               │                                 │                         │                     │
│  │ 指挥官：已停止              │                                 │ 🌐 网络: ↓0.0 ↑0.0 MB/s │                     │
│  └─────────────────────────────┘                                 └─────────────────────────┘                     │
│                                                                                                                   │
│  ┌─────────────────────────────┐     📊 API状态监控 (跨两列)                                                     │
│  │ ⚪ 数据分析项目             │     ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │ /path/to/data-analysis      │     │ DS R1-0528: ✅ 健康     DS V3-0324: ⚠️ 降智                           │ │
│  │ 最后打开：昨天              │     │ DeepCoder: ❌ 失效      Gemini Pro: ✅ 正常                            │ │
│  │ 状态：未启动               │     │                                                                         │ │
│  │ 指挥官：未创建              │     │ 主力API: 2/4 可用       备用API: 1/2 可用                              │ │
│  └─────────────────────────────┘     │ 置信度: 92% (可接受)    系统状态: 🟡 部分异常                          │ │
│                                      └─────────────────────────────────────────────────────────────────────────┘ │
│  [滚动条]                                                                                                        │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
```

## 🎨 状态指示系统

### 项目状态指示器
| 图标 | 状态 | 说明 | 指挥官状态 |
|------|------|------|------------|
| 🟢 | 活跃中 | 指挥官运行，有任务执行 | 运行中 |
| 🟡 | 暂停中 | 指挥官运行，无活动任务 | 运行中 |
| 🔴 | 已停止 | 指挥官已停止运行 | 已停止 |
| ⚪ | 未启动 | 从未启动过的项目 | 未创建 |
| ✅ | 已完成 | 项目任务已完成 | 可选停止 |

### API状态监控信息
```yaml
核心API状态:
  DS_R1_0528:
    - ✅ 健康 (响应正常，置信度95%+)
    - ⚠️ 降智 (响应质量下降，置信度80-90%)
    - ❌ 失效 (连接失败或响应异常)

  DS_V3_0324:
    - ✅ 正常 (代码生成质量良好)
    - ⚠️ 降智 (生成质量下降)
    - ❌ 失效 (无法生成有效代码)

  DeepCoder_14B:
    - ✅ 可用 (Chutes平台正常)
    - ❌ 失效 (平台异常或密钥失效)

  Gemini_Pro_2_5:
    - ✅ 正常 (备用逻辑处理正常)
    - ⚠️ 限流 (请求频率受限)
    - ❌ 失效 (Google API异常)

API统计信息:
  主力API可用率: "X/4 可用" (gmi_deepseek_r1, gmi_deepseek_v3等)
  备用API可用率: "X/2 可用" (chutes_deepseek_r1, chutes_deepcoder等)
  综合置信度: "XX% (状态评估)"
    - 95%+: 优秀 (所有主力API正常)
    - 90-94%: 良好 (大部分API正常)
    - 85-89%: 可接受 (部分API异常但可工作)
    - <85%: 需要关注 (多个API异常)

系统健康评估:
  - 🟢 健康 (所有API正常工作)
  - 🟡 部分异常 (部分API降智或失效，但系统可用)
  - 🔴 严重异常 (多个关键API失效，影响系统功能)
```

### 系统状态监控信息
```yaml
系统指标监控:
  内存状态:
    - 显示格式: "已用/总计 GB (使用率%)"
    - 进度条颜色: 绿色(<60%) | 橙色(60-80%) | 红色(>80%)
    - 数据来源: psutil.virtual_memory()
    - 更新频率: 30秒

  硬盘状态:
    - 显示格式: "已用/总计 GB (使用率%)"
    - 监控路径: Windows(C:\) | Linux(/)
    - 进度条颜色: 绿色(<60%) | 橙色(60-80%) | 红色(>80%)
    - 数据来源: psutil.disk_usage()

  CPU状态:
    - 显示格式: "使用率% (核心数)"
    - 采样间隔: 0.1秒
    - 进度条颜色: 绿色(<60%) | 橙色(60-80%) | 红色(>80%)
    - 数据来源: psutil.cpu_percent()

  系统负载:
    - 显示格式: "当前负载/最大负载"
    - Unix系统: 使用getloadavg()
    - Windows系统: 基于CPU使用率计算等效负载
    - 进度条显示: 负载百分比

  网络状态:
    - 显示格式: "↓下载速度 ↑上传速度 MB/s"
    - 计算方式: 基于时间间隔的字节差值
    - 数据来源: psutil.net_io_counters()
    - 实时更新: 每次刷新重新计算

技术实现:
  独立模块: system_monitor.py (不影响主服务器代码)
  API端点: /api/system_metrics
  依赖库: psutil (跨平台系统信息库)
  错误处理: 完善的异常捕获和默认值
  单例模式: 避免重复初始化网络监控状态
```

## 🔄 用户体验流程

### 第一次使用（空状态）
```
1. 用户启动系统
   ↓
2. 显示空的项目列表
   ↓
3. 右侧显示"欢迎使用四重会议系统"
   ↓
4. 提示"点击新建项目开始您的第一个项目"
   ↓
5. 用户点击"新建项目"或"打开项目"
   ↓
6. 填写项目信息（项目名称 + 工作目录）
   ↓
7. 系统统一处理：创建指挥官 + 准备环境 + 启动容器
   ↓
8. 自动进入九宫格工作界面
```

### 有项目历史后
```
1. 用户启动系统
   ↓
2. 显示项目卡片列表（按最后打开时间排序）
   ↓
3. 显示每个项目的状态和指挥官状态
   ↓
4. 用户操作选择：
   - 点击项目卡片 → 直接启动该项目
   - 点击"新建项目" → 创建新的工作环境
   - 点击"打开项目" → 输入新的项目信息
   ↓
5. 系统统一处理：
   - 创建新的指挥官实例
   - 准备对应的工作环境（新建或已存在）
   - 启动万用状态容器
   - 指挥官连接数据库和Meeting目录
   ↓
6. 进入九宫格工作界面，指挥官开始工作
```

### 统一的项目启动流程

#### 核心理念：新建和打开本质相同
**业务本质**: 创建指挥官 + 准备环境 + 启动容器

#### 1. 项目信息输入（统一界面）
```
点击"🆕 新建项目" 或 "📁 打开项目"
↓
显示统一的项目对话框:
┌─────────────────────────────────────┐
│ 项目工作环境                        │
├─────────────────────────────────────┤
│ 项目名称: [xkongcloud            ] │
│                                     │
│ 工作目录: [docs\features\F007-建立  │
│           Commons库的治理机制-      │
│           20250610\nexus万用插座\   │
│           design\v1               ] │
│                                     │
│ 描述: F007万用插座的设计文档工作    │
│                                     │
│ 或选择最近项目:                     │
│ ○ xkongcloud (F007万用插座设计)     │
│ ○ ai_platform (模型训练架构)        │
│ ○ data_analysis (数据处理流程)      │
│                                     │
│ [取消]              [启动项目]      │
└─────────────────────────────────────┘
```

#### 2. 系统统一处理流程
```
用户点击"启动项目"
↓
系统执行统一的项目启动逻辑:
1. 生成环境路径:
   - 数据库: databases/xkongcloud.db
   - Meeting: meetings/docs/features/F007-.../design/v1/
   - 日志: logs/xkongcloud/

2. 确保环境存在:
   - 如果路径不存在 → 自动创建（相当于新建）
   - 如果路径已存在 → 直接使用（相当于打开）

3. 创建项目指挥官:
   - 实例化指挥官
   - 绑定万用状态容器
   - 设置数据路径

4. 启动工作环境:
   - 指挥官连接数据库
   - 加载Meeting历史（如果有）
   - 准备工作状态

5. 进入九宫格界面
↓
用户开始工作
```

#### 3. 简化的路径处理
```yaml
系统只需要处理:
  路径生成:
    数据库路径: "databases/{项目名称}.db"
    Meeting路径: "meetings/{工作目录}/"
    日志路径: "logs/{项目名称}/"

  环境准备:
    - 确保目录存在
    - 初始化数据库（如果是新环境）
    - 准备日志系统

  指挥官配置:
    - 设置数据库连接
    - 设置Meeting工作目录
    - 启动状态容器
```

### 项目管理操作
```
右键点击项目卡片:
├── 📂 进入项目 (加载上下文，进入九宫格)
├── ⏸️ 暂停项目 (停止指挥官，保留数据)
├── 🔄 重启项目 (重启指挥官)
├── 📊 查看详情 (项目详细信息和工作进度)
├── 📝 编辑项目 (修改项目名称、工作目录)
├── 📁 在文件管理器中显示
├── 🔄 切换版本 (v1 ↔ v2)
└── 🗑️ 删除项目 (谨慎操作)
```

## 💻 技术实现架构

### 1. 服务器端架构改造

#### 核心类设计
```python
class ProjectStatusManager:
    """项目状态管理器"""
    
    def __init__(self):
        self.project_history = {}     # 项目历史记录
        self.active_commanders = {}   # 活跃的指挥官实例
        self.project_configs = {}     # 项目配置信息
    
    def get_project_cards(self) -> List[Dict]:
        """获取项目卡片数据"""
        
    def get_server_status(self) -> Dict:
        """获取服务器状态信息"""
        
    def start_project_commander(self, project_name: str, work_directory: str):
        """统一的项目启动逻辑 - 新建和打开都使用此方法"""

        # 1. 生成环境路径
        environment_paths = self._generate_environment_paths(project_name, work_directory)

        # 2. 确保环境存在（自动处理新建和已存在的情况）
        self._ensure_environment_exists(environment_paths)

        # 3. 创建或获取项目指挥官
        if project_name not in self.project_manager.project_commanders:
            # 创建新的指挥官和容器
            container = self.project_manager.create_project(project_name)
            commander = self.project_manager.get_project_commander(project_name)

            # 4. 配置指挥官工作环境
            self._setup_commander_environment(commander, environment_paths)

            print(f"✅ 项目 {project_name} 指挥官已启动")
            print(f"   工作目录: {work_directory}")
            print(f"   数据库: {environment_paths['database']}")
            print(f"   Meeting: {environment_paths['meeting']}")

        return self.project_manager.get_project_commander(project_name)

    def _generate_environment_paths(self, project_name: str, work_directory: str):
        """生成项目环境路径"""
        return {
            "database": f"databases/{project_name}.db",
            "meeting": f"meetings/{work_directory}/",
            "logs": f"logs/{project_name}/",
            "project_name": project_name,
            "work_directory": work_directory
        }

    def _ensure_environment_exists(self, paths: Dict):
        """确保环境目录存在"""
        import os

        # 创建必要的目录
        os.makedirs(os.path.dirname(paths["database"]), exist_ok=True)
        os.makedirs(paths["meeting"], exist_ok=True)
        os.makedirs(paths["logs"], exist_ok=True)

        print(f"✅ 环境路径已准备完成")

    def _setup_commander_environment(self, commander, paths: Dict):
        """配置指挥官工作环境"""
        try:
            # 设置指挥官的数据路径
            if hasattr(commander, 'set_data_paths'):
                commander.set_data_paths(
                    database_path=paths["database"],
                    meeting_path=paths["meeting"],
                    log_path=paths["logs"]
                )

            # 设置工作目录（如果指挥官支持）
            if hasattr(commander, 'set_work_directory_context'):
                commander.set_work_directory_context(paths["work_directory"])

            print(f"✅ 指挥官环境配置完成")

        except Exception as e:
            print(f"⚠️ 指挥官环境配置失败: {e}")
            # 不影响项目启动，继续执行

    def stop_project_commander(self, project_name: str):
        """停止项目指挥官"""

class SystemMonitor:
    """系统监控器 - 独立模块，获取真实系统指标"""

    def __init__(self):
        self.last_network_io = None
        self.last_network_time = None

    def get_memory_info(self) -> Dict:
        """获取内存信息"""
        memory = psutil.virtual_memory()
        return {
            "used": round(memory.used / (1024**3), 1),  # GB
            "total": round(memory.total / (1024**3), 1),  # GB
            "percentage": round(memory.percent, 1),
            "available": round(memory.available / (1024**3), 1)
        }

    def get_disk_info(self) -> Dict:
        """获取硬盘信息"""
        disk_path = "C:\\" if platform.system() == "Windows" else "/"
        disk = psutil.disk_usage(disk_path)
        return {
            "used": round(disk.used / (1024**3), 0),
            "total": round(disk.total / (1024**3), 0),
            "percentage": round((disk.used / disk.total) * 100, 1),
            "free": round(disk.free / (1024**3), 0)
        }

    def get_cpu_info(self) -> Dict:
        """获取CPU信息"""
        cpu_percent = psutil.cpu_percent(interval=0.1)
        cpu_count = psutil.cpu_count()
        return {
            "usage": round(cpu_percent, 1),
            "cores": cpu_count
        }

    def get_network_info(self) -> Dict:
        """获取网络信息（实时速度计算）"""
        current_time = time.time()
        current_io = psutil.net_io_counters()

        if self.last_network_io and self.last_network_time:
            time_delta = current_time - self.last_network_time
            upload_speed = (current_io.bytes_sent - self.last_network_io.bytes_sent) / time_delta / (1024 * 1024)
            download_speed = (current_io.bytes_recv - self.last_network_io.bytes_recv) / time_delta / (1024 * 1024)
        else:
            upload_speed = download_speed = 0.0

        self.last_network_io = current_io
        self.last_network_time = current_time

        return {
            "download": round(download_speed, 1),
            "upload": round(upload_speed, 1)
        }

    def get_all_metrics(self) -> Dict:
        """获取所有系统指标"""
        return {
            "memory": self.get_memory_info(),
            "disk": self.get_disk_info(),
            "cpu": self.get_cpu_info(),
            "load": self.get_load_info(),
            "network": self.get_network_info(),
            "timestamp": datetime.now().isoformat()
        }

class SimplifiedMCPServer:
    """简化的MCP服务器 - 无默认指挥官版本"""
    
    def __init__(self):
        # 🔧 关键改造：完全移除默认指挥官创建
        self.project_manager = ProjectManager()
        self.project_status_manager = ProjectStatusManager()
        self.server_status = ServerStatusMonitor()
        
        # 不创建任何默认项目！
        print("✅ 服务器已就绪，等待项目选择")
```

#### API端点设计
```python
# 封面页面相关API
@app.route('/')
def startup_cover():
    """IDEA式启动封面"""
    
@app.route('/api/project_cards')
def get_project_cards():
    """获取项目卡片数据"""
    
@app.route('/api/server_status')
def get_server_status():
    """获取服务器状态"""

@app.route('/api/system_metrics')
def get_system_metrics():
    """获取系统指标 - 独立的系统监控API"""
    try:
        from four_layer_meeting_server.system_monitor import get_system_metrics
        metrics = get_system_metrics()
        return jsonify({
            "status": "success",
            "metrics": metrics,
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"系统监控模块错误: {str(e)}",
            "metrics": None
        })

# 项目操作API
@app.route('/api/start_project', methods=['POST'])
def api_start_project():
    """统一的项目启动API - 处理新建和打开"""
    try:
        data = request.get_json()
        project_name = data.get('name')
        work_directory = data.get('workDirectory')
        description = data.get('description', '')

        if not project_name or not work_directory:
            return jsonify({"success": False, "error": "项目名称和工作目录不能为空"})

        # 启动项目指挥官（统一处理新建和打开）
        commander = server.start_project_commander(project_name, work_directory)

        if commander:
            # 记录项目信息
            project_info = {
                "name": project_name,
                "work_directory": work_directory,
                "description": description,
                "last_opened": datetime.now().isoformat()
            }

            server.project_status_manager.record_project_activity(project_name, project_info)

            return jsonify({
                "success": True,
                "message": f"项目 {project_name} 启动成功",
                "project_info": project_info
            })
        else:
            return jsonify({"success": False, "error": "项目指挥官启动失败"})

    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/project/<project_name>/nine-grid')
def project_nine_grid(project_name):
    """项目的九宫格界面"""
    # 获取项目配置
    project_config = server.project_status_manager.get_project_config(project_name)

    if not project_config:
        return redirect('/')  # 项目不存在，返回首页

    return render_template('nine_grid.html',
                         project=project_name,
                         project_config=project_config)

@app.route('/api/project/<project_name>/start')
def start_project(project_name):
    """启动项目指挥官"""

@app.route('/api/project/<project_name>/stop')
def stop_project(project_name):
    """停止项目指挥官"""
```

### 2. 前端实现

#### 项目卡片组件
```javascript
class ProjectCard {
    constructor(projectData) {
        this.data = projectData;
        this.element = this.createCardElement();
    }
    
    createCardElement() {
        const card = document.createElement('div');
        card.className = `project-card ${this.data.status}`;
        card.innerHTML = this.getCardHTML();
        this.bindEvents(card);
        return card;
    }
    
    getCardHTML() {
        return `
            <div class="card-header">
                <span class="status-icon">${this.data.icon}</span>
                <span class="project-name">${this.data.name}</span>
                <span class="commander-status">${this.data.commander_status}</span>
            </div>
            <div class="card-body">
                <div class="project-path">${this.data.path}</div>
                <div class="last-opened">最后打开：${this.data.last_opened}</div>
                <div class="current-task">状态：${this.data.current_task}</div>
            </div>
        `;
    }
    
    bindEvents(card) {
        // 左键点击：打开项目
        card.onclick = () => this.openProject();
        
        // 右键点击：显示上下文菜单
        card.oncontextmenu = (e) => {
            e.preventDefault();
            this.showContextMenu(e.clientX, e.clientY);
        };
    }
}
```

#### 服务器状态组件
```javascript
class ServerStatusPanel {
    constructor() {
        this.element = document.getElementById('server-status');
        this.updateInterval = setInterval(() => this.updateStatus(), 5000);
    }
    
    async updateStatus() {
        try {
            const response = await fetch('/api/server_status');
            const status = await response.json();
            this.renderStatus(status);
        } catch (error) {
            this.renderError(error);
        }
    }
    
    renderStatus(status) {
        this.element.innerHTML = `
            <h3>📊 服务器状态</h3>
            <div class="status-grid">
                <div class="status-item">
                    <span class="label">API状态:</span>
                    <span class="value ${status.api_status.class}">${status.api_status.text}</span>
                </div>
                <div class="status-item">
                    <span class="label">WebSocket:</span>
                    <span class="value ${status.websocket_status.class}">${status.websocket_status.text}</span>
                </div>
                <div class="status-item">
                    <span class="label">端口:</span>
                    <span class="value">${status.ports}</span>
                </div>
                <div class="status-item">
                    <span class="label">活跃连接:</span>
                    <span class="value">${status.active_connections}个客户端</span>
                </div>
                <div class="status-item">
                    <span class="label">内存使用:</span>
                    <span class="value">${status.memory_usage}</span>
                </div>
                <div class="status-item">
                    <span class="label">CPU使用:</span>
                    <span class="value">${status.cpu_usage}</span>
                </div>
                <div class="status-item">
                    <span class="label">系统状态:</span>
                    <span class="value ${status.system_health.class}">${status.system_health.text}</span>
                </div>
                <div class="status-item">
                    <span class="label">可创建项目:</span>
                    <span class="value ${status.can_create_project.class}">${status.can_create_project.text}</span>
                </div>
            </div>
        `;
    }
}
```

## 🎯 关键特性

### 1. 无默认指挥官架构
- **服务器启动**: 不创建任何项目指挥官
- **资源节约**: 避免不必要的内存和CPU占用
- **按需分配**: 只有用户选择项目时才启动指挥官

### 2. 智能状态管理
- **项目状态**: 实时显示每个项目的运行状态
- **指挥官状态**: 明确显示指挥官是否运行
- **服务器状态**: 全面的系统健康监控

### 3. 用户友好体验
- **熟悉界面**: 类似IDEA的启动体验
- **状态透明**: 所有状态信息一目了然
- **操作便捷**: 一键进入项目工作状态

### 4. 可扩展设计
- **多项目支持**: 支持同时管理多个项目
- **滚动列表**: 项目列表可滚动，支持大量项目
- **上下文菜单**: 丰富的项目管理操作

## 📊 实施计划

### Phase 1: 核心架构改造
1. 移除默认指挥官创建逻辑
2. 实现ProjectStatusManager
3. 创建按需启动机制

### Phase 2: 前端界面开发
1. 创建IDEA式启动封面（全屏宽度布局）
2. 实现项目卡片组件
3. 添加API状态监控面板
4. 新增系统状态监控面板（内存、硬盘、CPU、负载、网络）
5. 创建独立的系统监控模块（system_monitor.py）

### Phase 3: 功能完善
1. 添加项目管理操作
2. 实现状态实时更新
3. 优化用户体验

### Phase 4: 测试和优化
1. 全面功能测试
2. 性能优化
3. 用户体验调优

## 🔧 详细实施方案

### 1. 服务器端代码修改

#### 移除默认指挥官创建
```python
# 修改 server_launcher.py
class SimplifiedMCPServer:
    def __init__(self):
        # ❌ 移除这行：
        # default_container = self.project_manager.create_project(self.default_project)

        # ✅ 改为：
        self.project_manager = ProjectManager()
        self.project_status_manager = ProjectStatusManager()
        print("✅ 服务器已就绪，等待项目选择")
```

#### 新增项目状态管理器
```python
# 新建 project_status_manager.py
class ProjectStatusManager:
    def __init__(self):
        self.project_history_file = "data/project_history.json"
        self.load_project_history()

    def get_project_cards(self):
        """获取项目卡片数据"""
        cards = []

        # 获取活跃项目
        for name, commander in self.active_commanders.items():
            cards.append({
                "name": name,
                "path": commander.project_path,
                "status": "active" if commander.is_running() else "paused",
                "icon": "🟢" if commander.is_running() else "🟡",
                "last_opened": commander.last_activity_time,
                "current_task": commander.get_current_task(),
                "commander_status": "运行中" if commander.is_running() else "已停止"
            })

        # 获取历史项目
        for name, info in self.project_history.items():
            if name not in self.active_commanders:
                cards.append({
                    "name": name,
                    "path": info["path"],
                    "status": "inactive",
                    "icon": "⚪",
                    "last_opened": info["last_opened"],
                    "current_task": "未启动",
                    "commander_status": "未创建"
                })

        return sorted(cards, key=lambda x: x["last_opened"], reverse=True)

    def get_server_status(self):
        """获取服务器状态 - 重点关注API状态"""
        api_status = self._check_all_api_status()

        return {
            "api_monitoring": api_status,
            "primary_apis_available": f"{api_status['primary_available']}/{api_status['primary_total']} 可用",
            "backup_apis_available": f"{api_status['backup_available']}/{api_status['backup_total']} 可用",
            "overall_confidence": f"{api_status['confidence_score']}% ({api_status['confidence_level']})",
            "system_health": api_status['system_health'],
            "can_create_project": api_status['can_create_project']
        }

    def _check_all_api_status(self):
        """检查所有API状态 - 基于实际配置"""
        # 从配置中获取API列表
        primary_apis = {
            "gmi_deepseek_r1_0528": "deepseek-ai/DeepSeek-R1-0528",
            "gmi_deepseek_v3_0324": "deepseek-ai/DeepSeek-V3-0324"
        }

        backup_apis = {
            "chutes_deepseek_r1": "deepseek-ai/DeepSeek-R1",
            "chutes_deepcoder_14b": "agentica-org/DeepCoder-14B-Preview"
        }

        # 检查每个API的状态
        api_details = {}
        primary_available = 0
        backup_available = 0

        # 检查主力API
        for api_key, model_name in primary_apis.items():
            status = self._check_single_api_status(api_key, model_name)
            api_details[api_key] = status
            if status["status"] in ["healthy", "normal"]:
                primary_available += 1

        # 检查备用API
        for api_key, model_name in backup_apis.items():
            status = self._check_single_api_status(api_key, model_name)
            api_details[api_key] = status
            if status["status"] in ["healthy", "normal"]:
                backup_available += 1

        # 计算综合置信度
        total_available = primary_available + backup_available
        total_apis = len(primary_apis) + len(backup_apis)
        confidence_score = int((total_available / total_apis) * 100)

        # 评估置信度等级
        if confidence_score >= 95:
            confidence_level = "优秀"
            system_health = {"status": "healthy", "icon": "🟢", "text": "健康"}
        elif confidence_score >= 90:
            confidence_level = "良好"
            system_health = {"status": "good", "icon": "🟢", "text": "良好"}
        elif confidence_score >= 85:
            confidence_level = "可接受"
            system_health = {"status": "warning", "icon": "🟡", "text": "部分异常"}
        else:
            confidence_level = "需要关注"
            system_health = {"status": "critical", "icon": "🔴", "text": "严重异常"}

        return {
            "api_details": api_details,
            "primary_available": primary_available,
            "primary_total": len(primary_apis),
            "backup_available": backup_available,
            "backup_total": len(backup_apis),
            "confidence_score": confidence_score,
            "confidence_level": confidence_level,
            "system_health": system_health,
            "can_create_project": confidence_score >= 85  # 85%以上可创建项目
        }

    def _check_single_api_status(self, api_key: str, model_name: str):
        """检查单个API状态"""
        try:
            # 这里应该调用实际的API健康检查
            # 可以复用现有的API测试器逻辑

            # 模拟API状态检查结果
            if "deepseek-ai/DeepSeek-R1-0528" in model_name:
                return {"status": "healthy", "icon": "✅", "text": "健康", "details": "响应正常，置信度95%+"}
            elif "deepseek-ai/DeepSeek-V3-0324" in model_name:
                return {"status": "degraded", "icon": "⚠️", "text": "降智", "details": "响应质量下降"}
            elif "DeepCoder-14B" in model_name:
                return {"status": "failed", "icon": "❌", "text": "失效", "details": "连接失败"}
            elif "gemini" in model_name.lower():
                return {"status": "normal", "icon": "✅", "text": "正常", "details": "备用逻辑处理正常"}
            else:
                return {"status": "unknown", "icon": "❓", "text": "未知", "details": "状态未知"}

        except Exception as e:
            return {"status": "error", "icon": "❌", "text": "错误", "details": f"检查失败: {str(e)}"}
```

#### 修改API端点
```python
# 修改 server_launcher.py 中的API
@self.web_app.app.route('/api/system_status', methods=['GET'])
def api_system_status():
    """获取系统状态 - 不依赖默认指挥官"""
    try:
        server_status = self.project_status_manager.get_server_status()
        return jsonify({
            "status": "success",
            "server_status": server_status,
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)})

@self.web_app.app.route('/api/project_cards', methods=['GET'])
def api_project_cards():
    """获取项目卡片数据"""
    try:
        cards = self.project_status_manager.get_project_cards()
        return jsonify({
            "status": "success",
            "project_cards": cards,
            "total_projects": len(cards)
        })
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)})
```

#### 新增系统监控模块
```python
# 新建 system_monitor.py - 独立模块，不影响主服务器代码
import psutil
import platform
import time
from datetime import datetime

class SystemMonitor:
    """系统监控器 - 获取真实系统指标"""

    def __init__(self):
        self.last_network_io = None
        self.last_network_time = None
        print("✅ SystemMonitor初始化完成")

    def get_memory_info(self) -> Dict:
        """获取内存信息"""
        memory = psutil.virtual_memory()
        return {
            "used": round(memory.used / (1024**3), 1),  # GB
            "total": round(memory.total / (1024**3), 1),  # GB
            "percentage": round(memory.percent, 1),
            "available": round(memory.available / (1024**3), 1)
        }

    def get_disk_info(self) -> Dict:
        """获取硬盘信息"""
        disk_path = "C:\\" if platform.system() == "Windows" else "/"
        disk = psutil.disk_usage(disk_path)
        return {
            "used": round(disk.used / (1024**3), 0),
            "total": round(disk.total / (1024**3), 0),
            "percentage": round((disk.used / disk.total) * 100, 1)
        }

    def get_cpu_info(self) -> Dict:
        """获取CPU信息"""
        cpu_percent = psutil.cpu_percent(interval=0.1)
        cpu_count = psutil.cpu_count()
        return {
            "usage": round(cpu_percent, 1),
            "cores": cpu_count
        }

    def get_network_info(self) -> Dict:
        """获取网络信息（实时速度计算）"""
        current_time = time.time()
        current_io = psutil.net_io_counters()

        if self.last_network_io and self.last_network_time:
            time_delta = current_time - self.last_network_time
            upload_speed = (current_io.bytes_sent - self.last_network_io.bytes_sent) / time_delta / (1024 * 1024)
            download_speed = (current_io.bytes_recv - self.last_network_io.bytes_recv) / time_delta / (1024 * 1024)
        else:
            upload_speed = download_speed = 0.0

        self.last_network_io = current_io
        self.last_network_time = current_time

        return {
            "download": round(download_speed, 1),
            "upload": round(upload_speed, 1)
        }

    def get_all_metrics(self) -> Dict:
        """获取所有系统指标"""
        return {
            "memory": self.get_memory_info(),
            "disk": self.get_disk_info(),
            "cpu": self.get_cpu_info(),
            "load": self.get_load_info(),
            "network": self.get_network_info(),
            "timestamp": datetime.now().isoformat()
        }

# 全局实例和便捷函数
def get_system_metrics() -> Dict:
    """快速获取系统指标的便捷函数"""
    monitor = SystemMonitor()
    return monitor.get_all_metrics()
```

#### 新增系统监控API端点
```python
# 在 app.py 中添加
@self.app.route('/api/system_metrics')
def get_system_metrics():
    """获取系统指标 - 独立的系统监控API"""
    try:
        from four_layer_meeting_server.system_monitor import get_system_metrics
        metrics = get_system_metrics()
        return jsonify({
            "status": "success",
            "metrics": metrics,
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"系统监控模块错误: {str(e)}",
            "metrics": None
        })
```

### 2. 前端界面实现

#### HTML模板
直接参考源代码 `tools\ace\src\web_interface\templates\startup_cover.html`

#### JavaScript实现
```javascript
// 新建 static/js/startup_cover.js
class StartupCover {
    constructor() {
        this.projectCards = [];
        this.serverStatus = null;
        this.init();
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.startStatusUpdates();
    }

    async loadData() {
        try {
            // 并行加载项目卡片和服务器状态
            const [cardsResponse, statusResponse] = await Promise.all([
                fetch('/api/project_cards'),
                fetch('/api/server_status')
            ]);

            const cardsData = await cardsResponse.json();
            const statusData = await statusResponse.json();

            if (cardsData.status === 'success') {
                this.projectCards = cardsData.project_cards;
                this.renderProjectCards();
            }

            if (statusData.status === 'success') {
                this.serverStatus = statusData.server_status;
                this.renderServerStatus();
            }
        } catch (error) {
            console.error('加载数据失败:', error);
            this.renderError();
        }
    }

    renderProjectCards() {
        const container = document.getElementById('project-cards');

        if (this.projectCards.length === 0) {
            // 空状态
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🎯</div>
                    <h3>欢迎使用四重会议系统</h3>
                    <p>您还没有任何项目</p>
                    <p>点击右侧"新建项目"开始您的第一个项目</p>
                </div>
            `;
            return;
        }

        // 渲染项目卡片
        container.innerHTML = '';
        this.projectCards.forEach(card => {
            const cardElement = this.createProjectCard(card);
            container.appendChild(cardElement);
        });
    }

    createProjectCard(cardData) {
        const card = document.createElement('div');
        card.className = `project-card ${cardData.status}`;
        card.innerHTML = `
            <div class="card-header">
                <span class="status-icon">${cardData.icon}</span>
                <span class="project-name">${cardData.name}</span>
                <span class="commander-status">${cardData.commander_status}</span>
            </div>
            <div class="card-body">
                <div class="project-path">${cardData.path}</div>
                <div class="last-opened">最后打开：${cardData.last_opened}</div>
                <div class="current-task">状态：${cardData.current_task}</div>
            </div>
        `;

        // 绑定事件
        card.onclick = () => this.openProject(cardData.name);
        card.oncontextmenu = (e) => {
            e.preventDefault();
            this.showProjectContextMenu(cardData, e.clientX, e.clientY);
        };

        return card;
    }

    renderServerStatus() {
        const container = document.getElementById('server-status');
        const status = this.serverStatus;
        const apiMonitoring = status.api_monitoring;

        // 构建API状态显示
        let apiStatusHTML = '';

        // 显示主要API状态
        const apiDisplayNames = {
            'gmi_deepseek_r1_0528': 'DS R1-0528',
            'gmi_deepseek_v3_0324': 'DS V3-0324',
            'chutes_deepcoder_14b': 'DeepCoder',
            'chutes_deepseek_r1': 'DS R1(备用)'
        };

        for (const [apiKey, apiInfo] of Object.entries(apiMonitoring.api_details)) {
            const displayName = apiDisplayNames[apiKey] || apiKey;
            apiStatusHTML += `
                <div class="status-item api-status-item">
                    <span class="label">${displayName}:</span>
                    <span class="value api-${apiInfo.status}">${apiInfo.icon} ${apiInfo.text}</span>
                </div>
            `;
        }

        container.innerHTML = `
            <div class="status-grid">
                ${apiStatusHTML}

                <div class="status-divider"></div>

                <div class="status-item">
                    <span class="label">主力API:</span>
                    <span class="value">${status.primary_apis_available}</span>
                </div>
                <div class="status-item">
                    <span class="label">备用API:</span>
                    <span class="value">${status.backup_apis_available}</span>
                </div>
                <div class="status-item">
                    <span class="label">置信度:</span>
                    <span class="value confidence-${apiMonitoring.confidence_score >= 90 ? 'good' : apiMonitoring.confidence_score >= 85 ? 'warning' : 'critical'}">${status.overall_confidence}</span>
                </div>

                <div class="status-divider"></div>

                <div class="status-item">
                    <span class="label">系统状态:</span>
                    <span class="value system-${apiMonitoring.system_health.status}">${apiMonitoring.system_health.icon} ${apiMonitoring.system_health.text}</span>
                </div>
                <div class="status-item">
                    <span class="label">可创建项目:</span>
                    <span class="value ${status.can_create_project ? 'success' : 'error'}">${status.can_create_project ? '✅ 是' : '❌ 否'}</span>
                </div>
            </div>
        `;

        // 添加API状态详情的悬停提示
        this.addAPIStatusTooltips(apiMonitoring.api_details);
    }

    addAPIStatusTooltips(apiDetails) {
        """为API状态添加详细信息提示"""
        document.querySelectorAll('.api-status-item').forEach((item, index) => {
            const apiKey = Object.keys(apiDetails)[index];
            const apiInfo = apiDetails[apiKey];

            item.title = `${apiInfo.details}\n点击查看详细信息`;
            item.style.cursor = 'pointer';

            item.onclick = () => {
                this.showAPIDetailModal(apiKey, apiInfo);
            };
        });
    }

    showAPIDetailModal(apiKey, apiInfo) {
        """显示API详细信息模态框"""
        // 这里可以显示更详细的API状态信息
        // 包括响应时间、错误率、最近的测试结果等
        alert(`API详细信息:\n${apiKey}\n状态: ${apiInfo.text}\n详情: ${apiInfo.details}`);
    }

    async openProject(projectName) {
        try {
            // 显示加载状态
            this.showLoading(`正在启动项目 ${projectName}...`);

            // 请求打开项目
            const response = await fetch(`/open_project/${projectName}`);

            if (response.ok) {
                // 成功，跳转到项目页面
                window.location.href = `/project/${projectName}/nine-grid`;
            } else {
                throw new Error('项目启动失败');
            }
        } catch (error) {
            console.error('打开项目失败:', error);
            this.showError(`打开项目失败: ${error.message}`);
        }
    }

    startStatusUpdates() {
        // 每5秒更新一次状态
        setInterval(() => {
            this.loadData();
        }, 5000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new StartupCover();
});

// 全局函数
function createNewProject() {
    showProjectDialog('create');
}

function openExistingProject() {
    showProjectDialog('open');
}

function showProjectDialog(mode) {
    const dialogHTML = mode === 'create' ? createProjectDialogHTML() : openProjectDialogHTML();

    // 创建模态对话框
    const modal = document.createElement('div');
    modal.className = 'project-modal';
    modal.innerHTML = dialogHTML;
    document.body.appendChild(modal);

    // 绑定事件
    bindProjectDialogEvents(modal, mode);

    // 显示动画
    setTimeout(() => modal.classList.add('show'), 10);
}

function createProjectDialogHTML() {
    return `
        <div class="modal-backdrop">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>🆕 创建新项目</h2>
                    <button class="close-btn" onclick="closeProjectDialog()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>项目名称:</label>
                        <input type="text" id="project-name" placeholder="例如: xkongcloud" />
                        <small>项目的根名称，用于标识项目</small>
                    </div>

                    <div class="form-group">
                        <label>工作目录:</label>
                        <textarea id="work-directory" rows="3" placeholder="例如: docs\\features\\F007-建立Commons库的治理机制-20250610\\nexus万用插座\\design\\v1"></textarea>
                        <small>相对于项目根目录的工作路径</small>
                    </div>

                    <div class="form-group">
                        <label>项目类型:</label>
                        <select id="project-type">
                            <option value="design">设计文档工作</option>
                            <option value="implementation">代码实施工作</option>
                            <option value="architecture">架构设计工作</option>
                            <option value="analysis">分析评估工作</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>项目描述:</label>
                        <textarea id="project-description" rows="2" placeholder="简要描述这个项目的工作内容"></textarea>
                    </div>

                    <div class="preview-section">
                        <h3>🔍 项目预览</h3>
                        <div id="project-preview" class="preview-content">
                            <p>请填写项目信息以查看预览</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="closeProjectDialog()">取消</button>
                    <button class="btn-primary" onclick="confirmCreateProject()">创建项目</button>
                </div>
            </div>
        </div>
    `;
}

function openProjectDialogHTML() {
    return `
        <div class="modal-backdrop">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>📁 打开现有项目</h2>
                    <button class="close-btn" onclick="closeProjectDialog()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>项目名称:</label>
                        <input type="text" id="project-name" placeholder="例如: enterprise_system" />
                    </div>

                    <div class="form-group">
                        <label>工作目录:</label>
                        <textarea id="work-directory" rows="3" placeholder="例如: docs\\features\\F008-企业系统重构\\architecture\\microservices\\v2"></textarea>
                    </div>

                    <div class="recent-projects">
                        <h3>📋 最近项目</h3>
                        <div id="recent-projects-list">
                            <div class="recent-project-item" onclick="selectRecentProject('xkongcloud', 'docs\\\\features\\\\F007-建立Commons库的治理机制-20250610\\\\nexus万用插座\\\\design\\\\v1')">
                                <div class="project-info">
                                    <strong>xkongcloud</strong>
                                    <small>F007万用插座设计</small>
                                </div>
                                <div class="project-time">2小时前</div>
                            </div>
                            <div class="recent-project-item" onclick="selectRecentProject('ai_platform', 'docs\\\\features\\\\T001-AI平台\\\\model_training\\\\v3')">
                                <div class="project-info">
                                    <strong>ai_platform</strong>
                                    <small>模型训练架构</small>
                                </div>
                                <div class="project-time">1天前</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="closeProjectDialog()">取消</button>
                    <button class="btn-primary" onclick="confirmOpenProject()">打开项目</button>
                </div>
            </div>
        </div>
    `;
}

function bindProjectDialogEvents(modal, mode) {
    if (mode === 'create') {
        // 绑定实时预览事件
        const inputs = modal.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('input', updateProjectPreview);
        });
    }

    // ESC键关闭
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeProjectDialog();
        }
    });
}

function updateProjectPreview() {
    const projectName = document.getElementById('project-name').value;
    const workDirectory = document.getElementById('work-directory').value;
    const projectType = document.getElementById('project-type').value;
    const description = document.getElementById('project-description').value;

    if (!projectName || !workDirectory) {
        document.getElementById('project-preview').innerHTML = '<p>请填写项目信息以查看预览</p>';
        return;
    }

    // 解析工作内容
    const analysis = analyzeProjectPath(workDirectory);

    const previewHTML = `
        <div class="preview-item">
            <strong>项目标识:</strong> ${projectName}
        </div>
        <div class="preview-item">
            <strong>工作类型:</strong> ${getProjectTypeText(projectType)}
        </div>
        <div class="preview-item">
            <strong>功能模块:</strong> ${analysis.feature || '未识别'}
        </div>
        <div class="preview-item">
            <strong>工作阶段:</strong> ${analysis.stage || '未识别'}
        </div>
        <div class="preview-item">
            <strong>版本:</strong> ${analysis.version || '未识别'}
        </div>
        ${description ? `<div class="preview-item"><strong>描述:</strong> ${description}</div>` : ''}
    `;

    document.getElementById('project-preview').innerHTML = previewHTML;
}

function analyzeProjectPath(path) {
    const analysis = {
        feature: null,
        stage: null,
        version: null
    };

    // 解析功能编号
    const featureMatch = path.match(/F(\d+)-([^\\]+)/);
    if (featureMatch) {
        analysis.feature = `F${featureMatch[1]} - ${featureMatch[2]}`;
    }

    // 解析工作阶段
    if (path.includes('design')) analysis.stage = '设计阶段';
    else if (path.includes('implementation')) analysis.stage = '实施阶段';
    else if (path.includes('architecture')) analysis.stage = '架构阶段';
    else if (path.includes('analysis')) analysis.stage = '分析阶段';

    // 解析版本
    const versionMatch = path.match(/v(\d+)/);
    if (versionMatch) {
        analysis.version = `v${versionMatch[1]}`;
    }

    return analysis;
}

function getProjectTypeText(type) {
    const types = {
        'design': '设计文档工作',
        'implementation': '代码实施工作',
        'architecture': '架构设计工作',
        'analysis': '分析评估工作'
    };
    return types[type] || type;
}

function selectRecentProject(name, directory) {
    document.getElementById('project-name').value = name;
    document.getElementById('work-directory').value = directory;
}

function closeProjectDialog() {
    const modal = document.querySelector('.project-modal');
    if (modal) {
        modal.classList.add('hide');
        setTimeout(() => modal.remove(), 300);
    }
}

async function confirmProject() {
    """统一的项目启动确认函数"""
    const projectData = {
        name: document.getElementById('project-name').value,
        workDirectory: document.getElementById('work-directory').value,
        description: document.getElementById('project-description')?.value || ''
    };

    if (!projectData.name || !projectData.workDirectory) {
        alert('请填写项目名称和工作目录');
        return;
    }

    try {
        // 显示启动状态
        showProjectStartupStatus(`正在启动项目 ${projectData.name}...`);

        // 调用统一的项目启动API
        const response = await fetch('/api/start_project', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(projectData)
        });

        const result = await response.json();

        if (result.success) {
            showProjectStartupStatus(`项目 ${projectData.name} 启动成功！正在进入工作界面...`);

            // 延迟一下让用户看到成功消息
            setTimeout(() => {
                closeProjectDialog();
                // 直接进入项目九宫格
                window.location.href = `/project/${projectData.name}/nine-grid`;
            }, 1000);
        } else {
            alert(`项目启动失败: ${result.error}`);
        }
    } catch (error) {
        alert(`项目启动失败: ${error.message}`);
    }
}

function showProjectStartupStatus(message) {
    """显示项目启动状态"""
    const statusDiv = document.getElementById('project-startup-status');
    if (statusDiv) {
        statusDiv.textContent = message;
        statusDiv.style.display = 'block';
    }
}

function showSystemSettings() {
    // 显示系统设置页面
    window.location.href = '/settings';
}

function showHelpDocs() {
    // 显示帮助文档
    window.open('/help', '_blank');
}
```

## 🔧 具体代码修改点

### 1. 服务器端修改（server_launcher.py）

#### 修改点1：移除默认指挥官创建
**当前代码**（第200-202行）：
```python
# 创建默认项目（包含指挥官和容器）
default_container = self.project_manager.create_project(self.default_project)
print("✅ 默认项目系统已加载")
```

**修改为**：
```python
# 🔧 IDEA式启动：不创建默认指挥官，等待项目选择
print("✅ 服务器已就绪，等待项目选择")
```

#### 修改点2：增强ProjectManager.create_project方法
**当前代码**（第37行）：
```python
def create_project(self, project_name: str, meeting_path: str = None):
```

**修改为**：
```python
def create_project(self, project_name: str, work_directory: str = None):
    """统一的项目创建方法 - 支持工作目录"""
    # 1. 生成环境路径
    environment_paths = self._generate_environment_paths(project_name, work_directory)

    # 2. 确保环境存在
    self._ensure_environment_exists(environment_paths)

    # 3. 创建指挥官和容器（现有逻辑）
    # 4. 配置工作环境
```

### 2. Web界面修改（app.py）

#### 修改点3：更新项目创建API
**当前代码**（第242-272行）：
```python
@self.app.route('/api/create_project', methods=['POST'])
def api_create_project():
    data = request.get_json()
    project_name = data.get('project_name')
    meeting_path = data.get('meeting_path')
```

**修改为**：
```python
@self.app.route('/api/start_project', methods=['POST'])
def api_start_project():
    """统一的项目启动API"""
    data = request.get_json()
    project_name = data.get('name')
    work_directory = data.get('workDirectory')
    description = data.get('description', '')
```

#### 修改点4：添加启动封面路由
**新增代码**：
```python
@self.app.route('/')
def startup_cover():
    """IDEA式启动封面"""
    return render_template('startup_cover.html')

@self.app.route('/api/project_cards')
def get_project_cards():
    """获取项目卡片数据"""
    # 实现项目状态管理逻辑
```

### 3. 前端界面修改

#### 修改点5：创建启动封面模板
**新建文件**：`tools/ace/src/web_interface/templates/startup_cover.html`
```html
<!-- IDEA式启动界面 -->
<div class="idea-startup-cover">
    <header class="startup-header">
        <h1>四重会议系统 V4.5</h1>
    </header>
    <!-- 项目卡片和操作面板 -->
</div>
```

#### 修改点6：更新主页路由
**当前代码**：`index.html` 直接显示九宫格
**修改为**：首页显示启动封面，九宫格移到 `/project/{name}/nine-grid`

### 4. 项目状态管理

#### 修改点7：新增ProjectStatusManager
**新建文件**：`tools/ace/src/four_layer_meeting_server/project_status_manager.py`
```python
class ProjectStatusManager:
    """项目状态管理器"""
    def __init__(self):
        self.project_history = {}
        self.active_commanders = {}

    def get_project_cards(self):
        """获取项目卡片数据"""

    def get_server_status(self):
        """获取API状态监控信息"""
```

### 5. API状态监控

#### 修改点8：集成现有API检查逻辑
**基于现有代码**：复用 `tools/ace/src/` 中的API测试器
```python
def _check_all_api_status(self):
    """检查所有API状态"""
    # 复用现有的API健康检查逻辑
    # 检查 DS R1-0528, DS V3-0324, DeepCoder 等
```

## 📋 实施流程

### Phase 1: 服务器端改造
1. **修改 server_launcher.py**：
   - 移除默认指挥官创建（第200-202行）
   - 增强 ProjectManager.create_project 方法
   - 添加 ProjectStatusManager 集成

2. **新增 project_status_manager.py**：
   - 实现项目状态管理
   - 集成API状态监控
   - 项目历史记录管理

### Phase 2: Web界面改造
1. **修改 app.py**：
   - 更新项目创建API（第242-272行）
   - 添加启动封面路由
   - 添加项目状态API

2. **新增 startup_cover.html**：
   - IDEA式启动界面
   - 项目卡片显示
   - API状态监控面板

### Phase 3: 前端功能完善
1. **前端实现**：直接参考源代码 `tools\ace\src\web_interface\static\js\startup_cover.js` 和 `tools\ace\src\web_interface\static\css\startup_cover.css`

2. **样式实现**：直接参考源代码 `tools\ace\src\web_interface\static\css\startup_cover.css`

### Phase 4: 测试和集成
1. **功能测试**：
   - 项目创建流程
   - API状态监控
   - 九宫格跳转

2. **集成测试**：
   - 与现有指挥官集成
   - 数据库和Meeting目录创建
   - 多项目并发测试

## 🎯 关键DRY原则

### 复用现有代码：
- **ProjectManager**: 复用现有的项目创建逻辑
- **API检查**: 复用现有的API测试器
- **指挥官**: 复用现有的指挥官实例化
- **容器**: 复用现有的万用状态容器

### 最小化修改：
- 不破坏现有的九宫格界面
- 不修改指挥官核心逻辑
- 不改变数据库和Meeting结构
- 只添加启动封面和项目管理功能

这样的设计确保了与现有代码的完美集成，同时实现了IDEA式的用户体验。
