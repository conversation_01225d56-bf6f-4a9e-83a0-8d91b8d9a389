/home/<USER>/apps/jdk-21.0.5/bin/java -javaagent:/home/<USER>/apps/idea-IU-243.23654.189/lib/idea_rt.jar=40445:/home/<USER>/apps/idea-IU-243.23654.189/bin -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -classpath /media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/test-classes:/media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/classes:/home/<USER>/works/project/mvnRepository/com/xfvape/uid/uid-generator/0.0.4-RELEASE/uid-generator-0.0.4-RELEASE.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-core/6.2.6/spring-core-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-jcl/6.2.6/spring-jcl-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-beans/6.2.6/spring-beans-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/mybatis/mybatis/3.2.3/mybatis-3.2.3.jar:/home/<USER>/works/project/mvnRepository/org/mybatis/mybatis-spring/1.2.4/mybatis-spring-1.2.4.jar:/home/<USER>/works/project/mvnRepository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/home/<USER>/works/project/mvnRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/log4j-over-slf4j/2.0.17/log4j-over-slf4j-2.0.17.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-context/6.2.6/spring-context-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-aop/6.2.6/spring-aop-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-expression/6.2.6/spring-expression-6.2.6.jar:/home/<USER>/works/project/mvnRepository/io/micrometer/micrometer-observation/1.14.6/micrometer-observation-1.14.6.jar:/home/<USER>/works/project/mvnRepository/io/micrometer/micrometer-commons/1.14.6/micrometer-commons-1.14.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-jdbc/6.2.6/spring-jdbc-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-tx/6.2.6/spring-tx-6.2.6.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-annotations/2.18.3/jackson-annotations-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-core/2.18.3/jackson-core-2.18.3.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter-test/3.4.5/spring-boot-starter-test-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter/3.4.5/spring-boot-starter-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot/3.4.5/spring-boot-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-autoconfigure/3.4.5/spring-boot-autoconfigure-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter-logging/3.4.5/spring-boot-starter-logging-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/home/<USER>/works/project/mvnRepository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/home/<USER>/works/project/mvnRepository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/home/<USER>/works/project/mvnRepository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-test/3.4.5/spring-boot-test-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-test-autoconfigure/3.4.5/spring-boot-test-autoconfigure-3.4.5.jar:/home/<USER>/works/project/mvnRepository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/home/<USER>/works/project/mvnRepository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/home/<USER>/works/project/mvnRepository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/home/<USER>/works/project/mvnRepository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/home/<USER>/works/project/mvnRepository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/home/<USER>/works/project/mvnRepository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/home/<USER>/works/project/mvnRepository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/home/<USER>/works/project/mvnRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-test/6.2.6/spring-test-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/xmlunit/xmlunit-core/2.10.0/xmlunit-core-2.10.0.jar:/home/<USER>/works/project/mvnRepository/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar:/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar:/home/<USER>/works/project/mvnRepository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/home/<USER>/works/project/mvnRepository/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-launcher/1.11.4/junit-platform-launcher-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/testcontainers/1.19.7/testcontainers-1.19.7.jar:/home/<USER>/works/project/mvnRepository/junit/junit/4.13.2/junit-4.13.2.jar:/home/<USER>/works/project/mvnRepository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/home/<USER>/works/project/mvnRepository/org/apache/commons/commons-compress/1.24.0/commons-compress-1.24.0.jar:/home/<USER>/works/project/mvnRepository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/home/<USER>/works/project/mvnRepository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-api/3.3.6/docker-java-api-3.3.6.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-transport-zerodep/3.3.6/docker-java-transport-zerodep-3.3.6.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-transport/3.3.6/docker-java-transport-3.3.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/postgresql/1.19.7/postgresql-1.19.7.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/jdbc/1.20.6/jdbc-1.20.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/database-commons/1.20.6/database-commons-1.20.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/junit-jupiter/1.19.7/junit-jupiter-1.19.7.jar:/home/<USER>/works/project/mvnRepository/org/openjdk/jmh/jmh-core/1.37/jmh-core-1.37.jar:/home/<USER>/works/project/mvnRepository/net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar:/home/<USER>/works/project/mvnRepository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/home/<USER>/works/project/mvnRepository/org/openjdk/jmh/jmh-generator-annprocess/1.37/jmh-generator-annprocess-1.37.jar:/home/<USER>/works/project/mvnRepository/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/home/<USER>/works/project/mvnRepository/org/postgresql/postgresql/42.7.5/postgresql-42.7.5.jar:/home/<USER>/works/project/mvnRepository/org/checkerframework/checker-qual/3.48.3/checker-qual-3.48.3.jar:/home/<USER>/works/project/mvnRepository/com/github/oshi/oshi-core/6.5.0/oshi-core-6.5.0.jar:/home/<USER>/works/project/mvnRepository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/home/<USER>/works/project/mvnRepository/net/java/dev/jna/jna-platform/5.14.0/jna-platform-5.14.0.jar:/home/<USER>/works/project/mvnRepository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar:/home/<USER>/works/project/mvnRepository/ch/qos/logback/logback-classic/1.5.16/logback-classic-1.5.16.jar:/home/<USER>/works/project/mvnRepository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/home/<USER>/works/project/mvnRepository/org/xkongkit/xkongkit-core/1.0.0-SNAPSHOT/xkongkit-core-1.0.0-SNAPSHOT.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/kryo/5.5.0/kryo-5.5.0.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/home/<USER>/works/project/mvnRepository/org/lmdbjava/lmdbjava/0.9.1/lmdbjava-0.9.1.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-constants/0.10.4/jnr-constants-0.10.4.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-ffi/2.2.17/jnr-ffi-2.2.17.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jffi/1.3.13/jffi-1.3.13.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jffi/1.3.13/jffi-1.3.13-native.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-commons/9.7.1/asm-commons-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-a64asm/1.0.0/jnr-a64asm-1.0.0.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-x86asm/1.0.2/jnr-x86asm-1.0.2.jar:/home/<USER>/works/project/mvnRepository/com/tokyocabinet/tokyocabinet/1.24/tokyocabinet-1.24.jar:/home/<USER>/works/project/mvnRepository/org/jackson/databind/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.3/jackson-datatype-jsr310-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/github/luben/zstd-jni/1.5.5-3/zstd-jni-1.5.5-3.jar:/home/<USER>/works/project/mvnRepository/com/github/ben-manes/caffeine/caffeine/3.1.8/caffeine-3.1.8.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/home/<USER>/works/project/mvnRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/home/<USER>/works/project/mvnRepository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-context/1.70.0/grpc-context-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-impl/0.31.1/opencensus-impl-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-impl-core/0.31.1/opencensus-impl-core-0.31.1.jar:/home/<USER>/works/project/mvnRepository/com/lmax/disruptor/3.4.2/disruptor-3.4.2.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-census/1.71.0/grpc-census-1.71.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-api/1.70.0/grpc-api-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-contrib-grpc-metrics/0.31.1/opencensus-contrib-grpc-metrics-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-services/1.71.0/grpc-services-1.71.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-stub/1.70.0/grpc-stub-1.70.0.jar:/home/<USER>/works/project/mvnRepository/org/codehaus/mojo/animal-sniffer-annotations/1.24/animal-sniffer-annotations-1.24.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-core/1.70.0/grpc-core-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar:/home/<USER>/works/project/mvnRepository/io/perfmark/perfmark-api/0.27.0/perfmark-api-0.27.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-protobuf/1.70.0/grpc-protobuf-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/protobuf/protobuf-java/3.25.6/protobuf-java-3.25.6.jar:/home/<USER>/works/project/mvnRepository/com/google/api/grpc/proto-google-common-protos/2.46.0/proto-google-common-protos-2.46.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-protobuf-lite/1.70.0/grpc-protobuf-lite-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-util/1.70.0/grpc-util-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/protobuf/protobuf-java-util/3.25.6/protobuf-java-util-3.25.6.jar:/home/<USER>/works/project/mvnRepository/com/google/errorprone/error_prone_annotations/2.30.0/error_prone_annotations-2.30.0.jar:/home/<USER>/works/project/mvnRepository/com/google/code/gson/gson/2.11.0/gson-2.11.0.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-x86_64.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-common/4.1.119.Final/netty-common-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-buffer/4.1.119.Final/netty-buffer-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport/4.1.119.Final/netty-transport-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-resolver/4.1.119.Final/netty-resolver-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-native-unix-common/4.1.119.Final/netty-transport-native-unix-common-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-classes-epoll/4.1.119.Final/netty-transport-classes-epoll-4.1.119.Final.jar org.xkong.cloud.commons.uid.TestRunner
===== 运行单元测试 =====
14:38:55,861 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-classic version 1.5.16
14:38:55,861 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-core version 1.5.18
14:38:55,862 |-WARN in ch.qos.logback.classic.LoggerContext[default] - Versions of logback-core and logback-classic are different!
14:38:55,888 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4e5ed836 - Here is a list of configurators discovered as a service, by rank: 
14:38:55,889 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4e5ed836 -   org.springframework.boot.logging.logback.RootLogLevelConfigurator
14:38:55,889 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4e5ed836 - They will be invoked in order until ExecutionStatus.DO_NOT_INVOKE_NEXT_IF_ANY is returned.
14:38:55,889 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4e5ed836 - Constructed configurator of type class org.springframework.boot.logging.logback.RootLogLevelConfigurator
14:38:55,903 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4e5ed836 - org.springframework.boot.logging.logback.RootLogLevelConfigurator.configure() call lasted 7 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
14:38:55,903 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4e5ed836 - Trying to configure with ch.qos.logback.classic.joran.SerializedModelConfigurator
14:38:55,904 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4e5ed836 - Constructed configurator of type class ch.qos.logback.classic.joran.SerializedModelConfigurator
14:38:55,905 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.scmo]
14:38:55,906 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback.scmo]
14:38:55,906 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4e5ed836 - ch.qos.logback.classic.joran.SerializedModelConfigurator.configure() call lasted 2 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
14:38:55,906 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4e5ed836 - Trying to configure with ch.qos.logback.classic.util.DefaultJoranConfigurator
14:38:55,906 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4e5ed836 - Constructed configurator of type class ch.qos.logback.classic.util.DefaultJoranConfigurator
14:38:55,907 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-test.xml] at [file:/media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/test-classes/logback-test.xml]
14:38:56,378 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [CONSOLE]
14:38:56,378 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
14:38:56,386 |-INFO in ch.qos.logback.core.model.processor.ImplicitModelHandler - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
14:38:56,489 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - BEWARE: Writing to the console can be very slow. Avoid logging to the 
14:38:56,489 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - console in production environments, especially in high volume systems.
14:38:56,489 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - See also https://logback.qos.ch/codes.html#slowConsole
14:38:56,489 |-INFO in ch.qos.logback.classic.model.processor.RootLoggerModelHandler - Setting level of ROOT logger to INFO
14:38:56,489 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[ROOT]
14:38:56,491 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [org.xkong.cloud.commons.uid] to DEBUG
14:38:56,491 |-INFO in ch.qos.logback.core.model.processor.DefaultProcessor@eadd4fb - End of configuration.
14:38:56,492 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@740fb309 - Registering current configuration as safe fallback point
14:38:56,492 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4e5ed836 - ch.qos.logback.classic.util.DefaultJoranConfigurator.configure() call lasted 586 milliseconds. ExecutionStatus=DO_NOT_INVOKE_NEXT_IF_ANY

2025-05-22 14:38:58.177 [main] INFO  o.x.c.c.u.f.MachineFingerprints - 使用OSHI库成功获取系统信息
2025-05-22 14:39:04.176 [main] INFO  o.x.c.c.u.f.MachineFingerprints - 已收集机器特征码: {"os_arch":"amd64","os_name":"Linux","fingerprint_hash":"2c974415142f2dfb2c262abd914df33384b81aa504f53b02d0658bc18c767b85","hostname":"long-VirtualBox","mac_addresses":["7E:2****:E8:3D","08:0****:3A:87"],"os_version":"5.4.0-91-generic"}
Mockito is currently self-attaching to enable the inline-mock-maker. This will no longer work in future releases of the JDK. Please add Mockito as an agent to your build what is described in Mockito's documentation: https://javadoc.io/doc/org.mockito/mockito-core/latest/org/mockito/Mockito.html#0.3
WARNING: A Java agent has been loaded dynamically (/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
Java HotSpot(TM) 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classpath has been appended
2025-05-22 14:39:06.461 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 14:39:06.554 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 14:39:06.570 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 14:39:06.841 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 14:39:06.842 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 14:39:06.848 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 14:39:06.853 [main] DEBUG o.x.c.c.u.c.KeyManagementService - 使用NativePRNGNonBlocking算法的SecureRandom
2025-05-22 14:39:06.855 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已为应用 test-app 环境 test-env 创建新的 test-key-type 类型密钥
2025-05-22 14:39:06.862 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 14:39:06.870 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 14:39:06.874 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 14:39:06.876 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已清除密钥缓存
2025-05-22 14:39:06.909 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 14:39:06.917 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-22 14:39:06.920 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 14:39:06.920 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-22 14:39:06.922 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 14:39:06.923 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-22 14:39:06.924 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-22 14:39:06.924 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-22 14:39:07.134 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 14:39:07.134 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-22 14:39:07.136 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 14:39:07.137 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-22 14:39:07.137 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-22 14:39:07.137 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-22 14:39:07.137 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 已清除所有验证缓存
2025-05-22 14:39:07.224 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 14:39:07.224 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 14:39:07.224 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 14:39:07.224 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 14:39:07.224 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:07.225 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 14:39:07.225 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 14:39:07.225 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 14:39:07.226 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: main
2025-05-22 14:39:07.227 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: main
2025-05-22 14:39:07.227 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: main
2025-05-22 14:39:07.227 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: main
2025-05-22 14:39:07.227 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: main
2025-05-22 14:39:07.227 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: main
2025-05-22 14:39:07.228 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: main
2025-05-22 14:39:07.232 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:07.269 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 14:39:07.269 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 14:39:07.269 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 14:39:07.269 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 14:39:07.269 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:07.270 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 14:39:07.280 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-22 14:39:07.280 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: main
2025-05-22 14:39:07.280 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 14:39:07.280 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 14:39:07.290 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 14:39:07.290 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 14:39:07.290 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 14:39:07.290 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 14:39:07.291 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:07.291 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 14:39:07.291 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 无法为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 14:39:07.292 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 14:39:07.292 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取工作机器ID时发生异常，当前线程: main, 异常: 无法为实例 12345 分配工作机器ID
java.lang.IllegalStateException: 无法为实例 12345 分配工作机器ID
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.getWorkerId(PersistentInstanceWorkerIdAssigner.java:161)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.lambda$testGetWorkerId_MaxReached$2(PersistentInstanceWorkerIdAssignerTest.java:248)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3128)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.testGetWorkerId_MaxReached(PersistentInstanceWorkerIdAssignerTest.java:248)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:767)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$8(TestMethodTestDescriptor.java:217)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:213)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:138)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.SessionPerRequestLauncher.execute(SessionPerRequestLauncher.java:63)
	at org.xkong.cloud.commons.uid.TestRunner.runTests(TestRunner.java:88)
	at org.xkong.cloud.commons.uid.TestRunner.runUnitTests(TestRunner.java:53)
	at org.xkong.cloud.commons.uid.TestRunner.main(TestRunner.java:27)
2025-05-22 14:39:07.303 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 14:39:07.303 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 14:39:07.303 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 14:39:07.304 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 14:39:07.304 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:07.304 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 14:39:07.307 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 插入新的工作机器ID失败，可能存在并发冲突，工作机器ID: 42
2025-05-22 14:39:07.308 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，实例ID: null
2025-05-22 14:39:07.308 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 无法为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 14:39:07.308 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 14:39:07.308 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取工作机器ID时发生异常，当前线程: main, 异常: 无法为实例 12345 分配工作机器ID
java.lang.IllegalStateException: 无法为实例 12345 分配工作机器ID
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.getWorkerId(PersistentInstanceWorkerIdAssigner.java:161)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.testGetWorkerId_AllocateNew(PersistentInstanceWorkerIdAssignerTest.java:185)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:767)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$8(TestMethodTestDescriptor.java:217)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:213)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:138)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.SessionPerRequestLauncher.execute(SessionPerRequestLauncher.java:63)
	at org.xkong.cloud.commons.uid.TestRunner.runTests(TestRunner.java:88)
	at org.xkong.cloud.commons.uid.TestRunner.runUnitTests(TestRunner.java:53)
	at org.xkong.cloud.commons.uid.TestRunner.main(TestRunner.java:27)
2025-05-22 14:39:07.318 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: main
2025-05-22 14:39:07.318 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: false -> false，当前线程: main
2025-05-22 14:39:07.318 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: main
2025-05-22 14:39:07.319 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: main
2025-05-22 14:39:07.319 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 14:39:07.319 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 14:39:07.319 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 14:39:07.319 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 14:39:07.319 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:07.319 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 14:39:07.319 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 14:39:07.319 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 14:39:07.319 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: main
2025-05-22 14:39:07.328 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 14:39:07.329 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 14:39:07.329 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 14:39:07.329 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 14:39:07.329 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:07.329 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 14:39:07.329 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 14:39:07.329 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 14:39:07.332 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 14:39:07.333 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 14:39:07.348 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 14:39:07.348 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 14:39:07.349 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 14:39:07.349 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 14:39:07.349 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:07.349 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 14:39:07.349 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 14:39:07.349 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 14:39:07.355 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 14:39:07.355 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 14:39:07.355 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 14:39:07.355 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 14:39:07.355 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:07.356 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 14:39:07.356 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 14:39:07.356 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 14:39:07.362 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放成功
2025-05-22 14:39:07.369 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 14:39:07.369 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 14:39:07.369 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 14:39:07.369 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 14:39:07.369 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:07.370 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 14:39:07.370 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 14:39:07.370 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 14:39:07.370 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: main
2025-05-22 14:39:07.370 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: main
2025-05-22 14:39:07.371 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: main
2025-05-22 14:39:07.371 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: main
2025-05-22 14:39:07.371 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: main
2025-05-22 14:39:07.371 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: main
2025-05-22 14:39:07.371 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: main
2025-05-22 14:39:07.373 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign

Test run finished after 8888 ms
[         5 containers found      ]
[         0 containers skipped    ]
[         5 containers started    ]
[         0 containers aborted    ]
[         5 containers successful ]
[         0 containers failed     ]
[        25 tests found           ]
[         0 tests skipped         ]
[        25 tests started         ]
[         0 tests aborted         ]
[        22 tests successful      ]
[         3 tests failed          ]


===== 运行集成测试 =====
2025-05-22 14:39:07.733 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:07.733 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:39:07.733 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:39:07.734 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:07.734 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:07.734 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:07.734 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:07.735 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:07.736 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:07.738 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:39:07.738 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:39:07.738 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:39:07.805 [main] INFO  o.testcontainers.images.PullPolicy - Image pull policy will be performed by: DefaultPullPolicy()
2025-05-22 14:39:07.809 [main] INFO  o.t.utility.ImageNameSubstitutor - Image name substitution will be performed by: DefaultImageNameSubstitutor (composite of 'ConfigurationFileImageNameSubstitutor' and 'PrefixingImageNameSubstitutor')
2025-05-22 14:39:07.874 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:07.874 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:39:07.874 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:39:07.874 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:07.874 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:07.874 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:07.874 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:07.884 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:07.884 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:07.884 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:39:07.884 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:39:07.884 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:39:08.972 [main] INFO  o.t.d.DockerClientProviderStrategy - Found Docker environment with local Unix socket (unix:///var/run/docker.sock)
2025-05-22 14:39:08.975 [main] INFO  o.testcontainers.DockerClientFactory - Docker host IP address is localhost
2025-05-22 14:39:08.996 [main] INFO  o.testcontainers.DockerClientFactory - Connected to docker: 
  Server Version: 28.1.1
  API Version: 1.49
  Operating System: Linux Mint 20.3
  Total Memory: 7960 MB
2025-05-22 14:39:09.295 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Pulling docker image: testcontainers/ryuk:0.6.0. Please be patient; this may take some time but only needs to be done once.
2025-05-22 14:39:09.315 [main] INFO  o.t.utility.RegistryAuthLocator - Failure when attempting to lookup auth config. Please ignore if you don't have images in an authenticated registry. Details: (dockerImageName: testcontainers/ryuk:latest, configFile: /home/<USER>/.docker/config.json, configEnv: DOCKER_AUTH_CONFIG). Falling back to docker-java default behaviour. Exception message: Status 404: No config supplied. Checked in order: /home/<USER>/.docker/config.json (file not found), DOCKER_AUTH_CONFIG (not set)
2025-05-22 14:39:13.682 [docker-java-stream--623331358] INFO  tc.testcontainers/ryuk:0.6.0 - Starting to pull image
2025-05-22 14:39:14.039 [docker-java-stream--623331358] INFO  tc.testcontainers/ryuk:0.6.0 - Pulling image layers:  0 pending,  0 downloaded,  0 extracted, (0 bytes/0 bytes)
2025-05-22 14:39:19.081 [docker-java-stream--623331358] INFO  tc.testcontainers/ryuk:0.6.0 - Pulling image layers:  2 pending,  1 downloaded,  0 extracted, (3 MB/? MB)
2025-05-22 14:39:19.213 [docker-java-stream--623331358] INFO  tc.testcontainers/ryuk:0.6.0 - Pulling image layers:  1 pending,  2 downloaded,  0 extracted, (5 MB/? MB)
2025-05-22 14:39:19.339 [docker-java-stream--623331358] INFO  tc.testcontainers/ryuk:0.6.0 - Pulling image layers:  1 pending,  2 downloaded,  1 extracted, (6 MB/? MB)
2025-05-22 14:39:26.692 [docker-java-stream--623331358] INFO  tc.testcontainers/ryuk:0.6.0 - Pulling image layers:  0 pending,  3 downloaded,  1 extracted, (6 MB/6 MB)
2025-05-22 14:39:26.805 [docker-java-stream--623331358] INFO  tc.testcontainers/ryuk:0.6.0 - Pulling image layers:  0 pending,  3 downloaded,  2 extracted, (6 MB/6 MB)
2025-05-22 14:39:26.893 [docker-java-stream--623331358] INFO  tc.testcontainers/ryuk:0.6.0 - Pulling image layers:  0 pending,  3 downloaded,  3 extracted, (6 MB/6 MB)
2025-05-22 14:39:26.969 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Image testcontainers/ryuk:0.6.0 pull took PT17.673428674S
2025-05-22 14:39:26.969 [docker-java-stream--623331358] INFO  tc.testcontainers/ryuk:0.6.0 - Pull complete. 3 layers, pulled in 13s (downloaded 6 MB at 499 KB/s)
2025-05-22 14:39:27.090 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Creating container for image: testcontainers/ryuk:0.6.0
2025-05-22 14:39:27.213 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.214 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.215 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.215 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.215 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.215 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.215 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.215 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.274 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.275 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.275 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.275 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.275 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.275 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.275 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.276 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.287 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.301 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.318 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.319 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.320 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.320 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.320 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.320 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.320 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.320 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.340 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.341 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.341 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.341 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.341 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.341 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.341 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.342 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.370 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.371 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.372 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.372 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.372 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.372 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.372 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:39:27.372 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.717 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.717 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:39:27.717 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:39:27.717 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.717 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.717 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.717 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.720 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.720 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:27.720 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:39:27.720 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.720 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:39:27.776 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.776 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:39:27.777 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:39:27.777 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.777 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.777 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.777 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.777 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.777 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:27.778 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.779 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-22 14:39:27.779 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:39:27.779 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.779 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:39:27.823 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.823 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:39:27.823 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:39:27.823 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.823 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.823 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.823 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.824 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.824 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:27.825 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:39:27.825 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.825 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:39:27.850 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.850 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:39:27.850 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:39:27.850 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.850 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.850 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.850 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.851 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.851 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:27.852 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:39:27.852 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.852 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:39:27.872 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.873 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:39:27.873 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:39:27.873 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.873 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.873 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.873 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.874 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:27.874 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:27.874 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:39:27.874 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:39:27.874 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:39:28.413 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Container testcontainers/ryuk:0.6.0 is starting: eeb0609809da5382602086f6440d777c4140823cf068a29ef68f5693d2c2209a
2025-05-22 14:39:30.535 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Container testcontainers/ryuk:0.6.0 started in PT3.445494254S
2025-05-22 14:39:30.544 [main] INFO  o.t.utility.RyukResourceReaper - Ryuk started - will monitor and terminate Testcontainers containers on JVM exit
2025-05-22 14:39:30.544 [main] INFO  o.testcontainers.DockerClientFactory - Checking the system...
2025-05-22 14:39:30.546 [main] INFO  o.testcontainers.DockerClientFactory - ✔︎ Docker server version should be at least 1.6.0
2025-05-22 14:39:30.557 [main] INFO  tc.postgres:17.4 - Pulling docker image: postgres:17.4. Please be patient; this may take some time but only needs to be done once.
2025-05-22 14:39:34.474 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Starting to pull image
2025-05-22 14:39:34.474 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  0 pending,  0 downloaded,  0 extracted, (0 bytes/0 bytes)
2025-05-22 14:39:36.587 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers: 13 pending,  1 downloaded,  0 extracted, (1 KB/? MB)
2025-05-22 14:39:38.269 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers: 12 pending,  2 downloaded,  0 extracted, (5 MB/? MB)
2025-05-22 14:39:38.549 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers: 11 pending,  3 downloaded,  0 extracted, (7 MB/? MB)
2025-05-22 14:39:40.811 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers: 10 pending,  4 downloaded,  0 extracted, (31 MB/? MB)
2025-05-22 14:39:41.447 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  9 pending,  5 downloaded,  0 extracted, (32 MB/? MB)
2025-05-22 14:39:41.791 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  8 pending,  6 downloaded,  0 extracted, (33 MB/? MB)
2025-05-22 14:39:42.130 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  7 pending,  7 downloaded,  0 extracted, (34 MB/? MB)
2025-05-22 14:39:42.533 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  6 pending,  8 downloaded,  0 extracted, (39 MB/? MB)
2025-05-22 14:39:43.019 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  6 pending,  8 downloaded,  1 extracted, (40 MB/? MB)
2025-05-22 14:39:43.071 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  6 pending,  8 downloaded,  2 extracted, (40 MB/? MB)
2025-05-22 14:39:43.172 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  5 pending,  9 downloaded,  2 extracted, (40 MB/? MB)
2025-05-22 14:39:43.299 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  5 pending,  9 downloaded,  3 extracted, (40 MB/? MB)
2025-05-22 14:39:43.373 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  5 pending,  9 downloaded,  4 extracted, (40 MB/? MB)
2025-05-22 14:39:43.796 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  4 pending, 10 downloaded,  4 extracted, (41 MB/? MB)
2025-05-22 14:39:43.915 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  4 pending, 10 downloaded,  5 extracted, (41 MB/? MB)
2025-05-22 14:39:44.028 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  4 pending, 10 downloaded,  6 extracted, (42 MB/? MB)
2025-05-22 14:39:44.053 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  4 pending, 10 downloaded,  7 extracted, (42 MB/? MB)
2025-05-22 14:39:44.090 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  4 pending, 10 downloaded,  8 extracted, (42 MB/? MB)
2025-05-22 14:39:44.349 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  3 pending, 11 downloaded,  8 extracted, (44 MB/? MB)
2025-05-22 14:39:45.001 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  2 pending, 12 downloaded,  8 extracted, (49 MB/? MB)
2025-05-22 14:39:45.057 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  1 pending, 13 downloaded,  8 extracted, (49 MB/? MB)
2025-05-22 14:39:47.211 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.212 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.212 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.212 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.212 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.212 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.212 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.213 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.267 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.267 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.268 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.268 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.268 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.287 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.300 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.316 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.317 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.317 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.317 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.317 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.317 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.317 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.317 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.337 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.338 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.338 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.338 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.338 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.338 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.338 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.339 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.367 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.368 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.368 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.368 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.368 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.368 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.368 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:39:47.368 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.713 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.713 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:39:47.713 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:39:47.713 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.713 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.713 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.713 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.715 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.716 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:47.716 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:39:47.716 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.716 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:39:47.769 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.769 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:39:47.769 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:39:47.769 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.769 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.769 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.769 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.770 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:47.771 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.772 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-22 14:39:47.772 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:39:47.772 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.772 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:39:47.818 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:39:47.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:39:47.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.818 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.819 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.819 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:47.819 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:39:47.819 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.819 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:39:47.839 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.839 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:39:47.839 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:39:47.839 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.839 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.839 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.839 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.840 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.840 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:47.841 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:39:47.841 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.841 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:39:47.868 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.869 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:39:47.869 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:39:47.869 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.869 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.869 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.869 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.870 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:39:47.870 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:39:47.870 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:39:47.870 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:39:47.870 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:01.104 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  0 pending, 14 downloaded,  8 extracted, (148 MB/149 MB)
2025-05-22 14:40:07.211 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.211 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.212 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.212 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.212 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.212 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.212 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.212 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.267 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.267 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.268 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.268 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.269 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.286 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.300 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.316 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.317 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.317 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.317 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.317 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.317 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.317 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.317 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.337 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.338 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.338 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.338 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.338 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.338 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.338 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.338 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.372 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.373 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.373 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.373 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.373 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.373 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.373 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:07.373 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.713 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.713 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:07.713 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:07.713 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.713 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.713 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.713 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.716 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.716 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:07.717 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:07.717 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.717 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:07.769 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.769 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:07.769 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:07.769 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.769 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.769 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.769 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.770 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:07.770 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.771 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-22 14:40:07.771 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:07.771 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.771 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:07.818 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:07.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:07.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.818 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.819 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.819 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:07.819 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:07.819 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.819 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:07.838 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.838 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:07.838 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:07.838 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.838 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.838 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.839 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.839 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.839 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:07.840 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:07.840 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.840 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:07.874 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.874 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:07.874 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:07.874 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.874 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.874 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.874 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.875 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:07.875 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:07.876 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:07.876 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:07.876 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:08.737 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  0 pending, 14 downloaded,  9 extracted, (149 MB/149 MB)
2025-05-22 14:40:08.773 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  0 pending, 14 downloaded, 10 extracted, (149 MB/149 MB)
2025-05-22 14:40:08.818 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  0 pending, 14 downloaded, 11 extracted, (149 MB/149 MB)
2025-05-22 14:40:08.851 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  0 pending, 14 downloaded, 12 extracted, (149 MB/149 MB)
2025-05-22 14:40:08.884 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  0 pending, 14 downloaded, 13 extracted, (149 MB/149 MB)
2025-05-22 14:40:08.961 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pulling image layers:  0 pending, 14 downloaded, 14 extracted, (149 MB/149 MB)
2025-05-22 14:40:09.000 [docker-java-stream--921108485] INFO  tc.postgres:17.4 - Pull complete. 14 layers, pulled in 34s (downloaded 149 MB at 4 MB/s)
2025-05-22 14:40:09.000 [main] INFO  tc.postgres:17.4 - Image postgres:17.4 pull took PT38.443407832S
2025-05-22 14:40:09.008 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 14:40:21.939 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 03c59939047bbde620b3436c1fba904da4e60c31bf8e7b6d768dc19e6d4f66a2
2025-05-22 14:40:26.968 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT17.960789632S
2025-05-22 14:40:26.969 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 14:40:27.159 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-22 14:40:27.213 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.213 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.214 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.214 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.214 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.214 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.214 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.214 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.268 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.268 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.269 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.287 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.301 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.317 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.317 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.317 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.317 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.317 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.317 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.317 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.318 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.344 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.344 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.344 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.344 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.345 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.345 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.345 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.345 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.367 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.368 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.368 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.368 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.368 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.368 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.368 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:27.369 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.714 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.715 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:27.715 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:27.715 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.715 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.715 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.715 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.716 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.716 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:27.716 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:27.716 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.716 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:27.760 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7fac18dc
2025-05-22 14:40:27.762 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-22 14:40:27.769 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.769 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:27.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:27.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.770 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.770 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:27.771 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.771 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-22 14:40:27.772 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:27.772 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.772 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:27.818 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:27.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:27.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.818 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.818 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.819 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.819 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:27.819 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:27.819 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.819 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:27.832 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 14:40:27.835 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 14:40:27.836 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 14:40:27.836 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 14:40:27.840 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-22 14:40:27.845 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.845 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:27.845 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:27.845 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.845 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.845 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.845 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.847 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.847 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:27.847 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:27.847 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.847 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:27.857 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 14:40:27.858 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-22 14:40:27.859 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-22 14:40:27.869 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.869 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:27.869 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:27.869 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.869 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.869 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.869 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.871 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:27.871 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:27.872 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:27.872 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:27.872 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:27.884 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 14:40:27.884 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-22 14:40:27.886 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 14:40:27.896 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 14:40:27.896 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 14:40:27.896 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 14:40:29.766 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 14:40:30.311 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 0d060bf64ee0e3452672f8517c035c67772ae2fbf5bae1c462fad4cc38b1e0f8
2025-05-22 14:40:36.010 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT6.244514209S
2025-05-22 14:40:36.010 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 14:40:36.043 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-05-22 14:40:36.064 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@2830315f
2025-05-22 14:40:36.064 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-05-22 14:40:36.064 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 14:40:36.065 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 14:40:36.068 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 不存在，正在自动创建
2025-05-22 14:40:36.070 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 已成功创建
2025-05-22 14:40:36.070 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 14:40:36.072 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-22 14:40:36.084 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 14:40:36.084 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-22 14:40:36.085 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-22 14:40:36.094 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 14:40:36.094 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-22 14:40:36.095 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 14:40:36.103 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 14:40:36.103 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 14:40:36.103 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 14:40:36.119 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Starting...
2025-05-22 14:40:36.142 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-3 - Added connection org.postgresql.jdbc.PgConnection@312a738d
2025-05-22 14:40:36.142 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Start completed.
2025-05-22 14:40:36.142 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 14:40:36.143 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 14:40:36.145 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 14:40:36.145 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 14:40:36.148 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 14:40:36.152 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 14:40:36.165 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 14:40:36.165 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 14:40:36.173 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 14:40:36.174 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 14:40:36.179 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 14:40:36.179 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 14:40:36.181 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 14:40:36.182 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 14:40:36.185 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 14:40:36.186 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 14:40:36.186 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 14:40:36.189 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Starting...
2025-05-22 14:40:36.212 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-4 - Added connection org.postgresql.jdbc.PgConnection@20be890d
2025-05-22 14:40:36.212 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Start completed.
2025-05-22 14:40:36.212 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 14:40:36.213 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 14:40:36.217 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 14:40:36.217 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 14:40:36.223 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 14:40:36.225 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 14:40:36.236 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 14:40:36.237 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 14:40:36.243 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 14:40:36.248 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 14:40:36.261 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 14:40:36.261 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 14:40:36.263 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 14:40:36.265 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 14:40:36.269 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 14:40:36.269 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 14:40:36.269 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 14:40:37.815 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 14:40:38.410 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: ff31850c68c8c9025fb89c3b432246504320efb957e064a4d2ca484c101a123b
2025-05-22 14:40:43.298 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.482542366S
2025-05-22 14:40:43.298 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 14:40:43.300 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-5 - Starting...
2025-05-22 14:40:43.325 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-5 - Added connection org.postgresql.jdbc.PgConnection@517fbf62
2025-05-22 14:40:43.325 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-5 - Start completed.
2025-05-22 14:40:43.325 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 14:40:43.326 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 14:40:43.330 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 不存在，正在自动创建
2025-05-22 14:40:43.331 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 已成功创建
2025-05-22 14:40:43.331 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 14:40:43.335 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-22 14:40:43.353 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 14:40:43.353 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-22 14:40:43.357 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-22 14:40:43.369 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 14:40:43.370 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-22 14:40:43.377 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 14:40:43.385 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 14:40:43.385 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 14:40:43.385 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 14:40:43.390 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件清理关闭钩子
2025-05-22 14:40:43.390 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/expiry-test2958915610935984957, 描述: 创建的临时目录
2025-05-22 14:40:43.390 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/expiry-test-210650072466825966817, 描述: 创建的临时目录
2025-05-22 14:40:43.391 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/expiry-test2958915610935984957/instance-id
2025-05-22 14:40:43.391 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 14:40:43.394 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 14:40:43.394 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 14:40:43.417 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 1
2025-05-22 14:40:43.640 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/expiry-test2958915610935984957/instance-id
2025-05-22 14:40:43.643 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-6 - Starting...
2025-05-22 14:40:43.661 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-6 - Added connection org.postgresql.jdbc.PgConnection@147c00aa
2025-05-22 14:40:43.661 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-6 - Start completed.
2025-05-22 14:40:43.661 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 14:40:43.662 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 14:40:43.664 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 14:40:43.664 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 14:40:43.666 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 14:40:43.667 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 14:40:43.674 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 14:40:43.674 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 14:40:43.675 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 14:40:43.676 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 14:40:43.679 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 14:40:43.679 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 14:40:43.680 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 14:40:43.681 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 14:40:43.684 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 14:40:43.684 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 14:40:43.684 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 14:40:43.684 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/lease-test10706408677785311086, 描述: 创建的临时目录
2025-05-22 14:40:43.685 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/lease-test10706408677785311086/instance-id
2025-05-22 14:40:43.685 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 14:40:43.686 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 14:40:43.687 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 14:40:43.687 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 2
2025-05-22 14:40:43.689 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/lease-test10706408677785311086/instance-id
2025-05-22 14:40:43.689 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 14:40:43.689 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 14:40:43.689 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 14:40:43.689 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 2 分配工作机器ID，当前线程: main
2025-05-22 14:40:43.689 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 2 是否已分配工作机器ID
2025-05-22 14:40:43.691 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 14:40:43.695 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 0，实例ID: 2
2025-05-22 14:40:43.696 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 2 分配了新的工作机器ID: 0，当前线程: main
2025-05-22 14:40:43.696 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 14:40:43.696 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 14:40:44.701 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: worker-id-lease-renewal
2025-05-22 14:40:44.701 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 2，schemaName: infra_uid，当前线程: worker-id-lease-renewal
2025-05-22 14:40:44.703 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:44.704 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '5 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 2]，当前线程: worker-id-lease-renewal
2025-05-22 14:40:44.705 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:44.706 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: worker-id-lease-renewal
2025-05-22 14:40:44.709 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-22 14:40:44.710 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-7 - Starting...
2025-05-22 14:40:44.729 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-7 - Added connection org.postgresql.jdbc.PgConnection@6076c66
2025-05-22 14:40:44.729 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-7 - Start completed.
2025-05-22 14:40:44.729 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 14:40:44.730 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 14:40:44.732 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 14:40:44.732 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 14:40:44.735 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 14:40:44.736 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 14:40:44.742 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 14:40:44.742 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 14:40:44.743 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 14:40:44.744 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 14:40:44.747 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 14:40:44.747 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 14:40:44.749 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 14:40:44.750 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 14:40:44.752 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 14:40:44.752 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 14:40:44.752 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 14:40:44.753 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test4327541172270449065, 描述: 创建的临时目录
2025-05-22 14:40:44.753 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test4327541172270449065/instance-id
2025-05-22 14:40:44.753 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 14:40:44.755 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 14:40:44.755 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 14:40:44.757 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 3
2025-05-22 14:40:44.758 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test4327541172270449065/instance-id
2025-05-22 14:40:44.758 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 14:40:44.758 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 14:40:44.758 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 14:40:44.758 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 3 分配工作机器ID，当前线程: main
2025-05-22 14:40:44.758 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 3 是否已分配工作机器ID
2025-05-22 14:40:44.759 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 14:40:44.761 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 3
2025-05-22 14:40:44.761 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 3 分配了新的工作机器ID: 0，当前线程: main
2025-05-22 14:40:44.762 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 14:40:44.762 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 14:40:44.763 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-22 14:40:44.765 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-8 - Starting...
2025-05-22 14:40:44.782 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-8 - Added connection org.postgresql.jdbc.PgConnection@6c70b7c3
2025-05-22 14:40:44.782 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-8 - Start completed.
2025-05-22 14:40:44.782 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 14:40:44.783 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 14:40:44.785 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 14:40:44.785 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 14:40:44.787 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 14:40:44.788 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 14:40:44.795 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 14:40:44.795 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 14:40:44.797 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 14:40:44.798 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 14:40:44.801 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 14:40:44.801 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 14:40:44.803 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 14:40:44.803 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 14:40:44.806 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 14:40:44.806 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 14:40:44.806 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 14:40:44.807 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test8875718148752879513, 描述: 创建的临时目录
2025-05-22 14:40:44.807 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test8875718148752879513, 描述: 并发测试基础目录
2025-05-22 14:40:44.809 [pool-1-thread-2] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test8875718148752879513/instance-id-1, 描述: 并发测试实例 1 的ID文件
2025-05-22 14:40:44.809 [pool-1-thread-3] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test8875718148752879513/instance-id-2, 描述: 并发测试实例 2 的ID文件
2025-05-22 14:40:44.809 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test8875718148752879513/instance-id-2
2025-05-22 14:40:44.809 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test8875718148752879513/instance-id-1
2025-05-22 14:40:44.809 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 14:40:44.809 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 14:40:44.809 [pool-1-thread-1] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test8875718148752879513/instance-id-0, 描述: 并发测试实例 0 的ID文件
2025-05-22 14:40:44.809 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test8875718148752879513/instance-id-0
2025-05-22 14:40:44.809 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 14:40:44.809 [pool-1-thread-4] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test8875718148752879513/instance-id-3, 描述: 并发测试实例 3 的ID文件
2025-05-22 14:40:44.810 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test8875718148752879513/instance-id-3
2025-05-22 14:40:44.810 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 14:40:44.813 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 14:40:44.813 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 14:40:44.814 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 4
2025-05-22 14:40:44.811 [pool-1-thread-5] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test8875718148752879513/instance-id-4, 描述: 并发测试实例 4 的ID文件
2025-05-22 14:40:44.815 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test8875718148752879513/instance-id-4
2025-05-22 14:40:44.815 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 14:40:44.815 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test8875718148752879513/instance-id-1
2025-05-22 14:40:44.816 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-2
2025-05-22 14:40:44.816 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-2
2025-05-22 14:40:44.816 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-2
2025-05-22 14:40:44.816 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 4 分配工作机器ID，当前线程: pool-1-thread-2
2025-05-22 14:40:44.816 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 4 是否已分配工作机器ID
2025-05-22 14:40:44.816 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 14:40:44.816 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 14:40:44.817 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 14:40:44.817 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 14:40:44.818 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 14:40:44.818 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 14:40:44.819 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 5
2025-05-22 14:40:44.820 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test8875718148752879513/instance-id-3
2025-05-22 14:40:44.820 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-4
2025-05-22 14:40:44.820 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-4
2025-05-22 14:40:44.820 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-4
2025-05-22 14:40:44.820 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 5 分配工作机器ID，当前线程: pool-1-thread-4
2025-05-22 14:40:44.820 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 5 是否已分配工作机器ID
2025-05-22 14:40:44.821 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-4
2025-05-22 14:40:44.822 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-2
2025-05-22 14:40:44.822 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 6
2025-05-22 14:40:44.823 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test8875718148752879513/instance-id-2
2025-05-22 14:40:44.823 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 14:40:44.823 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 14:40:44.824 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-3
2025-05-22 14:40:44.824 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-3
2025-05-22 14:40:44.824 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-3
2025-05-22 14:40:44.824 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 6 分配工作机器ID，当前线程: pool-1-thread-3
2025-05-22 14:40:44.824 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 6 是否已分配工作机器ID
2025-05-22 14:40:44.824 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 7
2025-05-22 14:40:44.825 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test8875718148752879513/instance-id-4
2025-05-22 14:40:44.827 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 5
2025-05-22 14:40:44.828 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 5 分配了新的工作机器ID: 0，当前线程: pool-1-thread-4
2025-05-22 14:40:44.828 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 14:40:44.828 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-4
2025-05-22 14:40:44.828 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: pool-1-thread-4
2025-05-22 14:40:44.828 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 5，schemaName: infra_uid，当前线程: pool-1-thread-4
2025-05-22 14:40:44.829 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-5
2025-05-22 14:40:44.829 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-5
2025-05-22 14:40:44.829 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-5
2025-05-22 14:40:44.829 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 7 分配工作机器ID，当前线程: pool-1-thread-5
2025-05-22 14:40:44.829 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 7 是否已分配工作机器ID
2025-05-22 14:40:44.829 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 8
2025-05-22 14:40:44.830 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test8875718148752879513/instance-id-0
2025-05-22 14:40:44.831 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-3
2025-05-22 14:40:44.831 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-1
2025-05-22 14:40:44.831 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-1
2025-05-22 14:40:44.832 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-1
2025-05-22 14:40:44.832 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 8 分配工作机器ID，当前线程: pool-1-thread-1
2025-05-22 14:40:44.832 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 8 是否已分配工作机器ID
2025-05-22 14:40:44.835 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 1，实例ID: 6
2025-05-22 14:40:44.835 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 6 分配了新的工作机器ID: 1，当前线程: pool-1-thread-3
2025-05-22 14:40:44.836 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 1
2025-05-22 14:40:44.836 [pool-1-thread-2] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 插入新的工作机器ID失败，可能存在并发冲突，工作机器ID: 1
2025-05-22 14:40:44.836 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-3
2025-05-22 14:40:44.836 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 1，当前线程: pool-1-thread-3
2025-05-22 14:40:44.836 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 6，schemaName: infra_uid，当前线程: pool-1-thread-3
2025-05-22 14:40:44.836 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: pool-1-thread-4
2025-05-22 14:40:44.837 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-5
2025-05-22 14:40:44.838 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-1
2025-05-22 14:40:44.839 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 当前状态: ACTIVE，当前线程: pool-1-thread-3
2025-05-22 14:40:44.839 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [1, 6]，当前线程: pool-1-thread-3
2025-05-22 14:40:44.840 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-3
2025-05-22 14:40:44.841 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 租约续约成功，当前线程: pool-1-thread-3
2025-05-22 14:40:44.844 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 2，实例ID: 7
2025-05-22 14:40:44.844 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 7 分配了新的工作机器ID: 2，当前线程: pool-1-thread-5
2025-05-22 14:40:44.844 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 2
2025-05-22 14:40:44.844 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-5
2025-05-22 14:40:44.844 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 2，当前线程: pool-1-thread-5
2025-05-22 14:40:44.844 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 7，schemaName: infra_uid，当前线程: pool-1-thread-5
2025-05-22 14:40:44.847 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 3，实例ID: 8
2025-05-22 14:40:44.848 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 当前状态: ACTIVE，实例ID: 6
2025-05-22 14:40:44.849 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 8 分配了新的工作机器ID: 3，当前线程: pool-1-thread-1
2025-05-22 14:40:44.849 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 3
2025-05-22 14:40:44.849 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-1
2025-05-22 14:40:44.849 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 3，当前线程: pool-1-thread-1
2025-05-22 14:40:44.849 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 8，schemaName: infra_uid，当前线程: pool-1-thread-1
2025-05-22 14:40:44.849 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 5]，当前线程: pool-1-thread-4
2025-05-22 14:40:44.849 [pool-1-thread-2] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 无法为实例 4 分配工作机器ID，当前线程: pool-1-thread-2
2025-05-22 14:40:44.849 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-2
2025-05-22 14:40:44.849 [pool-1-thread-2] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取工作机器ID时发生异常，当前线程: pool-1-thread-2, 异常: 无法为实例 4 分配工作机器ID
java.lang.IllegalStateException: 无法为实例 4 分配工作机器ID
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.getWorkerId(PersistentInstanceWorkerIdAssigner.java:161)
	at org.xkong.cloud.commons.uid.integration.WorkerIdAssignmentIntegrationTest.lambda$testConcurrentLeaseManagement$0(WorkerIdAssignmentIntegrationTest.java:317)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-22 14:40:44.849 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-4
2025-05-22 14:40:44.850 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 当前状态: ACTIVE，当前线程: pool-1-thread-5
2025-05-22 14:40:44.850 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [2, 7]，当前线程: pool-1-thread-5
2025-05-22 14:40:44.851 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: pool-1-thread-4
2025-05-22 14:40:44.851 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-5
2025-05-22 14:40:44.852 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 租约续约成功，当前线程: pool-1-thread-5
2025-05-22 14:40:44.853 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 释放成功
2025-05-22 14:40:44.853 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 当前状态: ACTIVE，当前线程: pool-1-thread-1
2025-05-22 14:40:44.853 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-22 14:40:44.853 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [3, 8]，当前线程: pool-1-thread-1
2025-05-22 14:40:44.854 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-1
2025-05-22 14:40:44.854 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 释放成功
2025-05-22 14:40:44.855 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 租约续约成功，当前线程: pool-1-thread-1
2025-05-22 14:40:44.856 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 释放成功
2025-05-22 14:40:44.859 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 开始清理临时文件，当前注册的临时文件数量: 0
java.lang.IllegalStateException: 获取工作机器ID时发生异常: 无法为实例 4 分配工作机器ID
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.getWorkerId(PersistentInstanceWorkerIdAssigner.java:189)
	at org.xkong.cloud.commons.uid.integration.WorkerIdAssignmentIntegrationTest.lambda$testConcurrentLeaseManagement$0(WorkerIdAssignmentIntegrationTest.java:317)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.IllegalStateException: 无法为实例 4 分配工作机器ID
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.getWorkerId(PersistentInstanceWorkerIdAssigner.java:161)
	... 6 more
2025-05-22 14:40:46.887 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 14:40:47.213 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.214 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.214 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.214 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.214 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.214 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.214 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.214 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.269 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.269 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.269 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.269 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.270 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.270 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.270 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.270 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.299 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.304 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.324 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.325 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.325 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.325 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.325 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.325 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.325 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.326 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.338 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.338 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.339 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.339 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.339 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.339 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.339 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.342 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.367 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.368 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.369 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.369 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.369 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.369 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.369 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:40:47.370 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.412 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 64e86704739c19a741738268e15c3b2f8db4fafe84f72e23cb96962aab62a7eb
2025-05-22 14:40:47.716 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.716 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:47.716 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:47.716 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.716 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.716 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.716 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.718 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.718 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:47.718 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:47.718 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.718 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:47.770 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:47.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:47.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.770 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.778 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.778 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:47.778 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.779 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-22 14:40:47.779 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:47.779 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.779 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:47.826 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.826 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:47.826 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:47.826 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.826 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.826 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.826 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.827 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.827 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:47.827 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:47.827 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.827 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:47.843 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.843 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:47.843 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:47.843 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.843 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.843 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.843 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.844 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.844 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:47.844 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:47.844 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.844 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:47.870 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.871 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:40:47.871 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:40:47.871 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.871 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.871 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.871 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.872 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:40:47.872 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:40:47.872 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:40:47.872 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:40:47.872 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:40:52.740 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.853425654S
2025-05-22 14:40:52.741 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 14:40:52.742 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-9 - Starting...
2025-05-22 14:40:52.763 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-9 - Added connection org.postgresql.jdbc.PgConnection@42b4df5a
2025-05-22 14:40:52.763 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-9 - Start completed.
2025-05-22 14:40:52.763 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 14:40:52.764 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 14:40:52.766 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 不存在，正在自动创建
2025-05-22 14:40:52.767 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 已成功创建
2025-05-22 14:40:52.767 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 14:40:52.770 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-22 14:40:52.782 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 14:40:52.782 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-22 14:40:52.784 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-22 14:40:52.792 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 14:40:52.792 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-22 14:40:52.794 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 14:40:52.801 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 14:40:52.801 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 14:40:52.801 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 14:40:52.801 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: key-test, 环境: test, Schema: infra_uid
2025-05-22 14:40:52.802 [main] DEBUG o.x.c.c.u.c.KeyManagementService - 使用NativePRNGNonBlocking算法的SecureRandom
2025-05-22 14:40:52.803 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已为应用 key-test 环境 test 创建新的 test-key-type 类型密钥

Test run finished after 107179 ms
[         5 containers found      ]
[         0 containers skipped    ]
[         5 containers started    ]
[         0 containers aborted    ]
[         5 containers successful ]
[         0 containers failed     ]
[         9 tests found           ]
[         0 tests skipped         ]
[         9 tests started         ]
[         0 tests aborted         ]
[         4 tests successful      ]
[         5 tests failed          ]


===== 运行简化测试 =====

1. 基本功能测试
2025-05-22 14:40:54.730 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 14:40:55.340 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: e8be8fd85644d163d4d68d6c87edd9785a701cdb779d41bed55b745b78485911
2025-05-22 14:41:01.035 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT6.30483655S
2025-05-22 14:41:01.035 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 14:41:01.035 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-10 - Starting...
2025-05-22 14:41:01.055 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-10 - Added connection org.postgresql.jdbc.PgConnection@269c7104
2025-05-22 14:41:01.055 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-10 - Start completed.
2025-05-22 14:41:01.055 [main] INFO  o.x.c.c.u.test.PostgresTestContainer - 正在执行数据库初始化脚本: init-schema.sql
2025-05-22 14:41:01.126 [main] INFO  o.x.c.c.u.test.PostgresTestContainer - 数据库初始化脚本执行完成
2025-05-22 14:41:01.127 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-11 - Starting...
2025-05-22 14:41:01.162 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-11 - Added connection org.postgresql.jdbc.PgConnection@22e813fc
2025-05-22 14:41:01.162 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-11 - Start completed.
2025-05-22 14:41:01.162 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 14:41:01.177 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 14:41:01.182 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 14:41:01.182 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 14:41:01.187 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 14:41:01.190 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 14:41:01.198 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 14:41:01.198 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 14:41:01.199 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 14:41:01.201 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 14:41:01.204 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 14:41:01.204 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 14:41:01.205 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 14:41:01.213 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 14:41:01.213 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 14:41:01.213 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 14:41:01.247 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 1
2025-05-22 14:41:01.251 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 1 在数据库中不存在
2025-05-22 14:41:01.251 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 14:41:01.260 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 14:41:01.260 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 14:41:01.262 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 1
2025-05-22 14:41:01.275 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/test-instance-id.dat
实例ID: 1
2025-05-22 14:41:01.279 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 14:41:01.279 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 14:41:01.279 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 14:41:01.279 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 1 分配工作机器ID，当前线程: main
2025-05-22 14:41:01.279 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 1 是否已分配工作机器ID
2025-05-22 14:41:01.289 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 14:41:01.296 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 0，实例ID: 1
2025-05-22 14:41:01.297 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配了新的工作机器ID: 0，当前线程: main
2025-05-22 14:41:01.297 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 14:41:01.297 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
工作机器ID: 0
2025-05-22 14:41:01.299 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功

2. 实例恢复测试
2025-05-22 14:41:01.307 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-12 - Starting...
2025-05-22 14:41:01.350 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-12 - Added connection org.postgresql.jdbc.PgConnection@7283877
2025-05-22 14:41:01.350 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-12 - Start completed.
2025-05-22 14:41:01.350 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 14:41:01.351 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 14:41:01.354 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 14:41:01.354 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 14:41:01.357 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 14:41:01.359 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 14:41:01.366 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 14:41:01.367 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 14:41:01.368 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 14:41:01.370 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 14:41:01.374 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 14:41:01.374 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 14:41:01.377 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 14:41:01.378 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 14:41:01.382 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 14:41:01.382 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 14:41:01.383 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
===== 第一次运行 =====
2025-05-22 14:41:01.384 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 2
2025-05-22 14:41:01.386 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 2 在数据库中不存在
2025-05-22 14:41:01.386 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 14:41:01.390 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 14:41:01.390 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 14:41:01.392 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 2
2025-05-22 14:41:01.396 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
第一次运行 - 实例ID: 2

===== 第二次运行 (从文件恢复) =====
2025-05-22 14:41:01.398 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 2
2025-05-22 14:41:01.399 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 2 在数据库中存在，更新实例信息
2025-05-22 14:41:01.400 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 2 的最后活动时间
第二次运行 - 实例ID: 2
从文件恢复结果: 成功

===== 删除文件后运行 (从特征码恢复) =====
2025-05-22 14:41:01.401 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
2025-05-22 14:41:01.401 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 14:41:01.402 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 通过指纹哈希精确匹配找到实例ID: 2
2025-05-22 14:41:01.402 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 成功恢复实例ID: 2
2025-05-22 14:41:01.403 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 2 的最后活动时间
2025-05-22 14:41:01.407 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
删除文件后运行 - 实例ID: 2
从特征码恢复结果: 成功

3. 租约管理测试
2025-05-22 14:41:01.410 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - ===== 租约管理测试开始 =====
2025-05-22 14:41:01.410 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1. 准备测试环境 - 初始化数据源
2025-05-22 14:41:01.410 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.1 检查Docker是否正常运行
2025-05-22 14:41:07.213 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.213 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.214 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.214 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.214 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.214 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.214 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.214 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.268 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.268 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.268 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.269 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.287 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.301 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.318 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.319 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.319 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.319 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.319 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.319 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.319 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.320 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.338 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.338 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.338 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.339 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.339 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.339 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.339 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.339 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.368 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.368 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.369 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.369 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.369 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.369 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.369 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 14:41:07.369 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.715 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.715 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:41:07.715 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:41:07.715 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.715 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.715 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.715 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.717 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.717 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:41:07.717 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:41:07.717 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.717 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:41:07.770 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:41:07.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:41:07.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.770 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.770 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.771 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.771 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:41:07.771 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.772 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-22 14:41:07.772 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:41:07.772 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.772 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:41:07.820 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.821 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:41:07.821 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:41:07.821 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.821 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.821 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.821 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.821 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.821 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:41:07.822 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:41:07.822 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.822 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:41:07.843 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.843 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:41:07.843 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:41:07.843 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.843 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.843 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.843 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.844 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.844 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:41:07.845 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:41:07.845 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.845 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:41:07.875 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.875 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 14:41:07.875 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 14:41:07.875 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.875 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.876 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.876 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.877 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 14:41:07.877 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 14:41:07.877 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 14:41:07.877 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 14:41:07.877 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 14:41:14.611 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - Docker正常运行
2025-05-22 14:41:14.611 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.2 创建PostgresTestContainer实例
2025-05-22 14:41:14.611 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - PostgresTestContainer实例创建成功
2025-05-22 14:41:14.611 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.3 检查容器是否正在运行
2025-05-22 14:41:14.615 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 容器运行状态: true
2025-05-22 14:41:14.615 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.4 获取JDBC连接信息
2025-05-22 14:41:14.615 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - JDBC URL: ******************************************************
2025-05-22 14:41:14.615 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 用户名: test
2025-05-22 14:41:14.615 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 密码: test
2025-05-22 14:41:14.615 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.5 获取数据源
2025-05-22 14:41:14.616 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-13 - Starting...
2025-05-22 14:41:14.632 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-13 - Added connection org.postgresql.jdbc.PgConnection@e7b0bec
2025-05-22 14:41:14.632 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-13 - Start completed.
2025-05-22 14:41:14.632 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据源获取成功
2025-05-22 14:41:14.632 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.6 测试数据库连接
2025-05-22 14:41:14.632 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库连接成功
2025-05-22 14:41:14.639 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库产品名称: PostgreSQL
2025-05-22 14:41:14.640 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库产品版本: 17.4 (Debian 17.4-1.pgdg120+2)
2025-05-22 14:41:14.640 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库驱动名称: PostgreSQL JDBC Driver
2025-05-22 14:41:14.640 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库驱动版本: 42.7.5
2025-05-22 14:41:14.640 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.7 创建JdbcTemplate和TransactionTemplate
2025-05-22 14:41:14.640 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - JdbcTemplate和TransactionTemplate创建成功
2025-05-22 14:41:14.640 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.8 初始化表结构
2025-05-22 14:41:14.640 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 14:41:14.641 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 14:41:14.643 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 14:41:14.644 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 14:41:14.646 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 14:41:14.648 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 14:41:14.657 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 14:41:14.657 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 14:41:14.658 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 14:41:14.659 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 14:41:14.662 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 14:41:14.663 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 14:41:14.664 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 14:41:14.665 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 14:41:14.668 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 14:41:14.668 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 14:41:14.668 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 14:41:14.668 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.2 验证表结构是否创建成功
2025-05-22 14:41:14.670 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 已创建的表: [{table_name=instance_registry}, {table_name=worker_id_assignment}, {table_name=encryption_key}]
2025-05-22 14:41:14.670 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 2. 创建 KeyManagementService
2025-05-22 14:41:14.670 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: lease-test, 环境: test, Schema: infra_uid
2025-05-22 14:41:14.670 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - KeyManagementService 创建成功，加密状态: false
2025-05-22 14:41:14.670 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 3. 创建实例管理器
2025-05-22 14:41:14.672 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 3
2025-05-22 14:41:14.673 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 3 在数据库中不存在
2025-05-22 14:41:14.673 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 14:41:14.674 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到精确匹配，且恢复策略为创建新实例，跳过模糊匹配
2025-05-22 14:41:14.674 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 14:41:14.676 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 3
2025-05-22 14:41:14.677 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/lease-test-instance-id.dat
2025-05-22 14:41:14.678 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 2.1 实例管理器创建成功，实例ID: 3
2025-05-22 14:41:14.682 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 已注册的实例: [{instance_unique_id=3, application_name=lease-test, environment=test, instance_group=default, status=ACTIVE, first_registered_at=2025-05-22 14:41:14.675474, last_seen_at=2025-05-22 14:41:14.675474, custom_metadata={"os_arch": "amd64", "os_name": "Linux", "hostname": "long-VirtualBox", "os_version": "5.4.0-91-generic", "mac_addresses": ["7E:21:C9:1A:E8:3D", "08:00:27:D6:3A:87"], "fingerprint_hash": "2c974415142f2dfb2c262abd914df33384b81aa504f53b02d0658bc18c767b85"}}]
2025-05-22 14:41:14.683 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 3. 创建工作机器ID分配器
2025-05-22 14:41:14.683 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 4. 开始分配工作机器ID
2025-05-22 14:41:14.683 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 14:41:14.683 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 14:41:14.683 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 14:41:14.683 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 3 分配工作机器ID，当前线程: main
2025-05-22 14:41:14.683 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 3 是否已分配工作机器ID
2025-05-22 14:41:14.684 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 14:41:14.686 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 3
2025-05-22 14:41:14.686 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 3 分配了新的工作机器ID: 0，当前线程: main
2025-05-22 14:41:14.686 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 14:41:14.686 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 14:41:14.686 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 分配的工作机器ID: 0
2025-05-22 14:41:14.688 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 工作机器ID分配记录: [{worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:14.685028, lease_expires_at=2025-05-22 14:42:14.685028, released_at=2025-05-22 14:41:01.298322}]
2025-05-22 14:41:14.688 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5. 测试租约续约 - 通过心跳线程自动续约
2025-05-22 14:41:14.688 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.1 等待心跳线程自动续约前，当前状态
2025-05-22 14:41:14.690 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:14.685028, lease_expires_at=2025-05-22 14:42:14.685028, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:14.690 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-22 14:41:15.693 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.1 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:14.685028, lease_expires_at=2025-05-22 14:42:14.685028, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:15.693 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.2 等待心跳线程自动续约前，当前状态
2025-05-22 14:41:15.694 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:14.685028, lease_expires_at=2025-05-22 14:42:14.685028, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:15.694 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-22 14:41:16.697 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.2 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:14.685028, lease_expires_at=2025-05-22 14:42:14.685028, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:16.698 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.3 等待心跳线程自动续约前，当前状态
2025-05-22 14:41:16.701 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:14.685028, lease_expires_at=2025-05-22 14:42:14.685028, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:16.701 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-22 14:41:17.703 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.3 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:14.685028, lease_expires_at=2025-05-22 14:42:14.685028, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:17.703 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6. 测试手动续约
2025-05-22 14:41:17.703 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.1 手动续约前，当前状态
2025-05-22 14:41:17.705 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:14.685028, lease_expires_at=2025-05-22 14:42:14.685028, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:17.705 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-22 14:41:17.705 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-22 14:41:17.705 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-22 14:41:17.707 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-22 14:41:17.707 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-22 14:41:17.708 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-22 14:41:17.709 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-22 14:41:17.710 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.1 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:17.707911, lease_expires_at=2025-05-22 14:42:17.707911, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:17.711 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-22 14:41:18.712 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.2 手动续约前，当前状态
2025-05-22 14:41:18.714 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:17.707911, lease_expires_at=2025-05-22 14:42:17.707911, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:18.714 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-22 14:41:18.714 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-22 14:41:18.714 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-22 14:41:18.715 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-22 14:41:18.715 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-22 14:41:18.716 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-22 14:41:18.717 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-22 14:41:18.717 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.2 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:18.716142, lease_expires_at=2025-05-22 14:42:18.716142, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:18.717 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-22 14:41:19.718 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.3 手动续约前，当前状态
2025-05-22 14:41:19.728 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:18.716142, lease_expires_at=2025-05-22 14:42:18.716142, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:19.728 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-22 14:41:19.728 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-22 14:41:19.728 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-22 14:41:19.731 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-22 14:41:19.731 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-22 14:41:19.732 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-22 14:41:19.732 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-22 14:41:19.734 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.3 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:19.731852, lease_expires_at=2025-05-22 14:42:19.731852, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:19.734 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-22 14:41:20.737 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7. 测试租约失效后的重新分配
2025-05-22 14:41:20.737 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.1 模拟租约失效 - 将工作机器ID状态设置为AVAILABLE
2025-05-22 14:41:20.739 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 更新结果: 影响行数 = 1
2025-05-22 14:41:20.739 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 更新后状态: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:19.731852, lease_expires_at=2025-05-22 14:42:19.731852, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:20.739 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.2 等待2秒，让租约续约线程发现租约失效
2025-05-22 14:41:22.743 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.3 尝试重新获取工作机器ID
2025-05-22 14:41:22.743 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 直接返回已缓存的工作机器ID: 0
2025-05-22 14:41:22.743 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 重新分配的工作机器ID: 0
2025-05-22 14:41:22.744 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 新工作机器ID记录: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:19.731852, lease_expires_at=2025-05-22 14:42:19.731852, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:22.745 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 8. 关闭心跳线程
2025-05-22 14:41:22.747 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放失败
2025-05-22 14:41:22.748 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 关闭后状态: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-22 14:41:14.685028, last_renewed_at=2025-05-22 14:41:19.731852, lease_expires_at=2025-05-22 14:42:19.731852, released_at=2025-05-22 14:41:01.298322}
2025-05-22 14:41:22.748 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - ===== 租约管理测试完成 =====
2025-05-22 14:41:25.098 [Thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 14:41:25.098 [Thread-7] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 14:41:25.098 [Thread-12] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 14:41:25.099 [Thread-15] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 14:41:25.099 [Thread-14] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 14:41:25.099 [Thread-17] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 14:41:25.104 [Thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 14:41:25.105 [Thread-0] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 14:41:25.105 [Thread-0] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 14:41:25.107 [Thread-16] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 14:41:25.107 [Thread-6] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 14:41:25.107 [Thread-6] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 14:41:25.107 [Thread-8] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 14:41:25.107 [Thread-8] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 14:41:25.108 [Thread-5] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 14:41:25.108 [Thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 14:41:25.109 [Thread-13] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 14:41:25.109 [Thread-1] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 14:41:25.109 [Thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 14:41:25.110 [Thread-20] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-13 - Failed to validate connection org.postgresql.jdbc.PgConnection@e7b0bec (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-22 14:41:25.114 [temp-file-cleanup-hook] DEBUG o.x.c.commons.uid.util.TempFileUtils - 执行临时文件清理关闭钩子
2025-05-22 14:41:25.114 [temp-file-cleanup-hook] DEBUG o.x.c.commons.uid.util.TempFileUtils - 开始清理临时文件，当前注册的临时文件数量: 0
2025-05-22 14:41:25.114 [Thread-19] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 14:41:25.115 [Thread-11] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 14:41:25.116 [Thread-4] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 14:41:25.117 [Thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 14:41:25.118 [Thread-20] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-13 - Failed to validate connection org.postgresql.jdbc.PgConnection@69a65cdb (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-22 14:41:55.125 [Thread-20] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放失败: Failed to obtain JDBC Connection
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:653)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:972)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1016)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1026)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.releaseWorkerId(PersistentInstanceWorkerIdAssigner.java:525)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.lambda$initialize$1(PersistentInstanceWorkerIdAssigner.java:85)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.sql.SQLTransientConnectionException: HikariPool-13 - Connection is not available, request timed out after 30007ms (total=0, active=0, idle=0, waiting=0)
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:686)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:179)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:144)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:99)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 7 common frames omitted
Caused by: org.postgresql.util.PSQLException: Connection to localhost:32773 refused. Check that the hostname and port are correct and that the postmaster is accepting TCP/IP connections.
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:352)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273)
	at org.postgresql.Driver.makeConnection(Driver.java:446)
	at org.postgresql.Driver.connect(Driver.java:298)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:724)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:703)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	... 1 common frames omitted
Caused by: java.net.ConnectException: 拒绝连接
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260)
	at org.postgresql.core.PGStream.<init>(PGStream.java:121)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268)
	... 14 common frames omitted
2025-05-22 14:41:55.126 [Thread-20] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 0

进程已结束，退出代码为 0
