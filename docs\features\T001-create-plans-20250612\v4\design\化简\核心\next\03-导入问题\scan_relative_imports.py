#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相对导入扫描脚本
Relative Import Scanner Script

系统性扫描tools/ace/src目录下所有Python文件中的相对导入语句
识别需要修复的文件和具体的导入语句

作者：AI架构修复团队
日期：2025-01-18
版本：v1.0
"""

import os
import re
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

class RelativeImportScanner:
    """相对导入扫描器"""
    
    def __init__(self, base_dir: str = "tools/ace/src"):
        self.base_dir = base_dir
        self.scan_results = []
        self.statistics = {
            'total_files_scanned': 0,
            'files_with_relative_imports': 0,
            'total_relative_imports': 0,
            'scan_time': None
        }
        
        # 相对导入模式
        self.relative_import_patterns = [
            r'from\s+\.([a-zA-Z_][a-zA-Z0-9_]*)\s+import\s+(.+)',      # from .module import ...
            r'from\s+\.\.([a-zA-Z_][a-zA-Z0-9_]*)\s+import\s+(.+)',    # from ..module import ...
            r'from\s+\.\.\.\s*([a-zA-Z_][a-zA-Z0-9_]*)\s+import\s+(.+)', # from ...module import ...
            r'from\s+\.\s+import\s+(.+)',                               # from . import ...
            r'from\s+\.\.\s+import\s+(.+)',                             # from .. import ...
            r'import\s+\.([a-zA-Z_][a-zA-Z0-9_]*)',                     # import .module
        ]
    
    def scan_file(self, file_path: str) -> List[Dict[str, Any]]:
        """扫描单个文件的相对导入"""
        relative_imports = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                line_stripped = line.strip()
                
                # 跳过注释行
                if line_stripped.startswith('#'):
                    continue
                
                # 检查每个相对导入模式
                for pattern in self.relative_import_patterns:
                    match = re.search(pattern, line)
                    if match:
                        relative_imports.append({
                            'line_number': line_num,
                            'original_line': line.strip(),
                            'pattern_matched': pattern,
                            'match_groups': match.groups()
                        })
                        
        except Exception as e:
            print(f"❌ 读取文件失败: {file_path}, 错误: {e}")
        
        return relative_imports
    
    def scan_directory(self) -> Dict[str, Any]:
        """扫描整个目录"""
        print(f"🔍 开始扫描目录: {self.base_dir}")
        start_time = datetime.now()
        
        if not os.path.exists(self.base_dir):
            print(f"❌ 目录不存在: {self.base_dir}")
            return {}
        
        # 遍历所有Python文件
        for root, dirs, files in os.walk(self.base_dir):
            # 跳过__pycache__目录
            dirs[:] = [d for d in dirs if d != '__pycache__']
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    self.statistics['total_files_scanned'] += 1
                    
                    # 扫描文件
                    relative_imports = self.scan_file(file_path)
                    
                    if relative_imports:
                        self.statistics['files_with_relative_imports'] += 1
                        self.statistics['total_relative_imports'] += len(relative_imports)
                        
                        # 计算相对路径
                        rel_path = os.path.relpath(file_path, self.base_dir)
                        
                        self.scan_results.append({
                            'file_path': file_path,
                            'relative_path': rel_path,
                            'package_path': self._get_package_path(rel_path),
                            'relative_imports': relative_imports
                        })
        
        end_time = datetime.now()
        self.statistics['scan_time'] = (end_time - start_time).total_seconds()
        
        print(f"✅ 扫描完成，耗时: {self.statistics['scan_time']:.2f}秒")
        return self._generate_report()
    
    def _get_package_path(self, rel_path: str) -> str:
        """获取包路径"""
        path_parts = rel_path.replace('\\', '/').split('/')
        if path_parts[-1].endswith('.py'):
            path_parts = path_parts[:-1]  # 去掉文件名
        return '.'.join(path_parts) if path_parts else ''
    
    def _generate_report(self) -> Dict[str, Any]:
        """生成扫描报告"""
        report = {
            'scan_metadata': {
                'scan_time': datetime.now().isoformat(),
                'base_directory': self.base_dir,
                'scanner_version': 'v1.0'
            },
            'statistics': self.statistics,
            'scan_results': self.scan_results,
            'summary': self._generate_summary()
        }
        return report
    
    def _generate_summary(self) -> Dict[str, Any]:
        """生成扫描摘要"""
        # 按包分组统计
        package_stats = {}
        priority_files = []
        
        for result in self.scan_results:
            package = result['package_path'] or 'root'
            if package not in package_stats:
                package_stats[package] = {
                    'file_count': 0,
                    'import_count': 0,
                    'files': []
                }
            
            package_stats[package]['file_count'] += 1
            package_stats[package]['import_count'] += len(result['relative_imports'])
            package_stats[package]['files'].append(result['relative_path'])
            
            # 标记高优先级文件（__init__.py）
            if result['relative_path'].endswith('__init__.py'):
                priority_files.append(result['relative_path'])
        
        return {
            'package_statistics': package_stats,
            'high_priority_files': priority_files,
            'most_problematic_packages': sorted(
                package_stats.items(), 
                key=lambda x: x[1]['import_count'], 
                reverse=True
            )[:5]
        }
    
    def save_report(self, output_file: str = None) -> str:
        """保存扫描报告"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"relative_import_scan_report_{timestamp}.json"
        
        report = self._generate_report()
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"📄 扫描报告已保存: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
            return ""
    
    def print_summary(self):
        """打印扫描摘要"""
        print("\n" + "="*60)
        print("📊 相对导入扫描摘要")
        print("="*60)
        
        print(f"📁 扫描目录: {self.base_dir}")
        print(f"📄 总文件数: {self.statistics['total_files_scanned']}")
        print(f"⚠️  问题文件数: {self.statistics['files_with_relative_imports']}")
        print(f"🔍 相对导入总数: {self.statistics['total_relative_imports']}")
        print(f"⏱️  扫描耗时: {self.statistics['scan_time']:.2f}秒")
        
        if self.scan_results:
            print(f"\n🔥 问题最严重的文件:")
            sorted_results = sorted(
                self.scan_results, 
                key=lambda x: len(x['relative_imports']), 
                reverse=True
            )
            
            for i, result in enumerate(sorted_results[:5], 1):
                import_count = len(result['relative_imports'])
                print(f"  {i}. {result['relative_path']} ({import_count}个相对导入)")
        
        print("\n" + "="*60)

def main():
    """主函数"""
    print("🚀 启动相对导入扫描器")
    
    # 创建扫描器实例
    scanner = RelativeImportScanner()
    
    # 执行扫描
    report = scanner.scan_directory()
    
    # 打印摘要
    scanner.print_summary()
    
    # 保存报告
    report_file = scanner.save_report()
    
    # 生成修复建议
    if scanner.scan_results:
        print(f"\n💡 修复建议:")
        print(f"1. 优先修复__init__.py文件（包初始化文件）")
        print(f"2. 使用绝对导入替换相对导入")
        print(f"3. 参考修复指南文档进行系统性修复")
        print(f"4. 修复后运行验证脚本确认效果")
    else:
        print(f"\n🎉 恭喜！未发现相对导入问题")
    
    return report_file

if __name__ == "__main__":
    main()
