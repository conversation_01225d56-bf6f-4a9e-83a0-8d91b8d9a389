【任务 1：架构设计文档】  
版本：v1.0  
作者：首席架构师 Kimi  
日期：2024-06-05  

1  文档目的  
本文档用于指导将现有单体订单系统演进为可支撑 1 000 万订单/小时峰值的新一代电商订单平台，并满足海外业务、实时推荐、智能定价等新需求。

2  现状与痛点  
• 单体 50 万行代码，耦合严重，发布窗口 4 小时，故障恢复 30–60 分钟。  
• MySQL 主从，单实例写瓶颈，日 100 万订单已接近上限。  
• Redis 单集群，命中率 65%，热点 Key 抖动。  
• 无熔断/限流，大促常因下游支付超时雪崩。  

3  目标  
• 峰值：1 000 万订单/小时 ≈ 2.8 k TPS（持续 11.11 当天 10 小时）。  
• 可用性：≥ 99.99 %（全年宕机 < 52 min）。  
• 一致性：订单支付与库存扣减最终一致，金融级强一致场景采用 SAGA+TCC。  
• 可观测：秒级监控、分钟级告警、小时级容量预测。  

4  架构演进路径  
阶段 0：单体加固（1 个月）  
• 引入网关 + 限流 + 熔断，缓解雪崩。  
阶段 1：垂直拆分（3 个月）  
• 拆出用户、商品、库存、营销、支付五个域服务，保持共享 DB。  
阶段 2：水平拆分 + 事件驱动（6 个月）  
• 订单域独立 DB，采用 CQRS + 事件总线，完成读写分离。  
阶段 3：单元化 + 多活（12 个月）  
• 按买家 ID 分片，三地五中心，异地多活。  

5  三种候选架构方案对比  

| 维度 | 事件驱动（EDA） | CQRS | 六边形（Hexagonal） |
|---|---|---|---|
| 核心思想 | 事件总线解耦，异步化 | 读写模型分离 | 领域为核心，端口/适配器隔离 |
| 优点 | 高吞吐、天然可扩展、支持事件溯源 | 读性能线性扩展、模型清晰 | 业务与技术细节彻底解耦、单测友好 |
| 缺点 | 事件顺序与幂等复杂、调试困难 | 写模型仍可能成瓶颈、最终一致 | 落地成本高、对团队 DDD 能力要求高 |
| 适用场景 | 高并发写、需要审计/回溯 | 读多写少、复杂查询 | 业务规则复杂、长期演进 |
| 选型结论 | **阶段 2 起核心采用 EDA**，CQRS 用于订单查询域，六边形用于订单核心域内部建模。 |

6  技术选型决策  

6.1 数据层  
• 分库分表策略：买家 ID 64 分片 → 64 物理库 × 32 表 = 2 048 表，单表 500 万行上限。  
• NewSQL 备选：TiDB 5.x 兼容 MySQL，支持在线扩容，用于实时报表。  
• 分布式数据库：OceanBase 4.x，金融级强一致，用于支付子域。  
• 选型结论：**订单主库保持分库分表 + TiDB 作为 HTAP 副本**。

6.2 缓存  
• L1 本地 Caffeine：热点 SKU、运费模板。  
• L2 Redis 集群：订单详情缓存，采用 RDB+AOF 混合持久化。  
• L3 异地 CDN：静态商品图。  
• 命中率目标：≥ 90 %，Key 带版本号防并发更新。

6.3 消息队列  
| 维度 | Kafka | RocketMQ | Pulsar |
|---|---|---|---|
| 延迟 | < 5 ms | < 3 ms | < 5 ms |
| 事务消息 | 不支持 | 支持 | 支持 |
| 多租户 | 弱 | 中 | 强 |
| 选型结论 | 日志/埋点 | **订单交易事件** | 实时推荐/广告 |

6.4 服务治理  
• Spring Cloud Alibaba：开发效率高，团队熟悉。  
• Istio：灰度、流量镜像、零信任网络，阶段 3 引入。  
• Dapr：跨语言 sidecar，海外第三方供应商系统接入时采用。  
• 选型结论：**SCA + Istio 双模治理**，Dapr 作为补充。

7  非功能性设计  

7.1 性能  
• 目标拆解：2.8 k TPS → 单实例 500 TPS → 6 副本即可。  
• 异步化：下单 → 发送事件 → 返回“受理中”，99 % 请求 < 100 ms。  
• 批量写：库存扣减合并 10 ms 批次。  

7.2 可用性  
• 同城三可用区，Paxos 同步复制，RPO=0，RTO<30 s。  
• 订单幂等键（用户+幂等号）写入前先在 Redis SETNX 防重放。  
• Chaos 工程：每周注入节点/网络故障，验证熔断阈值。  

7.3 一致性  
• 普通订单：SAGA 补偿（逆向冲正）。  
• 秒杀库存：TCC + 乐观锁（版本号）。  
• 对账：每日离线核对，差异 < 0.01 %。  

7.4 安全  
• 零信任网络，mTLS 双向认证。  
• 敏感字段 AES-256 加密，KMS 托管密钥。  
• 审计日志存对象存储，WORM 30 天。  

8  风险与应对  
• 分库分表后跨片查询 → 采用 ElasticSearch + CDC 方案。  
• 事件乱序 → 使用 Kafka partition key = orderId，单分区保序。  

9  里程碑  
M0（1 个月）：网关+限流上线  
M1（4 个月）：垂直拆分完成，灰度 20 % 流量  
M2（10 个月）：水平拆分+事件驱动，双 11 压测 800 万单/小时  
M3（12 个月）：三地五中心，全量切换  

10  结论  
采用“EDA + CQRS + 分库分表”混合架构，可在 12 个月内平滑支撑 1 000 万订单/小时，并具备持续演进能力。

——————————————————————  
【任务 2：API 设计文档】  
符合 OpenAPI 3.0.3，文件：order-api-v1.yaml（节选）

1  创建订单  
POST /api/v1/orders  
Request  
Headers  
  Idempotency-Key: uuid  
Body  
{
  "buyerId": 123456789,
  "skuItems": [
    {"skuId":"A1001","price":1990,"quantity":2}
  ],
  "couponIds": ["CP_001"],
  "addressId": 987,
  "payMethod": "ALIPAY"
}  
Responses  
201 Created  
{
  "orderId": "2024060512345678",
  "status": "PENDING_PAY",
  "expireTime": "2024-06-05T20:00:00Z"
}  
400 Bad Request  
{
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "skuItems cannot be empty",
    "field": "skuItems"
  }
}  
429 Too Many Requests  
Headers: Retry-After: 5  

幂等：Idempotency-Key + Redis SETNX 5 min TTL。  

2  查询订单详情  
GET /api/v1/orders/{orderId}  
200 OK  
{
  "orderId": "2024060512345678",
  "buyerId": 123456789,
  "skuItems":[...],
  "status":"PAID",
  "payment": {...},
  "logistics": {...}
}  
404 Not Found  

3  更新订单状态  
PUT /api/v1/orders/{orderId}/status  
Body  
{
  "action": "CONFIRM_RECEIPT"
}  
202 Accepted  

4  订单列表  
GET /api/v1/orders?buyerId=123&status=PAID&page=1&size=20&sort=createdTime,desc  
分页：page,size 最大 100；返回  
{
  "data":[...],
  "page":1,
  "size":20,
  "total":145
}  

5  限流与安全  
• 网关层：IP 100 req/min，用户 20 req/min（令牌桶）。  
• OAuth2 + JWT，scope=order:write。  

6  客户端示例（Java）  
RestTemplate rest = new RestTemplate();  
HttpHeaders h = new HttpHeaders();  
h.add("Authorization","Bearer <jwt>");  
h.add("Idempotency-Key", UUID.randomUUID().toString());  
OrderCreateReq req = new OrderCreateReq(...);  
ResponseEntity<OrderCreateResp> resp = rest.exchange(
  "https://api.xxx.com/v1/orders",
  HttpMethod.POST,
  new HttpEntity<>(req,h),
  OrderCreateResp.class);

——————————————————————  
【任务 3：性能分析与优化方案】

1  数据现状  
| 接口 | 平均 | P99 | 目标 | Gap |
|---|---|---|---|---|
| 创建订单 | 800 ms | 2.5 s | 100 ms | 8× |
| 查询订单 | 300 ms | 1.2 s | 50 ms | 6× |

2  根本原因与瓶颈  
a) 创建订单  
• 同步写 MySQL 主库 3 次（订单、库存、优惠券），RT 150 ms × 3 = 450 ms。  
• 分布式锁获取库存 200 ms。  
• 大事务 800 ms 触发锁竞争。  

b) 查询订单  
• 单表 2 000 万行，回表 4 次。  
• Redis 命中率 65%，穿透到 DB 35%。  

c) 数据库连接池  
• Hikari 默认 max 20，峰值 19.8，等待队列 300+。  

d) Redis  
• 单分片 8 GB，内存 85%，淘汰策略 allkeys-lru 导致热点失效。  

e) JVM  
• 堆 8 G，老年代 6 G，每小时 Full GC 3 次，Stop-The-World 200 ms。  

3  优化措施与收益预估  

| 措施 | 说明 | 预期收益 | 优先级 |
|---|---|---|---|
| 异步下单 | 下单→消息队列→异步落库 | 接口 RT 100 ms，TPS 提升 5× | P0 |
| 库存预扣 | Redis 扣减 + 延迟消息回补 | 库存 RT 10 ms | P0 |
| 热点 SKU 分片 | 热点 Key 拆 10 子 Key | 缓存命中率 90 % | P1 |
| 连接池调优 | max 100, idle 30 | 等待队列 0 | P1 |
| 索引优化 | 联合索引 (buyer_id, status, created_time) | 查询 RT 50 ms | P1 |
| JVM G1 调优 | 堆 12 G，MaxGCPauseMillis 100 | Full GC 0.3 次/小时 | P2 |
| 多级缓存 | 本地 Caffeine 1 G | 查询 RT 30 ms | P2 |

4  监控体系  
• KPI：下单接口 QPS、P99 RT、错误率、库存扣减成功率。  
• Prometheus + Grafana：秒级采集，告警阈值 P99>150 ms 持续 2 min。  
• 基线：压测 1 000 万单/小时，P99 RT<100 ms，错误率<0.1 %。  

5  实施计划  
Week 1–2：异步下单、库存预扣  
Week 3：连接池、索引、JVM  
Week 4：热点 SKU、多级缓存  
Week 5：全链路压测，回归验证  

——————————————————————  
【任务 4：安全威胁分析】

1  威胁建模（STRIDE）  

| 威胁 | 示例场景 | 风险等级 | 缓解措施 |
|---|---|---|---|
| Spoofing | 伪造内部服务调用 | 高 | mTLS + SPIFFE ID |
| Tampering | 修改订单金额 | 高 | 参数签名 + 审计日志 |
| Repudiation | 用户否认下单 | 中 | 订单操作链路追踪 |
| Information Disclosure | JWT 泄露获取用户隐私 | 高 | 短有效期+刷新令牌 |
| Denial of Service | 秒杀接口刷流量 | 高 | 网关限流+验证码 |
| Elevation of Privilege | 容器逃逸 | 中 | PodSecurityPolicy+Seccomp |

2  防护体系  
• 网络层：VPC 隔离、SecurityGroup 最小权限、WAF。  
• 传输层：TLS1.3，HSTS，强制 Forward Secrecy。  
• 应用层：OAuth2 + JWT（15 min），刷新令牌 7 天，scope 细粒度。  
• 数据层：AES-256-GCM，KMS 自动轮换密钥，列级加密（信用卡号）。  
• 审计：所有管理操作写审计日志，存对象存储 180 天不可变。  

3  合规映射  
• PCI DSS：信用卡数据加密、定期漏洞扫描、访问控制。  
• GDPR：用户数据可删除、可导出、数据最小化。  
• 中国《个人信息保护法》：境内存储，跨境传输评估。  

——————————————————————  
【任务 5：系统重构计划】

1  现状  
• 代码：50 万行 Java，200+ 类，模块边界模糊。  
• 数据：100 张业务表，最大表 8 亿行。  
• 部署：物理机 20 台，手工脚本。  

2  重构策略  
采用“绞杀者模式”分三阶段：  

阶段 1：基础设施就绪（1 个月）  
• 搭建 Kubernetes、DevOps、监控。  
• 建立 API 网关，老系统流量 100 % 透传。  

阶段 2：服务拆分（4–8 个月）  
• 领域建模工作坊 → 识别限界上下文：订单、库存、支付、营销、用户。  
• 每域采用“数据库先拆分”策略：  
  - 新服务 + 新库 → 双写 → 数据校验 → 切流。  
• 优先级：库存（写压力大）→ 订单 → 支付。  

阶段 3：单元化与多活（8–12 个月）  
• 按买家 ID 分片，异地容灾。  
• 老系统下线，代码删除。  

3  风险控制  
| 风险 | 概率 | 影响 | 缓解 |
|---|---|---|---|
| 数据不一致 | 中 | 高 | 双写校验 + 对账 |
| 性能回退 | 中 | 高 | 金丝雀 5 % → 20 % → 100 % |
| 团队认知不足 | 高 | 中 | 每周培训、DDD 教练 |
| 回滚困难 | 低 | 高 | 蓝绿部署，一键回滚脚本 |

4  实施时间表  

| 月份 | 里程碑 | 资源 |
|---|---|---|
| M1 | K8s 就绪，网关上线 | 4 DevOps |
| M2–M3 | 库存服务拆分 | 6 后端 + 2 DBA |
| M4–M5 | 订单服务拆分 | 8 后端 |
| M6 | 双 11 压测 800 万单/小时 | 全团队 |
| M7–M8 | 支付服务拆分 | 6 后端 |
| M9–M10 | 单元化 & 多活 | 4 架构 + 3 SRE |
| M11 | 老系统下线 | 全团队 |
| M12 | 复盘 & 优化 | 2 架构 |

5  测试策略  
• 单元测试覆盖率 ≥ 80 %。  
• 契约测试：Pact 验证服务间兼容性。  
• 混沌工程：每月注入节点故障。  
• 性能基线：每次迭代回归压测。  

6  沟通与治理  
• 周会同步进度、风险看板。  
• 设立架构决策记录（ADR）仓库。  
• 代码审查强制两人通过。  

—— 以上即为新一代千万级订单系统的完整设计、API、性能、安全与重构方案。 