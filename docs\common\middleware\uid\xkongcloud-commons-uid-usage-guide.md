---
title: XKongCloud Commons UID库使用指南
document_id: C050
document_type: 中间件使用指南
category: 分布式系统
scope: 通用库指南
keywords: [分布式UID, PostgreSQL, 实例身份管理, Worker ID分配, 门面模式, 性能监控]
created_date: 2025-01-15
updated_date: 2025-01-15
status: 生效
version: 1.0
authors: [系统架构组]
library_version: 1.0.0-SNAPSHOT
---

# XKongCloud Commons UID库使用指南

## 概述

XKongCloud Commons UID是一个基于PostgreSQL的分布式唯一ID生成库，支持持久化ID指纹恢复。该库采用门面模式设计，提供了完整的实例身份管理、Worker ID分配、故障恢复和性能监控功能。

## 核心特性

### 1. 分布式UID生成
- **高性能生成**: 基于雪花算法的高性能UID生成
- **可配置位数**: 支持自定义时间位、Worker位、序列位
- **批量生成**: 支持批量获取UID提升性能

### 2. 实例身份管理
- **持久化实例ID**: 实例ID可持久化到本地文件和数据库
- **机器特征码收集**: 收集BIOS UUID、MAC地址、云实例ID等特征码
- **智能恢复策略**: 支持精确匹配和模糊匹配的多级恢复策略

### 3. Worker ID分配
- **租约机制**: 基于租约的Worker ID分配和管理
- **心跳维护**: 自动心跳机制保持Worker ID活跃
- **防拥堵设计**: 智能续约时间分散，避免系统拥堵

### 4. 故障恢复机制
- **故障分类**: 网络、数据库、系统资源、业务逻辑故障分类
- **差异化重试**: 不同故障类型采用不同的重试策略
- **自动恢复**: 支持自动故障恢复和人工干预

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>org.xkong.cloud</groupId>
    <artifactId>xkongcloud-commons-uid</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 基本使用（推荐门面模式）

```java
import org.xkong.cloud.commons.uid.facade.UidGeneratorFacade;
import org.xkong.cloud.commons.uid.facade.UidGeneratorFacadeBuilder;

public class UidExample {

    public void basicUsage() {
        // 使用门面模式创建UID生成器
        try (UidGeneratorFacade facade = new UidGeneratorFacadeBuilder()
            .withDataSource(dataSource)
            .withSchemaName("infra_uid")
            .withApplicationName("my-app")
            .withEnvironment("production")
            .build()) {

            // 生成单个UID
            long uid = facade.getUID();
            System.out.println("生成的UID: " + uid);

            // 批量生成UID
            long[] uids = facade.getUIDBatch(10);
            System.out.println("批量生成UID数量: " + uids.length);

        } // 自动关闭资源
    }
}
```

### 3. Spring Boot集成

```java
@Configuration
public class UidGeneratorConfig {

    @Autowired
    private DataSource dataSource;

    @Bean
    public UidGeneratorFacade uidGeneratorFacade() {
        return UidGeneratorFacadeBuilder.buildForProduction(dataSource, "my-app");
    }

    @Bean
    public UidGenerator uidGenerator(UidGeneratorFacade facade) {
        return new UidGenerator() {
            @Override
            public long getUID() {
                return facade.getUID();
            }

            @Override
            public long[] getUIDList(int size) {
                return facade.getUIDBatch(size);
            }

            @Override
            public String parseUID(long uid) {
                throw new UnsupportedOperationException("parseUID方法未实现");
            }
        };
    }

    // 重要：配置关闭顺序
    @Bean
    @DependsOn("uidGeneratorFacade")
    public UidShutdownOrderBean uidShutdownOrder(UidGeneratorFacade facade) {
        return new UidShutdownOrderBean(facade);
    }

    @Component
    public static class UidShutdownOrderBean implements DisposableBean {
        private final UidGeneratorFacade facade;

        public UidShutdownOrderBean(UidGeneratorFacade facade) {
            this.facade = facade;
        }

        @Override
        @PreDestroy
        public void destroy() {
            facade.close();
        }
    }
}
```

## 完整配置参数

### 1. 数据库配置

```yaml
# 数据库相关配置
database:
  schema: "infra_uid"                    # 数据库Schema名称
  connection-timeout: 30000              # 连接超时时间(毫秒)
  query-timeout: 10000                   # 查询超时时间(毫秒)
```

### 2. 实例管理配置

```yaml
# 实例管理配置
instance:
  application-name: "my-app"             # 应用名称
  environment: "production"              # 环境名称
  instance-group: "default"              # 实例组名称

  # 本地存储配置
  storage:
    path: "${user.home}/.xkong/instance.id"  # 实例ID存储路径
    backup-enabled: true                 # 是否启用备份
    backup-count: 3                      # 备份文件数量

  # 恢复策略配置
  recovery:
    enabled: true                        # 是否启用恢复
    high-confidence-threshold: 150       # 高置信度阈值
    minimum-acceptable-score: 70         # 最小可接受分数
    strategy: "ALERT_AUTO_WITH_TIMEOUT"  # 恢复策略
    timeout-seconds: 300                 # 恢复超时时间

  # 加密配置
  encryption:
    enabled: false                       # 是否启用加密
    key-management-service: null         # 密钥管理服务
```

### 3. Worker ID分配配置

```yaml
# Worker ID分配配置
worker:
  assignment:
    max-worker-id: 262143                # 最大Worker ID
    lease-duration-seconds: 300          # 租约持续时间
    renewal-interval-ratio: 0.33         # 续约间隔比例

  renewal:
    # 重试配置
    retry:
      network:
        max-retries: 3                   # 网络故障最大重试次数
        base-delay-ms: 1000              # 网络故障基础延迟
      database:
        max-retries: 5                   # 数据库故障最大重试次数
        base-delay-ms: 2000              # 数据库故障基础延迟
      resource:
        max-retries: 2                   # 资源故障最大重试次数
        base-delay-ms: 10000             # 资源故障基础延迟

    # 防拥堵配置
    anti-congestion:
      enabled: true                      # 是否启用防拥堵
      random-offset-ratio: 0.2           # 随机偏移比例
      min-interval-seconds: 10           # 最小间隔时间
      max-interval-ratio: 2.0            # 最大间隔比例
```

### 4. UID生成器配置

```yaml
# UID生成器配置
uid-generator:
  epoch-str: "2023-01-01"               # 时间起点
  time-bits: 31                         # 时间位数
  worker-bits: 18                       # Worker位数
  seq-bits: 14                          # 序列位数
  boost-power: 3                        # 性能提升倍数
  padding-factor: 50                    # 填充因子
  schedule-interval: 60                 # 调度间隔(秒)
```

## 性能指标和监控

### 1. 核心性能指标

```yaml
# 性能基准数据
performance-benchmarks:
  uid-generation:
    single-thread: "~100,000 ops/sec"   # 单线程生成性能
    multi-thread: "~500,000 ops/sec"    # 多线程生成性能
    batch-generation: "~1,000,000 ops/sec"  # 批量生成性能

  worker-id-assignment:
    allocation-time: "< 100ms"           # Worker ID分配时间
    renewal-time: "< 50ms"               # 续约时间
    recovery-time: "< 500ms"             # 恢复时间

  resource-usage:
    memory-overhead: "< 50MB"            # 内存开销
    database-connections: "2-5"          # 数据库连接数
    thread-pool-size: "2-4"              # 线程池大小
```

### 2. 监控指标配置

```yaml
# Micrometer监控指标
monitoring:
  metrics:
    # UID生成指标
    uid-generation:
      - name: "uid.generation.count"
        type: "Counter"
        description: "UID生成总数"
      - name: "uid.generation.duration"
        type: "Timer"
        description: "UID生成耗时"

    # Worker ID分配指标
    worker-assignment:
      - name: "worker.assignment.success"
        type: "Counter"
        description: "Worker ID分配成功次数"
      - name: "worker.assignment.failure"
        type: "Counter"
        description: "Worker ID分配失败次数"
      - name: "worker.renewal.success"
        type: "Counter"
        description: "Worker ID续约成功次数"
      - name: "worker.renewal.failure"
        type: "Counter"
        description: "Worker ID续约失败次数"

    # 实例恢复指标
    instance-recovery:
      - name: "instance.recovery.attempt"
        type: "Counter"
        description: "实例恢复尝试次数"
      - name: "instance.recovery.success"
        type: "Counter"
        description: "实例恢复成功次数"
      - name: "instance.match.score"
        type: "DistributionSummary"
        description: "实例匹配分数分布"
```

### 3. 告警规则配置

```yaml
# Prometheus告警规则
alerts:
  - alert: UidGenerationFailureRateHigh
    expr: |
      (
        rate(uid_generation_failure_total[5m]) /
        rate(uid_generation_total[5m])
      ) > 0.01
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "UID生成失败率过高"
      description: "过去5分钟UID生成失败率超过1%"

  - alert: WorkerIdExhaustion
    expr: worker_id_active_count / worker_id_max_count > 0.8
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Worker ID资源即将耗尽"
      description: "Worker ID使用率已达到80%"

  - alert: InstanceRecoveryFailureFrequent
    expr: increase(instance_recovery_failure_total[10m]) > 5
    for: 1m
    labels:
      severity: warning
    annotations:
      summary: "实例恢复频繁失败"
      description: "过去10分钟内有{{ $value }}次实例恢复失败"
```

## 故障处理和维护

### 1. 故障分类和处理策略

```yaml
# 故障分类处理
failure-handling:
  network-failures:
    types: ["ConnectException", "SocketTimeoutException", "UnknownHostException"]
    strategy: "指数退避重试"
    max-retries: 3
    base-delay: "1秒"
    max-delay: "8秒"

  database-failures:
    types: ["SQLTransientException", "SQLTimeoutException", "DataAccessResourceFailureException"]
    strategy: "线性增长重试"
    max-retries: 5
    base-delay: "2秒"
    increment: "线性增长"

  resource-failures:
    types: ["OutOfMemoryError", "RejectedExecutionException"]
    strategy: "固定长延迟重试"
    max-retries: 2
    base-delay: "10秒"

  business-logic-failures:
    types: ["更新行数为0"]
    strategy: "不重试，直接重新分配"
    action: "触发新的Worker ID分配"
```

### 2. 维护操作指南

```sql
-- 数据库维护操作

-- 1. 清理过期的Worker ID分配
DELETE FROM infra_uid.worker_id_assignment
WHERE assignment_status = 'ASSIGNED'
  AND expires_at < NOW() - INTERVAL '1 hour';

-- 2. 查看当前活跃的Worker ID
SELECT worker_id, assigned_instance_unique_id, expires_at, assignment_status
FROM infra_uid.worker_id_assignment
WHERE assignment_status = 'ASSIGNED'
ORDER BY worker_id;

-- 3. 查看实例注册信息
SELECT instance_id, application_name, environment,
       created_at, last_seen_at, fingerprints
FROM infra_uid.instance_registry
ORDER BY last_seen_at DESC;

-- 4. 统计Worker ID使用情况
SELECT
  COUNT(*) as total_workers,
  COUNT(CASE WHEN assignment_status = 'ASSIGNED' THEN 1 END) as assigned_workers,
  COUNT(CASE WHEN assignment_status = 'AVAILABLE' THEN 1 END) as available_workers
FROM infra_uid.worker_id_assignment;
```

### 3. 日常维护检查清单

```yaml
# 日常维护检查清单
daily-maintenance:
  database-checks:
    - "检查数据库连接池状态"
    - "验证表结构完整性"
    - "清理过期的Worker ID分配"
    - "检查实例注册表大小"

  performance-checks:
    - "监控UID生成性能指标"
    - "检查Worker ID分配延迟"
    - "验证实例恢复成功率"
    - "分析故障重试分布"

  resource-checks:
    - "检查内存使用情况"
    - "监控线程池状态"
    - "验证本地存储文件"
    - "检查日志文件大小"

weekly-maintenance:
  deep-analysis:
    - "分析一周内的性能趋势"
    - "检查故障模式和频率"
    - "评估Worker ID使用率"
    - "审查配置参数优化空间"

  cleanup-tasks:
    - "清理历史实例注册记录"
    - "归档过期的监控数据"
    - "更新性能基线数据"
    - "检查加密密钥轮换"
```

## 最佳实践

### 1. 生产环境部署

```yaml
# 生产环境最佳实践
production-best-practices:
  configuration:
    - "使用生产环境专用配置"
    - "启用实例恢复功能"
    - "配置合适的租约时间"
    - "设置监控和告警"

  security:
    - "启用本地文件加密"
    - "使用专用数据库Schema"
    - "限制数据库访问权限"
    - "定期轮换加密密钥"

  performance:
    - "合理配置Worker位数"
    - "优化数据库连接池"
    - "使用批量生成提升性能"
    - "监控系统资源使用"

  reliability:
    - "配置多级故障恢复"
    - "设置合理的重试策略"
    - "实施健康检查机制"
    - "准备故障应急预案"
```

### 2. 性能优化建议

```java
/**
 * 性能优化示例
 */
public class UidPerformanceOptimization {

    // 1. 使用批量生成提升性能
    public List<Long> generateBatchUids(int batchSize) {
        long[] uids = facade.getUIDBatch(batchSize);
        return Arrays.stream(uids).boxed().collect(Collectors.toList());
    }

    // 2. 缓存UID生成器实例
    @Component
    public class UidService {
        private final UidGeneratorFacade facade;

        public UidService(UidGeneratorFacade facade) {
            this.facade = facade;
        }

        public long generateUid() {
            return facade.getUID();
        }
    }

    // 3. 异步处理非关键路径
    @Async
    public CompletableFuture<Void> updateInstanceFingerprints() {
        // 异步更新实例特征码
        return CompletableFuture.completedFuture(null);
    }
}
```

### 3. 错误处理最佳实践

```java
/**
 * 错误处理示例
 */
public class UidErrorHandling {

    private static final Logger log = LoggerFactory.getLogger(UidErrorHandling.class);

    public Long generateUidWithFallback() {
        try {
            return facade.getUID();
        } catch (Exception e) {
            log.error("UID生成失败，使用降级策略", e);

            // 降级策略：使用时间戳 + 随机数
            return System.currentTimeMillis() * 1000 +
                   ThreadLocalRandom.current().nextInt(1000);
        }
    }

    public void handleWorkerIdAssignmentFailure(Exception e) {
        if (e instanceof ConnectException) {
            log.warn("网络连接失败，将在{}秒后重试", calculateRetryDelay());
        } else if (e instanceof SQLTimeoutException) {
            log.warn("数据库超时，检查数据库性能");
        } else {
            log.error("Worker ID分配失败", e);
        }
    }
}
```

## 高级配置

### 1. 自定义配置构建器

```java
/**
 * 自定义配置示例
 */
public class CustomUidConfiguration {

    public UidGeneratorFacade createCustomFacade(DataSource dataSource) {
        return new UidGeneratorFacadeBuilder()
            // 数据库配置
            .withDataSource(dataSource)
            .withSchemaName("custom_uid")

            // 实例管理配置
            .withApplicationName("custom-app")
            .withEnvironment("production")
            .withInstanceGroup("high-performance")
            .withLocalStoragePath("/opt/app/data/instance.id")

            // 恢复策略配置
            .withRecoveryEnabled(true)
            .withHighConfidenceThreshold(180)  // 更高的置信度要求
            .withMinimumAcceptableScore(100)   // 更严格的最小分数
            .withRecoveryStrategy(PersistentInstanceManager.RECOVERY_STRATEGY_ALERT_AND_MANUAL)
            .withRecoveryTimeoutSeconds(600)   // 更长的恢复超时

            // 加密配置
            .withEncryptionEnabled(true)
            .withKeyManagementService(new CustomKeyManagementService())

            // Worker ID配置
            .withLeaseDurationSeconds(7200)    // 2小时租约

            // UID生成器配置
            .withEpochStr("2024-01-01")        // 自定义时间起点
            .withTimeBits(32)                  // 更多时间位
            .withWorkerBits(16)                // 较少Worker位
            .withSeqBits(15)                   // 更多序列位
            .withBoostPower(4)                 // 更高性能提升
            .withPaddingFactor(75)             // 更大填充因子
            .withScheduleInterval(30L)         // 更频繁的调度
            .build();
    }
}
```

### 2. 监控集成示例

```java
/**
 * 监控集成示例
 */
@Component
public class UidMonitoringIntegration {

    private final MeterRegistry meterRegistry;
    private final UidGeneratorFacade facade;

    public UidMonitoringIntegration(MeterRegistry meterRegistry, UidGeneratorFacade facade) {
        this.meterRegistry = meterRegistry;
        this.facade = facade;

        // 注册自定义监控指标
        registerCustomMetrics();
    }

    private void registerCustomMetrics() {
        // UID生成速率监控
        Timer.builder("uid.generation.timer")
            .description("UID生成耗时监控")
            .register(meterRegistry);

        // Worker ID使用率监控
        Gauge.builder("uid.worker.utilization")
            .description("Worker ID使用率")
            .register(meterRegistry, this, UidMonitoringIntegration::getWorkerIdUtilization);
    }

    @EventListener
    public void handleUidGenerationEvent(UidGenerationEvent event) {
        // 记录UID生成事件
        meterRegistry.counter("uid.generation.count",
                            "result", event.isSuccess() ? "success" : "failure")
                    .increment();
    }

    private double getWorkerIdUtilization() {
        // 计算Worker ID使用率
        return 0.0; // 实际实现需要查询数据库
    }
}
```

## 故障排除指南

### 1. 常见问题和解决方案

```yaml
# 常见问题排除
troubleshooting:
  uid-generation-slow:
    symptoms: ["UID生成耗时超过预期", "性能监控显示延迟增加"]
    causes: ["数据库连接池不足", "Worker ID续约失败", "系统资源不足"]
    solutions:
      - "增加数据库连接池大小"
      - "检查Worker ID分配状态"
      - "监控系统CPU和内存使用"
      - "优化数据库查询性能"

  worker-id-exhaustion:
    symptoms: ["Worker ID分配失败", "告警显示资源耗尽"]
    causes: ["实例数量超过Worker ID容量", "过期Worker ID未及时清理"]
    solutions:
      - "增加Worker位数配置"
      - "清理过期的Worker ID分配"
      - "实施Worker ID回收机制"
      - "优化实例部署策略"

  instance-recovery-failure:
    symptoms: ["实例重启后无法恢复ID", "匹配分数过低"]
    causes: ["机器特征码变化", "置信度阈值过高", "本地文件损坏"]
    solutions:
      - "检查机器特征码收集"
      - "调整置信度阈值配置"
      - "恢复本地存储文件"
      - "使用手动恢复策略"
```

### 2. 诊断工具和命令

```bash
# 诊断脚本示例
#!/bin/bash

# 1. 检查数据库连接
echo "检查数据库连接..."
psql -h localhost -U uid_user -d uid_db -c "SELECT 1;"

# 2. 查看Worker ID分配状态
echo "查看Worker ID分配状态..."
psql -h localhost -U uid_user -d uid_db -c "
SELECT
  assignment_status,
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM infra_uid.worker_id_assignment
GROUP BY assignment_status;"

# 3. 检查实例注册状态
echo "检查实例注册状态..."
psql -h localhost -U uid_user -d uid_db -c "
SELECT
  application_name,
  environment,
  COUNT(*) as instance_count,
  MAX(last_seen_at) as last_activity
FROM infra_uid.instance_registry
GROUP BY application_name, environment;"

# 4. 分析性能指标
echo "分析性能指标..."
curl -s http://localhost:8080/actuator/metrics/uid.generation.timer | jq .

# 5. 检查本地存储文件
echo "检查本地存储文件..."
ls -la ~/.xkong/instance.id*
```

## 版本兼容性

### 1. 版本升级指南

```yaml
# 版本升级指南
version-compatibility:
  current-version: "1.0.0-SNAPSHOT"

  upgrade-path:
    from-0.x:
      breaking-changes:
        - "配置参数结构调整"
        - "数据库表结构变更"
        - "API接口变化"
      migration-steps:
        - "备份现有数据"
        - "执行数据库迁移脚本"
        - "更新配置文件"
        - "测试功能完整性"

    from-1.0.x:
      compatibility: "向后兼容"
      recommended-actions:
        - "更新依赖版本"
        - "检查新增配置项"
        - "验证性能改进"
```

### 2. API兼容性

```java
/**
 * API兼容性说明
 */
public interface UidGeneratorCompatibility {

    // 稳定API - 保证向后兼容
    long getUID();
    long[] getUIDBatch(int size);
    void close();

    // 实验性API - 可能在未来版本中变更
    @Experimental
    CompletableFuture<Long> getUIDAsync();

    // 已废弃API - 将在下个主版本中移除
    @Deprecated
    String parseUID(long uid);
}
```

## 相关文档

- [分布式租约管理模式](../../architecture/patterns/distributed-lease-management-pattern.md)
- [实例身份识别与恢复模式](../../architecture/patterns/instance-identity-recovery-pattern.md)
- [基础设施工具类设计指南](../best-practices/coding-standards/infrastructure-utilities-design-guide.md)
- [分布式系统监控指标设计指南](../best-practices/monitoring/distributed-system-metrics-design-guide.md)
- [PostgreSQL集成最佳实践](../postgresql/postgresql-integration-guide.md)
```
