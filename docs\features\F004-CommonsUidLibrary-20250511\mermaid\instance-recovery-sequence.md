---
title: 实例恢复时序图
description: 展示xkongcloud-commons-uid库基于机器特征码进行实例恢复的时序图
created_date: 2025-05-18
updated_date: 2025-05-18
version: 1.0
status: 草稿
author: AI助手
---

# 实例恢复时序图

此时序图展示了xkongcloud-commons-uid库在本地实例ID丢失时，如何基于机器特征码进行实例身份恢复的过程。

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant PIM as PersistentInstanceManager
    participant MF as MachineFingerprints
    participant DB as 数据库(PostgreSQL)
    participant FS as 文件系统
    
    App->>PIM: 初始化实例管理器
    PIM->>FS: 尝试加载本地实例ID
    
    alt 本地实例ID丢失或损坏
        FS-->>PIM: 返回加载失败
        PIM->>MF: 收集机器特征码
        MF->>MF: 收集BIOS UUID
        MF->>MF: 收集系统序列号
        MF->>MF: 收集MAC地址
        MF->>MF: 收集OS主机名
        MF->>MF: 收集云环境特征码
        MF-->>PIM: 返回特征码集合
        
        PIM->>DB: 查询匹配的实例记录
        DB-->>PIM: 返回匹配结果和置信度分数
        
        alt 找到高置信度匹配(>150分)
            PIM->>PIM: 自动恢复实例身份
            PIM->>DB: 更新实例特征码
        else 找到中等置信度匹配(70-150分)
            alt 恢复策略为AUTO
                PIM->>PIM: 自动恢复实例身份
                PIM->>DB: 更新实例特征码
            else 恢复策略为ALERT_AUTO_WITH_TIMEOUT
                PIM->>PIM: 记录警告日志
                PIM->>PIM: 等待超时时间
                PIM->>PIM: 自动恢复实例身份
                PIM->>DB: 更新实例特征码
            else 恢复策略为ALERT_MANUAL
                PIM->>PIM: 记录警告日志并抛出异常
                Note over PIM: 需要人工干预
            end
        else 未找到匹配或置信度低(<70分)
            PIM->>DB: 注册新实例
            DB-->>PIM: 返回新实例ID
        end
        
        PIM->>FS: 保存实例ID到本地
    end
    
    PIM-->>App: 返回实例ID
```
