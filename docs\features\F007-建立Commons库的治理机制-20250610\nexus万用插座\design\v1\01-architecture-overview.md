# XKongCloud Commons Nexus V1.0: 微内核可扩展架构

## 文档元数据

- **文档ID**: `F007-NEXUS-ARCHITECTURE-DESIGN-001`
- **版本**: `V1.0`
- **创建日期**: `2025-06-12`
- **状态**: `设计稿`
- **技术栈**: `Java 21, Spring Boot 3.4.5, Virtual Threads`
- **复杂度等级**: L2

## 实施约束标注

### 🔒 强制性技术约束
- **Java版本**: 必须使用Java 21或更高版本，强制启用Virtual Threads和模块系统
- **Spring Boot版本**: 必须使用Spring Boot 3.4.5+，确保自动配置和事件机制兼容
- **Maven版本**: 必须使用Maven 3.9.0+，确保依赖管理和模块化构建正确
- **JVM参数**: 生产环境必须启用`--enable-preview`和`-XX:+UseZGC`

### ⚡ 性能指标约束
- **框架启动时间**: ≤1000ms（从@EnableNexus到所有插件启动完成）
- **插件加载时间**: ≤500ms（单个插件从发现到启动完成）
- **服务总线延迟**: ≤5ms（事件发布到监听器接收的时间）
- **内存占用**: 框架基础内存≤50MB，每个插件额外≤20MB
- **并发处理能力**: ≥10,000 events/second（单机模式）

### 🔄 兼容性要求
- **Spring生态兼容**: 100%兼容Spring Boot 3.x系列和Spring Framework 6.x系列
- **JVM兼容**: 支持HotSpot、OpenJ9、GraalVM等主流JVM实现
- **操作系统兼容**: 支持Linux、Windows、macOS等主流操作系统
- **向后兼容**: API接口保证向后兼容，配置格式保持稳定

### ⚠️ 违规后果定义
- **技术约束违规**: 应用启动失败，记录ERROR级别日志，提供详细错误信息和修复建议
- **性能指标超标**: 记录WARN级别日志，触发性能监控告警，可能影响系统稳定性
- **兼容性问题**: 应用启动失败或功能降级，记录兼容性问题报告

### 🎯 验证锚点设置
- **编译验证**: `mvn clean compile -Djava.version=21 --enable-preview`
- **单元测试**: `mvn test -Dtest=NexusFrameworkTest`
- **集成测试**: `mvn verify -Dtest=PluginIntegrationTest`
- **性能测试**: `mvn test -Dtest=PerformanceBenchmarkTest`
- **兼容性测试**: `mvn test -Dtest=CompatibilityTest`

## 核心定位

`xkongcloud-commons-nexus` (后续简称Nexus) 是一个基于**微内核 (Microkernel)** 和 **服务总线 (Service Bus)** 的轻量级、高性能、可扩展的应用基础框架。它的核心定位是成为应用内部的 **"中央插座板" (Central Socket Board)**。

任何功能模块，无论是数据访问、缓存、消息队列还是自定义业务服务，都可以被封装成一个标准的 **"插头" (Plugin)**，轻松地插入到Nexus这个"插座板"上。一旦插入，它便能立即被系统发现，并能通过服务总线与其他插件高效、安全地通信。

## 设计哲学

Nexus的设计哲学深度借鉴并升华了 `commons-db` 和 `commons-cache` 的成功经验，融合了两者的核心思想：

### 核心设计原则

1. **"组合优化" (Combination Optimization)**: Nexus不仅是一个插件容器，更是一个**能力组合平台**。它鼓励和促进插件间的协同工作，通过异步、非阻塞的通信机制，催生出 `1+1 > 2` 的性能和功能倍增效应

2. **"内置电池，并提供逃生舱口" (Batteries-included, with escape hatches)**:
   - **内置电池**: 为80%的场景提供一个高度自动化的环境。开发者只需将插件放入类路径，Nexus就能自动完成加载、生命周期管理和服务注册
   - **逃生舱口**: 为20%的复杂场景提供底层API。高级开发者可以直接与内核、服务总线交互，实现对插件生命周期、服务路由和通信方式的精细化控制

### 架构设计原则

- **微内核原则**: 保持内核极简，所有业务功能通过插件实现
- **服务总线原则**: 插件间通过统一的服务总线进行解耦通信
- **事件驱动原则**: 基于异步事件驱动的响应式架构
- **依赖倒置原则**: 高层模块不依赖低层模块，都依赖于抽象接口
- **开放封闭原则**: 对扩展开放，对修改封闭
- **单一职责原则**: 每个插件只负责一个特定的功能领域

### 技术选型逻辑

- **Java 21**: 利用Virtual Threads实现高并发，模块系统提供更好的隔离
- **Spring Boot 3.4.5+**: 成熟的自动配置和依赖注入机制
- **事件驱动架构**: 基于发布-订阅模式的松耦合通信
- **微内核架构**: 参考OSGi和Eclipse RCP的成功经验
- **服务总线模式**: 借鉴企业服务总线(ESB)的设计思想

## 包含范围

本项目包含以下核心功能和组件：

- **微内核框架**: 插件生命周期管理、依赖解析、安全沙箱
- **服务总线**: 异步事件驱动通信、消息路由、服务发现
- **插件接口定义**: 标准Plugin接口、@ExtensionPoint注解、Event基类
- **Spring Boot集成**: @EnableNexus自动配置、属性配置、插件扫描
- **官方插件适配**: DB库和Cache库的插件化封装
- **开发工具支持**: 插件开发脚手架、调试工具、性能监控

## 排除范围

本项目明确不包含以下内容：

- **具体业务逻辑**: 不包含任何特定业务领域的实现
- **外部系统集成**: 不直接集成第三方系统，通过插件方式扩展
- **UI界面组件**: 纯后端框架，不提供前端界面
- **分布式协调**: 专注于单应用内插件管理，不涉及跨应用协调
- **数据持久化**: 不直接提供数据存储，通过DB插件实现
- **网络通信协议**: 不定义跨进程通信协议，专注进程内通信

## 整体架构设计

### 分层架构模型

```mermaid
graph TB
    subgraph "应用层 (Application Layer)"
        UserService[User Service]
        OrderService[Order Service]
        PaymentService[Payment Service]
    end

    subgraph "Spring Boot集成层 (Integration Layer)"
        EnableNexus[@EnableNexus]
        AutoConfig[Auto Configuration]
        ServiceBridge[Service Bridge]
    end

    subgraph "Nexus框架层 (Framework Layer)"
        Kernel[Nexus Kernel]
        ServiceBusPublisher[Service Bus Publisher]
        ServiceBusSubscriber[Service Bus Subscriber]
        EventDispatcher[Event Dispatcher<br/>服务总线组件]
        PluginManager[Plugin Manager]
        SecurityManager[Security Manager]
    end

    subgraph "插件层 (Plugin Layer)"
        DBPlugin[DB Plugin]
        CachePlugin[Cache Plugin]
        MQPlugin[MQ Plugin]
        CustomPlugin[Custom Plugin]
    end

    subgraph "基础设施层 (Infrastructure Layer)"
        Database[(Database)]
        Cache[(Cache)]
        MessageQueue[(Message Queue)]
        ExternalAPI[External API]
    end

    UserService --> ServiceBridge
    OrderService --> ServiceBridge
    PaymentService --> ServiceBridge

    EnableNexus --> AutoConfig
    AutoConfig --> ServiceBridge
    ServiceBridge --> ServiceBusPublisher

    ServiceBusPublisher --> EventDispatcher
    EventDispatcher --> ServiceBusSubscriber
    ServiceBusPublisher --> SecurityManager

    Kernel --> ServiceBusPublisher
    Kernel --> PluginManager
    ServiceBusSubscriber --> Kernel

    PluginManager --> DBPlugin
    PluginManager --> CachePlugin
    PluginManager --> MQPlugin
    PluginManager --> CustomPlugin

    DBPlugin --> Database
    CachePlugin --> Cache
    MQPlugin --> MessageQueue
    CustomPlugin --> ExternalAPI
```

### 微内核架构详细设计

```mermaid
graph LR
    subgraph "微内核 (Microkernel)"
        LifecycleManager[生命周期管理器]
        DependencyResolver[依赖解析器]
        ClassLoaderManager[类加载器管理器]
        SecuritySandbox[安全沙箱]
    end

    subgraph "服务总线 (Service Bus) - 已修复循环依赖"
        ServiceBusPublisher[服务总线发布者]
        ServiceBusSubscriber[服务总线订阅者]
        ServiceRegistry[服务注册表]
        MessageRouter[消息路由器]
    end

    subgraph "扩展点系统 (Extension Points)"
        ExtensionScanner[扩展扫描器]
        ExtensionRegistry[扩展注册表]
        ServiceProvider[服务提供者]
        EventListener[事件监听器]
        EventDispatcher[事件分发器<br/>服务总线支撑组件]
    end

    LifecycleManager --> ServiceBusPublisher
    DependencyResolver --> ExtensionScanner
    ClassLoaderManager --> SecuritySandbox

    ServiceBusPublisher --> EventDispatcher
    EventDispatcher --> ServiceBusSubscriber
    ServiceBusSubscriber --> ServiceRegistry
    MessageRouter --> ExtensionRegistry

    ExtensionScanner --> ServiceBusPublisher
    ExtensionRegistry --> ServiceProvider
    ServiceProvider --> EventListener
```

### 通信协议架构

```mermaid
sequenceDiagram
    participant App as Application
    participant Bridge as Service Bridge
    participant Bus as Service Bus
    participant Plugin as Plugin
    participant Kernel as Kernel

    Note over App,Kernel: 应用启动阶段
    App->>Bridge: @EnableNexus
    Bridge->>Kernel: 启动内核
    Kernel->>Plugin: 扫描并加载插件
    Plugin->>Bus: 注册服务
    Bus->>Bridge: 服务注册事件
    Bridge->>App: 注册Spring Bean

    Note over App,Kernel: 运行时通信
    App->>Bridge: 调用服务
    Bridge->>Bus: 转发请求
    Bus->>Plugin: 路由到插件
    Plugin->>Bus: 发布事件
    Bus->>Plugin: 事件分发
    Plugin->>Bus: 返回结果
    Bus->>Bridge: 返回响应
    Bridge->>App: 返回结果
```

### 核心组件详细说明

#### 1. 微内核 (Microkernel)
系统的核心，但保持极简。它不包含任何业务逻辑，其唯一职责是管理插件的生命周期、处理插件间的依赖关系以及提供一个安全的运行沙箱。

**核心功能模块**：
- **生命周期管理器**: 管理插件的发现、加载、启动、停止、卸载全生命周期
- **依赖解析器**: 分析插件依赖关系，构建依赖图，执行拓扑排序
- **类加载器管理器**: 为每个插件创建独立的类加载器，实现真正的隔离
- **安全沙箱**: 基于Java SecurityManager的权限控制和安全隔离

**技术特性**：
- **插件发现机制**: 自动扫描类路径，发现符合规范的插件JAR包
- **热插拔支持**: 支持运行时动态加载和卸载插件
- **依赖管理**: 智能处理插件间的依赖关系和版本冲突
- **故障隔离**: 单个插件的故障不会影响整个系统的稳定性

#### 2. 服务总线 (Service Bus)
插件间通信的唯一通道。它是一个解耦的、异步的通信中介，实现插件间的完全解耦。

**核心功能模块**：
- **事件分发器**: 基于Virtual Threads的高性能异步事件分发
- **服务注册表**: 管理所有插件注册的服务，支持服务发现
- **消息路由器**: 智能路由消息到目标插件或监听器
- **事件总线**: 提供发布-订阅模式的事件通信机制

**通信模式**：
- **发布-订阅模式**: 事件驱动的异步通信
- **请求-响应模式**: 同步服务调用（内部异步实现）
- **点对点模式**: 直接的服务间通信
- **广播模式**: 向所有监听器广播事件

**技术特性**：
- **高性能**: 基于Virtual Threads，支持10,000+ events/second
- **类型安全**: 基于泛型的类型安全事件处理
- **错误隔离**: 监听器异常不会影响事件发布
- **可扩展**: 支持从进程内到分布式的平滑演进

#### 3. 扩展点系统 (Extension Points)
定义插件间协作的标准契约，实现面向接口的插件开发。

**核心组件**：
- **@ExtensionPoint注解**: 标记可被扩展的接口
- **@Extension注解**: 标记扩展点的具体实现
- **扩展注册表**: 管理所有扩展点和扩展实现的映射关系
- **服务提供者接口**: 标准化的SPI机制

**设计特性**：
- **面向契约**: 基于接口的松耦合设计
- **多实现支持**: 同一扩展点可以有多个实现
- **优先级排序**: 支持扩展实现的优先级排序
- **动态发现**: 运行时动态发现和注册扩展实现

#### 4. Spring Boot集成层
提供与Spring Boot生态系统的无缝集成。

**核心组件**：
- **@EnableNexus注解**: 一键启用Nexus框架
- **自动配置类**: 基于Spring Boot的自动配置机制
- **服务桥接器**: 将插件服务自动注册为Spring Bean
- **配置属性**: 类型安全的配置属性管理

**集成特性**：
- **零侵入**: 只需添加一个注解即可启用
- **配置统一**: 通过application.yml统一配置
- **生命周期同步**: 与Spring应用生命周期完全同步
- **监控集成**: 与Spring Boot Actuator无缝集成

## 模块结构与依赖关系

### 核心模块架构

```mermaid
graph TB
    subgraph "API层"
        NexusAPI[nexus-api<br/>核心API定义]
    end

    subgraph "框架核心层"
        NexusKernel[nexus-kernel<br/>微内核实现]
        NexusServiceBus[nexus-service-bus<br/>服务总线实现]
        NexusSecurity[nexus-security<br/>安全沙箱]
    end

    subgraph "集成层"
        NexusStarter[nexus-starter<br/>Spring Boot集成]
    end

    subgraph "插件层"
        PluginDB[nexus-plugin-db<br/>数据库插件]
        PluginCache[nexus-plugin-cache<br/>缓存插件]
        PluginMQ[nexus-plugin-mq<br/>消息队列插件]
        PluginCustom[nexus-plugin-*<br/>自定义插件]
    end

    subgraph "工具层"
        NexusTools[nexus-tools<br/>开发工具]
        NexusTest[nexus-test<br/>测试工具]
    end

    NexusKernel --> NexusAPI
    NexusServiceBus --> NexusAPI
    NexusSecurity --> NexusAPI
    NexusStarter --> NexusKernel
    NexusStarter --> NexusServiceBus

    %% 修复后的单向依赖
    NexusKernel --> NexusServiceBus

    PluginDB --> NexusAPI
    PluginCache --> NexusAPI
    PluginMQ --> NexusAPI
    PluginCustom --> NexusAPI

    NexusTools --> NexusAPI
    NexusTest --> NexusAPI
```

### 模块详细说明

为了实现高度内聚、低耦合，Nexus将遵循`commons`库的既定规范，划分为以下模块：

```
xkongcloud-commons-nexus/
├── nexus-api/                 # 核心契约: Plugin接口, @ExtensionPoint注解, Event基类
├── nexus-kernel/              # 微内核实现: 插件加载器, 生命周期管理器, 依赖解析器
├── nexus-service-bus/         # 服务总线抽象与核心实现 (默认提供进程内总线)
├── nexus-security/            # 安全模块: 权限管理, 安全沙箱, SecurityManager
├── nexus-starter/             # Spring Boot自动配置: @EnableNexus, 属性配置, 插件扫描
├── nexus-tools/               # 开发工具: 脚手架生成器, 权限策略工具, 打包工具
├── nexus-test/                # 测试工具: @NexusTest, MockServiceBus, 集成测试
└── nexus-plugins/             # (目录) 官方提供的标准插件集
    ├── nexus-plugin-db/       # DB库的插件化适配
    ├── nexus-plugin-cache/    # Cache库的插件化适配
    ├── nexus-plugin-mq/       # 消息队列插件
    └── nexus-plugin-monitoring/ # 监控插件
```

#### 核心模块职责

| 模块 | 职责 | 主要组件 | 依赖关系 | 循环依赖修复 |
|------|------|----------|----------|------------|
| **nexus-api** | 定义核心API和接口契约 | ServiceBusPublisher, ServiceBusSubscriber, Event | 无外部依赖 | ✅ 接口分离 |
| **nexus-kernel** | 微内核实现和生命周期管理 | NexusKernel, PluginManager, DependencyResolver | nexus-api | ✅ 单向依赖ServiceBus |
| **nexus-service-bus** | 发布者-订阅者分离的服务总线 | InProcessServiceBusPublisher, InProcessServiceBusSubscriber, EventDispatcher | nexus-api | ✅ 通过EventDispatcher解耦 |
| **nexus-security** | 安全沙箱和权限管理 | NexusSecurityManager, PermissionPolicy | nexus-api | ✅ 独立安全检查 |
| **nexus-starter** | Spring Boot集成 | @EnableNexus, AutoConfiguration | nexus-kernel, nexus-service-bus | ✅ 集成层无循环依赖 |
| **nexus-tools** | 开发和调试工具 | 脚手架生成器, 性能分析工具 | nexus-api | ✅ 工具层独立 |
| **nexus-test** | 测试支持工具 | @NexusTest, MockServiceBusPublisher | nexus-api | ✅ 测试层独立 |

## 监控与统计

### 关键性能指标 (KPI)

| 指标类别 | 指标名称 | 目标值 | 监控方式 | 告警阈值 |
|----------|----------|--------|----------|----------|
| **启动性能** | 框架启动时间 | ≤1000ms | Timer统计 | >1500ms |
| **插件性能** | 插件加载时间 | ≤500ms | Timer统计 | >1000ms |
| **通信性能** | 服务总线延迟 | ≤1ms | Timer统计 | >5ms |
| **并发性能** | 事件处理吞吐量 | ≥10,000/s | Counter统计 | <5,000/s |
| **内存使用** | 框架基础内存 | ≤50MB | JVM监控 | >100MB |
| **稳定性** | 插件启动成功率 | ≥99% | 成功/失败计数 | <95% |

## 总结与架构价值

### 核心架构价值

1. **微内核架构**: 保持内核极简，所有业务功能通过插件实现，确保系统的高度可扩展性
2. **服务总线模式**: 插件间通过统一的服务总线进行解耦通信，实现真正的松耦合
3. **事件驱动架构**: 基于异步事件驱动的响应式架构，提供高性能和高并发能力
4. **Spring Boot集成**: 与Spring Boot生态系统无缝集成，提供零侵入的插件化能力

### 设计模式应用

- **微内核模式 (Microkernel Pattern)**: 核心功能最小化，业务逻辑插件化
- **服务总线模式 (Service Bus Pattern)**: 统一的通信中介，实现插件解耦
- **发布-订阅模式 (Publish-Subscribe Pattern)**: 事件驱动的异步通信机制
- **依赖注入模式 (Dependency Injection Pattern)**: 基于Spring的依赖注入和服务发现
- **策略模式 (Strategy Pattern)**: 可插拔的插件实现策略
- **观察者模式 (Observer Pattern)**: 事件监听和状态变化通知

### 技术创新点

1. **Virtual Threads集成**: 充分利用Java 21 Virtual Threads实现高并发事件处理
2. **类加载器隔离**: 基于独立类加载器的真正插件隔离机制
3. **零侵入集成**: 通过@EnableNexus注解实现与Spring Boot的零侵入集成
4. **智能依赖解析**: 基于拓扑排序的智能插件依赖管理
5. **事件驱动协作**: 通过事件机制实现插件间的智能协作

### 架构演进能力

- **水平扩展**: 支持从单机到分布式的平滑演进
- **垂直扩展**: 支持插件功能的持续增强和优化
- **技术栈演进**: 支持新技术栈的无缝集成
- **生态系统扩展**: 支持第三方插件生态的建设

### 业务价值

- **开发效率**: 插件化开发模式大幅提升开发效率
- **系统稳定性**: 插件隔离机制确保系统的高稳定性
- **可维护性**: 模块化架构大幅提升系统的可维护性
- **可扩展性**: 微内核架构提供无限的扩展可能

### 未来演进方向

- **云原生支持**: 支持Kubernetes等云原生环境的插件管理
- **分布式插件**: 支持跨JVM的分布式插件部署和通信
- **AI辅助开发**: 基于AI的智能插件推荐和代码生成
- **插件市场**: 建设完整的插件生态市场和治理体系

## 详细设计文档链接

- **[01-architecture-overview.md](./01-architecture-overview.md)** (本文档)
- **[02-kernel-and-plugin-lifecycle.md](./02-kernel-and-plugin-lifecycle.md)**: 详细设计微内核与插件生命周期管理
- **[03-service-bus-and-communication.md](./03-service-bus-and-communication.md)**: 详细设计服务总线、事件模型与通信协议
- **[04-extension-points-and-spi.md](./04-extension-points-and-spi.md)**: 定义插件如何通过扩展点和SPI机制暴露和消费服务
- **[05-security-and-sandboxing.md](./05-security-and-sandboxing.md)**: 设计插件的安全与隔离模型
- **[06-starter-and-configuration.md](./06-starter-and-configuration.md)**: 描述与Spring Boot的无缝集成方案
- **[07-use-case-db-and-cache-as-plugins.md](./07-use-case-db-and-cache-as-plugins.md)**: 展示如何将现有库改造为Nexus插件的实际案例

这个架构概览为XKongCloud Commons Nexus框架提供了完整的技术蓝图，确保了系统的高性能、高可用性、高可扩展性和高可维护性。
