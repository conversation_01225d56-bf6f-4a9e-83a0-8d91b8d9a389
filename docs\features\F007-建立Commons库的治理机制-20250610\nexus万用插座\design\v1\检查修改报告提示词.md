# AI提示词：00-护栏约束上下文总览 深度修复与架构优化

## 🎯 任务目标

你是一位**顶级架构师**，拥有20年以上的大型系统设计经验。你的任务是对设计文档进行**深度架构分析和问题修复**，像一位资深架构师审视整个系统那样，识别并解决系统的**架构约束问题**、**设计一致性问题**和**实施可行性问题**。

## 📋 待修复文档

**文档路径**: `C:\ExchangeWorks\xkong\xkongcloud\docs\features\F007-建立Commons库的治理机制-20250610\nexus万用插座\design\v1\00-护栏约束上下文总览.md`
**文档名称**: `00-护栏约束上下文总览`
**检查评分**: `0.73/1.00`
**核心问题数**: `2个`

## 🚨 AI负载控制与循环解决策略

### ⚠️ 认知负载管理
为了确保AI能够深度思考每个问题，**必须按类别循环解决**，每次只专注一个类别：

1. **单类别专注原则**：每次对话只处理一个问题类别
2. **深度思考要求**：每个类别需要进行架构级深度分析
3. **循环迭代策略**：完成一个类别后，再处理下一个类别
4. **验证确认机制**：每个类别修复后需要重新检查验证

### 📊 问题类别优先级排序

#### 🔥 按优先级处理的问题类别：

**1. 架构设计问题** (2个问题)

   **问题1**: ❌ 致命错误: 检测到循环依赖，违反架构设计原则: ['ServiceBus -> ServiceRegistry -> ServiceBus', 'ExtensionPoint -> ExtensionImpl -> ExtensionImpl -> ServiceRegistry', 'UserService -> UserService -> ServiceBridge', 'OrderService -> OrderService -> ServiceBridge']
   - **问题位置**: 待确定具体位置
   - **问题上下文**: 问题涉及：❌ 致命错误: 检测到循环依赖，违反架构设计原则: ['ServiceBus -> ServiceRegistry -> ServiceBus', 'ExtensionPoint -> ExtensionImpl -> ExtensionImpl -> ServiceRegistry', 'UserService -> UserService -> ServiceBridge', 'OrderService -> OrderService -> ServiceBridge']，需要根据具体情况进行分析和修复
   - **参考文档**: 01-architecture-overview.md, 02-kernel-and-plugin-lifecycle.md, 03-service-bus-and-communication.md

   **问题2**: ❌ 致命错误: 循环依赖将导致初始化死锁和测试困难，必须重构架构
   - **问题位置**: 待确定具体位置
   - **问题上下文**: 问题涉及：❌ 致命错误: 循环依赖将导致初始化死锁和测试困难，必须重构架构，需要根据具体情况进行分析和修复
   - **参考文档**: 01-architecture-overview.md, 02-kernel-and-plugin-lifecycle.md, 03-service-bus-and-communication.md

## 📚 相关参考文档分析

### 🔍 同目录相关文档（共7个）
这些文档提供了系统的不同视角和详细设计，是解决问题的重要参考：

- `01-architecture-overview.md` - 需要分析其设计思路和约束要求
- `02-kernel-and-plugin-lifecycle.md` - 需要分析其设计思路和约束要求
- `03-service-bus-and-communication.md` - 需要分析其设计思路和约束要求
- `04-extension-points-and-spi.md` - 需要分析其设计思路和约束要求
- `05-security-and-sandboxing.md` - 需要分析其设计思路和约束要求
- `06-starter-and-configuration.md` - 需要分析其设计思路和约束要求
- `07-use-case-db-and-cache-as-plugins.md` - 需要分析其设计思路和约束要求


## 🧠 深度思考与分析要求

### 🎯 架构师思维模式
在处理每个问题类别时，你必须：

1. **系统性思考**：
   - 站在整个系统架构的高度分析问题
   - 考虑问题之间的关联性和影响范围
   - 识别根本原因而非表面症状

2. **多维度验证**：
   - 技术可行性：解决方案是否技术上可行
   - 架构一致性：是否符合整体架构设计原则
   - 实施复杂度：实施难度和风险评估
   - 长期演进性：是否支持未来扩展和演进

3. **深度分析方法**：
   - **问题根因分析**：为什么会出现这个问题？
   - **影响范围评估**：这个问题会影响哪些其他部分？
   - **解决方案设计**：如何从架构层面彻底解决？
   - **验证标准制定**：如何验证解决方案的有效性？

### 🔍 必须深度思考的核心问题

以下问题需要**架构师级别的深度思考**，不能简单修改：

1. **❌ 致命错误: 检测到循环依赖，违反架构设计原则: ['ServiceBus -> ServiceRegistry -> ServiceBus', 'ExtensionPoint -> ExtensionImpl -> ExtensionImpl -> ServiceRegistry', 'UserService -> UserService -> ServiceBridge', 'OrderService -> OrderService -> ServiceBridge']**
   - **位置**: 待确定具体位置
   - **上下文**: 问题涉及：❌ 致命错误: 检测到循环依赖，违反架构设计原则: ['ServiceBus -> ServiceRegistry -> ServiceBus', 'ExtensionPoint -> ExtensionImpl -> ExtensionImpl -> ServiceRegistry', 'UserService -> UserService -> ServiceBridge', 'OrderService -> OrderService -> ServiceBridge']，需要根据具体情况进行分析和修复
   - **参考文档**: 01-architecture-overview.md, 02-kernel-and-plugin-lifecycle.md, 03-service-bus-and-communication.md
   - **分析要求**: 为什么会出现这种问题？根本原因是什么？
   - **设计要求**: 如何从架构层面重新设计解决方案？
   - **验证要求**: 如何确保修复后的一致性和有效性？

2. **❌ 致命错误: 循环依赖将导致初始化死锁和测试困难，必须重构架构**
   - **位置**: 待确定具体位置
   - **上下文**: 问题涉及：❌ 致命错误: 循环依赖将导致初始化死锁和测试困难，必须重构架构，需要根据具体情况进行分析和修复
   - **参考文档**: 01-architecture-overview.md, 02-kernel-and-plugin-lifecycle.md, 03-service-bus-and-communication.md
   - **分析要求**: 为什么会出现这种问题？根本原因是什么？
   - **设计要求**: 如何从架构层面重新设计解决方案？
   - **验证要求**: 如何确保修复后的一致性和有效性？

## 🚀 执行指导与工作流程

### 📋 第一步：选择问题类别
从上述问题类别中选择**一个**进行深度分析和修复。建议按优先级顺序处理。

### 🔍 第二步：深度分析
1. **阅读相关文档**：仔细阅读目标文档和相关参考文档
2. **理解问题本质**：分析问题的根本原因和影响范围
3. **设计解决方案**：从架构角度设计彻底的解决方案
4. **制定实施计划**：明确修改步骤和验证方法

### ✏️ 第三步：精确修复
1. **保持架构一致性**：确保修改符合整体架构设计
2. **维护文档完整性**：保持文档的逻辑性和可读性
3. **遵循最佳实践**：应用行业最佳实践和设计模式
4. **确保可实施性**：修改后的设计必须可以实际实施

### ✅ 第四步：验证确认
1. **自我检查**：验证修改是否解决了目标问题
2. **一致性检查**：确保与其他部分保持一致
3. **完整性检查**：确保没有引入新的问题
4. **准备下一轮**：为处理下一个类别做准备

## 🎯 开始执行

请选择一个问题类别开始深度分析和修复。记住：
- **一次只处理一个类别**
- **必须进行深度思考**
- **确保架构级别的解决方案**
- **完成后再处理下一个类别**

---

**当前文档状态**：需要人工干预，存在关键问题
**修复目标**：提升文档质量，确保项目能够顺利实施
