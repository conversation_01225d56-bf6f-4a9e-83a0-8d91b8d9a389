Augment Agent 工具集标准化工作流提示词模板
:clipboard: 目录
工具分类概览
工具选择决策流程
标准工作流程模板
具体任务工作流
最佳实践指南
快速参考指南
:hammer_and_wrench: 工具分类概览
:file_folder: 文件管理类 (File Management)
核心功能: 文件和目录的创建、查看、编辑、删除操作

工具名称	核心参数	使用限制	适用场景
view	path, type, view_range	大文件需指定范围	查看文件内容、目录结构
str-replace-editor	command, path, old_str_1, new_str_1	每次最多200行编辑	精确修改现有文件
save-file	path, file_content	最多300行，仅创建新文件	创建新文件和文档
remove-files	file_paths	仅删除工作区文件	安全删除不需要的文件
:gear: 进程控制类 (Process Control)
核心功能: 系统进程的启动、监控、交互、终止管理

工具名称	核心参数	使用限制	适用场景
launch-process	command, wait, max_wait_seconds	同时只能运行一个wait=true进程	执行命令、启动服务
read-process	terminal_id, wait, max_wait_seconds	需要有效的terminal_id	获取进程输出结果
write-process	terminal_id, input_text	需要交互式进程	向进程发送输入
kill-process	terminal_id	强制终止，不可恢复	终止卡死或不需要的进程
list-processes	无	仅显示launch-process创建的进程	查看当前进程状态
read-terminal	only_selected	读取VSCode终端内容	获取终端历史输出
:globe_with_meridians: 网络访问类 (Network Access)
核心功能: 网络信息获取、网页访问、在线资源检索

工具名称	核心参数	使用限制	适用场景
web-search	query, num_results	最多10个结果	搜索技术文档、解决方案
web-fetch	url	需要有效URL，返回Markdown	获取网页内容、API文档
open-browser	url	不返回内容，仅供用户查看	展示结果、打开文档
:magnifying_glass_tilted_left: 开发辅助类 (Development Support)
核心功能: 代码分析、问题诊断、可视化、智能检索

工具名称	核心参数	使用限制	适用场景
codebase-retrieval	information_request	需要自然语言描述	代码查找、架构理解
diagnostics	paths	仅显示IDE检测到的问题	错误检查、代码质量分析
render-mermaid	diagram_definition, title	需要有效Mermaid语法	流程图、架构图可视化
remember	memory	仅存储长期有价值信息	重要信息记录、经验积累
:bullseye: 工具选择决策流程
文件操作需求

查看内容

修改现有文件

创建新文件

删除文件

执行命令需求

快速命令

后台服务

交互式操作

查看历史

信息获取需求

代码库内

网络搜索

特定网页

展示给用户

开发辅助需求

问题诊断

可视化

信息记录

开始任务

任务性质分析

具体操作类型?

view

str-replace-editor

save-file

remove-files

执行方式?

launch-process wait=true

launch-process wait=false

launch-process + write-process

read-terminal

信息来源?

codebase-retrieval

web-search

web-fetch

open-browser

辅助类型?

diagnostics

render-mermaid

remember

执行并验证

监控进程状态

read-process/list-processes

任务完成

:clipboard: 标准工作流程模板
通用五阶段工作流
阶段1: 需求分析
目标: 明确任务目标和范围，识别所需工具类别，制定执行计划

具体工具使用:

render-mermaid: 创建任务分析图表，可视化需求和依赖关系

参数: diagram_definition="任务分解流程图", title="需求分析图"
remember: 记录关键需求和约束条件

参数: memory="项目关键需求和技术约束"
save-file: 创建任务计划文档

参数: path="task_plan.md", file_content="详细的任务计划和时间安排"
检查清单:

 任务目标明确且可衡量
 识别出所有相关的工具类别
 制定了详细的执行计划
 评估了风险和依赖关系
阶段2: 信息收集
目标: 收集项目现状信息，了解代码结构，识别潜在问题

具体工具使用:

codebase-retrieval: 了解项目现状和相关代码

参数: information_request="详细描述需要了解的功能模块和代码结构"
view: 查看关键文件和目录结构

参数: path="目标路径", type="file/directory", view_range=[起始行, 结束行]
diagnostics: 检查现有问题和错误

参数: paths=["相关文件路径列表"]
web-search: 搜索相关技术资料和解决方案

参数: query="技术关键词", num_results=5
检查清单:

 完成代码库结构分析
 识别出所有相关文件
 发现并记录现有问题
 收集了必要的技术资料
阶段3: 执行操作
目标: 按计划执行具体操作，实时监控状态，记录重要信息

具体工具使用:

str-replace-editor: 修改现有文件

参数: command="str_replace", path="文件路径", old_str_1="原内容", new_str_1="新内容"
save-file: 创建新文件

参数: path="新文件路径", file_content="文件内容"
launch-process: 执行命令和启动服务

参数: command="执行命令", wait=true/false, max_wait_seconds=300
list-processes: 监控进程状态

参数: 无
read-process: 获取进程输出

参数: terminal_id=进程ID, wait=true, max_wait_seconds=60
检查清单:

 所有计划操作已执行
 进程状态正常
 重要信息已记录
 无异常错误发生
阶段4: 验证结果
目标: 检查操作结果，运行测试验证，保存经验教训

具体工具使用:

diagnostics: 检查操作后的问题状态

参数: paths=["修改后的文件路径"]
launch-process: 运行测试和验证命令

参数: command="测试命令", wait=true, max_wait_seconds=600
view: 查看结果文件

参数: path="结果文件路径", type="file"
web-fetch: 验证Web服务响应

参数: url="服务健康检查URL"
remember: 保存重要经验和教训

参数: memory="关键经验教训和最佳实践"
检查清单:

 所有功能按预期工作
 测试全部通过
 性能指标满足要求
 经验教训已记录
阶段5: 清理收尾
目标: 清理临时文件，终止不需要的进程，更新文档记录

具体工具使用:

remove-files: 清理临时文件

参数: file_paths=["临时文件路径列表"]
kill-process: 终止不需要的进程

参数: terminal_id=进程ID
save-file: 更新项目文档

参数: path="README.md", file_content="更新后的项目文档"
render-mermaid: 创建最终的架构图

参数: diagram_definition="最终架构图", title="项目架构"
检查清单:

 临时文件已清理
 无用进程已终止
 文档已更新
 项目状态整洁
:wrench: 具体任务工作流
代码编辑任务工作流
阶段1: 需求分析
render-mermaid: 创建代码修改流程图
codebase-retrieval: 了解要修改的代码模块
remember: 记录修改目标和约束
阶段2: 信息收集
view: 查看目标文件当前状态
codebase-retrieval: 查找相关代码和依赖
diagnostics: 检查现有代码问题
web-search: 搜索最佳实践和解决方案
阶段3: 执行操作
str-replace-editor: 进行精确代码修改
save-file: 创建新的配置或测试文件（如需要）
launch-process: 运行代码格式化工具
阶段4: 验证结果
diagnostics: 检查修改后的代码质量
launch-process: 运行单元测试
read-process: 查看测试结果
view: 确认修改效果
阶段5: 清理收尾
remove-files: 删除临时测试文件
save-file: 更新相关文档
remember: 记录修改经验
问题调试任务工作流
阶段1: 需求分析
render-mermaid: 创建问题分析图
remember: 记录问题现象和影响范围
阶段2: 信息收集
diagnostics: 获取IDE错误信息
read-terminal: 查看终端错误输出
codebase-retrieval: 查找问题相关代码
web-search: 搜索类似问题解决方案
阶段3: 执行操作
view: 详细查看问题文件
str-replace-editor: 应用修复方案
launch-process: 重现问题或测试修复
阶段4: 验证结果
launch-process: 运行回归测试
diagnostics: 确认问题已解决
read-process: 验证程序正常运行
阶段5: 清理收尾
save-file: 更新故障排除文档
remember: 记录调试经验和解决方案
项目部署任务工作流
阶段1: 需求分析
render-mermaid: 创建部署流程图
codebase-retrieval: 了解部署配置
remember: 记录部署要求和环境信息
阶段2: 信息收集
view: 查看部署配置文件
web-fetch: 获取部署文档
diagnostics: 检查代码质量
阶段3: 执行操作
launch-process: 执行构建命令 (wait=false)
read-process: 监控构建进度
launch-process: 启动部署服务
list-processes: 监控所有进程状态
阶段4: 验证结果
web-fetch: 验证服务健康状态
launch-process: 运行部署后测试
open-browser: 展示部署结果给用户
阶段5: 清理收尾
kill-process: 终止临时构建进程
save-file: 更新部署日志
remember: 记录部署经验
技术研究任务工作流
阶段1: 需求分析
render-mermaid: 创建研究计划图
remember: 记录研究目标和范围
阶段2: 信息收集
web-search: 搜索相关技术资料
web-fetch: 获取详细技术文档
codebase-retrieval: 查找项目中相关实现
阶段3: 执行操作
save-file: 创建研究笔记文档
render-mermaid: 创建技术架构图
launch-process: 运行技术验证实验
阶段4: 验证结果
view: 查看实验结果
web-search: 验证技术可行性
open-browser: 查看相关技术演示
阶段5: 清理收尾
save-file: 整理最终研究报告
remember: 记录关键技术结论
remove-files: 清理实验临时文件
:artist_palette: 最佳实践指南
工具组合使用模式
模式1: 文件操作三步法
view → codebase-retrieval → str-replace-editor
适用场景: 修改现有代码文件
具体步骤:

view: 了解文件结构 path="目标文件", type="file"
codebase-retrieval: 获取上下文 information_request="相关功能描述"
str-replace-editor: 精确修改 command="str_replace"
模式2: 进程监控循环
launch-process → list-processes → read-process → write-process
适用场景: 长期运行的交互式任务
具体步骤:

launch-process: 启动服务 wait=false
list-processes: 检查状态
read-process: 获取输出 wait=false
write-process: 发送输入（如需要）
模式3: 问题诊断链
diagnostics → codebase-retrieval → web-search → str-replace-editor
适用场景: 错误修复和问题解决
具体步骤:

diagnostics: 获取错误信息
codebase-retrieval: 理解错误上下文
web-search: 寻找解决方案
str-replace-editor: 应用修复
性能优化建议
文件操作优化
批量查看: 使用 view_range 参数限制大文件查看范围
批量编辑: str-replace-editor 支持多个替换，一次完成
智能检索: codebase-retrieval 使用具体描述，避免过于宽泛
进程管理优化
合理等待: 短期命令用 wait=true，长期服务用 wait=false
超时设置: 合理设置 max_wait_seconds，避免无限等待
进程清理: 定期使用 list-processes 检查，及时清理
网络访问优化
结果限制: web-search 使用合适的 num_results
URL验证: 使用 web-fetch 前验证URL有效性
避免重复: 不要重复 open-browser 相同URL
安全注意事项
文件安全
权限检查: 确认文件操作权限
备份策略: 重要修改前考虑备份
路径验证: 确认文件路径正确
进程安全
命令验证: 执行前验证命令安全性
权限控制: 避免执行危险命令
资源限制: 设置合理超时，防止资源耗尽
网络安全
URL验证: 验证URL安全性
数据保护: 不在网络请求中包含敏感信息
访问控制: 谨慎使用 open-browser
:bar_chart: 快速参考指南
任务类型快速匹配表
任务类型	阶段1工具	阶段2工具	阶段3工具	阶段4工具	阶段5工具
代码编辑	render-mermaid
remember	view
codebase-retrieval
diagnostics	str-replace-editor
launch-process	diagnostics
launch-process	save-file
remember
问题调试	render-mermaid
remember	diagnostics
read-terminal
web-search	view
str-replace-editor	launch-process
diagnostics	save-file
remember
项目部署	render-mermaid
remember	view
web-fetch
diagnostics	launch-process
list-processes	web-fetch
open-browser	kill-process
save-file
技术研究	render-mermaid
remember	web-search
web-fetch
codebase-retrieval	save-file
render-mermaid	view
web-search	save-file
remember
工具参数快速参考
高频参数组合
# 阶段1 - 需求分析
render-mermaid: diagram_definition="流程图代码", title="分析图"
remember: memory="关键需求和约束"

# 阶段2 - 信息收集
view: path="文件路径", type="file", view_range=[1, 100]
codebase-retrieval: information_request="详细功能描述"
diagnostics: paths=["文件路径列表"]

# 阶段3 - 执行操作
str-replace-editor: command="str_replace", path="文件路径"
launch-process: command="命令", wait=true/false, max_wait_seconds=300

# 阶段4 - 验证结果
launch-process: command="测试命令", wait=true
read-process: terminal_id=ID, wait=true, max_wait_seconds=60

# 阶段5 - 清理收尾
remove-files: file_paths=["临时文件列表"]
kill-process: terminal_id=进程ID
执行前检查清单
通用检查清单
 阶段1: 任务目标明确，计划详细
 阶段2: 信息收集完整，问题识别清楚
 阶段3: 操作步骤正确，监控及时
 阶段4: 验证全面，结果符合预期
 阶段5: 清理彻底，文档更新
工具使用检查清单
 文件路径正确且有权限
 进程命令语法正确
 网络URL有效且安全
 参数设置合理
 错误处理方案准备
常见问题快速解决
问题类型	涉及阶段	快速解决方案	预防措施
文件编辑失败	阶段3	用view确认内容，调整old_str	复制粘贴确保准确性
进程卡死	阶段3-4	使用kill-process终止	设置合理超时时间
网络请求失败	阶段2	检查URL，重试请求	验证URL有效性
代码搜索无结果	阶段2	调整搜索描述	使用具体关键词
权限错误	各阶段	检查文件权限	确认操作权限范围
:memo: 模板使用说明
如何使用此模板
选择任务类型: 根据具体需求选择对应的工作流
按阶段执行: 严格按照五个阶段顺序执行
工具选择: 参考快速匹配表选择合适工具
参数设置: 使用参考指南设置正确参数
质量检查: 使用检查清单确保质量
模板定制建议
项目适配: 根据项目特点调整工具组合
团队协作: 结合团队习惯定制流程
技术栈: 针对特定技术栈优化命令
环境配置: 根据开发环境调整参数
持续改进
定期回顾: 评估模板使用效果
收集反馈: 记录使用中的问题和建议
版本更新: 根据工具更新调整模板
经验分享: 积累和分享最佳实践
此模板为Augment Agent工具集的标准化工作流指导文档，建议根据具体项目需求进行调整和优化。