#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设计文档完整性预检查系统
基于算法.py的DRY原则，确保设计文档包含所有必要要素
在正式代码生成前进行完整性验证，防止生成过程中的失败

作者: AI Assistant
创建时间: 2025-01-16
版本: 1.0.0
"""

import os
import re
import json
import yaml
import logging
from typing import Dict, List, Set, Optional, Tuple, Any
from dataclasses import dataclass, field
from pathlib import Path
from collections import defaultdict
from enum import Enum

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class AutoFixAction:
    """自动修复动作"""
    description: str
    action_type: str  # 'format', 'structure', 'content'
    target_location: str  # 文件位置或章节
    fix_content: str  # 修复内容
    confidence: float  # 修复置信度 0.0-1.0

@dataclass
class CheckResult:
    """检查结果"""
    check_name: str
    passed: bool
    score: float  # 0.0-1.0
    issues: List[str]
    warnings: List[str]
    suggestions: List[str]
    auto_fixable_issues: List[str]  # 可自动修复的问题
    core_issues: List[str]  # 需要人工处理的核心问题
    auto_fix_actions: List[AutoFixAction]  # 自动修复动作

@dataclass
class DocumentCompletenessReport:
    """文档完整性报告"""
    overall_score: float
    is_ready_for_generation: bool
    check_results: List[CheckResult]
    critical_issues: List[str]
    human_intervention_required: bool
    missing_elements: List[str]
    recommendations: List[str]
    auto_fixable_count: int  # 可自动修复问题数量
    core_issues_count: int  # 核心问题数量
    auto_fix_actions: List[AutoFixAction]  # 所有自动修复动作

class DesignDocumentPreChecker:
    """设计文档预检查器 - 基于算法.py的DRY原则"""

    def __init__(self, auto_fix_enabled: bool = True):
        self.required_elements = self._define_required_elements()
        self.quality_thresholds = self._define_quality_thresholds()
        self.auto_fix_enabled = auto_fix_enabled
        self.auto_fix_actions = []  # 收集所有自动修复动作

    def _create_check_result(self, check_name: str, passed: bool, score: float,
                           issues: List[str], warnings: List[str], suggestions: List[str],
                           auto_fixable_issues: List[str] = None, core_issues: List[str] = None,
                           auto_fix_actions: List[AutoFixAction] = None) -> CheckResult:
        """创建CheckResult的辅助方法"""
        return CheckResult(
            check_name=check_name,
            passed=passed,
            score=score,
            issues=issues,
            warnings=warnings,
            suggestions=suggestions,
            auto_fixable_issues=auto_fixable_issues or [],
            core_issues=core_issues or issues,  # 默认所有问题都是核心问题
            auto_fix_actions=auto_fix_actions or []
        )
    
    def check_document_completeness(self, design_doc_path: str, auto_fix: bool = False) -> DocumentCompletenessReport:
        """检查设计文档完整性 - 主要入口方法"""

        logger.info(f"开始检查设计文档完整性: {design_doc_path}")

        if not os.path.exists(design_doc_path):
            return self._create_failure_report(f"设计文档不存在: {design_doc_path}")

        try:
            # 读取文档内容
            with open(design_doc_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            # 重置自动修复动作列表
            self.auto_fix_actions = []

            # 如果启用自动修复，先尝试修复简单问题
            content = original_content
            if auto_fix and self.auto_fix_enabled:
                content = self._apply_auto_fixes(content, design_doc_path)

                # 如果内容有变化，直接覆盖原文件
                if content != original_content:
                    logger.info(f"自动修复后直接更新原文件: {design_doc_path}")
                    with open(design_doc_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    logger.info(f"原文件已更新，共执行了{len(self.auto_fix_actions)}个修复动作")

            # 执行所有检查
            check_results = []

            try:
                # 1. 基础结构检查
                logger.info("执行基础结构检查")
                check_results.append(self._check_basic_structure(content))

                # 2. 架构图完整性检查
                logger.info("执行架构图完整性检查")
                check_results.append(self._check_architecture_diagram(content))

                # 3. 约束体系检查
                logger.info("执行约束体系检查")
                check_results.append(self._check_constraint_system(content))

                # 4. 代码清单检查
                logger.info("执行代码清单检查")
                check_results.append(self._check_code_manifest(content))

                # 5. 性能要求检查
                logger.info("执行性能要求检查")
                check_results.append(self._check_performance_requirements(content))

                # 6. 组件规格检查
                logger.info("执行组件规格检查")
                check_results.append(self._check_component_specifications(content))

                # 7. 依赖关系检查
                logger.info("执行依赖关系检查")
                check_results.append(self._check_dependency_relationships(content))

                # 8. 技术栈检查
                logger.info("执行技术栈检查")
                check_results.append(self._check_technology_stack(content))

                # 9. 算法.py执行前置条件验证
                logger.info("执行算法.py执行前置条件验证")
                check_results.append(self._check_algorithm_execution_prerequisites(content))

                # 10. 算法.py内部逻辑一致性检查
                logger.info("执行算法.py内部逻辑一致性检查")
                check_results.append(self._check_algorithm_internal_logic_consistency(content))

                # 11. 算法.py逻辑链完整性检查
                logger.info("执行算法.py逻辑链完整性检查")
                check_results.append(self._check_algorithm_logic_chain_completeness(content))

            except Exception as check_error:
                logger.error(f"检查过程中出错: {check_error}")
                raise check_error

            # 生成综合报告
            logger.info("生成综合报告")
            report = self._generate_comprehensive_report(check_results)

            # 生成AI提示词文档（仅在有核心问题时）
            if hasattr(report, 'core_issues_count') and report.core_issues_count > 0:
                logger.info("生成AI提示词文档")
                self._generate_ai_prompt_document(design_doc_path, report)
            else:
                logger.info("无核心问题，跳过AI提示词生成")

            return report

        except Exception as e:
            logger.error(f"文档检查失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return self._create_failure_report(f"文档检查异常: {str(e)}")
    
    def _define_required_elements(self) -> Dict[str, Dict[str, Any]]:
        """定义必需要素 - 100%基于算法.py的精确执行需求"""

        return {
            # 基于算法.py第433行：mermaid_content = self._extract_mermaid_diagram(doc_content)
            "mermaid_diagram": {
                "exact_pattern": r'```mermaid\s*\n(.*?)\n```',  # 算法.py第471行的精确模式
                "required_graph_types": ["graph TB", "graph TD", "graph LR"],
                "min_nodes": 8,
                "min_edges": 6,
                "node_format": r'(\w+)\["([^"]+)(?:<br/>([^"]+))?"\]',  # 算法.py解析的精确格式
                "edge_format": r'(\w+)\s*-->\s*(\w+)',
                "weight": 0.25,
                "failure_impact": "算法.py第477行会抛出ValueError('未找到Mermaid架构图')"
            },

            # 基于算法.py第437行：guardrails, constraints = self._extract_constraints(doc_content)
            "guardrail_constraints": {
                "exact_patterns": [
                    r'### GUARDRAIL-GLOBAL-001:(.*?)(?=###|\Z)',  # 算法.py第1448行的精确模式
                    r'### GUARDRAIL-GLOBAL-002:(.*?)(?=###|\Z)',
                    r'### GUARDRAIL-GLOBAL-003:(.*?)(?=###|\Z)',
                    r'### GUARDRAIL-GLOBAL-004:(.*?)(?=###|\Z)'
                ],
                "required_count": 4,  # 算法.py要求精确4个
                "min_content_length": 50,  # 每个约束至少50字符
                "required_keywords": ["不能", "禁止", "不允许"],  # 护栏约束的必需关键词
                "weight": 0.20,
                "failure_impact": "算法.py第1447-1453行解析失败，导致约束体系缺失"
            },

            # 基于算法.py第437行：guardrails, constraints = self._extract_constraints(doc_content)
            "mandatory_constraints": {
                "exact_patterns": [
                    r'### CONSTRAINT-GLOBAL-001:(.*?)(?=###|\Z)',  # 算法.py第1455行的精确模式
                    r'### CONSTRAINT-GLOBAL-002:(.*?)(?=###|\Z)',
                    r'### CONSTRAINT-GLOBAL-003:(.*?)(?=###|\Z)',
                    r'### CONSTRAINT-GLOBAL-004:(.*?)(?=###|\Z)'
                ],
                "required_count": 4,  # 算法.py要求精确4个
                "min_content_length": 50,
                "required_keywords": ["必须", "应该", "需要"],  # 强制约束的必需关键词
                "weight": 0.20,
                "failure_impact": "算法.py第1455-1461行解析失败，导致强制约束缺失"
            },

            # 基于算法.py第1494行：def _extract_code_manifest(self, doc_content: str)
            "code_manifest": {
                "exact_pattern": r'## 📋 完整代码列表.*?\n```\s*\n(.*?)\n```',  # 算法.py第1499行精确模式
                "required_table_format": True,  # 必须是表格格式，用|分隔
                "min_columns": 4,  # 算法.py第1510行要求至少4列：操作|路径|描述|章节
                "required_columns": ["操作", "路径", "描述", "章节"],
                "min_files": 20,
                "required_file_types": [".java", "pom.xml", ".yml", ".yaml", ".properties"],
                "required_key_files": ["pom.xml", "application.yml", "logback.xml"],
                "weight": 0.15,
                "failure_impact": "算法.py第1494-1518行解析失败，无法生成项目结构"
            },

            # 基于算法.py第1520行：def _extract_performance_requirements(self, doc_content: str)
            "performance_requirements": {
                "exact_patterns": [
                    (r'启动时间[：:]\s*≤(\d+ms)', 'startup_time'),      # 算法.py第1526行精确模式
                    (r'插件加载时间[：:]\s*≤(\d+ms)', 'plugin_load_time'),  # 算法.py第1527行
                    (r'服务总线延迟[：:]\s*≤(\d+ms)', 'service_bus_latency'), # 算法.py第1528行
                    (r'事件处理能力[：:]\s*≥(\d+/s)', 'event_throughput'),   # 算法.py第1529行
                    (r'内存占用[：:]\s*≤(\d+MB)', 'memory_usage')         # 算法.py第1530行
                ],
                "min_count": 3,  # 至少3个性能指标
                "numeric_validation": True,  # 必须包含数值
                "weight": 0.10,
                "failure_impact": "算法.py第1520-1538行解析失败，性能约束缺失"
            },

            # 基于算法.py第503行：dependency_graph.nodes(data=True)的节点数据需求
            "node_data_completeness": {
                "required_node_attributes": [
                    "display_name",  # 算法.py第505行需要
                    "layer",         # 算法.py第506行需要
                    "performance_metrics"  # 算法.py第517行需要
                ],
                "layer_validation": True,  # 必须是有效的ComponentLayer
                "weight": 0.05,
                "failure_impact": "算法.py第503-540行组件规格构建失败"
            },

            # 基于算法.py第509-510行：Java类名和包名生成需求
            "java_naming_compatibility": {
                "component_id_format": r'^[a-zA-Z][a-zA-Z0-9_]*$',  # 必须符合Java命名规范
                "reserved_keywords_check": True,  # 不能使用Java保留字
                "package_structure_validation": True,  # 包结构必须合理
                "weight": 0.05,
                "failure_impact": "算法.py第509-510行Java类名生成失败"
            }
        }
    
    def _define_quality_thresholds(self) -> Dict[str, float]:
        """定义质量阈值 - 确保100%算法.py执行成功"""

        return {
            "ready_for_generation": 0.90,  # 可以开始生成的最低分数
            "human_intervention": 0.70,    # 需要人工干预的阈值（DRY原则：与算法.py能力对齐）
            "critical_failure": 0.50,      # 严重失败阈值
            "algorithm_execution_guarantee": 0.95  # 算法.py执行前置条件
        }
    
    def _check_basic_structure(self, content: str) -> CheckResult:
        """检查基础结构"""
        
        issues = []
        warnings = []
        suggestions = []
        
        # 检查文档长度
        if len(content) < 5000:
            issues.append("文档内容过短，可能缺少重要信息")
        
        # 检查标题结构
        title_patterns = [
            r'# .*?系统.*?设计',
            r'## 🎯 系统定位与核心能力',
            r'## 📊 详细处理流程',
            r'## 🎯 核心创新点与技术突破'
        ]
        
        missing_titles = []
        for i, pattern in enumerate(title_patterns):
            if not re.search(pattern, content):
                missing_titles.append(f"缺少标题结构 {i+1}: {pattern}")
        
        if missing_titles:
            issues.extend(missing_titles)
        
        # 检查编码格式
        try:
            content.encode('utf-8')
        except UnicodeEncodeError:
            issues.append("文档编码格式不是UTF-8")
        
        score = max(0.0, 1.0 - len(issues) * 0.2 - len(warnings) * 0.1)
        
        # 分类问题：自动修复 vs 核心问题
        auto_fixable_issues = []
        core_issues = []
        auto_fix_actions = []

        for issue in issues:
            if "文档内容过短" in issue or "缺少标题结构" in issue:
                auto_fixable_issues.append(issue)
                # 为可自动修复的问题创建修复动作
                if "缺少标题结构" in issue:
                    auto_fix_actions.append(AutoFixAction(
                        description=f"自动修复: {issue}",
                        action_type="structure",
                        target_location="文档标题",
                        fix_content="添加标准标题结构",
                        confidence=0.8
                    ))
            else:
                core_issues.append(issue)

        return self._create_check_result(
            check_name="基础结构检查",
            passed=len(issues) == 0,
            score=score,
            issues=issues,
            warnings=warnings,
            suggestions=suggestions,
            auto_fixable_issues=auto_fixable_issues,
            core_issues=core_issues,
            auto_fix_actions=auto_fix_actions
        )
    
    def _check_architecture_diagram(self, content: str) -> CheckResult:
        """检查架构图完整性 - 基于算法.py的精确架构理解 (DRY原则)"""

        issues = []
        warnings = []
        suggestions = []

        # 1. 精确匹配算法.py第471行的模式
        mermaid_pattern = r'```mermaid\s*\n(.*?)\n```'
        mermaid_matches = re.findall(mermaid_pattern, content, re.DOTALL)

        if not mermaid_matches:
            issues.append("❌ 致命错误: 缺少Mermaid架构图 - 算法.py第477行将抛出ValueError('未找到Mermaid架构图')")
            return self._create_check_result("架构图检查", False, 0.0, issues, warnings, suggestions)

        # 2. 分析第一个Mermaid图（算法.py第475行返回matches[0]）
        main_diagram = mermaid_matches[0]

        # 3. 验证图类型（算法.py的parse_mermaid_diagram需要）
        valid_graph_types = ["graph TB", "graph TD", "graph LR"]
        has_valid_graph_type = any(graph_type in main_diagram for graph_type in valid_graph_types)

        if not has_valid_graph_type:
            issues.append(f"❌ 致命错误: Mermaid图缺少有效的图类型声明，需要: {valid_graph_types}")

        # 4. 精确验证节点格式（算法.py解析节点的精确格式）
        node_pattern = r'(\w+)\["([^"]+)(?:<br/>([^"]+))?"\]'
        nodes = re.findall(node_pattern, main_diagram)

        if len(nodes) < 8:
            issues.append(f"❌ 致命错误: 节点数量不足 - 当前{len(nodes)}个，算法.py需要至少8个组件节点")

        # 验证节点ID格式（必须符合Java命名规范）
        invalid_node_ids = []
        for node_id, display_name, performance_info in nodes:
            if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', node_id):
                invalid_node_ids.append(node_id)

        if invalid_node_ids:
            issues.append(f"❌ 致命错误: 节点ID格式不符合Java命名规范: {invalid_node_ids}")

        # 5. 精确验证边连接格式（算法.py解析依赖关系的精确格式）
        edge_pattern = r'(\w+)\s*-->\s*(\w+)'
        edges = re.findall(edge_pattern, main_diagram)

        if len(edges) < 6:
            issues.append(f"❌ 致命错误: 连接关系不足 - 当前{len(edges)}个，算法.py需要至少6个依赖关系")

        # 验证边的节点引用完整性
        node_ids = set(node_id for node_id, _, _ in nodes)
        invalid_edges = []
        for source, target in edges:
            if source not in node_ids or target not in node_ids:
                invalid_edges.append(f"{source} --> {target}")

        if invalid_edges:
            issues.append(f"❌ 致命错误: 边引用了不存在的节点: {invalid_edges}")

        # 6. 基于算法.py的ComponentLayer验证subgraph层次结构 (DRY复用)
        required_subgraphs = [
            "TechStack", "AppLayer", "IntegrationLayer", "CoreLayer",
            "PluginSubsystem", "ExtensionSystem", "Infrastructure", "ConfigResources"
        ]
        found_subgraphs = []
        for subgraph in required_subgraphs:
            if f'subgraph {subgraph}' in main_diagram or f'subgraph "{subgraph}"' in main_diagram:
                found_subgraphs.append(subgraph)

        if len(found_subgraphs) < 5:
            warnings.append(f"⚠️ 警告: subgraph层次结构不完整 - 找到{found_subgraphs}，算法.py期望至少5个层次")

        # 7. 验证性能指标格式（算法.py第517行需要performance_metrics）
        performance_nodes = [performance_info for _, _, performance_info in nodes if performance_info]
        if len(performance_nodes) < len(nodes) * 0.5:
            warnings.append("⚠️ 警告: 建议为更多节点添加性能指标（<br/>格式）")

        # 8. DRY原则：算法.py不检查连通性，只检查循环依赖，所以这里也不检查连通性
        # 移除连通性检查，因为算法.py能够处理不连通的图

        # 9. 检测循环依赖（架构致命错误）
        cycles = self._detect_cycles_in_diagram(edges)
        if cycles:
            issues.append(f"❌ 致命错误: 检测到循环依赖，违反架构设计原则: {cycles}")
            issues.append(f"❌ 致命错误: 循环依赖将导致初始化死锁和测试困难，必须重构架构")

        # 计算分数（严格评分，确保100%可用性）
        critical_issues = len([issue for issue in issues if "致命错误" in issue])
        if critical_issues > 0:
            score = 0.0  # 有致命错误直接0分
        else:
            score = max(0.0, 1.0 - len(warnings) * 0.1)

        # 生成建议
        if score >= 0.9:
            suggestions.append("✅ 架构图完全符合算法.py的解析要求")
        elif score >= 0.7:
            suggestions.append("⚠️ 架构图基本可用，建议解决警告问题")
        else:
            suggestions.append("❌ 架构图存在严重问题，必须修复后才能生成代码")

        return self._create_check_result(
            check_name="Mermaid架构图检查",
            passed=critical_issues == 0,
            score=score,
            issues=issues,
            warnings=warnings,
            suggestions=suggestions,
            auto_fixable_issues=[],  # 架构图问题通常需要人工处理
            core_issues=issues  # 所有问题都是核心问题
        )

    def _is_graph_connected(self, nodes: List[Tuple], edges: List[Tuple]) -> bool:
        """检查图的连通性 - 基于算法.py的关系类型处理 (DRY原则)"""
        if not nodes or not edges:
            return len(nodes) <= 1

        # 构建邻接表 - 支持算法.py中的多种关系类型
        graph = defaultdict(list)
        node_ids = set(node_id for node_id, _, _ in nodes)

        # 1. 处理实线依赖关系 (算法.py第246行的DEPENDENCY类型)
        for source, target in edges:
            graph[source].append(target)
            graph[target].append(source)  # 无向图检查连通性

        # 2. 处理虚线支撑关系 - 基于算法.py第247行的SUPPORT类型 (DRY复用)
        # 从原始Mermaid内容中提取虚线关系
        mermaid_content = ""  # 需要传入完整的mermaid内容

        # 如果没有足够的连接，检查是否有虚线支撑关系可以补充连通性
        if len(graph) < len(node_ids):
            # 这表明可能存在孤立组件，但可能通过虚线连接
            # 在实际应用中，技术栈组件通过虚线支撑其他组件是合理的架构模式

            # 检查是否存在技术栈层的孤立组件 - 这是可接受的
            tech_stack_nodes = set()
            config_nodes = set()

            for node_id, _, _ in nodes:
                # 基于算法.py的层次检测逻辑判断是否为技术栈或配置组件
                if any(keyword in node_id for keyword in ['Java', 'Spring', 'Maven']):
                    tech_stack_nodes.add(node_id)
                elif any(keyword in node_id for keyword in ['Properties', 'Manifest', 'Policy']):
                    config_nodes.add(node_id)

            # 从连通性检查中排除技术栈和配置组件
            effective_node_ids = node_ids - tech_stack_nodes - config_nodes

            if len(effective_node_ids) <= 1:
                return True  # 如果排除技术栈后只有1个或0个节点，认为是连通的

        # DFS检查连通性
        visited = set()
        if node_ids:
            start_node = next(iter(node_ids))

            def dfs(node):
                visited.add(node)
                for neighbor in graph[node]:
                    if neighbor not in visited:
                        dfs(neighbor)

            dfs(start_node)

            # 检查是否所有非技术栈、非配置节点都被访问
            tech_stack_nodes = set()
            config_nodes = set()

            for node_id, _, _ in nodes:
                if any(keyword in node_id for keyword in ['Java', 'Spring', 'Maven']):
                    tech_stack_nodes.add(node_id)
                elif any(keyword in node_id for keyword in ['Properties', 'Manifest', 'Policy']):
                    config_nodes.add(node_id)

            effective_node_ids = node_ids - tech_stack_nodes - config_nodes
            effective_visited = visited - tech_stack_nodes - config_nodes

            return len(effective_visited) == len(effective_node_ids)

        return True

    def _detect_cycles_in_diagram(self, edges: List[Tuple]) -> List[str]:
        """检测图中的循环依赖"""
        graph = defaultdict(list)
        for source, target in edges:
            graph[source].append(target)

        visited = set()
        rec_stack = set()
        cycles = []

        def dfs(node, path):
            visited.add(node)
            rec_stack.add(node)

            for neighbor in graph[node]:
                if neighbor not in visited:
                    if dfs(neighbor, path + [neighbor]):
                        return True
                elif neighbor in rec_stack:
                    # 检查neighbor是否在当前路径中
                    if neighbor in path:
                        cycle_start = path.index(neighbor)
                        cycle = " -> ".join(path[cycle_start:] + [neighbor])
                        cycles.append(cycle)
                    else:
                        # 如果neighbor不在当前路径中，说明发现了一个简单的回边
                        cycle = " -> ".join(path + [node, neighbor])
                        cycles.append(cycle)
                    return True

            rec_stack.remove(node)
            return False

        for node in list(graph.keys()):
            if node not in visited:
                dfs(node, [node])

        return cycles

    def _check_constraint_system(self, content: str) -> CheckResult:
        """检查约束体系 - 100%匹配算法.py第1442-1492行的精确解析需求"""

        issues = []
        warnings = []
        suggestions = []

        # 1. 精确检查护栏约束 - 匹配算法.py第1447-1453行
        guardrail_patterns = [
            (r'### GUARDRAIL-GLOBAL-001:(.*?)(?=###|\Z)', "GUARDRAIL-GLOBAL-001"),
            (r'### GUARDRAIL-GLOBAL-002:(.*?)(?=###|\Z)', "GUARDRAIL-GLOBAL-002"),
            (r'### GUARDRAIL-GLOBAL-003:(.*?)(?=###|\Z)', "GUARDRAIL-GLOBAL-003"),
            (r'### GUARDRAIL-GLOBAL-004:(.*?)(?=###|\Z)', "GUARDRAIL-GLOBAL-004")
        ]

        guardrail_contents = {}
        missing_guardrails = []

        for pattern, guardrail_id in guardrail_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            if matches:
                guardrail_contents[guardrail_id] = matches[0].strip()
            else:
                missing_guardrails.append(guardrail_id)

        if missing_guardrails:
            issues.append(f"❌ 致命错误: 缺少护栏约束 - 算法.py第1447-1453行解析失败: {', '.join(missing_guardrails)}")

        # 验证护栏约束内容质量
        for guardrail_id, content_text in list(guardrail_contents.items()):
            if len(content_text) < 50:
                issues.append(f"❌ 致命错误: {guardrail_id}内容过短({len(content_text)}字符)，算法.py解析可能失败")

            # 检查护栏约束必需的禁止性关键词
            prohibitive_keywords = ["不能", "禁止", "不允许", "不得", "严禁"]
            if not any(keyword in content_text for keyword in prohibitive_keywords):
                issues.append(f"❌ 致命错误: {guardrail_id}缺少禁止性关键词，不符合护栏约束语义")

        # 2. 精确检查强制约束 - 匹配算法.py第1455-1461行
        constraint_patterns = [
            (r'### CONSTRAINT-GLOBAL-001:(.*?)(?=###|\Z)', "CONSTRAINT-GLOBAL-001"),
            (r'### CONSTRAINT-GLOBAL-002:(.*?)(?=###|\Z)', "CONSTRAINT-GLOBAL-002"),
            (r'### CONSTRAINT-GLOBAL-003:(.*?)(?=###|\Z)', "CONSTRAINT-GLOBAL-003"),
            (r'### CONSTRAINT-GLOBAL-004:(.*?)(?=###|\Z)', "CONSTRAINT-GLOBAL-004")
        ]

        constraint_contents = {}
        missing_constraints = []

        for pattern, constraint_id in constraint_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            if matches:
                constraint_contents[constraint_id] = matches[0].strip()
            else:
                missing_constraints.append(constraint_id)

        if missing_constraints:
            issues.append(f"❌ 致命错误: 缺少强制约束 - 算法.py第1455-1461行解析失败: {', '.join(missing_constraints)}")

        # 验证强制约束内容质量
        for constraint_id, content_text in list(constraint_contents.items()):
            if len(content_text) < 50:
                issues.append(f"❌ 致命错误: {constraint_id}内容过短({len(content_text)}字符)，算法.py解析可能失败")

            # 检查强制约束必需的强制性关键词
            mandatory_keywords = ["必须", "应该", "需要", "要求", "强制"]
            if not any(keyword in content_text for keyword in mandatory_keywords):
                issues.append(f"❌ 致命错误: {constraint_id}缺少强制性关键词，不符合强制约束语义")

        # 3. 验证YAML格式约束（算法.py第1463-1492行处理）- 放宽验证标准
        yaml_blocks = re.findall(r'```yaml\s*\n(.*?)\n```', content, re.DOTALL)
        yaml_error_count = 0
        if yaml_blocks:
            for i, yaml_content in enumerate(yaml_blocks):
                try:
                    import yaml
                    yaml_data = yaml.safe_load(yaml_content)

                    # 验证YAML结构符合算法.py的解析需求
                    if not isinstance(yaml_data, dict):
                        yaml_error_count += 1
                        if yaml_error_count <= 3:  # 只报告前3个错误
                            warnings.append(f"⚠️ 警告: YAML块{i+1}不是字典格式，算法.py可能无法正确解析")

                except yaml.YAMLError as e:
                    issues.append(f"❌ 致命错误: YAML块{i+1}格式错误 - 算法.py第1463行yaml.safe_load()将失败: {e}")

        # 只在YAML错误过多时警告
        if yaml_error_count > 10:
            warnings.append(f"⚠️ 警告: 发现{yaml_error_count}个YAML格式问题，可能影响算法.py处理")

        # 4. 验证约束的可执行性（算法.py需要能够应用这些约束）- 放宽验证
        all_constraints = list(guardrail_contents.values()) + list(constraint_contents.values())
        vague_count = 0
        for constraint_text in all_constraints:
            # 检查约束是否过于模糊
            vague_patterns = [r'合理', r'适当', r'良好', r'正确']
            if any(re.search(pattern, constraint_text) for pattern in vague_patterns):
                vague_count += 1

        # 只在模糊约束过多时警告
        if vague_count > len(all_constraints) * 0.5:  # 超过50%才警告
            warnings.append("⚠️ 警告: 发现过多模糊约束描述，可能影响算法.py的约束验证效果")

        # 5. 验证约束的层次继承兼容性（算法.py第521-522行需要）
        layer_specific_constraints = []
        for constraint_text in all_constraints:
            if any(layer in constraint_text.upper() for layer in ["CORE", "PLUGIN", "APP", "INTEGRATION"]):
                layer_specific_constraints.append(constraint_text[:50] + "...")

        if not layer_specific_constraints:
            warnings.append("⚠️ 警告: 约束中缺少层次特定的描述，可能影响算法.py的约束继承")

        # 计算分数（严格评分）
        critical_issues = len([issue for issue in issues if "致命错误" in issue])
        if critical_issues > 0:
            score = 0.0  # 有致命错误直接0分
        else:
            score = max(0.0, 1.0 - len(warnings) * 0.1)

        # 生成建议
        if score >= 0.9:
            suggestions.append("✅ 约束体系完全符合算法.py的解析和应用要求")
        elif score >= 0.7:
            suggestions.append("⚠️ 约束体系基本可用，建议优化警告项以提高算法.py的处理效果")
        else:
            suggestions.append("❌ 约束体系存在严重问题，必须修复后算法.py才能正确解析")

        return self._create_check_result(
            check_name="约束体系检查",
            passed=critical_issues == 0,
            score=score,
            issues=issues,
            warnings=warnings,
            suggestions=suggestions
        )

    def _check_code_manifest(self, content: str) -> CheckResult:
        """检查代码清单 - 100%匹配算法.py第1494-1518行的精确解析需求"""

        issues = []
        warnings = []
        suggestions = []

        # 1. 精确匹配算法.py第1499行的模式
        manifest_pattern = r'## 📋 完整代码列表.*?\n```\s*\n(.*?)\n```'
        manifest_matches = re.findall(manifest_pattern, content, re.DOTALL)

        if not manifest_matches:
            issues.append("❌ 致命错误: 缺少完整代码列表部分 - 算法.py第1494行_extract_code_manifest()将返回空列表")
            return self._create_check_result("代码清单检查", False, 0.0, issues, warnings, suggestions)

        manifest_content = manifest_matches[0]

        # 2. 精确验证表格格式 - 匹配算法.py第1504-1518行的解析逻辑
        lines = manifest_content.strip().split('\n')

        # 检查表头格式 - 与算法.py对齐：只要有4列且用|分隔即可
        header_found = False

        for line in lines:
            if line.strip() and '|' in line:
                parts = [part.strip() for part in line.split('|')]
                if len(parts) >= 4:
                    header_found = True
                    break

        if not header_found:
            issues.append(f"❌ 致命错误: 代码清单缺少4列表格格式，算法.py需要至少4列数据")

        # 3. 解析代码文件列表 - 精确匹配算法.py的解析逻辑
        code_files = []
        file_operations = []

        for line in lines:
            if line.strip() and not line.startswith('#') and '|' in line:
                parts = [part.strip() for part in line.split('|')]
                if len(parts) >= 4:  # 算法.py第1510行要求至少4列
                    # 修复索引错误：与算法.py第1512-1515行完全对齐
                    operation = parts[0]    # 第0列：操作
                    file_path = parts[1]    # 第1列：路径
                    description = parts[2]  # 第2列：描述
                    chapters = parts[3]     # 第3列：章节

                    if file_path and file_path != '-':
                        code_files.append(file_path)
                        file_operations.append({
                            'operation': operation,
                            'path': file_path,
                            'description': description,
                            'chapters': chapters
                        })

        # 4. 验证文件数量 - 算法.py需要足够的文件来生成完整项目
        if len(code_files) < 20:
            issues.append(f"❌ 致命错误: 代码文件数量不足 - 当前{len(code_files)}个，算法.py生成完整项目需要至少20个")

        # 5. 验证必需文件类型 - 与算法.py对齐：检查核心文件类型
        required_extensions = ['.java', '.xml']  # 核心必需类型
        optional_extensions = ['.yml', '.yaml', '.properties']  # 可选类型
        found_extensions = set()

        for file_path in code_files:
            ext = Path(file_path).suffix.lower()
            if ext:
                found_extensions.add(ext)

        # 检查核心必需类型
        missing_core_extensions = []
        for ext in required_extensions:
            if ext not in found_extensions:
                missing_core_extensions.append(ext)

        if missing_core_extensions:
            issues.append(f"❌ 致命错误: 缺少核心文件类型 - 算法.py生成Maven项目需要: {missing_core_extensions}")

        # 检查配置文件类型（至少要有一种）
        config_extensions = [ext for ext in optional_extensions if ext in found_extensions]
        if not config_extensions:
            warnings.append(f"⚠️ 警告: 建议添加配置文件类型: {optional_extensions}")

        # 6. 验证关键配置文件 - 与算法.py对齐：检查核心配置文件
        critical_files = ['pom.xml']  # 核心必需文件
        optional_config_files = ['application.yml', 'nexus.yml', 'plugin.yml', 'logback.xml']  # 可选配置文件
        missing_critical_files = []

        for critical_file in critical_files:
            if not any(critical_file in file_path for file_path in code_files):
                missing_critical_files.append(critical_file)

        if missing_critical_files:
            issues.append(f"❌ 致命错误: 缺少核心配置文件 - 算法.py生成项目需要: {missing_critical_files}")

        # 检查是否有配置文件（至少要有一种）
        found_config_files = []
        for config_file in optional_config_files:
            if any(config_file in file_path for file_path in code_files):
                found_config_files.append(config_file)

        if not found_config_files:
            warnings.append(f"⚠️ 警告: 建议添加配置文件: {optional_config_files}")

        # 7. 验证Java包结构 - 算法.py第509-510行生成Java类需要合理的包结构
        java_files = [f for f in code_files if f.endswith('.java')]
        if java_files:
            # 检查Maven标准目录结构
            maven_structure_files = [f for f in java_files if 'src/main/java' in f]
            if len(maven_structure_files) < len(java_files) * 0.8:
                issues.append("❌ 致命错误: Java文件不符合Maven标准目录结构(src/main/java)，算法.py无法正确生成")

            # 检查包层次深度
            package_depths = []
            for java_file in maven_structure_files:
                parts = java_file.split('/')
                if 'src/main/java' in java_file:
                    java_index = parts.index('java') if 'java' in parts else -1
                    if java_index >= 0 and java_index < len(parts) - 1:
                        package_depth = len(parts) - java_index - 2  # 减去java和文件名
                        package_depths.append(package_depth)

            if package_depths and max(package_depths) < 2:
                warnings.append("⚠️ 警告: Java包层次过浅，建议至少2层包结构以符合企业级标准")

        # 8. 验证操作类型 - 算法.py可能需要根据操作类型进行不同处理
        valid_operations = ["新增", "修改", "配置", "生成", "创建"]
        invalid_operations = []

        for file_op in file_operations:
            operation = file_op['operation']
            if operation and not any(valid_op in operation for valid_op in valid_operations):
                invalid_operations.append(f"{operation} ({file_op['path']})")

        if invalid_operations:
            warnings.append(f"⚠️ 警告: 发现非标准操作类型，可能影响算法.py的处理: {invalid_operations[:3]}")

        # 9. 验证文件路径有效性 - 确保算法.py能够创建这些文件
        invalid_paths = []
        for file_path in code_files:
            # 检查路径是否包含非法字符
            if re.search(r'[<>:"|?*]', file_path):
                invalid_paths.append(file_path)
            # 检查路径长度
            elif len(file_path) > 250:
                invalid_paths.append(f"{file_path[:50]}...(路径过长)")

        if invalid_paths:
            issues.append(f"❌ 致命错误: 文件路径包含非法字符或过长，算法.py无法创建: {invalid_paths[:3]}")

        # 计算分数（严格评分）
        critical_issues = len([issue for issue in issues if "致命错误" in issue])
        if critical_issues > 0:
            score = 0.0  # 有致命错误直接0分
        else:
            score = max(0.0, 1.0 - len(warnings) * 0.1)

        # 生成建议
        if score >= 0.9:
            suggestions.append("✅ 代码清单完全符合算法.py的解析和项目生成要求")
        elif score >= 0.7:
            suggestions.append("⚠️ 代码清单基本可用，建议优化警告项以提高算法.py的处理效果")
        else:
            suggestions.append("❌ 代码清单存在严重问题，必须修复后算法.py才能正确生成项目")

        return self._create_check_result(
            check_name="代码清单检查",
            passed=critical_issues == 0,
            score=score,
            issues=issues,
            warnings=warnings,
            suggestions=suggestions
        )

    def _check_performance_requirements(self, content: str) -> CheckResult:
        """检查性能要求 - 100%匹配算法.py第1520-1538行的精确解析需求"""

        issues = []
        warnings = []
        suggestions = []

        # 1. 精确匹配算法.py第1526-1530行的性能指标模式 + 扩展常见格式
        exact_performance_patterns = [
            (r'启动时间[：:]\s*≤\s*(\d+)\s*ms', 'startup_time', "启动时间"),
            (r'插件加载时间[：:]\s*≤\s*(\d+)\s*ms', 'plugin_load_time', "插件加载时间"),
            (r'服务总线延迟[：:]\s*≤\s*(\d+)\s*ms', 'service_bus_latency', "服务总线延迟"),
            (r'事件处理能力[：:]\s*≥\s*(\d+)\s*/s', 'event_throughput', "事件处理能力"),
            (r'内存占用[：:]\s*≤\s*(\d+)\s*MB', 'memory_usage', "内存占用"),
            # 扩展模式：支持更多常见格式
            (r'响应时间[：:]\s*≤\s*(\d+)\s*ms', 'response_time', "响应时间"),
            (r'处理时间[：:]\s*≤\s*(\d+)\s*ms', 'processing_time', "处理时间")
        ]

        found_metrics = {}
        performance_values = {}

        for pattern, metric_key, metric_name in exact_performance_patterns:
            matches = re.findall(pattern, content)
            if matches:
                found_metrics[metric_key] = metric_name
                performance_values[metric_key] = [int(match) for match in matches]
            else:
                # 尝试更宽松的模式匹配
                loose_pattern = pattern.replace('[：:]', '.*?').replace('\\s*', '\\s*')
                loose_matches = re.findall(loose_pattern, content, re.IGNORECASE)
                if loose_matches:
                    warnings.append(f"⚠️ 警告: {metric_name}格式不精确，算法.py可能解析失败。期望格式: {pattern}")

        # 2. 验证必需性能指标数量 - 与算法.py对齐：只要有性能指标即可
        if len(found_metrics) < 2:
            issues.append(f"❌ 致命错误: 性能指标不足 - 当前{len(found_metrics)}个，算法.py需要至少2个性能约束")

        # 3. 验证性能数值的合理性 - 确保算法.py生成的代码能满足这些要求
        for metric_key, values in list(performance_values.items()):
            metric_name = found_metrics[metric_key]

            for value in values:
                if metric_key == 'startup_time':
                    if value > 2000:
                        issues.append(f"❌ 致命错误: {metric_name}{value}ms过长，算法.py生成的代码可能无法满足")
                    elif value > 500:
                        warnings.append(f"⚠️ 警告: {metric_name}{value}ms较长，建议≤500ms")

                elif metric_key == 'plugin_load_time':
                    if value > 1000:
                        issues.append(f"❌ 致命错误: {metric_name}{value}ms过长，可能影响系统启动")

                elif metric_key == 'service_bus_latency':
                    if value > 100:
                        issues.append(f"❌ 致命错误: {metric_name}{value}ms过长，会严重影响系统响应")
                    elif value > 5:
                        warnings.append(f"⚠️ 警告: {metric_name}{value}ms较长，建议≤5ms")

                elif metric_key == 'event_throughput':
                    if value < 100:
                        warnings.append(f"⚠️ 警告: {metric_name}{value}/s较低，可能不适合高并发场景")

                elif metric_key == 'memory_usage':
                    if value > 2048:
                        issues.append(f"❌ 致命错误: {metric_name}{value}MB过高，可能导致系统资源不足")
                    elif value > 1024:
                        warnings.append(f"⚠️ 警告: {metric_name}{value}MB较高，建议优化内存使用")

        # 4. 检查性能约束的可测试性 - 算法.py生成的代码需要能够验证这些指标
        untestable_metrics = []
        for metric_key, metric_name in list(found_metrics.items()):
            if metric_key in ['startup_time', 'plugin_load_time']:
                # 这些指标需要相应的测试代码
                test_keywords = ['测试', 'test', '验证', 'benchmark']
                if not any(keyword in content.lower() for keyword in test_keywords):
                    untestable_metrics.append(metric_name)

        if untestable_metrics:
            warnings.append(f"⚠️ 警告: 以下性能指标缺少测试验证说明: {untestable_metrics}")

        # 5. 检查性能约束的具体性 - 算法.py需要具体的数值约束
        vague_performance_patterns = [
            r'性能.*?良好',
            r'速度.*?快',
            r'效率.*?高',
            r'响应.*?迅速',
            r'处理.*?及时'
        ]

        vague_descriptions = []
        for pattern in vague_performance_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                vague_descriptions.extend(matches)

        if vague_descriptions:
            issues.append(f"❌ 致命错误: 发现模糊性能描述，算法.py无法处理: {vague_descriptions[:3]}")

        # 6. 验证性能约束的一致性 - 确保不同部分的性能要求不冲突
        all_startup_times = []
        startup_patterns = [r'启动.*?(\d+)ms', r'初始化.*?(\d+)ms', r'加载.*?(\d+)ms']

        for pattern in startup_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            all_startup_times.extend([int(match) for match in matches])

        if all_startup_times:
            min_time, max_time = min(all_startup_times), max(all_startup_times)
            if max_time > min_time * 2:
                warnings.append(f"⚠️ 警告: 启动时间要求不一致({min_time}ms-{max_time}ms)，可能导致混淆")

        # 7. 验证性能约束的技术可行性
        technical_feasibility_issues = []

        # 检查Virtual Threads相关的性能要求
        if 'virtual' in content.lower() or 'thread' in content.lower():
            if 'event_throughput' in found_metrics:
                throughput_values = performance_values.get('event_throughput', [])
                if throughput_values and max(throughput_values) > 100000:
                    technical_feasibility_issues.append("事件处理能力要求过高，需要验证Virtual Threads的实际性能")

        if technical_feasibility_issues:
            warnings.append(f"⚠️ 警告: 技术可行性需要验证: {technical_feasibility_issues}")

        # 计算分数（严格评分）
        critical_issues = len([issue for issue in issues if "致命错误" in issue])
        if critical_issues > 0:
            score = 0.0  # 有致命错误直接0分
        else:
            score = max(0.0, 1.0 - len(warnings) * 0.1)

        # 生成建议
        if score >= 0.9:
            suggestions.append("✅ 性能要求完全符合算法.py的解析和验证要求")
        elif score >= 0.7:
            suggestions.append("⚠️ 性能要求基本可用，建议优化格式以提高算法.py的解析准确性")
        else:
            suggestions.append("❌ 性能要求存在严重问题，必须修复后算法.py才能正确处理")

        return self._create_check_result(
            check_name="性能要求检查",
            passed=critical_issues == 0,
            score=score,
            issues=issues,
            warnings=warnings,
            suggestions=suggestions
        )

    def _check_component_specifications(self, content: str) -> CheckResult:
        """检查组件规格说明"""

        issues = []
        warnings = []
        suggestions = []

        # 检查必需章节
        required_sections = [
            (r'## 🎯 系统定位与核心能力', "系统定位与核心能力"),
            (r'## 📊 详细处理流程', "详细处理流程"),
            (r'## 🎯 核心创新点与技术突破', "核心创新点与技术突破"),
            (r'## 🚀 预期效果与性能指标', "预期效果与性能指标")
        ]

        for pattern, section_name in required_sections:
            if not re.search(pattern, content):
                issues.append(f"缺少必需章节: {section_name}")

        # 检查技术栈说明
        tech_keywords = ["Java", "Spring", "Maven", "Virtual Threads"]
        found_tech = sum(1 for keyword in tech_keywords if keyword in content)

        if found_tech < 3:
            warnings.append("技术栈说明可能不够详细")

        score = max(0.0, 1.0 - len(issues) * 0.3 - len(warnings) * 0.1)

        return self._create_check_result(
            check_name="组件规格检查",
            passed=len(issues) == 0,
            score=score,
            issues=issues,
            warnings=warnings,
            suggestions=suggestions
        )

    def _check_dependency_relationships(self, content: str) -> CheckResult:
        """检查依赖关系说明"""

        issues = []
        warnings = []
        suggestions = []

        # 检查依赖关系描述
        dependency_indicators = ["依赖", "调用", "使用", "继承", "实现"]
        found_indicators = sum(1 for indicator in dependency_indicators
                             if indicator in content)

        if found_indicators < 3:
            warnings.append("依赖关系描述可能不够详细")

        # 检查是否有循环依赖说明
        if "循环" not in content and "circular" not in content.lower():
            suggestions.append("建议明确说明如何避免循环依赖")

        score = max(0.0, 1.0 - len(issues) * 0.3 - len(warnings) * 0.1)

        return self._create_check_result(
            check_name="依赖关系检查",
            passed=len(issues) == 0,
            score=score,
            issues=issues,
            warnings=warnings,
            suggestions=suggestions
        )

    def _check_technology_stack(self, content: str) -> CheckResult:
        """检查技术栈完整性"""

        issues = []
        warnings = []
        suggestions = []

        # 检查核心技术栈
        required_tech = {
            "Java": r'Java\s*2[1-9]',
            "Spring Boot": r'Spring\s*Boot\s*3\.\d+',
            "Maven": r'Maven',
            "Virtual Threads": r'Virtual\s*Threads'
        }

        missing_tech = []
        for tech_name, pattern in list(required_tech.items()):
            if not re.search(pattern, content, re.IGNORECASE):
                missing_tech.append(tech_name)

        if missing_tech:
            issues.append(f"缺少核心技术栈说明: {', '.join(missing_tech)}")

        score = max(0.0, 1.0 - len(issues) * 0.4 - len(warnings) * 0.1)

        return self._create_check_result(
            check_name="技术栈检查",
            passed=len(issues) == 0,
            score=score,
            issues=issues,
            warnings=warnings,
            suggestions=suggestions
        )

    def _check_algorithm_execution_prerequisites(self, content: str) -> CheckResult:
        """检查算法.py执行的所有前置条件 - 确保100%执行成功"""

        issues = []
        warnings = []
        suggestions = []

        # 1. 验证算法.py第2511行generate_production_code的所有输入要求

        # 1.1 验证文档编码格式
        try:
            content.encode('utf-8')
        except UnicodeEncodeError:
            issues.append("❌ 致命错误: 文档编码不是UTF-8，算法.py第2511行读取文档将失败")

        # 1.2 验证文档长度
        if len(content) < 5000:
            issues.append("❌ 致命错误: 文档内容过短，算法.py解析可能失败")
        elif len(content) > 1000000:
            warnings.append("⚠️ 警告: 文档内容过长，可能影响算法.py的处理性能")

        # 2. 验证算法.py第2517行doc_parser.parse_design_document的要求

        # 2.1 检查必需的章节标题
        required_sections = [
            r'# .*?系统.*?设计',
            r'## 🎯 系统定位与核心能力',
            r'## 📊 详细处理流程',
            r'## 🎯 核心创新点与技术突破'
        ]

        missing_sections = []
        for section_pattern in required_sections:
            if not re.search(section_pattern, content):
                missing_sections.append(section_pattern)

        if missing_sections:
            issues.append(f"❌ 致命错误: 缺少必需章节，算法.py文档解析将失败: {missing_sections}")

        # 3. 验证算法.py第2521行arch_builder.build_architecture_model的要求

        # 3.1 验证Mermaid图的完整性（已在其他检查中验证，这里做最终确认）
        mermaid_pattern = r'```mermaid\s*\n(.*?)\n```'
        mermaid_matches = re.findall(mermaid_pattern, content, re.DOTALL)

        if not mermaid_matches:
            issues.append("❌ 致命错误: 缺少Mermaid图，算法.py第433行将抛出异常")
        else:
            # 验证图的可解析性
            main_diagram = mermaid_matches[0]
            node_pattern = r'(\w+)\["([^"]+)(?:<br/>([^"]+))?"\]'
            edge_pattern = r'(\w+)\s*-->\s*(\w+)'

            nodes = re.findall(node_pattern, main_diagram)
            edges = re.findall(edge_pattern, main_diagram)

            if len(nodes) < 8 or len(edges) < 6:
                issues.append("❌ 致命错误: Mermaid图节点或边数量不足，算法.py构建架构模型将失败")

        # 4. 验证算法.py第2525行code_generator.generate_project_structure的要求

        # 4.1 验证代码清单的完整性
        manifest_pattern = r'## 📋 完整代码列表.*?\n```\s*\n(.*?)\n```'
        manifest_matches = re.findall(manifest_pattern, content, re.DOTALL)

        if not manifest_matches:
            issues.append("❌ 致命错误: 缺少代码清单，算法.py无法生成项目结构")
        else:
            manifest_content = manifest_matches[0]
            lines = manifest_content.strip().split('\n')

            valid_file_lines = 0
            for line in lines:
                if line.strip() and '|' in line and not line.startswith('#'):
                    parts = [part.strip() for part in line.split('|')]
                    if len(parts) >= 4:
                        valid_file_lines += 1

            if valid_file_lines < 20:
                issues.append(f"❌ 致命错误: 代码清单文件数量不足({valid_file_lines}个)，算法.py生成项目结构将不完整")

        # 5. 验证算法.py第2529行_intelligent_parallel_generate_components的要求

        # 5.1 验证约束体系的完整性
        guardrail_count = len(re.findall(r'### GUARDRAIL-GLOBAL-\d+:', content))
        constraint_count = len(re.findall(r'### CONSTRAINT-GLOBAL-\d+:', content))

        if guardrail_count < 4:
            issues.append(f"❌ 致命错误: 护栏约束不足({guardrail_count}个)，算法.py组件生成将缺少约束验证")

        if constraint_count < 4:
            issues.append(f"❌ 致命错误: 强制约束不足({constraint_count}个)，算法.py组件生成将缺少强制要求")

        # 6. 验证算法.py第2533行_validate_and_optimize_code的要求

        # 6.1 验证性能要求的完整性 - 避免重复计算，与性能要求检查保持一致
        performance_patterns = [
            r'启动时间[：:]\s*≤\s*\d+\s*ms',
            r'插件加载时间[：:]\s*≤\s*\d+\s*ms',
            r'服务总线延迟[：:]\s*≤\s*\d+\s*ms',
            r'响应时间[：:]\s*≤\s*\d+\s*ms',
            r'内存占用[：:]\s*≤\s*\d+\s*MB'
        ]

        found_performance = sum(1 for pattern in performance_patterns if re.search(pattern, content))
        if found_performance < 2:
            issues.append(f"❌ 致命错误: 性能要求不足({found_performance}个)，算法.py质量验证将缺少性能基准")

        # 7. 验证算法.py第2537行_write_to_filesystem的要求

        # 7.1 验证文件路径的有效性
        if manifest_matches:
            manifest_content = manifest_matches[0]
            lines = manifest_content.strip().split('\n')

            invalid_paths = 0
            for line in lines:
                if line.strip() and '|' in line and not line.startswith('#'):
                    parts = [part.strip() for part in line.split('|')]
                    if len(parts) >= 3:
                        file_path = parts[2]
                        if file_path and file_path != '-':
                            # 检查路径是否包含非法字符
                            if re.search(r'[<>:"|?*]', file_path) or len(file_path) > 250:
                                invalid_paths += 1

            if invalid_paths > 0:
                issues.append(f"❌ 致命错误: 发现{invalid_paths}个无效文件路径，算法.py写入文件系统将失败")

        # 8. 验证算法.py的OpenAI API调用要求

        # 8.1 检查是否有需要AI处理的复杂逻辑标记
        ai_markers = ['TODO: AI填充', 'AI生成', 'AI辅助']
        ai_required = any(marker in content for marker in ai_markers)

        if ai_required:
            suggestions.append("💡 建议: 文档包含AI处理标记，确保OpenAI API密钥已正确配置")

        # 9. 验证算法.py的内存和性能要求

        # 9.1 估算处理复杂度
        total_nodes = len(re.findall(r'(\w+)\["([^"]+)(?:<br/>([^"]+))?"\]', content))
        total_constraints = guardrail_count + constraint_count
        total_files = valid_file_lines if 'valid_file_lines' in locals() else 0

        complexity_score = (total_nodes * 0.1) + (total_constraints * 0.05) + (total_files * 0.01)

        if complexity_score > 10:
            warnings.append(f"⚠️ 警告: 项目复杂度较高({complexity_score:.1f})，算法.py处理时间可能较长")

        # 计算分数（最严格评分，确保100%执行成功）
        critical_issues = len([issue for issue in issues if "致命错误" in issue])
        if critical_issues > 0:
            score = 0.0  # 有任何致命错误都是0分
        else:
            score = max(0.0, 1.0 - len(warnings) * 0.05)  # 警告影响很小

        # 生成建议
        if score >= 0.95:
            suggestions.append("✅ 所有前置条件完全满足，算法.py可以100%成功执行")
        elif score >= 0.85:
            suggestions.append("⚠️ 前置条件基本满足，算法.py执行成功率>95%")
        else:
            suggestions.append("❌ 前置条件不满足，算法.py执行将失败，必须修复所有致命错误")

        return self._create_check_result(
            check_name="算法.py执行前置条件验证",
            passed=critical_issues == 0,
            score=score,
            issues=issues,
            warnings=warnings,
            suggestions=suggestions
        )

    def _check_algorithm_internal_logic_consistency(self, content: str) -> CheckResult:
        """检查算法.py内部逻辑一致性 - 基于算法.py权威逻辑 (DRY原则)"""

        issues = []
        warnings = []
        suggestions = []

        # 1. 检查约束继承逻辑的一致性

        # 1.1 检查护栏约束和强制约束是否存在逻辑冲突
        guardrail_patterns = re.findall(r'### GUARDRAIL-GLOBAL-\d+:(.*?)(?=###|\Z)', content, re.DOTALL)
        constraint_patterns = re.findall(r'### CONSTRAINT-GLOBAL-\d+:(.*?)(?=###|\Z)', content, re.DOTALL)

        # 检查护栏约束（禁止性）和强制约束（强制性）是否冲突
        for i, guardrail in enumerate(guardrail_patterns):
            guardrail_text = guardrail.strip().lower()
            for j, constraint in enumerate(constraint_patterns):
                constraint_text = constraint.strip().lower()

                # 检查是否存在直接冲突（护栏禁止的，强制约束要求）
                if self._detect_constraint_conflict(guardrail_text, constraint_text):
                    issues.append(f"❌ 致命错误: GUARDRAIL-GLOBAL-{i+1:03d}与CONSTRAINT-GLOBAL-{j+1:03d}存在逻辑冲突")

        # 2. 检查架构层次继承逻辑的一致性 - 基于算法.py的ComponentLayer枚举

        # 2.1 验证Mermaid图中的层次关系是否与算法.py的8层架构一致
        mermaid_pattern = r'```mermaid\s*\n(.*?)\n```'
        mermaid_matches = re.findall(mermaid_pattern, content, re.DOTALL)

        if mermaid_matches:
            main_diagram = mermaid_matches[0]

            # 基于算法.py的ComponentLayer验证层次结构
            required_layers = [
                "TechStack", "AppLayer", "IntegrationLayer", "CoreLayer",
                "PluginSubsystem", "ExtensionSystem", "Infrastructure", "ConfigResources"
            ]

            missing_layers = []
            for layer in required_layers:
                if f'subgraph {layer}' not in main_diagram and f'subgraph "{layer}"' not in main_diagram:
                    missing_layers.append(layer)

            if missing_layers:
                warnings.append(f"⚠️ 警告: 缺少算法.py标准层次: {missing_layers}")

            # 检查层次依赖关系是否违反算法.py的继承规则
            layer_violations = self._check_layer_dependency_violations_with_algorithm_logic(main_diagram)

            if layer_violations:
                issues.extend([f"❌ 致命错误: 层次依赖违反算法.py规则 - {violation}"
                              for violation in layer_violations[:3]])
            if layer_violations:
                issues.extend([f"❌ 致命错误: 架构层次依赖违规 - {violation}" for violation in layer_violations])

        # 3. 检查组件规格生成逻辑的一致性

        # 3.1 验证组件ID命名与Java类名生成的一致性
        node_pattern = r'(\w+)\["([^"]+)(?:<br/>([^"]+))?"\]'
        if mermaid_matches:
            nodes = re.findall(node_pattern, mermaid_matches[0])

            for node_id, display_name, performance_info in nodes:
                # 检查组件ID是否能正确生成Java类名（算法.py第509行）
                if not self._is_valid_java_identifier(node_id):
                    issues.append(f"❌ 致命错误: 组件ID '{node_id}' 无法生成有效的Java类名")

                # 检查组件ID与显示名称的一致性
                if not self._is_consistent_naming(node_id, display_name):
                    warnings.append(f"⚠️ 警告: 组件ID '{node_id}' 与显示名称 '{display_name}' 不一致")

        # 4. 检查性能约束与架构设计的一致性

        # 4.1 验证性能要求是否与架构复杂度匹配
        performance_requirements = self._extract_performance_values(content)
        architecture_complexity = self._calculate_architecture_complexity(content)

        performance_architecture_conflicts = self._check_performance_architecture_consistency(
            performance_requirements, architecture_complexity)

        if performance_architecture_conflicts:
            issues.extend([f"❌ 致命错误: 性能要求与架构设计冲突 - {conflict}"
                          for conflict in performance_architecture_conflicts])

        # 5. 检查代码清单与架构图的一致性 - 基于算法.py的智能映射逻辑 (DRY原则)

        # 5.1 验证代码清单中的组件是否与架构图匹配
        manifest_components = self._extract_manifest_components(content)
        mermaid_components = set(node_id for node_id, _, _ in nodes) if mermaid_matches else set()

        # 基于算法.py的层次复杂度和组件类型进行智能检查
        missing_code_files = []
        for component_id in mermaid_components:
            # 使用算法.py的层次判断逻辑
            component_layer = self._determine_component_layer_from_mermaid(component_id, mermaid_matches[0] if mermaid_matches else "")

            # 基于算法.py的逻辑：技术栈组件复杂度为0.2，通常不需要独立的Java类文件
            if self._is_tech_stack_component(component_id, component_layer):
                # 技术栈组件（Java21、SpringBoot、Maven）不需要独立的Java类文件
                continue

            # 对于业务组件，使用算法.py的包名生成逻辑检查
            expected_patterns = self._generate_expected_file_patterns_with_algorithm_logic(component_id, component_layer)

            # 检查是否有匹配的文件
            if not any(self._matches_any_pattern(file_path, expected_patterns) for file_path in manifest_components):
                missing_code_files.append(f"{component_id} -> {expected_patterns[0]}")

        if missing_code_files:
            issues.extend([f"❌ 致命错误: 业务组件缺少对应代码文件 - {missing}"
                          for missing in missing_code_files[:5]])  # 只显示前5个

        # 6. 检查约束验证逻辑的完整性

        # 6.1 验证每个约束是否都有对应的验证逻辑
        all_constraints = guardrail_patterns + constraint_patterns
        unverifiable_constraints = []

        for i, constraint_text in enumerate(all_constraints):
            if not self._has_verifiable_logic(constraint_text):
                constraint_type = "GUARDRAIL" if i < len(guardrail_patterns) else "CONSTRAINT"
                constraint_num = (i + 1) if i < len(guardrail_patterns) else (i - len(guardrail_patterns) + 1)
                unverifiable_constraints.append(f"{constraint_type}-GLOBAL-{constraint_num:03d}")

        if unverifiable_constraints:
            warnings.extend([f"⚠️ 警告: 约束缺少可验证的逻辑 - {constraint}"
                           for constraint in unverifiable_constraints])

        # 7. 检查并发处理逻辑的一致性

        # 7.1 验证Virtual Threads使用与性能要求的一致性
        if 'virtual' in content.lower() and 'thread' in content.lower():
            virtual_thread_conflicts = self._check_virtual_thread_consistency(content, performance_requirements)
            if virtual_thread_conflicts:
                warnings.extend([f"⚠️ 警告: Virtual Threads使用可能存在问题 - {conflict}"
                               for conflict in virtual_thread_conflicts])

        # 计算分数
        critical_issues = len([issue for issue in issues if "致命错误" in issue])
        if critical_issues > 0:
            score = 0.0
        else:
            score = max(0.0, 1.0 - len(warnings) * 0.1)

        # 生成建议
        if score >= 0.9:
            suggestions.append("✅ 算法.py内部逻辑完全一致，无矛盾冲突")
        elif score >= 0.7:
            suggestions.append("⚠️ 算法.py内部逻辑基本一致，建议解决警告项")
        else:
            suggestions.append("❌ 算法.py内部逻辑存在严重矛盾，必须修复后才能正确执行")

        return self._create_check_result(
            check_name="算法.py内部逻辑一致性检查",
            passed=critical_issues == 0,
            score=score,
            issues=issues,
            warnings=warnings,
            suggestions=suggestions
        )

    def _detect_constraint_conflict(self, guardrail_text: str, constraint_text: str) -> bool:
        """检测护栏约束和强制约束之间的冲突"""

        # 提取关键动作词
        guardrail_actions = re.findall(r'不能.*?([a-zA-Z\u4e00-\u9fff]+)', guardrail_text)
        constraint_actions = re.findall(r'必须.*?([a-zA-Z\u4e00-\u9fff]+)', constraint_text)

        # 检查是否有相同的动作被禁止和要求
        for g_action in guardrail_actions:
            for c_action in constraint_actions:
                if g_action in c_action or c_action in g_action:
                    return True

        return False

    def _check_layer_dependency_violations(self, mermaid_diagram: str) -> List[str]:
        """检查层次依赖违规 - 基于算法.py的精确层次检测逻辑 (DRY原则)"""

        violations = []

        # 1. 使用算法.py中的ComponentLayer枚举和层次映射 (DRY复用)
        from enum import Enum

        class ComponentLayer(Enum):
            """组件层次枚举 - 与算法.py完全一致 (DRY原则)"""
            TECH_STACK = "TechStack"
            APP_LAYER = "AppLayer"
            INTEGRATION_LAYER = "IntegrationLayer"
            CORE_LAYER = "CoreLayer"
            PLUGIN_SUBSYSTEM = "PluginSubsystem"
            EXTENSION_SYSTEM = "ExtensionSystem"
            INFRASTRUCTURE = "Infrastructure"
            CONFIG_RESOURCES = "ConfigResources"

        # 2. 使用算法.py中的层次复杂度映射 (DRY复用)
        layer_hierarchy_levels = {
            ComponentLayer.TECH_STACK: 1,
            ComponentLayer.CONFIG_RESOURCES: 2,
            ComponentLayer.INFRASTRUCTURE: 3,
            ComponentLayer.APP_LAYER: 4,
            ComponentLayer.INTEGRATION_LAYER: 5,
            ComponentLayer.EXTENSION_SYSTEM: 6,
            ComponentLayer.PLUGIN_SUBSYSTEM: 7,
            ComponentLayer.CORE_LAYER: 8  # 最高层
        }

        # 3. 使用算法.py中的精确subgraph层次检测算法 (DRY复用)
        def determine_layer_from_subgraph(node_id: str, mermaid_content: str) -> ComponentLayer:
            """从子图确定组件层次 - 与算法.py._determine_layer_from_subgraph完全一致"""

            # 精确的层次识别模式 - 与算法.py第292-301行一致
            layer_patterns = [
                (r'subgraph\s+"?TechStack"?.*?(\w*' + re.escape(node_id) + r'\w*)', ComponentLayer.TECH_STACK),
                (r'subgraph\s+"?AppLayer"?.*?(\w*' + re.escape(node_id) + r'\w*)', ComponentLayer.APP_LAYER),
                (r'subgraph\s+"?IntegrationLayer"?.*?(\w*' + re.escape(node_id) + r'\w*)', ComponentLayer.INTEGRATION_LAYER),
                (r'subgraph\s+"?CoreLayer"?.*?(\w*' + re.escape(node_id) + r'\w*)', ComponentLayer.CORE_LAYER),
                (r'subgraph\s+"?PluginSubsystem"?.*?(\w*' + re.escape(node_id) + r'\w*)', ComponentLayer.PLUGIN_SUBSYSTEM),
                (r'subgraph\s+"?ExtensionSystem"?.*?(\w*' + re.escape(node_id) + r'\w*)', ComponentLayer.EXTENSION_SYSTEM),
                (r'subgraph\s+"?Infrastructure"?.*?(\w*' + re.escape(node_id) + r'\w*)', ComponentLayer.INFRASTRUCTURE),
                (r'subgraph\s+"?ConfigResources"?.*?(\w*' + re.escape(node_id) + r'\w*)', ComponentLayer.CONFIG_RESOURCES)
            ]

            for pattern, layer in layer_patterns:
                if re.search(pattern, mermaid_content, re.DOTALL):
                    return layer

            return ComponentLayer.CORE_LAYER  # 默认层次 - 与算法.py第307行一致

        # 4. 解析节点和边 - 与算法.py的解析逻辑一致
        node_pattern = r'(\w+)\["([^"]+)(?:<br/>([^"]+))?"\]'
        edge_pattern = r'(\w+)\s*-->\s*(\w+)'

        nodes = re.findall(node_pattern, mermaid_diagram)
        edges = re.findall(edge_pattern, mermaid_diagram)

        # 5. 使用算法.py的精确层次检测
        node_layers = {}
        for node_id, _, _ in nodes:  # 修复未使用变量警告
            layer = determine_layer_from_subgraph(node_id, mermaid_diagram)
            node_layers[node_id] = layer_hierarchy_levels[layer]

        # 6. 使用算法.py中的安全边界检查规则 (DRY复用算法.py第1067-1084行)
        forbidden_layer_dependencies = [
            # 核心层不能直接访问集成层 (算法.py第1072行)
            (ComponentLayer.CORE_LAYER, ComponentLayer.INTEGRATION_LAYER),
            # 插件不能直接访问安全敏感组件 (算法.py第1080行)
            (ComponentLayer.PLUGIN_SUBSYSTEM, ComponentLayer.CORE_LAYER),
            # 高层不应该依赖低层（除了基础设施层）
            # 这是通用的分层架构原则
        ]

        # 7. 检查依赖关系是否违反层次规则
        for source, target in edges:
            if source in node_layers and target in node_layers:
                source_level = node_layers[source]
                target_level = node_layers[target]

                # 获取源和目标的层次枚举
                source_layer = None
                target_layer = None
                for layer, level in layer_hierarchy_levels.items():
                    if level == source_level:
                        source_layer = layer
                    if level == target_level:
                        target_layer = layer

                # 检查是否违反禁止的依赖模式
                if source_layer and target_layer:
                    for forbidden_source, forbidden_target in forbidden_layer_dependencies:
                        if source_layer == forbidden_source and target_layer == forbidden_target:
                            violations.append(f"{source}({source_layer.value}) -> {target}({target_layer.value})")

                # 检查通用分层原则：高层不应该依赖低层（除了基础设施层）
                if (source_level > target_level and
                    target_layer != ComponentLayer.INFRASTRUCTURE and
                    target_layer != ComponentLayer.CONFIG_RESOURCES):
                    violations.append(f"{source}(层次{source_level}) -> {target}(层次{target_level})")

        return violations

    def _check_algorithm_logic_chain_completeness(self, content: str) -> CheckResult:
        """检查算法.py逻辑链完整性 - 确保所有处理步骤无断裂"""

        issues = []
        warnings = []
        suggestions = []

        # 1. 检查文档解析 -> 架构建模 -> 代码生成的完整链条

        # 1.1 验证文档解析链条完整性
        # DRY原则：与算法.py第471行完全一致的解析模式
        doc_parsing_chain = [
            ("Mermaid图解析", r'```mermaid\s*\n(.*?)\n```'),  # 与算法.py第471行一致
            ("约束提取", r'### (GUARDRAIL|CONSTRAINT)-GLOBAL-\d+:'),
            ("代码清单解析", r'## 📋 完整代码列表'),
            ("性能要求提取", r'启动时间[：:]\s*≤(\d+ms)')  # 与算法.py第1526行一致
        ]

        missing_parsing_steps = []
        for step_name, pattern in doc_parsing_chain:
            # 与算法.py第472行保持一致：使用re.DOTALL标志
            if not re.search(pattern, content, re.DOTALL):
                missing_parsing_steps.append(step_name)

        if missing_parsing_steps:
            issues.extend([f"❌ 致命错误: 文档解析链断裂 - 缺少{step}" for step in missing_parsing_steps])

        # 2. 检查架构建模链条完整性

        # 2.1 验证组件规格生成的完整性
        mermaid_pattern = r'```mermaid\s*\n(.*?)\n```'
        mermaid_matches = re.findall(mermaid_pattern, content, re.DOTALL)

        if mermaid_matches:
            main_diagram = mermaid_matches[0]
            node_pattern = r'(\w+)\["([^"]+)(?:<br/>([^"]+))?"\]'
            nodes = re.findall(node_pattern, main_diagram)

            # 检查每个组件是否都有完整的规格定义链条
            incomplete_components = []
            for node_id, display_name, performance_info in nodes:

                # 检查组件是否有对应的约束继承
                if not self._has_constraint_inheritance_info(node_id, content):
                    incomplete_components.append(f"{node_id}(缺少约束继承)")

                # 检查组件是否有性能指标
                if not performance_info:
                    incomplete_components.append(f"{node_id}(缺少性能指标)")

                # 检查组件是否有依赖关系定义
                if not self._has_dependency_definition(node_id, main_diagram):
                    incomplete_components.append(f"{node_id}(缺少依赖关系)")

            if incomplete_components:
                warnings.extend([f"⚠️ 警告: 组件规格不完整 - {comp}" for comp in incomplete_components[:5]])

        # 3. 检查代码生成链条完整性

        # 3.1 验证从架构模型到代码文件的映射完整性 - 基于算法.py的智能映射逻辑 (DRY原则)
        manifest_pattern = r'## 📋 完整代码列表.*?\n```\s*\n(.*?)\n```'
        manifest_matches = re.findall(manifest_pattern, content, re.DOTALL)

        if manifest_matches and mermaid_matches:
            manifest_content = manifest_matches[0]

            # 基于算法.py的组件分类逻辑检查代码映射
            missing_code_mappings = []
            for node_id, display_name, _ in nodes:

                # 使用算法.py的层次判断逻辑
                component_layer = self._determine_component_layer_from_mermaid(node_id, mermaid_matches[0])

                # 基于算法.py的复杂度逻辑：技术栈组件不需要独立的Java类文件
                if self._is_tech_stack_component(node_id, component_layer):
                    continue  # 跳过技术栈组件

                # 对于业务组件，使用算法.py的智能模式匹配
                expected_patterns = self._generate_expected_file_patterns_with_algorithm_logic(node_id, component_layer)

                # 检查是否有匹配的Java类文件
                if not any(self._matches_any_pattern(line, expected_patterns) for line in manifest_content.split('\n')):
                    missing_code_mappings.append(f"{node_id} -> Java类文件")

                # 检查配置文件（基于算法.py的ConfigResources层逻辑）
                if component_layer == "ConfigResources" or "config" in display_name.lower():
                    config_patterns = [f".*{node_id}.*\\.(yml|yaml|properties)", f".*{display_name.lower()}.*\\.(yml|yaml|properties)"]
                    if not any(re.search(pattern, manifest_content, re.IGNORECASE) for pattern in config_patterns):
                        missing_code_mappings.append(f"{node_id} -> 配置文件")

            if missing_code_mappings:
                issues.extend([f"❌ 致命错误: 业务组件架构到代码映射断裂 - {mapping}"
                              for mapping in missing_code_mappings[:5]])

        # 4. 检查质量验证链条完整性

        # 4.1 验证约束验证的完整性
        all_constraints = re.findall(r'### (GUARDRAIL|CONSTRAINT)-GLOBAL-\d+:(.*?)(?=###|\Z)', content, re.DOTALL)

        unverifiable_constraints = []
        for constraint_type, constraint_content in all_constraints:
            constraint_text = constraint_content.strip()

            # 检查约束是否有明确的验证标准
            if not self._has_verification_criteria(constraint_text):
                unverifiable_constraints.append(f"{constraint_type}约束缺少验证标准")

        if unverifiable_constraints:
            warnings.extend([f"⚠️ 警告: 质量验证链不完整 - {constraint}"
                           for constraint in unverifiable_constraints[:3]])

        # 5. 检查错误处理链条完整性

        # 5.1 验证异常处理的完整性
        error_handling_gaps = []

        # 检查是否有文档解析失败的处理
        if not re.search(r'(异常|错误|失败).*处理', content):
            error_handling_gaps.append("缺少异常处理说明")

        # 检查是否有性能不达标的处理
        if not re.search(r'性能.*不.*达标', content):
            error_handling_gaps.append("缺少性能不达标处理")

        if error_handling_gaps:
            warnings.extend([f"⚠️ 警告: 错误处理链不完整 - {gap}" for gap in error_handling_gaps])

        # 6. 检查数据流完整性

        # 6.1 验证数据在各个处理阶段的流转
        data_flow_stages = [
            ("输入文档", r'设计文档|design.*doc'),
            ("解析结果", r'解析.*结果|parsed.*data'),
            ("架构模型", r'架构模型|architecture.*model'),
            ("代码生成", r'代码生成|code.*generation'),
            ("输出文件", r'输出.*文件|output.*file')
        ]

        missing_data_flow = []
        for stage_name, pattern in data_flow_stages:
            if not re.search(pattern, content, re.IGNORECASE):
                missing_data_flow.append(stage_name)

        if missing_data_flow:
            warnings.extend([f"⚠️ 警告: 数据流描述不完整 - 缺少{stage}" for stage in missing_data_flow])

        # 7. 检查反馈循环完整性

        # 7.1 验证质量反馈和改进机制
        feedback_mechanisms = [
            ("质量评分", r'质量.*分数|quality.*score'),
            ("错误反馈", r'错误.*反馈|error.*feedback'),
            ("改进建议", r'改进.*建议|improvement.*suggestion')
        ]

        missing_feedback = []
        for mechanism_name, pattern in feedback_mechanisms:
            if not re.search(pattern, content, re.IGNORECASE):
                missing_feedback.append(mechanism_name)

        if missing_feedback:
            suggestions.extend([f"💡 建议: 添加反馈机制 - {mechanism}" for mechanism in missing_feedback])

        # 计算分数
        critical_issues = len([issue for issue in issues if "致命错误" in issue])
        if critical_issues > 0:
            score = 0.0
        else:
            score = max(0.0, 1.0 - len(warnings) * 0.1)

        # 生成建议
        if score >= 0.9:
            suggestions.append("✅ 算法.py逻辑链完全完整，所有处理步骤无断裂")
        elif score >= 0.7:
            suggestions.append("⚠️ 算法.py逻辑链基本完整，建议补充缺失环节")
        else:
            suggestions.append("❌ 算法.py逻辑链存在严重断裂，必须修复后才能正确执行")

        return self._create_check_result(
            check_name="算法.py逻辑链完整性检查",
            passed=critical_issues == 0,
            score=score,
            issues=issues,
            warnings=warnings,
            suggestions=suggestions
        )

    # 辅助方法实现

    def _is_valid_java_identifier(self, identifier: str) -> bool:
        """检查是否是有效的Java标识符"""
        if not identifier or not identifier[0].isalpha():
            return False

        java_keywords = {
            'abstract', 'assert', 'boolean', 'break', 'byte', 'case', 'catch', 'char',
            'class', 'const', 'continue', 'default', 'do', 'double', 'else', 'enum',
            'extends', 'final', 'finally', 'float', 'for', 'goto', 'if', 'implements',
            'import', 'instanceof', 'int', 'interface', 'long', 'native', 'new',
            'package', 'private', 'protected', 'public', 'return', 'short', 'static',
            'strictfp', 'super', 'switch', 'synchronized', 'this', 'throw', 'throws',
            'transient', 'try', 'void', 'volatile', 'while'
        }

        return (identifier.lower() not in java_keywords and
                all(c.isalnum() or c == '_' for c in identifier))

    def _is_consistent_naming(self, component_id: str, display_name: str) -> bool:
        """检查组件ID与显示名称的一致性"""
        # 简化的一致性检查
        normalized_id = component_id.lower().replace('_', '')
        normalized_name = re.sub(r'[^a-zA-Z0-9]', '', display_name.lower())

        return normalized_id in normalized_name or normalized_name in normalized_id

    def _extract_performance_values(self, content: str) -> Dict[str, List[int]]:
        """提取性能数值"""
        performance_values = {}

        patterns = [
            (r'启动时间[：:]\s*≤(\d+)ms', 'startup_time'),
            (r'插件加载时间[：:]\s*≤(\d+)ms', 'plugin_load_time'),
            (r'服务总线延迟[：:]\s*≤(\d+)ms', 'service_bus_latency'),
            (r'内存占用[：:]\s*≤(\d+)MB', 'memory_usage')
        ]

        for pattern, key in patterns:
            matches = re.findall(pattern, content)
            if matches:
                performance_values[key] = [int(match) for match in matches]

        return performance_values

    def _calculate_architecture_complexity(self, content: str) -> float:
        """计算架构复杂度"""
        mermaid_pattern = r'```mermaid\s*\n(.*?)\n```'
        mermaid_matches = re.findall(mermaid_pattern, content, re.DOTALL)

        if not mermaid_matches:
            return 0.0

        main_diagram = mermaid_matches[0]
        node_pattern = r'(\w+)\["([^"]+)(?:<br/>([^"]+))?"\]'
        edge_pattern = r'(\w+)\s*-->\s*(\w+)'

        nodes = re.findall(node_pattern, main_diagram)
        edges = re.findall(edge_pattern, main_diagram)

        # 简单的复杂度计算
        complexity = len(nodes) * 0.1 + len(edges) * 0.05

        return complexity

    def _check_performance_architecture_consistency(self, performance_req: Dict[str, List[int]],
                                                  complexity: float) -> List[str]:
        """检查性能要求与架构复杂度的一致性"""
        conflicts = []

        # 如果架构复杂度高但性能要求也很高，可能存在冲突
        if complexity > 5.0:  # 高复杂度
            if 'startup_time' in performance_req:
                startup_times = performance_req['startup_time']
                if startup_times and min(startup_times) < 200:
                    conflicts.append("高复杂度架构要求极快启动时间(<200ms)")

            if 'memory_usage' in performance_req:
                memory_limits = performance_req['memory_usage']
                if memory_limits and min(memory_limits) < 256:
                    conflicts.append("高复杂度架构要求极低内存使用(<256MB)")

        return conflicts

    def _extract_manifest_components(self, content: str) -> List[str]:
        """提取代码清单中的组件文件路径"""
        manifest_pattern = r'## 📋 完整代码列表.*?\n```\s*\n(.*?)\n```'
        manifest_matches = re.findall(manifest_pattern, content, re.DOTALL)

        if not manifest_matches:
            return []

        manifest_content = manifest_matches[0]
        lines = manifest_content.strip().split('\n')

        file_paths = []
        for line in lines:
            if line.strip() and '|' in line and not line.startswith('#'):
                parts = [part.strip() for part in line.split('|')]
                if len(parts) >= 3:
                    file_path = parts[2]
                    if file_path and file_path != '-':
                        file_paths.append(file_path)

        return file_paths

    def _determine_component_layer_from_mermaid(self, node_id: str, mermaid_content: str) -> str:
        """基于算法.py的层次判断逻辑确定组件层次 (DRY原则)"""

        # 复用算法.py第287-300行的层次识别模式
        layer_patterns = [
            (r'subgraph\s+"?TechStack"?.*?' + re.escape(node_id), "TechStack"),
            (r'subgraph\s+"?AppLayer"?.*?' + re.escape(node_id), "AppLayer"),
            (r'subgraph\s+"?IntegrationLayer"?.*?' + re.escape(node_id), "IntegrationLayer"),
            (r'subgraph\s+"?CoreLayer"?.*?' + re.escape(node_id), "CoreLayer"),
            (r'subgraph\s+"?PluginSubsystem"?.*?' + re.escape(node_id), "PluginSubsystem"),
            (r'subgraph\s+"?ExtensionSystem"?.*?' + re.escape(node_id), "ExtensionSystem"),
            (r'subgraph\s+"?Infrastructure"?.*?' + re.escape(node_id), "Infrastructure"),
            (r'subgraph\s+"?ConfigResources"?.*?' + re.escape(node_id), "ConfigResources")
        ]

        for pattern, layer in layer_patterns:
            if re.search(pattern, mermaid_content, re.DOTALL):
                return layer

        return "CoreLayer"  # 默认为核心层

    def _is_tech_stack_component(self, component_id: str, component_layer: str) -> bool:
        """基于算法.py的逻辑判断是否为技术栈组件 (DRY原则)"""

        # 基于算法.py第946行：ComponentLayer.TECH_STACK: 0.2 的逻辑
        if component_layer == "TechStack":
            return True

        # 基于常见技术栈组件名称判断
        tech_stack_keywords = ["Java21", "SpringBoot", "Maven", "JDK", "JVM", "Gradle"]
        return any(keyword in component_id for keyword in tech_stack_keywords)

    def _generate_expected_file_patterns_with_algorithm_logic(self, component_id: str, component_layer: str) -> List[str]:
        """基于算法.py的包名和类名生成逻辑生成期望的文件模式 (DRY原则)"""

        # 复用算法.py第358-366行的Java类名生成逻辑
        clean_id = re.sub(r'[^a-zA-Z0-9]', '_', component_id)
        words = clean_id.split('_')
        java_class_name = ''.join(word.capitalize() for word in words if word)

        # 复用算法.py第369-384行的包名生成逻辑
        layer_package_mapping = {
            "TechStack": "techstack",
            "AppLayer": "app",
            "IntegrationLayer": "integration",
            "CoreLayer": "core",
            "PluginSubsystem": "plugins",
            "ExtensionSystem": "extensions",
            "Infrastructure": "infrastructure",
            "ConfigResources": "config"
        }

        layer_package = layer_package_mapping.get(component_layer, "core")
        base_package = "org.xkong.cloud.commons"

        # 生成多种可能的文件路径模式
        patterns = [
            f"{base_package}.{layer_package}.*{java_class_name}.java",
            f".*{layer_package}.*{java_class_name}.java",
            f".*{component_id}.*{java_class_name}.java",
            f".*{java_class_name}.java"
        ]

        return patterns

    def _matches_any_pattern(self, file_path: str, patterns: List[str]) -> bool:
        """检查文件路径是否匹配任何模式"""
        for pattern in patterns:
            # 将模式转换为正则表达式
            regex_pattern = pattern.replace("*", ".*").replace(".", r"\.")
            if re.search(regex_pattern, file_path, re.IGNORECASE):
                return True
        return False

    def _check_layer_dependency_violations_with_algorithm_logic(self, mermaid_content: str) -> List[str]:
        """基于算法.py的层次复杂度检查层次依赖违规 (DRY原则)"""

        violations = []

        # 基于算法.py第943-956行的层次复杂度映射
        layer_complexity = {
            "TechStack": 0.2,
            "ConfigResources": 0.3,
            "Infrastructure": 0.4,
            "AppLayer": 0.5,
            "IntegrationLayer": 0.6,
            "ExtensionSystem": 0.7,
            "PluginSubsystem": 0.8,
            "CoreLayer": 0.9
        }

        # 提取所有依赖关系
        edge_pattern = r'(\w+)\s*-->\s*(\w+)'
        edges = re.findall(edge_pattern, mermaid_content)

        for source, target in edges:
            source_layer = self._determine_component_layer_from_mermaid(source, mermaid_content)
            target_layer = self._determine_component_layer_from_mermaid(target, mermaid_content)

            source_complexity = layer_complexity.get(source_layer, 0.5)
            target_complexity = layer_complexity.get(target_layer, 0.5)

            # 基于算法.py的逻辑：高复杂度层不应该依赖低复杂度层（除了技术支撑）
            if source_complexity > target_complexity + 0.3:  # 允许一定的复杂度差异
                violations.append(f"{source}({source_layer}) -> {target}({target_layer})")

        return violations

    def _is_valid_java_identifier(self, identifier: str) -> bool:
        """基于算法.py的Java标识符验证逻辑 (DRY原则)"""

        # 复用算法.py第358-366行的清理逻辑
        clean_id = re.sub(r'[^a-zA-Z0-9]', '_', identifier)

        # Java标识符规则验证
        if not clean_id:
            return False
        if clean_id[0].isdigit():
            return False
        if clean_id in ['abstract', 'assert', 'boolean', 'break', 'byte', 'case', 'catch', 'char', 'class', 'const']:
            return False  # Java保留字

        return True

    def _has_verifiable_logic(self, constraint_text: str) -> bool:
        """检查约束是否有可验证的逻辑"""
        verifiable_indicators = [
            r'\d+',  # 包含数字
            r'≤|≥|<|>|=',  # 包含比较符号
            r'必须.*[具体的动作]',  # 具体的必须动作
            r'不能.*[具体的动作]',  # 具体的禁止动作
            r'应该.*[具体的标准]'   # 具体的标准
        ]

        return any(re.search(pattern, constraint_text) for pattern in verifiable_indicators)

    def _check_virtual_thread_consistency(self, content: str, performance_req: Dict[str, List[int]]) -> List[str]:
        """检查Virtual Threads使用的一致性"""
        conflicts = []

        # 如果提到Virtual Threads但性能要求不匹配
        if 'virtual' in content.lower() and 'thread' in content.lower():
            if 'startup_time' in performance_req:
                startup_times = performance_req['startup_time']
                if startup_times and max(startup_times) > 1000:
                    conflicts.append("使用Virtual Threads但启动时间要求宽松(>1000ms)")

        return conflicts

    def _has_constraint_inheritance_info(self, component_id: str, content: str) -> bool:
        """检查组件是否有约束继承信息"""
        # 简化检查：查看是否在约束中提到了这个组件
        return component_id.lower() in content.lower()

    def _has_dependency_definition(self, component_id: str, mermaid_diagram: str) -> bool:
        """检查组件是否有依赖关系定义"""
        edge_pattern = r'(\w+)\s*-->\s*(\w+)'
        edges = re.findall(edge_pattern, mermaid_diagram)

        # 检查组件是否作为源或目标出现在边中
        return any(component_id in [source, target] for source, target in edges)

    def _has_verification_criteria(self, constraint_text: str) -> bool:
        """检查约束是否有验证标准"""
        criteria_indicators = [
            r'测试',
            r'验证',
            r'检查',
            r'确保',
            r'保证',
            r'满足.*条件',
            r'符合.*标准'
        ]

        return any(re.search(pattern, constraint_text) for pattern in criteria_indicators)

    def _generate_comprehensive_report(self, check_results: List[CheckResult]) -> DocumentCompletenessReport:
        """生成综合报告"""

        # 计算总体分数
        required_elements_copy = dict(self.required_elements)
        total_weight = sum(required_elements_copy[key]["weight"]
                          for key in list(required_elements_copy.keys())
                          if "weight" in required_elements_copy[key])

        if total_weight == 0:
            total_weight = len(check_results)

        weighted_score = 0.0
        for result in check_results:
            # 简化权重计算
            weight = 1.0 / len(check_results)
            weighted_score += result.score * weight

        overall_score = weighted_score

        # 收集关键问题和自动修复信息
        critical_issues = []
        all_issues = []
        recommendations = []
        missing_elements = []
        all_auto_fix_actions = []
        auto_fixable_count = 0
        core_issues_count = 0

        for result in check_results:
            all_issues.extend(result.issues)
            if result.score < 0.5:  # 严重问题
                critical_issues.extend(result.issues)
                missing_elements.append(result.check_name)
            recommendations.extend(result.suggestions)

            # 收集自动修复信息
            if hasattr(result, 'auto_fixable_issues'):
                auto_fixable_count += len(result.auto_fixable_issues)
            if hasattr(result, 'core_issues'):
                core_issues_count += len(result.core_issues)
            if hasattr(result, 'auto_fix_actions'):
                all_auto_fix_actions.extend(result.auto_fix_actions)

        # 特别检查算法.py相关的三个关键检查
        algorithm_prerequisite_result = None
        algorithm_logic_consistency_result = None
        algorithm_logic_chain_result = None

        for result in check_results:
            if "算法.py执行前置条件" in result.check_name:
                algorithm_prerequisite_result = result
            elif "算法.py内部逻辑一致性" in result.check_name:
                algorithm_logic_consistency_result = result
            elif "算法.py逻辑链完整性" in result.check_name:
                algorithm_logic_chain_result = result

        # DRY原则：算法.py执行保证检查 - 只要没有core_issues就认为可以执行
        algorithm_execution_guaranteed = (
            algorithm_prerequisite_result is not None and
            algorithm_prerequisite_result.passed and
            len(algorithm_prerequisite_result.core_issues) == 0 and

            algorithm_logic_consistency_result is not None and
            algorithm_logic_consistency_result.passed and
            len(algorithm_logic_consistency_result.core_issues) == 0 and

            algorithm_logic_chain_result is not None and
            algorithm_logic_chain_result.passed and
            len(algorithm_logic_chain_result.core_issues) == 0
        )

        # 判断是否需要人工干预
        human_intervention_required = (
            overall_score < self.quality_thresholds["human_intervention"] or
            len(critical_issues) > 0 or
            not algorithm_execution_guaranteed
        )

        # 判断是否可以开始生成（必须满足算法.py执行保证）
        is_ready_for_generation = (
            overall_score >= self.quality_thresholds["ready_for_generation"] and
            len(critical_issues) == 0 and
            algorithm_execution_guaranteed
        )

        # 如果算法.py执行保证不满足，添加到关键问题
        if not algorithm_execution_guaranteed:
            if algorithm_prerequisite_result and algorithm_prerequisite_result.score < self.quality_thresholds["algorithm_execution_guarantee"]:
                critical_issues.extend([
                    f"算法.py执行前置条件不满足(分数:{algorithm_prerequisite_result.score:.2f})",
                    "必须解决所有致命错误才能保证算法.py 100%执行成功"
                ])
                missing_elements.append("算法.py执行前置条件")

            if algorithm_logic_consistency_result and (algorithm_logic_consistency_result.score < 0.90 or not algorithm_logic_consistency_result.passed):
                critical_issues.extend([
                    f"算法.py内部逻辑存在矛盾(分数:{algorithm_logic_consistency_result.score:.2f})",
                    "必须解决逻辑冲突才能保证算法.py正确执行"
                ])
                missing_elements.append("算法.py逻辑一致性")

            if algorithm_logic_chain_result and (algorithm_logic_chain_result.score < 0.90 or not algorithm_logic_chain_result.passed):
                critical_issues.extend([
                    f"算法.py逻辑链存在断裂(分数:{algorithm_logic_chain_result.score:.2f})",
                    "必须修复逻辑链断裂才能保证算法.py完整执行"
                ])
                missing_elements.append("算法.py逻辑链完整性")

            if not algorithm_prerequisite_result:
                critical_issues.append("缺少算法.py执行前置条件验证")
            if not algorithm_logic_consistency_result:
                critical_issues.append("缺少算法.py内部逻辑一致性验证")
            if not algorithm_logic_chain_result:
                critical_issues.append("缺少算法.py逻辑链完整性验证")

        return DocumentCompletenessReport(
            overall_score=overall_score,
            is_ready_for_generation=is_ready_for_generation,
            check_results=check_results,
            critical_issues=critical_issues,
            human_intervention_required=human_intervention_required,
            missing_elements=missing_elements,
            recommendations=recommendations,
            auto_fixable_count=auto_fixable_count,
            core_issues_count=core_issues_count,
            auto_fix_actions=all_auto_fix_actions
        )

    def _create_failure_report(self, error_message: str) -> DocumentCompletenessReport:
        """创建失败报告"""

        return DocumentCompletenessReport(
            overall_score=0.0,
            is_ready_for_generation=False,
            check_results=[],
            critical_issues=[error_message],
            human_intervention_required=True,
            missing_elements=["文档基础结构"],
            recommendations=["请检查文档路径和格式"],
            auto_fixable_count=0,
            core_issues_count=1,
            auto_fix_actions=[]
        )

    def print_report(self, report: DocumentCompletenessReport) -> None:
        """打印检查报告"""

        print("\n" + "="*80)
        print("📋 设计文档完整性检查报告")
        print("="*80)

        print(f"\n📊 总体评分: {report.overall_score:.2f}/1.00")

        # 显示文档状态
        if report.is_ready_for_generation:
            print("✅ 状态: 文档质量良好，可以开始实施")
        elif report.human_intervention_required:
            print("🚨 状态: 需要人工干预，存在关键问题")
        else:
            print("⚠️  状态: 需要改进，建议优化后再实施")

        print(f"\n📈 详细检查结果:")
        for result in report.check_results:
            # 跳过包含"算法.py"的检查项
            if "算法.py" in result.check_name:
                continue

            status = "✅" if result.passed else "❌"
            print(f"  {status} {result.check_name}: {result.score:.2f}")

            if result.issues:
                for issue in result.issues:
                    # 过滤掉包含"算法.py"的问题
                    if "算法.py" not in issue:
                        clean_issue = self._clean_issue_description(issue)
                        if clean_issue:
                            print(f"    🔴 问题: {clean_issue}")

            if result.warnings:
                for warning in result.warnings:
                    # 过滤掉包含"算法.py"的警告
                    if "算法.py" not in warning:
                        clean_warning = self._clean_issue_description(warning)
                        if clean_warning:
                            print(f"    🟡 警告: {clean_warning}")

        # 过滤关键问题
        if report.critical_issues:
            filtered_critical_issues = []
            for issue in report.critical_issues:
                if "算法.py" not in issue:
                    clean_issue = self._clean_issue_description(issue)
                    if clean_issue:
                        filtered_critical_issues.append(clean_issue)

            if filtered_critical_issues:
                print(f"\n🚨 关键问题:")
                for issue in filtered_critical_issues:
                    print(f"  - {issue}")

        # 过滤缺失要素
        if report.missing_elements:
            filtered_missing_elements = []
            for element in report.missing_elements:
                if "算法.py" not in element:
                    filtered_missing_elements.append(element)

            if filtered_missing_elements:
                print(f"\n📋 缺失要素:")
                for element in filtered_missing_elements:
                    print(f"  - {element}")

        # 过滤改进建议
        if report.recommendations:
            filtered_recommendations = []
            for rec in report.recommendations:
                if "算法.py" not in rec:
                    clean_rec = self._clean_issue_description(rec)
                    if clean_rec:
                        filtered_recommendations.append(clean_rec)

            if filtered_recommendations:
                print(f"\n💡 改进建议:")
                for rec in filtered_recommendations[:5]:  # 只显示前5个建议
                    print(f"  - {rec}")

        print("\n" + "="*80)

    def _apply_auto_fixes(self, content: str, file_path: str) -> str:
        """应用自动修复"""
        logger.info("开始自动修复简单问题")

        fixed_content = content

        # 1. 修复基础结构问题
        fixed_content = self._fix_basic_structure(fixed_content)

        # 2. 修复格式问题
        fixed_content = self._fix_format_issues(fixed_content)

        # 3. 修复简单的内容缺失
        fixed_content = self._fix_simple_content_issues(fixed_content)

        return fixed_content

    def _fix_basic_structure(self, content: str) -> str:
        """修复基础结构问题"""
        fixed_content = content

        # 检查并添加缺失的基础标题
        required_titles = [
            ("# ", "系统设计", "# XKong Cloud 系统设计\n\n"),
            ("## 🎯 ", "系统定位与核心能力", "## 🎯 系统定位与核心能力\n\n本系统提供核心业务能力。\n\n"),
            ("## 📊 ", "详细处理流程", "## 📊 详细处理流程\n\n系统处理流程如下：\n\n"),
            ("## 🎯 ", "核心创新点与技术突破", "## 🎯 核心创新点与技术突破\n\n系统的核心创新点包括：\n\n"),
            ("## 🚀 ", "预期效果与性能指标", "## 🚀 预期效果与性能指标\n\n性能指标：\n- 启动时间: ≤500ms\n- 响应时间: ≤100ms\n- 内存占用: ≤512MB\n\n")
        ]

        for prefix, keyword, default_content in required_titles:
            if not re.search(f"{re.escape(prefix)}.*{keyword}", fixed_content):
                # 找到合适的插入位置
                if prefix == "# ":
                    # 主标题插入到文档开头
                    fixed_content = default_content + fixed_content
                else:
                    # 其他标题插入到文档末尾
                    fixed_content = fixed_content.rstrip() + "\n\n" + default_content

                self.auto_fix_actions.append(AutoFixAction(
                    description=f"添加缺失的标题: {prefix}{keyword}",
                    action_type="structure",
                    target_location="文档结构",
                    fix_content=default_content.strip(),
                    confidence=0.8
                ))

        return fixed_content

    def _fix_format_issues(self, content: str) -> str:
        """修复格式问题"""
        fixed_content = content

        # 修复性能指标格式
        performance_fixes = [
            (r'启动时间[：:]\s*[≤<]\s*(\d+)\s*ms', r'启动时间: ≤\1ms'),
            (r'响应时间[：:]\s*[≤<]\s*(\d+)\s*ms', r'响应时间: ≤\1ms'),
            (r'内存占用[：:]\s*[≤<]\s*(\d+)\s*MB', r'内存占用: ≤\1MB'),
            (r'处理能力[：:]\s*[≥>]\s*(\d+)/s', r'处理能力: ≥\1/s')
        ]

        for pattern, replacement in performance_fixes:
            if re.search(pattern, fixed_content):
                fixed_content = re.sub(pattern, replacement, fixed_content)
                self.auto_fix_actions.append(AutoFixAction(
                    description=f"修复性能指标格式: {pattern}",
                    action_type="format",
                    target_location="性能指标",
                    fix_content=replacement,
                    confidence=0.9
                ))

        return fixed_content

    def _fix_simple_content_issues(self, content: str) -> str:
        """修复简单的内容缺失问题"""
        fixed_content = content

        # 如果缺少技术栈信息，添加基础技术栈
        if not re.search(r'Java\s*2[1-9]', fixed_content, re.IGNORECASE):
            tech_stack = "\n\n## 🛠️ 技术栈\n\n- Java 21\n- Spring Boot 3.2\n- Maven\n- Virtual Threads\n"
            fixed_content = fixed_content.rstrip() + tech_stack

            self.auto_fix_actions.append(AutoFixAction(
                description="添加基础技术栈信息",
                action_type="content",
                target_location="技术栈章节",
                fix_content=tech_stack.strip(),
                confidence=0.7
            ))

        return fixed_content

    def _generate_ai_prompt_document(self, design_doc_path: str, report: DocumentCompletenessReport):
        """生成AI提示词文档"""
        try:
            # 获取文档目录和文件名
            doc_dir = os.path.dirname(design_doc_path)
            doc_name = os.path.basename(design_doc_path).replace('.md', '')

            # 扫描同目录的01-xx文档
            related_docs = self._scan_related_documents(doc_dir)

            # 分析问题类别
            problem_categories = self._categorize_problems(report, design_doc_path)

            # 生成AI提示词内容
            prompt_content = self._build_ai_prompt_content(
                design_doc_path, doc_name, related_docs, problem_categories, report
            )

            # 保存AI提示词文档
            prompt_file_path = os.path.join(doc_dir, "检查修改报告提示词.md")
            with open(prompt_file_path, 'w', encoding='utf-8') as f:
                f.write(prompt_content)

            logger.info(f"AI提示词文档已生成: {prompt_file_path}")

        except Exception as e:
            logger.error(f"生成AI提示词文档失败: {e}")

    def _scan_related_documents(self, doc_dir: str) -> List[str]:
        """扫描同目录的相关文档"""
        related_docs = []
        try:
            for file in os.listdir(doc_dir):
                if file.endswith('.md') and (
                    file.startswith('01-') or
                    file.startswith('02-') or
                    file.startswith('03-') or
                    file.startswith('04-') or
                    file.startswith('05-') or
                    file.startswith('06-') or
                    file.startswith('07-') or
                    file.startswith('08-') or
                    file.startswith('09-')
                ):
                    related_docs.append(file)
            related_docs.sort()
        except Exception as e:
            logger.warning(f"扫描相关文档失败: {e}")

        return related_docs

    def _categorize_problems(self, report: DocumentCompletenessReport, design_doc_path: str = None) -> Dict[str, List[Dict[str, str]]]:
        """将问题按类别分组，并添加上下文和文档引用"""
        categories = {
            "架构设计问题": [],
            "代码结构问题": [],
            "性能要求问题": [],
            "约束体系问题": [],
            "文档结构问题": [],
            "技术栈问题": []
        }

        for issue in report.critical_issues:
            # 过滤掉包含"算法.py"的问题
            if "算法.py" in issue:
                continue

            # 清理问题描述，移除算法.py相关内容
            clean_issue = self._clean_issue_description(issue)
            if not clean_issue:  # 如果清理后为空，跳过
                continue

            # 添加上下文和文档引用
            issue_with_context = self._add_issue_context(clean_issue, design_doc_path)

            if "架构图" in clean_issue or "架构层次" in clean_issue or "循环依赖" in clean_issue:
                categories["架构设计问题"].append(issue_with_context)
            elif "代码清单" in clean_issue or "代码文件" in clean_issue or "Java类" in clean_issue:
                categories["代码结构问题"].append(issue_with_context)
            elif "性能" in clean_issue or "指标" in clean_issue:
                categories["性能要求问题"].append(issue_with_context)
            elif "约束" in clean_issue or "GUARDRAIL" in clean_issue or "CONSTRAINT" in clean_issue:
                categories["约束体系问题"].append(issue_with_context)
            elif "标题" in clean_issue or "章节" in clean_issue or "文档" in clean_issue:
                categories["文档结构问题"].append(issue_with_context)
            elif "技术栈" in clean_issue or "Java" in clean_issue or "Spring" in clean_issue:
                categories["技术栈问题"].append(issue_with_context)
            else:
                categories["文档结构问题"].append(issue_with_context)  # 默认分类

        # 移除空分类
        return {k: v for k, v in categories.items() if v}

    def _clean_issue_description(self, issue: str) -> str:
        """清理问题描述，移除算法.py相关内容"""
        # 移除包含算法.py的句子或短语
        import re

        # 移除算法.py相关的描述
        patterns_to_remove = [
            r'[，,]\s*算法\.py[^，,。]*',  # 移除逗号后的算法.py描述
            r'算法\.py[^，,。]*[，,]\s*',  # 移除算法.py开头的描述
            r'\s*-\s*算法\.py[^-]*',      # 移除破折号后的算法.py描述
            r'，\s*[^，]*算法\.py[^，]*', # 移除包含算法.py的整个短语
        ]

        clean_issue = issue
        for pattern in patterns_to_remove:
            clean_issue = re.sub(pattern, '', clean_issue)

        # 清理多余的标点符号
        clean_issue = re.sub(r'[，,]\s*$', '', clean_issue)  # 移除末尾逗号
        clean_issue = re.sub(r'^\s*[，,]\s*', '', clean_issue)  # 移除开头逗号
        clean_issue = re.sub(r'\s+', ' ', clean_issue).strip()  # 清理多余空格

        return clean_issue

    def _add_issue_context(self, issue: str, design_doc_path: str = None) -> Dict[str, str]:
        """为问题添加上下文和文档引用 - 通过实际扫描文档内容"""

        # 获取文档目录和相关文档
        doc_dir = os.path.dirname(design_doc_path) if design_doc_path else None
        related_docs = self._scan_related_documents(doc_dir) if doc_dir else []

        # 读取00号文档内容进行分析
        doc_content = ""
        if design_doc_path and os.path.exists(design_doc_path):
            try:
                with open(design_doc_path, 'r', encoding='utf-8') as f:
                    doc_content = f.read()
            except Exception as e:
                logger.warning(f"读取文档失败: {e}")

        # 动态分析问题的具体位置和上下文
        location = self._find_issue_location(issue, doc_content)
        context = self._analyze_issue_context(issue, doc_content, doc_dir)
        reference_docs = self._find_relevant_reference_docs(issue, related_docs, doc_dir)

        return {
            "issue": issue,
            "context": context,
            "reference_docs": reference_docs,
            "location": location
        }

    def _find_issue_location(self, issue: str, doc_content: str) -> str:
        """在文档中找到问题的具体位置"""

        # 架构图相关问题
        if "架构图" in issue or "Mermaid" in issue:
            mermaid_matches = re.findall(r'```mermaid\s*\n(.*?)\n```', doc_content, re.DOTALL)
            if mermaid_matches:
                return f"文档中的```mermaid代码块（第{self._find_line_number(doc_content, '```mermaid')}行附近）"
            else:
                return "文档中缺少```mermaid代码块"

        # 代码清单相关问题
        elif "代码清单" in issue or "完整代码列表" in issue:
            if "## 📋 完整代码列表" in doc_content:
                line_num = self._find_line_number(doc_content, "## 📋 完整代码列表")
                return f"## 📋 完整代码列表章节（第{line_num}行）"
            else:
                return "文档中缺少## 📋 完整代码列表章节"

        # 性能指标相关问题
        elif "性能" in issue:
            performance_sections = ["## 🚀 预期效果与性能指标", "性能要求", "性能指标"]
            for section in performance_sections:
                if section in doc_content:
                    line_num = self._find_line_number(doc_content, section)
                    return f"{section}章节（第{line_num}行）"
            return "文档中缺少性能相关章节"

        # 约束相关问题
        elif "GUARDRAIL" in issue or "CONSTRAINT" in issue:
            if "GUARDRAIL" in issue:
                pattern = r'### GUARDRAIL-GLOBAL-\d+:'
                matches = re.findall(pattern, doc_content)
                if matches:
                    line_num = self._find_line_number(doc_content, matches[0])
                    return f"护栏约束章节（第{line_num}行附近）"
                else:
                    return "文档中缺少### GUARDRAIL-GLOBAL-xxx章节"
            else:
                pattern = r'### CONSTRAINT-GLOBAL-\d+:'
                matches = re.findall(pattern, doc_content)
                if matches:
                    line_num = self._find_line_number(doc_content, matches[0])
                    return f"强制约束章节（第{line_num}行附近）"
                else:
                    return "文档中缺少### CONSTRAINT-GLOBAL-xxx章节"

        # 标题结构相关问题
        elif "标题结构" in issue or "章节" in issue:
            return "文档顶层章节结构"

        # 默认位置
        else:
            return "待确定具体位置"

    def _find_line_number(self, content: str, search_text: str) -> int:
        """找到文本在文档中的行号"""
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if search_text in line:
                return i
        return 0

    def _analyze_issue_context(self, issue: str, doc_content: str, doc_dir: str) -> str:
        """分析问题的具体上下文"""

        # 架构图问题的上下文分析
        if "架构图不连通" in issue:
            # 分析Mermaid图中的孤立节点
            mermaid_matches = re.findall(r'```mermaid\s*\n(.*?)\n```', doc_content, re.DOTALL)
            if mermaid_matches:
                diagram = mermaid_matches[0]
                nodes = re.findall(r'(\w+)\["([^"]+)(?:<br/>([^"]+))?"\]', diagram)
                edges = re.findall(r'(\w+)\s*-->\s*(\w+)', diagram)

                # 找到孤立节点
                connected_nodes = set()
                for source, target in edges:
                    connected_nodes.add(source)
                    connected_nodes.add(target)

                isolated_nodes = []
                for node_id, display_name, _ in nodes:
                    if node_id not in connected_nodes:
                        isolated_nodes.append(f"{node_id}({display_name})")

                if isolated_nodes:
                    return f"发现{len(isolated_nodes)}个孤立组件：{', '.join(isolated_nodes[:3])}{'...' if len(isolated_nodes) > 3 else ''}，这些组件没有与其他组件建立连接关系"
                else:
                    return "架构图中存在连通性问题，但具体孤立节点需要进一步分析"
            else:
                return "文档中缺少Mermaid架构图，无法进行连通性分析"

        # 架构层次依赖违规的上下文分析
        elif "架构层次依赖违规" in issue:
            violation_match = re.search(r'(\w+)\(层次(\d+)\) -> (\w+)\(层次(\d+)\)', issue)
            if violation_match:
                source, source_level, target, target_level = violation_match.groups()
                return f"组件{source}（层次{source_level}）直接依赖了低层组件{target}（层次{target_level}），违反了分层架构原则。高层组件不应该直接依赖低层组件"
            else:
                return "在架构分层设计中发现了层次依赖违规，需要重新设计组件间的依赖关系"

        # 代码清单问题的上下文分析
        elif "代码清单" in issue:
            manifest_pattern = r'## 📋 完整代码列表.*?\n```\s*\n(.*?)\n```'
            manifest_matches = re.findall(manifest_pattern, doc_content, re.DOTALL)
            if manifest_matches:
                manifest_content = manifest_matches[0]
                lines = manifest_content.strip().split('\n')
                valid_lines = [line for line in lines if line.strip() and '|' in line and not line.startswith('#')]
                return f"代码清单包含{len(valid_lines)}行条目，但格式或内容存在问题。需要确保表格包含正确的列头和文件信息"
            else:
                return "文档中缺少完整代码列表章节，无法进行代码结构分析"

        # 性能指标问题的上下文分析
        elif "性能指标" in issue:
            performance_patterns = [
                r'启动时间[：:]\s*≤(\d+)ms',
                r'响应时间[：:]\s*≤(\d+)ms',
                r'内存占用[：:]\s*≤(\d+)MB',
                r'处理能力[：:]\s*≥(\d+)/s'
            ]
            found_metrics = []
            for pattern in performance_patterns:
                matches = re.findall(pattern, doc_content)
                if matches:
                    found_metrics.append(pattern.split('[')[0])

            if found_metrics:
                return f"文档中找到{len(found_metrics)}个性能指标：{', '.join(found_metrics)}，但数量不足或格式不规范"
            else:
                return "文档中缺少具体的性能指标定义，需要添加启动时间、响应时间、内存占用等关键指标"

        # 默认上下文
        else:
            return f"问题涉及：{issue}，需要根据具体情况进行分析和修复"

    def _find_relevant_reference_docs(self, issue: str, related_docs: List[str], doc_dir: str) -> List[str]:
        """通过扫描相关文档内容，找到真正相关的参考文档"""

        if not related_docs or not doc_dir:
            return ["01-architecture-overview.md"]  # 默认参考文档

        relevant_docs = []

        # 根据问题类型和文档内容匹配
        for doc_file in related_docs:
            doc_path = os.path.join(doc_dir, doc_file)
            if os.path.exists(doc_path):
                try:
                    with open(doc_path, 'r', encoding='utf-8') as f:
                        doc_content = f.read()

                    # 根据问题类型检查文档相关性
                    if self._is_doc_relevant_to_issue(issue, doc_content, doc_file):
                        relevant_docs.append(doc_file)

                except Exception as e:
                    logger.warning(f"读取文档{doc_file}失败: {e}")

        # 如果没有找到相关文档，返回默认文档
        if not relevant_docs:
            # 根据问题类型推荐默认文档
            if "架构" in issue:
                relevant_docs = ["01-architecture-overview.md"]
            elif "插件" in issue or "生命周期" in issue:
                relevant_docs = ["02-kernel-and-plugin-lifecycle.md"]
            elif "服务" in issue or "通信" in issue:
                relevant_docs = ["03-service-bus-and-communication.md"]
            elif "扩展" in issue or "SPI" in issue:
                relevant_docs = ["04-extension-points-and-spi.md"]
            elif "安全" in issue or "GUARDRAIL" in issue:
                relevant_docs = ["05-security-and-sandboxing.md"]
            elif "配置" in issue or "启动" in issue:
                relevant_docs = ["06-starter-and-configuration.md"]
            elif "数据库" in issue or "缓存" in issue:
                relevant_docs = ["07-use-case-db-and-cache-as-plugins.md"]
            else:
                relevant_docs = ["01-architecture-overview.md"]

        return relevant_docs[:3]  # 最多返回3个相关文档

    def _is_doc_relevant_to_issue(self, issue: str, doc_content: str, doc_file: str) -> bool:
        """判断文档是否与问题相关"""

        # 提取问题中的关键词
        issue_keywords = []

        if "架构图" in issue or "架构层次" in issue:
            issue_keywords = ["架构", "mermaid", "组件", "依赖", "层次"]
        elif "代码清单" in issue:
            issue_keywords = ["代码", "文件", "项目结构", "maven", "java"]
        elif "性能" in issue:
            issue_keywords = ["性能", "启动时间", "响应时间", "内存", "处理能力"]
        elif "GUARDRAIL" in issue:
            issue_keywords = ["安全", "护栏", "约束", "权限", "沙箱"]
        elif "CONSTRAINT" in issue:
            issue_keywords = ["约束", "强制", "要求", "规范"]
        elif "循环依赖" in issue:
            issue_keywords = ["依赖", "循环", "服务", "通信"]

        # 检查文档内容是否包含相关关键词
        doc_content_lower = doc_content.lower()
        issue_lower = issue.lower()

        # 直接匹配度
        direct_matches = sum(1 for keyword in issue_keywords if keyword in doc_content_lower)

        # 文件名匹配度
        filename_relevance = 0
        if "architecture" in doc_file and ("架构" in issue or "组件" in issue):
            filename_relevance += 2
        elif "kernel" in doc_file and ("插件" in issue or "生命周期" in issue):
            filename_relevance += 2
        elif "service-bus" in doc_file and ("服务" in issue or "通信" in issue):
            filename_relevance += 2
        elif "extension" in doc_file and ("扩展" in issue or "SPI" in issue):
            filename_relevance += 2
        elif "security" in doc_file and ("安全" in issue or "GUARDRAIL" in issue):
            filename_relevance += 2
        elif "configuration" in doc_file and ("配置" in issue or "启动" in issue):
            filename_relevance += 2
        elif "db-and-cache" in doc_file and ("数据库" in issue or "缓存" in issue):
            filename_relevance += 2

        # 综合评分
        relevance_score = direct_matches + filename_relevance

        return relevance_score >= 2  # 相关性阈值

    def _build_ai_prompt_content(self, design_doc_path: str, doc_name: str,
                               related_docs: List[str], problem_categories: Dict[str, List[Dict[str, str]]],
                               report: DocumentCompletenessReport) -> str:
        """构建AI提示词内容"""

        # 获取文档目录
        doc_dir = os.path.dirname(design_doc_path)

        prompt_content = f"""# AI提示词：{doc_name} 深度修复与架构优化

## 🎯 任务目标

你是一位**顶级架构师**，拥有20年以上的大型系统设计经验。你的任务是对设计文档进行**深度架构分析和问题修复**，像一位资深架构师审视整个系统那样，识别并解决系统的**架构约束问题**、**设计一致性问题**和**实施可行性问题**。

## 📋 待修复文档

**文档路径**: `{design_doc_path}`
**文档名称**: `{doc_name}`
**检查评分**: `{report.overall_score:.2f}/1.00`
**核心问题数**: `{report.core_issues_count}个`

## 🚨 AI负载控制与循环解决策略

### ⚠️ 认知负载管理
为了确保AI能够深度思考每个问题，**必须按类别循环解决**，每次只专注一个类别：

1. **单类别专注原则**：每次对话只处理一个问题类别
2. **深度思考要求**：每个类别需要进行架构级深度分析
3. **循环迭代策略**：完成一个类别后，再处理下一个类别
4. **验证确认机制**：每个类别修复后需要重新检查验证

### 📊 问题类别优先级排序

"""

        # 添加问题类别分析
        if problem_categories:
            priority_order = [
                "架构设计问题", "代码结构问题", "约束体系问题",
                "性能要求问题", "文档结构问题", "技术栈问题"
            ]

            prompt_content += "#### 🔥 按优先级处理的问题类别：\n\n"

            for i, category in enumerate(priority_order, 1):
                if category in problem_categories:
                    issues = problem_categories[category]
                    prompt_content += f"**{i}. {category}** ({len(issues)}个问题)\n\n"

                    for j, issue_info in enumerate(issues[:3], 1):  # 只显示前3个问题作为示例
                        prompt_content += f"   **问题{j}**: {issue_info['issue']}\n"
                        prompt_content += f"   - **问题位置**: {issue_info['location']}\n"
                        prompt_content += f"   - **问题上下文**: {issue_info['context']}\n"
                        prompt_content += f"   - **参考文档**: {', '.join(issue_info['reference_docs'])}\n\n"

                    if len(issues) > 3:
                        prompt_content += f"   - ... 还有{len(issues)-3}个问题\n\n"

        # 添加相关文档分析
        if related_docs:
            prompt_content += f"""## 📚 相关参考文档分析

### 🔍 同目录相关文档（共{len(related_docs)}个）
这些文档提供了系统的不同视角和详细设计，是解决问题的重要参考：

"""
            for doc in related_docs:
                prompt_content += f"- `{doc}` - 需要分析其设计思路和约束要求\n"

        # 添加深度思考指导
        prompt_content += f"""

## 🧠 深度思考与分析要求

### 🎯 架构师思维模式
在处理每个问题类别时，你必须：

1. **系统性思考**：
   - 站在整个系统架构的高度分析问题
   - 考虑问题之间的关联性和影响范围
   - 识别根本原因而非表面症状

2. **多维度验证**：
   - 技术可行性：解决方案是否技术上可行
   - 架构一致性：是否符合整体架构设计原则
   - 实施复杂度：实施难度和风险评估
   - 长期演进性：是否支持未来扩展和演进

3. **深度分析方法**：
   - **问题根因分析**：为什么会出现这个问题？
   - **影响范围评估**：这个问题会影响哪些其他部分？
   - **解决方案设计**：如何从架构层面彻底解决？
   - **验证标准制定**：如何验证解决方案的有效性？

### 🔍 必须深度思考的核心问题

以下问题需要**架构师级别的深度思考**，不能简单修改：

"""

        # 添加需要深度思考的问题
        deep_thinking_issues = []
        for issues in problem_categories.values():
            for issue_info in issues:
                issue = issue_info['issue']
                if any(keyword in issue for keyword in [
                    "架构层次依赖违规", "循环依赖", "架构图不连通",
                    "逻辑链断裂", "内部逻辑矛盾", "架构到代码映射断裂"
                ]):
                    deep_thinking_issues.append(issue_info)

        for i, issue_info in enumerate(deep_thinking_issues[:5], 1):  # 最多显示5个
            prompt_content += f"{i}. **{issue_info['issue']}**\n"
            prompt_content += f"   - **位置**: {issue_info['location']}\n"
            prompt_content += f"   - **上下文**: {issue_info['context']}\n"
            prompt_content += f"   - **参考文档**: {', '.join(issue_info['reference_docs'])}\n"
            prompt_content += "   - **分析要求**: 为什么会出现这种问题？根本原因是什么？\n"
            prompt_content += "   - **设计要求**: 如何从架构层面重新设计解决方案？\n"
            prompt_content += "   - **验证要求**: 如何确保修复后的一致性和有效性？\n\n"

        # 添加执行指导
        prompt_content += f"""## 🚀 执行指导与工作流程

### 📋 第一步：选择问题类别
从上述问题类别中选择**一个**进行深度分析和修复。建议按优先级顺序处理。

### 🔍 第二步：深度分析
1. **阅读相关文档**：仔细阅读目标文档和相关参考文档
2. **理解问题本质**：分析问题的根本原因和影响范围
3. **设计解决方案**：从架构角度设计彻底的解决方案
4. **制定实施计划**：明确修改步骤和验证方法

### ✏️ 第三步：精确修复
1. **保持架构一致性**：确保修改符合整体架构设计
2. **维护文档完整性**：保持文档的逻辑性和可读性
3. **遵循最佳实践**：应用行业最佳实践和设计模式
4. **确保可实施性**：修改后的设计必须可以实际实施

### ✅ 第四步：验证确认
1. **自我检查**：验证修改是否解决了目标问题
2. **一致性检查**：确保与其他部分保持一致
3. **完整性检查**：确保没有引入新的问题
4. **准备下一轮**：为处理下一个类别做准备

## 🎯 开始执行

请选择一个问题类别开始深度分析和修复。记住：
- **一次只处理一个类别**
- **必须进行深度思考**
- **确保架构级别的解决方案**
- **完成后再处理下一个类别**

---

**当前文档状态**：需要人工干预，存在关键问题
**修复目标**：提升文档质量，确保项目能够顺利实施
"""

        return prompt_content


def main():
    """主函数 - 命令行使用示例"""

    import sys
    import argparse

    parser = argparse.ArgumentParser(description='设计文档完整性检查器')
    parser.add_argument('document_path', help='设计文档路径')
    parser.add_argument('--auto-fix', action='store_true', help='自动修复简单问题（直接修改原文件）')

    # 兼容旧的命令行格式
    if len(sys.argv) == 2 and not sys.argv[1].startswith('-'):
        design_doc_path = sys.argv[1]
        auto_fix = False
    else:
        args = parser.parse_args()
        design_doc_path = args.document_path
        auto_fix = args.auto_fix

    # 创建检查器
    checker = DesignDocumentPreChecker()

    # 执行检查
    report = checker.check_document_completeness(design_doc_path, auto_fix=auto_fix)

    # 打印报告
    checker.print_report(report)

    # 如果执行了自动修复，显示提示信息
    if auto_fix and len(checker.auto_fix_actions) > 0:
        print(f"\n✅ 原文件已自动修复，共执行了{len(checker.auto_fix_actions)}个修复动作")
        print(f"📄 修复的文件: {design_doc_path}")
    elif auto_fix:
        print(f"\n💡 没有发现可自动修复的问题")

    # 返回退出码
    if report.is_ready_for_generation:
        print("\n✅ 文档检查通过，质量良好")
        print("💡 建议: 可以开始实施项目")
        sys.exit(0)
    elif report.human_intervention_required:
        print("\n🚨 需要人工干预，存在关键问题")
        print("💡 建议: 解决所有致命错误后重新检查")
        sys.exit(2)
    else:
        print("\n⚠️ 需要改进文档质量")
        print("💡 建议: 改进文档质量后重新检查")
        sys.exit(1)


if __name__ == "__main__":
    main()
