# 批量文档修改指令

## 项目概况
- **检查报告目录**: docs\features\F007-建立Commons库的治理机制-20250610\nexus-messaging-ecosystem库\v1\checkresult
- **文档总数**: 5个
- **当前平均得分**: 90.3/100
- **当前兼容性**: 96.7%
- **目标平均得分**: ≥80/100
- **目标兼容性**: ≥80%

## 需要修改的文档列表
1. **01-architecture-overview.md** - 87.8/100 (✅ 良好)
2. **02-api-design-specifications.md** - 91.9/100 (✅ 良好)
3. **02-intelligent-api-design.md** - 95.6/100 (✅ 良好)
4. **03-implementation-guide.md** - 92.7/100 (✅ 良好)
5. **04-performance-benchmarks.md** - 83.6/100 (✅ 良好)

## 批量修改策略

### 阶段1：解决关键问题（优先级最高）
处理得分<60的文档，重点解决：
- 元提示词必需信息缺失
- 标题格式不符合提取要求
- 缺少核心定位和设计哲学章节

### 阶段2：提升整体质量（优先级中等）
处理得分60-79的文档，重点改进：
- 实施约束标注不完整
- 架构蓝图描述不清晰
- 技术栈信息不明确

### 阶段3：优化细节（优先级较低）
处理得分≥80的文档，进行细节优化：
- 提升表述的准确性
- 完善架构描述
- 优化章节结构

## 修改执行原则
1. **按报告执行**：每个文档都要严格按照对应的检查报告进行修改
2. **保持一致性**：确保所有文档的格式和结构保持一致
3. **分批验证**：每修改3-5个文档后运行扫描器验证效果
4. **质量优先**：宁可少修改几个文档，也要确保质量达标

## 成功标准
- 📈 整体平均得分提升至80分以上
- 🎯 design_document_extractor.py兼容性达到80%以上
- ✅ 至少70%的文档达到"良好"等级
- 🚨 消除所有"急需修改"的文档

## 验证命令
```bash
python tools/advanced-doc-scanner.py "docs\features\F007-建立Commons库的治理机制-20250610\nexus-messaging-ecosystem库\v1"
```
