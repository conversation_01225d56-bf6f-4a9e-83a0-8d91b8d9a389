# 数据分析引擎集成使用示例

## 概述

本文档展示如何将数据分析引擎集成到现有的AI测试架构中，实现自动化的数据收集和分析。

## 集成步骤

### 1. 在AITestExecutor中集成引擎

```java
// 修改现有的AITestExecutor类
public class AITestExecutor {
    
    private final DataAnalysisEngine analysisEngine;
    
    public AITestExecutor() {
        this.analysisEngine = new DataAnalysisEngine();
    }
    
    public AITestResult executeAdaptiveTest() {
        // 现有的测试执行逻辑
        AITestResult testResult = executeTestLogic();
        
        // 新增：执行数据分析
        executeDataAnalysis(testResult);
        
        return testResult;
    }
    
    /**
     * 执行数据分析
     */
    private void executeDataAnalysis(AITestResult testResult) {
        try {
            String outputPath = "docs/features/F003-PostgreSQL迁移-20250508/test/phase3/data";
            
            // 执行完整的数据分析流程
            AnalysisExecutionSummary summary = analysisEngine.executeFullAnalysis(
                outputPath, 
                testResult  // 传入测试结果作为上下文数据
            );
            
            log.info("数据分析完成: {} 个策略执行，耗时 {}ms", 
                    summary.getExecutedStrategiesCount(), 
                    summary.getTotalExecutionTimeMs());
            
        } catch (Exception e) {
            log.error("数据分析执行失败", e);
            // 不影响主测试流程
        }
    }
}
```

### 2. 创建测试结果适配器

```java
// 适配器：将AITestResult转换为分析引擎可用的格式
public class AITestResultAdapter implements DataAdapter<AITestResult> {
    
    @Override
    public Map<String, Object> adapt(AITestResult testResult) {
        Map<String, Object> adaptedData = new HashMap<>();
        
        // 基础测试数据
        adaptedData.put("totalTests", testResult.getTotalTestCount());
        adaptedData.put("passedTests", testResult.getPassedTestCount());
        adaptedData.put("failedTests", testResult.getFailedTestCount());
        adaptedData.put("overallPassRate", testResult.getOverallPassRate());
        
        // 详细测试结果
        adaptedData.put("iterationResults", testResult.getIterationResults());
        adaptedData.put("testCaseResults", extractTestCaseResults(testResult));
        
        // AI洞察数据
        adaptedData.put("aiInsights", extractAIInsights(testResult));
        
        return adaptedData;
    }
    
    private List<Map<String, Object>> extractTestCaseResults(AITestResult testResult) {
        // 提取测试用例详细结果
        return testResult.getIterationResults().stream()
            .flatMap(iteration -> iteration.getTestCaseResults().stream())
            .map(this::convertTestCaseResult)
            .collect(Collectors.toList());
    }
    
    private Map<String, Object> convertTestCaseResult(AITestCaseResult caseResult) {
        Map<String, Object> result = new HashMap<>();
        result.put("testClass", caseResult.getTestClass());
        result.put("passed", caseResult.isPassed());
        result.put("executionTime", caseResult.getExecutionTime());
        result.put("aiInsight", caseResult.getAIInsight());
        result.put("aiRecommendation", caseResult.getAIRecommendation());
        return result;
    }
}
```

### 3. 配置分析策略

```java
// 注册PostgreSQL迁移专用分析策略
@Component
public class AnalysisStrategyRegistry {
    
    @PostConstruct
    public void registerStrategies() {
        // 策略会通过@AnalysisComponent注解自动发现
        // 这里可以进行额外的配置或验证
        
        log.info("分析策略注册完成");
    }
}
```

### 4. 使用示例

```java
// 在测试完成后调用数据分析
@Test
public void testPostgreSQLMigrationWithAnalysis() {
    // 执行AI测试
    AITestExecutor executor = new AITestExecutor();
    AITestResult result = executor.executeAdaptiveTest();
    
    // 数据分析已经在executeAdaptiveTest中自动执行
    // 检查分析结果文件
    String latestDataPath = "docs/features/F003-PostgreSQL迁移-20250508/test/phase3/data/latest";
    
    // 验证分析文件是否生成
    assertThat(Paths.get(latestDataPath + "/postgresql-migration-analysis.json")).exists();
    assertThat(Paths.get(latestDataPath + "/configuration-conflict-analysis.json")).exists();
    
    // 读取分析结果
    PostgreSQLMigrationAnalysisData analysisData = readAnalysisResult(
        latestDataPath + "/postgresql-migration-analysis.json",
        PostgreSQLMigrationAnalysisData.class
    );
    
    // 验证分析结果
    assertThat(analysisData.getConfidenceScore()).isGreaterThan(0.8);
    assertThat(analysisData.getMigrationStatus()).isNotNull();
}
```

## AI分析数据的使用

### 1. 读取分析结果

```java
// AI读取和分析数据的工具类
public class AnalysisDataReader {
    
    private final ObjectMapper jsonMapper = new ObjectMapper()
        .registerModule(new JavaTimeModule());
    
    /**
     * 读取最新的分析数据
     */
    public <T> T readLatestAnalysis(String analysisType, Class<T> dataClass) {
        String latestPath = "docs/features/F003-PostgreSQL迁移-20250508/test/phase3/data/latest";
        String fileName = analysisType + ".json";
        
        try {
            return jsonMapper.readValue(
                new File(latestPath + "/" + fileName), 
                dataClass
            );
        } catch (IOException e) {
            throw new RuntimeException("Failed to read analysis data: " + fileName, e);
        }
    }
    
    /**
     * 读取历史分析数据
     */
    public <T> List<T> readHistoricalAnalysis(String analysisType, Class<T> dataClass, int days) {
        String basePath = "docs/features/F003-PostgreSQL迁移-20250508/test/phase3/data";
        List<T> historicalData = new ArrayList<>();
        
        // 扫描最近N天的数据目录
        LocalDateTime cutoff = LocalDateTime.now().minusDays(days);
        
        try (Stream<Path> paths = Files.list(Paths.get(basePath))) {
            paths.filter(Files::isDirectory)
                 .filter(path -> isAfterCutoff(path, cutoff))
                 .forEach(path -> {
                     try {
                         String fileName = analysisType + ".json";
                         File file = path.resolve(fileName).toFile();
                         if (file.exists()) {
                             T data = jsonMapper.readValue(file, dataClass);
                             historicalData.add(data);
                         }
                     } catch (IOException e) {
                         log.warn("Failed to read historical data: " + path, e);
                     }
                 });
        } catch (IOException e) {
            throw new RuntimeException("Failed to scan historical data", e);
        }
        
        return historicalData;
    }
}
```

### 2. AI分析数据生成报告

```java
// AI基于数据生成专家级报告
public class AIExpertReportGenerator {
    
    private final AnalysisDataReader dataReader = new AnalysisDataReader();
    
    /**
     * 生成专家级分析报告
     */
    public String generateExpertReport() {
        // 读取最新分析数据
        PostgreSQLMigrationAnalysisData migrationData = dataReader.readLatestAnalysis(
            "postgresql-migration-analysis", 
            PostgreSQLMigrationAnalysisData.class
        );
        
        ConfigurationConflictAnalysisData conflictData = dataReader.readLatestAnalysis(
            "configuration-conflict-analysis",
            ConfigurationConflictAnalysisData.class
        );
        
        // AI基于数据生成报告
        StringBuilder report = new StringBuilder();
        
        report.append("# PostgreSQL迁移专家级分析报告\n\n");
        
        // 执行摘要
        report.append("## 执行摘要\n");
        report.append(String.format("- **整体通过率**: %.1f%%\n", migrationData.getOverallPassRate()));
        report.append(String.format("- **迁移状态**: %s\n", migrationData.getMigrationStatus()));
        report.append(String.format("- **置信度评分**: %.1f%%\n", migrationData.getConfidenceScore() * 100));
        
        // 关键发现
        report.append("\n## 关键发现\n");
        if (conflictData.isConflictDetected()) {
            report.append("### 🚨 配置冲突问题\n");
            report.append(String.format("- **问题类型**: %s\n", conflictData.getConflictType()));
            report.append(String.format("- **根本原因**: %s\n", conflictData.getRootCause()));
            report.append(String.format("- **解决方案置信度**: %.1f%%\n", conflictData.getSolutionConfidence() * 100));
        }
        
        // 风险评估
        report.append("\n## 风险评估\n");
        for (Risk risk : migrationData.getRiskAssessment().getRisks()) {
            report.append(String.format("### %s (%s)\n", risk.getName(), risk.getSeverity()));
            report.append(String.format("- **概率**: %.1f%%\n", risk.getProbability() * 100));
            report.append(String.format("- **描述**: %s\n", risk.getDescription()));
            report.append(String.format("- **缓解措施**: %s\n", risk.getMitigation()));
        }
        
        // 专家建议
        report.append("\n## 专家建议\n");
        for (ExpertRecommendation rec : migrationData.getExpertRecommendations()) {
            report.append(String.format("### %s (%s优先级)\n", rec.getTitle(), rec.getPriority()));
            report.append(String.format("- **描述**: %s\n", rec.getDescription()));
            report.append(String.format("- **置信度**: %.1f%%\n", rec.getConfidence() * 100));
            report.append("- **行动项**:\n");
            for (String action : rec.getActions()) {
                report.append(String.format("  - %s\n", action));
            }
        }
        
        return report.toString();
    }
}
```

## 自动化工作流

### 1. 测试完成后自动分析

```java
// 在测试监听器中自动触发分析
@Component
public class TestCompletionListener {
    
    private final DataAnalysisEngine analysisEngine;
    private final AIExpertReportGenerator reportGenerator;
    
    @EventListener
    public void onTestCompletion(TestCompletionEvent event) {
        if (event.isAITestExecution()) {
            // 执行数据分析
            executeDataAnalysis(event.getTestResult());
            
            // 生成专家报告
            generateExpertReport();
        }
    }
    
    private void executeDataAnalysis(AITestResult testResult) {
        String outputPath = "docs/features/F003-PostgreSQL迁移-20250508/test/phase3/data";
        analysisEngine.executeFullAnalysis(outputPath, testResult);
    }
    
    private void generateExpertReport() {
        String report = reportGenerator.generateExpertReport();
        String reportPath = "docs/features/F003-PostgreSQL迁移-20250508/test/phase3/reports/expert-analysis";
        
        // 保存报告
        saveReport(reportPath, report);
    }
}
```

### 2. 定期趋势分析

```java
// 定期执行趋势分析
@Component
public class TrendAnalysisScheduler {
    
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void performTrendAnalysis() {
        AnalysisDataReader reader = new AnalysisDataReader();
        
        // 读取最近7天的数据
        List<PostgreSQLMigrationAnalysisData> weeklyData = reader.readHistoricalAnalysis(
            "postgresql-migration-analysis",
            PostgreSQLMigrationAnalysisData.class,
            7
        );
        
        // 分析趋势
        TrendAnalysisResult trend = analyzeTrends(weeklyData);
        
        // 生成趋势报告
        generateTrendReport(trend);
    }
}
```

## 总结

通过这个引擎化架构，AI可以：

1. **最小改动**：只需要实现Strategy接口即可添加新分析
2. **自动执行**：测试完成后自动触发数据分析
3. **结构化数据**：获得标准化的JSON格式分析数据
4. **专家级分析**：基于数据生成深度分析报告
5. **趋势监控**：支持历史数据分析和趋势预测

这个架构实现了程序负责数据收集，AI负责数据分析的职责分离，为AI提供了强大而灵活的分析能力。
