# V4.2方案：实施计划与里程碑 (虚拟项目经理交互版)

**【注意：文档内容已过时】**
**本实施计划基于一个早期的、设想中的架构（如九宫格界面、WebSocket通信、四阶段治理流程）。当前的V4.2实现采用的是更简洁的HTTP轮询异步任务框架，其实施路径与本文档描述有较大差异。本文档仅供历史参考。**

## 1. 文档信息

- **文档版本**: V2.1
- **创建日期**: 2025-07-31
- **更新日期**: 2025-08-02
- **核心架构**: 以V1成熟的虚拟项目经理交互模式，驱动V2先进的、基于"统一语义模型与插件化"的可扩展治理引擎。
- **项目周期**: 4周 (预估)

## 2. 项目总体目标

我们的核心交付物是一个**由虚拟项目经理工作台驱动的、高度自动化的"自我验证的架构治理引擎"**。

### 2.1. 核心交付物

1.  **V2治理引擎后端 (`governance_engine`)**:
    - 一个实现了V2"四阶段治理流程"的、异步的、模块化的Python应用。
    - 能够对指定的设计文档目录进行深度分析，生成"全局知识库"和`DocumentHealthReport`。
2.  **V1风格的九宫格虚拟项目经理交互界面**:
    - 一个基于Flask和WebSocket的实时Web界面。
    - 能够触发后端治理流程，并实时、可视化地展示V2引擎的工作状态和所有核心产出物。
3.  **前后端通信契约**:
    - 一套清晰的HTTP API和WebSocket消息格式，用于前后端解耦通信。

### 2.2. 关键技术指标

- **全局一致性保证率**: > 99% (最终产出中，违反已验证全局约束的实例应接近于0)。
- **幻觉抑制率**: 最终产出中，由AI幻觉导致的不准确内容应为0。
- **界面响应**: WebSocket消息延迟 < 100ms，API响应时间 < 200ms。

## 3. 技术选型

| 领域 | 技术选型 | 理由 |
| :--- | :--- | :--- |
| **后端语言** | `Python 3.11+` | 强大的生态系统，完美支持AI和算法任务。 |
| **后端Web框架** | `Flask`, `Flask-SocketIO` | 轻量、灵活，易于与现有`tools/ace/`工程集成。 |
| **核心数据结构** | `dataclasses` | 用于精确实现V2的`AtomicConstraint`等核心数据模型。 |
| **图算法** | `NetworkX` | 用于“全局知识库”的可视化和“阶段四”的整体性审计。 |
| **前端** | 原生 `JavaScript (ES6+)`, `CSS Grid` | 无需重型框架，轻量、高效，满足九宫格界面的所有需求。 |
| **AI模型交互** | (复用ace现有资产) `APIManagerAdapter` | 与现有工程保持一致。 |

## 4. 四周实施计划 (施工蓝图)

我们将严格按照此计划，以周为单位进行迭代开发。

- **第一周：搭建核心服务与项目经理基础架构 (部分完成)**
    - **任务清单**:
        1.  [x] 严格按照V4.2.2架构图，创建`project_manager_v2`下的`services`目录结构。
        2.  [x] 在`project_manager_v2/services/`下创建`project_manager_service.py`，并实现了`get_or_create_manager`方法，将目录验证逻辑内聚。
        3.  [ ] 在`project_manager_v2/manager/`下创建`project_manager.py`，定义`ProjectManager`类的基本结构。(下一步)
        4.  [ ] 在`web_interface/app.py`中完成对`ProjectManagerService`的初始化。
- **第二周：实现项目经理与核心治理逻辑 (计划调整)**
    - **任务清单**:
        1.  完善`project_manager.py`中驱动治理蓝图顺序执行的核心逻辑。
        2.  开发第一个核心治理蓝图`bp_stage_0_standardize.py`，完成文档标准化与预验证的逻辑。
        3.  为`ProjectManager`和`bp_stage_0`编写单元测试。
- **第三周：实现剩余治理蓝图与端到端流程**
    - **任务清单**:
        1.  开发所有剩余的治理蓝图 (`bp_stage_1`, `bp_stage_2`等)。
        2.  打通从前端API请求 -> `ProjectManagerService` -> `ProjectManager` -> 各个`GovernanceBlueprint`执行的完整流程。
        3.  在`project_manager_service.py`中，完善`get_or_create_manager`方法，确保返回功能完整的`ProjectManager`实例。
- **第四周：集成、端到端测试与优化**
    - **任务清单**:
        1.  打通从前端API请求 -> `ProjectManagerService` -> `ProjectManager` -> 各个`GovernanceBlueprint`执行的完整流程。
        2.  实现完整的WebSocket进度推送逻辑。
        3.  进行全面的端到端测试，修复bug并优化性能。

## 5. 风险管理

| 风险项 | 概率 | 影响 | 缓解措施 |
| :--- | :--- | :--- | :--- |
| **AI分析质量不可控** | 中 | 高 | **V2核心设计缓解**: V2的“预验证”和“契约审计”机制本身就是为了降低此风险。同时，实施计划将真实AI逻辑的集成放在最后，前期使用模拟数据，确保框架稳定。 |
| **前后端WebSocket通信复杂** | 中 | 中 | **简化设计缓解**: 采用简单的JSON消息格式。每周都有明确的联调目标，逐步增加通信的复杂度，避免问题积压。 |
| **知识库可视化性能问题** | 低 | 中 | **技术选型缓解**: 初期使用轻量级的Mermaid.js。如果节点过多（>200），再考虑切换到性能更好的D3.js，并采用虚拟化渲染技术。 |
