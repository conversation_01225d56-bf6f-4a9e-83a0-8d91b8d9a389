# V3.1生成器测试策略详细方案

## 文档信息
- **文档ID**: T001-V3.1-TESTING-STRATEGY-DETAILED
- **创建日期**: 2025-06-14
- **版本**: v3.1
- **目标**: 补充详细的测试策略，确保V3.1生成器质量
- **覆盖范围**: 单元测试、集成测试、性能测试、端到端测试

## 1. 单元测试策略

### 1.1 AI负载计算器测试 (JsonLoadCalculator)

#### 测试用例设计
```python
class TestJsonLoadCalculator(unittest.TestCase):
    """AI负载计算器单元测试"""
    
    def setUp(self):
        """测试初始化"""
        self.calculator = JsonLoadCalculator()
        self.sample_json = self._load_sample_json()
    
    def test_cognitive_complexity_calculation(self):
        """测试认知复杂度计算"""
        # 测试用例1: 简单接口 (预期: ≤0.3)
        simple_json = {
            "interface_system": {
                "core_interfaces": [{"name": "SimpleInterface", "methods": ["method1"]}]
            }
        }
        complexity = self.calculator.calculate_cognitive_complexity(simple_json)
        self.assertLessEqual(complexity, 0.3)
        
        # 测试用例2: 复杂接口 (预期: 0.3-0.7)
        complex_json = {
            "interface_system": {
                "core_interfaces": [
                    {"name": "ComplexInterface", "methods": ["method1", "method2", "method3"]},
                    {"name": "AbstractInterface", "methods": ["abstractMethod"]}
                ]
            }
        }
        complexity = self.calculator.calculate_cognitive_complexity(complex_json)
        self.assertGreater(complexity, 0.3)
        self.assertLessEqual(complexity, 0.7)
        
        # 测试用例3: 超复杂接口 (预期: >0.7)
        super_complex_json = {
            "interface_system": {
                "core_interfaces": [
                    {"name": f"Interface{i}", "methods": [f"method{j}" for j in range(5)]}
                    for i in range(10)
                ]
            }
        }
        complexity = self.calculator.calculate_cognitive_complexity(super_complex_json)
        self.assertGreater(complexity, 0.7)
    
    def test_memory_pressure_calculation(self):
        """测试记忆边界压力计算"""
        # 测试用例1: 低压力 (预期: ≤0.3)
        low_pressure_json = {
            "code_blocks": {"total_lines": 200},
            "concepts": {"count": 10},
            "files": {"count": 5}
        }
        pressure = self.calculator.calculate_memory_pressure(low_pressure_json)
        self.assertLessEqual(pressure, 0.3)
        
        # 测试用例2: 高压力 (预期: >0.6)
        high_pressure_json = {
            "code_blocks": {"total_lines": 900},
            "concepts": {"count": 60},
            "files": {"count": 25}
        }
        pressure = self.calculator.calculate_memory_pressure(high_pressure_json)
        self.assertGreater(pressure, 0.6)
    
    def test_hallucination_risk_calculation(self):
        """测试幻觉风险系数计算"""
        # 测试用例1: 低风险 (预期: ≤0.15)
        low_risk_json = {
            "abstract_concepts": [],
            "undefined_references": [],
            "complex_configurations": []
        }
        risk = self.calculator.calculate_hallucination_risk(low_risk_json)
        self.assertLessEqual(risk, 0.15)
        
        # 测试用例2: 高风险 (预期: >0.3)
        high_risk_json = {
            "abstract_concepts": [f"concept{i}" for i in range(15)],
            "undefined_references": [f"ref{i}" for i in range(8)],
            "complex_configurations": [f"config{i}" for i in range(20)]
        }
        risk = self.calculator.calculate_hallucination_risk(high_risk_json)
        self.assertGreater(risk, 0.3)
```

### 1.2 代码占位符生成器测试 (CodePlaceholderGenerator)

#### 测试用例设计
```python
class TestCodePlaceholderGenerator(unittest.TestCase):
    """代码占位符生成器单元测试"""
    
    def test_interface_placeholder_generation(self):
        """测试接口占位符生成"""
        generator = CodePlaceholderGenerator(self.sample_json, self.sample_metrics)
        
        interface_info = {
            "name": "TestInterface",
            "package": "com.test",
            "methods": ["testMethod"]
        }
        
        placeholder = generator.generate_interface_placeholder(interface_info)
        
        # 验证占位符包含必要元素
        self.assertIn("【AI代码填充区域】", placeholder)
        self.assertIn("JSON约束引用", placeholder)
        self.assertIn("AI质量约束", placeholder)
        self.assertIn("验证锚点", placeholder)
        self.assertIn("{{AI_FILL_REQUIRED}}", placeholder)
    
    def test_class_placeholder_generation(self):
        """测试类占位符生成"""
        generator = CodePlaceholderGenerator(self.sample_json, self.sample_metrics)
        
        class_info = {
            "name": "TestClass",
            "package": "com.test",
            "interfaces": ["TestInterface"]
        }
        
        placeholder = generator.generate_class_placeholder(class_info)
        
        # 验证占位符格式正确
        self.assertIn("```java", placeholder)
        self.assertIn("```", placeholder)
        self.assertIn("验证命令", placeholder)
        self.assertIn("成功标准", placeholder)
```

### 1.3 DRY引用引擎测试 (DryReferenceEngine)

#### 测试用例设计
```python
class TestDryReferenceEngine(unittest.TestCase):
    """DRY引用引擎单元测试"""
    
    def test_reference_detection(self):
        """测试引用检测"""
        engine = DryReferenceEngine(self.sample_json)
        
        # 测试重复内容检测
        duplicates = engine.detect_duplicate_content()
        self.assertIsInstance(duplicates, list)
        
        # 测试引用映射生成
        references = engine.generate_reference_map()
        self.assertIsInstance(references, dict)
    
    def test_json_reference_generation(self):
        """测试JSON引用生成"""
        engine = DryReferenceEngine(self.sample_json)
        
        reference = engine.generate_json_reference("interface_system.core_interfaces[0]")
        self.assertIn("@", reference)
        self.assertIn("→", reference)
```

## 2. 集成测试策略

### 2.1 端到端流程测试

#### 测试场景设计
```python
class TestV3GeneratorIntegration(unittest.TestCase):
    """V3生成器集成测试"""
    
    def test_complete_generation_flow(self):
        """测试完整生成流程"""
        # 1. 准备测试数据
        json_file_path = "test_data/sample_design_analysis.json"
        
        # 2. 初始化生成器
        generator = V3JsonEnhancedGenerator()
        
        # 3. 执行生成流程
        result = generator.generate_implementation_plan(json_file_path)
        
        # 4. 验证生成结果
        self.assertIsNotNone(result)
        self.assertIn("implementation_plan", result)
        self.assertIn("quality_metrics", result)
        
        # 5. 验证质量指标
        metrics = result["quality_metrics"]
        self.assertLessEqual(metrics["cognitive_complexity"], 0.7)
        self.assertLessEqual(metrics["memory_pressure"], 0.6)
        self.assertLessEqual(metrics["hallucination_risk"], 0.3)
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        generator = V3JsonEnhancedGenerator()
        
        # 测试无效JSON处理
        with self.assertRaises(JSONParsingError):
            generator.generate_implementation_plan("invalid_json_file.json")
        
        # 测试边界违规处理
        large_json = self._create_large_json()
        result = generator.generate_implementation_plan(large_json)
        self.assertTrue(result["boundary_violations_handled"])
```

### 2.2 模块协作测试

#### 测试用例设计
```python
class TestModuleCollaboration(unittest.TestCase):
    """模块协作测试"""
    
    def test_calculator_generator_collaboration(self):
        """测试计算器与生成器协作"""
        calculator = JsonLoadCalculator()
        generator = CodePlaceholderGenerator(self.sample_json, None)
        
        # 计算负载指标
        metrics = calculator.calculate_load_metrics(self.sample_json)
        
        # 使用指标生成占位符
        generator.load_metrics = metrics
        placeholder = generator.generate_interface_placeholder(self.sample_interface)
        
        # 验证协作结果
        self.assertIn(str(metrics.cognitive_complexity), placeholder)
        self.assertIn(str(metrics.memory_pressure), placeholder)
        self.assertIn(str(metrics.hallucination_risk), placeholder)
```

## 3. 性能测试策略

### 3.1 性能基准测试

#### 测试指标定义
```python
class TestPerformanceBenchmarks(unittest.TestCase):
    """性能基准测试"""
    
    def test_load_calculation_performance(self):
        """测试负载计算性能"""
        calculator = JsonLoadCalculator()
        
        # 性能测试数据
        large_json = self._create_large_json(interfaces=100, methods=500)
        
        # 执行性能测试
        start_time = time.time()
        metrics = calculator.calculate_load_metrics(large_json)
        end_time = time.time()
        
        # 验证性能指标
        execution_time = end_time - start_time
        self.assertLess(execution_time, 5.0)  # 5秒内完成
        
        # 验证内存使用
        memory_usage = self._measure_memory_usage()
        self.assertLess(memory_usage, 100)  # 100MB内存限制
    
    def test_generation_performance(self):
        """测试生成性能"""
        generator = V3JsonEnhancedGenerator()
        
        # 批量生成测试
        json_files = [f"test_data/sample_{i}.json" for i in range(10)]
        
        start_time = time.time()
        results = [generator.generate_implementation_plan(f) for f in json_files]
        end_time = time.time()
        
        # 验证批量处理性能
        avg_time_per_file = (end_time - start_time) / len(json_files)
        self.assertLess(avg_time_per_file, 30.0)  # 平均30秒内完成单个文件
```

### 3.2 并发性能测试

#### 测试用例设计
```python
class TestConcurrentPerformance(unittest.TestCase):
    """并发性能测试"""
    
    def test_concurrent_generation(self):
        """测试并发生成"""
        generator = V3JsonEnhancedGenerator()
        
        # 并发测试数据
        json_files = [f"test_data/concurrent_{i}.json" for i in range(5)]
        
        # 执行并发测试
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = [
                executor.submit(generator.generate_implementation_plan, f)
                for f in json_files
            ]
            results = [future.result() for future in futures]
        
        # 验证并发结果
        self.assertEqual(len(results), len(json_files))
        for result in results:
            self.assertIsNotNone(result)
            self.assertIn("implementation_plan", result)
```

## 4. 端到端测试策略

### 4.1 真实场景测试

#### 测试场景定义
```python
class TestRealWorldScenarios(unittest.TestCase):
    """真实场景端到端测试"""
    
    def test_nexus_plugin_scenario(self):
        """测试Nexus插件场景"""
        # 使用真实的Nexus设计文档JSON
        nexus_json = "test_data/nexus_design_analysis_complete.json"
        
        generator = V3JsonEnhancedGenerator()
        result = generator.generate_implementation_plan(nexus_json)
        
        # 验证Nexus特定要求
        plan = result["implementation_plan"]
        self.assertIn("Plugin接口", plan)
        self.assertIn("ServiceBus", plan)
        self.assertIn("微内核", plan)
        self.assertIn("Virtual Threads", plan)
    
    def test_commons_library_scenario(self):
        """测试Commons库场景"""
        # 使用真实的Commons库设计文档JSON
        commons_json = "test_data/commons_design_analysis_complete.json"
        
        generator = V3JsonEnhancedGenerator()
        result = generator.generate_implementation_plan(commons_json)
        
        # 验证Commons库特定要求
        plan = result["implementation_plan"]
        self.assertIn("异常处理", plan)
        self.assertIn("工具类", plan)
        self.assertIn("配置管理", plan)
```

## 5. 测试数据管理

### 5.1 测试数据准备

#### 测试数据结构
```
tests/
├── data/
│   ├── sample_design_analysis_complete.json     # 标准测试JSON
│   ├── large_design_analysis.json               # 大型项目测试JSON
│   ├── simple_design_analysis.json              # 简单项目测试JSON
│   ├── invalid_design_analysis.json             # 无效JSON测试
│   └── edge_cases/                              # 边界情况测试数据
│       ├── empty_interfaces.json
│       ├── circular_dependencies.json
│       └── missing_required_fields.json
├── fixtures/
│   ├── expected_outputs/                        # 预期输出
│   └── baseline_metrics/                        # 基准指标
└── utils/
    ├── test_data_generator.py                   # 测试数据生成器
    └── test_helpers.py                          # 测试辅助工具
```

### 5.2 测试环境配置

#### 环境要求
- Python 3.9+
- 内存: 最少4GB，推荐8GB
- 磁盘空间: 最少1GB测试数据
- 网络: 无需外网连接（离线测试）

## 6. 测试执行计划

### 6.1 测试阶段划分

#### 阶段1: 单元测试 (预计2天)
- AI负载计算器测试
- 代码占位符生成器测试
- DRY引用引擎测试
- 质量验证器测试

#### 阶段2: 集成测试 (预计1天)
- 模块协作测试
- 端到端流程测试
- 错误处理集成测试

#### 阶段3: 性能测试 (预计1天)
- 性能基准测试
- 并发性能测试
- 内存使用测试

#### 阶段4: 端到端测试 (预计1天)
- 真实场景测试
- 用户验收测试
- 回归测试

### 6.2 成功标准

#### 质量指标
- **单元测试覆盖率**: ≥95%
- **集成测试通过率**: 100%
- **性能测试达标率**: ≥90%
- **端到端测试成功率**: 100%

#### 性能指标
- **单文件生成时间**: ≤30秒
- **内存使用**: ≤100MB
- **并发处理能力**: 支持3个并发任务
- **错误恢复率**: ≥85%
