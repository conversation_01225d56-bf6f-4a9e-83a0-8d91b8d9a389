# 阶段2: 依赖更新计划

**执行时间**: 基于AI认知单元，非时间驱动
**前置条件**: 阶段1验收通过，AI认知约束激活
**执行模式**: AI护栏保护下的原子操作

## 🚨 实施范围边界（必读）

### 📋 包含范围（AI允许操作）
- ✅ 修改2个项目的pom.xml依赖配置
- ✅ 更新3个Java文件的import语句
- ✅ 执行编译验证和功能测试
- ✅ 全局搜索和清理遗漏引用

### 🚫 排除范围（AI禁止操作）
- ❌ 修改任何业务逻辑代码
- ❌ 改变异常处理的核心机制
- ❌ 修改Spring Boot配置
- ❌ 改变项目的版本号

### 🔍 认知负载控制
- **单次操作**: ≤1个项目，≤3个文件修改
- **概念数量**: ≤3个相关概念（依赖、包名、编译）
- **依赖层级**: ≤2层依赖关系
- **验证频率**: 每个项目修改后立即验证

## 🎯 阶段目标

更新所有依赖项目的异常处理模块引用，确保从旧模块平滑迁移到新模块。

## 🧠 AI认知约束激活

### 强制激活命令
```bash
@L1:global-constraints                    # 全局约束和命名规范
@L1:ai-implementation-design-principles  # AI实施设计原则
@L3:dependency-index:core-dependencies   # 核心依赖关系
@L2:project-groups                       # 项目分组信息
@AI_COGNITIVE_CONSTRAINTS                # AI认知约束激活
@BOUNDARY_GUARD_ACTIVATION              # 边界护栏激活
@HALLUCINATION_PREVENTION               # 幻觉防护激活
@MEMORY_BOUNDARY_CHECK                  # 记忆边界检查
@ATOMIC_OPERATION_VALIDATION            # 原子操作验证
```

### 🛡️ 护栏机制检查
- **依赖验证**: 每个依赖修改都必须验证存在性
- **编译验证**: 每个项目修改后立即编译验证
- **功能验证**: 确保异常处理功能完全正常
- **回滚准备**: 每个操作都有明确的回滚方案

### 📊 依赖项目分析
基于前期分析，需要更新的项目：
- **xkongcloud-service-center**: 低复杂度，主要是pom.xml修改
- **xkongcloud-business-internal-core**: 中等复杂度，需要修改import语句

## 📋 详细执行步骤

### 步骤2.1: 更新xkongcloud-service-center（原子操作）

#### 🧠 认知负载控制
- **操作范围**: 1个项目，1个文件修改
- **概念数量**: 2个（依赖配置、编译验证）
- **验证点**: 立即编译验证

#### 2.1.1 查看现有依赖（幻觉防护）
```bash
# 先查看现有配置，防止AI幻觉
view工具查看: xkongcloud-service-center\pom.xml
搜索关键字: "xkongcloud-common-exception"
```

#### 2.1.2 修改pom.xml依赖（单一操作）
**文件路径**: `xkongcloud-service-center\pom.xml`
**🤖 AI执行指令**:
```bash
使用str-replace-editor工具修改依赖
原内容: <artifactId>xkongcloud-common-exception</artifactId>
新内容: <artifactId>xkongcloud-commons-exception</artifactId>
```

#### 2.1.3 立即验证（强制执行）
```bash
# 编译验证（项目根目录执行）
mvn clean compile -pl xkongcloud-service-center
# 预期结果: BUILD SUCCESS

# 依赖验证
mvn dependency:tree -pl xkongcloud-service-center | grep "commons-exception"
# 预期结果: 显示新的commons-exception依赖
```

**🔄 回滚方案**: 恢复原有的pom.xml配置

### 步骤2.2: 更新xkongcloud-business-internal-core

#### 2.2.1 修改pom.xml依赖
**文件路径（绝对路径）**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-business-internal-core\pom.xml`

**修改内容**:
```xml
<!-- 原依赖 -->
<dependency>
    <groupId>org.xkong.cloud</groupId>
    <artifactId>xkongcloud-common-exception</artifactId>
    <version>${project.version}</version>
</dependency>

<!-- 新依赖 -->
<dependency>
    <groupId>org.xkong.cloud</groupId>
    <artifactId>xkongcloud-commons-exception</artifactId>
    <version>${project.version}</version>
</dependency>
```

#### 2.2.2 更新Java文件import语句
**需要修改的文件（绝对路径）**:

1. **CustomGlobalExceptionHandler.java**
   - 文件路径: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-business-internal-core\src\main\java\org\xkong\cloud\business\internal\core\exception\CustomGlobalExceptionHandler.java`
```java
// 原import
import org.xkong.cloud.common.exception.GlobalExceptionHandler;

// 新import
import org.xkong.cloud.commons.exception.GlobalExceptionHandler;
```

2. **KVParamBusinessException.java**
   - 文件路径: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-business-internal-core\src\main\java\org\xkong\cloud\business\internal\core\exception\KVParamBusinessException.java`
```java
// 原import
import org.xkong.cloud.common.model.ErrorCodes;

// 新import
import org.xkong.cloud.commons.model.ErrorCodes;
```

3. **KVParamSystemException.java**
   - 文件路径: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-business-internal-core\src\main\java\org\xkong\cloud\business\internal\core\exception\KVParamSystemException.java`
```java
// 原import
import org.xkong.cloud.common.model.ErrorCodes;

// 新import
import org.xkong.cloud.commons.model.ErrorCodes;
```

#### 2.2.3 验证business-internal-core编译
**验证命令**:
```bash
cd xkongcloud-business-internal-core
mvn clean compile
```

**预期结果**: 编译成功，无错误

### 步骤2.3: 检查其他潜在依赖

#### 2.3.1 全局搜索依赖引用
**搜索命令**:
```bash
# 搜索pom.xml中的依赖引用
grep -r "xkongcloud-common-exception" . --include="*.xml"

# 搜索Java代码中的import引用  
grep -r "org.xkong.cloud.common.exception" . --include="*.java"
grep -r "org.xkong.cloud.common.model" . --include="*.java"
```

#### 2.3.2 更新发现的其他引用
**处理原则**:
- 所有pom.xml中的artifactId都要更新
- 所有Java代码中的包名都要更新
- 配置文件中的类名引用也要更新

### 步骤2.4: 运行集成测试

#### 2.4.1 编译整个项目
**验证命令**:
```bash
# 在根目录执行
mvn clean compile -DskipTests
```

#### 2.4.2 运行关键测试
**测试重点**:
- 异常处理功能测试
- Spring自动配置测试
- 业务逻辑集成测试

## ✅ 阶段2验收标准

### 🔍 AI认知护栏验证
```bash
# 引用checklist-templates标准验证
@checklist-templates:ai_memory_guardrail_system
@checklist-templates:standardized_verification_commands
@checklist-templates:ai_hallucination_prevention
@checklist-templates:temporary_code_management
```

### 📋 编译验证（强制执行）
```bash
# 全项目编译验证
mvn clean compile -DskipTests
# 预期结果: BUILD SUCCESS

# 依赖树验证
mvn dependency:tree | grep "commons-exception"
# 预期结果: 显示新的commons-exception依赖

# 旧依赖清理验证
grep -r "xkongcloud-common-exception" . --include="*.xml"
# 预期结果: 无匹配（确保旧依赖已清理）
```

### 🧪 功能验证
- [ ] 异常处理功能正常
- [ ] 全局异常处理器工作正常
- [ ] Spring自动配置机制正常
- [ ] 所有import语句正确更新

### 🛡️ 护栏验证
- [ ] 认知负载≤3个概念
- [ ] 边界范围严格遵守
- [ ] 所有操作都有回滚方案
- [ ] 幻觉防护验证通过

## 🚨 AI执行错误防护

### 常见错误模式及防护
1. **遗漏的import**: 每个文件修改后立即验证编译
2. **版本不一致**: 使用view工具查看现有配置，避免幻觉
3. **路径错误**: 使用相对路径，避免绝对路径错误
4. **依赖冲突**: 每个依赖修改后立即验证依赖树

### 🔧 错误恢复机制
```bash
# 编译失败恢复
1. 检查import语句是否完全更新
2. 验证pom.xml依赖是否正确
3. 清理Maven缓存: mvn clean
4. 必要时回滚到上一个稳定状态

# 运行时错误恢复
1. 检查Spring自动配置是否正确
2. 验证META-INF/spring.factories文件
3. 检查类路径中是否有冲突
```

## ➡️ 下一步

**阶段2完成检查清单**:
- [ ] 所有验证命令执行通过
- [ ] 护栏机制验证完成
- [ ] 回滚方案确认可用
- [ ] 继续执行 `04-阶段3-测试清理计划.md`
