# F007 Cache库-核心抽象层设计

## 文档元数据

- **文档ID**: `F007-CACHE-CORE-ABSTRACTION-DESIGN-002`
- **版本**: `V1.0`
- **模块**: `commons-cache-api`
- **技术栈**: Java 21, Spring Boot 3.4.5, Caffeine 3.1
- **复杂度等级**: L2

## 核心定位

核心抽象层是整个xkongcloud-commons-cache库的API契约中心，定义了缓存操作的统一接口、注解和异常体系。它是连接应用层与实现层的桥梁，确保缓存操作的标准化和类型安全。

## 设计哲学

本模块遵循以下设计哲学：
1. **接口分离原则**: 针对不同缓存场景（本地、远程、混合）提供专门的接口
2. **最小依赖**: 仅依赖JDK和必要的Spring注解，保持轻量级
3. **类型安全**: 通过泛型确保编译时类型检查
4. **扩展友好**: 接口设计为未来功能扩展预留空间

## 包含范围

**核心功能模块**：
- 缓存操作模板接口（CacheTemplate系列）
- 注解驱动缓存（@XkCacheable、@XkCacheEvict、@XkCachePut）
- 统一异常体系
- 缓存统计和监控接口

## 排除范围

**不包含功能**：
- 具体的缓存实现逻辑
- Spring Boot自动配置
- 监控指标收集的具体实现
- 缓存序列化机制

## 1. 核心职责

`commons-cache-api` 是整个缓存库的"契约"层，它定义了所有外部交互的接口、注解和数据模型，确保了上层应用与底层实现之间的完全解耦。

## 2. 核心接口设计

### 2.1 `CacheTemplate<K, V>`

这是最基础的缓存操作模板接口，定义了所有缓存实现都必须遵循的原子操作。

```java
// package org.xkong.cloud.commons.cache.api;

public interface CacheTemplate<K, V> {
    Optional<V> get(K key);
    void put(K key, V value, Duration ttl);
    void evict(K key);
    // ... 其他基础操作
}
```

### 2.2 `LocalCacheTemplate<K, V>`

专用于本地缓存的模板接口，继承基础模板，未来可添加本地缓存特有的操作（如统计信息）。

```java
// package org.xkong.cloud.commons.cache.api;

public interface LocalCacheTemplate<K, V> extends CacheTemplate<K, V> {
    // 可选：暴露Caffeine特有的统计API
    CacheStats getStats();
}
```

### 2.3 `ValkeyCacheTemplate`

为Valkey设计的强大模板接口，除了基础CRUD，还暴露了Valkey的高级数据结构和特性。

```java
// package org.xkong.cloud.commons.cache.api;

public interface ValkeyCacheTemplate extends CacheTemplate<String, Object> {
    // 高级数据结构操作
    HyperLogLogOperations<String, String> hyperLogLog();
    JsonOperations<String> json();
    StreamOperations<String, String, Object> streams();

    // 高性能操作
    List<Object> executePipelined(Consumer<ValkeyAsyncCommands<String, String>> commands);
}
```

## 3. 核心注解设计

注解是实现"易用性"的关键，我们复用Spring Cache的设计思想，并进行增强。

```java
// package org.xkong.cloud.commons.cache.api.annotation;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface XkCacheable {
    String cacheName();
    String key() default ""; // SpEL表达式
    String ttl() default ""; // 默认从配置读取
    boolean useL1Cache() default true; // 关键：是否启用L1本地缓存
}

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface XkCacheEvict {
    String cacheName();
    String key(); // SpEL表达式
}

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface XkCachePut {
    String cacheName();
    String key(); // SpEL表达式
    String ttl() default "";
}
```

## 4. 异常体系

定义统一的、可识别的异常体系，便于上层应用进行捕获和处理。

```java
// package org.xkong.cloud.commons.cache.api.exception;

// 基础运行时异常
public class CacheException extends RuntimeException { ... }

// 缓存访问异常（如网络超时）
public class CacheAccessException extends CacheException { ... }

// 配置错误异常
public class CacheConfigurationException extends CacheException { ... }
```

## 5. 分层架构设计

### 5.1 层次划分

核心抽象层采用严格的分层架构，确保职责清晰和依赖方向正确：

```mermaid
graph TD
    A[应用层] --> B[注解层]
    B --> C[接口层]
    C --> D[异常层]
    
    B1[XkCacheable/XkCacheEvict] --> C1[CacheTemplate]
    C1 --> D1[CacheException]
```

### 5.2 职责定义

- **注解层**: 提供声明式缓存注解，简化业务代码
- **接口层**: 定义统一的缓存操作契约，支持不同实现
- **异常层**: 提供标准化的异常处理机制

### 5.3 依赖方向

严格遵循自上而下的依赖方向：
- 注解层依赖接口层的类型定义
- 接口层独立于具体实现
- 异常层为最底层，被所有其他层使用

## 6. 门面模式设计

### 6.1 统一接口定义

CacheTemplate作为门面接口，为不同缓存实现提供统一的访问入口：

```java
// 统一的门面接口
public interface CacheTemplate<K, V> {
    // 基础操作
    Optional<V> get(K key);
    void put(K key, V value, Duration ttl);
    void evict(K key);
    
    // 批量操作
    Map<K, V> multiGet(Collection<K> keys);
    void multiPut(Map<K, V> keyValues, Duration ttl);
    void multiEvict(Collection<K> keys);
    
    // 统计信息
    CacheStats getStats();
}
```

### 6.2 子系统封装

通过继承和特化，为不同缓存场景提供专门的接口：
- LocalCacheTemplate: 封装本地缓存特性
- ValkeyCacheTemplate: 封装远程缓存和高级数据结构

### 6.3 客户端简化

客户端只需要依赖抽象接口，无需了解具体实现细节：

```java
@Service
public class UserService {
    private final CacheTemplate<String, User> userCache;
    
    // 客户端代码与具体实现解耦
    public User getUser(String id) {
        return userCache.get(id).orElseGet(() -> loadFromDatabase(id));
    }
}
```

## 7. 技术选型逻辑

### 7.1 Spring Boot 3.4.5+ 选择理由

- **注解处理**: 提供完整的@ConfigurationProperties支持
- **自动配置**: 新的条件注解和自动配置机制
- **观测性**: 原生支持Micrometer和Observation API

### 7.2 Java 21+ 选择理由

- **Record类**: 简化数据传输对象的定义
- **Pattern Matching**: 优化异常处理和类型判断
- **虚拟线程**: 为异步缓存操作提供基础支持

### 7.3 版本选择策略

- **Spring Boot**: 3.4.5+ (支持最新观测性特性)
- **Spring Framework**: 6.1.0+ (与Spring Boot 3.4.5匹配)
- **Caffeine**: 3.1.8+ (最新稳定版，性能优化)
- **SLF4J**: 2.0.9+ (与Spring Boot 3.4兼容)

## 8. 配置参数设计

### 8.1 注解配置参数

```java
@XkCacheable(
    cacheName = "userCache",           // 缓存名称
    key = "#user.id",                 // SpEL表达式定义key
    ttl = "PT10M",                    // ISO-8601 Duration格式
    condition = "#user.active",       // 缓存条件
    unless = "#result == null"        // 排除条件
)
```

### 8.2 TTL格式规范

支持多种时间格式：
- ISO-8601: "PT10M", "PT1H", "P1D"
- 简化格式: "10m", "1h", "1d"
- 毫秒数: "600000"

### 8.3 SpEL表达式支持

- 方法参数: `#paramName`
- 返回值: `#result`
- 根对象: `#root`
- Spring上下文: `@beanName`

## 实施约束

### 强制性技术要求
- **Java版本**: 必须使用Java 21+，确保Record和Pattern Matching等现代语法可用
- **Spring版本**: 必须使用Spring Boot 3.4.5+，确保注解处理的兼容性
- **依赖隔离**: 本模块只能依赖JDK标准库和Spring注解，不得引入第三方缓存实现

### 设计约束
- **接口稳定性**: 所有public接口一旦发布，必须保持向后兼容
- **泛型类型安全**: 所有模板接口必须使用泛型确保类型安全
- **异常处理**: 所有异常必须继承自CacheException，提供统一的异常层次

### 性能指标要求
- **接口调用开销**: 接口层调用开销≤1μs
- **内存占用**: 接口定义和注解处理的内存开销≤1MB

### 兼容性要求
- **JDK兼容**: 支持Java 21+的所有LTS版本
- **Spring兼容**: 与Spring Boot 3.4.5+完全兼容
- **向后兼容**: 在同一主版本内保证API向后兼容

### 约束违规后果
- **版本不兼容**: 编译失败，无法构建项目
- **接口破坏**: 导致依赖项目的编译或运行时错误
- **性能不达标**: 影响整体缓存性能表现

### 验证锚点
- **编译验证**: `mvn clean compile -P api-only`
- **兼容性测试**: `mvn test -Dtest=ApiCompatibilityTest`
- **接口稳定性验证**: `mvn verify -P api-stability-check`
