---
title: PostgreSQL演进架构开发模式指南
document_id: F003-GUIDE-001
document_type: 指南文档
category: 数据库迁移
scope: XKC-CORE
keywords: [PostgreSQL, 演进架构, 开发模式, 服务抽象层, 配置驱动, 数据库表重置, DDL自动生成, 数据初始化, 分布式ID, xkongcloud-commons-uid, 加密]
created_date: 2025-05-09
updated_date: 2025-01-15
status: 已批准
version: 2.0
authors: [系统架构组, AI助手]
affected_features:
  - F003
  - F004
related_docs:
  - ../design/migration-design.md
  - ../design/postgresql-evolution-architecture-integration.md
  - ../plan/implementation-plan.md
  - ../../../common/architecture/patterns/postgresql-evolution-implementation-guide.md
  - ../../../common/middleware/postgresql/integration-guide.md
  - ../../../features/F004-CommonsUidLibrary-20250511/design/commons-uid-library-design.md
  - ../../../features/F004-CommonsUidLibrary-20250511/design/instance-id-encryption-design.md
  - ../../../features/F004-CommonsUidLibrary-20250511/plan/implementation-plan.md
---

# PostgreSQL演进架构开发模式指南

## 概述

本文档提供了在开发环境中使用支持持续演进架构的PostgreSQL的指南。该指南涵盖了演进架构的配置和使用、开发模式下的数据库表自动重置功能，以及如何集成xkongcloud-commons-uid公共库。通过演进架构设计，系统能够从单体架构平滑演进到微服务架构，同时保持业务代码的稳定性。

演进架构的核心特性包括：
- **服务抽象层**：支持本地和远程服务的透明切换
- **配置驱动机制**：通过配置文件控制架构模式和服务调用方式
- **数据访问抽象**：统一的数据访问接口，支持多种数据访问策略
- **Schema演进管理**：根据架构模式自动管理数据库Schema

同时，本指南还介绍了如何在开发环境中集成和使用xkongcloud-commons-uid公共库，该库提供了基于百度UID生成器的分布式唯一ID生成功能，支持持久化的实例身份管理、通过机器特征码进行实例身份的自动恢复，以及基于租约的Worker ID分配机制。

## 演进架构开发模式功能

### 演进架构配置

在开发环境中，演进架构提供了以下核心功能：

#### 1. 服务抽象层配置

通过配置文件控制服务的调用方式：

```yaml
# application-evolution.yml
xkong:
  services:
    architecture-mode: MONOLITHIC  # 当前为单体架构

    user-management:
      mode: LOCAL                   # 本地服务模式
      data-access: LOCAL           # 本地数据访问
      protocol: LOCAL_CALL         # 本地方法调用

    # 未来微服务配置示例（注释状态）
    # user-management:
    #   mode: REMOTE
    #   data-access: REMOTE
    #   protocol: GRPC
    #   address: "user-service:8081"
```

#### 2. Schema演进管理

系统会根据架构模式自动创建相应的Schema：

- **MONOLITHIC模式**：创建user_management、common_config、infra_uid等Schema
- **MICROSERVICES模式**：创建独立的服务Schema和共享基础设施Schema

#### 3. 配置驱动的数据访问

数据访问层支持多种策略的透明切换：

- **本地策略**：基于JPA的直接数据库访问
- **远程策略**：基于gRPC的分布式数据访问（预留）

### 数据库表自动重置

在开发过程中，经常需要一种机制来自动重置数据库表，以便：

1. 快速测试数据模型变更
2. 确保测试环境的一致性
3. 避免手动清理数据库的麻烦
4. 简化开发和测试流程
5. 验证演进架构的Schema管理功能

PostgreSQL通过Hibernate的DDL自动生成策略（`hibernate.hbm2ddl.auto`参数）提供了这种功能，我们在项目中通过`postgresql.ddl-auto`参数进行配置。

### 与Cassandra RECREATE模式的对比

| 功能 | Cassandra RECREATE | PostgreSQL create/create-drop |
|------|-------------------|------------------------------|
| 表重置时机 | 应用启动时 | 应用启动时 |
| 删除现有表 | 是 | 是 |
| 创建新表 | 是 | 是 |
| 应用关闭时删除表 | 否 | 仅create-drop模式 |
| 数据初始化 | 通过DataInitializer | 通过PostgreSQLDataInitializer |
| 配置方式 | SchemaAction枚举 | postgresql.ddl-auto参数 |

## 配置开发模式

### KV参数配置

在xkongcloud-service-center中**必须**配置以下KV参数：

#### PostgreSQL DDL参数

| 环境 | postgresql.ddl-auto | 说明 |
|------|-------------------|------|
| 开发环境 | create | 应用启动时重置表，但不在应用关闭时删除表 |
| 测试环境 | create-drop | 应用启动时重置表，应用关闭时删除表 |
| 生产环境 | validate 或 none | 仅验证表结构或不执行任何操作 |

> **重要**：`postgresql.ddl-auto`参数**必须**在xkongcloud-service-center中配置，无默认值。如果缺少此参数，应用将无法启动。这与Cassandra的`cassandra.schema-action`参数的处理方式相同，确保了配置的一致性和安全性。

#### xkongcloud-commons-uid参数

使用xkongcloud-commons-uid公共库时，需要在xkongcloud-service-center中配置以下KV参数：

| 参数 | 说明 | 默认值 | 是否必需 |
|-----|------|-------|---------|
| uid.epochStr | 时间基点，格式为"yyyy-MM-dd" | 无 | 是 |
| uid.timeBits | 时间戳位数 | 无 | 是 |
| uid.workerBits | 工作机器ID位数 | 无 | 是 |
| uid.seqBits | 序列号位数 | 无 | 是 |
| uid.instance.environment | 部署环境 | default | 否 |
| uid.instance.group | 实例分组 | null | 否 |
| uid.instance.local-storage-path | 本地存储路径 | .instance_id | 否 |
| uid.instance.recovery.enabled | 是否启用实例恢复 | true | 否 |
| uid.instance.recovery.high-confidence-threshold | 高置信度阈值 | 150 | 否 |
| uid.instance.recovery.minimum-acceptable-score | 最低可接受分数 | 70 | 否 |
| uid.instance.recovery.strategy | 恢复策略 | ALERT_AUTO_WITH_TIMEOUT | 否 |
| uid.instance.recovery.timeout-seconds | 恢复策略超时时间（秒） | 300 | 否 |
| uid.instance.override | 实例ID覆盖 | null | 否 |
| uid.worker.lease-duration-seconds | 租约时长（秒） | 300 | 否 |
| uid.instance.encryption.enabled | 是否启用实例ID文件加密 | false | 否 |
| uid.instance.encryption.algorithm | 加密算法 | AES-256-GCM | 否 |
| uid.boostPower | RingBuffer扩容参数 | 3 | 否 |
| uid.paddingFactor | RingBuffer填充因子 | 50 | 否 |
| uid.scheduleInterval | RingBuffer填充间隔（秒） | 60 | 否 |

> **重要**：`uid.epochStr`、`uid.timeBits`、`uid.workerBits`和`uid.seqBits`参数**必须**在xkongcloud-service-center中配置，无默认值。如果缺少这些参数，应用将无法启动。

### 配置验证

PostgreSQLConfig类会在应用启动时验证`postgresql.ddl-auto`参数：

1. 检查参数是否存在，如果不存在则抛出异常，阻止应用启动
2. 验证参数值是否有效（必须是none、validate、update、create或create-drop之一）
3. 将验证后的值用于配置Hibernate的DDL自动生成策略

这种强制配置机制确保了：

1. 开发人员必须明确指定DDL策略，避免意外的数据库操作
2. 生产环境中不会因为配置缺失而使用可能危险的默认值
3. 与Cassandra配置保持一致的处理方式，简化了开发人员的学习曲线

## 使用开发模式

### 启动应用

在开发模式下启动应用时，会自动执行以下操作：

1. 删除现有的数据库表
2. 根据实体映射创建新的表
3. 通过PostgreSQLDataInitializer初始化数据

## 集成xkongcloud-commons-uid公共库

### 添加依赖

要在项目中使用xkongcloud-commons-uid公共库，首先需要在项目的`pom.xml`文件中添加依赖：

```xml
<dependency>
    <groupId>org.xkong.cloud</groupId>
    <artifactId>xkongcloud-commons-uid</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 配置数据库表

xkongcloud-commons-uid公共库需要在PostgreSQL数据库中创建两个表：`infra_uid.instance_registry`和`infra_uid.worker_id_assignment`。这些表应该放在专门的`infra_uid` Schema中，符合PostgreSQL最佳实践中推荐的多Schema组织方式。这些表用于管理实例身份和工作机器ID分配。

在开发模式下（`postgresql.ddl-auto=create`或`create-drop`），这些表会自动创建。但在生产环境中，应该使用数据库迁移工具（如Flyway）来管理这些表的创建和更新。

### 配置Bean

在应用的配置类中，需要配置xkongcloud-commons-uid公共库的Bean：

```java
@Configuration
@EnableScheduling
@DependsOn("kvParamService")
public class UidGeneratorConfig {

    private static final Logger logger = LoggerFactory.getLogger(UidGeneratorConfig.class);

    @Autowired
    private KVParamService kvParamService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Value("${xkong.kv.cluster-id}")
    private String clusterId;

    @Bean
    public KeyManagementService keyManagementService() {
        // 从KVParamService获取参数
        String appName = clusterId; // 使用已有的clusterId变量
        String environment = kvParamService.getParam("uid.instance.environment", "default");
        boolean encryptionEnabled = Boolean.parseBoolean(kvParamService.getParam("uid.instance.encryption.enabled", "false"));

        // 创建KeyManagementService实例
        return new KeyManagementService(
            jdbcTemplate,
            transactionTemplate,
            appName,
            environment,
            encryptionEnabled
        );
    }

    @Bean
    public PersistentInstanceManager persistentInstanceManager(KeyManagementService keyManagementService) {
        String appName = clusterId; // 使用已有的clusterId变量
        String environment = kvParamService.getParam("uid.instance.environment", "default");
        String instanceGroup = kvParamService.getParam("uid.instance.group", null);
        String localStoragePath = kvParamService.getParam("uid.instance.local-storage-path", ".instance_id");
        boolean recoveryEnabled = Boolean.parseBoolean(kvParamService.getParam("uid.instance.recovery.enabled", "true"));
        int highConfThreshold = Integer.parseInt(kvParamService.getParam("uid.instance.recovery.high-confidence-threshold", "150"));
        int minAcceptScore = Integer.parseInt(kvParamService.getParam("uid.instance.recovery.minimum-acceptable-score", "70"));
        String recoveryStrategy = kvParamService.getParam("uid.instance.recovery.strategy", "ALERT_AUTO_WITH_TIMEOUT");
        int recoveryTimeoutSeconds = Integer.parseInt(kvParamService.getParam("uid.instance.recovery.timeout-seconds", "300"));
        Long instanceIdOverride = kvParamService.getParam("uid.instance.override") != null ? Long.parseLong(kvParamService.getParam("uid.instance.override")) : null;
        boolean encryptionEnabled = Boolean.parseBoolean(kvParamService.getParam("uid.instance.encryption.enabled", "false"));

        PersistentInstanceManager manager = new PersistentInstanceManager(
            jdbcTemplate,
            transactionTemplate,
            appName,
            environment,
            instanceGroup,
            localStoragePath,
            recoveryEnabled,
            highConfThreshold,
            minAcceptScore,
            recoveryStrategy,
            recoveryTimeoutSeconds,
            instanceIdOverride,
            encryptionEnabled,
            keyManagementService
        );
        return manager;
    }

    @Bean
    public WorkerIdAssigner workerIdAssigner(PersistentInstanceManager persistentInstanceManager) {
        int leaseDuration = Integer.parseInt(kvParamService.getParam("uid.worker.lease-duration-seconds", "300"));

        PersistentInstanceWorkerIdAssigner assigner = new PersistentInstanceWorkerIdAssigner(
            jdbcTemplate,
            transactionTemplate,
            persistentInstanceManager,
            leaseDuration
        );
        return assigner;
    }

    @Bean
    public UidGenerator uidGenerator(WorkerIdAssigner workerIdAssigner) {
        CachedUidGenerator cachedUidGenerator = new CachedUidGenerator();
        cachedUidGenerator.setWorkerIdAssigner(workerIdAssigner);

        cachedUidGenerator.setEpochStr(kvParamService.getParam("uid.epochStr"));
        cachedUidGenerator.setTimeBits(Integer.parseInt(kvParamService.getParam("uid.timeBits")));
        cachedUidGenerator.setWorkerBits(Integer.parseInt(kvParamService.getParam("uid.workerBits")));
        cachedUidGenerator.setSeqBits(Integer.parseInt(kvParamService.getParam("uid.seqBits")));

        cachedUidGenerator.setBoostPower(Integer.parseInt(kvParamService.getParam("uid.boostPower", "3")));
        cachedUidGenerator.setPaddingFactor(Integer.parseInt(kvParamService.getParam("uid.paddingFactor", "50")));
        cachedUidGenerator.setScheduleInterval(Long.parseLong(kvParamService.getParam("uid.scheduleInterval", "60")));

        logger.info("百度UID生成器 (CachedUidGenerator) 配置完成。");
        return cachedUidGenerator;
    }
}
```

### 使用UID生成器

配置完成后，可以在应用中通过依赖注入使用UID生成器：

```java
@Service
public class UserService {
    @Autowired
    private UidGenerator uidGenerator;

    public User createUser(String name, String email) {
        User user = new User();
        user.setId(uidGenerator.getUID()); // 使用UID生成器生成唯一ID
        user.setName(name);
        user.setEmail(email);
        return userRepository.save(user);
    }
}
```

### 数据初始化

数据初始化由`PostgreSQLDataInitializer`类负责，它会根据当前的DDL策略决定是否执行初始化：

- 当`postgresql.ddl-auto=create`或`create-drop`时：直接初始化数据
- 当`postgresql.ddl-auto=update`或`validate`时：检查表是否为空，如果为空则初始化数据
- 当`postgresql.ddl-auto=none`时：不执行初始化操作

### 自定义初始数据

如果需要自定义初始数据，可以修改`PostgreSQLDataInitializer`类的`initializeData`方法：

```java
private void initializeData() {
    log.info("开始添加初始数据...");
    try {
        // 初始化用户数据
        User admin = new User();
        admin.setName("管理员");
        admin.setMailAddress("<EMAIL>");
        admin.setRegistTime(LocalDateTime.now());
        userRepository.save(admin);

        // 添加自定义初始数据
        User testUser = new User();
        testUser.setName("测试用户");
        testUser.setMailAddress("<EMAIL>");
        testUser.setRegistTime(LocalDateTime.now());
        userRepository.save(testUser);

        log.info("初始数据添加完成");
    } catch (Exception e) {
        log.error("添加初始数据时发生错误: {}", e.getMessage(), e);
    }
}
```

## 最佳实践

### 开发环境推荐配置

对于大多数开发场景，推荐使用以下配置：

```properties
postgresql.ddl-auto=create
postgresql.show-sql=true
postgresql.format-sql=true

# xkongcloud-commons-uid配置
uid.epochStr=2025-01-01
uid.timeBits=31
uid.workerBits=18
uid.seqBits=14
uid.instance.environment=development
uid.instance.recovery.strategy=ALERT_AUTO_WITH_TIMEOUT
uid.instance.recovery.timeout-seconds=300
uid.worker.lease-duration-seconds=60
```

这样可以在每次应用启动时重置数据库表，并显示格式化的SQL语句，方便调试。同时，UID生成器使用较短的租约时长和更适合开发环境的恢复策略。

### 测试环境推荐配置

对于自动化测试环境，推荐使用以下配置：

```properties
postgresql.ddl-auto=create-drop
postgresql.show-sql=false

# xkongcloud-commons-uid配置
uid.epochStr=2025-01-01
uid.timeBits=31
uid.workerBits=18
uid.seqBits=14
uid.instance.environment=testing
uid.instance.recovery.strategy=ALERT_AUTO_WITH_TIMEOUT
uid.instance.recovery.timeout-seconds=300
uid.worker.lease-duration-seconds=60
```

这样可以确保每次测试都在干净的环境中运行，并在测试完成后清理数据库。

### 生产环境配置

在生产环境中，必须使用以下配置之一：

```properties
# 选项1：验证表结构
postgresql.ddl-auto=validate

# 选项2：不执行任何操作
postgresql.ddl-auto=none

# xkongcloud-commons-uid配置
uid.epochStr=2025-01-01
uid.timeBits=31
uid.workerBits=18
uid.seqBits=14
uid.instance.environment=production
uid.instance.recovery.strategy=ALERT_AUTO_WITH_TIMEOUT
uid.instance.recovery.timeout-seconds=300
uid.worker.lease-duration-seconds=300
```

> **警告**：在生产环境中，严禁使用`create`、`create-drop`或`update`，以防止意外删除或修改生产数据。

### xkongcloud-commons-uid最佳实践

1. **ID生成配置**：
   - 根据业务需求合理配置时间戳位数、工作机器ID位数和序列号位数
   - 建议配置：31位时间戳(秒级)、18位workerId、14位序列号，可以支持较长的时间范围和较大的并发量

2. **实例恢复策略**：
   - 在生产环境中，建议使用`ALERT_AUTO_WITH_TIMEOUT`策略，发出告警后等待一定时间让人工干预，如果超时无人响应则自动选择最佳匹配或创建新实例
   - 在开发和测试环境中，也可以使用`ALERT_AUTO_WITH_TIMEOUT`策略，但可以设置较短的超时时间

3. **租约管理**：
   - 根据应用的部署频率和运行稳定性调整租约时长
   - 对于频繁部署的环境，可以使用较短的租约时长（如60秒）
   - 对于稳定运行的生产环境，可以使用较长的租约时长（如300秒或更长）

4. **本地存储**：
   - 确保本地存储路径是持久的，即使在应用重启后也能访问
   - 对于容器化环境，可以考虑使用持久卷来存储实例ID文件

5. **监控与告警**：
   - 监控工作机器ID分配情况，及时发现异常
   - 设置告警，当实例恢复置信度低于阈值时通知运维人员

6. **加密功能**：
   - 在开发环境中，可以禁用加密功能以简化调试
   - 在测试环境中，建议启用加密功能进行测试
   - 在生产环境中，应该启用加密功能以提高安全性
   - 确保加密密钥的安全存储和管理

## 常见问题

### 问题1：应用启动时报表结构不匹配错误

**症状**：应用启动时报错"表结构与实体映射不匹配"

**解决方案**：
1. 在xkongcloud-service-center中将`postgresql.ddl-auto`参数设置为`create`或`update`（开发环境）
2. 在生产环境中，使用数据库迁移工具（如Flyway）更新表结构

### 问题2：初始数据重复创建

**症状**：每次应用启动时都创建重复的初始数据

**解决方案**：
1. 在`initializeData`方法中添加检查逻辑，避免创建重复数据
2. 使用`findByXXX`方法检查数据是否已存在，如果存在则不创建

### 问题3：生产环境意外使用了create模式

**症状**：生产数据被意外删除

**解决方案**：
1. 立即停止应用
2. 从备份恢复数据
3. 在xkongcloud-service-center中修改`postgresql.ddl-auto`参数为`validate`或`none`
4. 添加环境检查逻辑，在生产环境中禁止使用`create`、`create-drop`或`update`

### 问题4：应用无法启动，报错"必需的'postgresql.ddl-auto'参数未在KV服务中找到"

**症状**：应用启动失败，日志中显示"PostgreSQL配置错误: 必需的'postgresql.ddl-auto'参数未在KV服务中找到"

**解决方案**：
1. 在xkongcloud-service-center中配置`postgresql.ddl-auto`参数
2. 确保参数值为有效值（none、validate、update、create或create-drop之一）
3. 重启应用

### 问题5：应用无法启动，报错"必需的UID生成器参数未在KV服务中找到"

**症状**：应用启动失败，日志中显示"UID生成器配置错误: 必需的'uid.epochStr'参数未在KV服务中找到"（或其他必需的UID参数）

**解决方案**：
1. 在xkongcloud-service-center中配置所有必需的UID参数（uid.epochStr、uid.timeBits、uid.workerBits、uid.seqBits）
2. 确保参数值符合要求（如uid.epochStr格式为"yyyy-MM-dd"）
3. 重启应用

### 问题6：实例ID恢复失败，无法分配工作机器ID

**症状**：应用启动失败，日志中显示"无法恢复实例ID，无法分配工作机器ID"

**解决方案**：
1. 检查机器特征码收集是否正常
2. 检查PostgreSQL数据库连接是否正常
3. 检查infra_uid.instance_registry表和infra_uid.worker_id_assignment表是否存在且结构正确
4. 如果是新环境，可以尝试设置`uid.instance.recovery.strategy=NEW_ONLY`
5. 如果需要强制使用特定实例ID，可以配置`uid.instance.override`参数

### 问题7：实例ID文件加密后无法解密

**症状**：应用启动失败，日志中显示"解密实例ID文件失败"

**解决方案**：
1. 检查加密密钥是否正确存储在数据库中
2. 检查`uid.instance.encryption.enabled`参数是否正确配置
3. 如果无法解决，可以删除本地实例ID文件，让应用重新生成
4. 在极端情况下，可以配置`uid.instance.override`参数强制使用特定实例ID

## 结论

PostgreSQL开发模式下的数据库表自动重置功能是一个强大的工具，可以大大简化开发和测试过程。通过正确配置`postgresql.ddl-auto`参数和实现适当的数据初始化逻辑，可以实现类似于Cassandra RECREATE模式的功能，同时保持对不同环境的灵活控制。

同时，通过集成xkongcloud-commons-uid公共库，可以为应用提供高性能、可靠的分布式唯一ID生成功能。该库支持持久化的实例身份管理、通过机器特征码进行实例身份的自动恢复，以及基于租约的Worker ID分配机制，确保在分布式环境中生成的ID全局唯一且单调递增。通过合理配置UID生成器参数和实例恢复策略，可以满足不同环境下的需求，提高系统的可靠性和可用性。

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 2.0 | 2025-01-15 | 重构为演进架构开发模式指南，增加服务抽象层和配置驱动机制 | AI助手 |
| 1.4 | 2025-06-13 | 更新恢复策略为ALERT_AUTO_WITH_TIMEOUT并添加超时参数 | AI助手 |
| 1.3 | 2025-06-10 | 添加UID加密功能相关配置和最佳实践 | AI助手 |
| 1.2 | 2025-06-02 | 明确指定UID生成器相关表应该放在`infra_uid` Schema中 | AI助手 |
| 1.1 | 2025-05-26 | 添加xkongcloud-commons-uid公共库集成指南 | AI助手 |
| 1.0 | 2025-05-09 | 初始版本 | AI助手 |
