# XKongCloud Commons Nexus V1.0: 微内核与插件生命周期

## 文档元数据

- **文档ID**: `F007-NEXUS-ARCHITECTURE-DESIGN-002`
- **版本**: `V1.0`
- **创建日期**: `2025-06-12`
- **状态**: `设计稿`
- **技术栈**: `Java 21, Spring Boot 3.4.5, Virtual Threads`
- **复杂度等级**: L2

## 实施约束标注

### 🔒 强制性技术约束
- **Java版本**: 必须使用Java 21或更高版本，强制启用Virtual Threads
- **Spring Boot版本**: 必须使用Spring Boot 3.4.5+，确保与Virtual Threads兼容
- **类加载器隔离**: 严格禁止插件直接访问其他插件的内部类
- **依赖版本**: 插件依赖必须遵循SemVer规范，禁止使用SNAPSHOT版本

### ⚡ 性能指标约束
- **内核启动时间**: ≤500ms（包含所有插件扫描和依赖解析）
- **单插件启动时间**: ≤100ms（PluginActivator.start()方法执行时间）
- **内存占用**: 内核基础内存≤50MB，每个插件额外内存≤20MB
- **依赖解析性能**: 支持≤1000个插件的依赖图解析，时间≤200ms

### 🔄 兼容性要求
- **向后兼容**: 插件API保证向后兼容，主版本升级前6个月通知
- **JVM兼容**: 支持HotSpot、OpenJ9、GraalVM等主流JVM实现
- **操作系统**: 支持Linux、Windows、macOS等主流操作系统

### ⚠️ 违规后果定义
- **技术约束违规**: 插件启动失败，记录ERROR级别日志
- **性能指标超标**: 记录WARN级别日志，触发监控告警
- **兼容性问题**: 降级处理，记录兼容性问题报告

### 🎯 验证锚点设置
- **编译验证**: `mvn clean compile -Djava.version=21`
- **单元测试**: `mvn test -Dtest=KernelLifecycleTest`
- **集成测试**: `mvn verify -Dtest=PluginIntegrationTest`
- **性能测试**: `mvn test -Dtest=PerformanceBenchmarkTest`

## 核心定位

Nexus微内核是XKongCloud Commons Nexus框架的核心组件，负责插件的生命周期管理、依赖解析和运行时隔离。它的核心定位是成为一个**稳定基石而非控制中心**，为插件提供安全、可预测的运行环境。

## 设计哲学

The Nexus Kernel (微内核) 的核心设计哲学是 **"提供稳定基石，而非成为控制中心"**。它自身不包含任何业务逻辑或通信逻辑。其全部职责被严格限定在以下三个方面，确保其极度稳定、可预测和安全：

1.  **插件生命周期管理**: 精确控制插件从被发现到被销毁的全过程
2.  **依赖与隔离保障**: 确保插件在独立的沙箱中运行，并能正确访问其声明的依赖
3.  **核心上下文提供**: 为插件提供运行所需的最小环境信息

### 架构设计原则
- **单一职责原则**: 内核只负责插件管理，不涉及业务逻辑
- **开放封闭原则**: 对插件扩展开放，对内核修改封闭
- **依赖倒置原则**: 内核依赖抽象接口，不依赖具体实现
- **接口隔离原则**: 为插件提供最小化的接口集合

## 包含范围

本文档包含以下核心内容：

- **微内核架构设计**: 内核的职责边界和设计原则
- **插件契约定义**: 插件清单格式和激活器接口
- **生命周期管理**: 插件状态机和生命周期控制
- **依赖解析机制**: 依赖图构建和拓扑排序算法
- **类加载器隔离**: 插件沙箱和类加载委托机制
- **内核服务接口**: 编程式插件管理API

## 排除范围

本文档明确不包含以下内容：

- **服务总线设计**: 插件间通信机制在其他文档中描述
- **具体插件实现**: 不涉及DB、Cache等具体插件的实现细节
- **Spring Boot集成**: 自动配置和启动器在专门文档中描述
- **安全策略**: 插件安全和沙箱策略在安全文档中描述
- **性能优化**: 运行时性能调优不在此文档范围内

## 微内核架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "应用层"
        App[Spring Boot Application]
        Config[Auto Configuration]
    end

    subgraph "内核层"
        Kernel[Nexus Kernel]
        Registry[Plugin Registry]
        Resolver[Dependency Resolver]
        Lifecycle[Lifecycle Manager]
    end

    subgraph "插件层"
        PluginA[Plugin A]
        PluginB[Plugin B]
        PluginC[Plugin C]
    end

    subgraph "隔离层"
        ClassLoaderA[ClassLoader A]
        ClassLoaderB[ClassLoader B]
        ClassLoaderC[ClassLoader C]
    end

    App --> Config
    Config --> Kernel
    Kernel --> Registry
    Kernel --> Resolver
    Kernel --> Lifecycle

    Registry --> PluginA
    Registry --> PluginB
    Registry --> PluginC

    PluginA --> ClassLoaderA
    PluginB --> ClassLoaderB
    PluginC --> ClassLoaderC

    Resolver -.-> PluginA
    Resolver -.-> PluginB
    Resolver -.-> PluginC
```

### 核心组件关系图

```mermaid
classDiagram
    class NexusKernel {
        +start()
        +stop()
        +getPluginRegistry()
        +getDependencyResolver()
        +getLifecycleManager()
    }

    class PluginRegistry {
        +registerPlugin(Plugin)
        +unregisterPlugin(String)
        +getPlugin(String)
        +getAllPlugins()
    }

    class DependencyResolver {
        +resolveDependencies(List~Plugin~)
        +buildDependencyGraph()
        +topologicalSort()
        +validateDependencies()
    }

    class LifecycleManager {
        +startPlugin(String)
        +stopPlugin(String)
        +getPluginState(String)
        +transitionState(String, State)
    }

    class Plugin {
        +getId()
        +getVersion()
        +getActivator()
        +getDependencies()
        +getState()
    }

    class PluginClassLoader {
        +loadClass(String)
        +findClass(String)
        +getParentClassLoader()
        +getDependencyClassLoaders()
    }

    NexusKernel --> PluginRegistry
    NexusKernel --> DependencyResolver
    NexusKernel --> LifecycleManager
    PluginRegistry --> Plugin
    Plugin --> PluginClassLoader
    DependencyResolver --> Plugin
    LifecycleManager --> Plugin
```

## 插件的契约：如何成为一个"标准插头"

一个标准的Nexus插件（一个"插头"）必须遵循以下契约。这个契约由两部分组成：一份描述自己的"说明书"（Manifest）和一个"电源开关"（Activator）。

### 插件清单 (`META-INF/nexus-plugin.json`)

每个插件JAR包的 `META-INF` 目录下都必须包含一个 `nexus-plugin.json` 文件，用于向内核声明其身份和需求。

```json
{
  "$schema": "https://schemas.xkong.cloud/nexus-plugin-v1.json",
  "id": "org.xkong.cloud.commons.db",
  "version": "1.0.0",
  "name": "XKongCloud Commons DB Plugin",
  "description": "Provides unified data access capabilities.",
  "provider": "XKongCloud Inc.",
  "activator": "org.xkong.cloud.commons.db.plugin.DbPluginActivator",
  "dependencies": [
    {
      "id": "org.xkong.cloud.commons.cache",
      "version": "[1.0.0, 2.0.0)",
      "optional": false
    }
  ],
  "exports": [
    "org.xkong.cloud.commons.db.api",
    "org.xkong.cloud.commons.db.spi"
  ],
  "imports": [
    "org.xkong.cloud.commons.cache.api"
  ],
  "metadata": {
    "category": "data-access",
    "tags": ["database", "persistence", "commons"],
    "documentation": "https://docs.xkong.cloud/commons/db",
    "license": "Apache-2.0"
  }
}
```

### 插件清单JSON Schema定义

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "Nexus Plugin Manifest",
  "type": "object",
  "required": ["id", "version", "name", "activator"],
  "properties": {
    "id": {
      "type": "string",
      "pattern": "^[a-z][a-z0-9]*(\\.([a-z][a-z0-9]*))*$",
      "description": "插件的全局唯一标识符，遵循Java包命名规范"
    },
    "version": {
      "type": "string",
      "pattern": "^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$",
      "description": "插件版本，必须遵循语义化版本规范"
    },
    "name": {
      "type": "string",
      "minLength": 1,
      "maxLength": 100,
      "description": "插件的显示名称"
    },
    "description": {
      "type": "string",
      "maxLength": 500,
      "description": "插件的详细描述"
    },
    "provider": {
      "type": "string",
      "description": "插件提供商"
    },
    "activator": {
      "type": "string",
      "pattern": "^[a-zA-Z_$][a-zA-Z\\d_$]*(\\.([a-zA-Z_$][a-zA-Z\\d_$]*))*$",
      "description": "实现PluginActivator接口的类的完全限定名"
    },
    "dependencies": {
      "type": "array",
      "items": {
        "type": "object",
        "required": ["id", "version"],
        "properties": {
          "id": {"type": "string"},
          "version": {"type": "string"},
          "optional": {"type": "boolean", "default": false}
        }
      }
    },
    "exports": {
      "type": "array",
      "items": {"type": "string"},
      "description": "插件导出的包列表"
    },
    "imports": {
      "type": "array",
      "items": {"type": "string"},
      "description": "插件导入的包列表"
    }
  }
}
```

**插件接口定义**：
- **`id`**: 插件的全局唯一标识符，遵循包命名规范。
- **`version`**: 插件的版本，**必须**遵循**语义化版本（Semantic Versioning, SemVer）**。
- **`activator`**: 实现了 `PluginActivator` 接口的类的完全限定名。
- **`dependencies`**: 依赖的其他插件列表。内核将据此进行依赖解析和启动排序，并构建类加载器委托链。

### 插件激活器 (`PluginActivator`)

激活器是插件的入口点，是内核与插件代码交互的唯一桥梁。

```java
package org.xkong.cloud.commons.nexus.api;

/**
 * The entry point for a Nexus plugin.
 *
 * <p>插件激活器是插件与内核交互的核心接口，负责插件的启动和停止逻辑。
 * 实现类必须提供无参构造函数，并确保线程安全。</p>
 *
 * @since 1.0.0
 * <AUTHOR> Team
 */
public interface PluginActivator {

    /**
     * 插件启动时调用此方法。
     *
     * <p>在此方法中，插件应该：</p>
     * <ul>
     *   <li>初始化插件的核心组件</li>
     *   <li>通过服务总线的事件机制注册服务</li>
     *   <li>通过服务总线的事件机制订阅必要的事件</li>
     *   <li>执行必要的健康检查</li>
     * </ul>
     *
     * <p><strong>性能约束</strong>：此方法执行时间不得超过100ms</p>
     * <p><strong>异常处理</strong>：如果启动失败，应抛出具体的异常信息</p>
     *
     * @param context 插件上下文，提供对内核服务的访问
     * @throws PluginStartException 如果插件启动失败
     * @throws IllegalStateException 如果插件已经启动
     * @throws SecurityException 如果插件没有启动权限
     */
    void start(PluginContext context) throws PluginStartException;

    /**
     * 插件停止时调用此方法。
     *
     * <p>在此方法中，插件应该：</p>
     * <ul>
     *   <li>注销所有注册的服务</li>
     *   <li>取消事件订阅</li>
     *   <li>释放占用的资源</li>
     *   <li>清理临时文件</li>
     * </ul>
     *
     * <p><strong>性能约束</strong>：此方法执行时间不得超过200ms</p>
     * <p><strong>异常处理</strong>：停止过程中的异常不应阻止其他插件的停止</p>
     *
     * @param context 插件上下文
     * @throws PluginStopException 如果插件停止失败
     */
    void stop(PluginContext context) throws PluginStopException;

    /**
     * 获取插件的健康状态。
     *
     * <p>内核会定期调用此方法检查插件健康状态。</p>
     *
     * @param context 插件上下文
     * @return 插件健康状态
     * @since 1.1.0
     */
    default PluginHealth getHealth(PluginContext context) {
        return PluginHealth.UP;
    }
}
```

### 插件上下文接口 (`PluginContext`)

```java
package org.xkong.cloud.commons.nexus.api;

import java.util.Optional;
import java.util.Properties;

/**
 * 插件运行时上下文，提供插件与内核交互的接口。
 *
 * <p>插件上下文是插件访问内核服务和其他插件的唯一入口，
 * 提供了类型安全的服务发现和配置管理功能。</p>
 *
 * @since 1.0.0
 */
public interface PluginContext {

    /**
     * 获取当前插件的元数据信息。
     *
     * @return 插件元数据，包含ID、版本、依赖等信息
     */
    PluginMetadata getPluginMetadata();

    /**
     * 获取插件的配置属性。
     *
     * <p>配置来源优先级：</p>
     * <ol>
     *   <li>系统属性 (-D参数)</li>
     *   <li>环境变量</li>
     *   <li>application.properties</li>
     *   <li>插件默认配置</li>
     * </ol>
     *
     * @return 插件配置属性
     */
    Properties getProperties();

    /**
     * 获取指定类型的服务实例。
     *
     * <p>服务查找范围：</p>
     * <ul>
     *   <li>当前插件注册的服务</li>
     *   <li>依赖插件导出的服务</li>
     *   <li>内核提供的核心服务</li>
     * </ul>
     *
     * @param <T> 服务类型
     * @param serviceType 服务接口类型
     * @return 服务实例，如果未找到则返回空
     * @throws SecurityException 如果没有访问权限
     */
    <T> Optional<T> getService(Class<T> serviceType);

    /**
     * 注册服务到服务总线。
     *
     * @param <T> 服务类型
     * @param serviceType 服务接口类型
     * @param serviceInstance 服务实例
     * @param properties 服务属性
     * @return 服务注册句柄，用于后续注销
     * @throws IllegalArgumentException 如果参数无效
     * @throws SecurityException 如果没有注册权限
     */
    <T> ServiceRegistration<T> registerService(Class<T> serviceType,
                                               T serviceInstance,
                                               Properties properties);

    /**
     * 获取内核服务接口。
     *
     * @return 内核服务接口
     */
    NexusKernelService getKernelService();

    /**
     * 获取插件的类加载器。
     *
     * @return 插件专用的类加载器
     */
    ClassLoader getClassLoader();

    /**
     * 获取插件的数据目录。
     *
     * <p>每个插件都有独立的数据目录，用于存储持久化数据。</p>
     *
     * @return 插件数据目录的绝对路径
     */
    String getDataDirectory();
}
```

### 异常处理接口定义

```java
package org.xkong.cloud.commons.nexus.api;

/**
 * 插件启动异常。
 */
public class PluginStartException extends Exception {
    private final String pluginId;
    private final ErrorCode errorCode;

    public PluginStartException(String pluginId, ErrorCode errorCode, String message) {
        super(message);
        this.pluginId = pluginId;
        this.errorCode = errorCode;
    }

    public PluginStartException(String pluginId, ErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.pluginId = pluginId;
        this.errorCode = errorCode;
    }

    public String getPluginId() { return pluginId; }
    public ErrorCode getErrorCode() { return errorCode; }
}

/**
 * 插件停止异常。
 */
public class PluginStopException extends Exception {
    private final String pluginId;
    private final ErrorCode errorCode;

    public PluginStopException(String pluginId, ErrorCode errorCode, String message) {
        super(message);
        this.pluginId = pluginId;
        this.errorCode = errorCode;
    }

    public String getPluginId() { return pluginId; }
    public ErrorCode getErrorCode() { return errorCode; }
}

/**
 * 错误代码枚举。
 */
public enum ErrorCode {
    DEPENDENCY_NOT_FOUND("E001", "依赖插件未找到"),
    DEPENDENCY_VERSION_CONFLICT("E002", "依赖版本冲突"),
    ACTIVATOR_NOT_FOUND("E003", "激活器类未找到"),
    ACTIVATOR_INSTANTIATION_FAILED("E004", "激活器实例化失败"),
    INITIALIZATION_TIMEOUT("E005", "初始化超时"),
    RESOURCE_ACCESS_DENIED("E006", "资源访问被拒绝"),
    CONFIGURATION_ERROR("E007", "配置错误"),
    HEALTH_CHECK_FAILED("E008", "健康检查失败");

    private final String code;
    private final String description;

    ErrorCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() { return code; }
    public String getDescription() { return description; }
}
```

## 插件生命周期：精确的状态机管理

内核通过一个严谨的状态机来管理每个插件的生命周期，确保其行为的可预测性。

### 生命周期状态转换图

```mermaid
stateDiagram-v2
    [*] --> DETECTED : 发现插件JAR
    DETECTED --> RESOLVED : 依赖解析成功
    DETECTED --> FAILED : 依赖解析失败
    RESOLVED --> STARTING : 开始启动
    RESOLVED --> FAILED : 启动前检查失败
    STARTING --> ACTIVE : 启动成功
    STARTING --> FAILED : 启动失败
    ACTIVE --> STOPPING : 开始停止
    ACTIVE --> FAILED : 运行时异常
    STOPPING --> RESOLVED : 停止成功
    STOPPING --> FAILED : 停止失败
    RESOLVED --> UNINSTALLED : 卸载插件
    FAILED --> RESOLVED : 重置状态
    FAILED --> UNINSTALLED : 强制卸载
    UNINSTALLED --> [*]

    note right of DETECTED : 扫描阶段
    note right of RESOLVED : 就绪阶段
    note right of STARTING : 启动阶段
    note right of ACTIVE : 运行阶段
    note right of STOPPING : 停止阶段
    note right of FAILED : 异常阶段
    note right of UNINSTALLED : 卸载阶段
```

### 状态转换时序图

```mermaid
sequenceDiagram
    participant K as Nexus Kernel
    participant R as Plugin Registry
    participant D as Dependency Resolver
    participant L as Lifecycle Manager
    participant P as Plugin Activator
    participant C as ClassLoader

    K->>R: scanPlugins()
    R->>R: 扫描JAR包
    R->>K: 插件列表 [DETECTED]

    K->>D: resolveDependencies()
    D->>D: 构建依赖图
    D->>D: 拓扑排序
    D->>K: 启动顺序 [RESOLVED]

    loop 按依赖顺序启动
        K->>L: startPlugin(pluginId)
        L->>C: 创建类加载器
        L->>P: new Activator()
        L->>P: start(context) [STARTING]
        P->>P: 初始化逻辑
        P->>L: 启动完成 [ACTIVE]
    end

    Note over K,P: 运行阶段...

    K->>L: stopAllPlugins()
    loop 按反向依赖顺序停止
        L->>P: stop(context) [STOPPING]
        P->>P: 清理逻辑
        P->>L: 停止完成 [RESOLVED]
        L->>C: 销毁类加载器
    end
```

### 状态详细说明

| 状态 | 描述 | 持续时间 | 可执行操作 | 错误处理 |
|------|------|----------|------------|----------|
| **DETECTED** | 内核已在类路径中发现插件JAR包，但尚未处理清单文件 | 瞬时 | resolve() | 进入FAILED |
| **RESOLVED** | 清单解析成功，依赖验证通过，已按拓扑排序 | 持久 | start(), uninstall() | 进入FAILED |
| **STARTING** | 正在调用PluginActivator.start()方法 | ≤100ms | 无 | 进入FAILED |
| **ACTIVE** | 插件运行中，服务已通过ServiceBusPublisher注册 | 持久 | stop(), getHealth() | 进入FAILED |
| **STOPPING** | 正在调用PluginActivator.stop()方法 | ≤200ms | 无 | 进入FAILED |
| **FAILED** | 发生不可恢复错误，插件停止处理 | 持久 | reset(), uninstall() | 记录错误日志 |
| **UNINSTALLED** | 插件已卸载，资源已释放 | 终态 | 无 | 无 |

### 状态转换约束

- **原子性约束**: 状态转换必须是原子操作，不允许中间状态
- **时序约束**: STARTING状态最长100ms，STOPPING状态最长200ms
- **依赖约束**: 插件启动前必须确保所有依赖插件已处于ACTIVE状态
- **顺序约束**: 插件停止时必须按依赖关系的反向顺序执行
- **并发约束**: 同一插件的状态转换操作必须串行化执行
- **幂等约束**: 重复的状态转换操作应该是幂等的

## 内核核心流程：极致的隔离与依赖管理

### 依赖解析与启动排序

内核启动时，会执行以下关键步骤：

#### 1. 插件扫描阶段
```java
// 伪代码示例
public class PluginScanner {
    public List<PluginManifest> scanPlugins(List<URL> classPaths) {
        List<PluginManifest> manifests = new ArrayList<>();
        for (URL classPath : classPaths) {
            // 扫描 META-INF/nexus-plugin.json
            Optional<PluginManifest> manifest = loadManifest(classPath);
            if (manifest.isPresent()) {
                validateManifest(manifest.get()); // JSON Schema验证
                manifests.add(manifest.get());
            }
        }
        return manifests;
    }
}
```

#### 2. 依赖图构建
```java
public class DependencyResolver {
    public DependencyGraph buildDependencyGraph(List<PluginManifest> manifests) {
        DependencyGraph graph = new DependencyGraph();

        // 添加所有插件节点
        for (PluginManifest manifest : manifests) {
            graph.addNode(manifest.getId(), manifest);
        }

        // 添加依赖边
        for (PluginManifest manifest : manifests) {
            for (Dependency dep : manifest.getDependencies()) {
                if (!graph.hasNode(dep.getId())) {
                    throw new DependencyNotFoundException(dep.getId());
                }
                graph.addEdge(manifest.getId(), dep.getId());
            }
        }

        // 检测循环依赖
        if (graph.hasCycle()) {
            List<String> cycle = graph.findCycle();
            throw new CircularDependencyException(cycle);
        }

        return graph;
    }
}
```

#### 3. 拓扑排序算法
```java
public List<String> topologicalSort(DependencyGraph graph) {
    List<String> result = new ArrayList<>();
    Queue<String> queue = new LinkedList<>();
    Map<String, Integer> inDegree = new HashMap<>();

    // 计算入度
    for (String node : graph.getNodes()) {
        inDegree.put(node, graph.getInDegree(node));
        if (inDegree.get(node) == 0) {
            queue.offer(node);
        }
    }

    // Kahn算法
    while (!queue.isEmpty()) {
        String current = queue.poll();
        result.add(current);

        for (String neighbor : graph.getNeighbors(current)) {
            inDegree.put(neighbor, inDegree.get(neighbor) - 1);
            if (inDegree.get(neighbor) == 0) {
                queue.offer(neighbor);
            }
        }
    }

    if (result.size() != graph.getNodes().size()) {
        throw new CircularDependencyException("拓扑排序失败，存在循环依赖");
    }

    return result;
}
```

#### 4. 依赖解析流程图

```mermaid
flowchart TD
    A[开始扫描] --> B[扫描类路径]
    B --> C{发现插件清单?}
    C -->|是| D[解析JSON清单]
    C -->|否| E[继续扫描]
    D --> F{清单格式有效?}
    F -->|是| G[添加到插件列表]
    F -->|否| H[记录错误，跳过]
    G --> E
    H --> E
    E --> I{扫描完成?}
    I -->|否| B
    I -->|是| J[构建依赖图]
    J --> K{存在循环依赖?}
    K -->|是| L[抛出异常，启动失败]
    K -->|否| M[拓扑排序]
    M --> N{排序成功?}
    N -->|否| L
    N -->|是| O[生成启动顺序]
    O --> P[依赖解析完成]
```

### 类加载器隔离：真正的"沙箱"

这是内核设计的**核心精髓**，也是实现极致隔离的关键。

#### 类加载器层次结构

```mermaid
graph TD
    subgraph "JVM类加载器层次"
        Bootstrap[Bootstrap ClassLoader<br/>rt.jar, 核心类库]
        Extension[Extension ClassLoader<br/>ext目录, 扩展类库]
        System[System ClassLoader<br/>classpath, 应用类库]
    end

    subgraph "Nexus类加载器层次"
        Kernel[Kernel ClassLoader<br/>Nexus API, 共享库]
        PluginA[Plugin A ClassLoader<br/>Plugin A JAR]
        PluginB[Plugin B ClassLoader<br/>Plugin B JAR]
        PluginC[Plugin C ClassLoader<br/>Plugin C JAR]
    end

    Bootstrap --> Extension
    Extension --> System
    System --> Kernel
    Kernel --> PluginA
    Kernel --> PluginB
    Kernel --> PluginC

    PluginA -.->|依赖| PluginB
    PluginC -.->|依赖| PluginB
```

#### 类加载器委托机制

```java
public class PluginClassLoader extends URLClassLoader {
    private final String pluginId;
    private final List<PluginClassLoader> dependencyClassLoaders;
    private final ClassLoader kernelClassLoader;
    private final Set<String> exportedPackages;

    @Override
    protected Class<?> loadClass(String name, boolean resolve) throws ClassNotFoundException {
        synchronized (getClassLoadingLock(name)) {
            // 1. 检查是否已加载
            Class<?> c = findLoadedClass(name);
            if (c != null) {
                return c;
            }

            // 2. 委托给依赖插件的类加载器
            for (PluginClassLoader depClassLoader : dependencyClassLoaders) {
                if (depClassLoader.isPackageExported(name)) {
                    try {
                        c = depClassLoader.loadClass(name);
                        if (resolve) resolveClass(c);
                        return c;
                    } catch (ClassNotFoundException ignored) {
                        // 继续尝试下一个依赖
                    }
                }
            }

            // 3. 委托给内核类加载器（Nexus API + 共享库）
            try {
                c = kernelClassLoader.loadClass(name);
                if (resolve) resolveClass(c);
                return c;
            } catch (ClassNotFoundException ignored) {
                // 继续尝试自己的JAR
            }

            // 4. 从自己的JAR包中加载
            try {
                c = findClass(name);
                if (resolve) resolveClass(c);
                return c;
            } catch (ClassNotFoundException e) {
                throw new ClassNotFoundException("无法加载类: " + name + " (插件: " + pluginId + ")");
            }
        }
    }

    public boolean isPackageExported(String className) {
        String packageName = getPackageName(className);
        return exportedPackages.contains(packageName);
    }
}
```

#### 类加载器隔离效果

| 隔离维度 | 实现机制 | 效果 | 示例 |
|----------|----------|------|------|
| **依赖冲突隔离** | 独立类加载器空间 | 不同插件可使用不同版本的同一库 | Plugin A用Guava 23, Plugin B用Guava 30 |
| **可见性控制** | 包导出机制 | 插件只能访问显式导出的包 | Plugin A无法访问Plugin B的内部实现 |
| **资源隔离** | 独立资源查找路径 | 插件资源文件互不冲突 | 各插件有独立的配置文件 |
| **内存隔离** | 类加载器生命周期管理 | 插件卸载时释放所有相关类 | 支持热插拔和内存回收 |

#### 类加载器创建流程

```mermaid
sequenceDiagram
    participant LM as Lifecycle Manager
    participant CF as ClassLoader Factory
    participant DR as Dependency Resolver
    participant PCL as PluginClassLoader
    participant KCL as Kernel ClassLoader

    LM->>CF: createClassLoader(plugin)
    CF->>DR: getDependencies(pluginId)
    DR->>CF: dependencyList

    loop 为每个依赖创建委托
        CF->>CF: 获取依赖插件的ClassLoader
    end

    CF->>PCL: new PluginClassLoader(urls, parent, dependencies)
    PCL->>KCL: 设置内核类加载器为父加载器
    PCL->>PCL: 配置导出包列表
    CF->>LM: 返回PluginClassLoader实例

    Note over PCL: 类加载器就绪，可以加载插件类
```

这种机制带来的核心优势：
- **杜绝依赖冲突**: 插件A和插件B可以依赖完全不同版本的同一个库，它们会被加载到各自的类加载器中，互不干扰
- **精确的可见性**: 插件只能"看到"它自己、它显式依赖的插件、以及Nexus API的类，无法意外访问或破坏其他插件的内部实现
- **支持热卸载/热更新**: 由于类的边界清晰，使得在未来实现插件的热插拔成为可能
- **内存安全**: 插件卸载时，其类加载器及所有加载的类都会被垃圾回收，避免内存泄漏

## 内核服务接口：编程式插件管理

遵循"提供逃生舱口"的哲学，Nexus内核自身也会被注册为一个Spring Bean (`NexusKernelService`)。高级用户可以注入此服务，以编程方式与插件生命周期进行交互。

### 内核服务API定义

```java
package org.xkong.cloud.commons.nexus.api;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * Nexus内核服务接口，提供编程式插件管理能力。
 *
 * <p>此接口允许应用程序动态管理插件生命周期，查询插件状态，
 * 以及监听插件事件。所有操作都是线程安全的。</p>
 *
 * @since 1.0.0
 */
@Component
public interface NexusKernelService {

    /**
     * 获取所有已注册的插件信息。
     *
     * @return 插件信息列表，按依赖顺序排序
     */
    List<PluginInfo> getAllPlugins();

    /**
     * 根据插件ID获取插件信息。
     *
     * @param pluginId 插件ID
     * @return 插件信息，如果不存在则返回空
     */
    Optional<PluginInfo> getPlugin(String pluginId);

    /**
     * 获取插件的当前状态。
     *
     * @param pluginId 插件ID
     * @return 插件状态
     * @throws PluginNotFoundException 如果插件不存在
     */
    PluginState getPluginState(String pluginId) throws PluginNotFoundException;

    /**
     * 异步启动指定插件。
     *
     * <p>此方法会自动启动所有依赖的插件。</p>
     *
     * @param pluginId 插件ID
     * @return 启动结果的Future
     * @throws PluginNotFoundException 如果插件不存在
     * @throws IllegalStateException 如果插件已经启动
     */
    CompletableFuture<Void> startPlugin(String pluginId)
        throws PluginNotFoundException, IllegalStateException;

    /**
     * 异步停止指定插件。
     *
     * <p>此方法会自动停止所有依赖此插件的其他插件。</p>
     *
     * @param pluginId 插件ID
     * @return 停止结果的Future
     * @throws PluginNotFoundException 如果插件不存在
     * @throws IllegalStateException 如果插件未启动
     */
    CompletableFuture<Void> stopPlugin(String pluginId)
        throws PluginNotFoundException, IllegalStateException;

    /**
     * 重启指定插件。
     *
     * @param pluginId 插件ID
     * @return 重启结果的Future
     */
    CompletableFuture<Void> restartPlugin(String pluginId);

    /**
     * 获取插件的健康状态。
     *
     * @param pluginId 插件ID
     * @return 健康状态
     * @throws PluginNotFoundException 如果插件不存在
     */
    PluginHealth getPluginHealth(String pluginId) throws PluginNotFoundException;

    /**
     * 注册插件状态变化监听器。
     *
     * @param listener 状态变化监听器
     * @return 监听器注册句柄
     */
    ListenerRegistration addPluginStateListener(PluginStateListener listener);

    /**
     * 获取内核统计信息。
     *
     * @return 内核统计信息
     */
    KernelStatistics getStatistics();

    /**
     * 执行内核健康检查。
     *
     * @return 内核健康状态
     */
    KernelHealth performHealthCheck();
}
```

### 插件信息数据结构

```java
/**
 * 插件信息数据传输对象。
 */
public class PluginInfo {
    private final String id;
    private final String version;
    private final String name;
    private final String description;
    private final String provider;
    private final List<String> dependencies;
    private final PluginState state;
    private final long startTime;
    private final String dataDirectory;

    // 构造函数、getter方法等...

    public boolean isActive() {
        return state == PluginState.ACTIVE;
    }

    public Duration getUptime() {
        return state == PluginState.ACTIVE ?
            Duration.between(Instant.ofEpochMilli(startTime), Instant.now()) :
            Duration.ZERO;
    }
}

/**
 * 插件状态枚举。
 */
public enum PluginState {
    DETECTED("已发现"),
    RESOLVED("已解析"),
    STARTING("启动中"),
    ACTIVE("运行中"),
    STOPPING("停止中"),
    FAILED("失败"),
    UNINSTALLED("已卸载");

    private final String description;

    PluginState(String description) {
        this.description = description;
    }

    public String getDescription() { return description; }
    public boolean isTransient() { return this == STARTING || this == STOPPING; }
    public boolean isStable() { return this == RESOLVED || this == ACTIVE || this == FAILED; }
}

/**
 * 插件健康状态。
 */
public class PluginHealth {
    public static final PluginHealth UP = new PluginHealth(Status.UP, "插件运行正常");
    public static final PluginHealth DOWN = new PluginHealth(Status.DOWN, "插件运行异常");

    public enum Status { UP, DOWN, UNKNOWN }

    private final Status status;
    private final String message;
    private final Map<String, Object> details;

    // 构造函数、getter方法等...
}
```

### 使用示例

```java
@Service
public class PluginManagementService {

    @Autowired
    private NexusKernelService kernelService;

    /**
     * 获取所有活跃插件的状态报告。
     */
    public List<PluginStatusReport> getActivePluginsReport() {
        return kernelService.getAllPlugins().stream()
            .filter(PluginInfo::isActive)
            .map(plugin -> {
                PluginHealth health = kernelService.getPluginHealth(plugin.getId());
                return new PluginStatusReport(plugin, health);
            })
            .collect(Collectors.toList());
    }

    /**
     * 安全重启插件（带依赖检查）。
     */
    public CompletableFuture<Void> safeRestartPlugin(String pluginId) {
        return kernelService.getPlugin(pluginId)
            .map(plugin -> {
                // 检查是否有其他插件依赖此插件
                List<String> dependents = findDependentPlugins(pluginId);
                if (!dependents.isEmpty()) {
                    throw new IllegalStateException("无法重启插件 " + pluginId +
                        "，存在依赖插件: " + dependents);
                }
                return kernelService.restartPlugin(pluginId);
            })
            .orElseThrow(() -> new PluginNotFoundException(pluginId));
    }
}
```

## 监控与运维支持

### 关键性能指标 (KPI)

| 指标类别 | 指标名称 | 目标值 | 监控方式 | 告警阈值 |
|----------|----------|--------|----------|----------|
| **启动性能** | 内核启动时间 | ≤500ms | JMX MBean | >800ms |
| **启动性能** | 单插件启动时间 | ≤100ms | 事件监听 | >150ms |
| **内存使用** | 内核基础内存 | ≤50MB | JVM监控 | >80MB |
| **内存使用** | 单插件内存 | ≤20MB | ClassLoader监控 | >30MB |
| **可用性** | 插件启动成功率 | ≥99% | 统计计算 | <95% |
| **可用性** | 依赖解析成功率 | 100% | 异常监控 | <100% |

### JMX监控接口

```java
@MXBean
public interface NexusKernelMXBean {

    // 基础统计
    int getTotalPluginCount();
    int getActivePluginCount();
    int getFailedPluginCount();
    long getKernelStartTime();
    long getTotalStartupTime();

    // 性能指标
    double getAveragePluginStartTime();
    long getMaxPluginStartTime();
    String getSlowestStartingPlugin();

    // 内存统计
    long getKernelMemoryUsage();
    long getTotalPluginMemoryUsage();
    String[] getTopMemoryConsumingPlugins();

    // 操作方法
    void refreshPluginStatistics();
    String[] performHealthCheck();
    void forceGarbageCollection();
}
```

## 总结与架构价值

### 核心架构价值

1. **极致隔离**: 通过独立类加载器实现真正的插件隔离，杜绝依赖冲突
2. **精确控制**: 基于状态机的生命周期管理，确保插件行为可预测
3. **高度可扩展**: 微内核架构支持无限插件扩展，不影响核心稳定性
4. **运维友好**: 完整的监控体系和编程式管理接口，支持动态运维

### 设计模式应用

- **微内核模式**: 核心功能最小化，业务逻辑插件化
- **依赖注入模式**: 通过PluginContext提供服务发现和注入
- **状态机模式**: 严格的插件生命周期状态管理
- **委托模式**: 类加载器委托链实现精确的可见性控制
- **观察者模式**: 插件状态变化事件通知机制

### 技术创新点

1. **基于SemVer的依赖版本管理**: 支持语义化版本范围依赖
2. **包级别的可见性控制**: 通过exports/imports实现精确的API暴露
3. **异步生命周期管理**: 支持非阻塞的插件启停操作
4. **认知友好的错误处理**: 结构化的错误代码和详细的诊断信息

### 未来演进方向

- **热插拔支持**: 基于当前类加载器隔离机制，支持运行时插件更新
- **分布式插件**: 支持跨JVM的远程插件调用
- **插件市场**: 支持插件的动态下载和安装
- **AI辅助运维**: 基于监控数据的智能插件管理建议

这个微内核与插件生命周期设计为XKongCloud Commons Nexus框架提供了坚实的基础，确保了系统的稳定性、可扩展性和可维护性。
```
