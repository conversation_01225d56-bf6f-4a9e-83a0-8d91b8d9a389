# V4.0架构设计提示词

## 🏗️ 基于V3/V3.1的V4一体化架构设计

```
设计V4.0扫描器+生成器一体化架构（基于现有V3/V3.1架构，避免重复造轮子）：

核心设计原则：
1. 充分复用V3扫描器的成熟架构和文档解析逻辑
2. 充分复用V3.1生成器的模板生成和工作流机制
3. 在现有基础上增加AI增强层，而非重构
4. 保持现有接口和数据格式的兼容性

🔄 V4一体化架构优化原则（新增核心原则）：
5. 扫描器+生成器融合：统一的文档处理和生成流程
6. 多阶段AI协作：Phase1架构分析→Phase2实施计划→Phase3代码生成
7. 全流程质量门禁：95%置信度的端到端质量控制
8. AI认知边界管理：整个流程的认知约束和幻觉防护

🔧 扫描器处理优化原则（新增核心原则）：
5. 基于AI认知边界的智能文档切割策略
6. 针对复杂架构的结构化信息精准提取
7. 扫描器与AI协作的最佳实践集成
8. 防止AI幻觉和记忆溢出的强制约束机制

V4一体化增强组件（基于现有架构融合）：
1. V4统一文档处理引擎：V3扫描器 + AI增强分析 + 智能切割策略
2. V4多阶段生成引擎：V3.1生成器 + 多模型协作 + 质量门禁集成
3. V4全DeepSeek生态AI编排层：三阶段协作 + 专业化分工 + 结果融合
4. V4端到端质量保证系统：全流程监控 + 95%置信度门禁 + 自动回退机制

🛡️ V4一体化95%置信度质量门禁架构：
1. **Phase1架构分析质量门禁**：
   - DeepSeek-R1-0528架构分析 → 置信度评估 → ≥85%进入Phase2 / <85%重新分析
   - 架构准确性验证：目标≥90%（基于实测91.7%标准）

2. **Phase2实施计划质量门禁**：
   - DeepSeek-V3-0324实施计划生成 → 质量评估 → ≥85分进入Phase3 / <85分回退优化
   - 实施计划质量验证：目标≥90分（从实测81.8分提升）

3. **Phase3代码生成质量门禁**：
   - DeepCoder-14B代码生成 → 编译验证 → ≥90%通过率 / 不足时优化重生成
   - 代码质量验证：目标75-80分可编译运行

4. **V4一体化质量监控**：
   - 全流程实时置信度监控
   - 多阶段协作结果交叉验证
   - 自动回退到V3/V3.1策略
   - 人工介入通知和处理

技术架构要求（基于现有架构）：
- 保持现有的模块化设计，在关键节点增加AI增强和质量门禁
- 复用现有的异步处理机制，优化AI调用性能
- 扩展现有的错误处理，增加AI调用的降级策略和质量门禁
- 保持现有的配置管理，增加AI相关配置和置信度阈值

V4一体化数据流设计（基于现有流程，增加多阶段协作和质量门禁）：
设计文档 → V4统一文档处理引擎 → [Phase1架构分析门禁] → V4多阶段AI协作 → [Phase2实施计划门禁] → V4代码生成 → [Phase3代码质量门禁] → 高质量实施文档集合

请设计基于现有V3/V3.1架构的V4一体化增强方案，包含完整的多阶段质量门禁和回退机制。
```

## 🔧 基于V4实测的一体化多阶段AI协作引擎设计

```
设计V4.0扫描器+生成器一体化多阶段AI协作引擎（基于V4设计文档结构验证测试结果）：

核心发现（基于2025-01-14实测数据）：
1. 架构理解是核心突破点：91.7% vs 37.5%准确性（+144%）
2. 模型特性差异显著：需要专业化分工而非简单协同
3. 多阶段协作比并行协作更有效
4. 质量门禁比冗余性更重要
5. 一体化处理比分离式处理更高效

V4一体化多模型协作策略（深度优化）：

**Phase1 一体化架构分析与文档处理**：
- **统一文档处理**（V4文档处理引擎）：
  * 输入：原始设计文档
  * 处理：智能切割 + 结构化提取 + AI认知约束激活
  * 输出：structured_document_analysis.json

- **深度架构分析**（DeepSeek-R1-0528，84.1分最优）：
  * 输入：structured_document_analysis.json
  * 专注：微内核+服务总线架构理解 + 现状分析
  * 输出：architecture_analysis_primary.json
  * 质量目标：≥90%架构准确性

- **架构交叉验证**（DeepSeek-V3-0324，综合验证）：
  * 输入：architecture_analysis_primary.json + structured_document_analysis.json
  * 专注：架构一致性验证、潜在问题识别、实施可行性评估
  * 输出：architecture_validation_report.json
  * 协作价值：发现主分析的盲点和不一致

- **Phase1融合优化**（V4一体化融合引擎）：
  * 输入：primary + validation + document_analysis结果
  * 输出：optimized_architecture_blueprint.json
  * 目标：≥90%架构准确性（基于交叉验证提升）

**Phase2 实施计划多步推导**：
- **框架设计**（DeepSeek-V3-0324，综合能力强）：
  * 输入：optimized_architecture_blueprint.json
  * 专注：实施框架、阶段划分、关键里程碑
  * 输出：implementation_framework.json

- **细节补充**（DeepSeek-R1-0528，架构一致性保证）：
  * 输入：implementation_framework.json + architecture_blueprint
  * 专注：确保实施细节与架构设计一致
  * 输出：detailed_implementation_steps.json

- **风险评估**（DeepCoder-14B-Preview，技术可行性）：
  * 输入：detailed_implementation_steps.json
  * 专注：技术风险、实现难度、代码复杂度评估
  * 输出：risk_assessment_report.json

- **最终融合**：
  * 输入：framework + details + risk_assessment
  * 输出：comprehensive_implementation_plan.json
  * 目标：≥90分实施质量（通过多步推导优化）

**Phase3 代码生成与质量保证**：
- **核心代码生成**（DeepCoder-14B-Preview，代码专家）：
  * 输入：comprehensive_implementation_plan.json
  * 专注：核心类、接口、配置文件生成
  * 输出：core_code_templates.json

- **架构合规检查**（DeepSeek-R1-0528，架构一致性）：
  * 输入：core_code_templates.json + architecture_blueprint
  * 专注：代码与架构设计的一致性检查
  * 输出：architecture_compliance_report.json

- **代码质量优化**（DeepSeek-V3-0324，综合优化）：
  * 输入：core_code + compliance_report
  * 专注：代码质量、最佳实践、性能优化
  * 输出：optimized_production_code.json

**中间结果协作价值分析**：
1. **架构一致性保证**：通过多模型交叉验证确保架构理解准确
2. **实施可行性验证**：通过技术专家评估确保实施方案可行
3. **质量渐进提升**：通过多步推导逐步优化结果质量
4. **风险提前识别**：通过专业化分析提前发现潜在问题
5. **知识互补**：不同模型的专业优势互补，避免单一模型局限

🛡️ 基于V4实测的质量门禁机制：
1. **阶段性质量门禁**：
   - Phase1门禁：架构准确性≥85%（基于实测91.7%标准）
   - Phase2门禁：实施计划质量≥85分（基于实测84.1分标准）
   - Phase3门禁：代码可编译性≥90%
   - 综合门禁：整体置信度≥80分

2. **质量门禁决策**：
   - 达标：进入下一阶段
   - 不达标：当前阶段重新处理或回退到上一阶段
   - 连续失败：回退到V3/V3.1原始策略

3. **跨模型验证机制**：
   - Phase1结果由Phase2模型验证架构一致性
   - Phase2结果由Phase3模型验证技术可行性
   - 最终结果进行交叉验证确保一致性

技术实现（基于现有V3架构）：
- 复用V3的异步调用框架，增加AI编排层和质量门禁
- 智能超时控制（单模型120秒，总计4分钟）
- 错误恢复机制（重试、降级、回退到V3.1逻辑）
- 结果缓存优化（复用相似请求结果）
- **置信度评估引擎**（新增核心组件）

性能目标（基于V4实测数据调整）：
- 总响应时间：180-360秒（基于实测30-120秒/阶段 × 3阶段）
- 架构准确性：≥90%（已验证91.7%）
- 整体置信度：≥85分（已验证84.1分）
- 实施计划质量：≥90分（需要从81.8分提升）
- JSON使用率：≥95%（已验证96.7%-100%）

🕐 运行时间控制和进度提醒机制：
1. **多线程协作优化**：
   - 主力架构师（DeepSeek V3 0324）：处理60-70%核心任务
   - 备用快速生成（DeepSeek R1 0528）：处理20-25%基础任务
   - 代码专家（DeepCoder-14B）：处理10-15%技术细节
   - 并行执行，最大化效率

2. **实时进度提醒**：
   - 进度条显示：[████████████████████████████████] 85%
   - 当前状态：正在处理架构设计 | 质量: 87.5/100 | 剩余: 45s
   - 阶段提醒：步骤3/7完成，AI协作处理中...

3. **时间控制策略**：
   - 单个AI模型最大120秒超时
   - 总体处理时间控制在4分钟内
   - 超时自动切换到备用模型或回退策略

请设计基于现有架构的AI编排引擎增强方案，包含完整的质量门禁机制和进度控制。
```

## 📊 基于V3.1的质量保证系统增强

```
设计V4.0质量保证系统（基于V3.1现有质量体系增强）：

质量维度（基于V3.1扩展）：
1. 技术准确性（代码语法、架构一致性）- 重点提升架构准确性
2. 完整性（文档覆盖率、配置完整度）- 目标65-70%覆盖率
3. 可用性（编译通过率、部署成功率）- 目标75-80分代码质量
4. 一致性（多AI结果的协调性）- 全DeepSeek生态协同

验证机制（基于现有机制增强）：
- 复用V3.1的静态代码分析，增加AI质量检查
- 扩展现有的架构一致性检查，重点改进架构理解
- 保持现有的配置验证，增加AI填充内容验证
- 复用现有的集成测试，增加AI增强功能测试

🛡️ 95%置信度算法（基于V3.1增强）：
V3.1原有7维度置信度 + 全DeepSeek生态AI增强置信度 + 架构准确性专项评估 + **95%置信度门禁评估**

**核心置信度计算公式**：
```
综合置信度 = (V3.1基础置信度 × 0.3) + (AI增强置信度 × 0.4) + (架构准确性 × 0.2) + (质量验证置信度 × 0.1)

质量门禁决策：
- 综合置信度 ≥ 95%：采用AI增强结果
- 综合置信度 < 95%：回退到V3/V3.1原始策略
- 90% ≤ 综合置信度 < 95%：可选择性人工介入
```

**质量门禁触发机制**：
1. **JSON填充阶段**：AI填充结果置信度<95% → 回退V3扫描器原始JSON
2. **实施计划生成阶段**：生成结果置信度<95% → 回退V3.1生成器原始策略
3. **人工介入触发**：自动通知主力IDE AI，提供详细分析报告

质量反馈循环（基于现有流程）：
用户反馈 → V3.1质量问题识别 + AI质量分析 + **置信度分析** → 模型调优 → 系统改进

监控指标（扩展现有指标）：
- 复用V3.1的实时质量分数，增加AI增强评估
- **新增置信度达标率监控**（核心指标）
- **新增回退策略触发频率监控**
- 新增AI模型表现趋势监控
- 保持现有的用户满意度指标
- 扩展现有的系统性能指标

重点改进领域（基于测试发现）：
- 架构准确性提升：从43.8%提升到50%+
- JSON使用率监控：从1.5%提升到75%
- AI填充质量：确保95%完成率
- 代码编译通过率：确保75-80分质量
- **置信度稳定性**：确保95%置信度达标率>90%

请设计基于V3.1质量体系的增强方案，包含完整的95%置信度门禁机制。
```

## 🔍 V3/V3.1源码深度分析提示词

```
V3/V3.1源码深度分析（避免重复造轮子的关键）：

V3扫描器源码分析重点：
1. 核心架构理解：
   - 文档解析引擎的实现逻辑
   - JSON生成器的核心算法
   - 配置管理和参数处理机制
   - 错误处理和异常恢复逻辑

2. 关键模块分析：
   - 文档解析模块：如何从Markdown提取结构化信息
   - JSON构建模块：如何生成800-1000个key的复杂结构
   - 验证模块：如何确保JSON的完整性和正确性
   - 输出模块：如何格式化和保存结果

3. 扩展点识别：
   - 哪些地方可以插入AI增强逻辑
   - 哪些接口可以扩展而不影响现有功能
   - 哪些配置可以增加AI相关参数

V3.1生成器源码分析重点：
1. 核心架构理解：
   - JSON读取和解析机制
   - 模板生成引擎的实现
   - 文档输出的工作流程
   - 质量控制和验证逻辑

2. 关键模块分析：
   - JSON使用模块：为什么只使用1.5%的key
   - 模板引擎：如何基于JSON生成文档
   - 工作流控制：如何管理生成过程
   - 质量评估：如何计算置信度和质量分数

3. 优化机会识别：
   - 如何扩展JSON使用率到75%
   - 如何集成AI增强功能
   - 如何提升架构理解能力
   - 如何保持向后兼容性

复用策略制定：
- 保留哪些核心逻辑不变
- 扩展哪些功能模块
- 新增哪些AI增强组件
- 如何确保平滑升级

请制定详细的源码分析计划和复用策略。
```

## 🔄 数据流和接口设计提示词

```
设计V4.0数据流和接口规范（基于V3/V3.1现有架构）：

数据流架构（基于现有流程增强）：
1. 输入层：复用V3的设计文档解析，增加预处理优化
2. 处理层：扩展V3的JSON生成，增加AI填充（95%完成率）
3. 生成层：增强V3.1的文档生成，提升JSON使用率到75%
4. 输出层：保持现有输出格式，增加AI质量报告

接口规范（基于现有接口扩展）：
- 保持V3/V3.1的现有API接口，确保向后兼容
- 扩展现有的错误处理，增加AI调用相关状态码
- 保持现有的版本控制机制
- 扩展现有的API文档，增加AI增强功能说明

数据格式（基于现有格式优化）：
- 输入：保持现有的Markdown设计文档格式
- 中间：优化现有的JSON结构（从800-1000个key精简到300个有效key）
- 输出：保持现有的实施文档集合格式
- 元数据：扩展现有的质量报告，增加AI增强评估

安全和权限（基于现有机制）：
- 复用现有的API访问控制，增加AI调用权限管理
- 保持现有的数据加密机制
- 扩展现有的审计日志，增加AI调用追踪
- 复用现有的数据备份机制

关键设计原则：
- 最大化复用现有接口和数据格式
- 在现有基础上增加AI增强功能
- 确保平滑升级和向后兼容
- 避免重新设计已验证的机制

请设计基于现有架构的数据流增强方案。
```

## 🚀 部署和运维设计提示词

```
设计V4.0部署和运维方案：

部署架构：
- 容器化部署（Docker + Kubernetes）
- 微服务独立部署和扩展
- 负载均衡和高可用配置
- 蓝绿部署和滚动更新

运维监控：
- 应用性能监控（APM）
- 日志聚合和分析
- 告警和通知机制
- 自动化运维脚本

扩展性设计：
- 水平扩展支持
- AI模型的动态加载
- 配置热更新
- 插件化架构

灾难恢复：
- 数据备份策略
- 故障转移机制
- 服务降级方案
- 快速恢复流程

成本优化：
- 资源使用监控
- AI调用成本控制
- 缓存策略优化
- 自动伸缩配置

请设计完整的部署运维方案和最佳实践。
```

---

*基于微服务架构和云原生最佳实践*  
*确保系统的可扩展性和可维护性*  
*创建时间：2025-06-14*
