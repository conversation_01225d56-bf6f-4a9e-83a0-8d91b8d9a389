# V4 - 核心数据模型设计

## 📋 实施概述
**文档ID**: V4-PLAN-002  
**阶段**: 核心数据模型设计  
**置信度**: 95%  

## 🎯 核心目标
基于Pydantic v2设计V4系统核心数据模型，支持全景拼图认知、多维抽象映射和AI增强的数据结构。

## 🔧 核心实施代码

### 基础模型 - src/v4_scaffolding/models/base.py
```python
"""V4系统基础数据模型"""
from __future__ import annotations
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4
from pydantic import BaseModel, Field, ConfigDict, computed_field


class AnalysisStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class DimensionType(str, Enum):
    DESIGN = "design"
    CODE = "code"
    BUSINESS = "business"
    TEST = "test"
    OPERATIONS = "operations"


class V4BaseModel(BaseModel, ABC):
    """V4系统基础模型"""
    
    model_config = ConfigDict(
        validate_assignment=True,
        extra="forbid",
        str_strip_whitespace=True,
    )
    
    id: UUID = Field(default_factory=uuid4)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    confidence_score: float = Field(default=0.0, ge=0.0, le=1.0)
    
    @abstractmethod
    def validate_integrity(self) -> bool:
        """验证模型完整性"""
        pass
    
    @computed_field
    @property
    def is_valid(self) -> bool:
        try:
            return self.validate_integrity()
        except Exception:
            return False


class DocumentInfo(BaseModel):
    """文档信息模型"""
    path: Path
    name: str
    size: int = Field(ge=0)
    last_modified: datetime
    
    @computed_field
    @property
    def relative_path(self) -> str:
        return str(self.path).replace("\\", "/")


class ProcessingResult(V4BaseModel):
    """处理结果模型"""
    status: AnalysisStatus = Field(default=AnalysisStatus.PENDING)
    processing_time: float = Field(default=0.0, ge=0.0)
    error_message: Optional[str] = None
    warnings: List[str] = Field(default_factory=list)
    
    def validate_integrity(self) -> bool:
        if self.status == AnalysisStatus.FAILED and not self.error_message:
            return False
        return True
```

### 全景拼图模型 - src/v4_scaffolding/models/panoramic_models.py
```python
"""全景拼图认知模型"""
from typing import Dict, List, Optional
from pydantic import Field, computed_field
from .base import V4BaseModel, DocumentInfo


class ArchitecturalPosition(V4BaseModel):
    """架构位置模型"""
    layer: str = Field(description="架构层次")
    component_type: str = Field(description="组件类型")
    business_domain: Optional[str] = None
    technical_stack: List[str] = Field(default_factory=list)
    
    def validate_integrity(self) -> bool:
        return bool(self.layer and self.component_type)


class ContextDependency(V4BaseModel):
    """上下文依赖模型"""
    prerequisite_dependencies: List[str] = Field(default_factory=list)
    impact_targets: List[str] = Field(default_factory=list)
    horizontal_collaborations: List[str] = Field(default_factory=list)
    constraint_conditions: List[str] = Field(default_factory=list)
    
    def validate_integrity(self) -> bool:
        return any([
            self.prerequisite_dependencies,
            self.impact_targets,
            self.horizontal_collaborations,
            self.constraint_conditions
        ])
    
    @computed_field
    @property
    def total_dependency_count(self) -> int:
        return sum([
            len(self.prerequisite_dependencies),
            len(self.impact_targets),
            len(self.horizontal_collaborations),
            len(self.constraint_conditions)
        ])


class RoleFunction(V4BaseModel):
    """角色功能模型"""
    core_functions: List[str] = Field(default_factory=list)
    solved_problems: List[str] = Field(default_factory=list)
    value_contributions: List[str] = Field(default_factory=list)
    importance_level: float = Field(ge=0.0, le=1.0)
    
    def validate_integrity(self) -> bool:
        return bool(self.core_functions and self.importance_level > 0)


class PanoramicPositioning(V4BaseModel):
    """全景拼图定位模型"""
    document_info: DocumentInfo
    architectural_position: ArchitecturalPosition
    context_dependency: ContextDependency
    role_function: RoleFunction
    
    # 全景视图状态
    system_boundary_identified: bool = Field(default=False)
    layer_positioning_clear: bool = Field(default=False)
    relationship_network_complete: bool = Field(default=False)
    
    def validate_integrity(self) -> bool:
        return all([
            self.architectural_position.is_valid,
            self.context_dependency.is_valid,
            self.role_function.is_valid,
            self.system_boundary_identified,
            self.layer_positioning_clear
        ])
    
    @computed_field
    @property
    def positioning_completeness(self) -> float:
        """定位完整度计算"""
        factors = [
            self.system_boundary_identified,
            self.layer_positioning_clear,
            self.relationship_network_complete,
            bool(self.architectural_position.business_domain),
            len(self.architectural_position.technical_stack) > 0,
            self.context_dependency.total_dependency_count > 0,
            len(self.role_function.core_functions) > 0
        ]
        return sum(factors) / len(factors)
```

### 认知构建模型 - src/v4_scaffolding/models/cognitive_models.py
```python
"""认知构建模型"""
from typing import Dict, List, Optional
from pydantic import Field, computed_field
from .base import V4BaseModel, AnalysisStatus


class CognitiveLevel(V4BaseModel):
    """认知层次模型"""
    level_name: str
    concepts: List[str] = Field(default_factory=list)
    understanding_depth: float = Field(ge=0.0, le=1.0)
    relationships: Dict[str, List[str]] = Field(default_factory=dict)
    
    def validate_integrity(self) -> bool:
        return bool(self.level_name and self.concepts)
    
    @computed_field
    @property
    def concept_count(self) -> int:
        return len(self.concepts)
    
    @computed_field
    @property
    def relationship_density(self) -> float:
        if not self.concepts:
            return 0.0
        total_relations = sum(len(relations) for relations in self.relationships.values())
        max_possible = len(self.concepts) * (len(self.concepts) - 1)
        return total_relations / max_possible if max_possible > 0 else 0.0


class ProgressiveCognition(V4BaseModel):
    """渐进式认知构建模型"""
    high_level: CognitiveLevel
    medium_level: CognitiveLevel  
    detail_level: CognitiveLevel
    
    construction_status: AnalysisStatus = Field(default=AnalysisStatus.PENDING)
    cognitive_load: float = Field(ge=0.0, le=1.0, default=0.0)
    
    # 缺啥补啥策略
    identified_gaps: List[str] = Field(default_factory=list)
    completion_strategies: List[str] = Field(default_factory=list)
    
    def validate_integrity(self) -> bool:
        return all([
            self.high_level.is_valid,
            self.medium_level.is_valid,
            self.detail_level.is_valid,
            self.cognitive_load <= 1.0
        ])
    
    @computed_field
    @property
    def total_concepts(self) -> int:
        return (
            self.high_level.concept_count +
            self.medium_level.concept_count +
            self.detail_level.concept_count
        )
    
    def add_gap(self, gap: str, strategy: Optional[str] = None) -> None:
        """添加识别的缺口"""
        self.identified_gaps.append(gap)
        if strategy:
            self.completion_strategies.append(strategy)


class CognitiveConstraintCheck(V4BaseModel):
    """认知约束检查模型"""
    max_elements_check: bool = Field(default=True)
    concept_overload_risk: float = Field(ge=0.0, le=1.0, default=0.0)
    memory_pressure: float = Field(ge=0.0, le=1.0, default=0.0)
    context_switching_cost: float = Field(ge=0.0, le=1.0, default=0.0)
    
    violations: List[str] = Field(default_factory=list)
    recommendations: List[str] = Field(default_factory=list)
    
    def validate_integrity(self) -> bool:
        return True
    
    @computed_field
    @property
    def overall_constraint_score(self) -> float:
        return 1.0 - max(
            self.concept_overload_risk,
            self.memory_pressure,
            self.context_switching_cost
        )
```

## 🧪 核心测试用例

### tests/unit/test_models.py
```python
"""数据模型测试"""
import pytest
from datetime import datetime
from pathlib import Path
from uuid import UUID

from v4_scaffolding.models.base import V4BaseModel, DocumentInfo, AnalysisStatus
from v4_scaffolding.models.panoramic_models import ArchitecturalPosition, PanoramicPositioning
from v4_scaffolding.models.cognitive_models import CognitiveLevel, ProgressiveCognition


class ConcreteModel(V4BaseModel):
    """测试用具体模型"""
    name: str
    value: int
    
    def validate_integrity(self) -> bool:
        return bool(self.name and self.value > 0)


class TestV4BaseModel:
    """基础模型测试"""
    
    def test_model_creation(self):
        """测试模型创建"""
        model = ConcreteModel(name="test", value=10)
        
        assert isinstance(model.id, UUID)
        assert isinstance(model.created_at, datetime)
        assert model.name == "test"
        assert model.value == 10
        assert model.is_valid is True
    
    def test_invalid_model(self):
        """测试无效模型"""
        model = ConcreteModel(name="", value=0)
        assert model.is_valid is False


class TestArchitecturalPosition:
    """架构位置测试"""
    
    def test_valid_position(self):
        """测试有效架构位置"""
        position = ArchitecturalPosition(
            layer="business",
            component_type="service",
            business_domain="user_management",
            technical_stack=["Python", "FastAPI"]
        )
        
        assert position.is_valid is True
        assert position.layer == "business"
        assert len(position.technical_stack) == 2
    
    def test_invalid_position(self):
        """测试无效架构位置"""
        position = ArchitecturalPosition(layer="", component_type="")
        assert position.is_valid is False


class TestCognitiveLevel:
    """认知层次测试"""
    
    def test_cognitive_level(self):
        """测试认知层次"""
        level = CognitiveLevel(
            level_name="high_level",
            concepts=["architecture", "design"],
            understanding_depth=0.8,
            relationships={"architecture": ["design"]}
        )
        
        assert level.concept_count == 2
        assert level.understanding_depth == 0.8
        assert level.relationship_density > 0
    
    def test_relationship_density(self):
        """测试关系密度计算"""
        level = CognitiveLevel(
            level_name="test",
            concepts=["A", "B", "C"],
            relationships={"A": ["B"], "B": ["C"]}
        )
        
        # 3个概念，最大关系数 = 3 * 2 = 6，实际关系数 = 2
        expected_density = 2 / 6
        assert abs(level.relationship_density - expected_density) < 0.01


class TestProgressiveCognition:
    """渐进式认知测试"""
    
    def test_progressive_cognition(self):
        """测试渐进式认知"""
        high = CognitiveLevel(
            level_name="high", 
            concepts=["system"], 
            understanding_depth=0.9
        )
        medium = CognitiveLevel(
            level_name="medium", 
            concepts=["module"], 
            understanding_depth=0.8
        )
        detail = CognitiveLevel(
            level_name="detail", 
            concepts=["function"], 
            understanding_depth=0.7
        )
        
        cognition = ProgressiveCognition(
            high_level=high,
            medium_level=medium,
            detail_level=detail,
            cognitive_load=0.6
        )
        
        assert cognition.total_concepts == 3
        assert cognition.is_valid is True
    
    def test_gap_management(self):
        """测试缺口管理"""
        high = CognitiveLevel(level_name="high", concepts=["system"], understanding_depth=0.9)
        medium = CognitiveLevel(level_name="medium", concepts=["module"], understanding_depth=0.8)
        detail = CognitiveLevel(level_name="detail", concepts=["function"], understanding_depth=0.7)
        
        cognition = ProgressiveCognition(
            high_level=high,
            medium_level=medium,
            detail_level=detail
        )
        
        cognition.add_gap("missing design", "add ERD")
        
        assert len(cognition.identified_gaps) == 1
        assert len(cognition.completion_strategies) == 1
```

## 📋 验收标准

### 功能验收
- [ ] 基础模型类型安全 (100%)
- [ ] 全景拼图数据模型 (100%)
- [ ] 认知构建模型 (100%)
- [ ] Pydantic v2特性使用 (100%)

### 质量验收
- [ ] 测试覆盖率 ≥ 95%
- [ ] 类型注解完整性 100%
- [ ] 数据验证准确率 ≥ 98%

### 性能验收
- [ ] 模型创建时间 ≤ 1ms
- [ ] 数据验证时间 ≤ 5ms

## 🚀 下一步骤
1. **03-全景拼图认知引擎.md** - 核心认知算法
2. **04-算法驱动AI增强引擎.md** - AI增强机制
3. **05-多维抽象映射引擎.md** - 抽象映射算法 