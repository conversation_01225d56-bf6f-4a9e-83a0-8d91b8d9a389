# JSON输出接口设计文档

## 📋 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-12
- **设计目标**: 为APIHttpClient添加统一的JSON输出支持
- **适用模型**: Gemini 2.5 Pro, DeepSeek R1-0528, DeepSeek V3-0324, Qwen 3-235B-A22B

## 🎯 设计目标

### 核心原则
1. **调用者透明**: 调用者只需指定JSON输出需求，不关心具体模型实现
2. **APIHttpClient适配**: 内部根据模型类型适配不同的JSON实现方式
3. **统一接口**: 所有模型使用相同的调用接口
4. **输出不解析**: APIHttpClient只负责获取JSON，调用者负责解析

### 业务需求
- 支持结构化数据输出（验证报告、API配置、架构元数据）
- 兼容不同模型的JSON实现差异
- 提供可选的JSON Schema约束
- 保持向后兼容性

## 🔧 接口设计

### 方法签名扩展

```python
def perform_real_http_test(self, api_key: str, api_url: str, model_name: str,
                          interface_type: str, test_content: str = None, 
                          role: str = None, task_type: str = None,
                          # 新增JSON输出参数
                          json_output: bool = False,
                          json_schema: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    执行真实的HTTP API测试，支持JSON输出

    Args:
        api_key: API密钥
        api_url: API地址
        model_name: 模型名称
        interface_type: 接口类型
        test_content: 测试内容
        role: 调用者指定的角色
        task_type: 调用者指定的任务类型
        json_output: 是否要求JSON输出
        json_schema: JSON结构定义（可选）

    Returns:
        测试结果字典，包含JSON格式的响应内容
    """
```

### 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `json_output` | `bool` | 否 | 是否要求JSON输出，默认False |
| `json_schema` | `Dict[str, Any]` | 否 | JSON结构定义，用于约束输出格式 |

## 🎭 模型适配策略

### Gemini 2.5 Pro（严格JSON模式）

**特点**: 支持原生JSON模式，可靠性95%+

```python
# 配置示例
if json_output:
    payload["generationConfig"]["response_mime_type"] = "application/json"
    
    if json_schema:
        payload["generationConfig"]["response_schema"] = json_schema
    else:
        # 默认自由JSON格式
        payload["generationConfig"]["response_schema"] = {
            "type": "object",
            "properties": {},
            "additionalProperties": True
        }
```

**适用场景**: 结构化验证报告、复杂数据结构

### DeepSeek V3-0324（OpenAI兼容JSON模式）

**特点**: 支持OpenAI兼容的JSON模式，可靠性90%+

```python
# 配置示例
if json_output:
    payload["response_format"] = {"type": "json_object"}
    
    # 在system message中添加JSON格式要求
    if json_schema:
        schema_prompt = f"Output must be valid JSON matching this schema: {json.dumps(json_schema)}"
    else:
        schema_prompt = "Output must be valid JSON format."
```

**适用场景**: API配置生成、代码相关结构化数据

### DeepSeek R1-0528（提示工程JSON模式）

**特点**: 依赖提示工程，可靠性70%+，可能需要后处理

```python
# 配置示例
if json_output:
    # 尝试使用response_format（可能不完全支持）
    payload["response_format"] = {"type": "json_object"}
    
    # 强化提示工程
    json_instruction = "IMPORTANT: Output ONLY valid JSON format, no markdown, no explanations."
    if json_schema:
        json_instruction += f" Follow this schema: {json.dumps(json_schema)}"
```

**适用场景**: 架构元数据生成（建议配合后处理）

### Qwen 3-235B-A22B（企业级JSON模式）

**特点**: 支持OpenAI兼容JSON模式，企业场景可靠性85-90%+

```python
# 配置示例
if json_output:
    payload["response_format"] = {"type": "json_object"}

    # 针对企业级场景优化提示
    if json_schema:
        schema_prompt = f"Generate enterprise-grade JSON matching schema: {json.dumps(json_schema)}"
    else:
        schema_prompt = "Output valid JSON format for enterprise applications."

    # 在user message中添加企业级JSON要求
    for message in payload.get("messages", []):
        if message.get("role") == "user":
            message["content"] += f"\n\n{schema_prompt}"
            break
```

**适用场景**: 企业架构设计、业务配置生成、微服务API定义、Java Spring Boot配置

## 💻 实现方案

### 核心方法扩展

#### _build_interface_specific_payload方法

```python
def _build_interface_specific_payload(self, model_name: str, interface_type: str,
                                    test_content: str = None, role: str = None, 
                                    task_type: str = None, json_output: bool = False,
                                    json_schema: Dict[str, Any] = None) -> Dict[str, Any]:
    """根据接口类型构建特定的测试载荷，支持JSON输出"""
    
    # 现有逻辑保持不变...
    
    if json_output:
        if interface_type.lower() == 'gemini':
            # Gemini严格JSON模式
            payload = self._add_gemini_json_config(payload, json_schema)
        elif 'qwen' in model_name.lower():
            # Qwen企业级JSON模式
            payload = self._add_qwen_json_config(payload, json_schema, test_content)
        else:
            # OpenAI兼容JSON模式
            payload = self._add_openai_json_config(payload, json_schema, test_content)
    
    return payload
```

#### JSON配置添加方法

```python
def _add_gemini_json_config(self, payload: Dict, json_schema: Dict = None) -> Dict:
    """为Gemini添加JSON输出配置"""
    payload["generationConfig"]["response_mime_type"] = "application/json"
    
    if json_schema:
        payload["generationConfig"]["response_schema"] = json_schema
    else:
        payload["generationConfig"]["response_schema"] = {
            "type": "object",
            "additionalProperties": True
        }
    
    print("🔧 启用Gemini严格JSON模式")
    return payload

def _add_openai_json_config(self, payload: Dict, json_schema: Dict = None, 
                           content: str = None) -> Dict:
    """为OpenAI兼容接口添加JSON输出配置"""
    payload["response_format"] = {"type": "json_object"}
    
    # 增强提示内容
    json_instruction = "\n\nIMPORTANT: Output ONLY valid JSON format."
    if json_schema:
        json_instruction += f" Follow this schema: {json.dumps(json_schema)}"
    
    # 修改用户消息
    for message in payload.get("messages", []):
        if message.get("role") == "user":
            message["content"] += json_instruction
            break
    
    print("🔧 启用OpenAI兼容JSON模式")
    return payload

def _add_qwen_json_config(self, payload: Dict, json_schema: Dict = None,
                         content: str = None) -> Dict:
    """为Qwen添加企业级JSON输出配置"""
    payload["response_format"] = {"type": "json_object"}

    # 企业级JSON提示优化
    if json_schema:
        json_instruction = f"\n\nAs an enterprise architect, generate professional JSON output matching this schema: {json.dumps(json_schema)}"
    else:
        json_instruction = "\n\nAs an enterprise architect, output valid JSON format for enterprise applications."

    json_instruction += "\nRequirements: Valid JSON only, no markdown, no explanations, enterprise-grade structure."

    # 修改用户消息
    for message in payload.get("messages", []):
        if message.get("role") == "user":
            message["content"] += json_instruction
            break

    print("🔧 启用Qwen企业级JSON模式")
    return payload
```

## 📊 模型可靠性对比

| 模型 | JSON支持等级 | 可靠性 | 实现方式 | 适用场景 |
|------|-------------|--------|----------|----------|
| **Gemini 2.5 Pro** | 严格模式 | 95%+ | response_mime_type + response_schema | 结构化数据、验证报告 |
| **Qwen 3-235B-A22B** | 企业级模式 | 85-90%+ | response_format + 企业级提示 | Java企业架构、业务建模、Spring配置 |
| **DeepSeek V3-0324** | 兼容模式 | 90%+ | response_format + 提示增强 | API配置、代码生成 |
| **DeepSeek R1-0528** | 提示工程 | 70%+ | response_format + 强化提示 | 架构元数据（需后处理） |

## 🚀 使用示例

### 基础JSON输出（无Schema约束）

```python
result = api_http_client.perform_real_http_test(
    api_key="your-key",
    api_url="your-url",
    model_name="gemini-2.5-pro",
    interface_type="gemini",
    test_content="Generate a user profile with name, age, and email",
    json_output=True  # 👈 要求JSON输出
)

# 调用者解析JSON
if result.get("success"):
    json_data = json.loads(result["response_content"])
    print(json_data)
```

### 带Schema约束的JSON输出

```python
user_schema = {
    "type": "object",
    "properties": {
        "name": {"type": "string"},
        "age": {"type": "integer", "minimum": 0},
        "email": {"type": "string", "format": "email"}
    },
    "required": ["name", "age", "email"]
}

result = api_http_client.perform_real_http_test(
    api_key="your-key",
    api_url="your-url",
    model_name="qwen-3-235b-a22b",  # 👈 使用Qwen企业级模型
    interface_type="openai",
    test_content="Generate a user profile for enterprise system",
    json_output=True,
    json_schema=user_schema  # 👈 指定JSON结构
)
```

### 企业级Spring Boot配置生成

```python
spring_config_schema = {
    "type": "object",
    "properties": {
        "server": {
            "type": "object",
            "properties": {
                "port": {"type": "integer"},
                "servlet": {
                    "type": "object",
                    "properties": {
                        "context-path": {"type": "string"}
                    }
                }
            }
        },
        "spring": {
            "type": "object",
            "properties": {
                "datasource": {
                    "type": "object",
                    "properties": {
                        "url": {"type": "string"},
                        "username": {"type": "string"},
                        "driver-class-name": {"type": "string"}
                    }
                }
            }
        }
    },
    "required": ["server", "spring"]
}

result = api_http_client.perform_real_http_test(
    api_key="your-key",
    api_url="your-url",
    model_name="qwen-3-235b-a22b",
    interface_type="openai",
    test_content="Generate Spring Boot application.yml configuration for microservice",
    json_output=True,
    json_schema=spring_config_schema
)
```

### 角色化JSON输出

```python
validation_schema = {
    "type": "object",
    "properties": {
        "system_name": {"type": "string"},
        "status": {"type": "string", "enum": ["pass", "fail"]},
        "issues": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "issue_id": {"type": "string"},
                    "description": {"type": "string"}
                }
            }
        }
    },
    "required": ["system_name", "status", "issues"]
}

result = api_http_client.perform_real_http_test(
    api_key="your-key",
    api_url="your-url",
    model_name="gemini-2.5-pro",
    interface_type="gemini",
    role="架构专家",  # 使用角色配置
    test_content="Generate a system validation report",
    json_output=True,
    json_schema=validation_schema
)
```

## ⚠️ 错误处理和后备机制

### JSON验证和修复

```python
def _validate_json_output(self, response_content: str, model_name: str) -> Dict:
    """验证和修复JSON输出"""
    try:
        return json.loads(response_content)
    except json.JSONDecodeError as e:
        print(f"⚠️ JSON解析失败 ({model_name}): {e}")

        # 尝试提取JSON部分（处理markdown包装）
        if "```json" in response_content:
            json_part = response_content.split("```json")[1].split("```")[0]
            try:
                return json.loads(json_part)
            except:
                pass

        # 尝试提取大括号内容
        import re
        json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
        if json_match:
            try:
                return json.loads(json_match.group())
            except:
                pass

        # 返回错误信息
        return {
            "error": "JSON_PARSE_FAILED",
            "raw_content": response_content,
            "model": model_name,
            "suggestion": "Consider using a more reliable model or adjust the prompt"
        }
```

### 模型降级策略

```python
def _get_json_reliability_score(self, model_name: str) -> float:
    """获取模型JSON输出可靠性评分"""
    reliability_map = {
        "gemini-2.5-pro": 0.95,
        "qwen-3-235b-a22b": 0.87,  # 👈 新增Qwen企业级可靠性
        "qwen": 0.85,  # 通用Qwen模型
        "deepseek-ai/DeepSeek-V3-0324": 0.90,
        "deepseek-ai/DeepSeek-R1-0528": 0.70
    }

    for model_key, score in reliability_map.items():
        if model_key in model_name.lower():
            return score

    return 0.60  # 默认可靠性
```

## 📈 性能优化建议

### 1. Schema复杂度控制
- **简单Schema**: 嵌套层级 ≤ 3层，属性数量 ≤ 10个
- **复杂Schema**: 考虑拆分为多个简单Schema
- **数组限制**: 避免过深的数组嵌套

### 2. 提示工程优化
```python
# 针对不同模型的提示优化
def _optimize_json_prompt(self, content: str, model_name: str, json_schema: Dict = None) -> str:
    """根据模型特性优化JSON提示"""

    if "qwen" in model_name.lower():
        # Qwen模型企业级JSON优化
        prefix = "As an enterprise architect, generate professional JSON output. "
        if json_schema:
            prefix += f"Follow this enterprise schema precisely: {json.dumps(json_schema)} "
        prefix += "Requirements: Valid JSON only, no explanations, enterprise-grade structure. "
        return prefix + content

    elif "r1" in model_name.lower():
        # R1模型需要更强的JSON约束
        prefix = "You MUST output ONLY valid JSON. No explanations, no markdown, no extra text. "
        if json_schema:
            prefix += f"Follow this exact schema: {json.dumps(json_schema)} "
        return prefix + content

    elif "v3" in model_name.lower():
        # V3模型适合结构化提示
        if json_schema:
            return f"{content}\n\nOutput format: JSON matching schema {json.dumps(json_schema)}"
        return f"{content}\n\nOutput format: JSON"

    else:
        # Gemini等支持原生JSON的模型
        return content
```

### 3. 缓存和重试机制
```python
def _retry_json_generation(self, payload: Dict, max_retries: int = 3) -> Dict:
    """JSON生成重试机制"""
    for attempt in range(max_retries):
        try:
            response = self._send_request(payload)
            json_data = json.loads(response["content"])
            return {"success": True, "data": json_data}
        except json.JSONDecodeError:
            if attempt < max_retries - 1:
                # 增强JSON约束
                payload = self._enhance_json_constraints(payload)
                continue
            else:
                return {"success": False, "raw_content": response["content"]}
```

## 🔍 测试用例

### 基础功能测试
```python
def test_basic_json_output():
    """测试基础JSON输出功能"""
    test_cases = [
        {
            "model": "gemini-2.5-pro",
            "interface": "gemini",
            "content": "Generate a simple user object",
            "expected_keys": ["name", "age"]
        },
        {
            "model": "qwen-3-235b-a22b",
            "interface": "openai",
            "content": "Generate an enterprise API configuration",
            "expected_keys": ["api_name", "version", "environment"]
        },
        {
            "model": "deepseek-ai/DeepSeek-V3-0324",
            "interface": "openai",
            "content": "Generate an API configuration",
            "expected_keys": ["api_name", "version"]
        }
    ]

    for case in test_cases:
        result = api_http_client.perform_real_http_test(
            api_key=test_api_key,
            api_url=test_api_url,
            model_name=case["model"],
            interface_type=case["interface"],
            test_content=case["content"],
            json_output=True
        )

        assert result["success"] == True
        json_data = json.loads(result["response_content"])
        for key in case["expected_keys"]:
            assert key in json_data
```

### Schema约束测试
```python
def test_schema_constraint():
    """测试Schema约束功能"""
    schema = {
        "type": "object",
        "properties": {
            "users": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "integer"},
                        "name": {"type": "string"}
                    },
                    "required": ["id", "name"]
                }
            }
        },
        "required": ["users"]
    }

    result = api_http_client.perform_real_http_test(
        api_key=test_api_key,
        api_url=test_api_url,
        model_name="gemini-2.5-pro",
        interface_type="gemini",
        test_content="Generate a list of 3 users",
        json_output=True,
        json_schema=schema
    )

    json_data = json.loads(result["response_content"])
    assert "users" in json_data
    assert len(json_data["users"]) == 3
    for user in json_data["users"]:
        assert "id" in user and "name" in user
```

## 📋 实施计划

### 阶段1: 核心功能实现（1-2天）
- [ ] 扩展`perform_real_http_test`方法签名
- [ ] 实现`_add_gemini_json_config`方法
- [ ] 实现`_add_openai_json_config`方法
- [ ] 实现`_add_qwen_json_config`方法（企业级JSON支持）
- [ ] 更新`_build_interface_specific_payload`方法

### 阶段2: 错误处理和优化（1天）
- [ ] 实现JSON验证和修复机制
- [ ] 添加模型可靠性评分
- [ ] 实现提示工程优化

### 阶段3: 测试和文档（1天）
- [ ] 编写单元测试
- [ ] 编写集成测试
- [ ] 更新API文档
- [ ] 性能测试和优化

## 🎯 预期效果

### 功能目标
- ✅ 统一的JSON输出接口
- ✅ 多模型自动适配
- ✅ 可选的Schema约束
- ✅ 向后兼容性保证

### 性能目标
- **Gemini 2.5 Pro**: JSON输出成功率 ≥ 95%
- **Qwen 3-235B-A22B**: JSON输出成功率 ≥ 85%（企业级场景 ≥ 90%）
- **DeepSeek V3-0324**: JSON输出成功率 ≥ 90%
- **DeepSeek R1-0528**: JSON输出成功率 ≥ 70%

### 用户体验
- 调用者无需了解模型差异
- 统一的错误处理机制
- 清晰的可靠性指标
- 灵活的Schema配置

## 📚 参考资料

### 官方文档
- [Gemini API JSON输出文档](https://ai.google.dev/gemini-api/docs/json-mode)
- [OpenAI JSON模式文档](https://platform.openai.com/docs/guides/structured-outputs)
- [DeepSeek API文档](https://api-docs.deepseek.com/)

### JSON Schema规范
- [JSON Schema官方规范](https://json-schema.org/)
- [JSON Schema验证工具](https://www.jsonschemavalidator.net/)

---

**文档状态**: ✅ 设计完成，待实施
**负责人**: API管理团队
**审核人**: 架构团队
**更新日期**: 2025-01-12
