# 文档更新指南

**文档ID**: G006
**创建日期**: 2025-05-08
**版本**: 1.0
**状态**: 已批准

本文档提供了关于如何在AI-DEV-FLOW工作流中使用文档更新功能的指南。文档更新是确保项目文档与代码保持同步的重要环节，本指南将帮助您理解和使用这一功能。

## 1. 文档更新概述

在AI-DEV-FLOW工作流中，文档更新是一个集成到RIPER-5协议中的功能，它允许AI在完成某个开发阶段后自动修改和记录相应的文档。这个功能的核心是在任务文件中添加"Document Update Plan"部分，详细记录需要更新的文档和变更内容。

文档更新流程包括三个主要阶段：
1. **计划阶段**：AI分析任务和相关文档，生成文档更新计划
2. **执行阶段**：AI按照计划执行文档更新，并记录更新状态
3. **审查阶段**：AI验证所有计划的文档更新是否已完成

## 2. 使用文档更新功能

### 2.1 创建任务文件

要使用文档更新功能，首先需要创建一个包含"Document Update Plan"部分的任务文件。您可以使用`docs/common/templates/task-file-template-with-doc-updates.md`模板来创建任务文件。

```markdown
# Context
Filename: [Task Filename.md]
Created On: [DateTime]
Created By: [Username/AI]
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
[完整的任务描述]

# Project Overview
[项目详情或AI根据上下文自动推断的简要项目信息]

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)
[代码调查结果、关键文件、依赖关系、约束条件等]

# Proposed Solution (Populated by INNOVATE mode)
[讨论的不同方法、优缺点评估、最终推荐的解决方案方向]

# Implementation Plan (Generated by PLAN mode)
```
Implementation Checklist:
1. [具体操作1]
2. [具体操作2]
...
n. [最终操作]
```

# Document Update Plan (Generated by PLAN mode)
```
Document Update Checklist:
1. [文档路径1]: [更新内容描述]
   - 更新部分: [具体部分，如"变更历史"、"API定义"等]
   - 更新类型: [添加/修改/删除]
   - 更新内容: [具体内容]
   - 更新原因: [为什么需要更新]
2. [文档路径2]: [更新内容描述]
   ...
```

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: "[步骤编号和名称]"

# Task Progress (Appended by EXECUTE mode after each step completion)
*   [DateTime]
    *   Step: [检查表项目编号和描述]
    *   Modifications: [文件和代码更改列表，包括报告的微小偏差修正]
    *   Change Summary: [此更改的简要摘要]
    *   Reason: [执行计划步骤[X]]
    *   Blockers: [遇到的问题，或无]
    *   User Confirmation Status: [成功 / 有小问题但成功 / 失败]

*   [DateTime]
    *   Step: Document Update - [文档路径]
    *   Modifications: [文档更新内容]
    *   Change Summary: [更新摘要]
    *   Reason: [更新原因]
    *   Blockers: [阻塞因素，如果有]
    *   User Confirmation Status: [待确认/已确认/已拒绝]

# Final Review (Populated by REVIEW mode)
[对照最终计划的实施合规性评估，是否发现未报告的偏差]

## 文档更新审查
- 计划的文档更新数量: [数量]
- 成功完成的更新数量: [数量]
- 未完成的更新: [列出未完成的更新，如果有]
- 文档更新符合计划: [是/否]
```

### 2.2 PLAN模式下的文档更新计划

在PLAN模式下，AI会分析任务和相关文档，生成文档更新计划。文档更新计划应包含以下内容：

- **文档路径**：需要更新的文档的完整路径
- **更新部分**：文档中需要更新的具体部分
- **更新类型**：添加、修改或删除
- **更新内容**：具体的更新内容
- **更新原因**：为什么需要更新这个文档

例如：

```markdown
# Document Update Plan (Generated by PLAN mode)
```
Document Update Checklist:
1. docs/features/F001-功能X-20250510/README.md: 更新功能状态
   - 更新部分: 功能状态
   - 更新类型: 修改
   - 更新内容: 将状态从"计划中"改为"进行中"
   - 更新原因: 功能开发已启动

2. docs/features/F001-功能X-20250510/api/api-doc.md: 添加API文档
   - 更新部分: 整个文档
   - 更新类型: 添加
   - 更新内容: 创建API文档，包括接口定义、请求参数、响应格式等
   - 更新原因: 功能X需要API文档
```
```

### 2.3 EXECUTE模式下的文档更新执行

在EXECUTE模式下，AI会按照文档更新计划执行文档更新。文档更新的执行应遵循以下规则：

1. 按照文档更新计划中的优先级和时机执行更新
2. 在Task Progress中记录文档更新的状态
3. 请求用户确认文档更新

例如：

```markdown
*   2025-05-10T10:45:00+08:00
    *   Step: Document Update - docs/features/F001-功能X-20250510/README.md
    *   Modifications: 将功能状态从"计划中"改为"进行中"
    *   Change Summary: 更新功能状态以反映当前进度
    *   Reason: 功能开发已启动
    *   Blockers: 无
    *   User Confirmation Status: 已确认
```

### 2.4 REVIEW模式下的文档更新审查

在REVIEW模式下，AI会验证所有计划的文档更新是否已完成。文档更新审查应包含以下内容：

1. 计划的文档更新数量
2. 成功完成的更新数量
3. 未完成的更新列表
4. 文档更新是否符合计划

例如：

```markdown
## 文档更新审查
- 计划的文档更新数量: 4
- 成功完成的更新数量: 4
- 未完成的更新: 无
- 文档更新符合计划: 是
```

## 3. 最佳实践

### 3.1 文档更新计划的制定

- **全面分析**：在制定文档更新计划时，全面分析任务和相关文档，确保所有需要更新的文档都被包含在计划中
- **明确更新内容**：明确指定每个文档的更新内容，避免模糊不清的描述
- **合理安排优先级**：根据文档的重要性和更新的紧急程度，合理安排更新优先级
- **考虑更新时机**：考虑文档更新的最佳时机，有些文档可能需要在实现后才能更新，有些文档可能需要在审查前更新

### 3.2 文档更新的执行

- **按计划执行**：严格按照文档更新计划执行更新，避免遗漏或错误
- **及时记录状态**：在执行每个文档更新后，及时记录更新状态
- **请求用户确认**：对于重要的文档更新，请求用户确认
- **处理阻塞因素**：如果遇到阻塞因素，及时记录并寻求解决方案

### 3.3 文档更新的审查

- **全面验证**：验证所有计划的文档更新是否已完成
- **检查更新质量**：检查文档更新的质量，确保更新内容符合要求
- **报告未完成的更新**：如果有未完成的更新，明确报告并说明原因
- **提出改进建议**：如果发现文档更新过程中的问题，提出改进建议

## 4. 常见问题

### 4.1 如何处理文档更新计划中的变更？

如果在执行过程中发现文档更新计划需要变更，应该在Task Progress中记录变更原因，并请求用户确认。例如：

```markdown
*   2025-05-10T11:00:00+08:00
    *   Step: Document Update Plan Change
    *   Modifications: 添加对docs/features/F001-功能X-20250510/test/test-doc.md的更新
    *   Change Summary: 添加测试文档更新计划
    *   Reason: 实现过程中发现需要更新测试文档
    *   Blockers: 无
    *   User Confirmation Status: 待确认
```

### 4.2 如何处理文档更新失败？

如果文档更新失败，应该在Task Progress中记录失败原因，并请求用户指导。例如：

```markdown
*   2025-05-10T11:15:00+08:00
    *   Step: Document Update - docs/features/F001-功能X-20250510/api/api-doc.md
    *   Modifications: 无法完成
    *   Change Summary: 文档更新失败
    *   Reason: 文档不存在，需要先创建文档
    *   Blockers: 文档不存在
    *   User Confirmation Status: 待确认
```

### 4.3 如何处理用户拒绝的文档更新？

如果用户拒绝某个文档更新，应该在Task Progress中记录拒绝原因，并根据用户的反馈调整更新计划。例如：

```markdown
*   2025-05-10T11:30:00+08:00
    *   Step: Document Update Plan Adjustment
    *   Modifications: 移除对docs/features/F001-功能X-20250510/design/design-doc.md的更新
    *   Change Summary: 根据用户反馈调整更新计划
    *   Reason: 用户表示设计文档暂不需要更新
    *   Blockers: 无
    *   User Confirmation Status: 已确认
```

## 5. 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 1.0 | 2025-05-08 | 初始版本 | AI助手 |
