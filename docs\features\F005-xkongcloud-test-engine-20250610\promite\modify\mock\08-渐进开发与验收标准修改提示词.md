# 08-渐进开发与验收标准修改提示词

**文档版本**: MODIFY-PROGRESSIVE-DEVELOPMENT-ACCEPTANCE  
**创建时间**: 2025年6月10日  
**修改目标**: 在渐进开发中增加Mock先行策略和Mock相关验收标准

---

## 🎯 修改目标

在渐进开发与验收标准中增加Mock先行的开发策略，并在验收标准中增加Mock环境的验收标准。

## 📝 具体修改内容

### **修改位置1：渐进开发实施路径 - 增加Mock先行策略**

**在ProgressiveDevelopmentCoordinator类中增加Mock先行开发方法**：
```java
/**
 * 渐进式开发协调器（增加Mock先行策略）
 * 按模块渐进式开发通用引擎，采用Mock先行验证策略
 */
@Component
public class ProgressiveDevelopmentCoordinator {
    
    @Autowired
    private MockFirstDevelopmentManager mockFirstDevelopmentManager;
    
    @Autowired
    private DualPhaseValidationManager dualPhaseValidationManager;
    
    /**
     * 执行渐进式开发（增加Mock先行策略）
     */
    public ProgressiveDevelopmentResult executeProgressiveDevelopment(V2ArchitectureAnalysisResult v2Analysis) {
        log.info("开始执行通用引擎渐进式开发，采用Mock先行策略");
        
        try {
            // Step 1: Mock先行核心引擎开发
            MockFirstCoreEngineResult mockFirstCoreResult = developCoreEnginesWithMockFirst(v2Analysis);
            
            // Step 2: Mock先行可选引擎开发
            MockFirstOptionalEngineResult mockFirstOptionalResult = developOptionalEnginesWithMockFirst(v2Analysis);
            
            // Step 3: 双阶段持续集成测试
            DualPhaseContinuousIntegrationResult dualPhaseCIResult = executeDualPhaseContinuousIntegration(
                mockFirstCoreResult, mockFirstOptionalResult);
            
            // Step 4: ProjectAdapter框架Mock适配完善
            ProjectAdapterMockFrameworkResult adapterMockResult = perfectProjectAdapterMockFramework(dualPhaseCIResult);
            
            return ProgressiveDevelopmentResult.builder()
                .mockFirstCoreEngineResult(mockFirstCoreResult)
                .mockFirstOptionalEngineResult(mockFirstOptionalResult)
                .dualPhaseCIResult(dualPhaseCIResult)
                .projectAdapterMockResult(adapterMockResult)
                .developmentStrategy(DevelopmentStrategy.MOCK_FIRST_PROGRESSIVE)
                .developmentStatus(DevelopmentStatus.COMPLETED)
                .build();
                
        } catch (Exception e) {
            log.error("Mock先行渐进式开发失败", e);
            return ProgressiveDevelopmentResult.failure(e.getMessage());
        }
    }
    
    /**
     * Mock先行核心引擎开发
     * 开发阶段：Mock快速验证程序逻辑正确性
     */
    private MockFirstCoreEngineResult developCoreEnginesWithMockFirst(V2ArchitectureAnalysisResult v2Analysis) {
        MockFirstCoreEngineResult result = new MockFirstCoreEngineResult();
        
        // L1感知引擎Mock先行开发
        L1MockFirstDevelopmentResult l1MockFirstResult = developL1EngineWithMockFirst(v2Analysis.getL1EngineAnalysis());
        result.setL1MockFirstResult(l1MockFirstResult);
        
        // L1引擎Mock验证通过后，进行TestContainers验证
        if (l1MockFirstResult.isMockValidationSuccessful()) {
            L1TestContainersValidationResult l1ContainersResult = validateL1EngineWithTestContainers(l1MockFirstResult);
            result.setL1ContainersResult(l1ContainersResult);
        }
        
        // L2认知引擎Mock先行开发
        L2MockFirstDevelopmentResult l2MockFirstResult = developL2EngineWithMockFirst(v2Analysis.getL2EngineAnalysis());
        result.setL2MockFirstResult(l2MockFirstResult);
        
        // L2引擎Mock验证通过后，进行TestContainers验证
        if (l2MockFirstResult.isMockValidationSuccessful()) {
            L2TestContainersValidationResult l2ContainersResult = validateL2EngineWithTestContainers(l2MockFirstResult);
            result.setL2ContainersResult(l2ContainersResult);
        }
        
        // L3理解引擎Mock先行开发
        L3MockFirstDevelopmentResult l3MockFirstResult = developL3EngineWithMockFirst(v2Analysis.getL3EngineAnalysis());
        result.setL3MockFirstResult(l3MockFirstResult);
        
        // L3引擎Mock验证通过后，进行TestContainers验证
        if (l3MockFirstResult.isMockValidationSuccessful()) {
            L3TestContainersValidationResult l3ContainersResult = validateL3EngineWithTestContainers(l3MockFirstResult);
            result.setL3ContainersResult(l3ContainersResult);
        }
        
        // L4智慧引擎Mock先行开发（新增）
        L4MockFirstDevelopmentResult l4MockFirstResult = developL4EngineWithMockFirst();
        result.setL4MockFirstResult(l4MockFirstResult);
        
        // L4引擎Mock验证通过后，进行TestContainers验证
        if (l4MockFirstResult.isMockValidationSuccessful()) {
            L4TestContainersValidationResult l4ContainersResult = validateL4EngineWithTestContainers(l4MockFirstResult);
            result.setL4ContainersResult(l4ContainersResult);
        }
        
        return result;
    }
    
    /**
     * L1引擎Mock先行开发
     * 开发阶段Mock快速验证，TestContainers完整验证
     */
    private L1MockFirstDevelopmentResult developL1EngineWithMockFirst(L1EngineAnalysisResult l1Analysis) {
        L1MockFirstDevelopmentResult result = new L1MockFirstDevelopmentResult();
        result.setStartTime(LocalDateTime.now());
        
        try {
            // Phase 1: Mock环境快速开发验证
            L1MockDevelopmentResult mockDevelopmentResult = mockFirstDevelopmentManager.developL1EngineInMockEnvironment(l1Analysis);
            result.setMockDevelopmentResult(mockDevelopmentResult);
            
            if (mockDevelopmentResult.isSuccessful()) {
                // Phase 2: Mock环境程序逻辑验证
                L1MockLogicValidationResult mockLogicValidation = validateL1EngineLogicInMock(mockDevelopmentResult);
                result.setMockLogicValidation(mockLogicValidation);
                
                result.setMockValidationSuccessful(mockLogicValidation.isValid());
                result.setMockValidationNote("L1引擎程序逻辑在Mock环境下验证通过");
            } else {
                result.setMockValidationSuccessful(false);
                result.setMockValidationNote("L1引擎Mock环境开发失败: " + mockDevelopmentResult.getFailureReason());
            }
            
            result.setEndTime(LocalDateTime.now());
            result.setDevelopmentDuration(Duration.between(result.getStartTime(), result.getEndTime()));
            
        } catch (Exception e) {
            log.error("L1引擎Mock先行开发失败", e);
            result.setMockValidationSuccessful(false);
            result.setMockValidationNote("L1引擎Mock先行开发异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * L1引擎TestContainers验证
     * 集成阶段：TestContainers验证环境集成完整性
     */
    private L1TestContainersValidationResult validateL1EngineWithTestContainers(L1MockFirstDevelopmentResult mockFirstResult) {
        L1TestContainersValidationResult result = new L1TestContainersValidationResult();
        result.setStartTime(LocalDateTime.now());
        
        try {
            // 启动TestContainers环境
            TestContainersEnvironmentResult containersEnvironment = startTestContainersForL1Engine();
            result.setContainersEnvironment(containersEnvironment);
            
            if (containersEnvironment.isSuccessful()) {
                // 在TestContainers环境中验证L1引擎
                L1ContainersExecutionResult containersExecution = executeL1EngineInTestContainers(
                    mockFirstResult.getMockDevelopmentResult());
                result.setContainersExecution(containersExecution);
                
                // 对比Mock和TestContainers结果
                L1MockContainersComparisonResult comparison = compareL1MockAndContainersResults(
                    mockFirstResult.getMockLogicValidation(), containersExecution);
                result.setMockContainersComparison(comparison);
                
                result.setValidationSuccessful(
                    containersExecution.isSuccessful() && comparison.isConsistent());
                result.setValidationNote("L1引擎TestContainers环境验证通过，与Mock结果一致");
            } else {
                // TestContainers启动失败，启用Mock保护模式
                L1MockProtectionResult mockProtection = activateL1MockProtectionMode(mockFirstResult);
                result.setMockProtectionResult(mockProtection);
                
                result.setValidationSuccessful(mockProtection.isProtectionSuccessful());
                result.setValidationNote("TestContainers失败，L1引擎已启用Mock保护模式");
            }
            
            result.setEndTime(LocalDateTime.now());
            result.setValidationDuration(Duration.between(result.getStartTime(), result.getEndTime()));
            
        } catch (Exception e) {
            log.error("L1引擎TestContainers验证失败", e);
            result.setValidationSuccessful(false);
            result.setValidationNote("L1引擎TestContainers验证异常: " + e.getMessage());
        }
        
        return result;
    }
}
```

### **修改位置2：验收标准 - 增加Mock相关验收标准**

**在基准数据驱动验证器中增加Mock环境验收标准**：
```java
/**
 * 基准数据驱动验证器（增加Mock环境验收标准）
 * 基于预先建立的V2基准数据进行验证，包括Mock环境验收标准
 */
@Component
public class BenchmarkDataDrivenValidator {
    
    @Autowired
    private MockEnvironmentAcceptanceValidator mockAcceptanceValidator;
    
    /**
     * 执行基准数据驱动验证（增加Mock环境验收）
     */
    public BenchmarkValidationResult validateAgainstBenchmarkData(TestScenario scenario) {
        log.info("开始执行基准数据驱动验证，场景: {}", scenario.getScenarioName());
        
        try {
            // Step 1-4: 原有验证逻辑（保持不变）
            V2BenchmarkData benchmarkData = benchmarkRepository.getBenchmarkData(scenario.getScenarioName());
            UniversalTestResult universalResult = universalEngine.executeTest(scenario);
            StandardizedTestResult standardizedResult = scenarioStandardizer.standardize(universalResult);
            
            L1BenchmarkValidation l1Validation = validateL1AgainstBenchmark(
                benchmarkData.getL1Benchmark(), standardizedResult.getL1Data());
            L2BenchmarkValidation l2Validation = validateL2AgainstBenchmark(
                benchmarkData.getL2Benchmark(), standardizedResult.getL2Data());
            L3BenchmarkValidation l3Validation = validateL3AgainstBenchmark(
                benchmarkData.getL3Benchmark(), standardizedResult.getL3Data());
            
            // Step 5: Mock环境验收标准验证
            MockEnvironmentAcceptanceResult mockAcceptanceResult = validateMockEnvironmentAcceptance(
                universalResult, scenario);
            
            // Step 6: 综合评估基准符合性（包括Mock验收）
            boolean benchmarkCompliant = l1Validation.isCompliant() && 
                                       l2Validation.isCompliant() && 
                                       l3Validation.isCompliant() &&
                                       mockAcceptanceResult.isAcceptable();
            
            return BenchmarkValidationResult.builder()
                .scenarioName(scenario.getScenarioName())
                .l1Validation(l1Validation)
                .l2Validation(l2Validation)
                .l3Validation(l3Validation)
                .mockAcceptanceResult(mockAcceptanceResult)
                .benchmarkCompliant(benchmarkCompliant)
                .validationTimestamp(LocalDateTime.now())
                .build();
                
        } catch (Exception e) {
            log.error("基准数据驱动验证失败", e);
            return BenchmarkValidationResult.failure(scenario.getScenarioName(), e.getMessage());
        }
    }
    
    /**
     * 验证Mock环境验收标准
     * Mock环境功能等价性、数据一致性、性能基准、故障保护能力验证
     */
    private MockEnvironmentAcceptanceResult validateMockEnvironmentAcceptance(
            UniversalTestResult universalResult, 
            TestScenario scenario) {
        
        return mockAcceptanceValidator.validateMockAcceptance(universalResult, scenario);
    }
}

/**
 * Mock环境验收标准验证器
 * 专门验证Mock环境的验收标准
 */
@Component
public class MockEnvironmentAcceptanceValidator {
    
    @Autowired
    private MockFunctionalEquivalenceValidator functionalEquivalenceValidator;
    
    @Autowired
    private MockDataConsistencyValidator dataConsistencyValidator;
    
    @Autowired
    private MockPerformanceBenchmarkValidator performanceBenchmarkValidator;
    
    @Autowired
    private MockFailureProtectionValidator failureProtectionValidator;
    
    /**
     * 验证Mock环境验收标准
     */
    public MockEnvironmentAcceptanceResult validateMockAcceptance(
            UniversalTestResult universalResult, 
            TestScenario scenario) {
        
        MockEnvironmentAcceptanceResult result = new MockEnvironmentAcceptanceResult();
        result.setScenarioName(scenario.getScenarioName());
        result.setValidationStartTime(LocalDateTime.now());
        
        try {
            // 1. Mock环境功能等价性验证
            MockFunctionalEquivalenceResult functionalEquivalence = validateMockFunctionalEquivalence(
                universalResult, scenario);
            result.setFunctionalEquivalenceResult(functionalEquivalence);
            
            // 2. Mock数据一致性验证
            MockDataConsistencyResult dataConsistency = validateMockDataConsistency(
                universalResult, scenario);
            result.setDataConsistencyResult(dataConsistency);
            
            // 3. Mock性能基准验证
            MockPerformanceBenchmarkResult performanceBenchmark = validateMockPerformanceBenchmark(
                universalResult, scenario);
            result.setPerformanceBenchmarkResult(performanceBenchmark);
            
            // 4. Mock故障保护能力验证
            MockFailureProtectionResult failureProtection = validateMockFailureProtection(
                universalResult, scenario);
            result.setFailureProtectionResult(failureProtection);
            
            // 综合评估Mock环境验收
            result.setAcceptable(
                functionalEquivalence.isEquivalent() &&
                dataConsistency.isConsistent() &&
                performanceBenchmark.isBenchmarkMet() &&
                failureProtection.isProtectionCapable()
            );
            
            result.setValidationEndTime(LocalDateTime.now());
            result.setValidationDuration(Duration.between(
                result.getValidationStartTime(), result.getValidationEndTime()));
            
        } catch (Exception e) {
            log.error("Mock环境验收标准验证失败", e);
            result.setAcceptable(false);
            result.setFailureReason(e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 验证Mock环境功能等价性
     * Mock环境下的功能与真实环境一致
     */
    private MockFunctionalEquivalenceResult validateMockFunctionalEquivalence(
            UniversalTestResult universalResult, 
            TestScenario scenario) {
        
        return functionalEquivalenceValidator.validateEquivalence(universalResult, scenario);
    }
    
    /**
     * 验证Mock数据一致性
     * Mock数据与真实数据的一致性验证
     */
    private MockDataConsistencyResult validateMockDataConsistency(
            UniversalTestResult universalResult, 
            TestScenario scenario) {
        
        return dataConsistencyValidator.validateConsistency(universalResult, scenario);
    }
    
    /**
     * 验证Mock性能基准
     * Mock环境的性能基准和响应时间要求
     */
    private MockPerformanceBenchmarkResult validateMockPerformanceBenchmark(
            UniversalTestResult universalResult, 
            TestScenario scenario) {
        
        return performanceBenchmarkValidator.validateBenchmark(universalResult, scenario);
    }
    
    /**
     * 验证Mock故障保护能力
     * TestContainers失败时Mock的保护能力验证
     */
    private MockFailureProtectionResult validateMockFailureProtection(
            UniversalTestResult universalResult, 
            TestScenario scenario) {
        
        return failureProtectionValidator.validateProtection(universalResult, scenario);
    }
}

/**
 * Mock故障保护能力验证器
 * 验证TestContainers失败时Mock的保护能力
 */
@Component
public class MockFailureProtectionValidator {
    
    @Autowired
    private TestContainersFailureSimulator failureSimulator;
    
    @Autowired
    private MockProtectionModeActivator protectionModeActivator;
    
    /**
     * 验证Mock故障保护能力
     */
    public MockFailureProtectionResult validateProtection(
            UniversalTestResult universalResult, 
            TestScenario scenario) {
        
        MockFailureProtectionResult result = new MockFailureProtectionResult();
        result.setScenarioName(scenario.getScenarioName());
        
        try {
            // 1. 模拟TestContainers失败
            TestContainersFailureSimulationResult failureSimulation = failureSimulator.simulateFailure(scenario);
            result.setFailureSimulation(failureSimulation);
            
            // 2. 验证Mock保护模式激活
            MockProtectionActivationResult protectionActivation = protectionModeActivator.activateProtectionMode(
                failureSimulation);
            result.setProtectionActivation(protectionActivation);
            
            // 3. 验证Mock保护模式下的基础功能
            MockProtectionFunctionalityResult protectionFunctionality = validateProtectionModeFunctionality(
                protectionActivation, scenario);
            result.setProtectionFunctionality(protectionFunctionality);
            
            // 4. 验证Mock保护模式的性能表现
            MockProtectionPerformanceResult protectionPerformance = validateProtectionModePerformance(
                protectionActivation, scenario);
            result.setProtectionPerformance(protectionPerformance);
            
            // 评估保护能力
            result.setProtectionCapable(
                protectionActivation.isActivationSuccessful() &&
                protectionFunctionality.isFunctionalityMaintained() &&
                protectionPerformance.isPerformanceAcceptable()
            );
            
            result.setProtectionNote("Mock故障保护能力验证完成");
            
        } catch (Exception e) {
            log.error("Mock故障保护能力验证失败", e);
            result.setProtectionCapable(false);
            result.setProtectionNote("Mock故障保护能力验证异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 验证保护模式下的基础功能
     */
    private MockProtectionFunctionalityResult validateProtectionModeFunctionality(
            MockProtectionActivationResult protectionActivation, 
            TestScenario scenario) {
        
        MockProtectionFunctionalityResult result = new MockProtectionFunctionalityResult();
        
        // 验证L1-L4引擎在保护模式下的基础运行能力
        L1ProtectionModeResult l1Protection = validateL1ProtectionMode(protectionActivation, scenario);
        L2ProtectionModeResult l2Protection = validateL2ProtectionMode(protectionActivation, scenario);
        L3ProtectionModeResult l3Protection = validateL3ProtectionMode(protectionActivation, scenario);
        L4ProtectionModeResult l4Protection = validateL4ProtectionMode(protectionActivation, scenario);
        
        result.setL1ProtectionResult(l1Protection);
        result.setL2ProtectionResult(l2Protection);
        result.setL3ProtectionResult(l3Protection);
        result.setL4ProtectionResult(l4Protection);
        
        result.setFunctionalityMaintained(
            l1Protection.isFunctional() &&
            l2Protection.isFunctional() &&
            l3Protection.isFunctional() &&
            l4Protection.isFunctional()
        );
        
        return result;
    }
}
```

## 🎯 修改原则

1. **Mock先行开发策略**：开发阶段Mock快速验证程序逻辑，集成阶段TestContainers验证环境集成
2. **双阶段持续验证**：Mock验证通过后进行TestContainers验证，失败时启用Mock保护模式
3. **Mock环境验收标准**：功能等价性、数据一致性、性能基准、故障保护能力四个维度
4. **故障保护能力验证**：专门验证TestContainers失败时Mock的保护能力

## 📋 验证要点

修改完成后，文档应该能够让AI清晰理解：
- Mock先行开发策略的实施流程和优势
- 双阶段验证的执行机制和故障处理
- Mock环境验收标准的四个核心维度
- Mock故障保护能力的验证方法和标准
