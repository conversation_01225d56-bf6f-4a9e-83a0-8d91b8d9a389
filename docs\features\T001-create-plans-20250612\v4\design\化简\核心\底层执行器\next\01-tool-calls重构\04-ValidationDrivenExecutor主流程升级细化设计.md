# 04-ValidationDrivenExecutor主流程升级细化设计（伪代码级）

## 1. 现有实现溯源
- 文件：`tools/ace/src/executors/validation_driven_executor.py`
- 主要类/方法：
  - `class ValidationDrivenExecutor`（第359行）
  - `async def execute_with_validation(...)`（第525行）
- 现状：
  - 仅支持自由文本AI调用，未集成Tool Calling分支
  - API参数构建与AI调用逻辑耦合，缺乏分层

## 2. 目标主流程（伪代码级）

### 2.1 execute_with_validation主流程
```python
async def execute_with_validation(self, original_content, pycrud_operations, guardrails, constraints, context, ...):
    # 1. JSON协议封装
    complete_json_request = self.json_protocol_manager.encapsulate_complete_info(...)

    # 2. Tool Calling参数准备
    extra_api_params = {}
    if pycrud_operations:
        tools = self.tool_compiler.compile(pycrud_operations)
        extra_api_params["tools"] = tools
        extra_api_params["tool_choice"] = "auto"
    else:
        extra_api_params = self.strategy_resolver.resolve_api_params(constraints)

    # 3. 调用AI服务
    ai_response = await self.ai_service_manager.call_ai(
        model_name=self._select_model_by_role(self.executor_role),
        content=json.dumps(complete_json_request, ensure_ascii=False, indent=2),
        role=self.executor_role,
        json_output=False,
        **extra_api_params
    )

    # 4. Tool Calling分支
    tool_calls = ai_response.get("tool_calls")
    if tool_calls:
        execution_result = await self.pycrud_executor.execute_tool_calls(tool_calls)
        validation_result = await self.validation_loop.semantic_validation(execution_result)
        if validation_result.passed:
            return ExecutionResult.generate_and_execute(execution_result, validation_result)
        else:
            # 可选：ReAct模式反馈AI修正
            return ExecutionResult.validation_failed(validation_result, max_iterations=3)
    # 5. 兼容性兜底：自由文本分支
    complete_output = str(ai_response.get("content", ""))
    # ...原有验证-优化循环...
```

### 2.2 兼容性与异常处理
- Tool Calling分支为增量升级，旧有流程完整保留
- tool_calls缺失/异常时自动回退自由文本分支
- AI服务异常时抛出详细错误，主流程捕获并生成失败结果

### 2.3 单元测试建议
```python
def test_tool_call_branch():
    # 构造带tool_calls的AI响应，验证主流程分支
    ...
def test_fallback_branch():
    # 构造无tool_calls的AI响应，验证自由文本兜底
    ...
```

## 3. 需变更/新建/删除内容（精确到代码）
- 修改：
  - `execute_with_validation`方法主流程分支、参数传递、异常处理
- 新建：
  - Tool Calling分支的单元测试与集成测试用例

## 4. 兼容性与测试建议
- Tool Calling分支为增量升级，旧有流程完整保留
- 建议：
  - 单元测试：tool_calls分支与自由文本分支的全覆盖
  - 集成测试：端到端流程回归

## 5. 代码引用与路径
- `tools/ace/src/executors/validation_driven_executor.py`（execute_with_validation方法、主流程） 