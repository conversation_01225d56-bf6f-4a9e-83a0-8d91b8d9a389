# Commons DB V3: 代码实现与修改指南

## 文档元数据

- **文档ID**: `xkongcloud-commons-db-v3-code-changes`
- **版本**: `V1.0`
- **关联设计**: `xkongcloud-commons-db-v3-pragmatic`
- **状态**: `待实施`

## 1. 核心目标

本文档为 `Commons DB V3` 的具体代码实现提供详细指导和模板，旨在确保代码质量、统一编程风格，并精确落地《架构总览与设计哲学》中定义的设计原则。

## 2. 实现总览：模块化构建

我们将严格按照设计的模块结构，自底向上、逐层构建。请在对应的模块下进行开发。

```
xkongcloud-commons/
└── commons-db/
    ├── commons-db-core/         # 1. 核心抽象 (最先开始)
    ├── commons-db-dialect/      # 2. 数据库方言
    ├── commons-db-jpa/          # 3. JPA 实现
    ├── commons-db-querydsl/     # 4. Querydsl 实现
    ├── commons-db-jdbc/         # 5. JDBC 实现
    ├── commons-db-migration/    # 6. Schema 迁移
    ├── commons-db-monitoring/   # 7. 监控集成
    └── commons-db-starter/      # 8. 自动配置 (最后)
```

---

## 3. 分步实现指南

### 步骤 1: `commons-db-core` - 核心抽象层

**目标**: 定义整个DB库的契约和基础。此模块不包含任何具体实现。

**1.1. `api` 包: 定义统一数据访问接口**

*   **`DataAccessTemplate.java`**: 定义核心的数据访问模板，包含CRUD、查询、批量操作等。

```java
// package: com.xkong.xkongcloud.commons.db.core.api

public interface DataAccessTemplate<T, ID> {

    // === 基础操作 ===
    Optional<T> findById(ID id);
    T save(T entity);
    void delete(T entity);
    List<T> findAll();

    // === 条件查询 ===
    List<T> findBySpecification(Specification<T> spec);
    Page<T> findBySpecification(Specification<T> spec, Pageable pageable);

    // === 批量操作 ===
    void saveAll(Iterable<T> entities);
    void deleteAll(Iterable<T> entities);

    // === 执行器接口 ===
    <R> R execute(DataAccessCallback<R> action);
}
```

*   **`Specification.java`**: 定义条件查询的规约接口。
*   **`DataAccessCallback.java`**: 定义一个回调接口，用于执行底层特定操作。

**1.2. `spi` 包: 定义服务提供者接口**

*   **`DialectProvider.java`**: 定义获取数据库方言的SPI接口，供不同实现（JPA, JDBC）使用。

**1.3. `dialect` 包: 定义方言基础**

*   **`Dialect.java`**: 定义方言的抽象，如SQL语法转换、分页处理等。

### 步骤 2: `commons-db-dialect` - 数据库方言实现

**目标**: 为支持的数据库提供具体的方言实现。

*   **`PostgreSQLDialect.java`**: 实现 `Dialect` 接口，处理PostgreSQL特有的语法和函数。
*   **`MySQLDialect.java`**: 实现 `Dialect` 接口，处理MySQL特有的语法和函数。

### 步骤 3: `commons-db-jpa` - L1: Spring Data JPA 实现

**目标**: 基于JPA实现核心数据访问逻辑。

*   **`repository` 包**: 创建 `JpaDataAccessTemplate`，实现 `DataAccessTemplate` 接口。

```java
// package: com.xkong.xkongcloud.commons.db.jpa.repository

@Repository
public class JpaDataAccessTemplate<T, ID> implements DataAccessTemplate<T, ID> {
    
    @PersistenceContext
    private EntityManager entityManager;

    // ... 实现接口方法，内部使用 entityManager 或 Spring Data JPA Repository ...
}
```

*   **`transaction` 包**: 提供事务协调的辅助工具，可与Spring的 `@Transactional` 深度集成。

### 步骤 4: `commons-db-querydsl` - L2: Querydsl 集成

**目标**: 提供类型安全的查询构建能力。

*   **`builder` 包**: 创建 `QuerydslDataAccessTemplate`。

```java
// package: com.xkong.xkongcloud.commons.db.querydsl.builder

@Repository
public class QuerydslDataAccessTemplate<T, ID> implements DataAccessTemplate<T, ID> {

    private final JPAQueryFactory queryFactory;
    private final EntityPath<T> entityPath;

    public QuerydslDataAccessTemplate(EntityManager entityManager, Class<T> domainClass) {
        this.queryFactory = new JPAQueryFactory(entityManager);
        this.entityPath = new PathBuilder<>(domainClass, domainClass.getSimpleName().toLowerCase());
    }

    // ... 使用 queryFactory 实现接口方法 ...
}
```

### 步骤 5: `commons-db-jdbc` - L3: JdbcTemplate 封装

**目标**: 为需要极致性能的场景提供轻量级JDBC访问。

*   **`executor` 包**: 创建 `JdbcDataAccessTemplate`，内部封装 `JdbcTemplate`。
*   **`mapper` 包**: 提供自定义的 `RowMapper` 实现。

### 步骤 6: `commons-db-migration` - Schema 管理

**目标**: 集成Flyway进行数据库版本控制。

*   在 `resources/db/migration` 目录下，按Flyway的命名规范创建SQL迁移脚本。
    *   `V1__create_initial_tables.sql`
    *   `V2__add_user_email_column.sql`
*   通过 `application.properties` 配置Flyway的行为。

### 步骤 7: `commons-db-monitoring` - 监控集成

**目标**: 集成Micrometer，暴露数据库性能指标。

*   **`metrics` 包**: 定义 `DataSourceMetrics` 类，用于收集HikariCP连接池的指标。
*   **`health` 包**: 创建自定义的 `DataSourceHealthIndicator`，集成到Spring Boot Actuator。

### 步骤 8: `commons-db-starter` - Spring Boot 自动配置

**目标**: 实现所有组件的自动装配和配置。

*   **`autoconfigure` 包**: 创建 `CommonsDbAutoConfiguration`。

```java
// package: com.xkong.xkongcloud.commons.db.starter.autoconfigure

@Configuration
@EnableConfigurationProperties(CommonsDbProperties.class)
@ConditionalOnClass(DataAccessTemplate.class)
public class CommonsDbAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(name = "xkong.commons.db.access-layer", havingValue = "jpa")
    public DataAccessTemplate<?, ?> jpaDataAccessTemplate(EntityManager entityManager) {
        // ... 创建 JpaDataAccessTemplate 实例 ...
    }

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(name = "xkong.commons.db.access-layer", havingValue = "querydsl", matchIfMissing = true)
    public DataAccessTemplate<?, ?> querydslDataAccessTemplate(EntityManager entityManager, ...) {
        // ... 创建 QuerydslDataAccessTemplate 实例 ...
    }
}
```

*   **`properties` 包**: 创建 `CommonsDbProperties` 类，用于接收 `application.yml` 中的配置。

```java
// package: com.xkong.xkongcloud.commons.db.starter.properties

@ConfigurationProperties(prefix = "xkong.commons.db")
public class CommonsDbProperties {
    /**
     * The data access layer to activate. Can be 'jpa' or 'querydsl'.
     */
    private String accessLayer = "querydsl";

    // getters and setters
}
```

## 4. 编码规范

- **命名**: 遵循Java标准命名规范，类名、方法名、变量名清晰表意。
- **注释**: 对所有公共API、复杂逻辑和配置项进行详细的JavaDoc注释。
- **测试**: 为每个模块编写充分的单元测试和集成测试，确保代码质量。
- **日志**: 使用SLF4J作为日志门面，在关键路径添加Debug和Info级别的日志。

---
