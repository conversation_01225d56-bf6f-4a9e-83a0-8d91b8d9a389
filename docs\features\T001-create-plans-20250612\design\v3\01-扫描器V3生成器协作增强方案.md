 另外必须强调的是：

1.对的扫描器v3放在目录是tools/doc/design/v3
2.按照设计文档实施完整的"扫描器V3生成器协作增强方案"
3.tools/doc/design/v3，只有一个以前的扫描器代码，要继续使用，见文档。

advanced-doc-scanner.py生成检测设计文档的报告文档不变（目录和内容），多出来json的输出（输出到design/v1/json/目录，不是通用模板,v1的{1}是版本号和设计文档（文件名对齐，例如1-x.md设计文档，那么就是/json/1-x.json）对齐）。并且ai-prompt-batch-improvement.md中会提醒ai完善json。

也就是说对程序对已有的json就不是覆盖而是扫描json输出到ai-prompt-batch-improvement.md中了（并且告诉ai，如果是空值是怎么处理）
注意这个advanced-doc-scanner.py程序已经很大了，不要去做json的任何功能，josn功能都要放在另外一个json_completeness代码中
 
 # 扫描器V3生成器协作增强方案

## 文档信息
- **文档ID**: T001-SCANNER-V3-ENHANCEMENT-001
- **创建日期**: 2025-01-16
- **版本**: v1.0 (超完整JSON模板设计)
- **实施方式**: 扫描器生成基础版本 + AI校验填充机制
- **核心目标**: 解决架构信息丢失问题，提升架构一致性从60%到90%+

## 核心方案总结

**扫描器增强原理**：类似test_v3_simple.py的生成机制，但专门针对高质量设计文档（如评分92.7/100的合格文档）进行深度架构特征提取，生成超完整的design-analysis-complete.json模板。

**AI校验填充机制**：扫描器生成基础版本后，AI基于原始设计文档校验并填充所有空缺项，确保JSON包含完整架构信息供V3生成器精准执行。

## V3生成器现状分析

### V3生成器已具备强大能力
基于对`tools/doc/plans/v3/test_v3_simple.py`分析，V3生成器已具备：

1. **完整Java生态支持**：
   - Java 21 + Virtual Threads完整支持
   - Spring Boot 3.4.5精确版本集成
   - 详细JVM参数和配置生成

2. **详细接口定义生成能力**：
   - PluginActivator接口(使用PluginContext参数)
   - ServiceBus接口(包含publishAsync、getStatistics、getHealth方法)
   - 完整Event基类体系和监听器机制
   - 精确路径映射到具体Java文件

3. **现有模块集成能力**：
   - 与commons-uid模块集成支持
   - @ServiceInterface演进注解兼容
   - 适配器模式桥接策略

4. **生产级代码模板**：
   - 精确到行号的代码生成
   - 完整的异常处理和性能约束
   - 详细的依赖关系JSON配置

### 扫描器增强目标
基于V3生成器强大能力，扫描器需要增强：
- **标准化JSON输出**：生成design-analysis-complete.json供V3精准读取
- **Java全生态项目识别**：Spring Boot、Maven、JPA、Virtual Threads等
- **1对1设计文档映射**：输出到design/v1/json/目录，不是通用模板,v1的{1}是版本号和设计文档对齐
- **架构特征精确提取**：复用现有86KB扫描器的架构语义分析能力

### 关键组件

#### 1. 复用test_v3_simple.py的设计文档提取能力
**发现**：`tools/doc/plans/v3/test_v3_simple.py`已具备非常成熟的设计文档提取能力
**策略调整**：
- **避免重复造轮子**：直接复用test_v3_simple.py的设计文档分析能力
- **功能块复用**：提取test_v3_simple.py中的设计文档扫描功能模块
- **增强JSON输出**：在现有能力基础上增加标准化JSON输出
- **完整度评测集成**：类似advanced-doc-scanner.py的ai-prompt-batch-improvement.md机制

#### 2. JSON完整度评测机制
**参考模式**：`advanced-doc-scanner.py` → `ai-prompt-batch-improvement.md`
**评测机制**：
- **多JSON完整度检查**：对输出的多个JSON进行完整度评测
- **AI检查指令生成**：让AI自动检查JSON完整度并生成改进建议  
- **批量评测报告**：类似ai-prompt-batch-improvement.md的批量评测文档
- **迭代改进流程**：AI检查→改进建议→重新生成→再次评测

#### 3. 复用现有能力的实施方案
**核心策略**：
- **功能模块提取**：从test_v3_simple.py中提取设计文档分析功能
- **advanced-doc-scanner.py评测集成**：复用其JSON完整度评测机制
- **避免重复开发**：最大化复用现有成熟功能
- **专注JSON标准化**：重点开发标准化JSON输出和完整度评测

### 1. 基于现有能力的协作增强

#### 复用现有成熟功能
```
现有成熟工具:
├── tools/doc/plans/v3/test_v3_simple.py     # 成熟的设计文档提取能力
├── tools/doc/design/v3/advanced-doc-scanner.py  # JSON完整度评测机制  
└── 新增协作模块:
    ├── design_doc_analyzer.py               # 复用test_v3_simple.py功能块
    ├── json_completeness_evaluator.py      # 复用advanced-doc-scanner.py评测机制
    └── v3_integration_bridge.py            # V3生成器协作桥接
```

#### 核心协作设计
```python
# design_doc_analyzer.py - 复用test_v3_simple.py功能
class DesignDocumentAnalyzer:
    """复用test_v3_simple.py的设计文档分析能力"""
    
    def __init__(self):
        # 导入test_v3_simple.py的分析功能
        from test_v3_simple import TestV3Generator
        self.v3_generator = TestV3Generator(".")
    
    def extract_design_features(self, design_doc_path: str) -> dict:
        """复用V3生成器的设计文档提取能力"""
        # 复用test_v3_simple.py中已经成熟的设计文档分析逻辑
        # 避免重复开发相同的功能
        
        # 步骤1: 使用V3生成器的文档分析能力
        v3_analysis = self.v3_generator._analyze_design_document(design_doc_path)
        
        # 步骤2: 提取关键架构特征
        architecture_features = self._extract_architecture_features(v3_analysis)
        
        # 步骤3: 标准化为JSON格式
        standardized_json = self._standardize_to_json(architecture_features)
        
        return standardized_json

# json_completeness_evaluator.py - 复用advanced-doc-scanner.py评测机制
class JsonCompletenessEvaluator:
    """复用advanced-doc-scanner.py的完整度评测机制"""
    
    def __init__(self):
        # 导入advanced-doc-scanner.py的评测功能
        from advanced_doc_scanner import AdvancedDesignDocScanner
        self.scanner = AdvancedDesignDocScanner()
    
    def evaluate_json_completeness(self, json_files: list) -> dict:
        """对多个JSON进行完整度评测"""
        # 步骤1: 类似ai-prompt-batch-improvement.md的评测机制
        evaluation_results = []
        
        for json_file in json_files:
            # 步骤2: 使用扫描器的评测逻辑检查JSON完整度
            completeness_score = self._calculate_json_completeness(json_file)
            missing_fields = self._identify_missing_fields(json_file)
            ai_improvement_suggestions = self._generate_ai_suggestions(json_file)
            
            evaluation_results.append({
                'file': json_file,
                'completeness_score': completeness_score,
                'missing_fields': missing_fields,
                'ai_suggestions': ai_improvement_suggestions
            })
        
        # 步骤3: 生成类似ai-prompt-batch-improvement.md的批量改进报告
        self._generate_batch_improvement_report(evaluation_results)
        
        return evaluation_results
```

### 2. 超完整JSON模板设计

#### design-analysis-complete.json模板结构
```json
{
  "metadata": {
    "document_id": "{{AI_FILL_REQUIRED}}",
    "version": "v1.0",
    "generated_at": "{{SCANNER_AUTO_FILL}}",
    "source_design_docs": ["{{SCANNER_AUTO_FILL}}"],
    "ai_validation_status": "{{AI_FILL_REQUIRED}}",
    "completeness_score": "{{AI_CALCULATE_REQUIRED}}",
    "fill_status": {
      "scanner_filled": ["generated_at", "source_design_docs"],
      "ai_required": ["document_id", "ai_validation_status", "completeness_score"]
    }
  },
  
  "project_identity": {
    "project_name": "{{SCANNER_AUTO_FILL}}",
    "base_package": "{{SCANNER_AUTO_FILL}}",
    "maven_artifact_id": "{{SCANNER_AUTO_FILL}}",
    "java_version": "{{SCANNER_AUTO_FILL}}",
    "spring_boot_version": "{{SCANNER_AUTO_FILL}}",
    "fill_status": {
      "scanner_filled": ["project_name", "base_package", "maven_artifact_id"],
      "ai_required": []
    }
  },
  
  "architecture_specification": {
    "pattern_type": "{{AI_FILL_REQUIRED}}",
    "microkernel_config": {
      "kernel_interfaces": "{{AI_FILL_REQUIRED}}",
      "plugin_system_enabled": "{{AI_FILL_REQUIRED}}",
      "plugin_discovery_mechanism": "{{AI_FILL_REQUIRED}}",
      "lifecycle_management": "{{AI_FILL_REQUIRED}}",
      "security_sandbox": "{{AI_FILL_REQUIRED}}"
    },
    "service_bus_config": {
      "async_event_processing": "{{AI_FILL_REQUIRED}}",
      "event_types": "{{AI_FILL_REQUIRED}}",
      "message_routing_strategy": "{{AI_FILL_REQUIRED}}",
      "performance_targets": {
        "events_per_second": "{{SCANNER_DETECTED}}",
        "max_latency_ms": "{{AI_FILL_REQUIRED}}"
      }
    },
    "virtual_threads_config": {
      "enabled": "{{SCANNER_DETECTED}}",
      "jvm_parameters": "{{AI_FILL_REQUIRED}}",
      "spring_configuration": "{{AI_FILL_REQUIRED}}",
      "carrier_thread_pool_size": "{{AI_FILL_REQUIRED}}"
    },
    "fill_status": {
      "scanner_filled": ["performance_targets.events_per_second", "virtual_threads_config.enabled"],
      "ai_required": ["pattern_type", "microkernel_config.*", "service_bus_config.*", "virtual_threads_config.*"]
    }
  },
  
  "interface_system": {
    "core_interfaces": [
      {
        "name": "{{AI_FILL_REQUIRED}}",
        "package": "{{AI_FILL_REQUIRED}}",
        "methods": "{{AI_FILL_REQUIRED}}",
        "dependencies": "{{AI_FILL_REQUIRED}}",
        "design_doc_reference": "{{AI_FILL_REQUIRED}}"
      }
    ],
    "plugin_interfaces": "{{AI_FILL_REQUIRED}}",
    "service_interfaces": "{{AI_FILL_REQUIRED}}",
    "event_interfaces": "{{AI_FILL_REQUIRED}}",
    "fill_status": {
      "scanner_filled": [],
      "ai_required": ["core_interfaces", "plugin_interfaces", "service_interfaces", "event_interfaces"]
    }
  },
  
  "technology_stack": {
    "mandatory_versions": {
      "java_min_version": "{{SCANNER_AUTO_FILL}}",
      "spring_boot_min_version": "{{SCANNER_AUTO_FILL}}",
      "maven_min_version": "{{SCANNER_AUTO_FILL}}"
    },
    "frameworks": "{{AI_FILL_REQUIRED}}",
    "libraries": "{{AI_FILL_REQUIRED}}",
    "build_tools": "{{SCANNER_AUTO_FILL}}",
    "fill_status": {
      "scanner_filled": ["mandatory_versions", "build_tools"],
      "ai_required": ["frameworks", "libraries"]
    }
  },
  
  "performance_requirements": {
    "throughput_targets": "{{SCANNER_DETECTED}}",
    "latency_targets": "{{AI_FILL_REQUIRED}}",
    "memory_constraints": "{{AI_FILL_REQUIRED}}",
    "scalability_requirements": "{{AI_FILL_REQUIRED}}",
    "fill_status": {
      "scanner_filled": ["throughput_targets"],
      "ai_required": ["latency_targets", "memory_constraints", "scalability_requirements"]
    }
  },
  
  "configuration_schema": {
    "spring_profiles": "{{AI_FILL_REQUIRED}}",
    "application_properties": "{{AI_FILL_REQUIRED}}",
    "environment_variables": "{{AI_FILL_REQUIRED}}",
    "jvm_parameters": "{{AI_FILL_REQUIRED}}",
    "fill_status": {
      "scanner_filled": [],
      "ai_required": ["spring_profiles", "application_properties", "environment_variables", "jvm_parameters"]
    }
  },
  
  "implementation_guidelines": {
    "coding_standards": "{{AI_FILL_REQUIRED}}",
    "design_patterns": "{{AI_FILL_REQUIRED}}",
    "error_handling_strategy": "{{AI_FILL_REQUIRED}}",
    "testing_requirements": "{{AI_FILL_REQUIRED}}",
    "fill_status": {
      "scanner_filled": [],
      "ai_required": ["coding_standards", "design_patterns", "error_handling_strategy", "testing_requirements"]
    }
  },
  
  "ai_constraints": {
    "memory_limitations": "{{AI_FILL_REQUIRED}}",
    "complexity_boundaries": "{{AI_FILL_REQUIRED}}",
    "validation_checkpoints": "{{AI_FILL_REQUIRED}}",
    "cognitive_load_management": "{{AI_FILL_REQUIRED}}",
    "fill_status": {
      "scanner_filled": [],
      "ai_required": ["memory_limitations", "complexity_boundaries", "validation_checkpoints", "cognitive_load_management"]
    }
  },
  
  "validation_checklist": {
    "architecture_consistency": {
      "target_score": 0.9,
      "current_score": "{{AI_CALCULATE_REQUIRED}}",
      "validation_items": "{{AI_FILL_REQUIRED}}"
    },
    "interface_completeness": {
      "required_interfaces": "{{AI_FILL_REQUIRED}}",
      "missing_interfaces": "{{AI_CALCULATE_REQUIRED}}",
      "completeness_percentage": "{{AI_CALCULATE_REQUIRED}}"
    },
    "performance_validation": {
      "benchmark_tests": "{{AI_FILL_REQUIRED}}",
      "performance_gates": "{{AI_FILL_REQUIRED}}"
    },
    "fill_status": {
      "scanner_filled": ["architecture_consistency.target_score"],
      "ai_required": ["*"]
    }
  }
}
```

### 3. AI填充状态标记系统

#### 填充状态标记定义
- `{{SCANNER_AUTO_FILL}}`: 扫描器能够自动检测并填充的内容
- `{{SCANNER_DETECTED}}`: 扫描器能够检测到但需要AI验证的内容
- `{{AI_FILL_REQUIRED}}`: 必须由AI基于设计文档原文填充的内容
- `{{AI_CALCULATE_REQUIRED}}`: 需要AI基于其他数据计算得出的内容

#### fill_status节点说明
每个主要节点都包含fill_status子节点，明确标识：
- `scanner_filled`: 已由扫描器填充的字段列表
- `ai_required`: 需要AI填充的字段列表（支持通配符*）

### 4. 完整度评估与报告生成

#### 报告输出位置
```
checkresult/ai-prompt-batch-improvement.md
```

#### 报告模板
```markdown
# AI批量改进提示 - design-analysis-complete.json完整度报告

## 扫描结果概览
- **扫描时间**: {{timestamp}}
- **源设计文档**: {{design_doc_path}}
- **文档质量评分**: {{quality_score}}/100
- **JSON完整度评分**: {{completeness_score}}% (目标90%)
- **需要AI填充的字段数**: {{ai_required_count}}

## 完整度分析

### 已完成填充 (扫描器自动)
{{scanner_filled_summary}}

### 需要AI填充的关键领域
1. **微内核接口体系** (优先级: 高)
   - 缺失字段: interface_system.core_interfaces, interface_system.plugin_interfaces
   - AI任务: 基于设计文档02-kernel-and-plugin-lifecycle.md填充完整接口定义
   
2. **Virtual Threads配置** (优先级: 高)
   - 缺失字段: virtual_threads_config.jvm_parameters, virtual_threads_config.spring_configuration
   - AI任务: 基于技术约束要求补全配置参数

3. **性能要求体系** (优先级: 中)
   - 缺失字段: performance_requirements.latency_targets, performance_requirements.memory_constraints
   - AI任务: 基于性能指标约束补全要求

## AI填充指令模板

请基于设计文档`{{design_doc_path}}`完善以下JSON节点:

### 任务1: 完善微内核接口体系
```json
"interface_system": {
  "core_interfaces": [
    // 请基于设计文档定义PluginActivator、ServiceBus、PluginManager等接口
  ]
}
```

### 任务2: 完善Virtual Threads配置
```json
"virtual_threads_config": {
  "jvm_parameters": [
    // 请基于设计文档技术约束填充JVM参数
  ]
}
```

## 验证检查点
- [ ] 微内核接口定义完整性
- [ ] Virtual Threads配置正确性
- [ ] 性能要求可测量性
- [ ] 架构一致性评分≥90%
```

## 工作流程设计

### 1. 扫描阶段
```bash
# 执行扫描器
python tools/doc/design/v3/advanced-doc-scanner.py \
  --input docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/ \
  --output docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/design-analysis-complete.json
```

### 2. AI校验填充阶段
AI基于生成的design-analysis-complete.json和原始设计文档，执行以下任务：
1. 读取JSON中所有标记为`{{AI_FILL_REQUIRED}}`的字段
2. 基于设计文档原文填充这些字段
3. 验证并更新`ai_validation_status`为`validated`
4. 计算并更新`completeness_score`

### 3. V3生成器增强阶段
V3生成器读取完整的design-analysis-complete.json，基于完整架构信息生成详细实施计划，确保：
- 架构一致性≥90%
- 接口定义与设计文档100%匹配
- 技术栈特性完整实现

## 成功标准

### 技术指标
- **JSON完整度**: ≥95% (所有AI_FILL_REQUIRED字段已填充)
- **架构一致性**: ≥90% (生成计划与设计文档匹配度)
- **接口覆盖率**: 100% (设计文档中所有接口均在JSON中定义)

### 质量门禁
- [ ] 扫描器能正确识别文档质量≥90分的设计文档
- [ ] JSON模板包含所有可能的架构特征key
- [ ] AI填充机制能基于设计文档原文准确填充
- [ ] V3生成器能基于完整JSON生成精准实施计划

## 相关文档
- [技术实施清单](./02-技术实施清单.md) - 详细的开发任务列表
- [风险评估与回滚方案](./03-风险评估与回滚方案.md) - 风险控制策略
- [验收标准与测试方案](./04-验收标准与测试方案.md) - 质量验证标准