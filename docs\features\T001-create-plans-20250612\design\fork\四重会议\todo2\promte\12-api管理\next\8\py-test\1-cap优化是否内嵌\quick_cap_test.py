#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAP方法快速验证测试
简化版本，用于快速验证LogicDepthDetector作为CAP优化评估工具的有效性

作者：AI助手
日期：2025-01-09
"""

import json
import re
import time
import urllib.request
from datetime import datetime
from typing import Dict, Any

# ==================== 配置信息 ====================
API_CONFIG = {
    "url": "https://api.gmi-serving.com/v1/chat/completions",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjA4ZDMxOTBjLWNjNzItNDc4ZS1hOGYwLTY3NmEwMGY1MDY2ZCIsInR5cGUiOiJpZV9tb2RlbCJ9.q7zRm2BAySo2zOi2QL5m6ukgmbhMZm2Ig4ITmJ3ZsM8"
}

# 快速测试任务
QUICK_TEST_TASK = """设计一个高性能的分布式缓存系统，要求：
1. 支持每秒100万次读写操作
2. 数据一致性保障
3. 故障自动恢复
4. 成本控制
请提供完整的技术方案和架构设计。"""

# ==================== 简化的LogicDepthDetector ====================
class QuickLogicDetector:
    """简化版逻辑深度检测器"""
    
    def detect_logic_depth(self, content: str) -> Dict[str, Any]:
        """快速检测逻辑深度"""
        
        # 推理深度指标
        reasoning_patterns = [
            r'因为.*所以', r'由于.*导致', r'基于.*可以',
            r'首先.*其次.*最后', r'第一.*第二.*第三'
        ]
        reasoning_score = sum(10 for pattern in reasoning_patterns if re.search(pattern, content))
        
        # 逻辑结构指标
        structure_patterns = [
            r'\d+\.', r'[一二三四五六七八九十]+、', r'##', r'###',
            r'然而', r'但是', r'因此', r'所以', r'另外'
        ]
        structure_score = sum(8 for pattern in structure_patterns if re.search(pattern, content))
        
        # 概念复杂度指标
        concept_patterns = [
            r'架构', r'算法', r'协议', r'框架', r'模式',
            r'原理', r'本质', r'规律', r'模型', r'理论'
        ]
        concept_score = sum(6 for pattern in concept_patterns if re.search(pattern, content))
        
        # 创新水平指标
        innovation_patterns = [
            r'创新', r'突破', r'革命', r'颠覆', r'前沿',
            r'重新定义', r'颠覆传统', r'创新.*方法'
        ]
        innovation_score = sum(12 for pattern in innovation_patterns if re.search(pattern, content))
        
        # 综合评分
        total_score = min(reasoning_score + structure_score + concept_score + innovation_score, 100)
        
        return {
            "overall_score": total_score,
            "reasoning_score": reasoning_score,
            "structure_score": structure_score,
            "concept_score": concept_score,
            "innovation_score": innovation_score,
            "content_length": len(content)
        }

# ==================== 简化的API客户端 ====================
class QuickAPIClient:
    """简化版API客户端"""
    
    def __init__(self):
        self.api_url = API_CONFIG["url"]
        self.api_token = API_CONFIG["token"]
    
    def call_api(self, model: str, prompt: str) -> Dict[str, Any]:
        """调用API"""
        try:
            data = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.7,
                "max_tokens": 3000
            }
            
            json_data = json.dumps(data).encode('utf-8')
            
            req = urllib.request.Request(
                self.api_url,
                data=json_data,
                headers={
                    'Authorization': f'Bearer {self.api_token}',
                    'Content-Type': 'application/json'
                }
            )
            
            timeout = 300 if "R1" in model else 120
            
            with urllib.request.urlopen(req, timeout=timeout) as response:
                if response.status == 200:
                    result = json.loads(response.read().decode('utf-8'))
                    
                    if "choices" in result and len(result["choices"]) > 0:
                        message = result["choices"][0]["message"]
                        content = message.get("content", "")
                        reasoning = message.get("reasoning_content") or ""
                        
                        return {
                            "success": True,
                            "content": content,
                            "reasoning_content": reasoning,
                            "model": model
                        }
            
            return {"success": False, "error": "API调用失败"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}

# ==================== CAP方法库 ====================
def create_cognitive_ascent_prompt(base_prompt: str) -> str:
    """认知提升协议"""
    return f"""
<THOUGHT>
运用Cognitive Ascent Protocol进行深度分析：

**第一性原理分解**：
- 核心本质是什么？
- 基础约束有哪些？
- 传统假设是什么？

**多角色探索**：
- 架构师视角：最优结构设计
- 性能专家视角：性能优化策略
- 创新者视角：突破性解决方案

**递归批判**：
- 分析是否深入？
- 是否存在更好路径？
- 有无思维局限？

**涌现综合**：
- 如何融合多角度洞察？
- 能否产生创新方案？
</THOUGHT>

{base_prompt}

请运用深度认知提升协议，从多专家视角分析，寻找突破性解决方案。
"""

def create_logos_inquisitor_prompt(base_prompt: str) -> str:
    """逻辑审议者协议"""
    return f"""
按逻辑审议者协议执行严格分析：

**第一阶段：解构定义**
- 问题核心：我理解为...
- 约束条件：包括...
- 成功标准：需要满足...

**第二阶段：穷举探索**
- 路径A：[方案1及推演]
- 路径B：[方案2及推演]  
- 路径C：[方案3及推演]
- 魔鬼代言人质询：最强反驳是...

**第三阶段：验证收敛**
- 交叉验证各路径
- 排除矛盾方案
- 构建论证链条

**第四阶段：置信度评估**
- 95%置信度评估
- 不确定性识别
- 备选方案提供

任务：{base_prompt}
"""

def create_r1_optimized_prompt(base_prompt: str) -> str:
    """R1优化协议"""
    return f"""
<thinking>
运用R1模型优化协议进行深度分析：

**第一层：问题解构**
- 核心问题：{base_prompt}
- 约束分析：[识别限制]
- 目标定义：[明确标准]

**第二层：多路径探索**
- 路径A：[可能性1]
- 路径B：[可能性2]
- 路径C：[可能性3]

**第三层：批判验证**
- 反例寻找：[主动质疑]
- 逻辑检查：[验证一致性]
- 边界测试：[极端分析]

**第四层：综合优化**
- 方案对比：[权衡分析]
- 最优选择：[证据决策]
- 风险控制：[预防措施]

**第五层：元认知反思**
- 过程审查：[检查漏洞]
- 结论验证：[测试稳健性]
- 改进识别：[持续优化]
</thinking>

{base_prompt}

请充分利用推理能力，展示完整分析过程，提供多方案比较和置信度评估。
"""

# ==================== 快速测试器 ====================
class QuickCAPTester:
    """快速CAP测试器"""
    
    def __init__(self):
        self.detector = QuickLogicDetector()
        self.client = QuickAPIClient()
        
        self.cap_methods = {
            "baseline": lambda x: x,
            "cognitive_ascent": create_cognitive_ascent_prompt,
            "logos_inquisitor": create_logos_inquisitor_prompt,
            "r1_optimized": create_r1_optimized_prompt
        }
    
    def run_quick_test(self, model: str = "deepseek-ai/DeepSeek-R1-0528") -> Dict[str, Any]:
        """运行快速测试"""
        
        print(f"🚀 CAP方法快速验证测试")
        print(f"🤖 测试模型: {model}")
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        results = {
            "model": model,
            "timestamp": datetime.now().isoformat(),
            "cap_results": {},
            "optimization_analysis": {}
        }
        
        baseline_score = 0
        
        # 测试每种CAP方法
        for cap_name, cap_method in self.cap_methods.items():
            print(f"\n📝 测试: {cap_name}")
            print("-" * 40)
            
            # 生成提示词
            prompt = cap_method(QUICK_TEST_TASK)
            
            # 调用API
            api_result = self.client.call_api(model, prompt)
            
            if api_result["success"]:
                # 选择分析内容
                if "R1" in model and api_result.get("reasoning_content"):
                    analysis_content = api_result["reasoning_content"]
                    content_source = "reasoning_content"
                else:
                    analysis_content = api_result["content"]
                    content_source = "main_content"
                
                # 逻辑深度分析
                logic_result = self.detector.detect_logic_depth(analysis_content)
                
                results["cap_results"][cap_name] = {
                    "content_source": content_source,
                    "content_length": len(analysis_content),
                    "logic_score": logic_result["overall_score"],
                    "dimension_scores": {
                        "reasoning": logic_result["reasoning_score"],
                        "structure": logic_result["structure_score"],
                        "concept": logic_result["concept_score"],
                        "innovation": logic_result["innovation_score"]
                    }
                }
                
                print(f"✅ 逻辑深度得分: {logic_result['overall_score']:.1f}")
                print(f"📊 维度得分: 推理{logic_result['reasoning_score']} | 结构{logic_result['structure_score']} | 概念{logic_result['concept_score']} | 创新{logic_result['innovation_score']}")
                print(f"📝 内容长度: {len(analysis_content)} 字符")
                
                if cap_name == "baseline":
                    baseline_score = logic_result["overall_score"]
                
            else:
                print(f"❌ 测试失败: {api_result['error']}")
                results["cap_results"][cap_name] = {"error": api_result["error"]}
            
            time.sleep(3)  # 避免API限流
        
        # 分析优化效果
        results["optimization_analysis"] = self._analyze_optimization(results["cap_results"], baseline_score)
        
        # 生成报告
        self._generate_quick_report(results)
        
        return results
    
    def _analyze_optimization(self, cap_results: Dict[str, Any], baseline_score: float) -> Dict[str, Any]:
        """分析优化效果"""
        
        optimization_effects = {}
        
        for cap_name, result in cap_results.items():
            if cap_name == "baseline" or "error" in result:
                continue
            
            optimized_score = result["logic_score"]
            optimization_gain = optimized_score - baseline_score
            optimization_ratio = (optimization_gain / baseline_score * 100) if baseline_score > 0 else 0
            
            optimization_effects[cap_name] = {
                "optimized_score": optimized_score,
                "optimization_gain": optimization_gain,
                "optimization_ratio": optimization_ratio
            }
        
        # 找出最佳方法
        if optimization_effects:
            best_cap = max(optimization_effects.keys(), key=lambda x: optimization_effects[x]["optimization_ratio"])
            max_gain = max(effect["optimization_gain"] for effect in optimization_effects.values())
        else:
            best_cap = None
            max_gain = 0
        
        return {
            "baseline_score": baseline_score,
            "optimization_effects": optimization_effects,
            "best_cap_method": best_cap,
            "max_optimization_gain": max_gain
        }
    
    def _generate_quick_report(self, results: Dict[str, Any]) -> None:
        """生成快速报告"""
        
        print("\n" + "=" * 60)
        print("📊 快速测试报告")
        print("=" * 60)
        
        analysis = results["optimization_analysis"]
        
        print(f"\n🎯 基准分数: {analysis['baseline_score']:.1f}")
        print(f"🚀 最大优化增益: {analysis['max_optimization_gain']:.1f}分")
        
        if analysis["best_cap_method"]:
            best_method = analysis["best_cap_method"]
            best_effect = analysis["optimization_effects"][best_method]
            print(f"🏆 最佳CAP方法: {best_method}")
            print(f"   优化后得分: {best_effect['optimized_score']:.1f}")
            print(f"   优化幅度: {best_effect['optimization_ratio']:.1f}%")
        
        print("\n📈 各方法优化效果:")
        for cap_name, effect in analysis["optimization_effects"].items():
            print(f"   {cap_name}: {effect['optimization_gain']:+.1f}分 ({effect['optimization_ratio']:+.1f}%)")
        
        print("\n💡 验证结论:")
        if analysis["max_optimization_gain"] > 15:
            print("   ✅ LogicDepthDetector成功检测到显著的CAP优化效果")
            print("   ✅ 证明了LogicDepthDetector作为综合能力测试工具的有效性")
        elif analysis["max_optimization_gain"] > 5:
            print("   ✅ LogicDepthDetector检测到中等的CAP优化效果")
            print("   ✅ 部分验证了评估工具的有效性")
        else:
            print("   ⚠️ 优化效果不明显，可能需要调整测试参数")

def main():
    """主函数"""
    print("🎯 CAP方法快速验证测试")
    print("目标：验证LogicDepthDetector作为CAP优化评估工具的有效性")
    print()
    
    tester = QuickCAPTester()
    
    # 选择测试模型
    model = input("请选择测试模型 (1: DeepSeek-R1, 2: DeepSeek-V3, 默认: R1): ").strip()
    if model == "2":
        test_model = "deepseek-ai/DeepSeek-V3-0324"
    else:
        test_model = "deepseek-ai/DeepSeek-R1-0528"
    
    try:
        results = tester.run_quick_test(test_model)
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"quick_cap_test_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 测试结果已保存: {filename}")
        print("🎉 快速验证测试完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    main()
