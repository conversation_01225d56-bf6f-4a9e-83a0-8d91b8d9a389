# V4.5 MCP分离架构测试指南

## 🔍 核心概念：什么是"挂起"

### **MCP客户端挂起状态定义**
- **不是**：终端执行任务时的挂起
- **而是**：IDE调用MCP客户端后，MCP客户端连接到Web服务器并进入等待状态

### **挂起状态的具体表现**
1. **IDE启动MCP客户端**：通过IDE的MCP配置自动启动
2. **自动连接Web服务器**：MCP客户端检测到Web服务器运行，连接到ws://localhost:25527
3. **进入挂起等待**：连接成功后，MCP客户端进入挂起状态，等待Web服务器发送任务指令
4. **保持连接状态**：在整个过程中，MCP客户端与IDE的连接保持活跃，但同时也与Web服务器保持WebSocket连接
5. **任务执行时仍挂起**：即使在执行任务时，MCP客户端仍然保持挂起状态，执行完成后继续等待下一个任务

### **架构关系图**
```
IDE ←→ MCP客户端 ←→ Web服务器
     (MCP协议)    (WebSocket)
                      ↑
                   用户通过调试界面发送任务
```

## 🎯 测试目标

验证V4.5客户端-服务端分离架构的完整功能：
- **MCP客户端挂起等待**：IDE调用后连接Web服务器并挂起等待任务指令
- **Web服务器任务下发**：通过调试界面API发送任务给挂起的MCP客户端
- **任务执行与持续挂起**：MCP客户端执行文件读取/写入任务并返回结果，然后继续挂起等待

## 📋 测试前准备

### 1. 代码修改完成确认
✅ **MCP客户端修改**：`tools/ace/src/four_layer_meeting_system/mcp_server/simple_ascii_launcher.py`
- 强制Web服务器客户端挂起模式
- 自动连接到ws://localhost:25527
- 挂起等待任务指令

✅ **Web服务器API添加**：`tools/ace/src/four_layer_meeting_server/server_launcher.py`
- `/api/send_task` - 通用任务下发接口
- `/api/task_status/<task_id>` - 查询任务执行状态
- `/api/connected_clients` - 获取已连接的MCP客户端列表
- `/api/execute_mcp_tool` - 便捷的MCP工具执行接口

## 🚀 测试步骤

### 步骤1：重启Web服务器
```bash
# 停止当前Web服务器（Ctrl+C）
# 重新启动Web服务器
cd C:\ExchangeWorks\xkong\xkongcloud
python tools\ace\src\four_layer_meeting_server\server_launcher.py
```

**预期输出**：
```
🚀 四重会议Web服务器启动中...
✅ HTTP API路由已添加
✅ MCP任务下发API路由已添加
✅ 四重会议Web服务器完全启动:
   📡 WebSocket通信: ws://localhost:25527
   🌐 Web界面访问: http://localhost:25526
   🔧 调试中心: http://localhost:25526/debug
```

### 步骤2：重启MCP客户端（在IDE中）
**重要说明**：MCP客户端是通过IDE的MCP配置启动的，不是在终端运行！

#### **正确的启动方式**：
1. **IDE配置**：MCP客户端通过IDE的MCP服务器配置自动启动
2. **配置信息**：
   ```json
   {
     "mcpServers": {
       "v4-context-guidance-simple": {
         "command": "python",
         "args": ["C:\\ExchangeWorks\\xkong\\xkongcloud\\tools\\ace\\src\\four_layer_meeting_system\\mcp_server\\simple_ascii_launcher.py"],
         "env": {"PYTHONIOENCODING": "utf-8"}
       }
     }
   }
   ```
3. **重启方法**：在IDE中重新加载MCP配置或重启IDE

#### **挂起状态的具体表现**：
- **IDE层面**：MCP客户端与IDE保持MCP协议连接
- **Web服务器层面**：同时与Web服务器保持WebSocket连接
- **双重连接**：MCP客户端同时服务于IDE和Web服务器

**预期输出**（在IDE的MCP客户端日志中）：
```
🚀 V4.5 MCP分离架构启动
🔍 Web服务器检测结果: 可用
✅ 检测到Web服务器运行，启动Web服务器客户端挂起模式
🔌 启动MCP客户端，连接到四重会议Web服务器...
🔄 正在连接到四重会议Web服务器...
🔌 连接尝试 1/3
✅ 成功连接到四重会议Web服务器
⏳ MCP客户端进入挂起状态，等待Web服务器任务指令...
📡 WebSocket连接地址: ws://localhost:25527
🌐 Web调试界面: http://localhost:25526/debug
============================================================
⏳ 挂起等待任务指令...
```

#### **挂起状态验证**：
- ✅ IDE可以正常调用MCP工具（如get_system_status）
- ✅ 同时Web服务器可以通过API发送任务给MCP客户端
- ✅ MCP客户端在执行Web服务器任务时，仍然保持与IDE的连接

### 步骤3：验证连接状态
访问：http://localhost:25526/api/connected_clients

**预期响应**：
```json
{
  "status": "success",
  "connected_clients": [
    {
      "client_id": "mcp_client_20250627_001234",
      "connected_at": "2025-06-27T00:12:34.567890",
      "last_seen": "2025-06-27T00:12:34.567890"
    }
  ],
  "total_count": 1
}
```

## 🧪 核心测试用例

### 测试1：文件读取任务
**API调用**：
```bash
curl -X POST http://localhost:25526/api/send_task \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "file_operation",
    "command": {
      "operation": "read",
      "file_path": "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/fork/四重会议/todo2/promte/9-BS独立/V45-MCP分离架构设计要求.md"
    },
    "metadata": {
      "priority": "normal",
      "timeout": 300
    }
  }'
```

**预期MCP客户端输出**：
```
📥 收到任务指令: file_operation (ID: task_20250627_001235_0)
🔄 开始执行任务（保持挂起状态）...
⚙️ 执行方式: mcp_client_direct_python
📤 任务执行完成，继续挂起等待下一个任务...
```

**预期API响应**：
```json
{
  "status": "success",
  "task_id": "task_20250627_001235_0"
}
```

### 测试2：文件写入任务
**API调用**：
```bash
curl -X POST http://localhost:25526/api/send_task \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "file_operation",
    "command": {
      "operation": "write",
      "file_path": "test_mcp_write_output.txt",
      "content": "V4.5 MCP分离架构测试成功\n时间: 2025-06-27\n执行方式: MCP客户端直接Python执行\n状态: 文件写入完成"
    },
    "metadata": {
      "priority": "normal",
      "timeout": 300
    }
  }'
```

**预期结果**：
- MCP客户端执行文件写入
- 在项目根目录生成`test_mcp_write_output.txt`文件
- 文件内容包含测试信息

### 测试3：代码分析任务
**API调用**：
```bash
curl -X POST http://localhost:25526/api/send_task \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "code_analysis",
    "command": {
      "file_path": "tools/ace/src/four_layer_meeting_system/mcp_server/simple_ascii_launcher.py",
      "analysis_type": "structure"
    }
  }'
```

**预期结果**：
- 返回代码结构分析（行数、函数数、类数等）
- 执行方式：mcp_client_direct_analysis

### 测试4：MCP工具执行任务
**API调用**：
```bash
curl -X POST http://localhost:25526/api/execute_mcp_tool \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "get_system_status",
    "arguments": {}
  }'
```

## 🔍 调试界面测试

### 方法1：使用curl命令
如上述测试用例所示，直接使用curl调用API。

### 方法2：使用调试界面（如果支持）
1. 访问：http://localhost:25526/debug
2. 在命令输入框中输入JSON格式的任务指令
3. 观察MCP客户端的响应

### 方法3：使用浏览器开发者工具
```javascript
// 在浏览器控制台执行
fetch('http://localhost:25526/api/send_task', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    "task_type": "file_operation",
    "command": {
      "operation": "read",
      "file_path": "README.md"
    }
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

## ✅ 验证标准

### 1. 架构分离验证
- ✅ **MCP客户端双重连接**：同时连接IDE（MCP协议）和Web服务器（WebSocket）
- ✅ **挂起状态正确**：MCP客户端连接Web服务器后进入挂起等待状态
- ✅ **Web服务器任务下发**：能通过API发送任务指令给挂起的MCP客户端
- ✅ **任务传输正确**：任务通过WebSocket正确传输到MCP客户端
- ✅ **持续挂起机制**：MCP客户端执行任务后继续保持挂起等待状态
- ✅ **IDE连接保持**：在整个过程中IDE与MCP客户端的连接保持正常

### 2. 功能完整性验证
- ✅ 文件读取：能读取指定文件内容
- ✅ 文件写入：能创建新文件并写入内容
- ✅ 代码分析：能分析代码结构
- ✅ MCP工具：能执行现有MCP工具

### 3. 执行方式验证
- ✅ `mcp_client_direct_python` - 文件操作
- ✅ `mcp_client_direct_analysis` - 代码分析
- ✅ `mcp_tool_direct_call` - MCP工具执行

### 4. 状态管理验证
- ✅ 任务队列正确管理
- ✅ 客户端连接状态正确
- ✅ 任务执行状态可查询

## 🚨 故障排除

### 问题1：MCP客户端未进入挂起状态
**症状**：MCP客户端还在本地模式运行，没有连接到Web服务器
**原因分析**：
- MCP客户端没有检测到Web服务器运行
- 代码修改没有生效（还在运行旧版本）
- IDE没有重新加载MCP配置

**解决方案**：
1. **确认Web服务器运行**：检查端口25527是否有WebSocket服务
2. **重启IDE**：让MCP客户端重新加载修改后的代码
3. **强制重新加载**：在IDE中重新加载MCP配置
4. **检查日志**：查看MCP客户端是否输出"V4.5 MCP分离架构启动"

**验证方法**：
- 调用`get_system_status`工具，检查是否还能正常响应（说明IDE连接正常）
- 同时检查Web服务器是否能检测到MCP客户端连接

### 问题2：Web服务器API不可用
**症状**：API返回404或500错误
**解决**：重启Web服务器，确保新代码已加载

### 问题3：WebSocket连接失败
**症状**：MCP客户端无法连接到Web服务器
**解决**：检查端口25527是否被占用，确保Web服务器正常运行

### 问题4：任务执行失败
**症状**：任务发送成功但执行失败
**解决**：检查文件路径、权限等具体错误信息

## 📊 测试报告模板

```
V4.5 MCP分离架构测试报告
测试时间：2025-06-27
测试人员：[姓名]

1. 环境准备：
   - Web服务器启动：✅/❌
   - MCP客户端挂起：✅/❌
   - API可用性：✅/❌

2. 功能测试：
   - 文件读取：✅/❌
   - 文件写入：✅/❌
   - 代码分析：✅/❌
   - MCP工具执行：✅/❌

3. 架构验证：
   - 客户端-服务端分离：✅/❌
   - 挂起等待机制：✅/❌
   - 任务下发机制：✅/❌
   - 结果回传机制：✅/❌

4. 问题记录：
   [记录遇到的问题和解决方案]

5. 总体评价：
   V4.5 MCP分离架构功能完整性：[百分比]
```
