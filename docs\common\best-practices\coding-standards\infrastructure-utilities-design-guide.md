---
title: 基础设施工具类设计指南
document_id: C046
document_type: 编码标准
category: 最佳实践
scope: 通用设计指南
keywords: [工具类设计, 基础设施, 配置管理, 日志处理, SQL安全, 数据验证]
created_date: 2025-01-15
updated_date: 2025-01-15
status: 生效
version: 1.0
authors: [系统架构组]
source_extraction: F003-PostgreSQL迁移重构项目
---

# 基础设施工具类设计指南

## 概述

基础设施工具类是项目中提供通用功能的核心组件，负责配置管理、日志处理、SQL操作、数据验证等基础功能。本指南定义了设计高质量、可复用、安全的基础设施工具类的标准和最佳实践。

## 设计原则

### 1. 核心设计原则

- **单一职责**: 每个工具类专注于一个特定领域
- **静态方法优先**: 提供无状态的静态工具方法
- **线程安全**: 确保并发环境下的安全性
- **异常安全**: 提供完善的异常处理机制
- **测试友好**: 设计易于测试的接口

### 2. 命名规范

```java
// 工具类命名：功能域 + Utils
public class ConfigurationUtils { }
public class LoggingUtils { }
public class SqlUtils { }
public class ValidationUtils { }

// 方法命名：动词 + 名词
public static UidGeneratorConfig createTestConfig();
public static String formatMessage();
public static boolean validateNotNull();
```

## 四大核心工具类

### 1. ConfigurationUtils - 配置管理工具类

#### 1.1 设计目标
提供统一的配置管理机制，简化测试和生产环境的配置处理。

#### 1.2 核心功能

```java
public class ConfigurationUtils {
    
    /**
     * 创建测试环境配置
     */
    public static UidGeneratorConfig createTestConfig(String appName, String env) {
        return UidGeneratorConfig.builder()
            .applicationName(appName)
            .environment(env)
            .leaseDurationSeconds(60)  // 测试环境短租约
            .highConfidenceThreshold(100)  // 测试环境低阈值
            .minimumAcceptableScore(50)
            .recoveryStrategy("STRICT")
            .build();
    }
    
    /**
     * 创建生产环境配置
     */
    public static UidGeneratorConfig createProductionConfig(String appName, String env) {
        return UidGeneratorConfig.builder()
            .applicationName(appName)
            .environment(env)
            .leaseDurationSeconds(300)  // 生产环境长租约
            .highConfidenceThreshold(150)  // 生产环境高阈值
            .minimumAcceptableScore(70)
            .recoveryStrategy("ALERT_AUTO_WITH_TIMEOUT")
            .build();
    }
    
    /**
     * 配置验证
     */
    public static boolean validateConfig(UidGeneratorConfig config) {
        if (config == null) return false;
        if (StringUtils.isBlank(config.getApplicationName())) return false;
        if (config.getLeaseDurationSeconds() <= 0) return false;
        if (config.getHighConfidenceThreshold() <= config.getMinimumAcceptableScore()) return false;
        return true;
    }
    
    /**
     * 配置模板生成
     */
    public static Properties generateConfigTemplate() {
        Properties template = new Properties();
        template.setProperty("uid.application.name", "${spring.application.name}");
        template.setProperty("uid.environment", "${spring.profiles.active}");
        template.setProperty("uid.lease.duration.seconds", "300");
        template.setProperty("uid.confidence.high.threshold", "150");
        template.setProperty("uid.confidence.minimum.score", "70");
        return template;
    }
}
```

#### 1.3 测试策略

```java
@Test
public void testConfigCreation() {
    UidGeneratorConfig testConfig = ConfigurationUtils.createTestConfig("test-app", "test");
    assertNotNull(testConfig);
    assertEquals("test-app", testConfig.getApplicationName());
    assertEquals(60, testConfig.getLeaseDurationSeconds());
}

@Test
public void testConfigValidation() {
    UidGeneratorConfig validConfig = ConfigurationUtils.createTestConfig("app", "test");
    assertTrue(ConfigurationUtils.validateConfig(validConfig));
    
    UidGeneratorConfig invalidConfig = new UidGeneratorConfig();
    assertFalse(ConfigurationUtils.validateConfig(invalidConfig));
}
```

### 2. LoggingUtils - 日志处理工具类

#### 2.1 设计目标
统一日志输出格式，提升调试和监控效率。

#### 2.2 核心功能

```java
public class LoggingUtils {
    
    private static final String PERFORMANCE_LOG_FORMAT = "[PERF] {} took {}ms";
    private static final String ERROR_LOG_FORMAT = "[ERROR] {} failed: {}";
    private static final String BUSINESS_LOG_FORMAT = "[BIZ] {} - {}";
    
    /**
     * 格式化日志消息
     */
    public static String formatMessage(String template, Object... args) {
        if (template == null) return "";
        if (args == null || args.length == 0) return template;
        
        try {
            return MessageFormat.format(template, args);
        } catch (Exception e) {
            return template + " [格式化失败: " + e.getMessage() + "]";
        }
    }
    
    /**
     * 性能日志记录
     */
    public static void logPerformance(String operation, long duration) {
        Logger logger = LoggerFactory.getLogger(getCallerClass());
        logger.info(PERFORMANCE_LOG_FORMAT, operation, duration);
    }
    
    /**
     * 错误日志记录
     */
    public static void logError(String operation, Exception e) {
        Logger logger = LoggerFactory.getLogger(getCallerClass());
        logger.error(ERROR_LOG_FORMAT, operation, e.getMessage(), e);
    }
    
    /**
     * 业务日志记录
     */
    public static void logBusiness(String event, Map<String, Object> context) {
        Logger logger = LoggerFactory.getLogger(getCallerClass());
        String contextStr = context != null ? context.toString() : "{}";
        logger.info(BUSINESS_LOG_FORMAT, event, contextStr);
    }
    
    /**
     * 获取调用者类名
     */
    private static Class<?> getCallerClass() {
        StackTraceElement[] stack = Thread.currentThread().getStackTrace();
        // 跳过getStackTrace、getCallerClass、当前方法
        for (int i = 3; i < stack.length; i++) {
            String className = stack[i].getClassName();
            if (!className.equals(LoggingUtils.class.getName())) {
                try {
                    return Class.forName(className);
                } catch (ClassNotFoundException e) {
                    return LoggingUtils.class;
                }
            }
        }
        return LoggingUtils.class;
    }
}
```

### 3. SqlUtils - SQL操作工具类

#### 3.1 设计目标
提供安全的SQL构建和操作方法，减少SQL注入风险。

#### 3.2 核心功能

```java
public class SqlUtils {
    
    private static final Pattern SQL_INJECTION_PATTERN = 
        Pattern.compile("('.+(\\s)*(or|and|union|select|insert|update|delete|drop|create|alter)\\s*.*)", 
                       Pattern.CASE_INSENSITIVE);
    
    /**
     * 安全的SQL构建
     */
    public static String buildSafeSql(String template, Object... params) {
        if (template == null) {
            throw new IllegalArgumentException("SQL模板不能为空");
        }
        
        // 验证模板安全性
        if (!validateSqlSafety(template)) {
            throw new SecurityException("SQL模板包含潜在的安全风险");
        }
        
        // 转义参数
        Object[] escapedParams = Arrays.stream(params)
            .map(SqlUtils::escapeParameter)
            .toArray();
        
        return MessageFormat.format(template, escapedParams);
    }
    
    /**
     * SQL模板处理
     */
    public static String processTemplate(String template, Map<String, Object> context) {
        if (template == null || context == null) {
            return template;
        }
        
        String result = template;
        for (Map.Entry<String, Object> entry : context.entrySet()) {
            String placeholder = "{" + entry.getKey() + "}";
            String value = escapeParameter(entry.getValue()).toString();
            result = result.replace(placeholder, value);
        }
        
        return result;
    }
    
    /**
     * SQL安全性验证
     */
    public static boolean validateSqlSafety(String sql) {
        if (sql == null) return false;
        
        // 检查SQL注入模式
        if (SQL_INJECTION_PATTERN.matcher(sql).find()) {
            return false;
        }
        
        // 检查危险关键词
        String upperSql = sql.toUpperCase();
        String[] dangerousKeywords = {"DROP", "DELETE", "TRUNCATE", "ALTER"};
        for (String keyword : dangerousKeywords) {
            if (upperSql.contains(keyword)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 参数转义
     */
    public static String escapeParameter(Object param) {
        if (param == null) {
            return "NULL";
        }
        
        String str = param.toString();
        
        // 转义单引号
        str = str.replace("'", "''");
        
        // 转义反斜杠
        str = str.replace("\\", "\\\\");
        
        // 移除控制字符
        str = str.replaceAll("[\\x00-\\x1F\\x7F]", "");
        
        return str;
    }
}
```

### 4. ValidationUtils - 数据验证工具类

#### 4.1 设计目标
提供统一的参数验证和数据库验证功能，提升代码健壮性。

#### 4.2 核心功能

```java
public class ValidationUtils {
    
    /**
     * 非空验证
     */
    public static void validateNotNull(Object obj, String paramName) {
        if (obj == null) {
            throw new IllegalArgumentException(paramName + " 不能为空");
        }
    }
    
    /**
     * 范围验证
     */
    public static void validateRange(long value, long min, long max, String paramName) {
        if (value < min || value > max) {
            throw new IllegalArgumentException(
                String.format("%s 必须在 %d 到 %d 之间，当前值: %d", paramName, min, max, value));
        }
    }
    
    /**
     * 字符串验证
     */
    public static void validateString(String str, int minLength, int maxLength, String paramName) {
        validateNotNull(str, paramName);
        
        if (str.length() < minLength || str.length() > maxLength) {
            throw new IllegalArgumentException(
                String.format("%s 长度必须在 %d 到 %d 之间，当前长度: %d", 
                             paramName, minLength, maxLength, str.length()));
        }
    }
    
    /**
     * 数据库表存在性验证
     */
    public static boolean validateTableExists(JdbcTemplate jdbc, String tableName) {
        try {
            String sql = "SELECT 1 FROM " + tableName + " LIMIT 1";
            jdbc.queryForObject(sql, Integer.class);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 数据库列存在性验证
     */
    public static boolean validateColumnExists(JdbcTemplate jdbc, String tableName, String columnName) {
        try {
            String sql = "SELECT " + columnName + " FROM " + tableName + " LIMIT 1";
            jdbc.queryForList(sql);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 业务规则验证 - 实例ID
     */
    public static boolean validateInstanceId(Long instanceId) {
        return instanceId != null && instanceId > 0;
    }
    
    /**
     * 业务规则验证 - Worker ID
     */
    public static boolean validateWorkerId(Long workerId) {
        return workerId != null && workerId >= 0 && workerId <= 262143;
    }
}
```

## 测试策略

### 1. 测试覆盖要求

- **单元测试覆盖率**: ≥90%
- **边界条件测试**: 必须覆盖
- **异常处理测试**: 必须覆盖
- **并发安全测试**: 推荐覆盖

### 2. 测试模板

```java
@Test
public void testNormalCase() {
    // 正常情况测试
}

@Test
public void testBoundaryConditions() {
    // 边界条件测试
}

@Test
public void testExceptionHandling() {
    // 异常处理测试
}

@Test
public void testConcurrentAccess() {
    // 并发访问测试
}
```

## 性能考虑

### 1. 性能优化原则

- **避免重复计算**: 缓存计算结果
- **减少对象创建**: 复用对象
- **优化字符串操作**: 使用StringBuilder
- **合理使用缓存**: 避免内存泄露

### 2. 性能监控

```java
// 性能监控示例
public static <T> T withPerformanceLogging(String operation, Supplier<T> supplier) {
    long startTime = System.currentTimeMillis();
    try {
        T result = supplier.get();
        long duration = System.currentTimeMillis() - startTime;
        LoggingUtils.logPerformance(operation, duration);
        return result;
    } catch (Exception e) {
        long duration = System.currentTimeMillis() - startTime;
        LoggingUtils.logError(operation + " (耗时: " + duration + "ms)", e);
        throw e;
    }
}
```

## 最佳实践

### 1. 设计最佳实践

- **接口设计**: 简洁明了，易于理解
- **参数验证**: 在方法入口进行验证
- **异常处理**: 提供有意义的错误信息
- **文档注释**: 详细说明方法用途和参数

### 2. 使用最佳实践

- **统一使用**: 项目内统一使用工具类
- **避免重复**: 不要重复实现相同功能
- **及时更新**: 根据需求及时更新工具类
- **性能监控**: 监控工具类的性能表现

## 扩展指南

### 1. 新增工具类

1. 确定功能域和职责边界
2. 设计接口和方法签名
3. 实现核心功能
4. 编写完整的单元测试
5. 更新文档和示例

### 2. 工具类演进

1. 保持向后兼容性
2. 使用@Deprecated标记废弃方法
3. 提供迁移指南
4. 逐步移除废弃功能

## 相关标准

- [配置类设计标准](./configuration-class-standards.md)
- [异常处理最佳实践](../error-handling/exception-handling-guide.md)
- [日志记录规范](../logging/logging-standards.md)
- [单元测试指南](../testing/unit-testing-guide.md)
