# 05-PyCRUDExecutor工具执行器细化设计（伪代码级）

## 1. 现有实现溯源
- 文件：`tools/ace/src/executors/validation_driven_executor.py`
- 主要类/方法：
  - `class PyCRUDExecutor`（第1187行）
  - `async def execute(self, complete_output, pycrud_operations)`（第1194行）
- 现状：
  - 仅支持基于自由文本的操作执行，未有专门的tool_calls结构化执行分支
  - 缺乏tool_call到本地函数的安全映射与注册表机制

## 2. 目标结构与接口（伪代码级）

### 2.1 PyCRUDExecutor类结构
```python
class PyCRUDExecutor:
    def __init__(self):
        self.function_registry = {
            'file_manager.create_file': self.create_file,
            'file_manager.delete_file': self.delete_file,
            # ...更多函数注册...
        }

    async def execute_tool_calls(self, tool_calls: List[Dict]) -> Dict:
        execution_results = []
        for tool_call in tool_calls:
            function_name = tool_call.get('function', {}).get('name')
            try:
                arguments = json.loads(tool_call.get('function', {}).get('arguments', '{}'))
                func = self.function_registry[function_name]
                # 参数校验（可选）
                result = await func(**arguments)
                execution_results.append({'tool_call': tool_call, 'status': 'success', 'result': result})
            except Exception as e:
                execution_results.append({'tool_call': tool_call, 'status': 'error', 'result': str(e)})
        return {'status': 'completed', 'results': execution_results}
```

### 2.2 本地函数实现示例
```python
async def create_file(self, path: str, content: str) -> bool:
    # 实际文件写入逻辑
    ...
    return True

async def delete_file(self, path: str) -> bool:
    # 实际文件删除逻辑
    ...
    return True
```

### 2.3 异常处理与安全性
- 未注册函数名时抛出KeyError，捕获并记录为error状态
- 参数反序列化失败、类型不符等均捕获异常并返回详细信息

### 2.4 单元测试建议
```python
def test_execute_tool_calls_success():
    executor = PyCRUDExecutor()
    tool_calls = [{
        'function': {'name': 'file_manager.create_file', 'arguments': '{"path": "a.txt", "content": "hi"}'}
    }]
    result = asyncio.run(executor.execute_tool_calls(tool_calls))
    assert result['results'][0]['status'] == 'success'

def test_execute_tool_calls_error():
    executor = PyCRUDExecutor()
    tool_calls = [{
        'function': {'name': 'file_manager.unknown', 'arguments': '{}'}
    }]
    result = asyncio.run(executor.execute_tool_calls(tool_calls))
    assert result['results'][0]['status'] == 'error'
```

## 3. 需变更/新建/删除内容（精确到代码）
- 新建：
  - `execute_tool_calls`方法、函数注册表、本地函数实现
- 修改：
  - ValidationDrivenExecutor主流程，集成tool_calls分支时调用execute_tool_calls
- 单元测试：
  - `test_pycrud_executor.py`，覆盖所有tool_call类型的执行

## 4. 兼容性与测试建议
- execute_tool_calls为新增方法，不影响旧有流程
- 建议：
  - 单元测试：所有tool_call类型的本地执行
  - 集成测试：端到端流程回归

## 5. 代码引用与路径
- `tools/ace/src/executors/validation_driven_executor.py`（PyCRUDExecutor类、主流程调用点） 