---
title: UID库类图
description: 展示xkongcloud-commons-uid库主要类的结构和关系
created_date: 2025-05-18
updated_date: 2025-05-18
version: 1.0
status: 草稿
author: AI助手
---

# UID库类图

此图展示了xkongcloud-commons-uid库中主要类的结构和关系。

```mermaid
classDiagram
    class UidGenerator {
        -WorkerIdAssigner workerIdAssigner
        -long workerId
        -long timeBits
        -long workerBits
        -long seqBits
        -long epochSeconds
        +getUID() long
        +parseUID(long uid) String
    }
    
    class WorkerIdAssigner {
        <<interface>>
        +assignWorkerId() long
    }
    
    class PersistentInstanceWorkerIdAssigner {
        -JdbcTemplate jdbcTemplate
        -TransactionTemplate transactionTemplate
        -PersistentInstanceManager instanceManager
        -String schemaName
        -long leaseTimeoutSeconds
        -long renewalIntervalSeconds
        +assignWorkerId() long
        -renewLease() void
        -acquireWorkerId() long
    }
    
    class PersistentInstanceManager {
        -JdbcTemplate jdbcTemplate
        -TransactionTemplate transactionTemplate
        -String applicationName
        -String environment
        -String instanceGroup
        -String localStoragePath
        -boolean recoveryEnabled
        -int highConfidenceThreshold
        -int minimumAcceptableScore
        -String recoveryStrategy
        -int recoveryTimeoutSeconds
        -Long instanceIdOverride
        -boolean encryptionEnabled
        -String schemaName
        -KeyManagementService keyManagementService
        +getInstanceId() long
        -loadLocalInstanceId() Long
        -recoverInstanceId() long
        -registerNewInstance() long
        -saveInstanceId(long instanceId) void
    }
    
    class PersistentInstanceManagerBuilder {
        -JdbcTemplate jdbcTemplate
        -TransactionTemplate transactionTemplate
        -String applicationName
        -String environment
        -String instanceGroup
        -String localStoragePath
        -boolean recoveryEnabled
        -int highConfidenceThreshold
        -int minimumAcceptableScore
        -String recoveryStrategy
        -int recoveryTimeoutSeconds
        -Long instanceIdOverride
        -boolean encryptionEnabled
        -String schemaName
        -KeyManagementService keyManagementService
        +withJdbcTemplate(JdbcTemplate jdbcTemplate) PersistentInstanceManagerBuilder
        +withTransactionTemplate(TransactionTemplate transactionTemplate) PersistentInstanceManagerBuilder
        +withApplicationName(String applicationName) PersistentInstanceManagerBuilder
        +withEnvironment(String environment) PersistentInstanceManagerBuilder
        +withInstanceGroup(String instanceGroup) PersistentInstanceManagerBuilder
        +withLocalStoragePath(String localStoragePath) PersistentInstanceManagerBuilder
        +withRecoveryEnabled(boolean recoveryEnabled) PersistentInstanceManagerBuilder
        +withHighConfidenceThreshold(int highConfidenceThreshold) PersistentInstanceManagerBuilder
        +withMinimumAcceptableScore(int minimumAcceptableScore) PersistentInstanceManagerBuilder
        +withRecoveryStrategy(String recoveryStrategy) PersistentInstanceManagerBuilder
        +withRecoveryTimeoutSeconds(int recoveryTimeoutSeconds) PersistentInstanceManagerBuilder
        +withInstanceIdOverride(Long instanceIdOverride) PersistentInstanceManagerBuilder
        +withEncryptionEnabled(boolean encryptionEnabled) PersistentInstanceManagerBuilder
        +withSchemaName(String schemaName) PersistentInstanceManagerBuilder
        +withKeyManagementService(KeyManagementService keyManagementService) PersistentInstanceManagerBuilder
        +build() PersistentInstanceManager
    }
    
    class KeyManagementService {
        -JdbcTemplate jdbcTemplate
        -TransactionTemplate transactionTemplate
        -String applicationName
        -String environment
        -String schemaName
        -Map~String, String~ keyCache
        +getEncryptionKey() String
        +isEncryptionEnabled() boolean
        -createEncryptionKey() String
    }
    
    class MachineFingerprints {
        +collectFingerprints() Map~String, String~
        -getBiosUuid() String
        -getSystemSerialNumber() String
        -getMacAddresses() List~String~
        -getHostname() String
        -getCloudInstanceId() String
    }
    
    class ValidationResultCache {
        -ConcurrentMap~String, Boolean~ validationResults
        +recordValidation(String key, boolean result) void
        +isValidated(String key) boolean
        +clearCache() void
    }
    
    class UidValidationUtils {
        +validateDbConnection(JdbcTemplate jdbcTemplate) boolean
        +validateSchema(JdbcTemplate jdbcTemplate, String schemaName) boolean
        +validateTable(JdbcTemplate jdbcTemplate, String schemaName, String tableName) boolean
        +validateParameter(String paramName, Object paramValue) boolean
    }
    
    class UidTableManager {
        +createSchema(JdbcTemplate jdbcTemplate, String schemaName) void
        +createTables(JdbcTemplate jdbcTemplate, String schemaName) void
        +dropTables(JdbcTemplate jdbcTemplate, String schemaName) void
        +createTablesIfNotExist(JdbcTemplate jdbcTemplate, String schemaName) void
        +validateTables(JdbcTemplate jdbcTemplate, String schemaName) boolean
        +fillWorkerIdAssignments(JdbcTemplate jdbcTemplate, int workerBits, String schemaName) void
    }
    
    class WorkerNodeType {
        <<enumeration>>
        CONTAINER
        ACTUAL
        +getCode() int
        +getDescription() String
    }
    
    UidGenerator --> WorkerIdAssigner
    PersistentInstanceWorkerIdAssigner ..|> WorkerIdAssigner
    PersistentInstanceWorkerIdAssigner --> PersistentInstanceManager
    PersistentInstanceManagerBuilder --> PersistentInstanceManager : creates
    PersistentInstanceManager --> KeyManagementService
    PersistentInstanceManager --> MachineFingerprints
    PersistentInstanceManager --> UidValidationUtils
    PersistentInstanceWorkerIdAssigner --> UidValidationUtils
    KeyManagementService --> UidValidationUtils
    UidValidationUtils --> ValidationResultCache
    UidTableManager --> UidValidationUtils
```
