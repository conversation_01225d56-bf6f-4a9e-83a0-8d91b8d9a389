# 统一配置URI方案（详细迁移说明）

## 目标
- 所有API相关接口统一暴露在 `/api/config/api-management/xxx` 路径下（URI不变）。
- 业务实现分散在各自的py文件，避免单文件过大。
- 路由注册统一在 web_api.py，便于维护和扩展。

---

## 详细迁移步骤与代码位置

### 1. 业务实现分离

#### 1.1 拆分业务实现
- 将原 `tools/ace/src/web_interface/blueprints/api_management_bp.py` 中的每个接口实现（如 `batch_create_apis`, `auto_test_apis`, `smart_parse_input`, `get_api_status`, `explore_related_models`）
- 拷贝到各自的业务文件（可全部放在 `api_management_bp.py`，或按功能拆分为多个py文件）。
- **注意：这些函数只保留业务逻辑，不再用 `@api_management_bp.route` 装饰器注册路由。**

**示例：**
```python
# tools/ace/src/web_interface/blueprints/api_management_bp.py

def batch_create_apis():
    # ...原有实现...

def auto_test_apis():
    # ...原有实现...
# 其它同理
```

#### 1.2 业务归属划分（需迁移到 api_management_bp.py 的API管理相关接口清单）

| 路由 | 业务函数 | 依赖/说明 |
|------|----------|-----------|
| /api-management/batch-create | batch_create_apis | API批量创建，核心业务 |
| /api-management/smart-parse | smart_parse_api_input | 智能解析API输入，依赖SmartInputController |
| /api-management/auto-test | auto_test_apis | API自动测试，依赖perform_real_http_test |
| /api-management/status | get_api_management_status | 获取API管理状态，依赖APIAccountDatabase |
| /api-management/explore-models | explore_related_models | 模型探索，依赖perform_model_exploration |

- 以上接口的**真实业务实现全部迁移到 api_management_bp.py**，只在 web_api.py 做 add_url_rule 路由注册。
- 其它如配置中心、模板、导入导出等接口不属于API管理业务，无需迁移。

### 2. 统一路由注册

#### 2.1 在 web_api.py 统一注册路由
- 打开 `tools/ace/src/configuration_center/web_api.py`
- 导入所有业务实现函数：
```python
from web_interface.blueprints.api_management_bp import (
    batch_create_apis, auto_test_apis, smart_parse_api_input, get_api_management_status, explore_related_models
)
```
- 用 `config_api.add_url_rule` 注册所有API相关路由：
```python
config_api = Blueprint('config_api', __name__, url_prefix='/api/config')

config_api.add_url_rule('/api-management/batch-create', view_func=batch_create_apis, methods=['POST'])
config_api.add_url_rule('/api-management/auto-test', view_func=auto_test_apis, methods=['POST'])
config_api.add_url_rule('/api-management/smart-parse', view_func=smart_parse_api_input, methods=['POST'])
config_api.add_url_rule('/api-management/status', view_func=get_api_management_status, methods=['GET'])
config_api.add_url_rule('/api-management/explore-models', view_func=explore_related_models, methods=['POST'])
```
- **删除/注释掉 web_api.py 里原有的“假实现”或重复接口。**

### 3. 主app注册蓝图
- 在 `tools/ace/src/web_interface/app.py` 中，**只注册 config_api 蓝图**：
```python
from configuration_center.web_api import config_api
self.app.register_blueprint(config_api)
```
- **删除/注释掉对 api_management_bp 的注册。**

### 4. 清理无用代码
- 删除 `api_management_bp.py` 里所有 `@api_management_bp.route` 装饰器。
- 删除 `api_management_bp` 蓝图对象本身（如不再需要）。
- 删除 web_api.py 里所有 mock/假实现。

### 5. 前端/测试系统
- **无需任何改动**，继续使用 `/api/config/api-management/xxx` 路径。

---

## 变更点一览

| 文件 | 位置/函数 | 操作 |
|------|-----------|------|
| api_management_bp.py | 各API实现函数 | 保留业务逻辑，去掉路由装饰器，迁移所有API管理相关接口 |
| web_api.py | 顶部导入、add_url_rule注册 | 统一注册所有API路由 |
| web_api.py | 原有假实现 | 删除/注释 |
| app.py | 蓝图注册 | 只注册 config_api，去掉 api_management_bp |

---

## 迁移后结构优势
- URI不变，兼容所有现有前端和测试。
- 业务代码分离，避免单文件过大和AI幻觉。
- 路由聚合，结构清晰，易于维护和扩展。

---

## 适用范围
- 适用于所有需要统一API管理、提升可维护性和扩展性的中大型项目。 