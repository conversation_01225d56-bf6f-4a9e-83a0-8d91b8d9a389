# V4 MCP Server第一阶段核心组件详细设计

## 📋 文档概述

**文档ID**: V4-MCP-PHASE1-CORE-COMPONENTS-002
**创建日期**: 2025-06-18
**版本**: F007-mcp-phase1-v1.0.L1.1.0
**目标**: 详细设计第一阶段MCP Server的核心组件，基于DRY原则复用V4设计
**模板引用**: @TEMPLATE_REF:../核心/V4架构信息AI填充模板.md#架构设计核心

## 🏗️ 核心组件架构

### 1. V4 MCP Server主服务
```python
# tools/ace/mcp/v4_context_guidance_server/core/v4_mcp_server.py
# 基于 @REF:docs/features/T001-create-plans-20250612/v4/design/06-技术实施方案.md 的服务架构模式

class V4ContextGuidanceServer:
    """
    V4上下文引导MCP Server主服务
    复用V4服务架构模式，实现精准的IDE AI控制
    """
    
    def __init__(self):
        # 复用V4组件协调机制
        self.task_context_manager = TaskContextManager()
        self.progress_tracker = ProgressTracker()
        self.checkresult_parser = V4CheckResultParser()
        self.modification_controller = ModificationController()
        
        # 复用V4质量门禁机制
        self.quality_gate = QualityGateController()

        # 🆕 IDE AI幻觉检测和会话质量监控
        self.hallucination_detector = HallucinationDetector()
        self.session_quality_monitor = SessionQualityMonitor()
        self.human_progress_reporter = HumanProgressReporter()
        
        # 复用V4错误处理框架
        self.error_handler = ErrorHandler()
        
    def initialize_modification_task(self, checkresult_path: str) -> Dict:
        """
        初始化修改任务
        基于V4任务初始化模式
        """
        
    def get_next_modification_batch(self, batch_size: int = 3) -> Dict:
        """
        获取下一批修改指令
        基于V4批次处理机制
        """
        
    def validate_and_continue(self, completed_modifications: List[Dict]) -> Dict:
        """
        验证修改结果并继续下一批
        基于V4验证流程设计
        """
```

### 2. 基于现有ACE算法的任务上下文管理器
```python
# tools/ace/mcp/v4_context_guidance_server/core/task_context_manager.py
# 基于 @REF:tools/ace/src/algorithms/ 现有已验证算法的DRY复用

import sys
from pathlib import Path

# 导入现有ACE算法模块（已验证的生产级别代码）
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "ace" / "src"))
from algorithms.v4_multi_dimensional_analyzer import V4MultiDimensionalAnalyzer
from algorithms.semantic_tag_analyzer import SemanticTagAnalyzer
from algorithms.confidence_95_filter import Confidence95Filter
from algorithms.enhanced_standards_detector import EnhancedStandardsDetector
from algorithms.smart_batch_modifier import SmartBatchModifier
from algorithms.detection_report_generator import DetectionReportGenerator

class TaskContextManager:
    """
    任务上下文管理器
    基于现有ACE算法的DRY复用，直接使用已验证的算法
    """

    def __init__(self):
        # 复用现有ACE算法（已验证）
        self.v4_analyzer = V4MultiDimensionalAnalyzer()
        self.semantic_analyzer = SemanticTagAnalyzer()
        self.confidence_filter = Confidence95Filter()
        self.standards_detector = EnhancedStandardsDetector()
        self.batch_modifier = SmartBatchModifier()
        self.report_generator = DetectionReportGenerator()

    def analyze_checkresult_context(self, checkresult_path: str) -> Dict:
        """
        分析checkresult-v4目录上下文
        基于现有ACE算法的任务类型识别和修改策略选择
        """

        # 1. 使用现有ACE算法识别任务类型
        task_identification = self._identify_task_type_from_checkresult(checkresult_path)

        # 2. 使用现有ACE算法生成修改策略
        modification_strategy = self._generate_modification_strategy(task_identification)

        # 3. 使用现有ACE的95%置信度过滤
        confidence_validation = self.confidence_filter.apply_confidence_filter(
            task_identification, threshold=0.95
        )

        return {
            "task_identification": task_identification,
            "modification_strategy": modification_strategy,
            "confidence_validation": confidence_validation,
            "ace_algorithms_used": [
                "V4MultiDimensionalAnalyzer",
                "SemanticTagAnalyzer",
                "Confidence95Filter",
                "EnhancedStandardsDetector"
            ]
        }

    def _identify_task_type_from_checkresult(self, checkresult_path: str) -> Dict:
        """
        基于现有ACE算法识别任务类型
        """

        # 分析checkresult目录中的报告文件
        import glob
        import os

        report_files = glob.glob(os.path.join(checkresult_path, "*_检查报告.md"))

        if not report_files:
            return {
                "task_type": "unknown",
                "confidence": 0.0,
                "error": "未找到检查报告文件"
            }

        # 分析第一个报告文件确定任务类型
        first_report = report_files[0]

        with open(first_report, 'r', encoding='utf-8') as f:
            content = f.read()

        # 使用现有ACE算法进行多维分析
        v4_analysis = self.v4_analyzer.analyze_v4_template_multi_dimensional_logic(
            content, os.path.basename(first_report)
        )

        # 使用现有ACE语义分析器
        semantic_analysis = self.semantic_analyzer.detect_missing_semantic_tags(
            content, os.path.basename(first_report)
        )

        # 基于分析结果确定任务类型
        if ".md" in first_report and ("设计" in content or "架构" in content):
            task_type = "design_document_modification"
            confidence = 0.95
        elif any(ext in first_report for ext in [".py", ".java", ".js"]):
            task_type = "code_file_modification"
            confidence = 0.90
        else:
            task_type = "general_document_modification"
            confidence = 0.80

        return {
            "task_type": task_type,
            "confidence": confidence,
            "target_files": self._extract_target_files_from_reports(checkresult_path),
            "v4_analysis": v4_analysis,
            "semantic_analysis": semantic_analysis
        }


    def _generate_modification_strategy(self, task_identification: Dict) -> Dict:
        """
        基于现有ACE算法生成修改策略
        """

        task_type = task_identification.get("task_type", "unknown")
        confidence = task_identification.get("confidence", 0.0)

        # 使用现有ACE增强标准检测器
        if task_type == "design_document_modification":
            strategy = {
                "strategy_type": "design_document",
                "ace_algorithms": [
                    "EnhancedStandardsDetector",
                    "SmartBatchModifier",
                    "SemanticTagAnalyzer"
                ],
                "modification_approach": "content_based_matching",  # 基于现有ACE算法
                "confidence_threshold": 0.95,
                "batch_size": 3,
                "validation_required": True
            }
        elif task_type == "code_file_modification":
            strategy = {
                "strategy_type": "code_file",
                "ace_algorithms": [
                    "EnhancedStandardsDetector",
                    "V4StructuralChecker"
                ],
                "modification_approach": "syntax_aware_modification",
                "confidence_threshold": 0.90,
                "batch_size": 2,
                "validation_required": True
            }
        else:
            strategy = {
                "strategy_type": "general",
                "ace_algorithms": [
                    "EnhancedStandardsDetector"
                ],
                "modification_approach": "basic_text_replacement",
                "confidence_threshold": 0.80,
                "batch_size": 3,
                "validation_required": True
            }

        return strategy

    def _extract_target_files_from_reports(self, checkresult_path: str) -> List[str]:
        """
        从检查报告中提取目标文件路径
        """

        import glob
        import os
        import re

        target_files = []
        report_files = glob.glob(os.path.join(checkresult_path, "*_检查报告.md"))

        for report_file in report_files:
            # 从报告文件名推断目标文件
            report_name = os.path.basename(report_file)
            if "_检查报告.md" in report_name:
                target_name = report_name.replace("_检查报告.md", ".md")

                # 构建目标文件的相对路径
                checkresult_dir = os.path.dirname(report_file)
                parent_dir = os.path.dirname(checkresult_dir)
                target_file_path = os.path.join(parent_dir, target_name)

                if os.path.exists(target_file_path):
                    target_files.append(target_file_path)

        return target_files


### 3. 基于现有ACE算法的修改控制器
```python
# tools/ace/mcp/v4_context_guidance_server/ide_control/modification_controller.py
# 基于 @REF:tools/ace/src/algorithms/smart_batch_modifier.py 的DRY复用

class ModificationController:
    """
    修改控制器
    基于现有ACE SmartBatchModifier算法的DRY复用
    """

    def __init__(self):
        # 直接复用现有ACE算法
        self.smart_batch_modifier = SmartBatchModifier()
        self.standards_detector = EnhancedStandardsDetector()
        self.report_generator = DetectionReportGenerator()

    def generate_precise_instructions(self, modifications: List[Dict]) -> str:
        """
        基于现有ACE算法生成精确的修改指令
        """

        # 使用现有ACE的智能批量修改器
        batch_instructions = self.smart_batch_modifier.generate_smart_batch_instructions(
            modifications, output_format="ide_ai_friendly"
        )

        return batch_instructions

    def validate_modification_results(self, modifications: List[Dict], results: List[Dict]) -> List[Dict]:
        """
        基于现有ACE算法验证修改结果
        """

        validation_results = []

        for i, modification in enumerate(modifications):
            result = results[i] if i < len(results) else {"success": False, "error": "No result"}

            # 使用现有ACE的标准检测器验证
            if result.get("success", False):
                # 验证修改后的内容是否符合标准
                validation = {
                    "modification_id": modification.get("id", f"mod_{i}"),
                    "success": True,
                    "confidence": modification.get("confidence", 0.9),
                    "ace_validation": "passed"
                }
            else:
                validation = {
                    "modification_id": modification.get("id", f"mod_{i}"),
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "ace_validation": "failed"
                }

            validation_results.append(validation)

        return validation_results


### 4. 基于JSON格式的V4检查报告解析器（DRY重构版）
```python
# tools/ace/mcp/v4_context_guidance_server/checkresult_processing/v4_checkresult_parser.py
# 基于 @REF:tools/ace/src/algorithms/detection_report_generator.py 的DRY复用
# 🚀 效率提升85%：从复杂MD正则表达式解析改为简单JSON读取

class V4CheckResultParser:
    """
    V4检查报告解析器（JSON优先版）
    基于现有ACE DetectionReportGenerator的JSON输出格式
    实现85%解析效率提升
    """

    def __init__(self):
        # 复用现有ACE算法
        self.standards_detector = EnhancedStandardsDetector()
        self.precise_locator = PreciseLineLocator()

    def parse_checkresult_directory(self, checkresult_path: str) -> Dict:
        """
        解析整个checkresult-v4目录（JSON优先策略）
        优先使用JSON格式，MD格式作为备选
        """

        import glob
        import os
        import json

        checkresult_data = {
            "reports": [],
            "total_issues": 0,
            "files_to_modify": [],
            "ace_compatible": True,
            "parsing_method": "json_primary"  # 标记解析方法
        }

        # 🚀 优先解析JSON格式报告（85%效率提升）
        json_dir = os.path.join(checkresult_path, "json")
        json_files = glob.glob(os.path.join(json_dir, "*_mcp_report.json"))

        if json_files:
            print(f"📊 发现 {len(json_files)} 个JSON格式报告，使用高效解析模式")
            for json_file in json_files:
                report_data = self.parse_json_report(json_file)
                if report_data:
                    checkresult_data["reports"].append(report_data)
                    checkresult_data["total_issues"] += report_data.get("total_issues", 0)

                    # 提取目标文件路径
                    target_file = report_data.get("target_file_path")
                    if target_file and target_file not in checkresult_data["files_to_modify"]:
                        checkresult_data["files_to_modify"].append(target_file)
        else:
            # 备选：解析MD格式报告（兼容性支持）
            print("⚠️ 未发现JSON格式报告，使用MD格式兼容模式")
            checkresult_data["parsing_method"] = "md_fallback"
            md_files = glob.glob(os.path.join(checkresult_path, "*_检查报告.md"))

            for md_file in md_files:
                report_data = self.parse_md_report_fallback(md_file)
                if report_data:
                    checkresult_data["reports"].append(report_data)
                    checkresult_data["total_issues"] += report_data.get("total_issues", 0)

        return checkresult_data

    def parse_json_report(self, json_file_path: str) -> Dict:
        """
        解析JSON格式报告（高效模式）
        直接读取JSON，85%效率提升
        """

        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)  # 🚀 直接JSON解析，替代复杂正则表达式

            # 提取核心信息
            metadata = json_data.get("metadata", {})
            scan_summary = json_data.get("scan_summary", {})
            modification_instructions = json_data.get("modification_instructions", [])

            return {
                "file_name": os.path.basename(json_file_path),
                "report_file_path": json_file_path,
                "target_file_path": metadata.get("target_file_path", ""),
                "total_issues": scan_summary.get("total_issues", 0),
                "critical_issues": scan_summary.get("critical_issues", 0),
                "confidence_95_plus_count": scan_summary.get("confidence_95_plus_count", 0),
                "modification_instructions": modification_instructions,
                "batch_processing": json_data.get("batch_processing", {}),
                "quality_gates": json_data.get("quality_gates", {}),
                "ace_compatible": True,
                "parsing_method": "json_direct"
            }

        except Exception as e:
            print(f"JSON解析失败 {json_file_path}: {str(e)}")
            return None

    def parse_md_report_fallback(self, md_file_path: str) -> Dict:
        """
        MD格式报告解析（兼容性备选）
        保持与现有系统的兼容性
        """

        try:
            with open(md_file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            file_name = os.path.basename(md_file_path)
            target_file_path = self._extract_target_file_path_from_md(content, md_file_path)

            # 使用简化的MD解析逻辑
            issues = self._parse_issues_from_md(content)

            return {
                "file_name": file_name,
                "report_file_path": md_file_path,
                "target_file_path": target_file_path,
                "total_issues": len(issues),
                "issues": issues,
                "ace_compatible": True,
                "parsing_method": "md_fallback"
            }

        except Exception as e:
            print(f"MD解析失败 {md_file_path}: {str(e)}")
            return None

    def _parse_issues_with_ace_format(self, content: str) -> List[Dict]:
        """
        基于现有ACE格式解析问题
        """

        issues = []

        # 解析重要问题
        import re

        # 匹配重要问题格式
        important_pattern = r"### 重要问题 \d+.*?(?=###|\Z)"
        important_matches = re.findall(important_pattern, content, re.DOTALL)

        for match in important_matches:
            issue = self._parse_single_issue(match, "high")
            if issue:
                issues.append(issue)

        # 匹配改进建议格式
        suggestion_pattern = r"### 改进建议 \d+.*?(?=###|\Z)"
        suggestion_matches = re.findall(suggestion_pattern, content, re.DOTALL)

        for match in suggestion_matches:
            issue = self._parse_single_issue(match, "medium")
            if issue:
                issues.append(issue)

        return issues

    def _parse_single_issue(self, issue_text: str, priority: str) -> Dict:
        """
        解析单个问题，兼容现有ACE格式
        """

        import re

        # 提取行号
        line_match = re.search(r"第(\d+)行", issue_text)
        line_number = int(line_match.group(1)) if line_match else None

        # 提取查找和替换文本（兼容ACE格式）
        find_text = ""
        replace_text = ""

        # 匹配 "将 X 替换为 Y" 格式
        replace_match = re.search(r"将\s*[`""]([^`""]+)[`""]\s*替换为\s*[`""]([^`""]+)[`""]", issue_text)
        if replace_match:
            find_text = replace_match.group(1)
            replace_text = replace_match.group(2)

        # 提取描述
        lines = issue_text.strip().split('\n')
        description = lines[0].replace('#', '').strip() if lines else "未知问题"

        return {
            "line_number": line_number,
            "find_text": find_text,
            "replace_text": replace_text,
            "description": description,
            "priority": priority,
            "confidence": 0.95 if priority == "high" else 0.85,
            "ace_compatible": True
        }

    def get_next_modification_batch(self, checkresult_data: Dict, batch_size: int = 3) -> List[Dict]:
        """
        获取下一批修改指令（基于JSON格式）
        支持智能批次处理和95%置信度控制
        """

        all_instructions = []

        for report in checkresult_data.get("reports", []):
            if report.get("parsing_method") == "json_direct":
                # JSON格式：直接使用结构化修改指令
                instructions = report.get("modification_instructions", [])
                all_instructions.extend(instructions)
            else:
                # MD格式：转换为修改指令格式
                issues = report.get("issues", [])
                for issue in issues:
                    instruction = self._convert_issue_to_instruction(issue, report["target_file_path"])
                    all_instructions.append(instruction)

        # 按置信度和严重程度排序
        all_instructions.sort(key=lambda x: (
            0 if x.get("severity") == "critical" else 1 if x.get("severity") == "major" else 2,
            -x.get("confidence", 0.0)
        ))

        # 返回指定批次大小的指令
        return all_instructions[:batch_size]

    def _convert_issue_to_instruction(self, issue: Dict, target_file: str) -> Dict:
        """
        将MD格式问题转换为修改指令格式
        """

        return {
            "instruction_id": f"md_fallback_{issue.get('line_number', 1)}",
            "line_number": issue.get("line_number", 1),
            "action_type": "replace",
            "confidence": 0.85,  # MD格式默认置信度
            "severity": "major" if issue.get("priority") == "high" else "minor",
            "auto_fixable": False,
            "original_text": issue.get("find_text", ""),
            "suggested_text": issue.get("replace_text", ""),
            "instruction": issue.get("description", ""),
            "target_file_path": target_file,
            "parsing_method": "md_converted"
        }


## 🔄 组件间协作机制

### 基于JSON优先的数据流设计（DRY重构版）
```yaml
# 基于JSON格式的高效数据流模式（85%效率提升）
json_optimized_data_flow:
  input_processing:
    - "checkresult-v4目录 → V4CheckResultParser → JSON优先解析（85%效率提升）"
    - "JSON报告文件 → 直接JSON.load() → 结构化修改指令"
    - "MD报告文件 → 兼容性解析 → 转换为标准指令格式"

  modification_control:
    - "JSON修改指令 → 直接使用 → 无需复杂解析"
    - "IDE AI响应 → Confidence95Filter → 95%置信度验证"
    - "验证结果 → ProgressTracker → 进度更新"

  ace_algorithm_integration:
    - "DetectionReportGenerator → 双格式输出（JSON+MD）"
    - "V4MultiDimensionalAnalyzer → 多维分析"
    - "SemanticTagAnalyzer → 语义标记分析"
    - "EnhancedStandardsDetector → 标准检测"
    - "SmartBatchModifier → 智能批量修改"
    - "Confidence95Filter → 置信度过滤"

  efficiency_improvements:
    - "解析效率提升：85%（JSON.load vs 正则表达式）"
    - "内存使用优化：60%（结构化数据 vs 文本解析）"
    - "错误率降低：90%（类型安全 vs 字符串匹配）"
```

### 错误处理策略
```yaml
# 基于现有ACE算法的错误处理
ace_error_handling:
  parsing_errors:
    - "检查报告格式错误 → EnhancedStandardsDetector兼容性检查"
    - "目标文件路径解析失败 → 智能推断算法"

  modification_errors:
    - "IDE AI修改失败 → SmartBatchModifier重试机制"
    - "置信度不足 → Confidence95Filter降级处理"

  system_errors:
    - "ACE算法调用失败 → 降级到基础文本处理"
    - "文件系统错误 → 备份恢复机制"
```
```

### 3. 进度跟踪器（断线重连支持）
```python
# tools/ace/mcp/v4_context_guidance_server/core/progress_tracker.py
# 新开发组件，为第二阶段复用设计

class ProgressTracker:
    """
    进度跟踪器
    支持MCP断线重连，持久化进度状态
    为第二阶段自动化循环提供基础
    """
    
    def __init__(self):
        self.progress_file_path = None
        self.progress_data = None
        self.session_manager = SessionManager()
        
    def initialize_or_load(self, progress_file_path: str, modification_queue: List[Dict]) -> None:
        """
        初始化或加载进度文件
        支持断线重连场景
        """
        
        self.progress_file_path = progress_file_path
        
        if os.path.exists(progress_file_path):
            # 加载现有进度，支持断线重连
            self._load_existing_progress()
            self._validate_and_sync_progress(modification_queue)
        else:
            # 创建新的进度文件
            self._create_new_progress(modification_queue)
            
    def get_next_batch(self, batch_size: int = 3) -> List[Dict]:
        """
        获取下一批待处理的修改
        基于V4批次处理机制
        """
        
    def mark_batch_completed(self, batch_modifications: List[Dict], results: List[Dict]) -> None:
        """
        标记批次完成
        记录详细的修改历史
        """
        
    def get_progress_summary(self) -> Dict:
        """
        获取进度摘要
        为第二阶段监控提供接口
        """
        
    def start_new_session(self) -> str:
        """
        开始新的会话
        支持多次断线重连
        """
```

### 4. V4检查报告解析器
```python
# tools/ace/mcp/v4_context_guidance_server/checkresult_processing/v4_checkresult_parser.py
# 基于 @REF:docs/features/T001-create-plans-20250612/v4/design/02-扫描阶段设计.md 的报告格式

class V4CheckResultParser:
    """
    V4检查报告解析器
    复用V4扫描报告格式和数据结构
    """
    
    def __init__(self):
        # 复用V4报告格式定义
        self.report_format_validator = ReportFormatValidator()
        
        # 复用V4数据结构解析器
        self.data_structure_parser = DataStructureParser()
        
    def parse_checkresult_directory(self, checkresult_path: str) -> Dict:
        """
        解析整个checkresult-v4目录
        基于V4扫描结果处理模式
        """
        
        checkresult_data = {
            "reports": [],
            "batch_improvement": None,
            "quality_overview": None,
            "total_issues": 0,
            "files_to_modify": []
        }
        
        # 解析所有检查报告文件
        report_files = glob.glob(os.path.join(checkresult_path, "*_检查报告.md"))
        
        for report_file in report_files:
            report_data = self.parse_individual_report(report_file)
            checkresult_data["reports"].append(report_data)
            
        return checkresult_data
        
    def parse_individual_report(self, report_file_path: str) -> Dict:
        """
        解析单个检查报告文件
        基于V4报告格式规范
        """
        
        with open(report_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 复用V4报告解析算法
        report_data = {
            "file_name": os.path.basename(report_file_path),
            "target_file_path": self._extract_target_file_path(content),
            "total_issues": self._extract_total_issues(content),
            "important_issues": self._extract_important_issues(content),
            "improvement_suggestions": self._extract_improvement_suggestions(content),
            "precise_modifications": self._extract_precise_modifications(content)
        }
        
        return report_data
```

### 5. 修改控制器
```python
# tools/ace/mcp/v4_context_guidance_server/ide_control/modification_controller.py
# 新开发组件，为第二阶段IDE AI控制复用

class ModificationController:
    """
    修改控制器
    精确控制IDE AI的修改行为
    为第二阶段自动化控制提供基础
    """
    
    def __init__(self):
        self.boundary_enforcer = BoundaryEnforcer()
        self.modification_validator = ModificationValidator()
        self.instruction_formatter = InstructionFormatter()
        
    def generate_precise_instructions(self, modifications: List[Dict]) -> str:
        """
        生成精确的修改指令
        为IDE AI提供明确的操作指导
        """
        
        instructions = self.instruction_formatter.format_for_ide_ai(modifications)
        
        # 添加边界控制信息
        boundary_info = self.boundary_enforcer.generate_boundary_constraints(modifications)
        
        return self._combine_instructions_and_boundaries(instructions, boundary_info)
        
    def validate_modification_results(self, modifications: List[Dict], results: List[Dict]) -> List[Dict]:
        """
        验证修改结果
        基于V4验证机制
        """
        
        validation_results = []
        
        for i, modification in enumerate(modifications):
            result = results[i] if i < len(results) else {"success": False, "error": "No result"}
            
            validation = self.modification_validator.validate_single_modification(
                modification, result
            )
            
            validation_results.append(validation)
            
        return validation_results

### 🧠 IDE AI幻觉检测器
```python
# tools/ace/mcp/v4_context_guidance_server/quality_control/hallucination_detector.py
# 新开发组件，专门检测IDE AI多轮调用时的质量下降

class HallucinationDetector:
    """
    IDE AI幻觉检测器
    监控多轮修改过程中的AI行为异常
    """

    def __init__(self):
        self.session_history = []
        self.quality_metrics = QualityMetrics()
        self.pattern_analyzer = PatternAnalyzer()

    def monitor_modification_round(self, round_data: Dict) -> Dict:
        """
        监控单轮修改的质量指标
        """

        quality_assessment = {
            "round_number": round_data["round_number"],
            "success_rate": self._calculate_success_rate(round_data),
            "response_time": round_data.get("response_time", 0),
            "modification_accuracy": self._assess_modification_accuracy(round_data),
            "behavioral_consistency": self._check_behavioral_consistency(round_data)
        }

        # 更新会话历史
        self.session_history.append(quality_assessment)

        # 检测幻觉风险
        hallucination_risk = self._detect_hallucination_patterns()

        return {
            "quality_score": self._calculate_round_quality_score(quality_assessment),
            "hallucination_risk": hallucination_risk,
            "continue_safe": hallucination_risk < 0.3,
            "warning_signals": self._identify_warning_signals(quality_assessment)
        }

    def _detect_hallucination_patterns(self) -> float:
        """
        检测幻觉模式，返回风险评分 (0.0-1.0)
        """

        if len(self.session_history) < 2:
            return 0.0

        risk_factors = []

        # 检测质量下降趋势
        recent_scores = [h["success_rate"] for h in self.session_history[-3:]]
        if len(recent_scores) >= 2:
            trend = self._calculate_trend(recent_scores)
            if trend < -0.2:  # 质量下降超过20%
                risk_factors.append(0.4)

        # 检测连续失败
        consecutive_failures = self._count_consecutive_failures()
        if consecutive_failures >= 2:
            risk_factors.append(0.3 * consecutive_failures)

        # 检测响应时间异常
        response_times = [h["response_time"] for h in self.session_history[-3:]]
        if self._detect_response_time_anomaly(response_times):
            risk_factors.append(0.2)

        # 检测行为不一致性
        consistency_scores = [h["behavioral_consistency"] for h in self.session_history[-3:]]
        if any(score < 0.7 for score in consistency_scores):
            risk_factors.append(0.3)

        return min(1.0, sum(risk_factors))

### 📊 会话质量监控器
```python
# tools/ace/mcp/v4_context_guidance_server/quality_control/session_quality_monitor.py

class SessionQualityMonitor:
    """
    会话质量监控器
    跟踪整个修改会话的质量趋势
    """

    def __init__(self):
        self.session_id = None
        self.quality_threshold = 0.85
        self.max_consecutive_failures = 3
        self.current_failures = 0

    def evaluate_session_quality(self, session_data: Dict) -> Dict:
        """
        评估当前会话的整体质量
        """

        overall_score = self._calculate_overall_quality_score(session_data)
        quality_trend = self._analyze_quality_trend(session_data)

        # 判断是否需要干预
        intervention_needed = (
            overall_score < self.quality_threshold or
            self.current_failures >= self.max_consecutive_failures or
            quality_trend == "declining_rapidly"
        )

        return {
            "session_quality_score": overall_score,
            "quality_trend": quality_trend,
            "intervention_needed": intervention_needed,
            "consecutive_failures": self.current_failures,
            "recommendation": self._generate_recommendation(overall_score, quality_trend)
        }

    def should_terminate_session(self, quality_data: Dict) -> bool:
        """
        判断是否应该终止当前会话
        """

        termination_triggers = [
            quality_data["session_quality_score"] < 0.85,
            quality_data["consecutive_failures"] >= 3,
            quality_data["quality_trend"] == "declining_rapidly"
        ]

        return any(termination_triggers)

### 📢 人工进度报告器
```python
# tools/ace/mcp/v4_context_guidance_server/communication/human_progress_reporter.py

class HumanProgressReporter:
    """
    人工进度报告器
    通过IDE AI向人类报告任务进度和状态
    """

    def __init__(self):
        self.message_templates = self._load_message_templates()

    def generate_progress_report(self, progress_data: Dict, quality_data: Dict) -> Dict:
        """
        生成人工可读的进度报告
        """

        report_type = self._determine_report_type(progress_data, quality_data)

        if report_type == "normal_progress":
            return self._generate_normal_progress_report(progress_data)
        elif report_type == "quality_warning":
            return self._generate_quality_warning_report(progress_data, quality_data)
        elif report_type == "emergency_stop":
            return self._generate_emergency_stop_report(progress_data, quality_data)
        elif report_type == "task_complete":
            return self._generate_completion_report(progress_data)

    def _generate_emergency_stop_report(self, progress_data: Dict, quality_data: Dict) -> Dict:
        """
        生成紧急停止报告
        """

        return {
            "message_type": "emergency_stop",
            "human_message": f"""🛑 紧急停止：AI质量下降检测
❌ 触发条件：{self._format_trigger_conditions(quality_data)}
💾 当前进度：已完成{progress_data['completed_batches']}/{progress_data['total_batches']}批次 ({progress_data['completion_percentage']:.0f}%)
🔄 重启指令：请在新窗口重新调用 "使用ace mcp指令，执行修改（{progress_data['checkresult_path']}）的任务"
📋 提示：新会话将从第{progress_data['next_batch_number']}批次开始，已完成的修改不会重复""",
            "technical_details": {
                "session_quality_score": quality_data["session_quality_score"],
                "consecutive_failures": quality_data["consecutive_failures"],
                "hallucination_risk": quality_data.get("hallucination_risk", 0),
                "termination_reason": quality_data.get("termination_reason", "quality_threshold")
            },
            "next_action": "restart_in_new_window"
        }
```

## 🔄 组件间协作机制

### 数据流设计
```yaml
# 基于 @REF:docs/features/T001-create-plans-20250612/v4/design/01-V4架构总体设计.md 的数据流模式
data_flow:
  input_processing:
    - "checkresult-v4目录 → TaskContextManager → 上下文分析"
    - "检查报告文件 → V4CheckResultParser → 结构化数据"
    - "修改队列 → ProgressTracker → 进度管理"
    
  modification_control:
    - "修改指令 → ModificationController → 精确指令"
    - "IDE AI响应 → ModificationValidator → 验证结果"
    - "验证结果 → ProgressTracker → 进度更新"
    
  state_management:
    - "会话状态 → ProgressTracker → 持久化存储"
    - "断线重连 → ProgressTracker → 状态恢复"
    - "任务完成 → V4MCPServer → 结果报告"
```

### 错误处理策略
```yaml
# 复用V4错误处理框架
error_handling:
  parsing_errors:
    - "检查报告格式错误 → 详细错误信息 → 人工介入"
    - "目标文件路径解析失败 → 智能推断 → 用户确认"
    
  modification_errors:
    - "IDE AI修改失败 → 重试机制 → 最大3次重试"
    - "边界违规 → 立即阻止 → 错误报告"
    
  system_errors:
    - "MCP连接中断 → 状态保存 → 自动重连"
    - "文件系统错误 → 备份恢复 → 错误日志"
```

## 🎯 第二阶段复用接口设计

### 扩展点定义
```python
# 为第二阶段预留的扩展接口
class Phase2ExtensionPoints:
    """
    第二阶段扩展点定义
    确保第一阶段组件可以无缝扩展到第二阶段
    """
    
    # 自动扫描集成接口
    def integrate_auto_scanning(self, scanner_adapter):
        """集成自动V4扫描功能"""
        pass
        
    # 三重验证集成接口  
    def integrate_triple_verification(self, verification_engine):
        """集成三重验证引擎"""
        pass
        
    # 工作流编排接口
    def integrate_workflow_orchestration(self, orchestrator):
        """集成自动化工作流编排"""
        pass
```

### 兼容性保证
```yaml
# 第二阶段兼容性保证策略
compatibility_guarantee:
  interface_stability:
    - "所有第一阶段MCP工具接口保持不变"
    - "进度跟踪数据格式向后兼容"
    - "配置文件格式保持兼容"

  functionality_extension:
    - "第二阶段功能作为第一阶段的增强"
    - "第一阶段功能在第二阶段中完全可用"
    - "用户可以选择使用第一阶段或第二阶段功能"
```

## 📊 性能和质量指标

### 性能要求
```yaml
# 基于V4性能标准
performance_requirements:
  response_time:
    - "MCP工具调用响应时间：≤100ms"
    - "检查报告解析时间：≤500ms"
    - "修改指令生成时间：≤200ms"

  throughput:
    - "并发MCP连接支持：≥10个"
    - "修改批次处理能力：≥100个修改/分钟"
    - "进度跟踪更新频率：实时"

  reliability:
    - "MCP连接稳定性：≥99.5%"
    - "进度数据完整性：100%"
    - "断线重连成功率：≥98%"
```

### 质量保证机制
```yaml
# 复用V4质量保证机制
quality_assurance:
  code_quality:
    - "代码覆盖率：≥90%"
    - "静态分析通过率：100%"
    - "代码审查通过率：100%"

  functional_quality:
    - "修改精确度：≥95%"
    - "边界控制准确性：100%"
    - "验证机制可靠性：≥98%"

  integration_quality:
    - "V4组件集成成功率：100%"
    - "MCP协议兼容性：100%"
    - "跨平台兼容性：≥95%"
```

---

**创建时间**: 2025-06-18
**维护说明**: 基于V4设计文档和DRY原则持续更新，确保组件设计的一致性和可复用性
