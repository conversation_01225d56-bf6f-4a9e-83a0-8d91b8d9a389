# XKongCloud Commons Cache V1: 现代、混合式缓存架构设计

## 文档元数据

- **文档ID**: `F007-CACHE-ARCHITECTURE-DESIGN-001`
- **版本**: `V1.0`
- **创建日期**: `2025-06-12`
- **状态**: `设计稿`
- **技术栈**: Java 21, Spring Boot 3.4.5, Valkey 8, Caffeine 3.1
- **复杂度等级**: L2

## 核心定位

`xkongcloud-commons-cache` 是一个**易用性与灵活性兼备**的企业级缓存基础库。它旨在为分布式应用提供高性能、自动管理的双层缓存解决方案，通过现代技术栈的深度集成实现性能倍增效应。

- **为80%的场景提供极致易用性**：通过简单的注解即可启用高性能、自动管理的双层缓存（L1本地 + L2远程）。
- **为20%的复杂场景提供完全控制**：提供强大的`CacheTemplate`作为"逃生舱口"，允许开发者进行精细化的缓存操作和性能调优。

## 设计哲学

我们遵循 "batteries-included, with escape hatches" 的设计哲学，借鉴`commons-db`的成功经验：

1.  **分层与解耦**: 严格分离API、核心逻辑、具体实现和自动配置。
2.  **接口驱动**: 所有交互都通过统一的接口和注解进行，实现高度抽象。
3.  **组合优化**: 深度融合现代技术特性（Valkey 8, 虚拟线程, 异步I/O），追求性能倍增效应。
4.  **无缝集成**: 提供开箱即用的Spring Boot Starter和深度可观测性。

## 包含范围

**核心功能模块**：
- 双层缓存架构（L1本地缓存 + L2远程缓存）
- 注解驱动的缓存管理（@XkCacheable、@XkCacheEvict、@XkCachePut）
- Spring Boot 自动配置和Starter
- Micrometer指标集成和可观测性
- Valkey 8高级特性支持（客户端缓存、管道化、高级数据结构）
- 异步I/O和虚拟线程优化

**技术栈支持**：
- Java 21+ 运行时环境
- Spring Boot 3.4.5+ 框架集成
- Valkey 8+ 远程缓存引擎
- Caffeine 本地缓存引擎
- Lettuce 异步客户端

## 排除范围

**不包含功能**：
- Redis集群管理和部署
- 缓存数据的持久化存储
- 分布式锁机制
- 消息队列功能
- 数据库事务管理
- 缓存预热的自动化工具

**不支持技术栈**：
- Spring Boot 2.x及以下版本
- Java 17以下版本
- Redis（仅支持Valkey）
- 同步阻塞I/O客户端

## 实施约束

### 强制性技术要求
- **Java版本**: 必须使用Java 21+，低于此版本将导致虚拟线程特性不可用
- **Spring Boot版本**: 必须使用Spring Boot 3.4.5+，确保Observation API的完整支持
- **Valkey版本**: 必须使用Valkey 8+，确保客户端缓存特性可用
- **构建工具**: 推荐Maven 3.9+或Gradle 8.0+

### 性能指标要求
- **缓存命中率**: L1+L2综合命中率≥85%
- **缓存响应时间**: 本地缓存<1ms，远程缓存<10ms
- **内存使用**: L1缓存内存占用≤JVM堆内存的5%
- **网络开销**: 批量操作使用管道化，减少50%以上网络往返

### 兼容性要求
- **向后兼容**: 在同一主版本内保证API向后兼容
- **运行环境**: 支持Docker容器化部署
- **监控集成**: 与Micrometer、Prometheus、OpenTelemetry兼容

### 约束违规后果
- **版本不兼容**: 编译失败，运行时异常
- **性能不达标**: 触发监控告警，影响系统稳定性
- **配置错误**: 自动配置失败，组件无法启动

### 验证锚点
- **编译验证**: `mvn clean compile -P java21`
- **集成测试**: `mvn test -Dtest=CacheIntegrationTest`
- **性能测试**: `mvn test -Dtest=CachePerformanceTest`
- **兼容性验证**: `mvn verify -P compatibility-check`

## 2. 详细设计文档链接

- **[01-架构总览与设计哲学](./01-cache-architecture-and-design.md)** (本文档)
- **[02-核心抽象层设计](./02-核心抽象层设计.md)**: 定义缓存库的接口、注解和异常体系。
- **[03-实现层设计](./03-实现层设计.md)**: 详细描述Valkey和Caffeine的具体实现。
- **[04-双层缓存与AOP设计](./04-双层缓存与AOP设计.md)**: 阐述注解驱动的核心逻辑和L1/L2联动机制。
- **[05-监控集成设计](./05-监控集成设计.md)**: 定义可观测性策略和关键性能指标。
- **[06-自动配置设计](./06-自动配置设计.md)**: 描述与Spring Boot的无缝集成方案。
- **[07-使用指南和最佳实践](./07-使用指南和最佳实践.md)**: 提供面向开发者的使用说明和最佳实践。

## 3. 整体架构设计

### 2.1 混合式架构模型

```mermaid
graph TD
    subgraph Application Layer
        A1["@XkCacheable (80%场景)"]
        A2["CacheTemplates (20%场景)"]
    end

    subgraph Commons Cache Core Layer
        B1[CacheAspect (AOP)]
        B2[DualLayerCacheManager<br>(L1+L2 自动协调)]
        B3[ValkeyCacheTemplate<br>(直接访问Valkey)]
        B4[LocalCacheTemplate<br>(独立使用)]
    end

    subgraph Implementation Layer
        C1[Caffeine]
        C2[Valkey (Lettuce)]
    end

    A1 --> B1
    A2 --> B3
    A2 --> B4
    B1 --> B2
    B2 -- 内部调用 --> B3
    B2 -- 内部调用 --> B4
    B3 --> C2
    B4 --> C1
```

### 2.2 模块结构

```
commons-cache/
├── commons-cache-api/         # 核心接口与注解 (@XkCacheable, CacheTemplate)
├── commons-cache-caffeine/    # Caffeine 本地缓存实现 (LocalCacheTemplate)
├── commons-cache-valkey/      # Valkey 远程缓存实现 (ValkeyCacheTemplate)
├── commons-cache-core/        # 核心逻辑 (AOP, DualLayerCacheManager)
└── commons-cache-starter/     # Spring Boot 自动配置
```

## 3. 现代技术特性组合优化

我们深度集成现代技术，实现性能和开发体验的飞跃。

### 3.1 Valkey 8 特性集成

- **客户端缓存 (Client-Side Caching)**: `DualLayerCacheManager`的核心机制。通过Valkey的服务端推送（Tracking）能力，当远程key变更时，能自动、实时地使本地L1（Caffeine）缓存失效，极大降低了缓存一致性管理的复杂度。
- **多路复用 (Pipelining)**: `ValkeyCacheTemplate`将提供`executePipelined`方法，允许在一次网络往返中执行多条命令，显著降低高频操作的延迟。
- **高级数据结构**: `ValkeyCacheTemplate`将直接暴露对`HyperLogLog`, `Streams`, `JSON`等高级数据结构的操作接口，赋能复杂业务场景。
- **多键操作**: 充分利用`MGET`/`MSET`等命令，优化批量操作性能。

### 3.2 Spring Boot 3.4 + Java 21 组合优化

- **组合1: 高性能I/O (虚拟线程)**
  - **技术组合**: `Lettuce (异步I/O) + Java 21 Virtual Threads + Spring Boot 3.4.5 虚拟线程自动配置`
  - **效果**: 所有缓存的网络I/O操作都在虚拟线程上执行，避免了传统线程池的瓶颈，能够以极低的资源开销应对海量并发请求。

- **组合2: 深度可观测性**
  - **技术组合**: `Micrometer + Spring Boot 3.4.5 Observation API`
  - **效果**: 自动收集L1/L2缓存的命中率、延迟、大小、驱逐次数等关键指标，并集成到分布式追踪系统中，使缓存性能问题无所遁形。

- **组合3: 现代Java语法**
  - **技术组合**: `Java 21 Records, Pattern Matching, String Templates`
  - **效果**: 使用`Record`类定义缓存DTO，简化代码；使用`Pattern Matching`处理复杂的缓存结果；使用`String Templates`构建动态缓存键，提升代码的可读性和健壮性。

## 4. 核心组件设计

### 4.1 API层 (`commons-cache-api`)

```java
// 核心注解
package org.xkong.cloud.commons.cache.api.annotation;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface XkCacheable {
    String cacheName();
    String key() default ""; // 支持SpEL表达式
    String ttl() default "5m"; // 支持Duration格式
    boolean useL1Cache() default true; // 是否启用本地缓存
}

// 核心模板接口
package org.xkong.cloud.commons.cache.api;

public interface CacheTemplate<K, V> {
    Optional<V> get(K key);
    void put(K key, V value, Duration ttl);
    void evict(K key);
}
```

### 4.2 核心逻辑层 (`commons-cache-core`)

- **`DualLayerCacheManager`**:
  - 持有`LocalCacheTemplate`和`ValkeyCacheTemplate`实例。
  - 封装双层缓存的读写逻辑：读操作先查L1，再查L2，最后回源；写操作同时写入L2和L1。
  - **关键**: 初始化时，启动对Valkey的订阅，监听key失效通知，以驱逐L1缓存。

- **`CacheAspect`**:
  - AOP切面，拦截`@XkCacheable`等注解。
  - 解析注解参数（如SpEL表达式的key）。
  - 调用`DualLayerCacheManager`执行缓存的读写和驱逐逻辑。

### 4.3 实现层 (`commons-cache-valkey`)

```java
// Valkey模板接口扩展
package org.xkong.cloud.commons.cache.valkey;

public interface ValkeyCacheTemplate extends CacheTemplate<String, Object> {
    // 直接暴露高级数据结构操作
    HyperLogLogOperations<String, String> hyperLogLog();
    JsonOperations<String> json();
    // ... 其他数据结构

    // 提供管道化操作
    List<Object> executePipelined(Consumer<ValkeyCommands<String, String>> commands);
}
```

## 5. 自动配置与使用

### 5.1 Starter与配置 (`commons-cache-starter`)

- **`XkCacheAutoConfiguration`**: 自动配置所有核心Bean (`CacheAspect`, `DualLayerCacheManager`, `ValkeyCacheTemplate`等)。
- **`CacheProperties`**: 提供类型安全的配置项。

```yaml
xkong:
  cache:
    enabled: true
    default-ttl: 10m
    l1:
      enabled: true
      max-size: 1000
      expire-after-write: 5m
    l2:
      valkey:
        mode: standalone # or cluster
        host: localhost
        port: 6379
```

### 5.2 使用示例

**简单场景 (推荐)**
```java
@Service
public class UserService {
    @XkCacheable(cacheName = "user", key = "#id", ttl = "1h")
    public User getUserById(Long id) {
        // ... a slow database call
    }
}
```

**复杂场景 (高级)**
```java
@Service
public class AnalyticsService {
    @Autowired
    private ValkeyCacheTemplate valkeyTemplate;

    public long getDailyActiveUsers() {
        return valkeyTemplate.hyperLogLog().size("dau:" + LocalDate.now());
    }
}
```

## 6. 演进与风险

- **未来演进**: 引入缓存DSL、支持更多缓存中间件（如Redis）、集成AI进行智能缓存预热和淘汰。
- **关键风险**: `DualLayerCacheManager`中L1/L2一致性管理的实现复杂度较高，需要进行充分的并发和异常场景测试。Valkey客户端缓存机制的稳定性是关键依赖。
