# 项目经理正式代码结构与集成方案 - project_manager_risk_system集成设计

## 📋 文档信息
- **文档版本**: V1.0
- **创建日期**: 2025-01-16
- **设计目标**: 明确project_manager_risk_system的正式代码结构，详细设计与ace现有组件的集成方案
- **关键集成**: api_management、task_level_validation_driven_executor、web_interface

## 🏗️ 正式代码目录结构

### **完整目录层级设计**

```
tools/ace/src/
├── project_manager_risk_system/                    # 新增：项目经理架构风险检测系统
│   ├── __init__.py                              # 模块初始化
│   ├── core/                                    # 核心业务逻辑层
│   │   ├── __init__.py
│   │   ├── project_manager_risk_detector.py             # 项目经理风险检测器
│   │   ├── architecture_analyzer.py             # 架构分析器
│   │   ├── intelligent_code_generator.py        # 智能代码生成器
│   │   ├── document_parser.py                   # 文档解析器
│   │   ├── document_contradiction_preprocessor.py # 文档矛盾预处理器
│   │   └── report_generator.py                  # 报告生成器
│   ├── algorithms/                              # 算法引擎层（从算法.py提取）
│   │   ├── __init__.py
│   │   ├── networkx_analyzer.py                 # NetworkX图分析引擎
│   │   ├── security_rule_engine.py              # 安全规则引擎
│   │   ├── contradiction_detector.py            # 矛盾检测器
│   │   ├── component_inferrer.py                # 组件推断器
│   │   └── simple_detector.py                   # 简化检测器（从检查.py保留）
│   ├── integration/                             # 集成适配层
│   │   ├── __init__.py
│   │   ├── api_manager_adapter.py               # API管理器适配器
│   │   ├── task_executor_adapter.py             # 任务执行器适配器
│   │   ├── web_interface_adapter.py             # Web界面适配器
│   │   └── config_adapter.py                    # 配置适配器
│   ├── web/                                     # Web接口层
│   │   ├── __init__.py
│   │   ├── blueprints/
│   │   │   ├── __init__.py
│   │   │   └── project_manager_bp.py          # 项目经理架构风险检测蓝图
│   │   ├── controllers/
│   │   │   ├── __init__.py
│   │   │   ├── project_manager_risk_controller.py     # 项目经理风险检测控制器
│   │   │   └── code_generation_controller.py    # 代码生成控制器
│   │   ├── websocket/
│   │   │   ├── __init__.py
│   │   │   └── project_manager_risk_ws.py             # 项目经理风险检测WebSocket处理器
│   │   └── templates/
│   │       └── project_manager_nine_grid.html      # 项目经理九宫格模板
│   ├── data/                                    # 数据层
│   │   ├── __init__.py
│   │   ├── models.py                            # 数据模型
│   │   ├── storage.py                           # 存储管理
│   │   └── cache.py                             # 缓存管理
│   ├── config/                                  # 配置层
│   │   ├── __init__.py
│   │   ├── risk_detection_config.py             # 风险检测配置
│   │   ├── code_generation_config.py            # 代码生成配置
│   │   └── templates/                           # 代码模板
│   │       ├── java_spring_boot/
│   │       ├── python_flask/
│   │       └── typescript_react/
│   └── tests/                                   # 测试层
│       ├── __init__.py
│       ├── unit/                                # 单元测试
│       ├── integration/                         # 集成测试
│       └── fixtures/                            # 测试数据
└── task_executors/                              # 移动现有执行器到专门目录
    ├── __init__.py
    └── task_level_validation_driven_executor.py # 现有执行器
```

## 🔗 核心集成设计

### **1. API管理器集成适配器**

**文件位置**: `tools/ace/src/project_manager_risk_system/integration/api_manager_adapter.py`

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API管理器集成适配器
负责project_manager_risk_system与ace现有API管理器的集成
"""

import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

# 导入ace现有API管理组件
from ...api_management.global_api_connection_pool import GlobalAPIConnectionPool
from ...api_management.core.task_based_ai_service_manager import TaskBasedAIServiceManager
from ...api_management.core.quality_assurance_guard import get_quality_assurance_guard

@dataclass
class AIServiceRequest:
    """AI服务请求数据结构"""
    task_type: str  # "risk_detection", "code_generation", "analysis"
    content: str
    context: Dict[str, Any]
    quality_requirements: Dict[str, float]
    project_name: str = "project_manager_risk_system"

@dataclass
class AIServiceResponse:
    """AI服务响应数据结构"""
    success: bool
    content: str
    quality_score: float
    model_used: str
    execution_time: float
    error_message: str = ""

class APIManagerAdapter:
    """API管理器适配器 - 为架构风险检测系统提供AI服务"""
    
    def __init__(self):
        # 获取全局API连接池
        self.global_pool = GlobalAPIConnectionPool()
        self.project_name = "project_manager_risk_system"
        
        # 获取项目专用的API连接
        self.api_connection = self.global_pool.get_api_connection_for_project(self.project_name)
        
        # 初始化AI服务管理器
        self.ai_service_manager = TaskBasedAIServiceManager()
        
        # 获取质量保证守卫
        self.quality_guard = get_quality_assurance_guard()
        
        print(f"✅ API管理器适配器初始化完成 - 项目: {self.project_name}")
    
    async def request_ai_service(self, request: AIServiceRequest) -> AIServiceResponse:
        """
        请求AI服务
        
        Args:
            request: AI服务请求
            
        Returns:
            AIServiceResponse: AI服务响应
        """
        try:
            # 记录API调用开始
            start_time = asyncio.get_event_loop().time()
            
            # 根据任务类型选择合适的AI模型和参数
            ai_config = self._get_ai_config_for_task(request.task_type)
            
            # 构建AI请求参数
            ai_request_params = {
                "prompt": request.content,
                "context": request.context,
                "quality_requirements": request.quality_requirements,
                "model_preferences": ai_config["model_preferences"],
                "max_tokens": ai_config["max_tokens"],
                "temperature": ai_config["temperature"]
            }
            
            # 调用AI服务管理器
            ai_response = await self.ai_service_manager.process_request(ai_request_params)
            
            # 质量检查
            quality_result = await self.quality_guard.assess_response_quality(
                ai_response, request.quality_requirements
            )
            
            # 记录API调用结果
            execution_time = asyncio.get_event_loop().time() - start_time
            self.global_pool.record_project_api_call(self.project_name, ai_response.get("success", False))
            
            return AIServiceResponse(
                success=ai_response.get("success", False),
                content=ai_response.get("content", ""),
                quality_score=quality_result.get("overall_score", 0.0),
                model_used=ai_response.get("model_used", "unknown"),
                execution_time=execution_time,
                error_message=ai_response.get("error", "")
            )
            
        except Exception as e:
            # 记录失败的API调用
            self.global_pool.record_project_api_call(self.project_name, False)
            
            return AIServiceResponse(
                success=False,
                content="",
                quality_score=0.0,
                model_used="",
                execution_time=0.0,
                error_message=str(e)
            )
    
    def _get_ai_config_for_task(self, task_type: str) -> Dict[str, Any]:
        """根据任务类型获取AI配置"""
        configs = {
            "risk_detection": {
                "model_preferences": ["deepseek_v3", "claude_sonnet"],
                "max_tokens": 4000,
                "temperature": 0.1  # 低温度，确保准确性
            },
            "code_generation": {
                "model_preferences": ["deepseek_coder", "claude_sonnet"],
                "max_tokens": 8000,
                "temperature": 0.2  # 稍高温度，增加创造性
            },
            "analysis": {
                "model_preferences": ["claude_sonnet", "deepseek_v3"],
                "max_tokens": 6000,
                "temperature": 0.15  # 中等温度，平衡准确性和洞察力
            }
        }
        
        return configs.get(task_type, configs["analysis"])
    
    def get_api_statistics(self) -> Dict[str, Any]:
        """获取API使用统计"""
        return {
            "project_stats": self.api_connection["project_stats"],
            "global_stats": self.global_pool.connection_stats
        }
```

### **2. 任务执行器集成适配器**

**文件位置**: `tools/ace/src/architecture_risk_system/integration/task_executor_adapter.py`

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务执行器集成适配器
负责architecture_risk_system与task_level_validation_driven_executor的集成
"""

import asyncio
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from enum import Enum

# 导入现有任务执行器
from ...task_executors.task_level_validation_driven_executor import (
    ValidationDrivenExecutor,
    ExecutionResult,
    ValidationResult,
    PyCRUDOperation,
    PyCRUDOperationGroups
)

class ArchitectureTaskType(Enum):
    """架构任务类型枚举"""
    RISK_DETECTION = "risk_detection"
    CODE_GENERATION = "code_generation"
    DOCUMENT_ANALYSIS = "document_analysis"
    ARCHITECTURE_VALIDATION = "architecture_validation"

@dataclass
class ArchitectureTaskRequest:
    """架构任务请求"""
    task_type: ArchitectureTaskType
    input_data: Dict[str, Any]
    validation_requirements: Dict[str, Any]
    execution_mode: str = "generate_and_execute"  # "generate_only" or "generate_and_execute"
    max_iterations: int = 3
    confidence_threshold: float = 0.85

@dataclass
class ArchitectureTaskResult:
    """架构任务结果"""
    success: bool
    task_type: ArchitectureTaskType
    generated_content: str
    execution_result: Any
    validation_result: ValidationResult
    confidence: float
    execution_time: float
    iterations_used: int
    error_message: str = ""

class TaskExecutorAdapter:
    """任务执行器适配器"""
    
    def __init__(self, api_adapter):
        """
        初始化任务执行器适配器
        
        Args:
            api_adapter: API管理器适配器实例
        """
        self.api_adapter = api_adapter
        self.task_executor = ValidationDrivenExecutor()
        
        # 注册架构任务的验证器
        self._register_architecture_validators()
        
        print("✅ 任务执行器适配器初始化完成")
    
    async def execute_architecture_task(self, request: ArchitectureTaskRequest) -> ArchitectureTaskResult:
        """
        执行架构任务
        
        Args:
            request: 架构任务请求
            
        Returns:
            ArchitectureTaskResult: 架构任务结果
        """
        try:
            start_time = asyncio.get_event_loop().time()
            
            # 根据任务类型选择合适的PyCRUD操作
            operations = self._get_operations_for_task(request.task_type)
            
            # 构建任务执行参数
            execution_params = {
                "operations": operations,
                "input_data": request.input_data,
                "validation_requirements": request.validation_requirements,
                "execution_mode": request.execution_mode,
                "max_iterations": request.max_iterations,
                "confidence_threshold": request.confidence_threshold
            }
            
            # 执行任务
            if request.execution_mode == "generate_only":
                result = await self._execute_generate_only(execution_params)
            else:
                result = await self._execute_generate_and_execute(execution_params)
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            return ArchitectureTaskResult(
                success=result.success,
                task_type=request.task_type,
                generated_content=result.generated_content,
                execution_result=result.execution_result,
                validation_result=result.validation_result,
                confidence=result.confidence,
                execution_time=execution_time,
                iterations_used=getattr(result, 'iterations_used', 1),
                error_message=result.error_message
            )
            
        except Exception as e:
            return ArchitectureTaskResult(
                success=False,
                task_type=request.task_type,
                generated_content="",
                execution_result=None,
                validation_result=None,
                confidence=0.0,
                execution_time=0.0,
                iterations_used=0,
                error_message=str(e)
            )
    
    def _get_operations_for_task(self, task_type: ArchitectureTaskType) -> List[PyCRUDOperation]:
        """根据任务类型获取PyCRUD操作"""
        operation_mapping = {
            ArchitectureTaskType.RISK_DETECTION: [
                PyCRUDOperation.FILE_READ,
                PyCRUDOperation.DOC_VALIDATE_FORMAT,
                PyCRUDOperation.DATA_PROCESS_JSON
            ],
            ArchitectureTaskType.CODE_GENERATION: PyCRUDOperationGroups.CODE_GENERATION_COMBO,
            ArchitectureTaskType.DOCUMENT_ANALYSIS: PyCRUDOperationGroups.DOC_OPERATIONS,
            ArchitectureTaskType.ARCHITECTURE_VALIDATION: [
                PyCRUDOperation.FILE_READ,
                PyCRUDOperation.CODE_VALIDATE_SYNTAX,
                PyCRUDOperation.DATA_VALIDATE
            ]
        }
        
        return operation_mapping.get(task_type, PyCRUDOperationGroups.FILE_OPERATIONS)
    
    async def _execute_generate_only(self, params: Dict[str, Any]) -> ExecutionResult:
        """执行仅生成模式"""
        # 使用API适配器调用AI服务生成内容
        ai_request = AIServiceRequest(
            task_type=params["input_data"].get("task_type", "analysis"),
            content=params["input_data"].get("prompt", ""),
            context=params["input_data"].get("context", {}),
            quality_requirements=params["validation_requirements"]
        )
        
        ai_response = await self.api_adapter.request_ai_service(ai_request)
        
        if ai_response.success:
            # 验证生成的内容
            validation_result = await self._validate_generated_content(
                ai_response.content, params["validation_requirements"]
            )
            
            return ExecutionResult.generate_only(
                generated_content=ai_response.content,
                validation_result=validation_result
            )
        else:
            return ExecutionResult(
                success=False,
                error_message=ai_response.error_message
            )
    
    async def _execute_generate_and_execute(self, params: Dict[str, Any]) -> ExecutionResult:
        """执行生成并执行模式"""
        # 先生成内容
        generate_result = await self._execute_generate_only(params)
        
        if not generate_result.success:
            return generate_result
        
        # 然后执行生成的内容
        execution_result = await self._execute_generated_content(
            generate_result.generated_content, params["operations"]
        )
        
        return ExecutionResult.generate_and_execute(
            execution_result=execution_result,
            validation_result=generate_result.validation_result
        )
    
    def _register_architecture_validators(self):
        """注册架构任务的验证器"""
        # 这里可以注册特定于架构任务的验证器
        pass
    
    async def _validate_generated_content(self, content: str, requirements: Dict[str, Any]) -> ValidationResult:
        """验证生成的内容"""
        # 实现内容验证逻辑
        return ValidationResult(
            confidence=0.9,
            py_results=[],
            ai_results=[],
            issues=[],
            passed=True
        )
    
    async def _execute_generated_content(self, content: str, operations: List[PyCRUDOperation]) -> Any:
        """执行生成的内容"""
        # 实现内容执行逻辑
        return {"status": "executed", "content": content}
```

### **3. Web界面集成适配器**

**文件位置**: `tools/ace/src/architecture_risk_system/integration/web_interface_adapter.py`

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web界面集成适配器
负责architecture_risk_system与ace现有web界面的集成
"""

from flask import Blueprint, request, jsonify, render_template
from typing import Dict, Any

# 导入ace现有web组件
from ...web_interface.app import app
from ...web_interface.controllers.validation_controller import ValidationController

class WebInterfaceAdapter:
    """Web界面集成适配器"""
    
    def __init__(self, api_adapter, task_adapter):
        self.api_adapter = api_adapter
        self.task_adapter = task_adapter
        self.validation_controller = ValidationController()
        
        # 创建架构风险检测蓝图
        self.blueprint = self._create_blueprint()
        
        # 注册蓝图到ace主应用
        app.register_blueprint(self.blueprint, url_prefix='/architecture-risk')
        
        print("✅ Web界面适配器初始化完成")
    
    def _create_blueprint(self) -> Blueprint:
        """创建架构风险检测蓝图"""
        bp = Blueprint('architecture_risk', __name__)
        
        @bp.route('/nine-grid')
        def nine_grid():
            """九宫格界面"""
            return render_template('architecture_nine_grid.html')
        
        @bp.route('/api/validate-directory', methods=['POST'])
        async def validate_directory():
            """验证工作目录"""
            data = request.get_json()
            # 使用现有验证控制器
            result = await self.validation_controller.validate_directory(data)
            return jsonify(result)
        
        @bp.route('/api/detect-risks', methods=['POST'])
        async def detect_risks():
            """检测架构风险"""
            data = request.get_json()
            # 使用任务适配器执行风险检测
            task_request = ArchitectureTaskRequest(
                task_type=ArchitectureTaskType.RISK_DETECTION,
                input_data=data,
                validation_requirements={"confidence_threshold": 0.85}
            )
            result = await self.task_adapter.execute_architecture_task(task_request)
            return jsonify(result.__dict__)
        
        return bp
```

## 📁 代码迁移计划

### **1. 现有文件迁移**

**task_level_validation_driven_executor.py迁移**:
```bash
# 从当前位置
docs/features/T001-create-plans-20250612/v4/design/化简/核心/底层执行器/task_level_validation_driven_executor.py

# 迁移到
tools/ace/src/task_executors/task_level_validation_driven_executor.py
```

**算法.py和检查.py重构**:
- 提取算法.py → `tools/ace/src/architecture_risk_system/algorithms/`
- 提取检查.py → `tools/ace/src/architecture_risk_system/algorithms/simple_detector.py`

### **2. 集成测试计划**

**第一阶段**: 适配器单元测试
**第二阶段**: 集成测试（API管理器 + 任务执行器）
**第三阶段**: 端到端测试（Web界面 + 后端服务）

## 🔄 初始化顺序

```python
# 1. 初始化API管理器适配器
api_adapter = APIManagerAdapter()

# 2. 初始化任务执行器适配器
task_adapter = TaskExecutorAdapter(api_adapter)

# 3. 初始化Web界面适配器
web_adapter = WebInterfaceAdapter(api_adapter, task_adapter)

# 4. 初始化核心业务组件
risk_detector = UnifiedRiskDetector(api_adapter, task_adapter)
code_generator = IntelligentCodeGenerator(api_adapter, task_adapter)
```

---

**集成原则**: 最小侵入 + 最大复用 + 清晰分层 + 易于测试
**核心价值**: 通过适配器模式实现与ace现有组件的无缝集成
