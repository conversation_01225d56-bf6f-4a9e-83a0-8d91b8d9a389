# V4.2方案：架构数据模型标准-V2

## 1. 文档信息

- **文档版本**: V2.0
- **创建日期**: 2025-07-31
- **文档性质**: **核心架构文档** - 定义V2系统的核心数据模型和架构标准
- **界面交互**: 界面交互设计已在《03-九宫格交互界面设计-V2.md》中单独定义

**注意**: 本文档专注于核心架构和数据模型，不涉及界面交互设计。界面交互部分请参考《03-九宫格交互界面设计-V2.md》文档。

## 2. 设计原则

本数据模型标准服务于V4架构，遵循以下核心原则：

- **自检优先 (Self-Validation First)**: 数据模型的核心始于“文档健康报告”，它量化了“真理之源”的可靠性。
- **契约驱动 (Contract-Driven)**: “验证契约”是连接设计思想到最终产出的核心载体。
- **分层设计 (Layered)**: 模型需能体现“全局”与“局部”的层级关系。
- **通用性 (Universal)**: 模型必须是领域无关的，能适应任何类型的验证需求。
- **可审计与可追溯 (Auditable & Traceable)**: 所有模型的关键属性都必须是可审计和可追溯的。

## 3. 阶段零：文档健康报告数据模型 (V4新增)

**【注意：本章节描述的数据模型服务于未来设想的“文档健康度预先检查”流程，在当前V4.2的异步任务框架中尚未被实现和使用。】**

这是V4架构的起点，是“源头文档风险分析”阶段的产出，为后续所有决策提供可靠性基准。

### 3.1. 文档健康报告 (DocumentHealthReport)

| 属性名 | 数据类型 | 描述 |
| :--- | :--- | :--- |
| `document_path` | `string` | 被分析的文档路径。 |
| `reliability_score` | `float` | **[核心指标]** 文档的综合可靠性评分 (0.0-1.0)，低于阈值时系统将报警。 |
| `detected_risks` | `List<ArchitecturalRisk>` | 在文档中直接检测到的架构风险列表。 |
| `consistency_issues` | `List<ConsistencyIssue>` | 文档内部或与其他文档之间的不一致性问题列表。 |
| `summary` | `string` | 对文档健康状况的自然语言总结和建议。 |

### 3.2. 架构风险 (ArchitecturalRisk)

代表在文档中发现的一个具体的架构性风险。

| 属性名 | 数据类型 | 描述 |
| :--- | :--- | :--- |
| `risk_id` | `string` | 风险的唯一标识符。 |
| `risk_level` | `string` | 风险等级，**枚举**: `"CRITICAL"`, `"HIGH"`, `"MEDIUM"`, `"LOW"`。 |
| `risk_type` | `string` | 风险类型，**枚举**: `"PRINCIPLE_CONFLICT"`, `"CONSTRAINT_CONFLICT"`, `"TECH_CHOICE_CONFLICT"`, `"GUARDRAIL_INCONSISTENCY"`, `"MISSING_PRINCIPLE"`, `"MISSING_BOUNDARY_OR_STATE_ANALYSIS"`, `"OTHER"`。 |
| `description` | `string` | 对风险的详细描述。 |
| `evidence_text` | `string` | 支持该风险判断的原文片段。 |
| `conflicting_elements` | `List<string>` | 发生冲突的具体元素（如冲突的约束文本、原则描述等）。 |
| `suggestion` | `string` | 针对该风险的修复建议。 |
| `requires_human_intervention` | `boolean` | 是否需要人工介入修正（CRITICAL级别的决策层冲突为true）。 |

### 3.3. 一致性问题 (ConsistencyIssue)

| 属性名 | 数据类型 | 描述 |
| :--- | :--- | :--- |
| `issue_id` | `string` | 问题的唯一标识符。 |
| `issue_type` | `string` | 问题类型，如 `INTERNAL_CONTRADICTION`, `CROSS_DOC_INCONSISTENCY`。 |
| `description` | `string` | 对不一致问题的详细描述。 |
| `involved_elements` | `List<string>` | 涉及不一致的文档元素或片段。 |

## 3.5. 黄金准则整合数据模型 (V4新增)

这是V4架构与《ValidationDrivenExecutor上层调用者黄金准则》优雅整合的核心数据结构。

### 3.5.1. 分层请求构建结果 (LayeredRequestBuildResult)

代表分层AI团队协同工作的最终成果，完全符合《黄金准则》的四类输入规范。

| 属性名 | 数据类型 | 描述 |
| :--- | :--- | :--- |
| `original_content` | `string` | 由决策层AI提炼的核心使命和愿景，作为启发性软引导 |
| `constraints` | `Dict[string, string]` | 由逻辑层AI提炼的具体技术约束，非黑即白的要求性规则 |
| `guardrails` | `Dict[string, string]` | 由决策层AI提炼的全局护栏，非黑即白的禁止性规则 |
| `context` | `Dict[string, string]` | 由逻辑层AI收集的背景信息，纯粹的事实性数据 |
| `layer_traceability` | `Dict[string, LayerSource]` | 每个字段的层级来源追溯，确保完整的可追溯性 |

### 3.5.2. 层级来源追溯 (LayerSource)

提供每个信息片段的完整来源链条，实现从最终输出到源语义块的完整追溯。

| 属性名 | 数据类型 | 描述 |
| :--- | :--- | :--- |
| `ai_role` | `string` | 处理该信息的AI角色，枚举：`"首席架构师"`, `"领域专家"`, `"资深工程师"` |
| `source_layer` | `LayerType` | 信息来源的层级，枚举：`"Decision"`, `"Logic"`, `"Execution"` |
| `source_block_id` | `string` | 来源语义块的唯一标识符 |
| `confidence_score` | `float` | AI处理该信息的置信度 (0.0-1.0) |
| `processing_timestamp` | `string` | 处理时间戳，用于审计和调试 |

### 3.5.3. 双重分类语义块 (DualClassifiedBlock)

扩展的语义块，包含层级分类和准则映射的双重标签。

| 属性名 | 数据类型 | 描述 |
| :--- | :--- | :--- |
| `block_id` | `string` | 语义块的唯一标识符 |
| `content` | `string` | 语义块的文本内容 |
| `layer_classification` | `LayerType` | 层级分类：决策/逻辑/执行 |
| `guideline_mapping` | `GuidelineType` | **[上游概念]** 准则映射：original_content/constraints/guardrails/context。此字段由‘宏观语义地图构建’流程赋值，用于定义文本的‘意图’。下游流程严禁修改此字段。 |
| `confidence_score` | `float` | 分类置信度 (0.0-1.0) |
| `reasoning` | `string` | AI分类的推理过程，用于审计和调试 |
| `source_document` | `string` | 来源文档路径 |
| `line_range` | `Tuple[int, int]` | 在源文档中的行号范围 |

### 3.A. 统一语义模型 (V4.2核心升级)

这是V4.2架构的基石。我们不再为不同类型的知识创建不同的模型，而是采用一个统一的、可扩展的`AtomicConstraint`模型来承载所有信息。

#### 3.A.1. 原子知识单元 (AtomicConstraint)

| 属性名 | 数据类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `string` | **[核心]** 知识单元的唯一ID，由算法在预验证后生成 (如 `GC001`, `GB001`, `GS001`)。 |
| `parent_id` | `string` | **[核心]** (可选) 父知识单元的ID，用于表明此单元是对上层单元的细化或“分叉”。 |
| `source_block_id` | `string` | **[核心]** (可选) 指向上游`DualClassifiedBlock`的ID，确保血统可追溯。 |
| `category` | `string` | **[核心升级] 语义分类器**。定义此知识单元的宏观类型。该字段是**可扩展的**，例如：`'constraint'`, `'guardrail'`, `'boundary_condition'`, `'state_machine'`等。**系统的可扩展性完全依赖此字段。** |
| `type` | `string` | 知识单元的具体类型 (如 `memory_limit`, `rate_limit`, `order_lifecycle`)。 |
| `params` | `dict` | **[核心升级] 参数货箱**。一个结构化字典，其Schema由`category`和`type`决定，用于容纳任意复杂的参数。**必须为不同category提供清晰的params结构示例。** |
| `description` | `string` | 对知识单元的自然语言描述。 |

#### 3.A.2. `params` 结构化示例

**示例1：承载“边界条件” (`category: 'boundary_condition'`)**
```json
{
  "category": "boundary_condition",
  "type": "rate_limit",
  "params": {
    "target_entity": "api_request",
    "limit_value": 1000,
    "unit": "rps",
    "on_exceed_action": "return_http_429"
  }
}
```

**示例2：承载“状态机” (`category: 'state_machine'`)**
```json
{
  "category": "state_machine",
  "type": "order_lifecycle",
  "params": {
    "target_entity": "Order",
    "diagram_type": "mermaid_state_diagram_v2",
    "diagram_source": "stateDiagram-v2\n  [*] --> PENDING_PAYMENT\n  PENDING_PAYMENT --> PAID\n  ..."
  }
}
```

## 4. 阶段一与阶段二：核心契约数据模型

**【注意：本章节描述的契约模型（AIContract, ValidationChainPoint）服务于一个设想中的、基于“验证链”的治理引擎。在当前V4.2的实现中，核心验证逻辑将由更先进的“插件化验证器”架构（由`ValidationLoop`调度）来承载，该架构直接消费`AtomicConstraint`模型。】**

这是V4架构的核心治理工具，其结构与V3.1保持一致，但其生成过程现在会参考“文档健康报告”。

### 3.1. 验证契约 (AIContract)

代表由“验证架构师”AI生成的、完整的质量保证计划。

| 属性名 | 数据类型 | 描述 |
| :--- | :--- | :--- |
| `confidence_score` | `float` | AI对自己生成的这份契约能够指导产出高质量内容的信心（0.0-1.0）。 |
| `validation_chain` | `List<ValidationChainPoint>` | 验证链，包含一系列具体的、机器可读的验证点。 |
| `context_signature` | `string` | 用于确保上下文一致性的哈希签名。 |

### 3.2. 验证链点 (ValidationChainPoint)

验证链中的单个原子验证规则，是算法审计的基本单位。

| 属性名 | 数据类型 | 描述 | 算法审计作用 |
| :--- | :--- | :--- | :--- |
| `point_id` | `string` | 验证点的唯一标识符，如 `kernel_is_decision_layer`。 | 用于追踪和报告。 |
| `validation_type` | `string` | **[核心] 验证器类型**。一个可扩展的枚举，用于指定下游应调用哪个**专业验证器插件**。例如: `'STATE_MACHINE_VALIDATION'`, `'BOUNDARY_CONDITION_VALIDATION'`。 | 指示`ValidationLoop`微核从插件注册表中查找并调用对应的验证器插件。 |
| `target` | `string` | **验证模式 (Pattern)**。通常是一个高精度的正则表达式，由AI根据上下文生成，用于匹配产出内容。 | 产出审计阶段的核心匹配模式。 |
| `expected_result` | `string` | **期望结果**。严格枚举: `"MATCHES"`, `"NOT_FOUND"`。 | 在`ContractAuditor`的**意图审计**中，此字段将与`entity_keywords`的来源进行交叉验证。 |
| `entity_keywords` | `List<string>` | **溯源与意图审计关键词**。**必须**从原始需求中**逐字复制**的文本。这是算法审计AI的基石。 | **1. 溯源审计**: `ContractAuditor`会验证此列表中的每个关键词是否都真实存在于原始上下文中，杜绝AI幻觉。<br/>**2. 意图审计**: `ContractAuditor`会验证关键词的来源（如`constraints`或`guardrails`）与`expected_result`的意图是否匹配。 |
| `is_critical` | `boolean` | 标记该验证点是否为关键验证点。 | 决定其在产出审计失败时是否中断流程。 |
| `description` | `string` | 对该验证点的自然语言描述。 | 增强可读性和可维护性。 |

## 5. 全局与局部契约

V4架构继承并强化了V3.1的分层契约概念。

- **全局验证契约 (Global Validation Contract)**: 生成过程会参考“文档健康报告”。AI会被引导生成能够规避或检测已知风险的“防御性”规则，从而产出一份更鲁棒的“全局约束集”。
- **局部验证契约 (Local Validation Contract)**: 与V3.1一致，继承更可靠的全局约束，并结合局部文档生成。

## 6. 阶段三：最终产出数据模型

这是“契约履行”阶段的最终产出。其结构保持通用，可靠性由经过自检和分层治理的契约所保证。

### 6.1. 架构图节点 (Node) 属性标准

当产出为架构图时，节点是图中的核心实体，代表一个组件、模块、服务或一个架构概念。

### 4.1.1. 所有节点的通用属性

这是每个节点都必须具备的基础属性集。

| 属性名 | 数据类型 | 描述 | 在V3架构中的作用 |
| :--- | :--- | :--- | :--- |
| `id` | `string` | **唯一标识符**。如 `decision_nexus_kernel`。 | 作为图中节点的唯一键，用于建立关系。 |
| `label` | `string` | **可读名称**。如“Nexus Kernel”。 | 用于生成报告和可视化展示。 |
| `type` | `string` | **节点类型**。**严格枚举**: `"决策"`, `"逻辑"`, `"执行"`。 | 在**产出审计**阶段，由`ValidationChainPoint`进行验证。 |
| `layer` | `integer` | **所属层级**。**严格枚举**: `0` (决策), `1` (逻辑), `2` (执行)。 | 在**产出审计**阶段，由`ValidationChainPoint`进行验证。 |
| `description` | `string` | **功能描述**。从文档中提取的关于该节点核心职责的简短描述。 | 用于语义分析和生成报告。 |
| `source_doc` | `string` | **来源文档**。定义该节点的文档路径。 | 提供最终的、人工可读的追溯路径。 |
| `source_text` | `string` | **来源文本**。定义该节点的原始文本片段。 | 提供最终的、人工可读的追溯路径。 |

### 4.1.2. 特定类型节点的扩展属性

这些属性用于深化语义理解，其完整性和正确性同样由“验证契约”来保证。

#### **对于 `决策节点` (type: "决策")**

| 属性名 | 数据类型 | 描述 | 在V4架构中的作用 |
| :--- | :--- | :--- | :--- |
| `decision_logic` | `string` | 描述其核心决策逻辑或所用算法。 | 其存在性和内容由特定的`ValidationChainPoint`保证。 |
| `input_triggers` | `list[string]` | 触发该决策的输入信息或事件类型。 | 用于分析系统的触发路径和数据流。 |
| `global_constraints` | `List[AtomicConstraint]` | **[V4.1]** 该决策节点定义的、经过预验证的全局约束对象列表。 | 作为整个系统的“法律”源头，供所有下层节点引用。 |
| `global_guardrails` | `List[AtomicConstraint]` | **[V4.1]** 该决策节点定义的、经过预验证的全局护栏对象列表。 | 作为整个系统的“安全护栏”源头，供所有下层节点引用。 |

#### **对于 `逻辑节点` (type: "逻辑")**

| 属性名 | 数据类型 | 描述 | 在V3架构中的作用 |
| :--- | :--- | :--- | :--- |
| `routing_rules` | `list[string]` | 描述其数据转换或路由规则。 | 其存在性和内容由特定的`ValidationChainPoint`保证。 |
| `inherited_global_constraints` | `List[string]` | **[V4.1]** 直接引用的全局约束ID列表 (包括约束和护栏, 如 `["GC001", "GG001"]`)。 | 表明该节点遵守哪些顶层设计。 |
| `local_constraints` | `List[AtomicConstraint]` | **[V4.1]** 本逻辑层特有的、或对全局约束进行“分叉”的原子约束对象列表。 | 定义本层级的具体规则或对上层规则的细化。 |

#### **对于 `执行节点` (type: "执行")**

| 属性名 | 数据类型 | 描述 | 在V3架构中的作用 |
| :--- | :--- | :--- | :--- |
| `target_interface` | `string` | 描述其直接交互的外部系统、硬件或底层API。 | 用于验证其是否为真正的“执行者”。 |
| `inherited_global_constraints` | `List[string]` | **[V4.1]** 直接引用的全局约束ID列表 (包括约束和护栏)。 | 引用顶层设计。 |
| `inherited_logic_constraints` | `List[string]` | **[V4.1]** 直接引用的、来自其父逻辑层的本地约束ID列表 (如 `["LC001"]`)。 | 引用中间层设计。 |
| `local_constraints` | `List[AtomicConstraint]` | **[V4.1]** 本执行层特有的、或对逻辑层约束进行“分叉”的原子约束对象列表。 | 定义本层级的具体实现规则。 |

#### **V4.1 引用与分叉机制说明**

**核心机制**:
V4.1架构用“引用与分叉”取代了简单的继承。下层节点通过引用上层`AtomicConstraint`的ID来表达遵从。当需要对上层约束进行细化时，可以创建一个新的`local_constraints`条目，并使用`parent_id`字段指向被细化的上层约束ID，形成“分叉”。

**算法管控**:
所有`AtomicConstraint`的ID由`ConstraintPreprocessor`在预验证通过后统一生成和分配，确保了唯一性和全局可追溯性。AI只负责识别和定义约束的`category`, `type`, 和 `params`。详细逻辑见《4-微观图构建与双重验证引擎-V2.md》。

### 4.2. 关系 (Edge) 属性标准

关系（边）定义了节点之间的交互方式、依赖或数据流。

| 属性名 | 数据类型 | 描述 | 在V3架构中的作用 |
| :--- | :--- | :--- | :--- |
| `source` | `string` | **源节点ID**。必须是图中已存在的节点ID。 | 定义边的起点。 |
| `target` | `string` | **目标节点ID**。必须是图中已存在的节点ID。 | 定义边的终点。 |
| `type` | `string` | **关系类型**。**严格枚举**: `"控制"`, `"数据"`, `"触发"`, `"依赖"`。 | 在**产出审计**阶段，由`ValidationChainPoint`进行验证。 |
| `label` | `string` | **可读描述**。对关系的简短描述。 | 用于生成报告和可视化。 |
| `data_payload` | `list[string]` | **数据载荷**。如果关系是数据流，描述传输的核心数据项。 | 用于详细的接口一致性分析。 |
| `source_doc` | `string` | **来源文档**。定义该关系的文档路径。 | 提供最终的、人工可读的追溯路径。 |
| `source_text` | `string` | **来源文本**。定义该关系的原始文本片段。 | 提供最终的、人工可读的追溯路径。 |

## 7. 架构重构数据模型 (V4.2新增)

在执行“人机协同的架构重构工作流”时，V2引擎的核心产出不再是直接的代码，而是一个结构化的、可被人类审查的**“变更集” (`ChangeSet`)**。该模型是连接AI规划与人类决策的关键桥梁。

### 7.1. 变更集 (ChangeSet)

代表一个完整的、针对特定重构目标的变更计划。

| 属性名 | 数据类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `string` | 本次变更集的唯一ID。 |
| `description` | `string` | 对本次重构任务的自然语言描述，如“重构UserService以使用统一异常库”。 |
| `source_constraint_id` | `string` | 触发本次重构的设计文档约束ID，确保所有变更都可追溯至“法典”。 |
| `modifications` | `List<FileModification>` | 一个包含了对多个文件的具体修改指令的列表。 |

### 7.2. 文件修改指令 (FileModification)

描述了针对单个文件的所有修改操作。

| 属性名 | 数据类型 | 描述 |
| :--- | :--- | :--- |
| `file_path` | `string` | 需要被修改的文件的完整路径。 |
| `changes` | `List<CodeChange>` | 一个包含了对该文件的多处具体代码修改的列表。 |

### 7.3. 代码变更单元 (CodeChange)

定义了一个原子性的“查找并替换”操作，这是变更集中最基本的执行单元。

| 属性名 | 数据类型 | 描述 |
| :--- | :--- | :--- |
| `search_block` | `string` | **[核心]** 需要在文件中精确查找的旧代码块。**必须**完整匹配，包括缩进和换行。 |
| `replace_block` | `string` | **[核心]** 用于替换旧代码块的新代码块。 |
| `description` | `string` | (可选) 对本次具体修改的解释，如“将旧的RuntimeException替换为业务自定义的UserCreationException”。 |
| `is_critical` | `boolean` | (可选) 标记本次修改是否为关键性修改。 |

## 8. V4标准如何赋能“自检与治理”

这套数据模型是我们V4架构的基石，它通过其设计，在不同阶段赋能我们的核心理念。

### **阶段零：赋能“源头自检”**
- **`DocumentHealthReport`** 及其子模型，为我们提供了一个结构化的、可量化的方式来评估“真理之源”的可靠性，这是V4架构的根基。

### **阶段一与阶段二：赋能“风险感知的契约治理”**
- **`DocumentHealthReport`** 作为“全局契约生成”的关键输入，使得AI能够生成风险感知的、更智能的验证规则。
- **`ValidationChainPoint`** 作为统一的契约原子，使得经过自检的、分层的约束可以被同一个`ContractAuditor`审计。
- **`entity_keywords`** 和 **`expected_result`** 依然是算法进行溯源性和意图一致性审计的基础。

### **阶段三：赋能“通用产出审计”**
- **`validation_type`** 字段确保了系统的通用性和可扩展性，使其能够履行和审计任何领域的契约。
- **最终产出模型** (如节点和边) 的正确性，由一个经过了“源头自检”、“分层治理”和“算法审计”的、极其可靠的契约链条来保证。

通过这种方式，我们将一个复杂的“架构治理”问题，分解为了一个**可度量的、自洽的、分层的、可靠的“先体检、再签约、后履行”**的工程问题，从而实现了V4架构的核心目标。
