---
title: API文档标题
document_id: C022
document_type: 共享文档
category: 模板
scope: [全局|特定API|特定模块]
keywords: [API, 接口, 文档, 规范]
created_date: YYYY-MM-DD
updated_date: YYYY-MM-DD
status: 草稿
version: 1.0
authors: [作者1, 作者2]
affected_features:
  - 功能ID1
  - 功能ID2
related_docs:
  - 相关文档1的路径
  - 相关文档2的路径
---

# API文档标题

## API概述

[简要描述API的目的、功能和使用场景]

## 基本信息

- **API类型**: [REST | gRPC | 内部服务]
- **版本**: [API版本号]
- **状态**: [开发中 | 测试中 | 已发布 | 已弃用]
- **负责人**: [负责人姓名]
- **联系方式**: [联系方式]

## 接口详情

### 接口1: [接口名称]

#### 请求信息

- **路径**: `[请求路径]`
- **方法**: [GET | POST | PUT | DELETE | ...]
- **描述**: [接口功能描述]

#### 请求参数

##### 路径参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|-------|-----|------|------|-----|
| param1 | string | 是 | 参数1描述 | example1 |
| param2 | integer | 否 | 参数2描述 | 123 |

##### 查询参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|-------|-----|------|------|-----|
| query1 | string | 否 | 查询参数1描述 | example1 |
| query2 | integer | 否 | 查询参数2描述 | 123 |

##### 请求头

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|-------|-----|------|------|-----|
| Authorization | string | 是 | 认证令牌 | Bearer token123 |
| Content-Type | string | 是 | 内容类型 | application/json |

##### 请求体

```json
{
  "field1": "value1",
  "field2": 123,
  "field3": {
    "subfield1": "subvalue1"
  },
  "field4": [
    "item1",
    "item2"
  ]
}
```

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|-------|-----|------|------|-----|
| field1 | string | 是 | 字段1描述 | value1 |
| field2 | integer | 是 | 字段2描述 | 123 |
| field3 | object | 否 | 字段3描述 | {"subfield1": "subvalue1"} |
| field3.subfield1 | string | 是 | 子字段1描述 | subvalue1 |
| field4 | array | 否 | 字段4描述 | ["item1", "item2"] |

#### 响应信息

##### 响应状态码

| 状态码 | 描述 | 说明 |
|-------|-----|------|
| 200 | OK | 请求成功 |
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未授权 |
| 403 | Forbidden | 禁止访问 |
| 404 | Not Found | 资源不存在 |
| 500 | Internal Server Error | 服务器内部错误 |

##### 响应头

| 参数名 | 类型 | 描述 | 示例 |
|-------|-----|------|-----|
| Content-Type | string | 内容类型 | application/json |

##### 响应体

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "12345",
    "name": "示例名称",
    "createTime": "2023-01-01T12:00:00Z"
  }
}
```

| 字段名 | 类型 | 描述 | 示例 |
|-------|-----|------|-----|
| code | integer | 业务状态码 | 0 |
| message | string | 状态描述 | success |
| data | object | 响应数据 | {"id": "12345", "name": "示例名称", "createTime": "2023-01-01T12:00:00Z"} |
| data.id | string | 数据ID | 12345 |
| data.name | string | 数据名称 | 示例名称 |
| data.createTime | string | 创建时间 | 2023-01-01T12:00:00Z |

### 接口2: [接口名称]

[接口2的详细信息，格式同接口1]

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|-------|-----|---------|
| 10001 | 参数错误 | 检查请求参数是否符合要求 |
| 10002 | 认证失败 | 检查认证信息是否正确 |
| 20001 | 资源不存在 | 确认请求的资源ID是否存在 |
| 30001 | 服务器内部错误 | 联系管理员处理 |

## 使用示例

### 示例1: [示例名称]

#### 请求示例

```bash
curl -X POST "https://api.example.com/v1/resource" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer token123" \
  -d '{
    "field1": "value1",
    "field2": 123
  }'
```

#### 响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "12345",
    "name": "示例名称",
    "createTime": "2023-01-01T12:00:00Z"
  }
}
```

### 示例2: [示例名称]

[示例2的详细信息，格式同示例1]

## 注意事项

- [注意事项1]
- [注意事项2]
- [注意事项3]

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 1.0 | YYYY-MM-DD | 初始版本 | 作者 |
