# 字段级版本一致性检查机制修改提示词

**目标文件**: `05-字段级版本一致性检查机制.md`  
**修改原则**: 将"智能版本检查"重新设计为"规则化版本验证 + 外部AI服务增强"  
**核心理念**: 明确版本检查的规则化处理和AI服务的辅助作用

---

## 🎯 版本一致性检查重新定位

### 版本检查机制重新定义
```pseudocode
// 修改前：混淆的"智能版本检查"
❌ 基于AI的智能版本一致性检查
❌ 智能字段版本匹配算法

// 修改后：明确的"规则化版本验证"
✅ 基于规则的版本一致性验证
✅ 规则化字段版本匹配算法 + 外部AI服务增强

DEFINE VersionConsistencyCheckingPhilosophy:
    // 核心职责划分
    规则引擎职责:
        - 字段级版本规则验证
        - 版本兼容性规则检查
        - 标准版本冲突检测
        
    外部AI服务职责:
        - 复杂版本冲突分析
        - 版本升级路径建议
        - 跨版本兼容性评估
        
    人工决策职责:
        - 版本策略制定
        - 复杂版本冲突解决
        - 版本管理政策决策
END DEFINE
```

## 🔧 字段级版本验证引擎重新设计

### 规则化版本检查核心组件
```pseudocode
COMPONENT RuleBasedFieldVersionValidator:
    DEPENDENCIES:
        versionRuleRepository: VersionRuleRepository
        fieldVersionExtractor: FieldVersionExtractor
        compatibilityRuleEngine: CompatibilityRuleEngine
        externalAIClient: ExternalAIServiceClient
        versionConflictResolver: VersionConflictResolver
    
    FUNCTION validateFieldVersionConsistency(jsonConfig, engineVersion):
        // 1. 规则化字段版本提取
        fieldVersions = fieldVersionExtractor.extractAllFieldVersions(jsonConfig)
        
        // 2. 规则化版本兼容性检查
        compatibilityResults = []
        FOR fieldVersion IN fieldVersions:
            compatibilityRule = versionRuleRepository.findCompatibilityRule(
                fieldVersion.fieldName, fieldVersion.version, engineVersion)
            
            compatibilityResult = compatibilityRuleEngine.checkCompatibility(
                compatibilityRule, fieldVersion, engineVersion)
            
            compatibilityResults.add(compatibilityResult)
        END FOR
        
        // 3. 规则化冲突检测
        conflicts = detectVersionConflicts(compatibilityResults)
        
        // 4. 复杂版本冲突的AI分析（可选）
        aiConflictAnalysis = NULL
        IF conflicts.complexity > VERSION_CONFLICT_COMPLEXITY_THRESHOLD:
            aiRequest = buildVersionConflictAnalysisRequest(
                jsonConfig, fieldVersions, conflicts, engineVersion)
            aiConflictAnalysis = externalAIClient.analyzeVersionConflicts(aiRequest)
        
        RETURN FieldVersionValidationResult(
            fieldVersions: fieldVersions,
            compatibilityResults: compatibilityResults,
            conflicts: conflicts,
            aiConflictAnalysis: aiConflictAnalysis,
            overallCompatibility: calculateOverallCompatibility(compatibilityResults)
        )
    END FUNCTION
    
    FUNCTION detectVersionConflicts(compatibilityResults):
        conflicts = []
        
        // 规则化冲突检测算法
        FOR result IN compatibilityResults:
            IF result.compatibility == INCOMPATIBLE:
                conflict = VersionConflict(
                    fieldName: result.fieldName,
                    fieldVersion: result.fieldVersion,
                    engineVersion: result.engineVersion,
                    conflictType: INCOMPATIBLE_VERSION,
                    severity: calculateConflictSeverity(result)
                )
                conflicts.add(conflict)
            
            ELSE IF result.compatibility == DEPRECATED:
                conflict = VersionConflict(
                    fieldName: result.fieldName,
                    fieldVersion: result.fieldVersion,
                    engineVersion: result.engineVersion,
                    conflictType: DEPRECATED_VERSION,
                    severity: WARNING
                )
                conflicts.add(conflict)
        END FOR
        
        RETURN conflicts
    END FUNCTION
END COMPONENT
```

### 版本规则仓库设计
```pseudocode
COMPONENT VersionRuleRepository:
    DEPENDENCIES:
        ruleStorage: VersionRuleStorage
        ruleCache: VersionRuleCache
        ruleValidator: VersionRuleValidator
    
    FUNCTION findCompatibilityRule(fieldName, fieldVersion, engineVersion):
        // 1. 缓存查找
        cachedRule = ruleCache.get(fieldName, fieldVersion, engineVersion)
        IF cachedRule != NULL:
            RETURN cachedRule
        
        // 2. 存储查找
        storedRule = ruleStorage.findRule(fieldName, fieldVersion, engineVersion)
        IF storedRule != NULL:
            ruleCache.put(fieldName, fieldVersion, engineVersion, storedRule)
            RETURN storedRule
        
        // 3. 默认规则生成
        defaultRule = generateDefaultCompatibilityRule(fieldName, fieldVersion, engineVersion)
        RETURN defaultRule
    END FUNCTION
    
    FUNCTION generateDefaultCompatibilityRule(fieldName, fieldVersion, engineVersion):
        // 基于语义化版本的默认规则
        IF isSemanticVersion(fieldVersion) AND isSemanticVersion(engineVersion):
            RETURN generateSemanticVersionRule(fieldName, fieldVersion, engineVersion)
        ELSE:
            RETURN generateStrictVersionRule(fieldName, fieldVersion, engineVersion)
    END FUNCTION
    
    FUNCTION generateSemanticVersionRule(fieldName, fieldVersion, engineVersion):
        fieldSemVer = parseSemanticVersion(fieldVersion)
        engineSemVer = parseSemanticVersion(engineVersion)
        
        // 语义化版本兼容性规则
        IF fieldSemVer.major != engineSemVer.major:
            RETURN CompatibilityRule.INCOMPATIBLE("主版本不兼容")
        ELSE IF fieldSemVer.minor > engineSemVer.minor:
            RETURN CompatibilityRule.INCOMPATIBLE("次版本过高")
        ELSE:
            RETURN CompatibilityRule.COMPATIBLE("语义化版本兼容")
    END FUNCTION
END COMPONENT
```

## 🔧 版本冲突解决器重新设计

### 规则化冲突解决机制
```pseudocode
COMPONENT VersionConflictResolver:
    DEPENDENCIES:
        resolutionRuleEngine: ConflictResolutionRuleEngine
        versionUpgradePathFinder: VersionUpgradePathFinder
        externalAIClient: ExternalAIServiceClient
        humanEscalationService: HumanEscalationService
    
    FUNCTION resolveVersionConflicts(conflicts, jsonConfig, engineVersion):
        resolutionResults = []
        
        FOR conflict IN conflicts:
            // 1. 规则化冲突解决尝试
            ruleBasedResolution = attemptRuleBasedResolution(conflict, jsonConfig, engineVersion)
            
            IF ruleBasedResolution.isResolved():
                resolutionResults.add(ruleBasedResolution)
                CONTINUE
            
            // 2. 外部AI服务增强解决
            aiEnhancedResolution = attemptAIEnhancedResolution(
                conflict, ruleBasedResolution, jsonConfig, engineVersion)
            
            IF aiEnhancedResolution.isResolved():
                resolutionResults.add(aiEnhancedResolution)
                CONTINUE
            
            // 3. 人工决策升级
            humanResolution = escalateToHumanDecision(
                conflict, ruleBasedResolution, aiEnhancedResolution)
            
            resolutionResults.add(humanResolution)
        END FOR
        
        RETURN ConflictResolutionResult(
            originalConflicts: conflicts,
            resolutionResults: resolutionResults,
            overallResolutionStatus: calculateOverallResolutionStatus(resolutionResults)
        )
    END FUNCTION
    
    FUNCTION attemptRuleBasedResolution(conflict, jsonConfig, engineVersion):
        // 基于预定义规则的冲突解决
        resolutionRules = resolutionRuleEngine.findApplicableRules(
            conflict.conflictType, conflict.severity)
        
        FOR rule IN resolutionRules:
            resolutionAttempt = resolutionRuleEngine.applyRule(rule, conflict, jsonConfig)
            
            IF resolutionAttempt.isSuccessful():
                RETURN ConflictResolution.ruleBasedSuccess(
                    conflict: conflict,
                    appliedRule: rule,
                    resolutionAction: resolutionAttempt.action,
                    modifiedConfig: resolutionAttempt.modifiedConfig
                )
        END FOR
        
        RETURN ConflictResolution.ruleBasedFailure(conflict, "无适用规则")
    END FUNCTION
    
    FUNCTION attemptAIEnhancedResolution(conflict, ruleBasedAttempt, jsonConfig, engineVersion):
        // 构建AI分析请求
        aiRequest = buildConflictResolutionRequest(
            conflict: conflict,
            ruleBasedAttempt: ruleBasedAttempt,
            jsonConfig: jsonConfig,
            engineVersion: engineVersion,
            requestType: "VERSION_CONFLICT_RESOLUTION"
        )
        
        TRY:
            aiResponse = externalAIClient.resolveVersionConflict(aiRequest)
            
            // 验证AI建议的可行性
            feasibilityCheck = validateAIResolutionFeasibility(aiResponse, conflict)
            
            IF feasibilityCheck.isFeasible():
                RETURN ConflictResolution.aiEnhancedSuccess(
                    conflict: conflict,
                    aiRecommendation: aiResponse.recommendation,
                    feasibilityCheck: feasibilityCheck,
                    modifiedConfig: aiResponse.suggestedConfig
                )
            ELSE:
                RETURN ConflictResolution.aiEnhancedFailure(
                    conflict, "AI建议不可行: " + feasibilityCheck.reason)
            
        CATCH AIServiceException e:
            RETURN ConflictResolution.aiEnhancedFailure(
                conflict, "AI服务不可用: " + e.message)
        END TRY
    END FUNCTION
END COMPONENT
```

## 🔧 版本升级路径查找器重新设计

### 规则化升级路径算法
```pseudocode
COMPONENT VersionUpgradePathFinder:
    DEPENDENCIES:
        versionGraphRepository: VersionGraphRepository
        upgradeRuleEngine: UpgradeRuleEngine
        pathOptimizer: UpgradePathOptimizer
        externalAIClient: ExternalAIServiceClient
    
    FUNCTION findUpgradePath(fromVersion, toVersion, fieldName):
        // 1. 规则化路径查找
        versionGraph = versionGraphRepository.getVersionGraph(fieldName)
        possiblePaths = findAllPossiblePaths(versionGraph, fromVersion, toVersion)
        
        IF possiblePaths.isEmpty():
            RETURN UpgradePathResult.noPathFound(fromVersion, toVersion)
        
        // 2. 规则化路径评估
        evaluatedPaths = []
        FOR path IN possiblePaths:
            pathEvaluation = upgradeRuleEngine.evaluatePath(path, fieldName)
            evaluatedPaths.add(pathEvaluation)
        END FOR
        
        // 3. 规则化路径优化
        optimizedPath = pathOptimizer.selectOptimalPath(evaluatedPaths)
        
        // 4. 复杂升级路径的AI增强（可选）
        aiPathAnalysis = NULL
        IF optimizedPath.complexity > UPGRADE_PATH_COMPLEXITY_THRESHOLD:
            aiRequest = buildUpgradePathAnalysisRequest(
                fromVersion, toVersion, fieldName, optimizedPath)
            aiPathAnalysis = externalAIClient.analyzeUpgradePath(aiRequest)
        
        RETURN UpgradePathResult.success(
            fromVersion: fromVersion,
            toVersion: toVersion,
            optimizedPath: optimizedPath,
            aiPathAnalysis: aiPathAnalysis,
            estimatedEffort: calculateUpgradeEffort(optimizedPath)
        )
    END FUNCTION
    
    FUNCTION findAllPossiblePaths(versionGraph, fromVersion, toVersion):
        // 使用深度优先搜索算法查找所有可能路径
        allPaths = []
        currentPath = [fromVersion]
        visited = Set()
        
        dfsSearchPaths(versionGraph, fromVersion, toVersion, currentPath, visited, allPaths)
        
        RETURN allPaths
    END FUNCTION
    
    FUNCTION dfsSearchPaths(graph, currentVersion, targetVersion, currentPath, visited, allPaths):
        IF currentVersion == targetVersion:
            allPaths.add(currentPath.copy())
            RETURN
        
        visited.add(currentVersion)
        
        FOR nextVersion IN graph.getDirectUpgrades(currentVersion):
            IF NOT visited.contains(nextVersion):
                currentPath.add(nextVersion)
                dfsSearchPaths(graph, nextVersion, targetVersion, currentPath, visited, allPaths)
                currentPath.removeLast()
        
        visited.remove(currentVersion)
    END FUNCTION
END COMPONENT
```

## 🔧 版本兼容性策略重新设计

### 环境感知的版本策略
```pseudocode
COMPONENT EnvironmentAwareVersionStrategy:
    DEPENDENCIES:
        environmentDetector: EnvironmentTypeDetector
        strategyRuleRepository: VersionStrategyRuleRepository
        externalAIClient: ExternalAIServiceClient
    
    FUNCTION selectVersionStrategy(environmentType, jsonConfig, engineVersion):
        // 基于环境类型的规则化策略选择
        SWITCH environmentType:
            CASE MOCK_DEVELOPMENT:
                RETURN VersionStrategy(
                    strictness: RELAXED,
                    conflictTolerance: HIGH,
                    autoUpgrade: ENABLED,
                    purpose: "开发阶段快速验证"
                )
                
            CASE MOCK_DIAGNOSTIC:
                RETURN VersionStrategy(
                    strictness: STANDARD,
                    conflictTolerance: MEDIUM,
                    autoUpgrade: CONDITIONAL,
                    purpose: "故障诊断精确分析"
                )
                
            CASE MOCK_PROTECTION:
                RETURN VersionStrategy(
                    strictness: CONSERVATIVE,
                    conflictTolerance: LOW,
                    autoUpgrade: DISABLED,
                    purpose: "保护模式稳定运行"
                )
                
            CASE REAL_TESTCONTAINERS:
                RETURN VersionStrategy(
                    strictness: STRICT,
                    conflictTolerance: VERY_LOW,
                    autoUpgrade: MANUAL_APPROVAL,
                    purpose: "真实环境严格验证"
                )
        END SWITCH
    END FUNCTION
    
    FUNCTION adaptStrategyWithAI(baseStrategy, complexityIndicators):
        // 复杂场景的AI策略增强
        IF complexityIndicators.overallComplexity > STRATEGY_COMPLEXITY_THRESHOLD:
            aiRequest = buildStrategyOptimizationRequest(baseStrategy, complexityIndicators)
            aiStrategyEnhancement = externalAIClient.optimizeVersionStrategy(aiRequest)
            
            RETURN mergeStrategyWithAIEnhancement(baseStrategy, aiStrategyEnhancement)
        ELSE:
            RETURN baseStrategy
    END FUNCTION
END COMPONENT
```

## 📋 修改检查清单

### 必须删除的混淆概念
- [ ] 删除所有"智能版本检查"表述
- [ ] 删除所有"AI版本匹配算法"声明
- [ ] 删除所有"智能冲突解决"描述
- [ ] 删除所有版本检查的AI能力声明

### 必须添加的明确组件
- [ ] RuleBasedFieldVersionValidator规则化字段版本验证器
- [ ] VersionRuleRepository版本规则仓库
- [ ] VersionConflictResolver版本冲突解决器
- [ ] ExternalAIServiceClient外部AI服务客户端
- [ ] VersionUpgradePathFinder版本升级路径查找器

### 必须明确的职责边界
- [ ] 规则引擎：标准版本兼容性检查和冲突检测
- [ ] 外部AI服务：复杂版本冲突分析和升级路径建议
- [ ] 人工决策：版本策略制定和复杂冲突解决
- [ ] 环境感知：基于环境类型的版本策略选择

这个修改提示词确保了版本一致性检查机制的正确设计，明确区分了规则验证与AI服务的职责边界。
