---
title: 基于PostgreSQL的持久化实例ID及特征码恢复方案
document_id: C027
document_type: 设计文档
category: 中间件
scope: 全局
keywords: [持久化ID, PostgreSQL, 实例恢复, 机器特征码, 分布式ID, Worker ID, 高可用, Schema命名规范, 构建器模式, 解耦, 并发控制, 线程安全]
created_date: 2025-05-11
updated_date: 2025-07-01
status: 草稿
version: 1.8
authors: [AI助手, Cline]
affected_features:
  - F004 # Commons UID Library
related_docs:
  - docs/common/middleware/integration/baidu-uid-generator-postgresql-implementation.md
  - docs/common/middleware/integration/snowflake-id-high-availability-design.md
  - ../design/commons-uid-library-design.md
  - ../design/instance-id-encryption-design.md
  - ../plan/uid-library-refactoring-plan.md
---

# 基于PostgreSQL的持久化实例ID及特征码恢复方案

## 1. 简介

在分布式系统中，许多组件（如分布式ID生成器）依赖于一个稳定且唯一的实例标识符（通常称为Worker ID）。当应用实例因硬件故障、迁移或重新部署导致其本地存储的标识符丢失时，如何恢复其原有身份，或为其分配合适的新身份，是一个关键问题。

本文档提出了一种基于PostgreSQL的持久化实例ID管理方案，结合本地存储和机器特征码比对，旨在实现实例身份的自动或半自动恢复。该方案特别关注在实例丢失本地ID信息后，如何通过比对当前运行环境的机器特征码与历史注册信息，尝试恢复其原始的持久化实例ID (`instance_unique_id`)，进而恢复其Worker ID。同时，方案也包含人工干预作为备选和最终保障。

此方案设计考虑了从当前局域网环境到未来可能的云环境的扩展性。

## 2. 核心组件

本方案主要包含以下核心组件：两个PostgreSQL表用于中央注册和分配，以及实例本地的ID存储。

> **重要说明**：根据PostgreSQL最佳实践（参考`schema-planning-guide.md`），我们将UID生成器相关的表放在专门的`infra_uid` Schema中，而不是默认的"public" Schema。这符合多Schema组织方式和`infra_<组件类型>`格式的命名规范，有助于更好地组织数据，提高安全性，并为未来的扩展提供灵活性。

### 2.1. `instance_registry` 表 (实例注册表)

*   **用途**: 记录和管理所有逻辑应用实例的持久身份信息。每个实例在首次启动（且本地无ID）时，会在此表注册，获取一个全局唯一的 `instance_unique_id`。此表还存储了实例注册时收集的机器特征码，用于后续的身份恢复。
*   **DDL设想**:

    ```sql
    CREATE TABLE infra_uid.instance_registry (
        instance_unique_id BIGSERIAL,       -- 持久实例ID (推荐数字型，也可考虑UUID)
        application_name VARCHAR(255) NOT NULL,         -- 应用/服务名称
        environment VARCHAR(100) NOT NULL DEFAULT 'default',    -- 部署环境 (e.g., "dev", "prod", "lan")
        instance_group VARCHAR(255) NOT NULL,                   -- 实例分组或逻辑单元名

        status VARCHAR(50) NOT NULL DEFAULT 'ACTIVE',           -- 实例状态 (ACTIVE, INACTIVE, DECOMMISSIONED)

        first_registered_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        last_seen_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,         -- 最后心跳或活跃时间
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        version INTEGER NOT NULL DEFAULT 0,

        -- 存储注册时或后续更新的机器特征码
        custom_metadata JSONB,

        CONSTRAINT pk_instance_registry PRIMARY KEY (instance_unique_id),
        CONSTRAINT ck_instance_registry_status CHECK (status IN ('ACTIVE', 'INACTIVE', 'DECOMMISSIONED'))
    );

    -- 索引
    CREATE INDEX idx_instance_registry_app_env_group ON infra_uid.instance_registry (application_name, environment, instance_group);
    CREATE INDEX idx_instance_registry_status ON infra_uid.instance_registry (status);
    -- 针对 custom_metadata 中的特定特征码创建GIN索引以加速查询，例如：
    -- CREATE INDEX idx_instance_registry_metadata_bios_uuid ON infra_uid.instance_registry USING GIN ((custom_metadata -> 'fingerprints' ->> 'bios_uuid'));
    -- CREATE INDEX idx_instance_registry_metadata_cloud_instance_id ON infra_uid.instance_registry USING GIN ((custom_metadata -> 'fingerprints' ->> 'cloud_instance_id'));

    -- 表和列注释
    COMMENT ON TABLE infra_uid.instance_registry IS '实例注册表：记录和管理所有逻辑应用实例的持久身份信息';
    COMMENT ON COLUMN infra_uid.instance_registry.instance_unique_id IS '持久实例ID：全局唯一的实例标识符';
    COMMENT ON COLUMN infra_uid.instance_registry.application_name IS '应用名称：应用或服务的名称，通常使用xkong.kv.cluster-id的值';
    COMMENT ON COLUMN infra_uid.instance_registry.environment IS '部署环境：如dev、prod、lan等';
    COMMENT ON COLUMN infra_uid.instance_registry.instance_group IS '实例分组：实例所属的逻辑分组或单元名称';
    COMMENT ON COLUMN infra_uid.instance_registry.status IS '实例状态：ACTIVE（活跃）、INACTIVE（非活跃）或DECOMMISSIONED（已退役）';
    COMMENT ON COLUMN infra_uid.instance_registry.first_registered_at IS '首次注册时间：实例首次注册的时间戳';
    COMMENT ON COLUMN infra_uid.instance_registry.last_seen_at IS '最后活跃时间：实例最后一次心跳或活跃的时间戳';
    COMMENT ON COLUMN infra_uid.instance_registry.updated_at IS '更新时间：记录最后一次更新的时间戳';
    COMMENT ON COLUMN infra_uid.instance_registry.version IS '版本号：用于乐观锁控制';
    COMMENT ON COLUMN infra_uid.instance_registry.custom_metadata IS '自定义元数据：存储实例注册时或后续更新的机器特征码，JSON格式';
    ```
*   **`custom_metadata` 字段结构示例**:
    ```json
    {
      "fingerprints_collected_at": "2025-05-11T21:30:00Z",
      "bios_uuid": "ABC-123",
      "system_serial_number": "SYS-XYZ",
      "mac_addresses": ["00:1A:2B:3C:4D:5E"],
      "os_hostname": "appserver01.lan",
      "cloud_provider": "LAN", // "AWS", "Azure", etc.
      "cloud_instance_id": null, // 云实例ID
      "k8s_pod_uid": null // K8s Pod UID
      // ... 其他可收集的特征码
    }
    ```

### 2.2. `worker_id_assignment` 表 (Worker ID 分配与租约表)

*   **用途**: 管理和分配有限的 `worker_id` 资源池（例如，百度UID的18位ID或Snowflake的10位ID）。它将一个活跃的 `instance_unique_id` 映射到一个具体的 `worker_id`，并通过租约机制管理分配的生命周期。
*   **DDL设想**:
    ```sql
    CREATE TABLE infra_uid.worker_id_assignment (
        worker_id INT,  -- Worker ID (值域取决于具体ID算法)

        assigned_instance_unique_id BIGINT, -- 当前占用此worker_id的持久实例ID
                                           -- 外键关联 instance_registry(instance_unique_id)

        assignment_status VARCHAR(50) NOT NULL DEFAULT 'AVAILABLE', -- 分配状态 (AVAILABLE, ASSIGNED, RESERVED, EXPIRED)

        assigned_at TIMESTAMP WITH TIME ZONE,
        lease_duration_seconds INT DEFAULT 300,   -- 租约时长（例如300秒 = 5分钟）
        lease_expires_at TIMESTAMP WITH TIME ZONE, -- 租约到期时间
        last_heartbeat_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        version INTEGER NOT NULL DEFAULT 0,

        CONSTRAINT pk_worker_id_assignment PRIMARY KEY (worker_id),
        CONSTRAINT uk_worker_id_assignment_instance_id UNIQUE (assigned_instance_unique_id),
        CONSTRAINT ck_worker_id_assignment_status CHECK (assignment_status IN ('AVAILABLE', 'ASSIGNED', 'RESERVED', 'EXPIRED')),
        CONSTRAINT fk_worker_id_assignment_instance_registry
            FOREIGN KEY(assigned_instance_unique_id)
            REFERENCES infra_uid.instance_registry(instance_unique_id)
            ON DELETE SET NULL -- 如果instance_registry中的实例被删除，其占用的worker_id应被释放
    );

    -- 索引
    CREATE INDEX idx_worker_id_assignment_instance_id ON infra_uid.worker_id_assignment (assigned_instance_unique_id);
    CREATE INDEX idx_worker_id_assignment_status_expires ON infra_uid.worker_id_assignment (assignment_status, lease_expires_at);

    -- 表和列注释
    COMMENT ON TABLE infra_uid.worker_id_assignment IS 'Worker ID分配与租约表：管理和分配有限的worker_id资源池，并通过租约机制管理分配的生命周期';
    COMMENT ON COLUMN infra_uid.worker_id_assignment.worker_id IS 'Worker ID：分布式ID生成算法使用的工作机器ID，值域取决于具体ID算法';
    COMMENT ON COLUMN infra_uid.worker_id_assignment.assigned_instance_unique_id IS '分配的实例ID：当前占用此worker_id的持久实例ID，外键关联instance_registry表';
    COMMENT ON COLUMN infra_uid.worker_id_assignment.assignment_status IS '分配状态：AVAILABLE（可用）、ASSIGNED（已分配）、RESERVED（已预留）或EXPIRED（已过期）';
    COMMENT ON COLUMN infra_uid.worker_id_assignment.assigned_at IS '分配时间：Worker ID被分配给实例的时间戳';
    COMMENT ON COLUMN infra_uid.worker_id_assignment.lease_duration_seconds IS '租约时长：租约的有效期，单位为秒，默认300秒（5分钟）';
    COMMENT ON COLUMN infra_uid.worker_id_assignment.lease_expires_at IS '租约到期时间：当前租约的到期时间戳';
    COMMENT ON COLUMN infra_uid.worker_id_assignment.last_heartbeat_at IS '最后心跳时间：最后一次租约续约的时间戳';
    COMMENT ON COLUMN infra_uid.worker_id_assignment.created_at IS '创建时间：记录创建的时间戳';
    COMMENT ON COLUMN infra_uid.worker_id_assignment.updated_at IS '更新时间：记录最后一次更新的时间戳';
    COMMENT ON COLUMN infra_uid.worker_id_assignment.version IS '版本号：用于乐观锁控制';
    ```
    *   **注意**: `worker_id` 列的范围需要根据实际使用的分布式ID算法（如百度UID或Snowflake）来确定。此表可能需要预先填充所有可能的 `worker_id` 并将其 `assignment_status` 设置为 `AVAILABLE`。

### 2.3. 本地实例ID存储

*   **用途**: 应用实例在成功获取（或恢复）其 `instance_unique_id` 后，将其持久化存储在本地，以便后续启动时快速识别身份，避免频繁查询中央注册表。
*   **位置**: 通常是实例可访问的持久化文件系统上的一个特定文件（例如: `/var/data/app_name/instance.id` 或应用工作目录下的 `.instance_id`）。
*   **格式**: 可以是简单文本文件，仅包含 `instance_unique_id`；或者是JSON/Properties格式，可以存储额外信息（如获取时间）。
*   **安全性**: 此文件的读写权限应严格控制。
*   **加密存储**: 为提高安全性，特别是在云环境中，实例ID文件可以使用AES-256-GCM算法进行加密存储。
    *   **加密实现**: 使用外部工具包 `org.xkong.xkongkit.utils.EncryptionUtils` 提供的加密功能。
    *   **密钥管理**: 加密密钥存储在PostgreSQL数据库的 `infra_uid. encryption_key` 表中，与应用名称和环境关联。
    *   **兼容性**: 系统能够自动识别并处理加密和未加密的实例ID文件，确保平滑过渡。
    *   **配置选项**: 通过配置参数 `uid.instance.encryption.enabled` 控制是否启用加密功能（默认为false）。

### 2.4. 本地实例ID存储的并发控制

在多进程或多线程环境中，对本地实例ID文件的访问需要适当的并发控制。

1. **文件锁定机制**:
   - 在读取或写入实例ID文件时，使用文件锁（如Java的`FileLock`）确保独占访问
   - 实现超时机制，避免长时间等待锁定
   - 如果无法获取锁，记录警告并使用备选策略（如重试或回退到数据库查询）

2. **读写操作的原子性**:
   - 使用临时文件和重命名操作确保文件写入的原子性
   - 例如，先将新内容写入临时文件，然后原子性地重命名替换原文件
   - 这种方式可以防止在写入过程中发生崩溃导致文件损坏

3. **文件读写失败处理**:
   - 实现健壮的错误处理机制，包括重试逻辑和降级策略
   - 如果文件读取失败，尝试从数据库恢复实例ID
   - 如果文件写入失败，记录错误并在下次操作时重试

4. **多进程环境考虑**:
   - 在多进程共享同一实例ID文件的环境中（如应用集群），考虑使用更强的协调机制
   - 可以使用分布式锁或将实例ID存储在共享存储（如Redis）中
   - 确保所有进程使用相同的实例ID，避免ID冲突

## 3. 实例ID获取与Worker ID分配流程

### 3.1. 首次启动 (本地无 `instance_unique_id`)

1.  **收集当前机器特征码**: 应用实例启动时，执行特征码收集流程（详见4.2）。
2.  **尝试自动恢复身份**:
    *   使用收集到的特征码和业务标识（应用名、环境）查询 `instance_registry`，尝试匹配历史记录（详见4.3）。
    *   根据匹配结果执行决策分支（详见4.4）：
        *   **高置信度匹配成功**: 获得恢复的 `instance_unique_id`。将其存入本地。
        *   **模糊匹配/多重匹配**: 按策略处理（可能告警并转人工，或按策略选择，或放弃恢复）。
        *   **无匹配**: 进入全新注册流程。
3.  **全新实例注册 (若恢复失败或为真·新实例)**:
    *   向 `instance_registry` 表插入一条新记录，包含当前收集的机器特征码和业务标识。
    *   获取数据库返回的新的 `instance_unique_id`。
    *   将此新的 `instance_unique_id` 持久化到本地。
4.  **请求Worker ID**:
    *   使用已确定（恢复的或全新的）的 `instance_unique_id`，向 `worker_id_assignment` 表请求分配一个 `worker_id`。
    *   此过程涉及查找可用 `worker_id`、建立关联、设置租约。
    *   获取 `worker_id` 后，初始化分布式ID生成器。
5.  **启动租约续约任务**: 启动后台任务，在租约到期前定期向 `worker_id_assignment` 表续约。

### 3.2. 后续启动 (本地已存在 `instance_unique_id`)

1.  从本地存储读取 `instance_unique_id`。
2.  （可选，但推荐）向 `instance_registry` 发送一次"心跳"或元数据更新（例如更新 `last_seen_at` 和当前机器特征码，如果发生变化）。这有助于保持注册表信息的时效性。
3.  使用此 `instance_unique_id` 请求/确认 `worker_id` 分配和租约（流程同3.1.4 和 3.1.5）。

### 3.3. Worker ID 租约续约

实例需要定期（例如，租约时长的1/2或2/3处）向 `worker_id_assignment` 表发送续约请求，提供其 `instance_unique_id` 和当前持有的 `worker_id`。服务验证后延长 `lease_expires_at`。如果续约失败（例如，`worker_id` 已被回收或实例身份验证失败），实例应停止ID生成并尝试重新获取 `worker_id`。

#### 3.3.1. 续约的并发控制

1. **续约算法**:
   ```sql
   -- 使用事务和条件更新确保原子性和并发安全
   BEGIN TRANSACTION;

   -- 条件更新租约，确保只有当前实例能续约其Worker ID
   UPDATE infra_uid.worker_id_assignment
   SET lease_expires_at = CURRENT_TIMESTAMP + (? || ' seconds')::INTERVAL,
       last_heartbeat_at = CURRENT_TIMESTAMP,
       updated_at = CURRENT_TIMESTAMP,
       version = version + 1
   WHERE worker_id = ?
     AND assigned_instance_unique_id = ?
     AND assignment_status = 'ASSIGNED';

   -- 检查更新是否成功（影响的行数）
   -- 如果返回0，表示续约失败

   COMMIT;
   ```

2. **续约失败处理**:
   - 如果续约失败（条件不满足或数据库错误），实例将立即停止生成ID
   - 实例将尝试重新获取Worker ID（可能是相同的ID或新的ID）
   - 实现指数退避重试策略，避免在短时间内频繁请求数据库
   - 记录详细的错误日志，包括失败原因和当前状态

3. **租约监控**:
   - 实例应定期检查自己的租约状态，确保在租约到期前有足够时间续约
   - 推荐在租约时长的1/3处开始续约，以留出足够的缓冲时间
   - 如果连续多次续约失败，实例应进入安全模式，停止所有ID生成操作

4. **租约过期检测**:
   - 实例应实现自我检测机制，定期验证自己的租约是否有效
   - 如果发现自己的租约已过期，立即停止生成ID并尝试重新获取
   - 这种自我检测机制是防止"僵尸实例"（租约过期但仍在生成ID）的重要保障

### 3.4. Worker ID分配的并发控制

为确保在高并发环境下Worker ID分配的安全性，系统采用以下机制：

1. **数据库事务与锁**:
   - 使用数据库事务确保Worker ID分配的原子性
   - 在分配Worker ID时使用行级锁（SELECT FOR UPDATE）锁定目标记录
   - 使用乐观锁机制（版本号或条件更新）防止并发更新冲突

2. **分配算法**:
   ```sql
   -- 使用事务和行锁保证原子性
   BEGIN TRANSACTION;

   -- 查找可用的Worker ID并锁定
   SELECT worker_id FROM infra_uid.worker_id_assignment
   WHERE assignment_status = 'AVAILABLE'
   ORDER BY worker_id LIMIT 1
   FOR UPDATE;

   -- 更新分配状态
   UPDATE infra_uid.worker_id_assignment
   SET assigned_instance_unique_id = ?,
       assignment_status = 'ASSIGNED',
       assigned_at = CURRENT_TIMESTAMP,
       lease_expires_at = CURRENT_TIMESTAMP + (? || ' seconds')::INTERVAL,
       last_heartbeat_at = CURRENT_TIMESTAMP,
       updated_at = CURRENT_TIMESTAMP,
       version = version + 1
   WHERE worker_id = ? AND assignment_status = 'AVAILABLE';

   COMMIT;
   ```

3. **冲突处理**:
   - 如果多个实例同时请求，数据库事务和锁机制确保只有一个实例能成功获取特定的Worker ID
   - 其他实例会获取到不同的Worker ID或需要等待
   - 如果没有可用的Worker ID，实例将进入等待状态并定期重试，同时记录警告日志

4. **分配失败处理**:
   - 如果分配过程中发生异常，事务会回滚，确保数据一致性
   - 实例将进行有限次数的重试，使用指数退避策略
   - 达到最大重试次数后，应用将无法启动并记录错误日志

## 4. 自动实例身份恢复（通过机器特征码）

核心思想：当实例丢失本地 `instance_unique_id` 时，通过比对当前机器的特征码与 `instance_registry` 中历史记录的特征码，尝试自动找回其原始身份。

### 4.1. `instance_registry.custom_metadata` 中存储的特征码

已在 2.1 节中详细描述。关键是多样性和对不同环境的适应性。
*   **局域网核心特征码**: BIOS UUID, System Serial, Baseboard Serial, MAC Addresses.
*   **云环境核心特征码**: Cloud Provider Specific Instance ID, BIOS UUID (if stable in cloud VM).
*   其他辅助特征码: OS Hostname, K8s Pod UID (if applicable).

### 4.2. 应用实例启动时收集特征码的流程

1.  **定义收集范围**: 根据当前环境（LAN/Cloud/K8s）确定需要收集哪些特征码。
2.  **执行收集**:
    *   **OS层面**: 使用如 `dmidecode`, `ip link`, `hostname` 等命令 (Linux) 或 WMI/PowerShell (Windows)。
    *   **云层面**: 安全地访问云提供商的实例元数据服务 (IMDS)。例如 AWS: `curl http://***************/latest/meta-data/instance-id`。
    *   **K8s层面**: 通过Downward API读取Pod信息。
3.  **数据清洗与格式化**: 统一输出格式，例如，MAC地址统一为大写，去除分隔符。
4.  **错误处理**: 对无法获取的特征码进行标记（例如 `null` 或特定错误码），不应因单个特征码获取失败而中断整个收集过程。
5.  **组装**: 将收集到的信息组装成JSON对象，包含收集时间戳。

### 4.3. 匹配查询接口或逻辑（评分和置信度判断）

此逻辑通常在服务端实现，接收新实例上报的特征码和业务标识，查询 `instance_registry`。

1.  **候选筛选**: 使用 `application_name` (通常使用 `xkong.kv.cluster-id` 的值), `environment` 等业务标识初步过滤 `instance_registry` 中的记录。
2.  **特征码加权匹配**:
    *   **权重表 (示例)**:
        *   `cloud_instance_id`: 100 (如果当前在云环境且能获取到)
        *   `bios_uuid`: 80
        *   `k8s_pod_uid` (如果匹配且应用期望在同一Pod重启): 70
        *   `system_serial_number`: 60
        *   `mac_addresses` (至少一个匹配): 40 (多个匹配可适当加分)
        *   `os_hostname`: 20
    *   **评分算法**: 对每个候选历史记录，遍历其存储的特征码，与新实例上报的特征码进行比较。
        *   匹配成功则加上对应权重。
        *   对于列表型特征码（如MAC地址），可定义部分匹配的得分。
        *   考虑特征码是否存在：如果新旧双方都有某个特征码才进行比较。
    *   计算每个候选历史记录的总得分。
3.  **置信度与决策阈值**:
    *   `HIGH_CONFIDENCE_THRESHOLD` (例如: 150分)
    *   `MINIMUM_ACCEPTABLE_SCORE` (例如: 70分)
4.  **查询结果**:
    *   找出得分最高的历史记录。
    *   **高置信度唯一匹配**: 如果最高分 >= `HIGH_CONFIDENCE_THRESHOLD`，且它是唯一的最高分（或者远高于次高分）。
    *   **低置信度/模糊匹配**: 如果最高分 < `HIGH_CONFIDENCE_THRESHOLD` 但 >= `MINIMUM_ACCEPTABLE_SCORE`，或者存在多个得分相近的较高分记录。
    *   **无匹配**: 如果最高分 < `MINIMUM_ACCEPTABLE_SCORE`。

### 4.4. 自动恢复成功、失败的处理分支

1.  **高置信度唯一匹配成功**:
    *   应用实例使用找回的 `instance_unique_id`。
    *   本地存储此ID。
    *   （可选）更新 `instance_registry` 中该记录的 `last_seen_at` 和 `custom_metadata` (用新的特征码覆盖或合并)。
    *   日志记录："自动恢复身份成功，`instance_unique_id` = X"。

2.  **低置信度/模糊匹配**:
    *   **策略1 (ALERT_AND_MANUAL)**: 应用启动失败或进入安全模式。发送详细告警给运维（包含当前特征码、潜在匹配列表及其分数）。等待人工介入，通过"人工可改"机制指定ID。
    *   **策略2 (ALERT_AND_NEW)**: 放弃恢复尝试，按无匹配处理（注册全新ID）。但告警中应包含潜在匹配信息，供事后分析。
    *   **策略3 (ALERT_AUTO_WITH_TIMEOUT)**: 发出告警后等待一定时间（通过`uid.instance.recovery.timeout-seconds`参数配置，默认300秒）让人工干预，如果超时无人响应则自动选择最佳匹配或创建新实例。这种策略平衡了安全性和自动恢复的需求。
        *   **实现细节**:
            *   启动时检测到低置信度/模糊匹配，记录匹配结果和时间戳。
            *   发送告警（日志、邮件、监控系统等）。
            *   进入等待状态，定期检查是否有人工干预（如通过`instanceIdOverride`参数）。
            *   如果在超时时间内检测到人工干预，使用人工指定的ID。
            *   如果超时无人响应，自动选择得分最高的匹配（如果得分高于`MINIMUM_ACCEPTABLE_SCORE`）或创建新实例。
            *   记录最终决策和结果。
        *   **优势**:
            *   减少人工干预需求，提高系统可用性。
            *   保留人工干预的可能性，确保关键场景下的安全性。
            *   通过超时参数可灵活调整自动化程度。
    *   日志记录："自动恢复身份失败：匹配结果模糊/低置信度。当前特征码: {...}, 潜在匹配: [...]"。

3.  **无匹配**:
    *   按全新实例处理：向 `instance_registry` 注册，获取新 `instance_unique_id`。
    *   日志记录："自动恢复身份失败：未找到匹配项。注册为新实例。"

### 4.5. 实例身份恢复的并发控制

在多个实例同时进行身份恢复时，需要确保系统的一致性和安全性。

1. **特征码匹配的并发处理**:
   - 特征码匹配是一个只读操作，不存在并发写入冲突
   - 但多个实例可能同时匹配到同一个历史记录，系统需要确保它们不会同时使用相同的`instance_unique_id`

2. **新实例注册的并发控制**:
   ```sql
   -- 使用事务和唯一约束确保并发安全
   BEGIN TRANSACTION;

   -- 插入新记录，依赖数据库唯一约束防止重复
   INSERT INTO infra_uid.instance_registry
   (application_name, environment, instance_group, status, custom_metadata)
   VALUES (?, ?, ?, 'ACTIVE', ?::jsonb)
   RETURNING instance_unique_id;

   COMMIT;
   ```

3. **恢复冲突处理**:
   - 如果多个实例同时尝试恢复同一个身份，Worker ID分配阶段的并发控制会确保只有一个实例能成功获取Worker ID
   - 其他实例会在Worker ID分配阶段失败，然后回退到注册新实例或等待人工干预
   - 系统记录详细的冲突日志，包括冲突的实例信息和特征码匹配分数

4. **人工干预时的并发控制**:
   - 当需要人工干预时，系统应提供锁定机制，防止多个管理员同时操作同一个实例
   - 人工指定的实例ID会被优先使用，覆盖自动恢复的结果
   - 所有人工操作都会被详细记录，包括操作者、时间和具体操作

## 5. 人工ID Override/Recovery (Fallback)

此机制作为自动恢复的补充和最终保障。

*   **触发场景**: 自动恢复失败且策略要求人工介入；灾难恢复；有计划的实例迁移需保持身份。
*   **实现方式**:
    *   **恢复文件**: 运维人员在实例启动前，在约定路径放置一个包含预定 `instance_unique_id` 的文件 (例如 `.force_instance_id`)。应用启动时最高优先级检查此文件。
    *   **启动参数/环境变量**: 通过启动参数 `-Dinstance.id.override=xxx` 或环境变量 `INSTANCE_ID_OVERRIDE=xxx` 注入。
*   **流程**: 应用获取到这个强制指定的ID后，直接使用它，并更新本地存储。后续流程（如worker ID获取）照常。
*   **安全**: 此操作权限应严格控制，并有完整审计。

## 6. 安全考虑

*   **数据库访问**: 应用实例对PG的访问权限应最小化。特征码匹配逻辑最好在受控的服务端API中执行。
*   **本地ID文件**: 保护本地 `instance.id` 文件不被未授权读写。
    *   **加密保护**: 使用AES-256-GCM算法对实例ID文件进行加密，防止未授权访问和篡改。
    *   **文件权限**: 设置文件为仅所有者可读写（Linux系统上使用`chmod 600`）。
*   **密钥管理**:
    *   **密钥存储**: 加密密钥存储在PostgreSQL数据库的 `infra_uid.encryption_key` 表中，应严格限制对此表的访问权限。
    *   **密钥传输**: 确保数据库连接使用TLS/SSL加密，避免在日志中打印密钥信息。
    *   **密钥轮换**: 实现密钥版本管理，支持定期更换加密密钥。
*   **特征码收集**: 确保从可信来源（如OS内核、云IMDS）获取特征码，防止注入。
*   **人工恢复通道**: 严格控制权限，所有操作必须审计。
*   **通信安全**: 应用与PG或管理服务之间的通信应加密。
*   **解耦设计**: 通过构建器模式和参数传递，确保UID库与KV参数服务完全解耦，避免不必要的依赖和潜在的安全风险。

## 7. LAN到Cloud的扩展性

*   **特征码适配**:
    *   **LAN**: 依赖BIOS UUID, MAC等OS层信息。
    *   **Cloud**: 优先使用云提供商的Instance ID等稳定标识，IMDS是关键。
    *   收集逻辑和匹配权重需根据环境调整。`custom_metadata` 的设计已考虑存储多种特征码。
*   **部署差异**: 云环境实例生命周期更动态，自动恢复机制更为重要。
*   **网络**: 云环境网络配置（VPC, 安全组）需确保实例能访问PG和IMDS。
*   **安全性**: 云环境中应考虑更严格的安全措施，如启用实例ID文件加密和密钥轮换。

## 8. 结论

本方案通过结合PostgreSQL中央注册、本地ID持久化以及基于机器特征码的自动身份恢复机制，为分布式应用实例提供了一个相对健壮和灵活的持久身份管理方案。它旨在最大程度地减少因硬件故障或环境变化导致ID丢失后的人工干预，同时通过分层决策和人工备选通道来处理复杂和异常情况。方案的设计兼顾了当前局域网环境的需求和未来向云环境迁移的扩展性。

通过构建器模式和参数传递，确保UID库与KV参数服务完全解耦，提高了系统的灵活性和可维护性。同时，通过明确的验证职责分配，避免了重复验证，提高了系统的效率和可靠性。

## 9. 变更历史

| 版本 | 日期       | 变更内容 | 变更人      |
|------|------------|----------|-------------|
| 1.8  | 2025-07-01 | 改进表DDL：添加显式命名的主键约束和唯一约束，添加表和列的注释，符合PostgreSQL开发规范 | AI助手 |
| 1.7  | 2025-06-25 | 增强并发安全性：添加Worker ID分配、租约续约、实例身份恢复和本地文件操作的并发控制详细说明 | AI助手 |
| 1.6  | 2025-06-20 | 重构设计：添加构建器模式、明确验证职责、完全解耦KV参数服务、更新安全考虑部分 | AI助手 |
| 1.5  | 2025-06-13 | 添加ALERT_AUTO_WITH_TIMEOUT恢复策略和超时参数 | AI助手 |
| 1.4  | 2025-06-09 | 添加实例ID文件加密存储功能，更新安全考虑部分 | AI助手 |
| 1.3  | 2025-06-05 | 更新Schema命名规范：将uid_generator改为infra_uid，符合infra_<组件类型>格式 | AI助手 |
| 1.2  | 2025-06-02 | 修改表DDL定义，明确指定使用专门的Schema | AI助手 |
| 1.1  | 2025-05-25 | 修改方案，使用已有的`xkong.kv.cluster-id`变量作为`application_name`，而不是引入新的`uid.instance.application-name`参数 | AI助手 |
| 1.0  | 2025-05-11 | 初始草稿   | AI助手, Cline |
