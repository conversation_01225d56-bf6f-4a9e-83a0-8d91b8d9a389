# V4.2方案：V2修改说明文档

## 1. 修改概述

**【注意：文档内容已过时】**
**本文档描述的是V2设计阶段的初步构想，其核心“只改界面，不改架构”的原则已被后续的开发实践所改变。真实的V4.2架构演进对前后端都进行了重大重构（如用HTTP轮询替代WebSocket，简化核心流程等）。本文档仅供历史参考。**

本文档说明了V2方案相对于原始设计的修改内容，主要聚焦于**界面交互设计**的改进，同时保持**核心架构**不变。

## 2. 核心修改理念

### 2.1 虚拟项目经理角色定位
- **原始设计**: 系统被描述为"治理引擎"、"用户控制台"
- **V2修改**: 将系统重新定位为"虚拟项目经理"，使用项目经理的比喻让系统管理更形象
- **设计目标**: 让用户感觉是在与一个专业的项目经理进行交互，而不是与冷冰冰的技术系统

### 2.2 界面交互用语优化
- **原始设计**: 使用技术化术语，如"启动治理引擎"、"原子约束审查器"
- **V2修改**: 使用项目经理工作语言，如"开始项目检测"、"项目约束审查"
- **设计目标**: 降低技术门槛，提高用户体验

## 3. 具体修改内容

### 3.1 九宫格界面设计 (03-九宫格交互界面设计-V2.md)
**主要修改**:
- 将"用户控制台"改为"用户输入区"
- 将"启动治理引擎"按钮改为"检查"、"治理"、"生成代码"三个按钮
- 将"引擎实时思想链"改为"项目经理决策日志"
- 将"文档健康报告"改为"项目风险评估"
- 将"全局知识库可视化"改为"项目知识库"
- 明确要**新建HTML文件**，而不是修改现有的九宫格代码

**文件结构**:
- `project_manager_v2.html` - 新建界面主HTML结构
- `project_manager_v2_style.css` - 新建界面样式
- `project_manager_v2_app.js` - 新建前端交互逻辑

### 3.2 总体架构设计 (1-总体架构设计-V2.md)
**主要修改**:
- 将"人机交互层"改为"虚拟项目经理交互层"
- 将"Web界面"改为"用户输入区"
- 将"启动治理引擎"按钮改为"开始项目检测"按钮
- 保持核心架构和技术实现完全不变

### 3.3 实施计划 (5-实施计划与里程碑-V2.md)
**主要修改**:
- 更新文件路径，使用`project_manager_v2.html`等新文件名
- 将"Web界面"改为"用户输入区"
- 将"启动治理引擎"按钮改为"开始项目检测"按钮
- 保持实施计划和技术选型不变

### 3.4 其他核心架构文档
**主要修改**:
- 在文档开头添加说明，明确文档性质为"核心架构文档"
- 明确界面交互设计已在《03-九宫格交互界面设计-V2.md》中单独定义
- 保持核心架构内容完全不变

## 4. 保持不变的内容

### 4.1 核心架构
- V2的"四阶段治理流程"完全不变
- `AtomicConstraint`等核心数据模型完全不变
- 后端API接口和WebSocket通信完全不变
- 算法实现和业务逻辑完全不变
- `DocumentHealthReport`输出位置与v1检查.py保持一致（文档目录下的`检查修改报告提示词.md`）

### 4.2 技术实现
- Python后端架构完全不变
- 数据库设计和数据流完全不变
- AI模型调用和算法逻辑完全不变
- 系统性能和可靠性要求完全不变

## 5. 修改原则

### 5.1 只修改界面交互
- 只修改用户看到的界面文本和按钮
- 只修改前端HTML、CSS、JavaScript文件
- 不修改任何后端代码和核心逻辑

### 5.2 保持核心架构不变
- 所有核心架构文档的技术内容保持不变
- 所有数据模型和接口定义保持不变
- 所有算法和业务逻辑保持不变

### 5.3 新建不修改
- 明确要新建HTML文件，不修改现有的九宫格代码
- 新建的文件使用`project_manager_v2`命名，更符合虚拟项目经理的设计理念
- 保持现有系统的稳定性

## 6. 实施建议

### 6.1 开发顺序
1. 先完成核心架构文档的修改（已完成）
2. 新建`project_manager_v2.html`等前端文件
3. 实现用户输入区交互界面
4. 测试界面与后端API的对接

### 6.2 测试重点
- 验证界面交互是否符合用户输入区的设计理念
- 验证前端与后端API的通信是否正常
- 验证WebSocket实时通信是否正常工作
- 验证核心功能是否完全不受影响

## 7. 输出位置一致性说明

### 7.1 与v1检查.py的输出位置保持一致
为了确保v2系统与v1系统的兼容性和一致性，`DocumentHealthReport`的输出位置完全遵循v1检查.py的规范：

**输出文件**: `检查修改报告提示词.md`
**输出位置**: 目标设计文档目录下（与用户输入的目标设计文档目录相同）
**文件格式**: Markdown格式，包含完整的检查报告和修复建议

### 7.1.1 详细交互流程
1. **用户在区域8（用户输入区）输入目标设计文档的目录路径**
2. **点击"检查"按钮进行扫描**
3. **系统执行V2的"阶段零"流程，对目标设计文档进行分析**
4. **扫描完成后，输出《检查修改报告提示词.md》到该目标设计文档的目录**
5. **同时通过WebSocket实时更新前端界面的其他区域**
6. **用户可以在区域3（项目风险评估）中查看报告文件链接**

### 7.2 输出内容结构
v2的`DocumentHealthReport`输出内容与v1检查.py保持一致：
- 文档完整性评分
- 关键问题列表
- 自动修复建议
- 人工干预要求
- AI提示词内容

### 7.3 双重输出机制
v2系统采用双重输出机制：
1. **文件输出**: 保存到目标设计文档目录下的`检查修改报告提示词.md`
2. **实时推送**: 通过WebSocket推送到前端界面实时显示

这种设计确保了：
- 与v1系统的完全兼容
- 用户可以通过文件系统访问历史报告
- 前端界面提供实时交互体验

## 8. 总结

V2的修改主要集中在**界面交互设计**层面，通过引入"虚拟项目经理"的概念，让系统更加用户友好。同时，我们严格保持了**核心架构**的完整性，确保系统的技术实力和可靠性不受影响。

**特别强调**: v2的`DocumentHealthReport`输出位置与v1检查.py完全一致，确保了系统的向后兼容性和用户体验的一致性。

这种设计既满足了用户体验的需求，又保证了系统的技术先进性，是一个平衡的解决方案。
