#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAP方法优化上限测试器
验证不同CAP方法的优化能力天花板，基于LogicDepthDetector进行科学评估

研究目标：
1. 测试不同CAP方法的优化上限
2. 找到模型在特定任务下的逻辑深度天花板
3. 验证LogicDepthDetector作为综合能力测试工具的有效性
4. 为CAP方法选择提供科学依据

内聚设计：不依赖任何第三方库，纯Python实现
作者：AI助手
日期：2025-01-09
"""

import json
import re
import time
import urllib.request
import urllib.parse
from datetime import datetime
from typing import Dict, List, Any, Tuple

# ==================== 配置信息 ====================
API_CONFIG = {
    "url": "https://api.gmi-serving.com/v1/chat/completions",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjA4ZDMxOTBjLWNjNzItNDc4ZS1hOGYwLTY3NmEwMGY1MDY2ZCIsInR5cGUiOiJpZV9tb2RlbCJ9.q7zRm2BAySo2zOi2QL5m6ukgmbhMZm2Ig4ITmJ3ZsM8",
    "models": [
        "deepseek-ai/DeepSeek-V3-0324",
        "deepseek-ai/DeepSeek-R1-0528"
    ]
}

# ==================== 测试任务配置 ====================
TEST_TASKS = [
    {
        "id": "complex_architecture",
        "name": "复杂架构设计",
        "base_prompt": """设计一个支持每秒10万请求的高并发微服务架构，要求：
1. 99.99%可用性保障
2. 成本控制在合理范围
3. 技术债务最小化
4. 支持快速扩展
请提供完整的技术方案、架构图说明和实施计划。""",
        "expected_complexity": "high"
    },
    {
        "id": "logic_reasoning",
        "name": "逻辑推理分析", 
        "base_prompt": """分析以下逻辑问题：
在一个分布式系统中，如果服务A依赖服务B，服务B依赖服务C，而服务C在某些情况下需要调用服务A，
这种循环依赖在什么条件下是安全的？什么条件下会导致系统崩溃？
请提供严格的逻辑分析和数学证明。""",
        "expected_complexity": "high"
    },
    {
        "id": "innovation_design",
        "name": "创新方案设计",
        "base_prompt": """设计一个革命性的AI代码生成系统，要求：
1. 超越现有GPT/Claude的代码生成能力
2. 具备自我学习和优化能力
3. 支持多种编程语言和框架
4. 确保代码质量和安全性
请从第一性原理出发，提出创新的技术路径。""",
        "expected_complexity": "very_high"
    }
]

# ==================== LogicDepthDetector ====================
class LogicDepthDetector:
    """逻辑深度检测器 - 作为CAP优化效果的综合评估工具"""
    
    def __init__(self):
        self.weights = {
            "reasoning_depth": 0.35,    # 推理深度权重
            "logical_structure": 0.25,  # 逻辑结构权重
            "concept_complexity": 0.20, # 概念复杂度权重
            "innovation_level": 0.20    # 创新水平权重
        }
    
    def detect_logic_depth(self, content: str) -> Dict[str, Any]:
        """检测内容的逻辑深度，返回综合评估结果"""
        
        # 1. 推理深度分析
        reasoning_result = self._analyze_reasoning_depth(content)
        
        # 2. 逻辑结构分析
        structure_result = self._analyze_logical_structure(content)
        
        # 3. 概念复杂度分析
        complexity_result = self._analyze_concept_complexity(content)
        
        # 4. 创新水平分析
        innovation_result = self._analyze_innovation_level(content)
        
        # 5. 综合评分计算
        overall_score = (
            reasoning_result["score"] * self.weights["reasoning_depth"] +
            structure_result["score"] * self.weights["logical_structure"] +
            complexity_result["score"] * self.weights["concept_complexity"] +
            innovation_result["score"] * self.weights["innovation_level"]
        )
        
        return {
            "overall_score": overall_score,
            "dimension_scores": {
                "reasoning_depth": reasoning_result["score"],
                "logical_structure": structure_result["score"],
                "concept_complexity": complexity_result["score"],
                "innovation_level": innovation_result["score"]
            },
            "detailed_analysis": {
                "reasoning": reasoning_result,
                "structure": structure_result,
                "complexity": complexity_result,
                "innovation": innovation_result
            },
            "quality_grade": self._calculate_quality_grade(overall_score),
            "optimization_potential": self._estimate_optimization_potential(overall_score)
        }
    
    def _analyze_reasoning_depth(self, content: str) -> Dict[str, Any]:
        """分析推理深度"""
        depth_patterns = {
            "因果推理": [r'因为.*所以', r'由于.*导致', r'基于.*可以'],
            "层次分析": [r'首先.*其次.*最后', r'第一.*第二.*第三', r'初步.*深入.*最终'],
            "对比论证": [r'相比.*而言', r'与.*不同', r'优于.*在于'],
            "假设验证": [r'假设.*那么', r'如果.*则', r'假定.*结果'],
            "归纳演绎": [r'综上所述', r'总结.*规律', r'可以得出']
        }
        
        detected_patterns = []
        total_score = 0
        
        for category, patterns in depth_patterns.items():
            count = sum(1 for pattern in patterns if re.search(pattern, content))
            if count > 0:
                detected_patterns.append(f"{category}: {count}个")
                total_score += count * 12  # 每个模式12分
        
        # 推理链长度因子
        reasoning_chain_length = len(re.findall(r'[因由基].*?[所导可]', content))
        chain_score = min(reasoning_chain_length * 8, 40)
        total_score += chain_score
        
        final_score = min(total_score, 100)
        
        return {
            "score": final_score,
            "patterns_detected": detected_patterns,
            "reasoning_chain_length": reasoning_chain_length,
            "analysis": f"检测到{len(detected_patterns)}类推理模式，推理链长度{reasoning_chain_length}"
        }
    
    def _analyze_logical_structure(self, content: str) -> Dict[str, Any]:
        """分析逻辑结构"""
        structure_indicators = {
            "结构化标记": [r'\d+\.', r'[一二三四五六七八九十]+、', r'[ABCDEFG]\.', r'##', r'###'],
            "逻辑连接词": [r'然而', r'但是', r'因此', r'所以', r'另外', r'此外', r'同时'],
            "论证结构": [r'论点.*论据', r'前提.*结论', r'假设.*验证'],
            "层次递进": [r'进一步', r'更深层次', r'深入分析', r'具体而言']
        }
        
        structure_score = 0
        detected_structures = []
        
        for category, patterns in structure_indicators.items():
            count = sum(1 for pattern in patterns if re.search(pattern, content))
            if count > 0:
                detected_structures.append(f"{category}: {count}个")
                structure_score += count * 10
        
        # 段落结构分析
        paragraphs = content.split('\n\n')
        paragraph_score = min(len(paragraphs) * 5, 30)
        structure_score += paragraph_score
        
        final_score = min(structure_score, 100)
        
        return {
            "score": final_score,
            "structures_detected": detected_structures,
            "paragraph_count": len(paragraphs),
            "analysis": f"检测到{len(detected_structures)}类结构模式，{len(paragraphs)}个段落"
        }
    
    def _analyze_concept_complexity(self, content: str) -> Dict[str, Any]:
        """分析概念复杂度"""
        complexity_indicators = {
            "技术概念": [r'架构', r'算法', r'协议', r'框架', r'模式', r'机制'],
            "抽象概念": [r'原理', r'本质', r'规律', r'模型', r'理论', r'方法论'],
            "系统概念": [r'系统', r'平台', r'生态', r'环境', r'基础设施'],
            "创新概念": [r'创新', r'突破', r'革命', r'颠覆', r'前沿', r'先进']
        }
        
        concept_score = 0
        detected_concepts = []
        
        for category, patterns in complexity_indicators.items():
            count = sum(1 for pattern in patterns if re.search(pattern, content))
            if count > 0:
                detected_concepts.append(f"{category}: {count}个")
                concept_score += count * 8
        
        # 专业术语密度
        content_length = len(content)
        term_density = sum(len(concepts) for concepts in detected_concepts) / max(content_length, 1) * 1000
        density_score = min(term_density * 20, 40)
        concept_score += density_score
        
        final_score = min(concept_score, 100)
        
        return {
            "score": final_score,
            "concepts_detected": detected_concepts,
            "term_density": term_density,
            "analysis": f"检测到{len(detected_concepts)}类概念，术语密度{term_density:.2f}"
        }
    
    def _analyze_innovation_level(self, content: str) -> Dict[str, Any]:
        """分析创新水平"""
        innovation_patterns = {
            "创新思维": [r'重新定义', r'颠覆传统', r'突破.*限制', r'创新.*方法'],
            "前瞻视角": [r'未来.*趋势', r'前沿.*技术', r'下一代.*', r'革命性.*'],
            "跨界融合": [r'结合.*和.*', r'融合.*技术', r'跨领域.*', r'多学科.*'],
            "原创洞察": [r'独特.*见解', r'原创.*思路', r'新颖.*观点', r'创造性.*']
        }
        
        innovation_score = 0
        detected_innovations = []
        
        for category, patterns in innovation_patterns.items():
            count = sum(1 for pattern in patterns if re.search(pattern, content))
            if count > 0:
                detected_innovations.append(f"{category}: {count}个")
                innovation_score += count * 15
        
        # 创新表达的多样性
        unique_expressions = len(set(re.findall(r'[创新突破革命颠覆]\w*', content)))
        diversity_score = min(unique_expressions * 10, 40)
        innovation_score += diversity_score
        
        final_score = min(innovation_score, 100)
        
        return {
            "score": final_score,
            "innovations_detected": detected_innovations,
            "expression_diversity": unique_expressions,
            "analysis": f"检测到{len(detected_innovations)}类创新模式，表达多样性{unique_expressions}"
        }
    
    def _calculate_quality_grade(self, score: float) -> str:
        """计算质量等级"""
        if score >= 90:
            return "A+ (卓越)"
        elif score >= 80:
            return "A (优秀)"
        elif score >= 70:
            return "B (良好)"
        elif score >= 60:
            return "C (及格)"
        else:
            return "D (需改进)"
    
    def _estimate_optimization_potential(self, score: float) -> Dict[str, Any]:
        """估算优化潜力"""
        remaining_potential = 100 - score
        
        if remaining_potential <= 10:
            potential_level = "低"
            suggestions = ["微调表达方式", "增加细节描述"]
        elif remaining_potential <= 30:
            potential_level = "中"
            suggestions = ["深化分析层次", "增强逻辑连贯性", "丰富概念内容"]
        else:
            potential_level = "高"
            suggestions = ["重构思维框架", "引入创新视角", "系统性优化结构"]
        
        return {
            "potential_score": remaining_potential,
            "potential_level": potential_level,
            "optimization_suggestions": suggestions
        }

# ==================== CAP方法实现 ====================
class CAPMethodLibrary:
    """CAP方法库 - 实现不同的认知增强协议"""
    
    @staticmethod
    def cognitive_ascent_protocol(base_prompt: str) -> str:
        """认知提升协议 - 最深度的思考框架"""
        return f"""
<THOUGHT>
我需要运用Cognitive Ascent Protocol进行深度分析：

**第一性原理分解**：
- 这个任务的本质是什么？
- 有哪些不可违背的基础约束？
- 传统方案基于什么假设？

**多角色探索**：
- 作为架构师：如何设计最优结构？
- 作为哲学家：本质问题是什么？
- 作为创新者：如何突破现有限制？
- 作为批判者：现有方案有什么根本缺陷？

**递归自我批判**：
- 我的分析真的深入吗？
- 是否存在更根本的解决路径？
- 有没有被固有思维限制？

**涌现洞察综合**：
- 多个角色的洞察如何融合？
- 能否产生意想不到的解决方案？
</THOUGHT>

{base_prompt}

请运用最深度的认知提升协议，从多个专家角色视角进行分析，寻找突破性的解决方案。
"""

    @staticmethod
    def logos_inquisitor_protocol(base_prompt: str) -> str:
        """逻辑审议者协议 - 最严谨的逻辑分析框架"""
        return f"""
请按照逻辑审议者协议执行严格的逻辑分析：

**第一阶段：解构与框架定义**
- 精准复述问题核心：我的理解是...
- 识别所有显性和隐性约束：限制条件包括...
- 定义逻辑推理的成功标准：完美答案需要满足...

**第二阶段：穷举探索引擎**
- 生成所有可能的解决路径：
  A. [路径1及其逻辑推演]
  B. [路径2及其逻辑推演]
  C. [路径3及其逻辑推演]
- 逐一分析每个路径的逻辑链条
- 进行魔鬼代言人质询：最强反驳是...

**第三阶段：综合验证与收敛**
- 交叉验证所有路径
- 排除逻辑矛盾的方案
- 构建最终论证链条

**第四阶段：置信度评估**
- 给出95%置信度评估
- 识别剩余不确定性
- 提供备选方案

任务：{base_prompt}
"""

    @staticmethod
    def hidden_thinking_protocol(base_prompt: str) -> str:
        """隐藏思考显式答案协议 - 专业顾问模式"""
        return f"""
<thinking>
作为资深顾问，我需要为这个任务提供专业分析：

Step 1: 问题分析
- 核心挑战是什么？
- 有哪些关键约束条件？
- 成功的标准是什么？

Step 2: 方案生成
- 可行方案A：[分析优缺点]
- 可行方案B：[分析优缺点]
- 可行方案C：[分析优缺点]

Step 3: 风险评估
- 每个方案的主要风险
- 风险缓解策略
- 实施难度评估

Step 4: 最优选择
- 基于约束条件的最佳方案
- 实施路径和时间安排
- 关键成功因素

Self-check: 我的建议是否实用、可行、风险可控？
</thinking>

<answer>
【基于专业分析的最佳建议】
</answer>

任务：{base_prompt}
"""

    @staticmethod
    def r1_optimized_protocol(base_prompt: str) -> str:
        """R1模型优化协议 - 专门为R1模型设计的thinking增强"""
        return f"""
<thinking>
我需要对这个任务进行深度分析，充分利用R1模型的推理优势：

**第一层：问题解构分析**
- 核心问题识别：{base_prompt}
- 关键约束条件：[分析约束]
- 隐含假设挖掘：[识别假设]
- 成功标准定义：[明确目标]

**第二层：多路径探索**
- 路径A：[第一种可能性及其推理链]
- 路径B：[第二种可能性及其推理链]
- 路径C：[第三种可能性及其推理链]
- 穷举验证：[确保覆盖完整]

**第三层：批判性验证**
- 魔鬼代言人质询：[主动寻找反例]
- 逻辑一致性检查：[验证推理链]
- 边界条件测试：[极端情况分析]
- 不确定性评估：[识别风险点]

**第四层：综合优化**
- 方案比较权衡：[多维度对比]
- 最优路径选择：[基于证据决策]
- 实施可行性：[现实约束考虑]
- 风险缓解策略：[预防措施]

**第五层：元认知反思**
- 推理过程审查：[检查逻辑漏洞]
- 假设可靠性：[验证前提条件]
- 结论稳健性：[测试结论强度]
- 改进空间识别：[持续优化]
</thinking>

现在请基于以上深度思考框架，完成以下任务：

{base_prompt}

要求：
1. 充分利用你的推理能力，在thinking中展示完整的分析过程
2. 确保每个推理步骤都有充分的逻辑支撑
3. 主动寻找可能的反例和边界情况
4. 提供多种方案并进行比较
5. 给出置信度评估和不确定性分析
"""

# ==================== API客户端 ====================
class SimpleAPIClient:
    """简单API客户端，不依赖第三方库"""

    def __init__(self):
        self.api_url = API_CONFIG["url"]
        self.api_token = API_CONFIG["token"]
        self.models = API_CONFIG["models"]

    def call_api(self, model: str, prompt: str, max_retries: int = 2) -> Dict[str, Any]:
        """调用API获取响应，支持重试机制"""

        for attempt in range(max_retries + 1):
            if attempt > 0:
                print(f"🔄 第{attempt + 1}次尝试调用API...")
                time.sleep(5)

            try:
                # 构建请求数据
                data = {
                    "model": model,
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0.7,
                    "max_tokens": 4000  # 增加token限制以获得更完整的响应
                }

                json_data = json.dumps(data).encode('utf-8')

                req = urllib.request.Request(
                    self.api_url,
                    data=json_data,
                    headers={
                        'Authorization': f'Bearer {self.api_token}',
                        'Content-Type': 'application/json',
                        'User-Agent': 'CAP-Optimization-Tester/1.0'
                    }
                )

                # R1模型需要更长时间
                timeout = 300 if "R1" in model else 120
                print(f"⏱️ 调用{model}，超时时间: {timeout}秒")

                with urllib.request.urlopen(req, timeout=timeout) as response:
                    response_data = response.read().decode('utf-8')

                    if response.status == 200:
                        result = json.loads(response_data)

                        if "choices" in result and len(result["choices"]) > 0:
                            message = result["choices"][0]["message"]
                            content = message.get("content", "")
                            reasoning = message.get("reasoning_content") or ""

                            print(f"✅ API调用成功")
                            print(f"📝 响应内容长度: {len(content)} 字符")
                            print(f"🧠 推理内容长度: {len(reasoning)} 字符")

                            return {
                                "success": True,
                                "content": content,
                                "reasoning_content": reasoning,
                                "model": model,
                                "timestamp": datetime.now().isoformat(),
                                "token_usage": result.get("usage", {})
                            }
                        else:
                            return {
                                "success": False,
                                "error": "响应格式不正确",
                                "model": model
                            }
                    else:
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}",
                            "model": model
                        }

            except Exception as e:
                print(f"❌ 调用失败 (第{attempt + 1}次尝试): {str(e)}")
                if attempt < max_retries:
                    continue
                else:
                    return {
                        "success": False,
                        "error": f"所有{max_retries + 1}次尝试都失败: {str(e)}",
                        "model": model
                    }

        return {
            "success": False,
            "error": f"API调用失败，已重试{max_retries + 1}次",
            "model": model
        }

# ==================== CAP优化上限测试器 ====================
class CAPOptimizationLimitTester:
    """CAP方法优化上限测试器 - 核心测试引擎"""

    def __init__(self):
        self.logic_detector = LogicDepthDetector()
        self.api_client = SimpleAPIClient()
        self.cap_methods = {
            "baseline": None,  # 无优化基准
            "cognitive_ascent": CAPMethodLibrary.cognitive_ascent_protocol,
            "logos_inquisitor": CAPMethodLibrary.logos_inquisitor_protocol,
            "hidden_thinking": CAPMethodLibrary.hidden_thinking_protocol,
            "r1_optimized": CAPMethodLibrary.r1_optimized_protocol
        }

    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合CAP优化上限测试"""

        print("🚀 CAP方法优化上限测试器")
        print("=" * 80)
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 测试目标: 找到不同CAP方法的优化天花板")
        print(f"📊 评估工具: LogicDepthDetector综合能力测试")
        print()

        test_results = {
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "test_framework": "CAP优化上限测试器",
                "models_tested": API_CONFIG["models"],
                "cap_methods_tested": list(self.cap_methods.keys())
            },
            "task_results": {},
            "model_comparison": {},
            "cap_method_ranking": {},
            "optimization_limits": {}
        }

        # 对每个测试任务执行完整测试
        for task in TEST_TASKS:
            print(f"📋 测试任务: {task['name']} ({task['id']})")
            print("-" * 60)

            task_result = self._test_single_task(task)
            test_results["task_results"][task["id"]] = task_result

            print(f"✅ 任务 {task['name']} 测试完成")
            print()

        # 生成综合分析
        test_results["model_comparison"] = self._analyze_model_performance(test_results["task_results"])
        test_results["cap_method_ranking"] = self._rank_cap_methods(test_results["task_results"])
        test_results["optimization_limits"] = self._calculate_optimization_limits(test_results["task_results"])

        # 输出最终报告
        self._generate_final_report(test_results)

        return test_results

    def _test_single_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """测试单个任务的CAP优化效果"""

        task_results = {
            "task_info": task,
            "model_results": {}
        }

        # 对每个模型进行测试
        for model in API_CONFIG["models"]:
            print(f"🤖 测试模型: {model}")

            model_result = {
                "model": model,
                "cap_results": {},
                "optimization_analysis": {}
            }

            # 测试每种CAP方法
            for cap_name, cap_method in self.cap_methods.items():
                print(f"  📝 测试CAP方法: {cap_name}")

                # 生成提示词
                if cap_name == "baseline":
                    prompt = task["base_prompt"]
                else:
                    prompt = cap_method(task["base_prompt"])

                # 调用API
                api_result = self.api_client.call_api(model, prompt)

                if api_result["success"]:
                    # 选择分析内容
                    if "R1" in model and api_result.get("reasoning_content"):
                        analysis_content = api_result["reasoning_content"]
                        content_source = "reasoning_content"
                    else:
                        analysis_content = api_result["content"]
                        content_source = "main_content"

                    # LogicDepthDetector分析
                    logic_analysis = self.logic_detector.detect_logic_depth(analysis_content)

                    cap_result = {
                        "api_response": api_result,
                        "content_source": content_source,
                        "content_length": len(analysis_content),
                        "logic_analysis": logic_analysis,
                        "success": True
                    }

                    print(f"    📊 逻辑深度得分: {logic_analysis['overall_score']:.1f}")
                    print(f"    🏆 质量等级: {logic_analysis['quality_grade']}")

                else:
                    cap_result = {
                        "error": api_result["error"],
                        "success": False
                    }
                    print(f"    ❌ 测试失败: {api_result['error']}")

                model_result["cap_results"][cap_name] = cap_result
                time.sleep(2)  # 避免API限流

            # 分析该模型的优化效果
            model_result["optimization_analysis"] = self._analyze_model_optimization(model_result["cap_results"])
            task_results["model_results"][model] = model_result

        return task_results

    def _analyze_model_optimization(self, cap_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析单个模型的优化效果"""

        successful_results = {k: v for k, v in cap_results.items() if v.get("success", False)}

        if not successful_results:
            return {"error": "所有CAP方法都失败"}

        # 获取基准分数
        baseline_score = successful_results.get("baseline", {}).get("logic_analysis", {}).get("overall_score", 0)

        # 计算各CAP方法的优化效果
        optimization_effects = {}
        for cap_name, result in successful_results.items():
            if cap_name == "baseline":
                continue

            optimized_score = result["logic_analysis"]["overall_score"]
            optimization_gain = optimized_score - baseline_score
            optimization_ratio = (optimization_gain / baseline_score * 100) if baseline_score > 0 else 0

            optimization_effects[cap_name] = {
                "baseline_score": baseline_score,
                "optimized_score": optimized_score,
                "optimization_gain": optimization_gain,
                "optimization_ratio": optimization_ratio,
                "quality_grade": result["logic_analysis"]["quality_grade"]
            }

        # 找出最佳CAP方法
        if optimization_effects:
            best_cap = max(optimization_effects.keys(), key=lambda x: optimization_effects[x]["optimization_ratio"])
            optimization_ceiling = max(effect["optimized_score"] for effect in optimization_effects.values())
        else:
            best_cap = None
            optimization_ceiling = baseline_score

        return {
            "baseline_score": baseline_score,
            "optimization_effects": optimization_effects,
            "best_cap_method": best_cap,
            "optimization_ceiling": optimization_ceiling,
            "max_optimization_gain": optimization_ceiling - baseline_score,
            "max_optimization_ratio": ((optimization_ceiling - baseline_score) / baseline_score * 100) if baseline_score > 0 else 0
        }

    def _analyze_model_performance(self, task_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析不同模型的整体性能"""

        model_performance = {}

        for model in API_CONFIG["models"]:
            model_stats = {
                "total_tests": 0,
                "successful_tests": 0,
                "average_baseline": 0,
                "average_ceiling": 0,
                "average_optimization": 0,
                "best_cap_methods": []
            }

            baseline_scores = []
            ceiling_scores = []
            optimization_ratios = []
            best_caps = []

            for task_id, task_result in task_results.items():
                if model in task_result["model_results"]:
                    model_result = task_result["model_results"][model]
                    optimization_analysis = model_result.get("optimization_analysis", {})

                    if "error" not in optimization_analysis:
                        model_stats["total_tests"] += 1
                        model_stats["successful_tests"] += 1

                        baseline_scores.append(optimization_analysis["baseline_score"])
                        ceiling_scores.append(optimization_analysis["optimization_ceiling"])
                        optimization_ratios.append(optimization_analysis["max_optimization_ratio"])

                        if optimization_analysis["best_cap_method"]:
                            best_caps.append(optimization_analysis["best_cap_method"])
                    else:
                        model_stats["total_tests"] += 1

            if baseline_scores:
                model_stats["average_baseline"] = sum(baseline_scores) / len(baseline_scores)
                model_stats["average_ceiling"] = sum(ceiling_scores) / len(ceiling_scores)
                model_stats["average_optimization"] = sum(optimization_ratios) / len(optimization_ratios)

                # 统计最常用的最佳CAP方法
                from collections import Counter
                cap_counter = Counter(best_caps)
                model_stats["best_cap_methods"] = cap_counter.most_common(3)

            model_performance[model] = model_stats

        return model_performance

    def _rank_cap_methods(self, task_results: Dict[str, Any]) -> Dict[str, Any]:
        """对CAP方法进行排名"""

        cap_performance = {}

        # 初始化CAP方法统计
        for cap_name in self.cap_methods.keys():
            if cap_name != "baseline":
                cap_performance[cap_name] = {
                    "total_tests": 0,
                    "successful_tests": 0,
                    "total_optimization": 0,
                    "average_optimization": 0,
                    "best_performance_count": 0,
                    "task_performance": {}
                }

        # 收集所有测试数据
        for task_id, task_result in task_results.items():
            for model, model_result in task_result["model_results"].items():
                optimization_analysis = model_result.get("optimization_analysis", {})

                if "error" not in optimization_analysis:
                    best_cap = optimization_analysis.get("best_cap_method")
                    optimization_effects = optimization_analysis.get("optimization_effects", {})

                    # 统计最佳表现
                    if best_cap and best_cap in cap_performance:
                        cap_performance[best_cap]["best_performance_count"] += 1

                    # 统计各CAP方法的优化效果
                    for cap_name, effect in optimization_effects.items():
                        if cap_name in cap_performance:
                            cap_performance[cap_name]["total_tests"] += 1
                            cap_performance[cap_name]["successful_tests"] += 1
                            cap_performance[cap_name]["total_optimization"] += effect["optimization_ratio"]

                            # 记录任务级别的表现
                            if task_id not in cap_performance[cap_name]["task_performance"]:
                                cap_performance[cap_name]["task_performance"][task_id] = []
                            cap_performance[cap_name]["task_performance"][task_id].append(effect["optimization_ratio"])

        # 计算平均优化效果
        for cap_name, stats in cap_performance.items():
            if stats["successful_tests"] > 0:
                stats["average_optimization"] = stats["total_optimization"] / stats["successful_tests"]

        # 排名
        ranked_caps = sorted(cap_performance.items(),
                           key=lambda x: (x[1]["average_optimization"], x[1]["best_performance_count"]),
                           reverse=True)

        return {
            "cap_performance": cap_performance,
            "ranking": [{"rank": i+1, "cap_method": cap[0], "stats": cap[1]}
                       for i, cap in enumerate(ranked_caps)]
        }

    def _calculate_optimization_limits(self, task_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算优化上限分析"""

        limits_analysis = {
            "overall_limits": {},
            "task_specific_limits": {},
            "model_specific_limits": {},
            "theoretical_maximum": 0
        }

        all_ceilings = []
        all_baselines = []

        # 收集所有数据
        for task_id, task_result in task_results.items():
            task_limits = {
                "task_name": task_result["task_info"]["name"],
                "model_limits": {}
            }

            for model, model_result in task_result["model_results"].items():
                optimization_analysis = model_result.get("optimization_analysis", {})

                if "error" not in optimization_analysis:
                    ceiling = optimization_analysis["optimization_ceiling"]
                    baseline = optimization_analysis["baseline_score"]

                    all_ceilings.append(ceiling)
                    all_baselines.append(baseline)

                    task_limits["model_limits"][model] = {
                        "baseline": baseline,
                        "ceiling": ceiling,
                        "optimization_gain": ceiling - baseline,
                        "optimization_ratio": ((ceiling - baseline) / baseline * 100) if baseline > 0 else 0
                    }

            limits_analysis["task_specific_limits"][task_id] = task_limits

        # 计算整体统计
        if all_ceilings:
            limits_analysis["theoretical_maximum"] = max(all_ceilings)
            limits_analysis["overall_limits"] = {
                "max_ceiling": max(all_ceilings),
                "min_ceiling": min(all_ceilings),
                "average_ceiling": sum(all_ceilings) / len(all_ceilings),
                "max_baseline": max(all_baselines),
                "min_baseline": min(all_baselines),
                "average_baseline": sum(all_baselines) / len(all_baselines),
                "max_optimization_potential": max(all_ceilings) - min(all_baselines)
            }

        return limits_analysis

    def _generate_final_report(self, test_results: Dict[str, Any]) -> None:
        """生成最终测试报告"""

        print("\n" + "=" * 80)
        print("📊 CAP方法优化上限测试报告")
        print("=" * 80)

        # 1. 整体统计
        model_comparison = test_results["model_comparison"]
        cap_ranking = test_results["cap_method_ranking"]
        optimization_limits = test_results["optimization_limits"]

        print("\n🎯 整体测试统计:")
        print(f"   测试任务数: {len(test_results['task_results'])}")
        print(f"   测试模型数: {len(API_CONFIG['models'])}")
        print(f"   CAP方法数: {len(self.cap_methods) - 1}")  # 排除baseline

        # 2. 模型性能对比
        print("\n🤖 模型性能对比:")
        for model, stats in model_comparison.items():
            print(f"   {model}:")
            print(f"     成功率: {stats['successful_tests']}/{stats['total_tests']} ({stats['successful_tests']/stats['total_tests']*100:.1f}%)")
            print(f"     平均基准分: {stats['average_baseline']:.1f}")
            print(f"     平均天花板: {stats['average_ceiling']:.1f}")
            print(f"     平均优化幅度: {stats['average_optimization']:.1f}%")
            if stats['best_cap_methods']:
                best_cap = stats['best_cap_methods'][0]
                print(f"     最佳CAP方法: {best_cap[0]} (使用{best_cap[1]}次)")

        # 3. CAP方法排名
        print("\n🏆 CAP方法效果排名:")
        for rank_info in cap_ranking["ranking"]:
            rank = rank_info["rank"]
            cap_name = rank_info["cap_method"]
            stats = rank_info["stats"]
            print(f"   {rank}. {cap_name}:")
            print(f"      平均优化幅度: {stats['average_optimization']:.1f}%")
            print(f"      最佳表现次数: {stats['best_performance_count']}")
            print(f"      成功测试数: {stats['successful_tests']}")

        # 4. 优化上限分析
        print("\n📈 优化上限分析:")
        overall_limits = optimization_limits["overall_limits"]
        if overall_limits:
            print(f"   理论最高分: {overall_limits['max_ceiling']:.1f}")
            print(f"   平均天花板: {overall_limits['average_ceiling']:.1f}")
            print(f"   最大优化潜力: {overall_limits['max_optimization_potential']:.1f}分")
            print(f"   基准分范围: {overall_limits['min_baseline']:.1f} - {overall_limits['max_baseline']:.1f}")

        # 5. 关键发现
        print("\n💡 关键发现:")

        # 找出最佳CAP方法
        if cap_ranking["ranking"]:
            best_cap = cap_ranking["ranking"][0]
            print(f"   🥇 最佳CAP方法: {best_cap['cap_method']}")
            print(f"      平均优化效果: {best_cap['stats']['average_optimization']:.1f}%")

        # 找出最佳模型
        best_model = max(model_comparison.keys(),
                        key=lambda x: model_comparison[x]['average_optimization'])
        print(f"   🤖 最佳模型: {best_model}")
        print(f"      平均优化效果: {model_comparison[best_model]['average_optimization']:.1f}%")

        # 优化上限洞察
        if overall_limits:
            max_potential = overall_limits['max_optimization_potential']
            print(f"   📊 优化上限洞察: 最大可提升{max_potential:.1f}分")

            if max_potential > 30:
                print("      💡 建议: 存在巨大优化空间，值得深入研究CAP方法")
            elif max_potential > 15:
                print("      💡 建议: 存在中等优化空间，可以考虑应用CAP方法")
            else:
                print("      💡 建议: 优化空间有限，需要探索新的优化策略")

        # 6. 实施建议
        print("\n🚀 实施建议:")
        print("   1. 基于任务类型选择最适合的CAP方法")
        print("   2. 针对不同模型调整CAP策略")
        print("   3. 建立LogicDepthDetector作为标准评估工具")
        print("   4. 持续监控和优化CAP方法效果")

        print("\n" + "=" * 80)
        print("✅ 测试报告生成完成")

# ==================== 主函数 ====================
def main():
    """主函数 - 执行CAP优化上限测试"""

    print("🎯 CAP方法优化上限测试器启动")
    print("目标：验证LogicDepthDetector作为综合能力测试工具的有效性")
    print("方法：测试不同CAP方法的优化天花板")
    print()

    # 创建测试器
    tester = CAPOptimizationLimitTester()

    # 运行测试
    try:
        results = tester.run_comprehensive_test()

        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"cap_optimization_limit_test_report_{timestamp}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"\n💾 详细测试数据已保存: {filename}")
        print("🎉 CAP优化上限测试完成！")

        return results

    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return None
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
