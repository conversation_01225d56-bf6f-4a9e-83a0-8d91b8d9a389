# F007 Cache库-监控集成设计

## 文档元数据

- **文档ID**: `F007-CACHE-MONITORING-DESIGN-005`
- **版本**: `V1.0`
- **技术栈**: Micrometer 1.12.4, Spring Boot 3.4.5, Observation API 1.12.4, Prometheus 1.18, Maven 3.9
- **构建工具**: Maven 3.9.6
- **数据库技术栈**: N/A (监控数据通过时序数据库存储，如Prometheus)
- **复杂度等级**: `L2`

## 核心定位

监控集成模块是缓存库的可观测性中心，负责收集、暴露和传播缓存操作的关键指标、日志和追踪信息。它确保缓存性能问题能被及时发现和诊断，为性能调优提供数据支撑。

## 设计哲学

本模块遵循以下设计哲学：
1. **可观测性优先**: 让缓存的运行状态完全透明化，监控覆盖率≥95%
2. **标准化集成**: 深度集成现代可观测性标准和工具，兼容率≥90%
3. **自动化收集**: 最小化手动配置，自动收集关键指标，配置复杂度≤3步
4. **多维度监控**: 提供指标、日志、追踪的全方位监控，维度覆盖率≥85%

## 包含范围

**核心功能模块**：
- Micrometer指标收集和暴露
- Spring Boot Observation API集成
- 分布式追踪支持
- 缓存性能指标定义

## 排除范围

**不包含功能**：
- 监控数据的存储和展示
- 告警规则配置
- 性能调优建议
- 外部监控系统的具体配置

## 1. 分层架构设计

### 1.1 架构层次结构

```
┌─────────────────────────────────────────────────────────────────┐
│                    外部监控系统                                  │
│         Prometheus | Grafana | Zipkin | Jaeger                 │
├─────────────────────────────────────────────────────────────────┤
│                   监控数据暴露层                                 │
│              Micrometer Registry                               │
├─────────────────────────────────────────────────────────────────┤
│                 监控集成层 (本层)                                │
│         CacheObservationHandler | MetricsCollector            │
├─────────────────────────────────────────────────────────────────┤
│                  Spring Observation API                       │
│                 ObservationRegistry                           │
├─────────────────────────────────────────────────────────────────┤
│                    缓存业务层                                    │
│         DualLayerCacheManager | CacheTemplate                │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 模块依赖关系

**核心依赖图**:
- `CacheObservationHandler` → `ObservationRegistry` + `MeterRegistry`
- `MetricsCollector` → `CacheTemplates` + `Micrometer`
- **边界定义**: 监控层不侵入业务逻辑，通过观察者模式被动收集
- **职责分离**: 指标收集与数据暴露分离，便于扩展不同监控后端

### 1.3 接口契约定义

**CacheObservationHandler契约**:
```java
// 核心接口契约 - 观察处理器
public class CacheObservationHandler implements ObservationHandler<CacheObservationContext> {
    boolean supportsContext(Observation.Context context);           // 上下文支持检查
    void onStart(CacheObservationContext context);                  // 观察开始处理
    void onStop(CacheObservationContext context);                   // 观察结束处理  
    void onError(CacheObservationContext context);                  // 错误处理
}
```

**MetricsCollector契约**:
```java
// 核心接口契约 - 指标收集器
public interface MetricsCollector {
    void recordCacheOperation(String cacheName, String operation, Duration latency, boolean success); // 操作记录
    void recordCacheHit(String cacheName, CacheLayer layer);         // 命中记录
    void recordCacheMiss(String cacheName, CacheLayer layer);        // 未命中记录
    void recordCacheSize(String cacheName, CacheLayer layer, long size); // 大小记录
}
```

## 2. 设计目标

提供全面、深入、标准化的缓存可观测性，使开发和运维人员能够轻松度量缓存性能、诊断问题并设置告警。

**量化目标**:
- **监控开销**: ≤业务操作时间的2%
- **指标精度**: 时间精度≤1ms，计数精度100%
- **数据完整性**: 关键指标收集成功率≥99.9%
- **实时性**: 指标暴露延迟≤5s

## 3. 核心集成：Observation API

我们将深度集成Spring Boot 3.4.5的Observation API，而不是手动创建Meter。这能自动为我们带来Metrics、Logging和Tracing的全面支持。

**核心组件设计**:
- **ObservationRegistry**: 由Spring Boot自动配置，全局单例
- **CacheObservationContext**: 传递缓存操作上下文信息的载体
- **观察包装**: 所有缓存操作都被Observation包装，自动生成可观测性数据

### 3.1 CacheObservationContext实现

```java
public class CacheObservationContext extends Observation.Context {
    private String cacheName;           // 缓存名称
    private String cacheKey;           // 缓存键
    private String operation;          // 操作类型 (get/put/evict)
    private CacheLayer layer;          // 缓存层级 (L1/L2)
    private boolean hit;               // 是否命中
    private long startTime;            // 开始时间
    private Throwable error;           // 错误信息
    
    // 构造函数和getter/setter
    public CacheObservationContext(String cacheName, String cacheKey, String operation) {
        this.cacheName = cacheName;
        this.cacheKey = cacheKey;
        this.operation = operation;
        this.startTime = System.nanoTime();
    }
}
```

### 3.2 观察处理器实现

```java
@Component
public class CacheObservationHandler implements ObservationHandler<CacheObservationContext> {
    private final MeterRegistry meterRegistry;
    private final Timer.Builder timerBuilder;
    private final Counter.Builder counterBuilder;
    
    @Override
    public boolean supportsContext(Observation.Context context) {
        return context instanceof CacheObservationContext;
    }
    
    @Override
    public void onStart(CacheObservationContext context) {
        context.setStartTime(System.nanoTime());
        log.debug("缓存操作开始: cache={}, key={}, operation={}", 
                 context.getCacheName(), context.getCacheKey(), context.getOperation());
    }
    
    @Override
    public void onStop(CacheObservationContext context) {
        Duration latency = Duration.ofNanos(System.nanoTime() - context.getStartTime());
        
        // 记录操作延迟
        Timer.Sample.start(meterRegistry)
            .stop(Timer.builder("cache.operation.latency")
                .tag("cache.name", context.getCacheName())
                .tag("cache.layer", context.getLayer().name())
                .tag("operation", context.getOperation())
                .register(meterRegistry));
        
        // 记录操作计数
        Counter.builder("cache.operations")
            .tag("cache.name", context.getCacheName())
            .tag("cache.layer", context.getLayer().name())
            .tag("operation", context.getOperation())
            .tag("result", context.isHit() ? "hit" : "miss")
            .register(meterRegistry)
            .increment();
    }
    
    @Override
    public void onError(CacheObservationContext context) {
        Counter.builder("cache.errors")
            .tag("cache.name", context.getCacheName())
            .tag("operation", context.getOperation())
            .tag("error.type", context.getError().getClass().getSimpleName())
            .register(meterRegistry)
            .increment();
    }
}
```

## 4. 关键性能指标 (Metrics)

通过Observation API，我们将自动生成以下符合Micrometer命名规范的指标。

| 指标名称 | 类型 | 描述 | 关键标签 (Tags) |
| :--- | :--- | :--- | :--- |
| `cache.gets` | Counter | 缓存GET操作的总次数 | `cache.name`, `cache.layer` (L1/L2), `result` (hit/miss) |
| `cache.puts` | Counter | 缓存PUT操作的总次数 | `cache.name`, `cache.layer` (L1/L2) |
| `cache.evictions` | Counter | 缓存驱逐的总次数 | `cache.name`, `cache.layer` (L1/L2), `reason` (manual/capacity/ttl) |
| `cache.operation.latency` | Timer | 缓存操作的延迟分布 | `cache.name`, `cache.layer`, `operation` (get/put/evict) |
| `cache.size` | Gauge | 缓存中的条目数 | `cache.name`, `cache.layer` |
| `cache.hit.ratio` | Gauge | 缓存命中率 | `cache.name`, `cache.layer` |
| `cache.memory.usage` | Gauge | 缓存内存使用量(字节) | `cache.name`, `cache.layer` |
| `cache.errors` | Counter | 缓存操作错误次数 | `cache.name`, `operation`, `error.type` |

**标签说明**: 
- `cache.name`: 缓存的逻辑名称，来自`@XkCacheable`的`cacheName`属性
- `cache.layer`: 标识是L1层(Caffeine)还是L2层(Valkey)的指标
- `result`: 操作结果，hit表示命中，miss表示未命中
- `reason`: 驱逐原因，manual表示手动驱逐，capacity表示容量驱逐，ttl表示过期驱逐

### 4.1 指标收集实现

```java
@Component
public class CacheMetricsCollector {
    private final MeterRegistry meterRegistry;
    private final Map<String, CacheStats> statsCache = new ConcurrentHashMap<>();
    
    public void recordCacheOperation(String cacheName, String operation, 
                                   Duration latency, boolean success, CacheLayer layer) {
        // 操作延迟
        Timer.builder("cache.operation.latency")
            .tag("cache.name", cacheName)
            .tag("cache.layer", layer.name())
            .tag("operation", operation)
            .tag("success", String.valueOf(success))
            .register(meterRegistry)
            .record(latency);
        
        // 操作计数
        Counter.builder("cache.operations")
            .tag("cache.name", cacheName) 
            .tag("cache.layer", layer.name())
            .tag("operation", operation)
            .register(meterRegistry)
            .increment();
    }
    
    @EventListener
    @Async
    public void handleCacheStatistics(CacheStatisticsEvent event) {
        // 定期更新缓存统计指标
        Gauge.builder("cache.hit.ratio")
            .tag("cache.name", event.getCacheName())
            .tag("cache.layer", event.getLayer().name())
            .register(meterRegistry, event, e -> e.getHitRatio());
            
        Gauge.builder("cache.size")
            .tag("cache.name", event.getCacheName())
            .tag("cache.layer", event.getLayer().name())
            .register(meterRegistry, event, e -> e.getSize());
    }
}
```

## 5. 分布式追踪 (Tracing)

得益于Observation API，每一次缓存操作都会自动成为分布式追踪链路中的一个`Span`。

**Span配置**:
- **Span名称**: `cache <operation>` (e.g., `cache get`, `cache put`)
- **Span标签**: `cache.name`, `cache.key`, `cache.hit` (true/false) 等上下文信息将被自动添加到Span的标签中
- **Span事件**: 记录关键操作点，如L1查询、L2查询、回源加载等

### 5.1 分布式追踪实现

```java
@Component
public class CacheTracingHandler implements ObservationHandler<CacheObservationContext> {
    
    @Override
    public void onStart(CacheObservationContext context) {
        Span currentSpan = Span.current();
        currentSpan.setAttributes(Attributes.builder()
            .put("cache.name", context.getCacheName())
            .put("cache.key", context.getCacheKey())
            .put("cache.operation", context.getOperation())
            .put("cache.layer", context.getLayer().name())
            .build());
    }
    
    @Override
    public void onStop(CacheObservationContext context) {
        Span currentSpan = Span.current();
        currentSpan.setAttributes(Attributes.builder()
            .put("cache.hit", context.isHit())
            .put("cache.latency.ms", context.getLatency().toMillis())
            .build());
            
        // 记录关键事件
        if (context.isHit()) {
            currentSpan.addEvent("cache.hit", Attributes.builder()
                .put("cache.layer", context.getLayer().name())
                .build());
        } else {
            currentSpan.addEvent("cache.miss", Attributes.builder()
                .put("cache.layer", context.getLayer().name())
                .build());
        }
    }
}
```

这将极大地帮助开发者诊断一个慢请求中，究竟是哪一次缓存操作耗时过长。

## 6. 配置参数管理

### 6.1 监控配置属性

```java
@ConfigurationProperties(prefix = "xkong.cache.monitoring")
@Data
public class CacheMonitoringProperties {
    
    /**
     * 是否启用缓存监控
     */
    private boolean enabled = true;
    
    /**
     * 指标收集间隔(秒)
     */
    private Duration metricsInterval = Duration.ofSeconds(30);
    
    /**
     * 是否启用分布式追踪
     */
    private boolean tracingEnabled = true;
    
    /**
     * 追踪采样率 (0.0-1.0)
     */
    private double tracingSampleRate = 0.1;
    
    /**
     * 是否启用详细日志
     */
    private boolean verboseLogging = false;
    
    /**
     * 指标标签白名单
     */
    private Set<String> allowedTags = Set.of("cache.name", "cache.layer", "operation");
    
    /**
     * 监控数据保留时间
     */
    private Duration dataRetention = Duration.ofHours(24);
}
```

### 6.2 错误处理机制

```java
@Component
public class CacheMonitoringErrorHandler {
    
    public void handleMetricsCollectionError(String cacheName, String operation, Exception e) {
        // 监控异常不应影响缓存功能
        log.warn("指标收集失败: cache={}, operation={}", cacheName, operation, e);
        
        // 记录监控系统本身的错误指标
        Counter.builder("cache.monitoring.errors")
            .tag("cache.name", cacheName)
            .tag("operation", operation)
            .tag("error.type", e.getClass().getSimpleName())
            .register(Metrics.globalRegistry)
            .increment();
    }
    
    public void handleTracingError(String cacheName, Exception e) {
        log.debug("追踪数据收集失败: cache={}", cacheName, e);
        // 追踪失败通常不需要特殊处理，避免日志噪音
    }
}
```

## 7. 边界护栏机制

### 7.1 复杂度控制边界
- **指标数量控制**: 限制单个应用实例指标总数≤1000个，避免存储压力
- **标签维度控制**: 每个指标标签数量≤10个，控制查询复杂度
- **采样率动态调整**: 根据系统负载动态调整追踪采样率(0.01-0.5)
- **批量处理**: 指标数据批量上报，批次大小≤100条

### 7.2 架构演进策略
- **监控后端扩展**: 支持多种监控后端(Prometheus、InfluxDB、CloudWatch)
- **指标定制**: 提供MetricsCustomizer接口支持自定义指标
- **版本兼容**: 保持指标格式向后兼容，支持渐进式升级
- **性能优化**: 持续优化监控开销，确保对业务影响最小

## 实施约束

### 强制性技术要求
- **Micrometer版本**: 必须使用Micrometer 1.12.4，确保Observation API的完整支持
- **Spring Boot版本**: 必须使用Spring Boot 3.4.5，确保自动配置的兼容性
- **监控后端**: 兼容Prometheus 1.18+、Grafana 9.0+、Zipkin 2.24+等主流监控系统
- **JVM版本**: 必须使用Java 21+，确保性能监控特性可用
- **Maven版本**: 必须使用Maven 3.9.6+，确保依赖管理

### 监控指标要求
- **指标收集开销**: 监控开销≤业务操作时间的2%
- **指标精度**: 时间精度≤1ms，计数精度100%
- **数据保留**: 支持至少24小时的监控数据
- **告警延迟**: 异常检测延迟≤30s
- **指标暴露延迟**: 指标暴露延迟≤5s

### 兼容性要求
- **监控系统**: 与Prometheus、Micrometer生态完全兼容(≥95%)
- **追踪系统**: 与OpenTelemetry、Zipkin、Jaeger兼容(≥90%)
- **日志系统**: 与Logback、Log4j2兼容(≥95%)
- **云平台**: 与AWS CloudWatch、Azure Monitor、GCP Monitoring兼容

### 配置参数约束
- **采样率范围**: 追踪采样率必须在0.01-1.0之间
- **收集间隔**: 指标收集间隔不得小于1秒，不得大于300秒
- **标签数量**: 每个指标的标签数量不超过10个
- **数据保留**: 本地数据保留时间不超过7天

### 错误处理约束
- **异常隔离**: 监控异常不能影响缓存业务逻辑正常执行
- **降级策略**: 监控系统不可用时自动降级，不影响应用启动
- **资源保护**: 监控数据收集不能消耗过多内存(≤总内存的5%)
- **故障恢复**: 监控服务故障后能自动恢复，恢复时间≤60s

### 约束违规后果
- **监控失效**: 无法及时发现性能问题和故障，运维盲点增加
- **开销过大**: 影响业务性能表现，响应时间劣化≥10%
- **兼容性问题**: 与现有监控体系集成失败，监控断链
- **数据丢失**: 关键性能数据缺失，影响问题诊断和性能调优

### 验证锚点
- **监控性能测试**: `mvn test -Dtest=MonitoringPerformanceTest`
- **指标准确性测试**: `mvn test -Dtest=MetricsAccuracyTest`
- **集成兼容性测试**: `mvn test -Dtest=MonitoringIntegrationTest`
- **错误处理测试**: `mvn test -Dtest=MonitoringErrorHandlingTest`
- **配置验证测试**: `mvn test -Dtest=MonitoringConfigurationTest`
