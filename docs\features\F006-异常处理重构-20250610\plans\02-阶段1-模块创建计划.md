# 阶段1: 模块创建计划

**执行时间**: 基于AI认知单元，非时间驱动
**前置条件**: AI认知约束激活
**执行模式**: AI护栏保护下的原子操作

## 🚨 实施范围边界（必读）

### 📋 包含范围（AI允许操作）
- ✅ 创建目录结构：`xkongcloud-commons\xkongcloud-commons-exception\`
- ✅ 创建pom.xml配置文件
- ✅ 复制6个核心异常类到core包（包名修改）
- ✅ 复制2个模型类到model包（包名修改）
- ✅ 按技术类别创建8个新异常类
- ✅ 扩展ErrorCodes.java错误码
- ✅ 创建Spring自动配置文件

### 🚫 排除范围（AI禁止操作）
- ❌ 修改现有业务逻辑
- ❌ 改变异常处理机制
- ❌ 修改Spring Boot核心配置
- ❌ 创建测试代码（留待后续阶段）

### 🔍 认知负载控制
- **单次操作**: ≤1个文件，≤50行代码
- **概念数量**: ≤5个相关概念
- **依赖层级**: ≤2层依赖关系
- **验证频率**: 每个文件创建后立即验证

## 🎯 阶段目标

创建新的 `xkongcloud-commons-exception` 模块，按技术类别重新组织异常架构，迁移现有代码并扩展XCE异常类型。

## 🧠 AI认知约束激活

### 强制激活命令
```bash
@L1:global-constraints                    # 全局约束和命名规范
@L1:project-matrix                       # 项目关系矩阵
@L1:ai-implementation-design-principles  # AI实施设计原则
@L2:tech-stack:spring-boot              # Spring Boot技术栈要求
@AI_COGNITIVE_CONSTRAINTS                # AI认知约束激活
@BOUNDARY_GUARD_ACTIVATION              # 边界护栏激活
@HALLUCINATION_PREVENTION               # 幻觉防护激活
@MEMORY_BOUNDARY_CHECK                  # 记忆边界检查
@ATOMIC_OPERATION_VALIDATION            # 原子操作验证
```

### 🛡️ 护栏机制检查
- **目录位置确认**: 必须在正确的工作目录执行
- **包名一致性**: 所有包名必须使用 `org.xkong.cloud.commons.exception`
- **依赖验证**: 每个import语句都必须验证存在性
- **编译验证**: 每个文件创建后立即编译验证

## 📋 详细执行步骤

### 步骤1.1: 创建模块目录结构
```bash
# 创建目录结构（绝对路径）- 按技术类别分包
c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\
├── pom.xml
├── src\
│   ├── main\
│   │   ├── java\org\xkong\cloud\commons\exception\
│   │   │   ├── core\                    # 核心异常基础设施
│   │   │   ├── network\                 # 网络类异常
│   │   │   ├── database\                # 数据库类异常
│   │   │   ├── file\                    # 文件操作类异常
│   │   │   ├── validation\              # 验证类异常
│   │   │   ├── security\                # 安全类异常
│   │   │   └── model\                   # 通用模型
│   │   └── resources\META-INF\
│   └── test\
│       └── java\org\xkong\cloud\commons\exception\
```

**🤖 AI执行指令（原子操作）**:
```bash
# 步骤1.1.1: 验证当前工作目录
pwd
# 预期结果: c:\ExchangeWorks\xkong\xkongcloud

# 步骤1.1.2: 创建pom.xml文件（单一操作）
使用save-file工具创建文件
绝对路径: xkongcloud-commons\xkongcloud-commons-exception\pom.xml
认知负载: 1个文件，约40行代码

# 步骤1.1.3: 立即验证（强制执行）
验证文件存在: ls xkongcloud-commons\xkongcloud-commons-exception\pom.xml
验证XML格式: xmllint --noout xkongcloud-commons\xkongcloud-commons-exception\pom.xml
```

**🔄 回滚方案**: 删除文件 `xkongcloud-commons\xkongcloud-commons-exception\pom.xml`

### 步骤1.2: 创建pom.xml配置
**关键配置要点**:
- parent指向xkongcloud-commons
- artifactId为xkongcloud-commons-exception
- 依赖与原模块保持一致
- 版本号使用${project.version}

### 步骤1.3: 复制核心异常类到core包

**🧠 认知负载控制**: 每次处理1个文件，立即验证

#### 步骤1.3.1: ServiceException.java（原子操作）
```bash
# 源文件查看（幻觉防护）
view工具查看: xkongcloud-common-exception\src\main\java\org\xkong\cloud\common\exception\ServiceException.java

# 复制并修改包名（单一操作）
目标路径: xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\core\ServiceException.java
包名修改: org.xkong.cloud.common.exception → org.xkong.cloud.commons.exception.core

# 立即验证（强制执行）
编译验证: javac -cp "lib/*" [目标文件路径]
包名验证: grep "package org.xkong.cloud.commons.exception.core" [目标文件]
```

#### 步骤1.3.2-1.3.6: 其他核心异常类
**🔄 重复模式**: 每个文件使用相同的原子操作模式
1. `BusinessException.java` - 业务异常类
2. `SystemException.java` - 系统异常类
3. `GlobalExceptionHandler.java` - 全局异常处理器
4. `ExceptionLoggingAspect.java` - 异常日志切面
5. `ExceptionAutoConfiguration.java` - 自动配置类

**📋 标准化操作流程**:
```bash
# 每个文件的标准流程
1. view工具查看源文件（防止幻觉）
2. 使用str-replace-editor复制并修改包名
3. 立即编译验证
4. 检查import语句正确性
5. 确认无临时代码残留
```

**🔄 回滚方案**: 删除core包下的所有文件

### 步骤1.4: 复制模型类到model包
**需要复制的文件（绝对路径）**:
1. `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\model\ErrorInfo.java` - 错误信息封装类
2. `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\model\ErrorCodes.java` - 错误码常量类

**源文件位置（复制来源）**:
- `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-common-exception\src\main\java\org\xkong\cloud\common\model\*.java`

**包名修改规则**:
```java
// 原包名
package org.xkong.cloud.common.model;

// 新包名
package org.xkong.cloud.commons.exception.model;
```

### 步骤1.5: 按技术类别创建异常类

#### 1.5.1 网络类异常
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\network\`
1. `NetworkBusinessException.java` - 网络业务异常基类
2. `NetworkSystemException.java` - 网络系统异常基类
3. `MockEnvironmentException.java` - Mock环境创建异常（XCE扩展）
4. `NetworkErrorCodes.java` - 网络错误码常量

#### 1.5.2 数据库类异常
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\database\`
1. `DatabaseBusinessException.java` - 数据库业务异常基类
2. `DatabaseSystemException.java` - 数据库系统异常基类
3. `DatabaseErrorCodes.java` - 数据库错误码常量

#### 1.5.3 文件操作类异常
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\file\`
1. `FileBusinessException.java` - 文件业务异常基类
2. `FileSystemException.java` - 文件系统异常基类
3. `TestDataInjectionException.java` - 测试数据注入异常（XCE扩展）
4. `FileErrorCodes.java` - 文件错误码常量

#### 1.5.4 验证类异常
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\validation\`
1. `ValidationBusinessException.java` - 验证业务异常基类
2. `ParametricExecutionException.java` - 参数化推演异常（XCE扩展）
3. `L1PerceptionException.java` - L1感知层验证异常（XCE扩展）
4. `ValidationErrorCodes.java` - 验证错误码常量

#### 1.5.5 安全类异常
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\security\`
1. `SecurityBusinessException.java` - 安全业务异常基类
2. `SecurityErrorCodes.java` - 安全错误码常量

### 步骤1.6: 扩展错误码分配
**在ErrorCodes.java中添加技术类别错误码**:
```java
// 网络类错误码 (600-649)
public static final String NETWORK_CONNECTION_TIMEOUT = "XCE_NET_600";
public static final String NETWORK_SERVICE_UNAVAILABLE = "XCE_NET_601";
public static final String MOCK_ENVIRONMENT_CREATION_FAILED = "XCE_NET_602";

// 数据库类错误码 (650-699)
public static final String DATABASE_CONNECTION_POOL_EXHAUSTED = "XCE_DB_650";
public static final String DATABASE_TRANSACTION_TIMEOUT = "XCE_DB_651";

// 文件操作类错误码 (700-749)
public static final String FILE_NOT_FOUND = "XCE_FILE_700";
public static final String FILE_PERMISSION_DENIED = "XCE_FILE_701";
public static final String TEST_DATA_INJECTION_FAILED = "XCE_FILE_702";

// 验证类错误码 (750-799)
public static final String VALIDATION_PARAMETER_INVALID = "XCE_VAL_750";
public static final String PARAMETRIC_EXECUTION_FAILED = "XCE_VAL_751";
public static final String L1_PERCEPTION_VALIDATION_FAILED = "XCE_VAL_752";

// 安全类错误码 (800-849)
public static final String SECURITY_AUTHENTICATION_FAILED = "XCE_SEC_800";
public static final String SECURITY_AUTHORIZATION_DENIED = "XCE_SEC_801";

// 算法处理类错误码 (850-899) - 系统异常扩展
public static final String ALGORITHM_L2_COGNITION_FAILED = "XCE_ALG_850";
public static final String ALGORITHM_L3_UNDERSTANDING_FAILED = "XCE_ALG_851";
public static final String ALGORITHM_L4_WISDOM_FAILED = "XCE_ALG_852";
```

### 步骤1.7: 更新Spring自动配置
**关键文件（绝对路径）**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\resources\META-INF\spring.factories`
```properties
org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
  org.xkong.cloud.commons.exception.core.ExceptionAutoConfiguration
```

### 步骤1.8: 更新xkongcloud-commons的pom.xml
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\pom.xml`
**添加新模块**:
```xml
<modules>
    <module>xkongcloud-commons-uid</module>
    <module>xkongcloud-commons-exception</module>
</modules>
```

## ✅ 阶段1验收标准

### 🔍 AI认知护栏验证
```bash
# 引用checklist-templates标准验证
@checklist-templates:ai_memory_guardrail_system
@checklist-templates:standardized_verification_commands
@checklist-templates:ai_hallucination_prevention
```

### 📋 编译验证（强制执行）
```bash
# 模块编译验证
mvn clean compile -pl xkongcloud-commons-exception
# 预期结果: BUILD SUCCESS

# 依赖验证
mvn dependency:tree -pl xkongcloud-commons-exception
# 预期结果: 无依赖冲突

# 包名验证
grep -r "org.xkong.cloud.common.exception" xkongcloud-commons-exception/
# 预期结果: 无匹配（确保包名已全部更新）
```

### 🏗️ 结构验证
- [ ] 目录结构按技术类别正确组织
- [ ] 所有文件包名使用commons
- [ ] Spring自动配置文件路径正确
- [ ] 错误码分配无重复冲突

### 🧪 功能验证
- [ ] 所有原有异常类功能完整
- [ ] XCE扩展异常类创建完成
- [ ] 自动配置机制正常工作
- [ ] 无临时代码残留

### 🛡️ 护栏验证
- [ ] 认知负载≤800行代码
- [ ] 边界范围严格遵守
- [ ] 所有操作都有回滚方案
- [ ] 幻觉防护验证通过

## 🚨 AI执行错误防护

### 常见错误模式及防护
1. **包名错误**: 每个文件创建后立即验证包名
2. **路径错误**: 使用绝对路径，避免在根目录创建文件
3. **依赖幻觉**: 每个import都必须验证真实存在
4. **临时代码**: 禁止创建任何System.out或TODO标记

### 🔧 错误恢复机制
```bash
# 编译失败恢复
1. 检查包名是否正确
2. 验证import语句存在性
3. 确认文件路径正确
4. 必要时回滚到上一个稳定状态
```

## ➡️ 下一步

**阶段1完成检查清单**:
- [ ] 所有验证命令执行通过
- [ ] 护栏机制验证完成
- [ ] 回滚方案确认可用
- [ ] 继续执行 `03-阶段2-依赖更新计划.md`
