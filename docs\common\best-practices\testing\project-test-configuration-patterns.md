# 项目测试配置模式指南

## 概述

本文档定义了不同类型项目的标准测试配置模式，提供通用的测试脚本语法和配置示例，支持多种测试类型的统一管理。

## 通用测试脚本语法

### 标准语法格式
```bash
test-runner [PROJECT_PATH] [TEST_TYPE] [MAIN_CLASS] [ADDITIONAL_ARGS]
```

### 参数说明
- **PROJECT_PATH**: 项目相对路径
- **TEST_TYPE**: 测试类型（custom/spring-boot/maven-test）
- **MAIN_CLASS**: 自定义测试主类（仅custom类型需要）
- **ADDITIONAL_ARGS**: 额外的测试参数

## 支持的测试类型

### 1. custom - 自定义TestRunner
**适用场景**: 有专门测试运行器的项目（如库项目）

**配置要求**:
- 必须提供主类名
- 主类需要有`public static void main(String[] args)`方法
- 能够独立运行所有测试
- 正确处理TestContainers和Docker环境

**示例**:
```bash
test-runner commons/uid-library custom org.example.TestRunner
```

### 2. spring-boot - Spring Boot测试
**适用场景**: Spring Boot应用项目

**配置特点**:
- 自动检测Spring Boot主类
- 支持多Profile配置
- 集成TestContainers支持
- 自动配置测试数据源

**示例**:
```bash
test-runner service-center spring-boot
test-runner business-core spring-boot "" "-Dspring.profiles.active=test"
```

### 3. maven-test - Maven标准测试
**适用场景**: 标准Maven项目测试

**配置特点**:
- 使用Maven Surefire插件
- 支持测试过滤和分组
- 标准的Maven生命周期
- 支持并行测试执行

**示例**:
```bash
test-runner gateway maven-test
test-runner core maven-test "" "-Dtest=**/*IntegrationTest"
```

## 项目配置模式

### 库项目模式
```yaml
project_type: library
test_type: custom
main_class: org.example.TestRunner
dependencies:
  - junit5
  - testcontainers
  - mockito
test_profile: test
```

### 服务项目模式
```yaml
project_type: service
test_type: spring-boot
spring_profiles:
  - test
  - integration-test
dependencies:
  - spring-boot-starter-test
  - testcontainers-postgresql
  - testcontainers-redis
```

### 网关项目模式
```yaml
project_type: gateway
test_type: maven-test
test_groups:
  - unit
  - integration
  - performance
dependencies:
  - spring-cloud-starter-test
  - wiremock
  - testcontainers
```

## 高级配置模式

### 多环境测试配置
```yaml
environments:
  local:
    docker_required: false
    database: h2
  remote-docker:
    docker_required: true
    database: postgresql
    ssh_tunnel: true
  ci:
    docker_required: true
    database: postgresql
    parallel: true
```

### 测试分组配置
```yaml
test_groups:
  unit:
    pattern: "**/*Test.java"
    exclude_pattern: "**/*IntegrationTest.java"
  integration:
    pattern: "**/*IntegrationTest.java"
    requires_docker: true
  performance:
    pattern: "**/*PerformanceTest.java"
    timeout: 300
```

### 依赖管理配置
```yaml
test_dependencies:
  database:
    - testcontainers-postgresql
    - h2database
  messaging:
    - testcontainers-rabbitmq
    - embedded-rabbitmq
  cache:
    - testcontainers-redis
    - embedded-redis
```

## 自定义TestRunner要求

### 基本要求
```java
public class TestRunner {
    public static void main(String[] args) {
        // 1. 初始化测试环境
        initializeTestEnvironment();
        
        // 2. 配置TestContainers
        configureTestContainers();
        
        // 3. 执行测试套件
        TestResult result = executeTestSuite();
        
        // 4. 清理资源
        cleanupResources();
        
        // 5. 输出结果
        System.exit(result.isSuccess() ? 0 : 1);
    }
}
```

### TestContainers集成
```java
@TestMethodOrder(OrderAnnotation.class)
public class IntegrationTestSuite {
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:13")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test")
            .withStartupTimeout(Duration.ofMinutes(3));
    
    @Container
    static GenericContainer<?> redis = new GenericContainer<>("redis:7")
            .withExposedPorts(6379)
            .withStartupTimeout(Duration.ofMinutes(2));
}
```

## 配置文件模板

### application-test.yml
```yaml
spring:
  profiles:
    active: test
  datasource:
    url: ${TEST_DATABASE_URL:jdbc:h2:mem:testdb}
    username: ${TEST_DATABASE_USERNAME:test}
    password: ${TEST_DATABASE_PASSWORD:test}
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
logging:
  level:
    org.springframework: INFO
    org.testcontainers: INFO
```

### test.properties
```properties
# 测试环境配置
test.environment=integration
test.docker.enabled=true
test.database.type=postgresql
test.cache.type=redis
test.messaging.type=rabbitmq

# 超时配置
test.timeout.connection=30000
test.timeout.startup=180000
test.timeout.test=300000

# 并发配置
test.parallel.enabled=false
test.parallel.threads=4
```

## 最佳实践

### 测试隔离
- 每个测试使用独立的数据库Schema
- 测试间不共享状态
- 使用事务回滚确保数据清理

### 资源管理
- 合理设置容器启动超时
- 实施连接池配置
- 及时清理测试资源

### 性能优化
- 使用测试容器复用
- 实施并行测试策略
- 优化测试数据准备

### 错误处理
- 提供详细的错误信息
- 实施重试机制
- 记录测试执行日志

## 故障排除

### 常见问题
1. **项目路径不存在**: 检查相对路径配置
2. **测试类型无效**: 使用支持的测试类型
3. **主类缺失**: 为custom类型提供主类
4. **依赖冲突**: 检查测试依赖版本

### 调试技巧
- 启用详细日志输出
- 使用测试Profile隔离配置
- 检查容器启动状态
- 验证网络连接

## 扩展支持

### 新项目类型
1. 分析项目结构和依赖
2. 选择合适的测试类型
3. 配置测试环境
4. 验证测试执行

### 自定义测试类型
1. 定义测试类型标识
2. 实现测试执行逻辑
3. 配置环境要求
4. 添加文档说明

---
**文档版本**: 1.0  
**创建日期**: 2025-06-04  
**适用范围**: 多项目类型测试配置  
**维护状态**: 活跃维护
