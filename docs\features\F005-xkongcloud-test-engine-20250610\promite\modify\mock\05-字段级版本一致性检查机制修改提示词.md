# 05-字段级版本一致性检查机制修改提示词

**文档版本**: MODIFY-FIELD-VERSION-CONSISTENCY  
**创建时间**: 2025年6月10日  
**修改目标**: 在版本一致性检查中增加Mock环境的特殊处理策略

---

## 🎯 修改目标

在字段级版本一致性检查机制中增加Mock环境的版本检查策略，确保不同Mock环境类型的版本检查适配。

## 📝 具体修改内容

### **修改位置1：环境适配版本检查 - 增加Mock环境特殊处理**

**在UniversalFieldVersionConsistencyChecker类中修改adaptVersionCheckStrategy方法**：
```java
/**
 * 基于环境感知调整版本检查策略
 * 增加Mock环境的详细版本检查策略
 */
private VersionCheckStrategy adaptVersionCheckStrategy(UniversalEnvironmentAwareness awareness) {
    VersionCheckStrategy strategy = new VersionCheckStrategy();
    
    switch (awareness.getEnvironmentType()) {
        case MOCK_DEVELOPMENT:
            // 开发阶段Mock：宽松检查，专注开发效率
            strategy.setCheckLevel(VersionCheckLevel.DEVELOPMENT_FRIENDLY);
            strategy.setFailOnMismatch(false);
            strategy.setWarningOnly(true);
            strategy.setFieldLevelValidation(false);
            strategy.setPurpose("开发阶段快速验证，允许版本差异");
            strategy.setToleranceLevel(ToleranceLevel.HIGH);
            break;
            
        case MOCK_DIAGNOSTIC:
            // 故障诊断Mock：标准检查，确保诊断准确性
            strategy.setCheckLevel(VersionCheckLevel.DIAGNOSTIC_STANDARD);
            strategy.setFailOnMismatch(true);
            strategy.setWarningOnly(false);
            strategy.setFieldLevelValidation(true);
            strategy.setPurpose("故障诊断分析，确保版本一致性");
            strategy.setToleranceLevel(ToleranceLevel.MEDIUM);
            break;
            
        case MOCK_PROTECTION:
            // 保护模式Mock：保守检查，确保系统稳定性
            strategy.setCheckLevel(VersionCheckLevel.PROTECTION_CONSERVATIVE);
            strategy.setFailOnMismatch(false);
            strategy.setWarningOnly(true);
            strategy.setFieldLevelValidation(true);
            strategy.setPurpose("保护模式运行，保守版本检查");
            strategy.setToleranceLevel(ToleranceLevel.HIGH);
            strategy.setFallbackEnabled(true);
            break;
            
        case MOCK_INTERFACE:
            // 接口模拟Mock：严格检查，确保接口一致性
            strategy.setCheckLevel(VersionCheckLevel.INTERFACE_STRICT);
            strategy.setFailOnMismatch(true);
            strategy.setWarningOnly(false);
            strategy.setFieldLevelValidation(true);
            strategy.setPurpose("gRPC接口模拟，严格版本一致性");
            strategy.setToleranceLevel(ToleranceLevel.LOW);
            strategy.setInterfaceContractValidation(true);
            break;
            
        case REAL_TESTCONTAINERS:
            // TestContainers环境：严格检查，确保一致性
            strategy.setCheckLevel(VersionCheckLevel.STRICT);
            strategy.setFailOnMismatch(true);
            strategy.setWarningOnly(false);
            strategy.setFieldLevelValidation(true);
            strategy.setPurpose("真实环境验证，严格版本检查");
            strategy.setToleranceLevel(ToleranceLevel.LOW);
            break;
            
        case PRODUCTION_LIKE:
            // 生产类似环境：超严格检查，零容忍
            strategy.setCheckLevel(VersionCheckLevel.ULTRA_STRICT);
            strategy.setFailOnMismatch(true);
            strategy.setWarningOnly(false);
            strategy.setFieldLevelValidation(true);
            strategy.setPurpose("生产级别验证，零版本容忍");
            strategy.setToleranceLevel(ToleranceLevel.ZERO);
            break;
            
        default:
            // 未知环境：保守检查
            strategy.setCheckLevel(VersionCheckLevel.CONSERVATIVE);
            strategy.setFailOnMismatch(true);
            strategy.setWarningOnly(false);
            strategy.setFieldLevelValidation(true);
            strategy.setPurpose("未知环境保守处理");
            strategy.setToleranceLevel(ToleranceLevel.MEDIUM);
            break;
    }
    
    return strategy;
}
```

### **修改位置2：Mock环境版本检查适配器**

**增加MockEnvironmentVersionAdapter类**：
```java
/**
 * Mock环境版本检查适配器
 * 为不同Mock环境类型提供专门的版本检查适配
 */
@Component
public class MockEnvironmentVersionAdapter {
    
    /**
     * 适配开发阶段Mock的版本检查
     * 宽松检查，专注开发效率
     */
    public VersionCheckResult adaptDevelopmentMockVersionCheck(
            UniversalEngineConfig config,
            VersionCheckStrategy strategy) {
        
        VersionCheckResult result = new VersionCheckResult();
        
        // 开发阶段只检查关键字段
        List<String> criticalFields = Arrays.asList("engineVersion", "configVersion");
        
        for (String fieldName : criticalFields) {
            FieldVersionCheckResult fieldResult = checkFieldVersionWithTolerance(
                fieldName, config, strategy.getToleranceLevel());
            result.addFieldResult(fieldName, fieldResult);
        }
        
        // 开发阶段允许非关键字段版本差异
        result.setOverallResult(result.getCriticalFieldsValid());
        result.setAdaptationNote("开发阶段宽松检查，允许非关键字段版本差异");
        
        return result;
    }
    
    /**
     * 适配故障诊断Mock的版本检查
     * 标准检查，确保诊断准确性
     */
    public VersionCheckResult adaptDiagnosticMockVersionCheck(
            UniversalEngineConfig config,
            VersionCheckStrategy strategy) {
        
        VersionCheckResult result = new VersionCheckResult();
        
        // 诊断模式需要完整的版本检查
        for (Map.Entry<String, Object> field : config.getAllFields().entrySet()) {
            FieldVersionCheckResult fieldResult = checkFieldVersionStandard(
                field.getKey(), field.getValue(), config);
            result.addFieldResult(field.getKey(), fieldResult);
        }
        
        // 诊断模式要求版本一致性
        result.setOverallResult(result.getAllFieldsValid());
        result.setAdaptationNote("诊断模式标准检查，确保版本一致性");
        
        return result;
    }
    
    /**
     * 适配保护模式Mock的版本检查
     * 保守检查，确保系统稳定性
     */
    public VersionCheckResult adaptProtectionMockVersionCheck(
            UniversalEngineConfig config,
            VersionCheckStrategy strategy) {
        
        VersionCheckResult result = new VersionCheckResult();
        
        // 保护模式优先保证系统可用性
        try {
            // 尝试标准版本检查
            VersionCheckResult standardResult = performStandardVersionCheck(config);
            
            if (standardResult.isValid()) {
                result = standardResult;
                result.setAdaptationNote("保护模式标准检查通过");
            } else {
                // 标准检查失败，降级到最小版本检查
                result = performMinimalVersionCheck(config);
                result.setAdaptationNote("保护模式降级检查，优先保证系统可用性");
            }
            
        } catch (Exception e) {
            // 版本检查异常，使用默认兼容配置
            result = createDefaultCompatibleResult();
            result.setAdaptationNote("保护模式异常处理，使用默认兼容配置");
        }
        
        return result;
    }
    
    /**
     * 适配接口模拟Mock的版本检查
     * 严格检查，确保接口一致性
     */
    public VersionCheckResult adaptInterfaceMockVersionCheck(
            UniversalEngineConfig config,
            VersionCheckStrategy strategy) {
        
        VersionCheckResult result = new VersionCheckResult();
        
        // 接口模拟需要严格的版本检查
        for (Map.Entry<String, Object> field : config.getAllFields().entrySet()) {
            FieldVersionCheckResult fieldResult = checkFieldVersionStrict(
                field.getKey(), field.getValue(), config);
            result.addFieldResult(field.getKey(), fieldResult);
        }
        
        // 额外检查接口契约版本
        InterfaceContractVersionResult contractResult = checkInterfaceContractVersion(config);
        result.setInterfaceContractResult(contractResult);
        
        // 接口模拟要求严格一致性
        result.setOverallResult(result.getAllFieldsValid() && contractResult.isValid());
        result.setAdaptationNote("接口模拟严格检查，确保接口契约一致性");
        
        return result;
    }
    
    /**
     * 检查接口契约版本
     * 确保gRPC接口版本的一致性
     */
    private InterfaceContractVersionResult checkInterfaceContractVersion(UniversalEngineConfig config) {
        InterfaceContractVersionResult result = new InterfaceContractVersionResult();
        
        // 检查gRPC协议版本
        String grpcVersion = config.getGrpcProtocolVersion();
        if (grpcVersion != null) {
            boolean grpcVersionValid = validateGrpcProtocolVersion(grpcVersion);
            result.setGrpcVersionValid(grpcVersionValid);
        }
        
        // 检查API契约版本
        String apiVersion = config.getApiContractVersion();
        if (apiVersion != null) {
            boolean apiVersionValid = validateApiContractVersion(apiVersion);
            result.setApiVersionValid(apiVersionValid);
        }
        
        result.setValid(result.isGrpcVersionValid() && result.isApiVersionValid());
        
        return result;
    }
}
```

### **修改位置3：Mock环境版本容忍度配置**

**增加MockVersionToleranceConfiguration类**：
```java
/**
 * Mock环境版本容忍度配置
 * 为不同Mock环境定义版本检查的容忍度
 */
@Configuration
@ConfigurationProperties(prefix = "universal.engine.mock.version-tolerance")
public class MockVersionToleranceConfiguration {
    
    // 开发阶段Mock的版本容忍度配置
    private DevelopmentToleranceConfig development = new DevelopmentToleranceConfig();
    
    // 故障诊断Mock的版本容忍度配置
    private DiagnosticToleranceConfig diagnostic = new DiagnosticToleranceConfig();
    
    // 保护模式Mock的版本容忍度配置
    private ProtectionToleranceConfig protection = new ProtectionToleranceConfig();
    
    // 接口模拟Mock的版本容忍度配置
    private InterfaceToleranceConfig interfaceConfig = new InterfaceToleranceConfig();
    
    /**
     * 开发阶段容忍度配置
     */
    public static class DevelopmentToleranceConfig {
        private boolean allowMinorVersionDifference = true;
        private boolean allowPatchVersionDifference = true;
        private boolean allowConfigSchemaEvolution = true;
        private List<String> ignoredFields = Arrays.asList("buildTimestamp", "gitCommit");
        private double maxVersionDifferencePercentage = 10.0;
    }
    
    /**
     * 故障诊断容忍度配置
     */
    public static class DiagnosticToleranceConfig {
        private boolean allowMinorVersionDifference = false;
        private boolean allowPatchVersionDifference = true;
        private boolean allowConfigSchemaEvolution = false;
        private List<String> ignoredFields = Arrays.asList("buildTimestamp");
        private double maxVersionDifferencePercentage = 5.0;
    }
    
    /**
     * 保护模式容忍度配置
     */
    public static class ProtectionToleranceConfig {
        private boolean allowMinorVersionDifference = true;
        private boolean allowPatchVersionDifference = true;
        private boolean allowConfigSchemaEvolution = true;
        private boolean enableFallbackCompatibility = true;
        private List<String> ignoredFields = Arrays.asList("buildTimestamp", "gitCommit", "environment");
        private double maxVersionDifferencePercentage = 15.0;
    }
    
    /**
     * 接口模拟容忍度配置
     */
    public static class InterfaceToleranceConfig {
        private boolean allowMinorVersionDifference = false;
        private boolean allowPatchVersionDifference = false;
        private boolean allowConfigSchemaEvolution = false;
        private boolean strictInterfaceContractCheck = true;
        private List<String> ignoredFields = Arrays.asList();
        private double maxVersionDifferencePercentage = 0.0;
    }
}
```

## 🎯 修改原则

1. **Mock环境分类处理**：为不同Mock环境类型提供专门的版本检查策略
2. **容忍度分级管理**：根据Mock环境的用途设置不同的版本容忍度
3. **保护模式优先**：保护模式下优先保证系统可用性，而非版本严格性
4. **接口契约严格性**：接口模拟Mock要求最严格的版本一致性

## 📋 验证要点

修改完成后，文档应该能够让AI清晰理解：
- 不同Mock环境的版本检查策略差异
- Mock环境版本容忍度的配置和管理
- 保护模式下的版本检查降级机制
- 接口模拟Mock的严格版本要求
