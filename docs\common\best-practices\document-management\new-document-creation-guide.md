# 新文档创建完整指南

## 文档元数据

- **文档ID**: C006
- **文档类型**: 最佳实践指南
- **分类**: document-management
- **作用域**: XKongCloud项目文档管理
- **关键词**: document_creation, ai_memory_integration, attention_commands, workflow
- **创建日期**: 2025-05-29
- **更新日期**: 2025-05-29
- **状态**: 活跃
- **版本**: 1.0.0
- **作者**: AI系统
- **影响功能**: 所有功能文档创建
- **相关文档**: 
  - [Feature README模板](../../templates/feature-readme-template.md)
  - [设计文档模板](../../templates/design-doc.md)
  - [AI记忆系统集成文档](../../../ai-memory/)

## 概述

本指南提供在XKongCloud项目中创建新文档的完整流程，包括AI记忆系统集成、注意力命令使用和最佳实践。所有文档创建都应严格遵循本指南以确保系统一致性和AI可发现性。

## AI记忆系统激活

创建新文档时，AI系统会自动激活以下注意力机制：

### 自动激活的注意力命令

```
@L2:task-type:documentation-tasks  # 激活文档管理任务上下文
@DOCUMENT_CREATION_CHECK           # 验证文档创建规范
@MEMORY_INTEGRATION_CHECK          # 检查记忆系统集成
@DOCUMENT_STANDARDS_CHECK          # 验证文档质量标准
```

### 关键词触发机制

当检测到以下关键词时自动激活相应的注意力命令：
- `文档创建`, `document creation` → `@DOCUMENT_CREATION_CHECK`
- `文档管理`, `document management` → `@MEMORY_INTEGRATION_CHECK`
- `记忆系统`, `memory system` → `@MEMORY_INTEGRATION_CHECK`

## 创建特征文档 (Feature Documents)

### Step 1: 确定功能ID和基本信息

1. **查看现有功能映射**
   ```bash
   # 检查下一个可用的功能ID
   cat docs/feature-document-map.md
   ```

2. **确定项目代码**
   - XKC-CORE: 核心业务逻辑
   - XKC-UID: UID生成器相关
   - XKC-CENTER: 服务中心相关

3. **分配功能ID**
   - 格式: F + 递增数字 (F001, F002, F003...)

### Step 2: 创建目录结构

```bash
# 创建功能文档目录
mkdir -p "docs/features/F00X-功能名称-$(date +%Y%m%d)"

# 创建标准子目录 (@DOCUMENT_CREATION_CHECK 验证项)
cd "docs/features/F00X-功能名称-$(date +%Y%m%d)"
mkdir -p requirements design plan api test guide code
mkdir -p design/diagrams api/examples test/test-results code/examples code/scripts code/configurations
```

### Step 3: 创建README.md

使用模板创建README.md：

```bash
# 复制模板
cp docs/common/templates/feature-readme-template.md docs/features/F00X-功能名称-$(date +%Y%m%d)/README.md

# 编辑并替换所有 {变量} 占位符
```

**必需的元数据字段** (@DOCUMENT_CREATION_CHECK 验证):
- feature_id
- feature_name  
- project_code
- created_date
- status
- authors
- keywords
- related_features

### Step 4: 更新映射文件和状态跟踪

**更新feature-document-map.md**:
```markdown
| F00X | 功能名称 | XKC-CORE | docs/features/F00X-功能名称-20250529 | active_development |
```

**更新feature-status.json**:
```json
{
  "F00X": {
    "feature_name": "功能名称",
    "project_code": "XKC-CORE", 
    "status": "active_development",
    "created_date": "2025-05-29",
    "last_updated": "2025-05-29"
  }
}
```

### Step 5: 创建AI记忆索引 (@MEMORY_INTEGRATION_CHECK)

```bash
# 创建L3-index记忆文件
cp docs/ai-memory/templates/feature-index-template.md \
   docs/ai-memory/L3-index/feature-index/by-project/XKC-CORE/feature-F00X.md

# 编辑并替换模板变量
```

### Step 6: 验证AI记忆系统集成

执行验证命令：
```bash
# 验证记忆系统完整性
@L3:feature:F00X

# 验证项目上下文激活
@L2:project:XKC-CORE

# 验证任务类型路由
@L2:task-type:documentation-tasks
```

## 创建共享文档 (Shared Documents)

### Step 1: 确定文档分类

**docs/common/目录结构**:
- `architecture/` - 架构设计文档
- `best-practices/` - 最佳实践指南
- `middleware/` - 中间件集成文档
- `protocols/` - 通信协议文档
- `security/` - 安全相关文档
- `troubleshooting/` - 故障排除指南
- `templates/` - 文档模板

### Step 2: 分配文档ID

格式：C + 递增数字 (C001, C002, C003...)

### Step 3: 创建文档

```bash
# 创建文档
touch docs/common/[category]/[subcategory]/document-name.md

# 添加完整元数据头部 (@DOCUMENT_STANDARDS_CHECK 验证)
```

**必需的元数据字段**:
- title
- document_id
- document_type
- category
- scope
- keywords
- created_date
- updated_date
- status
- version
- authors
- affected_features
- related_docs

### Step 4: 更新L2-context映射

根据文档类型更新相应的L2-context文件：

```json
// 例如：docs/ai-memory/L2-context/task-types/[task-type].json
{
  "document_paths": [
    "docs/common/[category]/[subcategory]/new-document.md"
  ]
}
```

### Step 5: 更新memory-index.json

```json
{
  "path": "path/to/new-document.md",
  "type": "context/pattern/guide", 
  "priority": "high/medium/low",
  "title": "文档标题",
  "tags": ["关键词", "标签"],
  "language": "cn/en/en-cn"
}
```

## 文档质量标准 (@DOCUMENT_STANDARDS_CHECK)

### 模板合规性
- 必须使用对应的文档模板
- 所有 {变量} 占位符必须被替换

### 语言一致性
- 内容使用中文，技术术语使用英文
- 代码注释和API文档使用英文

### 跨引用格式
```markdown
# 正确的内部链接格式
[链接文本](../path/to/document.md)
[API文档](../../api/api-specification.md)
```

### 代码示例标准
- 代码必须可执行
- 遵循项目编码规范
- 包含适当的注释

### 版本控制
```markdown
## 变更历史

| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| 1.0.0 | 2025-05-29 | 初始创建 | 作者名 |
```

## 工作流集成 (RIPER-5)

### RESEARCH阶段
- 激活: `@DOCUMENT_STANDARDS_CHECK`
- 分析现有文档结构和标准
- 确定文档类型和目标受众

### PLAN阶段  
- 激活: `@DOCUMENT_CREATION_CHECK`
- 规划文档结构和元数据
- 设计AI记忆系统集成

### EXECUTE阶段
- 激活: `@MEMORY_INTEGRATION_CHECK`
- 创建文档内容
- 更新记忆系统文件

### REVIEW阶段
- 激活: `@DOCUMENT_STANDARDS_CHECK`
- 验证文档质量和完整性
- 确保AI系统可发现性

## 自动化检查清单

### 创建验证 (@DOCUMENT_CREATION_CHECK)
- [ ] 文档ID唯一性和格式正确
- [ ] 目录结构符合项目标准
- [ ] 元数据字段完整且格式正确
- [ ] 文档分类和范围明确
- [ ] 关键词和标签相关且适当

### 记忆集成验证 (@MEMORY_INTEGRATION_CHECK)
- [ ] feature-document-map.md已更新
- [ ] feature-status.json状态正确
- [ ] L3-index记忆文件已创建
- [ ] L2-context路由配置正确
- [ ] 注意力命令可发现新文档

### 标准合规验证 (@DOCUMENT_STANDARDS_CHECK)
- [ ] 遵循模板结构
- [ ] 语言使用一致
- [ ] 跨引用格式正确
- [ ] 代码示例可执行
- [ ] 变更历史完整

## 常见问题和解决方案

### Q: 如何选择正确的文档ID?
A: 检查对应映射文件，选择下一个可用的递增ID

### Q: 如何确保AI记忆系统可以找到新文档?
A: 执行完整的@MEMORY_INTEGRATION_CHECK验证流程

### Q: 文档创建后如何验证质量?
A: 使用@DOCUMENT_STANDARDS_CHECK进行全面质量检查

### Q: 如何处理跨功能的共享文档?
A: 在affected_features字段中列出所有相关功能ID

## 最佳实践

1. **始终使用模板**: 确保结构一致性和完整性
2. **完整的元数据**: 有助于AI系统理解和分类
3. **定期同步**: 重要变更后执行@sync:feature:{id}
4. **验证集成**: 创建后立即验证AI记忆系统集成
5. **保持更新**: 功能状态变更时同步更新文档状态

## 扩展阅读

- [AI记忆系统架构文档](../../../ai-memory/)
- [注意力命令参考](../../../ai-memory/L1-core/attention-commands.json)
- [RIPER-5协议集成](../../../../.augment-guidelines)
- [文档模板库](../../templates/) 