#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逻辑锥CAP方法深度对比测试器
基于2-推理深度的核心测试代码，专门针对逻辑锥任务的CAP方法对比

测试目标：
1. 理论CAP方法 vs 实用CAP方法
2. R1模型 + 不同CAP方法的适配性
3. V3模型 + 不同CAP方法的适配性
4. 逻辑锥L0-L2层的专用CAP方法验证

基于：docs/features/T001-create-plans-20250612/v4/design/核心/逻辑锥/ai调用/2-推理深度
作者：AI专家团队
日期：2025-01-10
"""

import sys
import os
import json
import re
import time
import urllib.request
import urllib.parse
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional

# 导入基础测试组件
current_dir = os.path.dirname(os.path.abspath(__file__))
base_test_dir = os.path.join(current_dir, "../2-推理深度")
sys.path.insert(0, base_test_dir)

try:
    from enhanced_cap_comparison_tester import (
        API_CONFIG, 
        EnhancedLogicDepthDetector, 
        SemanticAnalysisEngine,
        SimpleAPIClient
    )
except ImportError as e:
    print(f"❌ 导入基础测试组件失败: {e}")
    print("请确保2-推理深度目录下的测试文件存在")
    sys.exit(1)

# ==================== 逻辑锥专用任务集 ====================
LOGIC_CONE_TASKS = {
    "L0_philosophy": [
        {
            "id": "tech_philosophy",
            "name": "技术哲学思辨",
            "base_task": "分析微服务架构选择背后的哲学基础和价值导向",
            "context": "在单体架构和微服务架构之间选择时，不仅是技术决策，更是对系统复杂性、团队协作、业务发展的哲学思考",
            "expected_aspects": ["哲学基础", "价值导向", "本质思考", "长远影响"],
            "complexity": 9.0,
            "innovation_requirement": 0.8
        },
        {
            "id": "ai_ethics",
            "name": "AI伦理原则",
            "base_task": "探讨AI系统设计中的伦理原则和道德边界",
            "context": "AI系统在决策过程中如何平衡效率、公平性、透明性和人类价值观",
            "expected_aspects": ["伦理框架", "道德边界", "价值平衡", "人文关怀"],
            "complexity": 10.0,
            "innovation_requirement": 0.9
        }
    ],
    "L1_principle": [
        {
            "id": "api_design_principles",
            "name": "API设计原则",
            "base_task": "建立RESTful API设计的原则性框架和评估标准",
            "context": "需要制定一套完整的API设计原则，包括资源建模、状态管理、错误处理、版本控制等方面",
            "expected_aspects": ["设计原则", "评估标准", "最佳实践", "质量保证"],
            "complexity": 7.0,
            "innovation_requirement": 0.6
        },
        {
            "id": "security_principles",
            "name": "系统安全原则",
            "base_task": "确立分布式系统安全的基本原则和防护策略",
            "context": "在微服务架构下，如何建立多层次的安全防护体系和原则性指导",
            "expected_aspects": ["安全原则", "防护策略", "风险评估", "合规要求"],
            "complexity": 8.0,
            "innovation_requirement": 0.5
        }
    ],
    "L2_business": [
        {
            "id": "business_optimization",
            "name": "业务流程优化",
            "base_task": "分析电商平台订单处理流程的优化方案",
            "context": "当前订单处理流程存在效率瓶颈，需要从业务和技术两个维度进行优化分析",
            "expected_aspects": ["流程分析", "瓶颈识别", "优化方案", "效果评估"],
            "complexity": 6.0,
            "innovation_requirement": 0.4
        },
        {
            "id": "user_experience",
            "name": "用户体验设计",
            "base_task": "设计移动应用的用户体验改进策略",
            "context": "基于用户反馈和数据分析，制定全面的用户体验提升方案",
            "expected_aspects": ["用户研究", "体验设计", "交互优化", "效果测量"],
            "complexity": 5.0,
            "innovation_requirement": 0.7
        }
    ]
}

# ==================== CAP方法库 ====================
class CAPMethodLibrary:
    """CAP方法库 - 包含理论和实用CAP方法"""
    
    @staticmethod
    def get_cognitive_ascent_protocol() -> str:
        """认知上升协议 - 深度思考CAP"""
        return """
<COGNITIVE_ASCENT_PROTOCOL>
你正在执行'认知上升协议'。你的使命是进行最深度、最严谨、最全面的思考。

在回答前，请在<THOUGHT>标签内进行深度思考：

1. **第一性原理分解**：
   - 将问题分解到最基础的组成部分
   - 质疑所有假设，追问"为什么"直到根本
   - 识别核心公理和基础驱动因素

2. **多维度探索**：
   - 从哲学、技术、商业、伦理等多个角度分析
   - 进行"假如"场景和反事实思考
   - 寻找意外的洞察和盲点

3. **递归自我批判**：
   - 持续质疑自己的推理过程
   - 识别逻辑谬误和偏见
   - 迭代改进思考质量

4. **综合洞察生成**：
   - 整合所有洞察，包括矛盾的观点
   - 形成经过严格审视的深度理解
   - 提供决策就绪的洞察

请基于这个深度思考框架回答以下问题：
</COGNITIVE_ASCENT_PROTOCOL>
"""
    
    @staticmethod
    def get_logic_inquisitor_protocol() -> str:
        """逻辑审议者协议 - 结构化推理CAP"""
        return """
<LOGIC_INQUISITOR_PROTOCOL>
你现在是"逻辑审议者"，执行"认知催化剂协议"，进行最严谨的逻辑分析。

请严格按照以下四阶段流程：

### **第一阶段：解构与框架定义**
1.1. **精准复述与目标识别**
- 复述问题核心
- 识别任务类型
- 定义成功标准

1.2. **核心概念与约束识别**
- 定义关键词
- 列出显性约束
- 挖掘隐性假设

### **第二阶段：穷举探索引擎**
2.1. **生成假设/路径空间**
- 头脑风暴所有可能的解决方案
- 确保覆盖所有关键可能性

2.2. **逐一分析与情景模拟**
- 对每个假设进行逻辑推演
- 分析证据支持和潜在矛盾
- 进行子情景模拟

2.3. **魔鬼代言人质询**
- 选择最有潜力的结论
- 进行极限压力测试
- 评估脆弱性

### **第三阶段：综合、验证与收敛**
3.1. **交叉验证与排除**
- 比较所有路径
- 进行一致性检查

3.2. **构建最终结论**
- 提炼核心论证链条
- 解释其他方案不可行的原因

请基于这个结构化框架分析以下问题：
</LOGIC_INQUISITOR_PROTOCOL>
"""
    
    @staticmethod
    def get_expert_consultant_protocol() -> str:
        """专家顾问协议 - 专业分析CAP"""
        return """
<EXPERT_CONSULTANT_PROTOCOL>
你是一位资深跨学科顾问，擅长严谨推理、创造性发散和自我校正。

请遵循以下思考与答复准则：

1. **逐步推理**
   - 进行逐步分析，拆解问题并按逻辑顺序思考
   - 对复杂任务必须完整展开推理步骤

2. **隐藏思考、显式答案**
   - 将详细推理过程写在<thinking>标签内
   - 在<answer>标签内输出最终精炼结论
   - 确保答案独立完整、可直接阅读

3. **自我检查与反思**
   - 完成推理后进行自我审查
   - 寻找潜在谬误或遗漏，必要时修正
   - 提供多种可行方案并比较优缺点

4. **专业角色与语气**
   - 以专业、严谨口吻答复
   - 引用可靠原理、定律或最佳实践
   - 质量优先于篇幅，深度优先于速度

请基于这个专业顾问框架分析以下问题：
</EXPERT_CONSULTANT_PROTOCOL>
"""
    
    @staticmethod
    def get_semantic_enhanced_cap(semantic_analysis: Dict[str, Any]) -> str:
        """语义分析增强CAP - 基于V3语义分析引擎"""
        
        # 提取语义分析结果
        arch_patterns = list(semantic_analysis.get("architecture_patterns", {}).keys())
        design_patterns = list(semantic_analysis.get("design_patterns", {}).keys())
        
        arch_guidance = ""
        if arch_patterns:
            arch_guidance = f"- 应用识别的架构模式：{', '.join(arch_patterns)}\n"
        
        design_guidance = ""
        if design_patterns:
            design_guidance = f"- 运用识别的设计模式：{', '.join(design_patterns)}\n"
        
        return f"""
<SEMANTIC_ENHANCED_CAP>
请基于V3语义分析引擎识别的关键模式和概念进行分析：

**架构思维框架**：
{arch_guidance}- 采用系统化的架构思维，考虑组件间的交互和依赖关系
- 关注系统的可扩展性、可维护性和性能特性

**设计模式应用**：
{design_guidance}- 识别并应用适当的设计模式解决常见问题
- 避免过度设计，保持解决方案的简洁性

**认知优化指南**：
- 保持概念清晰度和一致性
- 使用系统化的分析方法
- 确保逻辑推理的完整性
- 提供具体的实施细节和示例

请基于以上语义增强框架，以最高专业水准回答以下问题：
</SEMANTIC_ENHANCED_CAP>
"""
    
    @staticmethod
    def get_header_optimized_cap() -> str:
        """头部优化CAP - 高效验证导向"""
        return """
<HEADER_OPTIMIZED_CAP>
请按照以下优化框架快速完成验证任务：

**快速分析模式**：
- 聚焦核心要点，避免过度发散
- 采用标准化分析流程，确保一致性
- 确保输出格式规范统一，便于后续处理

**效率优先原则**：
- 避免过度深入的哲学思考，专注实用性
- 专注于方案的可行性和正确性
- 提供明确的结论和置信度评估

**结构化输出**：
- 使用清晰的标题和分段
- 提供具体的建议和行动项
- 包含风险评估和缓解措施

请基于这个高效框架分析以下问题：
</HEADER_OPTIMIZED_CAP>
"""

# ==================== 逻辑锥专用评估器 ====================
class LogicConeEvaluator:
    """逻辑锥专用评估器 - 针对不同层级的专业化评估"""
    
    def __init__(self):
        self.base_evaluator = EnhancedLogicDepthDetector()
        
        # 层级特定的评估权重
        self.layer_weights = {
            "L0_philosophy": {
                "philosophical_depth": 0.40,
                "reasoning_depth": 0.30,
                "innovation_thinking": 0.20,
                "practical_value": 0.10
            },
            "L1_principle": {
                "logical_rigor": 0.35,
                "principle_clarity": 0.30,
                "systematic_thinking": 0.25,
                "practical_value": 0.10
            },
            "L2_business": {
                "business_insight": 0.35,
                "practical_value": 0.30,
                "reasoning_depth": 0.20,
                "implementation_feasibility": 0.15
            }
        }
    
    def evaluate_logic_cone_response(self, content: str, layer: str, task_info: Dict) -> Dict[str, Any]:
        """评估逻辑锥响应质量"""
        
        # 基础逻辑深度评估
        base_evaluation = self.base_evaluator.detect_logic_depth(content)
        
        # 层级特定评估
        layer_evaluation = self._evaluate_layer_specific(content, layer, task_info)
        
        # 综合评分
        weights = self.layer_weights.get(layer, self.layer_weights["L2_business"])
        
        weighted_score = 0
        for dimension, weight in weights.items():
            if dimension in layer_evaluation:
                weighted_score += layer_evaluation[dimension]["score"] * weight
            elif dimension == "reasoning_depth":
                weighted_score += base_evaluation["dimension_scores"]["reasoning_depth"] * weight
            elif dimension == "practical_value":
                weighted_score += base_evaluation["dimension_scores"]["practical_value"] * weight
        
        return {
            "overall_score": weighted_score,
            "layer": layer,
            "base_evaluation": base_evaluation,
            "layer_specific_evaluation": layer_evaluation,
            "quality_grade": self._calculate_layer_quality_grade(weighted_score, layer),
            "task_complexity_match": self._evaluate_complexity_match(content, task_info)
        }
    
    def _evaluate_layer_specific(self, content: str, layer: str, task_info: Dict) -> Dict[str, Any]:
        """层级特定评估"""
        
        if layer == "L0_philosophy":
            return self._evaluate_philosophical_depth(content)
        elif layer == "L1_principle":
            return self._evaluate_logical_rigor(content)
        elif layer == "L2_business":
            return self._evaluate_business_insight(content)
        else:
            return {}
    
    def _evaluate_philosophical_depth(self, content: str) -> Dict[str, Any]:
        """评估哲学思辨深度"""
        
        philosophical_indicators = {
            "本质思考": [r'本质', r'根本', r'核心', r'基础', r'原理'],
            "价值导向": [r'价值', r'意义', r'目的', r'愿景', r'使命'],
            "长远思考": [r'长远', r'未来', r'发展', r'演进', r'趋势'],
            "哲学概念": [r'哲学', r'思想', r'理念', r'观念', r'世界观']
        }
        
        philosophical_score = 0
        detected_indicators = []
        
        for category, patterns in philosophical_indicators.items():
            count = sum(1 for pattern in patterns if re.search(pattern, content, re.IGNORECASE))
            if count > 0:
                detected_indicators.append(f"{category}: {count}")
                philosophical_score += count * 15
        
        return {
            "philosophical_depth": {
                "score": min(philosophical_score, 100),
                "indicators": detected_indicators
            },
            "innovation_thinking": {
                "score": self._evaluate_innovation_thinking(content),
                "analysis": "创新思维评估"
            }
        }
    
    def _evaluate_logical_rigor(self, content: str) -> Dict[str, Any]:
        """评估逻辑严谨性"""
        
        logical_indicators = {
            "逻辑连接": [r'因此', r'所以', r'由于', r'基于', r'根据'],
            "结构化": [r'第一', r'第二', r'首先', r'其次', r'最后'],
            "论证": [r'证明', r'论证', r'推导', r'分析', r'验证'],
            "原则性": [r'原则', r'标准', r'规范', r'准则', r'框架']
        }
        
        logical_score = 0
        detected_indicators = []
        
        for category, patterns in logical_indicators.items():
            count = sum(1 for pattern in patterns if re.search(pattern, content, re.IGNORECASE))
            if count > 0:
                detected_indicators.append(f"{category}: {count}")
                logical_score += count * 12
        
        return {
            "logical_rigor": {
                "score": min(logical_score, 100),
                "indicators": detected_indicators
            },
            "principle_clarity": {
                "score": self._evaluate_principle_clarity(content),
                "analysis": "原则清晰度评估"
            },
            "systematic_thinking": {
                "score": self._evaluate_systematic_thinking(content),
                "analysis": "系统性思维评估"
            }
        }
    
    def _evaluate_business_insight(self, content: str) -> Dict[str, Any]:
        """评估商业洞察力"""
        
        business_indicators = {
            "商业价值": [r'价值', r'收益', r'效益', r'ROI', r'成本'],
            "用户导向": [r'用户', r'客户', r'体验', r'需求', r'满意度'],
            "市场分析": [r'市场', r'竞争', r'优势', r'机会', r'威胁'],
            "实施可行": [r'实施', r'执行', r'落地', r'可行', r'操作']
        }
        
        business_score = 0
        detected_indicators = []
        
        for category, patterns in business_indicators.items():
            count = sum(1 for pattern in patterns if re.search(pattern, content, re.IGNORECASE))
            if count > 0:
                detected_indicators.append(f"{category}: {count}")
                business_score += count * 10
        
        return {
            "business_insight": {
                "score": min(business_score, 100),
                "indicators": detected_indicators
            },
            "implementation_feasibility": {
                "score": self._evaluate_implementation_feasibility(content),
                "analysis": "实施可行性评估"
            }
        }
    
    def _evaluate_innovation_thinking(self, content: str) -> float:
        """评估创新思维"""
        innovation_patterns = [r'创新', r'突破', r'颠覆', r'变革', r'新颖', r'独特']
        count = sum(1 for pattern in innovation_patterns if re.search(pattern, content, re.IGNORECASE))
        return min(count * 20, 100)
    
    def _evaluate_principle_clarity(self, content: str) -> float:
        """评估原则清晰度"""
        clarity_patterns = [r'明确', r'清晰', r'具体', r'详细', r'准确']
        count = sum(1 for pattern in clarity_patterns if re.search(pattern, content, re.IGNORECASE))
        return min(count * 15, 100)
    
    def _evaluate_systematic_thinking(self, content: str) -> float:
        """评估系统性思维"""
        systematic_patterns = [r'系统', r'整体', r'全面', r'综合', r'统一']
        count = sum(1 for pattern in systematic_patterns if re.search(pattern, content, re.IGNORECASE))
        return min(count * 18, 100)
    
    def _evaluate_implementation_feasibility(self, content: str) -> float:
        """评估实施可行性"""
        feasibility_patterns = [r'可行', r'实用', r'操作', r'执行', r'落地']
        count = sum(1 for pattern in feasibility_patterns if re.search(pattern, content, re.IGNORECASE))
        return min(count * 16, 100)
    
    def _evaluate_complexity_match(self, content: str, task_info: Dict) -> Dict[str, Any]:
        """评估复杂度匹配度"""
        task_complexity = task_info.get("complexity", 5.0)
        content_complexity = len(content) / 100  # 简单的复杂度估算
        
        match_score = 100 - abs(task_complexity - content_complexity) * 10
        match_score = max(0, min(100, match_score))
        
        return {
            "task_complexity": task_complexity,
            "content_complexity": content_complexity,
            "match_score": match_score
        }
    
    def _calculate_layer_quality_grade(self, score: float, layer: str) -> str:
        """计算层级质量等级"""
        if layer == "L0_philosophy":
            # 哲学层要求更高
            if score >= 85:
                return "A+ (哲学卓越)"
            elif score >= 75:
                return "A (哲学优秀)"
            elif score >= 65:
                return "B (哲学良好)"
            elif score >= 55:
                return "C (哲学及格)"
            else:
                return "D (哲学需改进)"
        elif layer == "L1_principle":
            # 原则层注重逻辑
            if score >= 80:
                return "A+ (逻辑卓越)"
            elif score >= 70:
                return "A (逻辑优秀)"
            elif score >= 60:
                return "B (逻辑良好)"
            elif score >= 50:
                return "C (逻辑及格)"
            else:
                return "D (逻辑需改进)"
        else:
            # 业务层注重实用
            if score >= 75:
                return "A+ (业务卓越)"
            elif score >= 65:
                return "A (业务优秀)"
            elif score >= 55:
                return "B (业务良好)"
            elif score >= 45:
                return "C (业务及格)"
            else:
                return "D (业务需改进)"

# ==================== 主测试器类 ====================
class LogicConeCAPMethodComparator:
    """逻辑锥CAP方法深度对比测试器"""

    def __init__(self):
        self.api_client = SimpleAPIClient()
        self.evaluator = LogicConeEvaluator()
        self.semantic_engine = SemanticAnalysisEngine()
        self.cap_library = CAPMethodLibrary()

        # CAP方法配置
        self.cap_methods = {
            "cognitive_ascent": {
                "name": "认知上升协议",
                "type": "理论CAP",
                "best_model": "R1",
                "target_layers": ["L0_philosophy"],
                "generator": self.cap_library.get_cognitive_ascent_protocol
            },
            "logic_inquisitor": {
                "name": "逻辑审议者协议",
                "type": "理论CAP",
                "best_model": "R1",
                "target_layers": ["L1_principle"],
                "generator": self.cap_library.get_logic_inquisitor_protocol
            },
            "expert_consultant": {
                "name": "专家顾问协议",
                "type": "理论CAP",
                "best_model": "R1",
                "target_layers": ["L2_business"],
                "generator": self.cap_library.get_expert_consultant_protocol
            },
            "semantic_enhanced": {
                "name": "语义分析增强CAP",
                "type": "实用CAP",
                "best_model": "R1",
                "target_layers": ["L0_philosophy", "L1_principle", "L2_business"],
                "generator": self._generate_semantic_enhanced_cap
            },
            "header_optimized": {
                "name": "头部优化CAP",
                "type": "实用CAP",
                "best_model": "V3",
                "target_layers": ["L1_principle", "L2_business"],
                "generator": self.cap_library.get_header_optimized_cap
            }
        }

        # 测试模型配置
        self.models = {
            "R1": "deepseek-ai/DeepSeek-R1-0528",
            "V3": "deepseek-ai/DeepSeek-V3-0324"
        }

    def run_comprehensive_cap_comparison(self) -> Dict[str, Any]:
        """运行全面的CAP方法对比测试"""

        print("🚀 逻辑锥CAP方法深度对比测试器启动")
        print("=" * 80)
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 测试目标: 理论CAP vs 实用CAP + 模型适配性验证")
        print(f"📊 测试范围: 逻辑锥L0-L2层专用任务")
        print()

        test_results = {
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "test_framework": "逻辑锥CAP方法深度对比测试器",
                "cap_methods_tested": list(self.cap_methods.keys()),
                "models_tested": list(self.models.keys()),
                "layers_tested": ["L0_philosophy", "L1_principle", "L2_business"]
            },
            "layer_results": {},
            "cap_method_comparison": {},
            "model_adaptation_analysis": {},
            "theoretical_vs_practical_analysis": {}
        }

        # 按层级进行测试
        for layer, tasks in LOGIC_CONE_TASKS.items():
            print(f"📋 测试层级: {layer}")
            print("-" * 60)

            layer_results = {
                "layer": layer,
                "task_results": {}
            }

            for task in tasks:
                print(f"🎯 测试任务: {task['name']} ({task['id']})")

                task_results = {
                    "task_info": task,
                    "cap_method_results": {}
                }

                # 测试每种CAP方法
                for cap_method_id, cap_config in self.cap_methods.items():
                    if layer in cap_config["target_layers"]:
                        print(f"  🔧 测试CAP方法: {cap_config['name']}")

                        # 测试推荐模型
                        best_model = cap_config["best_model"]
                        result = self._test_cap_method_on_task(
                            cap_method_id, best_model, task, layer
                        )

                        task_results["cap_method_results"][cap_method_id] = result

                        if result["success"]:
                            print(f"    ✅ {cap_config['name']} + {best_model}: {result['evaluation']['overall_score']:.1f}分")
                        else:
                            print(f"    ❌ {cap_config['name']} + {best_model}: 失败")

                        time.sleep(2)  # 避免API限流

                layer_results["task_results"][task["id"]] = task_results
                print()

            test_results["layer_results"][layer] = layer_results
            print(f"✅ 层级 {layer} 测试完成")
            print()

        # 生成综合分析
        test_results["cap_method_comparison"] = self._generate_cap_method_comparison(test_results["layer_results"])
        test_results["model_adaptation_analysis"] = self._generate_model_adaptation_analysis(test_results["layer_results"])
        test_results["theoretical_vs_practical_analysis"] = self._generate_theoretical_vs_practical_analysis(test_results["layer_results"])

        # 输出最终报告
        self._generate_comprehensive_report(test_results)

        return test_results

    def _test_cap_method_on_task(self, cap_method_id: str, model: str, task: Dict, layer: str) -> Dict[str, Any]:
        """测试特定CAP方法在特定任务上的表现"""

        try:
            # 生成CAP增强提示
            cap_prompt = self._generate_cap_prompt(cap_method_id, task, layer)

            # 执行AI调用
            model_name = self.models[model]
            ai_result = self.api_client.call_api(model_name, cap_prompt)

            if not ai_result["success"]:
                return {
                    "success": False,
                    "error": ai_result["error"],
                    "cap_method": cap_method_id,
                    "model": model
                }

            # 评估结果
            content = ai_result["content"]
            if "R1" in model_name and ai_result.get("reasoning_content"):
                analysis_content = ai_result["reasoning_content"] + "\n\n" + content
            else:
                analysis_content = content

            evaluation = self.evaluator.evaluate_logic_cone_response(analysis_content, layer, task)

            return {
                "success": True,
                "cap_method": cap_method_id,
                "model": model,
                "layer": layer,
                "task_id": task["id"],
                "ai_result": ai_result,
                "evaluation": evaluation,
                "content_length": len(content),
                "reasoning_length": len(ai_result.get("reasoning_content", "")),
                "token_usage": ai_result.get("token_usage", {}),
                "processing_time": ai_result.get("processing_time", 0)
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "cap_method": cap_method_id,
                "model": model
            }

    def _generate_cap_prompt(self, cap_method_id: str, task: Dict, layer: str) -> str:
        """生成CAP增强提示"""

        cap_config = self.cap_methods[cap_method_id]

        if cap_method_id == "semantic_enhanced":
            # 语义增强CAP需要先进行语义分析
            content_to_analyze = task["context"] + " " + task["base_task"]
            semantic_analysis = self.semantic_engine.analyze_content(content_to_analyze)
            cap_header = cap_config["generator"](semantic_analysis)
        else:
            # 其他CAP方法直接生成
            cap_header = cap_config["generator"]()

        # 组合完整提示
        full_prompt = f"""{cap_header}

{task["base_task"]}

**任务上下文**：
{task["context"]}

**期望分析维度**：
{', '.join(task["expected_aspects"])}

**任务复杂度**：{task["complexity"]}/10
**创新需求**：{task["innovation_requirement"]}/1.0
"""

        return full_prompt

    def _generate_semantic_enhanced_cap(self, semantic_analysis: Dict[str, Any]) -> str:
        """生成语义增强CAP"""
        return self.cap_library.get_semantic_enhanced_cap(semantic_analysis)

    def _generate_cap_method_comparison(self, layer_results: Dict) -> Dict[str, Any]:
        """生成CAP方法对比分析"""

        cap_method_stats = {}

        for layer, layer_data in layer_results.items():
            for task_id, task_data in layer_data["task_results"].items():
                for cap_method, result in task_data["cap_method_results"].items():
                    if result.get("success"):
                        if cap_method not in cap_method_stats:
                            cap_method_stats[cap_method] = {
                                "scores": [],
                                "layers": [],
                                "success_count": 0,
                                "total_attempts": 0
                            }

                        cap_method_stats[cap_method]["scores"].append(result["evaluation"]["overall_score"])
                        cap_method_stats[cap_method]["layers"].append(layer)
                        cap_method_stats[cap_method]["success_count"] += 1

                    if cap_method not in cap_method_stats:
                        cap_method_stats[cap_method] = {
                            "scores": [],
                            "layers": [],
                            "success_count": 0,
                            "total_attempts": 0
                        }
                    cap_method_stats[cap_method]["total_attempts"] += 1

        # 计算统计数据
        comparison = {}
        for cap_method, stats in cap_method_stats.items():
            if stats["scores"]:
                comparison[cap_method] = {
                    "average_score": sum(stats["scores"]) / len(stats["scores"]),
                    "max_score": max(stats["scores"]),
                    "min_score": min(stats["scores"]),
                    "success_rate": stats["success_count"] / stats["total_attempts"] * 100,
                    "tested_layers": list(set(stats["layers"])),
                    "total_tests": stats["total_attempts"]
                }
            else:
                comparison[cap_method] = {
                    "average_score": 0,
                    "max_score": 0,
                    "min_score": 0,
                    "success_rate": 0,
                    "tested_layers": [],
                    "total_tests": stats["total_attempts"]
                }

        # 排名分析
        successful_methods = [(name, data) for name, data in comparison.items() if data["success_rate"] > 0]
        if successful_methods:
            quality_ranking = sorted(successful_methods, key=lambda x: x[1]["average_score"], reverse=True)
            comparison["rankings"] = {
                "quality": [{"method": name, "score": data["average_score"]} for name, data in quality_ranking],
                "best_overall": quality_ranking[0][0] if quality_ranking else None
            }

        return comparison

    def _generate_model_adaptation_analysis(self, layer_results: Dict) -> Dict[str, Any]:
        """生成模型适配性分析"""

        model_performance = {"R1": [], "V3": []}

        for layer, layer_data in layer_results.items():
            for task_id, task_data in layer_data["task_results"].items():
                for cap_method, result in task_data["cap_method_results"].items():
                    if result.get("success"):
                        model = result["model"]
                        if model in model_performance:
                            model_performance[model].append({
                                "score": result["evaluation"]["overall_score"],
                                "layer": layer,
                                "cap_method": cap_method,
                                "reasoning_length": result.get("reasoning_length", 0)
                            })

        analysis = {}
        for model, performances in model_performance.items():
            if performances:
                scores = [p["score"] for p in performances]
                reasoning_lengths = [p["reasoning_length"] for p in performances]

                analysis[model] = {
                    "average_score": sum(scores) / len(scores),
                    "max_score": max(scores),
                    "min_score": min(scores),
                    "average_reasoning_length": sum(reasoning_lengths) / len(reasoning_lengths) if reasoning_lengths else 0,
                    "total_tests": len(performances),
                    "layer_distribution": self._count_layer_distribution([p["layer"] for p in performances])
                }

        return analysis

    def _generate_theoretical_vs_practical_analysis(self, layer_results: Dict) -> Dict[str, Any]:
        """生成理论CAP vs 实用CAP分析"""

        theoretical_caps = ["cognitive_ascent", "logic_inquisitor", "expert_consultant"]
        practical_caps = ["semantic_enhanced", "header_optimized"]

        theoretical_scores = []
        practical_scores = []

        for layer, layer_data in layer_results.items():
            for task_id, task_data in layer_data["task_results"].items():
                for cap_method, result in task_data["cap_method_results"].items():
                    if result.get("success"):
                        score = result["evaluation"]["overall_score"]
                        if cap_method in theoretical_caps:
                            theoretical_scores.append(score)
                        elif cap_method in practical_caps:
                            practical_scores.append(score)

        analysis = {
            "theoretical_caps": {
                "average_score": sum(theoretical_scores) / len(theoretical_scores) if theoretical_scores else 0,
                "max_score": max(theoretical_scores) if theoretical_scores else 0,
                "test_count": len(theoretical_scores)
            },
            "practical_caps": {
                "average_score": sum(practical_scores) / len(practical_scores) if practical_scores else 0,
                "max_score": max(practical_scores) if practical_scores else 0,
                "test_count": len(practical_scores)
            }
        }

        if theoretical_scores and practical_scores:
            analysis["comparison"] = {
                "theoretical_advantage": analysis["theoretical_caps"]["average_score"] - analysis["practical_caps"]["average_score"],
                "better_approach": "theoretical" if analysis["theoretical_caps"]["average_score"] > analysis["practical_caps"]["average_score"] else "practical"
            }

        return analysis

    def _count_layer_distribution(self, layers: List[str]) -> Dict[str, int]:
        """统计层级分布"""
        distribution = {}
        for layer in layers:
            distribution[layer] = distribution.get(layer, 0) + 1
        return distribution

    def _generate_comprehensive_report(self, test_results: Dict[str, Any]) -> None:
        """生成综合测试报告"""

        print("\n" + "=" * 100)
        print("📊 逻辑锥CAP方法深度对比测试报告")
        print("=" * 100)

        # 1. 测试概览
        metadata = test_results["metadata"]
        print(f"\n🎯 测试概览:")
        print(f"   测试时间: {metadata['timestamp']}")
        print(f"   CAP方法数: {len(metadata['cap_methods_tested'])}")
        print(f"   测试模型数: {len(metadata['models_tested'])}")
        print(f"   测试层级数: {len(metadata['layers_tested'])}")

        # 2. CAP方法对比
        cap_comparison = test_results.get("cap_method_comparison", {})
        print(f"\n🔧 CAP方法对比分析:")

        for cap_method, data in cap_comparison.items():
            if cap_method != "rankings":
                cap_config = self.cap_methods.get(cap_method, {})
                cap_name = cap_config.get("name", cap_method)
                cap_type = cap_config.get("type", "未知")

                print(f"   {cap_name} ({cap_type}):")
                print(f"     平均质量分: {data['average_score']:.1f}")
                print(f"     最高分: {data['max_score']:.1f}")
                print(f"     成功率: {data['success_rate']:.1f}%")
                print(f"     测试层级: {', '.join(data['tested_layers'])}")

        # 3. 排名分析
        if "rankings" in cap_comparison:
            rankings = cap_comparison["rankings"]
            print(f"\n🏆 CAP方法质量排名:")
            for i, rank_data in enumerate(rankings["quality"], 1):
                method_name = self.cap_methods.get(rank_data["method"], {}).get("name", rank_data["method"])
                print(f"     {i}. {method_name}: {rank_data['score']:.1f}分")

            if rankings.get("best_overall"):
                best_method = self.cap_methods.get(rankings["best_overall"], {}).get("name", rankings["best_overall"])
                print(f"   🥇 最佳CAP方法: {best_method}")

        # 4. 模型适配性分析
        model_analysis = test_results.get("model_adaptation_analysis", {})
        print(f"\n🤖 模型适配性分析:")

        for model, data in model_analysis.items():
            print(f"   {model}模型:")
            print(f"     平均质量分: {data['average_score']:.1f}")
            print(f"     最高分: {data['max_score']:.1f}")
            print(f"     平均推理长度: {data['average_reasoning_length']:.0f}字符")
            print(f"     测试次数: {data['total_tests']}")

        # 5. 理论vs实用分析
        theoretical_analysis = test_results.get("theoretical_vs_practical_analysis", {})
        print(f"\n💡 理论CAP vs 实用CAP分析:")

        if "theoretical_caps" in theoretical_analysis:
            theoretical = theoretical_analysis["theoretical_caps"]
            practical = theoretical_analysis["practical_caps"]

            print(f"   理论CAP方法:")
            print(f"     平均质量分: {theoretical['average_score']:.1f}")
            print(f"     最高分: {theoretical['max_score']:.1f}")
            print(f"     测试次数: {theoretical['test_count']}")

            print(f"   实用CAP方法:")
            print(f"     平均质量分: {practical['average_score']:.1f}")
            print(f"     最高分: {practical['max_score']:.1f}")
            print(f"     测试次数: {practical['test_count']}")

            if "comparison" in theoretical_analysis:
                comparison = theoretical_analysis["comparison"]
                advantage = comparison["theoretical_advantage"]
                better = comparison["better_approach"]

                print(f"   🔍 对比结果:")
                print(f"     理论CAP优势: {advantage:+.1f}分")
                print(f"     更优方法: {'理论CAP' if better == 'theoretical' else '实用CAP'}")

        print("\n" + "=" * 100)
        print("✅ 逻辑锥CAP方法深度对比测试完成")
        print("🎯 为逻辑锥架构提供了科学的CAP方法选择依据")

# ==================== 主函数 ====================
def main():
    """主函数 - 执行逻辑锥CAP方法深度对比测试"""

    print("🎯 逻辑锥CAP方法深度对比测试器")
    print("目标：理论CAP vs 实用CAP + R1/V3模型适配性验证")
    print("特性：逻辑锥L0-L2层专用任务 + 层级特定评估")
    print()

    # 创建测试器
    comparator = LogicConeCAPMethodComparator()

    # 运行测试
    try:
        results = comparator.run_comprehensive_cap_comparison()

        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"logic_cone_cap_comparison_report_{timestamp}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"\n💾 详细测试数据已保存: {filename}")
        print("🎉 逻辑锥CAP方法深度对比测试完成！")

        return results

    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return None
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
