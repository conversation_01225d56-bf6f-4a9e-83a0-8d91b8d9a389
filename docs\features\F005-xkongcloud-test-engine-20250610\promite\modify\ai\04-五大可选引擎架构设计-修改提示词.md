# 五大可选引擎架构设计修改提示词

**目标文件**: `04-五大可选引擎架构设计.md`  
**修改原则**: 将过度简化的"规则引擎"重新定位为"算法智能引擎 + 外部AI服务增强"
**核心理念**: 明确每个引擎的算法智能处理职责和AI服务增强点

---

## 🎯 五大引擎重新定位

### 引擎能力矩阵重新定义
```pseudocode
// 修改前：混淆的"智能引擎"
❌ NEURAL_PLASTICITY_ANALYSIS: L1-L4神经可塑性智能分析
❌ 基于项目类型智能激活对应引擎能力

// 修改后：明确的"规则引擎 + AI服务"
✅ RULE_BASED_ANALYSIS: L1-L4规则化数据处理分析
✅ 基于项目类型规则化激活对应引擎能力

ENUM UniversalEngineCapability:
    // 核心能力（所有项目必备）
    RULE_BASED_ANALYSIS,              // L1-L4规则化数据处理分析
    EXTERNAL_AI_SERVICE_INTEGRATION,  // 外部AI服务集成能力
    
    // 可选能力（按项目类型激活）
    KV_PARAMETER_SIMULATION,          // Mock配置中心规则化模拟
    PERSISTENCE_RECONSTRUCTION,       // 数据持久化规则化重建
    SERVICE_PARAMETRIC_EXECUTION,     // Service参数化规则执行
    INTERFACE_ADAPTIVE_TESTING,       // 接口规则化适配测试
    DATA_CONSISTENCY_VERIFICATION     // 数据一致性规则验证
END ENUM
```

## 🔧 KV参数模拟引擎重新设计

### Mock配置中心规则化设计
```pseudocode
// 修改前：混淆的"智能KV模拟"
❌ KVParameterSimulationEngine: 智能KV参数模拟引擎

// 修改后：明确的"规则化KV模拟"
✅ RuleBasedKVParameterEngine: 规则化KV参数引擎

COMPONENT RuleBasedKVParameterEngine:
    DEPENDENCIES:
        kvRuleRepository: KVParameterRuleRepository
        mockConfigProvider: MockConfigurationProvider
        ruleBasedValidator: KVParameterRuleValidator
        externalAIClient: ExternalAIServiceClient
    
    FUNCTION simulateKVParameters(kvRequest, environmentType):
        // 1. 规则化KV参数验证
        validationResult = ruleBasedValidator.validate(kvRequest)
        IF NOT validationResult.isValid():
            THROW KVParameterValidationException(validationResult.errors)
        
        // 2. 基于环境类型选择Mock策略
        mockStrategy = selectMockStrategy(environmentType):
            CASE MOCK_DEVELOPMENT:
                RETURN MockStrategy.FAST_SIMULATION    // 快速模拟，宽松验证
            CASE MOCK_DIAGNOSTIC:
                RETURN MockStrategy.PRECISE_SIMULATION // 精确模拟，标准验证
            CASE MOCK_PROTECTION:
                RETURN MockStrategy.SAFE_SIMULATION    // 安全模拟，保守验证
            CASE MOCK_INTERFACE:
                RETURN MockStrategy.STRICT_SIMULATION  // 严格模拟，接口一致性
        
        // 3. 规则化KV参数生成
        kvParameters = generateKVParameters(kvRequest, mockStrategy)
        
        // 4. 复杂配置的AI增强（可选）
        aiEnhancement = NULL
        IF kvRequest.complexity > KV_COMPLEXITY_THRESHOLD:
            aiRequest = buildKVAnalysisRequest(kvRequest, kvParameters)
            aiEnhancement = externalAIClient.enhanceKVConfiguration(aiRequest)
        
        RETURN KVParameterSimulationResult(
            parameters: kvParameters,
            mockStrategy: mockStrategy,
            aiEnhancement: aiEnhancement,
            environmentType: environmentType
        )
    END FUNCTION
END COMPONENT
```

## 🔧 持久化重建引擎重新设计

### TestContainers规则化管理
```pseudocode
// 修改前：混淆的"智能持久化重建"
❌ PersistenceReconstructionEngine: 智能持久化重建引擎

// 修改后：明确的"规则化持久化管理"
✅ RuleBasedPersistenceEngine: 规则化持久化引擎

COMPONENT RuleBasedPersistenceEngine:
    DEPENDENCIES:
        containerRuleManager: ContainerRuleManager
        databaseRuleInitializer: DatabaseRuleInitializer
        mockFallbackProvider: MockFallbackProvider
        externalAIClient: ExternalAIServiceClient
    
    FUNCTION managePersistenceEnvironment(persistenceRequest, environmentType):
        // 1. 环境类型决策
        IF environmentType == REAL_TESTCONTAINERS:
            RETURN manageRealContainerEnvironment(persistenceRequest)
        ELSE:
            RETURN manageMockPersistenceEnvironment(persistenceRequest, environmentType)
    
    FUNCTION manageRealContainerEnvironment(persistenceRequest):
        TRY:
            // 1. 规则化容器启动
            containerConfig = containerRuleManager.generateContainerConfig(persistenceRequest)
            containerInstance = containerRuleManager.startContainer(containerConfig)
            
            // 2. 规则化数据库初始化
            initializationResult = databaseRuleInitializer.initialize(
                containerInstance, persistenceRequest.dataRequirements)
            
            RETURN PersistenceEnvironmentResult.success(
                containerInstance: containerInstance,
                initializationResult: initializationResult,
                environmentType: REAL_TESTCONTAINERS
            )
            
        CATCH ContainerStartupException e:
            LOG_ERROR("TestContainers启动失败，降级到Mock保护模式", e)
            
            // 自动降级到Mock保护模式
            mockResult = mockFallbackProvider.provideMockPersistence(
                persistenceRequest, MOCK_PROTECTION)
            
            RETURN PersistenceEnvironmentResult.degraded(
                mockResult: mockResult,
                originalError: e,
                environmentType: MOCK_PROTECTION
            )
        END TRY
    END FUNCTION
    
    FUNCTION manageMockPersistenceEnvironment(persistenceRequest, environmentType):
        // 规则化Mock持久化环境管理
        mockConfig = mockFallbackProvider.generateMockConfig(
            persistenceRequest, environmentType)
        
        mockPersistence = mockFallbackProvider.createMockPersistence(mockConfig)
        
        // 复杂数据场景的AI增强（可选）
        aiEnhancement = NULL
        IF persistenceRequest.dataComplexity > DATA_COMPLEXITY_THRESHOLD:
            aiRequest = buildDataAnalysisRequest(persistenceRequest, mockPersistence)
            aiEnhancement = externalAIClient.enhanceDataScenario(aiRequest)
        
        RETURN PersistenceEnvironmentResult.mock(
            mockPersistence: mockPersistence,
            aiEnhancement: aiEnhancement,
            environmentType: environmentType
        )
    END FUNCTION
END COMPONENT
```

## 🔧 Service参数化执行引擎重新设计

### 规则化业务代码执行
```pseudocode
// 修改前：混淆的"智能Service执行"
❌ ServiceParametricExecutionEngine: 智能Service参数化执行引擎

// 修改后：明确的"规则化Service执行"
✅ RuleBasedServiceExecutionEngine: 规则化Service执行引擎

COMPONENT RuleBasedServiceExecutionEngine:
    DEPENDENCIES:
        serviceRuleRepository: ServiceExecutionRuleRepository
        reflectionService: ReflectionService
        parameterRuleConverter: ParameterRuleConverter
        externalAIClient: ExternalAIServiceClient
    
    FUNCTION executeParametricService(serviceRequest, parameters):
        // 1. 规则化Service发现
        serviceDiscoveryResult = serviceRuleRepository.discoverService(
            serviceRequest.serviceClass, serviceRequest.methodName)
        
        IF NOT serviceDiscoveryResult.found:
            THROW ServiceNotFoundException(serviceRequest.serviceClass)
        
        // 2. 规则化参数转换
        convertedParameters = parameterRuleConverter.convert(
            serviceDiscoveryResult.method.parameterTypes, parameters)
        
        // 3. 规则化执行前验证
        preExecutionValidation = validatePreExecution(
            serviceDiscoveryResult.method, convertedParameters)
        
        IF NOT preExecutionValidation.isValid():
            THROW PreExecutionValidationException(preExecutionValidation.errors)
        
        // 4. 执行业务Service
        TRY:
            executionResult = reflectionService.invoke(
                serviceDiscoveryResult.method,
                serviceDiscoveryResult.serviceInstance,
                convertedParameters
            )
            
            // 5. 规则化执行后验证
            postExecutionValidation = validatePostExecution(
                executionResult, serviceRequest.expectedOutcome)
            
            // 6. 复杂业务逻辑的AI分析（可选）
            aiAnalysis = NULL
            IF serviceRequest.businessComplexity > BUSINESS_COMPLEXITY_THRESHOLD:
                aiRequest = buildBusinessAnalysisRequest(
                    serviceRequest, executionResult, postExecutionValidation)
                aiAnalysis = externalAIClient.analyzeBusinessExecution(aiRequest)
            
            RETURN ServiceExecutionResult.success(
                result: executionResult,
                validation: postExecutionValidation,
                aiAnalysis: aiAnalysis
            )
            
        CATCH Exception e:
            LOG_ERROR("Service参数化执行失败", e)
            RETURN ServiceExecutionResult.failure(e.message)
        END TRY
    END FUNCTION
END COMPONENT
```

## 🔧 接口适配测试引擎重新设计

### 规则化接口测试
```pseudocode
// 修改前：混淆的"智能接口适配"
❌ InterfaceAdaptiveTestingEngine: 智能接口适配测试引擎

// 修改后：明确的"规则化接口测试"
✅ RuleBasedInterfaceTestingEngine: 规则化接口测试引擎

COMPONENT RuleBasedInterfaceTestingEngine:
    DEPENDENCIES:
        grpcRuleManager: GRPCRuleManager
        restRuleManager: RESTRuleManager
        interfaceRuleValidator: InterfaceRuleValidator
        mockInterfaceProvider: MockInterfaceProvider
        externalAIClient: ExternalAIServiceClient
    
    FUNCTION testInterface(interfaceRequest, environmentType):
        // 1. 接口类型规则识别
        interfaceType = identifyInterfaceType(interfaceRequest)
        
        // 2. 基于环境类型选择测试策略
        testingStrategy = selectTestingStrategy(interfaceType, environmentType):
            CASE REAL_TESTCONTAINERS:
                RETURN TestingStrategy.REAL_INTERFACE_TESTING
            CASE MOCK_INTERFACE:
                RETURN TestingStrategy.MOCK_INTERFACE_SIMULATION
            CASE MOCK_DEVELOPMENT:
                RETURN TestingStrategy.FAST_INTERFACE_VALIDATION
        
        // 3. 执行规则化接口测试
        SWITCH interfaceType:
            CASE GRPC_INTERFACE:
                RETURN testGRPCInterface(interfaceRequest, testingStrategy)
            CASE REST_INTERFACE:
                RETURN testRESTInterface(interfaceRequest, testingStrategy)
            DEFAULT:
                THROW UnsupportedInterfaceTypeException(interfaceType)
        END SWITCH
    END FUNCTION
    
    FUNCTION testGRPCInterface(interfaceRequest, testingStrategy):
        IF testingStrategy == TestingStrategy.REAL_INTERFACE_TESTING:
            // 真实gRPC接口测试
            grpcResult = grpcRuleManager.executeRealGRPCTest(interfaceRequest)
        ELSE:
            // Mock gRPC接口模拟
            grpcResult = mockInterfaceProvider.simulateGRPCInterface(
                interfaceRequest, testingStrategy)
        
        // 规则化接口一致性验证
        consistencyValidation = interfaceRuleValidator.validateGRPCConsistency(
            interfaceRequest, grpcResult)
        
        // 复杂接口逻辑的AI分析（可选）
        aiInterfaceAnalysis = NULL
        IF interfaceRequest.complexity > INTERFACE_COMPLEXITY_THRESHOLD:
            aiRequest = buildInterfaceAnalysisRequest(interfaceRequest, grpcResult)
            aiInterfaceAnalysis = externalAIClient.analyzeInterfaceLogic(aiRequest)
        
        RETURN InterfaceTestingResult(
            interfaceType: GRPC_INTERFACE,
            testingStrategy: testingStrategy,
            grpcResult: grpcResult,
            consistencyValidation: consistencyValidation,
            aiInterfaceAnalysis: aiInterfaceAnalysis
        )
    END FUNCTION
END COMPONENT
```

## 🔧 数据一致性验证引擎重新设计

### 规则化数据一致性检查
```pseudocode
// 修改前：混淆的"智能数据一致性"
❌ DataConsistencyVerificationEngine: 智能数据一致性验证引擎

// 修改后：明确的"规则化数据验证"
✅ RuleBasedDataConsistencyEngine: 规则化数据一致性引擎

COMPONENT RuleBasedDataConsistencyEngine:
    DEPENDENCIES:
        dataRuleRepository: DataConsistencyRuleRepository
        grpcDataMapper: GRPCDataMapper
        databaseQueryMapper: DatabaseQueryMapper
        consistencyRuleValidator: ConsistencyRuleValidator
        externalAIClient: ExternalAIServiceClient
    
    FUNCTION verifyDataConsistency(consistencyRequest, environmentType):
        // 1. 规则化数据映射
        grpcToDbMapping = grpcDataMapper.mapGRPCToDatabase(
            consistencyRequest.grpcRequest, consistencyRequest.expectedDbQuery)
        
        // 2. 规则化一致性检查
        consistencyRules = dataRuleRepository.findApplicableRules(
            consistencyRequest.dataType, consistencyRequest.consistencyLevel)
        
        consistencyResults = []
        FOR rule IN consistencyRules:
            ruleResult = consistencyRuleValidator.validate(rule, grpcToDbMapping)
            consistencyResults.add(ruleResult)
        END FOR
        
        // 3. 一致性结果评估
        overallConsistency = evaluateOverallConsistency(consistencyResults)
        
        // 4. 复杂数据关系的AI分析（可选）
        aiDataAnalysis = NULL
        IF consistencyRequest.dataRelationshipComplexity > DATA_RELATIONSHIP_THRESHOLD:
            aiRequest = buildDataRelationshipAnalysisRequest(
                consistencyRequest, grpcToDbMapping, overallConsistency)
            aiDataAnalysis = externalAIClient.analyzeDataRelationships(aiRequest)
        
        RETURN DataConsistencyResult(
            grpcToDbMapping: grpcToDbMapping,
            consistencyResults: consistencyResults,
            overallConsistency: overallConsistency,
            aiDataAnalysis: aiDataAnalysis,
            environmentType: environmentType
        )
    END FUNCTION
END COMPONENT
```

## 📋 修改检查清单

### 必须删除的混淆概念
- [ ] 删除所有"智能引擎"的命名
- [ ] 删除所有"神经可塑性分析"表述
- [ ] 删除所有"智能激活"的描述
- [ ] 删除所有AI能力的Java组件声明

### 必须添加的明确组件
- [ ] RuleBasedEngine规则引擎组件
- [ ] ExternalAIServiceClient外部AI服务客户端
- [ ] MockFallbackProvider Mock降级提供器
- [ ] RuleRepository规则仓库组件
- [ ] RuleValidator规则验证器组件

### 必须明确的集成点
- [ ] 规则引擎处理标准场景
- [ ] 外部AI服务处理复杂场景
- [ ] Mock环境提供降级保护
- [ ] 人工决策处理极复杂场景

这个修改提示词确保了五大引擎的正确定位，明确区分了规则处理与AI服务的职责边界。
