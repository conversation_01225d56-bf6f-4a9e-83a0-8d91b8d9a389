#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAP方法对比测试器
对比内容嵌入式CAP vs 外部头部式CAP的实际效果差异

测试目标：
1. 方案A：内容嵌入式CAP（两次AI调用）
2. 方案B：外部头部式CAP（头部规则嵌入）
3. 使用结构化提示词工程模板
4. 模拟实际任务场景
5. 对比质量差异和效率差异

作者：AI助手
日期：2025-01-10
"""

import json
import re
import time
import urllib.request
import urllib.parse
from datetime import datetime
from typing import Dict, List, Any, Tuple

# ==================== 配置信息 ====================
API_CONFIG = {
    "url": "https://api.gmi-serving.com/v1/chat/completions",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjA4ZDMxOTBjLWNjNzItNDc4ZS1hOGYwLTY3NmEwMGY1MDY2ZCIsInR5cGUiOiJpZV9tb2RlbCJ9.q7zRm2BAySo2zOi2QL5m6ukgmbhMZm2Ig4ITmJ3ZsM8",
    "models": [
        "deepseek-ai/DeepSeek-V3-0324",
        "deepseek-ai/DeepSeek-R1-0528"
    ]
}

# ==================== 实际任务测试集 ====================
REAL_WORLD_TASKS = [
    {
        "id": "code_review",
        "name": "代码审查任务",
        "base_task": "请审查以下Python代码的质量，找出潜在问题并提供改进建议",
        "context": """
def process_user_data(data):
    result = []
    for item in data:
        if item['status'] == 'active':
            result.append(item['name'].upper())
    return result
""",
        "expected_aspects": ["代码质量", "性能优化", "错误处理", "可读性"]
    },
    {
        "id": "system_design",
        "name": "系统设计任务", 
        "base_task": "设计一个支持10万并发用户的在线聊天系统架构",
        "context": "需要考虑实时性、可扩展性、数据一致性和成本控制",
        "expected_aspects": ["架构设计", "技术选型", "扩展性", "成本分析"]
    },
    {
        "id": "problem_solving",
        "name": "问题解决任务",
        "base_task": "分析并解决数据库查询性能问题",
        "context": "用户反馈系统响应缓慢，初步排查发现某些查询耗时超过5秒",
        "expected_aspects": ["问题诊断", "解决方案", "优化策略", "预防措施"]
    }
]

# ==================== 结构化提示词工程模板 ====================
class StructuredPromptTemplate:
    """结构化提示词工程模板生成器"""
    
    @staticmethod
    def generate_structured_prompt(task_description: str, context: str = "", 
                                 role: str = "专业顾问", 
                                 expected_aspects: List[str] = None) -> str:
        """生成结构化提示词"""
        
        instructions = [
            "深入理解任务的核心要求和约束条件",
            "运用专业知识和最佳实践进行分析",
            "提供具体、可操作的建议和解决方案",
            "考虑多个角度和潜在的风险因素",
            "确保回答的逻辑性和结构化",
            "使用清晰、专业的语言表达",
            "提供具体的示例或案例支持",
            "考虑实际实施的可行性",
            "评估解决方案的优缺点",
            "提供量化的评估指标",
            "考虑长期影响和可持续性",
            "确保方案的完整性和系统性",
            "提供备选方案和应急预案",
            "考虑成本效益和资源配置",
            "确保符合行业标准和最佳实践"
        ]
        
        format_template = """
**分析概述**
[插入问题核心分析]

**详细方案**
[插入具体解决方案]

**实施建议**
[插入实施步骤和注意事项]

**风险评估**
[插入潜在风险和缓解措施]

**总结建议**
[插入最终建议和关键要点]
"""
        
        example = f"""
原始任务示例：优化网站加载速度
回答示例：
**分析概述**
网站加载速度问题主要涉及前端资源优化、服务器性能和网络传输三个层面...

**详细方案**
1. 前端优化：压缩CSS/JS文件，优化图片格式...
2. 服务器优化：启用缓存机制，优化数据库查询...
"""
        
        structured_prompt = f"""===
Role: {role}

===
Task: {task_description}

===
Context: {context}

===
Instructions:
{chr(10).join([f"{i+1}. {instruction}" for i, instruction in enumerate(instructions)])}

===
Format: {format_template}

===
Example: {example}

===
What's Next:
请按照上述结构化要求完成任务分析，确保回答全面、专业、可操作。
"""
        
        return structured_prompt

# ==================== LogicDepthDetector ====================
class LogicDepthDetector:
    """逻辑深度检测器 - 评估回答质量"""
    
    def __init__(self):
        self.weights = {
            "reasoning_depth": 0.35,
            "logical_structure": 0.25, 
            "concept_complexity": 0.20,
            "practical_value": 0.20
        }
    
    def detect_logic_depth(self, content: str) -> Dict[str, Any]:
        """检测内容的逻辑深度"""
        
        reasoning_result = self._analyze_reasoning_depth(content)
        structure_result = self._analyze_logical_structure(content)
        complexity_result = self._analyze_concept_complexity(content)
        practical_result = self._analyze_practical_value(content)
        
        overall_score = (
            reasoning_result["score"] * self.weights["reasoning_depth"] +
            structure_result["score"] * self.weights["logical_structure"] +
            complexity_result["score"] * self.weights["concept_complexity"] +
            practical_result["score"] * self.weights["practical_value"]
        )
        
        return {
            "overall_score": overall_score,
            "dimension_scores": {
                "reasoning_depth": reasoning_result["score"],
                "logical_structure": structure_result["score"],
                "concept_complexity": complexity_result["score"],
                "practical_value": practical_result["score"]
            },
            "quality_grade": self._calculate_quality_grade(overall_score),
            "detailed_analysis": {
                "reasoning": reasoning_result,
                "structure": structure_result,
                "complexity": complexity_result,
                "practical": practical_result
            }
        }
    
    def _analyze_reasoning_depth(self, content: str) -> Dict[str, Any]:
        """分析推理深度"""
        depth_patterns = {
            "因果推理": [r'因为.*所以', r'由于.*导致', r'基于.*可以'],
            "层次分析": [r'首先.*其次.*最后', r'第一.*第二.*第三'],
            "对比论证": [r'相比.*而言', r'与.*不同', r'优于.*在于'],
            "假设验证": [r'假设.*那么', r'如果.*则'],
            "归纳演绎": [r'综上所述', r'总结.*规律', r'可以得出']
        }
        
        detected_patterns = []
        total_score = 0
        
        for category, patterns in depth_patterns.items():
            count = sum(1 for pattern in patterns if re.search(pattern, content))
            if count > 0:
                detected_patterns.append(f"{category}: {count}个")
                total_score += count * 12
        
        reasoning_chain_length = len(re.findall(r'[因由基].*?[所导可]', content))
        chain_score = min(reasoning_chain_length * 8, 40)
        total_score += chain_score
        
        final_score = min(total_score, 100)
        
        return {
            "score": final_score,
            "patterns_detected": detected_patterns,
            "reasoning_chain_length": reasoning_chain_length
        }
    
    def _analyze_logical_structure(self, content: str) -> Dict[str, Any]:
        """分析逻辑结构"""
        structure_indicators = {
            "结构化标记": [r'\d+\.', r'[一二三四五六七八九十]+、', r'[ABCDEFG]\.', r'##', r'###'],
            "逻辑连接词": [r'然而', r'但是', r'因此', r'所以', r'另外', r'此外', r'同时'],
            "论证结构": [r'分析.*', r'方案.*', r'建议.*'],
            "层次递进": [r'进一步', r'更深层次', r'深入分析', r'具体而言']
        }
        
        structure_score = 0
        detected_structures = []
        
        for category, patterns in structure_indicators.items():
            count = sum(1 for pattern in patterns if re.search(pattern, content))
            if count > 0:
                detected_structures.append(f"{category}: {count}个")
                structure_score += count * 10
        
        paragraphs = content.split('\n\n')
        paragraph_score = min(len(paragraphs) * 5, 30)
        structure_score += paragraph_score
        
        final_score = min(structure_score, 100)
        
        return {
            "score": final_score,
            "structures_detected": detected_structures,
            "paragraph_count": len(paragraphs)
        }
    
    def _analyze_concept_complexity(self, content: str) -> Dict[str, Any]:
        """分析概念复杂度"""
        complexity_indicators = {
            "技术概念": [r'架构', r'算法', r'协议', r'框架', r'模式', r'机制'],
            "抽象概念": [r'原理', r'本质', r'规律', r'模型', r'理论', r'方法论'],
            "系统概念": [r'系统', r'平台', r'生态', r'环境', r'基础设施'],
            "实践概念": [r'实施', r'部署', r'优化', r'监控', r'维护']
        }
        
        concept_score = 0
        detected_concepts = []
        
        for category, patterns in complexity_indicators.items():
            count = sum(1 for pattern in patterns if re.search(pattern, content))
            if count > 0:
                detected_concepts.append(f"{category}: {count}个")
                concept_score += count * 8
        
        content_length = len(content)
        term_density = sum(len(concepts) for concepts in detected_concepts) / max(content_length, 1) * 1000
        density_score = min(term_density * 20, 40)
        concept_score += density_score
        
        final_score = min(concept_score, 100)
        
        return {
            "score": final_score,
            "concepts_detected": detected_concepts,
            "term_density": term_density
        }
    
    def _analyze_practical_value(self, content: str) -> Dict[str, Any]:
        """分析实用价值"""
        practical_patterns = {
            "具体建议": [r'建议.*', r'推荐.*', r'应该.*', r'可以.*'],
            "实施步骤": [r'步骤.*', r'流程.*', r'过程.*', r'阶段.*'],
            "量化指标": [r'\d+%', r'\d+倍', r'\d+秒', r'\d+个'],
            "风险控制": [r'风险.*', r'注意.*', r'避免.*', r'防止.*']
        }
        
        practical_score = 0
        detected_practical = []
        
        for category, patterns in practical_patterns.items():
            count = sum(1 for pattern in patterns if re.search(pattern, content))
            if count > 0:
                detected_practical.append(f"{category}: {count}个")
                practical_score += count * 12
        
        # 检查是否包含具体示例
        example_indicators = ['例如', '比如', '举例', '案例']
        example_count = sum(1 for indicator in example_indicators if indicator in content)
        practical_score += example_count * 15
        
        final_score = min(practical_score, 100)
        
        return {
            "score": final_score,
            "practical_elements": detected_practical,
            "example_count": example_count
        }
    
    def _calculate_quality_grade(self, score: float) -> str:
        """计算质量等级"""
        if score >= 90:
            return "A+ (卓越)"
        elif score >= 80:
            return "A (优秀)"
        elif score >= 70:
            return "B (良好)"
        elif score >= 60:
            return "C (及格)"
        else:
            return "D (需改进)"

# ==================== API客户端 ====================
class SimpleAPIClient:
    """简单API客户端"""

    def __init__(self):
        self.api_url = API_CONFIG["url"]
        self.api_token = API_CONFIG["token"]
        self.models = API_CONFIG["models"]

    def call_api(self, model: str, prompt: str, max_retries: int = 2) -> Dict[str, Any]:
        """调用API获取响应"""

        for attempt in range(max_retries + 1):
            if attempt > 0:
                print(f"🔄 第{attempt + 1}次尝试调用API...")
                time.sleep(3)

            try:
                data = {
                    "model": model,
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0.7,
                    "max_tokens": 4000
                }

                json_data = json.dumps(data).encode('utf-8')

                req = urllib.request.Request(
                    self.api_url,
                    data=json_data,
                    headers={
                        'Authorization': f'Bearer {self.api_token}',
                        'Content-Type': 'application/json',
                        'User-Agent': 'CAP-Comparison-Tester/1.0'
                    }
                )

                timeout = 300 if "R1" in model else 120
                print(f"⏱️ 调用{model}，超时时间: {timeout}秒")

                with urllib.request.urlopen(req, timeout=timeout) as response:
                    response_data = response.read().decode('utf-8')

                    if response.status == 200:
                        result = json.loads(response_data)

                        if "choices" in result and len(result["choices"]) > 0:
                            message = result["choices"][0]["message"]
                            content = message.get("content", "")
                            reasoning = message.get("reasoning_content") or ""

                            print(f"✅ API调用成功")
                            print(f"📝 响应内容长度: {len(content)} 字符")
                            if reasoning:
                                print(f"🧠 推理内容长度: {len(reasoning)} 字符")

                            return {
                                "success": True,
                                "content": content,
                                "reasoning_content": reasoning,
                                "model": model,
                                "timestamp": datetime.now().isoformat(),
                                "token_usage": result.get("usage", {})
                            }
                        else:
                            return {
                                "success": False,
                                "error": "响应格式不正确",
                                "model": model
                            }
                    else:
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}",
                            "model": model
                        }

            except Exception as e:
                print(f"❌ 调用失败 (第{attempt + 1}次尝试): {str(e)}")
                if attempt < max_retries:
                    continue
                else:
                    return {
                        "success": False,
                        "error": f"所有{max_retries + 1}次尝试都失败: {str(e)}",
                        "model": model
                    }

        return {
            "success": False,
            "error": f"API调用失败，已重试{max_retries + 1}次",
            "model": model
        }

# ==================== CAP方法实现 ====================
class CAPApproachTester:
    """CAP方法对比测试器"""

    def __init__(self):
        self.api_client = SimpleAPIClient()
        self.logic_detector = LogicDepthDetector()
        self.prompt_template = StructuredPromptTemplate()

    def test_approach_a_embedded_cap(self, task: Dict, model: str) -> Dict[str, Any]:
        """方案A：内容嵌入式CAP（两次AI调用）"""
        print(f"🔄 测试方案A - 内容嵌入式CAP")

        # 第一步：生成结构化提示词
        base_structured_prompt = self.prompt_template.generate_structured_prompt(
            task["base_task"],
            task["context"],
            "专业技术顾问",
            task["expected_aspects"]
        )

        # 第二步：使用AI优化提示词（嵌入CAP）
        cap_optimization_prompt = f"""
请作为提示词工程专家，优化以下结构化提示词，使其能够引导AI产生更深度、更专业的回答。

原始结构化提示词：
{base_structured_prompt}

优化要求：
1. 保持原有结构化格式
2. 增强思维深度引导
3. 加强专业性要求
4. 提升逻辑严密性
5. 确保实用性导向

请输出优化后的完整提示词：
"""

        # 第一次API调用：优化提示词
        print("  📝 第一次调用：优化提示词...")
        optimization_result = self.api_client.call_api(model, cap_optimization_prompt)

        if not optimization_result["success"]:
            return {
                "success": False,
                "error": f"提示词优化失败: {optimization_result['error']}",
                "approach": "A_embedded"
            }

        optimized_prompt = optimization_result["content"]

        # 第二次API调用：执行优化后的任务
        print("  🎯 第二次调用：执行优化任务...")
        execution_result = self.api_client.call_api(model, optimized_prompt)

        if not execution_result["success"]:
            return {
                "success": False,
                "error": f"任务执行失败: {execution_result['error']}",
                "approach": "A_embedded"
            }

        # 分析最终结果
        final_content = execution_result["content"]
        if "R1" in model and execution_result.get("reasoning_content"):
            analysis_content = execution_result["reasoning_content"] + "\n\n" + final_content
        else:
            analysis_content = final_content

        logic_analysis = self.logic_detector.detect_logic_depth(analysis_content)

        return {
            "success": True,
            "approach": "A_embedded",
            "api_calls": 2,
            "base_prompt": base_structured_prompt,
            "optimized_prompt": optimized_prompt,
            "final_result": final_content,
            "analysis_content": analysis_content,
            "logic_analysis": logic_analysis,
            "optimization_step": optimization_result,
            "execution_step": execution_result,
            "total_tokens": (optimization_result.get("token_usage", {}).get("total_tokens", 0) +
                           execution_result.get("token_usage", {}).get("total_tokens", 0))
        }

    def test_approach_b_header_cap(self, task: Dict, model: str) -> Dict[str, Any]:
        """方案B：外部头部式CAP（头部规则嵌入）"""
        print(f"🔄 测试方案B - 外部头部式CAP")

        # 生成基础结构化提示词
        base_structured_prompt = self.prompt_template.generate_structured_prompt(
            task["base_task"],
            task["context"],
            "专业技术顾问",
            task["expected_aspects"]
        )

        # 在头部添加CAP优化规则
        cap_header = """
<CAP_OPTIMIZATION_RULES>
请在处理以下任务时严格遵循CAP优化框架：

**Chain-of-Thought (思维链优化)**：
- 运用系统性思维，从问题识别→深度分析→方案设计→实施规划的完整链条
- 每个推理步骤都要有明确的逻辑依据和证据支撑
- 主动寻找潜在的反例和边界情况进行验证

**Augmentation (增强优化)**：
- 整合多领域专业知识和最佳实践
- 提供具体的量化指标和评估标准
- 结合实际案例和行业经验进行论证
- 考虑长期影响和可扩展性

**Prompting (提示优化)**：
- 确保回答结构化、逻辑严密、层次清晰
- 提供可操作的具体建议和实施步骤
- 包含风险评估和应急预案
- 符合专业标准和行业规范

请以最高专业水准完成以下任务，展现深度思考和专业洞察。
</CAP_OPTIMIZATION_RULES>

"""

        # 组合完整提示词
        full_prompt = cap_header + base_structured_prompt

        # 一次API调用完成任务
        print("  🎯 单次调用：执行CAP优化任务...")
        execution_result = self.api_client.call_api(model, full_prompt)

        if not execution_result["success"]:
            return {
                "success": False,
                "error": f"任务执行失败: {execution_result['error']}",
                "approach": "B_header"
            }

        # 分析结果
        final_content = execution_result["content"]
        if "R1" in model and execution_result.get("reasoning_content"):
            analysis_content = execution_result["reasoning_content"] + "\n\n" + final_content
        else:
            analysis_content = final_content

        logic_analysis = self.logic_detector.detect_logic_depth(analysis_content)

        return {
            "success": True,
            "approach": "B_header",
            "api_calls": 1,
            "base_prompt": base_structured_prompt,
            "cap_header": cap_header,
            "full_prompt": full_prompt,
            "final_result": final_content,
            "analysis_content": analysis_content,
            "logic_analysis": logic_analysis,
            "execution_step": execution_result,
            "total_tokens": execution_result.get("token_usage", {}).get("total_tokens", 0)
        }

    def run_comparison_test(self) -> Dict[str, Any]:
        """运行完整的对比测试"""
        print("🚀 CAP方法对比测试器启动")
        print("=" * 80)
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 测试目标: 对比内容嵌入式CAP vs 外部头部式CAP")
        print(f"📊 测试任务: {len(REAL_WORLD_TASKS)}个实际任务场景")
        print(f"🤖 测试模型: {len(API_CONFIG['models'])}个模型")
        print()

        test_results = {
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "test_framework": "CAP方法对比测试器",
                "models_tested": API_CONFIG["models"],
                "tasks_tested": [task["id"] for task in REAL_WORLD_TASKS]
            },
            "task_results": {},
            "comparison_analysis": {},
            "efficiency_analysis": {},
            "quality_analysis": {}
        }

        # 对每个任务和模型进行测试
        for task in REAL_WORLD_TASKS:
            print(f"📋 测试任务: {task['name']} ({task['id']})")
            print("-" * 60)

            task_result = {
                "task_info": task,
                "model_results": {}
            }

            for model in API_CONFIG["models"]:
                print(f"🤖 测试模型: {model}")

                model_result = {
                    "model": model,
                    "approach_a_result": {},
                    "approach_b_result": {},
                    "comparison": {}
                }

                # 测试方案A
                try:
                    approach_a_result = self.test_approach_a_embedded_cap(task, model)
                    model_result["approach_a_result"] = approach_a_result

                    if approach_a_result["success"]:
                        print(f"  ✅ 方案A完成 - 质量分数: {approach_a_result['logic_analysis']['overall_score']:.1f}")
                    else:
                        print(f"  ❌ 方案A失败: {approach_a_result['error']}")

                except Exception as e:
                    print(f"  ❌ 方案A异常: {str(e)}")
                    model_result["approach_a_result"] = {"success": False, "error": str(e)}

                time.sleep(3)  # 避免API限流

                # 测试方案B
                try:
                    approach_b_result = self.test_approach_b_header_cap(task, model)
                    model_result["approach_b_result"] = approach_b_result

                    if approach_b_result["success"]:
                        print(f"  ✅ 方案B完成 - 质量分数: {approach_b_result['logic_analysis']['overall_score']:.1f}")
                    else:
                        print(f"  ❌ 方案B失败: {approach_b_result['error']}")

                except Exception as e:
                    print(f"  ❌ 方案B异常: {str(e)}")
                    model_result["approach_b_result"] = {"success": False, "error": str(e)}

                # 对比分析
                model_result["comparison"] = self._compare_approaches(
                    model_result["approach_a_result"],
                    model_result["approach_b_result"]
                )

                task_result["model_results"][model] = model_result
                print(f"  📊 对比完成")
                print()

                time.sleep(2)  # 避免API限流

            test_results["task_results"][task["id"]] = task_result
            print(f"✅ 任务 {task['name']} 测试完成")
            print()

        # 生成综合分析
        test_results["comparison_analysis"] = self._generate_comparison_analysis(test_results["task_results"])
        test_results["efficiency_analysis"] = self._generate_efficiency_analysis(test_results["task_results"])
        test_results["quality_analysis"] = self._generate_quality_analysis(test_results["task_results"])

        # 输出最终报告
        self._generate_final_report(test_results)

        return test_results

    def _compare_approaches(self, approach_a: Dict, approach_b: Dict) -> Dict[str, Any]:
        """对比两种方案的结果"""
        if not approach_a.get("success") or not approach_b.get("success"):
            return {
                "comparison_available": False,
                "reason": "一个或多个方案执行失败"
            }

        a_score = approach_a["logic_analysis"]["overall_score"]
        b_score = approach_b["logic_analysis"]["overall_score"]

        a_tokens = approach_a.get("total_tokens", 0)
        b_tokens = approach_b.get("total_tokens", 0)

        a_calls = approach_a.get("api_calls", 0)
        b_calls = approach_b.get("api_calls", 0)

        return {
            "comparison_available": True,
            "quality_comparison": {
                "approach_a_score": a_score,
                "approach_b_score": b_score,
                "quality_difference": b_score - a_score,
                "quality_winner": "方案A" if a_score > b_score else "方案B" if b_score > a_score else "平局",
                "quality_advantage": abs(a_score - b_score)
            },
            "efficiency_comparison": {
                "approach_a_tokens": a_tokens,
                "approach_b_tokens": b_tokens,
                "token_difference": b_tokens - a_tokens,
                "approach_a_calls": a_calls,
                "approach_b_calls": b_calls,
                "efficiency_winner": "方案A" if a_tokens < b_tokens else "方案B" if b_tokens < a_tokens else "平局"
            },
            "dimension_comparison": {
                "reasoning_depth": {
                    "a": approach_a["logic_analysis"]["dimension_scores"]["reasoning_depth"],
                    "b": approach_b["logic_analysis"]["dimension_scores"]["reasoning_depth"],
                    "winner": "A" if approach_a["logic_analysis"]["dimension_scores"]["reasoning_depth"] >
                             approach_b["logic_analysis"]["dimension_scores"]["reasoning_depth"] else "B"
                },
                "logical_structure": {
                    "a": approach_a["logic_analysis"]["dimension_scores"]["logical_structure"],
                    "b": approach_b["logic_analysis"]["dimension_scores"]["logical_structure"],
                    "winner": "A" if approach_a["logic_analysis"]["dimension_scores"]["logical_structure"] >
                             approach_b["logic_analysis"]["dimension_scores"]["logical_structure"] else "B"
                },
                "concept_complexity": {
                    "a": approach_a["logic_analysis"]["dimension_scores"]["concept_complexity"],
                    "b": approach_b["logic_analysis"]["dimension_scores"]["concept_complexity"],
                    "winner": "A" if approach_a["logic_analysis"]["dimension_scores"]["concept_complexity"] >
                             approach_b["logic_analysis"]["dimension_scores"]["concept_complexity"] else "B"
                },
                "practical_value": {
                    "a": approach_a["logic_analysis"]["dimension_scores"]["practical_value"],
                    "b": approach_b["logic_analysis"]["dimension_scores"]["practical_value"],
                    "winner": "A" if approach_a["logic_analysis"]["dimension_scores"]["practical_value"] >
                             approach_b["logic_analysis"]["dimension_scores"]["practical_value"] else "B"
                }
            }
        }

    def _generate_comparison_analysis(self, task_results: Dict) -> Dict[str, Any]:
        """生成对比分析"""
        analysis = {
            "overall_winner": {"A": 0, "B": 0, "tie": 0},
            "quality_stats": {"A": [], "B": []},
            "efficiency_stats": {"A": [], "B": []},
            "dimension_winners": {
                "reasoning_depth": {"A": 0, "B": 0},
                "logical_structure": {"A": 0, "B": 0},
                "concept_complexity": {"A": 0, "B": 0},
                "practical_value": {"A": 0, "B": 0}
            }
        }

        for task_id, task_result in task_results.items():
            for model, model_result in task_result["model_results"].items():
                comparison = model_result.get("comparison", {})

                if comparison.get("comparison_available"):
                    # 统计质量胜负
                    quality_winner = comparison["quality_comparison"]["quality_winner"]
                    if "方案A" in quality_winner:
                        analysis["overall_winner"]["A"] += 1
                    elif "方案B" in quality_winner:
                        analysis["overall_winner"]["B"] += 1
                    else:
                        analysis["overall_winner"]["tie"] += 1

                    # 收集质量分数
                    analysis["quality_stats"]["A"].append(comparison["quality_comparison"]["approach_a_score"])
                    analysis["quality_stats"]["B"].append(comparison["quality_comparison"]["approach_b_score"])

                    # 收集效率数据
                    analysis["efficiency_stats"]["A"].append(comparison["efficiency_comparison"]["approach_a_tokens"])
                    analysis["efficiency_stats"]["B"].append(comparison["efficiency_comparison"]["approach_b_tokens"])

                    # 统计各维度胜负
                    for dimension, dim_data in comparison["dimension_comparison"].items():
                        winner = dim_data["winner"]
                        analysis["dimension_winners"][dimension][winner] += 1

        # 计算平均值
        if analysis["quality_stats"]["A"]:
            analysis["average_quality"] = {
                "A": sum(analysis["quality_stats"]["A"]) / len(analysis["quality_stats"]["A"]),
                "B": sum(analysis["quality_stats"]["B"]) / len(analysis["quality_stats"]["B"])
            }

        if analysis["efficiency_stats"]["A"]:
            analysis["average_efficiency"] = {
                "A": sum(analysis["efficiency_stats"]["A"]) / len(analysis["efficiency_stats"]["A"]),
                "B": sum(analysis["efficiency_stats"]["B"]) / len(analysis["efficiency_stats"]["B"])
            }

        return analysis

    def _generate_efficiency_analysis(self, task_results: Dict) -> Dict[str, Any]:
        """生成效率分析"""
        efficiency_data = {
            "api_calls_comparison": {"A": [], "B": []},
            "token_usage_comparison": {"A": [], "B": []},
            "cost_efficiency": {},
            "time_efficiency": {}
        }

        for task_id, task_result in task_results.items():
            for model, model_result in task_result["model_results"].items():
                a_result = model_result.get("approach_a_result", {})
                b_result = model_result.get("approach_b_result", {})

                if a_result.get("success") and b_result.get("success"):
                    efficiency_data["api_calls_comparison"]["A"].append(a_result.get("api_calls", 0))
                    efficiency_data["api_calls_comparison"]["B"].append(b_result.get("api_calls", 0))

                    efficiency_data["token_usage_comparison"]["A"].append(a_result.get("total_tokens", 0))
                    efficiency_data["token_usage_comparison"]["B"].append(b_result.get("total_tokens", 0))

        # 计算效率指标
        if efficiency_data["api_calls_comparison"]["A"]:
            efficiency_data["average_api_calls"] = {
                "A": sum(efficiency_data["api_calls_comparison"]["A"]) / len(efficiency_data["api_calls_comparison"]["A"]),
                "B": sum(efficiency_data["api_calls_comparison"]["B"]) / len(efficiency_data["api_calls_comparison"]["B"])
            }

            efficiency_data["average_token_usage"] = {
                "A": sum(efficiency_data["token_usage_comparison"]["A"]) / len(efficiency_data["token_usage_comparison"]["A"]),
                "B": sum(efficiency_data["token_usage_comparison"]["B"]) / len(efficiency_data["token_usage_comparison"]["B"])
            }

            # 成本效率分析（假设每1000 tokens成本为0.01元）
            cost_per_1k_tokens = 0.01
            efficiency_data["cost_efficiency"] = {
                "A": efficiency_data["average_token_usage"]["A"] / 1000 * cost_per_1k_tokens,
                "B": efficiency_data["average_token_usage"]["B"] / 1000 * cost_per_1k_tokens,
                "cost_difference": (efficiency_data["average_token_usage"]["A"] - efficiency_data["average_token_usage"]["B"]) / 1000 * cost_per_1k_tokens
            }

        return efficiency_data

    def _generate_quality_analysis(self, task_results: Dict) -> Dict[str, Any]:
        """生成质量分析"""
        quality_data = {
            "dimension_analysis": {
                "reasoning_depth": {"A": [], "B": []},
                "logical_structure": {"A": [], "B": []},
                "concept_complexity": {"A": [], "B": []},
                "practical_value": {"A": [], "B": []}
            },
            "task_type_performance": {},
            "model_performance": {}
        }

        for task_id, task_result in task_results.items():
            task_name = task_result["task_info"]["name"]
            quality_data["task_type_performance"][task_name] = {"A": [], "B": []}

            for model, model_result in task_result["model_results"].items():
                if model not in quality_data["model_performance"]:
                    quality_data["model_performance"][model] = {"A": [], "B": []}

                a_result = model_result.get("approach_a_result", {})
                b_result = model_result.get("approach_b_result", {})

                if a_result.get("success") and b_result.get("success"):
                    a_dimensions = a_result["logic_analysis"]["dimension_scores"]
                    b_dimensions = b_result["logic_analysis"]["dimension_scores"]

                    # 收集各维度数据
                    for dimension in quality_data["dimension_analysis"]:
                        quality_data["dimension_analysis"][dimension]["A"].append(a_dimensions[dimension])
                        quality_data["dimension_analysis"][dimension]["B"].append(b_dimensions[dimension])

                    # 收集任务类型表现
                    quality_data["task_type_performance"][task_name]["A"].append(a_result["logic_analysis"]["overall_score"])
                    quality_data["task_type_performance"][task_name]["B"].append(b_result["logic_analysis"]["overall_score"])

                    # 收集模型表现
                    quality_data["model_performance"][model]["A"].append(a_result["logic_analysis"]["overall_score"])
                    quality_data["model_performance"][model]["B"].append(b_result["logic_analysis"]["overall_score"])

        # 计算平均值
        for dimension in quality_data["dimension_analysis"]:
            if quality_data["dimension_analysis"][dimension]["A"]:
                quality_data["dimension_analysis"][dimension]["average"] = {
                    "A": sum(quality_data["dimension_analysis"][dimension]["A"]) / len(quality_data["dimension_analysis"][dimension]["A"]),
                    "B": sum(quality_data["dimension_analysis"][dimension]["B"]) / len(quality_data["dimension_analysis"][dimension]["B"])
                }

        return quality_data

    def _generate_final_report(self, test_results: Dict[str, Any]) -> None:
        """生成最终测试报告"""

        print("\n" + "=" * 80)
        print("📊 CAP方法对比测试报告")
        print("=" * 80)

        # 1. 测试概览
        comparison_analysis = test_results["comparison_analysis"]
        efficiency_analysis = test_results["efficiency_analysis"]
        quality_analysis = test_results["quality_analysis"]

        print("\n🎯 测试概览:")
        print(f"   测试任务数: {len(test_results['task_results'])}")
        print(f"   测试模型数: {len(API_CONFIG['models'])}")
        print(f"   总测试场景: {len(test_results['task_results']) * len(API_CONFIG['models'])}")

        # 2. 整体胜负统计
        print("\n🏆 整体胜负统计:")
        total_tests = sum(comparison_analysis["overall_winner"].values())
        if total_tests > 0:
            a_win_rate = comparison_analysis["overall_winner"]["A"] / total_tests * 100
            b_win_rate = comparison_analysis["overall_winner"]["B"] / total_tests * 100
            tie_rate = comparison_analysis["overall_winner"]["tie"] / total_tests * 100

            print(f"   方案A胜利: {comparison_analysis['overall_winner']['A']}次 ({a_win_rate:.1f}%)")
            print(f"   方案B胜利: {comparison_analysis['overall_winner']['B']}次 ({b_win_rate:.1f}%)")
            print(f"   平局: {comparison_analysis['overall_winner']['tie']}次 ({tie_rate:.1f}%)")

            if a_win_rate > b_win_rate:
                print(f"   🥇 总体优胜者: 方案A (内容嵌入式CAP)")
            elif b_win_rate > a_win_rate:
                print(f"   🥇 总体优胜者: 方案B (外部头部式CAP)")
            else:
                print(f"   🤝 总体结果: 平局")

        # 3. 质量对比分析
        print("\n📈 质量对比分析:")
        if "average_quality" in comparison_analysis:
            avg_a = comparison_analysis["average_quality"]["A"]
            avg_b = comparison_analysis["average_quality"]["B"]
            print(f"   方案A平均质量分: {avg_a:.1f}")
            print(f"   方案B平均质量分: {avg_b:.1f}")
            print(f"   质量差异: {abs(avg_a - avg_b):.1f}分")

            if avg_a > avg_b:
                print(f"   📊 质量优势: 方案A领先 {avg_a - avg_b:.1f}分")
            elif avg_b > avg_a:
                print(f"   📊 质量优势: 方案B领先 {avg_b - avg_a:.1f}分")
            else:
                print(f"   📊 质量表现: 两方案相当")

        # 4. 效率对比分析
        print("\n⚡ 效率对比分析:")
        if "average_api_calls" in efficiency_analysis:
            avg_calls_a = efficiency_analysis["average_api_calls"]["A"]
            avg_calls_b = efficiency_analysis["average_api_calls"]["B"]
            print(f"   方案A平均API调用: {avg_calls_a:.1f}次")
            print(f"   方案B平均API调用: {avg_calls_b:.1f}次")

            avg_tokens_a = efficiency_analysis["average_token_usage"]["A"]
            avg_tokens_b = efficiency_analysis["average_token_usage"]["B"]
            print(f"   方案A平均Token消耗: {avg_tokens_a:.0f}")
            print(f"   方案B平均Token消耗: {avg_tokens_b:.0f}")

            if "cost_efficiency" in efficiency_analysis:
                cost_a = efficiency_analysis["cost_efficiency"]["A"]
                cost_b = efficiency_analysis["cost_efficiency"]["B"]
                cost_diff = efficiency_analysis["cost_efficiency"]["cost_difference"]
                print(f"   方案A平均成本: ¥{cost_a:.4f}")
                print(f"   方案B平均成本: ¥{cost_b:.4f}")
                print(f"   成本差异: ¥{abs(cost_diff):.4f}")

                if cost_a < cost_b:
                    print(f"   💰 成本优势: 方案A更经济，节省 {(cost_b - cost_a) / cost_b * 100:.1f}%")
                elif cost_b < cost_a:
                    print(f"   💰 成本优势: 方案B更经济，节省 {(cost_a - cost_b) / cost_a * 100:.1f}%")

        # 5. 各维度表现分析
        print("\n🔍 各维度表现分析:")
        for dimension, winners in comparison_analysis["dimension_winners"].items():
            total_dim = winners["A"] + winners["B"]
            if total_dim > 0:
                a_rate = winners["A"] / total_dim * 100
                b_rate = winners["B"] / total_dim * 100
                print(f"   {dimension}:")
                print(f"     方案A胜率: {a_rate:.1f}% ({winners['A']}次)")
                print(f"     方案B胜率: {b_rate:.1f}% ({winners['B']}次)")

                if "dimension_analysis" in quality_analysis and dimension in quality_analysis["dimension_analysis"]:
                    if "average" in quality_analysis["dimension_analysis"][dimension]:
                        avg_a = quality_analysis["dimension_analysis"][dimension]["average"]["A"]
                        avg_b = quality_analysis["dimension_analysis"][dimension]["average"]["B"]
                        print(f"     平均分数: A={avg_a:.1f}, B={avg_b:.1f}")

        # 6. 任务类型表现
        print("\n📋 任务类型表现:")
        for task_name, performance in quality_analysis["task_type_performance"].items():
            if performance["A"] and performance["B"]:
                avg_a = sum(performance["A"]) / len(performance["A"])
                avg_b = sum(performance["B"]) / len(performance["B"])
                print(f"   {task_name}:")
                print(f"     方案A: {avg_a:.1f}分")
                print(f"     方案B: {avg_b:.1f}分")
                if avg_a > avg_b:
                    print(f"     优势: 方案A领先 {avg_a - avg_b:.1f}分")
                elif avg_b > avg_a:
                    print(f"     优势: 方案B领先 {avg_b - avg_a:.1f}分")
                else:
                    print(f"     结果: 平局")

        # 7. 模型表现
        print("\n🤖 模型表现:")
        for model, performance in quality_analysis["model_performance"].items():
            if performance["A"] and performance["B"]:
                avg_a = sum(performance["A"]) / len(performance["A"])
                avg_b = sum(performance["B"]) / len(performance["B"])
                print(f"   {model}:")
                print(f"     方案A: {avg_a:.1f}分")
                print(f"     方案B: {avg_b:.1f}分")
                if avg_a > avg_b:
                    print(f"     该模型下方案A更优，领先 {avg_a - avg_b:.1f}分")
                elif avg_b > avg_a:
                    print(f"     该模型下方案B更优，领先 {avg_b - avg_a:.1f}分")
                else:
                    print(f"     该模型下两方案相当")

        # 8. 关键发现和建议
        print("\n💡 关键发现:")

        # 质量vs效率权衡分析
        if "average_quality" in comparison_analysis and "average_api_calls" in efficiency_analysis:
            quality_leader = "A" if comparison_analysis["average_quality"]["A"] > comparison_analysis["average_quality"]["B"] else "B"
            efficiency_leader = "A" if efficiency_analysis["average_api_calls"]["A"] < efficiency_analysis["average_api_calls"]["B"] else "B"

            if quality_leader == efficiency_leader:
                print(f"   🎯 方案{quality_leader}在质量和效率两方面都表现更优")
            else:
                print(f"   ⚖️ 存在质量vs效率权衡：方案{quality_leader}质量更高，方案{efficiency_leader}效率更高")

        # 实际应用建议
        print("\n🚀 实际应用建议:")
        if "average_quality" in comparison_analysis:
            quality_diff = abs(comparison_analysis["average_quality"]["A"] - comparison_analysis["average_quality"]["B"])
            if quality_diff < 5:
                print("   📊 两种方案质量差异较小（<5分），可优先考虑效率因素")
            elif quality_diff >= 10:
                print("   📊 两种方案质量差异显著（≥10分），建议优先选择质量更高的方案")
            else:
                print("   📊 两种方案质量差异中等，需要根据具体场景权衡选择")

        if "cost_efficiency" in efficiency_analysis:
            cost_diff_pct = abs(efficiency_analysis["cost_efficiency"]["cost_difference"]) / min(efficiency_analysis["cost_efficiency"]["A"], efficiency_analysis["cost_efficiency"]["B"]) * 100
            if cost_diff_pct > 20:
                print("   💰 成本差异超过20%，在大规模应用中需要重点考虑成本因素")

        print("\n" + "=" * 80)
        print("✅ 对比测试报告生成完成")

# ==================== 主函数 ====================
def main():
    """主函数 - 执行CAP方法对比测试"""

    print("🎯 CAP方法对比测试器启动")
    print("目标：对比内容嵌入式CAP vs 外部头部式CAP的实际效果")
    print("方法：使用结构化提示词工程模板，在实际任务场景下测试")
    print()

    # 创建测试器
    tester = CAPApproachTester()

    # 运行测试
    try:
        results = tester.run_comparison_test()

        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"cap_approach_comparison_report_{timestamp}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"\n💾 详细测试数据已保存: {filename}")
        print("🎉 CAP方法对比测试完成！")

        return results

    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return None
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
