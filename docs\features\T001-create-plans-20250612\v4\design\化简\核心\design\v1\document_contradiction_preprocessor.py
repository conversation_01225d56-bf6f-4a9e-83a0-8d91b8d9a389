#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DocumentContradictionPreprocessor - 文档矛盾预处理器
基于现有V4文档处理系统扩展，支持通用00-xx文档矛盾检测预处理
"""

import asyncio
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


@dataclass
class ProcessedDocument:
    """预处理后的文档数据结构"""
    doc_id: str
    doc_type: str  # "00_overview", "01_implementation", etc.
    file_path: str
    
    # 结构化内容
    architecture_definitions: Dict[str, Any]
    constraints: List[Dict[str, Any]]
    interfaces: List[Dict[str, Any]]
    dependencies: List[Dict[str, Any]]
    
    # 元数据
    reliability_score: float
    completeness_score: float
    processing_timestamp: str
    
    # 原始内容（用于语义分析）
    raw_content: str
    structured_sections: Dict[str, str]


class DocumentContradictionPreprocessor:
    """
    文档矛盾预处理器
    
    职责：
    1. 解析00-xx格式的文档
    2. 提取结构化信息用于矛盾检测
    3. 评估文档可靠性和完整性
    4. 为矛盾检测提供标准化输入
    """
    
    def __init__(self):
        # 文档类型识别模式
        self.doc_type_patterns = {
            r"00.*overview|00.*总览|00.*护栏": "00_overview",
            r"01.*architecture|01.*架构": "01_architecture", 
            r"02.*implementation|02.*实现": "02_implementation",
            r"03.*interface|03.*接口": "03_interface",
            r"04.*module|04.*模块": "04_module",
            r"05.*api|05.*API": "05_api",
            r"06.*plan|06.*计划": "06_plan",
            r"07.*structure|07.*结构": "07_structure",
            r"08.*risk|08.*风险": "08_risk"
        }
        
        # 内容提取模式
        self.extraction_patterns = {
            "mermaid_diagram": r"```mermaid\s*\n(.*?)```",
            "yaml_constraint": r"```yaml\s*\n(.*?)```",
            "architecture_section": r"##\s*架构.*?\n(.*?)(?=##|\Z)",
            "constraint_section": r"##\s*约束.*?\n(.*?)(?=##|\Z)",
            "interface_section": r"##\s*接口.*?\n(.*?)(?=##|\Z)",
            "dependency_section": r"##\s*依赖.*?\n(.*?)(?=##|\Z)"
        }
    
    async def preprocess_documents(self, doc_paths: List[str]) -> List[ProcessedDocument]:
        """
        预处理文档列表
        
        Args:
            doc_paths: 文档路径列表
            
        Returns:
            List[ProcessedDocument]: 预处理后的文档列表
        """
        processed_docs = []
        
        for doc_path in doc_paths:
            try:
                processed_doc = await self._preprocess_single_document(doc_path)
                processed_docs.append(processed_doc)
                logger.info(f"成功预处理文档: {doc_path}")
            except Exception as e:
                logger.error(f"预处理文档失败 {doc_path}: {e}")
                continue
        
        return processed_docs
    
    async def _preprocess_single_document(self, doc_path: str) -> ProcessedDocument:
        """预处理单个文档"""
        
        # 1. 读取文档内容
        raw_content = self._read_document(doc_path)
        
        # 2. 识别文档类型
        doc_type = self._identify_document_type(doc_path)
        
        # 3. 提取结构化信息
        architecture_definitions = self._extract_architecture_definitions(raw_content)
        constraints = self._extract_constraints(raw_content)
        interfaces = self._extract_interfaces(raw_content)
        dependencies = self._extract_dependencies(raw_content)
        
        # 4. 评估文档质量
        reliability_score = self._assess_document_reliability(raw_content)
        completeness_score = self._assess_document_completeness(raw_content, doc_type)
        
        # 5. 提取结构化章节
        structured_sections = self._extract_structured_sections(raw_content)
        
        return ProcessedDocument(
            doc_id=self._generate_doc_id(doc_path),
            doc_type=doc_type,
            file_path=doc_path,
            architecture_definitions=architecture_definitions,
            constraints=constraints,
            interfaces=interfaces,
            dependencies=dependencies,
            reliability_score=reliability_score,
            completeness_score=completeness_score,
            processing_timestamp=self._get_current_timestamp(),
            raw_content=raw_content,
            structured_sections=structured_sections
        )
    
    def _read_document(self, doc_path: str) -> str:
        """读取文档内容"""
        try:
            with open(doc_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取文档失败 {doc_path}: {e}")
            raise
    
    def _identify_document_type(self, doc_path: str) -> str:
        """识别文档类型"""
        file_name = Path(doc_path).name.lower()
        
        for pattern, doc_type in self.doc_type_patterns.items():
            if re.search(pattern, file_name, re.IGNORECASE):
                return doc_type
        
        # 默认类型
        return "unknown"
    
    def _extract_architecture_definitions(self, content: str) -> Dict[str, Any]:
        """提取架构定义"""
        architecture_defs = {}
        
        # 提取Mermaid架构图
        mermaid_matches = re.findall(self.extraction_patterns["mermaid_diagram"], content, re.DOTALL)
        if mermaid_matches:
            architecture_defs["mermaid_diagrams"] = mermaid_matches
        
        # 提取架构模式描述
        arch_section_matches = re.findall(self.extraction_patterns["architecture_section"], content, re.DOTALL)
        if arch_section_matches:
            architecture_defs["architecture_descriptions"] = arch_section_matches
        
        return architecture_defs
    
    def _extract_constraints(self, content: str) -> List[Dict[str, Any]]:
        """提取约束条件"""
        constraints = []
        
        # 提取YAML约束
        yaml_matches = re.findall(self.extraction_patterns["yaml_constraint"], content, re.DOTALL)
        for yaml_content in yaml_matches:
            constraints.append({
                "type": "yaml_constraint",
                "content": yaml_content.strip(),
                "source": "yaml_block"
            })
        
        # 提取约束章节
        constraint_matches = re.findall(self.extraction_patterns["constraint_section"], content, re.DOTALL)
        for constraint_content in constraint_matches:
            constraints.append({
                "type": "text_constraint",
                "content": constraint_content.strip(),
                "source": "constraint_section"
            })
        
        return constraints
    
    def _extract_interfaces(self, content: str) -> List[Dict[str, Any]]:
        """提取接口定义"""
        interfaces = []
        
        # 提取接口章节
        interface_matches = re.findall(self.extraction_patterns["interface_section"], content, re.DOTALL)
        for interface_content in interface_matches:
            interfaces.append({
                "type": "interface_definition",
                "content": interface_content.strip(),
                "source": "interface_section"
            })
        
        return interfaces
    
    def _extract_dependencies(self, content: str) -> List[Dict[str, Any]]:
        """提取依赖关系"""
        dependencies = []
        
        # 提取依赖章节
        dep_matches = re.findall(self.extraction_patterns["dependency_section"], content, re.DOTALL)
        for dep_content in dep_matches:
            dependencies.append({
                "type": "dependency_definition",
                "content": dep_content.strip(),
                "source": "dependency_section"
            })
        
        return dependencies
    
    def _assess_document_reliability(self, content: str) -> float:
        """评估文档可靠性"""
        reliability_factors = {
            "has_architecture_diagram": 0.25,
            "has_constraints": 0.25,
            "has_detailed_sections": 0.25,
            "content_completeness": 0.25
        }
        
        score = 0.0
        
        # 检查是否有架构图
        if re.search(self.extraction_patterns["mermaid_diagram"], content):
            score += reliability_factors["has_architecture_diagram"]
        
        # 检查是否有约束定义
        if re.search(self.extraction_patterns["yaml_constraint"], content):
            score += reliability_factors["has_constraints"]
        
        # 检查是否有详细章节
        section_count = len(re.findall(r"##\s+", content))
        if section_count >= 5:
            score += reliability_factors["has_detailed_sections"]
        
        # 检查内容完整性（基于长度）
        if len(content) > 2000:
            score += reliability_factors["content_completeness"]
        
        return min(score, 1.0)
    
    def _assess_document_completeness(self, content: str, doc_type: str) -> float:
        """评估文档完整性"""
        # 基于文档类型的完整性检查
        type_requirements = {
            "00_overview": ["架构", "约束", "护栏"],
            "01_architecture": ["架构图", "组件", "接口"],
            "02_implementation": ["实现", "模块", "代码"],
            "03_interface": ["接口", "API", "协议"],
            "04_module": ["模块", "组件", "功能"],
            "05_api": ["API", "接口", "端点"],
            "06_plan": ["计划", "里程碑", "时间"],
            "07_structure": ["结构", "目录", "组织"],
            "08_risk": ["风险", "检测", "分析"]
        }
        
        requirements = type_requirements.get(doc_type, [])
        if not requirements:
            return 0.8  # 默认分数
        
        found_count = 0
        for requirement in requirements:
            if re.search(requirement, content, re.IGNORECASE):
                found_count += 1
        
        return found_count / len(requirements)
    
    def _extract_structured_sections(self, content: str) -> Dict[str, str]:
        """提取结构化章节"""
        sections = {}
        
        # 提取所有二级标题章节
        section_pattern = r"##\s+([^#\n]+)\n(.*?)(?=##|\Z)"
        matches = re.findall(section_pattern, content, re.DOTALL)
        
        for title, section_content in matches:
            sections[title.strip()] = section_content.strip()
        
        return sections
    
    def _generate_doc_id(self, doc_path: str) -> str:
        """生成文档ID"""
        return f"doc_{hash(doc_path) % 10000:04d}"
    
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()


# 使用示例
async def main():
    """使用示例"""
    preprocessor = DocumentContradictionPreprocessor()
    
    doc_paths = [
        "docs/00-护栏约束上下文总览.md",
        "docs/01-系统架构设计文档.md",
        "docs/02-DRY优化重构方案.md"
    ]
    
    processed_docs = await preprocessor.preprocess_documents(doc_paths)
    
    for doc in processed_docs:
        print(f"文档ID: {doc.doc_id}")
        print(f"文档类型: {doc.doc_type}")
        print(f"可靠性评分: {doc.reliability_score:.2f}")
        print(f"完整性评分: {doc.completeness_score:.2f}")
        print(f"架构定义数量: {len(doc.architecture_definitions)}")
        print(f"约束数量: {len(doc.constraints)}")
        print("---")


if __name__ == "__main__":
    asyncio.run(main())
