# UID库切换XCE异常库执行检查清单

## 文档信息
- **文档ID**: F007-UID-XCE-MIGRATION-CHECKLIST-002
- **关联计划**: 01-UID库切换XCE异常库实施计划.md
- **创建日期**: 2025-06-11
- **版本**: v2.2 (精神融入版)
- **验证原则**: 每个检查项都是质量门禁，必须100%通过
- **ACE优化**: 在代码理解环节添加ACE分析验证检查

## 执行前准备检查

### 环境准备
**状态确认原则**: 验证当前环境状态，确保基础条件满足
- [ ] 确认工作目录：`xkongcloud-commons`
- [ ] 确认XCE库状态：验证XCE异常库正常工作

### 依赖验证
**完整性检查**: 确保所有依赖项目存在且可访问
- [ ] 确认XCE库位置：`xkongcloud-commons\xkongcloud-commons-exception`
- [ ] 确认UID库位置：`xkongcloud-commons\xkongcloud-commons-uid`
- [ ] 确认Maven环境：可正常执行`mvn compile`
- [ ] 确认IDE环境：可正常导入和编译项目

## 阶段0：XCE异常分类优化检查清单

### 步骤0.1：解决错误码冲突问题
**执行检查**:
**风险识别**: 确认冲突错误码的当前使用情况，避免破坏现有功能
- [ ] 确认当前XCE异常库状态
- [ ] 确认UID异常当前使用情况

**重新分类验证**:
- 分类参考：09-配置参数映射.json → exception_mapping_rules.technical_categories
- [ ] 业务逻辑异常：UID生成、Worker ID分配、实例注册
- [ ] 数据库异常：实例恢复、租约续约（数据库操作）
- [ ] 文件异常：机器特征码收集（文件访问）
- [ ] 验证异常：参数验证、状态验证
- [ ] 网络异常：服务不可用、网络超时

### 步骤0.2：更新ErrorCodes.java
**目标**: 删除冲突错误码，添加新分类错误码

**执行检查**:
- 文件位置参考：08-依赖关系映射.json → dependency_map.xce_exception_library.core_files[0]
- [ ] 文件存在且可编辑

**关键操作**（来自09-配置参数映射.json → exception_mapping_rules.uid_error_codes）:
- 删除冲突错误码：XCE_UID_800-805
- 添加新分类错误码：
  - [ ] 业务逻辑类(180-189)：`XCE_BIZ_180`、`XCE_BIZ_181`、`XCE_BIZ_182`
  - [ ] 数据库类(680-689)：`XCE_DB_680`、`XCE_DB_681`、`XCE_DB_682`
  - [ ] 文件类(720-729)：`XCE_FILE_720`、`XCE_FILE_721`
  - [ ] 验证类(780-789)：`XCE_VAL_780`、`XCE_VAL_781`
  - [ ] 网络类(620-629)：`XCE_NET_620`、`XCE_NET_621`

**质量门禁**:
- [ ] 编译成功：`mvn compile -pl xkongcloud-commons/xkongcloud-commons-exception`
- [ ] 错误码无冲突，命名规范一致

### 步骤0.3：扩展异常类方法
**目标**: 在对应技术类别异常类中添加UID专用方法

**执行检查**:
- 异常类映射参考：09-配置参数映射.json → exception_mapping_rules.exception_class_mapping
- [ ] BusinessException添加UID业务逻辑异常方法
- [ ] DatabaseSystemException添加UID数据库异常方法
- [ ] FileSystemException添加UID文件异常方法
- [ ] ValidationBusinessException添加UID验证异常方法
- [ ] NetworkSystemException添加UID网络异常方法

**质量门禁**:
- [ ] 所有异常类编译成功
- [ ] 异常方法可正常调用，metadata信息正确设置

## 阶段1：依赖配置检查清单

### 步骤1.1：添加XCE依赖
**目标**: 建立UID库到XCE库的Maven依赖关系

**执行检查**:
- 依赖配置参考：08-依赖关系映射.json → dependency_map.xce_exception_library
- [ ] 文件存在且可编辑：`xkongcloud-commons\xkongcloud-commons-uid\pom.xml`
- [ ] 在dependencies节点添加XCE依赖

**质量门禁**:
- [ ] 执行`mvn compile`成功
- [ ] 可正常导入XCE类

### 步骤1.2：验证XCE类可用性
**目标**: 确认XCE异常类可正常导入和使用

**执行检查**:
- 验证类列表参考：08-依赖关系映射.json → dependency_map.xce_exception_library.core_classes
- [ ] 验证核心XCE异常类可正常导入

**执行后验证**:
- [ ] IDE无编译错误，类路径解析正常

## 阶段2：异常映射设计检查清单

### 步骤2.1：异常类型映射验证
**目标**: 建立Java标准异常到XCE异常的完整映射关系

**执行检查**:
- 异常映射规则参考：09-配置参数映射.json → exception_mapping_rules
- [ ] 确认网络异常映射关系
- [ ] 确认数据库异常映射关系
- [ ] 确认文件操作异常映射关系
- [ ] 确认验证异常映射关系
- [ ] 确认业务逻辑异常映射关系

### 步骤2.2：XCE错误码定义验证
**目标**: 确认UID库专用错误码按技术类别正确分配

**执行检查**:
- 错误码分配参考：09-配置参数映射.json → exception_mapping_rules.uid_error_codes
- [ ] 确认UID专用错误码设计
- [ ] 验证错误码格式符合XCE标准

## 阶段3：核心组件异常切换检查清单

### 步骤3.1：修改PersistentInstanceWorkerIdAssigner.java（28个异常点）
**目标**: 将核心组件的异常处理切换到XCE体系

**ACE分析验证检查**:
- [ ] ACE成功分析@PersistentInstanceWorkerIdAssigner.java的完整代码结构
- [ ] ACE识别出该组件在UID生成架构中的作用和位置
- [ ] ACE分析出所有相关的Worker ID分配文件和依赖关系
- [ ] ACE分析结果与JSON配置映射规则保持一致

**执行检查**（来自08-依赖关系映射.json → dependency_map.uid_library.critical_files[0]）:
- [ ] 文件存在且可编辑：`xkongcloud-commons\xkongcloud-commons-uid\src\main\java\org\xkong\cloud\commons\uid\worker\PersistentInstanceWorkerIdAssigner.java`
- [ ] 确认当前异常抛出点：28个

**关键操作**（来自09-配置参数映射.json → exception_mapping_rules）:
**导入语句添加检查**:
- [ ] 添加`import org.xkong.cloud.commons.exception.core.BusinessException;`
- [ ] 添加`import org.xkong.cloud.commons.exception.core.SystemException;`
- [ ] 添加`import org.xkong.cloud.commons.exception.validation.ValidationBusinessException;`
- [ ] 添加`import org.xkong.cloud.commons.exception.network.NetworkSystemException;`
- [ ] 添加`import org.xkong.cloud.commons.exception.database.DatabaseSystemException;`

**关键异常替换检查**:
- [ ] 第175行：`IllegalStateException("WorkerIdAssigner未运行")` → `ValidationBusinessException.invalidState()` + XCE_VAL_780
- [ ] 第198行：`IllegalStateException("获取workerId锁超时")` → `SystemException.resourceTimeout()` + XCE_BIZ_180
- [ ] 第202行：`IllegalStateException("获取工作机器ID时被中断")` → `SystemException.operationInterrupted()` + XCE_BIZ_180
- [ ] 第230行：Worker ID分配失败 → `BusinessException.resourceAllocationFailed()` + XCE_BIZ_181
- [ ] 其他23个异常处理点按类型分组处理

**执行策略**: 分5批处理，每批5-6个异常点，每批完成后立即编译验证

**质量门禁**:
- [ ] 编译成功：`mvn compile`，单元测试通过，不影响依赖文件

### 步骤3.2：修改PersistentInstanceManager.java（20个异常点）
**目标**: 实例管理组件异常处理切换

**ACE分析验证检查**:
- [ ] ACE成功分析@PersistentInstanceManager.java的完整实现和实例管理逻辑
- [ ] ACE识别出该组件与@PersistentInstanceWorkerIdAssigner.java的协作关系
- [ ] ACE分析出实例管理模块的依赖关系和数据流
- [ ] ACE分析结果与JSON配置映射规则保持一致

**执行检查**（来自08-依赖关系映射.json → dependency_map.uid_library.critical_files[1]）:
- [ ] 文件存在且可编辑：`xkongcloud-commons\xkongcloud-commons-uid\src\main\java\org\xkong\cloud\commons\uid\instance\PersistentInstanceManager.java`
- [ ] 确认当前异常抛出点：20个

**关键操作**（来自09-配置参数映射.json → exception_mapping_rules）:
**导入语句添加检查**:
- [ ] 添加`import org.xkong.cloud.commons.exception.core.BusinessException;`
- [ ] 添加`import org.xkong.cloud.commons.exception.core.SystemException;`
- [ ] 添加`import org.xkong.cloud.commons.exception.validation.ValidationBusinessException;`
- [ ] 添加`import org.xkong.cloud.commons.exception.file.FileSystemException;`

**关键异常替换检查**:
- [ ] 第126行：`IllegalArgumentException("无效的恢复策略")` → `ValidationBusinessException.invalidArgument()` + XCE_VAL_780
- [ ] 第138行：`IllegalStateException("实例ID尚未初始化")` → `ValidationBusinessException.invalidState()` + XCE_VAL_781
- [ ] 第230行：IOException文件读取 → `FileSystemException.fileReadError()` + XCE_FILE_720
- [ ] 第265行：IOException文件写入 → `FileSystemException.fileWriteError()` + XCE_FILE_721
- [ ] 其他16个异常处理点按功能模块分组处理

**执行策略**: 分4批处理，每批5个异常点，每批完成后立即编译验证

**质量门禁**:
- [ ] 编译成功：`mvn compile`，单元测试通过

### 步骤3.3：其他组件异常切换
**目标**: 完成剩余组件的异常切换

**ACE分析验证检查**:
- [ ] ACE成功分析@08-依赖关系映射.json中列出的所有其他组件
- [ ] ACE识别出各组件的异常处理模式和与核心组件的一致性
- [ ] ACE分析出组件间的调用关系和异常传播路径
- [ ] ACE分析结果与标准Java异常映射规则保持一致

**执行检查**:
- 组件列表参考：08-依赖关系映射.json → dependency_map.uid_library.other_files
- [ ] UidGeneratorFacade.java：门面层异常处理
- [ ] PostgreSQLMetadataService.java：数据库元数据异常
- [ ] MachineFingerprints.java：机器特征码收集异常
- [ ] UidTableManager.java：表管理异常
- [ ] KeyManagementService.java：密钥管理异常

**执行策略**: 每个文件修改后立即验证编译和基础功能

## 阶段4：异常处理器集成检查清单

### 步骤4.1：创建UID专用异常处理器
**目标**: 创建继承XCE的专用异常处理器

**执行检查**:
- 异常处理器模板参考：03-代码修改模板.md → UID专用异常处理器
- [ ] 确认目录存在或创建exception目录
- [ ] 创建UidExceptionHandler.java文件
- [ ] 实现继承GlobalExceptionHandler

**质量门禁**:
- [ ] 编译成功，异常处理器正常工作

### 步骤4.2：配置XCE自动配置
**目标**: 启用XCE异常处理自动配置

**执行检查**:
- 配置文件参考：08-依赖关系映射.json → dependency_map.uid_library.config_files
- [ ] 确认目录存在或创建META-INF目录
- [ ] 创建或更新spring.factories文件

**执行后验证**:
- [ ] Spring Boot应用启动时自动加载异常处理器

## 阶段5：测试验证检查清单

### 步骤5.1：单元测试更新
**目标**: 更新所有涉及异常处理的单元测试

**ACE分析验证检查**:
- [ ] ACE成功分析整个项目中的测试结构和异常测试组织方式
- [ ] ACE在项目范围内发现所有涉及异常处理的测试文件
- [ ] ACE分析出当前测试对异常处理的覆盖情况
- [ ] ACE分析结果与测试代码修改模板保持一致

**执行检查**:
- 测试文件列表参考：08-依赖关系映射.json → dependency_map.uid_library.test_files
- [ ] 识别所有涉及异常处理的单元测试
- [ ] 更新测试用例以验证XCE异常
- [ ] 更新异常类型断言和错误码验证
- [ ] 确保测试覆盖所有XCE异常类型和错误码

**执行后验证**:
- [ ] 所有单元测试通过：`mvn test`

### 步骤5.2：集成测试验证
**目标**: 端到端功能测试验证

**执行检查**:
- 测试场景参考：02-执行检查清单.md → 阶段5测试验证检查清单
- [ ] 端到端UID生成功能测试
- [ ] Worker ID分配功能测试
- [ ] 实例恢复功能测试
- [ ] 异常场景模拟测试

**执行后验证**:
- [ ] 所有集成测试通过，异常处理符合预期

### 步骤5.3：文档更新
**目标**: 更新相关文档说明

**执行检查**:
- 文档列表参考：08-依赖关系映射.json → dependency_map.uid_library.documentation_files
- [ ] 更新README.md异常处理说明
- [ ] 更新使用指南异常处理最佳实践
- [ ] 更新API文档异常类型说明

**执行后验证**:
- [ ] 文档内容准确完整，异常处理说明清晰

## 阶段6：Core项目配套修改检查清单

### 步骤6.1：Core项目UidGeneratorConfig异常处理更新
**目标**: 更新Core项目中的UID配置异常处理

**ACE分析验证检查**:
- [ ] ACE成功分析@UidGeneratorConfig.java在整个Core项目中的作用和集成方式
- [ ] ACE分析出Core项目通过UID库间接依赖XCE异常库的完整调用链
- [ ] ACE识别出配置层异常处理与UID库异常处理的一致性要求
- [ ] ACE分析结果与错误码映射配置保持一致

**执行检查**:
- 配置文件参考：05-Core项目配套修改指导.md → Core项目异常处理适配
- [ ] 确认UID库XCE切换已完成
- [ ] 文件存在且可编辑：`xkongcloud-business-internal-core\src\main\java\org\xkong\cloud\business\internal\core\config\UidGeneratorConfig.java`

**关键操作**（来自05-Core项目配套修改指导.md → 具体修改内容）:
**导入语句添加检查**:
- [ ] 添加`import org.xkong.cloud.commons.exception.core.SystemException;`
- [ ] 添加`import org.xkong.cloud.commons.exception.validation.ValidationBusinessException;`

**异常处理替换检查**:
- [ ] 第188-191行：配置异常处理替换
  ```java
  // 原代码
  throw new IllegalStateException("Failed to create UidGeneratorFacade", e);
  // 替换为
  throw SystemException.configurationError("UID生成器配置失败: " + e.getMessage(), e);
  ```
- [ ] 第209行：UnsupportedOperationException替换
  ```java
  // 原代码
  throw new UnsupportedOperationException("parseUID方法未实现");
  // 替换为
  throw ValidationBusinessException.operationNotSupported("parseUID方法未实现");
  ```

**执行后验证**:
- [ ] 编译成功：`mvn compile -pl xkongcloud-business-internal-core`
- [ ] Spring Boot应用正常启动，XCE异常类可正常导入

### 步骤6.2：Core项目依赖验证
**目标**: 验证Core项目对XCE异常库的依赖关系

**执行检查**:
- 依赖关系参考：08-依赖关系映射.json → dependency_map.core_project
- [ ] 验证间接依赖：通过UID库依赖XCE异常库
- [ ] 测试XCE异常类导入
- [ ] 如需要，添加显式XCE依赖

**执行后验证**:
- [ ] XCE异常类可正常导入，依赖关系清晰无冲突

### 步骤6.3：Core项目测试验证
**目标**: 验证Core项目集成测试正常

**执行检查**:
- 测试验证参考：05-Core项目配套修改指导.md → 测试验证
- [ ] 运行UID生成集成测试
- [ ] 验证异常场景测试
- [ ] 执行性能基准测试

**执行后验证**:
- [ ] 所有集成测试通过，性能无显著下降

## 最终验证检查清单

### 功能验证
**验证指引**:
- 功能验证参考：02-执行检查清单.md → 成功标准验证
- [ ] UID库：UID生成、Worker ID分配、实例恢复功能正常
- [ ] UID库：异常处理功能正常
- [ ] Core项目：UID生成功能正常，Spring Boot应用正常启动
- [ ] 所有原有功能保持不变

### 技术验证
**验证指引**:
- 技术标准参考：11-自动化质量检查.md → 质量门禁标准
- [ ] UID库和Core项目编译无错误无警告
- [ ] 所有单元测试和集成测试通过
- [ ] 性能测试通过，内存使用正常

### 兼容性验证
- [ ] UID库与XCE异常库兼容
- [ ] Core项目与更新后的UID库兼容
- [ ] API接口保持不变，依赖关系正常

### 文档验证
**验证指引**:
- 文档标准参考：02-执行检查清单.md → 文档更新检查清单
- [ ] UID库相关文档更新完成
- [ ] Core项目相关文档更新完成

## 回滚检查清单

### 回滚触发条件
**回滚策略参考**：04-风险评估与回滚方案.md → 回滚触发条件
- [ ] 编译失败且无法快速修复
- [ ] 核心功能异常且影响业务
- [ ] 性能显著下降超过10%
- [ ] 兼容性问题无法解决

### 回滚操作
**注意**: 不自动执行Git或备份操作，需要人类决策
- [ ] 按照04-风险评估与回滚方案.md执行回滚
- [ ] 验证回滚后功能正常
