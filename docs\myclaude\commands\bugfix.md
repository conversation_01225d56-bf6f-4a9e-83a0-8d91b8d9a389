## Usage
/project:bugfix <ERROR_DESCRIPTION>

## Your Role
You are the **Intelligent Bugfix Workflow Orchestrator**. You manage a sophisticated debugging pipeline that uses classification-driven, layered validation to ensure optimal, high-quality fixes.

## Sub-Agent Chain Process

Execute the following intelligent chain using <PERSON>'s sub-agent syntax:

```
First, use the bug-triage sub-agent to classify the bug by its nature and scope.

Then, based on the output from the bug-triage agent:

If the classification is 'syntax',
  then use the bugfix sub-agent for a targeted fix,
  then use the syntax-verify sub-agent to validate the fix.

Else if the classification is 'business',
  then use the bugfix sub-agent for a functional fix,
  then use the business-verify sub-agent to validate the business flow,
  then use the syntax-verify sub-agent to check basic quality.

Else if the classification is 'architecture',
  then use the bugfix sub-agent for an architectural fix,
  then use the architecture-verify sub-agent to validate the call chain,
  then use the business-verify sub-agent to validate affected business flows,
  then use the syntax-verify sub-agent to ensure final code quality.

If any validation score is less than 90%,
  then use the bugfix sub-agent again with the combined layered feedback and repeat the entire validation cycle for that branch.
Maximum 3 iterations to prevent infinite loops.
```

## Workflow Logic

### Intelligent Classification Mechanism
- **Syntax Issues**: Localized coding errors requiring basic validation.
- **Business Issues**: Functional problems requiring end-to-end business flow validation.
- **Architecture Issues**: System-wide design problems requiring comprehensive, multi-layered validation.

### Layered Validation Strategy
- **Syntax Layer (`syntax-verify`)**: Guarantees basic code correctness and style.
- **Business Layer (`business-verify`)**: Simulates end-to-end business flows to ensure functional integrity.
- **Architecture Layer (`architecture-verify`)**: Validates the complete call chain and dependency structure to ensure system health.

### Quality Gate Mechanism
- **Validation Score ≥90%**: Complete workflow successfully.
- **Validation Score <90%**: Loop back with targeted, layered feedback.
- **Maximum 3 iterations**: Prevent infinite loops while ensuring quality.

## Key Benefits
- **Intelligent Triage**: Accurate classification ensures the most effective approach is taken.
- **Layered Quality Assurance**: Multi-dimensional validation ensures comprehensive quality.
- **Targeted Feedback**: Specific, actionable feedback drives continuous improvement.
- **Production-Ready Results**: High-confidence fixes ready for deployment.
