# V4.0综合评估提示词

## 🎯 综合评估体系设计提示词

```
设计V4.0综合评估体系：

评估维度：
1. 技术指标：置信度95%、覆盖率80%
2. 质量指标：代码可编译率、架构一致性
3. 效率指标：生成速度、AI调用成本
4. 用户体验：操作简便性、错误处理

评估方法：
- 自动化测试套件（单元测试、集成测试、端到端测试）
- 多维度置信度算法（7维度+AI增强+质量验证）
- 实时质量监控（代码质量、性能指标、用户反馈）
- 用户反馈收集（满意度调研、使用体验分析）

评估指标：
- 技术成熟度：TRL 7-8级别（技术验证到系统原型）
- 质量成熟度：CMMI Level 3（已定义级别）
- 用户接受度：>90%满意度
- 商业价值：ROI>300%

请设计完整的评估框架和改进机制。
```

## 📊 置信度算法增强提示词

```
设计V4.0增强版置信度算法：

算法结构：
总置信度 = 基础置信度(40%) + AI增强置信度(35%) + 质量验证置信度(25%)

基础置信度（V3.1原有7维度）：
1. 基础置信度：25%
2. 步骤顺序：20%
3. 记忆库对齐：15%
4. 依赖分析：15%
5. 外部验证：10%
6. 专家思维链：15%

AI增强置信度（新增维度）：
1. AI模型一致性：40%（多AI结果的一致程度）
2. 内容完整性：30%（AI填充的完整度和质量）
3. 技术准确性：30%（AI生成内容的技术正确性）

质量验证置信度（新增维度）：
1. 编译通过率：40%（代码语法正确性）
2. 架构一致性：30%（与设计文档的匹配度）
3. 最佳实践符合度：30%（代码质量和规范）

动态调整机制：
- 基于历史成功率调整权重
- 根据项目复杂度调整阈值
- 实时反馈优化算法参数

请设计详细的置信度计算算法和优化策略。
```

## 🔍 质量监控体系提示词

```
设计V4.0实时质量监控体系：

监控层级：
1. 系统级监控（整体性能、可用性）
2. 组件级监控（扫描器、生成器、AI引擎）
3. 任务级监控（单个文档生成过程）
4. 用户级监控（用户体验、满意度）

监控指标：
- 技术指标：响应时间、成功率、错误率、资源使用
- 质量指标：代码质量分数、文档完整度、架构一致性
- 业务指标：用户活跃度、任务完成率、满意度评分
- 成本指标：AI调用成本、资源消耗、运维成本

监控工具：
- APM工具（Application Performance Monitoring）
- 日志聚合（ELK Stack或类似）
- 指标收集（Prometheus + Grafana）
- 告警系统（PagerDuty或类似）

数据分析：
- 实时仪表板（关键指标可视化）
- 趋势分析（性能趋势、质量趋势）
- 异常检测（自动识别异常模式）
- 预测分析（容量规划、故障预测）

请设计完整的质量监控和分析系统。
```

## 📈 性能基准测试提示词

```
设计V4.0性能基准测试体系：

测试类型：
1. 功能性能测试（单个功能的响应时间）
2. 负载测试（并发用户下的系统表现）
3. 压力测试（极限负载下的系统稳定性）
4. 容量测试（系统容量上限和扩展性）

性能指标：
- 响应时间：P50<2分钟，P95<5分钟，P99<10分钟
- 吞吐量：>10个文档/小时（单实例）
- 并发能力：支持5个并发任务
- 资源使用：CPU<80%，内存<4GB，磁盘I/O<100MB/s

测试场景：
- 小型项目（<50个类）：目标2分钟完成
- 中型项目（50-200个类）：目标5分钟完成
- 大型项目（>200个类）：目标10分钟完成
- 复杂架构项目：目标15分钟完成

基准数据：
- V3.1当前性能作为基线
- 行业标准和竞品对比
- 用户期望和业务需求
- 技术限制和成本约束

请设计完整的性能测试方案和基准体系。
```

## 🔄 持续改进机制提示词

```
设计V4.0持续改进机制：

改进循环：
1. 数据收集（用户反馈、系统指标、质量数据）
2. 问题识别（自动分析、人工审核、优先级排序）
3. 解决方案设计（技术方案、实施计划、风险评估）
4. 实施验证（A/B测试、灰度发布、效果评估）

反馈渠道：
- 用户反馈（满意度调研、使用体验报告）
- 系统监控（性能指标、错误日志、质量数据）
- 专家评审（技术评审、架构评审、代码评审）
- 市场反馈（竞品分析、行业趋势、客户需求）

改进策略：
- 快速迭代（2周一个小版本）
- 数据驱动（基于指标和反馈决策）
- 风险控制（渐进式改进、回滚机制）
- 用户参与（Beta测试、用户共创）

改进重点：
- AI模型优化（提升成功率和质量）
- 用户体验改进（简化操作、优化界面）
- 性能优化（提升速度、降低资源消耗）
- 功能扩展（新的文档类型、更多配置选项）

请设计完整的持续改进体系和实施方案。
```

## 🎖️ 成功标准定义提示词

```
定义V4.0项目成功标准：

技术成功标准：
- 置信度达到95%（高置信度）
- 文档覆盖率达到80%（高覆盖率）
- JSON使用率达到90%（高利用率）
- 代码质量达到90+分（生产级质量）

业务成功标准：
- 用户满意度>90%（高用户满意度）
- 任务完成率>95%（高成功率）
- 系统可用性>99%（高可靠性）
- 成本效益比>3:1（高投资回报）

用户体验标准：
- 学习成本<2小时（易于上手）
- 操作效率提升>50%（显著提效）
- 错误率<5%（低错误率）
- 支持响应时间<4小时（快速支持）

质量标准：
- 代码编译通过率100%（无语法错误）
- 架构一致性>95%（高度一致）
- 安全漏洞0个（安全可靠）
- 性能基准达标100%（性能优秀）

验收标准：
- 所有自动化测试通过
- 专家评审通过
- 用户验收测试通过
- 生产环境试运行成功

请设计完整的成功标准和验收体系。
```

---

*基于SMART原则制定的可量化成功标准*  
*确保项目目标明确、可衡量、可达成*  
*创建时间：2025-06-14*
