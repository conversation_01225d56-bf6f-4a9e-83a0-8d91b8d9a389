---
title: 分布式系统监控指标设计指南
document_id: C048
document_type: 最佳实践指南
category: 监控与运维
scope: 通用指南
keywords: [监控指标, 分布式系统, 告警规则, 性能基线, 可观测性]
created_date: 2025-01-15
updated_date: 2025-01-15
status: 生效
version: 1.0
authors: [系统架构组]
source_extraction: F003-PostgreSQL迁移重构项目
---

# 分布式系统监控指标设计指南

## 概述

分布式系统监控指标设计是确保系统可观测性和运维效率的关键。本指南提供了从指标分类、收集策略到告警规则设计的完整方法论，帮助构建全面、高效的监控体系。

## 监控指标分类

### 1. 四大指标类型

#### 1.1 计数器 (Counter)
**特点**: 单调递增，用于统计事件发生次数

```java
// 计数器示例
private final Counter renewalSuccessCounter = Counter.builder("uid.renewal.success")
    .description("成功续约次数")
    .register(meterRegistry);

private final Counter renewalFailureCounter = Counter.builder("uid.renewal.failure")
    .description("续约失败次数")
    .tag("type", "unknown")
    .register(meterRegistry);

// 使用方式
public void recordRenewalSuccess() {
    renewalSuccessCounter.increment();
}

public void recordRenewalFailure(FailureType type) {
    Counter.builder("uid.renewal.failure")
        .tag("type", type.name().toLowerCase())
        .register(meterRegistry)
        .increment();
}
```

#### 1.2 计时器 (Timer)
**特点**: 测量操作耗时和频率

```java
// 计时器示例
private final Timer renewalTimer = Timer.builder("uid.renewal.duration")
    .description("续约操作耗时")
    .register(meterRegistry);

private final Timer allocationTimer = Timer.builder("uid.worker.allocation.duration")
    .description("Worker ID分配耗时")
    .register(meterRegistry);

// 使用方式
public void recordRenewalDuration(Duration duration) {
    renewalTimer.record(duration);
}

// 或者使用Timer.Sample
Timer.Sample sample = Timer.start(meterRegistry);
// 执行操作
sample.stop(renewalTimer);
```

#### 1.3 仪表盘 (Gauge)
**特点**: 反映当前状态值，可增可减

```java
// 仪表盘示例
private volatile int activeWorkerIdCount = 0;
private volatile int lastRecoveryScore = 0;

private final Gauge activeWorkerIdsGauge = Gauge.builder("uid.worker.active.count")
    .description("活跃的Worker ID数量")
    .register(meterRegistry, this, UidMetricsCollector::getActiveWorkerIdCount);

private final Gauge instanceRecoveryScoreGauge = Gauge.builder("uid.instance.recovery.score")
    .description("最近一次实例恢复的匹配分数")
    .register(meterRegistry, this, UidMetricsCollector::getLastRecoveryScore);

// 更新方式
public void updateActiveWorkerIdCount(int count) {
    this.activeWorkerIdCount = count;
}
```

#### 1.4 分布汇总 (Distribution Summary)
**特点**: 统计数值分布情况

```java
// 分布汇总示例
private final DistributionSummary matchScoreDistribution = 
    DistributionSummary.builder("uid.instance.match.score")
        .description("实例匹配分数分布")
        .minimumExpectedValue(0.0)
        .maximumExpectedValue(300.0)
        .register(meterRegistry);

// 使用方式
public void recordMatchScore(int score) {
    matchScoreDistribution.record(score);
}
```

### 2. 业务指标分层

#### 2.1 基础设施层指标
```yaml
系统资源:
  - CPU使用率: system.cpu.usage
  - 内存使用率: system.memory.usage
  - 磁盘使用率: system.disk.usage
  - 网络IO: system.network.io

JVM指标:
  - 堆内存使用: jvm.memory.used
  - GC次数和耗时: jvm.gc.pause
  - 线程数: jvm.threads.live
  - 类加载数: jvm.classes.loaded
```

#### 2.2 中间件层指标
```yaml
数据库连接池:
  - 活跃连接数: hikaricp.connections.active
  - 等待连接数: hikaricp.connections.pending
  - 连接超时次数: hikaricp.connections.timeout

消息队列:
  - 消息发送成功率: mq.message.send.success.rate
  - 消息消费延迟: mq.message.consume.lag
  - 队列深度: mq.queue.depth
```

#### 2.3 应用层指标
```yaml
业务功能:
  - 租约申请成功率: lease.acquisition.success.rate
  - 续约成功率: lease.renewal.success.rate
  - 实例恢复成功率: instance.recovery.success.rate
  - 平均响应时间: application.response.time

错误统计:
  - 错误率: application.error.rate
  - 异常类型分布: application.exception.type
  - 重试次数分布: application.retry.count
```

## 指标收集策略

### 1. 指标收集器设计

```java
@Component
public class ComprehensiveMetricsCollector {
    
    private final MeterRegistry meterRegistry;
    
    // 指标缓存，避免重复创建
    private final ConcurrentHashMap<String, Counter> counterCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Timer> timerCache = new ConcurrentHashMap<>();
    
    public ComprehensiveMetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        initializeCommonMetrics();
    }
    
    /**
     * 初始化常用指标
     */
    private void initializeCommonMetrics() {
        // 预创建常用计数器
        getOrCreateCounter("application.startup", "应用启动次数");
        getOrCreateCounter("application.shutdown", "应用关闭次数");
        
        // 预创建常用计时器
        getOrCreateTimer("application.operation.duration", "应用操作耗时");
    }
    
    /**
     * 获取或创建计数器
     */
    private Counter getOrCreateCounter(String name, String description, String... tags) {
        String key = buildCacheKey(name, tags);
        return counterCache.computeIfAbsent(key, k -> {
            Counter.Builder builder = Counter.builder(name).description(description);
            for (int i = 0; i < tags.length; i += 2) {
                builder.tag(tags[i], tags[i + 1]);
            }
            return builder.register(meterRegistry);
        });
    }
    
    /**
     * 获取或创建计时器
     */
    private Timer getOrCreateTimer(String name, String description, String... tags) {
        String key = buildCacheKey(name, tags);
        return timerCache.computeIfAbsent(key, k -> {
            Timer.Builder builder = Timer.builder(name).description(description);
            for (int i = 0; i < tags.length; i += 2) {
                builder.tag(tags[i], tags[i + 1]);
            }
            return builder.register(meterRegistry);
        });
    }
    
    /**
     * 构建缓存键
     */
    private String buildCacheKey(String name, String... tags) {
        StringBuilder key = new StringBuilder(name);
        for (int i = 0; i < tags.length; i += 2) {
            key.append(":").append(tags[i]).append("=").append(tags[i + 1]);
        }
        return key.toString();
    }
    
    /**
     * 记录业务事件
     */
    public void recordBusinessEvent(String eventType, String result, Duration duration) {
        // 记录计数
        getOrCreateCounter("business.event", "业务事件计数", 
                          "type", eventType, "result", result).increment();
        
        // 记录耗时
        getOrCreateTimer("business.event.duration", "业务事件耗时",
                        "type", eventType).record(duration);
    }
    
    /**
     * 记录错误事件
     */
    public void recordError(String component, String errorType, Exception e) {
        getOrCreateCounter("application.error", "应用错误计数",
                          "component", component, "type", errorType).increment();
        
        // 记录错误详情到日志
        log.error("组件 {} 发生 {} 类型错误", component, errorType, e);
    }
}
```

### 2. 自动化指标注册

```java
/**
 * 基于注解的自动指标收集
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Monitored {
    String name() default "";
    String description() default "";
    String[] tags() default {};
}

@Aspect
@Component
public class MetricsAspect {
    
    private final ComprehensiveMetricsCollector metricsCollector;
    
    @Around("@annotation(monitored)")
    public Object monitor(ProceedingJoinPoint joinPoint, Monitored monitored) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        String metricName = monitored.name().isEmpty() ? methodName : monitored.name();
        
        Timer.Sample sample = Timer.start();
        try {
            Object result = joinPoint.proceed();
            Duration duration = sample.stop(Timer.builder(metricName + ".duration")
                .register(meterRegistry));
            
            metricsCollector.recordBusinessEvent(metricName, "success", duration);
            return result;
            
        } catch (Exception e) {
            Duration duration = sample.stop(Timer.builder(metricName + ".duration")
                .register(meterRegistry));
            
            metricsCollector.recordBusinessEvent(metricName, "failure", duration);
            metricsCollector.recordError(joinPoint.getTarget().getClass().getSimpleName(), 
                                       e.getClass().getSimpleName(), e);
            throw e;
        }
    }
}

// 使用示例
@Service
public class LeaseService {
    
    @Monitored(name = "lease.acquisition", description = "租约申请操作")
    public Lease acquireLease(String instanceId) {
        // 业务逻辑
    }
    
    @Monitored(name = "lease.renewal", description = "租约续约操作")
    public boolean renewLease(String leaseId) {
        // 业务逻辑
    }
}
```

## 告警规则设计

### 1. 告警规则分类

#### 1.1 可用性告警
```yaml
# 服务可用性告警
groups:
  - name: availability-alerts
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务 {{ $labels.instance }} 不可用"
          description: "服务已停止响应超过1分钟"
          
      - alert: HighErrorRate
        expr: |
          (
            rate(application_error_total[5m]) / 
            rate(application_request_total[5m])
          ) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "错误率过高"
          description: "过去5分钟错误率超过5%"
```

#### 1.2 性能告警
```yaml
# 性能相关告警
- alert: HighLatency
  expr: |
    histogram_quantile(0.95, 
      rate(application_operation_duration_seconds_bucket[5m])
    ) > 2
  for: 3m
  labels:
    severity: warning
  annotations:
    summary: "响应延迟过高"
    description: "95%的请求响应时间超过2秒"

- alert: HighThroughputDrop
  expr: |
    (
      rate(application_request_total[5m]) / 
      rate(application_request_total[5m] offset 1h)
    ) < 0.5
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "吞吐量大幅下降"
    description: "当前吞吐量比1小时前下降超过50%"
```

#### 1.3 资源告警
```yaml
# 资源使用告警
- alert: HighCpuUsage
  expr: system_cpu_usage > 0.8
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "CPU使用率过高"
    description: "CPU使用率持续5分钟超过80%"

- alert: HighMemoryUsage
  expr: system_memory_usage > 0.85
  for: 3m
  labels:
    severity: critical
  annotations:
    summary: "内存使用率过高"
    description: "内存使用率超过85%"

- alert: DatabaseConnectionPoolExhaustion
  expr: hikaricp_connections_active / hikaricp_connections_max > 0.9
  for: 2m
  labels:
    severity: critical
  annotations:
    summary: "数据库连接池即将耗尽"
    description: "连接池使用率超过90%"
```

### 2. 业务指标告警

```yaml
# 业务相关告警
- alert: LeaseRenewalFailureRateHigh
  expr: |
    (
      rate(lease_renewal_failure_total[5m]) / 
      (rate(lease_renewal_success_total[5m]) + rate(lease_renewal_failure_total[5m]))
    ) > 0.1
  for: 2m
  labels:
    severity: warning
  annotations:
    summary: "租约续约失败率过高"
    description: "过去5分钟续约失败率超过10%"

- alert: InstanceRecoveryFailureFrequent
  expr: increase(instance_recovery_failure_total[10m]) > 5
  for: 1m
  labels:
    severity: warning
  annotations:
    summary: "实例恢复频繁失败"
    description: "过去10分钟内有{{ $value }}次实例恢复失败"

- alert: WorkerIdResourceExhaustion
  expr: worker_id_active_count / worker_id_total_count > 0.8
  for: 1m
  labels:
    severity: critical
  annotations:
    summary: "Worker ID资源即将耗尽"
    description: "Worker ID使用率已达到{{ $value | humanizePercentage }}"
```

## 性能基线建立

### 1. 基线指标定义

```yaml
性能基线标准:
  响应时间:
    P50: < 100ms
    P95: < 500ms
    P99: < 1000ms
    
  吞吐量:
    正常负载: 1000 TPS
    峰值负载: 2000 TPS
    
  可用性:
    服务可用性: > 99.9%
    数据一致性: > 99.99%
    
  资源使用:
    CPU使用率: < 70%
    内存使用率: < 80%
    数据库连接池: < 80%
    
  业务指标:
    租约申请成功率: > 99.5%
    续约成功率: > 99.9%
    实例恢复成功率: > 95%
```

### 2. 基线监控实现

```java
@Component
public class BaselineMonitor {
    
    private final MeterRegistry meterRegistry;
    
    // 基线阈值配置
    @Value("${monitoring.baseline.response-time.p95:500}")
    private long responseTimeP95Threshold;
    
    @Value("${monitoring.baseline.success-rate:0.995}")
    private double successRateThreshold;
    
    /**
     * 检查性能基线
     */
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void checkPerformanceBaseline() {
        checkResponseTimeBaseline();
        checkSuccessRateBaseline();
        checkResourceUsageBaseline();
    }
    
    private void checkResponseTimeBaseline() {
        Timer responseTimer = meterRegistry.find("application.response.time").timer();
        if (responseTimer != null) {
            double p95 = responseTimer.takeSnapshot().percentileValue(0.95);
            if (p95 > responseTimeP95Threshold) {
                recordBaselineViolation("response_time_p95", p95, responseTimeP95Threshold);
            }
        }
    }
    
    private void checkSuccessRateBaseline() {
        Counter successCounter = meterRegistry.find("application.success").counter();
        Counter totalCounter = meterRegistry.find("application.total").counter();
        
        if (successCounter != null && totalCounter != null) {
            double successRate = successCounter.count() / totalCounter.count();
            if (successRate < successRateThreshold) {
                recordBaselineViolation("success_rate", successRate, successRateThreshold);
            }
        }
    }
    
    private void recordBaselineViolation(String metric, double actual, double threshold) {
        meterRegistry.counter("baseline.violation", 
                            "metric", metric,
                            "severity", actual < threshold * 0.8 ? "critical" : "warning")
                    .increment();
        
        log.warn("性能基线违规: {} 当前值={}, 阈值={}", metric, actual, threshold);
    }
}
```

## 可观测性最佳实践

### 1. 三大支柱集成

```yaml
Metrics (指标):
  - 量化系统行为
  - 支持告警和趋势分析
  - 低存储成本

Logging (日志):
  - 详细的事件记录
  - 支持问题诊断
  - 结构化日志格式

Tracing (链路追踪):
  - 分布式请求追踪
  - 性能瓶颈定位
  - 依赖关系分析
```

### 2. 监控数据关联

```java
/**
 * 统一的可观测性上下文
 */
@Component
public class ObservabilityContext {
    
    private static final String TRACE_ID_HEADER = "X-Trace-ID";
    private static final String SPAN_ID_HEADER = "X-Span-ID";
    
    /**
     * 记录带追踪信息的指标
     */
    public void recordMetricWithTrace(String metricName, double value, String... tags) {
        String traceId = getCurrentTraceId();
        String spanId = getCurrentSpanId();
        
        List<String> allTags = new ArrayList<>(Arrays.asList(tags));
        if (traceId != null) {
            allTags.add("trace_id");
            allTags.add(traceId);
        }
        
        meterRegistry.gauge(metricName, Tags.of(allTags.toArray(new String[0])), value);
    }
    
    /**
     * 记录带追踪信息的日志
     */
    public void logWithTrace(String message, Object... args) {
        String traceId = getCurrentTraceId();
        MDC.put("traceId", traceId);
        try {
            log.info(message, args);
        } finally {
            MDC.remove("traceId");
        }
    }
}
```

## 相关指南

- [日志记录最佳实践](../logging/logging-best-practices.md)
- [性能测试指南](../testing/performance-testing-guide.md)
- [分布式租约管理模式](../../architecture/patterns/distributed-lease-management-pattern.md)
- [故障排除指南](../../troubleshooting/distributed-system-troubleshooting.md)
