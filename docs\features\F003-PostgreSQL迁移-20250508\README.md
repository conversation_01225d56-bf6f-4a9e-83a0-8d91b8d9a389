---
title: PostgreSQL迁移
document_id: F003-README
document_type: 功能文档
category: 数据库迁移
scope: XKC-CORE
keywords: [PostgreSQL, 数据库迁移, Cassandra, 关系型数据库]
created_date: 2025-05-08
updated_date: 2025-05-08
status: 进行中
version: 1.0
authors: [系统架构组, AI助手]
---

# PostgreSQL迁移

- **功能ID**: F003
- **创建日期**: 2025-05-08
- **状态**: 进行中
- **负责人**: 系统架构组
- **项目代号**: XKC-CORE

## 功能概述

本功能旨在将XKC-CORE项目中的数据库从Cassandra迁移到PostgreSQL，以获得更好的关系型数据库支持、事务处理能力和查询灵活性。迁移过程包括依赖调整、配置转换、实体类转换、仓库接口转换和数据模型重新设计等步骤。

## 文档索引

### 需求文档
- [迁移需求](./requirements/migration-requirements.md)

### 设计文档
- [迁移方案设计](./design/migration-design.md)
- [PostgreSQL迁移与持续演进架构整合方案](./design/postgresql-evolution-architecture-integration.md)

### 实施计划
- [迁移实施计划](./plan/implementation-plan.md)
- [PostgreSQL迁移第3阶段演进架构实施方案](./plan/phase3-evolution-architecture-implementation.md)
- [PostgreSQL替代Cassandra迁移方案](../../plans/2-PostgreSQL/postgresql_migration_plan.md)

### API文档
- [数据访问层API](./api/data-access-api.md)

### 测试文档
- [迁移测试计划](./test/migration-test-plan.md)

## 相关文档
- [PostgreSQL集成指南](../../common/middleware/postgresql/integration-guide.md)
- [Cassandra数据模型设计](../../common/middleware/cassandra/data-model-design.md)

## 变更历史
| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 1.0 | 2025-05-08 | 初始版本 | AI助手 |
