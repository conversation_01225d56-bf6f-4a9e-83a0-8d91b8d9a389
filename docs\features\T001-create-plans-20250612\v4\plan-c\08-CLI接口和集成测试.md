# V4 - CLI接口和集成测试

## 📋 实施概述
**文档ID**: V4-PLAN-008  
**阶段**: CLI接口和集成测试实现  
**置信度**: 95%  

## 🎯 核心目标
实现V4命令行接口和完整的端到端集成测试，提供用户友好的CLI工具，支持所有V4引擎功能，确保系统整体集成质量达到95%置信度。

## 🏗️ CLI架构设计

### 核心CLI结构
```
src/v4_scaffolding/cli/
├── __init__.py
├── main.py                      # CLI入口
├── commands/                    # 命令模块
│   ├── __init__.py
│   ├── analyze.py               # 分析命令
│   ├── map.py                  # 映射命令
│   ├── check.py                # 检查命令
│   └── report.py               # 报告命令
├── utils/                       # CLI工具
│   ├── __init__.py
│   ├── output_formatter.py     # 输出格式化
│   ├── progress_tracker.py     # 进度跟踪
│   └── config_loader.py        # 配置加载
└── templates/                   # 输出模板
    ├── analysis_report.jinja2
    ├── mapping_report.jinja2
    └── consistency_report.jinja2
```

## 🔧 核心CLI实施代码

### CLI主入口 - src/v4_scaffolding/cli/main.py
```python
"""V4 Scaffolding CLI主入口"""

import asyncio
import sys
from pathlib import Path
from typing import Optional
import click
import logging

from ..core.system import V4ScaffoldingSystem
from ..core.config import config
from .utils.output_formatter import OutputFormatter
from .utils.progress_tracker import ProgressTracker


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), help='配置文件路径')
@click.option('--verbose', '-v', is_flag=True, help='详细输出')
@click.option('--quiet', '-q', is_flag=True, help='安静模式')
@click.pass_context
def cli(ctx, config: Optional[str], verbose: bool, quiet: bool):
    """V4 Scaffolding - 智能脚手架系统
    
    用于分析设计文档、生成代码映射、检查一致性的工具。
    """
    # 设置日志级别
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    elif quiet:
        logging.getLogger().setLevel(logging.WARNING)
    
    # 初始化系统
    ctx.ensure_object(dict)
    ctx.obj['config_path'] = config
    ctx.obj['verbose'] = verbose
    ctx.obj['quiet'] = quiet
    
    # 创建输出格式化器
    ctx.obj['formatter'] = OutputFormatter(
        verbose=verbose,
        quiet=quiet
    )
    
    # 创建进度跟踪器
    ctx.obj['progress'] = ProgressTracker(
        enabled=not quiet
    )


@cli.command()
@click.argument('document_path', type=click.Path(exists=True))
@click.option('--output', '-o', type=click.Path(), help='输出文件路径')
@click.option('--format', 'output_format', 
              type=click.Choice(['json', 'yaml', 'markdown']), 
              default='json', help='输出格式')
@click.option('--engines', '-e', multiple=True,
              type=click.Choice(['panoramic', 'algorithm', 'mapping', 'version']),
              default=['panoramic'], help='要使用的引擎')
@click.pass_context
def analyze(ctx, document_path: str, output: Optional[str], 
           output_format: str, engines: tuple):
    """分析设计文档
    
    对指定的设计文档执行全景分析、算法分析或多维映射。
    
    示例:
        v4cli analyze design.md --engines panoramic algorithm
        v4cli analyze code.py --format yaml --output result.yml
    """
    return asyncio.run(_run_analyze(ctx, document_path, output, output_format, engines))


async def _run_analyze(ctx, document_path: str, output: Optional[str], 
                      output_format: str, engines: tuple):
    """异步执行分析命令"""
    formatter = ctx.obj['formatter']
    progress = ctx.obj['progress']
    
    try:
        # 初始化系统
        progress.start("初始化V4系统...")
        system = V4ScaffoldingSystem(
            project_root=Path.cwd(),
            config_path=ctx.obj.get('config_path')
        )
        progress.update("系统初始化完成")
        
        doc_path = Path(document_path)
        results = {}
        
        # 执行选定的引擎分析
        total_engines = len(engines)
        for i, engine_name in enumerate(engines):
            progress.update(f"执行{engine_name}分析... ({i+1}/{total_engines})")
            
            if engine_name == 'panoramic':
                result = await system.engines.panoramic_puzzle.analyze_document_panoramic_position(doc_path)
                results['panoramic_analysis'] = result
                
            elif engine_name == 'algorithm':
                result = await system.engines.algorithm_driven_ai.analyze_algorithm_patterns(doc_path)
                results['algorithm_analysis'] = result
                
            elif engine_name == 'mapping':
                from ..engines.multidimensional_abstraction import AbstractionDimension
                result = await system.engines.multidimensional_abstraction.create_multidimensional_mapping(
                    doc_path, [AbstractionDimension.CONCEPT, AbstractionDimension.IMPLEMENTATION]
                )
                results['mapping_analysis'] = result
                
            elif engine_name == 'version':
                from ..engines.version_consistency import ConsistencyCheckContext
                context = ConsistencyCheckContext(
                    project_root=doc_path.parent,
                    include_patterns=["*.py", "*.md"],
                    exclude_patterns=["__pycache__"]
                )
                result = await system.engines.version_consistency.perform_consistency_check(context)
                results['version_analysis'] = result
        
        progress.finish("分析完成")
        
        # 格式化输出
        formatted_output = formatter.format_analysis_results(
            results, output_format
        )
        
        # 输出结果
        if output:
            Path(output).write_text(formatted_output, encoding='utf-8')
            formatter.success(f"结果已保存到: {output}")
        else:
            formatter.output(formatted_output)
            
    except Exception as e:
        progress.error(f"分析失败: {str(e)}")
        formatter.error(f"分析失败: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    cli()
```

### 输出格式化器 - src/v4_scaffolding/cli/utils/output_formatter.py
```python
"""CLI输出格式化器"""

import json
import yaml
from typing import Any, Dict
from datetime import datetime
import click


class OutputFormatter:
    """输出格式化器"""
    
    def __init__(self, verbose: bool = False, quiet: bool = False):
        self.verbose = verbose
        self.quiet = quiet
    
    def format_analysis_results(self, results: Dict[str, Any], format_type: str) -> str:
        """格式化分析结果"""
        
        # 添加元数据
        formatted_results = {
            'timestamp': datetime.now().isoformat(),
            'format_version': '1.0',
            'results': results
        }
        
        if format_type == 'json':
            return json.dumps(formatted_results, indent=2, ensure_ascii=False, default=str)
        elif format_type == 'yaml':
            return yaml.dump(formatted_results, default_flow_style=False, allow_unicode=True)
        elif format_type == 'markdown':
            return self._format_results_as_markdown(formatted_results)
        else:
            raise ValueError(f"不支持的输出格式: {format_type}")
    
    def _format_results_as_markdown(self, data: Dict[str, Any]) -> str:
        """将结果格式化为Markdown"""
        md_content = f"""# V4分析结果报告

**生成时间**: {data['timestamp']}
**格式版本**: {data['format_version']}

## 分析结果概览

"""
        
        for engine, result in data['results'].items():
            md_content += f"### {engine.title()}分析\n\n"
            
            if hasattr(result, 'confidence_score'):
                md_content += f"- **置信度**: {result.confidence_score:.2%}\n"
            if hasattr(result, 'processing_time'):
                md_content += f"- **处理时间**: {result.processing_time:.2f}秒\n"
            
            md_content += f"- **状态**: 完成\n\n"
        
        return md_content
    
    def success(self, message: str):
        """显示成功消息"""
        if not self.quiet:
            click.echo(click.style(f"✅ {message}", fg='green'))
    
    def warning(self, message: str):
        """显示警告消息"""
        if not self.quiet:
            click.echo(click.style(f"⚠️ {message}", fg='yellow'))
    
    def error(self, message: str):
        """显示错误消息"""
        click.echo(click.style(f"❌ {message}", fg='red'), err=True)
    
    def info(self, message: str):
        """显示信息消息"""
        if not self.quiet:
            click.echo(click.style(f"ℹ️ {message}", fg='blue'))
    
    def output(self, content: str):
        """输出内容"""
        click.echo(content)
```

### 进度跟踪器 - src/v4_scaffolding/cli/utils/progress_tracker.py
```python
"""进度跟踪器"""

import time
from typing import Optional
import click


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, enabled: bool = True):
        self.enabled = enabled
        self.start_time: Optional[float] = None
        self.current_step: Optional[str] = None
    
    def start(self, message: str):
        """开始进度跟踪"""
        if not self.enabled:
            return
        
        self.start_time = time.time()
        self.current_step = message
        click.echo(f"🚀 {message}")
    
    def update(self, message: str):
        """更新进度"""
        if not self.enabled:
            return
        
        elapsed = time.time() - (self.start_time or time.time())
        click.echo(f"   {message} ({elapsed:.1f}s)")
    
    def finish(self, message: str):
        """完成进度跟踪"""
        if not self.enabled:
            return
        
        elapsed = time.time() - (self.start_time or time.time())
        click.echo(click.style(f"✅ {message} (总计: {elapsed:.1f}s)", fg='green'))
    
    def error(self, message: str):
        """错误进度"""
        if not self.enabled:
            return
        
        elapsed = time.time() - (self.start_time or time.time())
        click.echo(click.style(f"❌ {message} ({elapsed:.1f}s)", fg='red'))
```

## 🧪 端到端集成测试

### tests/e2e/test_cli_integration.py
```python
"""CLI端到端集成测试"""

import pytest
import tempfile
import shutil
from pathlib import Path
from click.testing import CliRunner
import json

from src.v4_scaffolding.cli.main import cli


class TestCLIIntegration:
    """CLI集成测试"""
    
    @pytest.fixture
    def cli_runner(self):
        """CLI测试运行器"""
        return CliRunner()
    
    @pytest.fixture
    def test_project(self):
        """测试项目结构"""
        temp_dir = Path(tempfile.mkdtemp())
        
        try:
            # 创建测试项目
            (temp_dir / "design.md").write_text("""
            # 测试设计文档
            版本: v1.0.0
            
            ## 架构
            用户管理系统设计
            """)
            
            (temp_dir / "src" / "main.py").write_text("""
            __version__ = "1.0.0"
            
            class UserManager:
                def create_user(self):
                    pass
            """)
            
            yield temp_dir
            
        finally:
            shutil.rmtree(temp_dir)
    
    def test_cli_analyze_command(self, cli_runner, test_project):
        """测试analyze命令"""
        design_doc = test_project / "design.md"
        
        result = cli_runner.invoke(cli, [
            'analyze', 
            str(design_doc),
            '--engines', 'panoramic',
            '--format', 'json'
        ])
        
        assert result.exit_code == 0
        assert len(result.output) > 0
    
    def test_cli_error_handling(self, cli_runner):
        """测试CLI错误处理"""
        # 测试不存在的文件
        result = cli_runner.invoke(cli, [
            'analyze',
            'nonexistent.md'
        ])
        
        assert result.exit_code != 0
    
    def test_cli_help_commands(self, cli_runner):
        """测试帮助命令"""
        # 测试主帮助
        result = cli_runner.invoke(cli, ['--help'])
        assert result.exit_code == 0
        assert 'V4 Scaffolding' in result.output
```

## 📋 验收标准

### 功能验收
- [ ] CLI命令完整实现 (100%)
- [ ] 多格式输出支持 (100%)
- [ ] 错误处理和用户反馈 (100%)
- [ ] 进度跟踪和状态显示 (100%)
- [ ] 端到端工作流集成 (100%)

### 质量验收
- [ ] CLI测试覆盖率 ≥ 90%
- [ ] 用户体验测试通过率 ≥ 95%
- [ ] 错误处理覆盖率 ≥ 90%
- [ ] 输出格式正确性 ≥ 99%

### 性能验收
- [ ] CLI启动时间 ≤ 2秒
- [ ] 单命令执行时间 ≤ 30秒
- [ ] 大项目分析时间 ≤ 120秒
- [ ] 内存占用 ≤ 1GB

## 🚀 部署和使用

### 安装方式
```bash
# 开发安装
pip install -e .

# 生产安装
pip install v4-scaffolding

# 验证安装
v4cli --version
```

### 基本使用示例
```bash
# 分析设计文档
v4cli analyze design.md --engines panoramic algorithm

# 创建多维映射
v4cli map-dimensions code.py concept implementation --format yaml

# 检查版本一致性
v4cli check-consistency ./project --format markdown

# 生成综合报告
v4cli generate-report *.json --template detailed --format html
```

## 📊 总结
V4 CLI接口和集成测试实现了完整的命令行工具，提供用户友好的接口访问所有V4引擎功能，确保系统整体质量达到95%置信度标准。