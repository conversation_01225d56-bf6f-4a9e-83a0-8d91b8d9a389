# 渐进开发与验收标准修改提示词

**目标文件**: `08-渐进开发与验收标准.md`  
**修改原则**: 将"智能验收标准"重新设计为"规则化验收体系 + 外部AI服务评估"  
**核心理念**: 明确验收标准的规则化制定和AI服务的辅助评估作用

---

## 🎯 渐进开发策略重新定位

### 开发验收体系重新定义
```pseudocode
// 修改前：混淆的"智能验收标准"
❌ 基于AI的智能验收标准制定和自动化质量评估
❌ 智能渐进开发策略和自适应验收

// 修改后：明确的"规则化验收体系"
✅ 基于规则的验收标准制定和质量评估
✅ 规则化渐进开发策略 + 外部AI服务评估

DEFINE ProgressiveDevelopmentPhilosophy:
    // 核心职责划分
    规则引擎职责:
        - 验收标准规则制定
        - 质量指标规则计算
        - 渐进开发规则执行
        
    外部AI服务职责:
        - 复杂质量场景分析
        - 验收标准优化建议
        - 开发进度风险评估
        
    人工决策职责:
        - 验收标准策略制定
        - 质量标准最终确认
        - 复杂验收问题决策
END DEFINE
```

## 🔧 渐进开发管理器重新设计

### 规则化开发阶段管理
```pseudocode
COMPONENT RuleBasedProgressiveDevelopmentManager:
    DEPENDENCIES:
        developmentRuleRepository: DevelopmentRuleRepository
        phaseRuleValidator: PhaseRuleValidator
        qualityRuleAssessor: QualityRuleAssessor
        progressRuleTracker: ProgressRuleTracker
        externalAIClient: ExternalAIServiceClient
    
    FUNCTION manageDevelopmentProgress(developmentPlan, currentPhase):
        // 1. 规则化阶段验证
        phaseValidation = validateCurrentPhase(currentPhase, developmentPlan)
        
        // 2. 规则化质量评估
        qualityAssessment = assessPhaseQuality(currentPhase, phaseValidation)
        
        // 3. 规则化进度跟踪
        progressTracking = trackDevelopmentProgress(currentPhase, qualityAssessment)
        
        // 4. 规则化下一阶段准备
        nextPhasePreparation = prepareNextPhase(
            currentPhase, qualityAssessment, progressTracking)
        
        // 5. 复杂开发场景的AI评估（可选）
        aiDevelopmentAssessment = NULL
        IF progressTracking.complexity > DEVELOPMENT_COMPLEXITY_THRESHOLD:
            aiRequest = buildDevelopmentAssessmentRequest(
                currentPhase, qualityAssessment, progressTracking, nextPhasePreparation)
            aiDevelopmentAssessment = externalAIClient.assessDevelopmentProgress(aiRequest)
        
        RETURN ProgressiveDevelopmentResult(
            phaseValidation: phaseValidation,
            qualityAssessment: qualityAssessment,
            progressTracking: progressTracking,
            nextPhasePreparation: nextPhasePreparation,
            aiDevelopmentAssessment: aiDevelopmentAssessment
        )
    END FUNCTION
    
    FUNCTION validateCurrentPhase(currentPhase, developmentPlan):
        // 基于规则的阶段验证
        phaseRules = developmentRuleRepository.getPhaseRules(currentPhase.phaseType)
        
        validationResults = []
        FOR rule IN phaseRules:
            ruleResult = phaseRuleValidator.validate(rule, currentPhase, developmentPlan)
            validationResults.add(ruleResult)
        END FOR
        
        // 计算阶段完成度
        phaseCompleteness = calculatePhaseCompleteness(validationResults)
        
        RETURN PhaseValidationResult(
            phaseType: currentPhase.phaseType,
            validationResults: validationResults,
            phaseCompleteness: phaseCompleteness,
            canProceedToNext: phaseCompleteness >= PHASE_COMPLETION_THRESHOLD
        )
    END FUNCTION
END COMPONENT
```

### 规则化质量评估器
```pseudocode
COMPONENT QualityRuleAssessor:
    DEPENDENCIES:
        qualityRuleRepository: QualityRuleRepository
        metricsRuleCalculator: MetricsRuleCalculator
        benchmarkRuleComparator: BenchmarkRuleComparator
        externalAIClient: ExternalAIServiceClient
    
    FUNCTION assessPhaseQuality(currentPhase, phaseValidation):
        // 1. 规则化质量指标计算
        qualityMetrics = calculateQualityMetrics(currentPhase, phaseValidation)
        
        // 2. 规则化基准对比
        benchmarkComparison = compareToBenchmarks(qualityMetrics, currentPhase.phaseType)
        
        // 3. 规则化质量评级
        qualityRating = calculateQualityRating(qualityMetrics, benchmarkComparison)
        
        // 4. 复杂质量场景的AI分析（可选）
        aiQualityAnalysis = NULL
        IF qualityRating.complexity > QUALITY_COMPLEXITY_THRESHOLD:
            aiRequest = buildQualityAnalysisRequest(
                qualityMetrics, benchmarkComparison, qualityRating)
            aiQualityAnalysis = externalAIClient.analyzeQualityMetrics(aiRequest)
        
        RETURN QualityAssessmentResult(
            qualityMetrics: qualityMetrics,
            benchmarkComparison: benchmarkComparison,
            qualityRating: qualityRating,
            aiQualityAnalysis: aiQualityAnalysis,
            meetsBenchmark: qualityRating.score >= QUALITY_BENCHMARK_THRESHOLD
        )
    END FUNCTION
    
    FUNCTION calculateQualityMetrics(currentPhase, phaseValidation):
        qualityMetrics = QualityMetrics()
        
        // 基于阶段类型的规则化指标计算
        SWITCH currentPhase.phaseType:
            CASE DESIGN_PHASE:
                qualityMetrics.designCompleteness = calculateDesignCompleteness(currentPhase)
                qualityMetrics.architectureConsistency = calculateArchitectureConsistency(currentPhase)
                qualityMetrics.documentationQuality = calculateDocumentationQuality(currentPhase)
                
            CASE IMPLEMENTATION_PHASE:
                qualityMetrics.codeQuality = calculateCodeQuality(currentPhase)
                qualityMetrics.testCoverage = calculateTestCoverage(currentPhase)
                qualityMetrics.performanceBenchmark = calculatePerformanceBenchmark(currentPhase)
                
            CASE TESTING_PHASE:
                qualityMetrics.testCompleteness = calculateTestCompleteness(currentPhase)
                qualityMetrics.bugDetectionRate = calculateBugDetectionRate(currentPhase)
                qualityMetrics.regressionTestSuccess = calculateRegressionTestSuccess(currentPhase)
                
            CASE DEPLOYMENT_PHASE:
                qualityMetrics.deploymentSuccess = calculateDeploymentSuccess(currentPhase)
                qualityMetrics.systemStability = calculateSystemStability(currentPhase)
                qualityMetrics.performanceValidation = calculatePerformanceValidation(currentPhase)
        END SWITCH
        
        RETURN qualityMetrics
    END FUNCTION
END COMPONENT
```

## 🔧 验收标准管理器重新设计

### 规则化验收标准制定
```pseudocode
COMPONENT RuleBasedAcceptanceCriteriaManager:
    DEPENDENCIES:
        criteriaRuleRepository: AcceptanceCriteriaRuleRepository
        standardRuleGenerator: StandardRuleGenerator
        criteriaRuleValidator: CriteriaRuleValidator
        externalAIClient: ExternalAIServiceClient
        humanApprovalService: HumanApprovalService
    
    FUNCTION defineAcceptanceCriteria(projectType, phaseType, qualityRequirements):
        // 1. 规则化基础标准生成
        baseCriteria = generateBaseCriteria(projectType, phaseType)
        
        // 2. 规则化质量要求适配
        adaptedCriteria = adaptToQualityRequirements(baseCriteria, qualityRequirements)
        
        // 3. 规则化标准验证
        criteriaValidation = validateCriteria(adaptedCriteria, projectType, phaseType)
        
        IF NOT criteriaValidation.isValid():
            THROW CriteriaValidationException(criteriaValidation.errors)
        
        // 4. 复杂标准的AI优化（可选）
        aiCriteriaOptimization = NULL
        IF adaptedCriteria.complexity > CRITERIA_COMPLEXITY_THRESHOLD:
            aiRequest = buildCriteriaOptimizationRequest(
                adaptedCriteria, projectType, phaseType, qualityRequirements)
            aiCriteriaOptimization = externalAIClient.optimizeAcceptanceCriteria(aiRequest)
        
        // 5. 人工审核确认（关键标准）
        humanApproval = NULL
        IF adaptedCriteria.criticality >= CRITICAL_CRITERIA_THRESHOLD:
            humanApproval = humanApprovalService.requestCriteriaApproval(
                adaptedCriteria, aiCriteriaOptimization)
        
        RETURN AcceptanceCriteriaResult(
            baseCriteria: baseCriteria,
            adaptedCriteria: adaptedCriteria,
            criteriaValidation: criteriaValidation,
            aiCriteriaOptimization: aiCriteriaOptimization,
            humanApproval: humanApproval
        )
    END FUNCTION
    
    FUNCTION generateBaseCriteria(projectType, phaseType):
        // 基于规则的基础验收标准生成
        criteriaTemplate = criteriaRuleRepository.getTemplate(projectType, phaseType)
        
        baseCriteria = AcceptanceCriteria()
        
        // 应用项目类型规则
        projectRules = criteriaRuleRepository.getProjectTypeRules(projectType)
        FOR rule IN projectRules:
            criterion = standardRuleGenerator.generateCriterion(rule, criteriaTemplate)
            baseCriteria.addCriterion(criterion)
        END FOR
        
        // 应用阶段类型规则
        phaseRules = criteriaRuleRepository.getPhaseTypeRules(phaseType)
        FOR rule IN phaseRules:
            criterion = standardRuleGenerator.generateCriterion(rule, criteriaTemplate)
            baseCriteria.addCriterion(criterion)
        END FOR
        
        RETURN baseCriteria
    END FUNCTION
END COMPONENT
```

## 🔧 质量门控管理器重新设计

### 规则化质量门控检查
```pseudocode
COMPONENT RuleBasedQualityGateManager:
    DEPENDENCIES:
        gateRuleRepository: QualityGateRuleRepository
        gateRuleEvaluator: QualityGateRuleEvaluator
        blockingRuleAnalyzer: BlockingRuleAnalyzer
        externalAIClient: ExternalAIServiceClient
        humanEscalationService: HumanEscalationService
    
    FUNCTION evaluateQualityGate(phaseResult, qualityGateConfig):
        // 1. 规则化质量门控检查
        gateEvaluationResults = performGateEvaluation(phaseResult, qualityGateConfig)
        
        // 2. 规则化阻塞问题分析
        blockingIssues = analyzeBlockingIssues(gateEvaluationResults)
        
        // 3. 规则化通过决策
        gateDecision = makeGateDecision(gateEvaluationResults, blockingIssues)
        
        // 4. 复杂质量问题的AI分析（可选）
        aiQualityGateAnalysis = NULL
        IF blockingIssues.complexity > QUALITY_GATE_COMPLEXITY_THRESHOLD:
            aiRequest = buildQualityGateAnalysisRequest(
                phaseResult, gateEvaluationResults, blockingIssues)
            aiQualityGateAnalysis = externalAIClient.analyzeQualityGate(aiRequest)
        
        // 5. 阻塞问题的人工决策（必要时）
        humanDecision = NULL
        IF gateDecision.isBlocked() AND blockingIssues.requiresHumanDecision():
            humanDecision = humanEscalationService.escalateQualityGateDecision(
                gateDecision, blockingIssues, aiQualityGateAnalysis)
        
        RETURN QualityGateResult(
            gateEvaluationResults: gateEvaluationResults,
            blockingIssues: blockingIssues,
            gateDecision: gateDecision,
            aiQualityGateAnalysis: aiQualityGateAnalysis,
            humanDecision: humanDecision,
            canProceed: determineCanProceed(gateDecision, humanDecision)
        )
    END FUNCTION
    
    FUNCTION performGateEvaluation(phaseResult, qualityGateConfig):
        // 基于规则的质量门控评估
        gateRules = gateRuleRepository.getGateRules(qualityGateConfig.gateType)
        
        evaluationResults = []
        FOR rule IN gateRules:
            ruleEvaluation = gateRuleEvaluator.evaluate(rule, phaseResult)
            evaluationResults.add(ruleEvaluation)
        END FOR
        
        RETURN GateEvaluationResults(
            individualEvaluations: evaluationResults,
            overallScore: calculateOverallGateScore(evaluationResults),
            passedRules: filterPassedRules(evaluationResults),
            failedRules: filterFailedRules(evaluationResults)
        )
    END FUNCTION
    
    FUNCTION analyzeBlockingIssues(gateEvaluationResults):
        // 规则化阻塞问题分析
        blockingIssues = []
        
        FOR failedRule IN gateEvaluationResults.failedRules:
            IF failedRule.severity >= BLOCKING_SEVERITY_THRESHOLD:
                blockingIssue = BlockingIssue(
                    rule: failedRule,
                    severity: failedRule.severity,
                    impact: calculateImpact(failedRule),
                    resolutionComplexity: calculateResolutionComplexity(failedRule)
                )
                blockingIssues.add(blockingIssue)
        
        RETURN BlockingIssuesAnalysis(
            blockingIssues: blockingIssues,
            totalBlockingCount: blockingIssues.size(),
            highSeverityCount: countHighSeverityIssues(blockingIssues),
            complexity: calculateOverallComplexity(blockingIssues),
            requiresHumanDecision: determineHumanDecisionRequirement(blockingIssues)
        )
    END FUNCTION
END COMPONENT
```

## 🔧 验收报告生成器重新设计

### 规则化验收报告生成
```pseudocode
COMPONENT RuleBasedAcceptanceReportGenerator:
    DEPENDENCIES:
        reportRuleRepository: ReportRuleRepository
        templateRuleEngine: TemplateRuleEngine
        dataRuleAggregator: DataRuleAggregator
        externalAIClient: ExternalAIServiceClient
    
    FUNCTION generateAcceptanceReport(developmentResult, acceptanceCriteria, qualityGateResults):
        // 1. 规则化数据聚合
        aggregatedData = aggregateReportData(
            developmentResult, acceptanceCriteria, qualityGateResults)
        
        // 2. 规则化报告模板选择
        reportTemplate = selectReportTemplate(
            developmentResult.projectType, acceptanceCriteria.reportType)
        
        // 3. 规则化报告内容生成
        reportContent = generateReportContent(aggregatedData, reportTemplate)
        
        // 4. 规则化报告验证
        reportValidation = validateReport(reportContent, acceptanceCriteria)
        
        // 5. 复杂报告的AI增强（可选）
        aiReportEnhancement = NULL
        IF reportContent.complexity > REPORT_COMPLEXITY_THRESHOLD:
            aiRequest = buildReportEnhancementRequest(
                reportContent, aggregatedData, acceptanceCriteria)
            aiReportEnhancement = externalAIClient.enhanceAcceptanceReport(aiRequest)
        
        RETURN AcceptanceReportResult(
            aggregatedData: aggregatedData,
            reportTemplate: reportTemplate,
            reportContent: reportContent,
            reportValidation: reportValidation,
            aiReportEnhancement: aiReportEnhancement,
            finalReport: mergeFinalReport(reportContent, aiReportEnhancement)
        )
    END FUNCTION
    
    FUNCTION aggregateReportData(developmentResult, acceptanceCriteria, qualityGateResults):
        // 基于规则的报告数据聚合
        aggregatedData = ReportData()
        
        // 开发结果数据聚合规则
        developmentDataRules = reportRuleRepository.getDevelopmentDataRules()
        FOR rule IN developmentDataRules:
            dataPoint = dataRuleAggregator.aggregate(rule, developmentResult)
            aggregatedData.addDevelopmentData(dataPoint)
        END FOR
        
        // 验收标准数据聚合规则
        criteriaDataRules = reportRuleRepository.getCriteriaDataRules()
        FOR rule IN criteriaDataRules:
            dataPoint = dataRuleAggregator.aggregate(rule, acceptanceCriteria)
            aggregatedData.addCriteriaData(dataPoint)
        END FOR
        
        // 质量门控数据聚合规则
        qualityGateDataRules = reportRuleRepository.getQualityGateDataRules()
        FOR rule IN qualityGateDataRules:
            dataPoint = dataRuleAggregator.aggregate(rule, qualityGateResults)
            aggregatedData.addQualityGateData(dataPoint)
        END FOR
        
        RETURN aggregatedData
    END FUNCTION
END COMPONENT
```

## 📋 修改检查清单

### 必须删除的混淆概念
- [ ] 删除所有"智能验收标准"表述
- [ ] 删除所有"AI质量评估"声明
- [ ] 删除所有"智能渐进开发"描述
- [ ] 删除所有验收管理的AI能力声明

### 必须添加的明确组件
- [ ] RuleBasedProgressiveDevelopmentManager规则化渐进开发管理器
- [ ] QualityRuleAssessor质量规则评估器
- [ ] RuleBasedAcceptanceCriteriaManager规则化验收标准管理器
- [ ] ExternalAIServiceClient外部AI服务客户端
- [ ] RuleBasedQualityGateManager规则化质量门控管理器

### 必须明确的职责边界
- [ ] 规则引擎：标准验收标准制定和质量评估
- [ ] 外部AI服务：复杂质量场景分析和优化建议
- [ ] 人工决策：验收标准策略制定和复杂问题决策
- [ ] 质量门控：规则化质量检查和阻塞问题分析

这个修改提示词确保了渐进开发与验收标准的正确设计，明确区分了规则处理与AI服务的职责边界。
