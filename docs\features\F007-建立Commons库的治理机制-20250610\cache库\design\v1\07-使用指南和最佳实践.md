# F007 Cache库-使用指南和最佳实践

## 文档元数据

- **文档ID**: `F007-CACHE-USAGE-GUIDE-007`
- **版本**: `V1.0`
- **技术栈**: Java 21, Spring Boot 3.4.5, Maven 3.9
- 复杂度等级: L1

## 核心定位

使用指南和最佳实践是缓存库的开发者文档中心，提供从入门到精通的完整使用指导，帮助开发者快速上手并遵循最佳实践，避免常见的缓存使用陷阱。

## 设计哲学

本指南遵循以下原则：
1. **循序渐进**: 从简单入门到高级用法的渐进式学习路径
2. **实用导向**: 提供可直接复制使用的代码示例
3. **最佳实践**: 分享生产环境验证的最佳实践
4. **问题预防**: 帮助开发者避免常见的缓存设计陷阱

## 架构概览

### 分层架构设计

缓存库采用经典的四层架构模式，确保清晰的职责分离和高度的可扩展性：

```
┌─────────────────────────────────┐
│    应用层 (Application Layer)    │  ← 使用指南关注层
├─────────────────────────────────┤
│    接口层 (Interface Layer)     │  ← 注解 + Template API
├─────────────────────────────────┤
│    服务层 (Service Layer)       │  ← 缓存逻辑处理
├─────────────────────────────────┤
│   存储层 (Storage Layer)        │  ← L1本地 + L2远程
└─────────────────────────────────┘
```

**层级职责定义**：
- **应用层**: 业务服务集成缓存注解，使用指南主要服务此层
- **接口层**: 提供@XkCacheable注解和ValkeyTemplate，统一缓存操作接口
- **服务层**: 实现双层缓存逻辑、监控指标收集、AOP切面处理
- **存储层**: 管理Caffeine本地缓存和Valkey远程缓存

### 核心模块依赖关系

```
Usage Guide (本文档)
    ↓ 使用指导
Annotation API ←→ Template API
    ↓ 统一接口
Cache Service Layer
    ↓ 存储管理
L1 Local (Caffeine) + L2 Remote (Valkey)
```

**依赖约束**：
- 使用指南不依赖具体实现，仅依赖公共API
- 注解API和Template API可独立使用，互不依赖
- 双层存储采用透明代理模式，应用无感知

### 接口契约定义

**核心接口契约**：
```java
// 注解接口契约
@XkCacheable(cacheName, key, ttl, condition, unless)
→ 返回: 缓存值或方法执行结果
→ 约束: key必须唯一，ttl>0，方法必须public

// Template接口契约  
valkeyTemplate.get(key) 
→ 返回: Optional<T>，永不抛出异常
→ 约束: key非空，序列化兼容

valkeyTemplate.set(key, value, ttl)
→ 返回: boolean操作结果
→ 约束: value可序列化，ttl合法
```

### 演进策略与兼容性保证

**架构演进原则**：
1. **向后兼容**: 新版本必须兼容现有注解和API使用方式
2. **渐进式升级**: 支持新旧版本并行运行，平滑迁移
3. **接口稳定**: 核心注解接口保持稳定，内部实现可优化
4. **配置兼容**: 配置文件格式向后兼容，新增配置项有默认值

**迁移路径规划**：
- **V1.0→V1.1**: 新增功能通过新注解属性实现，保持现有行为
- **V1.x→V2.0**: 提供迁移工具和兼容层，分阶段废弃旧API
- **版本策略**: 采用语义化版本控制，主版本号变更表示破坏性变更

**风险控制机制**：
- **功能开关**: 新功能通过配置开关控制，默认关闭
- **监控保护**: 新版本部署时增强监控，快速回滚机制
- **测试覆盖**: 兼容性测试覆盖主要使用场景和边界情况

## 包含范围

**核心内容模块**：
- 快速入门和基础配置
- 注解使用和高级用法
- 性能优化和监控指导
- 生产环境最佳实践

## 排除范围

**不包含内容**：
- 详细的架构设计说明
- 源码级别的实现细节
- 特定业务场景的定制化方案
- 第三方工具的详细配置

## 1. 快速入门

### 1.1 添加依赖

**Maven:**
```xml
<dependency>
    <groupId>org.xkong.cloud</groupId>
    <artifactId>xkongcloud-commons-cache-starter</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 1.2 添加配置

在`application.yml`中添加最简配置：
```yaml
xkong:
  cache:
    l2:
      valkey:
        host: your-valkey-host
```

## 2. 基础用法 (80%场景推荐)

对于绝大多数缓存"方法结果"的场景，请始终优先使用注解。

```java
@Service
public class ProductService {

    // 缓存key为'product::123', TTL为1小时
    @XkCacheable(cacheName = "product", key = "#id", ttl = "1h")
    public Product getProductById(Long id) {
        // ... 数据库调用(响应时间100-500ms)
        return database.findProductById(id);
    }

    // 当产品更新时，从缓存中驱逐
    @XkCacheEvict(cacheName = "product", key = "#product.id")
    public void updateProduct(Product product) {
        database.update(product);
    }
}
```

**关键点**: 
- `key`属性支持强大的**SpEL表达式**，可以引用方法参数。
- `ttl`支持`10s`, `5m`, `1h`等易读的格式。

## 3. 高级用法 (20%场景)

当遇到注解无法满足的复杂逻辑，或需要直接操作Valkey高级数据结构时，可以注入`Template`。

### 3.1 注入Template

```java
@Service
public class AnalyticsService {
    private final ValkeyCacheTemplate valkeyTemplate;

    @Autowired
    public AnalyticsService(ValkeyCacheTemplate valkeyTemplate) {
        this.valkeyTemplate = valkeyTemplate;
    }
}
```

### 3.2 使用高级数据结构

```java
// 统计日活用户 (DAU)
public void recordUserLogin(String userId) {
    String key = "dau:" + LocalDate.now();
    valkeyTemplate.hyperLogLog().add(key, userId);
}

public long getDauCount() {
    String key = "dau:" + LocalDate.now();
    return valkeyTemplate.hyperLogLog().size(key);
}
```

### 3.3 使用管道提升性能

```java
// 一次网络往返执行多条命令(延迟<10ms vs 单独执行30-50ms)
public void initUserData(String userId) {
    valkeyTemplate.executePipelined(commands -> {
        commands.hset("user:" + userId, "name", "test");
        commands.hset("user:" + userId, "email", "<EMAIL>");
        commands.expire("user:" + userId, Duration.ofHours(24));
    });
}
```

## 4. 最佳实践

1.  **优先使用注解**: 保持业务代码的干净和声明式。
2.  **精心设计Key**: 缓存Key应具备良好的结构和可读性，如`service:object:id`。
3.  **避免大Value**: 单个缓存对象不应超过1MB，推荐保持在10KB以内，减少网络开销和序列化成本。
4.  **处理缓存穿透**: 对于返回null的查询，缓存特殊的`EmptyValue`对象(TTL设置为5-10分钟)，防止缓存穿透。
5.  **监控与告警**: 将缓存的关键指标(命中率≥85%、P99延迟<50ms)接入监控系统，设置告警阈值。

## 实施约束

### 强制性技术要求
- **Java版本**: 必须使用Java 21+，确保现代语法和虚拟线程特性可用
- **Spring Boot版本**: 必须使用Spring Boot 3.4.5+，确保注解和自动配置的兼容性
- **构建工具**: 推荐Maven 3.9+或Gradle 8.0+
- **IDE支持**: 推荐IntelliJ IDEA 2024.1+或Eclipse 2024-03+

### 使用约束
- **注解位置**: 缓存注解只能用于public方法，不支持private或protected方法
- **SpEL表达式**: key属性必须使用有效的SpEL表达式，避免复杂的方法调用
- **TTL格式**: 必须使用标准Duration格式（如"10s", "5m", "1h"）
- **序列化要求**: 缓存对象必须实现Serializable接口或提供自定义序列化

### 性能指标要求
- **缓存命中率**: 业务缓存命中率应≥85%
- **响应时间**: 缓存操作不应显著增加方法执行时间（≤5%）
- **内存使用**: 本地缓存内存占用≤JVM堆内存的5%
- **网络开销**: 合理使用批量操作，减少网络往返

### 兼容性要求
- **应用集成**: 与现有Spring Boot 3.x应用无缝集成，支持Spring Boot 3.4.5+版本
- **监控集成**: 与Micrometer 1.10+、Prometheus 2.40+等监控系统兼容
- **日志集成**: 与Logback 1.4+、Log4j2 2.19+等日志框架兼容
- **JVM兼容**: 支持Oracle JDK 21+、OpenJDK 21+、GraalVM 21+
- **构建工具**: 兼容Maven 3.8+、Gradle 7.6+构建环境

### 约束违规后果
- **注解使用错误**: 缓存功能不生效，性能得不到改善
- **配置错误**: 应用启动失败或缓存服务不可用
- **性能问题**: 缓存反而成为性能瓶颈

### 验证锚点
- **功能验证**: `mvn test -Dtest=CacheFunctionalTest`
- **性能验证**: `mvn test -Dtest=CachePerformanceTest`
- **集成验证**: `mvn test -Dtest=CacheIntegrationTest`
