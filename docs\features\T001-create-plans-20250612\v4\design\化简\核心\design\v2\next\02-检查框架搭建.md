# 技术规格文档: DesignValidationWorkflow

## 1. 引言

本文档旨在详细定义 `DesignValidationWorkflow` 的技术规格、核心职责、代码结构、接口、数据模型及执行模式。该工作流是项目管理系统 V2 (`pm_v2`) 中的一个关键组件，负责在 AI 正式介入项目规划前，对设计文档进行自动化健康检查。

## 2. 核心目标与职责

`DesignValidationWorkflow` 的核心任务是**对 `01号` 设计文档进行自动化健康检查**。

主要职责包括：
- **一致性验证**: 检查文档是否遵循既定的结构和规范。
- **完整性检查**: 确保所有必需章节和内容都已包含。
- **风险识别**: 识别潜在的模糊性、冲突或技术实现风险。
- **生成健康报告**: 输出一份结构化的健康报告，为后续的 AI 规划或人工审查提供依据。

此工作流在 AI 对设计进行任何修改或执行之前运行，充当质量保障的“守门员”。

## 3. 最终命名约定

为保证代码库的一致性和可读性，项目采用以下核心命名：

- **工作流 (Workflow)**: `DesignValidationWorkflow`
- **检查器基类 (Checker Base Class)**: `BaseChecker`
- **风险数据模型 (Risk Data Model)**: `HealthRisk`
- **报告数据模型 (Report Data Model)**: `DocumentHealthReport`

## 4. 程序代码结构

代码将组织在 `project_manager_v2` 包内，遵循模块化的设计原则。

```bash
project_manager_v2/
├── workflows/
│   └── design_validation_workflow.py   # 工作流核心实现
├── utils/
│   └── checkers/
│       ├── base_checker.py             # 检查器抽象基类
│       ├── specification_conformity_checker.py # 具体检查器实现
│       ├── completeness_checker.py     # ...
│       └── ...
└── models/
    └── health_report.py                # 包含HealthRisk和DocumentHealthReport数据类
```

- **`workflows/design_validation_workflow.py`**: 包含 `DesignValidationWorkflow` 类的定义，负责编排和执行所有检查器。
- **`utils/checkers/`**: 存放所有检查器模块。每个检查器都是一个独立的类，负责一项具体的检查任务。
- **`models/health_report.py`**: 定义系统中使用的数据结构，如 `HealthRisk` 和 `DocumentHealthReport`。

## 5. 边界与接口定义

### 5.1 `BaseChecker` 抽象基类

所有检查器都必须继承自 `BaseChecker`，并实现其定义的接口。

```python
# project_manager_v2/utils/checkers/base_checker.py

from abc import ABC, abstractmethod
from typing import List
from project_manager_v2.models.health_report import HealthRisk

class BaseChecker(ABC):
    """
    检查器抽象基类。
    每个检查器负责对文档的特定方面进行健康检查。
    """
    
    @property
    def name(self) -> str:
        """返回检查器的唯一名称。"""
        return self.__class__.__name__

    @abstractmethod
    def check(self, document_content: str) -> List[HealthRisk]:
        """
        执行检查并返回发现的健康风险列表。

        Args:
            document_content: 要检查的文档的文本内容。

        Returns:
            一个 HealthRisk 对象的列表。如果未发现风险，则返回空列表。
        """
        pass
```

### 5.2 具体检查器实现示例

这是一个具体检查器的骨架代码，展示了如何继承 `BaseChecker`。

```python
# project_manager_v2/utils/checkers/specification_conformity_checker.py

from typing import List
from project_manager_v2.models.health_report import HealthRisk
from project_manager_v2.utils.checkers.base_checker import BaseChecker

class SpecificationConformityChecker(BaseChecker):
    """
    检查文档是否遵循了核心技术规格（如必需的章节、标题格式等）。
    """
    
    def check(self, document_content: str) -> List[HealthRisk]:
        """
        实现具体的检查逻辑。
        """
        risks = []
        
        # 示例逻辑：检查是否包含 "## 核心目标与职责" 章节
        if "## 核心目标与职责" not in document_content:
            risk = HealthRisk(
                level="High",
                description="文档缺少'核心目标与职责'章节，这可能导致后续规划偏离方向。",
                checker_name=self.name
            )
            risks.append(risk)
            
        # ... 其他检查逻辑 ...
        
        return risks
```

## 6. 核心工作流实现 (`DesignValidationWorkflow`)

工作流本身是一个编排器，它以**单线程、顺序**的方式执行一系列检查器。

```python
# project_manager_v2/workflows/design_validation_workflow.py

from typing import List
from project_manager_v2.models.health_report import DocumentHealthReport
from project_manager_v2.utils.checkers.base_checker import BaseChecker
# 导入所有具体的检查器
from project_manager_v2.utils.checkers.specification_conformity_checker import SpecificationConformityChecker
from project_manager_v2.utils.checkers.completeness_checker import CompletenessChecker

class DesignValidationWorkflow:
    """
    编排和执行设计文档健康检查的工作流。
    """
    def __init__(self, document_path: str):
        self.document_path = document_path
        self.logbook = None  # 将由 ProjectManager 注入
        self.checkers: List[BaseChecker] = [
            SpecificationConformityChecker(),
            CompletenessChecker(),
            # ... 在此实例化所有需要的检查器
        ]

    def execute(self) -> bool:
        """
        按顺序执行所有检查器，并返回一个布尔值表示验证是否通过。
        这是一个同步的、阻塞式的方法。
        """
        if self.logbook:
            self.logbook.log(f"开始对文档 {self.document_path} 执行设计验证工作流。")

        # 1. 读取文档内容 (此处为伪代码)
        document_content = self._read_document(self.document_path)

        # 2. 以单线程、顺序方式执行检查
        all_risks = []
        for checker in self.checkers:
            if self.logbook:
                self.logbook.log(f"正在运行检查器: {checker.name}...")
            risks = checker.check(document_content)
            all_risks.extend(risks)
            if self.logbook:
                self.logbook.log(f"检查器 {checker.name} 完成，发现 {len(risks)} 个风险。")

        # 3. 生成内部报告对象
        report = DocumentHealthReport(
            document_path=self.document_path,
            risks=all_risks,
            summary=f"共发现 {len(all_risks)} 个潜在健康风险。"
        )
        
        # (在此处将报告写入文件)
        
        if self.logbook:
            self.logbook.log("设计验证工作流执行完毕。")
            
        return report.passed

    def _read_document(self, path: str) -> str:
        # 在实际实现中，这里将包含文件读取逻辑
        with open(path, 'r', encoding='utf-8') as f:
            return f.read()

```

## 7. 数据模型定义

系统使用强类型的数据类来表示健康风险和最终报告。

```python
# project_manager_v2/models/health_report.py

from dataclasses import dataclass, field
from typing import List, Literal

RiskLevel = Literal["Low", "Medium", "High", "Critical"]

@dataclass
class HealthRisk:
    """
    表示单个健康风险的数据模型。
    """
    level: RiskLevel
    description: str
    checker_name: str
    context: str = "" # 可选，提供风险相关的上下文摘录

@dataclass
class DocumentHealthReport:
    """
    表示整个文档健康检查的最终报告。
    """
    document_path: str
    risks: List[HealthRisk]
    summary: str
    passed: bool = field(init=False)

    def __post_init__(self):
        self.passed = not bool(self.risks)
```

## 8. 执行与交互模式

### 外部异步，内部同步

系统的核心交互模式是“外部异步，内部同步”，以确保 API 的快速响应和后台任务的稳定执行。

```mermaid
sequenceDiagram
    participant User
    participant API (pm_v2_blueprint)
    participant ProjectManager
    participant BackgroundThread
    participant DesignValidationWorkflow

    User->>+API: POST /api/pm_v2/validation-workflow/{manager_id}
    API->>API: 验证请求
    API->>ProjectManager: 调用 start_validation_task()
    Note right of API: 立即返回 202 Accepted
    API-->>-User: {"status": "validation_started"}
    
    ProjectManager->>BackgroundThread: 创建并启动后台线程
    ProjectManager-->>ProjectManager: (主线程继续运行)
    
    BackgroundThread->>ProjectManager: 调用 _run_full_workflow()
    ProjectManager->>DesignValidationWorkflow: 1. 创建实例
    ProjectManager->>DesignValidationWorkflow: 2. 注入 logbook 依赖
    ProjectManager->>DesignValidationWorkflow: 3. 调用 execute() [同步/阻塞]
    DesignValidationWorkflow-->>ProjectManager: return result (True/False)
    ProjectManager->>ProjectManager: 根据布尔结果决策，更新状态
    Note right of ProjectManager: 任务完成
```

- **外部异步**: `pm_v2_blueprint` (Flask Blueprint) 接收到 API 请求后，会立即委托 `ProjectManager` 实例启动一个**后台线程**来执行耗时的工作流，并迅速向客户端返回 `202 Accepted` 响应。这避免了长时间的 HTTP 连接等待。
- **内部同步**: 在后台线程中，`ProjectManager` 的 `_run_full_workflow` 方法会**同步、阻塞地**调用 `DesignValidationWorkflow` 的 `execute` 方法。`execute` 方法内部也是同步的，它会按顺序执行每一个检查器，直到所有检查完成。这种设计简化了工作流内部的逻辑，避免了在单个任务中管理复杂异步状态的困难。

### 依赖注入

为了解耦和便于测试，工作流所需的外部依赖（如日志记录器 `logbook`）通过依赖注入的方式提供。

1.  `ProjectManager` 持有 `self.logbook` 实例。
2.  在调用工作流之前，`ProjectManager` 会创建 `DesignValidationWorkflow` 的实例。
3.  `ProjectManager` 将自己的 `logbook` 赋给工作流实例的 `logbook` 属性：`workflow.logbook = self.logbook`。
4.  `DesignValidationWorkflow` 内部便可以使用 `self.logbook` 来记录其执行过程。

## 9. API 端点定义

`DesignValidationWorkflow` 的触发，将作为现有三段式API流程中“执行”环节的具体实现。

1.  **`POST /api/pm_v2/get_and_create`**: 在请求体中包含项目目录路径，一步完成`ProjectManager`实例的创建和目标设定，返回`manager_id`。
2.  **`GET /api/pm_v2/status/{manager_id}`**: (轮询) 获取任务的当前状态。
3.  **`POST /api/pm_v2/validation-workflow/{manager_id}`**: **此端点将触发 `DesignValidationWorkflow` 的执行**。它启动一个后台任务，并立即返回，表示任务已接受并开始处理。如果需要指定具体分析的文档（如`01号`），其路径可以在此请求的body中提供。

## 10. 产出物交付 (Deliverables)

工作流执行完毕后，会生成两种核心产出物：

### 10.1 人类可读的健康报告

- **格式**: Markdown 文件。
- **文件名**: `检查修改报告提示词.md`。
- **位置**: 输出到与被检查文档相同的目录下。
- **内容**: 这份报告使用自然语言，清晰地总结所有发现的风险，并可能包含对AI的修改建议。它的主要受众是人类开发者和项目经理，用于快速审查和决策。

### 10.2 内部辅助数据对象

- **格式**: `DocumentHealthReport` 数据类的实例。
- **内容**: 包含所有 `HealthRisk` 对象的完整列表，以及元数据。
- **用途**: 这是一个**内部辅助数据类**，其生命周期仅限于 `DesignValidationWorkflow` 的单次 `execute` 调用中。它的主要作用是帮助工作流在内部聚合所有检查结果，计算最终的 `passed` 状态，并作为构建人类可读的Markdown报告的数据源。**此对象不会作为返回值传递给外部调用者 (`ProjectManager`)**。
