---
title: 任务文件模板（含文档更新）
document_id: T003
document_type: 模板
category: 任务文件
scope: 全局
keywords: [任务文件, 模板, 文档更新, RIPER-5]
created_date: 2025-05-08
updated_date: 2025-05-08
status: 已批准
version: 1.0
authors: [AI助手]
related_docs:
  - docs/guides/document-update-guide.md
  - docs/ai-rules/ai-dev-flow-rules.md
---

# Context
Filename: [Task Filename.md]
Created On: [DateTime]
Created By: [Username/AI]
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
[完整的任务描述]

# Project Overview
[项目详情或AI根据上下文自动推断的简要项目信息]

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)
[代码调查结果、关键文件、依赖关系、约束条件等]

# Proposed Solution (Populated by INNOVATE mode)
[讨论的不同方法、优缺点评估、最终推荐的解决方案方向]

# Implementation Plan (Generated by PLAN mode)
```
Implementation Checklist:
1. [具体操作1]
2. [具体操作2]
...
n. [最终操作]
```

# Document Update Plan (Generated by PLAN mode)
```
Document Update Checklist:
1. [文档路径1]: [更新内容描述]
   - 更新部分: [具体部分，如"变更历史"、"API定义"等]
   - 更新类型: [添加/修改/删除]
   - 更新内容: [具体内容]
   - 更新原因: [为什么需要更新]
2. [文档路径2]: [更新内容描述]
   ...
```

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: "[步骤编号和名称]"

# Task Progress (Appended by EXECUTE mode after each step completion)
*   [DateTime]
    *   Step: [检查表项目编号和描述]
    *   Modifications: [文件和代码更改列表，包括报告的微小偏差修正]
    *   Change Summary: [此更改的简要摘要]
    *   Reason: [执行计划步骤[X]]
    *   Blockers: [遇到的问题，或无]
    *   User Confirmation Status: [成功 / 有小问题但成功 / 失败]

*   [DateTime]
    *   Step: Document Update - [文档路径]
    *   Modifications: [文档更新内容]
    *   Change Summary: [更新摘要]
    *   Reason: [更新原因]
    *   Blockers: [阻塞因素，如果有]
    *   User Confirmation Status: [待确认/已确认/已拒绝]

*   [DateTime]
    *   Step: ...

# Final Review (Populated by REVIEW mode)
[对照最终计划的实施合规性评估，是否发现未报告的偏差]

## 文档更新审查
- 计划的文档更新数量: [数量]
- 成功完成的更新数量: [数量]
- 未完成的更新: [列出未完成的更新，如果有]
- 文档更新符合计划: [是/否]
