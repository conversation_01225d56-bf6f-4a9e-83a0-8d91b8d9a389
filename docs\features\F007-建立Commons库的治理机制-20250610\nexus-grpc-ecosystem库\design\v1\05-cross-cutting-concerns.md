# Nexus gRPC Ecosystem: 横切关注点插件设计 (v1)

文档ID: NX-GRPC-DESIGN-05
版本: v1.0.0
状态: 设计中

---

## 核心定位

本项目的核心定位是构建一个企业级的、可插拔的 gRPC 治理生态系统，专注于横切关注点的模块化管理。通过分离服务发现、安全认证和监控追踪等治理功能，实现微服务架构中 gRPC 通信的标准化、可观测性和高可用性。

**技术栈**: Java 21、Spring Boot 3.4.5、gRPC-Java 1.73.0、Nacos 2.3.0、Micrometer 1.12.4
复杂度等级: L2

## 包含范围
- 服务发现插件设计与实现 (Nacos、Consul、Kubernetes)
- 安全认证插件设计与实现 (JWT、OAuth2)
- 监控观测插件设计与实现 (Metrics、Tracing、Logging)
- 插件自动配置机制
- 插件间协调与编排

## 排除范围
- 核心客户端/服务端插件实现 (在其他文档中描述)
- 具体的业务逻辑插件
- 第三方系统的详细配置
- 性能优化和调优策略

## 设计哲学

本项目遵循以下设计哲学：
1. **分离关注点** - 将治理功能从核心通信逻辑中完全分离
2. **独立演进** - 每个插件可以独立升级和替换
3. **按需选择** - 支持根据技术栈灵活选择插件组合
4. **职责单一** - 每个插件专注于单一治理职责

## 实施约束

### 强制性要求
- **插件接口实现**: 所有横切关注点插件必须实现 nexus-grpc-api 中定义的标准接口
  - 违规后果：编译失败，无法通过CI/CD流水线
  - 验证方式：通过接口实现检查和集成测试验证
- **自动配置机制**: 每个插件必须提供 Spring Boot AutoConfiguration 类
  - 约束细节：必须包含@Configuration、@ConditionalOnClass、@ConditionalOnProperty注解
  - 验证方式：通过Spring Boot应用启动测试验证
- **版本兼容性**: 插件必须与 Java 21+ 和 Spring Boot 3.4.5+ 兼容
  - 具体版本：Java 21+、Spring Boot 3.4.5+、gRPC-Java 1.73.0+
  - 验证方式：多版本矩阵测试覆盖主要版本组合
- **依赖隔离**: 插件不得直接依赖其他横切关注点插件
  - 隔离策略：通过接口抽象和事件机制实现插件间通信
  - 验证方式：依赖图分析和架构测试工具检查

### 性能要求
- **启动时间**: 插件初始化时间不得超过 100ms
  - 测量方式：从AutoConfiguration加载到Bean完全初始化的耗时
  - 验证工具：Spring Boot Actuator + Micrometer计时器
- **内存占用**: 单个插件内存占用不得超过 50MB
  - 测量范围：插件相关的所有对象（包括缓存、连接池等）
  - 验证方式：JVM内存分析工具（如VisualVM、JProfiler）
- **响应延迟**: 拦截器处理延迟不得超过 5ms
  - 测量标准：从拦截器入口到出口的纯处理时间（不包括网络IO）
  - 验证方式：JMH微基准测试，99%分位数小于5ms
- **并发性能**: 支持每秒至少1000次并发调用
  - 负载测试：使用ghz工具进行压力测试
  - 验证标准：在1000 RPS下，响应时间P99 < 10ms

### 兼容性要求
- **gRPC版本兼容**: 支持gRPC-Java 1.73.0及以上版本
  - 测试覆盖：1.73.0、1.72.x、1.71.x版本
  - 向前兼容：确保在新版本gRPC上正常工作
- **Spring生态集成**: 与Spring Cloud、Spring Security无缝集成
  - 集成测试：验证与Spring Cloud Gateway、Eureka等组件的兼容性
  - 配置冲突检测：自动检测和解决配置冲突

### 安全约束
- **认证强制**: 生产环境必须启用认证插件
  - 配置检查：启动时检查nexus.grpc.security.enabled=true
  - 默认策略：开发环境可禁用，生产环境强制启用
- **传输加密**: 生产环境必须启用TLS
  - TLS版本：支持TLS 1.2和1.3
  - 证书管理：支持自签名（开发）和CA签名（生产）证书
- **敏感信息保护**: 禁止在日志中输出敏感信息
  - 脱敏策略：自动脱敏JWT token、密码等敏感字段
  - 审计要求：记录认证和授权相关的关键操作

### 监控要求
- **指标暴露**: 必须暴露核心业务和技术指标
  - 业务指标：请求数、成功率、响应时间分布
  - 技术指标：JVM内存、GC情况、线程池状态
- **链路追踪**: 支持分布式链路追踪
  - 追踪覆盖：100%的gRPC调用必须包含在trace中
  - 采样策略：生产环境建议采样率10-20%
- **健康检查**: 提供标准的健康检查端点
  - 检查项目：数据库连接、外部服务连通性、内存使用率
  - 响应格式：符合Spring Boot Actuator标准

### 验证锚点
- **接口契约验证**: 通过集成测试验证插件接口实现正确性
  - 测试覆盖：所有公开接口的正常流程和异常流程
  - 自动化程度：CI/CD流水线中100%自动执行
- **性能基准测试**: 通过 JMH 基准测试验证性能指标
  - 基准环境：标准化的测试环境（CPU、内存、网络配置）
  - 回归检测：性能退化超过20%时自动告警
- **兼容性验证**: 通过多版本矩阵测试验证兼容性
  - 测试矩阵：覆盖支持的Java、Spring Boot、gRPC版本组合
  - 验证频率：每个版本发布前必须通过完整矩阵测试
- **安全扫描**: 通过SAST/DAST工具进行安全漏洞扫描
  - 扫描工具：SonarQube、OWASP ZAP等
  - 阻断策略：发现高危漏洞时阻止部署

## 架构蓝图

### 分层架构设计

#### 层次划分与职责定义
系统采用严格的分层架构，确保横切关注点的模块化管理：

```
┌─────────────────────────────────────────┐
│           Business Layer               │  ← 业务逻辑层
│  • 业务服务实现                         │
│  • 业务规则处理                         │
│  • 领域模型管理                         │
├─────────────────────────────────────────┤
│       Horizontal Concerns Layer        │  ← 横切关注点层
│  ┌──────────┬──────────┬──────────┐    │
│  │Discovery │Security  │Monitoring│    │  ← 治理插件层
│  │ Plugin   │ Plugin   │ Plugin   │    │  • 服务发现：注册/发现
│  │          │          │          │    │  • 安全认证：JWT/OAuth2
│  │          │          │          │    │  • 监控观测：指标/追踪
│  └──────────┴──────────┴──────────┘    │
├─────────────────────────────────────────┤
│         Core gRPC Layer               │  ← 核心gRPC层
│     (Client & Server Plugins)          │  • 客户端插件
│                                         │  • 服务端插件
│                                         │  • 拦截器链管理
├─────────────────────────────────────────┤
│           Transport Layer              │  ← 传输层
│          (gRPC Framework)              │  • 网络通信
│                                         │  • 协议处理
│                                         │  • 连接管理
└─────────────────────────────────────────┘
```

#### 依赖方向与约束
- **向下依赖原则**：上层可依赖下层，严禁向上依赖
- **横向隔离原则**：同层插件间不得直接依赖
- **接口契约原则**：所有依赖通过标准接口实现

### 模块依赖关系图

```
nexus-grpc-api (接口层)
       ↑
       │ implements
       │
┌──────┼──────────────────────────┐
│      │                          │
│  Discovery Plugins          Security Plugins     Monitoring Plugins
│      │                          │                      │
│  ┌───▼────┐                ┌───▼────┐            ┌───▼────┐
│  │ Nacos  │                │  JWT   │            │Metrics │
│  │Consul  │                │OAuth2  │            │Tracing │
│  │  K8s   │                │ LDAP   │            │Logging │
│  └────────┘                └────────┘            └────────┘
└─────────────────────────────────────────────────────────────┘
                           │
                           ▼
                Spring Boot AutoConfiguration
                           │
                           ▼
                   Application Context
```

#### 接口契约定义

**1. 服务发现契约 (ServiceDiscovery)**
```java
public interface ServiceDiscovery {
    // 服务注册：将当前实例注册到注册中心
    CompletableFuture<Void> register(ServiceInstance instance);
    
    // 服务注销：从注册中心移除当前实例
    CompletableFuture<Void> deregister(ServiceInstance instance);
    
    // 实例发现：获取指定服务的所有可用实例
    CompletableFuture<List<ServiceInstance>> getInstances(String serviceName);
    
    // 健康检查：检查指定实例的健康状态
    CompletableFuture<Boolean> isHealthy(ServiceInstance instance);
}
```

**2. 安全认证契约 (CredentialsProvider)**
```java
public interface CredentialsProvider {
    // 获取当前请求的认证凭据
    CallCredentials getCredentials(CallContext context);
    
    // 刷新认证凭据（用于token续期）
    CompletableFuture<Void> refreshCredentials();
    
    // 验证凭据有效性
    boolean isValid(CallCredentials credentials);
}
```

**3. 拦截器契约 (NexusInterceptor)**
```java
public interface NexusInterceptor extends Ordered {
    // 拦截处理逻辑
    <ReqT, RespT> ServerCall.Listener<ReqT> intercept(
        ServerCall<ReqT, RespT> call,
        Metadata headers,
        ServerCallHandler<ReqT, RespT> next
    );
    
    // 获取执行顺序（数值越小越先执行）
    @Override
    int getOrder();
}
```

### 架构演进策略

#### 演进原则
1. **向后兼容**：新版本必须兼容旧版本的API契约
2. **渐进式升级**：支持插件的独立升级和回滚
3. **功能开关**：通过配置控制新功能的启用/禁用
4. **平滑迁移**：提供完整的迁移路径和工具

#### 扩展性设计
- **插件热插拔**：支持运行时动态加载/卸载插件
- **多实现共存**：同一类型插件可配置多个实现
- **策略模式应用**：通过策略模式支持算法的动态切换
- **事件驱动架构**：通过事件总线实现插件间的松耦合通信

#### 版本兼容性管理
- **语义化版本控制**：严格遵循SemVer规范
- **API版本标记**：通过注解标记API的稳定性等级
- **兼容性测试矩阵**：覆盖主要版本组合的兼容性测试
- **弃用策略**：提供至少两个大版本的弃用过渡期

## 1. 设计理念：横切关注点的模块化治理

### 1.1 核心概念定义

**横切关注点 (Cross-Cutting Concerns)**: 指在应用程序中跨越多个模块的功能性需求，如服务发现、安全认证、监控观测等。这些关注点与核心业务逻辑正交，但对系统运行至关重要。

**插件化架构 (Plugin Architecture)**: 通过标准化接口将横切关注点实现为可插拔的模块，实现功能的热插拔、独立演进和灵活组合。

**治理生态 (Governance Ecosystem)**: 围绕gRPC通信构建的完整治理体系，包括服务发现、安全认证、监控观测等横切关注点的统一管理框架。

### 1.2 设计原则体系

#### 分离关注点原则 (Separation of Concerns)
- **业务逻辑纯净性**: 业务代码专注于核心逻辑，不掺杂治理相关代码
- **技术栈解耦**: 治理功能与具体技术栈解耦，支持技术选型的灵活变更
- **层次清晰**: 严格按照分层架构组织代码，每层职责明确且不越界

#### 独立演进原则 (Independent Evolution)  
- **版本独立**: 每个插件具有独立的版本生命周期，可单独升级和回滚
- **API稳定性**: 通过契约接口保证API的稳定性，支持实现的平滑替换
- **向后兼容**: 新版本必须保持向后兼容，确保升级过程的平滑性

#### 按需组装原则 (Compose on Demand)
- **最小依赖**: 应用只引入实际需要的插件，避免不必要的资源消耗
- **灵活配置**: 通过配置文件控制插件的启用/禁用和行为调整
- **运行时组装**: 支持在运行时动态调整插件组合，无需重启应用

#### 职责单一原则 (Single Responsibility)
- **功能聚焦**: 每个插件专注于单一治理职责，避免功能膨胀
- **接口最小化**: 插件接口只包含必要的方法，保持接口的简洁性
- **依赖最小化**: 减少插件间的依赖关系，通过事件机制实现松耦合

### 1.3 架构价值分析

#### 技术价值
- **可维护性提升**: 通过模块化降低系统复杂度，每个插件可独立开发和测试
- **可扩展性增强**: 新的治理需求可通过开发新插件快速响应，无需修改现有代码
- **技术债务控制**: 避免治理逻辑与业务逻辑混合，减少技术债务积累

#### 业务价值  
- **上线速度提升**: 标准化的插件体系减少重复开发，加快新项目上线速度
- **运维成本降低**: 统一的治理框架简化运维操作，降低人员培训成本
- **风险控制增强**: 通过标准化的安全和监控插件，提升系统的安全性和可观测性

### 1.4 实施模式规范

#### 标准插件模式 (Standard Plugin Pattern)
所有横切关注点插件都遵循统一的实施模式：

**第一步：契约实现 (Contract Implementation)**
```java
// 实现nexus-grpc-api中定义的标准接口
public class NacosServiceDiscovery implements ServiceDiscovery {
    // 具体实现逻辑
}
```

**第二步：自动配置 (Auto Configuration)**  
```java
@Configuration
@ConditionalOnClass(NamingService.class)
@ConditionalOnProperty(prefix = "nexus.grpc.discovery", name = "type", havingValue = "nacos")
public class NacosDiscoveryAutoConfiguration {
    // Bean定义和配置
}
```

**第三步：服务注册 (Service Registration)**
```java
// 将插件实现注册为Spring Bean，供框架调用
@Bean
@Primary
public ServiceDiscovery serviceDiscovery(NamingService namingService) {
    return new NacosServiceDiscovery(namingService);
}
```

#### 生命周期管理模式 (Lifecycle Management Pattern)
- **初始化阶段**: 在Spring容器启动时完成插件的依赖注入和初始化配置
- **运行阶段**: 响应框架调用，执行具体的治理功能
- **销毁阶段**: 在应用关闭时完成资源清理和连接关闭

#### 错误处理模式 (Error Handling Pattern)  
- **优雅降级**: 插件异常时不影响核心业务功能，提供降级机制
- **快速失败**: 对于关键错误（如配置错误），采用快速失败策略
- **可观测性**: 所有错误都通过统一的监控插件进行记录和告警

## 2. 服务发现插件 (`discovery-plugin`)

这是 `Nexus` 中最重要的一类插件，它允许 gRPC 客户端动态地找到服务端。

### 2.1. `nexus-grpc-discovery-nacos`

- **依赖**: `com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery`
- **契约实现**: `NacosServiceDiscovery` 类实现 `ServiceDiscovery` 接口。
    - `register()`: 调用 Nacos `NamingService.registerInstance()`。
    - `deregister()`: 调用 Nacos `NamingService.deregisterInstance()`。
    - `getInstances()`: 调用 Nacos `NamingService.selectInstances()`。
- **自动配置**: `NacosDiscoveryAutoConfiguration`
    - `@ConditionalOnClass(NamingService.class)`: 确保只有在项目中引入了 Nacos 客户端依赖时才生效。
    - `@ConditionalOnProperty(prefix = "nexus.grpc.discovery", name = "type", havingValue = "nacos")`: 允许用户通过配置显式选择 nacos。
    - 它会创建一个 `NacosServiceDiscovery` 的 Bean，并注入 Spring Cloud Nacos 自动配置好的 `NamingService` 实例。

### 2.2. 其他实现 (如 `consul`, `kubernetes`)

遵循同样的模式，可以轻松实现对接其他注册中心的插件。例如，`nexus-grpc-discovery-kubernetes` 可以通过与 Kubernetes API Server 交互，直接查询 `Endpoints` 或 `EndpointSlice` 资源来获取服务的 Pod IP 列表，从而在 K8s 环境中实现原生的服务发现。

## 3. 安全插件 (`security-plugin`)

安全插件主要通过 gRPC `Interceptor` 机制来工作，在请求处理链中执行认证和授权逻辑。

### 3.1. `nexus-grpc-security-jwt`

- **职责**: 提供基于 JWT (JSON Web Token) 的认证能力。
- **客户端**: 
    - `JwtClientCredentialsProvider` 实现 `CredentialsProvider` 接口。
    - 它负责从某个地方（如 `SecurityContextHolder` 或 RPC `Context`）获取当前用户的 JWT，并将其作为 `CallCredentials` 附加到即将发出的 gRPC 请求的 `Metadata` 中（通常在 `Authorization` 头里）。
- **服务端**:
    - `JwtServerSecurityInterceptor` 实现 `NexusServerInterceptor`。
    - 它的 `@Order` 值非常高（例如 `Ordered.HIGHEST_PRECEDENCE`），确保它是最先执行的拦截器之一。
    - 它从请求的 `Metadata` 中提取 JWT，使用配置的公钥或密钥对其进行验证（签名、过期时间等）。
    - 验证通过后，它可以将解析出的用户信息（如用户ID、角色）存入 gRPC `Context`，供后续的业务逻辑使用。
    - 验证失败，则立即返回 `UNAUTHENTICATED` 状态码，终止请求。
- **自动配置**: `JwtSecurityAutoConfiguration` 会自动创建上述的 `Provider` 和 `Interceptor` Bean，并允许用户通过 `application.yml` 配置 JWT 的密钥、签发者等信息。

## 4. 监控插件 (`monitoring-plugin`)

监控插件的目标是提供 `Nexus` 生态的"黄金三指标"：Metrics (度量)、Tracing (追踪) 和 Logging (日志)。

### 4.1. Metrics with Micrometer

- **职责**: 收集 gRPC 调用的关键性能指标，并将其暴露给 Prometheus 等监控系统。
- **集成**: `MicrometerMonitoringAutoConfiguration`
    - `@ConditionalOnClass(MeterRegistry.class)`: 确保在引入了 Micrometer 的项目中才生效。
    - 它会创建 `MicrometerClientInterceptor` 和 `MicrometerServerInterceptor`。
- **拦截器逻辑**:
    - 在调用开始时，启动一个计时器 `Timer.Sample`。
    - 在调用结束时，停止计时器，并根据调用的结果（成功/失败、gRPC 状态码）记录到 `Timer` 中。
    - 使用 Micrometer 的 `Tags` 来区分不同的服务和方法。例如 `grpc.server.requests.seconds{grpc_service="UserService", grpc_method="FindUserById", status="OK"}`。
- **核心指标**: 
    - `grpc.client.requests.seconds`: 客户端调用耗时。
    - `grpc.server.requests.seconds`: 服务端处理耗时。
    - `grpc.server.requests.active`: 当前正在处理的请求数。

### 4.2. Tracing with OpenTelemetry

- **职责**: 实现分布式链路追踪，将跨服务的 gRPC 调用串联起来。
- **集成**: `OpenTelemetryTracingAutoConfiguration`
    - gRPC-Java 官方已经提供了 `grpc-opentelemetry` 模块，它提供了现成的 `OpenTelemetryClientInterceptor` 和 `OpenTelemetryServerInterceptor`。
    - `Nexus` 的自动配置类只需探测到 OpenTelemetry 的 SDK 是否存在于 classpath 中，如果存在，就将官方提供的这两个拦截器注册为 Spring Bean，并确保它们的执行顺序是正确的（通常在所有其他拦截器之前）。
- **工作原理**: 
    - **客户端**: 在发起调用前，从当前 `Context` 中获取 `Span`，创建一个新的子 `Span`，并将其上下文信息（`trace_id`, `span_id`）通过 `Metadata` 注入到请求中。
    - **服务端**: 在收到请求时，从 `Metadata` 中提取追踪上下文，并创建一个新的 `Span` 作为客户端 `Span` 的子节点。这样，一条完整的调用链就建立起来了。

---

**下一步**: [06-developer-guide.md](./06-developer-guide.md)
