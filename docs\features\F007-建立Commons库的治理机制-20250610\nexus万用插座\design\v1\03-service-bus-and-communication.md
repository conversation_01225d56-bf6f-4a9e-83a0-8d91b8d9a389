# XKongCloud Commons Nexus V1.0: 服务总线与异步通信模型

## 文档元数据

- **文档ID**: `F007-NEXUS-ARCHITECTURE-DESIGN-003`
- **版本**: `V1.0`
- **创建日期**: `2025-06-12`
- **状态**: `设计稿`
- **技术栈**: `Java 21, Spring Boot 3.4.5, Virtual Threads`
- **复杂度等级**: L2

## 实施约束标注

### 🔒 强制性技术约束
- **Java版本**: 必须使用Java 21或更高版本，强制启用Virtual Threads
- **Spring Boot版本**: 必须使用Spring Boot 3.4.5+，确保事件机制兼容
- **线程模型**: 严格禁止使用传统线程池，必须使用Virtual Threads
- **事件处理**: 所有事件监听器必须是无状态的，禁止共享可变状态

### ⚡ 性能指标约束
- **事件发布延迟**: ≤1ms（事件发布到总线的时间）
- **事件处理吞吐量**: ≥10,000 events/second（单机模式）
- **监听器注册时间**: ≤10ms（监听器注册到总线的时间）
- **服务查找延迟**: ≤0.1ms（服务发现的响应时间）
- **内存占用**: 总线基础内存≤30MB，每1000个监听器额外≤10MB

### 🔄 兼容性要求
- **事件向后兼容**: 事件接口保证向后兼容，字段只能新增不能删除
- **监听器兼容**: 支持基于注解和接口的两种监听器注册方式
- **序列化兼容**: 事件对象必须支持JSON序列化，为分布式扩展做准备

### ⚠️ 违规后果定义
- **技术约束违规**: 事件处理失败，记录ERROR级别日志，触发熔断机制
- **性能指标超标**: 记录WARN级别日志，触发性能监控告警
- **兼容性问题**: 降级处理，记录兼容性问题报告

### 🎯 验证锚点设置
- **编译验证**: `mvn clean compile -Djava.version=21`
- **单元测试**: `mvn test -Dtest=ServiceBusTest`
- **性能测试**: `mvn test -Dtest=ServiceBusPerformanceTest`
- **集成测试**: `mvn verify -Dtest=EventCommunicationTest`

## 核心定位

服务总线 (Service Bus) 是 Nexus 架构的**中央神经系统**，负责插件间的解耦通信和能力组合。它的核心定位是成为**"组合优化"**理念的核心赋能者，通过异步事件驱动机制实现插件间的高效协作。

## 设计哲学

服务总线的设计哲学超越了简单的消息传递，旨在实现真正的插件能力组合：

- **极致解耦**: 插件之间永不直接引用。所有交互都通过总线这一中介进行，使得任何插件都可以被独立替换、升级或移除，而不影响其他插件
- **异步优先**: 所有通信本质上都是异步的，即使是请求/响应模式。这确保了系统的高吞吐和高伸缩性，并能无缝利用Java 21虚拟线程的优势，避免线程阻塞
- **可组合性**: 总线不仅传递数据，更传递"能力"。通过监听和响应事件，插件可以将自身能力与其它插件的能力动态组合，创造出新的、更强大的功能

### 架构设计原则
- **发布-订阅模式**: 基于事件驱动的松耦合通信机制
- **命令查询职责分离**: 区分命令事件和查询事件的处理方式
- **单一职责原则**: 每个事件监听器只处理一种类型的事件
- **开放封闭原则**: 对事件扩展开放，对总线核心修改封闭

## 包含范围

本文档包含以下核心内容：

- **服务总线接口设计**: ServiceBus核心契约和API定义
- **事件模型设计**: Event基类和监听器机制
- **通信协议定义**: 异步事件驱动通信协议
- **消息路由规则**: 事件分发和路由机制
- **实现策略**: 进程内总线和分布式扩展方案
- **虚拟线程集成**: Java 21虚拟线程的性能优化

## 排除范围

本文档明确不包含以下内容：

- **插件生命周期管理**: 由微内核负责，在其他文档中描述
- **具体业务事件**: 不定义具体的业务事件类型
- **分布式事务**: 不涉及跨插件的事务管理
- **消息持久化**: 不涉及事件的持久化存储
- **安全认证**: 插件间通信的安全机制在安全文档中描述
- **监控指标**: 总线性能监控在运维文档中描述

## 服务总线架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "插件层"
        PluginA[Plugin A]
        PluginB[Plugin B]
        PluginC[Plugin C]
    end

    subgraph "服务总线层"
        ServiceBus[Service Bus Core]
        EventDispatcher[Event Dispatcher]
        ServiceRegistry[Service Registry]
        ListenerRegistry[Listener Registry]
    end

    subgraph "通信协议层"
        EventBus[Event Bus]
        ServiceDiscovery[Service Discovery]
        MessageRouter[Message Router]
    end

    subgraph "执行引擎层"
        VirtualThreadPool[Virtual Thread Pool]
        EventProcessor[Event Processor]
        ErrorHandler[Error Handler]
    end

    PluginA --> ServiceBus
    PluginB --> ServiceBus
    PluginC --> ServiceBus

    ServiceBus --> EventDispatcher
    ServiceBus --> ServiceRegistry
    ServiceBus --> ListenerRegistry

    EventDispatcher --> EventBus
    ServiceRegistry --> ServiceDiscovery
    ListenerRegistry --> MessageRouter

    EventBus --> VirtualThreadPool
    ServiceDiscovery --> EventProcessor
    MessageRouter --> ErrorHandler
```

### 核心组件关系图（已修复循环依赖）

```mermaid
classDiagram
    class ServiceBusPublisher {
        <<interface>>
        +publish(Event)
        +publishAsync(Event) CompletableFuture
        +registerService(Class~T~, T)
        +unregisterService(ServiceRegistration)
    }

    class ServiceBusSubscriber {
        <<interface>>
        +registerListener(Object)
        +unregisterListener(Object)
        +getService(Class~T~) Optional~T~
        +getServices(Class~T~) List~T~
    }

    class InProcessServiceBusPublisher {
        -eventDispatcher: EventDispatcher
        -securityManager: SecurityManager
        +publish(Event)
        +publishAsync(Event) CompletableFuture
        +registerService(Class~T~, T)
        +unregisterService(ServiceRegistration)
    }

    class InProcessServiceBusSubscriber {
        -serviceRegistry: ServiceRegistry
        -listenerRegistry: ListenerRegistry
        +registerListener(Object)
        +unregisterListener(Object)
        +getService(Class~T~) Optional~T~
        +getServices(Class~T~) List~T~
    }

    class EventDispatcher {
        <<服务总线核心实现组件>>
        -virtualExecutor: Executor
        -subscriber: ServiceBusSubscriber
        +dispatch(Event)
        +dispatchAsync(Event) CompletableFuture
        +configure(ExecutorConfig)
        +getMetrics() EventMetrics
        -invokeListener(Object, Method, Event)
    }

    class ServiceRegistry {
        -services: Map~Class, List~Object~~
        -serviceLock: ReadWriteLock
        +register(Class~T~, T)
        +unregister(Class~T~, T)
        +findService(Class~T~) Optional~T~
        +findServices(Class~T~) List~T~
    }

    class ListenerRegistry {
        -listeners: Map~Class, List~ListenerMethod~~
        -listenerLock: ReadWriteLock
        +register(Object)
        +unregister(Object)
        +getListeners(Class~Event~) List~ListenerMethod~
    }

    class Event {
        <<interface>>
        +getTimestamp() Instant
        +getSourcePluginId() String
        +getEventId() String
        +getCorrelationId() String
    }

    class ListenerMethod {
        -target: Object
        -method: Method
        -eventType: Class~Event~
        -async: boolean
        +invoke(Event)
        +getEventType() Class~Event~
    }

    ServiceBusPublisher <|-- InProcessServiceBusPublisher
    ServiceBusSubscriber <|-- InProcessServiceBusSubscriber
    InProcessServiceBusPublisher --> EventDispatcher
    EventDispatcher --> InProcessServiceBusSubscriber
    InProcessServiceBusSubscriber --> ServiceRegistry
    InProcessServiceBusSubscriber --> ListenerRegistry
    ListenerRegistry --> ListenerMethod
    EventDispatcher ..> Event
    ListenerMethod ..> Event
```

## 核心通信契约: 发布者-订阅者模式

为了消除循环依赖，我们将原来的`ServiceBus`接口拆分为`ServiceBusPublisher`和`ServiceBusSubscriber`两个独立的接口，实现职责分离和单向依赖。

### `ServiceBusPublisher` 接口 - 发布者

`ServiceBusPublisher` 接口专门负责事件发布和服务注册，不依赖任何其他业务组件。

```java
package org.xkong.cloud.commons.nexus.api;

import java.util.Properties;
import java.util.concurrent.CompletableFuture;

/**
 * 服务总线发布者接口 - 专门负责事件发布和服务注册。
 *
 * <p>发布者接口的设计原则：</p>
 * <ul>
 *   <li><strong>单向依赖</strong>：只依赖EventDispatcher，不依赖订阅者</li>
 *   <li><strong>职责单一</strong>：专注于发布和注册，不处理查询</li>
 *   <li><strong>性能优化</strong>：发布操作≤2ms，注册操作≤50ms</li>
 * </ul>
 *
 * @since 1.0.0
 * <AUTHOR> Team
 */
public interface ServiceBusPublisher {

    /**
     * 发布一个事件 (Publish/Subscribe模式)。
     *
     * <p>这是一个"发射后不管"的操作，事件将被异步地派发给所有监听此事件类型的监听器。
     * 所有监听器的调用都在Virtual Threads中执行，确保高并发性能。</p>
     *
     * <p><strong>性能约束</strong>：此方法必须在5ms内返回</p>
     * <p><strong>异常处理</strong>：监听器异常不会影响事件发布的成功</p>
     *
     * @param event 要发布的事件对象，不能为null
     * @throws IllegalArgumentException 如果event为null
     * @throws ServiceBusException 如果总线不可用
     */
    void publish(Event event);

    /**
     * 异步发布事件并返回Future。
     *
     * <p>与publish()方法不同，此方法返回一个Future，可以用于：</p>
     * <ul>
     *   <li>等待所有监听器处理完成</li>
     *   <li>获取监听器处理结果</li>
     *   <li>处理监听器异常</li>
     * </ul>
     *
     * @param event 要发布的事件对象
     * @return CompletableFuture，完成时表示所有监听器已处理
     * @since 1.1.0
     */
    CompletableFuture<Void> publishAsync(Event event);



    /**
     * 注册一个服务实例。
     *
     * <p>服务注册后可以被其他插件通过getService()方法发现和使用。
     * 同一接口可以注册多个实现，通过getServices()获取所有实现。</p>
     *
     * <p><strong>生命周期</strong>：服务生命周期与注册插件绑定</p>
     *
     * @param <T> 服务类型
     * @param serviceInterface 服务的接口类型，不能为null
     * @param serviceInstance 服务的实例，不能为null
     * @param properties 服务属性，可以为null
     * @return 服务注册句柄，用于后续注销
     * @throws IllegalArgumentException 如果参数无效
     * @throws ServiceBusException 如果注册失败
     */
    <T> ServiceRegistration<T> registerService(Class<T> serviceInterface,
                                               T serviceInstance,
                                               Properties properties);

    /**
     * 注册服务（简化版本）。
     *
     * @param <T> 服务类型
     * @param serviceInterface 服务的接口类型
     * @param serviceInstance 服务的实例
     * @return 服务注册句柄
     */
    default <T> ServiceRegistration<T> registerService(Class<T> serviceInterface, T serviceInstance) {
        return registerService(serviceInterface, serviceInstance, null);
    }

}
```

### `ServiceBusSubscriber` 接口 - 订阅者

`ServiceBusSubscriber` 接口专门负责事件订阅和服务查找，不依赖发布者。

```java
package org.xkong.cloud.commons.nexus.api;

import java.util.List;
import java.util.Optional;

/**
 * 服务总线订阅者接口 - 专门负责事件订阅和服务查找。
 *
 * <p>订阅者接口的设计原则：</p>
 * <ul>
 *   <li><strong>单向依赖</strong>：只依赖ServiceRegistry，不依赖发布者</li>
 *   <li><strong>职责单一</strong>：专注于订阅和查询，不处理发布</li>
 *   <li><strong>性能优化</strong>：查询操作≤1ms，订阅操作≤3ms</li>
 * </ul>
 *
 * @since 1.0.0
 * <AUTHOR> Team
 */
public interface ServiceBusSubscriber {

    /**
     * 注册一个事件监听器。
     *
     * <p>监听器可以是任何对象，只要其方法标注了@Subscribe注解。
     * 监听器注册后立即生效，可以接收相应类型的事件。</p>
     *
     * @param listener 监听器实例，不能为null
     * @throws IllegalArgumentException 如果listener为null或不符合约束
     */
    void registerListener(Object listener);

    /**
     * 注销事件监听器。
     *
     * @param listener 要注销的监听器实例
     * @return true如果成功注销，false如果监听器未注册
     */
    boolean unregisterListener(Object listener);

    /**
     * 获取单个服务实例。
     *
     * <p>如果有多个实现，返回第一个注册的实例。</p>
     *
     * @param <T> 服务类型
     * @param serviceInterface 服务的接口类型，不能为null
     * @return 服务实例的Optional，如果未找到则返回空
     * @throws IllegalArgumentException 如果serviceInterface为null
     */
    <T> Optional<T> getService(Class<T> serviceInterface);

    /**
     * 获取指定接口的所有服务实例。
     *
     * @param <T> 服务类型
     * @param serviceInterface 服务的接口类型，不能为null
     * @return 所有服务实例的列表，如果未找到则返回空列表
     * @throws IllegalArgumentException 如果serviceInterface为null
     */
    <T> List<T> getServices(Class<T> serviceInterface);

    /**
     * 根据服务属性查找服务。
     *
     * @param <T> 服务类型
     * @param serviceInterface 服务的接口类型
     * @param filter 服务属性过滤器
     * @return 匹配的服务实例列表
     */
    <T> List<T> getServices(Class<T> serviceInterface, ServiceFilter filter);
}
```

## 消息/事件模型设计

为了保证通信的规范性和可追溯性，所有在总线上传递的消息都继承自一个基础接口。

### Event基类定义

```java
package org.xkong.cloud.commons.nexus.api;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

/**
 * 事件基础接口，所有在服务总线上传递的事件都必须实现此接口。
 *
 * <p>事件代表一个已经发生的事实，具有以下特征：</p>
 * <ul>
 *   <li>不可变性：事件一旦创建就不能修改</li>
 *   <li>可序列化：支持JSON序列化，为分布式扩展做准备</li>
 *   <li>可追溯性：包含完整的元数据信息</li>
 * </ul>
 *
 * <p><strong>命名约定</strong>：事件名称采用过去时，如UserRegisteredEvent</p>
 *
 * @since 1.0.0
 */
public interface Event {

    /**
     * 获取事件的唯一标识符。
     *
     * <p>每个事件实例都有一个全局唯一的ID，用于事件追踪和去重。</p>
     *
     * @return 事件唯一标识符，通常是UUID
     */
    default String getEventId() {
        return UUID.randomUUID().toString();
    }

    /**
     * 获取事件发生的时间戳。
     *
     * @return 事件时间戳，使用UTC时间
     */
    Instant getTimestamp();

    /**
     * 获取事件源的插件ID。
     *
     * @return 发布此事件的插件ID
     */
    String getSourcePluginId();

    /**
     * 获取关联ID，用于关联相关的事件。
     *
     * <p>在一个业务流程中，多个事件可以共享同一个关联ID，
     * 便于追踪完整的业务流程。</p>
     *
     * @return 关联ID，可以为null
     * @since 1.1.0
     */
    default String getCorrelationId() {
        return null;
    }

    /**
     * 获取事件类型。
     *
     * @return 事件类型，通常是类的简单名称
     */
    default String getEventType() {
        return this.getClass().getSimpleName();
    }

    /**
     * 获取事件版本。
     *
     * <p>用于事件模式演进和向后兼容性管理。</p>
     *
     * @return 事件版本，默认为"1.0"
     * @since 1.2.0
     */
    default String getVersion() {
        return "1.0";
    }

    /**
     * 获取事件的自定义属性。
     *
     * <p>用于传递额外的元数据信息。</p>
     *
     * @return 事件属性映射，不能为null
     * @since 1.1.0
     */
    default Map<String, Object> getProperties() {
        return Map.of();
    }

    /**
     * 检查事件是否需要持久化。
     *
     * <p>某些重要事件可能需要持久化存储，用于审计或恢复。</p>
     *
     * @return true如果需要持久化，默认为false
     * @since 1.2.0
     */
    default boolean isPersistent() {
        return false;
    }
}
```

### 事件分类体系

```java
/**
 * 领域事件：表示业务领域内发生的重要事件
 */
public interface DomainEvent extends Event {
    /**
     * 获取聚合根ID
     */
    String getAggregateId();

    /**
     * 获取聚合根类型
     */
    String getAggregateType();

    @Override
    default boolean isPersistent() {
        return true; // 领域事件默认需要持久化
    }
}

/**
 * 系统事件：表示系统级别的事件
 */
public interface SystemEvent extends Event {
    /**
     * 获取系统组件名称
     */
    String getComponentName();

    /**
     * 获取事件严重级别
     */
    EventSeverity getSeverity();
}

/**
 * 集成事件：用于跨系统边界的事件
 */
public interface IntegrationEvent extends Event {
    /**
     * 获取目标系统
     */
    String getTargetSystem();

    /**
     * 获取消息格式
     */
    String getMessageFormat();

    @Override
    default boolean isPersistent() {
        return true; // 集成事件默认需要持久化
    }
}

/**
 * 事件严重级别枚举
 */
public enum EventSeverity {
    INFO("信息"),
    WARN("警告"),
    ERROR("错误"),
    CRITICAL("严重");

    private final String description;

    EventSeverity(String description) {
        this.description = description;
    }

    public String getDescription() { return description; }
}
```

**Event**代表一个**已经发生的事实**。命名通常采用过去时，如 `UserRegisteredEvent`。

### 监听器机制

#### 基于注解的监听器

```java
package org.xkong.cloud.commons.nexus.api;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 事件订阅注解，标记方法为事件监听器。
 *
 * <p>被标记的方法必须满足以下约束：</p>
 * <ul>
 *   <li>方法必须是public的</li>
 *   <li>方法只能有一个参数，且参数类型必须是Event的子类</li>
 *   <li>方法不能有返回值（void）</li>
 *   <li>方法不能抛出检查异常</li>
 * </ul>
 *
 * @since 1.0.0
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Subscribe {

    /**
     * 监听器优先级，数值越小优先级越高。
     *
     * @return 优先级，默认为0
     */
    int priority() default 0;

    /**
     * 是否异步处理事件。
     *
     * @return true表示异步处理，false表示同步处理，默认为true
     */
    boolean async() default true;

    /**
     * 错误处理策略。
     *
     * @return 错误处理策略，默认为LOG_AND_CONTINUE
     */
    ErrorHandlingStrategy errorHandling() default ErrorHandlingStrategy.LOG_AND_CONTINUE;

    /**
     * 监听器描述信息。
     *
     * @return 描述信息，用于监控和调试
     */
    String description() default "";
}

/**
 * 错误处理策略枚举
 */
public enum ErrorHandlingStrategy {
    /**
     * 记录错误日志并继续处理其他监听器
     */
    LOG_AND_CONTINUE,

    /**
     * 记录错误日志并停止处理后续监听器
     */
    LOG_AND_STOP,

    /**
     * 重新抛出异常
     */
    RETHROW,

    /**
     * 忽略异常
     */
    IGNORE
}
```

#### 监听器实现示例

```java
/**
 * 用户活动监听器示例
 */
@Component
public class UserActivityListener {

    private static final Logger logger = LoggerFactory.getLogger(UserActivityListener.class);

    /**
     * 处理用户注册事件
     */
    @Subscribe(
        priority = 1,
        async = true,
        description = "处理用户注册后的初始化工作"
    )
    public void onUserRegistration(UserRegisteredEvent event) {
        logger.info("用户注册事件: userId={}, timestamp={}",
                   event.getUserId(), event.getTimestamp());

        // 执行用户注册后的初始化逻辑
        initializeUserProfile(event.getUserId());
        sendWelcomeEmail(event.getEmail());
    }

    /**
     * 处理用户登录事件
     */
    @Subscribe(
        priority = 2,
        async = true,
        errorHandling = ErrorHandlingStrategy.LOG_AND_CONTINUE
    )
    public void onUserLogin(UserLoginEvent event) {
        logger.info("用户登录事件: userId={}, ip={}",
                   event.getUserId(), event.getIpAddress());

        // 更新用户最后登录时间
        updateLastLoginTime(event.getUserId(), event.getTimestamp());

        // 记录登录日志
        recordLoginActivity(event);
    }

    /**
     * 处理系统错误事件（高优先级，同步处理）
     */
    @Subscribe(
        priority = -1,
        async = false,
        errorHandling = ErrorHandlingStrategy.RETHROW,
        description = "处理系统严重错误"
    )
    public void onSystemError(SystemErrorEvent event) {
        if (event.getSeverity() == EventSeverity.CRITICAL) {
            // 立即处理严重错误
            handleCriticalError(event);
        }
    }

    private void initializeUserProfile(String userId) {
        // 初始化用户配置文件
    }

    private void sendWelcomeEmail(String email) {
        // 发送欢迎邮件
    }

    private void updateLastLoginTime(String userId, Instant timestamp) {
        // 更新最后登录时间
    }

    private void recordLoginActivity(UserLoginEvent event) {
        // 记录登录活动
    }

    private void handleCriticalError(SystemErrorEvent event) {
        // 处理严重错误
    }
}
```

#### 基于接口的监听器（可选）

```java
/**
 * 事件监听器接口，提供类型安全的事件处理
 */
public interface EventListener<T extends Event> {

    /**
     * 处理事件
     *
     * @param event 事件实例
     */
    void handle(T event);

    /**
     * 获取监听的事件类型
     *
     * @return 事件类型
     */
    Class<T> getEventType();

    /**
     * 获取监听器优先级
     *
     * @return 优先级，默认为0
     */
    default int getPriority() {
        return 0;
    }

    /**
     * 是否异步处理
     *
     * @return true表示异步处理，默认为true
     */
    default boolean isAsync() {
        return true;
    }
}

/**
 * 用户注册事件监听器实现
 */
@Component
public class UserRegistrationListener implements EventListener<UserRegisteredEvent> {

    @Override
    public void handle(UserRegisteredEvent event) {
        // 处理用户注册事件
    }

    @Override
    public Class<UserRegisteredEvent> getEventType() {
        return UserRegisteredEvent.class;
    }

    @Override
    public int getPriority() {
        return 1;
    }
}
```

## 通信协议定义

### 事件通信协议

```mermaid
sequenceDiagram
    participant P1 as Plugin A
    participant SB as Service Bus
    participant ED as Event Dispatcher
    participant VT as Virtual Thread Pool
    participant P2 as Plugin B
    participant P3 as Plugin C

    P1->>SB: publish(UserRegisteredEvent)
    SB->>SB: 验证事件格式
    SB->>ED: dispatch(event)

    par 异步分发到所有监听器
        ED->>VT: 提交监听器任务
        VT->>P2: 调用监听器方法
        VT->>P3: 调用监听器方法
    end

    Note over P2,P3: 监听器在Virtual Threads中并行执行

    P2->>P2: 处理事件逻辑
    P3->>P3: 处理事件逻辑

    alt 监听器处理成功
        P2->>VT: 返回成功
        P3->>VT: 返回成功
    else 监听器处理失败
        P2->>VT: 抛出异常
        VT->>ED: 记录错误日志
        Note over ED: 根据错误策略处理
    end
```

### 服务发现协议

```mermaid
sequenceDiagram
    participant P1 as Plugin A
    participant SB as Service Bus
    participant SR as Service Registry
    participant P2 as Plugin B

    Note over P1,P2: 服务注册阶段
    P2->>SB: registerService(UserService.class, userServiceImpl)
    SB->>SR: 注册服务映射
    SR->>SR: 存储服务实例

    Note over P1,P2: 服务发现阶段
    P1->>SB: getService(UserService.class)
    SB->>SR: 查找服务实例
    SR->>SR: 检查访问权限
    SR->>SB: 返回服务实例
    SB->>P1: 返回UserService实例

    Note over P1,P2: 服务调用阶段
    P1->>P2: 直接调用服务方法
    P2->>P1: 返回调用结果
```

## 实现策略: 发布者-订阅者分离架构

为了消除循环依赖，我们采用发布者-订阅者分离的架构设计，通过EventDispatcher作为服务总线的核心实现组件实现解耦。

### EventDispatcher: 服务总线的核心实现组件

EventDispatcher是服务总线架构中的关键基础设施组件，负责：

#### 核心职责
- **事件分发**: 接收来自ServiceBusPublisher的事件并分发给ServiceBusSubscriber
- **异步处理**: 基于Virtual Threads提供高性能的异步事件处理
- **解耦机制**: 实现发布者和订阅者之间的完全解耦

#### 技术特性
- **Virtual Threads集成**: 利用Java 21的Virtual Threads实现轻量级并发
- **可配置执行器**: 支持不同的线程执行策略
- **监控指标**: 提供事件处理的性能监控和统计

#### 扩展能力
- **分布式扩展**: 可扩展为基于Kafka等消息中间件的分布式实现
- **事件过滤**: 支持基于规则的事件过滤和路由
- **持久化**: 可扩展事件持久化和事件溯源功能

### 默认实现: 进程内发布者-订阅者

#### `InProcessServiceBusPublisher` - 发布者实现

```java
package org.xkong.cloud.commons.nexus.servicebus;

import org.xkong.cloud.commons.nexus.api.ServiceBusPublisher;
import org.xkong.cloud.commons.nexus.api.Event;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * 进程内服务总线发布者实现
 */
@Component
@ConditionalOnProperty(name = "nexus.service-bus.type", havingValue = "in-process", matchIfMissing = true)
public class InProcessServiceBusPublisher implements ServiceBusPublisher {

    private final EventDispatcher eventDispatcher;
    private final SecurityManager securityManager;

    public InProcessServiceBusPublisher(EventDispatcher eventDispatcher,
                                       SecurityManager securityManager) {
        this.eventDispatcher = eventDispatcher;
        this.securityManager = securityManager;
    }

    @Override
    public void publish(Event event) {
        // 1. 安全检查
        securityManager.checkPublishPermission(event);

        // 2. 事件验证
        validateEvent(event);

        // 3. 委托给EventDispatcher
        eventDispatcher.dispatch(event);
    }

    @Override
    public CompletableFuture<Void> publishAsync(Event event) {
        // 1. 安全检查
        securityManager.checkPublishPermission(event);

        // 2. 事件验证
        validateEvent(event);

        // 3. 异步委托给EventDispatcher
        return eventDispatcher.dispatchAsync(event);
    }

    @Override
    public <T> ServiceRegistration<T> registerService(Class<T> serviceInterface,
                                                      T serviceInstance,
                                                      Properties properties) {
        // 1. 安全检查
        securityManager.checkRegisterPermission(serviceInterface);

        // 2. 参数验证
        validateServiceRegistration(serviceInterface, serviceInstance);

        // 3. 通过事件通知注册（避免直接依赖ServiceRegistry）
        ServiceRegistrationEvent registrationEvent = new ServiceRegistrationEvent(
            serviceInterface, serviceInstance, properties);
        eventDispatcher.dispatch(registrationEvent);

        return new DefaultServiceRegistration<>(serviceInterface, serviceInstance, properties);
    }

    private void validateEvent(Event event) {
        if (event == null) {
            throw new IllegalArgumentException("Event cannot be null");
        }
        if (event.getSourcePluginId() == null) {
            throw new IllegalArgumentException("Event source plugin ID cannot be null");
        }
    }

    private <T> void validateServiceRegistration(Class<T> serviceInterface, T serviceInstance) {
        if (serviceInterface == null) {
            throw new IllegalArgumentException("Service interface cannot be null");
        }
        if (serviceInstance == null) {
            throw new IllegalArgumentException("Service instance cannot be null");
        }
        if (!serviceInterface.isInstance(serviceInstance)) {
            throw new IllegalArgumentException("Service instance must implement the service interface");
        }
    }
}
```

#### `InProcessServiceBusSubscriber` - 订阅者实现

```java
package org.xkong.cloud.commons.nexus.servicebus;

import org.xkong.cloud.commons.nexus.api.ServiceBusSubscriber;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Optional;

/**
 * 进程内服务总线订阅者实现
 */
@Component
public class InProcessServiceBusSubscriber implements ServiceBusSubscriber {

    private final ServiceRegistry serviceRegistry;
    private final ListenerRegistry listenerRegistry;

    public InProcessServiceBusSubscriber(ServiceRegistry serviceRegistry,
                                        ListenerRegistry listenerRegistry) {
        this.serviceRegistry = serviceRegistry;
        this.listenerRegistry = listenerRegistry;
    }

    @Override
    public void registerListener(Object listener) {
        validateListener(listener);
        listenerRegistry.register(listener);
    }

    @Override
    public boolean unregisterListener(Object listener) {
        return listenerRegistry.unregister(listener);
    }

    @Override
    public <T> Optional<T> getService(Class<T> serviceInterface) {
        validateServiceInterface(serviceInterface);
        return serviceRegistry.findService(serviceInterface);
    }

    @Override
    public <T> List<T> getServices(Class<T> serviceInterface) {
        validateServiceInterface(serviceInterface);
        return serviceRegistry.findServices(serviceInterface);
    }

    @Override
    public <T> List<T> getServices(Class<T> serviceInterface, ServiceFilter filter) {
        validateServiceInterface(serviceInterface);
        return serviceRegistry.findServices(serviceInterface, filter);
    }

    private void validateListener(Object listener) {
        if (listener == null) {
            throw new IllegalArgumentException("Listener cannot be null");
        }
    }

    private <T> void validateServiceInterface(Class<T> serviceInterface) {
        if (serviceInterface == null) {
            throw new IllegalArgumentException("Service interface cannot be null");
        }
    }
}
```

#### 核心特性：
- **发布者特性**: 专注发布和注册，通过EventDispatcher实现解耦，包含安全检查
- **订阅者特性**: 专注订阅和查询，直接访问ServiceRegistry，性能优化
- **技术**: 基于内存实现，内部使用 `java.util.concurrent` 包和 `CompletableFuture`
- **虚拟线程集成**: **所有监听器的调用都将被分派到Java 21的虚拟线程上执行**。这消除了传统线程池的瓶颈，允许系统以极低的资源开销处理海量的并发事件，是实现"组合优化"的关键性能保障
- **优点**: 零网络开销，零序列化成本，单向依赖确保架构清晰

### 分布式实现 (未来扩展)

当系统需要演进为分布式架构时，可以提供针对特定中间件的发布者-订阅者实现：

#### Kafka实现示例

```java
@Component
@ConditionalOnProperty(name = "nexus.service-bus.type", havingValue = "kafka")
public class KafkaServiceBusPublisher implements ServiceBusPublisher {

    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final SecurityManager securityManager;

    @Override
    public void publish(Event event) {
        securityManager.checkPublishPermission(event);
        String topic = getTopicForEvent(event.getClass());
        kafkaTemplate.send(topic, event.getEventId(), event);
    }

    @Override
    public <T> ServiceRegistration<T> registerService(Class<T> serviceInterface,
                                                      T serviceInstance,
                                                      Properties properties) {
        // 分布式服务注册逻辑
        return new DistributedServiceRegistration<>(serviceInterface, serviceInstance);
    }
}

@Component
@ConditionalOnProperty(name = "nexus.service-bus.type", havingValue = "kafka")
public class KafkaServiceBusSubscriber implements ServiceBusSubscriber {

    private final DistributedServiceRegistry distributedServiceRegistry;

    @Override
    public <T> Optional<T> getService(Class<T> serviceInterface) {
        return distributedServiceRegistry.findService(serviceInterface);
    }

    // 其他查询方法实现...
}
```

开发者只需在配置文件中更改总线类型，插件代码**无需任何修改**，即可从单机模式切换到分布式模式：

```yaml
# 单机模式配置
nexus:
  service-bus:
    type: in-process

# Kafka分布式模式配置
nexus:
  service-bus:
    type: kafka
    kafka:
      bootstrap-servers: localhost:9092

# RabbitMQ分布式模式配置
nexus:
  service-bus:
    type: rabbitmq
    rabbitmq:
      host: localhost
      port: 5672
```

这体现了架构的终极灵活性。

## 消息路由规则

### 事件分发机制详解

#### 1. 事件路由流程图

```mermaid
flowchart TD
    A[事件发布] --> B{事件验证}
    B -->|验证失败| C[抛出异常]
    B -->|验证成功| D[查找监听器]
    D --> E{有监听器?}
    E -->|否| F[记录日志，结束]
    E -->|是| G[按优先级排序]
    G --> H[创建Virtual Thread任务]
    H --> I[并行执行监听器]
    I --> J{监听器执行}
    J -->|成功| K[记录成功日志]
    J -->|异常| L{错误策略}
    L -->|LOG_AND_CONTINUE| M[记录错误，继续]
    L -->|LOG_AND_STOP| N[记录错误，停止]
    L -->|RETHROW| O[重新抛出异常]
    L -->|IGNORE| P[忽略异常]
    K --> Q[更新统计信息]
    M --> Q
    N --> Q
    P --> Q
    Q --> R[分发完成]
```

#### 2. 监听器匹配规则

```java
/**
 * 事件监听器匹配器
 */
public class EventListenerMatcher {

    /**
     * 根据事件类型查找匹配的监听器
     */
    public List<ListenerMethod> findMatchingListeners(Class<? extends Event> eventType) {
        List<ListenerMethod> matches = new ArrayList<>();

        // 1. 精确类型匹配
        matches.addAll(getListenersByExactType(eventType));

        // 2. 父类型匹配（支持多态）
        Class<?> superClass = eventType.getSuperclass();
        while (superClass != null && Event.class.isAssignableFrom(superClass)) {
            matches.addAll(getListenersByExactType((Class<? extends Event>) superClass));
            superClass = superClass.getSuperclass();
        }

        // 3. 接口类型匹配
        for (Class<?> interfaceType : eventType.getInterfaces()) {
            if (Event.class.isAssignableFrom(interfaceType)) {
                matches.addAll(getListenersByExactType((Class<? extends Event>) interfaceType));
            }
        }

        // 4. 按优先级排序
        matches.sort(Comparator.comparingInt(ListenerMethod::getPriority));

        return matches;
    }
}
```

#### 3. 异步执行策略

```java
/**
 * Virtual Thread事件分发器
 */
public class VirtualThreadEventDispatcher implements EventDispatcher {

    private final Executor virtualExecutor = Executors.newVirtualThreadPerTaskExecutor();
    private final ListenerRegistry listenerRegistry;
    private final EventStatistics statistics;

    @Override
    public void dispatch(Event event) {
        List<ListenerMethod> listeners = listenerRegistry.getListeners(event.getClass());

        for (ListenerMethod listener : listeners) {
            if (listener.isAsync()) {
                // 异步执行
                virtualExecutor.execute(() -> invokeListener(listener, event));
            } else {
                // 同步执行
                invokeListener(listener, event);
            }
        }
    }

    @Override
    public CompletableFuture<Void> dispatchAsync(Event event) {
        List<ListenerMethod> listeners = listenerRegistry.getListeners(event.getClass());

        List<CompletableFuture<Void>> futures = listeners.stream()
            .map(listener -> CompletableFuture.runAsync(
                () -> invokeListener(listener, event),
                virtualExecutor))
            .collect(Collectors.toList());

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    }

    private void invokeListener(ListenerMethod listener, Event event) {
        try {
            long startTime = System.nanoTime();
            listener.invoke(event);
            long duration = System.nanoTime() - startTime;

            statistics.recordSuccess(listener, duration);

        } catch (Exception e) {
            statistics.recordError(listener, e);
            handleListenerError(listener, event, e);
        }
    }

    private void handleListenerError(ListenerMethod listener, Event event, Exception error) {
        switch (listener.getErrorHandlingStrategy()) {
            case LOG_AND_CONTINUE:
                logger.error("监听器执行失败，继续处理其他监听器: {}", listener, error);
                break;
            case LOG_AND_STOP:
                logger.error("监听器执行失败，停止处理: {}", listener, error);
                throw new EventProcessingException("监听器执行失败", error);
            case RETHROW:
                throw new EventProcessingException("监听器执行失败", error);
            case IGNORE:
                // 忽略异常，不记录日志
                break;
        }
    }
}
```

#### 4. 性能优化策略

| 优化维度 | 策略 | 实现方式 | 性能提升 |
|----------|------|----------|----------|
| **并发处理** | Virtual Threads | 每个监听器独立线程 | 10x+ 并发能力 |
| **内存优化** | 对象池化 | 重用Event包装对象 | 减少50% GC压力 |
| **缓存优化** | 监听器缓存 | 预编译监听器映射 | 减少90% 查找时间 |
| **批处理** | 事件批量分发 | 批量提交Virtual Thread任务 | 提升30% 吞吐量 |

### 服务发现机制详解

#### 1. 服务注册流程

```mermaid
sequenceDiagram
    participant P as Plugin
    participant SB as ServiceBus
    participant SR as ServiceRegistry
    participant SM as SecurityManager
    participant LM as LifecycleManager

    P->>SB: registerService(interface, impl, props)
    SB->>SM: 检查注册权限
    SM->>SB: 权限验证通过
    SB->>SR: 注册服务映射
    SR->>SR: 存储服务元数据
    SR->>LM: 绑定生命周期
    LM->>SR: 生命周期绑定成功
    SR->>SB: 返回注册句柄
    SB->>P: 返回ServiceRegistration
```

#### 2. 服务查找算法

```java
/**
 * 服务注册表实现
 */
public class ConcurrentServiceRegistry implements ServiceRegistry {

    private final ConcurrentHashMap<Class<?>, List<ServiceEntry<?>>> services = new ConcurrentHashMap<>();
    private final ReadWriteLock lock = new ReentrantReadWriteLock();

    @Override
    public <T> Optional<T> findService(Class<T> serviceInterface) {
        lock.readLock().lock();
        try {
            List<ServiceEntry<T>> entries = getServiceEntries(serviceInterface);

            // 1. 按优先级排序
            entries.sort(Comparator.comparingInt(ServiceEntry::getPriority));

            // 2. 检查可见性权限
            for (ServiceEntry<T> entry : entries) {
                if (isServiceVisible(entry, getCurrentPlugin())) {
                    return Optional.of(entry.getInstance());
                }
            }

            return Optional.empty();

        } finally {
            lock.readLock().unlock();
        }
    }

    @Override
    public <T> List<T> findServices(Class<T> serviceInterface) {
        lock.readLock().lock();
        try {
            List<ServiceEntry<T>> entries = getServiceEntries(serviceInterface);

            return entries.stream()
                .filter(entry -> isServiceVisible(entry, getCurrentPlugin()))
                .sorted(Comparator.comparingInt(ServiceEntry::getPriority))
                .map(ServiceEntry::getInstance)
                .collect(Collectors.toList());

        } finally {
            lock.readLock().unlock();
        }
    }

    private boolean isServiceVisible(ServiceEntry<?> entry, String currentPlugin) {
        // 1. 检查服务是否导出
        if (!entry.isExported()) {
            return entry.getOwnerPlugin().equals(currentPlugin);
        }

        // 2. 检查依赖关系
        return isDependencyAllowed(entry.getOwnerPlugin(), currentPlugin);
    }
}
```

#### 3. 服务生命周期管理

```java
/**
 * 服务注册句柄
 */
public class ServiceRegistrationImpl<T> implements ServiceRegistration<T> {

    private final Class<T> serviceInterface;
    private final T serviceInstance;
    private final Properties properties;
    private final String ownerPlugin;
    private volatile boolean active = true;

    @Override
    public void unregister() {
        if (active) {
            serviceRegistry.unregister(this);
            active = false;
        }
    }

    @Override
    public boolean isActive() {
        return active;
    }

    @Override
    public Properties getProperties() {
        return new Properties(properties);
    }

    @Override
    public void updateProperties(Properties newProperties) {
        if (active) {
            this.properties.clear();
            this.properties.putAll(newProperties);
            serviceRegistry.notifyPropertiesChanged(this);
        }
    }
}
```

#### 4. 服务发现特性

1. **接口注册**: 插件通过接口类型注册服务
2. **多实例支持**: 同一接口可以有多个实现
3. **动态查找**: 运行时动态查找和获取服务
4. **生命周期绑定**: 服务生命周期与插件生命周期绑定
5. **权限控制**: 基于插件依赖关系的服务可见性控制
6. **属性过滤**: 支持基于服务属性的过滤查找
7. **优先级排序**: 支持服务优先级排序
8. **热更新**: 支持服务的动态注册和注销

## 监控与统计

### 关键性能指标 (KPI)

| 指标类别 | 指标名称 | 目标值 | 监控方式 | 告警阈值 |
|----------|----------|--------|----------|----------|
| **事件性能** | 事件发布延迟 | ≤1ms | 方法耗时统计 | >5ms |
| **事件性能** | 事件处理吞吐量 | ≥10,000/s | 计数器统计 | <5,000/s |
| **监听器性能** | 监听器平均执行时间 | ≤10ms | 执行时间统计 | >50ms |
| **监听器性能** | 监听器成功率 | ≥99% | 成功/失败计数 | <95% |
| **服务发现** | 服务查找延迟 | ≤0.1ms | 方法耗时统计 | >1ms |
| **资源使用** | Virtual Thread数量 | 动态 | JVM监控 | >10,000 |

### 统计信息接口

```java
/**
 * 服务总线统计信息
 */
public class ServiceBusStatistics {

    // 事件统计
    private final AtomicLong totalEventsPublished = new AtomicLong();
    private final AtomicLong totalEventsProcessed = new AtomicLong();
    private final AtomicLong totalEventsFailed = new AtomicLong();

    // 监听器统计
    private final AtomicLong totalListenersRegistered = new AtomicLong();
    private final AtomicLong totalListenerInvocations = new AtomicLong();
    private final AtomicLong totalListenerErrors = new AtomicLong();

    // 服务统计
    private final AtomicLong totalServicesRegistered = new AtomicLong();
    private final AtomicLong totalServiceLookups = new AtomicLong();

    // 性能统计
    private final LongAdder totalEventPublishTime = new LongAdder();
    private final LongAdder totalListenerExecutionTime = new LongAdder();
    private final LongAdder totalServiceLookupTime = new LongAdder();

    // Getter方法
    public long getTotalEventsPublished() { return totalEventsPublished.get(); }
    public long getTotalEventsProcessed() { return totalEventsProcessed.get(); }
    public long getTotalEventsFailed() { return totalEventsFailed.get(); }

    public double getEventSuccessRate() {
        long total = totalEventsProcessed.get();
        return total > 0 ? (double)(total - totalEventsFailed.get()) / total : 0.0;
    }

    public double getAverageEventPublishTime() {
        long count = totalEventsPublished.get();
        return count > 0 ? (double)totalEventPublishTime.sum() / count / 1_000_000.0 : 0.0; // ms
    }

    public double getAverageListenerExecutionTime() {
        long count = totalListenerInvocations.get();
        return count > 0 ? (double)totalListenerExecutionTime.sum() / count / 1_000_000.0 : 0.0; // ms
    }

    public double getAverageServiceLookupTime() {
        long count = totalServiceLookups.get();
        return count > 0 ? (double)totalServiceLookupTime.sum() / count / 1_000.0 : 0.0; // μs
    }
}

/**
 * 服务总线健康状态
 */
public class ServiceBusHealth {

    public enum Status { UP, DOWN, DEGRADED }

    private final Status status;
    private final String message;
    private final Map<String, Object> details;
    private final Instant timestamp;

    public ServiceBusHealth(Status status, String message, Map<String, Object> details) {
        this.status = status;
        this.message = message;
        this.details = details != null ? Map.copyOf(details) : Map.of();
        this.timestamp = Instant.now();
    }

    // Getter方法
    public Status getStatus() { return status; }
    public String getMessage() { return message; }
    public Map<String, Object> getDetails() { return details; }
    public Instant getTimestamp() { return timestamp; }

    public boolean isHealthy() { return status == Status.UP; }
}
```

### JMX监控接口

```java
/**
 * 服务总线JMX管理接口
 */
@MXBean
public interface ServiceBusMXBean {

    // 基础统计
    long getTotalEventsPublished();
    long getTotalEventsProcessed();
    long getTotalEventsFailed();
    double getEventSuccessRate();

    // 性能指标
    double getAverageEventPublishTime();
    double getAverageListenerExecutionTime();
    double getAverageServiceLookupTime();
    double getCurrentThroughput();

    // 监听器统计
    long getTotalListenersRegistered();
    long getTotalListenerInvocations();
    long getTotalListenerErrors();
    String[] getTopSlowListeners();

    // 服务统计
    long getTotalServicesRegistered();
    long getTotalServiceLookups();
    String[] getRegisteredServiceTypes();

    // 健康检查
    String getHealthStatus();
    String[] performHealthCheck();

    // 操作方法
    void resetStatistics();
    void enableDebugMode();
    void disableDebugMode();
}
```

## 总结与架构价值

### 核心架构价值

1. **极致解耦**: 通过事件驱动实现插件间的完全解耦，支持独立开发和部署
2. **异步优先**: 基于Virtual Threads的异步处理，实现高并发和高吞吐量
3. **可组合性**: 通过事件和服务机制实现插件能力的动态组合
4. **平滑演进**: 支持从单机到分布式的平滑架构演进

### 设计模式应用

- **发布-订阅模式**: 事件驱动的松耦合通信机制
- **观察者模式**: 事件监听器的注册和通知机制
- **策略模式**: 可插拔的服务总线实现策略
- **注册表模式**: 服务注册和发现机制
- **命令模式**: 事件作为命令的封装和传递

### 技术创新点

1. **Virtual Threads集成**: 充分利用Java 21 Virtual Threads实现高并发
2. **类型安全的事件系统**: 基于泛型的类型安全事件处理
3. **多层次错误处理**: 灵活的错误处理策略配置
4. **智能服务发现**: 基于依赖关系的服务可见性控制
5. **性能监控体系**: 完整的性能指标和健康检查机制

### 性能特征

- **事件发布延迟**: ≤1ms
- **事件处理吞吐量**: ≥10,000 events/second
- **服务查找延迟**: ≤0.1ms
- **监听器成功率**: ≥99%
- **内存占用**: 基础≤30MB，每1000监听器+10MB

### 未来演进方向

- **分布式事件**: 支持跨JVM的事件传播
- **事件溯源**: 支持事件的持久化和重放
- **智能路由**: 基于内容的智能事件路由
- **性能自适应**: 基于负载的自动性能调优

这个服务总线与异步通信模型为XKongCloud Commons Nexus框架提供了强大的插件间通信能力，确保了系统的高性能、高可用性和高可扩展性。
