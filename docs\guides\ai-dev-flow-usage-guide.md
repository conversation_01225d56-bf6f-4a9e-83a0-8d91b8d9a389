# AI-DEV-FLOW规则系统使用指南

**文档ID**: G001
**创建日期**: 2023-05-08
**版本**: 1.0
**状态**: 草稿

## 1. 简介

本文档是AI-DEV-FLOW规则系统的人工使用指南，旨在帮助开发人员理解和应用这套规则系统，以提高AI辅助开发的效率和质量。AI-DEV-FLOW规则系统是一套专注于任务导向的文档读取和记忆刷新的规则体系，与RIPER-5协议无缝集成，支持多维思考和严格的执行协议。

## 2. 规则系统概述

### 2.1 什么是AI-DEV-FLOW规则系统

AI-DEV-FLOW规则系统是一套指导AI如何根据任务类型和阶段有选择地读取相关文档、刷新记忆并提供精准支持的规则体系。它与RIPER-5协议（RESEARCH、INNOVATE、PLAN、EXECUTE、REVIEW）集成，使AI能够在不同阶段应用不同的思维原则和文档读取策略。

### 2.2 核心理念

AI-DEV-FLOW规则系统基于以下核心理念：

1. **任务导向的文档读取**：不是让AI读取所有文档，而是根据当前任务和阶段有选择地读取相关文档
2. **分阶段与非分阶段文档区域**：区分需要在特定阶段读取的文档和需要在所有阶段都读取的基础文档
3. **功能导向的文档组织**：按功能组织文档，每个功能有唯一ID和创建日期，便于查找和关联
4. **记忆刷新机制**：设计明确的记忆刷新触发点和机制，确保AI在需要时能够获取最新信息
5. **模式感知**：让AI能够识别当前处于RIPER-5的哪个模式，并据此读取相应文档
6. **上下文压缩**：优化文档内容的提取和压缩，确保在有限的上下文窗口中包含最重要的信息

### 2.3 与RIPER-5协议的关系

AI-DEV-FLOW规则系统与RIPER-5协议无缝集成，支持：

- **模式映射**：将RIPER-5的模式映射到文档读取策略
- **思维原则应用**：根据当前模式应用相应的思维原则
- **模式转换规则**：定义模式转换的触发条件和转换行为

## 3. 文档组织结构

### 3.1 目录结构概览

AI-DEV-FLOW规则系统的文档组织结构如下：

```
docs/
├── ai-rules/                # AI规则文档
│   ├── README.md            # 规则系统概述
│   ├── ai-dev-flow-rules.md # 规则系统整体结构
│   ├── document-reader-memory-refresh.md # 文档读取和记忆刷新规则
│   └── context-compression-riper5-integration.md # 上下文压缩和RIPER-5协议集成
│
├── features/                # 功能文档
│   ├── [功能ID-功能名称-日期]/ # 功能目录
│   │   ├── README.md        # 功能概述和文档索引
│   │   ├── requirements/    # 需求文档
│   │   ├── design/          # 设计文档
│   │   ├── plan/            # 实施计划
│   │   ├── api/             # API文档
│   │   └── test/            # 测试文档
│   └── ...
│
├── common/                  # 共享文档
│   ├── architecture/        # 架构设计相关文档
│   ├── best-practices/      # 最佳实践指南
│   ├── middleware/          # 中间件集成文档
│   ├── protocols/           # 协议和接口规范
│   ├── security/            # 安全相关文档
│   ├── troubleshooting/     # 故障排除指南
│   └── templates/           # 文档模板
│
├── guides/                  # 使用指南
│   └── ai-dev-flow-usage-guide.md # 本文档
│
└── feature-document-map.md  # 功能文档映射表
```

### 3.2 文档区域划分

AI-DEV-FLOW的文档区域划分为以下几类：

#### 3.2.1 基础文档区域（非分阶段）

这些文档在所有任务和阶段都应该被读取，提供基础上下文：

- **项目上下文**：项目的基本信息、目标和约束
- **决策日志**：重要决策及其理由
- **系统模式**：系统使用的设计模式和架构模式

#### 3.2.2 任务导向文档区域（分阶段）

这些文档根据RIPER-5的不同阶段有选择地读取：

- **RESEARCH阶段**：开发日志、架构文档、相关计划文档
- **INNOVATE阶段**：提示模板、成功会话记录、设计文档
- **PLAN阶段**：开发指南、相关计划文档、提示模板
- **EXECUTE阶段**：代码实现提示、测试要求、相关会话记录
- **REVIEW阶段**：代码审查提示、最佳实践

#### 3.2.3 功能文档区域

这些文档按功能组织，每个功能有专门的目录：

- **功能目录**：`docs/features/[功能ID-功能名称-日期]/`
- **功能README**：功能概述和文档索引
- **需求文档**：功能的需求规格
- **设计文档**：功能的设计方案
- **实施计划**：功能的实施步骤
- **API文档**：功能的API接口
- **测试文档**：功能的测试用例和结果

### 3.3 文档映射表

文档映射表是查找特定功能文档的中央索引，位于`docs/feature-document-map.md`。它包含以下信息：

- 功能ID
- 功能名称
- 项目代号
- 创建日期
- 状态
- 文档目录链接

示例：

| 功能ID | 功能名称 | 项目代号 | 创建日期 | 状态 | 文档目录 |
|-------|---------|---------|---------|------|---------|
| F001 | 配置中心 | XKC-CORE | 2023-05-01 | 已完成 | [链接](../features/F001-配置中心-20230501) |
| F002 | 异常处理 | XKC-CORE | 2023-05-02 | 进行中 | [链接](../features/F002-异常处理-20230502) |

## 4. 使用流程

### 4.1 项目初始设置

1. **创建基础目录结构**
   ```bash
   mkdir -p docs/ai-rules docs/features docs/common docs/guides
   ```

2. **创建文档映射表**
   ```bash
   touch docs/feature-document-map.md
   ```

3. **初始化文档映射表内容**
   ```markdown
   # 功能文档映射表

   本文档记录了功能ID与文档目录的映射关系，用于快速定位特定功能的文档。

   ## 功能-文档映射

   | 功能ID | 功能名称 | 项目代号 | 创建日期 | 状态 | 文档目录 |
   |-------|---------|---------|---------|------|---------|
   ```

4. **创建共享文档目录结构**
   ```bash
   mkdir -p docs/common/{architecture,best-practices,middleware,protocols,security,troubleshooting,templates}
   mkdir -p docs/common/architecture/{patterns,principles,diagrams}
   mkdir -p docs/common/best-practices/{coding-standards,error-handling,logging,testing}
   mkdir -p docs/common/middleware/{postgresql,rabbitmq,valkey}
   mkdir -p docs/common/protocols/{grpc,rest,internal}
   mkdir -p docs/common/security/{authentication,authorization,data-protection}
   mkdir -p docs/common/troubleshooting/{common-issues,debugging,performance}
   ```

5. **创建共享文档映射表**
   ```bash
   touch docs/common/document-map.md
   ```

### 4.2 功能开发流程

1. **创建新功能目录**
   ```bash
   # 格式：功能ID-功能名称-日期
   mkdir -p docs/features/F001-配置中心-20230501/{requirements,design,plan,api,test}
   ```

2. **创建功能README**
   ```bash
   touch docs/features/F001-配置中心-20230501/README.md
   ```

3. **更新文档映射表**
   在`docs/feature-document-map.md`中添加新功能的条目。

4. **按RIPER-5流程创建和更新文档**
   - RESEARCH阶段：创建需求文档
   - INNOVATE阶段：创建设计文档
   - PLAN阶段：创建实施计划
   - EXECUTE阶段：创建API文档和代码
   - REVIEW阶段：创建测试文档和审查结果

### 4.3 文档更新和维护

1. **更新文档内容**
   - 保持文档的最新状态
   - 记录重要的变更
   - 更新文档的版本和日期

2. **更新文档映射表**
   - 当功能状态变化时，更新文档映射表中的状态
   - 当添加新文档时，更新相关链接

3. **定期审查文档**
   - 检查文档的准确性和完整性
   - 移除过时的文档
   - 合并相关的文档

### 4.4 项目代号与功能查找

1. **通过项目代号查找功能**
   - 在`docs/feature-document-map.md`中查找项目代号列
   - 找出所有匹配该项目代号的功能ID和功能名称
   - 根据功能ID查找对应的文档目录

2. **模糊匹配处理**
   - 忽略大小写差异（如"xkc-core"与"XKC-CORE"视为匹配）
   - 允许部分匹配（如"CORE"可匹配"XKC-CORE"）
   - 允许连字符差异（如"XKCCORE"与"XKC-CORE"视为匹配）

## 5. 最佳实践

### 5.1 文档命名和组织建议

- **使用一致的命名约定**
  - 功能目录：`[功能ID-功能名称-日期]`
  - 文档文件：使用小写字母和连字符，如`api-design.md`

- **保持目录结构清晰**
  - 每个功能有自己的目录
  - 共享文档按类别组织
  - 避免深层嵌套

- **使用README文件**
  - 每个目录都应有README.md文件
  - README应提供目录内容的概述和索引

### 5.2 文档内容编写建议

- **使用标准的Markdown格式**
  - 清晰的标题层级
  - 适当使用列表、表格和代码块
  - 添加必要的链接和引用

- **包含元数据**
  - 文档ID
  - 创建日期和更新日期
  - 版本和状态
  - 作者和贡献者

- **保持内容简洁明了**
  - 使用简单直接的语言
  - 避免冗长的描述
  - 使用图表和示例说明复杂概念

### 5.3 文档更新频率建议

- **基础文档**：当项目基础信息变化时更新
- **功能文档**：随着功能开发的进展持续更新
- **共享文档**：当相关标准或最佳实践变化时更新
- **文档映射表**：当添加、移除或更改功能状态时更新

### 5.4 常见问题和解决方案

- **文档过时**：定期审查和更新文档，设置文档过期提醒
- **文档不一致**：使用模板和标准格式，进行交叉引用检查
- **文档难以查找**：维护准确的文档映射表，使用一致的命名约定
- **文档过于冗长**：使用摘要和目录，将详细内容放在附录中

## 6. 实际案例

### 6.1 创建新功能文档的案例

假设我们要创建一个名为"用户认证"的新功能，项目代号为"XKC-AUTH"：

1. **分配功能ID**：F004

2. **创建功能目录**
   ```bash
   mkdir -p docs/features/F004-用户认证-20230504/{requirements,design,plan,api,test}
   ```

3. **创建功能README**
   ```markdown
   # 用户认证

   - **功能ID**: F004
   - **创建日期**: 2023-05-04
   - **状态**: 计划中
   - **负责人**: 张三

   ## 功能概述

   用户认证功能提供安全的用户身份验证机制，支持多种认证方式，包括用户名密码、手机验证码和第三方OAuth认证。

   ## 文档索引

   ### 需求文档
   - [需求规格](./requirements/requirements-spec.md)

   ### 设计文档
   - [设计方案](./design/design-doc.md)

   ### 实施计划
   - [实施计划](./plan/implementation-plan.md)

   ### API文档
   - [API接口](./api/api-spec.md)

   ## 相关功能
   - [F005-权限控制](../F005-权限控制-20230505)

   ## 变更历史
   | 版本 | 日期 | 变更内容 | 变更人 |
   |-----|-----|---------|-------|
   | 1.0 | 2023-05-04 | 初始版本 | 张三 |
   ```

4. **更新文档映射表**
   在`docs/feature-document-map.md`中添加：
   ```markdown
   | F004 | 用户认证 | XKC-AUTH | 2023-05-04 | 计划中 | [链接](./features/F004-用户认证-20230504) |
   ```

### 6.2 更新现有文档的案例

假设我们要更新"用户认证"功能的状态从"计划中"到"进行中"：

1. **更新功能README**
   修改状态和变更历史：
   ```markdown
   - **状态**: 进行中

   ## 变更历史
   | 版本 | 日期 | 变更内容 | 变更人 |
   |-----|-----|---------|-------|
   | 1.0 | 2023-05-04 | 初始版本 | 张三 |
   | 1.1 | 2023-05-10 | 更新状态为进行中 | 李四 |
   ```

2. **更新文档映射表**
   在`docs/feature-document-map.md`中修改：
   ```markdown
   | F004 | 用户认证 | XKC-AUTH | 2023-05-04 | 进行中 | [链接](./features/F004-用户认证-20230504) |
   ```

### 6.3 使用项目代号查找功能的案例

假设我们要查找所有与"XKC-AUTH"项目代号相关的功能：

1. **查询文档映射表**
   在`docs/feature-document-map.md`中查找"XKC-AUTH"列，找到：
   ```
   | F004 | 用户认证 | XKC-AUTH | 2023-05-04 | 进行中 | [链接](./features/F004-用户认证-20230504) |
   | F005 | 权限控制 | XKC-AUTH | 2023-05-05 | 计划中 | [链接](./features/F005-权限控制-20230505) |
   ```

2. **访问相关功能文档**
   根据链接访问`docs/features/F004-用户认证-20230504`和`docs/features/F005-权限控制-20230505`目录。

## 7. 参考资料

### 7.1 相关文档链接

- [AI-DEV-FLOW规则系统概述](../ai-rules/README.md) (G005)
- [AI-DEV-FLOW规则系统整体结构](../ai-rules/ai-dev-flow-rules.md) (G002)
- [文档读取和记忆刷新规则](../ai-rules/document-reader-memory-refresh.md) (G003)
- [上下文压缩和RIPER-5协议集成](../ai-rules/context-compression-riper5-integration.md) (G004)

### 7.2 工具和资源推荐

- **Markdown编辑器**：VS Code、Typora、Obsidian
- **图表工具**：draw.io、PlantUML、Mermaid
- **版本控制**：Git
- **文档生成**：MkDocs、Docusaurus、VuePress

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 1.0 | 2023-05-08 | 初始版本 | AI助手 |
