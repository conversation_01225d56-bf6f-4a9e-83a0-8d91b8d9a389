# 阶段0第1步：基于纯算法的设计文档健康检查MVP

## 1. 概述

### 1.1 目标
实现一个基于纯算法的设计文档健康检查系统，作为阶段0的第一步。该系统将通过三个核心检查器自动检测设计文档中的常见问题。

### 1.2 范围
- 实现三个核心算法检查器
- 集成到现有的DesignValidationWorkflow框架
- 生成结构化的健康检查报告
- 提供完整的测试验证方案
- 修复前端扫描功能

## 2. 系统架构

### 2.1 整体架构

```mermaid
graph TD
    A[DesignValidationWorkflow] --> B[StructuralIntegrityChecker]
    A --> C[ConstraintConsistencyChecker]
    A --> D[TextualDependencyChecker]
    A --> E[SpecificationConformityChecker]
    A --> F[CompletenessChecker]
    
    subgraph "检查器层"
        B
        C
        D
        E
        F
    end
    
    subgraph "集成层"
        A
    end
    
    G[设计文档] --> A
    A --> H[健康检查报告]
```

### 2.2 核心组件

#### 2.2.1 ConstraintConsistencyChecker（约束一致性检查器）
**职责**：检查量化约束的逻辑一致性
**检测目标**：
- 最大值小于最小值的逻辑矛盾
- 约束表达式的语法正确性

#### 2.2.2 StructuralIntegrityChecker（结构完整性检查器）
**职责**：检查文档中Mermaid图表的结构完整性
**检测目标**：
- 图表中的循环依赖关系
- 图表语法的正确性

#### 2.2.3 TextualDependencyChecker（文本依赖检查器）
**职责**：检查自然语言描述中的循环依赖
**检测目标**：
- 文本中描述的组件间循环依赖
- 依赖关系的一致性

### 2.3 组件交互流程

```mermaid
sequenceDiagram
    participant W as DesignValidationWorkflow
    participant S as StructuralIntegrityChecker
    participant C as ConstraintConsistencyChecker
    participant T as TextualDependencyChecker
    participant SC as SpecificationConformityChecker
    participant CO as CompletenessChecker
    participant D as 设计文档
    participant R as 健康检查报告

    W->>D: 读取文档内容
    W->>S: 执行结构完整性检查
    S-->>W: 返回检查结果
    W->>C: 执行约束一致性检查
    C-->>W: 返回检查结果
    W->>T: 执行文本依赖检查
    T-->>W: 返回检查结果
    W->>SC: 执行规格符合性检查
    SC-->>W: 返回检查结果
    W->>CO: 执行完整性检查
    CO-->>W: 返回检查结果
    W->>R: 生成综合健康报告
```

## 3. 详细设计

### 3.1 数据模型

#### 3.1.1 HealthRisk（健康风险）

```python
@dataclass
class HealthRisk:
    level: Literal["Low", "Medium", "High", "Critical"]
    description: str
    checker_name: str
    context: str = ""
```

#### 3.1.2 DocumentHealthReport（文档健康报告）

```python
@dataclass
class DocumentHealthReport:
    document_path: str
    risks: List[HealthRisk]
    summary: str
    passed: bool = field(init=False)
```

### 3.2 检查器接口

#### 3.2.1 BaseChecker（基类）

```python
class BaseChecker(ABC):
    @property
    def name(self) -> str:
        return self.__class__.__name__
    
    @abstractmethod
    def check(self, document_content: str) -> List[HealthRisk]:
        pass
```

#### 3.2.2 ConstraintConsistencyChecker

```python
class ConstraintConsistencyChecker(BaseChecker):
    def check(self, document_content: str) -> List[HealthRisk]:
        # 实现约束一致性检查逻辑
        pass
```

#### 3.2.3 StructuralIntegrityChecker

```python
class StructuralIntegrityChecker(BaseChecker):
    def check(self, document_content: str) -> List[HealthRisk]:
        # 实现结构完整性检查逻辑
        pass
```

#### 3.2.4 TextualDependencyChecker

```python
class TextualDependencyChecker(BaseChecker):
    def check(self, document_content: str) -> List[HealthRisk]:
        # 实现文本依赖检查逻辑
        pass
```

### 3.3 算法实现

#### 3.3.1 约束冲突检测算法

1. 使用正则表达式匹配约束定义
2. 识别"最大"/"最小"相关的约束
3. 检查同一指标的最大值和最小值是否矛盾

```mermaid
flowchart TD
    A[开始约束检查] --> B[使用正则表达式提取约束]
    B --> C[分类约束为最大值/最小值]
    C --> D[检查同一指标的最大值是否小于最小值]
    D --> E{存在矛盾?}
    E -->|是| F[创建严重风险报告]
    E -->|否| G[继续检查其他约束]
    F --> H[返回风险列表]
    G --> I[返回空风险列表]
```

#### 3.3.2 循环依赖检测算法

1. 提取Mermaid代码块
2. 解析节点和依赖关系
3. 使用深度优先搜索检测循环依赖

```mermaid
flowchart TD
    A[开始结构检查] --> B[提取所有Mermaid图表]
    B --> C[解析图表节点和边]
    C --> D[构建依赖图]
    D --> E[使用DFS检测循环]
    E --> F{发现循环?}
    F -->|是| G[创建高风险报告]
    F -->|否| H[继续检查其他图表]
    G --> I[返回风险列表]
    H --> J[返回空风险列表]
```

#### 3.3.3 文本依赖检测算法

1. 使用正则表达式提取依赖关系描述
2. 构建依赖图
3. 检测循环依赖

```mermaid
flowchart TD
    A[开始文本依赖检查] --> B[提取文本中的依赖描述]
    B --> C[构建文本依赖图]
    C --> D[使用DFS检测循环]
    D --> E{发现循环?}
    E -->|是| F[创建中等风险报告]
    E -->|否| G[继续检查其他文本]
    F --> H[返回风险列表]
    G --> I[返回空风险列表]
```

## 4. 集成方案

### 4.1 DesignValidationWorkflow集成

```python
class DesignValidationWorkflow:
    def __init__(self, document_path: str):
        self.checkers: List[BaseChecker] = [
            StructuralIntegrityChecker(),
            ConstraintConsistencyChecker(),
            TextualDependencyChecker(),
            SpecificationConformityChecker(),
            CompletenessChecker(),
        ]
```

### 4.2 执行顺序

1. StructuralIntegrityChecker（结构检查）
2. ConstraintConsistencyChecker（约束检查）
3. TextualDependencyChecker（文本依赖检查）

### 4.3 工作流执行流程

```mermaid
flowchart TD
    A[DesignValidationWorkflow.execute] --> B[读取文档内容]
    B --> C[执行StructuralIntegrityChecker]
    C --> D[执行ConstraintConsistencyChecker]
    D --> E[执行TextualDependencyChecker]
    E --> F[执行SpecificationConformityChecker]
    F --> G[执行CompletenessChecker]
    G --> H[收集所有风险]
    H --> I[生成健康报告]
    I --> J[写入报告文件]
    J --> K[返回验证结果]
```

## 5. 输出报告

### 5.1 报告格式
生成Markdown格式的健康检查报告，包含以下内容：
- 文档基本信息
- 总体健康评分
- 按风险级别分类的风险列表
- 详细的修改建议

### 5.2 报告结构

```markdown
# 设计文档健康检查报告
## 文档信息
- 文档路径: [path]
- 检查时间: [timestamp]
## 总体评估
- 健康评分: [score]
- 检查结果: ✅ 通过 / ❌ 未通过
## 发现的风险
### 🔴 严重风险 (Critical)
1. [风险描述] (检查器: [检查器名称])
### 🟠 高风险 (High)
...
## 🛠 修改建议
...
```

### 5.3 报告生成流程

```mermaid
flowchart TD
    A[收集所有检查器的风险] --> B[按风险级别分类]
    B --> C[计算健康评分]
    C --> D[生成报告内容]
    D --> E[格式化为Markdown]
    E --> F[写入文件]
    F --> G[完成]
```

## 6. 前端扫描功能设计

### 6.1 功能概述
前端扫描功能允许用户通过点击"扫描"按钮触发设计文档的健康检查。该功能需要实现从前端到后端的完整调用链。

### 6.2 功能流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant H as HumanInputComponent
    participant A as AppManager
    participant D as DataManager
    participant B as 后端API
    participant W as DesignValidationWorkflow

    U->>H: 点击扫描按钮
    H->>H: 禁用按钮，显示扫描状态
    H->>A: 触发scan-documents事件
    A->>A: 处理事件，检查taskId有效性
    A->>D: 调用fetchData方法
    D->>B: 发送POST请求到/validation-workflow/{manager_id}
    B->>W: 启动DesignValidationWorkflow
    W->>W: 执行健康检查
    W-->>B: 返回响应
    B-->>D: 返回API响应
    D-->>A: 返回结果
    A->>A: 恢复按钮状态
```

### 6.3 前端组件交互

```
HumanInputComponent (扫描按钮)
        ↓ (triggerEvent)
    scan-documents 事件
        ↓ (window.dispatchEvent)
     AppManager (事件监听)
        ↓ (API调用)
  DataManager (fetchData)
        ↓ (HTTP请求)
    后端API端点: /validation-workflow/{manager_id}
```

### 6.4 UI设计
- 扫描按钮状态反馈（正常/禁用/扫描中）
- 扫描结果展示区域
- 错误提示和用户引导

### 6.5 前端扫描功能修复

#### 6.5.1 问题分析
当前前端扫描功能存在以下问题：
1. `HumanInputComponent` 触发了 `scan-documents` 事件，但没有相应的处理器
2. `AppManager` 没有监听 `scan-documents` 事件
3. 缺少调用后端 `/validation-workflow/{manager_id}` API 的机制

#### 6.6 前端修复方案

##### 6.6.1 AppManager事件监听
在 `AppManager` 类中添加事件监听和处理：

```javascript
class AppManager {
    // ... existing code ...
    
    /**
     * 设置组件间通信
     */
    setupComponentCommunication() {
        // 添加对 scan-documents 事件的监听
        window.addEventListener('scan-documents', (event) => {
            this.handleScanDocuments(event.detail);
        });
    }
    
    /**
     * 处理扫描文档事件
     */
    async handleScanDocuments(detail = {}) {
        try {
            // 检查是否有有效的 taskId
            if (!this.dataManager.taskId || this.dataManager.taskId === 'standalone' || this.dataManager.taskId === 'pm_v2') {
                console.warn('Invalid taskId for scanning:', this.dataManager.taskId);
                return;
            }
            
            // 调用后端 API 启动验证工作流
            const result = await this.dataManager.fetchData('validation_workflow', {
                manager_id: this.dataManager.taskId
            }, false);
            
            console.log('Validation workflow started:', result);
            
        } catch (error) {
            console.error('Failed to start validation workflow:', error);
        }
    }
}
```

##### 6.6.2 数据配置更新
在 `pm_v2_data_config.js` 中添加验证工作流的配置：

```javascript
const PM_V2_DATA_TYPE_MAPPING = {
    // ... existing configurations ...
    
    'validation_workflow': {
        endpoint: '/api/pm_v2/validation-workflow/{manager_id}',
        method: 'POST',
        cache: false,
        mockData: null
    }
};
```

##### 6.6.3 DataManager API调用支持
确保 `DataManager` 能正确处理带参数的API调用：

```javascript
class DataManager {
    // ... existing code ...
    
    /**
     * 获取API端点（支持参数替换）
     */
    getEndpoint(dataType, params = {}) {
        let endpoint = null;
        
        // 优先使用PM V2数据配置
        if (window.PM_V2_DATA_TYPE_MAPPING && window.PM_V2_DATA_TYPE_MAPPING[dataType]) {
            endpoint = window.PM_V2_DATA_TYPE_MAPPING[dataType].endpoint;
        }
        
        // 替换路径参数
        if (endpoint && params) {
            Object.keys(params).forEach(key => {
                endpoint = endpoint.replace(`{${key}}`, params[key]);
            });
        }
        
        return endpoint;
    }
}
```

### 6.7 前端用户体验优化

#### 6.7.1 扫描状态反馈
在 `HumanInputComponent` 中添加扫描状态的视觉反馈：

```javascript
class HumanInputComponent extends BaseComponent {
    // ... existing code ...
    
    handleScanningClick() {
        console.log("处理扫描点击");
        
        // 禁用扫描按钮，显示正在扫描状态
        const scanBtn = this.container.querySelector('#scan-btn');
        if (scanBtn) {
            scanBtn.disabled = true;
            scanBtn.innerHTML = '🔍 扫描中...';
        }
        
        // 触发扫描事件
        this.triggerEvent('scan-documents');
        
        // 3秒后恢复按钮状态（或等待实际完成回调）
        setTimeout(() => {
            if (scanBtn) {
                scanBtn.disabled = false;
                scanBtn.innerHTML = '🔍 扫描';
            }
        }, 3000);
    }
}
```

## 7. 测试方案

### 7.1 单元测试
为每个检查器编写独立的测试用例，验证：
- 正常情况下的检测准确性
- 边界情况的处理
- 异常情况的容错性

#### 7.1.1 ConstraintConsistencyChecker测试用例

```python
def test_constraint_consistency_checker():
    checker = ConstraintConsistencyChecker()
    
    # 测试正常情况 - 无矛盾
    doc1 = "启动时间: ≤1000ms\n启动时间: ≥500ms"
    risks = checker.check(doc1)
    assert len(risks) == 0
    
    # 测试矛盾情况 - 最大值小于最小值
    doc2 = "启动时间: ≤500ms\n启动时间: ≥1000ms"
    risks = checker.check(doc2)
    assert len(risks) == 1
    assert risks[0].level == "Critical"
    
    # 测试边界情况 - 相等值
    doc3 = "启动时间: ≤1000ms\n启动时间: ≥1000ms"
    risks = checker.check(doc3)
    assert len(risks) == 0  # 相等值不算矛盾
```

#### 7.1.2 StructuralIntegrityChecker测试用例

```python
def test_structural_integrity_checker():
    checker = StructuralIntegrityChecker()
    
    # 测试正常情况 - 无循环依赖
    doc1 = "```mermaid\ngraph TD\nA-->B\nB-->C\nC-->D\n```"
    risks = checker.check(doc1)
    assert len(risks) == 0
    
    # 测试循环依赖情况
    doc2 = "```mermaid\ngraph TD\nA-->B\nB-->C\nC-->A\n```"
    risks = checker.check(doc2)
    assert len(risks) == 1
    assert risks[0].level == "High"
```

#### 7.1.3 TextualDependencyChecker测试用例

```python
def test_textual_dependency_checker():
    checker = TextualDependencyChecker()
    
    # 测试正常情况 - 无循环依赖
    doc1 = "模块A依赖模块B\n模块B依赖模块C"
    risks = checker.check(doc1)
    assert len(risks) == 0
    
    # 测试循环依赖情况
    doc2 = "模块A依赖模块B\n模块B依赖模块A"
    risks = checker.check(doc2)
    assert len(risks) == 1
    assert risks[0].level == "Medium"
```

### 7.2 集成测试
验证检查器与DesignValidationWorkflow的集成：
- 检查器的正确注册
- 执行顺序的正确性
- 结果汇总的准确性

### 7.3 端到端测试
模拟真实使用场景，验证整个流程：
- 从文档输入到报告生成的完整流程
- 与ProjectManager的集成
- 前端展示功能
- 前后端扫描功能的完整性

### 7.4 前端测试
验证前端扫描功能的完整性：
- 扫描按钮点击事件的正确触发
- 事件传递到AppManager的正确性
- API调用的正确性
- UI状态更新的及时性
- 扫描结果展示的正确性

#### 7.4.1 前端组件测试

```javascript
// 测试HumanInputComponent的扫描功能
describe('HumanInputComponent', () => {
    test('点击扫描按钮应触发scan-documents事件', () => {
        // 模拟组件初始化
        const component = new HumanInputComponent('test-container');
        component.render();
        
        // 监听事件
        const eventSpy = jest.fn();
        window.addEventListener('scan-documents', eventSpy);
        
        // 模拟点击扫描按钮
        const scanBtn = component.container.querySelector('#scan-btn');
        scanBtn.click();
        
        // 验证事件被触发
        expect(eventSpy).toHaveBeenCalled();
    });
});

// 测试AppManager的事件处理
describe('AppManager', () => {
    test('应正确处理scan-documents事件', async () => {
        const appManager = new AppManager('test-project');
        appManager.dataManager = {
            taskId: 'test-task',
            fetchData: jest.fn().mockResolvedValue({ success: true })
        };
        
        await appManager.handleScanDocuments();
        
        expect(appManager.dataManager.fetchData).toHaveBeenCalledWith(
            'validation_workflow',
            { manager_id: 'test-task' },
            false
        );
    });
});
```

## 8. 实施计划

### 8.1 时间安排
- 设计阶段：2小时
- 实现阶段：12小时
- 测试阶段：11小时
- 文档和验证：4小时
- 前端修复：6小时
- **总计：35小时**

### 8.2 交付物
1. 三个新的检查器类文件
2. 修改后的DesignValidationWorkflow集成代码
3. 前端扫描功能修复代码
4. 完整的测试套件
5. 使用文档和测试报告

### 8.3 实施步骤

```mermaid
gantt
    title 阶段0第1步实施计划
    dateFormat  YYYY-MM-DD
    section 设计
    设计阶段          :done, des1, 2025-06-15, 2h
    section 实现
    实现检查器        :active, dev1, 2025-06-16, 6h
    集成工作流        :dev2, 2025-06-17, 3h
    前端修复          :dev3, 2025-06-18, 3h
    section 测试
    单元测试          :test1, 2025-06-19, 4h
    集成测试          :test2, 2025-06-20, 3h
    端到端测试        :test3, 2025-06-21, 4h
    section 文档
    文档编写与验证    :doc1, 2025-06-22, 4h
```

### 8.4 风险与缓解措施

| 风险 | 影响 | 缓解措施 |
|------|------|----------|
| 算法检测准确性不足 | 检查结果不可靠 | 增加测试用例覆盖，优化正则表达式 |
| 前端集成失败 | 用户无法使用扫描功能 | 提前验证API接口，编写前端测试 |
| 性能问题 | 检查速度慢 | 优化算法实现，增加异步处理 |
| 错误处理不完善 | 系统崩溃 | 增加异常捕获和日志记录 |

### 8.5 错误处理机制

#### 8.5.1 检查器错误处理

```python
class BaseChecker(ABC):
    @abstractmethod
    def check(self, document_content: str) -> List[HealthRisk]:
        """
        执行检查并返回发现的健康风险列表。
        """
        try:
            # 实际检查逻辑
            return self._perform_check(document_content)
        except Exception as e:
            # 记录错误日志
            logger.error(f"检查器 {self.name} 执行时发生错误: {e}")
            # 返回错误风险而不是抛出异常
            return [HealthRisk(
                level="Critical",
                description=f"检查器 {self.name} 内部错误: {str(e)}",
                checker_name=self.name
            )]
```

#### 8.5.2 工作流错误处理

```python
class DesignValidationWorkflow:
    def execute(self) -> bool:
        try:
            # 读取文档
            document_content = self._read_document(self.document_path)
            if document_content is None:
                return False
            
            # 执行检查
            all_risks = []
            for checker in self.checkers:
                try:
                    risks = checker.check(document_content)
                    all_risks.extend(risks)
                except Exception as e:
                    # 单个检查器失败不影响其他检查器
                    all_risks.append(HealthRisk(
                        level="Critical",
                        description=f"检查器 {checker.name} 执行失败: {str(e)}",
                        checker_name=self.WORKFLOW_ENGINE_NAME
                    ))
            
            # 生成报告
            report = DocumentHealthReport(
                document_path=self.document_path,
                risks=all_risks,
                summary=f"共发现 {len(all_risks)} 个潜在健康风险。"
            )
            
            self._write_report(report)
            return report.passed
            
        except Exception as e:
            # 记录工作流级别错误
            logger.error(f"设计验证工作流执行失败: {e}")
            return False
```

#### 8.5.3 前端错误处理

```javascript
class AppManager {
    async handleScanDocuments(detail = {}) {
        try {
            // 检查taskId有效性
            if (!this.dataManager.taskId || this.dataManager.taskId === 'standalone' || this.dataManager.taskId === 'pm_v2') {
                throw new Error('无效的taskId，无法启动扫描');
            }
            
            // 调用后端API
            const result = await this.dataManager.fetchData('validation_workflow', {
                manager_id: this.dataManager.taskId
            }, false);
            
            if (!result.success) {
                throw new Error(result.error || '后端API调用失败');
            }
            
            console.log('Validation workflow started:', result);
            
        } catch (error) {
            console.error('Failed to start validation workflow:', error);
            // 显示用户友好的错误提示
            this.showErrorNotification({
                message: '扫描启动失败',
                detail: error.message
            });
        }
    }
}
```

## 9. 复杂度分析

### 9.1 复杂度级别确定
根据任务特征，本任务属于**L2 Medium Changes**级别：
- 跨模块实现：涉及前端组件、后端API、工作流引擎等多个模块
- 中等实现规模：约1000行代码
- 需要集成测试：前后端集成验证
- 中等技术风险：算法准确性、性能优化

### 9.2 技术复杂度评估

| 维度 | 评估 | 说明 |
|------|------|------|
| 算法复杂度 | 中等 | 需要实现图遍历、正则匹配等算法 |
| 集成复杂度 | 中等 | 前后端集成，多组件协调 |
| 测试复杂度 | 中等 | 需要单元测试、集成测试、端到端测试 |
| 部署复杂度 | 低 | 无需基础设施变更 |

## 10. 性能与可扩展性考虑

### 10.1 性能优化策略

1. **缓存机制**：对已检查的文档内容进行缓存，避免重复解析
2. **异步处理**：使用多线程处理大型文档
3. **增量检查**：只对文档变更部分进行检查

### 10.2 可扩展性设计

1. **插件化检查器**：通过继承BaseChecker可以轻松添加新的检查器
2. **配置化规则**：检查规则可通过配置文件调整
3. **模块化解耦**：各检查器独立实现，互不影响