# Project Manager V2 组件实现细节文档

## 文档概述

本文档详细描述了Project Manager V2九宫格界面中每个组件的具体实现细节、数据绑定方式和交互逻辑，为前端开发和后端对接提供精确的技术指导。

---

## 区域1-2：项目进度监控组件详解

### 1. 四阶段流程进度条组件

#### HTML结构
```html
<div class="stage-progress">
    <div class="stage-item stage-zero-highlight current">
        <span class="status-indicator status-thinking"></span>
        <span>阶段零：标准化与预验证</span>
        <span class="badge badge-reliability">可靠性基石</span>
    </div>
    <!-- 其他阶段... -->
</div>
```

#### 数据绑定逻辑
```javascript
function updateStageProgress(stageData) {
    const stages = document.querySelectorAll('.stage-item');
    stages.forEach((stage, index) => {
        const indicator = stage.querySelector('.status-indicator');
        
        if (index === stageData.current_stage.stage_number) {
            stage.classList.add('current');
            indicator.className = `status-indicator ${stageData.current_stage.status_indicator}`;
        } else if (index < stageData.current_stage.stage_number) {
            stage.classList.add('completed');
            indicator.className = 'status-indicator status-active';
        } else {
            stage.classList.add('pending');
            indicator.className = 'status-indicator status-pending';
        }
    });
}
```

#### 后端数据要求
```json
{
    "current_stage": {
        "stage_number": 0,
        "stage_name": "标准化与预验证",
        "status": "in_progress",
        "status_indicator": "status-thinking",
        "progress_percentage": 25
    }
}
```

### 2. 阶段零专用指标面板

#### 组件特色
- **突出显示**: 蓝色渐变背景，强调V4.2的"阶段零"重要性
- **实时更新**: WebSocket推送更新指标数值
- **进度可视化**: 每个指标都有对应的进度条

#### 数据更新函数
```javascript
function updateStageZeroMetrics(metricsData) {
    const metrics = [
        { id: 'pre-validation-pass-rate', key: 'pre_validation_pass_rate', suffix: '%' },
        { id: 'conflict-prevention-count', key: 'conflict_prevention_count', suffix: '' },
        { id: 'schema-validation', key: 'schema_validation_passed', suffix: '' }
    ];
    
    metrics.forEach(metric => {
        const valueElement = document.getElementById(metric.id);
        const progressBar = valueElement.nextElementSibling.querySelector('.progress-fill');
        
        const value = metricsData[metric.key];
        valueElement.textContent = value + metric.suffix;
        
        // 计算进度条宽度
        let percentage = 0;
        if (metric.key === 'pre_validation_pass_rate') {
            percentage = value;
        } else if (metric.key === 'conflict_prevention_count') {
            percentage = Math.min(value * 25, 100); // 假设最大4个冲突
        } else if (metric.key === 'schema_validation_passed') {
            percentage = (value / metricsData.schema_validation_total) * 100;
        }
        
        progressBar.style.width = `${percentage}%`;
    });
}
```

### 3. 关键指标统计组件

#### 动态数据绑定
```javascript
function updateKeyMetrics(keyMetrics) {
    const metricMappings = [
        { 
            id: 'atomic-constraints-count', 
            value: keyMetrics.atomic_constraints_discovered,
            progressWidth: (keyMetrics.atomic_constraints_discovered / 40) * 100 // 假设目标40个
        },
        {
            id: 'global-contracts-count',
            value: keyMetrics.global_contracts_generated,
            progressWidth: (keyMetrics.global_contracts_generated / 25) * 100
        },
        {
            id: 'processed-docs-count',
            value: `${keyMetrics.documents_processed}/${keyMetrics.documents_total}`,
            progressWidth: (keyMetrics.documents_processed / keyMetrics.documents_total) * 100
        },
        {
            id: 'current-reliability-score',
            value: `${keyMetrics.current_reliability_score}%`,
            progressWidth: keyMetrics.current_reliability_score
        }
    ];
    
    metricMappings.forEach(mapping => {
        const element = document.getElementById(mapping.id);
        const progressBar = element.closest('.metric-item').querySelector('.progress-fill');
        
        element.textContent = mapping.value;
        progressBar.style.width = `${mapping.progressWidth}%`;
        
        // 根据数值设置颜色
        if (mapping.progressWidth >= 80) {
            progressBar.className = 'progress-fill success';
        } else if (mapping.progressWidth >= 60) {
            progressBar.className = 'progress-fill info';
        } else {
            progressBar.className = 'progress-fill warning';
        }
    });
}
```

---

## 区域3：风险评估组件详解

### 1. 圆形可靠性评分环

#### SVG动画实现
```javascript
function updateCircularProgress(score, status) {
    const circle = document.getElementById('reliability-circle');
    const valueElement = document.getElementById('reliability-score');
    
    // 计算stroke-dashoffset (226是圆周长)
    const offset = 226 - (226 * score / 100);
    
    // 设置动画
    circle.style.strokeDashoffset = offset;
    
    // 根据分数设置颜色
    let colorClass = 'success';
    if (score < 60) colorClass = 'danger';
    else if (score < 80) colorClass = 'warning';
    
    circle.className = `progress-circle progress-bar-circle ${colorClass}`;
    valueElement.textContent = `${score}%`;
    
    // 添加数据更新动画
    valueElement.classList.add('data-update');
    setTimeout(() => valueElement.classList.remove('data-update'), 800);
}
```

### 2. 风险预防措施面板

#### 动态风险项生成
```javascript
function updatePreventionPanel(preventedRisks) {
    const container = document.querySelector('.risk-prevention-panel');
    const itemsContainer = container.querySelector('.prevention-items') || 
                          (() => {
                              const div = document.createElement('div');
                              div.className = 'prevention-items';
                              container.appendChild(div);
                              return div;
                          })();
    
    itemsContainer.innerHTML = '';
    
    preventedRisks.forEach(risk => {
        const item = document.createElement('div');
        item.className = 'prevention-item';
        item.innerHTML = `
            <div class="risk-header">
                <span class="risk-badge ${risk.level}">${risk.level.toUpperCase()}</span>
                <span class="prevention-status ${risk.prevention_status}">${getStatusText(risk.prevention_status)}</span>
            </div>
            <div class="risk-description">${risk.description}</div>
            <div class="prevention-detail">
                <span class="prevention-label">预防机制:</span>
                <span>${risk.prevention_mechanism}</span>
            </div>
        `;
        itemsContainer.appendChild(item);
    });
}

function getStatusText(status) {
    const statusMap = {
        'prevented': '已预防',
        'monitoring': '监控中',
        'resolved': '已解决'
    };
    return statusMap[status] || status;
}
```

---

## 区域5：算法思维组件详解

### 1. AI-算法协同展示

#### 协同状态更新
```javascript
function updateAIAlgorithmCollaboration(collaborationData) {
    const aiRole = document.querySelector('.ai-role-compact');
    const algorithmRole = document.querySelector('.algorithm-role-compact');
    const statusElement = document.querySelector('.validation-status-compact .check-item');
    
    aiRole.textContent = collaborationData.ai_role;
    algorithmRole.textContent = collaborationData.algorithm_component;
    
    // 更新状态
    const statusIcon = statusElement.querySelector('.check-icon');
    const statusText = statusElement.querySelector('span:last-child');
    
    if (collaborationData.current_status === 'pre_validation_passed') {
        statusIcon.textContent = '✓';
        statusElement.className = 'check-item success';
        statusText.textContent = `预验证通过 - ${collaborationData.result}`;
    } else if (collaborationData.current_status === 'processing') {
        statusIcon.textContent = '⏳';
        statusElement.className = 'check-item info';
        statusText.textContent = '正在处理...';
    }
}
```

### 2. 算法思维日志

#### 日志条目动态添加
```javascript
function addAlgorithmLogEntry(logData) {
    const container = document.getElementById('process-log');
    
    const logEntry = document.createElement('div');
    logEntry.className = 'log-entry expandable';
    logEntry.style.cssText = 'cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;';
    
    logEntry.onclick = () => showLogDetail(logEntry, logData.id);
    logEntry.onmouseover = () => logEntry.style.backgroundColor = '#393B40';
    logEntry.onmouseout = () => logEntry.style.backgroundColor = 'transparent';
    
    logEntry.innerHTML = `
        [${logData.timestamp}] ${logData.message}
        ${logData.expandable ? `
        <span class="log-arrows">
            <span class="arrow-ai-comm" onclick="event.stopPropagation(); toggleLogDetail(this, 'ai-comm', '${logData.id}')"></span>
            <span class="arrow-py-ops" onclick="event.stopPropagation(); toggleLogDetail(this, 'py-ops', '${logData.id}')"></span>
        </span>
        ` : ''}
    `;
    
    container.appendChild(logEntry);
    container.scrollTop = container.scrollHeight;
}
```

---

## 区域6：约束审查组件详解

### AtomicConstraint详细展示

#### 完整结构渲染
```javascript
function renderConstraintDetail(constraintData) {
    const container = document.getElementById('constraint-review-content');
    
    // 生成参数树HTML
    const paramsHtml = Object.entries(constraintData.params).map(([key, value]) => 
        `<div class="param-node">
            <span class="param-key">${key}</span>
            <span class="param-value">${formatParamValue(value)}</span>
        </div>`
    ).join('');
    
    // 生成血统关系HTML
    const lineageHtml = `
        <div class="lineage-item">
            <span class="lineage-label">parent_id:</span>
            <span class="lineage-value">${constraintData.parent_id || 'null (根约束)'}</span>
        </div>
        <div class="lineage-item">
            <span class="lineage-label">source_block_id:</span>
            <span class="lineage-value">${constraintData.source_block_id}</span>
        </div>
        ${constraintData.lineage && constraintData.lineage.children_count > 0 ? `
        <div class="lineage-item">
            <span class="lineage-label">children_count:</span>
            <span class="lineage-value">${constraintData.lineage.children_count}</span>
        </div>
        ` : ''}
    `;
    
    container.innerHTML = `
        <div class="constraint-detail-enhanced">
            <div class="constraint-header">
                <span class="constraint-id">${constraintData.id}</span>
                <span class="constraint-category category-${constraintData.category.replace('_', '-')}">${constraintData.category}</span>
            </div>
            
            <div class="constraint-section">
                <div class="section-title">🔧 核心字段</div>
                <div class="field-grid">
                    <div class="field-item">
                        <span class="field-label">category:</span>
                        <span class="field-value">${constraintData.category}</span>
                    </div>
                    <div class="field-item">
                        <span class="field-label">type:</span>
                        <span class="field-value">${constraintData.type}</span>
                    </div>
                </div>
            </div>
            
            <div class="constraint-section">
                <div class="section-title">📦 参数结构 (params)</div>
                <div class="params-tree">${paramsHtml}</div>
            </div>
            
            <div class="constraint-section">
                <div class="section-title">🔗 血统关系</div>
                <div class="lineage-info">${lineageHtml}</div>
            </div>
            
            <div class="constraint-section">
                <div class="section-title">📝 描述</div>
                <div style="background: rgba(255, 255, 255, 0.02); padding: 0.5rem; border-radius: 4px; font-size: 0.85rem; line-height: 1.4;">
                    ${constraintData.description}
                </div>
            </div>
        </div>
    `;
}

function formatParamValue(value) {
    if (typeof value === 'object') {
        return JSON.stringify(value, null, 2);
    }
    return String(value);
}
```

---

## 区域7：知识库可视化组件详解

### 1. 约束节点动态生成

#### 节点创建和定位
```javascript
function renderKnowledgeGraph(graphData) {
    const container = document.getElementById('knowledge-graph');
    
    // 清除现有节点（保留提示框）
    const existingNodes = container.querySelectorAll('.constraint-node, .constraint-connection');
    existingNodes.forEach(node => node.remove());
    
    // 创建节点
    graphData.nodes.forEach(nodeData => {
        const node = document.createElement('div');
        node.className = `constraint-node ${getCategoryClass(nodeData.category)} ${nodeData.is_forked ? 'forked' : ''}`;
        node.style.top = `${nodeData.position.y}px`;
        node.style.left = `${nodeData.position.x}px`;
        node.setAttribute('data-category', nodeData.category);
        node.setAttribute('data-parent-id', nodeData.parent_id || '');
        
        node.onclick = () => showConstraintDetail(nodeData.id);
        node.onmouseenter = () => showTooltip(node, nodeData.description);
        node.onmouseleave = () => hideTooltip();
        
        node.innerHTML = `
            ${nodeData.id}
            <div class="node-label">${getCategoryLabel(nodeData.category)}</div>
            ${nodeData.is_forked ? `<div class="fork-indicator">↗ ${nodeData.parent_id}</div>` : ''}
        `;
        
        container.appendChild(node);
    });
    
    // 创建连接线
    graphData.connections.forEach(connection => {
        const line = document.createElement('div');
        line.className = `constraint-connection ${connection.type}`;
        
        // 计算连接线位置和角度
        const fromNode = graphData.nodes.find(n => n.id === connection.from);
        const toNode = graphData.nodes.find(n => n.id === connection.to);
        
        if (fromNode && toNode) {
            const { top, left, width, transform } = calculateConnectionLine(fromNode.position, toNode.position);
            line.style.top = `${top}px`;
            line.style.left = `${left}px`;
            line.style.width = `${width}px`;
            line.style.transform = transform;
        }
        
        container.appendChild(line);
    });
}

function getCategoryClass(category) {
    const classMap = {
        'boundary_condition': 'boundary',
        'constraint': 'local',
        'guardrail': 'guardrail',
        'state_machine': 'state-machine'
    };
    return classMap[category] || 'local';
}

function getCategoryLabel(category) {
    const labelMap = {
        'boundary_condition': '边界条件',
        'constraint': '约束',
        'guardrail': '护栏',
        'state_machine': '状态机'
    };
    return labelMap[category] || category;
}

function calculateConnectionLine(fromPos, toPos) {
    const dx = toPos.x - fromPos.x;
    const dy = toPos.y - fromPos.y;
    const distance = Math.sqrt(dx * dx + dy * dy);
    const angle = Math.atan2(dy, dx) * 180 / Math.PI;
    
    return {
        top: fromPos.y + 25, // 节点中心
        left: fromPos.x + 25,
        width: distance,
        transform: `rotate(${angle}deg)`
    };
}
```

### 2. 帮助弹窗交互

#### 智能定位和事件管理
```javascript
function showKnowledgeBaseHelp(event) {
    event.stopPropagation();
    
    const popup = document.getElementById('knowledge-help-popup');
    const helpIcon = event.currentTarget;
    
    if (!popup || !helpIcon) return;
    
    // 智能定位：确保弹窗在可视区域内
    const knowledgeArea = helpIcon.closest('.grid-area');
    const areaRect = knowledgeArea.getBoundingClientRect();
    const popupRect = popup.getBoundingClientRect();
    
    let top = '2rem';
    let right = '1rem';
    
    // 如果弹窗会超出右边界，调整到左侧
    if (areaRect.right - 320 < areaRect.left) { // 320是弹窗宽度
        right = 'auto';
        popup.style.left = '1rem';
    }
    
    // 如果弹窗会超出下边界，调整到上方
    if (areaRect.bottom - 400 < areaRect.top) { // 400是弹窗高度
        top = 'auto';
        popup.style.bottom = '2rem';
    }
    
    popup.style.top = top;
    popup.style.right = right;
    popup.classList.add('show');
    
    // 延迟添加全局监听器，避免立即触发
    setTimeout(() => {
        document.addEventListener('click', handleOutsideClick);
    }, 100);
}
```

---

## 区域9：交付结果组件详解

### 处理统计进度条

#### 多维度统计展示
```javascript
function updateProcessingStatistics(statistics) {
    const statMappings = [
        {
            id: 'processed-docs',
            value: `${statistics.documents_processed.current}/${statistics.documents_processed.total}`,
            percentage: statistics.documents_processed.percentage,
            colorClass: 'info'
        },
        {
            id: 'total-constraints',
            value: statistics.constraints_generated.count,
            percentage: statistics.constraints_generated.percentage,
            colorClass: 'success'
        },
        {
            id: 'total-risks',
            value: statistics.risks_identified.count,
            percentage: statistics.risks_identified.percentage,
            colorClass: 'warning'
        },
        {
            id: 'total-time',
            value: statistics.processing_time.formatted,
            percentage: statistics.processing_time.percentage,
            colorClass: 'custom'
        }
    ];
    
    statMappings.forEach(mapping => {
        const valueElement = document.getElementById(mapping.id);
        const progressBar = valueElement.closest('.metric-item').querySelector('.progress-fill');
        
        valueElement.textContent = mapping.value;
        progressBar.style.width = `${mapping.percentage}%`;
        
        if (mapping.colorClass === 'custom') {
            progressBar.style.background = 'linear-gradient(90deg, #9C27B0, #BA68C8)';
        } else {
            progressBar.className = `progress-fill ${mapping.colorClass}`;
        }
    });
}
```

---

## WebSocket事件处理统一框架

### 事件分发器
```javascript
class WebSocketEventDispatcher {
    constructor() {
        this.handlers = new Map();
        this.setupWebSocket();
    }
    
    setupWebSocket() {
        this.ws = new WebSocket(`ws://localhost:25526/ws/project/${currentProjectId}`);
        
        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.dispatch(data.type, data.data);
        };
    }
    
    register(eventType, handler) {
        if (!this.handlers.has(eventType)) {
            this.handlers.set(eventType, []);
        }
        this.handlers.get(eventType).push(handler);
    }
    
    dispatch(eventType, data) {
        const handlers = this.handlers.get(eventType) || [];
        handlers.forEach(handler => {
            try {
                handler(data);
            } catch (error) {
                console.error(`Error in handler for ${eventType}:`, error);
            }
        });
    }
}

// 初始化事件分发器
const eventDispatcher = new WebSocketEventDispatcher();

// 注册各区域的事件处理器
eventDispatcher.register('stage_progress_update', updateStageProgress);
eventDispatcher.register('metric_update', updateKeyMetrics);
eventDispatcher.register('risk_assessment_update', updateRiskAssessment);
eventDispatcher.register('manager_status_update', updateManagerStatus);
eventDispatcher.register('algorithm_log_entry', addAlgorithmLogEntry);
eventDispatcher.register('constraint_created', handleConstraintCreated);
eventDispatcher.register('knowledge_graph_update', renderKnowledgeGraph);
eventDispatcher.register('deliverable_ready', updateDeliverables);
```

---

## 性能优化策略

### 1. 虚拟滚动（大量日志条目）
### 2. 防抖更新（频繁的进度更新）
### 3. 增量渲染（知识库节点）
### 4. 内存管理（WebSocket事件清理）

这些组件实现细节确保了前后端的精确对接和高性能的用户体验。
