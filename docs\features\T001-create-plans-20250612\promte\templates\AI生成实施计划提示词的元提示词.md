# AI生成实施计划提示词的元提示词

## 📋 立即执行指令

**当用户以以下格式提供指令时，立即按照本元提示词执行：**

```
设计文档：{设计文档路径}
按照提示原提示词：docs\ai-memory\templates\AI生成实施计划提示词的元提示词.md
生成该项目的提示词文档
```

### 🚀 自动执行流程
1. **路径智能解析**: 从用户提供的单个设计文档路径自动推断设计文档目录
2. **立即开始执行**: 无需等待用户确认，直接按照下述完整流程执行
3. **强制采用整合式策略**: 基于所有设计文档生成**一个统一的全局实施计划提示词**
4. **分步骤生成**: 按照4步骤策略生成专用提示词文件
5. **自动保存**: 在设计文档同级目录生成最终文件

### ⚡ 重要执行原则
**明确输出目标**: 
- ✅ **生成一个整合的全局实施计划提示词**（方案B）
- ❌ **不要**生成多个模块化提示词（避免方案A）
- 🎯 **目标是一个文件**：`AI生成{项目名称}实施计划的专用提示词.md`

**复杂度处理策略**:
- 📚 **全面理解**：基于所有12+文档的完整理解
- 🎯 **整体视角**：从全局角度规划实施步骤，自然考虑跨模块依赖
- 🔄 **分步生成**：虽然是全局提示词，但分4步生成以避免失败
- 📝 **详细但统一**：生成详细的实施指导，但保持在一个统一文档中

### 📁 路径自动推断规则
```
用户输入: {任意项目路径}\{设计目录}\{版本}\{序号}-{文档名}.md
自动推断: 
├── 设计文档目录: {任意项目路径}\{设计目录}\{版本}\
├── 项目名称: 从01-架构总览文档中动态提取
└── 输出路径: {设计文档目录}\AI生成{动态提取项目名称}实施计划的专用提示词.md
```

## 📋 使用说明
这是用于指导AI分析设计文档并生成针对该设计文档的专用实施计划提示词的完整元提示词。基于成功案例模板和记忆库约束，确保生成的提示词能够指导AI生成高质量、可执行的实施计划文档。

---

## 🎯 元提示词执行流程

```
请严格按照以下完整流程分析设计文档并生成专用的实施计划提示词：

## 📋 执行任务
1. **强制性实际架构和代码扫描**，深度调研项目真实代码结构、技术栈、模块关系（绝不基于推测）
2. **深度解析目标设计文档**，提取所有关键要素、约束条件和技术特征
3. **设计关键点预分析与AI负载评估**，识别所有关键决策点并评估AI处理复杂度和负载分解策略
4. **验证设计文档完整性**，确认设计文档的关键点是否在提示词模板中有对应处理机制
5. **映射设计要素到实施要求**，将设计约束转化为具体的实施指导原则
6. **基于实际架构+设计文档定制提示词**，生成针对该项目的专用实施计划提示词
7. **在设计文档同级目录生成提示词文件**

## 🧠 AI认知约束强制激活

### 必须激活的核心命令
```bash
@L1:global-constraints                    # 全局约束检查
@L1:ai-implementation-design-principles  # AI实施设计原则
@AI_COGNITIVE_CONSTRAINTS                # AI认知约束激活
@MEMORY_BOUNDARY_CHECK                  # 记忆边界检查
@HALLUCINATION_PREVENTION               # 幻觉防护激活
@ATOMIC_OPERATION_VALIDATION            # 原子操作验证
@COGNITIVE_GRANULARITY_CONTROL          # 认知粒度控制
@BOUNDARY_GUARD_ACTIVATION              # 边界护栏激活
@DESIGN_PATTERN_EXTRACTION              # 设计模式提取（元级别）
@TEMPLATE_ADAPTATION_CONTROL            # 模板适配控制（元级别）
@DRY_PRINCIPLE_ENFORCEMENT              # DRY原则强制执行（设计文档引用）
@CONTENT_REFERENCE_OPTIMIZATION        # 内容引用优化（避免重复）
```

## 🔍 第零步：强制性设计文档全目录扫描与深度解析

### 0.0 用户指令识别与自动启动
**目标**: 识别用户的标准指令格式，自动启动元提示词执行流程
**触发条件**: 用户消息包含以下模式
```
设计文档：{任意路径}
按照提示原提示词：docs\ai-memory\templates\AI生成实施计划提示词的元提示词.md
生成该项目的提示词文档
```

**自动执行操作**:
1. 立即识别这是元提示词执行请求
2. 提取设计文档路径信息
3. 开始执行完整的元提示词流程
4. 无需等待用户进一步确认

**重要说明**: 
- 看到上述格式指令时，直接开始执行，不要询问用户
- 这是标准的自动化执行触发机制
- 按照下述所有步骤顺序执行

### 0.1 设计文档目录完整扫描（绝不跳过）
**目标**: 确保完整扫描设计文档目录，识别所有相关设计文档
**使用工具**: `list_dir`, `file_search`

#### 0.1.1 路径智能解析（用户指令适配）
**执行要求**: 从用户提供的单个设计文档路径自动推断完整信息
```
输入解析通用模式:
用户输入: {任意项目路径}\{设计文档目录}\{版本}\{序号}-{文档名称}.md

自动推断执行:
1. 提取设计文档目录: 去除文件名，保留目录路径
2. 扫描该目录下所有.md文件
3. 识别项目标识: 从路径结构中动态识别
4. 确定文档版本: 从目录结构中提取版本信息
5. 设定输出路径: 同级目录下
```

#### 0.1.2 设计文档目录扫描
**执行要求**: 
- 基于推断出的设计文档目录，扫描整个目录
- 识别所有相关设计文档并进行分类
- 制定完整的文档阅读策略
- 验证设计文档集合的完整性和一致性

**关键验证点**:
- [ ] 设计文档目录已完整扫描
- [ ] 所有设计文档已识别和分类（架构文档、技术实现文档、配套文档、使用指导文档）
- [ ] 文档完整性和技术栈一致性已验证
- [ ] 基于全部设计文档制定了阅读和分析策略

### 0.2 项目信息自动提取
**目标**: 从01-架构总览文档中自动提取项目核心信息
**执行要求**: 优先读取01开头的架构总览文档，提取关键项目信息

#### 项目信息提取清单
```
必须提取的项目信息:
├── 项目正式名称: 从文档标题或"核心定位"章节提取
├── 项目版本: 从文档元数据或版本信息提取  
├── 技术栈概览: 从技术选型章节提取
├── 架构复杂度: 从复杂度等级标注提取
└── 核心定位描述: 从核心定位章节提取

输出路径计算:
├── 提取项目名称: 从设计文档中动态提取实际项目名称
├── 生成文件名: "AI生成{动态提取的项目名称}实施计划的专用提示词.md"
└── 完整路径: {设计文档目录}/AI生成{动态提取的项目名称}实施计划的专用提示词.md
```

### 0.3 设计文档深度解析与特征提取
**目标**: 基于完整的设计文档集合，进行深度解析和特征提取
**执行要求**: 严格按照第0.1步制定的阅读策略，完整阅读所有相关设计文档
**关键输出**: 
- 完整的设计要素清单（架构哲学、技术约束、范围边界、风险识别、成功标准）
- 准确的技术栈特征识别和架构复杂度评估
- 系统化的关键决策点提取和编号

## 🔍 第一步：深度代码架构调研（强制执行）
{基于架构复杂度定制的架构调研流程}

### 0.1 项目整体架构扫描
**使用工具**: `codebase_search`, `list_dir`
**目标**: 深度调研项目真实代码结构、技术栈、模块分布
**强制要求**: 绝不基于推测，必须基于实际代码文件
**执行要求**:
- 扫描项目根目录结构，识别主要模块和子项目
- 分析构建配置文件（pom.xml、package.json、requirements.txt等）
- 识别实际使用的技术栈版本和依赖关系
- 分析项目的实际代码组织方式和架构模式

**验证锚点**: 获得准确的项目架构认知，所有分析结果都有具体代码文件支撑

### 0.2 核心模块依赖关系扫描  
**使用工具**: `grep_search`, `file_search`
**目标**: 深度分析核心模块间的依赖关系、接口设计、数据流
**执行要求**:
- 搜索并分析核心接口定义和实现类
- 识别模块间的调用关系和数据传递路径
- 分析配置文件和启动类，理解系统启动流程
- 扫描测试代码，理解功能验证方式

**验证锚点**: 建立准确的依赖关系图和调用链路图

### 0.3 技术栈配置详细扫描
**使用工具**: `read_file`
**目标**: 详细分析配置文件、技术栈版本、运行环境要求
**执行要求**:
- 深度分析构建配置文件的实际依赖版本
- 扫描应用配置文件的具体配置项和参数
- 分析Docker配置、部署脚本等运行环境配置
- 识别数据库连接、中间件集成等外部依赖

**验证锚点**: 确认技术栈的实际版本和配置方式

### 0.4 现状问题和改进点识别
**使用工具**: `grep_search`（搜索TODO、FIXME、注释等）
**目标**: 识别现有代码中的问题点、技术债务、改进机会
**执行要求**:
- 搜索代码中的TODO、FIXME、HACK等标记
- 分析异常处理、日志记录等质量相关代码
- 识别重复代码、可优化的设计模式
- 分析测试覆盖情况和质量缺陷

**验证锚点**: 形成现状问题清单和改进建议

## 🔍 第二步：设计文档深度解析与特征提取

### 1.0 强制性设计文档全目录扫描（新增-关键步骤）
**目标**: 确保完整扫描设计文档目录，识别所有相关设计文档，避免基于单一文档生成不完整的实施计划
**使用工具**: `list_dir`, `file_search`, `read_file`

#### 1.0.1 设计文档目录结构扫描
**执行要求**: 必须先扫描设计文档所在的整个目录结构
**操作指导**:
```bash
# 扫描设计文档目录（基于用户提供的设计文档路径推断目录）
# 例如：如果用户提供 docs/features/F007-.../DB库/design/v1/01-xxx.md
# 则扫描 docs/features/F007-.../DB库/design/v1/ 整个目录

list_dir(设计文档目录)
```

**验证锚点**: 获得完整的设计文档文件清单

#### 1.0.2 设计文档类型识别与分类
**执行要求**: 对所有扫描到的设计文档进行分类和重要性排序
**分类标准**:
```
核心架构文档（最高优先级）:
├── 包含"架构"、"总览"、"哲学"关键词的文档
├── 包含"核心"、"抽象层"关键词的文档
└── 文档编号为01、02开头的架构性文档

技术实现文档（高优先级）:
├── 包含具体技术栈名称（JPA、Querydsl、JDBC等）的文档
├── 包含"实现"、"集成"关键词的文档
└── 文档编号为03-10的功能实现文档

配套支撑文档（中等优先级）:
├── 包含"监控"、"测试"、"配置"关键词的文档
├── 包含"API"、"接口"、"规范"关键词的文档
└── 文档编号为11-15的支撑性文档

使用指导文档（参考优先级）:
├── 包含"使用"、"指南"、"最佳实践"关键词的文档
└── 包含"示例"、"教程"关键词的文档
```

**验证锚点**: 形成完整的设计文档分类清单和读取优先级

#### 1.0.3 设计文档完整性验证
**执行要求**: 验证设计文档的完整性和一致性
**验证检查点**:
- [ ] **覆盖范围检查**: 是否覆盖了架构设计→技术实现→测试部署的完整链路
- [ ] **技术栈一致性**: 各文档中提到的技术栈版本和配置是否一致
- [ ] **依赖关系完整**: 模块间依赖关系在各文档中描述是否一致
- [ ] **接口契约一致**: API接口定义在不同文档中是否保持一致

**异常处理**:
```
如果发现设计文档不完整或矛盾：
1. 记录具体的缺失或冲突内容
2. 在生成的实施计划中明确标注风险
3. 提供补充调研建议
4. 不能基于不完整信息进行推测性补充
```

**验证锚点**: 确认设计文档集合的完整性和一致性状态

#### 1.0.4 设计文档阅读策略制定
**执行要求**: 基于扫描结果制定最优的文档阅读和分析策略
**策略框架**:
```
L1核心架构文档（必须完整深读）:
├── 架构总览类文档：完整理解整体架构和设计哲学
├── 核心抽象类文档：深度理解核心接口和抽象设计
└── 技术选型类文档：完整掌握技术栈决策和约束

L2技术实现文档（重点章节深读）:
├── 各技术栈实现文档：重点读取接口定义、依赖关系、配置要求
├── 集成方案文档：重点读取集成策略、配置方法、验证标准
└── 监控测试文档：重点读取验证要求、性能指标、质量标准

L3配套文档（关键信息提取）:
├── 使用指南文档：提取关键配置参数和使用约束
├── API规范文档：提取接口契约和兼容性要求
└── 最佳实践文档：提取重要的实施建议和注意事项
```

**🔢 强制性序列化阅读要求（关键）**:
```
必须严格按照文档序列号依次阅读，确保逻辑递进性：

序列化阅读模式（基于文档序列号自动识别）:
├── Step 1: 扫描所有文档，按序列号排序（01、02、03...）
├── Step 2: 识别文档类型（架构类、实现类、配置类、规范类）
└── Step 3: 按序列号依次阅读，建立完整概念体系

第一轮：架构基础文档（通常为01-02序列号）
├── XX-01-{架构总览/设计哲学}类文档    ← 必读：建立整体架构认知
└── XX-02-{核心抽象/基础设计}类文档    ← 必读：理解核心接口和抽象

第二轮：技术实现文档（通常为03-06序列号）  
├── XX-03-{技术实现A}类文档           ← 必读：第一层实现理解
├── XX-04-{技术实现B}类文档           ← 必读：第二层实现理解
├── XX-05-{技术实现C}类文档           ← 必读：第三层实现理解
└── XX-06-{技术实现D}类文档           ← 必读：第四层实现理解

第三轮：基础设施文档（通常为07-09序列号）
├── XX-07-{监控/日志}类文档           ← 必读：监控体系理解
├── XX-08-{数据管理}类文档            ← 必读：数据版本管理
└── XX-09-{自动配置}类文档            ← 必读：框架集成

第四轮：接口规范文档（通常为10+序列号）
├── XX-10-{API接口}类文档             ← 必读：统一接口标准
├── XX-11-{使用指南}类文档            ← 参考：实施指导
├── XX-12-{测试策略}类文档            ← 参考：验证标准
└── XX-13+{其他补充}文档              ← 按需：特定领域补充
```

**❗ 序列化阅读执行原则**:
- **严格顺序**: 必须按01→02→03→...→10的序列号顺序阅读
- **逻辑依赖**: 后续文档依赖前面文档的概念和定义，不能跳跃
- **完整性保证**: 每个文档读完才能进入下一个，确保概念体系完整建立
- **一致性验证**: 读取每个文档后，验证与前面文档的一致性和补充关系

**执行指导**: 严格按照上述策略进行文档阅读，确保信息获取的完整性和效率

### 1.1 设计文档结构分析
**使用工具**: `read_file`
**目标**: 深度理解设计文档的整体结构和组织方式
**执行要求**:
- 完整阅读设计文档，理解文档组织结构
- 提取文档的核心章节和关键内容分布
- 识别文档的复杂度等级和技术深度
- 分析文档的业务领域和技术领域特征

**验证锚点**: 准确理解设计文档的整体架构和内容组织

### 1.2 核心设计要素提取
**使用工具**: `grep_search` + `read_file`
**目标**: 系统性提取设计文档的所有关键要素
**执行要求**:

#### A. 架构哲学与设计原则提取
- 核心设计理念（如"务实架构"、"组合优化"、"技术特性协同"等）
- 设计驱动因子和价值导向
- 技术选型逻辑和权衡决策
- 架构演进策略和未来规划

#### B. 技术约束与限制提取
- 技术栈强制性要求（具体版本、组件、框架）
- 性能指标要求（响应时间、吞吐量、可用性等）
- 架构模式约束（分层架构、设计模式、交互限制）
- 兼容性要求和演进策略约束

#### C. 范围边界明确定义
- 包含范围的具体功能模块和技术组件
- 排除范围的明确界定和原因说明
- 现实能力边界和技术限制
- 依赖关系和外部系统集成要求

#### D. 风险识别与应对策略
- P0/P1/P2级别风险的具体识别
- 风险缓解措施和应对预案
- 技术债务和已知问题
- 监控预警和决策机制

#### E. 成功标准与验收要求
- 功能完整性的具体验收标准
- 性能指标的量化要求和基准
- 质量门禁和代码规范要求
- 部署和运维的成功标准

**验证锚点**: 形成完整的设计要素清单和约束矩阵

### 1.3 设计文档特征模式识别
**目标**: 识别设计文档的独特特征和技术模式
**执行要求**:

#### 技术栈特征模式
```
示例模式识别：
Spring生态模式：Spring Boot + Spring Data + HikariCP + Maven
↓ 转化为实施约束:
- 编译验证命令: mvn compile
- 测试执行命令: mvn test
- 启动验证命令: java -jar target/*.jar
- 依赖管理文件: pom.xml

Node.js生态模式：Express + TypeScript + npm + Jest
↓ 转化为实施约束:
- 编译验证命令: npm run build
- 测试执行命令: npm test
- 启动验证命令: npm start
- 依赖管理文件: package.json

Python生态模式：Django + SQLAlchemy + pip + pytest
↓ 转化为实施约束:
- 环境验证命令: python --version && pip --version
- 测试执行命令: pytest
- 启动验证命令: python manage.py runserver
- 依赖管理文件: requirements.txt
```

#### 架构复杂度模式
```
L1简单架构模式：单体应用，≤3个模块，直接功能实现
L2中等架构模式：分层架构，4-7个模块，多组件协调
L3复杂架构模式：微服务架构，≥8个模块，分布式系统
```

#### 业务领域模式
```
数据库中间件模式：连接池 + ORM + 监控 + Schema管理
缓存中间件模式：分布式缓存 + 一致性 + 失效策略
API网关模式：路由 + 认证 + 限流 + 监控
配置管理模式：多环境 + 热更新 + 版本控制
```

**验证锚点**: 准确识别设计文档的技术栈模式和架构复杂度等级

### 1.4 关键决策点提取与编号
**目标**: 提取并编号所有关键决策点，建立设计-实施映射基础
**执行要求**:

#### 关键点分类与编号体系（DRY引用机制）
```
业务关键点: D001-D099 (业务目标、功能边界、业务约束)
├── 引用格式: ref:[设计文档路径]#[章节锚点]#D00X
├── 描述方式: "详见 ref:[文档路径]#业务目标 中的核心定位"
└── 避免重复: 只引用，不复制粘贴设计文档内容

技术关键点: D101-D199 (技术选型、技术栈、技术约束)  
├── 引用格式: ref:[设计文档路径]#[技术章节]#D1XX
├── 描述方式: "技术栈要求详见 ref:[文档路径]#技术基石"
└── 版本引用: "具体版本要求 ref:[文档路径]#技术栈版本表"

架构关键点: D201-D299 (分层架构、组件设计、架构模式)
├── 引用格式: ref:[设计文档路径]#[架构章节]#D2XX  
├── 描述方式: "分层架构详见 ref:[文档路径]#分层架构概览"
└── 图表引用: "架构图 ref:[文档路径]#架构图表_Figure_X"

性能关键点: D301-D399 (性能目标、优化策略、性能约束)
├── 引用格式: ref:[设计文档路径]#[性能章节]#D3XX
├── 描述方式: "性能指标 ref:[文档路径]#性能基准表"
└── 优化策略: "优化方案 ref:[文档路径]#组合优化策略"

风险关键点: D401-D499 (风险识别、应对策略、风险控制)
├── 引用格式: ref:[设计文档路径]#[风险章节]#D4XX
├── 描述方式: "风险评估 ref:[文档路径]#风险评估矩阵"
└── 应对策略: "缓解措施 ref:[文档路径]#风险应对预案"

成功标准关键点: D501-D599 (验收标准、质量指标、成功标准)
├── 引用格式: ref:[设计文档路径]#[成功标准章节]#D5XX
├── 描述方式: "验收标准 ref:[文档路径]#验收标准清单"
└── 质量指标: "质量要求 ref:[文档路径]#质量门禁表"
```

#### 关键决策链识别
```
技术选型决策链：
问题识别 → 方案分析 → 技术选择 → 验证方案 → 实施计划

架构设计决策链：
需求分析 → 约束识别 → 架构模式 → 组件设计 → 接口定义

风险控制决策链：
风险识别 → 影响分析 → 应对策略 → 监控方案 → 预案制定

性能优化决策链：
性能要求 → 瓶颈分析 → 优化策略 → 验证方案 → 基准测试
```

**验证锚点**: 建立完整的关键点清单和决策链映射

## 🔍 第三步：设计关键点预分析与AI负载评估（核心增强）

### 2.1 设计文档关键点完整性验证
**目标**: 验证设计文档的关键点是否在提示词模板中有对应处理机制
**执行要求**:

#### 关键点覆盖性检查矩阵
```
设计文档关键点类型 | 基础模板对应模块 | 覆盖状态 | 定制化需求
────────────────┼──────────────┼─────────┼───────────
架构哲学与设计原则 | 设计约束转化模块 | ✅/❌ | 需要/无需定制
技术栈强制性要求 | 技术栈验证命令 | ✅/❌ | 需要/无需定制  
性能指标要求 | 验证锚点系统 | ✅/❌ | 需要/无需定制
架构模式约束 | 操作边界控制 | ✅/❌ | 需要/无需定制
兼容性要求 | 风险评估框架 | ✅/❌ | 需要/无需定制
风险识别与应对 | P0/P1/P2风险体系 | ✅/❌ | 需要/无需定制
成功标准验收 | 执行检查清单 | ✅/❌ | 需要/无需定制
```

#### 关键点缺失识别与补强机制
**执行流程**:
1. **逐一对比**: 设计文档每个关键点与基础模板的对应关系
2. **缺失识别**: 标记设计文档中基础模板未覆盖的关键点
3. **补强设计**: 为缺失的关键点设计专门的处理模块
4. **定制强化**: 为已覆盖但需要强化的关键点设计定制模块

**验证锚点**: 确保设计文档的每个关键点都有明确的处理机制

### 2.2 AI认知负载与处理复杂度评估
**目标**: 基于实际架构扫描和设计文档分析，评估AI处理负载并设计分解策略

#### AI负载评估多维度分析
```
维度1: 概念复杂度负载 (0-30分)
- 设计概念数量: ≤5个(0-10分) | 6-12个(11-20分) | ≥13个(21-30分)
- 技术栈复杂度: 单栈(0-5分) | 多栈集成(6-15分) | 异构复杂栈(16-30分)
- 架构抽象层次: ≤3层(0-5分) | 4-6层(6-15分) | ≥7层(16-30分)

维度2: 记忆边界压力 (0-25分)  
- 设计文档长度: ≤500行(0-8分) | 500-1500行(9-17分) | ≥1500行(18-25分)
- 代码文件数量: ≤20个(0-8分) | 20-50个(9-17分) | ≥50个(18-25分)
- 依赖关系复杂度: 简单(0-8分) | 中等(9-17分) | 复杂(18-25分)

维度3: 实施步骤复杂度 (0-25分)
- 实施步骤数量: ≤10步(0-8分) | 10-25步(9-17分) | ≥25步(18-25分)
- 步骤间依赖关系: 线性(0-8分) | 部分依赖(9-17分) | 强依赖(18-25分)
- 验证复杂度: 简单验证(0-8分) | 中等验证(9-17分) | 复杂验证(18-25分)

维度4: 风险与决策负载 (0-20分)
- 风险场景数量: ≤5个(0-6分) | 5-12个(7-14分) | ≥12个(15-20分)
- 决策链复杂度: 简单决策(0-6分) | 多分支决策(7-14分) | 复杂决策树(15-20分)
- 回滚复杂度: 简单回滚(0-6分) | 中等回滚(7-14分) | 复杂回滚(15-20分)
```

#### AI负载分解策略设计
```
总分0-30分: L1轻负载策略
- 单一大步骤处理
- 简化验证机制
- 标准模板直接适配

总分31-70分: L2中负载策略  
- 分阶段处理，每阶段≤5个概念
- 关键点验证机制
- 部分定制化处理

总分71-100分: L3重负载策略
- 强制原子化分解，每步≤3个概念
- 多层验证确认机制
- 深度定制化处理
- 强制记忆边界管理
```

**验证锚点**: 生成AI负载评估报告和对应的处理策略

### 2.3 AI处理步骤智能分解设计
**目标**: 基于负载评估结果，设计AI处理步骤的智能分解策略

#### 认知边界分解原则
```
单步骤认知边界控制:
- 概念数量: ≤3个核心概念/步骤（L3）| ≤5个核心概念/步骤（L2）| ≤7个核心概念/步骤（L1）
- 文件操作: ≤1个文件/步骤（L3）| ≤2个文件/步骤（L2）| ≤3个文件/步骤（L1）
- 代码行数: ≤30行/步骤（L3）| ≤50行/步骤（L2）| ≤100行/步骤（L1）
- 验证点数量: ≥5个验证点/步骤（L3）| ≥3个验证点/步骤（L2）| ≥2个验证点/步骤（L1）
```

#### 分解策略模板
```markdown
L3重负载分解策略模板:
### 阶段X.Y: {单一功能点名称} (预估X小时)
**认知单元**: {≤3个核心概念描述}
**操作边界**: 仅限{单一明确操作}，禁止{明确禁止内容}
**验证锚点**: {≥5个具体验证点}
**负载控制**: 概念数≤3，文件数≤1，代码行≤30，验证点≥5

#### X.Y.1 {原子操作1} (≤30行代码)
**目标**: {单一明确目标}
**修改文件**: `{单一文件路径}`
**认知约束**: {具体约束描述}

L2中负载分解策略模板:
### 阶段X: {功能模块名称} (预估X天)  
**认知单元**: {≤5个核心概念描述}
**操作边界**: {清晰的操作范围}，单次修改≤50行代码
**验证锚点**: {≥3个具体验证点}
**负载控制**: 概念数≤5，文件数≤2，代码行≤50，验证点≥3

L1轻负载分解策略模板:
### 阶段X: {功能领域名称} (预估X天)
**认知单元**: {≤7个核心概念描述}
**操作边界**: {功能领域范围}，批次处理允许
**验证锚点**: {≥2个关键验证点}
**负载控制**: 概念数≤7，文件数≤3，代码行≤100，验证点≥2
```

**验证锚点**: 生成完整的AI处理步骤分解策略和模板

## 🔍 第四步：实际架构与设计文档融合分析

### 3.1 架构-设计一致性验证
**目标**: 验证实际代码架构与设计文档的一致性，识别差异和冲突
**执行要求**:

#### 架构对齐验证矩阵
```
设计文档要求 | 实际代码架构 | 一致性状态 | 差异影响 | 处理策略
──────────┼────────────┼─────────┼────────┼─────────
技术栈版本要求 | 实际依赖版本 | ✅/❌/⚠️ | 高/中/低 | 升级/保持/注释
分层架构设计 | 实际代码组织 | ✅/❌/⚠️ | 高/中/低 | 重构/适配/文档
接口设计约束 | 实际接口实现 | ✅/❌/⚠️ | 高/中/低 | 修改/兼容/说明
性能要求 | 实际性能基准 | ✅/❌/⚠️ | 高/中/低 | 优化/测试/评估
```

#### 冲突解决策略
**执行流程**:
1. **冲突识别**: 标记实际架构与设计文档的不一致之处
2. **影响评估**: 评估每个冲突对实施计划的影响程度
3. **解决策略**: 为每个冲突设计具体的解决方案
4. **风险评估**: 评估解决策略可能带来的新风险

**验证锚点**: 形成完整的架构-设计一致性报告和冲突解决方案

### 3.2 基于实际情况的实施路径规划
**目标**: 基于实际架构扫描结果，规划最优的实施路径

#### 实施路径决策矩阵
```
实施选项 | 技术可行性 | 风险等级 | 工作量 | 与设计符合度 | 推荐度
───────┼─────────┼────────┼──────┼───────────┼──────
选项1：完全按设计 | 高/中/低 | P0/P1/P2 | 大/中/小 | 100%/80%/60% | ⭐⭐⭐
选项2：渐进式实施 | 高/中/低 | P0/P1/P2 | 大/中/小 | 100%/80%/60% | ⭐⭐⭐
选项3：适配性实施 | 高/中/低 | P0/P1/P2 | 大/中/小 | 100%/80%/60% | ⭐⭐⭐
```

#### 实际约束条件整合
**基于代码扫描的约束条件**:
- 现有代码的技术债务限制
- 现有依赖关系的约束
- 现有测试框架的限制
- 现有部署流程的约束

**验证锚点**: 生成基于实际情况的最优实施路径方案

## 🔍 第五步：模板基础要素识别与适配

### 4.1 基础模板要素提取
**目标**: 从标准模板中提取可复用的基础要素
**基础模板要素清单**:

#### 固定结构要素（所有项目通用）
```
1. 文档元数据结构
2. AI认知约束激活模块
3. 护栏机制检查模块
4. 基础架构调研流程
5. AI专业度量参数系统
6. 验证锚点系统框架
7. 操作边界控制框架
8. 风险评估与应对框架（P0/P1/P2结构）
9. 执行检查清单框架
10. 执行状态跟踪框架
```

#### 可变适配要素（根据设计文档定制）
```
1. 设计约束转化模块（基于具体设计约束）
2. 技术栈特定验证命令（基于实际技术栈）
3. 实施步骤内容（基于具体功能要求）
4. 架构特定调研方法（基于架构复杂度）
5. 业务特定验证标准（基于业务领域）
6. 技术特定风险场景（基于技术选型）
7. 项目特定文件路径（基于项目结构）
8. 领域特定代码示例（基于技术栈）
```

### 4.2 设计文档驱动的适配策略
**目标**: 基于设计文档特征确定模板适配策略

#### 复杂度驱动适配
```
L1简单项目适配：
- 生成：单一实施计划文档
- 简化：AI度量参数（基础5维度）
- 验证：批次验证策略
- 架构：简化架构调研流程

L2中等项目适配：
- 生成：核心实施计划 + 扩展文档
- 标准：完整AI度量参数系统
- 验证：关键点验证策略
- 架构：标准架构调研流程

L3复杂项目适配：
- 生成：完整文档矩阵（总体+模块+集成）
- 强化：AI认知约束和边界护栏
- 验证：多点验证确认机制
- 架构：深度架构调研和分析
```

#### 技术栈驱动适配
```
Java/Spring技术栈：
- 编译命令: mvn compile / gradle build
- 测试命令: mvn test / gradle test
- 启动命令: java -jar target/*.jar
- 配置文件: application.yml / application.properties
- 依赖文件: pom.xml / build.gradle

Node.js技术栈：
- 编译命令: npm run build / yarn build
- 测试命令: npm test / yarn test
- 启动命令: npm start / yarn start
- 配置文件: .env / config.json
- 依赖文件: package.json

Python技术栈：
- 环境命令: python --version / pip --version
- 测试命令: pytest / python -m unittest
- 启动命令: python app.py / gunicorn app:app
- 配置文件: settings.py / config.yaml
- 依赖文件: requirements.txt / pyproject.toml
```

#### 业务领域驱动适配
```
数据库中间件领域：
- 关注点: 连接池管理、ORM集成、Schema管理、性能监控
- 验证重点: 数据一致性、连接稳定性、性能基准
- 风险重点: 数据丢失、连接泄漏、性能下降

缓存中间件领域：
- 关注点: 缓存策略、一致性控制、失效机制、分布式协调
- 验证重点: 缓存命中率、数据一致性、故障恢复
- 风险重点: 缓存雪崩、数据不一致、内存泄漏

API网关领域：
- 关注点: 路由管理、认证授权、限流熔断、监控告警
- 验证重点: 路由准确性、认证有效性、性能稳定性
- 风险重点: 路由错误、认证绕过、服务雪崩
```

**验证锚点**: 建立完整的适配策略和定制化规则

## 🔍 第六步：专用提示词生成与定制

### 5.1 提示词结构定制生成
**目标**: 基于设计文档特征生成专用提示词结构

#### 文档元数据定制
```markdown
# {从设计文档提取的具体项目名称} - AI严格生成实施计划文档的专用提示词

## 使用说明
这是专门针对"{设计文档名称}"生成的实施计划提示词。基于该设计文档的架构哲学、技术约束和设计原则，确保AI生成高质量、可执行、完全符合设计要求的实施计划文档。

## 🎯 设计文档特征分析结果
- **文档类型**: {从设计文档提取的类型}
- **技术栈**: {从设计文档提取的具体技术栈}
- **架构复杂度**: {L1/L2/L3复杂度等级}
- **业务领域**: {从设计文档提取的业务领域}
- **关键约束数量**: {提取的约束总数}
- **风险等级**: {最高风险等级}
```

#### 设计约束专用转化模块（DRY引用机制）
```markdown
## 🎯 {设计文档名称}专用设计约束转化

### 核心设计哲学传承（引用模式）
**架构理念引用**: ref:[设计文档路径]#核心定位 - "务实的企业级数据访问架构"
**技术价值观引用**: ref:[设计文档路径]#组合优化设计哲学 - "技术特性协同效应"
**设计权衡引用**: ref:[设计文档路径]#架构范围边界 - "包含范围 vs 排除范围"

### 强制性架构约束清单（DRY引用来源：{设计文档路径}）
**重要提示**: 以下约束通过引用方式避免重复，确保信息同步更新

#### A类约束：技术栈强制性要求（引用模式）
| 约束ID | 约束引用 | 来源章节引用 | 验证标准引用 | 违规后果 |
|-------|---------|------------|------------|----------|
| A001 | ref:[设计文档路径]#技术基石#Spring生态要求 | ref:#技术基石 | ref:#验证机制 | 立即停止实施 |
| A002 | ref:[设计文档路径]#技术基石#HikariCP连接池 | ref:#技术基石 | ref:#连接池监控 | 性能风险P1 |
| A003 | ref:[设计文档路径]#技术基石#PostgreSQL版本 | ref:#数据库方言 | ref:#兼容性测试 | 兼容性问题P1 |

#### B类约束：架构模式强制性要求（引用模式）
| 约束ID | 约束引用 | 来源章节引用 | 验证标准引用 | 违规后果 |
|-------|---------|------------|------------|----------|
| B001 | ref:[设计文档路径]#分层架构概览#L1-L2-L3分层 | ref:#分层架构 | ref:#架构依赖检查 | 架构违规P0 |
| B002 | ref:[设计文档路径]#核心抽象层#SPI机制 | ref:#核心抽象层 | ref:#SPI验证 | 扩展性受限P2 |
| B003 | ref:[设计文档路径]#统一数据访问接口 | ref:#接口定义 | ref:#接口兼容性 | 接口不一致P1 |

#### C类约束：性能与质量强制性要求（引用模式）
| 约束ID | 约束引用 | 来源章节引用 | 验证标准引用 | 违规后果 |
|-------|---------|------------|------------|----------|
| C001 | ref:[设计文档路径]#组合优化哲学#性能倍增原理 | ref:#组合优化 | ref:#性能基准测试 | 性能不达标P1 |
| C002 | ref:[设计文档路径]#质量标准#测试覆盖率≥80% | ref:#质量门禁 | ref:#测试覆盖率检查 | 质量不达标P2 |
| C003 | ref:[设计文档路径]#监控体系#Micrometer集成 | ref:#监控集成 | ref:#监控指标验证 | 监控缺失P2 |

### 设计文档引用索引（便于快速定位）
```
核心设计文档结构引用:
├── ref:[设计文档路径]#核心定位                    # 项目定位和目标
├── ref:[设计文档路径]#架构范围边界                # 功能边界定义
├── ref:[设计文档路径]#分层架构概览                # 架构设计核心
├── ref:[设计文档路径]#技术基石                    # 技术选型决策
├── ref:[设计文档路径]#组合优化设计哲学            # 设计哲学
├── ref:[设计文档路径]#核心抽象层设计              # 抽象层定义
├── ref:[设计文档路径]#监控体系                    # 监控集成方案
└── ref:[设计文档路径]#风险评估                    # 风险控制策略
```
```

#### 技术栈专用验证命令定制
```markdown
## 🔧 {技术栈名称}专用验证命令体系（基于实际架构扫描）

### 环境验证命令（基于实际项目扫描）
```bash
# 基于{技术栈}的环境检查（实际版本：{扫描得到的实际版本}）
{基于架构扫描确定的具体环境验证命令}
# 例如：对于Spring Boot项目
java -version  # 验证实际使用的Java版本
mvn --version  # 验证实际使用的Maven版本
```

### 编译验证命令（基于实际构建配置）
```bash
# 基于{构建工具}的编译验证（实际构建文件：{扫描到的实际构建文件}）
{基于架构扫描确定的具体编译命令}
# 例如：对于Maven项目
mvn clean compile  # 基于实际pom.xml的编译
# 例如：对于Gradle项目  
./gradlew build    # 基于实际build.gradle的编译
```

### 测试验证命令（基于实际测试框架）
```bash
# 基于{测试框架}的测试验证（实际测试目录：{扫描到的测试目录}）
{基于架构扫描确定的具体测试命令}
# 例如：对于Spring Boot + JUnit项目
mvn test  # 运行实际存在的测试类
# 例如：对于Node.js项目
npm test  # 基于实际package.json中的test脚本
```

### 启动验证命令（基于实际启动类）
```bash
# 基于{运行时环境}的启动验证（实际主类：{扫描到的主类}）
{基于架构扫描确定的具体启动命令}
# 例如：对于Spring Boot项目
java -jar target/{实际扫描到的jar名称}.jar
# 例如：对于Node.js项目
npm start  # 基于实际package.json中的start脚本
```

### 实际项目文件路径（基于架构扫描结果）
- **主配置文件**: `{基于架构扫描确定的实际主配置文件路径}`
  - 例如：`src/main/resources/application.yml`（实际扫描发现）
- **依赖管理文件**: `{基于架构扫描确定的实际依赖文件路径}`
  - 例如：`pom.xml`（Maven项目）或 `build.gradle`（Gradle项目）
- **测试配置文件**: `{基于架构扫描确定的实际测试配置路径}`
  - 例如：`src/test/resources/application-test.yml`（实际存在）
- **核心业务代码路径**: `{基于架构扫描确定的实际业务代码路径}`
  - 例如：`src/main/java/com/company/project/`（实际包结构）
- **实际模块结构**: 
  ```
  {基于架构扫描的实际项目结构}
  例如：
  project-root/
  ├── module-a/                 # 实际扫描发现的模块A
  ├── module-b/                 # 实际扫描发现的模块B
  ├── common/                   # 实际扫描发现的公共模块
  └── {其他实际存在的模块}
  ```
```

#### 业务领域专用风险场景定制
```markdown
## ⚠️ {业务领域}专用风险评估与应对方案

### 🔴 P0级别风险 (立即回滚)
| 风险场景 | 触发条件 | 立即回滚操作 | 设计文档依据 |
|---------|----------|-------------|-------------|
{基于设计文档和业务领域提取的P0级风险清单}

### 🟡 P1级别风险 (1小时内决策)  
| 风险场景 | 触发条件 | 处理策略 | 设计文档依据 |
|---------|----------|----------|-------------|
{基于设计文档和业务领域提取的P1级风险清单}

### 🟢 P2级别风险 (计划处理)
| 风险场景 | 触发条件 | 处理策略 | 设计文档依据 |
|---------|----------|----------|-------------|
{基于设计文档和业务领域提取的P2级风险清单}

### 回滚执行命令
```bash
# P0级别：立即回滚（5分钟内）
git log --oneline -5
git checkout -f [稳定commit hash]  
{基于技术栈的具体重新构建命令}
{基于技术栈的具体服务重启命令}

# P1级别：计划回滚（1小时内）
git stash push -m "备份当前修改"
git checkout [稳定分支]
{基于技术栈的具体验证命令}
```
```

#### 关键点映射矩阵定制
```markdown
## 🎯 {设计文档名称}关键点与实施关键点精准对齐

### 设计关键点提取与分类（来源：{设计文档路径}）

#### 业务关键点清单
| 关键点ID | 关键点内容 | 来源章节 | 重要等级 | 对应实施点 |
|----------|----------|----------|----------|----------|
{基于设计文档提取的具体业务关键点}

#### 技术关键点清单
| 关键点ID | 关键点内容 | 来源章节 | 重要等级 | 对应实施点 |
|----------|----------|----------|----------|----------|
{基于设计文档提取的具体技术关键点}

#### 架构关键点清单
| 关键点ID | 关键点内容 | 来源章节 | 重要等级 | 对应实施点 |
|----------|----------|----------|----------|----------|
{基于设计文档提取的具体架构关键点}

#### 性能关键点清单
| 关键点ID | 关键点内容 | 来源章节 | 重要等级 | 对应实施点 |
|----------|----------|----------|----------|----------|
{基于设计文档提取的具体性能关键点}

#### 风险关键点清单
| 关键点ID | 关键点内容 | 来源章节 | 重要等级 | 对应实施点 |
|----------|----------|----------|----------|----------|
{基于设计文档提取的具体风险关键点}

#### 成功标准关键点清单
| 关键点ID | 关键点内容 | 来源章节 | 重要等级 | 对应实施点 |
|----------|----------|----------|----------|----------|
{基于设计文档提取的具体成功标准关键点}
```

### 5.2 实施步骤内容定制生成
**目标**: 基于设计文档的具体功能要求生成定制化实施步骤

#### 功能模块驱动的步骤定制
```markdown
### 阶段2: 核心功能实施 (预估{基于复杂度评估的天数})
**认知单元**: {基于设计文档的核心业务逻辑描述} (≤5个概念)
**操作边界**: 每次只修改一个文件，单次修改限制在50行以内，≤3个操作步骤
**验证锚点**: 智能编译验证(风险基础)，功能验证通过，冗余代码检查
**设计约束监控**: 实时验证{具体的架构分层要求}、{具体的技术选型要求}、{具体的性能指标要求}符合设计文档

#### 2.1 {从设计文档提取的功能模块1名称} 实施 (≤50行代码)
**目标**: {从设计文档提取的具体目标}
**修改文件**: `{基于架构调研的实际文件路径}`
**设计约束合规**: 严格按照设计文档的{具体约束ID}实施

**执行指引**:
  **现有代码** (基于 read_file 调研):
  ```{实际代码语言}
  [基于架构调研的实际现有代码片段]
  ```

  **修改为**:
  ```{实际代码语言}
  // 添加必要的导入语句 (基于设计文档技术栈)
  {实际需要的导入语句}
  
  {基于设计文档的具体实现代码}
  // 包含完整的异常处理和日志记录
  ```

**设计约束验证检查点**:
- [ ] **A类约束检查**: {具体的A类约束验证项}
- [ ] **B类约束检查**: {具体的B类约束验证项}  
- [ ] **C类约束检查**: {具体的C类约束验证项}

**智能编译验证** (风险基础策略):
  **风险评估**: [评估当前修改的风险等级] + **设计偏离风险评估**
  
  **智能验证点**:
  ```bash
  # 强制编译检查 (高风险变更后)
  {基于技术栈的实际编译命令}
  # 强制设计约束检查 (任何变更后)
  {设计约束验证命令}
  # 功能验证 (基于具体功能的验证方法)
  {具体验证步骤}
  # 冗余代码检查
  {检查多余import、无用函数、未使用变量}
  # 设计一致性验证
  {验证实施结果与设计文档的一致性}
  ```
```

### 5.3 分步骤专用提示词文件生成（整合式策略）
**目标**: 生成一个统一的全局实施计划提示词，采用分步骤策略避免失败
**策略**: 将专用提示词分解为多个独立模块，分步生成并最终组装成**一个完整的全局文件**
**重要原则**: 整合所有12+设计文档的内容到一个统一的实施计划提示词中

#### 分步骤生成策略（整合式全局提示词）
```
Step 1: 生成基础框架文件（整合所有设计文档的基础信息）
├── 文档元数据和使用说明（基于所有设计文档）
├── AI认知约束激活模块
├── 核心设计概览（整合所有架构、实现、配置文档）
└── 基础验证命令体系（覆盖所有技术栈）

Step 2: 生成核心执行模块（整合所有模块的实施步骤）
├── 设计约束转化模块（整合所有约束）
├── 实施步骤内容模块（整合所有模块的实施步骤）
├── 关键点映射矩阵（跨模块依赖关系）
└── 风险评估与应对框架（全局风险管理）

Step 3: 生成配套支撑模块（全局支撑体系）
├── 映射文件生成模块（整合式映射）
├── 验证检查清单（全模块验证）
├── 执行状态跟踪框架（全局跟踪）
└── 使用方式说明（整体使用指导）

Step 4: 组装完整全局文件
├── 读取所有分步生成的模块
├── 按照标准结构组装成一个统一的全局提示词
├── 验证所有模块间依赖关系的完整性和一致性
└── 生成最终的**一个**专用实施计划提示词文件

🎯 整合式策略关键要求:
├── 确保所有12+设计文档的内容都整合到这一个文件中
├── 体现跨模块的依赖关系和执行顺序
├── 提供全局视角的实施规划
└── 避免生成多个独立的模块化提示词
```

#### 文件命名和路径策略
```
分步生成临时文件:
├── {项目名称}_实施提示词_Step1_基础框架.md
├── {项目名称}_实施提示词_Step2_核心执行.md
├── {项目名称}_实施提示词_Step3_配套支撑.md
└── 最终组装后删除临时文件

最终生成文件:
生成路径: {设计文档所在目录}/AI生成{项目名称}实施计划的专用提示词.md
文件命名规则: AI生成{从设计文档提取的项目名称}实施计划的专用提示词.md
```

#### 分步生成执行原则
```
单步生成控制:
├── 每步生成内容 ≤ 800行代码
├── 每步专注单一功能模块
├── 每步生成后立即验证
└── 发现问题立即修正，不继续下一步

错误处理策略:
├── 单步失败时重新生成该步骤
├── 不影响其他已完成步骤
├── 提供明确的错误信息和修正建议
└── 支持从任意步骤重新开始

组装验证机制:
├── 验证所有步骤文件存在且完整
├── 验证内容逻辑一致性
├── 验证引用完整性
└── 验证最终文件可用性
```

#### 映射文件生成要求（新增）
**目标**: 基于设计文档特征生成定制化映射文件，提供精确的技术实施指导

##### 设计文档驱动的定制映射框架（方案2）
**核心理念**: 深度分析设计文档的具体特征，为每个设计文档定制专门的映射文件类型和结构

#### 映射类型定制分析矩阵
**第一步：设计文档特征深度提取**
```
技术栈特征分析：
├── 核心技术栈 → 确定基础映射类型（如Java+Spring → 依赖关系映射）
├── 中间件组件 → 确定配置映射类型（如数据库、缓存 → 配置参数映射）
├── 架构模式 → 确定架构映射类型（如微服务、分层 → 服务/模块映射）
└── 集成方式 → 确定接口映射类型（如API、SPI → 接口契约映射）

业务领域特征分析：
├── 数据访问领域 → 数据模型映射 + 连接配置映射
├── 异常处理领域 → 错误码映射 + 异常转换映射  
├── 缓存中间件领域 → 缓存策略映射 + 一致性配置映射
├── API网关领域 → 路由映射 + 策略配置映射
└── 监控体系领域 → 指标映射 + 告警配置映射

架构复杂度特征分析：
├── L1简单架构 → 单一综合映射文件
├── L2中等架构 → 核心映射 + 配置映射
└── L3复杂架构 → 多维度专业映射矩阵

风险特征分析：
├── 高风险变更 → 强制回滚映射 + 验证检查点映射
├── 兼容性风险 → 版本兼容映射 + 迁移路径映射
└── 性能风险 → 性能基准映射 + 监控指标映射
```

#### 定制映射文件生成策略
**第二步：基于特征分析结果定制映射文件**

##### 策略1：基于技术栈的映射定制
```markdown
Spring生态系统设计文档 → 定制映射：
├── XX-Spring模块依赖映射.json
│   ├── Spring Boot模块依赖关系
│   ├── Spring Data集成映射
│   ├── Spring Security配置映射
│   └── Spring Cloud组件依赖
├── XX-Maven构建配置映射.json
│   ├── 依赖版本映射
│   ├── 插件配置映射
│   └── 构建环境映射
└── XX-数据库集成映射.json
    ├── 数据源配置映射
    ├── ORM映射配置
    └── 数据库版本兼容映射

Node.js生态系统设计文档 → 定制映射：
├── XX-NPM包依赖映射.json
├── XX-Express路由映射.json
├── XX-TypeScript配置映射.json
└── XX-Node环境配置映射.json

微服务架构设计文档 → 定制映射：
├── XX-服务间依赖映射.json
├── XX-配置中心映射.json
├── XX-服务发现映射.json
└── XX-通信协议映射.json
```

##### 策略2：基于业务领域的映射定制
```markdown
数据访问中间件设计文档 → 定制映射：
├── XX-数据源依赖映射.json
│   ├── 数据库驱动依赖
│   ├── 连接池配置依赖
│   └── ORM框架依赖
├── XX-数据模型映射.json
│   ├── 实体类映射关系
│   ├── 数据库表结构映射
│   └── 字段类型映射
├── XX-配置参数映射.json
│   ├── 连接池参数配置
│   ├── 事务配置参数
│   └── 缓存配置参数
└── XX-性能监控映射.json
    ├── 性能指标收集配置
    ├── 监控告警阈值映射
    └── 性能基准测试映射

异常处理框架设计文档 → 定制映射：
├── XX-异常库依赖映射.json
├── XX-错误码映射.json
├── XX-异常转换映射.json
└── XX-异常处理配置映射.json

API网关设计文档 → 定制映射：
├── XX-网关路由映射.json
├── XX-认证策略映射.json
├── XX-限流配置映射.json
└── XX-监控集成映射.json
```

##### 策略3：基于架构复杂度的映射定制
```markdown
L1简单架构（单体应用）→ 定制映射：
└── XX-综合实施映射.json
    ├── 模块依赖（简化版）
    ├── 配置参数（核心参数）
    ├── 验证检查点（关键点）
    └── 回滚方案（简单回滚）

L2中等架构（分层应用）→ 定制映射：
├── XX-分层依赖映射.json
├── XX-配置参数映射.json
└── XX-集成验证映射.json

L3复杂架构（分布式系统）→ 定制映射：
├── XX-系统依赖映射.json
├── XX-服务配置映射.json
├── XX-通信协议映射.json
├── XX-监控体系映射.json
├── XX-安全策略映射.json
└── XX-运维部署映射.json
```

#### 定制映射文件模板框架

##### 基础结构模板（所有定制映射的通用结构）
```json
{
  "document_info": {
    "document_id": "{基于设计文档生成的唯一ID}",
    "design_document": "{设计文档路径和名称}",
    "mapping_type": "{基于特征分析确定的映射类型}",
    "related_plan": "{对应的实施计划文件名}",
    "created_date": "{当前日期}",
    "version": "v1.0",
    "purpose": "{基于设计文档特征定制的映射目的}",
    "customization_basis": "{定制化依据：技术栈/业务领域/架构复杂度}",
    "design_constraints": ["{从设计文档提取的关键约束}"]
  },
  
  "mapping_content": {
    "{定制化的主要内容结构}": {
      "基于设计文档特征的具体字段设计"
    }
  },
  
  "execution_guidance": {
    "sequence_principle": "{基于设计文档的执行原则}",
    "validation_checkpoints": ["{设计文档驱动的验证点}"],
    "risk_controls": ["{基于设计文档的风险控制措施}"]
  },
  
  "design_document_alignment": {
    "design_requirements_mapping": ["{设计要求与映射的对应关系}"],
    "constraint_compliance": ["{约束条件遵循情况}"],
    "success_criteria_mapping": ["{成功标准的映射关系}"]
  }
}
```

##### 定制化内容生成规则
```markdown
## 定制映射内容生成的核心规则

### 1. 设计文档特征驱动的字段设计
**执行原则**: 映射文件的每个字段都必须基于设计文档的具体特征和要求定制

**Spring数据访问框架示例**:
```json
"spring_data_mapping": {
  "jpa_configuration": {
    "entity_packages": "{从设计文档提取的实体包路径}",
    "repository_interfaces": ["{基于设计文档的Repository接口设计}"],
    "transaction_management": "{从设计文档提取的事务管理策略}",
    "lazy_loading_strategy": "{设计文档中的懒加载策略}"
  }
}
```

**微服务通信框架示例**:
```json
"service_communication_mapping": {
  "service_discovery": {
    "registry_type": "{从设计文档提取的注册中心类型}",
    "service_registration": ["{基于设计文档的服务注册配置}"],
    "load_balancing": "{设计文档中的负载均衡策略}"
  }
}
```

### 2. 业务领域驱动的约束设计
**执行原则**: 基于设计文档的业务领域特点，设计专门的约束和验证机制

**数据库中间件领域约束**:
```json
"database_constraints": {
  "connection_pool_limits": "{从设计文档提取的连接池限制}",
  "transaction_isolation": "{设计文档中的事务隔离级别}",
  "query_performance_thresholds": "{设计文档的查询性能阈值}"
}
```

**异常处理领域约束**:
```json
"exception_handling_constraints": {
  "error_code_ranges": ["{从设计文档提取的错误码范围}"],
  "exception_hierarchy": "{设计文档中的异常层次结构}",
  "logging_requirements": "{设计文档的日志记录要求}"
}
```

### 3. 技术栈驱动的实施指导
**执行原则**: 基于设计文档的技术栈选择，提供精确的技术实施指导

**Maven构建系统指导**:
```json
"maven_build_guidance": {
  "dependency_management": {
    "parent_pom": "{从设计文档提取的父POM配置}",
    "version_properties": ["{基于设计文档的版本属性}"],
    "dependency_exclusions": ["{设计文档中的依赖排除配置}"]
  }
}
```

**Docker容器化指导**:
```json
"containerization_guidance": {
  "base_image": "{从设计文档提取的基础镜像}",
  "environment_variables": ["{基于设计文档的环境变量}"],
  "volume_mappings": ["{设计文档中的数据卷映射}"]
}
```
```

#### 映射文件智能生成算法
```markdown
## 智能生成算法流程

### 步骤1: 设计文档深度特征提取
```python
def extract_design_features(design_document):
    features = {
        'technology_stack': extract_tech_stack(design_document),
        'business_domain': extract_business_domain(design_document),
        'architecture_pattern': extract_architecture_pattern(design_document),
        'complexity_level': assess_complexity(design_document),
        'risk_factors': extract_risks(design_document),
        'integration_requirements': extract_integrations(design_document)
    }
    return features
```

### 步骤2: 映射类型智能决策
```python
def determine_mapping_types(features):
    mapping_types = []
    
    # 基于技术栈决策
    if 'Spring' in features['technology_stack']:
        mapping_types.append('Spring模块依赖映射')
        mapping_types.append('Maven构建配置映射')
    
    # 基于业务领域决策
    if features['business_domain'] == '数据访问':
        mapping_types.append('数据模型映射')
        mapping_types.append('数据库配置映射')
    
    # 基于架构复杂度决策
    if features['complexity_level'] == 'L3':
        mapping_types.extend(['监控体系映射', '运维部署映射'])
    
    return mapping_types
```

### 步骤3: 定制化内容生成
```python
def generate_custom_mapping(mapping_type, features, design_content):
    template = get_base_template(mapping_type)
    customized_content = customize_template(template, features, design_content)
    return customized_content
```

### 步骤4: 序号自动分配与文件生成
```python
def generate_mapping_files(mapping_types, plan_directory):
    existing_files = scan_existing_files(plan_directory)
    next_sequence = calculate_next_sequence(existing_files)
    
    for i, mapping_type in enumerate(mapping_types):
        sequence_number = next_sequence + i
        filename = f"{sequence_number:02d}-{mapping_type}.json"
        generate_file(filename, mapping_content)
```
```

#### 质量保证与验证机制
```markdown
## 定制映射质量保证机制

### 1. 设计文档符合度验证
- [ ] **特征提取准确性**: 提取的特征与设计文档内容100%匹配
- [ ] **约束传承完整性**: 设计文档的所有约束都在映射中体现
- [ ] **目标对齐性**: 映射目标与设计文档目标完全一致

### 2. 技术实施可行性验证
- [ ] **技术栈兼容性**: 所有技术配置与实际技术栈兼容
- [ ] **路径真实性**: 所有文件路径基于架构扫描结果
- [ ] **命令有效性**: 所有技术命令在目标环境中有效

### 3. 映射逻辑正确性验证
- [ ] **依赖关系合理性**: 依赖关系逻辑正确，无循环依赖
- [ ] **执行序列可行性**: 执行序列符合技术约束和业务逻辑
- [ ] **风险评估客观性**: 风险等级评估客观准确

### 4. 定制化程度验证
- [ ] **非通用性**: 生成的映射具有明显的定制化特征
- [ ] **专业针对性**: 映射内容专门针对该设计文档的特定需求
- [ ] **差异化程度**: 与标准模板有明显差异，体现定制价值
```

#### 集成到专用提示词的执行指导
```markdown
## 📋 第四步：生成定制化映射文件（设计文档驱动）

### 4.1 设计文档特征深度分析
**执行要求**: 深度分析设计文档的技术栈、业务领域、架构模式特征

**分析维度**:
```bash
# 技术栈特征分析
analyze_tech_stack() {
    核心框架识别
    中间件组件识别  
    构建工具识别
    运行环境识别
}

# 业务领域特征分析
analyze_business_domain() {
    领域类型识别（数据访问/API网关/缓存/异常处理等）
    领域特定约束提取
    领域关键流程识别
}

# 架构复杂度特征分析
analyze_architecture_complexity() {
    模块数量统计
    依赖层级深度分析
    集成复杂度评估
}
```

### 4.2 映射类型智能决策
**目标**: 基于特征分析结果，智能决策需要生成的映射文件类型
**决策规则**: 严格基于设计文档内容，避免过度生成或遗漏关键映射

### 4.3 定制化映射内容生成
**目标**: 为每个决策的映射类型生成完全定制化的内容
**生成路径**: `{实施计划所在目录}/{自动分配序号}-{定制映射类型}.json`

**定制化要求**:
- 映射文件结构基于设计文档特征完全定制
- 所有字段内容基于设计文档具体要求填充
- 验证机制基于设计文档约束设计
- 执行指导基于设计文档技术栈定制

### 4.4 定制映射验证检查点
- [ ] **设计驱动性**: 映射类型和内容100%基于设计文档特征
- [ ] **定制化程度**: 映射具有明显的针对性，非通用模板
- [ ] **技术准确性**: 所有技术细节与设计文档和架构扫描一致
- [ ] **实施可行性**: 映射可以直接指导技术实施
- [ ] **约束传承**: 设计文档约束完整传承到映射中
```

## 🎯 {项目名称}专用使用方式

根据{设计文档名称}的特点，标准工作流程是：

1. **提供设计文档链接**: "设计文档: {设计文档路径}"
2. **提供专用提示词链接**: "提示词: {生成的专用提示词路径}"  
3. **指定输出目录**: "输出目录: {建议的输出目录路径}"
4. **AI自动执行**: 调研架构 → 学习设计 → 严格按模板生成文档

## 🌟 专用提示词特点

{基于设计文档特征的专用提示词特点清单}
```

**验证锚点**: 生成完整、可用、针对性强的专用实施计划提示词

## 🔍 第七步：质量验证与文件保存

### 6.1 专用提示词质量验证
**目标**: 确保生成的专用提示词质量和完整性

#### 完整性验证检查点
- [ ] **设计约束覆盖**: 设计文档的所有约束都在提示词中有对应转化
- [ ] **技术栈适配**: 所有技术栈相关的命令和路径都正确适配
- [ ] **业务领域适配**: 风险场景和验证标准都符合业务领域特点
- [ ] **关键点映射**: 设计关键点与实施关键点100%建立映射关系
- [ ] **复杂度匹配**: 提示词的复杂度处理策略与设计文档复杂度匹配

#### 可用性验证检查点
- [ ] **语法正确性**: 所有命令、代码示例、文件路径语法正确
- [ ] **逻辑一致性**: 执行流程逻辑清晰，前后一致，无矛盾
- [ ] **操作可执行**: 所有步骤都有具体的执行指导和验证方法
- [ ] **错误处理**: 包含完整的错误处理和回滚机制
- [ ] **边界清晰**: 实施范围边界明确，包含和排除范围清晰

#### 针对性验证检查点
- [ ] **设计依据性**: 所有约束和要求都有明确的设计文档依据
- [ ] **项目专用性**: 生成的内容专门针对该设计文档，非通用模板
- [ ] **约束传承性**: 设计文档的约束逻辑在实施计划中完整传承
- [ ] **决策链完整**: 设计文档的关键决策链在实施过程中完整体现

### 6.2 文件生成与保存
**使用工具**: `edit_file`
**目标**: 在设计文档同级目录生成专用提示词文件

**执行指引**:
- **文件路径**: `{设计文档目录}/AI生成{项目名称}实施计划的专用提示词.md`
- **文件内容**: 完整组装的专用提示词内容
- **编码格式**: UTF-8
- **换行符**: 统一使用LF

**验证**: 文件成功生成，路径正确，内容完整

## ✅ 执行检查清单

### 强制性架构扫描检查
- [ ] 项目整体架构已深度扫描
- [ ] 核心模块依赖关系已分析
- [ ] 技术栈配置已详细扫描
- [ ] 现状问题和改进点已识别
- [ ] 所有分析基于实际代码文件
- [ ] 无任何推测性结论

### 用户指令识别与自动启动检查
- [ ] **用户标准指令格式已识别**
- [ ] **设计文档路径已成功提取**
- [ ] **设计文档目录已自动推断**
- [ ] **项目信息已自动提取（名称、版本、技术栈）**
- [ ] **输出文件路径已确定**

### 设计文档解析检查
- [ ] **设计文档目录已完整扫描**
- [ ] **所有相关设计文档已识别和分类**
- [ ] **设计文档完整性已验证**
- [ ] **设计文档一致性已检查**
- [ ] **文档阅读策略已制定**
- [ ] **序列化阅读已严格执行（按01→02→03...顺序）**
- [ ] **文档间逻辑依赖关系已验证**
- [ ] **概念体系已完整建立**
- [ ] 设计文档已完全理解
- [ ] 核心设计要素已100%提取
- [ ] 技术约束已完整识别
- [ ] 架构复杂度已准确评估
- [ ] 关键决策点已系统编号
- [ ] 风险等级已正确识别

### 关键点预分析与负载评估检查
- [ ] 设计文档关键点完整性已验证
- [ ] AI认知负载已准确评估
- [ ] AI处理步骤已智能分解
- [ ] 负载分解策略已设计
- [ ] 关键点缺失已识别和补强

### 架构-设计融合分析检查
- [ ] 架构-设计一致性已验证
- [ ] 冲突解决策略已制定
- [ ] 实施路径已基于实际情况规划
- [ ] 实际约束条件已整合

### 模板适配检查
- [ ] 技术栈特征已准确识别
- [ ] 业务领域模式已正确匹配
- [ ] 复杂度驱动适配策略已确定
- [ ] 验证命令已正确定制
- [ ] 风险场景已专业定制

### 专用提示词生成检查
- [ ] **分步骤生成策略已制定**
- [ ] **Step1基础框架模块已生成**
- [ ] **Step2核心执行模块已生成**  
- [ ] **Step3配套支撑模块已生成**
- [ ] **Step4最终组装已完成**
- [ ] 设计约束转化模块已定制
- [ ] 技术栈验证命令已适配
- [ ] 实施步骤已基于功能定制
- [ ] 关键点映射矩阵已建立
- [ ] 风险应对方案已专业化
- [ ] **映射文件模板已集成**
- [ ] **映射文件生成规则已制定**

### 映射文件生成检查（新增）
- [ ] **映射文件类型已智能选择**: 基于设计文档特征自动选择映射类型
- [ ] **映射文件模板已准备**: 所有必需的映射模板已包含在提示词中
- [ ] **序号分配规则已设计**: 自动序号分配机制已建立
- [ ] **内容填充规则已明确**: 所有变量的填充规则已详细说明
- [ ] **验证机制已建立**: JSON格式、路径真实性、逻辑一致性验证要求已明确
- [ ] **集成指导已完成**: 映射文件生成已集成到专用提示词的执行流程中

### 质量验证检查
- [ ] 完整性验证通过
- [ ] 可用性验证通过
- [ ] 针对性验证通过
- [ ] 语法正确性确认
- [ ] 逻辑一致性确认
- [ ] **映射文件要求完整性确认**
- [ ] **映射模板可用性确认**

### 文件生成检查
- [ ] 文件路径正确
- [ ] 文件内容完整
- [ ] 格式规范统一
- [ ] 可直接使用
- [ ] **映射文件生成要求已集成**
- [ ] **映射文件验证机制已包含**

---

**严格执行原则**:
1. **设计驱动**: 所有定制都基于设计文档的具体特征和要求
2. **约束传承**: 设计文档的每个约束都必须在专用提示词中体现
3. **技术适配**: 根据实际技术栈定制所有命令和验证方法
4. **领域专业**: 基于业务领域特点定制风险场景和验证标准
5. **质量优先**: 确保生成的专用提示词质量高、可用性强
6. **完整性保证**: 覆盖从设计解析到实施验证的完整流程
7. **针对性强**: 生成的提示词专门针对该设计文档，具有唯一性

**请严格按照上述元提示词流程，分析设计文档并生成专用的实施计划提示词！** 