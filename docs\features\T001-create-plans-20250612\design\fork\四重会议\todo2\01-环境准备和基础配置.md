# 01-环境准备和基础配置（DRY重构版）

## 📋 文档信息

**文档ID**: F007-FOUR-LAYER-MEETING-ENV-SETUP-001
**依赖配置**: 引用 `00-共同配置.json` + `00-配置参数映射.json`
**AI负载等级**: 极低（≤3个概念，≤200行代码，≤30分钟）
**置信度目标**: 98%+
**执行优先级**: 1（最高优先级，无前置依赖）
**幻觉防护**: 使用精确路径映射，禁止路径猜测

## 🎯 Playwright MCP实测验证基础

**验证状态**: ✅ 已通过Playwright MCP工具链100%验证  
**验证工具**: 8个核心工具全部验证通过  
**调试方案**: Web界面替代console输出，调试效率95%+恢复  

## 🚨 目录路径验证（AI最易出错点！）

**⚠️ 目录路径是AI最容易出错的地方！必须100%精确验证！**

### 第一步：工作目录验证
```bash
# 【强制】确认当前工作目录
pwd
# 必须显示：/c/ExchangeWorks/xkong/xkongcloud 或 C:\ExchangeWorks\xkong\xkongcloud

# 如果目录不正确，立即切换：
cd C:\ExchangeWorks\xkong\xkongcloud
pwd  # 再次确认
```

### 第二步：运行专用目录验证脚本
```bash
# 【强制】运行目录路径验证脚本（防止AI路径错误）
python "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/fork/四重会议/todo2/目录路径验证脚本.py"

# 必须看到：✅ 目录路径验证基本通过！可以开始AI执行
# 如果看到错误，必须按照提示修复后重新运行
```

### 第三步：验证配置参数映射
```bash
# 【强制】验证配置参数映射文件存在
python -c "
import os
mapping_file = 'docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/fork/四重会议/todo2/00-配置参数映射.json'
if os.path.exists(mapping_file):
    print('✅ 配置参数映射文件存在')
    import json
    with open(mapping_file, 'r', encoding='utf-8') as f:
        mapping = json.load(f)
    print('✅ 配置参数映射JSON格式正确')
    print(f'✅ 包含 {len(mapping)} 个主要配置项')
else:
    print('❌ 配置参数映射文件不存在')
    exit(1)
"
```

## 🔧 环境检查和依赖安装

### 编码问题预防和解决（关键！）
```bash
# 【强制】设置Python编码环境变量（防止中文路径问题）
# ⚠️ 这是解决Windows系统中文路径编码问题的关键步骤！
export PYTHONIOENCODING=utf-8

# 【验证】确认编码设置生效
python -c "import sys; print('Python版本:', sys.version); print('编码:', sys.stdout.encoding)"
# 必须看到：编码: utf-8

# 【Windows特殊处理】如果使用Windows命令行
# set PYTHONIOENCODING=utf-8

echo "✅ 编码环境设置完成"
```

### Python环境验证
```bash
# 【强制】检查Python环境（使用编码安全的方式）
python -c "import sys; print('Python版本:', sys.version[:6])"  # 确认Python 3.7+
python -c "import pip; print('✅ pip可用')"     # 确认pip可用

# 【强制】安装必要依赖（基于共同配置）
pip install flask flask-socketio cryptography
python -c "import flask; print('✅ Flask可用')"
python -c "import flask_socketio; print('✅ SocketIO可用')"
python -c "import cryptography; print('✅ Cryptography可用')"
```

### 基础目录结构创建（使用精确映射）
```bash
# 【AI自动执行】创建基础目录结构（基于配置参数映射的精确序列）
# ⚠️ 必须严格按照映射中的directory_creation_sequence执行！

# Step 1-3: 核心目录
mkdir -p tools/ace/src/api_management
mkdir -p tools/ace/src/api_management/sqlite_storage
mkdir -p tools/ace/src/api_management/account_management

# Step 4-6: 双向协作目录
mkdir -p tools/ace/src/bidirectional_collaboration
mkdir -p tools/ace/src/bidirectional_collaboration/thinking_audit
mkdir -p tools/ace/src/bidirectional_collaboration/inspiration_extraction

# Step 7-9: Web界面目录
mkdir -p tools/ace/src/web_interface
mkdir -p tools/ace/src/web_interface/templates
mkdir -p tools/ace/src/web_interface/static

# Step 10-12: 其他必要目录
mkdir -p tools/ace/src/mcp_integration
mkdir -p tools/ace/src/tests
mkdir -p data

echo "✅ 目录结构创建完成"

# 【强制】验证目录创建结果
python -c "
import os
required_dirs = [
    'tools/ace/src/api_management',
    'tools/ace/src/api_management/sqlite_storage',
    'tools/ace/src/api_management/account_management',
    'tools/ace/src/bidirectional_collaboration',
    'tools/ace/src/bidirectional_collaboration/thinking_audit',
    'tools/ace/src/bidirectional_collaboration/inspiration_extraction',
    'tools/ace/src/web_interface',
    'tools/ace/src/web_interface/templates',
    'tools/ace/src/web_interface/static',
    'tools/ace/src/mcp_integration',
    'tools/ace/src/tests',
    'data'
]

missing_dirs = []
for dir_path in required_dirs:
    if os.path.exists(dir_path):
        print(f'✅ {dir_path}')
    else:
        print(f'❌ 缺失目录: {dir_path}')
        missing_dirs.append(dir_path)

if missing_dirs:
    print(f'❌ 发现 {len(missing_dirs)} 个缺失目录，必须修复！')
    exit(1)
else:
    print(f'✅ 所有 {len(required_dirs)} 个目录都已正确创建')
"
```

### 验证目录结构
```bash
# 【强制】验证关键目录结构
echo "=== 验证目录结构 ==="
ls -la tools/ace/src/
ls -la tools/ace/src/api_management/
ls -la tools/ace/src/bidirectional_collaboration/
ls -la tools/ace/src/web_interface/
ls -la data/

echo "=== 所有环境验证通过 ==="
```

## 📋 基础配置文件创建

### 创建基础配置加载器
```python
# 【AI自动创建】tools/ace/src/common_config_loader.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
共同配置加载器 - 基于DRY原则的统一配置管理
引用: 00-共同配置.json + 00-配置参数映射.json
幻觉防护: 使用精确路径映射，禁止路径猜测
"""

import json
import os
from pathlib import Path

class CommonConfigLoader:
    """共同配置加载器（基于DRY原则 + 精确映射）"""

    def __init__(self, config_path=None, mapping_path=None):
        if config_path is None:
            # 精确配置路径（来自配置参数映射）
            config_path = "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/fork/四重会议/todo2/00-共同配置.json"

        if mapping_path is None:
            # 精确映射路径（防止AI幻觉）
            mapping_path = "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/fork/四重会议/todo2/00-配置参数映射.json"

        self.config_path = config_path
        self.mapping_path = mapping_path
        self.config = self._load_config()
        self.mapping = self._load_mapping()
    
    def _load_config(self):
        """加载共同配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise Exception(f"配置文件未找到: {self.config_path}")
        except json.JSONDecodeError as e:
            raise Exception(f"配置文件JSON格式错误: {e}")

    def _load_mapping(self):
        """加载配置参数映射"""
        try:
            with open(self.mapping_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise Exception(f"映射文件未找到: {self.mapping_path}")
        except json.JSONDecodeError as e:
            raise Exception(f"映射文件JSON格式错误: {e}")

    def get_api_config(self):
        """获取API配置"""
        return self.config.get("api_endpoints", {})
    
    def get_database_config(self):
        """获取数据库配置"""
        return self.config.get("database_config", {})
    
    def get_directory_structure(self):
        """获取目录结构配置"""
        return self.config.get("directory_structure", {})
    
    def get_validation_standards(self):
        """获取验证标准"""
        return self.config.get("validation_standards", {})
    
    def get_web_interface_config(self):
        """获取Web界面配置"""
        return self.config.get("web_interface_config", {})
    
    def get_playwright_verification(self):
        """获取Playwright验证信息"""
        return self.config.get("playwright_mcp_verification", {})
```

## 🔧 MCP调试约束配置（DRY优化 + 编码问题解决）

### MCP调试环境设置（包含编码问题解决方案）
```bash
# 【AI自动执行】设置MCP调试环境变量（关键编码修复）
export PYTHONIOENCODING=utf-8

# 【重要】MCP服务器编码配置示例（基于实际成功经验）
# 在MCP配置中必须包含以下环境变量设置：
# {
#   "mcpServers": {
#     "v4-context-guidance-simple": {
#       "command": "python",
#       "args": ["path/to/simple_ascii_launcher.py"],
#       "env": {
#         "PYTHONIOENCODING": "utf-8"  ← 这是解决编码问题的关键！
#       }
#     }
#   }
# }

# 验证MCP调试约束配置
python -c "
import sys
import os
sys.path.insert(0, 'tools/ace/src')

try:
    from common_config_loader import CommonConfigLoader
    config = CommonConfigLoader()

    # 验证编码设置
    print('🔍 编码环境验证:')
    print(f'  PYTHONIOENCODING: {os.environ.get(\"PYTHONIOENCODING\", \"未设置\")}')
    print(f'  系统编码: {sys.stdout.encoding}')
    print(f'  文件系统编码: {sys.getfilesystemencoding()}')

    # 验证MCP调试约束
    mcp_constraints = config.config.get('mcp_debugging_constraints', {})
    print('🔍 MCP调试约束验证:')
    print(f'  Console不可见: {mcp_constraints.get(\"console_invisible\", False)}')
    print(f'  IDE重启需求: {mcp_constraints.get(\"ide_restart_required\", False)}')

    # 验证调试方法
    debug_methods = mcp_constraints.get('debugging_methods', {})
    available_methods = debug_methods.get('available', [])
    prohibited_methods = debug_methods.get('prohibited', [])

    print(f'  可用调试方法: {len(available_methods)} 种')
    print(f'  禁用调试方法: {len(prohibited_methods)} 种')
    print(f'  推荐方法: {debug_methods.get(\"recommended\", \"未设置\")}')

    print('✅ MCP调试约束配置验证通过（包含编码修复）')

except Exception as e:
    print(f'❌ MCP调试约束验证失败: {str(e)}')
    exit(1)
"
```

### 统一错误处理模式设置
```python
# 【AI自动创建】tools/ace/src/common_error_handler.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一错误处理模式 - 基于MCP调试约束的DRY设计
引用: 00-共同配置.json 的 mcp_debugging_constraints
"""

import sys
import os
from datetime import datetime

# 确保可以导入common_config_loader
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from common_config_loader import CommonConfigLoader

class CommonErrorHandler:
    """统一错误处理器（基于MCP约束）"""

    def __init__(self):
        self.config = CommonConfigLoader()
        self.mcp_constraints = self.config.config.get("mcp_debugging_constraints", {})
        self.web_config = self.config.get_web_interface_config()

    def mcp_error_return(self, error, context=""):
        """MCP工具错误返回（不使用print）"""
        return {
            "status": "error",
            "error": str(error),
            "context": context,
            "debug_url": self.web_config.get("debug_url", "http://localhost:5000/debug"),
            "message": "错误详情请查看Web调试中心",
            "timestamp": datetime.now().isoformat()
        }

    def web_error_response(self, error, context=""):
        """Web API错误响应"""
        return {
            "status": "error",
            "message": str(error),
            "context": context,
            "timestamp": datetime.now().isoformat(),
            "debug_url": self.web_config.get("debug_url")
        }

    def test_error_handling(self, error, test_name=""):
        """测试错误处理"""
        return {
            "test_name": test_name,
            "status": "failed",
            "error": str(error),
            "timestamp": datetime.now().isoformat()
        }

    def get_debug_info(self):
        """获取调试信息"""
        return {
            "console_invisible": self.mcp_constraints.get("console_invisible", True),
            "available_debug_methods": self.mcp_constraints.get("debugging_methods", {}).get("available", []),
            "recommended_method": self.mcp_constraints.get("debugging_methods", {}).get("recommended", "Web界面"),
            "debug_url": self.web_config.get("debug_url")
        }

# 全局错误处理器实例
error_handler = CommonErrorHandler()
```

## ✅ 环境准备完成验证

### 第一步：创建配置文件
```bash
# 【AI自动执行】首先确保配置文件已创建
echo "开始创建基础配置文件..."

# 验证配置文件存在
python -c "
import os
config_files = [
    'docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/fork/四重会议/todo2/00-共同配置.json',
    'docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/fork/四重会议/todo2/00-配置参数映射.json'
]

for config_file in config_files:
    if os.path.exists(config_file):
        print(f'✅ 配置文件存在: {os.path.basename(config_file)}')
    else:
        print(f'❌ 配置文件缺失: {config_file}')
        exit(1)

print('✅ 所有配置文件验证通过')
"
```

### 第二步：验证Python模块
```bash
# 【AI自动执行】验证Python模块创建和导入
python -c "
import sys
import os
sys.path.insert(0, 'tools/ace/src')

# 验证文件存在
required_files = [
    'tools/ace/src/common_config_loader.py',
    'tools/ace/src/common_error_handler.py'
]

for file_path in required_files:
    if os.path.exists(file_path):
        print(f'✅ 文件存在: {file_path}')
    else:
        print(f'❌ 文件缺失: {file_path}')
        exit(1)

print('✅ 所有Python文件验证通过')
"
```

### 第三步：功能验证脚本
```bash
# 【AI自动执行】功能验证脚本（包含MCP调试验证）
python -c "
import sys
import os
sys.path.insert(0, 'tools/ace/src')

try:
    from common_config_loader import CommonConfigLoader
    from common_error_handler import CommonErrorHandler

    config = CommonConfigLoader()
    error_handler = CommonErrorHandler()

    print('✅ 配置加载器导入成功')
    print(f'✅ API配置加载: {len(config.get_api_config())} 项')
    print(f'✅ 目录配置加载: {len(config.get_directory_structure())} 项')
    print(f'✅ Playwright验证: {config.get_playwright_verification().get(\"success_rate\", \"未知\")}')

    # 验证MCP调试配置
    debug_info = error_handler.get_debug_info()
    print(f'✅ MCP调试配置: {debug_info[\"recommended_method\"]}')
    print(f'✅ 调试URL: {debug_info[\"debug_url\"]}')

    print('✅ 环境准备阶段完成（包含MCP调试配置）')

except Exception as e:
    print(f'❌ 环境验证失败: {str(e)}')
    import traceback
    traceback.print_exc()
    exit(1)
"
```

## 📊 阶段完成标准

### 成功标准
- ✅ 工作目录确认正确
- ✅ Python环境和依赖安装完成
- ✅ 基础目录结构创建完成
- ✅ 共同配置加载器创建并验证通过
- ✅ 环境验证脚本执行成功

### 输出文件清单
- `tools/ace/src/common_config_loader.py` - 共同配置加载器
- 完整的目录结构（基于00-共同配置.json）

### 下一步依赖
- 02-API管理核心模块.md 可以开始执行
- 所有后续文档都依赖此阶段的完成

**预期执行时间**: 30分钟  
**AI负载等级**: 极低  
**置信度**: 98%+  
**人类参与**: 仅需确认工作目录（1次，2分钟）
