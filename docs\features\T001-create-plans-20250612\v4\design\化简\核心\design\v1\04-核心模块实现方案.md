# 项目经理核心模块实现方案 - 统一风险检测与代码生成

## 📋 文档信息
- **文档版本**: V1.0
- **创建日期**: 2025-01-16
- **实现目标**: 基于DRY优化原则，实现统一的项目经理架构风险检测和智能代码生成核心模块
- **技术栈**: Python 3.11+, NetworkX 3.0+, Flask 2.0+, Jinja2

## 🏗️ 核心模块架构

### **模块依赖关系图**

```mermaid
graph TB
    subgraph "Web接口层"
        A1[NineGridController<br/>九宫格控制器]
        A2[ProjectManagerRiskAPI<br/>项目经理风险检测API]
        A3[WebSocketHandler<br/>实时通信]
    end

    subgraph "业务逻辑层"
        B1[ProjectManagerRiskDetector<br/>项目经理风险检测器]
        B2[ArchitectureAnalyzer<br/>架构分析器]
        B3[IntelligentCodeGenerator<br/>智能代码生成器]
        B4[ReportGenerator<br/>报告生成器]
    end

    subgraph "算法引擎层"
        C1[NetworkXAnalyzer<br/>图分析引擎]
        C2[SecurityRuleEngine<br/>安全规则引擎]
        C3[ContradictionDetector<br/>矛盾检测器]
        C4[ComponentInferrer<br/>组件推断器]
    end

    subgraph "数据访问层"
        D1[DocumentParser<br/>文档解析器]
        D2[TemplateManager<br/>模板管理器]
        D3[ConfigurationLoader<br/>配置加载器]
        D4[ResultStorage<br/>结果存储器]
        D5[DocumentContradictionPreprocessor<br/>文档矛盾预处理器]
    end

    A1 --> B1
    A1 --> B3
    A2 --> B1
    A2 --> B4
    A3 --> B1
    B1 --> C1
    B1 --> C2
    B1 --> C3
    B2 --> C4
    B3 --> D2
    B4 --> D4
    C1 --> D1
    C1 --> D5
    C2 --> D3
```

## 🔧 核心模块详细设计

### **1. ProjectManagerRiskDetector (项目经理风险检测器)**

**职责**: 整合算法.py和检查.py的检测能力，提供统一的项目经理风险检测接口

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProjectManagerRiskDetector - 项目经理风险检测器
基于DRY原则整合算法.py和检查.py的检测能力
"""

import asyncio
import networkx as nx
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

# 导入集成适配器
from ..integration.api_manager_adapter import APIManagerAdapter, AIServiceRequest
from ..integration.task_executor_adapter import TaskExecutorAdapter, ArchitectureTaskRequest, ArchitectureTaskType

# 导入算法引擎
from ..algorithms.networkx_analyzer import NetworkXAnalyzer
from ..algorithms.security_rule_engine import SecurityRuleEngine
from ..algorithms.contradiction_detector import ContradictionDetector
from .document_parser import DocumentParser
from .document_contradiction_preprocessor import DocumentContradictionPreprocessor


class RiskLevel(Enum):
    """风险等级枚举"""
    CRITICAL = "CRITICAL"  # 致命级 - 100%可检测
    HIGH = "HIGH"         # 严重级 - 90%可检测
    MEDIUM = "MEDIUM"     # 重要级 - 80%可检测
    LOW = "LOW"          # 隐蔽级 - 70%可检测


@dataclass
class ArchitecturalRisk:
    """统一架构风险数据结构"""
    id: str
    type: str  # "circular_dependency", "security_violation", etc.
    level: RiskLevel
    title: str
    description: str
    components: List[str]
    impact_analysis: str
    solution_strategy: str
    confidence: float
    detection_method: str
    evidence: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "type": self.type,
            "level": self.level.value,
            "title": self.title,
            "description": self.description,
            "components": self.components,
            "impact_analysis": self.impact_analysis,
            "solution_strategy": self.solution_strategy,
            "confidence": self.confidence,
            "detection_method": self.detection_method,
            "evidence": self.evidence
        }


@dataclass
class RiskDetectionReport:
    """风险检测报告"""
    total_risks: int
    risks_by_level: Dict[RiskLevel, List[ArchitecturalRisk]]
    overall_confidence: float
    detection_summary: Dict[str, Any]
    recommendations: List[str]
    execution_time: float
    
    def get_critical_risks(self) -> List[ArchitecturalRisk]:
        """获取致命级风险"""
        return self.risks_by_level.get(RiskLevel.CRITICAL, [])
    
    def get_risk_count_by_level(self, level: RiskLevel) -> int:
        """获取指定等级的风险数量"""
        return len(self.risks_by_level.get(level, []))


class ProjectManagerRiskDetector:
    """项目经理风险检测器 - 矛盾推理系统 + 传统算法后备"""

    def __init__(self, api_adapter: APIManagerAdapter, task_adapter: TaskExecutorAdapter,
                 config: Optional[Dict[str, Any]] = None):
        """初始化检测器"""
        self.config = config or {}

        # 集成适配器
        self.api_adapter = api_adapter
        self.task_adapter = task_adapter

        # 主检测系统：矛盾推理引擎
        self.contradiction_driven_detector = ContradictionDrivenRiskDetector(api_adapter, task_adapter)

        # 后备检测系统：传统算法（保证85%基础覆盖率）
        self.fallback_system = FallbackDetectionSystem()

        # 结果融合系统
        self.result_fusion = ResultFusionAndArbitration()

        # 文档矛盾预处理器
        self.document_contradiction_preprocessor = DocumentContradictionPreprocessor()

        # 检测器配置
        self.enable_contradiction_inference = self.config.get('enable_contradiction_inference', True)
        self.enable_fallback_system = self.config.get('enable_fallback_system', True)
        self.confidence_threshold = self.config.get('confidence_threshold', 0.8)
        
    async def detect_all_risks(self, work_directory: str, 
                              progress_callback: Optional[callable] = None) -> RiskDetectionReport:
        """
        项目经理风险检测入口
        
        Args:
            work_directory: 工作目录路径
            progress_callback: 进度回调函数
            
        Returns:
            RiskDetectionReport: 完整的项目风险检测报告
        """
        start_time = time.time()
        
        if progress_callback:
            await progress_callback({"stage": "parsing", "progress": 0})
        
        # 1. 解析架构文档
        architecture_data = await self.document_parser.parse_directory(work_directory)
        architecture_graph = self._build_architecture_graph(architecture_data)
        
        if progress_callback:
            await progress_callback({"stage": "detection", "progress": 20})
        
        # 2. 执行矛盾推理检测（实现93-95%自动化覆盖率）
        all_risks = []

        if self.enable_contradiction_inference:
            # 主检测系统：矛盾推理引擎
            if progress_callback:
                await progress_callback({"stage": "contradiction_inference", "progress": 20})

            contradiction_risks = await self.contradiction_driven_detector.detect_risks_with_contradiction_inference(
                architecture_graph, {"work_directory": work_directory}
            )
            all_risks.extend(contradiction_risks)

            if progress_callback:
                await progress_callback({"stage": "contradiction_inference_completed", "progress": 70})

        if self.enable_fallback_system:
            # 后备检测系统：传统算法
            if progress_callback:
                await progress_callback({"stage": "fallback_detection", "progress": 75})

            fallback_risks = await self.fallback_system.run_fallback_detection(
                architecture_graph, {"work_directory": work_directory}
            )

            # 结果融合和置信度仲裁
            if progress_callback:
                await progress_callback({"stage": "result_fusion", "progress": 85})

            if self.enable_contradiction_inference:
                # 融合矛盾推理结果和后备检测结果
                all_risks = await self.result_fusion.fuse_and_arbitrate_results(
                    contradiction_risks, fallback_risks
                )
            else:
                # 仅使用后备检测结果
                all_risks = fallback_risks
        
        if progress_callback:
            await progress_callback({"stage": "analysis", "progress": 80})
        
        # 3. 风险分析和优先级排序
        prioritized_risks = self._prioritize_risks(all_risks)
        risks_by_level = self._group_risks_by_level(prioritized_risks)
        
        # 4. 生成检测报告
        overall_confidence = self._calculate_overall_confidence(all_risks)
        recommendations = self._generate_recommendations(prioritized_risks)
        
        if progress_callback:
            await progress_callback({"stage": "completed", "progress": 100})
        
        execution_time = time.time() - start_time
        
        return RiskDetectionReport(
            total_risks=len(all_risks),
            risks_by_level=risks_by_level,
            overall_confidence=overall_confidence,
            detection_summary=self._generate_detection_summary(all_risks),
            recommendations=recommendations,
            execution_time=execution_time
        )

    async def detect_document_contradictions(self, doc_paths: List[str]) -> List[ArchitecturalRisk]:
        """
        文档矛盾检测 - 新增功能，支持通用00-xx文档矛盾检测

        Args:
            doc_paths: 文档路径列表

        Returns:
            List[ArchitecturalRisk]: 检测到的文档矛盾风险列表
        """
        document_risks = []

        # 1. 文档预处理
        processed_docs = await self.document_contradiction_preprocessor.preprocess_documents(doc_paths)

        # 2. 文档间矛盾检测
        for i, doc1 in enumerate(processed_docs):
            for j, doc2 in enumerate(processed_docs[i+1:], i+1):
                contradictions = await self._detect_document_pair_contradictions(doc1, doc2)
                document_risks.extend(contradictions)

        # 3. 文档内部一致性检测
        for doc in processed_docs:
            internal_contradictions = await self._detect_document_internal_contradictions(doc)
            document_risks.extend(internal_contradictions)

        return document_risks

    async def _detect_circular_dependencies_with_ai(self, graph: nx.DiGraph) -> List[ArchitecturalRisk]:
        """检测循环依赖（NetworkX算法 + AI验证）"""
        risks = []

        # 1. 使用算法.py的NetworkX实现进行基础检测
        strongly_connected = list(nx.strongly_connected_components(graph))

        for i, component in enumerate(strongly_connected):
            if len(component) > 1:  # 多节点强连通分量即为循环依赖
                # 2. 使用AI适配器进行深度分析
                ai_request = AIServiceRequest(
                    task_type="risk_detection",
                    content=f"分析循环依赖: {list(component)}",
                    context={"graph_data": self._serialize_graph_component(graph, component)},
                    quality_requirements={"confidence_threshold": 0.9}
                )

                ai_response = await self.api_adapter.request_ai_service(ai_request)

                # 3. 结合NetworkX结果和AI分析
                risk = ArchitecturalRisk(
                    id=f"circular_dep_{i}",
                    type="circular_dependency",
                    level=RiskLevel.CRITICAL,
                    title=f"循环依赖: {' ↔ '.join(list(component)[:3])}{'...' if len(component) > 3 else ''}",
                    description=ai_response.content if ai_response.success else f"检测到{len(component)}个组件之间的循环依赖关系",
                    components=list(component),
                    impact_analysis="循环依赖可能导致系统启动失败、内存泄漏和维护困难",
                    solution_strategy="重构依赖关系，引入依赖注入或中介者模式",
                    confidence=min(0.98, ai_response.quality_score) if ai_response.success else 0.98,
                    detection_method="networkx_with_ai_verification",
                    evidence={
                        "component_count": len(component),
                        "dependency_chain": self._extract_dependency_chain(graph, component),
                        "ai_analysis": ai_response.content if ai_response.success else None
                    }
                )
                risks.append(risk)

        return risks
    
    async def _detect_security_violations_with_ai(self, graph: nx.DiGraph) -> List[ArchitecturalRisk]:
        """检测安全边界违反（结构化规则引擎 + AI验证）"""
        # 1. 使用结构化规则引擎进行基础检测
        base_violations = await self.security_rule_engine.detect_violations(graph)

        # 2. 对每个违反使用AI进行深度分析
        enhanced_violations = []
        for violation in base_violations:
            ai_request = AIServiceRequest(
                task_type="risk_detection",
                content=f"分析安全边界违反: {violation.description}",
                context={"violation_data": violation.evidence},
                quality_requirements={"confidence_threshold": 0.85}
            )

            ai_response = await self.api_adapter.request_ai_service(ai_request)

            # 增强违反信息
            if ai_response.success:
                violation.description = ai_response.content
                violation.confidence = min(violation.confidence, ai_response.quality_score)
                violation.detection_method = "security_rules_with_ai_verification"

            enhanced_violations.append(violation)

        return enhanced_violations

    async def _detect_contradictions_with_ai(self, graph: nx.DiGraph) -> List[ArchitecturalRisk]:
        """检测架构矛盾（统一矛盾检测框架 + AI分析）"""
        # 1. 使用统一矛盾检测框架进行基础检测
        base_contradictions = await self.contradiction_detector.detect_all_contradictions(graph)

        # 2. 使用AI进行矛盾深度分析
        enhanced_contradictions = []
        for contradiction in base_contradictions:
            ai_request = AIServiceRequest(
                task_type="analysis",
                content=f"深度分析架构矛盾: {contradiction.description}",
                context={"contradiction_data": contradiction.evidence},
                quality_requirements={"confidence_threshold": 0.8}
            )

            ai_response = await self.api_adapter.request_ai_service(ai_request)

            # 增强矛盾分析
            if ai_response.success:
                contradiction.impact_analysis = ai_response.content
                contradiction.confidence = min(contradiction.confidence, ai_response.quality_score)
                contradiction.detection_method = "contradiction_detector_with_ai_analysis"

            enhanced_contradictions.append(contradiction)

        return enhanced_contradictions

    async def _detect_critical_risks(self, graph: nx.DiGraph) -> List[ArchitecturalRisk]:
        """检测致命级风险 (100%自动化)"""
        critical_risks = []

        # 1. 循环依赖检测：强连通分量算法 + 依赖链分析
        circular_deps = await self._detect_circular_dependencies_with_ai(graph)
        critical_risks.extend(circular_deps)

        # 2. 单点故障识别：依赖计数 + 影响范围分析
        single_points = await self._detect_single_points_of_failure(graph)
        critical_risks.extend(single_points)

        # 3. 数据一致性缺失：事务边界 + 并发控制检测
        consistency_issues = await self._detect_data_consistency_issues(graph)
        critical_risks.extend(consistency_issues)

        # 4. 架构矛盾检测：多维度矛盾模式匹配
        contradictions = await self._detect_contradictions_with_ai(graph)
        critical_risks.extend(contradictions)

        return critical_risks

    async def _detect_high_risks(self, graph: nx.DiGraph) -> List[ArchitecturalRisk]:
        """检测严重级风险 (90%自动化)"""
        high_risks = []

        # 1. 安全架构缺陷：边界违反 + 权限提升检测
        security_violations = await self._detect_security_violations_with_ai(graph)
        high_risks.extend(security_violations)

        # 2. 性能反模式：瓶颈识别 + 资源竞争分析
        performance_issues = await self._detect_performance_antipatterns(graph)
        high_risks.extend(performance_issues)

        # 3. 紧耦合设计：依赖密度 + 变更影响分析
        coupling_issues = await self._detect_tight_coupling(graph)
        high_risks.extend(coupling_issues)

        return high_risks

    async def _detect_medium_risks(self, graph: nx.DiGraph) -> List[ArchitecturalRisk]:
        """检测重要级风险 (80%自动化)"""
        medium_risks = []

        # 1. 监控可观测性：日志覆盖 + 指标完整性检测
        observability_issues = await self._detect_observability_issues(graph)
        medium_risks.extend(observability_issues)

        # 2. 配置管理混乱：配置一致性 + 安全性检测
        config_issues = await self._detect_configuration_issues(graph)
        medium_risks.extend(config_issues)

        return medium_risks

    async def _detect_low_risks(self, graph: nx.DiGraph) -> List[ArchitecturalRisk]:
        """检测隐蔽级风险 (70%自动化)"""
        low_risks = []

        # 1. 技术债务积累：代码质量 + 架构债务量化
        debt_issues = await self._detect_technical_debt(graph)
        low_risks.extend(debt_issues)

        return low_risks

    async def _detect_single_points_of_failure(self, graph: nx.DiGraph) -> List[ArchitecturalRisk]:
        """检测单点故障：依赖计数 + 影响范围分析"""
        risks = []

        # 计算每个节点的入度和出度
        for node in graph.nodes():
            in_degree = graph.in_degree(node)
            out_degree = graph.out_degree(node)

            # 单点故障判断：高入度（被多个组件依赖）且无备份
            if in_degree >= 3 and out_degree <= 1:
                # 分析影响范围
                dependent_nodes = list(graph.predecessors(node))
                impact_scope = len(dependent_nodes)

                risk = ArchitecturalRisk(
                    id=f"single_point_{node}",
                    type="single_point_of_failure",
                    level=RiskLevel.CRITICAL,
                    title=f"单点故障: {node}",
                    description=f"组件{node}被{in_degree}个组件依赖，存在单点故障风险",
                    components=[node] + dependent_nodes,
                    impact_analysis=f"故障影响范围: {impact_scope}个组件",
                    solution_strategy="引入冗余设计、负载均衡或故障转移机制",
                    confidence=0.95,
                    detection_method="dependency_count_analysis",
                    evidence={
                        "in_degree": in_degree,
                        "out_degree": out_degree,
                        "dependent_components": dependent_nodes,
                        "impact_scope": impact_scope
                    }
                )
                risks.append(risk)

        return risks

    async def _detect_data_consistency_issues(self, graph: nx.DiGraph) -> List[ArchitecturalRisk]:
        """检测数据一致性缺失：事务边界 + 并发控制检测"""
        risks = []

        # 查找数据相关组件
        data_components = [node for node in graph.nodes()
                          if any(keyword in node.lower() for keyword in
                                ['database', 'repository', 'dao', 'data', 'cache', 'storage'])]

        for data_comp in data_components:
            # 检查事务边界
            consumers = list(graph.predecessors(data_comp))
            if len(consumers) > 1:
                # 多个消费者可能导致数据一致性问题
                risk = ArchitecturalRisk(
                    id=f"data_consistency_{data_comp}",
                    type="data_consistency_issue",
                    level=RiskLevel.CRITICAL,
                    title=f"数据一致性风险: {data_comp}",
                    description=f"数据组件{data_comp}被{len(consumers)}个组件访问，缺乏事务边界控制",
                    components=[data_comp] + consumers,
                    impact_analysis="可能导致数据不一致、脏读、幻读等问题",
                    solution_strategy="引入分布式事务、数据库锁机制或最终一致性设计",
                    confidence=0.85,
                    detection_method="transaction_boundary_analysis",
                    evidence={
                        "consumer_count": len(consumers),
                        "consumers": consumers,
                        "data_component": data_comp
                    }
                )
                risks.append(risk)

        return risks
    
    def _build_architecture_graph(self, architecture_data: Dict[str, Any]) -> nx.DiGraph:
        """构建架构依赖图"""
        graph = nx.DiGraph()
        
        # 添加组件节点
        for component in architecture_data.get('components', []):
            graph.add_node(component['name'], **component.get('attributes', {}))
        
        # 添加依赖边
        for dependency in architecture_data.get('dependencies', []):
            graph.add_edge(dependency['from'], dependency['to'], 
                          **dependency.get('attributes', {}))
        
        return graph
    
    def _prioritize_risks(self, risks: List[ArchitecturalRisk]) -> List[ArchitecturalRisk]:
        """风险优先级排序"""
        # 按风险等级和置信度排序
        level_priority = {
            RiskLevel.CRITICAL: 4,
            RiskLevel.HIGH: 3,
            RiskLevel.MEDIUM: 2,
            RiskLevel.LOW: 1
        }
        
        return sorted(risks, 
                     key=lambda r: (level_priority[r.level], r.confidence), 
                     reverse=True)
    
    def _group_risks_by_level(self, risks: List[ArchitecturalRisk]) -> Dict[RiskLevel, List[ArchitecturalRisk]]:
        """按风险等级分组"""
        grouped = {level: [] for level in RiskLevel}
        
        for risk in risks:
            grouped[risk.level].append(risk)
        
        return grouped
    
    def _calculate_overall_confidence(self, risks: List[ArchitecturalRisk]) -> float:
        """计算整体置信度"""
        if not risks:
            return 1.0
        
        # 加权平均置信度
        total_weight = 0
        weighted_confidence = 0
        
        level_weights = {
            RiskLevel.CRITICAL: 4,
            RiskLevel.HIGH: 3,
            RiskLevel.MEDIUM: 2,
            RiskLevel.LOW: 1
        }
        
        for risk in risks:
            weight = level_weights[risk.level]
            weighted_confidence += risk.confidence * weight
            total_weight += weight
        
        return weighted_confidence / total_weight if total_weight > 0 else 1.0
    
    def _generate_recommendations(self, risks: List[ArchitecturalRisk]) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        # 按风险类型生成建议
        risk_types = {}
        for risk in risks:
            if risk.type not in risk_types:
                risk_types[risk.type] = []
            risk_types[risk.type].append(risk)
        
        for risk_type, type_risks in risk_types.items():
            if risk_type == "circular_dependency":
                recommendations.append(f"发现{len(type_risks)}个循环依赖，建议重构依赖关系")
            elif risk_type == "security_violation":
                recommendations.append(f"发现{len(type_risks)}个安全边界违反，建议加强访问控制")
            # 添加更多风险类型的建议
        
        return recommendations
    
    def _generate_detection_summary(self, risks: List[ArchitecturalRisk]) -> Dict[str, Any]:
        """生成检测摘要"""
        return {
            "total_components_analyzed": len(set(comp for risk in risks for comp in risk.components)),
            "detection_methods_used": list(set(risk.detection_method for risk in risks)),
            "average_confidence": sum(risk.confidence for risk in risks) / len(risks) if risks else 1.0,
            "risk_distribution": {
                level.value: len([r for r in risks if r.level == level])
                for level in RiskLevel
            }
        }
```

### **2. IntelligentCodeGenerator (智能代码生成器)**

**职责**: 基于风险检测结果和架构分析，智能生成目标代码

```python
class IntelligentCodeGenerator:
    """智能代码生成器"""
    
    def __init__(self, template_manager: TemplateManager):
        self.template_manager = template_manager
        self.task_executor = TaskLevelValidationDrivenExecutor()
    
    async def generate_code(self, detection_report: RiskDetectionReport, 
                           generation_options: Dict[str, Any],
                           progress_callback: Optional[callable] = None) -> CodeGenerationResult:
        """
        基于检测报告生成代码
        
        Args:
            detection_report: 风险检测报告
            generation_options: 生成选项配置
            progress_callback: 进度回调
            
        Returns:
            CodeGenerationResult: 代码生成结果
        """
        # 1. 分析生成需求
        generation_plan = self._create_generation_plan(detection_report, generation_options)
        
        # 2. 执行代码生成
        generated_files = []
        for i, task in enumerate(generation_plan.tasks):
            if progress_callback:
                progress = (i + 1) / len(generation_plan.tasks) * 100
                await progress_callback({"stage": "generating", "progress": progress, "current_task": task.name})
            
            result = await self._execute_generation_task(task)
            generated_files.extend(result.files)
        
        # 3. 质量验证
        quality_report = await self._validate_generated_code(generated_files)
        
        return CodeGenerationResult(
            files=generated_files,
            quality_report=quality_report,
            generation_plan=generation_plan
        )
```

## 📊 性能优化策略

### **1. 异步处理**
- 使用`asyncio`实现非阻塞检测
- WebSocket实时进度推送
- 并发执行多个检测任务

### **2. 缓存机制**
- 文档解析结果缓存
- 检测结果本地存储
- 模板编译缓存

### **3. 内存优化**
- 大文件流式处理
- 及时释放临时对象
- 配置内存使用限制

## 🧪 测试策略

### **1. 单元测试**
- 每个检测方法独立测试
- 模拟数据覆盖边界情况
- 性能基准测试

### **2. 集成测试**
- 端到端检测流程测试
- 真实项目文档测试
- 并发访问测试

### **3. 回归测试**
- 与原有算法.py和检查.py结果对比
- 准确率和性能回归验证
- 兼容性测试

---

## 🤖 矛盾推理系统AI交互模式

### **混合模式设计**

**输入格式**: JSON结构化
```python
class ContradictionInferenceRequest:
    """矛盾推理请求 - JSON结构化输入"""

    def to_ai_prompt(self) -> str:
        """转换为AI提示词 - JSON输入转自然语言"""
        return f"""
请基于以下结构化信息进行深度矛盾推理：

【矛盾信息】
{json.dumps(self.contradiction, indent=2, ensure_ascii=False)}

【系统上下文】
{json.dumps(self.context, indent=2, ensure_ascii=False)}

【推理约束】
{json.dumps(self.inference_constraints, indent=2, ensure_ascii=False)}

请按照思维链条进行推理：
1. 矛盾本质分析
2. 风险推导过程
3. 风险具体化
4. 证据支持

请详细阐述推理过程...
"""
```

**处理流程**: 两阶段AI调用
```python
async def execute_contradiction_inference(self, contradiction, context, constraints):
    # 第一阶段：自然语言推理
    reasoning_result = await self._ai_reasoning_phase(
        self._build_reasoning_request(contradiction, context, constraints)
    )

    # 第二阶段：结构化提取
    structured_result = await self._ai_extraction_phase(
        self._build_extraction_request(reasoning_result)
    )

    # 第三阶段：验证和后处理
    return await self._validate_and_process_results(
        reasoning_result, structured_result, contradiction
    )
```

**输出格式**: JSON结构化
```python
{
  "inferred_risks": [
    {
      "id": "风险唯一标识",
      "type": "风险类型",
      "level": "CRITICAL|HIGH|MEDIUM|LOW",
      "inference_chain": ["推理步骤1", "推理步骤2", "推理步骤3"],
      "confidence": 0.0-1.0,
      "evidence": {
        "theoretical_basis": "理论依据",
        "real_world_cases": ["实际案例"],
        "technical_principles": ["技术原理"]
      }
    }
  ],
  "reasoning_quality": {
    "logical_consistency": 0.0-1.0,
    "evidence_strength": 0.0-1.0,
    "inference_depth": 整数
  }
}
```

### **AI幻觉控制机制**

**多重护栏**:
- **输入约束**: JSON结构化输入减少理解偏差
- **推理深度限制**: 最多2层推理，避免AI发散
- **置信度衰减**: 推理结果置信度 = 原矛盾置信度 × 0.9 × AI质量分数
- **逻辑一致性验证**: 验证推理链条和风险类型的合理性
- **证据要求**: 所有推理必须有理论依据和实际案例支持

**验证机制**:
```python
def _validate_logical_consistency(self, risk_data: Dict, original_contradiction: Contradiction) -> bool:
    # 1. 检查推理链条完整性
    # 2. 检查风险类型与矛盾类型的相关性
    # 3. 检查置信度合理性
    # 4. 验证证据支持的充分性
    return validation_passed
```

---

**实现原则**: 矛盾推理(主) + 传统算法(备) + 混合AI交互模式
**核心价值**: 通过矛盾推理实现93-95%自动化架构风险检测覆盖率
**可靠性保证**: 多重护栏控制AI幻觉，确保推理质量和系统稳定性

### **4. 文档矛盾检测辅助方法**

```python
    async def _detect_document_pair_contradictions(self, doc1: Dict, doc2: Dict) -> List[ArchitecturalRisk]:
        """检测两个文档之间的矛盾"""
        contradictions = []

        # 1. 架构定义矛盾检测
        architecture_contradictions = self._detect_architecture_definition_contradictions(doc1, doc2)
        contradictions.extend(architecture_contradictions)

        # 2. 约束违反检测
        constraint_contradictions = self._detect_constraint_contradictions(doc1, doc2)
        contradictions.extend(constraint_contradictions)

        # 3. 接口不一致检测
        interface_contradictions = self._detect_interface_inconsistencies(doc1, doc2)
        contradictions.extend(interface_contradictions)

        return contradictions

    async def _detect_document_internal_contradictions(self, doc: Dict) -> List[ArchitecturalRisk]:
        """检测文档内部的矛盾"""
        contradictions = []

        # 1. 内部一致性检测
        consistency_contradictions = self._detect_internal_consistency_issues(doc)
        contradictions.extend(consistency_contradictions)

        # 2. 完整性检测
        completeness_contradictions = self._detect_completeness_issues(doc)
        contradictions.extend(completeness_contradictions)

        return contradictions
```
