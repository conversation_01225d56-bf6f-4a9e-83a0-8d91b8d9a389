#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全代码生成系统 - 集成预检查和生成
先进行设计文档完整性检查，确保无问题后再正式生成代码
基于DRY原则，复用算法.py和检查.py的核心逻辑

作者: AI Assistant
创建时间: 2025-01-16
版本: 1.0.0
"""

import os
import sys
import logging
from typing import Dict, Any, Optional
from pathlib import Path

# 导入检查和生成模块
from 检查 import DesignDocumentPreChecker, DocumentCompletenessReport
from 算法 import UniversalCodeGenerationSystem

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SafeCodeGenerationSystem:
    """安全代码生成系统 - 预检查+生成的完整流程"""
    
    def __init__(self, openai_api_key: Optional[str] = None):
        self.pre_checker = DesignDocumentPreChecker()
        self.code_generator = UniversalCodeGenerationSystem(openai_api_key)
        self.safety_thresholds = {
            "minimum_score": 0.85,      # 最低通过分数
            "critical_threshold": 0.70,  # 严重问题阈值
            "auto_proceed": 0.95        # 自动继续阈值
        }
    
    def safe_generate_code(self, design_doc_path: str, output_dir: str, 
                          force_generate: bool = False) -> Dict[str, Any]:
        """安全代码生成 - 主要入口方法"""
        
        logger.info("="*80)
        logger.info("🚀 启动安全代码生成系统")
        logger.info("="*80)
        
        # 阶段1: 预检查
        logger.info("\n📋 阶段1: 设计文档完整性预检查")
        pre_check_result = self._execute_pre_check(design_doc_path)
        
        # 阶段2: 安全决策
        logger.info("\n🛡️ 阶段2: 安全决策分析")
        safety_decision = self._make_safety_decision(pre_check_result, force_generate)
        
        if safety_decision["action"] == "abort":
            return self._create_abort_result(safety_decision["reason"], pre_check_result)
        elif safety_decision["action"] == "manual_intervention":
            return self._create_manual_intervention_result(safety_decision["reason"], pre_check_result)
        
        # 阶段3: 正式代码生成
        logger.info("\n🏭 阶段3: 正式代码生成")
        generation_result = self._execute_code_generation(design_doc_path, output_dir)
        
        # 阶段4: 综合报告
        logger.info("\n📊 阶段4: 生成综合报告")
        final_result = self._create_comprehensive_result(
            pre_check_result, generation_result, safety_decision)
        
        return final_result
    
    def _execute_pre_check(self, design_doc_path: str) -> DocumentCompletenessReport:
        """执行预检查"""
        
        logger.info(f"检查设计文档: {design_doc_path}")
        
        if not os.path.exists(design_doc_path):
            logger.error(f"设计文档不存在: {design_doc_path}")
            raise FileNotFoundError(f"设计文档不存在: {design_doc_path}")
        
        # 执行完整性检查
        report = self.pre_checker.check_document_completeness(design_doc_path)
        
        # 打印检查报告
        self.pre_checker.print_report(report)
        
        return report
    
    def _make_safety_decision(self, pre_check_result: DocumentCompletenessReport, 
                            force_generate: bool) -> Dict[str, Any]:
        """做出安全决策"""
        
        score = pre_check_result.overall_score
        
        logger.info(f"文档完整性分数: {score:.2f}")
        logger.info(f"关键问题数量: {len(pre_check_result.critical_issues)}")
        logger.info(f"是否强制生成: {force_generate}")
        
        # 强制生成模式
        if force_generate:
            logger.warning("⚠️ 强制生成模式已启用，跳过安全检查")
            return {
                "action": "proceed",
                "reason": "用户强制生成",
                "confidence": 0.5
            }
        
        # 严重问题检查
        if pre_check_result.critical_issues:
            return {
                "action": "abort",
                "reason": f"存在{len(pre_check_result.critical_issues)}个严重问题",
                "confidence": 1.0
            }
        
        # 分数检查
        if score < self.safety_thresholds["critical_threshold"]:
            return {
                "action": "abort",
                "reason": f"文档质量分数过低: {score:.2f} < {self.safety_thresholds['critical_threshold']}",
                "confidence": 0.9
            }
        elif score < self.safety_thresholds["minimum_score"]:
            return {
                "action": "manual_intervention",
                "reason": f"文档质量需要改进: {score:.2f} < {self.safety_thresholds['minimum_score']}",
                "confidence": 0.8
            }
        elif score >= self.safety_thresholds["auto_proceed"]:
            return {
                "action": "proceed",
                "reason": f"文档质量优秀: {score:.2f}",
                "confidence": 0.95
            }
        else:
            return {
                "action": "proceed_with_caution",
                "reason": f"文档质量良好: {score:.2f}",
                "confidence": 0.85
            }
    
    def _execute_code_generation(self, design_doc_path: str, output_dir: str) -> Dict[str, Any]:
        """执行代码生成"""
        
        logger.info("开始正式代码生成...")
        
        try:
            # 调用算法.py的代码生成系统
            result = self.code_generator.generate_production_code(design_doc_path, output_dir)
            
            logger.info("✅ 代码生成完成")
            return result
            
        except Exception as e:
            logger.error(f"❌ 代码生成失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "total_time": 0.0
            }
    
    def _create_abort_result(self, reason: str, pre_check_result: DocumentCompletenessReport) -> Dict[str, Any]:
        """创建中止结果"""
        
        logger.error(f"🚨 代码生成已中止: {reason}")
        
        return {
            "success": False,
            "action": "aborted",
            "reason": reason,
            "pre_check_score": pre_check_result.overall_score,
            "critical_issues": pre_check_result.critical_issues,
            "missing_elements": pre_check_result.missing_elements,
            "recommendations": pre_check_result.recommendations,
            "next_steps": [
                "解决所有关键问题",
                "补充缺失的文档要素",
                "重新运行预检查",
                "确认分数≥0.85后重新生成"
            ]
        }
    
    def _create_manual_intervention_result(self, reason: str, 
                                         pre_check_result: DocumentCompletenessReport) -> Dict[str, Any]:
        """创建人工干预结果"""
        
        logger.warning(f"⚠️ 需要人工干预: {reason}")
        
        return {
            "success": False,
            "action": "manual_intervention_required",
            "reason": reason,
            "pre_check_score": pre_check_result.overall_score,
            "issues_to_resolve": [
                issue for result in pre_check_result.check_results 
                for issue in result.issues
            ],
            "recommendations": pre_check_result.recommendations,
            "next_steps": [
                "根据检查报告改进文档",
                "解决标记的问题和警告",
                "重新运行预检查",
                "或使用 --force 参数强制生成"
            ]
        }
    
    def _create_comprehensive_result(self, pre_check_result: DocumentCompletenessReport,
                                   generation_result: Dict[str, Any],
                                   safety_decision: Dict[str, Any]) -> Dict[str, Any]:
        """创建综合结果"""
        
        return {
            "success": generation_result.get("success", False),
            "pre_check": {
                "score": pre_check_result.overall_score,
                "passed": pre_check_result.is_ready_for_generation,
                "issues_count": len([issue for result in pre_check_result.check_results 
                                   for issue in result.issues])
            },
            "safety_decision": safety_decision,
            "generation": generation_result,
            "overall_quality": self._calculate_overall_quality(pre_check_result, generation_result),
            "recommendations": self._generate_final_recommendations(
                pre_check_result, generation_result)
        }
    
    def _calculate_overall_quality(self, pre_check_result: DocumentCompletenessReport,
                                 generation_result: Dict[str, Any]) -> float:
        """计算总体质量分数"""
        
        pre_check_score = pre_check_result.overall_score
        generation_score = generation_result.get("quality_score", 0.0)
        
        # 加权平均
        overall_quality = pre_check_score * 0.4 + generation_score * 0.6
        
        return overall_quality
    
    def _generate_final_recommendations(self, pre_check_result: DocumentCompletenessReport,
                                      generation_result: Dict[str, Any]) -> List[str]:
        """生成最终建议"""
        
        recommendations = []
        
        # 基于预检查的建议
        if pre_check_result.overall_score < 0.9:
            recommendations.append("继续改进设计文档质量")
        
        # 基于生成结果的建议
        if generation_result.get("success", False):
            quality_score = generation_result.get("quality_score", 0.0)
            if quality_score < 0.9:
                recommendations.append("建议对生成的代码进行人工审查和优化")
            
            recommendations.append("建议运行单元测试验证代码质量")
            recommendations.append("建议进行集成测试验证系统功能")
        
        return recommendations


def main():
    """主函数"""
    
    import argparse
    
    parser = argparse.ArgumentParser(description="安全代码生成系统")
    parser.add_argument("design_doc", help="设计文档路径")
    parser.add_argument("output_dir", help="输出目录")
    parser.add_argument("--force", action="store_true", help="强制生成（跳过安全检查）")
    parser.add_argument("--api-key", help="OpenAI API密钥")
    
    args = parser.parse_args()
    
    # 创建安全生成系统
    safe_generator = SafeCodeGenerationSystem(args.api_key)
    
    # 执行安全生成
    result = safe_generator.safe_generate_code(
        args.design_doc, 
        args.output_dir, 
        args.force
    )
    
    # 打印最终结果
    print("\n" + "="*80)
    print("🎯 最终生成结果")
    print("="*80)
    
    if result["success"]:
        print("✅ 代码生成成功")
        print(f"📊 总体质量分数: {result.get('overall_quality', 0.0):.2f}")
        print(f"📁 输出目录: {result['generation'].get('output_directory', 'N/A')}")
        print(f"⏱️ 总耗时: {result['generation'].get('total_time', 0.0):.2f}秒")
    else:
        print("❌ 代码生成失败")
        print(f"📋 失败原因: {result.get('reason', 'Unknown')}")
        
        if "next_steps" in result:
            print("\n📝 下一步操作:")
            for step in result["next_steps"]:
                print(f"  - {step}")
    
    # 返回适当的退出码
    sys.exit(0 if result["success"] else 1)


if __name__ == "__main__":
    main()
