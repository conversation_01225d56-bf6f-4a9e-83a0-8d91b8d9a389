# V4设计文档SQLite全景模型机制修改任务提示词

## 📋 任务背景与核心要求

### 🎯 用户原始需求

```
要明确一个核心的扫描机制第1个算法：SQLite本地存储有全局的模型，全景的模型里边有设计文档的历史抽象以及版本。当任务要求扫描当前某个功能的设计文档的时候，扫描阶段先去分析所有的设计文档，已录入的设计文档，它的版本的性质有版本号和设计文档的哈希值确定修改过没有。如果hash改变了，但是版本没改变，要警告提示用户（这样设计的目的是实现一个更新机制）。如果版本改变了，那么更新SQLite的全景数据抽象数据。这样才能实现快速的扫描，而不是全部重新文档重新建模，那就非常的慢。
```

### 📂 分析目标文档范围

```
docs\features\T001-create-plans-20250612\v4\design\01-V4架构总体设计.md
docs\features\T001-create-plans-20250612\v4\design\02-扫描阶段设计.md
docs\features\T001-create-plans-20250612\v4\design\03-多维拼接引擎设计.md
docs\features\T001-create-plans-20250612\v4\design\03-实施文档生成阶段设计.md
docs\features\T001-create-plans-20250612\v4\design\04-业务抽象映射设计.md
docs\features\T001-create-plans-20250612\v4\design\04-AI协作引擎设计.md
docs\features\T001-create-plans-20250612\v4\design\05-质量门禁机制设计.md
docs\features\T001-create-plans-20250612\v4\design\06-技术实施方案.md
docs\features\T001-create-plans-20250612\v4\design\06-算法驱动AI增强引擎设计.md
docs\features\T001-create-plans-20250612\v4\design\07-风险评估与回退策略.md
docs\features\T001-create-plans-20250612\v4\design\08-实施路线图.md
docs\features\T001-create-plans-20250612\v4\design\09-V4工作目录和功能代码规划.md
docs\features\T001-create-plans-20250612\v4\design\10-API兼容性设计.md
docs\features\T001-create-plans-20250612\v4\design\11-算法实现映射指引.md
docs\features\T001-create-plans-20250612\v4\design\12-95%置信度计算与验证标准.md
docs\features\T001-create-plans-20250612\v4\design\13-AI认知约束下的多维拼接设计模式.md
docs\features\T001-create-plans-20250612\v4\design\14-全景拼图认知构建指引.md
docs\features\T001-create-plans-20250612\v4\design\15-V3V31算法复用策略与风险控制.md
docs\features\T001-create-plans-20250612\v4\design\16-V4开发测试规划设计.md
```

## 📊 关键分析发现

### 🚨 严重设计不一致性评估（总体一致性：仅15%）

#### 1. SQLite存储机制完全缺失 ❌

**现状分析**：
- 所有16个设计文档中均无SQLite数据库设计
- 仅有抽象的"存储系统"概念提及（01-V4架构总体设计.md:1823）
- 仅有"数据库存储的路径"模糊引用（01-V4架构总体设计.md:883）
- 无具体的数据库schema、表结构、连接管理设计

**缺失内容**：
```sql
-- 完全缺失的数据库设计
CREATE TABLE panoramic_models (...);
CREATE TABLE document_history (...);
CREATE INDEX idx_doc_path_version (...);
```

#### 2. 全景模型数据结构未定义 ❌

**现状分析**：
- 有"全景拼图认知构建"概念性描述（多个文档）
- 有"多维抽象映射引擎"抽象概念（01-V4架构总体设计.md:1823）
- 缺乏具体的全景模型类定义
- 无历史抽象数据的存储格式设计

**缺失内容**：
```python
class PanoramicModel:
    """完全缺失的全景模型数据结构"""
    def __init__(self):
        self.document_abstractions = {}
        self.version_history = {}
        self.design_relationships = {}
```

#### 3. 版本+哈希检测机制缺失 ❌

**现状分析**：
- 有模糊的"版本一致性检测"概念（01-V4架构总体设计.md:720）
- 有"Fxxx版本号识别"逻辑（14-全景拼图认知构建指引.md:468）
- 完全缺乏文档哈希值计算算法
- 无版本号与哈希值的对应关系设计
- 无变更检测和用户警告机制

**缺失内容**：
```python
def calculate_document_hash(doc_path: str) -> str:
    """完全缺失的哈希计算算法"""
    pass

def calculate_code_file_hash(code_path: str) -> str:
    """完全缺失的代码文件哈希计算算法"""
    pass

def check_version_hash_consistency(file_path: str, file_type: str) -> Dict:
    """完全缺失的版本-哈希一致性检查（支持文档和代码）"""
    pass

def check_design_code_mapping_consistency(design_doc: str, code_file: str) -> Dict:
    """完全缺失的设计文档与代码映射一致性检查"""
    pass
```

#### 4. 快速扫描算法不存在 ❌

**现状分析**：
- 现有设计全部基于"三重验证全量处理"
- 有"V4三重验证智能扫描引擎"（06-技术实施方案.md:168）
- 无增量扫描vs全量重建的区分逻辑
- 无基于已有全景数据的快速检索机制

**缺失内容**：
```python
def execute_incremental_scan(changed_docs: List[str]) -> Dict:
    """完全缺失的增量扫描算法"""
    pass

def execute_fast_scan_mode(target_docs: List[str]) -> Dict:
    """完全缺失的快速扫描模式"""
    pass
```

### 📋 发现的相关但不完整概念

```yaml
existing_related_concepts:
  storage_references:
    - "安全存储系统" (01-V4架构总体设计.md:1823)
    - "数据库存储的路径" (01-V4架构总体设计.md:883)
    - "三重验证抽象模型存储" (11-算法实现映射指引.md:85)
    
  version_management_concepts:
    - "版本一致性检测引擎" (01-V4架构总体设计.md:720)
    - "Fxxx版本号识别" (14-全景拼图认知构建指引.md:468)
    - "版本管理制度" (01-V4架构总体设计.md:903)
    
  panoramic_model_concepts:
    - "全景拼图认知构建" (多个文档概念性描述)
    - "多维抽象映射引擎" (01-V4架构总体设计.md:1823)
    - "全景数据整合" (06-算法驱动AI增强引擎设计.md:180)
    
     missing_critical_components:
     - "❌ SQLite数据库schema设计"
     - "❌ 文档哈希值计算算法"
     - "❌ 代码文件哈希值计算算法"
     - "❌ 版本-哈希对应关系存储"
     - "❌ 设计文档与代码映射关系管理"
     - "❌ 快速扫描vs全量重建逻辑"
     - "❌ 全景模型数据结构定义"
     - "❌ 历史版本演进追踪机制"
     - "❌ 映射一致性检查机制"
```

## 🔧 具体修改指导

### 🎯 需要补充的核心技术组件

#### A. 全景模型SQLite数据库类（完全缺失）

```python
class PanoramicModelDatabase:
    """全景模型SQLite数据库 - 需要从零设计"""
    
    def __init__(self, db_path: str):
        # 需要设计：SQLite连接、表创建、索引优化
        self.db_path = db_path
        self.connection = None
        self._initialize_database()
    
    def _initialize_database(self):
        """需要设计：数据库初始化和表结构创建"""
        pass
    
    def store_document_abstraction(self, doc_path: str, version: str, 
                                 content_hash: str, abstraction_data: Dict):
        """需要设计：文档抽象数据的结构化存储"""
        pass
    
    def store_code_file_info(self, file_path: str, version: str, 
                           content_hash: str, related_design_doc: str, 
                           code_data: Dict):
        """需要设计：代码文件信息的结构化存储"""
        pass
    
    def check_document_changes(self, doc_path: str, current_hash: str) -> Dict:
        """需要设计：版本+哈希变更检测逻辑"""
        # 返回格式：{"hash_changed": bool, "version_changed": bool, "action": str}
        pass
    
    def check_code_file_changes(self, file_path: str, current_hash: str) -> Dict:
        """需要设计：代码文件版本+哈希变更检测逻辑"""
        # 返回格式：{"hash_changed": bool, "version_changed": bool, "mapping_consistent": bool}
        pass
    
    def check_design_code_consistency(self, design_doc_path: str) -> Dict:
        """需要设计：设计文档与代码映射一致性检查"""
        # 返回格式：{"consistent_mappings": [], "inconsistent_mappings": [], "missing_mappings": []}
        pass
    
    def get_panoramic_model(self, scope: str) -> Dict:
        """需要设计：基于范围的全景模型快速检索"""
        pass
    
    def update_version_info(self, doc_path: str, new_version: str, new_hash: str):
        """需要设计：版本信息更新逻辑"""
        pass
    
    def update_code_version_info(self, file_path: str, new_version: str, new_hash: str):
        """需要设计：代码文件版本信息更新逻辑"""
        pass
    
    def create_design_code_mapping(self, design_doc: str, code_file: str, 
                                 mapping_type: str, strength: float):
        """需要设计：设计文档与代码文件映射关系创建"""
        pass
    
    def get_document_history(self, doc_path: str) -> List[Dict]:
        """需要设计：文档历史版本追踪"""
        pass
    
    def get_code_file_history(self, file_path: str) -> List[Dict]:
        """需要设计：代码文件历史版本追踪"""
        pass
```

#### B. 智能扫描引擎类（需要重新设计核心算法）

```python
class IntelligentScanningEngine:
    """智能扫描引擎 - 需要重新设计核心算法"""
    
    def __init__(self, panoramic_db: PanoramicModelDatabase):
        self.panoramic_db = panoramic_db
        self.hash_calculator = DocumentHashCalculator()
        self.version_parser = DocumentVersionParser()
    
    def execute_smart_scan(self, target_docs: List[str]) -> Dict:
        """需要设计：智能扫描主流程"""
        # 1. 从SQLite加载已有全景模型
        # 2. 计算当前文档哈希值
        # 3. 对比版本号和哈希值变化
        # 4. 决定增量扫描vs全量重建
        pass
    
    def _determine_scan_mode(self, doc_path: str, current_hash: str, 
                           current_version: str) -> str:
        """需要设计：扫描模式决策逻辑"""
        # 返回："fast_mode", "incremental_mode", "full_rebuild"
        pass
    
    def execute_fast_scan_mode(self, doc_paths: List[str]) -> Dict:
        """需要设计：基于已有全景数据的快速扫描"""
        pass
    
    def execute_incremental_scan_mode(self, changed_docs: List[str]) -> Dict:
        """需要设计：增量扫描模式"""
        pass
    
    def execute_full_rebuild_mode(self, all_docs: List[str]) -> Dict:
        """需要设计：全量重建模式"""
        pass
    
    def warn_version_hash_mismatch(self, doc_path: str, old_version: str, 
                                 current_version: str, new_hash: str) -> None:
        """需要设计：版本-哈希不匹配警告机制"""
        warning_message = f"""
        ⚠️  版本-内容不一致警告
        文档：{doc_path}
        版本号未变：{current_version}
        但内容已修改（哈希值变化）
        建议：更新版本号或确认内容变更
        """
        print(warning_message)
        # 需要设计：日志记录、用户交互确认机制
        pass
```

#### C. 文档哈希计算器（完全缺失）

```python
class DocumentHashCalculator:
    """文档哈希计算器 - 完全缺失的组件"""
    
    def calculate_content_hash(self, doc_path: str) -> str:
        """需要设计：文档内容哈希值计算算法"""
        # 需要考虑：忽略格式变化、仅关注内容语义
        pass
    
    def calculate_semantic_hash(self, doc_content: str) -> str:
        """需要设计：语义级别的哈希计算"""
        # 需要过滤：空格、换行、注释等非语义内容
        pass
    
         def batch_calculate_hashes(self, file_paths: List[str]) -> Dict[str, str]:
         """需要设计：批量哈希计算优化（支持文档和代码文件）"""
         pass
     
     def calculate_code_hash(self, code_file_path: str) -> str:
         """需要设计：代码文件哈希值计算算法"""
         # 需要考虑：忽略注释、空行、格式化等非功能性变化
         pass
     
     def calculate_code_semantic_hash(self, code_content: str, language: str) -> str:
         """需要设计：基于代码语义的哈希计算"""
         # 需要基于AST或语义分析，忽略变量名、格式等表面变化
         pass
```

### 🗄️ 数据库Schema设计需求

```sql
-- 需要设计的SQLite表结构

-- 全景模型主表
CREATE TABLE panoramic_models (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_path TEXT NOT NULL UNIQUE,
    version_number TEXT NOT NULL,
    content_hash TEXT NOT NULL,
    semantic_hash TEXT NOT NULL,
    abstraction_data TEXT NOT NULL, -- JSON格式的抽象数据
    relationships_data TEXT, -- JSON格式的关联关系数据
    quality_metrics TEXT, -- JSON格式的质量评估数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 文档历史记录表
CREATE TABLE document_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_path TEXT NOT NULL,
    version_number TEXT NOT NULL,
    content_hash TEXT NOT NULL,
    semantic_hash TEXT NOT NULL,
    change_type TEXT NOT NULL, -- 'created', 'version_updated', 'content_modified', 'deleted'
    change_description TEXT,
    abstraction_delta TEXT, -- JSON格式的变更差异
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 扫描任务记录表
CREATE TABLE scan_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT NOT NULL UNIQUE,
    scan_mode TEXT NOT NULL, -- 'fast', 'incremental', 'full_rebuild'
    target_documents TEXT NOT NULL, -- JSON数组格式
    execution_time_ms INTEGER,
    documents_processed INTEGER,
    documents_updated INTEGER,
    warnings_generated INTEGER,
    status TEXT NOT NULL, -- 'running', 'completed', 'failed'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- 代码文件版本管理表（用于设计文档映射的代码）
CREATE TABLE code_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_path TEXT NOT NULL UNIQUE,
    version_number TEXT NOT NULL,
    content_hash TEXT NOT NULL,
    semantic_hash TEXT NOT NULL,
    related_design_document TEXT, -- 关联的设计文档路径
    code_type TEXT NOT NULL, -- 'implementation', 'test', 'configuration'
    abstraction_data TEXT, -- JSON格式的代码抽象数据
    dependency_info TEXT, -- JSON格式的依赖信息
    quality_metrics TEXT, -- JSON格式的代码质量评估
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 代码文件历史记录表
CREATE TABLE code_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_path TEXT NOT NULL,
    version_number TEXT NOT NULL,
    content_hash TEXT NOT NULL,
    semantic_hash TEXT NOT NULL,
    change_type TEXT NOT NULL, -- 'created', 'version_updated', 'content_modified', 'deleted'
    change_description TEXT,
    code_delta TEXT, -- JSON格式的代码变更差异
    related_design_document TEXT, -- 关联的设计文档路径
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 设计文档与代码映射关系表
CREATE TABLE design_code_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    design_document_path TEXT NOT NULL,
    code_file_path TEXT NOT NULL,
    mapping_type TEXT NOT NULL, -- 'implements', 'tests', 'configures', 'extends'
    mapping_strength REAL NOT NULL, -- 映射强度 0.0-1.0
    mapping_description TEXT,
    version_consistency_status TEXT, -- 'consistent', 'inconsistent', 'unknown'
    last_checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(design_document_path, code_file_path, mapping_type)
);

-- 版本警告记录表（扩展支持代码文件）
CREATE TABLE version_warnings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_path TEXT NOT NULL, -- 可以是设计文档或代码文件路径
    file_type TEXT NOT NULL, -- 'design_document', 'code_file'
    warning_type TEXT NOT NULL, -- 'hash_changed_version_same', 'version_conflict', 'mapping_inconsistent'
    old_version TEXT,
    current_version TEXT,
    old_hash TEXT,
    current_hash TEXT,
    related_file_path TEXT, -- 关联的文件路径（设计文档<->代码文件）
    user_action TEXT, -- 'ignored', 'version_updated', 'content_reverted', 'mapping_updated'
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 性能优化索引
CREATE INDEX idx_doc_path_version ON panoramic_models(document_path, version_number);
CREATE INDEX idx_content_hash ON panoramic_models(content_hash);
CREATE INDEX idx_semantic_hash ON panoramic_models(semantic_hash);
CREATE INDEX idx_doc_path_history ON document_history(document_path, timestamp DESC);
CREATE INDEX idx_scan_task_status ON scan_tasks(status, created_at);
CREATE INDEX idx_warnings_unresolved ON version_warnings(file_path, resolved_at);

-- 代码文件相关索引
CREATE INDEX idx_code_file_path_version ON code_files(file_path, version_number);
CREATE INDEX idx_code_content_hash ON code_files(content_hash);
CREATE INDEX idx_code_related_design ON code_files(related_design_document);
CREATE INDEX idx_code_file_history ON code_history(file_path, timestamp DESC);
CREATE INDEX idx_design_code_mapping ON design_code_mappings(design_document_path, code_file_path);
CREATE INDEX idx_mapping_consistency ON design_code_mappings(version_consistency_status, last_checked_at);
```

### 📋 修改优先级与策略

#### 第一优先级（立即修改） 🔴

1. **创建新文档**：`17-SQLite全景模型数据库设计.md`
   - 包含完整的数据库schema设计
   - 包含数据库操作类的详细设计
   - 包含性能优化和索引策略

2. **重写核心文档**：`02-扫描阶段设计.md`
   - 重写核心扫描算法部分
   - 增加智能扫描引擎设计
   - 增加版本-哈希检测机制设计

3. **补充架构文档**：`01-V4架构总体设计.md`
   - 补充数据存储架构层设计
   - 增加SQLite集成架构
   - 更新系统架构图包含数据库层

#### 第二优先级（架构对齐） 🟡

1. **修改技术方案**：`06-技术实施方案.md`
   - 增加SQLite技术栈配置
   - 增加数据库依赖和版本要求
   - 更新部署和配置说明

2. **更新算法映射**：`11-算法实现映射指引.md`
   - 增加数据库操作算法映射
   - 增加哈希计算算法映射
   - 增加扫描模式决策算法映射

3. **调整认知构建指引**：`14-全景拼图认知构建指引.md`
   - 明确数据持久化机制
   - 增加基于历史数据的认知构建策略
   - 更新全景模型构建流程

#### 第三优先级（一致性保证） 🟢

1. **更新相关引用**：其他设计文档中的"存储系统"引用
2. **确保术语一致性**：统一"全景模型"、"抽象数据"等术语使用
3. **更新接口设计**：确保API设计与新的SQLite架构兼容

### 🎯 成功标准

修改完成后应该实现：

#### 1. 具体的SQLite数据库设计 ✅
- 完整的数据库schema定义
- 优化的索引策略
- 数据库连接和事务管理机制
- 数据迁移和备份策略

#### 2. 明确的全景模型数据结构 ✅
- JSON格式的抽象数据定义
- 版本演进的数据结构
- 关联关系的存储格式
- 质量评估数据的结构

#### 3. 完整的版本+哈希管理机制 ✅
- 文档内容哈希计算算法
- 语义级别的哈希对比
- 版本号解析和验证逻辑
- 用户警告和交互机制

#### 4. 智能扫描算法 ✅
- 快速模式：基于已有数据的检索
- 增量模式：仅处理变更文档
- 全量模式：完整重建全景模型
- 智能模式决策逻辑

#### 5. 性能优化策略 ✅
- 基于SQLite的快速查询
- 批量处理优化
- 内存使用优化
- 并发处理能力

#### 6. 设计文档与代码映射管理 ✅
- 设计文档与代码文件的双向映射关系
- 映射一致性自动检查机制
- 版本同步状态监控
- 映射强度评估算法

### ⚠️ 注意事项

#### 1. 保持现有机制兼容性
- **保持三重验证机制**：在SQLite架构基础上集成，不要替换
- **保持V3/V3.1算法复用**：SQLite作为数据层，不影响算法层
- **保持现有质量门禁**：增强而非替换现有质量控制

#### 2. 技术实现约束
- **向后兼容性**：确保新的扫描机制能处理现有文档
- **数据迁移策略**：从当前无数据库状态迁移到SQLite
- **错误处理机制**：数据库异常时的降级策略

#### 3. 性能要求
- **快速扫描性能**：比现有全量处理快至少5倍
- **内存使用控制**：SQLite操作不应显著增加内存占用
- **并发安全性**：支持多进程/多线程安全访问

#### 4. 数据完整性保证
- **事务一致性**：确保数据库操作的ACID特性
- **数据验证机制**：存储前的数据格式和完整性验证
- **备份和恢复**：全景模型数据的备份和灾难恢复

### 📊 修改影响评估

#### 对现有设计的影响程度

| 文档名称 | 影响程度 | 主要修改内容 | 预估工作量 |
|---------|---------|-------------|-----------|
| 01-V4架构总体设计.md | 🔴 高 | 增加数据存储架构层 | 2-3天 |
| 02-扫描阶段设计.md | 🔴 高 | 重写核心扫描算法 | 3-4天 |
| 新建：17-SQLite数据库设计.md | 🔴 高 | 完整数据库设计 | 4-5天 |
| 06-技术实施方案.md | 🟡 中 | 增加SQLite技术栈 | 1-2天 |
| 11-算法实现映射指引.md | 🟡 中 | 增加数据库算法映射 | 1-2天 |
| 14-全景拼图认知构建指引.md | 🟡 中 | 更新认知构建流程 | 1-2天 |
| 其他13个文档 | 🟢 低 | 术语和引用一致性 | 2-3天 |

**总预估工作量**：15-21个工作日

---

## 📋 任务要求总结

**核心任务**：根据以上详细分析，修改现有V4设计文档，补充完整的SQLite全景模型存储机制、版本+哈希检测机制、智能扫描算法，确保设计文档与用户核心需求保持一致。

**关键成功因素**：
1. SQLite数据库设计的完整性和性能
2. 智能扫描算法的效率提升
3. 版本-哈希管理机制的准确性（支持文档和代码）
4. 设计文档与代码映射关系的自动化管理
5. 与现有三重验证机制的无缝集成
6. 全景模型数据结构的可扩展性

**最终目标**：实现快速扫描能力，避免每次都进行全量文档重新建模，通过SQLite全景模型数据库实现高效的增量更新和智能扫描。同时支持设计文档与代码文件的版本同步管理，确保映射关系的一致性。

**补充说明**：代码文件版本管理功能现在设计建表，为后续功能扩展做准备，当前阶段主要专注于设计文档的全景模型存储和扫描优化。 