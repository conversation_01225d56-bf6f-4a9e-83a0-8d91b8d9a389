# Gemini与DeepSeek AI模型技术配置手册

## 📋 配置概览

本手册详细说明了在V4测试框架中集成和优化Gemini与DeepSeek AI模型的具体技术配置，包括API参数、Token优化、错误处理和性能调优等关键技术细节。

---

## 🔧 基础配置

### V4测试框架配置文件

#### `v4_enhanced_test_framework.py` 配置
```python
@dataclass
class V4TestConfig:
    """V4.0测试配置"""
    # DeepSeek API配置
    api_key: str
    base_url: str = "https://llm.chutes.ai/v1/chat/completions"
    
    # Gemini API配置
    gemini_api_key: str = "sk-mVBbGAPeX7yExr3FskC1gaODAFJusdrncX8qXkLJ4nnuYeCb"
    gemini_base_url: str = "https://x666.me/v1/chat/completions"
    
    # 优化后的Token和超时配置
    timeout: int = 300  # 5分钟超时，适应大Token处理
    max_tokens: int = 100000  # 充分利用Gemini的1M token能力
    temperature: float = 0.1
    test_output_dir: str = "tools/doc/plans/v4/test/results"
    design_docs_base: str = "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1"
```

#### 配置获取函数
```python
def get_v4_test_config() -> V4TestConfig:
    """获取V4.0测试配置"""
    return V4TestConfig(
        api_key="cpk_3b6eb6d4ff254eec8d53bbb6d791acdb.4051c83fb6bd53adb8ea32923961cd47.RqEhaXfNmNEwbDzoQKlkp10y2BjL0jlP",
        gemini_api_key="sk-mVBbGAPeX7yExr3FskC1gaODAFJusdrncX8qXkLJ4nnuYeCb",
        gemini_base_url="https://x666.me/v1/chat/completions",
        timeout=300,  # 5分钟超时，适应大Token处理
        max_tokens=100000,  # 充分利用Gemini的1M token能力
        temperature=0.1
    )
```

---

## 🎯 模型组合配置

### Gemini模型组合定义

#### 速度优先组合
```python
"gemini_flash_speed": {
    "primary": {
        "model": "gemini-2.5-flash-preview-05-20",
        "role": "Google快速主力",
        "strength": "最快响应速度，适合快速原型",
        "api_config": "gemini"
    },
    "backup": {
        "model": "gemini-2.5-flash-preview-04-17",
        "role": "Google快速备用",
        "strength": "稳定的快速生成",
        "api_config": "gemini"
    },
    "specialist": {
        "model": "gemini-2.5-pro",
        "role": "Google专业验证",
        "strength": "质量保证和复杂任务处理",
        "api_config": "gemini"
    }
}
```

#### 质量优先组合
```python
"gemini_pro_quality": {
    "primary": {
        "model": "gemini-2.5-pro",
        "role": "Google专业主力",
        "strength": "最高质量输出",
        "api_config": "gemini"
    },
    "backup": {
        "model": "gemini-2.5-pro-preview-06-05",
        "role": "Google专业备用",
        "strength": "预览版本，功能丰富",
        "api_config": "gemini"
    },
    "specialist": {
        "model": "gemini-2.5-pro-preview-05-06",
        "role": "Google专业专家",
        "strength": "特殊任务处理",
        "api_config": "gemini"
    }
}
```

## 📊 Token优化配置

### 根据模型类型调整Token配置
```python
def get_optimal_token_config(model: str, task_type: str) -> dict:
    """根据模型和任务类型获取最优Token配置"""
    
    # Gemini模型配置
    if "gemini" in model.lower():
        base_config = {
            "max_tokens": 100000,
            "timeout": 300,
            "temperature": 0.1
        }
        
        # 根据任务类型调整
        if task_type == "large_codebase":
            base_config["max_tokens"] = 150000
            base_config["timeout"] = 420  # 7分钟
        elif task_type == "quick_prototype":
            base_config["max_tokens"] = 30000
            base_config["timeout"] = 180  # 3分钟
        elif task_type == "documentation":
            base_config["max_tokens"] = 50000
            base_config["timeout"] = 240  # 4分钟
            base_config["temperature"] = 0.2  # 稍高创造性
            
    # DeepSeek模型配置
    else:
        base_config = {
            "max_tokens": 4000,
            "timeout": 180,
            "temperature": 0.1
        }
        
        # DeepSeek针对精细任务优化
        if task_type == "code_quality":
            base_config["temperature"] = 0.05  # 更保守
        elif task_type == "creative_solution":
            base_config["temperature"] = 0.15  # 稍高创造性
    
    return base_config
```

---

## 📈 性能监控配置

### 详细指标收集

#### 增强的结果数据结构
```python
@dataclass
class V4TestResult:
    """V4.0测试结果数据结构"""
    test_id: str
    test_type: V4TestType
    ai_model: str
    ai_role: str
    success: bool
    response_time: float
    
    # V4.0核心指标
    json_usage_rate: float = 0.0
    ai_fill_completion_rate: float = 0.0
    documentation_coverage: float = 0.0
    production_code_score: float = 0.0
    architecture_accuracy: float = 0.0
    
    # 增强的性能指标
    content_length: int = 0
    token_efficiency: float = 0.0  # text_tokens / total_tokens
    cost_per_character: float = 0.0
    quality_per_second: float = 0.0
    
    content: str = ""
    error_message: Optional[str] = None
    metadata: Optional[Dict] = None
    timestamp: str = ""
    
    # 新增：详细的Token使用分析
    token_analysis: Optional[Dict] = None
    
    def calculate_derived_metrics(self):
        """计算衍生指标"""
        if self.metadata and "tokens_used" in self.metadata:
            tokens = self.metadata["tokens_used"]
            
            # Token效率计算
            if "completion_tokens_details" in tokens:
                details = tokens["completion_tokens_details"]
                text_tokens = details.get("text_tokens", 0)
                total_tokens = tokens.get("completion_tokens", 1)
                self.token_efficiency = text_tokens / total_tokens
            
            # 性价比计算
            if self.content:
                self.cost_per_character = tokens.get("total_tokens", 0) / len(self.content)
                self.quality_per_second = self.production_code_score / max(self.response_time, 1)
```

**报告生成时间**：2025-01-18  
**版本**：v1.0  
**测试框架版本**：V4.0 Enhanced 