# V4.5九步算法集成方案 - PanoramicPositioningEngine数据库初始化

## 📋 文档概述

**文档ID**: V4-5-NINE-STEP-ALGORITHM-INTEGRATION-005-5-DATABASE-INITIALIZATION
**创建日期**: 2025-06-24
**版本**: V4.5-Enhanced-Nine-Step-Integration-T001-Database-Init-Part5
**目标**: 实现T001项目PanoramicPositioningEngine的SQLite数据库初始化功能
**依赖文档**: 05_4-PanoramicPositioningEngine基础架构.md
**分步说明**: 这是05-V4.5九步算法集成方案.md的第5部分，专注于数据库初始化实现

## 🗄️ T001项目SQLite数据库初始化实现

**重要说明**: 本文档专注于数据库初始化的具体实现逻辑。完整的数据库表结构设计请参考 `05_3-SQLite数据库表结构扩展.md`，避免重复定义。

### 数据库初始化核心方法

```python
def _init_t001_database(self):
    """初始化T001项目SQLite数据库表结构"""
    try:
        # 确保数据库目录存在
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 注意：完整的表结构定义请参考 05_3-SQLite数据库表结构扩展.md
            # 本方法仅执行表创建，避免重复定义

            # 执行表结构创建脚本（基于05_3文档的权威设计）
            self._execute_table_creation_scripts_from_05_3(cursor)

    def _execute_table_creation_scripts_from_05_3(self, cursor):
        """
        执行05_3文档定义的表结构创建脚本

        注意：此方法引用05_3-SQLite数据库表结构扩展.md中的权威表结构定义
        避免在此处重复定义表结构，保持DRY原则
        """
        # 引用05_3文档的表结构定义
        # 1. panoramic_models表（权威定义在05_3文档第37行）
        # 2. panoramic_causal_mappings表（权威定义在05_3文档）
        # 3. strategy_routes_extended表（权威定义在05_3文档）
        # 4. strategy_selection_history表（权威定义在05_3文档）
        # 5. causal_inference_results表（权威定义在05_3文档）

        print("📋 执行05_3文档定义的表结构创建...")
        print("   详细表结构请参考: 05_3-SQLite数据库表结构扩展.md")

        # 实际的表创建SQL应该从05_3文档中读取
        # 这里仅作为占位符，实际实现时应该读取05_3文档的SQL定义

            # 新增：策略执行历史数据表（因果推理系统核心需求）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS strategy_selection_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    selected_routes TEXT NOT NULL,        -- JSON格式存储选择的策略路线
                    confidence_score REAL NOT NULL,       -- 策略选择置信度
                    context_data TEXT NOT NULL,           -- JSON格式存储执行上下文
                    execution_result TEXT NOT NULL,       -- JSON格式存储执行结果
                    success_rate REAL DEFAULT 0.0,        -- 策略成功率
                    performance_metrics TEXT,             -- JSON格式存储性能指标
                    causal_factors TEXT,                  -- JSON格式存储因果因素
                    panoramic_position_id TEXT,           -- 关联全景拼图位置
                    execution_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (panoramic_position_id) REFERENCES panoramic_models(id)
                )
            ''')

            # 新增：因果推理结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS causal_inference_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    panoramic_position_id TEXT NOT NULL,
                    algorithm_type TEXT NOT NULL,         -- PC/FCI/LiNGAM
                    causal_graph TEXT NOT NULL,           -- JSON格式存储因果图
                    structural_equations TEXT,           -- JSON格式存储结构方程
                    causal_strength_matrix TEXT,         -- JSON格式存储因果强度矩阵
                    discovery_accuracy REAL DEFAULT 0.0, -- 因果发现准确率
                    validation_status TEXT DEFAULT 'PENDING',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (panoramic_position_id) REFERENCES panoramic_models(id)
                )
            ''')

            # 创建索引优化查询性能
            self._create_database_indexes(cursor)

            # 创建触发器确保数据完整性
            self._create_database_triggers(cursor)

            # 插入初始配置数据
            self._insert_initial_configuration_data(cursor)

            conn.commit()
            print("✅ T001项目SQLite数据库表结构初始化完成")

    except Exception as e:
        print(f"❌ T001项目数据库初始化失败: {e}")
        raise DatabaseInitializationError(f"数据库初始化失败: {str(e)}")

def _create_database_indexes(self, cursor):
    """创建数据库索引优化查询性能"""
    try:
        # panoramic_models表索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_panoramic_models_path ON panoramic_models(document_path)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_panoramic_models_hash ON panoramic_models(content_hash)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_panoramic_models_confidence ON panoramic_models(confidence_score)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_panoramic_models_created ON panoramic_models(created_at)')

        # panoramic_causal_mappings表索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_mappings_panoramic ON panoramic_causal_mappings(panoramic_position_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_mappings_causal ON panoramic_causal_mappings(causal_strategy_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_mappings_quality ON panoramic_causal_mappings(mapping_quality_score)')

        # strategy_routes_extended表索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_routes_strategy ON strategy_routes_extended(strategy_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_routes_panoramic ON strategy_routes_extended(panoramic_position_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_routes_confidence ON strategy_routes_extended(confidence_score)')

        # strategy_selection_history表索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_history_panoramic ON strategy_selection_history(panoramic_position_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_history_timestamp ON strategy_selection_history(execution_timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_history_success_rate ON strategy_selection_history(success_rate)')

        # causal_inference_results表索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_causal_panoramic ON causal_inference_results(panoramic_position_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_causal_algorithm ON causal_inference_results(algorithm_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_causal_accuracy ON causal_inference_results(discovery_accuracy)')

        print("✅ 数据库索引创建完成")

    except Exception as e:
        print(f"⚠️ 数据库索引创建失败: {e}")
        # 索引创建失败不应该阻止数据库初始化

def _create_database_triggers(self, cursor):
    """创建数据库触发器确保数据完整性"""
    try:
        # 自动更新updated_at字段的触发器
        cursor.execute('''
            CREATE TRIGGER IF NOT EXISTS update_panoramic_models_timestamp
            AFTER UPDATE ON panoramic_models
            FOR EACH ROW
            BEGIN
                UPDATE panoramic_models SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        ''')

        # 置信度范围检查触发器
        cursor.execute('''
            CREATE TRIGGER IF NOT EXISTS check_confidence_score_range
            BEFORE INSERT ON panoramic_models
            FOR EACH ROW
            WHEN NEW.confidence_score < 0.0 OR NEW.confidence_score > 1.0
            BEGIN
                SELECT RAISE(ABORT, 'confidence_score must be between 0.0 and 1.0');
            END
        ''')

        # 映射质量评分范围检查触发器
        cursor.execute('''
            CREATE TRIGGER IF NOT EXISTS check_mapping_quality_range
            BEFORE INSERT ON panoramic_causal_mappings
            FOR EACH ROW
            WHEN NEW.mapping_quality_score < 0.0 OR NEW.mapping_quality_score > 1.0
            BEGIN
                SELECT RAISE(ABORT, 'mapping_quality_score must be between 0.0 and 1.0');
            END
        ''')

        # 策略执行历史数据完整性检查触发器
        cursor.execute('''
            CREATE TRIGGER IF NOT EXISTS check_strategy_history_completeness
            BEFORE INSERT ON strategy_selection_history
            FOR EACH ROW
            WHEN NEW.selected_routes = '' OR NEW.context_data = '' OR NEW.execution_result = ''
            BEGIN
                SELECT RAISE(ABORT, 'selected_routes, context_data, and execution_result cannot be empty');
            END
        ''')

        print("✅ 数据库触发器创建完成")

    except Exception as e:
        print(f"⚠️ 数据库触发器创建失败: {e}")
        # 触发器创建失败不应该阻止数据库初始化

def _insert_initial_configuration_data(self, cursor):
    """插入初始配置数据"""
    try:
        # 检查是否已有配置数据
        cursor.execute("SELECT COUNT(*) FROM panoramic_models WHERE document_path LIKE '%config%'")
        config_count = cursor.fetchone()[0]

        if config_count == 0:
            # 插入T001项目默认配置
            initial_config = {
                "t001_project_version": "1.0.0",
                "panoramic_engine_version": "T001-Enhanced",
                "default_execution_correctness_target": 93.3,
                "default_triple_verification_enabled": True,
                "default_four_step_cognition_enabled": True,
                "supported_algorithms": ["PC", "FCI", "LiNGAM"],
                "supported_scan_strategies": ["fast_scan", "incremental_scan", "full_rebuild"]
            }

            cursor.execute('''
                INSERT INTO panoramic_models (
                    document_path, version_number, content_hash, semantic_hash,
                    abstraction_data, quality_metrics, confidence_score,
                    panoramic_reliability_status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                'system/t001_project_config.json',
                '1.0.0',
                hashlib.sha256(json.dumps(initial_config).encode()).hexdigest(),
                hashlib.md5(json.dumps(initial_config).encode()).hexdigest(),
                json.dumps(initial_config),
                json.dumps({"config_type": "system", "reliability": "high"}),
                1.0,
                'SYSTEM_VERIFIED'
            ))

            print("✅ 初始配置数据插入完成")

    except Exception as e:
        print(f"⚠️ 初始配置数据插入失败: {e}")
        # 配置数据插入失败不应该阻止数据库初始化

async def _persist_panoramic_data(self, panoramic_data: PanoramicPositionExtended):
    """持久化全景拼图数据到SQLite数据库"""
    try:
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 准备数据
            abstraction_data = json.dumps({
                "position_id": panoramic_data.position_id,
                "architectural_layer": panoramic_data.architectural_layer,
                "component_type": panoramic_data.component_type,
                "strategy_routes_count": len(panoramic_data.strategy_routes),
                "complexity_level": panoramic_data.complexity_assessment.overall_complexity.value if panoramic_data.complexity_assessment else "unknown"
            })

            relationships_data = json.dumps(panoramic_data.causal_relationships)
            quality_metrics_data = json.dumps(panoramic_data.quality_metrics)

            # 计算文档哈希
            content_hash = hashlib.sha256(abstraction_data.encode()).hexdigest()
            semantic_hash = hashlib.md5(relationships_data.encode()).hexdigest()

            # 插入或更新panoramic_models表
            cursor.execute('''
                INSERT OR REPLACE INTO panoramic_models (
                    document_path, version_number, content_hash, semantic_hash,
                    abstraction_data, relationships_data, quality_metrics,
                    confidence_score, panoramic_reliability_status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                f"panoramic_position_{panoramic_data.position_id}",
                "1.0.0",
                content_hash,
                semantic_hash,
                abstraction_data,
                relationships_data,
                quality_metrics_data,
                panoramic_data.quality_metrics.get("confidence_score", 0.0),
                "PENDING_USER_CONFIRMATION"
            ))

            # 获取插入的记录ID
            panoramic_model_id = cursor.lastrowid

            # 插入策略路线数据
            for route in panoramic_data.strategy_routes:
                cursor.execute('''
                    INSERT INTO strategy_routes_extended (
                        strategy_id, panoramic_position_id, route_path,
                        complexity_assessment, confidence_score, execution_priority,
                        dependencies, risk_factors, success_criteria
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    route.route_id,
                    str(panoramic_model_id),
                    json.dumps(route.route_path),
                    json.dumps({"complexity": "calculated"}),
                    route.confidence_score,
                    route.execution_priority,
                    json.dumps(route.dependencies),
                    json.dumps(route.risk_factors),
                    json.dumps(route.success_criteria)
                ))

            conn.commit()
            print(f"✅ 全景拼图数据持久化完成: {panoramic_data.position_id}")

    except Exception as e:
        raise DataPersistenceError(f"全景拼图数据持久化失败: {str(e)}")

async def _load_panoramic_data_from_db(self, position_id: str) -> Optional[PanoramicPositionExtended]:
    """从数据库加载全景拼图数据"""
    try:
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 查询主要数据
            cursor.execute('''
                SELECT id, abstraction_data, relationships_data, quality_metrics,
                       confidence_score, created_at
                FROM panoramic_models
                WHERE document_path = ?
            ''', (f"panoramic_position_{position_id}",))

            result = cursor.fetchone()
            if not result:
                return None

            model_id, abstraction_data, relationships_data, quality_metrics, confidence_score, created_at = result

            # 解析JSON数据
            abstraction = json.loads(abstraction_data)
            relationships = json.loads(relationships_data) if relationships_data else []
            metrics = json.loads(quality_metrics) if quality_metrics else {}

            # 查询策略路线数据
            cursor.execute('''
                SELECT route_path, confidence_score, execution_priority,
                       dependencies, risk_factors, success_criteria
                FROM strategy_routes_extended
                WHERE panoramic_position_id = ?
                ORDER BY execution_priority
            ''', (str(model_id),))

            route_results = cursor.fetchall()
            strategy_routes = []

            for route_result in route_results:
                route_path, route_confidence, priority, deps, risks, criteria = route_result
                strategy_routes.append(StrategyRouteData(
                    route_id=f"route_{len(strategy_routes)}",
                    route_path=json.loads(route_path),
                    confidence_score=route_confidence,
                    execution_priority=priority,
                    dependencies=json.loads(deps) if deps else [],
                    risk_factors=json.loads(risks) if risks else [],
                    success_criteria=json.loads(criteria) if criteria else []
                ))

            # 重构PanoramicPositionExtended对象
            return PanoramicPositionExtended(
                position_id=position_id,
                architectural_layer=abstraction.get("architectural_layer", "unknown"),
                component_type=abstraction.get("component_type", "unknown"),
                strategy_routes=strategy_routes,
                complexity_assessment=None,  # 需要重新计算
                quality_metrics=metrics,
                execution_context={},
                causal_relationships=relationships,
                created_at=datetime.fromisoformat(created_at)
            )

    except Exception as e:
        print(f"⚠️ 从数据库加载全景拼图数据失败: {e}")
        return None

def _check_database_integrity(self):
    """检查数据库完整性"""
    try:
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute('''
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name IN (
                    'panoramic_models', 'panoramic_causal_mappings',
                    'strategy_routes_extended', 'strategy_selection_history',
                    'causal_inference_results'
                )
            ''')

            existing_tables = [row[0] for row in cursor.fetchall()]
            required_tables = [
                'panoramic_models', 'panoramic_causal_mappings',
                'strategy_routes_extended', 'strategy_selection_history',
                'causal_inference_results'
            ]

            missing_tables = set(required_tables) - set(existing_tables)
            if missing_tables:
                raise DatabaseIntegrityError(f"缺少必要的数据库表: {missing_tables}")

            # 检查外键约束
            cursor.execute('PRAGMA foreign_key_check')
            fk_violations = cursor.fetchall()
            if fk_violations:
                raise DatabaseIntegrityError(f"外键约束违反: {fk_violations}")

            print("✅ 数据库完整性检查通过")
            return True

    except Exception as e:
        print(f"❌ 数据库完整性检查失败: {e}")
        return False
```

### 自定义异常类

```python
class DatabaseInitializationError(Exception):
    """数据库初始化错误"""
    pass

class DataPersistenceError(Exception):
    """数据持久化错误"""
    pass

class DatabaseIntegrityError(Exception):
    """数据库完整性错误"""
    pass
```

## 📊 数据库初始化特性

### 初始化流程
1. **目录创建**: 确保数据库文件目录存在
2. **表结构创建**: 创建所有必要的数据库表
3. **索引优化**: 创建查询性能优化索引
4. **触发器设置**: 设置数据完整性保证触发器
5. **初始数据**: 插入系统配置和默认数据
6. **完整性检查**: 验证数据库结构完整性

### 性能优化
- **索引策略**: 针对常用查询字段创建索引
- **外键约束**: 确保数据引用完整性
- **触发器优化**: 自动维护数据一致性
- **批量操作**: 支持批量数据插入和更新

### 错误处理
- **渐进式初始化**: 部分失败不影响整体初始化
- **错误恢复**: 支持从初始化错误中恢复
- **完整性验证**: 初始化后自动验证数据库完整性

## 📚 相关文档索引

### 前置文档
- `05_4-PanoramicPositioningEngine基础架构.md` - 引擎基础架构

### 后续文档
- `05_6-数据映射机制实现.md` - 数据映射机制实现
- `05_7-数据结构适配器实现.md` - 数据结构适配器

---

**注意**: 本文档是05-V4.5九步算法集成方案.md的第5部分，专注于PanoramicPositioningEngine的SQLite数据库初始化实现。具体的数据映射机制请参考下一个分步文档。
