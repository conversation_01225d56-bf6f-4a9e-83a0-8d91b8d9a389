# V4 ACE MCP Server配置指南

## 📋 文档概述

**项目**: V4 ACE MCP Server配置
**创建日期**: 2025-06-18
**版本**: F007-mcp-config-v1.0.L1.0.0
**目标**: 提供V4 ACE MCP Server的完整配置指南和集成方案

## 🔧 MCP服务器配置

### 完整配置文件
将以下配置添加到您的MCP配置文件中：

```json
{
  "mcpServers": {
    "context_7": {
      "command": "cmd /c npx",
      "args": [
        "-y",
        "@upstash/context7-mcp"
      ]
    },
    "browser-tools": {
      "command": "cmd /c npx",
      "args": [
        "-y",
        "@agentdeskai/browser-tools-mcp@latest"
      ]
    },
    "interactive-feedback": {
      "command": "uvx",
      "args": [
        "interactive-feedback@latest"
      ],
      "timeout": 6000,
      "autoApprove": [
        "interactive_feedback"
      ]
    },
    "simple-timeserver": {
      "command": "python",
      "args": ["-m", "mcp_simple_timeserver"]
    },
    "v4-ace-mcp": {
      "command": "python",
      "args": [
        "tools/ace/mcp/v4_context_guidance_server/main.py"
      ],
      "cwd": "c:/ExchangeWorks/xkong/xkongcloud",
      "timeout": 10000,
      "autoApprove": [
        "execute_checkresult_v4_modification_task",
        "get_next_modification_batch",
        "validate_and_continue_modifications"
      ],
      "env": {
        "PYTHONPATH": "tools/ace/src",
        "ACE_MCP_LOG_LEVEL": "INFO",
        "ACE_MCP_CONFIDENCE_THRESHOLD": "0.95",
        "ACE_MCP_EXECUTION_MODE": "fully_automatic",
        "ACE_MCP_QUALITY_DEGRADATION_DETECTION": "true",
        "ACE_MCP_STEEP_DECLINE_THRESHOLD": "0.15",
        "ACE_MCP_CONSECUTIVE_FAILURE_LIMIT": "4",
        "ACE_MCP_PROGRESS_NOTIFICATION": "concise"
      }
    }
  }
}
```

## 🎯 V4 ACE MCP配置详解

### 核心配置参数（全自动模式）
```yaml
v4_ace_mcp_config:
  server_name: "v4-ace-mcp"
  execution_mode: "fully_automatic"  # 全自动执行，无需人工确认

  execution:
    command: "python"
    main_script: "tools/ace/mcp/v4_context_guidance_server/main.py"
    working_directory: "c:/ExchangeWorks/xkong/xkongcloud"
    timeout: 10000  # 10秒，考虑扫描和解析时间

  auto_approved_tools:
    - "execute_checkresult_v4_modification_task"  # 全自动初始化和执行
    - "get_next_modification_batch"               # 自动获取下一批次
    - "validate_and_continue_modifications"       # 自动验证并继续

  environment_variables:
    PYTHONPATH: "tools/ace/src"                          # Python模块路径
    ACE_MCP_LOG_LEVEL: "INFO"                            # 日志级别
    ACE_MCP_CONFIDENCE_THRESHOLD: "0.95"                 # 95%置信度门禁
    ACE_MCP_EXECUTION_MODE: "fully_automatic"            # 全自动执行模式
    ACE_MCP_QUALITY_DEGRADATION_DETECTION: "true"        # 质量阶梯下降检测
    ACE_MCP_STEEP_DECLINE_THRESHOLD: "0.15"              # 阶梯下降阈值15%
    ACE_MCP_CONSECUTIVE_FAILURE_LIMIT: "4"               # 连续失败上限4次
    ACE_MCP_PROGRESS_NOTIFICATION: "concise"             # 简洁进度通知
```

### 🛠️ MCP工具接口

#### 1. execute_checkresult_v4_modification_task（全自动执行）
```yaml
功能: 全自动初始化并执行V4修改任务
输入: checkresult-v4目录路径
输出: 自动执行状态和简洁进度通知
特性:
  - 无需人工确认，立即开始执行
  - 自动批次处理（每批3个修改）
  - 实时质量监控和阶梯下降检测
用法: "使用ace mcp指令，执行修改（checkresult-v4路径）的任务"
```

#### 2. get_next_modification_batch（智能自动化）
```yaml
功能: 智能获取和执行下一批修改（内部自动调用）
特性:
  - 95%置信度自动过滤
  - 按严重程度自动排序
  - 质量阶梯下降自动检测
  - 异常时自动停止并通知
```

#### 3. validate_and_continue_modifications（无缝自动化）
```yaml
功能: 自动验证并无缝继续下一批（内部自动调用）
特性:
  - 自动质量验证和趋势分析
  - 失败时自动回滚
  - 阶梯下降时自动停止
  - 简洁进度通知
```

## 📋 IDE AI使用协议

### 📁 JSON文件目录结构
```
checkresult-v4/
├── json/                           # 🆕 MCP专用JSON文件目录
│   ├── 01-architecture-overview_mcp_report.json
│   ├── 02-kernel-and-plugin-lifecycle_mcp_report.json
│   ├── 03-service-bus-and-communication_mcp_report.json
│   └── ...
├── 01-architecture-overview_检查报告.md    # 人工查看MD文件
├── 02-kernel-and-plugin-lifecycle_检查报告.md
└── ...
```

**重要说明**：
- ✅ **JSON文件**：位于 `checkresult-v4/json/` 子目录，供MCP Server解析使用
- ✅ **MD文件**：位于 `checkresult-v4/` 根目录，供人工查看
- ✅ **双格式输出**：扫描时自动生成两种格式，85%解析效率提升

### 标准调用格式
```bash
# 基本格式
使用ace mcp指令，执行修改（checkresult-v4目录路径）的任务

# 完整路径示例
使用ace mcp指令，执行修改（docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/checkresult-v4）的任务

# 相对路径示例
使用ace mcp指令，执行修改（./checkresult-v4）的任务
```

### JSON协议约定

#### IDE AI请求格式
```json
{
  "mcp_request": {
    "tool": "execute_checkresult_v4_modification_task",
    "parameters": {
      "checkresult_path": "相对路径/checkresult-v4",
      "batch_size": 3,
      "confidence_threshold": 0.95
    }
  }
}
```

#### MCP Server响应格式（全自动模式）
```json
{
  "mcp_response": {
    "status": "auto_executing",
    "task_id": "uuid-string",
    "session_id": "session-uuid",
    "execution_mode": "fully_automatic",
    "progress": {
      "current_batch": 1,
      "total_batches": 5,
      "completion_percentage": 20,
      "modifications_completed": 3,
      "modifications_remaining": 12
    },
    "quality_metrics": {
      "session_confidence": 0.97,
      "quality_trend": "stable",
      "auto_continue": true
    },
    "human_notification": {
      "brief_status": "🚀 V4自动修改中：批次1/5 (20%) | 置信度97% | 质量稳定",
      "show_details": false
    },
    "auto_execution": {
      "next_batch_scheduled": true,
      "estimated_completion": "2分钟",
      "intervention_needed": false
    }
  }
}
```

## 🔒 安全和质量控制

### 🧠 智能质量阶梯下降检测（全自动模式）
```yaml
quality_degradation_detection:
  monitoring_approach: "阶梯式质量下降检测"

  degradation_patterns:
    steep_decline:
      - "连续3个批次质量评分下降 > 15%"
      - "单批次成功率从 > 90% 降至 < 70%"
      - "连续失败次数 ≥ 4次"

    gradual_decline:
      - "5个批次内质量评分累计下降 > 25%"
      - "平均置信度从 > 95% 降至 < 85%"

    anomaly_patterns:
      - "响应时间突然增加 > 300%"
      - "修改偏离度突然增加 > 50%"

  auto_intervention_threshold:
    trigger_only_on: "明显的阶梯式质量下降"
    ignore_minor_fluctuations: true
    minimum_degradation_severity: "steep_decline"

  intervention_actions:
    - "立即停止自动执行"
    - "保存详细进度状态"
    - "生成简洁的停止通知"
    - "提供新窗口重启指令"
```

### 📊 简洁进度通知机制（珍惜输出长度）
```yaml
concise_progress_reporting:
  reporting_principle: "珍惜IDE AI输出长度，只报告核心指标"

  notification_frequency:
    normal_execution: "每完成1个批次通知一次"
    quality_concern: "仅在明显质量下降时通知"
    task_completion: "任务完成时简洁总结"

  message_templates:
    auto_progress: "🚀 V4自动修改：批次{X}/{Y} ({Z}%) | 置信度{confidence}% | 质量{trend}"
    quality_decline: "⚠️ 质量下降：批次{X} 成功率{rate}% | 趋势{trend} | 继续监控"
    emergency_stop: "🛑 质量阶梯下降 | 进度{X}/{Y} | 新窗口重启：ace mcp checkresult-v4"
    task_complete: "✅ V4修改完成：{total}项 | 成功率{rate}% | 用时{time}"

  core_metrics_only:
    - "批次进度 (X/Y)"
    - "完成百分比"
    - "当前置信度"
    - "质量趋势 (stable/declining/critical)"
    - "预计剩余时间"
```

### 🛑 阶梯式质量下降自动停止机制
```yaml
intelligent_auto_termination:
  detection_philosophy: "只在明显质量阶梯下降时干预，避免过度敏感"

  termination_triggers:
    steep_quality_drop:
      - "连续3批次质量评分下降 > 15%"
      - "单批次成功率从90%+骤降至70%-"
      - "连续4次修改失败（非偶发）"

    severe_anomalies:
      - "响应时间突增300%+（可能卡死）"
      - "修改完全偏离指令（明显幻觉）"
      - "出现循环错误模式"

  ignore_minor_issues:
    - "单次修改失败（正常波动）"
    - "质量评分小幅下降 < 10%"
    - "偶发的响应延迟"
    - "轻微的修改偏差"

  termination_process:
    1. "检测到明显阶梯下降 → 立即停止"
    2. "保存精确进度状态"
    3. "生成简洁停止通知"
    4. "提供一键重启指令"

  restart_optimization:
    - "新会话自动从中断点继续"
    - "已完成修改不会重复"
    - "保持修改历史记录"
```

### 95%置信度门禁
```yaml
confidence_control:
  threshold: 0.95
  enforcement: "严格执行"
  low_confidence_action:
    - "< 0.95: 自动跳过"
    - "0.85-0.94: 标记手动审查"
    - "< 0.85: 直接忽略"
```

### 安全边界
```yaml
safety_boundaries:
  file_scope:
    - "仅修改checkresult-v4指定的目标文件"
    - "禁止修改系统文件或配置文件"
    - "禁止访问项目外部文件"
  
  modification_scope:
    - "严格按照JSON指令执行"
    - "禁止添加指令外的任何内容"
    - "禁止修改文件结构"
  
  error_handling:
    - "自动错误检测和回滚"
    - "连续失败时请求人工介入"
    - "完整的操作日志记录"
```

## 🚀 部署和验证

### 环境要求
```yaml
system_requirements:
  python_version: "≥ 3.8"
  working_directory: "项目根目录"
  required_structure:
    - "tools/ace/src/"
    - "tools/ace/mcp/"
    - "checkresult-v4目录（包含JSON报告）"
```

### 验证步骤
```bash
# 1. 验证Python环境
python --version

# 2. 验证ACE模块
python -c "from tools.ace.src.algorithms.detection_report_generator import DetectionReportGenerator; print('✅ ACE模块正常')"

# 3. 验证MCP配置
# 在IDE中测试MCP连接

# 4. 验证JSON报告
ls checkresult-v4/*_mcp_report.json
```

### 🤖 IDE AI通知示例

#### 自动执行进度通知（简洁版）
```
🚀 V4自动修改：批次1/5 (20%) | 置信度97% | 质量稳定
🚀 V4自动修改：批次2/5 (40%) | 置信度95% | 质量稳定
🚀 V4自动修改：批次3/5 (60%) | 置信度93% | 质量稳定
```

#### 质量下降监控（仅明显下降时通知）
```
⚠️ 质量下降：批次4 成功率72% | 趋势declining | 继续监控
```

#### 阶梯下降自动停止（简洁通知）
```
🛑 质量阶梯下降 | 进度3/5 | 新窗口重启：ace mcp checkresult-v4
```

#### 任务完成通知（简洁版）
```
✅ V4修改完成：15项 | 成功率98% | 用时3分钟
```

### 故障排除
```yaml
common_issues:
  python_path_error:
    symptom: "ModuleNotFoundError"
    solution: "检查PYTHONPATH环境变量设置"

  timeout_error:
    symptom: "MCP工具调用超时"
    solution: "增加timeout值到15000"

  json_not_found:
    symptom: "未发现JSON格式报告"
    solution: "运行simple_scanner.py生成JSON报告"

  hallucination_detected:
    symptom: "自动停止并提示AI质量下降"
    solution: "在新窗口重新调用ace mcp，系统会自动从中断点继续"

  session_quality_low:
    symptom: "会话质量评分持续下降"
    solution: "检查IDE AI状态，考虑重启IDE或清理上下文"
```

## 📊 性能监控

### 关键指标
```yaml
performance_metrics:
  response_time:
    - "工具调用响应: ≤ 100ms"
    - "JSON解析时间: ≤ 75ms"
    - "修改指令生成: ≤ 200ms"
  
  throughput:
    - "并发连接支持: ≥ 10个"
    - "批次处理能力: ≥ 100修改/分钟"
  
  reliability:
    - "连接稳定性: ≥ 99.5%"
    - "修改成功率: ≥ 95%"
```

---

**创建时间**: 2025-06-18
**维护说明**: 基于V4设计文档和实际部署经验持续更新
