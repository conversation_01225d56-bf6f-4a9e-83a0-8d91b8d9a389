# V4.3方：实施计划与里程碑

## 1. 文档信息

- **文档版本**: V4.3
- **创建日期**: 2025-08-13
- **的**: 明确V4.3架构治理引擎的实施计划、关键里程碑和资源分配，确保项目按时、高质量地完成。

## 2. 总体实施策略

V4.3方案的实施将遵循**迭代开发、小步快跑**的原则，优先实现核心功能，并通过持续集成和测试确保质量。

-   **核心团队**: 架构师、算法工程师、AI工程师、前端工程师。
-   **技术栈**: Python (Py AI, 算法), Java (RooCode), JavaScript (UI), Neo4j (图数据库)。
-   **开发流程**: 敏捷开发，每两一个迭代周期。

## 3. 阶段划分与里程碑（V4.3-S：Serena 集成 + 两阶段门禁）

我们将 V4.3 的实施划分为以下 6 个阶段，确保“先设计、后代码”的确定性闭环逐步落地。

| 阶段编号 | 阶段名称 | 关键目标 | 交付物 | 预计完成时间 |
| :------- | :------- | :------- | :------- | :----------- |
| **阶段1** | **设计审计与门禁 v1** | 文档解析、Schema 校验、伪代码覆盖率评分、宏观图构建与一致性校验跑通。 | `RichReport v1`（COMPLIANT 为门禁）；宏观图。 | 2025-09-05 |
| **阶段2** | **为 ProjectManager 集成 Serena 适配器** | 完成 `roocode_adapter` 的重构，实现通过RooCode对Serena的稳定调用。 | Serena 调用通过、`micro_graph` 内存装配、错误与超时策略。 | 2025-09-20 |
| **阶段3** | **在 Workflows 中构建断言引擎** | 在 `design_validation_workflow.py` 内部建立“约束→证据谓词→断言”映射。 | `assertion_results.yaml`、可复现实验用例。 | 2025-10-05 |
| **阶段4** | **算法驱动规划与智能迭代核心** | 在 `validators` 中实现基于图查询的“计划骨架”生成器，并在 `analysts` 中实现对计划的辅助增强。同时完成核心的收敛性检查与提示词优化算法。 | 包含完整 `SuperTaskInstruction` 的 `RichReport v2` (YAML 格式)。 | 2025-10-20 |
| **阶段5** | **UI 对接重构后的 Workflow 输出** | 九宫格区域5/8 完成对新版 `RichReport` (YAML) 的解析与展示。 | 前后端联调通过、审批门禁生效。 | 2025-11-05 |
| **阶段6** | **端到端与性能优化** | 大仓库性能、缓存与并发；回归与用户验收。 | E2E 报告、性能基线、用户手册、发布说明。 | 2025-11-30 |

## 4. 资源分配 (示例)

| 角色/资源 | 阶段1 | 阶段2 | 阶段3 | 阶段4 | 阶段5 | 阶段6 |
| :-------- | :---- | :---- | :---- | :---- | :---- | :---- |
| **架构师** | 50% | 50% | 30% | 70% | 50% | 30% |
| **算法工程师** | 80% | 100% | 100% | 80% | 20% | 40% |
| **AI工程师** | 20% | 30% | 30% | 100% | 50% | 30% |
| **前端工程师** | 0% | 0% | 0% | 20% | 100% | 20% |
| **测试工程师** | 0% | 0% | 0% | 20% | 30% | 100% |
| **图数据库 (Neo4j)** | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| **算资源 (GPU)** | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ |

*注：百分比表示该角色在该阶段投入的时间比例。*

## 5. 风险与挑战（V4.3-S 更新）

-   **外部依赖稳定性**:
    -   **Serena/LSP 可用性**：语言服务器版本变更与兼容性；离线/受限网络环境下的运行策略。
    -   **请求性能与并发**：大仓库下查询风暴风险，需要限流、缓存与批量化策略。
-   **规则覆盖与误判**:
    -   断言规则的不完全覆盖或过严可能导致误报/漏报；需灰度与人工确认位作为兜底。
    -   规则生命周期管理（版本化、审计追踪、回滚）。
-   **确定性与可复现性**:
    -   报告哈希一致性校验（排除时间戳等非决定性字段）。
    -   Serena 超时/失败的降级与补偿策略，避免影响整体判定。
-   **人机协作与体验**:
    -   报告可读性、任务“一键复制”、区域8 Diff 审核体验。
    -   组织流程适配：审批门禁引入对交付节奏的影响。
-   **重构风险**:
    -   在升级 `DesignValidationWorkflow` 时，需要确保其接口与 `ProjectManager` 的现有调用方式保持兼容，避免对上层模块产生破坏性影响。

## 6. 成功标准

-   设计侧门禁：Schema 0 违规、图谱冲突=0、伪代码覆盖率≥60、Mermaid 全合法，`RichReport v1.overall_status=COMPLIANT`。
-   代码侧门禁：`MISSING=0`、`CONFLICT=0`、`LEGACY=0`，所有断言为 `OK`，`RichReport v2.overall_status=COMPLIANT`。
-   可复现性：同一 commit/同一配置重跑，报告哈希一致（忽略时间戳等非决定性字段）。
-   性能与稳定：Serena 单请求 P95 < 3s、审计全程成功率 ≥ 99%。

## 7. 后续计划

-   定期召开项目进展会议，跟踪里程碑完成情况。
-   建立持续集成/持续部署 (CI/CD) 流程，自动化测试和部署。
-   根据实际反馈，持续优化算法和UI。
