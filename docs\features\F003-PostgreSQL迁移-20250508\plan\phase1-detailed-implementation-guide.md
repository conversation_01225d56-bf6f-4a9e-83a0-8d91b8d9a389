---
title: PostgreSQL迁移第一阶段详细实施指南
document_id: F003-PLAN-003
document_type: 实现指南
category: 数据库迁移
scope: XKC-CORE
keywords: [PostgreSQL, 数据库迁移, 实施步骤, 第一阶段, 基础设施准备, 详细指南, <PERSON>hem<PERSON>规划, <PERSON>hema命名规范]
created_date: 2025-05-30
updated_date: 2025-06-07
status: 草稿
version: 1.7
authors: [AI助手]
affected_features:
  - F003
  - F004 # CommonsUidLibrary
related_docs:
  - ./implementation-plan.md
  - ./phase1-implementation-steps.md
  - ../../../common/middleware/postgresql/schema-planning-guide.md
  - ../../../common/middleware/integration/baidu-uid-generator-postgresql-implementation.md
  - ../../../common/middleware/integration/postgresql-persistent-id-fingerprint-recovery.md
  - ../../../features/F004-CommonsUidLibrary-20250511/plan/implementation-plan.md
---

# PostgreSQL迁移第一阶段详细实施指南

## 前言

本文档是PostgreSQL迁移第一阶段（基础设施准备）的详细实施指南，专为AI和人类配合执行而设计。文档提供了精确的操作步骤、预期结果和验证方法，以确保第一阶段的顺利完成。

根据截图显示，环境已经部分准备就绪：PostgreSQL 17.4已安装，数据库xkong_main_db已创建，并且pgAdmin 4管理工具已配置。本指南将从这一基础上继续完成剩余的第一阶段工作。

## 前提条件确认

在开始实施前，请确认以下条件已满足：

- [x] PostgreSQL 17.4已安装
- [x] pgAdmin 4管理工具已安装并可访问
- [x] 数据库xkong_main_db已创建
- [x] xkongcloud-service-center可访问并有权限配置KV参数

如果有任何前提条件未满足，请先完成相应的准备工作再继续。

## 详细实施步骤

### 1. 数据库用户和权限配置

从截图可以看到，数据库xkong_main_db已创建。现在需要创建专用数据库用户并配置适当的权限。

#### 1.1 创建专用数据库用户

1. **打开pgAdmin 4并连接到PostgreSQL服务器**
   - 双击左侧导航栏中的PostgreSQL服务器连接
   - 输入主密码（如果需要）

2. **创建新用户**
   - 在左侧导航栏中，展开"Servers" > "[您的服务器]" > "Login/Group Roles"
   - 右键点击"Login/Group Roles"，选择"Create" > "Login/Group Role..."

3. **配置用户基本信息**
   - 在"General"选项卡中，设置：
     - Name: `xkong_user`

4. **设置用户密码**
   - 切换到"Definition"选项卡
   - 设置密码：输入一个强密码，例如`XKong@2025internal-core`（建议使用更复杂的密码）
   - 记录此密码，后续配置KV参数时需要使用

5. **配置用户权限**
   - 切换到"Privileges"选项卡
   - 设置以下选项：
     - Can login? ✓
     - Superuser? ✗
     - Create databases? ✗
     - Create roles? ✗
     - Inherit rights from parent roles? ✓

6. **保存用户设置**
   - 点击"Save"按钮保存用户设置

**预期结果**：在"Login/Group Roles"下应该出现新创建的`xkong_user`用户。

#### 1.2 配置数据库级别权限

1. **配置数据库级别权限**
   - 在左侧导航栏中，展开"Servers" > "[您的服务器]" > "Databases"
   - 右键点击"xkong_main_db"，选择"Properties"
   - 切换到"Security"选项卡
   - 点击"+"按钮添加权限
   - 设置：
     - Grantee: `xkong_user`（从下拉列表中选择）
     - Privileges: 勾选以下权限
       - CONNECT
       - CREATE
       - TEMPORARY
       - USAGE
   - 点击"Save"保存

**预期结果**：`xkong_user`用户现在应该拥有对`xkong_main_db`数据库的必要权限。

> **注意**：根据PostgreSQL最佳实践，我们将创建多个业务相关的schema来更好地组织数据。所有业务表必须明确指定其所属的Schema，不依赖任何默认Schema设置。这符合`schema-planning-guide.md`中推荐的多Schema组织方式。

#### 1.3 创建业务Schema

根据PostgreSQL最佳实践（参考`schema-planning-guide.md`），我们应该创建多个业务相关的schema，而不是仅使用默认的"public" schema。这样可以更好地组织数据，提高安全性，并为未来的扩展提供灵活性。

1. **创建user_management Schema**
   - 连接到数据库（使用postgres超级用户或具有CREATE权限的用户）
   - 在pgAdmin中，右键点击"Schemas"，选择"Create" > "Schema..."
   - 在"General"选项卡中，设置：
     - Name: `user_management`
     - Owner: `xkong_user`（从下拉列表中选择）
   - 点击"Save"保存

2. **创建common_config Schema**
   - 右键点击"Schemas"，选择"Create" > "Schema..."
   - 在"General"选项卡中，设置：
     - Name: `common_config`
     - Owner: `xkong_user`（从下拉列表中选择）
   - 点击"Save"保存

3. **创建infra_uid Schema**
   - 右键点击"Schemas"，选择"Create" > "Schema..."
   - 在"General"选项卡中，设置：
     - Name: `infra_uid`
     - Owner: `xkong_user`（从下拉列表中选择）
   - 点击"Save"保存

**预期结果**：数据库中应该创建了三个新的schema：`user_management`、`common_config`和`infra_uid`，并且`xkong_user`用户是这些schema的所有者，自动拥有所有权限。

#### 1.4 验证数据库连接

1. **创建新的服务器连接**
   - 在pgAdmin 4中，点击左上角工具栏中的"Add New Server"按钮（或右键点击"Servers"，选择"Register" > "Server..."）

2. **配置连接信息**
   - 在"General"选项卡中，设置：
     - Name: `xkong_main_db (xkong_user)`

   - 切换到"Connection"选项卡，设置：
     - Host name/address: `localhost`（如果数据库在其他服务器上，请使用相应的IP地址）
     - Port: `5432`
     - Maintenance database: `xkong_main_db`
     - Username: `xkong_user`
     - Password: 输入之前设置的密码
     - Save password? ✓

3. **测试连接**
   - 点击"Save"保存连接设置
   - 系统将自动尝试连接到数据库

**预期结果**：新的服务器连接应该成功建立，并且在左侧导航栏中显示`xkong_main_db (xkong_user)`连接。

**可能的问题及解决方案**：
- 如果连接失败，检查密码是否正确
- 检查PostgreSQL服务是否正在运行
- 检查防火墙设置是否允许连接到PostgreSQL端口
- 检查pg_hba.conf文件中的访问控制设置

### 2. 在xkongcloud-service-center中配置PostgreSQL相关KV参数

#### 2.1 配置PostgreSQL基础连接参数

1. **访问xkongcloud-service-center**
   - 打开浏览器，访问xkongcloud-service-center的管理界面
   - 使用管理员账号登录

2. **导航到KV参数管理页面**
   - 在导航菜单中找到"KV参数管理"或类似的选项

3. **添加PostgreSQL基础连接参数**
   - 点击"添加参数"或类似的按钮
   - 添加以下参数（每个参数单独添加）：

     | 参数名 | 参数值 | 说明 |
     |-------|-------|------|
     | `postgresql.url` | `*************************************************` | 数据库连接URL，如果数据库在其他服务器上，请替换localhost为相应的IP地址 |
     | `postgresql.username` | `xkong_user` | 数据库用户名 |
     | `postgresql.password` | `[之前设置的密码]` | 数据库密码 |

   - 确保这些参数没有设置默认值，这样当参数缺失时应用将无法启动

**预期结果**：所有基础连接参数都应该成功添加到KV参数管理系统中。

#### 2.2 配置PostgreSQL DDL自动生成策略

1. **添加DDL自动生成策略参数**
   - 在KV参数管理页面，点击"添加参数"
   - 添加以下参数：

     | 参数名 | 参数值 | 说明 |
     |-------|-------|------|
     | `postgresql.ddl-auto` | `create` | 开发环境使用create，生产环境应使用validate或none |

**预期结果**：DDL自动生成策略参数应该成功添加到KV参数管理系统中。

#### 2.3 配置PostgreSQL连接池参数

1. **添加连接池参数**
   - 在KV参数管理页面，点击"添加参数"
   - 添加以下参数：

     | 参数名 | 参数值 | 说明 |
     |-------|-------|------|
     | `postgresql.pool.max-size` | `10` | 连接池最大连接数 |
     | `postgresql.pool.min-idle` | `5` | 连接池最小空闲连接数 |
     | `postgresql.pool.connection-timeout` | `30000` | 连接超时时间（毫秒） |
     | `postgresql.pool.idle-timeout` | `600000` | 空闲连接超时时间（毫秒） |
     | `postgresql.pool.max-lifetime` | `1800000` | 连接最大生存时间（毫秒） |

**预期结果**：连接池参数应该成功添加到KV参数管理系统中。

#### 2.4 配置PostgreSQL JPA参数

1. **添加JPA参数**
   - 在KV参数管理页面，点击"添加参数"
   - 添加以下参数：

     | 参数名 | 参数值 | 说明 |
     |-------|-------|------|
     | `postgresql.show-sql` | `true` | 是否显示SQL语句。开发环境建议设置为true以便调试；测试环境和生产环境建议设置为false以提高性能和减少日志量 |
     | `postgresql.format-sql` | `true` | 是否格式化SQL语句。开发环境建议设置为true以提高可读性；测试环境和生产环境建议设置为false |
     | `postgresql.batch-size` | `30` | 批处理大小。开发环境默认值30通常合适；测试环境可设置为30-50；生产环境建议根据性能测试结果调整为30-50。太小批处理效果不明显，太大可能增加延迟和内存消耗 |
     | `postgresql.fetch-size` | `100` | 查询结果集获取大小。开发环境和测试环境默认值100通常合适；生产环境可根据查询特性和内存情况调整为100-500。如果经常查询返回大量数据，增大此值可减少数据库往返次数 |

**重要说明**：Schema结构必须由DBA或开发人员手动管理，应用程序只负责验证Schema是否存在，不会尝试创建Schema。如果验证发现任何必需的Schema不存在，应用程序将无法启动，并显示明确的错误信息。这确保了数据库结构的可控性和一致性，避免了自动化和手动操作混合导致的冲突。

**预期结果**：JPA参数应该成功添加到KV参数管理系统中。

#### 2.5 配置百度UID生成器核心参数

1. **添加UID生成器核心参数**
   - 在KV参数管理页面，点击"添加参数"
   - 添加以下参数：

     | 参数名 | 参数值 | 说明 |
     |-------|-------|------|
     | `uid.schema.name` | `infra_uid` | UID生成器使用的数据库Schema名称，必须与创建的infra_uid Schema一致 |
     | `uid.epochStr` | `2025-01-01` | 时间基点，格式为"yyyy-MM-dd" |
     | `uid.timeBits` | `31` | 时间戳位数，31位约68年 |
     | `uid.workerBits` | `18` | 工作机器ID位数，18位约26万台 |
     | `uid.seqBits` | `14` | 序列号位数，14位每秒约1.6万个 |

   - 确保这些参数没有设置默认值，这样当参数缺失时应用将无法启动

**预期结果**：UID生成器核心参数应该成功添加到KV参数管理系统中。

#### 2.6 配置百度UID生成器高级参数

1. **添加UID生成器高级参数**
   - 在KV参数管理页面，点击"添加参数"
   - 添加以下参数：

     | 参数名 | 参数值 | 说明 |
     |-------|-------|------|
     | `uid.boostPower` | `3` | RingBuffer扩容参数，3表示RingBuffer大小为2^3=8倍 |
     | `uid.paddingFactor` | `50` | RingBuffer填充因子，50表示剩余50%时填充 |
     | `uid.scheduleInterval` | `60` | RingBuffer调度时间间隔（秒），60表示每分钟调度一次 |

**预期结果**：UID生成器高级参数应该成功添加到KV参数管理系统中。

#### 2.7 配置持久化实例ID及特征码恢复参数

1. **添加持久化实例ID及特征码恢复参数**
   - 在KV参数管理页面，点击"添加参数"
   - 添加以下参数：

     | 参数名 | 开发环境值 | 生产环境建议值 | 说明 |
     |-------|-----------|--------------|------|
     | `uid.instance.environment` | `dev` | `prod` | 环境标识，用于区分不同环境 |
     | `uid.instance.group` | `development` | `production` | 实例组标识，用于分组管理 |
     | `uid.instance.local-storage-path` | `./instance.id` | `/var/lib/xkong/instance.id` | 本地存储路径，生产环境应使用绝对路径 |
     | `uid.instance.recovery.enabled` | `true` | `true` | 是否启用特征码恢复，两种环境都建议启用 |
     | `uid.instance.recovery.high-confidence-threshold` | `150` | `180` | 高置信度阈值，生产环境建议提高以增加安全性 |
     | `uid.instance.recovery.minimum-acceptable-score` | `70` | `85` | 最低可接受分数，生产环境建议提高以减少误匹配风险 |
     | `uid.instance.recovery.strategy` | `ALERT_AND_NEW` | `ALERT_AUTO_WITH_TIMEOUT` | 恢复策略，生产环境建议使用带超时的混合模式，平衡自动恢复和人工干预 |
     | `uid.instance.recovery.timeout-seconds` | 不适用 | `300` | 恢复策略超时时间（秒），仅在使用`ALERT_AUTO_WITH_TIMEOUT`策略时有效 |
     | `uid.worker.lease-duration-seconds` | `259200` | `259200` | 租约持续时间（秒），设置为3天以提高稳定性 |
     | `uid.instance.encryption.enabled` | `false` | `true` | 是否启用实例ID文件加密，生产环境建议启用以提高安全性 |
     | `uid.instance.encryption.algorithm` | `AES-256-GCM` | `AES-256-GCM` | 加密算法，两种环境保持一致 **（注意：此参数在xkongcloud-commons-uid库中实际未被使用，库内部硬编码使用AES算法，不支持外部配置）** |

**预期结果**：持久化实例ID及特征码恢复参数应该成功添加到KV参数管理系统中。

**重要说明 - 参数实际使用情况**：

以下参数虽然在文档中列出，但在xkongcloud-commons-uid库的实际实现中并未被使用：

1. **`uid.instance.encryption.algorithm`**：
   - **问题**：KeyManagementService硬编码使用AES算法，不支持配置
   - **库实现**：`KeyGenerator.getInstance("AES")` 和固定的AES-256-GCM算法
   - **当前状态**：库内部不接受外部加密算法配置
   - **建议**：此参数可以从配置中移除，因为库实际不支持此参数

2. **`uid.worker.lease-renewal-interval-seconds`**：
   - **问题**：PersistentInstanceWorkerIdAssigner自动计算续约间隔
   - **库实现**：`this.leaseRenewalIntervalSeconds = Math.max(leaseDurationSeconds / 3, 1);`
   - **当前状态**：库内部固定为租约时长的1/3，不接受外部配置
   - **建议**：此参数可以从配置中移除，因为库实际不支持此参数

这些参数在动态参数分析器的报告中会被标记为"建议清理"，这是正确的，因为它们确实没有被实际使用。

**环境参数差异说明**：

1. **环境和分组标识**：
   - 生产环境使用`prod`和`production`明确标识环境和实例组
   - 不同环境使用不同的标识值是正常且推荐的做法，有助于环境隔离

2. **存储路径**：
   - 开发环境可以使用相对路径，简化开发和测试
   - 生产环境应使用绝对路径，提高稳定性和可靠性

3. **恢复参数**：
   - 生产环境提高了匹配阈值，增加了恢复过程的严格性
   - 生产环境使用带超时的混合策略（`ALERT_AUTO_WITH_TIMEOUT`），在发出告警后等待一定时间让人工干预，如果超时无人响应则自动选择最佳匹配或创建新实例，平衡了安全性和自动恢复的需求

4. **租约参数**：
   - 生产环境延长了租约时间，减少租约过期风险
   - 相应调整了续约间隔，保持合理的续约频率

5. **安全参数**：
   - 生产环境启用了加密功能，提高了安全性
   - 加密算法在两种环境中保持一致，使用强加密标准

#### 2.8 配置应用基础参数

根据`application.properties`中定义的`kv.param.required-keys`，还需要配置以下应用基础参数：

1. **添加应用配置参数**
   - 在KV参数管理页面，点击"添加参数"
   - 添加以下参数：

     | 参数名 | 开发环境值 | 生产环境建议值 | 说明 |
     |-------|-----------|--------------|------|
     | `app.config.timeout` | `30000` | `10000` | 应用配置超时时间（毫秒），生产环境建议减少超时时间以快速失败 |
     | `app.config.maxRetries` | `3` | `5` | 最大重试次数，生产环境建议增加重试次数以提高稳定性 |

2. **添加RabbitMQ配置参数**
   - 添加以下RabbitMQ相关参数：

     | 参数名 | 开发环境值 | 生产环境建议值 | 说明 |
     |-------|-----------|--------------|------|
     | `rabbitmq.host` | `localhost` | `[生产环境RabbitMQ主机地址]` | RabbitMQ服务器主机地址 |
     | `rabbitmq.port` | `5672` | `5672` | RabbitMQ服务器端口 |
     | `rabbitmq.username` | `guest` | `[生产环境用户名]` | RabbitMQ用户名，生产环境不应使用默认guest用户 |
     | `rabbitmq.password` | `guest` | `[生产环境密码]` | RabbitMQ密码，生产环境应使用强密码 |

3. **添加Valkey（Redis兼容）配置参数**
   - 添加以下Valkey相关参数：

     | 参数名 | 开发环境值 | 生产环境建议值 | 说明 |
     |-------|-----------|--------------|------|
     | `valkey.host` | `localhost` | `[生产环境Valkey主机地址]` | Valkey服务器主机地址 |
     | `valkey.port` | `6379` | `6379` | Valkey服务器端口 |

**重要说明**：
- 这些参数在`application.properties`的`kv.param.required-keys`中被定义为必需参数
- 如果任何一个参数缺失，应用启动时会显示明确的错误信息
- 生产环境的RabbitMQ和Valkey配置需要根据实际环境进行调整
- 密码和敏感信息应使用安全的方式管理，避免在配置文件中明文存储

**预期结果**：应用基础参数应该成功添加到KV参数管理系统中。

### 3. 验证配置

#### 3.1 验证Schema创建

1. **验证Schema是否成功创建**
   - 在pgAdmin中，展开"Servers" > "[您的服务器]" > "Databases" > "xkong_main_db" > "Schemas"
   - 确认以下Schema已成功创建：
     - `user_management`
     - `common_config`
     - `infra_uid`

2. **验证Schema所有权**
   - 对于每个Schema，右键点击并选择"Properties"
   - 在"General"选项卡中，确认Owner是`xkong_user`

**预期结果**：所有必需的Schema都已成功创建，并且`xkong_user`用户是这些Schema的所有者，自动拥有所有权限。

#### 3.2 验证KV参数

1. **查询已配置的参数**
   - 在xkongcloud-service-center的KV参数管理页面
   - 使用搜索功能，分别搜索"postgresql"和"uid"前缀的参数
   - 确认所有必需参数都已正确配置

2. **验证参数可访问性**
   - 使用xkongcloud-service-center的API或管理界面
   - 尝试获取几个关键参数，如`postgresql.url`和`uid.epochStr`
   - 确认可以正确获取这些参数

3. **特别验证Schema相关参数**
   - 确认`postgresql.schema.list`参数（如果已配置）包含所有必需的Schema（"user_management,common_config,infra_uid"）

4. **确认加密相关参数**（如果已配置）
   - 确认`uid.instance.encryption.enabled`参数已正确配置
   - 确认`uid.instance.encryption.algorithm`参数已正确配置

**预期结果**：所有配置的参数都应该可以通过API或管理界面正确获取，并且Schema相关参数符合最佳实践。

#### 3.3 记录配置信息

1. **创建配置记录文档**
   - 创建一个文档，记录所有配置的参数和值
   - 特别记录数据库连接信息和凭据
   - 记录创建的Schema及其用途
   - 将文档保存在安全的位置

**预期结果**：所有配置信息都应该被记录下来，以便后续参考。

## 注意事项

1. **不需要手动创建百度UID生成器所需的表结构**
   - 在此阶段**不需要**手动创建infra_uid.instance_registry和infra_uid.worker_id_assignment表
   - 这些表将在第二阶段由UidTableManagerService使用xkongcloud-commons-uid公共库中的UidTableManager工具类自动创建在`infra_uid` Schema中并进行管理

2. **参数检查机制**
   - PostgreSQLConfig和UidGeneratorConfig类将实现模块化参数检查
   - 每个配置类负责检查自己所需的必需参数
   - 缺少必需参数时应用将无法启动，并显示明确的错误信息
   - 这种方式比使用kv.param.required-keys配置更安全、更模块化

3. **实体类Schema指定（强制要求）**
   - 所有实体类必须使用`@Table(name = "表名", schema = "schema名")`明确指定Schema
   - 不允许依赖任何默认Schema设置
   - 这是一项强制性要求，没有例外情况
   - 代码审查过程必须确保所有实体类都遵循此规范

   **正确的实体类示例**：
   ```java
   @Entity
   @Table(name = "user", schema = "user_management")
   public class User {
       @Id
       private Long userId;

       @Column(name = "username", nullable = false)
       private String username;

       // 其他字段和方法...
   }
   ```

   **错误的实体类示例**：
   ```java
   @Entity
   @Table(name = "user")  // 错误：没有指定schema
   public class User {
       @Id
       private Long userId;

       @Column(name = "username", nullable = false)
       private String username;

       // 其他字段和方法...
   }
   ```

   **为什么这是强制性要求**：
   - 明确指定Schema提高了代码的可读性和明确性
   - 防止表被错误地创建在默认Schema中
   - 确保在多Schema环境中表的位置是明确的
   - 简化了配置，不需要依赖全局默认Schema设置

4. **密码安全**
   - 确保数据库密码足够复杂
   - 避免在代码或配置文件中硬编码密码
   - 考虑使用密码管理工具或密钥库管理敏感信息

5. **环境差异**
   - 开发环境可以使用`postgresql.ddl-auto=create`
   - 测试环境可以使用`postgresql.ddl-auto=create-drop`
   - 生产环境必须使用`postgresql.ddl-auto=validate`或`none`

## 完成标准检查清单

完成第一阶段实施后，请检查以下项目：

- [ ] 数据库用户`xkong_user`已创建
- [ ] `xkong_user`用户拥有对`xkong_main_db`数据库的必要权限
- [ ] 以下业务Schema已成功创建：
  - [ ] `user_management`
  - [ ] `common_config`
  - [ ] `infra_uid`
- [ ] `xkong_user`用户是所有创建的Schema的所有者
- [ ] 可以使用`xkong_user`用户成功连接到`xkong_main_db`数据库
- [ ] 所有必需的PostgreSQL基础连接参数已在xkongcloud-service-center中配置
- [ ] PostgreSQL DDL自动生成策略参数已配置
- [ ] 百度UID生成器核心参数已配置
- [ ] 应用基础参数（app.config、rabbitmq、valkey）已配置
- [ ] 持久化实例ID及特征码恢复参数已配置
- [ ] 可以通过xkongcloud-service-center的API或管理界面获取所有配置的参数
- [ ] 所有配置信息都已记录下来
- [ ] 所有实体类都明确指定了Schema

只有当所有项目都已完成时，才能认为第一阶段实施完成。

## 下一步

完成第一阶段后，可以进入第二阶段：xkongcloud-commons-uid公共库开发。在第二阶段中，将创建xkongcloud-commons-uid公共库，实现WorkerNodeType枚举、MachineFingerprints类、PersistentInstanceManager类和PersistentInstanceWorkerIdAssigner类，以及UidTableManager工具类。

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 2.2 | 2025-01-15 | 添加参数实际使用情况说明，明确uid.instance.encryption.algorithm和uid.worker.lease-renewal-interval-seconds参数在库中未被实际使用的真实情况 | AI助手 |
| 2.1 | 2025-06-14 | 添加缺失的uid.schema.name参数和应用基础参数配置（app.config、rabbitmq、valkey），移除未使用的uid.worker.lease-renewal-interval-seconds参数 | AI助手 |
| 2.0 | 2025-06-13 | 更新恢复策略为ALERT_AUTO_WITH_TIMEOUT并添加超时参数 | AI助手 |
| 1.9 | 2025-06-12 | 添加生产环境参数建议值和环境参数差异说明 | AI助手 |
| 1.8 | 2025-06-10 | 添加UID加密功能相关配置参数 | AI助手 |
| 1.7 | 2025-06-07 | 取消postgresql.schema.create-automatically参数，明确Schema由人工管理 | AI助手 |
| 1.6 | 2025-06-06 | 移除postgresql.schema参数，简化配置，强制所有实体类必须明确指定Schema | AI助手 |
| 1.5 | 2025-06-05 | 将默认Schema从user_management改回public，确保所有实体类明确指定Schema | AI助手 |
| 1.4 | 2025-06-04 | 将Schema所有者从postgres改为xkong_user，移除冗余的权限授予步骤 | AI助手 |
| 1.3 | 2025-06-03 | 将system_config Schema重命名为common_config，将uid_generator Schema重命名为infra_uid，完全符合Schema命名规范 | AI助手 |
| 1.2 | 2025-06-02 | 将core_business Schema拆分为user_management Schema，符合`<业务领域>_<可选子域>`格式的命名规范 | AI助手 |
| 1.1 | 2025-06-01 | 更新Schema配置，符合PostgreSQL最佳实践，使用多Schema组织方式，保留public作为默认Schema | AI助手 |
| 1.0 | 2025-05-30 | 初始版本 | AI助手 |
