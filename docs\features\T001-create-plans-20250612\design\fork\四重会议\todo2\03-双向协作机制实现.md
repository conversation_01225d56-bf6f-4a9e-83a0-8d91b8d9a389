# 03-双向协作机制实现（DRY重构版）

## 📋 文档信息

**文档ID**: F007-FOUR-LAYER-MEETING-BIDIRECTIONAL-003  
**依赖配置**: 引用 `00-共同配置.json`  
**前置依赖**: 02-API管理核心模块.md  
**AI负载等级**: 中等（≤8个概念，≤600行代码，≤90分钟）  
**置信度目标**: 90%+  
**执行优先级**: 3  

## 🎯 双向智能协作核心机制

### Thinking质量审查器
```python
# 【AI自动创建】tools/ace/src/bidirectional_collaboration/thinking_audit/thinking_quality_auditor.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Thinking质量审查器 - Python算法审查AI的thinking过程
引用: 00-共同配置.json 的 validation_standards
"""

import sys
import os
from datetime import datetime
import re

# 确保可以导入common模块
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', '..')
sys.path.insert(0, src_dir)

from common_config_loader import CommonConfigLoader

class ThinkingQualityAuditor:
    """Thinking质量审查器（基于双向协作机制）"""
    
    def __init__(self):
        self.config = CommonConfigLoader()
        self.validation_standards = self.config.get_validation_standards()
        self.confidence_threshold = self.validation_standards.get("confidence_threshold", 0.95)
        
        # 审查标准
        self.audit_criteria = {
            "logical_consistency": 0.25,  # 逻辑一致性权重
            "completeness": 0.25,         # 完整性权重
            "metacognitive_quality": 0.25, # 元认知质量权重
            "reasoning_depth": 0.25        # 推理深度权重
        }
    
    def audit_thinking_process(self, thinking_content, task_context):
        """审查AI的thinking过程"""
        audit_result = {
            "timestamp": datetime.now().isoformat(),
            "task_context": task_context,
            "thinking_length": len(thinking_content),
            "audit_scores": {},
            "overall_score": 0.0,
            "approval_status": "PENDING",
            "improvement_suggestions": []
        }
        
        # 1. 逻辑一致性审查
        logical_score = self._audit_logical_consistency(thinking_content)
        audit_result["audit_scores"]["logical_consistency"] = logical_score
        
        # 2. 完整性验证
        completeness_score = self._audit_completeness(thinking_content, task_context)
        audit_result["audit_scores"]["completeness"] = completeness_score
        
        # 3. 元认知质量评估
        metacognitive_score = self._audit_metacognitive_quality(thinking_content)
        audit_result["audit_scores"]["metacognitive_quality"] = metacognitive_score
        
        # 4. 推理深度评估
        reasoning_score = self._audit_reasoning_depth(thinking_content)
        audit_result["audit_scores"]["reasoning_depth"] = reasoning_score
        
        # 计算总分
        overall_score = sum(
            score * self.audit_criteria[criterion]
            for criterion, score in audit_result["audit_scores"].items()
        )
        audit_result["overall_score"] = overall_score
        
        # 决定审批状态
        if overall_score >= self.confidence_threshold:
            audit_result["approval_status"] = "THINKING_APPROVED"
        else:
            audit_result["approval_status"] = "THINKING_NEEDS_IMPROVEMENT"
            audit_result["improvement_suggestions"] = self._generate_improvement_suggestions(audit_result)
        
        return audit_result
    
    def _audit_logical_consistency(self, thinking_content):
        """审查逻辑一致性"""
        # 检查逻辑连接词
        logical_connectors = ["因为", "所以", "因此", "但是", "然而", "而且", "另外", "首先", "其次", "最后"]
        connector_count = sum(thinking_content.count(connector) for connector in logical_connectors)
        
        # 检查矛盾表述
        contradiction_patterns = [
            (r"不可能.*可能", "矛盾：同时表述可能和不可能"),
            (r"必须.*不需要", "矛盾：同时表述必须和不需要"),
            (r"总是.*从不", "矛盾：同时表述总是和从不")
        ]
        
        contradictions = []
        for pattern, description in contradiction_patterns:
            if re.search(pattern, thinking_content):
                contradictions.append(description)
        
        # 计算逻辑一致性分数
        base_score = min(1.0, connector_count / 10)  # 基础分数基于逻辑连接词
        contradiction_penalty = len(contradictions) * 0.2  # 每个矛盾扣0.2分
        
        return max(0.0, base_score - contradiction_penalty)
    
    def _audit_completeness(self, thinking_content, task_context):
        """审查完整性"""
        required_elements = {
            "problem_analysis": ["问题", "分析", "理解"],
            "solution_approach": ["方案", "方法", "策略"],
            "risk_consideration": ["风险", "问题", "注意"],
            "implementation_plan": ["实施", "执行", "步骤"]
        }
        
        completeness_score = 0.0
        for element, keywords in required_elements.items():
            if any(keyword in thinking_content for keyword in keywords):
                completeness_score += 0.25
        
        return completeness_score
    
    def _audit_metacognitive_quality(self, thinking_content):
        """审查元认知质量"""
        metacognitive_indicators = [
            "我需要考虑", "让我想想", "这里需要注意", "我应该检查",
            "可能的问题是", "需要验证", "不确定的是", "需要进一步"
        ]
        
        metacognitive_count = sum(thinking_content.count(indicator) for indicator in metacognitive_indicators)
        return min(1.0, metacognitive_count / 5)  # 最多5个元认知指标得满分
    
    def _audit_reasoning_depth(self, thinking_content):
        """审查推理深度"""
        depth_indicators = {
            "surface": ["简单", "直接", "明显"],
            "intermediate": ["考虑到", "基于", "根据"],
            "deep": ["深层原因", "根本问题", "系统性", "架构级"]
        }
        
        depth_score = 0.0
        for level, indicators in depth_indicators.items():
            if any(indicator in thinking_content for indicator in indicators):
                if level == "surface":
                    depth_score = max(depth_score, 0.3)
                elif level == "intermediate":
                    depth_score = max(depth_score, 0.7)
                elif level == "deep":
                    depth_score = max(depth_score, 1.0)
        
        return depth_score
    
    def _generate_improvement_suggestions(self, audit_result):
        """生成改进建议"""
        suggestions = []
        scores = audit_result["audit_scores"]
        
        if scores["logical_consistency"] < 0.7:
            suggestions.append("增强逻辑连接：使用更多逻辑连接词，检查并消除矛盾表述")
        
        if scores["completeness"] < 0.7:
            suggestions.append("提高完整性：确保包含问题分析、解决方案、风险考虑和实施计划")
        
        if scores["metacognitive_quality"] < 0.7:
            suggestions.append("加强元认知：增加自我质疑和验证环节，明确不确定性")
        
        if scores["reasoning_depth"] < 0.7:
            suggestions.append("深化推理：从表面现象深入到根本原因和系统性思考")
        
        return suggestions
```

### 算法启发提取器
```python
# 【AI自动创建】tools/ace/src/bidirectional_collaboration/inspiration_extraction/algorithmic_insight_extractor.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法启发提取器 - AI thinking启发算法自我优化
引用: 00-共同配置.json 的 validation_standards
"""

import sys
import os
from datetime import datetime
import json

# 确保可以导入common模块
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', '..')
sys.path.insert(0, src_dir)

from common_config_loader import CommonConfigLoader

class AlgorithmicInsightExtractor:
    """算法启发提取器（基于双向协作机制）"""
    
    def __init__(self):
        self.config = CommonConfigLoader()
        self.validation_standards = self.config.get_validation_standards()
        self.extracted_insights = []
        
        # 启发提取模式
        self.extraction_patterns = {
            "novel_reasoning": {
                "keywords": ["创新", "新颖", "独特", "突破"],
                "weight": 0.3
            },
            "constraint_insights": {
                "keywords": ["约束", "限制", "边界", "条件"],
                "weight": 0.25
            },
            "optimization_hints": {
                "keywords": ["优化", "改进", "提升", "效率"],
                "weight": 0.25
            },
            "pattern_recognition": {
                "keywords": ["模式", "规律", "趋势", "特征"],
                "weight": 0.2
            }
        }
    
    def extract_algorithmic_insights(self, thinking_content, execution_context):
        """从AI thinking中提取算法启发"""
        extraction_result = {
            "timestamp": datetime.now().isoformat(),
            "execution_context": execution_context,
            "thinking_source": thinking_content[:200] + "..." if len(thinking_content) > 200 else thinking_content,
            "extracted_insights": {},
            "algorithm_optimization_suggestions": [],
            "evolution_potential": 0.0
        }
        
        # 1. 新颖推理模式发现
        novel_insights = self._extract_novel_reasoning(thinking_content)
        extraction_result["extracted_insights"]["novel_reasoning"] = novel_insights
        
        # 2. 约束条件洞察
        constraint_insights = self._extract_constraint_insights(thinking_content)
        extraction_result["extracted_insights"]["constraint_insights"] = constraint_insights
        
        # 3. 算法优化提示
        optimization_hints = self._extract_optimization_hints(thinking_content)
        extraction_result["extracted_insights"]["optimization_hints"] = optimization_hints
        
        # 4. 模式识别洞察
        pattern_insights = self._extract_pattern_recognition(thinking_content)
        extraction_result["extracted_insights"]["pattern_recognition"] = pattern_insights
        
        # 生成算法自我优化建议
        extraction_result["algorithm_optimization_suggestions"] = self._generate_algorithm_optimization(extraction_result)
        
        # 计算进化潜力
        extraction_result["evolution_potential"] = self._calculate_evolution_potential(extraction_result)
        
        # 记录提取结果
        self.extracted_insights.append(extraction_result)
        
        return extraction_result
    
    def _extract_novel_reasoning(self, thinking_content):
        """提取新颖推理模式"""
        novel_patterns = []
        
        # 检测创新思维模式
        innovation_indicators = [
            "换个角度", "反向思考", "跳出框架", "突破常规",
            "创新方法", "独特视角", "新的可能性"
        ]
        
        for indicator in innovation_indicators:
            if indicator in thinking_content:
                # 提取相关上下文
                start_idx = thinking_content.find(indicator)
                context = thinking_content[max(0, start_idx-50):start_idx+100]
                novel_patterns.append({
                    "pattern": indicator,
                    "context": context,
                    "innovation_score": 0.8
                })
        
        return novel_patterns
    
    def _extract_constraint_insights(self, thinking_content):
        """提取约束条件洞察"""
        constraint_insights = []
        
        # 检测约束识别模式
        constraint_indicators = [
            "限制条件", "约束因素", "边界条件", "前提假设",
            "必须满足", "不能超过", "需要遵循"
        ]
        
        for indicator in constraint_indicators:
            if indicator in thinking_content:
                start_idx = thinking_content.find(indicator)
                context = thinking_content[max(0, start_idx-30):start_idx+80]
                constraint_insights.append({
                    "constraint_type": indicator,
                    "context": context,
                    "importance_score": 0.7
                })
        
        return constraint_insights
    
    def _extract_optimization_hints(self, thinking_content):
        """提取算法优化提示"""
        optimization_hints = []
        
        # 检测优化思维模式
        optimization_indicators = [
            "可以优化", "效率提升", "性能改进", "更好的方法",
            "减少复杂度", "简化流程", "加速处理"
        ]
        
        for indicator in optimization_indicators:
            if indicator in thinking_content:
                start_idx = thinking_content.find(indicator)
                context = thinking_content[max(0, start_idx-40):start_idx+90]
                optimization_hints.append({
                    "optimization_type": indicator,
                    "context": context,
                    "potential_impact": 0.6
                })
        
        return optimization_hints
    
    def _extract_pattern_recognition(self, thinking_content):
        """提取模式识别洞察"""
        pattern_insights = []
        
        # 检测模式识别能力
        pattern_indicators = [
            "发现规律", "识别模式", "重复出现", "相似特征",
            "共同点", "差异化", "分类特征"
        ]
        
        for indicator in pattern_indicators:
            if indicator in thinking_content:
                start_idx = thinking_content.find(indicator)
                context = thinking_content[max(0, start_idx-35):start_idx+85]
                pattern_insights.append({
                    "pattern_type": indicator,
                    "context": context,
                    "recognition_confidence": 0.75
                })
        
        return pattern_insights
    
    def _generate_algorithm_optimization(self, extraction_result):
        """生成算法自我优化建议"""
        suggestions = []
        insights = extraction_result["extracted_insights"]
        
        # 基于新颖推理的优化建议
        if insights["novel_reasoning"]:
            suggestions.append({
                "type": "reasoning_enhancement",
                "suggestion": "集成新颖推理模式到算法决策树中",
                "priority": "high",
                "implementation": "添加创新思维检测模块"
            })
        
        # 基于约束洞察的优化建议
        if insights["constraint_insights"]:
            suggestions.append({
                "type": "constraint_optimization",
                "suggestion": "优化约束条件处理算法",
                "priority": "medium",
                "implementation": "改进约束检测和处理逻辑"
            })
        
        # 基于优化提示的建议
        if insights["optimization_hints"]:
            suggestions.append({
                "type": "performance_optimization",
                "suggestion": "实施性能优化策略",
                "priority": "medium",
                "implementation": "优化算法执行效率"
            })
        
        return suggestions
    
    def _calculate_evolution_potential(self, extraction_result):
        """计算算法进化潜力"""
        insights = extraction_result["extracted_insights"]
        
        # 计算各类洞察的权重分数
        total_score = 0.0
        for insight_type, weight in self.extraction_patterns.items():
            insight_count = len(insights.get(insight_type, []))
            type_score = min(1.0, insight_count / 3) * weight["weight"]
            total_score += type_score
        
        return total_score
    
    def get_evolution_summary(self):
        """获取算法进化摘要"""
        if not self.extracted_insights:
            return {"status": "no_insights", "evolution_potential": 0.0}
        
        avg_evolution_potential = sum(
            insight["evolution_potential"] for insight in self.extracted_insights
        ) / len(self.extracted_insights)
        
        return {
            "total_insights": len(self.extracted_insights),
            "avg_evolution_potential": avg_evolution_potential,
            "recent_insights": self.extracted_insights[-3:],
            "evolution_trend": "improving" if avg_evolution_potential > 0.6 else "stable"
        }
```

## ✅ 双向协作机制完成验证

### 第一步：验证文件创建
```bash
# 【AI自动执行】验证双向协作模块文件创建
python -c "
import os

required_files = [
    'tools/ace/src/bidirectional_collaboration/thinking_audit/thinking_quality_auditor.py',
    'tools/ace/src/bidirectional_collaboration/inspiration_extraction/algorithmic_insight_extractor.py'
]

required_dirs = [
    'tools/ace/src/bidirectional_collaboration/thinking_audit',
    'tools/ace/src/bidirectional_collaboration/inspiration_extraction'
]

# 验证目录
for dir_path in required_dirs:
    if os.path.exists(dir_path):
        print(f'✅ 目录存在: {dir_path}')
    else:
        print(f'❌ 目录缺失: {dir_path}')
        exit(1)

# 验证文件
for file_path in required_files:
    if os.path.exists(file_path):
        print(f'✅ 文件存在: {file_path}')
    else:
        print(f'❌ 文件缺失: {file_path}')
        exit(1)

print('✅ 所有双向协作模块文件验证通过')
"
```

### 第二步：功能验证脚本
```bash
# 【AI自动执行】双向协作机制功能验证
python -c "
import sys
import os
sys.path.insert(0, 'tools/ace/src')

try:
    # 验证基础配置加载
    from common_config_loader import CommonConfigLoader
    config = CommonConfigLoader()
    print('✅ 基础配置加载成功')

    # 验证thinking审查器
    from bidirectional_collaboration.thinking_audit.thinking_quality_auditor import ThinkingQualityAuditor
    auditor = ThinkingQualityAuditor()
    test_thinking = '我需要考虑这个问题的复杂性。首先分析需求，然后设计方案，最后考虑风险。'
    audit_result = auditor.audit_thinking_process(test_thinking, {'task': 'test'})
    print(f'✅ Thinking审查器测试成功: 总分 {audit_result.get(\"overall_score\", 0):.2f}')

    # 验证启发提取器
    from bidirectional_collaboration.inspiration_extraction.algorithmic_insight_extractor import AlgorithmicInsightExtractor
    extractor = AlgorithmicInsightExtractor()
    test_thinking2 = '这里可以优化算法效率，发现了新的模式，需要考虑约束条件。'
    extraction_result = extractor.extract_algorithmic_insights(test_thinking2, {'context': 'test'})
    print(f'✅ 启发提取器测试成功: 进化潜力 {extraction_result.get(\"evolution_potential\", 0):.2f}')

    print('✅ 双向协作机制验证完成')

except ImportError as e:
    print(f'❌ 模块导入失败: {str(e)}')
    import traceback
    traceback.print_exc()
    exit(1)
except Exception as e:
    print(f'❌ 双向协作机制验证失败: {str(e)}')
    import traceback
    traceback.print_exc()
    exit(1)
"
```

## 📊 阶段完成标准

### 成功标准
- ✅ Thinking质量审查器实现完成
- ✅ 算法启发提取器实现完成
- ✅ 双向协作反馈循环机制建立
- ✅ 质量审查和启发提取功能验证通过

### 输出文件清单
- `tools/ace/src/bidirectional_collaboration/thinking_audit/thinking_quality_auditor.py`
- `tools/ace/src/bidirectional_collaboration/inspiration_extraction/algorithmic_insight_extractor.py`

### 下一步依赖
- 04-多API并发控制.md 可以开始执行
- 06-Web界面功能实现.md 依赖此模块

**预期执行时间**: 90分钟  
**AI负载等级**: 中等  
**置信度**: 90%+  
**人类参与**: 无需人类参与（AI自主执行）
