# V4 MCP Server第一阶段进度跟踪和断线重连设计

## 📋 文档概述

**文档ID**: V4-MCP-PHASE1-PROGRESS-TRACKING-004
**创建日期**: 2025-06-18
**版本**: F007-mcp-phase1-v1.0.L1.3.0
**目标**: 设计可靠的进度跟踪机制，支持MCP断线重连，为第二阶段复用
**模板引用**: @TEMPLATE_REF:../核心/V4架构信息AI填充模板.md#状态管理设计

## 🔄 进度跟踪机制设计

### 1. 进度数据结构
```json
// checkresult-v4/mcp_progress_tracker.json
// 基于V4状态管理模式设计的进度跟踪数据结构
{
  "task_metadata": {
    "task_id": "checkresult_v4_modification_20250618_143022",
    "checkresult_path": "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/checkresult-v4",
    "created_at": "2025-06-18T14:30:22.123Z",
    "last_updated": "2025-06-18T14:35:45.678Z",
    "task_type": "design_document_modification",
    "v4_version": "F007-mcp-phase1-v1.0.L1.3.0"
  },
  
  "modification_queue": {
    "total_modifications": 53,
    "completed_count": 15,
    "failed_count": 2,
    "pending_count": 36,
    "current_batch": 6,
    "batch_size": 3
  },
  
  "modifications": {
    "01-architecture-overview_141_001": {
      "id": "01-architecture-overview_141_001",
      "priority": "high",
      "file_path": "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/01-architecture-overview.md",
      "line_number": 141,
      "action": "replace",
      "find_text": "->",
      "replace_text": "→",
      "description": "替换箭头符号为Unicode箭头",
      "confidence": 0.95,
      "context": "UserService --> ServiceBridge",
      "status": "completed",
      "processing_started_at": "2025-06-18T14:31:00.000Z",
      "completed_at": "2025-06-18T14:31:15.000Z",
      "validation_result": {
        "success": true,
        "validation_time": "2025-06-18T14:31:16.000Z"
      }
    }
    // ... 其他52个修改项
  },
  
  "session_history": [
    {
      "session_id": "session_20250618_143022",
      "started_at": "2025-06-18T14:30:22.123Z",
      "ended_at": "2025-06-18T14:33:45.678Z",
      "status": "completed",
      "modifications_processed": 9,
      "connection_type": "initial"
    },
    {
      "session_id": "session_20250618_143500",
      "started_at": "2025-06-18T14:35:00.000Z",
      "ended_at": null,
      "status": "active",
      "modifications_processed": 6,
      "connection_type": "reconnection"
    }
  ],
  
  "completion_history": [
    {
      "batch_number": 1,
      "timestamp": "2025-06-18T14:31:30.000Z",
      "modifications": ["01-architecture-overview_141_001", "01-architecture-overview_142_002", "01-architecture-overview_143_003"],
      "success_count": 3,
      "failure_count": 0,
      "session_id": "session_20250618_143022"
    }
    // ... 其他批次记录
  ],
  
  "error_log": [
    {
      "timestamp": "2025-06-18T14:32:15.000Z",
      "modification_id": "01-architecture-overview_150_010",
      "error_type": "validation_failed",
      "error_message": "目标文本未找到",
      "retry_count": 1,
      "session_id": "session_20250618_143022"
    }
  ]
}
```

### 2. 进度跟踪器实现
```python
# tools/ace/mcp/v4_context_guidance_server/core/progress_tracker.py
# 基于V4状态管理机制，支持断线重连

class ProgressTracker:
    """
    进度跟踪器
    支持MCP断线重连，持久化进度状态
    为第二阶段自动化循环提供基础
    """
    
    def __init__(self):
        self.progress_file_path = None
        self.progress_data = None
        self.session_manager = SessionManager()
        self.backup_manager = BackupManager()
        
    def initialize_or_load(self, progress_file_path: str, modification_queue: List[Dict]) -> None:
        """
        初始化或加载进度文件
        支持断线重连场景
        """
        
        self.progress_file_path = progress_file_path
        
        if os.path.exists(progress_file_path):
            # 断线重连场景：加载现有进度
            self._load_existing_progress()
            self._validate_progress_integrity()
            self._sync_with_new_queue(modification_queue)
            
            # 记录重连事件
            self._log_reconnection_event()
        else:
            # 新任务场景：创建进度文件
            self._create_new_progress(modification_queue)
            
        # 创建备份
        self.backup_manager.create_backup(self.progress_file_path)
    
    def _load_existing_progress(self) -> None:
        """加载现有进度数据"""
        
        try:
            with open(self.progress_file_path, 'r', encoding='utf-8') as f:
                self.progress_data = json.load(f)
                
            # 验证数据格式版本兼容性
            self._validate_data_format_compatibility()
            
        except (json.JSONDecodeError, FileNotFoundError) as e:
            # 尝试从备份恢复
            backup_restored = self.backup_manager.restore_from_backup(self.progress_file_path)
            
            if backup_restored:
                self._load_existing_progress()  # 递归重试
            else:
                raise ProgressTrackingError(f"无法加载进度文件: {e}")
    
    def _validate_progress_integrity(self) -> None:
        """验证进度数据完整性"""
        
        required_fields = [
            "task_metadata", "modification_queue", "modifications", 
            "session_history", "completion_history"
        ]
        
        for field in required_fields:
            if field not in self.progress_data:
                raise ProgressTrackingError(f"进度数据缺少必要字段: {field}")
        
        # 验证修改计数一致性
        total_in_queue = self.progress_data["modification_queue"]["total_modifications"]
        total_in_modifications = len(self.progress_data["modifications"])
        
        if total_in_queue != total_in_modifications:
            self._repair_count_inconsistency()
    
    def get_next_batch(self, batch_size: int = 3) -> List[Dict]:
        """
        获取下一批待处理的修改
        支持断线重连后继续处理
        """
        
        # 查找所有待处理的修改
        pending_modifications = [
            mod for mod in self.progress_data["modifications"].values()
            if mod["status"] == "pending"
        ]
        
        if not pending_modifications:
            return []
        
        # 按优先级和行号排序
        pending_modifications.sort(key=lambda x: (
            0 if x["priority"] == "high" else 1,
            x["file_path"],
            x.get("line_number", 0)
        ))
        
        # 取前batch_size个修改
        next_batch = pending_modifications[:batch_size]
        
        # 更新状态为processing
        current_time = datetime.now().isoformat()
        for mod in next_batch:
            mod_id = mod["id"]
            self.progress_data["modifications"][mod_id]["status"] = "processing"
            self.progress_data["modifications"][mod_id]["processing_started_at"] = current_time
        
        # 更新批次计数
        self.progress_data["modification_queue"]["current_batch"] += 1
        self.progress_data["task_metadata"]["last_updated"] = current_time
        
        # 保存进度
        self._save_progress_with_backup()
        
        return next_batch
    
    def mark_batch_completed(self, batch_modifications: List[Dict], results: List[Dict]) -> None:
        """
        标记批次完成
        记录详细的修改历史和验证结果
        """
        
        current_time = datetime.now().isoformat()
        successful_modifications = []
        failed_modifications = []
        
        for i, mod in enumerate(batch_modifications):
            mod_id = mod["id"]
            result = results[i] if i < len(results) else {"success": False, "error": "No result provided"}
            
            if result.get("success", False):
                # 成功的修改
                self.progress_data["modifications"][mod_id]["status"] = "completed"
                self.progress_data["modifications"][mod_id]["completed_at"] = current_time
                self.progress_data["modifications"][mod_id]["validation_result"] = result
                
                successful_modifications.append(mod_id)
                self.progress_data["modification_queue"]["completed_count"] += 1
                
            else:
                # 失败的修改
                self.progress_data["modifications"][mod_id]["status"] = "failed"
                self.progress_data["modifications"][mod_id]["failed_at"] = current_time
                self.progress_data["modifications"][mod_id]["error"] = result.get("error", "Unknown error")
                
                failed_modifications.append(mod_id)
                self.progress_data["modification_queue"]["failed_count"] += 1
                
                # 记录错误日志
                self._log_error(mod_id, result.get("error", "Unknown error"))
        
        # 更新待处理计数
        self.progress_data["modification_queue"]["pending_count"] = (
            self.progress_data["modification_queue"]["total_modifications"] - 
            self.progress_data["modification_queue"]["completed_count"] - 
            self.progress_data["modification_queue"]["failed_count"]
        )
        
        # 记录批次完成历史
        self._record_batch_completion(successful_modifications, failed_modifications)
        
        # 更新元数据
        self.progress_data["task_metadata"]["last_updated"] = current_time
        
        # 保存进度
        self._save_progress_with_backup()
    
    def start_new_session(self) -> str:
        """
        开始新的会话
        支持多次断线重连
        """
        
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        current_time = datetime.now().isoformat()
        
        # 结束当前活跃会话
        self._end_active_sessions()
        
        # 创建新会话
        new_session = {
            "session_id": session_id,
            "started_at": current_time,
            "ended_at": None,
            "status": "active",
            "modifications_processed": 0,
            "connection_type": "reconnection" if self.progress_data["session_history"] else "initial"
        }
        
        self.progress_data["session_history"].append(new_session)
        self.progress_data["task_metadata"]["last_updated"] = current_time
        
        # 保存进度
        self._save_progress_with_backup()
        
        return session_id
```

## 🔌 断线重连机制

### 断线检测和恢复
```python
class ReconnectionManager:
    """
    断线重连管理器
    处理MCP连接中断和恢复场景
    """
    
    def __init__(self, progress_tracker: ProgressTracker):
        self.progress_tracker = progress_tracker
        self.connection_monitor = ConnectionMonitor()
        
    def detect_disconnection(self) -> bool:
        """检测MCP连接中断"""
        
        # 检查最后更新时间
        last_update = self.progress_tracker.get_last_update_time()
        time_since_update = datetime.now() - last_update
        
        # 如果超过5分钟没有更新，认为可能断线
        if time_since_update.total_seconds() > 300:
            return True
            
        return False
    
    def handle_reconnection(self, checkresult_path: str) -> Dict:
        """处理重连场景"""
        
        progress_file = os.path.join(checkresult_path, "mcp_progress_tracker.json")
        
        if not os.path.exists(progress_file):
            return {
                "status": "no_previous_task",
                "message": "没有发现之前的任务，请重新初始化"
            }
        
        # 加载进度
        self.progress_tracker.initialize_or_load(progress_file, [])
        
        # 检查是否有处理中的修改需要重置
        processing_modifications = self._find_processing_modifications()
        
        if processing_modifications:
            # 重置处理中的修改为待处理状态
            self._reset_processing_modifications(processing_modifications)
        
        # 开始新会话
        session_id = self.progress_tracker.start_new_session()
        
        return {
            "status": "reconnection_successful",
            "message": f"成功重连，恢复任务进度",
            "session_id": session_id,
            "progress_summary": self.progress_tracker.get_progress_summary(),
            "reset_modifications": len(processing_modifications)
        }
```

### 数据一致性保证
```python
class DataConsistencyManager:
    """
    数据一致性管理器
    确保进度数据的完整性和一致性
    """
    
    def __init__(self):
        self.integrity_checker = IntegrityChecker()
        self.repair_engine = RepairEngine()
        
    def validate_and_repair(self, progress_data: Dict) -> Dict:
        """验证并修复数据一致性"""
        
        validation_result = {
            "is_valid": True,
            "issues_found": [],
            "repairs_made": []
        }
        
        # 检查计数一致性
        count_issues = self._check_count_consistency(progress_data)
        if count_issues:
            validation_result["issues_found"].extend(count_issues)
            repairs = self._repair_count_issues(progress_data, count_issues)
            validation_result["repairs_made"].extend(repairs)
        
        # 检查状态一致性
        status_issues = self._check_status_consistency(progress_data)
        if status_issues:
            validation_result["issues_found"].extend(status_issues)
            repairs = self._repair_status_issues(progress_data, status_issues)
            validation_result["repairs_made"].extend(repairs)
        
        # 检查时间戳一致性
        timestamp_issues = self._check_timestamp_consistency(progress_data)
        if timestamp_issues:
            validation_result["issues_found"].extend(timestamp_issues)
            repairs = self._repair_timestamp_issues(progress_data, timestamp_issues)
            validation_result["repairs_made"].extend(repairs)
        
        validation_result["is_valid"] = len(validation_result["issues_found"]) == 0
        
        return validation_result
```

## 📊 进度监控和报告

### 进度摘要生成
```python
def generate_progress_summary(progress_data: Dict) -> Dict:
    """
    生成详细的进度摘要
    为IDE AI和第二阶段提供状态信息
    """
    
    total = progress_data["modification_queue"]["total_modifications"]
    completed = progress_data["modification_queue"]["completed_count"]
    failed = progress_data["modification_queue"]["failed_count"]
    pending = progress_data["modification_queue"]["pending_count"]
    
    # 计算进度百分比
    progress_percentage = (completed / total * 100) if total > 0 else 0
    success_rate = (completed / (completed + failed) * 100) if (completed + failed) > 0 else 0
    
    # 估算剩余时间
    estimated_remaining_time = _estimate_remaining_time(progress_data)
    
    return {
        "task_id": progress_data["task_metadata"]["task_id"],
        "overall_progress": {
            "total_modifications": total,
            "completed_count": completed,
            "failed_count": failed,
            "pending_count": pending,
            "progress_percentage": round(progress_percentage, 1),
            "success_rate": round(success_rate, 1)
        },
        "current_status": {
            "current_batch": progress_data["modification_queue"]["current_batch"],
            "batch_size": progress_data["modification_queue"]["batch_size"],
            "last_updated": progress_data["task_metadata"]["last_updated"]
        },
        "session_info": {
            "total_sessions": len(progress_data["session_history"]),
            "current_session": _get_current_session(progress_data),
            "reconnection_count": _count_reconnections(progress_data)
        },
        "time_estimates": {
            "estimated_remaining_time": estimated_remaining_time,
            "estimated_completion_time": _estimate_completion_time(progress_data, estimated_remaining_time)
        },
        "quality_metrics": {
            "success_rate": round(success_rate, 1),
            "average_batch_time": _calculate_average_batch_time(progress_data),
            "error_rate": round((failed / total * 100), 1) if total > 0 else 0
        }
    }
```

---

**创建时间**: 2025-06-18
**维护说明**: 基于V4状态管理机制设计，确保进度跟踪的可靠性和第二阶段的复用性
