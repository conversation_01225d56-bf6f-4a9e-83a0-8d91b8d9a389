# AI JSON协议规范 - Zen设计版

## 📋 文档概述

**文档名称**: AI JSON协议规范 - Zen设计版
**版本**: V1.0-Zen-Minimal
**创建日期**: 2025-01-14
**设计目标**: 最小化AI负担，避免冗余，确保可靠性和无歧义
**设计哲学**: "Simple is better than complex. Explicit is better than implicit."

## 🎯 Zen设计原则

### 核心设计哲学
1. **最小负担**：AI只需要处理必要的信息，减少认知负荷
2. **无歧义性**：每个字段都有唯一明确的含义，不存在模糊解释
3. **零冗余**：没有重复信息，每个字段都有明确用途
4. **可靠性**：简单结构降低出错概率，提高系统稳定性
5. **渐进式**：从最简单的核心开始，按需扩展

### 设计约束
- **字段数量**：核心协议≤10个字段
- **嵌套深度**：≤3层嵌套
- **字段命名**：简洁明确，避免技术术语
- **可选字段**：最小化可选字段，大部分字段必需

## 🔧 核心协议结构

### 1. 协议版本和元数据
```json
{
  "protocol_version": "1.0",
  "protocol_type": "ai_validation_driven_execution",
  "timestamp": "2025-01-14T10:30:00Z",
  "request_id": "req_12345678",
  "correlation_id": "corr_abcdef",
  "client_info": {
    "client_type": "validation_driven_executor",
    "client_version": "1.0.0",
    "environment": "production"
  }
}
```

### 2. 任务定义协议
```json
{
  "task": {
    "task_id": "task_001_code_generation",
    "task_type": "CODE_GENERATION",
    "task_category": "DEVELOPMENT",
    "priority": "HIGH",
    "timeout": 300,
    "retry_policy": {
      "max_retries": 3,
      "retry_delay": 5,
      "backoff_strategy": "exponential"
    }
  }
}
```

### 3. 执行器配置协议
```json
{
  "executor_config": {
    "executor_role": "code_generator",
    "execution_mode": "validation_driven",
    "confidence_requirement": 0.85,
    "model_selection": {
      "primary": {
        "model": "qwen3_235b",
        "confidence_threshold": 0.9,
        "max_retries": 2
      },
      "secondary": {
        "model": "deepseek_v3", 
        "confidence_threshold": 0.8,
        "max_retries": 1
      },
      "fallback": {
        "model": "gemini_2_5_pro",
        "confidence_threshold": 0.7,
        "max_retries": 1
      }
    },
    "validation_ai": {
      "enabled": true,
      "model": "gemini_2_5_pro",
      "confidence_threshold": 0.85,
      "timeout": 30
    }
  }
}
```

### 4. 操作定义协议
```json
{
  "operation": {
    "operation_type": "EXECUTE_WITH_VALIDATION",
    "operation_subtype": "CODE_GENERATION",
    "operation_params": {
      "task_description": "生成PluginManager类",
      "content": "实现支持热插拔的插件管理器",
      "expected_output_format": "java_class",
      "quality_requirements": {
        "compilation_required": true,
        "test_coverage_required": true,
        "documentation_required": true
      }
    }
  }
}
```

### 5. 上下文和约束协议
```json
{
  "context": {
    "task_context": {
      "project": "nexus-universal-socket",
      "module": "plugin-manager", 
      "language": "java",
      "framework": "spring-boot",
      "complexity": "high"
    },
    "technical_context": {
      "java_version": "21",
      "spring_boot_version": "3.4.5",
      "build_tool": "maven",
      "target_environment": "production"
    },
    "business_context": {
      "domain": "plugin_ecosystem",
      "use_case": "hot_swap_plugin_management",
      "performance_requirements": {
        "startup_time": "< 30s",
        "memory_usage": "< 2GB",
        "response_time": "< 100ms"
      }
    }
  },
  "constraints": {
    "technical_constraints": {
      "max_lines": 150,
      "max_complexity": 10,
      "compilation_required": true,
      "test_coverage_min": 80
    },
    "business_constraints": {
      "security_level": "enterprise",
      "compliance_requirements": ["SOX", "GDPR"],
      "audit_required": true
    },
    "quality_constraints": {
      "code_style": "google_java_style",
      "documentation_format": "javadoc",
      "naming_convention": "camelCase"
    }
  }
}
```

### 6. 验证和护栏协议
```json
{
  "validation": {
    "validation_protocols": [
      {
        "protocol_name": "architectural_thinking",
        "protocol_type": "python_algorithm",
        "enabled": true,
        "weight": 0.3,
        "config": {
          "analysis_depth": "comprehensive",
          "risk_assessment": true
        }
      },
      {
        "protocol_name": "expert_review",
        "protocol_type": "ai_validation",
        "enabled": true,
        "weight": 0.4,
        "config": {
          "expert_types": ["architecture", "security", "performance"],
          "consensus_required": true
        }
      }
    ],
    "confidence_calculation": {
      "method": "weighted_average",
      "python_weight": 0.6,
      "ai_weight": 0.4,
      "minimum_threshold": 0.7
    }
  },
  "guardrails": {
    "pre_execution": [
      {
        "guardrail_name": "security_check",
        "guardrail_type": "content_analysis",
        "severity": "critical",
        "enabled": true,
        "config": {
          "malicious_content_detection": true,
          "sensitive_data_detection": true
        }
      },
      {
        "guardrail_name": "permission_check",
        "guardrail_type": "access_control",
        "severity": "high",
        "enabled": true,
        "config": {
          "required_permissions": ["code_write", "file_modify"],
          "role_based_access": true
        }
      }
    ],
    "post_execution": [
      {
        "guardrail_name": "quality_verification",
        "guardrail_type": "output_validation",
        "severity": "medium",
        "enabled": true,
        "config": {
          "compilation_check": true,
          "syntax_validation": true,
          "style_check": true
        }
      }
    ]
  }
}
```

## 🔄 输入输出协议

### AI调用输入协议
```json
{
  "ai_input": {
    "model_info": {
      "model_name": "qwen3_235b",
      "model_version": "latest",
      "interface_type": "openai",
      "api_endpoint": "https://api.example.com/v1/chat/completions"
    },
    "prompt_config": {
      "system_prompt": "你是一个专业的Java开发专家...",
      "user_prompt": "请生成PluginManager类...",
      "prompt_template": "code_generation_v1",
      "prompt_variables": {
        "language": "java",
        "framework": "spring-boot",
        "complexity": "high"
      }
    },
    "generation_config": {
      "temperature": 0.7,
      "max_tokens": 4096,
      "top_p": 0.9,
      "frequency_penalty": 0.0,
      "presence_penalty": 0.0,
      "stop_sequences": ["```", "END_OF_CODE"]
    },
    "response_format": {
      "type": "json_object",
      "schema": {
        "type": "object",
        "properties": {
          "generated_code": {"type": "string"},
          "explanation": {"type": "string"},
          "confidence": {"type": "number"}
        },
        "required": ["generated_code", "confidence"]
      }
    }
  }
}
```

### AI调用输出协议
```json
{
  "ai_output": {
    "execution_info": {
      "execution_id": "exec_12345",
      "model_used": "qwen3_235b",
      "execution_time": 2.5,
      "token_usage": {
        "prompt_tokens": 1024,
        "completion_tokens": 2048,
        "total_tokens": 3072
      },
      "api_response_code": 200,
      "api_response_time": 2.3
    },
    "generated_content": {
      "primary_output": "public class PluginManager {...}",
      "metadata": {
        "content_type": "java_class",
        "estimated_lines": 145,
        "estimated_complexity": 8,
        "compilation_status": "success"
      },
      "additional_outputs": {
        "explanation": "这个PluginManager类实现了...",
        "suggestions": ["添加单元测试", "考虑异常处理"],
        "warnings": ["内存使用可能较高"]
      }
    },
    "quality_assessment": {
      "ai_confidence": 0.87,
      "quality_score": 0.85,
      "risk_indicators": [
        {
          "risk_type": "performance",
          "risk_level": "medium",
          "description": "可能存在性能瓶颈"
        }
      ],
      "improvement_suggestions": [
        "优化插件加载算法",
        "添加缓存机制"
      ]
    }
  }
}
```

## 📊 验证结果协议

### 验证执行协议
```json
{
  "validation_execution": {
    "validation_id": "val_12345",
    "validation_timestamp": "2025-01-14T10:35:00Z",
    "validation_duration": 1.2,
    "python_validations": [
      {
        "validator_name": "architectural_thinking",
        "validator_version": "2.0",
        "execution_time": 0.5,
        "result": {
          "confidence": 0.88,
          "quality_score": 0.85,
          "issues": [],
          "risk_points": [
            {
              "risk_type": "complexity",
              "severity": "medium",
              "description": "方法复杂度较高"
            }
          ]
        }
      }
    ],
    "ai_validations": [
      {
        "validator_name": "expert_review",
        "validation_ai_model": "gemini_2_5_pro",
        "execution_time": 0.7,
        "result": {
          "confidence": 0.86,
          "expert_opinions": [
            {
              "expert_type": "architecture",
              "rating": 0.9,
              "comments": "架构设计合理"
            },
            {
              "expert_type": "security", 
              "rating": 0.8,
              "comments": "需要加强输入验证"
            }
          ],
          "consensus_score": 0.85,
          "recommendations": ["添加输入验证", "优化错误处理"]
        }
      }
    ]
  }
}
```

### 综合验证结果协议
```json
{
  "validation_result": {
    "overall_confidence": 0.87,
    "validation_status": "PASSED",
    "confidence_breakdown": {
      "python_confidence": 0.88,
      "ai_confidence": 0.86,
      "combined_confidence": 0.87,
      "calculation_method": "weighted_average",
      "weights": {"python": 0.6, "ai": 0.4}
    },
    "quality_metrics": {
      "code_quality": 0.85,
      "architecture_quality": 0.90,
      "security_quality": 0.80,
      "performance_quality": 0.82,
      "maintainability": 0.88
    },
    "issues_summary": {
      "critical_issues": 0,
      "high_issues": 1,
      "medium_issues": 3,
      "low_issues": 5,
      "total_issues": 9
    },
    "recommendations": [
      {
        "priority": "high",
        "category": "security",
        "description": "添加输入验证机制",
        "estimated_effort": "2 hours"
      },
      {
        "priority": "medium", 
        "category": "performance",
        "description": "优化插件加载性能",
        "estimated_effort": "4 hours"
      }
    ]
  }
}
```

## 🚀 扩展机制设计

### 1. 插件式验证器扩展
```json
{
  "validator_extension": {
    "validator_name": "custom_security_validator",
    "validator_type": "python_algorithm",
    "validator_version": "1.0",
    "validator_config": {
      "entry_point": "custom_validators.security_validator",
      "dependencies": ["security-scanner>=1.0"],
      "config_schema": {
        "type": "object",
        "properties": {
          "scan_depth": {"type": "string", "enum": ["basic", "deep"]},
          "rule_sets": {"type": "array", "items": {"type": "string"}}
        }
      }
    }
  }
}
```

### 2. AI模型适配器扩展
```json
{
  "model_adapter_extension": {
    "adapter_name": "claude_adapter",
    "supported_models": ["claude-3-sonnet", "claude-3-opus"],
    "interface_mapping": {
      "request_format": "anthropic_messages",
      "response_format": "anthropic_response",
      "parameter_mapping": {
        "temperature": "temperature",
        "max_tokens": "max_tokens",
        "top_p": "top_p"
      }
    },
    "adapter_config": {
      "api_version": "2023-06-01",
      "default_headers": {
        "anthropic-version": "2023-06-01"
      }
    }
  }
}
```

### 3. 操作类型扩展
```json
{
  "operation_extension": {
    "operation_type": "CUSTOM_ANALYSIS",
    "operation_category": "ANALYSIS",
    "operation_config": {
      "input_schema": {
        "type": "object",
        "properties": {
          "analysis_target": {"type": "string"},
          "analysis_depth": {"type": "string", "enum": ["shallow", "deep"]}
        }
      },
      "output_schema": {
        "type": "object", 
        "properties": {
          "analysis_result": {"type": "string"},
          "confidence": {"type": "number"}
        }
      },
      "validation_requirements": ["custom_analysis_validator"],
      "guardrail_requirements": ["analysis_safety_check"]
    }
  }
}
```

## 📋 协议版本管理

### 版本兼容性矩阵
```json
{
  "version_compatibility": {
    "current_version": "1.0",
    "supported_versions": ["1.0", "0.9", "0.8"],
    "deprecated_versions": ["0.7", "0.6"],
    "migration_guides": {
      "0.9_to_1.0": "docs/migration/v0.9_to_v1.0.md",
      "0.8_to_1.0": "docs/migration/v0.8_to_v1.0.md"
    },
    "breaking_changes": {
      "1.0": [
        "validation_protocols结构调整",
        "ai_output格式标准化"
      ]
    }
  }
}
```

### 协议升级策略
```json
{
  "upgrade_strategy": {
    "backward_compatibility": {
      "support_duration": "12 months",
      "deprecation_notice": "6 months",
      "migration_assistance": true
    },
    "feature_flags": {
      "new_validation_engine": {
        "enabled": false,
        "rollout_percentage": 10,
        "target_version": "1.1"
      }
    },
    "rollback_plan": {
      "automatic_rollback": true,
      "rollback_triggers": ["error_rate > 5%", "latency > 10s"],
      "rollback_version": "0.9"
    }
  }
}
```

这个协议设计提供了：

1. **完整的结构化定义**：从输入到输出的全链路协议
2. **强大的扩展能力**：支持新验证器、新AI模型、新操作类型
3. **明确的类型约束**：每个字段都有明确的类型和验证规则
4. **版本管理机制**：支持协议演进和向后兼容
5. **可观测性支持**：丰富的元数据用于监控和调试

接下来我将创建具体的JSON Schema定义和示例代码。
