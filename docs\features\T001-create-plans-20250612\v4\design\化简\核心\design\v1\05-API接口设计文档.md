# 项目经理API接口设计文档 - 架构风险检测与代码生成

## 📋 文档信息
- **文档版本**: V1.0
- **创建日期**: 2025-01-16
- **设计目标**: 设计RESTful API和WebSocket接口，支持九宫格界面的项目经理架构风险检测和代码生成功能
- **基础框架**: 复用ace现有的Flask应用和API管理器

## 🌐 API架构概览

### **接口分层设计**

```mermaid
graph TB
    subgraph "客户端层"
        A1[九宫格Web界面]
        A2[移动端应用]
        A3[第三方集成]
    end

    subgraph "API网关层"
        B1[Flask Blueprint Router]
        B2[认证中间件]
        B3[限流中间件]
        B4[日志中间件]
    end

    subgraph "业务API层"
        C1[ProjectManagerRiskController]
        C2[CodeGenerationController]
        C3[ProjectManagementAPI]
        C4[ConfigurationAPI]
        C5[DocumentContradictionController]
    end

    subgraph "WebSocket层"
        D1[ProjectManagerRiskWebSocket]
        D2[CodeGenerationWebSocket]
        D3[StatusSyncWebSocket]
    end

    subgraph "集成适配层"
        E1[APIManagerAdapter]
        E2[TaskExecutorAdapter]
        E3[WebInterfaceAdapter]
    end

    subgraph "ACE现有服务层"
        F1[GlobalAPIConnectionPool]
        F2[ValidationDrivenExecutor]
        F3[TaskBasedAIServiceManager]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    B1 --> C1
    B1 --> C2
    B1 --> C3
    B1 --> C4
    B1 --> C5
    A1 --> D1
    C1 --> E1
    C2 --> E2
    C3 --> E3
    C5 --> E1
    E1 --> F1
    E2 --> F2
    E1 --> F3
```

## 🔧 RESTful API设计

### **1. 项目经理风险检测API**

#### **POST /api/project-manager/validate-directory**
验证项目文档目录有效性（通过WebInterfaceAdapter调用ace现有ValidationController）

**请求体**:
```json
{
    "directory": "/path/to/design/docs",
    "validation_options": {
        "check_permissions": true,
        "scan_file_types": [".md", ".json", ".yaml"],
        "max_depth": 5
    }
}
```

**响应体**:
```json
{
    "success": true,
    "data": {
        "valid": true,
        "directory_info": {
            "path": "/path/to/design/docs",
            "file_count": 47,
            "supported_files": 42,
            "total_size": "2.3MB",
            "last_modified": "2025-01-16T10:30:00Z"
        },
        "validation_details": {
            "permissions": "read_write",
            "structure_valid": true,
            "missing_files": [],
            "warnings": []
        }
    },
    "message": "目录验证成功",
    "timestamp": "2025-01-16T10:30:00Z"
}
```

#### **POST /api/project-manager/detect**
启动项目经理架构风险检测（通过TaskExecutorAdapter调用ProjectManagerRiskDetector）

**请求体**:
```json
{
    "directory": "/path/to/design/docs",
    "detection_options": {
        "enable_advanced_detection": true,
        "confidence_threshold": 0.8,
        "risk_levels": ["CRITICAL", "HIGH", "MEDIUM", "LOW"],
        "detection_types": [
            "circular_dependency",
            "security_violation", 
            "performance_bottleneck",
            "principle_violation"
        ],
        "output_format": "detailed"
    },
    "callback_url": "ws://localhost:5000/ws/risk-detection/session-123"
}
```

**响应体**:
```json
{
    "success": true,
    "data": {
        "detection_id": "det_20250116_103000_abc123",
        "status": "started",
        "estimated_duration": "2-5 minutes",
        "websocket_url": "ws://localhost:5000/ws/risk-detection/det_20250116_103000_abc123",
        "progress_endpoint": "/api/architecture-risk/detection/det_20250116_103000_abc123/progress"
    },
    "message": "风险检测已启动",
    "timestamp": "2025-01-16T10:30:00Z"
}
```

#### **GET /api/project-manager/detection/{detection_id}/progress**
获取项目检测进度

**响应体**:
```json
{
    "success": true,
    "data": {
        "detection_id": "det_20250116_103000_abc123",
        "status": "running",
        "progress": {
            "current_stage": "detection",
            "percentage": 65,
            "stage_details": {
                "parsing": {"status": "completed", "duration": "15s"},
                "detection": {"status": "running", "progress": 65},
                "analysis": {"status": "pending"},
                "reporting": {"status": "pending"}
            }
        },
        "partial_results": {
            "risks_found": 8,
            "critical_risks": 2,
            "high_risks": 3,
            "medium_risks": 2,
            "low_risks": 1
        },
        "estimated_completion": "2025-01-16T10:33:00Z"
    }
}
```

#### **GET /api/project-manager/detection/{detection_id}/report**
获取项目检测报告

**响应体**:
```json
{
    "success": true,
    "data": {
        "detection_id": "det_20250116_103000_abc123",
        "status": "completed",
        "report": {
            "summary": {
                "total_risks": 8,
                "risks_by_level": {
                    "CRITICAL": 2,
                    "HIGH": 3,
                    "MEDIUM": 2,
                    "LOW": 1
                },
                "overall_confidence": 0.92,
                "execution_time": 127.5
            },
            "risks": [
                {
                    "id": "circular_dep_0",
                    "type": "circular_dependency",
                    "level": "CRITICAL",
                    "title": "循环依赖: UserService ↔ OrderService ↔ PaymentService",
                    "description": "检测到3个组件之间的循环依赖关系",
                    "components": ["UserService", "OrderService", "PaymentService"],
                    "impact_analysis": "循环依赖可能导致系统启动失败、内存泄漏和维护困难",
                    "solution_strategy": "重构依赖关系，引入依赖注入或中介者模式",
                    "confidence": 0.98,
                    "detection_method": "networkx_strongly_connected_components",
                    "evidence": {
                        "component_count": 3,
                        "dependency_chain": ["UserService -> OrderService", "OrderService -> PaymentService", "PaymentService -> UserService"]
                    }
                }
            ],
            "recommendations": [
                "发现2个循环依赖，建议重构依赖关系",
                "发现3个安全边界违反，建议加强访问控制",
                "建议引入依赖注入框架解决循环依赖问题"
            ],
            "detection_summary": {
                "total_components_analyzed": 15,
                "detection_methods_used": ["networkx_strongly_connected_components", "security_rule_engine", "contradiction_detector"],
                "average_confidence": 0.89
            }
        }
    },
    "message": "检测报告生成完成",
    "timestamp": "2025-01-16T10:32:07Z"
}
```

### **2. 代码生成API**

#### **POST /api/architecture-risk/generate-code**
启动代码生成

**请求体**:
```json
{
    "detection_report_id": "det_20250116_103000_abc123",
    "generation_options": {
        "target_language": "java",
        "framework": "spring_boot",
        "output_directory": "/path/to/output",
        "generation_scope": {
            "fix_circular_dependencies": true,
            "implement_security_boundaries": true,
            "generate_missing_components": true,
            "create_test_cases": true
        },
        "code_style": {
            "naming_convention": "camelCase",
            "indentation": "4_spaces",
            "line_length": 120
        },
        "quality_requirements": {
            "min_test_coverage": 80,
            "enable_static_analysis": true,
            "enforce_coding_standards": true
        }
    }
}
```

**响应体**:
```json
{
    "success": true,
    "data": {
        "generation_id": "gen_20250116_103500_def456",
        "status": "started",
        "estimated_duration": "3-8 minutes",
        "websocket_url": "ws://localhost:5000/ws/code-generation/gen_20250116_103500_def456",
        "progress_endpoint": "/api/architecture-risk/generation/gen_20250116_103500_def456/progress"
    },
    "message": "代码生成已启动",
    "timestamp": "2025-01-16T10:35:00Z"
}
```

#### **GET /api/architecture-risk/generation/{generation_id}/result**
获取代码生成结果

**响应体**:
```json
{
    "success": true,
    "data": {
        "generation_id": "gen_20250116_103500_def456",
        "status": "completed",
        "result": {
            "summary": {
                "files_generated": 23,
                "lines_of_code": 2847,
                "test_files": 15,
                "test_coverage": 87.3,
                "quality_score": 92.1,
                "generation_time": 245.8
            },
            "generated_files": [
                {
                    "path": "src/main/java/com/example/service/UserService.java",
                    "type": "service_class",
                    "size": 1247,
                    "quality_metrics": {
                        "complexity": "medium",
                        "maintainability": 8.5,
                        "test_coverage": 95.2
                    }
                }
            ],
            "fixes_applied": [
                {
                    "risk_id": "circular_dep_0",
                    "fix_type": "dependency_injection",
                    "description": "引入依赖注入解决UserService和OrderService的循环依赖",
                    "files_modified": ["UserService.java", "OrderService.java", "ApplicationConfig.java"]
                }
            ],
            "quality_report": {
                "static_analysis": {
                    "issues_found": 3,
                    "critical_issues": 0,
                    "warnings": 3
                },
                "test_results": {
                    "total_tests": 45,
                    "passed": 45,
                    "failed": 0,
                    "coverage": 87.3
                }
            }
        }
    },
    "message": "代码生成完成",
    "timestamp": "2025-01-16T10:39:05Z"
}
```

### **3. 项目管理API**

#### **GET /api/architecture-risk/projects**
获取项目列表

**响应体**:
```json
{
    "success": true,
    "data": {
        "projects": [
            {
                "id": "proj_001",
                "name": "企业级微服务架构",
                "directory": "/projects/enterprise-microservices",
                "status": "active",
                "last_detection": "2025-01-16T09:15:00Z",
                "risk_summary": {
                    "total_risks": 5,
                    "critical_risks": 1,
                    "high_risks": 2
                }
            }
        ],
        "total_count": 1,
        "active_count": 1
    }
}
```

## 📡 WebSocket接口设计

### **1. 风险检测实时通信**

**连接端点**: `ws://localhost:5000/ws/risk-detection/{detection_id}`

**消息格式**:

**进度更新消息**:
```json
{
    "type": "progress_update",
    "data": {
        "detection_id": "det_20250116_103000_abc123",
        "stage": "detection",
        "progress": 65,
        "message": "正在检测安全边界违反...",
        "timestamp": "2025-01-16T10:31:30Z"
    }
}
```

**风险发现消息**:
```json
{
    "type": "risk_found",
    "data": {
        "detection_id": "det_20250116_103000_abc123",
        "risk": {
            "id": "circular_dep_0",
            "type": "circular_dependency",
            "level": "CRITICAL",
            "title": "循环依赖: UserService ↔ OrderService",
            "components": ["UserService", "OrderService"]
        },
        "timestamp": "2025-01-16T10:31:45Z"
    }
}
```

**检测完成消息**:
```json
{
    "type": "detection_completed",
    "data": {
        "detection_id": "det_20250116_103000_abc123",
        "total_risks": 8,
        "execution_time": 127.5,
        "report_url": "/api/architecture-risk/detection/det_20250116_103000_abc123/report",
        "timestamp": "2025-01-16T10:32:07Z"
    }
}
```

### **2. 代码生成实时通信**

**连接端点**: `ws://localhost:5000/ws/code-generation/{generation_id}`

**文件生成消息**:
```json
{
    "type": "file_generated",
    "data": {
        "generation_id": "gen_20250116_103500_def456",
        "file": {
            "path": "src/main/java/com/example/service/UserService.java",
            "size": 1247,
            "type": "service_class"
        },
        "progress": 45,
        "timestamp": "2025-01-16T10:37:30Z"
    }
}
```

## 🔒 安全与认证

### **1. API认证**
- 复用ace现有的API密钥认证
- JWT令牌支持
- 请求签名验证

### **2. 权限控制**
- 基于角色的访问控制(RBAC)
- 项目级别权限管理
- 操作审计日志

### **3. 数据安全**
- 敏感数据加密存储
- 传输层TLS加密
- 输入数据验证和清理

## 📊 性能与限制

### **1. 请求限制**
- 每用户每分钟最多10次检测请求
- 每用户每分钟最多5次代码生成请求
- WebSocket连接数限制

### **2. 响应时间**
- API响应时间 < 200ms
- 检测任务启动时间 < 2s
- WebSocket消息延迟 < 100ms

### **3. 并发处理**
- 最大并发检测任务: 10个
- 最大并发代码生成: 5个
- WebSocket连接池管理

## 🧪 API测试

### **1. 单元测试**
```python
def test_validate_directory_api():
    response = client.post('/api/architecture-risk/validate-directory', 
                          json={'directory': '/test/path'})
    assert response.status_code == 200
    assert response.json['success'] == True
```

### **2. 集成测试**
- 端到端API流程测试
- WebSocket连接测试
- 并发请求测试

### **3. 性能测试**
- 负载测试
- 压力测试
- 响应时间基准测试

---

## 🔧 文档矛盾检测API

### **POST /api/document-contradiction/detect**
检测多文档间的矛盾

**请求体**:
```json
{
    "document_paths": [
        "/path/to/00-overview.md",
        "/path/to/01-architecture.md",
        "/path/to/02-implementation.md"
    ],
    "detection_options": {
        "check_architecture_consistency": true,
        "check_constraint_violations": true,
        "check_interface_consistency": true,
        "confidence_threshold": 0.8
    }
}
```

**响应体**:
```json
{
    "success": true,
    "data": {
        "total_contradictions": 3,
        "contradictions": [
            {
                "id": "contradiction_001",
                "type": "architecture_inconsistency",
                "severity": "HIGH",
                "description": "00号文档定义微内核架构，但01号文档实现为分层架构",
                "involved_documents": ["00-overview.md", "01-architecture.md"],
                "confidence": 0.92,
                "detection_method": "semantic_analysis"
            }
        ],
        "detection_summary": {
            "total_documents": 3,
            "processing_time": 2.3,
            "overall_confidence": 0.87
        }
    }
}
```

### **POST /api/document-contradiction/assess-reliability**
评估文档可靠性

**请求体**:
```json
{
    "document_path": "/path/to/document.md",
    "assessment_criteria": {
        "completeness_check": true,
        "consistency_check": true,
        "technical_accuracy_check": true
    }
}
```

**响应体**:
```json
{
    "success": true,
    "data": {
        "overall_reliability": 0.85,
        "reliability_factors": {
            "completeness": 0.90,
            "consistency": 0.82,
            "technical_accuracy": 0.83
        },
        "reliability_level": "HIGH",
        "recommendations": [
            "补充缺失的接口定义",
            "统一术语使用"
        ]
    }
}
```

### **GET /api/document-contradiction/report/{report_id}**
获取矛盾检测报告

**响应体**:
```json
{
    "success": true,
    "data": {
        "report_id": "report_12345",
        "generated_at": "2025-01-16T10:30:00Z",
        "contradictions": [...],
        "mitigation_strategies": [...],
        "export_formats": ["json", "markdown", "pdf"]
    }
}
```

---

**设计原则**: RESTful规范 + 实时通信 + 安全可靠 + 高性能
**核心价值**: 提供完整、高效的API接口支持九宫格界面的所有功能需求
