# V4实施计划文档使用指南（简版程序开发）

## 🚀 快速开发指南

### 📖 **核心阅读顺序**

#### **第一步：理解整体架构（必读）**
1. **00-V4核心工作流程配置.md** 
   - 重点关注：简版程序一键运行流程（第16-40行）
   - 关键信息：硬编码路径、任务接口调用顺序
   - 注意事项：迭代扫描支持机制

#### **第二步：环境准备（必读）**
2. **01-项目架构初始化和Python环境配置.md**
   - 重点关注：tools/ace/目录结构（第50-100行）
   - 关键信息：Python环境配置、依赖管理
   - 注意事项：API密钥管理配置

#### **第三步：核心算法实现（重点）**
3. **02-全景拼图认知构建核心算法实现.md**
   - 重点关注：simple_scanner.py主程序（第2630-4000行）
   - 关键信息：100%复制粘贴V3真实算法
   - **⚠️ 幻觉控制重点**：绝不使用临时代码，只复制粘贴V3算法

### 🎯 **实施步骤控制**

#### **阶段1：代码复制粘贴（零幻觉风险）**
```bash
# 严格按照文档执行，禁止创新
1. 阅读 02-全景拼图认知构建核心算法实现.md 第2630-4000行
2. 逐行复制粘贴V3算法代码（20+个核心算法）
3. 创建 simple_scanner.py，硬编码配置路径
4. 验证：python simple_scanner.py 能运行
```

#### **阶段2：任务接口集成（低幻觉风险）**
```bash
# 参考文档中的标准接口定义
1. 阅读 00-V4核心工作流程配置.md 第24-27行
2. 实现：StructureScanTask → ArchitectureAnalysisTask → QualityValidationTask
3. 验证：输出到checkresult-v4目录
```

#### **阶段3：迭代验证（中等风险）**
```bash
# 基于文档中的迭代流程
1. 阅读 00-V4核心工作流程配置.md 第60-65行
2. 实现：修改→扫描→报告循环
3. 验证：支持重复运行和结果对比
```

### ⚠️ **幻觉负载控制要点**

#### **严格禁止区域（100%幻觉风险）**
- ❌ **禁止自创算法**：只能复制粘贴V3真实算法
- ❌ **禁止临时代码**：文档明确要求"绝不使用临时代码"
- ❌ **禁止import导入**：使用内嵌算法，避免依赖问题

#### **安全实施区域（低幻觉风险）**
- ✅ **硬编码配置**：TARGET_PATH、OUTPUT_PATH直接写死
- ✅ **标准接口调用**：按文档定义的任务接口
- ✅ **V3算法复用**：从advanced-doc-scanner.py等文件复制

#### **需要谨慎的区域（中等风险）**
- 🟡 **错误处理**：参考V3算法的异常处理方式
- 🟡 **输出格式**：严格按照checkresult-v4目录要求
- 🟡 **进度显示**：简单的print输出即可

### 📋 **关键实施注意点**

#### **1. 代码来源控制**
```python
# 正确做法：直接复制V3算法
def analyze_structure(self, content):
    # 【复制自V3 advanced-doc-scanner.py 第245-319行】
    # 原始V3算法代码...
    
# 错误做法：自创算法
def analyze_structure(self, content):
    # 自己写的新算法... ❌
```

#### **2. 路径配置控制**
```python
# 硬编码配置（安全）
TARGET_PATH = "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1"
OUTPUT_PATH = "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/checkresult-v4"

# 动态配置（风险）❌
target_path = input("请输入目标路径：")  # 增加复杂度
```

#### **3. 算法边界控制**
- **只读取**：02-全景拼图认知构建核心算法实现.md 中的V3算法
- **只复制**：不修改算法逻辑，保持V3原始功能
- **只验证**：确保复制的算法能正常运行

### 🔍 **质量验证检查点**

#### **开发过程检查**
1. **代码来源检查**：每个函数都能追溯到V3源文件
2. **依赖检查**：只使用Python标准库
3. **路径检查**：硬编码路径正确
4. **输出检查**：checkresult-v4目录生成正确

#### **运行验证检查**
```bash
# 一键运行测试
python simple_scanner.py

# 预期结果
✅ 扫描开始...
✅ 结构分析完成...
✅ 报告生成到 checkresult-v4/
✅ 扫描完成
```

### 📚 **可选阅读文档**

#### **深入理解（可选）**
- **03-算法驱动AI增强引擎开发.md**：了解AI增强机制
- **05-95%置信度计算与验证系统.md**：理解质量标准
- **07-系统集成测试与质量验证.md**：测试策略参考

#### **后期扩展（可选）**
- **08-第二阶段复用接口设计.md**：为后续开发做准备
- **09-基础设施与存储系统实施.md**：存储系统设计

### 🎯 **成功标准**

#### **最小可行版本**
- ✅ `python simple_scanner.py` 能运行
- ✅ 输出到checkresult-v4目录
- ✅ 包含基本的结构分析报告

#### **完整功能版本**
- ✅ 支持迭代扫描
- ✅ 三重验证机制
- ✅ 质量评分和改进建议

### 💡 **核心开发原则**

1. **严格按文档执行**：不允许任何偏离文档的创新
2. **复制粘贴V3算法**：确保算法的可靠性和稳定性
3. **避免临时代码**：所有代码都必须来自V3真实算法
4. **硬编码配置**：减少复杂度，专注核心功能
5. **快速迭代验证**：支持修改→扫描→报告循环

### 📝 **开发检查清单**

#### **开发前检查**
- [ ] 已阅读00-V4核心工作流程配置.md
- [ ] 已阅读01-项目架构初始化和Python环境配置.md
- [ ] 已阅读02-全景拼图认知构建核心算法实现.md（第2630-4000行）
- [ ] 理解V3算法复制粘贴要求

#### **开发中检查**
- [ ] 所有算法都来自V3真实代码
- [ ] 硬编码路径配置正确
- [ ] 没有使用import导入外部依赖
- [ ] 错误处理参考V3算法方式

#### **开发后检查**
- [ ] python simple_scanner.py 能正常运行
- [ ] 输出到checkresult-v4目录成功
- [ ] 生成的报告格式正确
- [ ] 支持迭代扫描功能

**记住：简版程序的目的是设计文档格式化结构化，快速迭代使用，为后期建立稳定算法做准备！**
