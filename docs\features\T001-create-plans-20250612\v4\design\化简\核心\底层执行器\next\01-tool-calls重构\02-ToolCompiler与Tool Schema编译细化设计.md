# 02-ToolCompiler与Tool Schema编译细化设计（伪代码级）

## 1. 现有实现溯源
- 文件：`tools/ace/src/executors/validation_driven_executor.py`
- 相关枚举：`PyCRUDOperation`（第30行起）
- 现状：
  - PyCRUDOperation定义了操作枚举，但未有专门的ToolCompiler类将其自动转换为OpenAI Tool Schema。
  - Tool Schema编译逻辑分散在业务流程中，缺乏独立、可测试的编译器。

## 2. 目标结构与接口（伪代码级）

### 2.1 ToolCompiler类
```python
# tool_compiler.py
from typing import List, Dict, Any
from .validation_driven_executor import PyCRUDOperation

class ToolCompiler:
    @staticmethod
    def compile(operations: List[PyCRUDOperation]) -> List[Dict[str, Any]]:
        """
        将PyCRUDOperation枚举列表编译为OpenAI Tool Calling规范的tools JSON Schema列表
        """
        return [parse_function_signature(op.value.split(' -> ')[0]) for op in operations]
```

### 2.2 parse_function_signature辅助函数
```python
def parse_function_signature(signature: str) -> Dict[str, Any]:
    # 例: 'file_manager.create_file(path: str, content: str)'
    import re
    match = re.match(r"([\w.]+)\((.*)\)", signature)
    if not match:
        raise ValueError(f"Invalid signature format: {signature}")
    full_name = match.group(1)
    params_str = match.group(2)
    properties = {}
    required = []
    if params_str:
        params = [p.strip() for p in params_str.split(',')]
        for param in params:
            name, type_hint = [x.strip() for x in param.split(':')]
            # 简化类型映射
            json_type = "string" if type_hint == "str" else "object"
            properties[name] = {"type": json_type, "description": f"Parameter {name}"}
            required.append(name)
    return {
        "type": "function",
        "function": {
            "name": full_name,
            "description": f"Executes the {full_name} operation.",
            "parameters": {
                "type": "object",
                "properties": properties,
                "required": required
            }
        }
    }
```

### 2.3 用法示例
```python
ops = [PyCRUDOperation.FILE_CREATE, PyCRUDOperation.FILE_DELETE]
tools_schema = ToolCompiler.compile(ops)
# tools_schema将直接传递给AI Tool Calling接口
```

### 2.4 异常处理
- ToolCompiler.compile遇到格式不符的PyCRUDOperation.value时抛出ValueError
- parse_function_signature内部异常需捕获并记录日志

### 2.5 单元测试建议
```python
def test_tool_compiler():
    ops = [PyCRUDOperation.FILE_CREATE, PyCRUDOperation.FILE_DELETE]
    schema = ToolCompiler.compile(ops)
    assert isinstance(schema, list)
    assert schema[0]['function']['name'] == 'file_manager.create_file'
    assert 'path' in schema[0]['function']['parameters']['properties']
```

## 3. 需变更/新建/删除内容（精确到代码）
- 新建：
  - `tools/ace/src/executors/tool_compiler.py`（ToolCompiler、parse_function_signature）
- 修改：
  - ValidationDrivenExecutor等调用点，统一通过ToolCompiler生成tools参数
- 单元测试：
  - `test_tool_compiler.py`，覆盖所有PyCRUDOperation类型

## 4. 兼容性与测试建议
- ToolCompiler为新增组件，不影响旧有流程
- 建议：
  - 单元测试：所有PyCRUDOperation类型的Schema输出
  - 集成测试：与AI服务管理器的集成调用

## 5. 代码引用与路径
- `tools/ace/src/executors/validation_driven_executor.py`（PyCRUDOperation定义、调用点）
- 新增：`tools/ace/src/executors/tool_compiler.py`（建议） 