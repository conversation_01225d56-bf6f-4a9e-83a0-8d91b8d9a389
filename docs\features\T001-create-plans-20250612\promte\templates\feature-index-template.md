# 功能索引: {功能ID} - {功能名称}

## 功能概述
- **功能ID**: {功能ID}
- **功能名称**: {功能名称}
- **所属项目**: {项目名称}
- **主要目标**: {功能主要目标简述}

## 关键信息
- **技术栈**: {涉及的技术栈}
- **依赖功能**: {依赖的其他功能}
- **被依赖功能**: {依赖于此功能的其他功能}
- **核心组件**: {核心组件列表}

## 实现细节
- **核心类/方法**: {核心实现类和方法}
- **关键配置**: {关键配置项}
- **数据结构**: {相关数据结构}
- **异常处理**: {异常处理机制}

## 同步状态
- **索引创建日期**: {YYYY-MM-DD}
- **最后同步日期**: {YYYY-MM-DD}
- **同步来源**: {原始功能文档路径}
- **功能状态**: {active_development|stable_maintenance|completed_archived|planned|deprecated}
- **同步状态**: {up_to_date|requires_update|sync_in_progress|sync_failed}
- **同步检查频率**: {根据功能状态确定的检查频率}
- **优先级**: {high|medium|low}

> **⚠️ 重要提醒**: 此索引文档为AI记忆辅助文档，信息时效性取决于同步状态。
> 
> - **如果功能状态为"active_development"**: 请**优先参考**原始功能文档 `{原始功能文档路径}` 获取最新信息
> - **如果同步状态为"requires_update"**: 此索引信息可能已过时，建议检查原始功能文档
> - **如果同步状态为"sync_failed"**: 此索引信息不可靠，必须查看原始功能文档
> 
> 使用命令 `@sync:feature:{功能ID}` 可手动触发索引同步更新。

## 使用指南
- **初始化方式**: {如何初始化和使用此功能}
- **常见用例**: {常见使用场景}
- **最佳实践**: {使用此功能的最佳实践}
- **已知限制**: {已知的限制和注意事项}

## 开发状态追踪
- **当前开发阶段**: {当前所在的开发阶段}
- **完成进度**: {开发完成百分比或里程碑}
- **下一步计划**: {下一阶段的开发计划}
- **关键风险点**: {开发过程中的关键风险}

## 集成信息
- **API接口**: {对外提供的API接口}
- **配置要求**: {使用此功能需要的配置}
- **环境依赖**: {运行环境和依赖项}
- **性能指标**: {性能基准和优化目标}

## 测试信息
- **测试覆盖率**: {当前测试覆盖情况}
- **关键测试用例**: {核心功能的测试用例}
- **已知问题**: {当前已知的问题和限制}
- **测试环境**: {测试环境配置要求}

## 相关文档
- **原始功能文档**: `{原始功能文档路径}`
- **API文档**: `{API文档路径}`
- **测试文档**: `{测试文档路径}`
- **相关示例**: `{示例代码或示例项目路径}`
- **功能状态注册表**: `docs/feature-status.json`

## 变更历史
- **{日期}**: {变更描述} - 同步状态: {同步状态}
- **{日期}**: {变更描述} - 同步状态: {同步状态}

---

**模板使用说明**:
1. 复制此模板创建新的功能索引文档
2. 替换所有 `{变量}` 为实际内容
3. 确保同步状态信息准确填写
4. 根据功能状态添加相应的警示信息
5. 定期更新同步状态和变更历史 