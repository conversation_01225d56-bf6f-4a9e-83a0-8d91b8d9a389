# UID库切换XCE异常库代码修改模板

## 文档信息
- **文档ID**: F007-UID-XCE-MIGRATION-TEMPLATE-003
- **关联计划**: 01-UID库切换XCE异常库实施计划.md
- **创建日期**: 2025-06-11
- **版本**: v2.1 (精神融入版)
- **用途**: 为复杂步骤提供详细的代码修改模板和示例
- **使用原则**: 严格按照模板执行，保持修改范围边界，确保每次修改后立即验证

## 导入语句模板

### 标准XCE导入语句
```java
// XCE核心异常类
import org.xkong.cloud.commons.exception.core.ServiceException;
import org.xkong.cloud.commons.exception.core.BusinessException;
import org.xkong.cloud.commons.exception.core.SystemException;

// XCE分类异常类
import org.xkong.cloud.commons.exception.validation.ValidationBusinessException;
import org.xkong.cloud.commons.exception.network.NetworkSystemException;
import org.xkong.cloud.commons.exception.database.DatabaseSystemException;
import org.xkong.cloud.commons.exception.file.FileSystemException;
import org.xkong.cloud.commons.exception.security.SecurityBusinessException;

// XCE错误码
import org.xkong.cloud.commons.exception.model.ErrorCodes;
```

## 异常替换模板

**精确替换原则**: 每个替换都必须基于实际代码分析，禁止模糊匹配或批量替换

### 1. IllegalStateException替换模板

**原代码**:
```java
throw new IllegalStateException("组件未运行，当前状态: " + state);
```

**替换为**:
```java
throw ValidationBusinessException.invalidState("组件未运行，当前状态: " + state);
```

### 2. IllegalArgumentException替换模板

**原代码**:
```java
throw new IllegalArgumentException("无效的参数: " + param);
```

**替换为**:
```java
throw ValidationBusinessException.invalidArgument("无效的参数: " + param);
```

### 3. RuntimeException替换模板

**原代码**:
```java
throw new RuntimeException("操作失败", e);
```

**替换为**:
```java
// 根据具体情况选择合适的异常类型
throw SystemException.internalError("操作失败: " + e.getMessage(), e);
// 或
throw BusinessException.operationFailed("操作失败: " + e.getMessage(), e);
```

### 4. IOException替换模板

**原代码**:
```java
} catch (IOException e) {
    log.warn("文件操作失败: {}", e.getMessage());
    throw new RuntimeException("文件操作失败", e);
}
```

**替换为**:
```java
} catch (IOException e) {
    log.warn("文件操作失败: {}", e.getMessage());
    throw FileSystemException.ioError("文件操作失败: " + e.getMessage(), e);
}
```

### 5. 网络异常替换模板

**原代码**:
```java
} catch (ConnectException e) {
    log.error("网络连接失败: {}", e.getMessage());
    throw new RuntimeException("网络连接失败", e);
}
```

**替换为**:
```java
} catch (ConnectException e) {
    log.error("网络连接失败: {}", e.getMessage());
    throw NetworkSystemException.connectionFailed("网络连接失败: " + e.getMessage(), e);
}
```

### 6. 数据库异常替换模板

**原代码**:
```java
} catch (SQLTimeoutException e) {
    log.error("数据库查询超时: {}", e.getMessage());
    throw new RuntimeException("数据库查询超时", e);
}
```

**替换为**:
```java
} catch (SQLTimeoutException e) {
    log.error("数据库查询超时: {}", e.getMessage());
    throw DatabaseSystemException.queryTimeout("数据库查询超时: " + e.getMessage(), e);
}
```

## 具体文件修改模板

### PersistentInstanceWorkerIdAssigner.java修改模板

**文件路径**: `xkongcloud-commons\xkongcloud-commons-uid\src\main\java\org\xkong\cloud\commons\uid\worker\PersistentInstanceWorkerIdAssigner.java`
**修改边界**: 仅修改异常处理相关代码，保持其他逻辑不变

#### 导入语句修改
**在现有导入语句后添加**:
```java
// 添加XCE异常导入
import org.xkong.cloud.commons.exception.core.BusinessException;
import org.xkong.cloud.commons.exception.core.SystemException;
import org.xkong.cloud.commons.exception.validation.ValidationBusinessException;
import org.xkong.cloud.commons.exception.network.NetworkSystemException;
import org.xkong.cloud.commons.exception.database.DatabaseSystemException;
import org.xkong.cloud.commons.exception.model.ErrorCodes;
```

#### 关键异常替换点

**1. 第151行 - 初始化失败异常**
```java
// 原代码
throw new IllegalStateException("WorkerIdAssigner初始化失败", e);

// 替换为
throw SystemException.internalError("WorkerIdAssigner初始化失败: " + e.getMessage(), e);
```

**2. 第175行 - 组件状态异常**
```java
// 原代码
throw new IllegalStateException("WorkerIdAssigner未运行，当前状态: " + lifecycleState.get());

// 替换为
throw ValidationBusinessException.invalidState("WorkerIdAssigner未运行，当前状态: " + lifecycleState.get());
```

**3. 第198行 - 锁超时异常**
```java
// 原代码
throw new IllegalStateException("获取workerId锁超时");

// 替换为
throw SystemException.resourceTimeout("获取workerId锁超时");
```

**4. 第230行 - 资源分配失败异常**
```java
// 原代码
throw new IllegalStateException("无法为实例 " + instanceId + " 分配工作机器ID");

// 替换为
throw BusinessException.resourceAllocationFailed("无法为实例 " + instanceId + " 分配工作机器ID");
```

**5. 第524行 - 最大重试异常**
```java
// 原代码
throw new IllegalStateException("无法分配工作机器ID，已达到最大重试次数");

// 替换为
throw BusinessException.resourceAllocationFailed("无法分配工作机器ID，已达到最大重试次数");
```

#### 异常处理块修改模板

**网络异常处理**:
```java
// 原代码
} catch (ConnectException e) {
    log.warn("网络连接失败: {}", e.getMessage());
    // 其他处理逻辑
}

// 替换为
} catch (ConnectException e) {
    log.warn("网络连接失败: {}", e.getMessage());
    throw NetworkSystemException.connectionFailed("网络连接失败: " + e.getMessage(), e);
}
```

**数据库异常处理**:
```java
// 原代码
} catch (SQLTransientException e) {
    log.error("数据库临时故障: {}", e.getMessage());
    // 其他处理逻辑
}

// 替换为
} catch (SQLTransientException e) {
    log.error("数据库临时故障: {}", e.getMessage());
    throw DatabaseSystemException.transientFailure("数据库临时故障: " + e.getMessage(), e);
}
```

### PersistentInstanceManager.java修改模板

**文件路径**: `xkongcloud-commons\xkongcloud-commons-uid\src\main\java\org\xkong\cloud\commons\uid\instance\PersistentInstanceManager.java`

#### 导入语句修改
```java
// 添加XCE异常导入
import org.xkong.cloud.commons.exception.core.BusinessException;
import org.xkong.cloud.commons.exception.core.SystemException;
import org.xkong.cloud.commons.exception.validation.ValidationBusinessException;
import org.xkong.cloud.commons.exception.file.FileSystemException;
import org.xkong.cloud.commons.exception.model.ErrorCodes;
```

#### 关键异常替换点

**1. 第126行 - 参数验证异常**
```java
// 原代码
throw new IllegalArgumentException("无效的恢复策略: " + strategy);

// 替换为
throw ValidationBusinessException.invalidArgument("无效的恢复策略: " + strategy);
```

**2. 第138行 - 状态验证异常**
```java
// 原代码
if (id <= 0) throw new IllegalStateException("实例ID尚未初始化");

// 替换为
if (id <= 0) throw ValidationBusinessException.invalidState("实例ID尚未初始化");
```

**3. 第230行 - 文件读取异常**
```java
// 原代码
} catch (IOException e) {
    log.warn("读取实例ID文件失败: {}", e.getMessage());
    return null;
}

// 替换为
} catch (IOException e) {
    log.warn("读取实例ID文件失败: {}", e.getMessage());
    throw FileSystemException.fileReadError("读取实例ID文件失败: " + e.getMessage(), e);
}
```

**4. 第326行 - 注册失败异常**
```java
// 原代码
if (newId == null) throw new RuntimeException("注册新实例失败：未返回ID");

// 替换为
if (newId == null) throw BusinessException.registrationFailed("注册新实例失败：未返回ID");
```

## XCE错误码扩展模板

### ErrorCodes.java扩展
**文件路径**: `xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\model\ErrorCodes.java`

**删除冲突错误码**:
```java
// 删除以下冲突的错误码
// public static final String UID_GENERATION_FAILED = "XCE_UID_800";
// public static final String WORKER_ID_ALLOCATION_FAILED = "XCE_UID_801";
// public static final String INSTANCE_REGISTRATION_FAILED = "XCE_UID_802";
// public static final String INSTANCE_RECOVERY_FAILED = "XCE_UID_803";
// public static final String LEASE_RENEWAL_FAILED = "XCE_UID_804";
// public static final String MACHINE_FINGERPRINT_COLLECTION_FAILED = "XCE_UID_805";
```

**添加新分类错误码**:
```java
// 业务逻辑类错误码 (180-189) - 在100-199段内添加
public static final String UID_GENERATION_FAILED = "XCE_BIZ_180";
public static final String UID_WORKER_ID_ALLOCATION_FAILED = "XCE_BIZ_181";
public static final String UID_INSTANCE_REGISTRATION_FAILED = "XCE_BIZ_182";

// 数据库类错误码 (680-689) - 在650-699段内添加
public static final String UID_INSTANCE_RECOVERY_FAILED = "XCE_DB_680";
public static final String UID_LEASE_RENEWAL_FAILED = "XCE_DB_681";
public static final String UID_INSTANCE_QUERY_TIMEOUT = "XCE_DB_682";

// 文件类错误码 (720-729) - 在700-749段内添加
public static final String UID_FINGERPRINT_COLLECTION_FAILED = "XCE_FILE_720";
public static final String UID_INSTANCE_FILE_ACCESS_FAILED = "XCE_FILE_721";

// 验证类错误码 (780-789) - 在750-799段内添加
public static final String UID_PARAMETER_VALIDATION_FAILED = "XCE_VAL_780";
public static final String UID_STATE_VALIDATION_FAILED = "XCE_VAL_781";

// 网络类错误码 (620-629) - 在600-649段内添加
public static final String UID_SERVICE_UNAVAILABLE = "XCE_NET_620";
public static final String UID_NETWORK_TIMEOUT = "XCE_NET_621";
```

### 异常类扩展模板

#### BusinessException扩展
**文件路径**: `xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\core\BusinessException.java`

**添加UID专用方法**:
```java
// UID业务逻辑异常方法
public static BusinessException uidGenerationFailed(String reason) {
    return new BusinessException(ErrorCodes.UID_GENERATION_FAILED,
        "UID生成失败: " + reason)
        .addMetadata("component", "uid")
        .addMetadata("operation", "generation");
}

public static BusinessException workerIdAllocationFailed(String instanceId) {
    return new BusinessException(ErrorCodes.UID_WORKER_ID_ALLOCATION_FAILED,
        "Worker ID分配失败，实例: " + instanceId)
        .addMetadata("component", "uid")
        .addMetadata("operation", "worker_id_allocation")
        .addMetadata("instanceId", instanceId);
}

public static BusinessException instanceRegistrationFailed(String reason) {
    return new BusinessException(ErrorCodes.UID_INSTANCE_REGISTRATION_FAILED,
        "实例注册失败: " + reason)
        .addMetadata("component", "uid")
        .addMetadata("operation", "instance_registration");
}
```

#### DatabaseSystemException扩展
**文件路径**: `xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\database\DatabaseSystemException.java`

**添加UID专用方法**:
```java
// UID数据库异常方法
public static DatabaseSystemException instanceRecoveryFailed(String instanceId, Throwable cause) {
    return new DatabaseSystemException(ErrorCodes.UID_INSTANCE_RECOVERY_FAILED,
        "实例恢复失败，实例ID: " + instanceId, cause)
        .addMetadata("component", "uid")
        .addMetadata("operation", "instance_recovery")
        .addMetadata("instanceId", instanceId);
}

public static DatabaseSystemException leaseRenewalFailed(String workerId, Throwable cause) {
    return new DatabaseSystemException(ErrorCodes.UID_LEASE_RENEWAL_FAILED,
        "租约续约失败，Worker ID: " + workerId, cause)
        .addMetadata("component", "uid")
        .addMetadata("operation", "lease_renewal")
        .addMetadata("workerId", workerId);
}
```

#### FileSystemException扩展
**文件路径**: `xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\file\FileSystemException.java`

**添加UID专用方法**:
```java
// UID文件异常方法
public static FileSystemException fingerprintCollectionFailed(String reason, Throwable cause) {
    return new FileSystemException(ErrorCodes.UID_FINGERPRINT_COLLECTION_FAILED,
        "机器特征码收集失败: " + reason, cause)
        .addMetadata("component", "uid")
        .addMetadata("operation", "fingerprint_collection");
}

public static FileSystemException instanceFileAccessFailed(String filePath, Throwable cause) {
    return new FileSystemException(ErrorCodes.UID_INSTANCE_FILE_ACCESS_FAILED,
        "实例文件访问失败: " + filePath, cause)
        .addMetadata("component", "uid")
        .addMetadata("operation", "file_access")
        .addMetadata("filePath", filePath);
}
```

#### ValidationBusinessException扩展
**文件路径**: `xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\validation\ValidationBusinessException.java`

**添加UID专用方法**:
```java
// UID验证异常方法
public static ValidationBusinessException parameterValidationFailed(String parameter, String reason) {
    return new ValidationBusinessException(ErrorCodes.UID_PARAMETER_VALIDATION_FAILED,
        "UID参数验证失败，参数: " + parameter + ", 原因: " + reason)
        .addMetadata("component", "uid")
        .addMetadata("operation", "parameter_validation")
        .addMetadata("parameter", parameter);
}

public static ValidationBusinessException stateValidationFailed(String currentState, String expectedState) {
    return new ValidationBusinessException(ErrorCodes.UID_STATE_VALIDATION_FAILED,
        "UID状态验证失败，当前状态: " + currentState + ", 期望状态: " + expectedState)
        .addMetadata("component", "uid")
        .addMetadata("operation", "state_validation")
        .addMetadata("currentState", currentState)
        .addMetadata("expectedState", expectedState);
}
```

#### NetworkSystemException扩展
**文件路径**: `xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\network\NetworkSystemException.java`

**添加UID专用方法**:
```java
// UID网络异常方法
public static NetworkSystemException serviceUnavailable(String serviceName) {
    return new NetworkSystemException(ErrorCodes.UID_SERVICE_UNAVAILABLE,
        "UID服务不可用: " + serviceName)
        .addMetadata("component", "uid")
        .addMetadata("operation", "service_access")
        .addMetadata("serviceName", serviceName);
}

public static NetworkSystemException networkTimeout(String operation, int timeoutMs) {
    return new NetworkSystemException(ErrorCodes.UID_NETWORK_TIMEOUT,
        "UID网络操作超时: " + operation + ", 超时时间: " + timeoutMs + "ms")
        .addMetadata("component", "uid")
        .addMetadata("operation", operation)
        .addMetadata("timeoutMs", String.valueOf(timeoutMs));
}
```

## 异常处理器创建模板

### UidExceptionHandler.java完整模板
**文件路径**: `xkongcloud-commons\xkongcloud-commons-uid\src\main\java\org\xkong\cloud\commons\uid\exception\UidExceptionHandler.java`

```java
package org.xkong.cloud.commons.uid.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.xkong.cloud.commons.exception.core.GlobalExceptionHandler;
import org.xkong.cloud.commons.exception.core.BusinessException;
import org.xkong.cloud.commons.exception.validation.ValidationBusinessException;
import org.xkong.cloud.commons.exception.model.ErrorCodes;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * UID库专用异常处理器
 * 继承XCE的GlobalExceptionHandler，添加UID特有的异常处理逻辑
 */
@RestControllerAdvice
public class UidExceptionHandler extends GlobalExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(UidExceptionHandler.class);

    /**
     * 处理Worker ID分配失败异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.SERVICE_UNAVAILABLE)
    public Object handleWorkerIdAllocationException(BusinessException ex) {
        if (ErrorCodes.WORKER_ID_ALLOCATION_FAILED.equals(ex.getErrorInfo().getCode())) {
            log.error("Worker ID分配失败: {}", ex.getMessage());
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", ex.getErrorInfo().getCode());
            errorResponse.put("message", "Worker ID资源不足，请联系管理员");
            errorResponse.put("suggestion", "检查Worker ID池配置或清理过期分配");
            errorResponse.put("timestamp", LocalDateTime.now());
            
            return errorResponse;
        }
        
        // 其他业务异常交给父类处理
        return super.handleServiceException(ex);
    }

    /**
     * 处理实例恢复失败异常
     */
    @ExceptionHandler(ValidationBusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Object handleInstanceRecoveryException(ValidationBusinessException ex) {
        if (ErrorCodes.INSTANCE_RECOVERY_FAILED.equals(ex.getErrorInfo().getCode())) {
            log.warn("实例恢复失败: {}", ex.getMessage());
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", ex.getErrorInfo().getCode());
            errorResponse.put("message", "实例身份恢复失败");
            errorResponse.put("suggestion", "检查机器特征码或调整置信度阈值");
            errorResponse.put("timestamp", LocalDateTime.now());
            
            return errorResponse;
        }
        
        // 其他验证异常交给父类处理
        return super.handleServiceException(ex);
    }
}
```

## Spring自动配置模板

### spring.factories文件模板
**文件路径**: `xkongcloud-commons\xkongcloud-commons-uid\src\main\resources\META-INF\spring.factories`

```properties
# XCE异常处理自动配置
org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
org.xkong.cloud.commons.exception.core.ExceptionAutoConfiguration,\
org.xkong.cloud.commons.uid.exception.UidExceptionHandler
```

## 验证模板

### 编译验证命令
```bash
# 在项目根目录执行
cd c:\ExchangeWorks\xkong\xkongcloud
mvn clean compile -pl xkongcloud-commons/xkongcloud-commons-uid
```

### 测试验证命令
```bash
# 执行单元测试
mvn test -pl xkongcloud-commons/xkongcloud-commons-uid

# 执行集成测试
mvn integration-test -pl xkongcloud-commons/xkongcloud-commons-uid
```

### 功能验证代码模板
```java
// 验证XCE异常正确抛出的测试代码
@Test
public void testXCEExceptionThrown() {
    // 准备测试数据
    
    // 执行操作并验证异常
    assertThrows(ValidationBusinessException.class, () -> {
        // 触发异常的操作
    });
    
    // 验证错误码
    try {
        // 触发异常的操作
    } catch (ValidationBusinessException e) {
        assertEquals(ErrorCodes.INSTANCE_RECOVERY_FAILED, e.getErrorInfo().getCode());
    }
}
```

## Core项目修改模板

### UidGeneratorConfig.java修改模板

**文件路径**: `xkongcloud-business-internal-core\src\main\java\org\xkong\cloud\business\internal\core\config\UidGeneratorConfig.java`

#### 导入语句修改
**在现有导入语句后添加**:
```java
// 添加XCE异常导入
import org.xkong.cloud.commons.exception.core.SystemException;
import org.xkong.cloud.commons.exception.validation.ValidationBusinessException;
```

#### 异常处理修改

**1. 第188-191行 - 配置异常处理**
```java
// 原代码
} catch (Exception e) {
    log.error("创建UidGeneratorFacade失败: {}", e.getMessage(), e);
    throw new IllegalStateException("Failed to create UidGeneratorFacade", e);
}

// 替换为
} catch (Exception e) {
    log.error("创建UidGeneratorFacade失败: {}", e.getMessage(), e);
    throw SystemException.configurationError("UID生成器配置失败: " + e.getMessage(), e);
}
```

**2. 第209行 - 不支持操作异常**
```java
// 原代码
throw new UnsupportedOperationException("parseUID方法未实现");

// 替换为
throw ValidationBusinessException.operationNotSupported("parseUID方法未实现");
```

### Core项目依赖配置模板

#### pom.xml依赖添加（如需要）
**文件路径**: `xkongcloud-business-internal-core\pom.xml`

**在dependencies节点添加**（仅在间接依赖不可用时）:
```xml
<!-- XCE异常处理库（如果间接依赖不可用） -->
<dependency>
    <groupId>org.xkong.cloud</groupId>
    <artifactId>xkongcloud-commons-exception</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### Core项目测试验证模板

#### 集成测试验证代码
**文件路径**: `src\test\java\org\xkong\cloud\business\internal\core\integration\UidGeneratorIntegrationTest.java`

**添加XCE异常测试**:
```java
@Test
public void testXCEExceptionHandling() {
    // 验证XCE异常正确处理
    try {
        // 触发配置错误的操作
        // 这里可以模拟配置错误场景
    } catch (SystemException e) {
        // 验证是XCE异常
        assertNotNull(e.getErrorInfo());
        assertNotNull(e.getErrorInfo().getCode());
        assertTrue(e.getErrorInfo().getCode().startsWith("XCE_"));
    }
}

@Test
public void testUnsupportedOperationException() {
    // 验证不支持操作异常
    assertThrows(ValidationBusinessException.class, () -> {
        uidGenerator.parseUID(123456L);
    });
}
```

### Core项目配置验证模板

#### Spring Boot启动验证
```java
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.properties")
public class CoreProjectXCEIntegrationTest {

    @Autowired
    private UidGenerator uidGenerator;

    @Autowired
    private UidGeneratorFacade uidGeneratorFacade;

    @Test
    public void testSpringBootStartupWithXCE() {
        // 验证Spring Boot应用正常启动
        assertNotNull(uidGenerator);
        assertNotNull(uidGeneratorFacade);

        // 验证UID生成功能正常
        long uid = uidGenerator.getUID();
        assertTrue(uid > 0);
    }

    @Test
    public void testXCEExceptionHandlerIntegration() {
        // 验证XCE异常处理器正常工作
        // 这里可以添加具体的异常处理验证逻辑
    }
}
```

## 验证命令模板

### Core项目编译验证
```bash
# 在项目根目录执行
cd c:\ExchangeWorks\xkong\xkongcloud
mvn clean compile -pl xkongcloud-business-internal-core
```

### Core项目测试验证
```bash
# 执行Core项目单元测试
mvn test -pl xkongcloud-business-internal-core

# 执行Core项目集成测试
mvn integration-test -pl xkongcloud-business-internal-core
```

### 端到端验证
```bash
# 同时编译UID库和Core项目
mvn clean compile -pl xkongcloud-commons/xkongcloud-commons-uid,xkongcloud-business-internal-core

# 同时测试UID库和Core项目
mvn test -pl xkongcloud-commons/xkongcloud-commons-uid,xkongcloud-business-internal-core
```

## 使用说明

### 模板使用原则
- **严格按照模板执行**: 确保每个替换都按照模板格式进行
- **保持错误码一致性**: 使用预定义的XCE错误码常量
- **异常链保持完整**: 确保原始异常作为cause传递
- **立即验证**: 每个文件修改后立即编译验证

### 回滚指引
**回滚操作参考**: 04-风险评估与回滚方案.md → 具体回滚步骤
**注意**: 不自动执行Git操作，需要人类决策
