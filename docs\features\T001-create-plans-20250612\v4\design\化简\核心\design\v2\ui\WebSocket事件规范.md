# Project Manager V2 WebSocket事件规范

## 概述

本文档定义了Project Manager V2与后端V4.2治理引擎之间的WebSocket实时通信协议，确保界面能够实时反映治理过程的状态变化。

## 连接规范

### 连接URL
```
ws://localhost:25526/ws/pm-v2
```

### 认证方式
```javascript
// 连接时发送认证信息
const ws = new WebSocket('ws://localhost:25526/ws/pm-v2', [], {
    headers: {
        'Authorization': 'Bearer ' + authToken
    }
});

// 连接后发送项目路径绑定（如果有）
ws.onopen = () => {
    const savedProjectPath = localStorage.getItem('pm_v2_current_project_path');
    if (savedProjectPath) {
        ws.send(JSON.stringify({
            type: 'bind_project_path',
            data: { project_path: savedProjectPath }
        }));
    }
};
```

### 心跳机制
```javascript
// 每30秒发送心跳
setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: 'ping' }));
    }
}, 30000);
```

---

## 事件消息格式

### 标准消息结构
```json
{
    "type": "event_type",
    "data": {
        // 事件具体数据
    },
    "timestamp": "2025-01-31T14:17:30Z",
    "project_path": "string",
    "sequence": 12345
}
```

### 项目路径绑定消息
```json
{
    "type": "bind_project_path",
    "data": {
        "project_path": "/path/to/project/documents"
    },
    "timestamp": "2025-01-31T14:17:30Z"
}
```

### 错误消息格式
```json
{
    "type": "error",
    "data": {
        "code": "ERROR_CODE",
        "message": "错误描述",
        "details": "详细错误信息"
    },
    "timestamp": "2025-01-31T14:17:30Z"
}
```

---

## 区域1-2：项目进度监控事件

### 1. 阶段进度更新
```json
{
    "type": "stage_progress_update",
    "data": {
        "stage_number": 0,
        "stage_name": "标准化与预验证",
        "status": "in_progress",
        "progress_percentage": 30,
        "status_indicator": "status-thinking",
        "updated_at": "2025-01-31T14:17:30Z"
    }
}
```

### 2. 阶段零指标更新
```json
{
    "type": "stage_zero_metrics_update",
    "data": {
        "pre_validation_pass_rate": 100,
        "conflict_prevention_count": 4,
        "schema_validation_passed": 28,
        "schema_validation_total": 30,
        "updated_fields": ["conflict_prevention_count", "schema_validation_passed"]
    }
}
```

### 3. 关键指标更新
```json
{
    "type": "key_metrics_update",
    "data": {
        "atomic_constraints_discovered": 28,
        "global_contracts_generated": 20,
        "documents_processed": 4,
        "documents_total": 5,
        "current_reliability_score": 89.2,
        "updated_fields": ["atomic_constraints_discovered", "current_reliability_score"]
    }
}
```

### 4. 整体进度更新
```json
{
    "type": "overall_progress_update",
    "data": {
        "percentage": 35,
        "status": "阶段零进行中",
        "estimated_completion": "2025-01-31T15:00:00Z"
    }
}
```

---

## 区域3：风险评估事件

### 1. 可靠性评分更新
```json
{
    "type": "reliability_score_update",
    "data": {
        "score": 89,
        "previous_score": 87,
        "status": "good",
        "improvement_reason": "预防了新的潜在冲突",
        "improvement_from_prevention": 24
    }
}
```

### 2. 风险预防事件
```json
{
    "type": "risk_prevented",
    "data": {
        "risk_id": "risk_004",
        "level": "medium",
        "description": "数据库连接池配置可能导致性能瓶颈",
        "prevention_mechanism": "自动应用最佳实践配置模板",
        "prevention_status": "prevented",
        "prevented_at": "2025-01-31T14:17:30Z"
    }
}
```

### 3. 文档健康报告更新
```json
{
    "type": "document_health_update",
    "data": {
        "reliability_score": 89,
        "prevented_conflicts": 4,
        "new_issues_found": 0,
        "resolved_issues": 1,
        "report_url": "/api/v2/project/123/health-report"
    }
}
```

---

## 区域4：项目经理状态事件

### 1. 管理器状态更新
```json
{
    "type": "manager_status_update",
    "data": {
        "current_manager": {
            "role": "首席架构师AI",
            "status": "active",
            "status_indicator": "status-active"
        },
        "work_status": {
            "status": "PROCESSING",
            "color": "#FF9800"
        }
    }
}
```

### 2. 当前任务更新
```json
{
    "type": "current_task_update",
    "data": {
        "description": "正在生成全局契约点",
        "status": "in_progress",
        "progress_percentage": 85,
        "status_indicator": "status-converging",
        "estimated_completion": "2025-01-31T14:20:00Z"
    }
}
```

### 3. 文档处理状态
```json
{
    "type": "document_processing_update",
    "data": {
        "current_document": {
            "name": "2-架构数据模型标准-V2.md",
            "status": "processing",
            "status_indicator": "status-thinking",
            "progress_percentage": 45
        }
    }
}
```

### 4. 处理时间更新
```json
{
    "type": "processing_time_update",
    "data": {
        "elapsed_seconds": 210,
        "formatted": "3分30秒",
        "color": "#FF9800",
        "efficiency_status": "normal"
    }
}
```

---

## 区域5：算法思维事件

### 1. AI-算法协同状态
```json
{
    "type": "ai_algorithm_collaboration_update",
    "data": {
        "ai_role": "首席架构师AI",
        "algorithm_component": "ConstraintPreprocessor",
        "current_status": "processing",
        "collaboration_stage": "schema_validation",
        "result": "正在验证约束Schema..."
    }
}
```

### 2. 算法日志条目
```json
{
    "type": "algorithm_log_entry",
    "data": {
        "id": "constraint_generation_001",
        "timestamp": "14:18:45",
        "message": "成功生成边界条件约束 BC004",
        "expandable": true,
        "ai_comm_details": {
            "request": "生成API超时边界条件",
            "response_time_ms": 35,
            "status": "成功",
            "constraint_id": "BC004"
        },
        "py_ops_details": {
            "operation": "约束对象创建",
            "execution_time_ms": 8,
            "memory_usage_mb": 2.8,
            "cpu_usage_percent": 0.15
        }
    }
}
```

### 3. 思维过程状态
```json
{
    "type": "thinking_process_update",
    "data": {
        "current_phase": "constraint_analysis",
        "phase_description": "正在分析约束间的依赖关系",
        "progress_percentage": 60,
        "estimated_completion": "2025-01-31T14:19:30Z"
    }
}
```

---

## 区域6：约束审查事件

### 1. 约束创建事件
```json
{
    "type": "constraint_created",
    "data": {
        "constraint": {
            "id": "BC004",
            "category": "boundary_condition",
            "type": "timeout",
            "params": {
                "target_entity": "database_query",
                "max_ms": 5000,
                "unit": "milliseconds"
            },
            "description": "数据库查询超时边界条件",
            "source_block_id": "block_005",
            "parent_id": "GB001",
            "created_at": "2025-01-31T14:18:45Z"
        }
    }
}
```

### 2. 约束更新事件
```json
{
    "type": "constraint_updated",
    "data": {
        "constraint_id": "BC004",
        "updated_fields": {
            "params.max_ms": 3000,
            "description": "数据库查询超时边界条件(已优化)"
        },
        "update_reason": "性能优化建议",
        "updated_at": "2025-01-31T14:19:00Z"
    }
}
```

### 3. 约束验证结果
```json
{
    "type": "constraint_validation_result",
    "data": {
        "constraint_id": "BC004",
        "validation_status": "passed",
        "validation_details": {
            "schema_valid": true,
            "conflict_check": "passed",
            "parameter_range": "valid"
        },
        "validated_at": "2025-01-31T14:18:46Z"
    }
}
```

---

## 区域7：知识库可视化事件

### 1. 知识图谱更新
```json
{
    "type": "knowledge_graph_update",
    "data": {
        "operation": "add_node",
        "node": {
            "id": "BC004",
            "category": "boundary_condition",
            "type": "timeout",
            "position": { "x": 150, "y": 160 },
            "parent_id": "GB001",
            "is_forked": true,
            "description": "BC004: 数据库查询超时边界条件"
        },
        "new_connections": [
            {
                "from": "GB001",
                "to": "BC004",
                "type": "fork",
                "style": "dashed"
            }
        ]
    }
}
```

### 2. 节点关系变化
```json
{
    "type": "node_relationship_update",
    "data": {
        "constraint_id": "BC004",
        "relationship_changes": {
            "new_children": ["LC005"],
            "updated_parent": "GB001"
        },
        "graph_layout_update": true
    }
}
```

---

## 区域8：控制区事件

### 1. 项目状态变化
```json
{
    "type": "project_status_change",
    "data": {
        "project_id": "123",
        "status": "paused",
        "reason": "用户暂停",
        "can_resume": true,
        "paused_at": "2025-01-31T14:19:00Z"
    }
}
```

### 2. 扫描操作结果
```json
{
    "type": "scan_operation_result",
    "data": {
        "operation": "document_scan",
        "status": "completed",
        "results": {
            "documents_found": 7,
            "new_documents": 2,
            "updated_documents": 1
        },
        "next_action": "start_processing"
    }
}
```

---

## 区域9：交付结果事件

### 1. 文件生成完成
```json
{
    "type": "deliverable_ready",
    "data": {
        "file": {
            "name": "约束规范文档.md",
            "type": "constraint_spec",
            "size": "2.5MB",
            "status": "completed",
            "download_url": "/api/v2/files/file_123/download",
            "generated_at": "2025-01-31T14:19:30Z"
        }
    }
}
```

### 2. 处理统计更新
```json
{
    "type": "processing_statistics_update",
    "data": {
        "documents_processed": { "current": 4, "total": 5, "percentage": 80 },
        "constraints_generated": { "count": 30, "percentage": 90 },
        "risks_identified": { "count": 2, "percentage": 40 },
        "processing_time": { "seconds": 240, "formatted": "4分00秒", "percentage": 80 }
    }
}
```

### 3. 处理完成事件
```json
{
    "type": "processing_complete",
    "data": {
        "project_id": "123",
        "completion_status": "success",
        "final_statistics": {
            "total_constraints": 32,
            "total_risks_prevented": 5,
            "final_reliability_score": 92,
            "processing_duration": "4分15秒"
        },
        "deliverables": [
            {
                "name": "约束规范文档.md",
                "download_url": "/api/v2/files/file_123/download"
            },
            {
                "name": "风险评估报告.pdf",
                "download_url": "/api/v2/files/file_124/download"
            }
        ],
        "completed_at": "2025-01-31T14:20:00Z"
    }
}
```

---

## 错误事件

### 1. 处理错误
```json
{
    "type": "processing_error",
    "data": {
        "error_code": "CONSTRAINT_VALIDATION_FAILED",
        "message": "约束验证失败",
        "details": "参数max_ms必须为正整数",
        "constraint_id": "BC005",
        "recovery_action": "请修正参数后重试",
        "can_retry": true
    }
}
```

### 2. 连接错误
```json
{
    "type": "connection_error",
    "data": {
        "error_code": "BACKEND_UNAVAILABLE",
        "message": "后端服务暂时不可用",
        "retry_after": 30,
        "auto_reconnect": true
    }
}
```

---

## 客户端事件处理框架

### 事件监听器注册
```javascript
class PM2WebSocketClient {
    constructor(projectId, authToken) {
        this.projectId = projectId;
        this.authToken = authToken;
        this.handlers = new Map();
        this.connect();
    }
    
    connect() {
        this.ws = new WebSocket(`ws://localhost:25526/ws/project/${this.projectId}`);
        
        this.ws.onopen = () => {
            console.log('WebSocket连接已建立');
            this.authenticate();
        };
        
        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket连接已关闭');
            this.reconnect();
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
        };
    }
    
    authenticate() {
        this.send({
            type: 'auth',
            data: { token: this.authToken }
        });
    }
    
    handleMessage(message) {
        const handlers = this.handlers.get(message.type) || [];
        handlers.forEach(handler => {
            try {
                handler(message.data);
            } catch (error) {
                console.error(`处理事件${message.type}时出错:`, error);
            }
        });
    }
    
    on(eventType, handler) {
        if (!this.handlers.has(eventType)) {
            this.handlers.set(eventType, []);
        }
        this.handlers.get(eventType).push(handler);
    }
    
    send(message) {
        if (this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }
    
    reconnect() {
        setTimeout(() => {
            console.log('尝试重新连接...');
            this.connect();
        }, 5000);
    }
}
```

### 使用示例
```javascript
const wsClient = new PM2WebSocketClient('123', authToken);

// 注册事件处理器
wsClient.on('stage_progress_update', updateStageProgress);
wsClient.on('constraint_created', handleConstraintCreated);
wsClient.on('reliability_score_update', updateReliabilityScore);
wsClient.on('algorithm_log_entry', addAlgorithmLogEntry);
```

---

## 性能和可靠性

### 1. 消息队列
- 客户端断线重连时自动获取错过的消息
- 服务端保留最近100条消息用于重放

### 2. 消息去重
- 每条消息包含sequence序号
- 客户端自动过滤重复消息

### 3. 错误恢复
- 自动重连机制
- 连接状态监控
- 优雅降级处理

### 4. 性能优化
- 消息批量发送
- 关键事件优先级处理
- 客户端缓存机制
