/home/<USER>/apps/jdk-21.0.5/bin/java -javaagent:/home/<USER>/apps/idea-IU-243.23654.189/lib/idea_rt.jar=33761:/home/<USER>/apps/idea-IU-243.23654.189/bin -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -classpath /media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/test-classes:/media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/classes:/home/<USER>/works/project/mvnRepository/com/xfvape/uid/uid-generator/0.0.4-RELEASE/uid-generator-0.0.4-RELEASE.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-core/6.2.6/spring-core-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-jcl/6.2.6/spring-jcl-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-beans/6.2.6/spring-beans-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/mybatis/mybatis/3.2.3/mybatis-3.2.3.jar:/home/<USER>/works/project/mvnRepository/org/mybatis/mybatis-spring/1.2.4/mybatis-spring-1.2.4.jar:/home/<USER>/works/project/mvnRepository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/home/<USER>/works/project/mvnRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/log4j-over-slf4j/2.0.17/log4j-over-slf4j-2.0.17.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-context/6.2.6/spring-context-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-aop/6.2.6/spring-aop-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-expression/6.2.6/spring-expression-6.2.6.jar:/home/<USER>/works/project/mvnRepository/io/micrometer/micrometer-observation/1.14.6/micrometer-observation-1.14.6.jar:/home/<USER>/works/project/mvnRepository/io/micrometer/micrometer-commons/1.14.6/micrometer-commons-1.14.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-jdbc/6.2.6/spring-jdbc-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-tx/6.2.6/spring-tx-6.2.6.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-annotations/2.18.3/jackson-annotations-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-core/2.18.3/jackson-core-2.18.3.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter-test/3.4.5/spring-boot-starter-test-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter/3.4.5/spring-boot-starter-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot/3.4.5/spring-boot-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-autoconfigure/3.4.5/spring-boot-autoconfigure-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter-logging/3.4.5/spring-boot-starter-logging-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/home/<USER>/works/project/mvnRepository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/home/<USER>/works/project/mvnRepository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/home/<USER>/works/project/mvnRepository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-test/3.4.5/spring-boot-test-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-test-autoconfigure/3.4.5/spring-boot-test-autoconfigure-3.4.5.jar:/home/<USER>/works/project/mvnRepository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/home/<USER>/works/project/mvnRepository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/home/<USER>/works/project/mvnRepository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/home/<USER>/works/project/mvnRepository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/home/<USER>/works/project/mvnRepository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/home/<USER>/works/project/mvnRepository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/home/<USER>/works/project/mvnRepository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/home/<USER>/works/project/mvnRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-test/6.2.6/spring-test-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/xmlunit/xmlunit-core/2.10.0/xmlunit-core-2.10.0.jar:/home/<USER>/works/project/mvnRepository/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar:/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar:/home/<USER>/works/project/mvnRepository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/home/<USER>/works/project/mvnRepository/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-launcher/1.11.4/junit-platform-launcher-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/testcontainers/1.19.7/testcontainers-1.19.7.jar:/home/<USER>/works/project/mvnRepository/junit/junit/4.13.2/junit-4.13.2.jar:/home/<USER>/works/project/mvnRepository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/home/<USER>/works/project/mvnRepository/org/apache/commons/commons-compress/1.24.0/commons-compress-1.24.0.jar:/home/<USER>/works/project/mvnRepository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/home/<USER>/works/project/mvnRepository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-api/3.3.6/docker-java-api-3.3.6.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-transport-zerodep/3.3.6/docker-java-transport-zerodep-3.3.6.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-transport/3.3.6/docker-java-transport-3.3.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/postgresql/1.19.7/postgresql-1.19.7.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/jdbc/1.20.6/jdbc-1.20.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/database-commons/1.20.6/database-commons-1.20.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/junit-jupiter/1.19.7/junit-jupiter-1.19.7.jar:/home/<USER>/works/project/mvnRepository/org/openjdk/jmh/jmh-core/1.37/jmh-core-1.37.jar:/home/<USER>/works/project/mvnRepository/net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar:/home/<USER>/works/project/mvnRepository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/home/<USER>/works/project/mvnRepository/org/openjdk/jmh/jmh-generator-annprocess/1.37/jmh-generator-annprocess-1.37.jar:/home/<USER>/works/project/mvnRepository/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/home/<USER>/works/project/mvnRepository/org/postgresql/postgresql/42.7.5/postgresql-42.7.5.jar:/home/<USER>/works/project/mvnRepository/org/checkerframework/checker-qual/3.48.3/checker-qual-3.48.3.jar:/home/<USER>/works/project/mvnRepository/com/github/oshi/oshi-core/6.5.0/oshi-core-6.5.0.jar:/home/<USER>/works/project/mvnRepository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/home/<USER>/works/project/mvnRepository/net/java/dev/jna/jna-platform/5.14.0/jna-platform-5.14.0.jar:/home/<USER>/works/project/mvnRepository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar:/home/<USER>/works/project/mvnRepository/ch/qos/logback/logback-classic/1.5.16/logback-classic-1.5.16.jar:/home/<USER>/works/project/mvnRepository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/home/<USER>/works/project/mvnRepository/org/xkongkit/xkongkit-core/1.0.0-SNAPSHOT/xkongkit-core-1.0.0-SNAPSHOT.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/kryo/5.5.0/kryo-5.5.0.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/home/<USER>/works/project/mvnRepository/org/lmdbjava/lmdbjava/0.9.1/lmdbjava-0.9.1.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-constants/0.10.4/jnr-constants-0.10.4.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-ffi/2.2.17/jnr-ffi-2.2.17.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jffi/1.3.13/jffi-1.3.13.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jffi/1.3.13/jffi-1.3.13-native.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-commons/9.7.1/asm-commons-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-a64asm/1.0.0/jnr-a64asm-1.0.0.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-x86asm/1.0.2/jnr-x86asm-1.0.2.jar:/home/<USER>/works/project/mvnRepository/com/tokyocabinet/tokyocabinet/1.24/tokyocabinet-1.24.jar:/home/<USER>/works/project/mvnRepository/org/jackson/databind/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.3/jackson-datatype-jsr310-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/github/luben/zstd-jni/1.5.5-3/zstd-jni-1.5.5-3.jar:/home/<USER>/works/project/mvnRepository/com/github/ben-manes/caffeine/caffeine/3.1.8/caffeine-3.1.8.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/home/<USER>/works/project/mvnRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/home/<USER>/works/project/mvnRepository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-context/1.70.0/grpc-context-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-impl/0.31.1/opencensus-impl-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-impl-core/0.31.1/opencensus-impl-core-0.31.1.jar:/home/<USER>/works/project/mvnRepository/com/lmax/disruptor/3.4.2/disruptor-3.4.2.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-census/1.71.0/grpc-census-1.71.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-api/1.70.0/grpc-api-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-contrib-grpc-metrics/0.31.1/opencensus-contrib-grpc-metrics-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-services/1.71.0/grpc-services-1.71.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-stub/1.70.0/grpc-stub-1.70.0.jar:/home/<USER>/works/project/mvnRepository/org/codehaus/mojo/animal-sniffer-annotations/1.24/animal-sniffer-annotations-1.24.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-core/1.70.0/grpc-core-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar:/home/<USER>/works/project/mvnRepository/io/perfmark/perfmark-api/0.27.0/perfmark-api-0.27.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-protobuf/1.70.0/grpc-protobuf-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/protobuf/protobuf-java/3.25.6/protobuf-java-3.25.6.jar:/home/<USER>/works/project/mvnRepository/com/google/api/grpc/proto-google-common-protos/2.46.0/proto-google-common-protos-2.46.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-protobuf-lite/1.70.0/grpc-protobuf-lite-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-util/1.70.0/grpc-util-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/protobuf/protobuf-java-util/3.25.6/protobuf-java-util-3.25.6.jar:/home/<USER>/works/project/mvnRepository/com/google/errorprone/error_prone_annotations/2.30.0/error_prone_annotations-2.30.0.jar:/home/<USER>/works/project/mvnRepository/com/google/code/gson/gson/2.11.0/gson-2.11.0.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-x86_64.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-common/4.1.119.Final/netty-common-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-buffer/4.1.119.Final/netty-buffer-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport/4.1.119.Final/netty-transport-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-resolver/4.1.119.Final/netty-resolver-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-native-unix-common/4.1.119.Final/netty-transport-native-unix-common-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-classes-epoll/4.1.119.Final/netty-transport-classes-epoll-4.1.119.Final.jar org.xkong.cloud.commons.uid.TestRunner
===== 运行单元测试 =====
21:26:58,107 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-classic version 1.5.16
21:26:58,107 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-core version 1.5.18
21:26:58,107 |-WARN in ch.qos.logback.classic.LoggerContext[default] - Versions of logback-core and logback-classic are different!
21:26:58,110 |-INFO in ch.qos.logback.classic.util.ContextInitializer@72c8e7b - Here is a list of configurators discovered as a service, by rank: 
21:26:58,110 |-INFO in ch.qos.logback.classic.util.ContextInitializer@72c8e7b -   org.springframework.boot.logging.logback.RootLogLevelConfigurator
21:26:58,110 |-INFO in ch.qos.logback.classic.util.ContextInitializer@72c8e7b - They will be invoked in order until ExecutionStatus.DO_NOT_INVOKE_NEXT_IF_ANY is returned.
21:26:58,110 |-INFO in ch.qos.logback.classic.util.ContextInitializer@72c8e7b - Constructed configurator of type class org.springframework.boot.logging.logback.RootLogLevelConfigurator
21:26:58,118 |-INFO in ch.qos.logback.classic.util.ContextInitializer@72c8e7b - org.springframework.boot.logging.logback.RootLogLevelConfigurator.configure() call lasted 1 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
21:26:58,118 |-INFO in ch.qos.logback.classic.util.ContextInitializer@72c8e7b - Trying to configure with ch.qos.logback.classic.joran.SerializedModelConfigurator
21:26:58,119 |-INFO in ch.qos.logback.classic.util.ContextInitializer@72c8e7b - Constructed configurator of type class ch.qos.logback.classic.joran.SerializedModelConfigurator
21:26:58,120 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.scmo]
21:26:58,120 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback.scmo]
21:26:58,120 |-INFO in ch.qos.logback.classic.util.ContextInitializer@72c8e7b - ch.qos.logback.classic.joran.SerializedModelConfigurator.configure() call lasted 1 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
21:26:58,120 |-INFO in ch.qos.logback.classic.util.ContextInitializer@72c8e7b - Trying to configure with ch.qos.logback.classic.util.DefaultJoranConfigurator
21:26:58,121 |-INFO in ch.qos.logback.classic.util.ContextInitializer@72c8e7b - Constructed configurator of type class ch.qos.logback.classic.util.DefaultJoranConfigurator
21:26:58,122 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-test.xml] at [file:/media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/test-classes/logback-test.xml]
21:26:58,664 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [CONSOLE]
21:26:58,664 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
21:26:58,672 |-INFO in ch.qos.logback.core.model.processor.ImplicitModelHandler - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
21:26:58,751 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - BEWARE: Writing to the console can be very slow. Avoid logging to the 
21:26:58,751 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - console in production environments, especially in high volume systems.
21:26:58,751 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - See also https://logback.qos.ch/codes.html#slowConsole
21:26:58,751 |-INFO in ch.qos.logback.classic.model.processor.RootLoggerModelHandler - Setting level of ROOT logger to INFO
21:26:58,752 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[ROOT]
21:26:58,753 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [org.xkong.cloud.commons.uid] to DEBUG
21:26:58,753 |-INFO in ch.qos.logback.core.model.processor.DefaultProcessor@65f8f5ae - End of configuration.
21:26:58,753 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@431cd9b2 - Registering current configuration as safe fallback point
21:26:58,753 |-INFO in ch.qos.logback.classic.util.ContextInitializer@72c8e7b - ch.qos.logback.classic.util.DefaultJoranConfigurator.configure() call lasted 632 milliseconds. ExecutionStatus=DO_NOT_INVOKE_NEXT_IF_ANY

2025-05-23 21:27:00.265 [main] INFO  o.x.c.c.u.f.MachineFingerprints - 使用OSHI库成功获取系统信息
2025-05-23 21:27:03.363 [main] INFO  o.x.c.c.u.f.MachineFingerprints - 已收集机器特征码: {"os_arch":"amd64","os_name":"Linux","fingerprint_hash":"0cd7e71f226e917911f5e1251a5665812c4065753531534bd12cbdfeaab5c60d","hostname":"long-VirtualBox","mac_addresses":["CE:A****:73:FD","08:0****:3A:87"],"os_version":"5.4.0-91-generic"}
Mockito is currently self-attaching to enable the inline-mock-maker. This will no longer work in future releases of the JDK. Please add Mockito as an agent to your build what is described in Mockito's documentation: https://javadoc.io/doc/org.mockito/mockito-core/latest/org/mockito/Mockito.html#0.3
WARNING: A Java agent has been loaded dynamically (/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
Java HotSpot(TM) 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classpath has been appended
2025-05-23 21:27:05.926 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 21:27:05.991 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 21:27:06.002 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 21:27:06.272 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 21:27:06.273 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 21:27:06.280 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 21:27:06.286 [main] DEBUG o.x.c.c.u.c.KeyManagementService - 使用NativePRNGNonBlocking算法的SecureRandom
2025-05-23 21:27:06.289 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已为应用 test-app 环境 test-env 创建新的 test-key-type 类型密钥
2025-05-23 21:27:06.298 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 21:27:06.306 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 21:27:06.310 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-23 21:27:06.312 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已清除密钥缓存
2025-05-23 21:27:06.336 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-23 21:27:06.345 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-23 21:27:06.348 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-23 21:27:06.349 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-23 21:27:06.353 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-23 21:27:06.353 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-23 21:27:06.354 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-23 21:27:06.354 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-23 21:27:06.557 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-23 21:27:06.558 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-23 21:27:06.560 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-23 21:27:06.560 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-23 21:27:06.560 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-23 21:27:06.561 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-23 21:27:06.561 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 已清除所有验证缓存
2025-05-23 21:27:06.642 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 21:27:06.642 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 21:27:06.642 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 21:27:06.643 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 21:27:06.643 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:06.644 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-23 21:27:06.644 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-23 21:27:06.644 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 21:27:06.646 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: main
2025-05-23 21:27:06.646 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: main
2025-05-23 21:27:06.647 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: main
2025-05-23 21:27:06.647 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: main
2025-05-23 21:27:06.647 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: main
2025-05-23 21:27:06.647 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: main
2025-05-23 21:27:06.647 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: main
2025-05-23 21:27:06.650 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:06.706 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 21:27:06.706 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 21:27:06.707 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 21:27:06.707 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 21:27:06.707 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:06.708 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-23 21:27:06.716 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-23 21:27:06.716 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: main
2025-05-23 21:27:06.716 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-23 21:27:06.716 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 21:27:06.730 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 21:27:06.730 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 21:27:06.730 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 21:27:06.730 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 21:27:06.730 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:06.731 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-23 21:27:06.731 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 无法为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 21:27:06.731 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 21:27:06.731 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取工作机器ID时发生异常，当前线程: main, 异常: 无法为实例 12345 分配工作机器ID
java.lang.IllegalStateException: 无法为实例 12345 分配工作机器ID
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.getWorkerId(PersistentInstanceWorkerIdAssigner.java:166)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.lambda$testGetWorkerId_MaxReached$2(PersistentInstanceWorkerIdAssignerTest.java:248)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3128)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.testGetWorkerId_MaxReached(PersistentInstanceWorkerIdAssignerTest.java:248)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:767)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$8(TestMethodTestDescriptor.java:217)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:213)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:138)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.SessionPerRequestLauncher.execute(SessionPerRequestLauncher.java:63)
	at org.xkong.cloud.commons.uid.TestRunner.runTests(TestRunner.java:93)
	at org.xkong.cloud.commons.uid.TestRunner.runUnitTests(TestRunner.java:55)
	at org.xkong.cloud.commons.uid.TestRunner.main(TestRunner.java:28)
2025-05-23 21:27:06.744 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 21:27:06.744 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 21:27:06.744 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 21:27:06.744 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 21:27:06.744 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:06.745 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-23 21:27:06.747 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 插入新的工作机器ID失败，可能存在并发冲突，工作机器ID: 42
2025-05-23 21:27:06.748 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，实例ID: null
2025-05-23 21:27:06.749 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 无法为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 21:27:06.749 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 21:27:06.749 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取工作机器ID时发生异常，当前线程: main, 异常: 无法为实例 12345 分配工作机器ID
java.lang.IllegalStateException: 无法为实例 12345 分配工作机器ID
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.getWorkerId(PersistentInstanceWorkerIdAssigner.java:166)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.testGetWorkerId_AllocateNew(PersistentInstanceWorkerIdAssignerTest.java:185)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:767)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$8(TestMethodTestDescriptor.java:217)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:213)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:138)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.SessionPerRequestLauncher.execute(SessionPerRequestLauncher.java:63)
	at org.xkong.cloud.commons.uid.TestRunner.runTests(TestRunner.java:93)
	at org.xkong.cloud.commons.uid.TestRunner.runUnitTests(TestRunner.java:55)
	at org.xkong.cloud.commons.uid.TestRunner.main(TestRunner.java:28)
2025-05-23 21:27:06.757 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: main
2025-05-23 21:27:06.758 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: false -> false，当前线程: main
2025-05-23 21:27:06.758 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: main
2025-05-23 21:27:06.758 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: main
2025-05-23 21:27:06.758 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 21:27:06.758 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 21:27:06.758 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 21:27:06.758 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 21:27:06.758 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:06.759 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-23 21:27:06.759 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-23 21:27:06.759 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 21:27:06.759 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: main
2025-05-23 21:27:06.767 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 21:27:06.767 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 21:27:06.767 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 21:27:06.768 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 21:27:06.768 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:06.768 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-23 21:27:06.768 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-23 21:27:06.768 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 21:27:06.769 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 21:27:06.771 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 21:27:06.777 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 21:27:06.777 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 21:27:06.777 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 21:27:06.778 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 21:27:06.778 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:06.778 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-23 21:27:06.778 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-23 21:27:06.778 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 21:27:06.791 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 21:27:06.791 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 21:27:06.791 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 21:27:06.791 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 21:27:06.791 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:06.792 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-23 21:27:06.792 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-23 21:27:06.792 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 21:27:06.818 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放成功
2025-05-23 21:27:06.825 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 21:27:06.826 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 21:27:06.826 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 21:27:06.826 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-23 21:27:06.826 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:06.826 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-23 21:27:06.826 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-23 21:27:06.827 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 21:27:06.827 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: main
2025-05-23 21:27:06.827 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: main
2025-05-23 21:27:06.828 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: main
2025-05-23 21:27:06.828 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: main
2025-05-23 21:27:06.828 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: main
2025-05-23 21:27:06.828 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: main
2025-05-23 21:27:06.828 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: main
2025-05-23 21:27:06.838 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:07.032 [main] INFO  o.testcontainers.images.PullPolicy - Image pull policy will be performed by: DefaultPullPolicy()
2025-05-23 21:27:07.088 [main] INFO  o.t.utility.ImageNameSubstitutor - Image name substitution will be performed by: DefaultImageNameSubstitutor (composite of 'ConfigurationFileImageNameSubstitutor' and 'PrefixingImageNameSubstitutor')
2025-05-23 21:27:07.173 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:07.173 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:27:07.173 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:27:07.173 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:07.176 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:07.176 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:07.176 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:07.179 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:07.179 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:07.180 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:27:07.180 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:27:07.180 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:27:07.345 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:07.345 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:27:07.345 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:27:07.345 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:07.345 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:07.345 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:07.345 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:07.356 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:07.356 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:07.356 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:27:07.357 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:27:07.361 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:27:07.715 [main] INFO  o.t.d.DockerClientProviderStrategy - Loaded org.testcontainers.dockerclient.UnixSocketClientProviderStrategy from ~/.testcontainers.properties, will try it first
2025-05-23 21:27:08.865 [main] INFO  o.t.d.DockerClientProviderStrategy - Found Docker environment with local Unix socket (unix:///var/run/docker.sock)
2025-05-23 21:27:08.867 [main] INFO  o.testcontainers.DockerClientFactory - Docker host IP address is localhost
2025-05-23 21:27:08.890 [main] INFO  o.testcontainers.DockerClientFactory - Connected to docker: 
  Server Version: 28.1.1
  API Version: 1.49
  Operating System: Linux Mint 20.3
  Total Memory: 7960 MB
2025-05-23 21:27:09.014 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Creating container for image: testcontainers/ryuk:0.6.0
2025-05-23 21:27:09.021 [main] INFO  o.t.utility.RegistryAuthLocator - Failure when attempting to lookup auth config. Please ignore if you don't have images in an authenticated registry. Details: (dockerImageName: testcontainers/ryuk:0.6.0, configFile: /home/<USER>/.docker/config.json, configEnv: DOCKER_AUTH_CONFIG). Falling back to docker-java default behaviour. Exception message: Status 404: No config supplied. Checked in order: /home/<USER>/.docker/config.json (file not found), DOCKER_AUTH_CONFIG (not set)
2025-05-23 21:27:11.251 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Container testcontainers/ryuk:0.6.0 is starting: b0ebf83b020a673626c6b2f8b2cf6a7db990a38cdd0386fddc813236aae74983
2025-05-23 21:27:15.825 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Container testcontainers/ryuk:0.6.0 started in PT6.810653275S
2025-05-23 21:27:15.831 [main] INFO  o.t.utility.RyukResourceReaper - Ryuk started - will monitor and terminate Testcontainers containers on JVM exit
2025-05-23 21:27:15.832 [main] INFO  o.testcontainers.DockerClientFactory - Checking the system...
2025-05-23 21:27:15.832 [main] INFO  o.testcontainers.DockerClientFactory - ✔︎ Docker server version should be at least 1.6.0
2025-05-23 21:27:15.832 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-23 21:27:16.802 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: f123e2ca52383d69408ce188ad1dbddbeb53d11615342d834624b99d547538d7
2025-05-23 21:27:26.638 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.641 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.642 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.642 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.642 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.642 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.642 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.642 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:26.701 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT10.868635048S
2025-05-23 21:27:26.702 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-23 21:27:26.704 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.705 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.705 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.705 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.705 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.705 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.705 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.706 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:26.720 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-23 21:27:26.724 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.740 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.758 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.759 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.759 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.759 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.759 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.759 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.759 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.760 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:26.776 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.777 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.777 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.777 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.777 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.777 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.777 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.778 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:26.823 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.824 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.824 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.825 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.825 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.825 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.825 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:27:26.826 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.039 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6003220a
2025-05-23 21:27:27.041 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-23 21:27:27.043 [main] INFO  o.x.c.c.u.test.PostgresTestContainer - 正在执行数据库初始化脚本: init-schema.sql
2025-05-23 21:27:27.094 [main] INFO  o.x.c.c.u.test.PostgresTestContainer - 数据库初始化脚本执行完成
2025-05-23 21:27:27.103 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-05-23 21:27:27.144 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.144 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:27:27.144 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:27:27.144 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.144 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.144 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.144 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.150 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.150 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:27.150 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:27:27.150 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.151 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:27:27.164 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@7ea3839e
2025-05-23 21:27:27.164 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-05-23 21:27:27.167 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 构建UidGeneratorFacade
2025-05-23 21:27:27.171 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Starting...
2025-05-23 21:27:27.197 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-3 - Added connection org.postgresql.jdbc.PgConnection@699d96bc
2025-05-23 21:27:27.197 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Start completed.
2025-05-23 21:27:27.197 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 为测试环境构建UidGeneratorFacade
2025-05-23 21:27:27.198 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 构建UidGeneratorFacade
2025-05-23 21:27:27.199 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 初始化UidGeneratorFacade
2025-05-23 21:27:27.199 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建JdbcTemplate
2025-05-23 21:27:27.199 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建TransactionTemplate
2025-05-23 21:27:27.205 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建DatabaseMetadataService
2025-05-23 21:27:27.206 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.206 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:27:27.206 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:27:27.206 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.206 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.206 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.206 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.207 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.207 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:27.207 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.208 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-23 21:27:27.208 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:27:27.208 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.208 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:27:27.213 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:27:27.213 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidValidationService
2025-05-23 21:27:27.214 [main] INFO  o.x.c.c.u.config.UidValidationConfig - 创建UidValidationService实例
2025-05-23 21:27:27.216 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidTableManager
2025-05-23 21:27:27.216 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:27:27.262 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.263 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:27:27.263 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:27:27.263 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.263 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.263 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.263 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.264 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.264 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:27.264 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:27:27.264 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.264 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:27:27.280 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.280 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:27:27.281 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:27:27.281 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.281 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.281 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.281 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.284 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.284 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:27.284 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:27:27.284 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.284 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:27:27.324 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:27:27.324 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:27:27.330 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.330 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:27:27.330 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:27:27.330 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.330 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.330 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.330 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.331 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:27.331 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:27.331 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:27:27.331 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:27:27.331 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:27:27.357 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:27:27.358 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:27.363 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 21:27:27.363 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:27.366 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 21:27:27.366 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 21:27:27.380 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 21:27:27.380 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:27:27.380 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:27.382 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 21:27:27.382 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:27.384 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 21:27:27.384 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 21:27:27.388 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 21:27:27.389 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:27:27.389 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:27.391 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-23 21:27:27.406 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:27:27.406 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-23 21:27:27.406 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:27:27.406 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceManager
2025-05-23 21:27:27.408 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:27:27.408 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:27:27.408 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: test-app, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:27:27.435 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 1
2025-05-23 21:27:27.444 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 1 在数据库中不存在
2025-05-23 21:27:27.444 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 21:27:27.452 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 21:27:27.452 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 21:27:27.475 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 1
2025-05-23 21:27:27.484 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/test-instance-id.dat
2025-05-23 21:27:27.484 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 21:27:27.484 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceWorkerIdAssigner
2025-05-23 21:27:27.487 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建CachedUidGenerator
2025-05-23 21:27:27.512 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade初始化完成
2025-05-23 21:27:27.512 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 关闭UidGeneratorFacade资源
2025-05-23 21:27:27.517 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade资源已关闭
2025-05-23 21:27:27.519 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Starting...
2025-05-23 21:27:27.551 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-4 - Added connection org.postgresql.jdbc.PgConnection@298e002d
2025-05-23 21:27:27.552 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Start completed.
2025-05-23 21:27:27.553 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 为测试环境构建UidGeneratorFacade
2025-05-23 21:27:27.553 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 构建UidGeneratorFacade
2025-05-23 21:27:27.553 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 初始化UidGeneratorFacade
2025-05-23 21:27:27.553 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建JdbcTemplate
2025-05-23 21:27:27.553 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建TransactionTemplate
2025-05-23 21:27:27.553 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建DatabaseMetadataService
2025-05-23 21:27:27.554 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:27:27.554 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidValidationService
2025-05-23 21:27:27.554 [main] INFO  o.x.c.c.u.config.UidValidationConfig - 创建UidValidationService实例
2025-05-23 21:27:27.554 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidTableManager
2025-05-23 21:27:27.554 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:27:27.556 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:27:27.556 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:27:27.561 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:27:27.562 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:27.573 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 21:27:27.573 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:27.588 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 21:27:27.588 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 21:27:27.599 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 21:27:27.599 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:27:27.599 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:27.602 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 21:27:27.602 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:27.605 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 21:27:27.605 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 21:27:27.609 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 21:27:27.610 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:27:27.610 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:27.613 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 21:27:27.613 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:27.615 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 21:27:27.616 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 21:27:27.619 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 21:27:27.620 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:27:27.620 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:27:27.620 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceManager
2025-05-23 21:27:27.620 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:27:27.620 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:27:27.620 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: test-app, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:27:27.621 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 1
2025-05-23 21:27:27.623 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 1 在数据库中存在，更新实例信息
2025-05-23 21:27:27.625 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 1 的最后活动时间
2025-05-23 21:27:27.625 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 21:27:27.625 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceWorkerIdAssigner
2025-05-23 21:27:27.626 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建CachedUidGenerator
2025-05-23 21:27:27.626 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade初始化完成
2025-05-23 21:27:27.628 [main] ERROR c.xfvape.uid.impl.CachedUidGenerator - Generate unique id exception. 
java.lang.NullPointerException: Cannot invoke "com.xfvape.uid.buffer.RingBuffer.take()" because "this.ringBuffer" is null
	at com.xfvape.uid.impl.CachedUidGenerator.getUID(CachedUidGenerator.java:80)
	at org.xkong.cloud.commons.uid.facade.UidGeneratorFacade.getUID(UidGeneratorFacade.java:89)
	at org.xkong.cloud.commons.uid.facade.UidGeneratorFacadeTest.testBuildForTest(UidGeneratorFacadeTest.java:125)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:767)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$8(TestMethodTestDescriptor.java:217)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:213)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:138)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.SessionPerRequestLauncher.execute(SessionPerRequestLauncher.java:63)
	at org.xkong.cloud.commons.uid.TestRunner.runTests(TestRunner.java:93)
	at org.xkong.cloud.commons.uid.TestRunner.runUnitTests(TestRunner.java:55)
	at org.xkong.cloud.commons.uid.TestRunner.main(TestRunner.java:28)
2025-05-23 21:27:27.629 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 关闭UidGeneratorFacade资源
2025-05-23 21:27:27.630 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade资源已关闭
2025-05-23 21:27:27.633 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-5 - Starting...
2025-05-23 21:27:27.653 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-5 - Added connection org.postgresql.jdbc.PgConnection@28fa541
2025-05-23 21:27:27.654 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-5 - Start completed.
2025-05-23 21:27:27.655 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 构建UidGeneratorFacade
2025-05-23 21:27:27.656 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-6 - Starting...
2025-05-23 21:27:27.722 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-6 - Added connection org.postgresql.jdbc.PgConnection@65af05b2
2025-05-23 21:27:27.722 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-6 - Start completed.
2025-05-23 21:27:27.723 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 构建UidGeneratorFacade
2025-05-23 21:27:27.723 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 初始化UidGeneratorFacade
2025-05-23 21:27:27.723 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建JdbcTemplate
2025-05-23 21:27:27.723 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建TransactionTemplate
2025-05-23 21:27:27.723 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建DatabaseMetadataService
2025-05-23 21:27:27.723 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:27:27.723 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidValidationService
2025-05-23 21:27:27.723 [main] INFO  o.x.c.c.u.config.UidValidationConfig - 创建UidValidationService实例
2025-05-23 21:27:27.723 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidTableManager
2025-05-23 21:27:27.723 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:27:27.724 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:27:27.724 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:27:27.727 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:27:27.727 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:27.732 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 21:27:27.732 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:27.735 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 21:27:27.736 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 21:27:27.751 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 21:27:27.751 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:27:27.751 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:27.766 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 21:27:27.767 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:27.798 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 21:27:27.798 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 21:27:27.804 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 21:27:27.805 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:27:27.805 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:27.810 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 21:27:27.810 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:27.814 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 21:27:27.814 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 21:27:27.818 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 21:27:27.819 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:27:27.819 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:27:27.819 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceManager
2025-05-23 21:27:27.819 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:27:27.819 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:27:27.819 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: test-app, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:27:27.820 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 1
2025-05-23 21:27:27.822 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 1 在数据库中存在，更新实例信息
2025-05-23 21:27:27.823 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 1 的最后活动时间
2025-05-23 21:27:27.823 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 21:27:27.823 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceWorkerIdAssigner
2025-05-23 21:27:27.824 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建CachedUidGenerator
2025-05-23 21:27:27.824 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade初始化完成
2025-05-23 21:27:27.824 [main] ERROR c.xfvape.uid.impl.CachedUidGenerator - Generate unique id exception. 
java.lang.NullPointerException: Cannot invoke "com.xfvape.uid.buffer.RingBuffer.take()" because "this.ringBuffer" is null
	at com.xfvape.uid.impl.CachedUidGenerator.getUID(CachedUidGenerator.java:80)
	at org.xkong.cloud.commons.uid.facade.UidGeneratorFacade.getUID(UidGeneratorFacade.java:89)
	at org.xkong.cloud.commons.uid.facade.UidGeneratorFacadeTest.testBuildAndGetUID(UidGeneratorFacadeTest.java:101)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:767)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$8(TestMethodTestDescriptor.java:217)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:213)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:138)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.SessionPerRequestLauncher.execute(SessionPerRequestLauncher.java:63)
	at org.xkong.cloud.commons.uid.TestRunner.runTests(TestRunner.java:93)
	at org.xkong.cloud.commons.uid.TestRunner.runUnitTests(TestRunner.java:55)
	at org.xkong.cloud.commons.uid.TestRunner.main(TestRunner.java:28)
2025-05-23 21:27:27.825 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 关闭UidGeneratorFacade资源
2025-05-23 21:27:27.825 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade资源已关闭

Test run finished after 29939 ms
[         6 containers found      ]
[         0 containers skipped    ]
[         6 containers started    ]
[         0 containers aborted    ]
[         6 containers successful ]
[         0 containers failed     ]
[        30 tests found           ]
[         0 tests skipped         ]
[        30 tests started         ]
[         0 tests aborted         ]
[        25 tests successful      ]
[         5 tests failed          ]


===== 运行集成测试 =====
2025-05-23 21:27:27.873 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-23 21:27:29.904 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 70cce421c20efd2757644862a236baf9ddabc412cf857980a431a65f84e83c51
2025-05-23 21:27:35.903 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT8.030030827S
2025-05-23 21:27:35.903 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-23 21:27:35.910 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:27:35.910 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-7 - Starting...
2025-05-23 21:27:35.974 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-7 - Added connection org.postgresql.jdbc.PgConnection@18dac12f
2025-05-23 21:27:35.974 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-7 - Start completed.
2025-05-23 21:27:35.979 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:27:35.980 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:27:35.980 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:27:35.981 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:27:35.982 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:35.985 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-23 21:27:35.997 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:27:35.997 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-23 21:27:35.997 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:35.999 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-23 21:27:36.011 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:27:36.011 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-23 21:27:36.011 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:36.015 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-23 21:27:36.023 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:27:36.023 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-23 21:27:36.023 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:27:38.145 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-23 21:27:38.666 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 185f5e5e1cd94504e33746cb9259fc79c059f0a7c650e77f074d68fe28d15698
2025-05-23 21:27:44.466 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT6.320421544S
2025-05-23 21:27:44.466 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: jdbc:postgresql://localhost:32807/test?loggerLevel=OFF)
2025-05-23 21:27:44.488 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-8 - Starting...
2025-05-23 21:27:44.509 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-8 - Added connection org.postgresql.jdbc.PgConnection@38dbeb39
2025-05-23 21:27:44.509 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-8 - Start completed.
2025-05-23 21:27:44.509 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:27:44.509 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:27:44.510 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:27:44.510 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:27:44.513 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 不存在，正在自动创建
2025-05-23 21:27:44.513 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 创建Schema（如果不存在）: infra_uid
2025-05-23 21:27:44.515 [main] DEBUG o.x.c.c.u.m.s.JdbcMetadataStrategy - 执行SQL: CREATE SCHEMA IF NOT EXISTS "infra_uid"
2025-05-23 21:27:44.516 [main] INFO  o.x.c.c.u.m.s.JdbcMetadataStrategy - 已创建Schema: infra_uid
2025-05-23 21:27:44.516 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 已成功创建
2025-05-23 21:27:44.516 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:27:44.516 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:44.520 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-23 21:27:44.542 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:27:44.542 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-23 21:27:44.542 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:44.546 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-23 21:27:44.560 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:27:44.560 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-23 21:27:44.560 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:44.562 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-23 21:27:44.571 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:27:44.571 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-23 21:27:44.571 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:27:44.571 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:27:44.571 [main] ERROR o.x.c.c.u.i.PersistentInstanceManagerBuilder - 构建失败: recoveryEnabled不能为空
2025-05-23 21:27:44.589 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-9 - Starting...
2025-05-23 21:27:44.613 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-9 - Added connection org.postgresql.jdbc.PgConnection@1073c664
2025-05-23 21:27:44.613 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-9 - Start completed.
2025-05-23 21:27:44.613 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:27:44.613 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:27:44.614 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:27:44.614 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:27:44.616 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:27:44.617 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:44.620 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 21:27:44.620 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:44.622 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 21:27:44.622 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 21:27:44.630 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 21:27:44.631 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:27:44.631 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:44.632 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 21:27:44.633 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:44.634 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 21:27:44.634 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 21:27:44.637 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 21:27:44.638 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:27:44.638 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:44.639 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 21:27:44.639 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:44.641 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 21:27:44.641 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 21:27:44.643 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 21:27:44.643 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:27:44.643 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:27:44.644 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:27:44.644 [main] ERROR o.x.c.c.u.i.PersistentInstanceManagerBuilder - 构建失败: localStoragePath不能为空
2025-05-23 21:27:44.648 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-10 - Starting...
2025-05-23 21:27:44.664 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-10 - Added connection org.postgresql.jdbc.PgConnection@24fc2c80
2025-05-23 21:27:44.664 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-10 - Start completed.
2025-05-23 21:27:44.664 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:27:44.664 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:27:44.665 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:27:44.666 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:27:44.667 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:27:44.667 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:44.671 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 21:27:44.671 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:44.673 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 21:27:44.673 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 21:27:44.680 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 21:27:44.680 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:27:44.680 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:44.682 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 21:27:44.682 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:44.685 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 21:27:44.685 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 21:27:44.692 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 21:27:44.693 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:27:44.693 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:44.695 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 21:27:44.696 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:44.698 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 21:27:44.698 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 21:27:44.706 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 21:27:44.706 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:27:44.706 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:27:44.707 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:27:44.707 [main] ERROR o.x.c.c.u.i.PersistentInstanceManagerBuilder - 构建失败: localStoragePath不能为空
2025-05-23 21:27:46.640 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.641 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.642 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.642 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.642 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.642 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.642 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.643 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:46.704 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.705 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.705 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.705 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.705 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.705 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.706 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.706 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:46.724 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.740 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.757 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.757 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.758 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.758 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.758 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.758 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.758 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.759 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:46.777 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.778 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.778 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.778 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.778 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.779 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.779 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.779 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:46.828 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.829 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.830 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.830 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.830 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.830 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.830 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:27:46.831 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.014 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-23 21:27:47.145 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.145 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:27:47.145 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:27:47.145 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.145 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.145 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.145 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.148 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.148 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:47.148 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:27:47.148 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.148 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:27:47.207 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.208 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:27:47.208 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:27:47.208 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.208 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.208 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.208 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.209 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.209 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:47.209 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.210 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-23 21:27:47.210 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:27:47.210 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.210 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:27:47.259 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.259 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:27:47.259 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:27:47.259 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.259 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.259 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.260 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.261 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.261 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:47.262 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:27:47.262 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.262 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:27:47.280 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.280 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:27:47.280 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:27:47.280 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.280 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.280 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.280 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.281 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.281 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:47.281 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:27:47.281 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.281 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:27:47.331 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.331 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:27:47.332 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:27:47.332 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.332 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.332 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.332 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.332 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:27:47.333 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:27:47.333 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:27:47.333 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:27:47.333 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:27:47.474 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: ace9ccd459016395e0bb1d18896dd21e236a54694d3e2490ea2b0e12c8e0fe56
2025-05-23 21:27:53.370 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT6.355670191S
2025-05-23 21:27:53.370 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-23 21:27:53.372 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-11 - Starting...
2025-05-23 21:27:53.392 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-11 - Added connection org.postgresql.jdbc.PgConnection@671da0f9
2025-05-23 21:27:53.393 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-11 - Start completed.
2025-05-23 21:27:53.393 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:27:53.393 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:27:53.393 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:27:53.393 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:27:53.396 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 不存在，正在自动创建
2025-05-23 21:27:53.396 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 创建Schema（如果不存在）: infra_uid
2025-05-23 21:27:53.397 [main] DEBUG o.x.c.c.u.m.s.JdbcMetadataStrategy - 执行SQL: CREATE SCHEMA IF NOT EXISTS "infra_uid"
2025-05-23 21:27:53.397 [main] INFO  o.x.c.c.u.m.s.JdbcMetadataStrategy - 已创建Schema: infra_uid
2025-05-23 21:27:53.397 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 已成功创建
2025-05-23 21:27:53.398 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:27:53.398 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:53.401 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-23 21:27:53.418 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:27:53.418 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-23 21:27:53.418 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:53.420 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-23 21:27:53.428 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:27:53.428 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-23 21:27:53.428 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:53.429 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-23 21:27:53.436 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:27:53.436 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-23 21:27:53.436 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:27:53.442 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件清理关闭钩子
2025-05-23 21:27:53.442 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/expiry-test9213973074012213285, 描述: 创建的临时目录
2025-05-23 21:27:53.442 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/expiry-test-213489151517551939353, 描述: 创建的临时目录
2025-05-23 21:27:53.442 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:27:53.442 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:27:53.442 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: expiry-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:27:53.442 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/expiry-test9213973074012213285/instance-id
2025-05-23 21:27:53.442 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 21:27:53.444 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 21:27:53.444 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 21:27:53.446 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 1
2025-05-23 21:27:53.447 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/expiry-test9213973074012213285/instance-id
2025-05-23 21:27:53.447 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 21:27:53.450 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-12 - Starting...
2025-05-23 21:27:53.469 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-12 - Added connection org.postgresql.jdbc.PgConnection@52856ff9
2025-05-23 21:27:53.469 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-12 - Start completed.
2025-05-23 21:27:53.469 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:27:53.469 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:27:53.471 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:27:53.471 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:27:53.472 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:27:53.472 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:53.475 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 21:27:53.475 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:53.477 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 21:27:53.477 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 21:27:53.486 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 21:27:53.486 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:27:53.486 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:53.488 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 21:27:53.488 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:53.490 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 21:27:53.490 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 21:27:53.493 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 21:27:53.494 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:27:53.494 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:53.496 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 21:27:53.496 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:53.499 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 21:27:53.499 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 21:27:53.503 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 21:27:53.503 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:27:53.503 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:27:53.504 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/lease-test11391745251932296710, 描述: 创建的临时目录
2025-05-23 21:27:53.504 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:27:53.504 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:27:53.504 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: lease-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:27:53.504 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/lease-test11391745251932296710/instance-id
2025-05-23 21:27:53.504 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 21:27:53.509 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 21:27:53.509 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 21:27:53.510 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 2
2025-05-23 21:27:53.511 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/lease-test11391745251932296710/instance-id
2025-05-23 21:27:53.512 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 21:27:53.512 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 21:27:53.512 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 21:27:53.512 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 21:27:53.512 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 2 分配工作机器ID，当前线程: main
2025-05-23 21:27:53.512 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 2 是否已分配工作机器ID
2025-05-23 21:27:53.513 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-23 21:27:53.518 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 0，实例ID: 2
2025-05-23 21:27:53.519 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 2 分配了新的工作机器ID: 0，当前线程: main
2025-05-23 21:27:53.519 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-23 21:27:53.519 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 21:27:54.513 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: worker-id-lease-renewal
2025-05-23 21:27:54.513 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 2，schemaName: infra_uid，当前线程: worker-id-lease-renewal
2025-05-23 21:27:54.515 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:27:54.517 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '5 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 2]，当前线程: worker-id-lease-renewal
2025-05-23 21:27:54.520 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: worker-id-lease-renewal
2025-05-23 21:27:54.524 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: worker-id-lease-renewal
2025-05-23 21:27:54.534 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-23 21:27:54.537 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-13 - Starting...
2025-05-23 21:27:54.557 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-13 - Added connection org.postgresql.jdbc.PgConnection@3cdfbbef
2025-05-23 21:27:54.557 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-13 - Start completed.
2025-05-23 21:27:54.558 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:27:54.558 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:27:54.559 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:27:54.559 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:27:54.561 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:27:54.561 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:54.565 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 21:27:54.565 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:54.568 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 21:27:54.569 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 21:27:54.586 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 21:27:54.586 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:27:54.586 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:54.591 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 21:27:54.591 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:54.593 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 21:27:54.593 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 21:27:54.597 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 21:27:54.597 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:27:54.598 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:54.601 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 21:27:54.601 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:54.603 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 21:27:54.603 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 21:27:54.607 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 21:27:54.607 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:27:54.607 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:27:54.608 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test6388693642183751878, 描述: 创建的临时目录
2025-05-23 21:27:54.608 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:27:54.608 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:27:54.608 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: worker-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:27:54.608 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test6388693642183751878/instance-id
2025-05-23 21:27:54.608 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 21:27:54.610 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 21:27:54.611 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 21:27:54.612 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 3
2025-05-23 21:27:54.613 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test6388693642183751878/instance-id
2025-05-23 21:27:54.613 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 21:27:54.613 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 21:27:54.613 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 21:27:54.613 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 21:27:54.613 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 3 分配工作机器ID，当前线程: main
2025-05-23 21:27:54.613 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 3 是否已分配工作机器ID
2025-05-23 21:27:54.615 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-23 21:27:54.620 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 3
2025-05-23 21:27:54.621 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 3 分配了新的工作机器ID: 0，当前线程: main
2025-05-23 21:27:54.621 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-23 21:27:54.621 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 21:27:54.623 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-23 21:27:54.625 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-14 - Starting...
2025-05-23 21:27:54.652 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-14 - Added connection org.postgresql.jdbc.PgConnection@3e39f08c
2025-05-23 21:27:54.652 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-14 - Start completed.
2025-05-23 21:27:54.652 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:27:54.652 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:27:54.653 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:27:54.653 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:27:54.655 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:27:54.655 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:54.658 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 21:27:54.658 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:27:54.660 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 21:27:54.664 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 21:27:54.671 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 21:27:54.671 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:27:54.671 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:54.673 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 21:27:54.673 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:27:54.675 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 21:27:54.675 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 21:27:54.679 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 21:27:54.679 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:27:54.679 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:54.681 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 21:27:54.681 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:27:54.683 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 21:27:54.684 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 21:27:54.687 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 21:27:54.687 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:27:54.687 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:27:54.688 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test17446115794588549627, 描述: 创建的临时目录
2025-05-23 21:27:54.688 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test17446115794588549627, 描述: 并发测试基础目录
2025-05-23 21:27:54.690 [pool-1-thread-2] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test17446115794588549627/instance-id-1, 描述: 并发测试实例 1 的ID文件
2025-05-23 21:27:54.690 [pool-1-thread-1] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test17446115794588549627/instance-id-0, 描述: 并发测试实例 0 的ID文件
2025-05-23 21:27:54.690 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:27:54.690 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:27:54.690 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:27:54.690 [pool-1-thread-2] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-1, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:27:54.690 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:27:54.691 [pool-1-thread-1] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-0, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:27:54.691 [pool-1-thread-3] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test17446115794588549627/instance-id-2, 描述: 并发测试实例 2 的ID文件
2025-05-23 21:27:54.691 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:27:54.691 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:27:54.691 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test17446115794588549627/instance-id-1
2025-05-23 21:27:54.691 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test17446115794588549627/instance-id-0
2025-05-23 21:27:54.691 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 21:27:54.691 [pool-1-thread-3] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-2, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:27:54.691 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 21:27:54.691 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test17446115794588549627/instance-id-2
2025-05-23 21:27:54.691 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 21:27:54.691 [pool-1-thread-5] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test17446115794588549627/instance-id-4, 描述: 并发测试实例 4 的ID文件
2025-05-23 21:27:54.691 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:27:54.691 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:27:54.691 [pool-1-thread-5] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-4, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:27:54.691 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test17446115794588549627/instance-id-4
2025-05-23 21:27:54.691 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 21:27:54.691 [pool-1-thread-4] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test17446115794588549627/instance-id-3, 描述: 并发测试实例 3 的ID文件
2025-05-23 21:27:54.692 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:27:54.692 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:27:54.692 [pool-1-thread-4] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: concurrent-test-3, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:27:54.692 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test17446115794588549627/instance-id-3
2025-05-23 21:27:54.692 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 21:27:54.696 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 21:27:54.697 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 21:27:54.698 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 21:27:54.698 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 21:27:54.703 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 4
2025-05-23 21:27:54.705 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 21:27:54.705 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 21:27:54.706 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 5
2025-05-23 21:27:54.707 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test17446115794588549627/instance-id-3
2025-05-23 21:27:54.707 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 21:27:54.708 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-4
2025-05-23 21:27:54.708 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-4
2025-05-23 21:27:54.708 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-4
2025-05-23 21:27:54.708 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 4 分配工作机器ID，当前线程: pool-1-thread-4
2025-05-23 21:27:54.708 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 4 是否已分配工作机器ID
2025-05-23 21:27:54.709 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test17446115794588549627/instance-id-0
2025-05-23 21:27:54.709 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 21:27:54.709 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-1
2025-05-23 21:27:54.709 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-1
2025-05-23 21:27:54.709 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-1
2025-05-23 21:27:54.709 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 5 分配工作机器ID，当前线程: pool-1-thread-1
2025-05-23 21:27:54.709 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 5 是否已分配工作机器ID
2025-05-23 21:27:54.710 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 21:27:54.710 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 21:27:54.712 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 21:27:54.712 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 6
2025-05-23 21:27:54.712 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 21:27:54.713 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test17446115794588549627/instance-id-4
2025-05-23 21:27:54.713 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 21:27:54.718 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 7
2025-05-23 21:27:54.719 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-1
2025-05-23 21:27:54.719 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-5
2025-05-23 21:27:54.719 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-5
2025-05-23 21:27:54.719 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-5
2025-05-23 21:27:54.719 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 6 分配工作机器ID，当前线程: pool-1-thread-5
2025-05-23 21:27:54.719 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 6 是否已分配工作机器ID
2025-05-23 21:27:54.722 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test17446115794588549627/instance-id-1
2025-05-23 21:27:54.722 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 21:27:54.723 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 5
2025-05-23 21:27:54.723 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 5 分配了新的工作机器ID: 0，当前线程: pool-1-thread-1
2025-05-23 21:27:54.723 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-23 21:27:54.723 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-1
2025-05-23 21:27:54.723 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: pool-1-thread-1
2025-05-23 21:27:54.723 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 5，schemaName: infra_uid，当前线程: pool-1-thread-1
2025-05-23 21:27:54.724 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-4
2025-05-23 21:27:54.725 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: pool-1-thread-1
2025-05-23 21:27:54.726 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-2
2025-05-23 21:27:54.726 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-2
2025-05-23 21:27:54.726 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-2
2025-05-23 21:27:54.726 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 7 分配工作机器ID，当前线程: pool-1-thread-2
2025-05-23 21:27:54.726 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 7 是否已分配工作机器ID
2025-05-23 21:27:54.728 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 8
2025-05-23 21:27:54.729 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test17446115794588549627/instance-id-2
2025-05-23 21:27:54.730 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 21:27:54.730 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 1，实例ID: 4
2025-05-23 21:27:54.733 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-3
2025-05-23 21:27:54.733 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-3
2025-05-23 21:27:54.733 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-3
2025-05-23 21:27:54.733 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 8 分配工作机器ID，当前线程: pool-1-thread-3
2025-05-23 21:27:54.733 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 8 是否已分配工作机器ID
2025-05-23 21:27:54.735 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-5
2025-05-23 21:27:54.740 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 4 分配了新的工作机器ID: 1，当前线程: pool-1-thread-4
2025-05-23 21:27:54.741 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 5]，当前线程: pool-1-thread-1
2025-05-23 21:27:54.741 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 1
2025-05-23 21:27:54.741 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-4
2025-05-23 21:27:54.741 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 1，当前线程: pool-1-thread-4
2025-05-23 21:27:54.741 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 4，schemaName: infra_uid，当前线程: pool-1-thread-4
2025-05-23 21:27:54.749 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-1
2025-05-23 21:27:54.756 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: pool-1-thread-1
2025-05-23 21:27:54.757 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 2，实例ID: 6
2025-05-23 21:27:54.757 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 6 分配了新的工作机器ID: 2，当前线程: pool-1-thread-5
2025-05-23 21:27:54.757 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-3
2025-05-23 21:27:54.757 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 2
2025-05-23 21:27:54.757 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-5
2025-05-23 21:27:54.757 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 2，当前线程: pool-1-thread-5
2025-05-23 21:27:54.757 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 6，schemaName: infra_uid，当前线程: pool-1-thread-5
2025-05-23 21:27:54.760 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 3，实例ID: 8
2025-05-23 21:27:54.761 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 8 分配了新的工作机器ID: 3，当前线程: pool-1-thread-3
2025-05-23 21:27:54.761 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 3
2025-05-23 21:27:54.761 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-3
2025-05-23 21:27:54.761 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 3，当前线程: pool-1-thread-3
2025-05-23 21:27:54.761 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 8，schemaName: infra_uid，当前线程: pool-1-thread-3
2025-05-23 21:27:54.762 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 当前状态: ACTIVE，当前线程: pool-1-thread-3
2025-05-23 21:27:54.767 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-23 21:27:54.767 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 当前状态: ACTIVE，当前线程: pool-1-thread-5
2025-05-23 21:27:54.767 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [2, 6]，当前线程: pool-1-thread-5
2025-05-23 21:27:54.772 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [3, 8]，当前线程: pool-1-thread-3
2025-05-23 21:27:54.775 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-3
2025-05-23 21:27:54.775 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-5
2025-05-23 21:27:54.777 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 租约续约成功，当前线程: pool-1-thread-3
2025-05-23 21:27:54.777 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 租约续约成功，当前线程: pool-1-thread-5
2025-05-23 21:27:54.778 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-2
2025-05-23 21:27:54.778 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 释放成功
2025-05-23 21:27:54.781 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 释放成功
2025-05-23 21:27:54.785 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 当前状态: ACTIVE，当前线程: pool-1-thread-4
2025-05-23 21:27:54.785 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [1, 4]，当前线程: pool-1-thread-4
2025-05-23 21:27:54.785 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 7
2025-05-23 21:27:54.788 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 7 分配了新的工作机器ID: 0，当前线程: pool-1-thread-2
2025-05-23 21:27:54.788 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-23 21:27:54.788 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-2
2025-05-23 21:27:54.788 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: pool-1-thread-2
2025-05-23 21:27:54.788 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 7，schemaName: infra_uid，当前线程: pool-1-thread-2
2025-05-23 21:27:54.789 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-4
2025-05-23 21:27:54.792 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: pool-1-thread-2
2025-05-23 21:27:54.792 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 7]，当前线程: pool-1-thread-2
2025-05-23 21:27:54.795 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-2
2025-05-23 21:27:54.795 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 租约续约成功，当前线程: pool-1-thread-4
2025-05-23 21:27:54.797 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: pool-1-thread-2
2025-05-23 21:27:54.799 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-23 21:27:54.802 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 释放成功
2025-05-23 21:27:54.810 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 开始清理临时文件，当前注册的临时文件数量: 0
2025-05-23 21:27:56.887 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-23 21:27:57.407 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 7b82285a86d19f79bf8223b31df72bf6dbdf64a2efa7f640bdc852de97deb888
2025-05-23 21:28:02.178 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.29137315S
2025-05-23 21:28:02.179 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-23 21:28:02.180 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-15 - Starting...
2025-05-23 21:28:02.203 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-15 - Added connection org.postgresql.jdbc.PgConnection@b77b0a0
2025-05-23 21:28:02.203 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-15 - Start completed.
2025-05-23 21:28:02.203 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:28:02.203 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:28:02.204 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:28:02.204 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:28:02.206 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 不存在，正在自动创建
2025-05-23 21:28:02.206 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 创建Schema（如果不存在）: infra_uid
2025-05-23 21:28:02.208 [main] DEBUG o.x.c.c.u.m.s.JdbcMetadataStrategy - 执行SQL: CREATE SCHEMA IF NOT EXISTS "infra_uid"
2025-05-23 21:28:02.208 [main] INFO  o.x.c.c.u.m.s.JdbcMetadataStrategy - 已创建Schema: infra_uid
2025-05-23 21:28:02.208 [main] INFO  o.x.c.c.uid.util.UidTableManager - Schema infra_uid 已成功创建
2025-05-23 21:28:02.208 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:28:02.208 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:28:02.212 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-23 21:28:02.222 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:28:02.222 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-23 21:28:02.222 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:28:02.224 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-23 21:28:02.232 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:28:02.232 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-23 21:28:02.232 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:28:02.234 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-23 21:28:02.241 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:28:02.241 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-23 21:28:02.241 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:28:02.241 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: key-test, 环境: test, Schema: infra_uid
2025-05-23 21:28:02.243 [main] DEBUG o.x.c.c.u.c.KeyManagementService - 使用NativePRNGNonBlocking算法的SecureRandom
2025-05-23 21:28:02.244 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已为应用 key-test 环境 test 创建新的 test-key-type 类型密钥
2025-05-23 21:28:03.960 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-23 21:28:04.398 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 79b13503e8a0259e5f7a8c159475d2f0ca73e16e3ff84f95f3358f36d33b4cf3
2025-05-23 21:28:06.639 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.640 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.641 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.641 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.641 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.641 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.641 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.642 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:06.705 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.705 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.706 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.706 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.706 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.706 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.706 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.707 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:06.725 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.745 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.761 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.762 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.762 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.763 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.763 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.763 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.763 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.763 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:06.779 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.779 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.780 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.780 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.780 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.780 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.780 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.782 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:06.824 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.824 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.825 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.825 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.825 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.825 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.825 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:28:06.826 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.143 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.143 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:28:07.143 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:28:07.143 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.143 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.143 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.143 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.146 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.146 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:28:07.147 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:28:07.147 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.147 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:28:07.208 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.208 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:28:07.209 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:28:07.209 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.209 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.209 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.209 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.211 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.211 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:28:07.211 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.213 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-23 21:28:07.213 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:28:07.213 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.213 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:28:07.264 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.264 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:28:07.264 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:28:07.264 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.264 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.264 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.264 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.265 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.265 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:28:07.266 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:28:07.266 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.266 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:28:07.289 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.290 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:28:07.290 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:28:07.290 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.290 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.290 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.290 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.291 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.291 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:28:07.291 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:28:07.291 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.291 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:28:07.331 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.331 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:28:07.331 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:28:07.331 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.331 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.331 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.331 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.332 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:07.332 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:28:07.332 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:28:07.332 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:28:07.332 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:28:09.435 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.474499901S
2025-05-23 21:28:09.435 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: jdbc:postgresql://localhost:32810/test?loggerLevel=OFF)
2025-05-23 21:28:09.437 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-16 - Starting...
2025-05-23 21:28:09.478 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-16 - Added connection org.postgresql.jdbc.PgConnection@3cb195dd
2025-05-23 21:28:09.478 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-16 - Start completed.
2025-05-23 21:28:09.479 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:28:09.482 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: test_schema
2025-05-23 21:28:09.485 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: non_existent_schema
2025-05-23 21:28:09.488 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-17 - Starting...
2025-05-23 21:28:09.556 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-17 - Added connection org.postgresql.jdbc.PgConnection@3fc1abf
2025-05-23 21:28:09.556 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-17 - Start completed.
2025-05-23 21:28:09.556 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:28:09.566 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-23 21:28:09.571 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.non_existent_table
2025-05-23 21:28:09.579 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-18 - Starting...
2025-05-23 21:28:09.605 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-18 - Added connection org.postgresql.jdbc.PgConnection@28bd5015
2025-05-23 21:28:09.606 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-18 - Start completed.
2025-05-23 21:28:09.606 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:28:09.615 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 验证表结构是否包含所需的列: test_schema.test_table
2025-05-23 21:28:09.615 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-23 21:28:09.621 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table
2025-05-23 21:28:09.627 [main] WARN  o.x.c.c.u.m.PostgreSQLMetadataService - 表 test_schema.test_table 缺少必需的列: name
2025-05-23 21:28:09.630 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-19 - Starting...
2025-05-23 21:28:09.698 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-19 - Added connection org.postgresql.jdbc.PgConnection@1de30c31
2025-05-23 21:28:09.698 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-19 - Start completed.
2025-05-23 21:28:09.698 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:28:09.720 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:28:09.720 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: test_schema
2025-05-23 21:28:09.724 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - Schema test_schema 验证通过
2025-05-23 21:28:09.724 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-23 21:28:09.740 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 test_schema.test_table 验证通过
2025-05-23 21:28:09.740 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-23 21:28:09.744 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 test_schema.test_table 验证通过
2025-05-23 21:28:09.744 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table
2025-05-23 21:28:09.753 [main] ERROR o.x.c.c.u.s.i.UidValidationServiceImpl - 表结构验证失败: 表 test_schema.test_table 缺少必需的列 name
2025-05-23 21:28:09.758 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-20 - Starting...
2025-05-23 21:28:09.782 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-20 - Added connection org.postgresql.jdbc.PgConnection@6f1d799
2025-05-23 21:28:09.783 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-20 - Start completed.
2025-05-23 21:28:09.783 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:28:09.794 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table

Test run finished after 43760 ms
[         6 containers found      ]
[         0 containers skipped    ]
[         6 containers started    ]
[         0 containers aborted    ]
[         6 containers successful ]
[         0 containers failed     ]
[        14 tests found           ]
[         0 tests skipped         ]
[        14 tests started         ]
[         0 tests aborted         ]
[         6 tests successful      ]
[         8 tests failed          ]


===== 运行简化测试 =====

1. 基本功能测试
2025-05-23 21:28:11.628 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-21 - Starting...
2025-05-23 21:28:11.645 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-21 - Added connection org.postgresql.jdbc.PgConnection@15cee630
2025-05-23 21:28:11.645 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-21 - Start completed.
2025-05-23 21:28:11.645 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:28:11.645 [main] INFO  o.x.c.c.u.config.UidValidationConfig - 创建UidValidationService实例
2025-05-23 21:28:11.645 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:28:11.647 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:28:11.647 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:28:11.649 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:28:11.649 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:28:11.652 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 21:28:11.653 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:28:11.654 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 21:28:11.654 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 21:28:11.660 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 21:28:11.660 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:28:11.660 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:28:11.662 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 21:28:11.662 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:28:11.664 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 21:28:11.664 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 21:28:11.667 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 21:28:11.667 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:28:11.667 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:28:11.670 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 21:28:11.670 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:28:11.671 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 21:28:11.671 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 21:28:11.674 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 21:28:11.675 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:28:11.675 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:28:11.675 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:28:11.675 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:28:11.675 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: test-app, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:28:11.676 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 1
2025-05-23 21:28:11.678 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 1 在数据库中存在，更新实例信息
2025-05-23 21:28:11.679 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 1 的最后活动时间
2025-05-23 21:28:11.679 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
实例ID: 1
2025-05-23 21:28:11.680 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 21:28:11.680 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 21:28:11.680 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 21:28:11.680 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 1 分配工作机器ID，当前线程: main
2025-05-23 21:28:11.680 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 1 是否已分配工作机器ID
2025-05-23 21:28:11.681 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-23 21:28:11.685 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 0，实例ID: 1
2025-05-23 21:28:11.686 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配了新的工作机器ID: 0，当前线程: main
2025-05-23 21:28:11.686 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-23 21:28:11.686 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
工作机器ID: 0
2025-05-23 21:28:11.688 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功

2. 实例恢复测试
2025-05-23 21:28:11.690 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-22 - Starting...
2025-05-23 21:28:11.706 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-22 - Added connection org.postgresql.jdbc.PgConnection@2c08c787
2025-05-23 21:28:11.706 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-22 - Start completed.
2025-05-23 21:28:11.706 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:28:11.707 [main] INFO  o.x.c.c.u.config.UidValidationConfig - 创建UidValidationService实例
2025-05-23 21:28:11.707 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:28:11.707 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:28:11.707 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:28:11.709 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:28:11.709 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:28:11.712 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 21:28:11.712 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:28:11.714 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 21:28:11.714 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 21:28:11.722 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 21:28:11.722 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:28:11.722 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:28:11.724 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 21:28:11.724 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:28:11.726 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 21:28:11.726 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 21:28:11.729 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 21:28:11.729 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:28:11.729 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:28:11.731 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 21:28:11.731 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:28:11.732 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 21:28:11.732 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 21:28:11.737 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 21:28:11.737 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:28:11.737 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
===== 第一次运行 =====
2025-05-23 21:28:11.737 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:28:11.737 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:28:11.737 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: recovery-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:28:11.739 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 2
2025-05-23 21:28:11.740 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 2 在数据库中不存在
2025-05-23 21:28:11.740 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 21:28:11.742 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 21:28:11.742 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 21:28:11.744 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 2
2025-05-23 21:28:11.746 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
2025-05-23 21:28:11.746 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
第一次运行 - 实例ID: 2

===== 第二次运行 (从文件恢复) =====
2025-05-23 21:28:11.746 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:28:11.747 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:28:11.747 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: recovery-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:28:11.748 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 2
2025-05-23 21:28:11.749 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 2 在数据库中存在，更新实例信息
2025-05-23 21:28:11.750 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 2 的最后活动时间
2025-05-23 21:28:11.750 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
第二次运行 - 实例ID: 2
从文件恢复结果: 成功

===== 删除文件后运行 (从特征码恢复) =====
2025-05-23 21:28:11.751 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:28:11.751 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:28:11.751 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: recovery-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:28:11.752 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
2025-05-23 21:28:11.752 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 21:28:11.754 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 通过指纹哈希精确匹配找到实例ID: 2
2025-05-23 21:28:11.754 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 成功恢复实例ID: 2
2025-05-23 21:28:11.756 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 2 的最后活动时间
2025-05-23 21:28:11.759 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
2025-05-23 21:28:11.760 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
删除文件后运行 - 实例ID: 2
从特征码恢复结果: 成功

3. 租约管理测试
2025-05-23 21:28:11.762 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - ===== 租约管理测试开始 =====
2025-05-23 21:28:11.762 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1. 准备测试环境 - 初始化数据源
2025-05-23 21:28:11.762 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.1 检查Docker是否正常运行
2025-05-23 21:28:23.376 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - Docker正常运行
2025-05-23 21:28:23.376 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.2 创建PostgresTestContainer实例
2025-05-23 21:28:23.376 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - PostgresTestContainer实例创建成功
2025-05-23 21:28:23.376 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.3 检查容器是否正在运行
2025-05-23 21:28:23.382 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 容器运行状态: true
2025-05-23 21:28:23.382 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.4 获取JDBC连接信息
2025-05-23 21:28:23.382 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - JDBC URL: ******************************************************
2025-05-23 21:28:23.382 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 用户名: test
2025-05-23 21:28:23.382 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 密码: test
2025-05-23 21:28:23.382 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.5 获取数据源
2025-05-23 21:28:23.383 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-23 - Starting...
2025-05-23 21:28:23.404 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-23 - Added connection org.postgresql.jdbc.PgConnection@631944d4
2025-05-23 21:28:23.404 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-23 - Start completed.
2025-05-23 21:28:23.404 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据源获取成功
2025-05-23 21:28:23.404 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.6 测试数据库连接
2025-05-23 21:28:23.404 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库连接成功
2025-05-23 21:28:23.404 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库产品名称: PostgreSQL
2025-05-23 21:28:23.404 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库产品版本: 17.4 (Debian 17.4-1.pgdg120+2)
2025-05-23 21:28:23.404 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库驱动名称: PostgreSQL JDBC Driver
2025-05-23 21:28:23.404 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库驱动版本: 42.7.5
2025-05-23 21:28:23.404 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.7 创建JdbcTemplate和TransactionTemplate
2025-05-23 21:28:23.404 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - JdbcTemplate和TransactionTemplate创建成功
2025-05-23 21:28:23.404 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.8 创建元数据服务和验证服务
2025-05-23 21:28:23.404 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:28:23.405 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.9 初始化表结构
2025-05-23 21:28:23.405 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:28:23.405 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:28:23.405 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:28:23.407 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:28:23.407 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:28:23.410 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 21:28:23.410 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:28:23.412 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 21:28:23.412 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 21:28:23.418 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 21:28:23.418 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:28:23.418 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:28:23.420 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 21:28:23.420 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:28:23.422 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 21:28:23.422 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 21:28:23.425 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 21:28:23.425 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:28:23.425 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:28:23.427 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 21:28:23.427 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:28:23.428 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 21:28:23.428 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 21:28:23.431 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 21:28:23.431 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:28:23.431 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:28:23.431 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.2 验证表结构是否创建成功
2025-05-23 21:28:23.439 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 已创建的表: [{table_name=instance_registry}, {table_name=worker_id_assignment}, {table_name=encryption_key}]
2025-05-23 21:28:23.439 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 2. 创建 KeyManagementService
2025-05-23 21:28:23.439 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: lease-test, 环境: test, Schema: infra_uid
2025-05-23 21:28:23.439 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - KeyManagementService 创建成功，加密状态: false
2025-05-23 21:28:23.439 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 3. 创建实例管理器
2025-05-23 21:28:23.439 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:28:23.439 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:28:23.439 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: lease-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AND_NEW, 加密启用: false
2025-05-23 21:28:23.441 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 3
2025-05-23 21:28:23.443 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 3 在数据库中不存在
2025-05-23 21:28:23.443 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 21:28:23.444 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到精确匹配，且恢复策略为创建新实例，跳过模糊匹配
2025-05-23 21:28:23.444 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 21:28:23.445 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 3
2025-05-23 21:28:23.447 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/lease-test-instance-id.dat
2025-05-23 21:28:23.447 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 21:28:23.447 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 2.1 实例管理器创建成功，实例ID: 3
2025-05-23 21:28:23.450 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 已注册的实例: [{instance_unique_id=3, application_name=lease-test, environment=test, instance_group=default, status=ACTIVE, first_registered_at=2025-05-23 21:28:23.444698, last_seen_at=2025-05-23 21:28:23.444698, custom_metadata={"os_arch": "amd64", "os_name": "Linux", "hostname": "long-VirtualBox", "os_version": "5.4.0-91-generic", "mac_addresses": ["CE:A9:04:3F:73:FD", "08:00:27:D6:3A:87"], "fingerprint_hash": "0cd7e71f226e917911f5e1251a5665812c4065753531534bd12cbdfeaab5c60d"}}]
2025-05-23 21:28:23.450 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 3. 创建工作机器ID分配器
2025-05-23 21:28:23.450 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 4. 开始分配工作机器ID
2025-05-23 21:28:23.450 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-23 21:28:23.450 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-23 21:28:23.450 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-23 21:28:23.450 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 3 分配工作机器ID，当前线程: main
2025-05-23 21:28:23.450 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 3 是否已分配工作机器ID
2025-05-23 21:28:23.451 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-23 21:28:23.453 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 3
2025-05-23 21:28:23.454 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 3 分配了新的工作机器ID: 0，当前线程: main
2025-05-23 21:28:23.454 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-23 21:28:23.454 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-23 21:28:23.454 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 分配的工作机器ID: 0
2025-05-23 21:28:23.456 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 工作机器ID分配记录: [{worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:23.452361, lease_expires_at=2025-05-23 21:29:23.452361, released_at=2025-05-23 21:28:11.687512}]
2025-05-23 21:28:23.456 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5. 测试租约续约 - 通过心跳线程自动续约
2025-05-23 21:28:23.456 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.1 等待心跳线程自动续约前，当前状态
2025-05-23 21:28:23.457 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:23.452361, lease_expires_at=2025-05-23 21:29:23.452361, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:23.457 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-23 21:28:24.460 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.1 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:23.452361, lease_expires_at=2025-05-23 21:29:23.452361, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:24.460 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.2 等待心跳线程自动续约前，当前状态
2025-05-23 21:28:24.463 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:23.452361, lease_expires_at=2025-05-23 21:29:23.452361, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:24.463 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-23 21:28:25.467 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.2 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:23.452361, lease_expires_at=2025-05-23 21:29:23.452361, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:25.467 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.3 等待心跳线程自动续约前，当前状态
2025-05-23 21:28:25.473 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:23.452361, lease_expires_at=2025-05-23 21:29:23.452361, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:25.473 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-23 21:28:26.477 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.3 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:23.452361, lease_expires_at=2025-05-23 21:29:23.452361, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:26.478 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6. 测试手动续约
2025-05-23 21:28:26.478 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.1 手动续约前，当前状态
2025-05-23 21:28:26.479 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:23.452361, lease_expires_at=2025-05-23 21:29:23.452361, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:26.480 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-23 21:28:26.480 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-23 21:28:26.480 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-23 21:28:26.481 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-23 21:28:26.482 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-23 21:28:26.483 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-23 21:28:26.484 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-23 21:28:26.486 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.1 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:26.482973, lease_expires_at=2025-05-23 21:29:26.482973, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:26.487 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-23 21:28:26.638 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.639 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.640 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.640 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.640 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.640 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.640 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.641 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:26.706 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.707 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.707 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.707 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.707 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.707 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.708 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.715 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:26.725 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.740 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.757 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.758 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.758 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.758 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.758 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.758 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.758 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.758 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:26.777 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.777 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.778 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.778 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.778 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.778 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.778 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.778 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:26.824 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.824 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.824 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.824 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.824 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.824 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.824 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-23 21:28:26.825 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.142 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.142 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:28:27.142 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:28:27.142 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.142 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.142 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.142 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.144 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.144 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:28:27.145 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:28:27.145 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.145 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:28:27.215 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.216 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:28:27.216 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:28:27.216 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.216 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.216 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.216 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.217 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.217 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:28:27.217 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.218 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-23 21:28:27.218 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:28:27.218 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.218 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:28:27.258 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.259 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:28:27.259 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:28:27.259 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.259 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.259 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.259 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.259 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.259 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:28:27.260 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:28:27.260 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.260 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:28:27.280 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.280 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:28:27.280 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:28:27.280 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.280 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.280 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.280 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.281 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.281 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:28:27.281 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:28:27.281 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.281 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:28:27.327 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.327 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-23 21:28:27.327 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-23 21:28:27.327 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.327 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.327 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.327 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.328 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-23 21:28:27.328 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-23 21:28:27.329 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-23 21:28:27.329 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-23 21:28:27.329 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-23 21:28:27.487 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.2 手动续约前，当前状态
2025-05-23 21:28:27.490 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:26.482973, lease_expires_at=2025-05-23 21:29:26.482973, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:27.490 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-23 21:28:27.490 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-23 21:28:27.490 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-23 21:28:27.491 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-23 21:28:27.491 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-23 21:28:27.492 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-23 21:28:27.493 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-23 21:28:27.493 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.2 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:27.492103, lease_expires_at=2025-05-23 21:29:27.492103, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:27.493 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-23 21:28:28.497 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.3 手动续约前，当前状态
2025-05-23 21:28:28.499 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:27.492103, lease_expires_at=2025-05-23 21:29:27.492103, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:28.499 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-23 21:28:28.499 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-23 21:28:28.499 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-23 21:28:28.500 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-23 21:28:28.500 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-23 21:28:28.501 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-23 21:28:28.501 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-23 21:28:28.502 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.3 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:28.500566, lease_expires_at=2025-05-23 21:29:28.500566, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:28.502 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-23 21:28:29.508 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7. 测试租约失效后的重新分配
2025-05-23 21:28:29.509 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.1 模拟租约失效 - 将工作机器ID状态设置为AVAILABLE
2025-05-23 21:28:29.527 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 更新结果: 影响行数 = 1
2025-05-23 21:28:29.531 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 更新后状态: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:28.500566, lease_expires_at=2025-05-23 21:29:28.500566, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:29.531 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.2 等待2秒，让租约续约线程发现租约失效
2025-05-23 21:28:31.533 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.3 尝试重新获取工作机器ID
2025-05-23 21:28:31.533 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 直接返回已缓存的工作机器ID: 0
2025-05-23 21:28:31.533 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 重新分配的工作机器ID: 0
2025-05-23 21:28:31.535 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 新工作机器ID记录: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:28.500566, lease_expires_at=2025-05-23 21:29:28.500566, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:31.535 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 8. 关闭心跳线程
2025-05-23 21:28:31.536 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放失败
2025-05-23 21:28:31.537 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 关闭后状态: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-23 21:28:23.452361, last_renewed_at=2025-05-23 21:28:28.500566, lease_expires_at=2025-05-23 21:29:28.500566, released_at=2025-05-23 21:28:11.687512}
2025-05-23 21:28:31.537 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - ===== 租约管理测试完成 =====

4. 门面模式测试
2025-05-23 21:28:31.540 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-24 - Starting...
2025-05-23 21:28:31.557 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-24 - Added connection org.postgresql.jdbc.PgConnection@25f7cc38
2025-05-23 21:28:31.557 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-24 - Start completed.
2025-05-23 21:28:31.557 [main] INFO  o.x.c.c.u.f.UidGeneratorFacadeBuilder - 构建UidGeneratorFacade
2025-05-23 21:28:31.557 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 初始化UidGeneratorFacade
2025-05-23 21:28:31.557 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建JdbcTemplate
2025-05-23 21:28:31.557 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建TransactionTemplate
2025-05-23 21:28:31.557 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建DatabaseMetadataService
2025-05-23 21:28:31.557 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-23 21:28:31.557 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidValidationService
2025-05-23 21:28:31.557 [main] INFO  o.x.c.c.u.config.UidValidationConfig - 创建UidValidationService实例
2025-05-23 21:28:31.557 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建UidTableManager
2025-05-23 21:28:31.557 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-23 21:28:31.558 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-23 21:28:31.558 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: infra_uid
2025-05-23 21:28:31.560 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-23 21:28:31.560 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:28:31.564 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-23 21:28:31.564 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.instance_registry
2025-05-23 21:28:31.566 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 验证通过
2025-05-23 21:28:31.566 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.instance_registry
2025-05-23 21:28:31.574 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.instance_registry 结构验证通过
2025-05-23 21:28:31.574 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-23 21:28:31.574 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:28:31.575 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-23 21:28:31.575 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.worker_id_assignment
2025-05-23 21:28:31.577 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 验证通过
2025-05-23 21:28:31.577 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.worker_id_assignment
2025-05-23 21:28:31.580 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-23 21:28:31.580 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-23 21:28:31.580 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:28:31.582 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-23 21:28:31.582 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: infra_uid.encryption_key
2025-05-23 21:28:31.583 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 验证通过
2025-05-23 21:28:31.583 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: infra_uid.encryption_key
2025-05-23 21:28:31.586 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 infra_uid.encryption_key 结构验证通过
2025-05-23 21:28:31.587 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-23 21:28:31.587 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-23 21:28:31.587 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceManager
2025-05-23 21:28:31.587 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 开始构建PersistentInstanceManager实例
2025-05-23 21:28:31.587 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - 参数验证通过，创建PersistentInstanceManager实例
2025-05-23 21:28:31.587 [main] DEBUG o.x.c.c.u.i.PersistentInstanceManagerBuilder - 应用名称: facade-test, 环境: test, 实例组: default, Schema: infra_uid, 恢复策略: ALERT_AUTO_WITH_TIMEOUT, 加密启用: false
2025-05-23 21:28:31.588 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/facade-test-instance-id.dat
2025-05-23 21:28:31.588 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-23 21:28:31.591 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-23 21:28:31.591 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-23 21:28:31.592 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 4
2025-05-23 21:28:31.594 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/facade-test-instance-id.dat
2025-05-23 21:28:31.595 [main] INFO  o.x.c.c.u.i.PersistentInstanceManagerBuilder - PersistentInstanceManager实例构建完成
2025-05-23 21:28:31.595 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建PersistentInstanceWorkerIdAssigner
2025-05-23 21:28:31.595 [main] DEBUG o.x.c.c.u.facade.UidGeneratorFacade - 创建CachedUidGenerator
2025-05-23 21:28:31.595 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade初始化完成
2025-05-23 21:28:31.595 [main] ERROR c.xfvape.uid.impl.CachedUidGenerator - Generate unique id exception. 
java.lang.NullPointerException: Cannot invoke "com.xfvape.uid.buffer.RingBuffer.take()" because "this.ringBuffer" is null
	at com.xfvape.uid.impl.CachedUidGenerator.getUID(CachedUidGenerator.java:80)
	at org.xkong.cloud.commons.uid.facade.UidGeneratorFacade.getUID(UidGeneratorFacade.java:89)
	at org.xkong.cloud.commons.uid.simplified.FacadeBasicTest.main(FacadeBasicTest.java:45)
	at org.xkong.cloud.commons.uid.TestRunner.runSimplifiedTests(TestRunner.java:81)
	at org.xkong.cloud.commons.uid.TestRunner.main(TestRunner.java:34)
2025-05-23 21:28:31.596 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - 关闭UidGeneratorFacade资源
2025-05-23 21:28:31.596 [main] INFO  o.x.c.c.u.facade.UidGeneratorFacade - UidGeneratorFacade资源已关闭
简化测试运行失败: java.lang.NullPointerException: Cannot invoke "com.xfvape.uid.buffer.RingBuffer.take()" because "this.ringBuffer" is null
com.xfvape.uid.exception.UidGenerateException: java.lang.NullPointerException: Cannot invoke "com.xfvape.uid.buffer.RingBuffer.take()" because "this.ringBuffer" is null
	at com.xfvape.uid.impl.CachedUidGenerator.getUID(CachedUidGenerator.java:83)
	at org.xkong.cloud.commons.uid.facade.UidGeneratorFacade.getUID(UidGeneratorFacade.java:89)
	at org.xkong.cloud.commons.uid.simplified.FacadeBasicTest.main(FacadeBasicTest.java:45)
	at org.xkong.cloud.commons.uid.TestRunner.runSimplifiedTests(TestRunner.java:81)
	at org.xkong.cloud.commons.uid.TestRunner.main(TestRunner.java:34)
Caused by: java.lang.NullPointerException: Cannot invoke "com.xfvape.uid.buffer.RingBuffer.take()" because "this.ringBuffer" is null
	at com.xfvape.uid.impl.CachedUidGenerator.getUID(CachedUidGenerator.java:80)
	... 4 more
2025-05-23 21:28:32.402 [Thread-7] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.402 [Thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.402 [temp-file-cleanup-hook] DEBUG o.x.c.commons.uid.util.TempFileUtils - 执行临时文件清理关闭钩子
2025-05-23 21:28:32.402 [temp-file-cleanup-hook] DEBUG o.x.c.commons.uid.util.TempFileUtils - 开始清理临时文件，当前注册的临时文件数量: 0
2025-05-23 21:28:32.403 [Thread-13] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.403 [Thread-12] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.403 [Thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.404 [Thread-18] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.405 [Thread-16] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.405 [Thread-24] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.405 [Thread-6] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 21:28:32.405 [Thread-6] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-23 21:28:32.405 [Thread-19] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.405 [Thread-4] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 21:28:32.406 [Thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-23 21:28:32.406 [Thread-23] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-23 - Failed to validate connection org.postgresql.jdbc.PgConnection@631944d4 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-23 21:28:32.406 [Thread-8] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 21:28:32.406 [Thread-8] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-23 21:28:32.406 [Thread-14] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.406 [Thread-5] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 21:28:32.406 [Thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-23 21:28:32.407 [Thread-20] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.407 [Thread-21] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.408 [Thread-15] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.408 [Thread-0] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 21:28:32.408 [Thread-0] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-23 21:28:32.408 [Thread-22] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.408 [Thread-17] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-23 21:28:32.409 [Thread-1] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-23 21:28:32.410 [Thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-23 21:28:32.420 [Thread-23] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-23 - Failed to validate connection org.postgresql.jdbc.PgConnection@2d21366f (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-23 21:29:02.574 [Thread-23] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放失败: Failed to obtain JDBC Connection
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:653)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:972)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1016)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1026)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.releaseWorkerId(PersistentInstanceWorkerIdAssigner.java:536)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.lambda$initialize$1(PersistentInstanceWorkerIdAssigner.java:79)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.sql.SQLTransientConnectionException: HikariPool-23 - Connection is not available, request timed out after 30002ms (total=0, active=0, idle=0, waiting=0)
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:686)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:179)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:144)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:99)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 7 common frames omitted
Caused by: org.postgresql.util.PSQLException: Connection to localhost:32805 refused. Check that the hostname and port are correct and that the postmaster is accepting TCP/IP connections.
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:352)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273)
	at org.postgresql.Driver.makeConnection(Driver.java:446)
	at org.postgresql.Driver.connect(Driver.java:298)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:724)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:703)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	... 1 common frames omitted
Caused by: java.net.ConnectException: 拒绝连接
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260)
	at org.postgresql.core.PGStream.<init>(PGStream.java:121)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268)
	... 14 common frames omitted
2025-05-23 21:29:02.575 [Thread-23] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 0

进程已结束，退出代码为 0
