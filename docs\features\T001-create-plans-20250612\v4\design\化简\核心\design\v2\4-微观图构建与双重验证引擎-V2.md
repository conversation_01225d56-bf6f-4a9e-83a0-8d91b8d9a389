# V4.2方案：微观图构建与双重验证引擎-V2

## 1. 文档信息

- **文档版本**: V2.0
- **创建日期**: 2025-07-31
- **文档性质**: **核心架构文档** - 定义V2系统的微观图构建与双重验证引擎
- **界面交互**: 界面交互设计已在《03-九宫格交互界面设计-V2.md》中单独定义

**【注意：未来架构愿景】**
**本章节描述的“四阶段、预验证驱动的治理流程”是一个设想中的、非常先进的后端治理引擎。在当前V4.2的实现中，核心是`ProjectManager`驱动的异步任务框架，为未来实现本文档描述的“插件化验证器”架构（`ValidationLoop`）奠定了基础，但完整的四阶段流程尚未实现。本文档内容可作为未来核心引擎开发的详细参考。**

**注意**: 本文档专注于核心架构和引擎设计，不涉及界面交互设计。界面交互部分请参考《03-九宫格交互界面设计-V2.md》文档。

## 2. 设计哲学：先标准化，再签约，后治理

V4.1引擎的核心哲学是“**冲突不应被检测，而应在设计上被预防**”。我们摒弃了在流程后期检测模糊文本冲突的脆弱模式，转而在流程的最前端，通过“**约束标准化与预验证**”流程，确保所有作为治理依据的全局约束，在诞生时就是无歧义、无冲突、结构化的。

## 3. 核心流程：四阶段、预验证驱动的治理流程

引擎的核心流程由四个独立的、顺序执行的阶段构成，形成一个完整的质量控制闭环。

```mermaid
graph TD
    A[输入: 01号等顶层设计文档] --> B["**阶段零: 全局约束库的标准化与预验证**"];
    B -- 100%无冲突的全局约束库 --> C["**阶段一: 全局契约生成与审计**"];
    C -- 全局约束集 --> D["**阶段二: 引用式契约生成与审计**"];
    D -- 经过审计的局部契约 --> E["**阶段三: 契约履行与原子审计**"];
    E -- 通过原子审计的产出 --> F["**阶段四: 最终产出整体性审计**"];
    F --> G[输出: 100%符合治理原则的、结构稳固的工程产物];
```

### 3.1. 阶段零：全局约束库的标准化与预验证 (V4.1 核心升级)

此阶段是V4.1架构的**可靠性基石**，它用全新的“事前冲突预防”模式，取代了旧的“事后风险分析”。

- **核心理念**: **所有冲突必须在源头被消灭。**
- **核心引擎**: `ConstraintPreprocessor` (约束预处理器)。

#### **工作流程：从意图到标准化的原子约束**

1.  **意图识别 (AI)**:
    -   `首席架构师AI` 分析 `01号` 等顶层设计文档，识别出需要被定义为全局约束或护栏的**设计意图**。
    -   **产出**: AI不会直接生成约束文本，而是输出一个“约束提议”，例如：“识别到需要一个关于数据库选型的全局约束”。

2.  **标准化填充 (AI/人工)**:
    -   系统提供一个基于 `AtomicConstraint` 模型的、标准化的输入模板。
    -   AI或人类决策者被要求在这个模板中，用**结构化的方式**填充约束的具体参数。
    -   **示例**: 对于数据库选型，模板会要求从一个预定义的枚举 `["PostgreSQL", "MySQL"]` 中选择。对于内存限制，模板会要求填写一个明确单位的数值字段 `max_mb`。

3.  **确定性预验证 (算法)**:
    -   在约束被赋予ID并存入“全局约束库”之前，`ConstraintPreprocessor` 算法会对其进行严格的、确定性的预验证。
    -   此验证**不涉及任何语义理解**，只进行逻辑和数值上的比较，因此100%可靠。

#### **`ConstraintPreprocessor` 伪代码示例**

```python
def preprocess_and_validate(
    proposed_constraint: dict, 
    existing_repo: list[AtomicConstraint]
) -> AtomicConstraint:
    """
    对提议的约束进行标准化和预验证。
    如果通过，则为其分配ID并返回完整的原子约束对象。
    如果失败，则直接抛出异常，强制决策者解决冲突。
    """
    # 1. 验证提议是否符合其类型的标准化模板
    if not validate_schema(proposed_constraint):
        raise SchemaValidationError(f"Proposed constraint does not match its type's schema.")

    # 2. 确定性冲突验证 (示例)
    if proposed_constraint['type'] == 'memory_limit' and 'max_mb' in proposed_constraint['params']:
        new_max = proposed_constraint['params']['max_mb']
        # 在库中查找是否已存在min_mb的约束
        for existing in existing_repo:
            if existing['type'] == 'memory_limit' and 'min_mb' in existing['params']:
                if new_max < existing['params']['min_mb']:
                    # 预验证失败，直接拒绝，从源头防止冲突
                    raise PrevalidationError(f"Max memory {new_max}MB cannot be less than existing min memory {existing['params']['min_mb']}MB.")

    # ... 其他确定性验证，如技术选型是否互斥等 ...

    # 3. 预验证通过，分配ID并创建最终对象
    new_id = generate_constraint_id(proposed_constraint['category']) # e.g., "GC003"
    
    final_constraint = AtomicConstraint(
        id=new_id,
        **proposed_constraint
    )
    return final_constraint
```

- **产出**: 一个经过了“意图识别 -> 标准化填充 -> 确定性预验证”流程的、包含了若干`AtomicConstraint`对象的、100%无内部冲突的“**全局约束库**”。

### 3.2. 阶段一与阶段二：契约生成、审计与自我修正

这两个阶段的核心逻辑保持不变，但它们现在消费的是由“阶段零”产出的、100%可靠的“全局约束库”，使其工作基础变得无比坚实。

- **核心引擎**: `ContractAuditor` (算法)。
- **AI角色**: 初级/高级架构师AI。

#### **工作流程：契约的“生成-审计-修正”闭环**

流程与V4一致，但AI的任务发生了变化：
- **V4.2中的AI任务**: AI现在需要处理的是统一的、结构化的`AtomicConstraint`对象。它的核心任务是：
    1.  **理解`category`**：识别出这是一个通用约束、边界条件，还是状态机。
    2.  **选择验证器**：在生成`ValidationChainPoint`时，为其`validation_type`字段选择一个最合适的、下游存在的**专业验证插件**（如`STATE_MACHINE_VALIDATION`）。
    3.  **准备精确参数**：为其`target`字段，生成该专业插件所需要的、精确的、结构化的参数。

- **产出**: 一份经过严格算法审计的、意图明确、可被插件化引擎高效执行的“**局部验证契约**”。

### 3.3. 阶段三：契约履行与原子审计 (Atomic Audit)

此阶段的核心是**保证每一个“原子”（节点、关系及其属性）的正确性**。

- **AI角色**: **Design Synthesizer (设计合成师)**
- **算法引擎**: `ValidationLoop`，采用**插件式验证器架构**。

#### 3.3.1. 插件式验证器架构 (Pluggable Validator Architecture)

为了实现系统的长期稳定性和可扩展性，`ValidationLoop`引擎本身被设计为一个小而美的“微核”，所有具体的验证逻辑都由可插拔的“验证器插件”来完成。

-   **核心引擎 (`ValidationLoop`)**：其职责极其简单，即根据验证点`ValidationChainPoint`中的`validation_type`字段，从一个**插件注册表 (Plugin Registry)**中查找并调用对应的验证器插件。
-   **专业能力下沉到插件**：所有具体的验证逻辑，都封装在独立的、实现了统一接口的插件类中（如`StateMachineValidator`, `BoundaryConditionValidator`）。

**伪代码示例：**
```python
# 简单的插件注册表
VALIDATOR_PLUGINS = {
    "GENERAL_PATTERN_MATCH": GeneralPatternValidator(),
    "STATE_MACHINE_VALIDATION": StateMachineValidator(),
    "BOUNDARY_CONDITION_VALIDATION": BoundaryConditionValidator()
}

# 稳定、简洁的核心循环
def validation_loop(contract):
    for point in contract.validation_chain:
        validator = VALIDATOR_PLUGINS.get(point.validation_type)
        if validator:
            validator.validate(point.target) # 每个插件负责解析自己的target结构
        else:
            raise ValidatorNotFoundError(f"No validator found for type: {point.validation_type}")
```

- **产出**: 一份通过了原子审计的、细节100%正确的工程产出。

### 3.4. 阶段四：最终产出整体性审计 (Holistic Audit)

此阶段是“锥形验证”的最后一块拼图，**确保所有完美的“积木块”搭建起来的“城堡”在宏观结构上是稳固和合理的**。此阶段逻辑与V4完全一致。

- **核心引擎**: 基于图论算法的确定性引擎（例如，使用`NetworkX`库）。

- **产出**: 100%符合治理原则且宏观结构稳固的最终工程产物。

## 4. 架构价值

这个“预验证驱动”的通用治理引擎，通过其严谨的V4.1流程，带来了无与伦比的架构价值：

- **根源上的可靠性**: 通过在流程开始前就对“真理之源”进行**标准化和预验证**，从根本上**预防**了所有可被算法识别的冲突，实现了真正的“无垃圾输入”。
- **职责清晰的AI-算法协同**: AI负责识别**意图**，算法负责**标准化、预验证、ID管理**等确定性工作，最大化地发挥了各自的优势。
- **完整的自动化治理闭环**: 实现了从“**标准化治理依据**” -> “**制定治理规则**” -> “**传递治理思想**” -> “**执行治理结果**”的完整质量治理闭环。
- **极高的自动化与安全性**: 自动化流程建立在一个经过机器自我验证的、100%无内部冲突的约束库之上，达到了前所未有的可靠性水平。

## 5. 应用模式：架构重构与代码治理

当V2引擎用于改造现有代码库时，其核心四阶段流程将被应用于一个“差异驱动”的场景，其具体实现如下：

### 5.1. 改造者模式下的扩展流程

#### 5.1.1. 步骤一：前置条件检查 (Pre-condition Check)

-   **核心理念**: 在进行任何代码分析或修改前，必须首先验证执行设计所需的所有外部条件是否满足。这是确保引擎鲁棒性的第一道防线。
-   **触发**: 人类启动“检查”或“治理”任务后，引擎首先进入此检查阶段。
-   **实现逻辑**:
    1.  **解析约束**: 引擎首先解析设计文档中的相关约束（`AtomicConstraint`）。
    2.  **识别外部依赖**: 它会识别出约束中隐含的外部依赖。例如，一个`type`为`library_usage`的约束，其`params`中会包含`"library_name": "@xkong/unified-exception-library"`。
    3.  **验证依赖存在性**: 引擎会调用一个**依赖分析器 (Dependency Analyzer)**，该分析器会扫描项目的依赖管理文件（如`pom.xml`, `package.json`），检查`library_name`所指向的库是否真实存在于项目的依赖树中。
    4.  **中止或继续**:
        -   **若检查失败**: 引擎立即中止工作流，并通过WebSocket向前端（九宫格界面区域5）发送一个包含明确错误信息和修复建议的“前置条件检查失败”通知。
        -   **若检查成功**: 引擎才会继续执行下一步的差异审计。

#### 5.1.2. 步骤二：差异审计 (Gap Audit)

-   **核心理念**: 引擎的工作是“弥合差异”，而非“从零创造”。
-   **触发**: 仅当“前置条件检查”成功后，此步骤才会执行。
-   **扩展逻辑**:
    1.  **读取“法典”**: `ConstraintPreprocessor`正常工作，从设计文档中提取出最新的、作为“真理之源”的`AtomicConstraint`集合。
    2.  **扫描“现实”**: 引擎会调用一个**代码分析器 (Code Analyzer)**，扫描目标代码模块的现有结构和模式。
    3.  **差异识别**: 引擎的核心算法会对比“法典”与“现实”，识别出所有不符合最新设计约束的代码点。例如，发现代码仍在使用旧的异常处理，而“法典”要求使用新库。

### 5.2. AI角色的演变：从“设计者”到“规划师”

-   **输入**: AI接收到的不再是模糊的设计意图，而是明确的“差异列表”。
-   **核心任务**: AI的任务是为每一个“差异点”，规划出一个最小化的、可执行的修复方案。
-   **产出**: AI不再直接生成代码或`Node`/`Edge`对象，而是生成一个在《架构数据模型标准》中定义的、结构化的**`ChangeSet`对象**。这份`ChangeSet`就是提交给人类进行“司法审查”的“修正案”。

### 5.3. 阶段四的扩展：集成验证 (Integration Validation)

-   **触发**: 在人类于九宫格界面批准`ChangeSet`后，引擎执行代码修改。
-   **扩展逻辑**: 在原有的“整体性审计”基础上，增加一个关键的外部验证步骤。
    1.  **执行变更**: 引擎使用`replace_in_file`等工具，精确应用`ChangeSet`中的所有修改。
    2.  **触发CI验证**: 修改完成后，引擎会**自动调用项目既有的持续集成（CI）脚本**，例如执行`mvn clean install test`或`npm test`。
    3.  **结果判定**: 只有当所有代码修改成功应用，**且**所有单元测试和集成测试全部通过时，引擎才会判定本次重构任务成功。任何一个环节失败，流程都会立即中止并向用户报警。
