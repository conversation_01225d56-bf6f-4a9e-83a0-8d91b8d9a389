---
title: PostgreSQL特定数据类型使用指南(演进架构版)
document_id: C031
document_type: 共享文档
category: 中间件
scope: PostgreSQL
keywords: [PostgreSQL, JSONB, 数组, 地理信息, PostGIS, 全文搜索, 枚举类型, 范围类型, 复合类型, 特殊数据类型, 演进架构, 数据类型抽象层]
created_date: 2025-06-20
updated_date: 2025-01-15
status: 已批准
version: 2.0
authors: [AI助手]
affected_features:
  - F003 # PostgreSQL迁移
related_docs:
  - ./development-standards-guide.md
  - ./framework-integration-best-practices.md
  - ./integration-guide.md
  - ./query-optimization-guide.md
  - ./schema-planning-guide.md
  - ./security-best-practices-guide.md
  - ./transaction-management-guide.md
  - ../architecture/patterns/postgresql-evolution-implementation-guide.md
---

# PostgreSQL特定数据类型使用指南(演进架构版)

## 演进架构整合概述

本文档已升级为支持持续演进架构的PostgreSQL特定数据类型使用指南。在传统数据类型使用技术的基础上，融入了以下演进架构特性：

### 核心演进特性
- **数据类型抽象层**：统一的数据类型处理接口，支持从本地到分布式的数据类型演进
- **配置驱动类型策略**：通过配置文件控制特殊数据类型的处理方式和优化策略
- **分布式数据类型管理**：支持跨服务的特殊数据类型协调和序列化
- **智能类型路由**：根据架构模式自动选择本地或远程数据类型处理
- **渐进式类型演进**：支持数据类型处理从本地实现逐步演进到分布式实现

### 架构演进路径
1. **单体阶段**：使用本地特殊数据类型处理，建立数据类型抽象层
2. **模块化阶段**：引入类型转换器和序列化策略
3. **混合阶段**：部分数据类型保持本地处理，部分使用远程服务
4. **微服务阶段**：全面使用分布式数据类型管理和服务间序列化

## 摘要

本文档提供了PostgreSQL特定数据类型（如JSONB、数组、地理信息等）的详细使用指南，支持持续演进架构模式。包括使用场景、优势、实现方式和最佳实践。本指南旨在帮助开发人员正确选择和使用PostgreSQL的高级数据类型，充分利用PostgreSQL的特性，同时支持架构演进过程中的数据类型处理需求。

## 文档关系说明

本文档是PostgreSQL相关文档体系的一部分，与其他文档的关系如下：

- [PostgreSQL开发规范指南](./development-standards-guide.md)：提供PostgreSQL的编码规范和最佳实践，包括基本数据类型选择指南。本文档对其中的特殊数据类型部分进行扩展和深入说明。
- [PostgreSQL框架集成最佳实践指南](./framework-integration-best-practices.md)：提供PostgreSQL与各种Java框架集成的最佳实践。本文档补充了特定数据类型与这些框架的集成方法。
- [PostgreSQL集成指南](./integration-guide.md)：提供PostgreSQL的配置和集成细节。本文档专注于特定数据类型的使用，是对基础集成的补充。
- [PostgreSQL查询优化指南](./query-optimization-guide.md)：提供查询优化技术。本文档补充了特定数据类型查询的优化方法。
- [PostgreSQL Schema规划指南](./schema-planning-guide.md)：提供数据库Schema设计和规划指南。本文档补充了使用特定数据类型进行Schema设计的考虑因素。
- [PostgreSQL安全最佳实践指南](./security-best-practices-guide.md)：提供安全最佳实践。本文档补充了特定数据类型的安全使用方法。
- [PostgreSQL事务管理指南](./transaction-management-guide.md)：提供事务管理技术。本文档补充了特定数据类型在事务中的使用注意事项。
- [PostgreSQL演进架构实施指南](../architecture/patterns/postgresql-evolution-implementation-guide.md)：提供演进架构的通用实施模式，本文档是其在特殊数据类型领域的具体应用。

本文档专注于特定数据类型的使用，是对其他PostgreSQL文档的补充，共同构成完整的PostgreSQL使用指南体系。

## 1. JSONB数据类型

### 1.1 基本概念和优势

JSONB是PostgreSQL提供的二进制JSON格式，相比文本格式的JSON类型，JSONB具有以下优势：

- 更快的查询和处理速度
- 支持索引（GIN索引）
- 支持包含、存在、路径查询等丰富的操作符
- 消除重复键和保留键顺序

**适用场景**：
- 半结构化数据存储
- 动态属性和配置
- 嵌套数据结构
- 需要灵活Schema的应用
- 文档存储

### 1.2 创建和索引

**创建JSONB列**：
```sql
-- 创建带JSONB列的表
CREATE TABLE user_preferences (
    user_id BIGINT PRIMARY KEY,
    preferences JSONB NOT NULL DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 插入JSONB数据
INSERT INTO user_preferences (user_id, preferences)
VALUES (
    1001,
    '{"theme": "dark", "notifications": {"email": true, "sms": false}, "favorites": [1, 2, 3]}'::jsonb
);
```

**索引JSONB数据**：
```sql
-- 创建GIN索引（索引整个JSONB文档）
CREATE INDEX idx_user_preferences_gin ON user_preferences USING GIN (preferences);

-- 创建特定路径的GIN索引（仅索引特定键）
CREATE INDEX idx_user_preferences_theme ON user_preferences USING GIN ((preferences->'theme'));

-- 使用jsonb_path_ops操作符类（仅支持@>操作符，但更小更快）
CREATE INDEX idx_user_preferences_path_ops ON user_preferences USING GIN (preferences jsonb_path_ops);
```

### 1.3 查询操作

**基本查询**：
```sql
-- 获取完整JSONB
SELECT preferences FROM user_preferences WHERE user_id = 1001;

-- 获取特定键的值（返回JSONB）
SELECT preferences->'theme' FROM user_preferences WHERE user_id = 1001;

-- 获取特定键的值（返回TEXT）
SELECT preferences->>'theme' FROM user_preferences WHERE user_id = 1001;

-- 获取嵌套值
SELECT preferences->'notifications'->>'email' FROM user_preferences WHERE user_id = 1001;

-- 获取数组元素
SELECT preferences->'favorites'->0 FROM user_preferences WHERE user_id = 1001;
```

**条件查询**：
```sql
-- 包含查询（使用@>操作符）
SELECT * FROM user_preferences
WHERE preferences @> '{"theme": "dark"}'::jsonb;

-- 键存在查询（使用?操作符）
SELECT * FROM user_preferences
WHERE preferences ? 'theme';

-- 任意键存在查询（使用?|操作符）
SELECT * FROM user_preferences
WHERE preferences ?| array['theme', 'language'];

-- 所有键存在查询（使用?&操作符）
SELECT * FROM user_preferences
WHERE preferences ?& array['theme', 'notifications'];

-- 路径查询（使用@>和->组合）
SELECT * FROM user_preferences
WHERE preferences->'notifications' @> '{"email": true}'::jsonb;
```

**高级查询**：
```sql
-- 使用jsonb_path_query函数（PostgreSQL 12+）
SELECT jsonb_path_query(preferences, '$.notifications.email')
FROM user_preferences
WHERE user_id = 1001;

-- 使用jsonb_path_exists函数（PostgreSQL 12+）
SELECT * FROM user_preferences
WHERE jsonb_path_exists(preferences, '$.favorites[*] ? (@ == 2)');

-- 使用jsonb_array_elements展开JSON数组
SELECT user_id, jsonb_array_elements(preferences->'favorites')
FROM user_preferences
WHERE preferences ? 'favorites';

-- 使用jsonb_each展开JSON对象
SELECT user_id, key, value
FROM user_preferences, jsonb_each(preferences)
WHERE user_id = 1001;
```

### 1.4 更新操作

**基本更新**：
```sql
-- 更新整个JSONB文档
UPDATE user_preferences
SET preferences = '{"theme": "light", "notifications": {"email": false}}'::jsonb
WHERE user_id = 1001;

-- 使用jsonb_set更新特定键
UPDATE user_preferences
SET preferences = jsonb_set(preferences, '{theme}', '"light"'::jsonb)
WHERE user_id = 1001;

-- 更新嵌套值
UPDATE user_preferences
SET preferences = jsonb_set(preferences, '{notifications,email}', 'false'::jsonb)
WHERE user_id = 1001;

-- 添加新键值对（使用||操作符）
UPDATE user_preferences
SET preferences = preferences || '{"language": "en"}'::jsonb
WHERE user_id = 1001;

-- 删除键（使用-操作符）
UPDATE user_preferences
SET preferences = preferences - 'language'
WHERE user_id = 1001;

-- 删除嵌套键
UPDATE user_preferences
SET preferences = preferences #- '{notifications,sms}'
WHERE user_id = 1001;
```

**数组操作**：
```sql
-- 向数组添加元素
UPDATE user_preferences
SET preferences = jsonb_set(
    preferences,
    '{favorites}',
    (preferences->'favorites') || '4'::jsonb
)
WHERE user_id = 1001;

-- 从数组删除元素（需要自定义函数）
CREATE OR REPLACE FUNCTION jsonb_array_remove(jsonb_array jsonb, element jsonb)
RETURNS jsonb AS $$
SELECT jsonb_agg(value)
FROM jsonb_array_elements(jsonb_array) AS value
WHERE value <> element
$$ LANGUAGE SQL;

UPDATE user_preferences
SET preferences = jsonb_set(
    preferences,
    '{favorites}',
    jsonb_array_remove(preferences->'favorites', '2'::jsonb)
)
WHERE user_id = 1001;
```

### 1.5 与ORM框架集成

**JPA/Hibernate集成**：
```java
// 使用Hibernate Types库
@Entity
@Table(name = "user_preferences")
public class UserPreferences {
    @Id
    private Long userId;

    @Type(JsonBinaryType.class)
    @Column(name = "preferences", columnDefinition = "jsonb")
    private Map<String, Object> preferences;

    // 构造函数、getter和setter
}

// 使用
@Repository
public interface UserPreferencesRepository extends JpaRepository<UserPreferences, Long> {
    // 使用原生查询
    @Query(value = "SELECT * FROM user_preferences WHERE preferences @> :filter::jsonb", nativeQuery = true)
    List<UserPreferences> findByPreferencesContaining(@Param("filter") String filter);
}
```

**jOOQ集成**：
```java
// 查询JSONB
public UserPreferences getUserPreferences(Long userId) {
    return dslContext.select()
        .from(USER_PREFERENCES)
        .where(USER_PREFERENCES.USER_ID.eq(userId))
        .fetchOneInto(UserPreferences.class);
}

// 条件查询
public List<UserPreferences> findUsersByTheme(String theme) {
    return dslContext.select()
        .from(USER_PREFERENCES)
        .where(field("{0} @> {1}::jsonb", USER_PREFERENCES.PREFERENCES,
            DSL.val(new JSONObject().put("theme", theme).toString())))
        .fetch()
        .into(UserPreferences.class);
}

// 更新JSONB
public void updateUserTheme(Long userId, String theme) {
    dslContext.update(USER_PREFERENCES)
        .set(USER_PREFERENCES.PREFERENCES,
            field("{0} || {1}::jsonb", USER_PREFERENCES.PREFERENCES,
                DSL.val(new JSONObject().put("theme", theme).toString())))
        .where(USER_PREFERENCES.USER_ID.eq(userId))
        .execute();
}
```

### 1.6 演进架构下的JSONB处理

在演进架构模式下，JSONB数据类型的处理需要考虑跨服务的序列化和数据一致性：

**数据类型抽象层实现**：
```java
// JSONB处理服务抽象接口
@ServiceInterface("jsonb-processor-service")
public interface JsonbProcessorService {
    <T> T extractValue(String jsonbData, String path, Class<T> type);
    String updateValue(String jsonbData, String path, Object value);
    boolean containsPath(String jsonbData, String path);
    String mergeJsonb(String jsonb1, String jsonb2);
    String convertForTransport(String jsonbData);
}

// 本地JSONB处理实现
@Service
@ConditionalOnProperty(name = "xkong.services.jsonb-processor-service.mode",
                       havingValue = "LOCAL", matchIfMissing = true)
public class LocalJsonbProcessorService implements JsonbProcessorService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public <T> T extractValue(String jsonbData, String path, Class<T> type) {
        String sql = "SELECT (?::jsonb #> ?::text[])::text";
        String[] pathArray = path.split("\\.");
        String result = jdbcTemplate.queryForObject(sql, String.class, jsonbData, pathArray);
        return convertToType(result, type);
    }

    @Override
    public String updateValue(String jsonbData, String path, Object value) {
        String sql = "SELECT jsonb_set(?::jsonb, ?::text[], ?::jsonb)::text";
        String[] pathArray = ("{" + path.replace(".", ",") + "}").split(",");
        String valueJson = objectMapper.writeValueAsString(value);
        return jdbcTemplate.queryForObject(sql, String.class, jsonbData, pathArray, valueJson);
    }

    @Override
    public boolean containsPath(String jsonbData, String path) {
        String sql = "SELECT jsonb_path_exists(?::jsonb, ?::jsonpath)";
        return jdbcTemplate.queryForObject(sql, Boolean.class, jsonbData, "$." + path);
    }

    private <T> T convertToType(String value, Class<T> type) {
        try {
            return objectMapper.readValue(value, type);
        } catch (Exception e) {
            throw new JsonbProcessingException("无法转换JSONB值到类型: " + type.getSimpleName(), e);
        }
    }
}

// 远程JSONB处理实现
@Service
@ConditionalOnProperty(name = "xkong.services.jsonb-processor-service.mode",
                       havingValue = "REMOTE")
public class RemoteJsonbProcessorService implements JsonbProcessorService {

    @Autowired
    private JsonbProcessorServiceGrpc.JsonbProcessorServiceBlockingStub jsonbStub;

    @Override
    public <T> T extractValue(String jsonbData, String path, Class<T> type) {
        ExtractValueRequest request = ExtractValueRequest.newBuilder()
            .setJsonbData(jsonbData)
            .setPath(path)
            .setTargetType(type.getSimpleName())
            .build();

        ExtractValueResponse response = jsonbStub.extractValue(request);
        return convertToType(response.getValue(), type);
    }

    @Override
    public String convertForTransport(String jsonbData) {
        // 在微服务架构下，可能需要对JSONB数据进行特殊处理以便传输
        // 例如：压缩、加密、格式转换等
        return compressAndEncode(jsonbData);
    }

    private String compressAndEncode(String jsonbData) {
        // 实现压缩和编码逻辑
        return jsonbData; // 简化实现
    }
}
```

**演进架构配置**：
```yaml
# JSONB处理配置
xkong:
  services:
    jsonb-processor-service:
      mode: LOCAL  # LOCAL, REMOTE, HYBRID
      serialization:
        compression: true
        encryption: false
      cache:
        enabled: true
        ttl: 300
```

## 2. 数组类型

### 2.1 基本概念和使用场景

PostgreSQL支持数组数据类型，允许在单个列中存储多个值。数组可以是一维或多维的，可以包含任何有效的PostgreSQL数据类型，包括基本类型、复合类型甚至其他数组。

**适用场景**：
- 存储多值属性（如标签、类别）
- 简单的一对多关系
- 固定长度的列表
- 矩阵和多维数据
- 批量操作

**使用数组vs关联表的考虑因素**：
- 使用数组：当数据量较小、不需要单独查询或索引数组元素、不需要引用完整性约束时
- 使用关联表：当数据量大、需要单独查询或索引元素、需要引用完整性约束时

### 2.2 创建和索引

**创建数组列**：
```sql
-- 创建带数组列的表
CREATE TABLE product (
    product_id BIGINT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    tags TEXT[] NOT NULL DEFAULT '{}',
    ratings INTEGER[] NOT NULL DEFAULT '{}',
    dimensions NUMERIC(10,2)[] NOT NULL DEFAULT '{}'
);

-- 插入数组数据
INSERT INTO product (product_id, name, tags, ratings)
VALUES (
    1001,
    'Smartphone X',
    ARRAY['electronics', 'smartphone', 'premium'],
    ARRAY[4, 5, 3, 5, 4]
);

-- 使用花括号语法插入
INSERT INTO product (product_id, name, tags, dimensions)
VALUES (
    1002,
    'Laptop Pro',
    '{"electronics", "laptop", "business"}',
    '{35.6, 25.4, 1.8}'
);
```

**索引数组**：
```sql
-- 创建GIN索引（支持包含、重叠、相等查询）
CREATE INDEX idx_product_tags ON product USING GIN (tags);

-- 创建特定元素的表达式索引
CREATE INDEX idx_product_first_tag ON product ((tags[1]));

-- 创建数组长度索引
CREATE INDEX idx_product_ratings_length ON product (array_length(ratings, 1));
```

### 2.3 查询和操作

**基本查询**：
```sql
-- 获取完整数组
SELECT tags FROM product WHERE product_id = 1001;

-- 获取特定元素（PostgreSQL数组索引从1开始）
SELECT tags[1] FROM product WHERE product_id = 1001;

-- 获取数组切片
SELECT tags[1:2] FROM product WHERE product_id = 1001;

-- 获取数组长度
SELECT array_length(tags, 1) FROM product WHERE product_id = 1001;

-- 获取多维数组元素
SELECT dimensions[1][2] FROM product WHERE product_id = 1001; -- 对于二维数组
```

**条件查询**：
```sql
-- 精确匹配
SELECT * FROM product
WHERE tags = ARRAY['electronics', 'smartphone', 'premium'];

-- 包含查询（使用@>操作符）
SELECT * FROM product
WHERE tags @> ARRAY['smartphone', 'premium'];

-- 被包含查询（使用<@操作符）
SELECT * FROM product
WHERE ARRAY['smartphone'] <@ tags;

-- 重叠查询（使用&&操作符）
SELECT * FROM product
WHERE tags && ARRAY['smartphone', 'tablet'];

-- 元素存在查询
SELECT * FROM product
WHERE 'smartphone' = ANY(tags);

-- 元素不存在查询
SELECT * FROM product
WHERE 'tablet' <> ALL(tags);

-- 数组长度查询
SELECT * FROM product
WHERE array_length(tags, 1) > 2;
```

**数组操作**：
```sql
-- 数组连接（使用||操作符）
SELECT ARRAY[1, 2] || ARRAY[3, 4]; -- 结果: {1,2,3,4}

-- 数组追加元素
SELECT array_append(ARRAY[1, 2], 3); -- 结果: {1,2,3}

-- 数组前置元素
SELECT array_prepend(0, ARRAY[1, 2]); -- 结果: {0,1,2}

-- 数组移除元素
SELECT array_remove(ARRAY[1, 2, 3, 2], 2); -- 结果: {1,3}

-- 数组替换元素
SELECT array_replace(ARRAY[1, 2, 3, 2], 2, 9); -- 结果: {1,9,3,9}

-- 数组位置查找
SELECT array_position(ARRAY[1, 2, 3], 2); -- 结果: 2

-- 数组交集
SELECT array_cat(ARRAY[1, 2, 3], ARRAY[3, 4, 5]); -- 结果: {1,2,3,3,4,5}
```

**数组展开和聚合**：
```sql
-- 展开数组为行
SELECT product_id, name, unnest(tags) AS tag
FROM product;

-- 聚合行为数组
SELECT product_id, array_agg(tag) AS tags
FROM (
    SELECT 1001 AS product_id, 'electronics' AS tag
    UNION ALL
    SELECT 1001, 'smartphone'
    UNION ALL
    SELECT 1001, 'premium'
) t
GROUP BY product_id;

-- 数组统计
SELECT
    product_id,
    name,
    array_length(ratings, 1) AS rating_count,
    array_to_string(ratings, ',') AS ratings_string,
    (SELECT avg(r) FROM unnest(ratings) r) AS average_rating
FROM product;
```

### 2.4 更新操作

**基本更新**：
```sql
-- 更新整个数组
UPDATE product
SET tags = ARRAY['electronics', 'smartphone', 'budget']
WHERE product_id = 1001;

-- 更新特定元素
UPDATE product
SET tags[3] = 'high-end'
WHERE product_id = 1001;

-- 追加元素
UPDATE product
SET tags = array_append(tags, 'touchscreen')
WHERE product_id = 1001;

-- 移除元素
UPDATE product
SET tags = array_remove(tags, 'budget')
WHERE product_id = 1001;

-- 替换元素
UPDATE product
SET ratings = array_replace(ratings, 3, 4)
WHERE product_id = 1001;
```

**复杂更新**：
```sql
-- 条件追加（仅当元素不存在时）
UPDATE product
SET tags = (
    CASE
        WHEN 'waterproof' = ANY(tags) THEN tags
        ELSE array_append(tags, 'waterproof')
    END
)
WHERE product_id = 1001;

-- 合并数组
UPDATE product
SET tags = (
    SELECT array_agg(DISTINCT tag)
    FROM unnest(tags || ARRAY['new', 'tags']) tag
)
WHERE product_id = 1001;
```

### 2.5 与ORM框架集成

**JPA/Hibernate集成**：
```java
// 使用Hibernate Types库
@Entity
@Table(name = "product")
public class Product {
    @Id
    private Long productId;

    private String name;

    @Type(StringArrayType.class)
    @Column(name = "tags", columnDefinition = "text[]")
    private String[] tags;

    @Type(IntArrayType.class)
    @Column(name = "ratings", columnDefinition = "integer[]")
    private Integer[] ratings;

    // 构造函数、getter和setter
}

// 使用
@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    // 使用原生查询
    @Query(value = "SELECT * FROM product WHERE tags @> CAST(ARRAY[:tags] AS text[])", nativeQuery = true)
    List<Product> findByTagsContaining(@Param("tags") String[] tags);
}
```

**jOOQ集成**：
```java
// 查询数组
public List<Product> findProductsByTag(String tag) {
    return dslContext.selectFrom(PRODUCT)
        .where(field("{0} @> ARRAY[{1}]", PRODUCT.TAGS.getDataType(), DSL.val(tag)))
        .fetch()
        .into(Product.class);
}

// 更新数组
public void addTagToProduct(Long productId, String tag) {
    dslContext.update(PRODUCT)
        .set(PRODUCT.TAGS, field("{0} || ARRAY[{1}]", PRODUCT.TAGS.getDataType(), DSL.val(tag)))
        .where(PRODUCT.PRODUCT_ID.eq(productId))
        .execute();
}
```

## 3. 地理信息数据类型

### 3.1 PostGIS扩展简介

PostGIS是PostgreSQL的空间数据库扩展，它增加了对地理信息数据类型、空间函数和空间索引的支持，使PostgreSQL成为功能强大的地理信息系统(GIS)数据库。

**主要功能**：
- 空间数据类型（点、线、多边形等）
- 空间关系函数（包含、相交、距离等）
- 空间操作函数（缓冲区、简化、合并等）
- 空间索引（提高空间查询性能）
- 坐标系转换
- 3D和4D空间支持

**安装PostGIS**：
```sql
-- 检查是否已安装
SELECT name, default_version FROM pg_available_extensions WHERE name = 'postgis';

-- 安装PostGIS扩展
CREATE EXTENSION postgis;

-- 验证安装
SELECT PostGIS_Version();
```

### 3.2 基本地理数据类型

PostGIS提供两种主要的空间数据类型：

1. **几何类型(geometry)**：使用笛卡尔坐标系（平面），适用于小区域分析
2. **地理类型(geography)**：使用球面坐标系（经纬度），适用于全球范围分析

**创建空间数据表**：
```sql
-- 使用几何类型
CREATE TABLE poi_geometry (
    poi_id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    location GEOMETRY(Point, 4326), -- SRID 4326 = WGS84
    area GEOMETRY(Polygon, 4326),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 使用地理类型
CREATE TABLE poi_geography (
    poi_id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    location GEOGRAPHY(Point, 4326),
    area GEOGRAPHY(Polygon, 4326),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**插入空间数据**：
```sql
-- 插入点数据（使用WKT格式）
INSERT INTO poi_geometry (poi_id, name, location)
VALUES (
    1001,
    'Central Park',
    ST_GeomFromText('POINT(-73.965355 40.782865)', 4326)
);

-- 插入点数据（使用经纬度函数）
INSERT INTO poi_geometry (poi_id, name, location)
VALUES (
    1002,
    'Empire State Building',
    ST_SetSRID(ST_MakePoint(-73.9857, 40.7484), 4326)
);

-- 插入多边形数据
INSERT INTO poi_geometry (poi_id, name, area)
VALUES (
    1003,
    'Manhattan',
    ST_GeomFromText('POLYGON((-74.0201 40.7026, -73.9596 40.7026, -73.9596 40.8212, -74.0201 40.8212, -74.0201 40.7026))', 4326)
);

-- 插入地理类型数据
INSERT INTO poi_geography (poi_id, name, location)
VALUES (
    1001,
    'Central Park',
    ST_GeogFromText('POINT(-73.965355 40.782865)')
);
```

### 3.3 空间索引

**创建空间索引**：
```sql
-- 几何类型索引（使用GIST）
CREATE INDEX idx_poi_geometry_location ON poi_geometry USING GIST (location);
CREATE INDEX idx_poi_geometry_area ON poi_geometry USING GIST (area);

-- 地理类型索引
CREATE INDEX idx_poi_geography_location ON poi_geography USING GIST (location);
CREATE INDEX idx_poi_geography_area ON poi_geography USING GIST (area);
```

**索引使用注意事项**：
- 空间索引对大数据集的空间查询至关重要
- 对于小数据集，全表扫描可能更快
- 索引会增加插入和更新的开销
- 使用EXPLAIN ANALYZE验证索引是否被使用

### 3.4 空间查询

**基本查询**：
```sql
-- 获取点的坐标
SELECT ST_X(location) AS longitude, ST_Y(location) AS latitude
FROM poi_geometry
WHERE poi_id = 1001;

-- 获取WKT表示
SELECT ST_AsText(location) FROM poi_geometry WHERE poi_id = 1001;

-- 获取GeoJSON表示
SELECT ST_AsGeoJSON(location) FROM poi_geometry WHERE poi_id = 1001;
```

**空间关系查询**：
```sql
-- 点在多边形内查询
SELECT p.poi_id, p.name
FROM poi_geometry p, poi_geometry a
WHERE a.poi_id = 1003
AND ST_Contains(a.area, p.location);

-- 距离查询（几何类型，平面距离，单位：度）
SELECT
    poi_id,
    name,
    ST_Distance(
        location,
        ST_SetSRID(ST_MakePoint(-73.9857, 40.7484), 4326)
    ) AS distance
FROM poi_geometry
ORDER BY distance
LIMIT 5;

-- 距离查询（地理类型，球面距离，单位：米）
SELECT
    poi_id,
    name,
    ST_Distance(
        location,
        ST_GeogFromText('POINT(-73.9857 40.7484)')
    ) AS distance_meters
FROM poi_geography
ORDER BY distance_meters
LIMIT 5;

-- 缓冲区查询（地理类型，单位：米）
SELECT poi_id, name
FROM poi_geography
WHERE ST_DWithin(
    location,
    ST_GeogFromText('POINT(-73.9857 40.7484)'),
    1000  -- 1000米半径
);
```

**空间分析**：
```sql
-- 计算面积（地理类型，单位：平方米）
SELECT poi_id, name, ST_Area(area) AS area_sqm
FROM poi_geography
WHERE poi_id = 1003;

-- 计算长度（地理类型，单位：米）
SELECT ST_Length(
    ST_GeogFromText('LINESTRING(-73.9857 40.7484, -73.965355 40.782865)')
) AS distance_meters;

-- 计算两点之间的距离（地理类型，单位：米）
SELECT ST_Distance(
    ST_GeogFromText('POINT(-73.9857 40.7484)'),
    ST_GeogFromText('POINT(-73.965355 40.782865)')
) AS distance_meters;

-- 创建缓冲区（地理类型，单位：米）
SELECT ST_Buffer(
    ST_GeogFromText('POINT(-73.9857 40.7484)'),
    1000  -- 1000米半径
);

-- 计算交集
SELECT ST_Intersection(
    ST_GeomFromText('POLYGON((0 0, 0 1, 1 1, 1 0, 0 0))'),
    ST_GeomFromText('POLYGON((0.5 0.5, 0.5 1.5, 1.5 1.5, 1.5 0.5, 0.5 0.5))')
);
```

### 3.5 与Java应用集成

**使用Hibernate Spatial**：
```java
// 添加依赖
// <dependency>
//     <groupId>org.hibernate</groupId>
//     <artifactId>hibernate-spatial</artifactId>
//     <version>5.6.15.Final</version>
// </dependency>

// 实体类
@Entity
@Table(name = "poi_geometry")
public class PointOfInterest {
    @Id
    private Long poiId;

    private String name;

    @Column(columnDefinition = "geometry(Point,4326)")
    private Point location;

    @Column(columnDefinition = "geometry(Polygon,4326)")
    private Polygon area;

    @Column(name = "created_at")
    private ZonedDateTime createdAt;

    // 构造函数、getter和setter
}

// 使用
@Repository
public interface PoiRepository extends JpaRepository<PointOfInterest, Long> {
    // 使用原生查询
    @Query(value = "SELECT * FROM poi_geometry WHERE ST_DWithin(location, ST_SetSRID(ST_MakePoint(:lon, :lat), 4326), :distance)", nativeQuery = true)
    List<PointOfInterest> findNearby(@Param("lon") double longitude, @Param("lat") double latitude, @Param("distance") double distanceDegrees);
}

// 创建空间对象
@Service
public class SpatialService {
    @PersistenceContext
    private EntityManager entityManager;

    public Point createPoint(double longitude, double latitude) {
        GeometryFactory geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);
        return geometryFactory.createPoint(new Coordinate(longitude, latitude));
    }

    public PointOfInterest createPoi(String name, double longitude, double latitude) {
        PointOfInterest poi = new PointOfInterest();
        poi.setName(name);
        poi.setLocation(createPoint(longitude, latitude));
        return poi;
    }
}
```

**使用jOOQ**：
```java
// 查询附近的POI
public List<PointOfInterest> findNearbyPois(double longitude, double latitude, double radiusMeters) {
    return dslContext.select()
        .from(POI_GEOGRAPHY)
        .where(field("ST_DWithin({0}, {1}, {2})",
            POI_GEOGRAPHY.LOCATION,
            field("ST_GeogFromText({0})", String.format("POINT(%f %f)", longitude, latitude)),
            radiusMeters))
        .orderBy(field("ST_Distance({0}, {1})",
            POI_GEOGRAPHY.LOCATION,
            field("ST_GeogFromText({0})", String.format("POINT(%f %f)", longitude, latitude))))
        .fetch()
        .into(PointOfInterest.class);
}
```

### 3.6 性能优化

**空间查询优化**：
1. **使用适当的空间索引**：对频繁查询的空间列创建GIST索引
2. **选择合适的数据类型**：
   - 小区域分析使用geometry（计算更快）
   - 全球范围分析使用geography（更准确）
3. **限制结果集大小**：使用LIMIT子句或分页
4. **使用ST_DWithin代替距离计算**：ST_DWithin可以更有效地使用空间索引
5. **简化复杂几何体**：使用ST_Simplify减少点数
6. **使用空间聚类**：对大量点数据使用ST_ClusterDBSCAN或ST_ClusterKMeans

**优化示例**：
```sql
-- 优化前：计算所有点的距离然后过滤
SELECT poi_id, name
FROM poi_geography
WHERE ST_Distance(
    location,
    ST_GeogFromText('POINT(-73.9857 40.7484)')
) < 1000;

-- 优化后：使用ST_DWithin（可以利用空间索引）
SELECT poi_id, name
FROM poi_geography
WHERE ST_DWithin(
    location,
    ST_GeogFromText('POINT(-73.9857 40.7484)'),
    1000
);

-- 简化复杂多边形
UPDATE poi_geometry
SET area = ST_Simplify(area, 0.0001)
WHERE poi_id = 1003;
```

## 4. 全文搜索类型

### 4.1 全文搜索概念

PostgreSQL内置了强大的全文搜索功能，无需额外扩展。全文搜索允许高效地搜索自然语言文档，支持词干提取、排名、高亮等高级功能。

**核心概念**：
- **文档(document)**：要搜索的文本内容
- **词条(token)**：文档被分解成的基本单位
- **词素(lexeme)**：词条的标准化形式
- **停用词(stop word)**：常见且无搜索价值的词
- **tsvector**：存储已处理的文档的数据类型
- **tsquery**：表示搜索查询的数据类型
- **配置(configuration)**：定义文档处理方式的规则集

### 4.2 tsvector和tsquery类型

**tsvector类型**：
- 存储已处理的文档，包含词素及其位置
- 移除停用词，应用词干提取
- 支持索引和高效搜索

**tsquery类型**：
- 表示搜索查询
- 支持布尔操作符：& (AND), | (OR), ! (NOT)
- 支持短语搜索和邻近搜索

**创建全文搜索表**：
```sql
CREATE TABLE article (
    article_id BIGINT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    ts_title TSVECTOR GENERATED ALWAYS AS (to_tsvector('english', title)) STORED,
    ts_content TSVECTOR GENERATED ALWAYS AS (to_tsvector('english', content)) STORED,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 或手动更新
CREATE TABLE article_manual (
    article_id BIGINT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    ts_vector TSVECTOR,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 手动更新触发器
CREATE OR REPLACE FUNCTION article_tsvector_update_trigger() RETURNS trigger AS $$
BEGIN
    NEW.ts_vector = setweight(to_tsvector('english', NEW.title), 'A') ||
                    setweight(to_tsvector('english', COALESCE(NEW.content, '')), 'B');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER tsvector_update BEFORE INSERT OR UPDATE
ON article_manual FOR EACH ROW EXECUTE FUNCTION article_tsvector_update_trigger();
```

**插入数据**：
```sql
INSERT INTO article (article_id, title, content)
VALUES (
    1001,
    'PostgreSQL Full Text Search Tutorial',
    'PostgreSQL offers powerful full text search capabilities. This tutorial explains how to use tsvector and tsquery types effectively.'
);

INSERT INTO article (article_id, title, content)
VALUES (
    1002,
    'Advanced Database Features',
    'Modern databases like PostgreSQL provide advanced features including full text search, JSON support, and spatial data handling.'
);
```

### 4.3 全文搜索索引

**创建全文搜索索引**：
```sql
-- GIN索引（适用于静态数据，索引较大但查询更快）
CREATE INDEX idx_article_ts_title ON article USING GIN (ts_title);
CREATE INDEX idx_article_ts_content ON article USING GIN (ts_content);

-- 组合索引
CREATE INDEX idx_article_ts_combined ON article_manual USING GIN (ts_vector);

-- GiST索引（适用于动态数据，索引较小但查询较慢）
CREATE INDEX idx_article_ts_title_gist ON article USING GIST (ts_title);
```

**索引使用注意事项**：
- GIN索引适合静态数据，查询性能更好
- GiST索引适合频繁更新的数据，更新性能更好
- 对于大型文档集合，索引是必要的
- 索引会增加插入和更新的开销

### 4.4 全文搜索查询

**基本查询**：
```sql
-- 使用@@操作符进行匹配
SELECT article_id, title
FROM article
WHERE ts_title @@ to_tsquery('english', 'postgresql');

-- 搜索标题和内容
SELECT article_id, title
FROM article
WHERE ts_title @@ to_tsquery('english', 'postgresql') OR
      ts_content @@ to_tsquery('english', 'postgresql');

-- 使用布尔操作符
SELECT article_id, title
FROM article
WHERE ts_content @@ to_tsquery('english', 'postgresql & search & !mysql');

-- 短语搜索
SELECT article_id, title
FROM article
WHERE ts_content @@ phraseto_tsquery('english', 'full text search');

-- 使用plainto_tsquery（将查询文本转换为tsquery，忽略特殊字符）
SELECT article_id, title
FROM article
WHERE ts_content @@ plainto_tsquery('english', 'PostgreSQL full text search');

-- 使用websearch_to_tsquery（更自然的网络搜索语法，PostgreSQL 11+）
SELECT article_id, title
FROM article
WHERE ts_content @@ websearch_to_tsquery('english', 'PostgreSQL -mysql "full text"');
```

**排名和排序**：
```sql
-- 使用ts_rank计算相关性排名
SELECT article_id, title, ts_rank(ts_content, query) AS rank
FROM article, to_tsquery('english', 'postgresql & search') query
WHERE ts_content @@ query
ORDER BY rank DESC;

-- 使用ts_rank_cd（考虑词条密度）
SELECT article_id, title, ts_rank_cd(ts_content, query) AS rank
FROM article, to_tsquery('english', 'postgresql & search') query
WHERE ts_content @@ query
ORDER BY rank DESC;

-- 组合标题和内容的排名（标题权重更高）
SELECT article_id, title,
       ts_rank(ts_title, query) * 0.5 +
       ts_rank(ts_content, query) * 0.5 AS rank
FROM article, to_tsquery('english', 'postgresql') query
WHERE ts_title @@ query OR ts_content @@ query
ORDER BY rank DESC;
```

**高亮搜索结果**：
```sql
-- 使用ts_headline高亮匹配的文本
SELECT article_id, title,
       ts_headline('english', content, to_tsquery('english', 'postgresql'),
                  'StartSel=<b>, StopSel=</b>, MaxWords=50, MinWords=5') AS headline
FROM article
WHERE ts_content @@ to_tsquery('english', 'postgresql');
```

### 4.5 自定义全文搜索配置

**查看可用配置**：
```sql
-- 列出所有可用配置
SELECT cfgname FROM pg_ts_config;

-- 查看特定配置的详细信息
SELECT * FROM pg_ts_config WHERE cfgname = 'english';
```

**创建自定义配置**：
```sql
-- 基于现有配置创建新配置
CREATE TEXT SEARCH CONFIGURATION my_config (COPY = english);

-- 修改停用词
ALTER TEXT SEARCH CONFIGURATION my_config
    DROP MAPPING FOR asciiword, asciihword, hword, hword_part, word, hword_asciipart;

ALTER TEXT SEARCH CONFIGURATION my_config
    ADD MAPPING FOR asciiword, asciihword, hword, hword_part, word, hword_asciipart
    WITH english_stem;

-- 使用自定义配置
SELECT to_tsvector('my_config', 'The PostgreSQL database system');
```

**自定义词典**：
```sql
-- 创建同义词词典
CREATE TEXT SEARCH DICTIONARY my_synonym (
    TEMPLATE = synonym,
    SYNONYMS = my_synonyms
);

-- 创建同义词文件 (my_synonyms)
-- db postgresql database
-- rdbms postgresql oracle mysql

-- 将词典添加到配置
ALTER TEXT SEARCH CONFIGURATION my_config
    ALTER MAPPING FOR asciiword
    WITH my_synonym, english_stem;
```

### 4.6 与Java应用集成

**使用JPA/Hibernate**：
```java
@Entity
@Table(name = "article")
public class Article {
    @Id
    private Long articleId;

    private String title;

    @Column(columnDefinition = "TEXT")
    private String content;

    @Column(name = "ts_vector", columnDefinition = "tsvector")
    private String tsVector;

    @Column(name = "created_at")
    private ZonedDateTime createdAt;

    // 构造函数、getter和setter
}

@Repository
public interface ArticleRepository extends JpaRepository<Article, Long> {
    // 使用原生查询进行全文搜索
    @Query(value = "SELECT a.* FROM article a WHERE a.ts_vector @@ plainto_tsquery('english', :query) ORDER BY ts_rank(a.ts_vector, plainto_tsquery('english', :query)) DESC", nativeQuery = true)
    List<Article> fullTextSearch(@Param("query") String query);

    // 带高亮的搜索
    @Query(value = "SELECT a.*, ts_headline('english', a.content, plainto_tsquery('english', :query), 'StartSel=<b>, StopSel=</b>') AS headline FROM article a WHERE a.ts_vector @@ plainto_tsquery('english', :query) ORDER BY ts_rank(a.ts_vector, plainto_tsquery('english', :query)) DESC", nativeQuery = true)
    List<Object[]> fullTextSearchWithHighlight(@Param("query") String query);
}
```

**使用jOOQ**：
```java
// 全文搜索查询
public List<Article> searchArticles(String query) {
    return dslContext.select()
        .from(ARTICLE)
        .where(field("{0} @@ plainto_tsquery({1}, {2})",
            ARTICLE.TS_VECTOR,
            inline("english"),
            inline(query)))
        .orderBy(field("ts_rank({0}, plainto_tsquery({1}, {2})) DESC",
            ARTICLE.TS_VECTOR,
            inline("english"),
            inline(query)))
        .fetch()
        .into(Article.class);
}

// 带高亮的搜索
public List<ArticleSearchResult> searchArticlesWithHighlight(String query) {
    return dslContext.select(
            ARTICLE.ARTICLE_ID,
            ARTICLE.TITLE,
            field("ts_headline({0}, {1}, plainto_tsquery({2}, {3}), {4})",
                inline("english"),
                ARTICLE.CONTENT,
                inline("english"),
                inline(query),
                inline("StartSel=<b>, StopSel=</b>")))
        .from(ARTICLE)
        .where(field("{0} @@ plainto_tsquery({1}, {2})",
            ARTICLE.TS_VECTOR,
            inline("english"),
            inline(query)))
        .orderBy(field("ts_rank({0}, plainto_tsquery({1}, {2})) DESC",
            ARTICLE.TS_VECTOR,
            inline("english"),
            inline(query)))
        .fetch()
        .into(ArticleSearchResult.class);
}
```

### 4.7 性能优化

**全文搜索优化**：
1. **使用适当的索引**：对tsvector列创建GIN索引
2. **预计算tsvector**：使用生成列或触发器预计算tsvector
3. **限制结果集大小**：使用LIMIT子句或分页
4. **优化查询表达式**：使用更具体的查询条件
5. **考虑使用外部全文搜索引擎**：对于非常大的数据集或复杂的搜索需求，考虑使用Elasticsearch或Solr

**优化示例**：
```sql
-- 优化前：每次查询时计算tsvector
SELECT article_id, title
FROM article
WHERE to_tsvector('english', content) @@ to_tsquery('english', 'postgresql');

-- 优化后：使用预计算的tsvector列和索引
SELECT article_id, title
FROM article
WHERE ts_content @@ to_tsquery('english', 'postgresql');

-- 优化查询表达式
-- 优化前：使用plainto_tsquery（可能生成多个AND连接的词条）
WHERE ts_content @@ plainto_tsquery('english', 'postgresql database system');

-- 优化后：使用更精确的tsquery表达式
WHERE ts_content @@ to_tsquery('english', 'postgresql & (database | system)');
```

## 5. 其他特殊数据类型

### 5.1 枚举类型

PostgreSQL的枚举类型允许定义一组固定的值，提供类型安全和代码可读性。

**创建和使用枚举**：
```sql
-- 创建枚举类型
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended', 'deleted');

-- 创建使用枚举的表
CREATE TABLE app_user (
    user_id BIGINT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    status user_status NOT NULL DEFAULT 'inactive',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 插入数据
INSERT INTO app_user (user_id, username, status)
VALUES (1001, 'john_doe', 'active');

-- 查询特定状态的用户
SELECT * FROM app_user WHERE status = 'active';

-- 排序（按枚举定义顺序）
SELECT * FROM app_user ORDER BY status;
```

**修改枚举类型**：
```sql
-- 添加新值（只能添加到末尾）
ALTER TYPE user_status ADD VALUE 'pending' AFTER 'inactive';

-- 重命名值（PostgreSQL 10+）
ALTER TYPE user_status RENAME VALUE 'deleted' TO 'removed';
```

**与Java集成**：
```java
// 使用JPA/Hibernate
@Entity
@Table(name = "app_user")
public class AppUser {
    @Id
    private Long userId;

    private String username;

    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = "user_status")
    private UserStatus status;

    // 枚举定义
    public enum UserStatus {
        active, inactive, suspended, deleted, pending
    }
}

// 使用jOOQ
public List<AppUser> findUsersByStatus(UserStatus status) {
    return dslContext.selectFrom(APP_USER)
        .where(APP_USER.STATUS.eq(status.name()))
        .fetch()
        .into(AppUser.class);
}
```

**最佳实践**：
- 使用枚举类型表示固定的、不经常变化的值集合
- 避免频繁修改枚举类型，因为这可能需要表结构变更
- 在Java代码中保持枚举值与数据库一致
- 考虑使用lookup表代替枚举，如果值集合可能频繁变化

### 5.2 范围类型

PostgreSQL的范围类型允许存储和操作值的范围，支持包含、重叠、相等等操作。

**内置范围类型**：
- `int4range`：整数范围
- `int8range`：长整数范围
- `numrange`：数值范围
- `tsrange`：时间戳范围（不带时区）
- `tstzrange`：时间戳范围（带时区）
- `daterange`：日期范围

**创建和使用范围**：
```sql
-- 创建使用范围的表
CREATE TABLE reservation (
    reservation_id BIGINT PRIMARY KEY,
    room_id BIGINT NOT NULL,
    period tstzrange NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建排他约束（防止时间重叠）
ALTER TABLE reservation ADD CONSTRAINT no_overlapping_reservations
    EXCLUDE USING gist (room_id WITH =, period WITH &&);

-- 插入数据
INSERT INTO reservation (reservation_id, room_id, period)
VALUES (
    1001,
    101,
    tstzrange(
        '2025-07-01 14:00:00+08',
        '2025-07-01 16:00:00+08',
        '[)'  -- 左闭右开区间
    )
);

-- 查询特定时间点的预订
SELECT * FROM reservation
WHERE period @> '2025-07-01 15:00:00+08'::timestamptz;

-- 查询重叠的预订
SELECT * FROM reservation
WHERE period && tstzrange('2025-07-01 15:30:00+08', '2025-07-01 17:30:00+08');

-- 查询特定日期的所有预订
SELECT * FROM reservation
WHERE period && tstzrange(
    '2025-07-01'::date,
    '2025-07-02'::date,
    '[)'
);
```

**范围操作符**：
- `@>` 包含
- `<@` 被包含
- `&&` 重叠
- `<<` 严格左侧
- `>>` 严格右侧
- `-|-` 相邻
- `+` 并集
- `*` 交集
- `-` 差集

**与Java集成**：
```java
// 使用Hibernate Types库
@Entity
@Table(name = "reservation")
public class Reservation {
    @Id
    private Long reservationId;

    private Long roomId;

    @Type(PostgreSQLTSTZRangeType.class)
    @Column(name = "period", columnDefinition = "tstzrange")
    private Range<ZonedDateTime> period;

    // 构造函数、getter和setter
}

// 创建范围
public Range<ZonedDateTime> createRange(ZonedDateTime start, ZonedDateTime end) {
    return Range.closed(start, end);
}

// 使用jOOQ
public List<Reservation> findOverlappingReservations(ZonedDateTime start, ZonedDateTime end) {
    return dslContext.selectFrom(RESERVATION)
        .where(field("{0} && {1}::tstzrange",
            RESERVATION.PERIOD,
            DSL.val(String.format("[%s,%s)", start, end))))
        .fetch()
        .into(Reservation.class);
}
```

### 5.3 网络地址类型

PostgreSQL提供了专门的网络地址数据类型，支持IP地址和MAC地址的存储和操作。

**网络地址类型**：
- `inet`：IPv4和IPv6地址，可选子网掩码
- `cidr`：IPv4和IPv6网络地址
- `macaddr`：MAC地址
- `macaddr8`：EUI-64格式的MAC地址（PostgreSQL 10+）

**创建和使用网络地址**：
```sql
-- 创建使用网络地址的表
CREATE TABLE network_device (
    device_id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    ip_address inet NOT NULL,
    network cidr NOT NULL,
    mac_address macaddr NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 插入数据
INSERT INTO network_device (device_id, name, ip_address, network, mac_address)
VALUES (
    1001,
    'Router-01',
    '***********',
    '***********/24',
    '08:00:2b:01:02:03'
);

-- 查询特定网络的设备
SELECT * FROM network_device
WHERE ip_address << '***********/24';

-- 检查IP是否在网络中
SELECT * FROM network_device
WHERE network >> '*************';

-- 计算子网中的主机数
SELECT device_id, name, network,
       2^(32-masklen(network)) AS max_hosts
FROM network_device
WHERE family(network) = 4;  -- IPv4
```

**网络操作符**：
- `<<` 包含于（子网）
- `<<=` 包含于或等于
- `>>` 包含（子网）
- `>>=` 包含或等于
- `&&` 重叠
- `~` 按位非
- `&` 按位与
- `|` 按位或
- `+` 加法
- `-` 减法

**与Java集成**：
```java
// 使用JPA/Hibernate
@Entity
@Table(name = "network_device")
public class NetworkDevice {
    @Id
    private Long deviceId;

    private String name;

    @Column(name = "ip_address", columnDefinition = "inet")
    private String ipAddress;

    @Column(name = "network", columnDefinition = "cidr")
    private String network;

    @Column(name = "mac_address", columnDefinition = "macaddr")
    private String macAddress;

    // 构造函数、getter和setter
}

// 使用jOOQ
public List<NetworkDevice> findDevicesInNetwork(String network) {
    return dslContext.selectFrom(NETWORK_DEVICE)
        .where(field("{0} << {1}::cidr", NETWORK_DEVICE.IP_ADDRESS, network))
        .fetch()
        .into(NetworkDevice.class);
}
```

### 5.4 UUID类型

UUID（通用唯一标识符）类型用于存储RFC 4122标准的UUID值，常用于分布式系统中的唯一标识符。

**创建和使用UUID**：
```sql
-- 创建使用UUID的表
CREATE TABLE document (
    document_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(200) NOT NULL,
    content TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 插入数据（自动生成UUID）
INSERT INTO document (title, content)
VALUES ('Sample Document', 'This is a sample document content.');

-- 插入数据（指定UUID）
INSERT INTO document (document_id, title, content)
VALUES (
    'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    'Another Document',
    'This is another document content.'
);

-- 查询特定UUID
SELECT * FROM document
WHERE document_id = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
```

**UUID生成函数**：
- `gen_random_uuid()`：生成随机UUID（PostgreSQL 13+）
- `uuid_generate_v1()`：生成基于时间和MAC地址的UUID（需要uuid-ossp扩展）
- `uuid_generate_v4()`：生成随机UUID（需要uuid-ossp扩展）

**与Java集成**：
```java
// 使用JPA/Hibernate
@Entity
@Table(name = "document")
public class Document {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "document_id", columnDefinition = "uuid")
    private UUID documentId;

    private String title;

    @Column(columnDefinition = "TEXT")
    private String content;

    // 构造函数、getter和setter
}

// 使用jOOQ
public Document findDocumentById(UUID documentId) {
    return dslContext.selectFrom(DOCUMENT)
        .where(DOCUMENT.DOCUMENT_ID.eq(documentId))
        .fetchOneInto(Document.class);
}
```

### 5.5 XML类型

PostgreSQL的XML类型用于存储XML数据，支持XML验证和XPath查询。

**创建和使用XML**：
```sql
-- 创建使用XML的表
CREATE TABLE xml_document (
    document_id BIGINT PRIMARY KEY,
    content XML NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 插入数据
INSERT INTO xml_document (document_id, content)
VALUES (
    1001,
    '<document>
        <title>Sample XML Document</title>
        <author>John Doe</author>
        <content>This is a sample XML document.</content>
        <tags>
            <tag>xml</tag>
            <tag>sample</tag>
            <tag>document</tag>
        </tags>
    </document>'
);

-- 使用XPath查询
SELECT
    document_id,
    (xpath('/document/title/text()', content))[1]::text AS title,
    (xpath('/document/author/text()', content))[1]::text AS author
FROM xml_document;

-- 检查XML是否包含特定元素
SELECT * FROM xml_document
WHERE xpath_exists('/document/tags/tag[text()="xml"]', content);

-- 提取所有标签
SELECT
    document_id,
    unnest(xpath('/document/tags/tag/text()', content))::text AS tag
FROM xml_document;
```

**XML函数**：
- `xmlparse()`：将字符串解析为XML
- `xmlserialize()`：将XML序列化为字符串
- `xpath()`：执行XPath查询
- `xpath_exists()`：检查XPath表达式是否匹配
- `xml_is_well_formed()`：检查XML是否格式良好

**与Java集成**：
```java
// 使用JPA/Hibernate
@Entity
@Table(name = "xml_document")
public class XmlDocument {
    @Id
    private Long documentId;

    @Column(name = "content", columnDefinition = "xml")
    private String content;

    // 构造函数、getter和setter
}

// 使用JAXB处理XML
@Service
public class XmlService {
    public String convertToXml(Document document) throws JAXBException {
        JAXBContext context = JAXBContext.newInstance(Document.class);
        Marshaller marshaller = context.createMarshaller();
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);

        StringWriter writer = new StringWriter();
        marshaller.marshal(document, writer);
        return writer.toString();
    }

    public Document convertFromXml(String xml) throws JAXBException {
        JAXBContext context = JAXBContext.newInstance(Document.class);
        Unmarshaller unmarshaller = context.createUnmarshaller();

        return (Document) unmarshaller.unmarshal(new StringReader(xml));
    }
}
```

### 5.6 货币类型

PostgreSQL的货币类型用于存储固定精度的货币金额，自动处理格式化和四舍五入。

**创建和使用货币**：
```sql
-- 创建使用货币的表
CREATE TABLE product_price (
    product_id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    price MONEY NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 插入数据
INSERT INTO product_price (product_id, name, price)
VALUES (1001, 'Product A', '$99.99');

INSERT INTO product_price (product_id, name, price)
VALUES (1002, 'Product B', '199.50');

-- 查询和计算
SELECT
    product_id,
    name,
    price,
    price * 0.9 AS discounted_price,
    price * 1.1 AS price_with_tax
FROM product_price;

-- 排序
SELECT * FROM product_price
ORDER BY price DESC;

-- 汇总
SELECT SUM(price) AS total_price
FROM product_price;
```

**货币类型注意事项**：
- 货币类型的精度和格式取决于数据库的locale设置
- 货币符号的显示也取决于locale
- 货币类型不适合需要精确计算的金融应用，这种情况应使用NUMERIC类型
- 货币类型主要用于显示目的

**与Java集成**：
```java
// 使用JPA/Hibernate
@Entity
@Table(name = "product_price")
public class ProductPrice {
    @Id
    private Long productId;

    private String name;

    @Column(name = "price", columnDefinition = "money")
    private BigDecimal price;

    // 构造函数、getter和setter
}

// 使用jOOQ
public List<ProductPrice> findProductsInPriceRange(BigDecimal min, BigDecimal max) {
    return dslContext.selectFrom(PRODUCT_PRICE)
        .where(PRODUCT_PRICE.PRICE.between(min, max))
        .orderBy(PRODUCT_PRICE.PRICE)
        .fetch()
        .into(ProductPrice.class);
}
```

### 5.7 自定义复合类型

PostgreSQL允许创建自定义复合类型，将多个字段组合成一个单一的数据类型。

**创建和使用复合类型**：
```sql
-- 创建复合类型
CREATE TYPE address AS (
    street VARCHAR(100),
    city VARCHAR(50),
    state VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(50)
);

-- 创建使用复合类型的表
CREATE TABLE customer (
    customer_id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    billing_address address,
    shipping_address address,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 插入数据
INSERT INTO customer (customer_id, name, billing_address, shipping_address)
VALUES (
    1001,
    'John Doe',
    ROW('123 Main St', 'New York', 'NY', '10001', 'USA'),
    ROW('123 Main St', 'New York', 'NY', '10001', 'USA')
);

-- 不同的地址
INSERT INTO customer (customer_id, name, billing_address, shipping_address)
VALUES (
    1002,
    'Jane Smith',
    ROW('456 Park Ave', 'Boston', 'MA', '02108', 'USA'),
    ROW('789 Broadway', 'New York', 'NY', '10003', 'USA')
);

-- 查询复合类型
SELECT
    customer_id,
    name,
    billing_address.city AS billing_city,
    shipping_address.city AS shipping_city
FROM customer;

-- 查询特定城市的客户
SELECT * FROM customer
WHERE billing_address.city = 'New York';

-- 更新复合类型的字段
UPDATE customer
SET billing_address.postal_code = '10002'
WHERE customer_id = 1001;

-- 更新整个复合类型
UPDATE customer
SET shipping_address = ROW('555 5th Ave', 'New York', 'NY', '10017', 'USA')
WHERE customer_id = 1001;
```

**复合类型数组**：
```sql
-- 创建使用复合类型数组的表
CREATE TABLE contact (
    contact_id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    addresses address[] NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 插入数据
INSERT INTO contact (contact_id, name, addresses)
VALUES (
    1001,
    'John Doe',
    ARRAY[
        ROW('123 Main St', 'New York', 'NY', '10001', 'USA'),
        ROW('456 Park Ave', 'Boston', 'MA', '02108', 'USA')
    ]
);

-- 查询复合类型数组
SELECT
    contact_id,
    name,
    addresses[1].city AS primary_city,
    addresses[2].city AS secondary_city
FROM contact;

-- 展开数组
SELECT
    contact_id,
    name,
    (addr).city AS city,
    (addr).state AS state
FROM contact, unnest(addresses) AS addr;
```

**与Java集成**：
```java
// 使用JPA/Hibernate
@Entity
@Table(name = "customer")
public class Customer {
    @Id
    private Long customerId;

    private String name;

    @Type(JsonBinaryType.class)
    @Column(name = "billing_address", columnDefinition = "address")
    private Address billingAddress;

    @Type(JsonBinaryType.class)
    @Column(name = "shipping_address", columnDefinition = "address")
    private Address shippingAddress;

    // 地址类
    @Embeddable
    public static class Address {
        private String street;
        private String city;
        private String state;
        private String postalCode;
        private String country;

        // 构造函数、getter和setter
    }
}

// 使用jOOQ
public List<Customer> findCustomersByCity(String city) {
    return dslContext.selectFrom(CUSTOMER)
        .where(field("billing_address->>'city'").eq(city))
        .fetch()
        .into(Customer.class);
}
```

## 6. ORM框架集成

### 6.1 JPA/Hibernate与特殊数据类型

Hibernate是Java中最流行的ORM框架之一，通过JPA规范提供了与PostgreSQL特殊数据类型的集成。

**基本配置**：
```java
// application.properties或application.yml（现代化配置）
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.boot.allow_jdbc_metadata_access=true
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

// 传统配置（仅在特殊需求下使用）
// spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
// spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
// spring.jpa.properties.hibernate.boot.allow_jdbc_metadata_access=false
```

**使用Hibernate Types库**：
```xml
<!-- pom.xml -->
<dependency>
    <groupId>com.vladmihalcea</groupId>
    <artifactId>hibernate-types-52</artifactId>
    <version>2.16.2</version>
</dependency>
```

**JSONB类型映射**：
```java
@Entity
@Table(name = "user_profile")
@TypeDefs({
    @TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
})
public class UserProfile {
    @Id
    private Long id;

    private String username;

    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    private Map<String, Object> attributes;

    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    private List<String> roles;

    // 构造函数、getter和setter
}

// 使用
@Repository
public interface UserProfileRepository extends JpaRepository<UserProfile, Long> {
    // 使用原生查询
    @Query(value = "SELECT * FROM user_profile WHERE attributes @> :filter::jsonb", nativeQuery = true)
    List<UserProfile> findByAttributesContaining(@Param("filter") String filter);
}
```

**数组类型映射**：
```java
@Entity
@Table(name = "product")
@TypeDefs({
    @TypeDef(name = "string-array", typeClass = StringArrayType.class),
    @TypeDef(name = "int-array", typeClass = IntArrayType.class)
})
public class Product {
    @Id
    private Long id;

    private String name;

    @Type(type = "string-array")
    @Column(columnDefinition = "text[]")
    private String[] tags;

    @Type(type = "int-array")
    @Column(columnDefinition = "integer[]")
    private Integer[] ratings;

    // 构造函数、getter和setter
}
```

**枚举类型映射**：
```java
@Entity
@Table(name = "app_user")
public class AppUser {
    @Id
    private Long id;

    private String username;

    // 使用字符串存储枚举
    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = "user_status")
    private UserStatus status;

    // 枚举定义
    public enum UserStatus {
        active, inactive, suspended, deleted
    }
}
```

**UUID类型映射**：
```java
@Entity
@Table(name = "document")
public class Document {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "uuid")
    private UUID id;

    private String title;

    @Column(columnDefinition = "TEXT")
    private String content;

    // 构造函数、getter和setter
}
```

**地理信息类型映射**：
```java
// 添加依赖
// <dependency>
//     <groupId>org.hibernate</groupId>
//     <artifactId>hibernate-spatial</artifactId>
// </dependency>

@Entity
@Table(name = "location")
public class Location {
    @Id
    private Long id;

    private String name;

    @Column(columnDefinition = "geometry(Point,4326)")
    private Point point;

    @Column(columnDefinition = "geometry(Polygon,4326)")
    private Polygon area;

    // 构造函数、getter和setter
}

// 使用
@Repository
public interface LocationRepository extends JpaRepository<Location, Long> {
    @Query(value = "SELECT * FROM location WHERE ST_DWithin(point, ST_SetSRID(ST_MakePoint(:lon, :lat), 4326), :distance)", nativeQuery = true)
    List<Location> findNearby(@Param("lon") double longitude, @Param("lat") double latitude, @Param("distance") double distanceDegrees);
}
```

**范围类型映射**：
```java
@Entity
@Table(name = "reservation")
@TypeDefs({
    @TypeDef(name = "tstzrange", typeClass = PostgreSQLTSTZRangeType.class)
})
public class Reservation {
    @Id
    private Long id;

    private String name;

    @Type(type = "tstzrange")
    @Column(columnDefinition = "tstzrange")
    private Range<ZonedDateTime> period;

    // 构造函数、getter和setter
}

// 使用
@Repository
public interface ReservationRepository extends JpaRepository<Reservation, Long> {
    @Query(value = "SELECT * FROM reservation WHERE period @> :point::timestamptz", nativeQuery = true)
    List<Reservation> findByTimePoint(@Param("point") String timePoint);

    @Query(value = "SELECT * FROM reservation WHERE period && :range::tstzrange", nativeQuery = true)
    List<Reservation> findOverlapping(@Param("range") String range);
}
```

### 6.2 jOOQ与特殊数据类型

jOOQ是一个基于Java的类型安全的SQL生成库，提供了与PostgreSQL特殊数据类型的强大集成。

**基本配置**：
```xml
<!-- pom.xml -->
<dependency>
    <groupId>org.jooq</groupId>
    <artifactId>jooq</artifactId>
    <version>3.16.6</version>
</dependency>
<dependency>
    <groupId>org.jooq</groupId>
    <artifactId>jooq-meta</artifactId>
    <version>3.16.6</version>
</dependency>
<dependency>
    <groupId>org.jooq</groupId>
    <artifactId>jooq-codegen</artifactId>
    <version>3.16.6</version>
</dependency>
```

**JSONB类型操作**：
```java
// 查询JSONB
public List<UserProfile> findUsersByAttribute(String key, String value) {
    return dslContext.selectFrom(USER_PROFILE)
        .where(field("{0} @> {1}::jsonb",
            USER_PROFILE.ATTRIBUTES,
            DSL.val(String.format("{\"%s\": \"%s\"}", key, value))))
        .fetch()
        .into(UserProfile.class);
}

// 更新JSONB
public void updateUserAttribute(Long userId, String key, String value) {
    dslContext.update(USER_PROFILE)
        .set(USER_PROFILE.ATTRIBUTES,
            field("jsonb_set({0}, {1}, {2})",
                USER_PROFILE.ATTRIBUTES,
                DSL.val(String.format("{\"%s\"}", key)),
                DSL.val(String.format("\"%s\"", value))))
        .where(USER_PROFILE.ID.eq(userId))
        .execute();
}
```

**数组类型操作**：
```java
// 查询数组包含
public List<Product> findProductsByTag(String tag) {
    return dslContext.selectFrom(PRODUCT)
        .where(field("{0} @> ARRAY[{1}]::text[]", PRODUCT.TAGS, DSL.val(tag)))
        .fetch()
        .into(Product.class);
}

// 更新数组
public void addTagToProduct(Long productId, String tag) {
    dslContext.update(PRODUCT)
        .set(PRODUCT.TAGS, field("{0} || ARRAY[{1}]::text[]", PRODUCT.TAGS, DSL.val(tag)))
        .where(PRODUCT.ID.eq(productId))
        .execute();
}
```

**地理信息类型操作**：
```java
// 查询附近的位置
public List<Location> findNearbyLocations(double longitude, double latitude, double radiusMeters) {
    return dslContext.selectFrom(LOCATION)
        .where(field("ST_DWithin({0}, ST_SetSRID(ST_MakePoint({1}, {2}), 4326), {3})",
            LOCATION.POINT,
            DSL.val(longitude),
            DSL.val(latitude),
            DSL.val(radiusMeters / 111320))) // 转换为度
        .orderBy(field("ST_Distance({0}, ST_SetSRID(ST_MakePoint({1}, {2}), 4326))",
            LOCATION.POINT,
            DSL.val(longitude),
            DSL.val(latitude)))
        .fetch()
        .into(Location.class);
}
```

**范围类型操作**：
```java
// 查询重叠的预订
public List<Reservation> findOverlappingReservations(ZonedDateTime start, ZonedDateTime end) {
    String range = String.format("[%s,%s)", start, end);
    return dslContext.selectFrom(RESERVATION)
        .where(field("{0} && {1}::tstzrange", RESERVATION.PERIOD, DSL.val(range)))
        .fetch()
        .into(Reservation.class);
}

// 查询特定时间点的预订
public List<Reservation> findReservationsByTimePoint(ZonedDateTime timePoint) {
    return dslContext.selectFrom(RESERVATION)
        .where(field("{0} @> {1}::timestamptz", RESERVATION.PERIOD, DSL.val(timePoint)))
        .fetch()
        .into(Reservation.class);
}
```

**枚举类型操作**：
```java
// 查询特定状态的用户
public List<AppUser> findUsersByStatus(UserStatus status) {
    return dslContext.selectFrom(APP_USER)
        .where(APP_USER.STATUS.eq(status.name()))
        .fetch()
        .into(AppUser.class);
}
```

**UUID类型操作**：
```java
// 查询特定UUID的文档
public Document findDocumentById(UUID id) {
    return dslContext.selectFrom(DOCUMENT)
        .where(DOCUMENT.ID.eq(id))
        .fetchOneInto(Document.class);
}

// 生成随机UUID
public UUID insertDocumentWithRandomId(String title, String content) {
    UUID id = UUID.randomUUID();
    dslContext.insertInto(DOCUMENT)
        .set(DOCUMENT.ID, id)
        .set(DOCUMENT.TITLE, title)
        .set(DOCUMENT.CONTENT, content)
        .execute();
    return id;
}
```

### 6.3 Spring JDBC与特殊数据类型

Spring JDBC提供了一种更轻量级的方式来处理PostgreSQL特殊数据类型。

**基本配置**：
```java
@Configuration
public class JdbcConfig {
    @Bean
    public JdbcTemplate jdbcTemplate(DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }
}
```

**JSONB类型操作**：
```java
@Repository
public class UserProfileJdbcRepository {
    private final NamedParameterJdbcTemplate jdbcTemplate;

    public UserProfileJdbcRepository(NamedParameterJdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<UserProfile> findByAttributeContaining(String key, String value) {
        String sql = "SELECT * FROM user_profile WHERE attributes @> CAST(:filter AS jsonb)";
        String filter = String.format("{\"%s\": \"%s\"}", key, value);

        return jdbcTemplate.query(
            sql,
            Map.of("filter", filter),
            (rs, rowNum) -> mapUserProfile(rs)
        );
    }

    private UserProfile mapUserProfile(ResultSet rs) throws SQLException {
        UserProfile profile = new UserProfile();
        profile.setId(rs.getLong("id"));
        profile.setUsername(rs.getString("username"));

        // 使用Jackson解析JSONB
        PGobject pgObject = (PGobject) rs.getObject("attributes");
        if (pgObject != null && pgObject.getValue() != null) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                profile.setAttributes(mapper.readValue(pgObject.getValue(), new TypeReference<Map<String, Object>>() {}));
            } catch (JsonProcessingException e) {
                throw new SQLException("Error parsing JSONB", e);
            }
        }

        return profile;
    }

    public void updateAttribute(Long id, String key, String value) {
        String sql = "UPDATE user_profile SET attributes = jsonb_set(attributes, CAST(:path AS text[]), CAST(:value AS jsonb)) WHERE id = :id";

        jdbcTemplate.update(
            sql,
            Map.of(
                "id", id,
                "path", String.format("{\"%s\"}", key),
                "value", String.format("\"%s\"", value)
            )
        );
    }
}
```

**数组类型操作**：
```java
@Repository
public class ProductJdbcRepository {
    private final NamedParameterJdbcTemplate jdbcTemplate;

    public ProductJdbcRepository(NamedParameterJdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<Product> findByTagContaining(String tag) {
        String sql = "SELECT * FROM product WHERE tags @> ARRAY[:tag]::text[]";

        return jdbcTemplate.query(
            sql,
            Map.of("tag", tag),
            (rs, rowNum) -> mapProduct(rs)
        );
    }

    private Product mapProduct(ResultSet rs) throws SQLException {
        Product product = new Product();
        product.setId(rs.getLong("id"));
        product.setName(rs.getString("name"));

        // 解析数组
        Array tagsArray = rs.getArray("tags");
        if (tagsArray != null) {
            product.setTags((String[]) tagsArray.getArray());
        }

        Array ratingsArray = rs.getArray("ratings");
        if (ratingsArray != null) {
            product.setRatings((Integer[]) ratingsArray.getArray());
        }

        return product;
    }

    public void addTag(Long id, String tag) {
        String sql = "UPDATE product SET tags = array_append(tags, :tag) WHERE id = :id";

        jdbcTemplate.update(
            sql,
            Map.of(
                "id", id,
                "tag", tag
            )
        );
    }
}
```

**地理信息类型操作**：
```java
@Repository
public class LocationJdbcRepository {
    private final NamedParameterJdbcTemplate jdbcTemplate;

    public LocationJdbcRepository(NamedParameterJdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<Location> findNearby(double longitude, double latitude, double radiusMeters) {
        String sql = "SELECT * FROM location WHERE ST_DWithin(point, ST_SetSRID(ST_MakePoint(:lon, :lat), 4326), :distance) " +
                     "ORDER BY ST_Distance(point, ST_SetSRID(ST_MakePoint(:lon, :lat), 4326))";

        return jdbcTemplate.query(
            sql,
            Map.of(
                "lon", longitude,
                "lat", latitude,
                "distance", radiusMeters / 111320 // 转换为度
            ),
            (rs, rowNum) -> mapLocation(rs)
        );
    }

    private Location mapLocation(ResultSet rs) throws SQLException {
        Location location = new Location();
        location.setId(rs.getLong("id"));
        location.setName(rs.getString("name"));

        // 解析几何类型需要使用PostGIS的PGgeometry
        PGgeometry pgGeometry = (PGgeometry) rs.getObject("point");
        if (pgGeometry != null) {
            // 需要使用PostGIS的Java API处理
            // 这里简化处理，实际应用中需要更复杂的转换
            location.setPoint(pgGeometry.getGeometry());
        }

        return location;
    }
}
```

### 6.4 自定义类型处理器

有时需要创建自定义类型处理器来处理特殊数据类型与Java对象之间的转换。

**Hibernate自定义类型**：
```java
// 自定义JSONB类型处理器
public class JsonbType extends AbstractSingleColumnStandardBasicType<Map<String, Object>> {
    public JsonbType() {
        super(JsonbSqlTypeDescriptor.INSTANCE, JsonbJavaTypeDescriptor.INSTANCE);
    }

    @Override
    public String getName() {
        return "jsonb";
    }

    // SqlTypeDescriptor实现
    public static class JsonbSqlTypeDescriptor extends PGObjectSqlTypeDescriptor {
        public static final JsonbSqlTypeDescriptor INSTANCE = new JsonbSqlTypeDescriptor();

        @Override
        public <X> ValueBinder<X> getBinder(JavaTypeDescriptor<X> javaTypeDescriptor) {
            return new BasicBinder<X>(javaTypeDescriptor, this) {
                @Override
                protected void doBind(PreparedStatement st, X value, int index, WrapperOptions options) throws SQLException {
                    if (value == null) {
                        st.setNull(index, Types.OTHER);
                    } else {
                        ObjectMapper mapper = new ObjectMapper();
                        PGobject pgObject = new PGobject();
                        pgObject.setType("jsonb");
                        try {
                            pgObject.setValue(mapper.writeValueAsString(value));
                            st.setObject(index, pgObject);
                        } catch (JsonProcessingException e) {
                            throw new SQLException("Error converting Map to JSONB", e);
                        }
                    }
                }
            };
        }
    }

    // JavaTypeDescriptor实现
    public static class JsonbJavaTypeDescriptor extends AbstractTypeDescriptor<Map<String, Object>> {
        public static final JsonbJavaTypeDescriptor INSTANCE = new JsonbJavaTypeDescriptor();

        @SuppressWarnings("unchecked")
        public JsonbJavaTypeDescriptor() {
            super((Class<Map<String, Object>>) (Class<?>) Map.class);
        }

        @Override
        public Map<String, Object> fromString(String string) {
            try {
                return new ObjectMapper().readValue(string, new TypeReference<Map<String, Object>>() {});
            } catch (JsonProcessingException e) {
                throw new HibernateException("Error converting String to Map", e);
            }
        }

        @Override
        public <X> X unwrap(Map<String, Object> value, Class<X> type, WrapperOptions options) {
            if (value == null) {
                return null;
            }
            if (String.class.isAssignableFrom(type)) {
                try {
                    return (X) new ObjectMapper().writeValueAsString(value);
                } catch (JsonProcessingException e) {
                    throw new HibernateException("Error converting Map to String", e);
                }
            }
            if (PGobject.class.isAssignableFrom(type)) {
                try {
                    PGobject pgObject = new PGobject();
                    pgObject.setType("jsonb");
                    pgObject.setValue(new ObjectMapper().writeValueAsString(value));
                    return (X) pgObject;
                } catch (Exception e) {
                    throw new HibernateException("Error converting Map to PGobject", e);
                }
            }
            throw unknownUnwrap(type);
        }

        @Override
        public <X> Map<String, Object> wrap(X value, WrapperOptions options) {
            if (value == null) {
                return null;
            }
            if (value instanceof String) {
                try {
                    return new ObjectMapper().readValue((String) value, new TypeReference<Map<String, Object>>() {});
                } catch (JsonProcessingException e) {
                    throw new HibernateException("Error converting String to Map", e);
                }
            }
            if (value instanceof PGobject) {
                try {
                    return new ObjectMapper().readValue(((PGobject) value).getValue(), new TypeReference<Map<String, Object>>() {});
                } catch (JsonProcessingException e) {
                    throw new HibernateException("Error converting PGobject to Map", e);
                }
            }
            throw unknownWrap(value.getClass());
        }
    }
}
```

**Spring JDBC自定义类型处理**：
```java
// 自定义RowMapper处理JSONB
public class JsonbRowMapper<T> implements RowMapper<T> {
    private final Class<T> targetClass;
    private final ObjectMapper objectMapper;

    public JsonbRowMapper(Class<T> targetClass) {
        this.targetClass = targetClass;
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public T mapRow(ResultSet rs, int rowNum) throws SQLException {
        try {
            PGobject pgObject = (PGobject) rs.getObject("data");
            if (pgObject == null || pgObject.getValue() == null) {
                return null;
            }
            return objectMapper.readValue(pgObject.getValue(), targetClass);
        } catch (JsonProcessingException e) {
            throw new SQLException("Error parsing JSONB", e);
        }
    }
}

// 使用自定义RowMapper
@Repository
public class DocumentRepository {
    private final NamedParameterJdbcTemplate jdbcTemplate;

    public DocumentRepository(NamedParameterJdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<Document> findByContentContaining(String keyword) {
        String sql = "SELECT * FROM document WHERE data @> :filter::jsonb";
        String filter = String.format("{\"content\": \"%s\"}", keyword);

        return jdbcTemplate.query(
            sql,
            Map.of("filter", filter),
            new JsonbRowMapper<>(Document.class)
        );
    }
}
```

## 7. 性能优化和最佳实践

### 7.1 索引策略

不同的特殊数据类型需要不同的索引策略来优化查询性能。

**JSONB索引**：
```sql
-- 索引整个JSONB文档（支持包含查询@>）
CREATE INDEX idx_jsonb_doc ON user_profile USING GIN (data);

-- 索引特定路径（更小更快，但功能有限）
CREATE INDEX idx_jsonb_path ON user_profile USING GIN (data jsonb_path_ops);

-- 索引特定键（适用于频繁查询的键）
CREATE INDEX idx_jsonb_key ON user_profile USING GIN ((data->'preferences'));

-- 索引特定键的值（适用于等值查询）
CREATE INDEX idx_jsonb_key_value ON user_profile ((data->>'email'));

-- 函数索引（适用于复杂查询）
CREATE INDEX idx_jsonb_array_length ON user_profile ((jsonb_array_length(data->'tags')));
```

**数组索引**：
```sql
-- GIN索引（支持包含、重叠、相等查询）
CREATE INDEX idx_array ON product USING GIN (tags);

-- 特定元素索引
CREATE INDEX idx_array_first ON product ((tags[1]));

-- 数组长度索引
CREATE INDEX idx_array_length ON product (array_length(tags, 1));
```

**地理信息索引**：
```sql
-- GIST索引（支持空间查询）
CREATE INDEX idx_geo ON location USING GIST (point);

-- 特定空间函数索引
CREATE INDEX idx_geo_distance ON location USING GIST (ST_Buffer(point, 0.01));
```

**全文搜索索引**：
```sql
-- GIN索引（适用于静态数据）
CREATE INDEX idx_fts ON article USING GIN (ts_vector);

-- GiST索引（适用于频繁更新的数据）
CREATE INDEX idx_fts_gist ON article USING GIST (ts_vector);
```

**范围类型索引**：
```sql
-- GIST索引（支持范围操作）
CREATE INDEX idx_range ON reservation USING GIST (period);

-- 特定范围边界索引
CREATE INDEX idx_range_lower ON reservation (lower(period));
CREATE INDEX idx_range_upper ON reservation (upper(period));
```

**索引选择指南**：
1. **GIN索引**：适用于JSONB、数组、全文搜索，查询性能好但更新较慢
2. **GiST索引**：适用于地理信息、范围类型、全文搜索，更新性能好但查询较慢
3. **B-tree索引**：适用于标量值（如JSONB中提取的特定值）
4. **函数索引**：适用于需要在查询中使用函数的场景

### 7.2 查询优化

优化特殊数据类型的查询可以显著提高性能。

**JSONB查询优化**：
```sql
-- 优化前：使用->操作符然后比较
SELECT * FROM user_profile WHERE data->'age' = '30';

-- 优化后：使用@>操作符（可以利用GIN索引）
SELECT * FROM user_profile WHERE data @> '{"age": 30}';

-- 优化前：使用多个条件
SELECT * FROM user_profile
WHERE data->'preferences'->>'theme' = 'dark'
AND data->'preferences'->>'notifications' = 'true';

-- 优化后：使用单个@>操作符
SELECT * FROM user_profile
WHERE data->'preferences' @> '{"theme": "dark", "notifications": "true"}';

-- 优化前：使用->>提取然后排序
SELECT * FROM user_profile ORDER BY data->>'username';

-- 优化后：创建并使用函数索引
CREATE INDEX idx_username ON user_profile ((data->>'username'));
SELECT * FROM user_profile ORDER BY data->>'username';
```

**数组查询优化**：
```sql
-- 优化前：使用ANY操作符
SELECT * FROM product WHERE 'tag1' = ANY(tags);

-- 优化后：使用@>操作符（可以利用GIN索引）
SELECT * FROM product WHERE tags @> ARRAY['tag1'];

-- 优化前：使用unnest展开然后聚合
SELECT product_id, COUNT(*)
FROM (SELECT product_id, unnest(tags) AS tag FROM product) t
WHERE tag LIKE 'prefix%'
GROUP BY product_id;

-- 优化后：使用数组函数
SELECT product_id, array_length(tags, 1) AS tag_count
FROM product
WHERE tags @> ARRAY(SELECT 'prefix' || i::text FROM generate_series(1, 5) i);
```

**地理信息查询优化**：
```sql
-- 优化前：计算所有点的距离然后过滤
SELECT * FROM location
WHERE ST_Distance(point, ST_SetSRID(ST_MakePoint(-73.9857, 40.7484), 4326)) < 0.01;

-- 优化后：使用ST_DWithin（可以利用空间索引）
SELECT * FROM location
WHERE ST_DWithin(point, ST_SetSRID(ST_MakePoint(-73.9857, 40.7484), 4326), 0.01);

-- 优化前：使用复杂的几何体
SELECT * FROM location
WHERE ST_Intersects(area, ST_Buffer(ST_SetSRID(ST_MakePoint(-73.9857, 40.7484), 4326), 0.01));

-- 优化后：简化几何体
SELECT * FROM location
WHERE ST_Intersects(ST_Simplify(area, 0.0001), ST_Buffer(ST_SetSRID(ST_MakePoint(-73.9857, 40.7484), 4326), 0.01));
```

**全文搜索查询优化**：
```sql
-- 优化前：每次查询时计算tsvector
SELECT * FROM article
WHERE to_tsvector('english', content) @@ to_tsquery('english', 'postgresql');

-- 优化后：使用预计算的tsvector列
SELECT * FROM article
WHERE ts_vector @@ to_tsquery('english', 'postgresql');

-- 优化前：使用复杂的排名计算
SELECT *, ts_rank(to_tsvector('english', content), query) AS rank
FROM article, to_tsquery('english', 'postgresql') query
WHERE to_tsvector('english', content) @@ query
ORDER BY rank DESC;

-- 优化后：使用预计算的tsvector列和简化的排名
SELECT *, ts_rank(ts_vector, query) AS rank
FROM article, to_tsquery('english', 'postgresql') query
WHERE ts_vector @@ query
ORDER BY rank DESC;
```

**范围类型查询优化**：
```sql
-- 优化前：使用日期比较
SELECT * FROM reservation
WHERE '2025-07-01 15:00:00+08'::timestamptz >= lower(period)
AND '2025-07-01 15:00:00+08'::timestamptz < upper(period);

-- 优化后：使用@>操作符（可以利用GiST索引）
SELECT * FROM reservation
WHERE period @> '2025-07-01 15:00:00+08'::timestamptz;

-- 优化前：使用日期范围比较
SELECT * FROM reservation
WHERE lower(period) >= '2025-07-01'::date
AND upper(period) <= '2025-07-31'::date;

-- 优化后：使用&&操作符（可以利用GiST索引）
SELECT * FROM reservation
WHERE period && tstzrange('2025-07-01', '2025-07-31', '[]');
```

### 7.3 存储考虑

特殊数据类型的存储策略对性能和维护有重要影响。

**JSONB存储优化**：
1. **避免过大的JSONB文档**：大文档会影响读写性能，考虑拆分
2. **规范化关键数据**：频繁查询或更新的数据应该放在单独的列中
3. **压缩重复数据**：使用JSONB的结构避免重复存储相同的键
4. **使用适当的JSONB函数**：如jsonb_strip_nulls()移除空值
5. **考虑TOAST策略**：大型JSONB可能会被TOAST存储，影响性能

**数组存储优化**：
1. **限制数组大小**：过大的数组会影响性能
2. **使用适当的数组类型**：如int[]比text[]更高效
3. **避免频繁修改大数组**：考虑使用关联表代替
4. **预先排序数组**：如果需要按顺序访问元素
5. **使用数组聚合函数**：如array_agg()高效地构建数组

**地理信息存储优化**：
1. **选择合适的SRID**：使用适合应用场景的坐标系统
2. **简化复杂几何体**：使用ST_Simplify()减少点数
3. **使用适当的精度**：避免不必要的高精度
4. **分区大型空间数据**：按地理区域分区
5. **考虑使用geography vs geometry**：根据应用场景选择

**全文搜索存储优化**：
1. **预计算tsvector**：使用生成列或触发器
2. **分离全文搜索数据**：考虑使用单独的表存储tsvector
3. **定期维护全文搜索索引**：使用REINDEX
4. **使用适当的文本搜索配置**：根据语言和需求选择
5. **考虑外部全文搜索引擎**：对于大规模应用考虑Elasticsearch

### 7.4 何时使用/不使用特殊数据类型

选择合适的数据类型对应用性能和可维护性至关重要。

**JSONB使用场景**：
- ✅ 半结构化数据存储
- ✅ 需要灵活Schema的应用
- ✅ 存储配置和偏好设置
- ✅ 嵌套数据结构
- ❌ 频繁更新的单个属性（使用单独的列）
- ❌ 需要强引用完整性的关系（使用关联表）
- ❌ 需要复杂查询和聚合的数据（使用规范化表）

**数组使用场景**：
- ✅ 存储多值属性（如标签、类别）
- ✅ 固定长度的列表
- ✅ 简单的一对多关系
- ❌ 大型集合（使用关联表）
- ❌ 需要单独索引或查询的元素（使用关联表）
- ❌ 需要引用完整性的关系（使用关联表）

**地理信息使用场景**：
- ✅ 位置数据存储和查询
- ✅ 空间分析和计算
- ✅ 基于位置的服务
- ❌ 简单的坐标存储（可以使用普通数值列）
- ❌ 非空间查询（使用普通索引）

**全文搜索使用场景**：
- ✅ 文档搜索和检索
- ✅ 需要词干提取和停用词处理
- ✅ 需要相关性排名
- ❌ 简单的LIKE查询（使用LIKE或正则表达式）
- ❌ 极大规模的搜索（考虑专用搜索引擎）

**范围类型使用场景**：
- ✅ 日期范围（如预订、活动）
- ✅ 数值范围（如价格区间）
- ✅ 需要范围操作（包含、重叠）
- ❌ 简单的边界比较（可以使用单独的起始和结束列）

**枚举类型使用场景**：
- ✅ 固定的、不经常变化的值集合
- ✅ 需要类型安全的状态或类别
- ❌ 频繁变化的值集合（使用lookup表）
- ❌ 需要国际化的值（使用lookup表）

### 7.5 常见陷阱和解决方案

使用特殊数据类型时常见的问题和解决方法。

**JSONB陷阱**：
1. **性能问题**：大型JSONB文档查询慢
   - 解决：创建适当的索引，提取频繁查询的字段到单独的列
2. **更新开销**：JSONB是不可变的，每次更新都会创建新副本
   - 解决：使用jsonb_set()进行部分更新，避免频繁更新大文档
3. **类型安全**：JSONB不提供Schema验证
   - 解决：在应用层实现验证，或考虑使用CHECK约束
4. **查询复杂性**：复杂的JSONB查询难以编写和维护
   - 解决：使用视图简化查询，或考虑部分规范化

**数组陷阱**：
1. **性能下降**：大型数组操作慢
   - 解决：限制数组大小，考虑使用关联表
2. **索引限制**：数组元素的索引有限
   - 解决：使用GIN索引，或考虑使用关联表
3. **更新开销**：整个数组需要重写
   - 解决：使用数组函数进行部分更新，避免频繁更新大数组
4. **排序问题**：数组元素的顺序可能不符合预期
   - 解决：使用array_agg()时指定ORDER BY

**地理信息陷阱**：
1. **性能问题**：复杂几何体操作慢
   - 解决：简化几何体，使用适当的索引
2. **精度问题**：不同坐标系统的精度不同
   - 解决：选择合适的SRID，注意单位转换
3. **索引选择**：不正确的索引类型
   - 解决：使用GIST索引，优化查询使用ST_DWithin()
4. **距离计算**：平面距离vs球面距离
   - 解决：geometry用于平面，geography用于球面

**全文搜索陷阱**：
1. **性能问题**：实时计算tsvector慢
   - 解决：预计算并存储tsvector，使用触发器更新
2. **索引大小**：全文搜索索引可能很大
   - 解决：只索引必要的列，考虑使用部分索引
3. **相关性排名**：默认排名可能不符合预期
   - 解决：调整ts_rank()参数，或实现自定义排名
4. **语言支持**：默认配置可能不支持特定语言
   - 解决：创建自定义文本搜索配置

**范围类型陷阱**：
1. **边界问题**：闭区间vs开区间
   - 解决：明确指定区间类型，如'[)'表示左闭右开
2. **空范围**：处理空范围可能导致意外结果
   - 解决：使用IS NULL检查，或使用COALESCE提供默认值
3. **索引选择**：不正确的索引类型
   - 解决：使用GIST索引，优化查询使用@>或&&操作符
4. **日期时区**：时区转换可能导致意外结果
   - 解决：使用带时区的时间戳类型，明确指定时区

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|------|---------|-------|
| 1.0 | 2025-06-20 | 初始版本 | AI助手 |
