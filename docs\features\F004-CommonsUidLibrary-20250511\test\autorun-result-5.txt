/home/<USER>/apps/jdk-21.0.5/bin/java -javaagent:/home/<USER>/apps/idea-IU-243.23654.189/lib/idea_rt.jar=43727:/home/<USER>/apps/idea-IU-243.23654.189/bin -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -classpath /media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/test-classes:/media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/classes:/home/<USER>/works/project/mvnRepository/com/xfvape/uid/uid-generator/0.0.4-RELEASE/uid-generator-0.0.4-RELEASE.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-core/6.2.6/spring-core-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-jcl/6.2.6/spring-jcl-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-beans/6.2.6/spring-beans-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/mybatis/mybatis/3.2.3/mybatis-3.2.3.jar:/home/<USER>/works/project/mvnRepository/org/mybatis/mybatis-spring/1.2.4/mybatis-spring-1.2.4.jar:/home/<USER>/works/project/mvnRepository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/home/<USER>/works/project/mvnRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/log4j-over-slf4j/2.0.17/log4j-over-slf4j-2.0.17.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-context/6.2.6/spring-context-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-aop/6.2.6/spring-aop-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-expression/6.2.6/spring-expression-6.2.6.jar:/home/<USER>/works/project/mvnRepository/io/micrometer/micrometer-observation/1.14.6/micrometer-observation-1.14.6.jar:/home/<USER>/works/project/mvnRepository/io/micrometer/micrometer-commons/1.14.6/micrometer-commons-1.14.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-jdbc/6.2.6/spring-jdbc-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-tx/6.2.6/spring-tx-6.2.6.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-annotations/2.18.3/jackson-annotations-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/core/jackson-core/2.18.3/jackson-core-2.18.3.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter-test/3.4.5/spring-boot-starter-test-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter/3.4.5/spring-boot-starter-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot/3.4.5/spring-boot-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-autoconfigure/3.4.5/spring-boot-autoconfigure-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-starter-logging/3.4.5/spring-boot-starter-logging-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/home/<USER>/works/project/mvnRepository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/home/<USER>/works/project/mvnRepository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/home/<USER>/works/project/mvnRepository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/home/<USER>/works/project/mvnRepository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-test/3.4.5/spring-boot-test-3.4.5.jar:/home/<USER>/works/project/mvnRepository/org/springframework/boot/spring-boot-test-autoconfigure/3.4.5/spring-boot-test-autoconfigure-3.4.5.jar:/home/<USER>/works/project/mvnRepository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/home/<USER>/works/project/mvnRepository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/home/<USER>/works/project/mvnRepository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/home/<USER>/works/project/mvnRepository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/home/<USER>/works/project/mvnRepository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/home/<USER>/works/project/mvnRepository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/home/<USER>/works/project/mvnRepository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/home/<USER>/works/project/mvnRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/works/project/mvnRepository/org/springframework/spring-test/6.2.6/spring-test-6.2.6.jar:/home/<USER>/works/project/mvnRepository/org/xmlunit/xmlunit-core/2.10.0/xmlunit-core-2.10.0.jar:/home/<USER>/works/project/mvnRepository/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar:/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar:/home/<USER>/works/project/mvnRepository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/home/<USER>/works/project/mvnRepository/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/works/project/mvnRepository/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/junit/platform/junit-platform-launcher/1.11.4/junit-platform-launcher-1.11.4.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/testcontainers/1.19.7/testcontainers-1.19.7.jar:/home/<USER>/works/project/mvnRepository/junit/junit/4.13.2/junit-4.13.2.jar:/home/<USER>/works/project/mvnRepository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/home/<USER>/works/project/mvnRepository/org/apache/commons/commons-compress/1.24.0/commons-compress-1.24.0.jar:/home/<USER>/works/project/mvnRepository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/home/<USER>/works/project/mvnRepository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-api/3.3.6/docker-java-api-3.3.6.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-transport-zerodep/3.3.6/docker-java-transport-zerodep-3.3.6.jar:/home/<USER>/works/project/mvnRepository/com/github/docker-java/docker-java-transport/3.3.6/docker-java-transport-3.3.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/postgresql/1.19.7/postgresql-1.19.7.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/jdbc/1.20.6/jdbc-1.20.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/database-commons/1.20.6/database-commons-1.20.6.jar:/home/<USER>/works/project/mvnRepository/org/testcontainers/junit-jupiter/1.19.7/junit-jupiter-1.19.7.jar:/home/<USER>/works/project/mvnRepository/org/openjdk/jmh/jmh-core/1.37/jmh-core-1.37.jar:/home/<USER>/works/project/mvnRepository/net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar:/home/<USER>/works/project/mvnRepository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/home/<USER>/works/project/mvnRepository/org/openjdk/jmh/jmh-generator-annprocess/1.37/jmh-generator-annprocess-1.37.jar:/home/<USER>/works/project/mvnRepository/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/home/<USER>/works/project/mvnRepository/org/postgresql/postgresql/42.7.5/postgresql-42.7.5.jar:/home/<USER>/works/project/mvnRepository/org/checkerframework/checker-qual/3.48.3/checker-qual-3.48.3.jar:/home/<USER>/works/project/mvnRepository/com/github/oshi/oshi-core/6.5.0/oshi-core-6.5.0.jar:/home/<USER>/works/project/mvnRepository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/home/<USER>/works/project/mvnRepository/net/java/dev/jna/jna-platform/5.14.0/jna-platform-5.14.0.jar:/home/<USER>/works/project/mvnRepository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar:/home/<USER>/works/project/mvnRepository/ch/qos/logback/logback-classic/1.5.16/logback-classic-1.5.16.jar:/home/<USER>/works/project/mvnRepository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/home/<USER>/works/project/mvnRepository/org/xkongkit/xkongkit-core/1.0.0-SNAPSHOT/xkongkit-core-1.0.0-SNAPSHOT.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/kryo/5.5.0/kryo-5.5.0.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/home/<USER>/works/project/mvnRepository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/home/<USER>/works/project/mvnRepository/org/lmdbjava/lmdbjava/0.9.1/lmdbjava-0.9.1.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-constants/0.10.4/jnr-constants-0.10.4.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-ffi/2.2.17/jnr-ffi-2.2.17.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jffi/1.3.13/jffi-1.3.13.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jffi/1.3.13/jffi-1.3.13-native.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-commons/9.7.1/asm-commons-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar:/home/<USER>/works/project/mvnRepository/org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-a64asm/1.0.0/jnr-a64asm-1.0.0.jar:/home/<USER>/works/project/mvnRepository/com/github/jnr/jnr-x86asm/1.0.2/jnr-x86asm-1.0.2.jar:/home/<USER>/works/project/mvnRepository/com/tokyocabinet/tokyocabinet/1.24/tokyocabinet-1.24.jar:/home/<USER>/works/project/mvnRepository/org/jackson/databind/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.3/jackson-datatype-jsr310-2.18.3.jar:/home/<USER>/works/project/mvnRepository/com/github/luben/zstd-jni/1.5.5-3/zstd-jni-1.5.5-3.jar:/home/<USER>/works/project/mvnRepository/com/github/ben-manes/caffeine/caffeine/3.1.8/caffeine-3.1.8.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/home/<USER>/works/project/mvnRepository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/home/<USER>/works/project/mvnRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/home/<USER>/works/project/mvnRepository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-context/1.70.0/grpc-context-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-impl/0.31.1/opencensus-impl-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-impl-core/0.31.1/opencensus-impl-core-0.31.1.jar:/home/<USER>/works/project/mvnRepository/com/lmax/disruptor/3.4.2/disruptor-3.4.2.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-census/1.71.0/grpc-census-1.71.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-api/1.70.0/grpc-api-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/opencensus/opencensus-contrib-grpc-metrics/0.31.1/opencensus-contrib-grpc-metrics-0.31.1.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-services/1.71.0/grpc-services-1.71.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-stub/1.70.0/grpc-stub-1.70.0.jar:/home/<USER>/works/project/mvnRepository/org/codehaus/mojo/animal-sniffer-annotations/1.24/animal-sniffer-annotations-1.24.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-core/1.70.0/grpc-core-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar:/home/<USER>/works/project/mvnRepository/io/perfmark/perfmark-api/0.27.0/perfmark-api-0.27.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-protobuf/1.70.0/grpc-protobuf-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/protobuf/protobuf-java/3.25.6/protobuf-java-3.25.6.jar:/home/<USER>/works/project/mvnRepository/com/google/api/grpc/proto-google-common-protos/2.46.0/proto-google-common-protos-2.46.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-protobuf-lite/1.70.0/grpc-protobuf-lite-1.70.0.jar:/home/<USER>/works/project/mvnRepository/io/grpc/grpc-util/1.70.0/grpc-util-1.70.0.jar:/home/<USER>/works/project/mvnRepository/com/google/protobuf/protobuf-java-util/3.25.6/protobuf-java-util-3.25.6.jar:/home/<USER>/works/project/mvnRepository/com/google/errorprone/error_prone_annotations/2.30.0/error_prone_annotations-2.30.0.jar:/home/<USER>/works/project/mvnRepository/com/google/code/gson/gson/2.11.0/gson-2.11.0.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-x86_64.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-common/4.1.119.Final/netty-common-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-buffer/4.1.119.Final/netty-buffer-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport/4.1.119.Final/netty-transport-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-resolver/4.1.119.Final/netty-resolver-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-native-unix-common/4.1.119.Final/netty-transport-native-unix-common-4.1.119.Final.jar:/home/<USER>/works/project/mvnRepository/io/netty/netty-transport-classes-epoll/4.1.119.Final/netty-transport-classes-epoll-4.1.119.Final.jar org.xkong.cloud.commons.uid.TestRunner
===== 运行单元测试 =====
19:20:16,399 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-classic version 1.5.16
19:20:16,399 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-core version 1.5.18
19:20:16,399 |-WARN in ch.qos.logback.classic.LoggerContext[default] - Versions of logback-core and logback-classic are different!
19:20:16,401 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7f485fda - Here is a list of configurators discovered as a service, by rank: 
19:20:16,402 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7f485fda -   org.springframework.boot.logging.logback.RootLogLevelConfigurator
19:20:16,402 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7f485fda - They will be invoked in order until ExecutionStatus.DO_NOT_INVOKE_NEXT_IF_ANY is returned.
19:20:16,402 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7f485fda - Constructed configurator of type class org.springframework.boot.logging.logback.RootLogLevelConfigurator
19:20:16,407 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7f485fda - org.springframework.boot.logging.logback.RootLogLevelConfigurator.configure() call lasted 0 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
19:20:16,407 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7f485fda - Trying to configure with ch.qos.logback.classic.joran.SerializedModelConfigurator
19:20:16,413 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7f485fda - Constructed configurator of type class ch.qos.logback.classic.joran.SerializedModelConfigurator
19:20:16,417 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.scmo]
19:20:16,418 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback.scmo]
19:20:16,418 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7f485fda - ch.qos.logback.classic.joran.SerializedModelConfigurator.configure() call lasted 5 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
19:20:16,418 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7f485fda - Trying to configure with ch.qos.logback.classic.util.DefaultJoranConfigurator
19:20:16,423 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7f485fda - Constructed configurator of type class ch.qos.logback.classic.util.DefaultJoranConfigurator
19:20:16,425 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-test.xml] at [file:/media/sf_ExchangeWorks/xkong/xkongcloud/xkongcloud-commons/xkongcloud-commons-uid/target/test-classes/logback-test.xml]
19:20:16,776 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [CONSOLE]
19:20:16,776 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
19:20:16,785 |-INFO in ch.qos.logback.core.model.processor.ImplicitModelHandler - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
19:20:16,850 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - BEWARE: Writing to the console can be very slow. Avoid logging to the 
19:20:16,850 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - console in production environments, especially in high volume systems.
19:20:16,850 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE] - See also https://logback.qos.ch/codes.html#slowConsole
19:20:16,850 |-INFO in ch.qos.logback.classic.model.processor.RootLoggerModelHandler - Setting level of ROOT logger to INFO
19:20:16,851 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[ROOT]
19:20:16,852 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [org.xkong.cloud.commons.uid] to DEBUG
19:20:16,852 |-INFO in ch.qos.logback.core.model.processor.DefaultProcessor@28261e8e - End of configuration.
19:20:16,853 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@d737b89 - Registering current configuration as safe fallback point
19:20:16,853 |-INFO in ch.qos.logback.classic.util.ContextInitializer@7f485fda - ch.qos.logback.classic.util.DefaultJoranConfigurator.configure() call lasted 430 milliseconds. ExecutionStatus=DO_NOT_INVOKE_NEXT_IF_ANY

2025-05-22 19:20:17.402 [main] INFO  o.x.c.c.u.f.MachineFingerprints - 使用OSHI库成功获取系统信息
2025-05-22 19:20:19.713 [main] INFO  o.x.c.c.u.f.MachineFingerprints - 已收集机器特征码: {"os_arch":"amd64","os_name":"Linux","fingerprint_hash":"2c974415142f2dfb2c262abd914df33384b81aa504f53b02d0658bc18c767b85","hostname":"long-VirtualBox","mac_addresses":["7E:2****:E8:3D","08:0****:3A:87"],"os_version":"5.4.0-91-generic"}
Mockito is currently self-attaching to enable the inline-mock-maker. This will no longer work in future releases of the JDK. Please add Mockito as an agent to your build what is described in Mockito's documentation: https://javadoc.io/doc/org.mockito/mockito-core/latest/org/mockito/Mockito.html#0.3
WARNING: A Java agent has been loaded dynamically (/home/<USER>/works/project/mvnRepository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
Java HotSpot(TM) 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classpath has been appended
2025-05-22 19:20:21.299 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 19:20:21.339 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 19:20:21.351 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 19:20:21.574 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 19:20:21.575 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 19:20:21.582 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 19:20:21.589 [main] DEBUG o.x.c.c.u.c.KeyManagementService - 使用NativePRNGNonBlocking算法的SecureRandom
2025-05-22 19:20:21.591 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已为应用 test-app 环境 test-env 创建新的 test-key-type 类型密钥
2025-05-22 19:20:21.597 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 19:20:21.609 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 19:20:21.613 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: test-app, 环境: test-env, Schema: test_schema
2025-05-22 19:20:21.620 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已清除密钥缓存
2025-05-22 19:20:21.643 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 19:20:21.644 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-22 19:20:21.649 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 19:20:21.649 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-22 19:20:21.661 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 19:20:21.662 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-22 19:20:21.662 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-22 19:20:21.662 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-22 19:20:21.872 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 19:20:21.873 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-22 19:20:21.878 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 验证结果缓存已创建，过期时间: 100毫秒，清理间隔: 1000毫秒
2025-05-22 19:20:21.879 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: test_schema
2025-05-22 19:20:21.879 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: test_table
2025-05-22 19:20:21.879 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记参数验证通过: test_param
2025-05-22 19:20:21.879 [main] INFO  o.x.c.c.u.v.ValidationResultCache - 已清除所有验证缓存
2025-05-22 19:20:21.948 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 19:20:21.948 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 19:20:21.948 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 19:20:21.948 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 19:20:21.948 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:21.950 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 19:20:21.950 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 19:20:21.950 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 19:20:21.951 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: main
2025-05-22 19:20:21.951 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: main
2025-05-22 19:20:21.952 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: main
2025-05-22 19:20:21.952 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: main
2025-05-22 19:20:21.952 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: main
2025-05-22 19:20:21.952 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: main
2025-05-22 19:20:21.952 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: main
2025-05-22 19:20:21.956 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:21.989 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 19:20:21.989 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 19:20:21.989 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 19:20:21.990 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 19:20:21.990 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:21.990 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 19:20:21.999 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-22 19:20:21.999 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: main
2025-05-22 19:20:21.999 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 19:20:21.999 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 19:20:22.009 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 19:20:22.010 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 19:20:22.010 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 19:20:22.010 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 19:20:22.010 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:22.010 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 19:20:22.010 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 无法为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 19:20:22.011 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 19:20:22.011 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取工作机器ID时发生异常，当前线程: main, 异常: 无法为实例 12345 分配工作机器ID
java.lang.IllegalStateException: 无法为实例 12345 分配工作机器ID
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.getWorkerId(PersistentInstanceWorkerIdAssigner.java:161)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.lambda$testGetWorkerId_MaxReached$2(PersistentInstanceWorkerIdAssignerTest.java:248)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3128)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.testGetWorkerId_MaxReached(PersistentInstanceWorkerIdAssignerTest.java:248)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:767)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$8(TestMethodTestDescriptor.java:217)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:213)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:138)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.SessionPerRequestLauncher.execute(SessionPerRequestLauncher.java:63)
	at org.xkong.cloud.commons.uid.TestRunner.runTests(TestRunner.java:88)
	at org.xkong.cloud.commons.uid.TestRunner.runUnitTests(TestRunner.java:53)
	at org.xkong.cloud.commons.uid.TestRunner.main(TestRunner.java:27)
2025-05-22 19:20:22.022 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 19:20:22.023 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 19:20:22.023 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 19:20:22.023 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 19:20:22.023 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:22.023 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 19:20:22.025 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 插入新的工作机器ID失败，可能存在并发冲突，工作机器ID: 42
2025-05-22 19:20:22.026 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，实例ID: null
2025-05-22 19:20:22.026 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 无法为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 19:20:22.026 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 19:20:22.026 [main] ERROR o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取工作机器ID时发生异常，当前线程: main, 异常: 无法为实例 12345 分配工作机器ID
java.lang.IllegalStateException: 无法为实例 12345 分配工作机器ID
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.getWorkerId(PersistentInstanceWorkerIdAssigner.java:161)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssignerTest.testGetWorkerId_AllocateNew(PersistentInstanceWorkerIdAssignerTest.java:185)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:767)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$8(TestMethodTestDescriptor.java:217)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:213)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:138)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.SessionPerRequestLauncher.execute(SessionPerRequestLauncher.java:63)
	at org.xkong.cloud.commons.uid.TestRunner.runTests(TestRunner.java:88)
	at org.xkong.cloud.commons.uid.TestRunner.runUnitTests(TestRunner.java:53)
	at org.xkong.cloud.commons.uid.TestRunner.main(TestRunner.java:27)
2025-05-22 19:20:22.038 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: main
2025-05-22 19:20:22.038 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: false -> false，当前线程: main
2025-05-22 19:20:22.038 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: main
2025-05-22 19:20:22.038 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: main
2025-05-22 19:20:22.038 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 19:20:22.038 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 19:20:22.039 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 19:20:22.039 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 19:20:22.039 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:22.039 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 19:20:22.039 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 19:20:22.039 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 19:20:22.039 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: main
2025-05-22 19:20:22.061 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 19:20:22.062 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 19:20:22.062 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 19:20:22.062 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 19:20:22.062 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:22.063 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 19:20:22.063 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 19:20:22.063 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 19:20:22.064 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 19:20:22.067 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 19:20:22.074 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 19:20:22.074 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 19:20:22.074 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 19:20:22.074 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 19:20:22.074 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:22.074 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 19:20:22.074 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 19:20:22.074 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 19:20:22.083 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 19:20:22.083 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 19:20:22.083 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 19:20:22.083 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 19:20:22.083 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:22.083 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 19:20:22.083 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 19:20:22.083 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 19:20:22.086 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放成功
2025-05-22 19:20:22.107 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 19:20:22.107 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 19:20:22.107 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 19:20:22.108 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: main
2025-05-22 19:20:22.108 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:22.108 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: main
2025-05-22 19:20:22.108 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 42
2025-05-22 19:20:22.108 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 19:20:22.109 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: main
2025-05-22 19:20:22.109 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: main
2025-05-22 19:20:22.109 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: main
2025-05-22 19:20:22.110 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: main
2025-05-22 19:20:22.110 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: main
2025-05-22 19:20:22.110 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: main
2025-05-22 19:20:22.110 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: main
2025-05-22 19:20:22.121 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign

Test run finished after 5916 ms
[         5 containers found      ]
[         0 containers skipped    ]
[         5 containers started    ]
[         0 containers aborted    ]
[         5 containers successful ]
[         0 containers failed     ]
[        25 tests found           ]
[         0 tests skipped         ]
[        25 tests started         ]
[         0 tests aborted         ]
[        22 tests successful      ]
[         3 tests failed          ]


===== 运行集成测试 =====
2025-05-22 19:20:22.388 [main] INFO  o.testcontainers.images.PullPolicy - Image pull policy will be performed by: DefaultPullPolicy()
2025-05-22 19:20:22.400 [main] INFO  o.t.utility.ImageNameSubstitutor - Image name substitution will be performed by: DefaultImageNameSubstitutor (composite of 'ConfigurationFileImageNameSubstitutor' and 'PrefixingImageNameSubstitutor')
2025-05-22 19:20:22.658 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:22.669 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 19:20:22.669 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 19:20:22.669 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:22.669 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:22.669 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 19:20:22.669 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 19:20:22.682 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:22.682 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 19:20:22.682 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 19:20:22.682 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:22.682 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:22.682 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 19:20:22.682 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 19:20:22.685 [main] INFO  o.t.d.DockerClientProviderStrategy - Loaded org.testcontainers.dockerclient.UnixSocketClientProviderStrategy from ~/.testcontainers.properties, will try it first
2025-05-22 19:20:22.690 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:22.690 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:22.691 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 19:20:22.691 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 19:20:22.691 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 19:20:22.692 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:22.692 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:22.692 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 19:20:22.692 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 19:20:22.692 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 19:20:23.210 [main] INFO  o.t.d.DockerClientProviderStrategy - Found Docker environment with local Unix socket (unix:///var/run/docker.sock)
2025-05-22 19:20:23.213 [main] INFO  o.testcontainers.DockerClientFactory - Docker host IP address is localhost
2025-05-22 19:20:23.232 [main] INFO  o.testcontainers.DockerClientFactory - Connected to docker: 
  Server Version: 28.1.1
  API Version: 1.49
  Operating System: Linux Mint 20.3
  Total Memory: 7960 MB
2025-05-22 19:20:23.256 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Creating container for image: testcontainers/ryuk:0.6.0
2025-05-22 19:20:23.261 [main] INFO  o.t.utility.RegistryAuthLocator - Failure when attempting to lookup auth config. Please ignore if you don't have images in an authenticated registry. Details: (dockerImageName: testcontainers/ryuk:0.6.0, configFile: /home/<USER>/.docker/config.json, configEnv: DOCKER_AUTH_CONFIG). Falling back to docker-java default behaviour. Exception message: Status 404: No config supplied. Checked in order: /home/<USER>/.docker/config.json (file not found), DOCKER_AUTH_CONFIG (not set)
2025-05-22 19:20:24.543 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Container testcontainers/ryuk:0.6.0 is starting: 630675812de8f251609e953cba06ea9f8aad85592d8a631e738551c1324c41b8
2025-05-22 19:20:25.030 [main] INFO  tc.testcontainers/ryuk:0.6.0 - Container testcontainers/ryuk:0.6.0 started in PT1.774405816S
2025-05-22 19:20:25.041 [main] INFO  o.t.utility.RyukResourceReaper - Ryuk started - will monitor and terminate Testcontainers containers on JVM exit
2025-05-22 19:20:25.041 [main] INFO  o.testcontainers.DockerClientFactory - Checking the system...
2025-05-22 19:20:25.042 [main] INFO  o.testcontainers.DockerClientFactory - ✔︎ Docker server version should be at least 1.6.0
2025-05-22 19:20:25.043 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 19:20:26.070 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: f90b7ff2fba13bf852b9e22975d13032c4dfad609ed9e9c3558b8902affc7fd9
2025-05-22 19:20:31.014 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.971166451S
2025-05-22 19:20:31.015 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: jdbc:postgresql://localhost:32795/test?loggerLevel=OFF)
2025-05-22 19:20:31.075 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-22 19:20:31.282 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@329b331f
2025-05-22 19:20:31.284 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-22 19:20:31.317 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 19:20:31.318 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 19:20:31.318 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 19:20:31.319 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 19:20:31.319 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 19:20:31.322 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-22 19:20:31.340 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 19:20:31.340 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-22 19:20:31.341 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-22 19:20:31.356 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 19:20:31.356 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-22 19:20:31.357 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 19:20:31.368 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 19:20:31.368 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 19:20:31.369 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 19:20:32.932 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 19:20:33.387 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 05323e2f9699916da6c1499c901150e41ca74797138378c12e4e552fb886ca6a
2025-05-22 19:20:38.847 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.914937582S
2025-05-22 19:20:38.847 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 19:20:38.862 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-05-22 19:20:38.887 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@32e830a0
2025-05-22 19:20:38.887 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-05-22 19:20:38.887 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 19:20:38.888 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 19:20:38.888 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 19:20:38.890 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 不存在，正在自动创建
2025-05-22 19:20:38.892 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 已成功创建
2025-05-22 19:20:38.892 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 19:20:38.894 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-22 19:20:38.910 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 19:20:38.911 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-22 19:20:38.912 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-22 19:20:38.925 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 19:20:38.925 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-22 19:20:38.926 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 19:20:38.939 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 19:20:38.939 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 19:20:38.939 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 19:20:39.059 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Starting...
2025-05-22 19:20:39.118 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-3 - Added connection org.postgresql.jdbc.PgConnection@20c3b34b
2025-05-22 19:20:39.118 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Start completed.
2025-05-22 19:20:39.118 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 19:20:39.121 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 19:20:39.121 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 19:20:39.128 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 19:20:39.129 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 19:20:39.136 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 19:20:39.136 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:39.139 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 19:20:39.140 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:39.151 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 19:20:39.152 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 19:20:39.158 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 19:20:39.158 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:39.168 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 19:20:39.169 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:39.175 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 19:20:39.176 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 19:20:39.184 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 19:20:39.184 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:39.196 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 19:20:39.198 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:39.228 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 19:20:39.228 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 19:20:39.228 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 19:20:39.232 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Starting...
2025-05-22 19:20:39.505 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-4 - Added connection org.postgresql.jdbc.PgConnection@1aed6f0b
2025-05-22 19:20:39.505 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Start completed.
2025-05-22 19:20:39.506 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 19:20:39.528 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 19:20:39.528 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 19:20:39.546 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 19:20:39.546 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 19:20:39.551 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 19:20:39.551 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:39.553 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 19:20:39.554 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:39.557 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 19:20:39.557 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 19:20:39.562 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 19:20:39.563 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:39.573 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 19:20:39.573 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:39.588 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 19:20:39.588 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 19:20:39.589 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 19:20:39.589 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:39.590 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 19:20:39.590 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:39.591 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 19:20:39.591 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 19:20:39.591 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 19:20:41.148 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 19:20:41.726 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: e0168252b7cd3143f0c94cc09f7cc52930449c0dd2a1d3367e09f4f2daf3bed0
2025-05-22 19:20:41.943 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 19:20:41.950 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 19:20:41.951 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-22 19:20:41.951 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 19:20:41.951 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 19:20:41.951 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 19:20:41.951 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 19:20:41.952 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:41.989 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 19:20:41.990 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 19:20:41.991 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 19:20:41.991 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 19:20:41.991 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 19:20:41.991 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 19:20:41.991 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 19:20:41.991 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.006 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.019 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.035 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.037 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.037 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.037 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.037 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.037 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.037 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.037 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.073 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.074 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.075 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.075 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.075 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.075 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.075 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.075 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.091 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.092 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.093 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.093 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.093 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.093 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.093 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 19:20:42.093 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.454 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.454 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 19:20:42.454 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 19:20:42.454 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.454 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.454 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.454 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.456 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.456 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:42.456 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 19:20:42.456 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.456 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 19:20:42.493 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.493 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 19:20:42.493 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 19:20:42.493 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.493 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.493 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.493 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.494 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.494 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:42.494 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.495 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-22 19:20:42.495 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 19:20:42.495 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.495 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 19:20:42.538 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.538 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 19:20:42.538 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 19:20:42.538 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.538 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.538 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.538 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.540 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.540 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:42.540 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 19:20:42.540 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.540 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 19:20:42.580 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.580 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 19:20:42.580 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 19:20:42.580 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.581 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.581 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.581 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.582 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.582 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:42.582 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 19:20:42.582 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.582 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 19:20:42.607 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.608 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 19:20:42.608 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 19:20:42.608 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.608 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.608 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.608 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.609 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:20:42.609 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:20:42.609 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 19:20:42.609 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 19:20:42.609 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 19:20:47.085 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.936651547S
2025-05-22 19:20:47.085 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 19:20:47.087 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-5 - Starting...
2025-05-22 19:20:47.107 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-5 - Added connection org.postgresql.jdbc.PgConnection@8ac512e
2025-05-22 19:20:47.107 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-5 - Start completed.
2025-05-22 19:20:47.107 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 19:20:47.108 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 19:20:47.108 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 19:20:47.110 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 不存在，正在自动创建
2025-05-22 19:20:47.111 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 已成功创建
2025-05-22 19:20:47.111 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 19:20:47.113 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-22 19:20:47.123 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 19:20:47.124 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-22 19:20:47.125 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-22 19:20:47.134 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 19:20:47.134 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-22 19:20:47.135 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 19:20:47.145 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 19:20:47.146 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 19:20:47.146 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 19:20:47.150 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件清理关闭钩子
2025-05-22 19:20:47.151 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/expiry-test9503440521780988828, 描述: 创建的临时目录
2025-05-22 19:20:47.151 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/expiry-test-211068700955589832951, 描述: 创建的临时目录
2025-05-22 19:20:47.151 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/expiry-test9503440521780988828/instance-id
2025-05-22 19:20:47.151 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 19:20:47.155 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 19:20:47.155 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 19:20:47.177 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 1
2025-05-22 19:20:47.187 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/expiry-test9503440521780988828/instance-id
2025-05-22 19:20:47.191 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-6 - Starting...
2025-05-22 19:20:47.210 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-6 - Added connection org.postgresql.jdbc.PgConnection@37a3ec27
2025-05-22 19:20:47.211 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-6 - Start completed.
2025-05-22 19:20:47.211 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 19:20:47.212 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 19:20:47.212 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 19:20:47.214 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 19:20:47.214 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 19:20:47.215 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 19:20:47.215 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:47.216 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 19:20:47.216 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:47.218 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 19:20:47.218 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 19:20:47.218 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 19:20:47.218 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:47.219 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 19:20:47.219 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:47.221 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 19:20:47.221 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 19:20:47.221 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 19:20:47.221 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:47.222 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 19:20:47.222 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:47.223 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 19:20:47.224 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 19:20:47.224 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 19:20:47.224 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/lease-test10376839453008086320, 描述: 创建的临时目录
2025-05-22 19:20:47.224 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/lease-test10376839453008086320/instance-id
2025-05-22 19:20:47.224 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 19:20:47.227 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 19:20:47.227 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 19:20:47.228 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 2
2025-05-22 19:20:47.230 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/lease-test10376839453008086320/instance-id
2025-05-22 19:20:47.231 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 19:20:47.231 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 19:20:47.231 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 19:20:47.231 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 2 分配工作机器ID，当前线程: main
2025-05-22 19:20:47.231 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 2 是否已分配工作机器ID
2025-05-22 19:20:47.235 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 19:20:47.240 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 0，实例ID: 2
2025-05-22 19:20:47.241 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 2 分配了新的工作机器ID: 0，当前线程: main
2025-05-22 19:20:47.241 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 19:20:47.241 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 19:20:48.231 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: worker-id-lease-renewal
2025-05-22 19:20:48.232 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 2，schemaName: infra_uid，当前线程: worker-id-lease-renewal
2025-05-22 19:20:48.234 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 19:20:48.238 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '5 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 2]，当前线程: worker-id-lease-renewal
2025-05-22 19:20:48.240 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: worker-id-lease-renewal
2025-05-22 19:20:48.241 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: worker-id-lease-renewal
2025-05-22 19:20:48.246 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-22 19:20:48.250 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-7 - Starting...
2025-05-22 19:20:48.285 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-7 - Added connection org.postgresql.jdbc.PgConnection@5a51336a
2025-05-22 19:20:48.285 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-7 - Start completed.
2025-05-22 19:20:48.285 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 19:20:48.286 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 19:20:48.286 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 19:20:48.287 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 19:20:48.287 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 19:20:48.289 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 19:20:48.289 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:48.290 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 19:20:48.290 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:48.292 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 19:20:48.292 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 19:20:48.293 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 19:20:48.293 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:48.294 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 19:20:48.294 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:48.295 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 19:20:48.295 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 19:20:48.296 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 19:20:48.296 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:48.297 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 19:20:48.297 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:48.301 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 19:20:48.301 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 19:20:48.301 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 19:20:48.302 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test6605596966015304498, 描述: 创建的临时目录
2025-05-22 19:20:48.302 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test6605596966015304498/instance-id
2025-05-22 19:20:48.302 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 19:20:48.305 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 19:20:48.305 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 19:20:48.307 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 3
2025-05-22 19:20:48.308 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test6605596966015304498/instance-id
2025-05-22 19:20:48.309 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 19:20:48.309 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 19:20:48.309 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 19:20:48.309 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 3 分配工作机器ID，当前线程: main
2025-05-22 19:20:48.309 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 3 是否已分配工作机器ID
2025-05-22 19:20:48.321 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 19:20:48.324 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 3
2025-05-22 19:20:48.325 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 3 分配了新的工作机器ID: 0，当前线程: main
2025-05-22 19:20:48.325 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 19:20:48.325 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 19:20:48.327 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-22 19:20:48.329 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-8 - Starting...
2025-05-22 19:20:48.346 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-8 - Added connection org.postgresql.jdbc.PgConnection@6c421123
2025-05-22 19:20:48.346 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-8 - Start completed.
2025-05-22 19:20:48.347 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 19:20:48.347 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 19:20:48.347 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 19:20:48.349 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 19:20:48.349 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 19:20:48.350 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 19:20:48.350 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:48.351 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 19:20:48.351 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:48.353 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 19:20:48.353 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 19:20:48.356 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 19:20:48.356 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:48.357 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 19:20:48.357 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:48.358 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 19:20:48.358 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 19:20:48.359 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 19:20:48.359 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:20:48.360 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 19:20:48.360 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:20:48.361 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 19:20:48.361 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 19:20:48.361 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 19:20:48.362 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test12251309826033394046, 描述: 创建的临时目录
2025-05-22 19:20:48.362 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test12251309826033394046, 描述: 并发测试基础目录
2025-05-22 19:20:48.364 [pool-1-thread-2] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test12251309826033394046/instance-id-1, 描述: 并发测试实例 1 的ID文件
2025-05-22 19:20:48.364 [pool-1-thread-3] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test12251309826033394046/instance-id-2, 描述: 并发测试实例 2 的ID文件
2025-05-22 19:20:48.365 [pool-1-thread-1] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test12251309826033394046/instance-id-0, 描述: 并发测试实例 0 的ID文件
2025-05-22 19:20:48.365 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test12251309826033394046/instance-id-2
2025-05-22 19:20:48.365 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test12251309826033394046/instance-id-1
2025-05-22 19:20:48.365 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 19:20:48.365 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 19:20:48.365 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test12251309826033394046/instance-id-0
2025-05-22 19:20:48.365 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 19:20:48.366 [pool-1-thread-4] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test12251309826033394046/instance-id-3, 描述: 并发测试实例 3 的ID文件
2025-05-22 19:20:48.367 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test12251309826033394046/instance-id-3
2025-05-22 19:20:48.367 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 19:20:48.367 [pool-1-thread-5] DEBUG o.x.c.commons.uid.util.TempFileUtils - 已注册临时文件: /tmp/worker-id-test12251309826033394046/instance-id-4, 描述: 并发测试实例 4 的ID文件
2025-05-22 19:20:48.367 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /tmp/worker-id-test12251309826033394046/instance-id-4
2025-05-22 19:20:48.367 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 19:20:48.373 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 19:20:48.373 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 19:20:48.375 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 4
2025-05-22 19:20:48.376 [pool-1-thread-2] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test12251309826033394046/instance-id-1
2025-05-22 19:20:48.376 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-2
2025-05-22 19:20:48.376 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-2
2025-05-22 19:20:48.376 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-2
2025-05-22 19:20:48.376 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 4 分配工作机器ID，当前线程: pool-1-thread-2
2025-05-22 19:20:48.376 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 4 是否已分配工作机器ID
2025-05-22 19:20:48.394 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 19:20:48.394 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 19:20:48.399 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 5
2025-05-22 19:20:48.400 [pool-1-thread-3] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test12251309826033394046/instance-id-2
2025-05-22 19:20:48.401 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-3
2025-05-22 19:20:48.402 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-3
2025-05-22 19:20:48.402 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-2
2025-05-22 19:20:48.402 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-3
2025-05-22 19:20:48.402 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 5 分配工作机器ID，当前线程: pool-1-thread-3
2025-05-22 19:20:48.402 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 5 是否已分配工作机器ID
2025-05-22 19:20:48.411 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 19:20:48.411 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 19:20:48.412 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 6
2025-05-22 19:20:48.414 [pool-1-thread-5] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test12251309826033394046/instance-id-4
2025-05-22 19:20:48.415 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-5
2025-05-22 19:20:48.415 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-5
2025-05-22 19:20:48.415 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-5
2025-05-22 19:20:48.415 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 6 分配工作机器ID，当前线程: pool-1-thread-5
2025-05-22 19:20:48.415 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 6 是否已分配工作机器ID
2025-05-22 19:20:48.415 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 19:20:48.415 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 19:20:48.416 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 7
2025-05-22 19:20:48.417 [pool-1-thread-4] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test12251309826033394046/instance-id-3
2025-05-22 19:20:48.418 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 4
2025-05-22 19:20:48.418 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-4
2025-05-22 19:20:48.418 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-4
2025-05-22 19:20:48.418 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-4
2025-05-22 19:20:48.418 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 7 分配工作机器ID，当前线程: pool-1-thread-4
2025-05-22 19:20:48.418 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 7 是否已分配工作机器ID
2025-05-22 19:20:48.418 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 4 分配了新的工作机器ID: 0，当前线程: pool-1-thread-2
2025-05-22 19:20:48.418 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 19:20:48.418 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-2
2025-05-22 19:20:48.418 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: pool-1-thread-2
2025-05-22 19:20:48.418 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 4，schemaName: infra_uid，当前线程: pool-1-thread-2
2025-05-22 19:20:48.418 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-5
2025-05-22 19:20:48.419 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: pool-1-thread-2
2025-05-22 19:20:48.420 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 4]，当前线程: pool-1-thread-2
2025-05-22 19:20:48.420 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-2
2025-05-22 19:20:48.421 [pool-1-thread-2] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: pool-1-thread-2
2025-05-22 19:20:48.424 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 1，实例ID: 6
2025-05-22 19:20:48.425 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 19:20:48.425 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 19:20:48.426 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 6 分配了新的工作机器ID: 1，当前线程: pool-1-thread-5
2025-05-22 19:20:48.426 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 1
2025-05-22 19:20:48.426 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-5
2025-05-22 19:20:48.426 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 1，当前线程: pool-1-thread-5
2025-05-22 19:20:48.426 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 6，schemaName: infra_uid，当前线程: pool-1-thread-5
2025-05-22 19:20:48.426 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-4
2025-05-22 19:20:48.427 [pool-1-thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-22 19:20:48.427 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-3
2025-05-22 19:20:48.428 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 8
2025-05-22 19:20:48.430 [pool-1-thread-1] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /tmp/worker-id-test12251309826033394046/instance-id-0
2025-05-22 19:20:48.430 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: pool-1-thread-1
2025-05-22 19:20:48.430 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: pool-1-thread-1
2025-05-22 19:20:48.431 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: pool-1-thread-1
2025-05-22 19:20:48.431 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 8 分配工作机器ID，当前线程: pool-1-thread-1
2025-05-22 19:20:48.431 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 8 是否已分配工作机器ID
2025-05-22 19:20:48.431 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 2，实例ID: 7
2025-05-22 19:20:48.432 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 7 分配了新的工作机器ID: 2，当前线程: pool-1-thread-4
2025-05-22 19:20:48.432 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 2
2025-05-22 19:20:48.432 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-4
2025-05-22 19:20:48.432 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 2，当前线程: pool-1-thread-4
2025-05-22 19:20:48.432 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 7，schemaName: infra_uid，当前线程: pool-1-thread-4
2025-05-22 19:20:48.433 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 当前状态: ACTIVE，当前线程: pool-1-thread-5
2025-05-22 19:20:48.433 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 5
2025-05-22 19:20:48.433 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [1, 6]，当前线程: pool-1-thread-5
2025-05-22 19:20:48.434 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-5
2025-05-22 19:20:48.434 [pool-1-thread-5] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 租约续约成功，当前线程: pool-1-thread-5
2025-05-22 19:20:48.437 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 5 分配了新的工作机器ID: 0，当前线程: pool-1-thread-3
2025-05-22 19:20:48.437 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 19:20:48.437 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-3
2025-05-22 19:20:48.437 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: pool-1-thread-3
2025-05-22 19:20:48.437 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 5，schemaName: infra_uid，当前线程: pool-1-thread-3
2025-05-22 19:20:48.451 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 当前状态: ACTIVE，当前线程: pool-1-thread-4
2025-05-22 19:20:48.451 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [2, 7]，当前线程: pool-1-thread-4
2025-05-22 19:20:48.454 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-4
2025-05-22 19:20:48.456 [pool-1-thread-4] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 租约续约成功，当前线程: pool-1-thread-4
2025-05-22 19:20:48.458 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: pool-1-thread-1
2025-05-22 19:20:48.468 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 3，实例ID: 8
2025-05-22 19:20:48.472 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 8 分配了新的工作机器ID: 3，当前线程: pool-1-thread-1
2025-05-22 19:20:48.473 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 3
2025-05-22 19:20:48.473 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: pool-1-thread-1
2025-05-22 19:20:48.473 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 3，当前线程: pool-1-thread-1
2025-05-22 19:20:48.473 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 8，schemaName: infra_uid，当前线程: pool-1-thread-1
2025-05-22 19:20:48.478 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: pool-1-thread-3
2025-05-22 19:20:48.478 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 5]，当前线程: pool-1-thread-3
2025-05-22 19:20:48.482 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 当前状态: ACTIVE，当前线程: pool-1-thread-1
2025-05-22 19:20:48.484 [pool-1-thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 2 释放成功
2025-05-22 19:20:48.486 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '3600 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [3, 8]，当前线程: pool-1-thread-1
2025-05-22 19:20:48.500 [pool-1-thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 1 释放成功
2025-05-22 19:20:48.501 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-1
2025-05-22 19:20:48.503 [pool-1-thread-1] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 租约续约成功，当前线程: pool-1-thread-1
2025-05-22 19:20:48.504 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: pool-1-thread-3
2025-05-22 19:20:48.504 [pool-1-thread-3] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: pool-1-thread-3
2025-05-22 19:20:48.505 [pool-1-thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 3 释放成功
2025-05-22 19:20:48.509 [pool-1-thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功
2025-05-22 19:20:48.513 [main] DEBUG o.x.c.commons.uid.util.TempFileUtils - 开始清理临时文件，当前注册的临时文件数量: 0
2025-05-22 19:20:50.485 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 19:20:51.044 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: ad089c1b7226d3fd97be232131c436ec12177b0f53baee7b7f22ee82530d64c6
2025-05-22 19:20:57.142 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT6.657414127S
2025-05-22 19:20:57.142 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 19:20:57.144 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-9 - Starting...
2025-05-22 19:20:57.170 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-9 - Added connection org.postgresql.jdbc.PgConnection@3feebf9c
2025-05-22 19:20:57.171 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-9 - Start completed.
2025-05-22 19:20:57.171 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 19:20:57.172 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 19:20:57.172 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 19:20:57.174 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 不存在，正在自动创建
2025-05-22 19:20:57.174 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 已成功创建
2025-05-22 19:20:57.174 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 19:20:57.176 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.instance_registry
2025-05-22 19:20:57.186 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 19:20:57.186 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 创建成功
2025-05-22 19:20:57.187 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.worker_id_assignment
2025-05-22 19:20:57.195 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 19:20:57.195 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 创建成功
2025-05-22 19:20:57.197 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 19:20:57.203 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 19:20:57.203 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 19:20:57.203 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 19:20:57.204 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密已启用，应用: key-test, 环境: test, Schema: infra_uid
2025-05-22 19:20:57.205 [main] DEBUG o.x.c.c.u.c.KeyManagementService - 使用NativePRNGNonBlocking算法的SecureRandom
2025-05-22 19:20:57.206 [main] INFO  o.x.c.c.u.c.KeyManagementService - 已为应用 key-test 环境 test 创建新的 test-key-type 类型密钥
2025-05-22 19:20:58.856 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 19:20:59.550 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 7ac5c6a8fca863951b47ff90c9cb20c71fc2184684e5452477aad06442fdae3a
2025-05-22 19:21:01.944 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 19:21:01.944 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 19:21:01.945 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: AVAILABLE，当前线程: worker-id-lease-renewal
2025-05-22 19:21:01.945 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: AVAILABLE)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 19:21:01.945 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 19:21:01.945 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 19:21:01.945 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 19:21:01.945 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:01.988 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 19:21:01.988 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 19:21:01.988 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 19:21:01.989 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 19:21:01.989 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 19:21:01.989 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 19:21:01.989 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 19:21:01.989 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.005 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.021 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID尚未分配，跳过租约续约，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.033 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.034 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.034 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.034 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.034 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.034 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.034 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.034 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.073 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.073 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.074 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.074 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.074 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.074 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.074 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.074 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.095 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 42，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.096 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 12345，schemaName: test_schema，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.096 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 当前状态: null，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.096 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 状态不是ACTIVE (当前: null)，需要重新分配，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.096 [worker-id-lease-renewal] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 处理租约续约失败，工作机器ID: 42，原因: 状态不是ACTIVE，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.096 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已将 workerId 设置为 -1，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.096 [worker-id-lease-renewal] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 设置需要重新分配标志: false -> true，当前线程: worker-id-lease-renewal
2025-05-22 19:21:02.097 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始尝试重新分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.454 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.454 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 19:21:02.455 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 19:21:02.455 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.455 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.455 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.455 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.458 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.458 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:21:02.459 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 19:21:02.459 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.459 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 19:21:02.489 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.489 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 19:21:02.489 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 19:21:02.489 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.489 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.489 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.489 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.492 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.493 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:21:02.494 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.495 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 42，实例ID: 12345
2025-05-22 19:21:02.496 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 12345 分配了新的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 19:21:02.496 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.496 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 19:21:02.536 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.536 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 19:21:02.536 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 19:21:02.536 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.536 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.536 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.537 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.538 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.538 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:21:02.539 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 19:21:02.539 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.539 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 19:21:02.575 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.575 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 19:21:02.575 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 19:21:02.575 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.575 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.575 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.575 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.577 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.577 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:21:02.577 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 19:21:02.577 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.577 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 19:21:02.599 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.599 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 重置需要重新分配标志: true -> false，当前线程: worker-id-reassign
2025-05-22 19:21:02.599 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 当前重试次数: 1/5，当前线程: worker-id-reassign
2025-05-22 19:21:02.599 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 调用 getWorkerId() 获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.599 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.599 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.599 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.601 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 12345 分配工作机器ID，当前线程: worker-id-reassign
2025-05-22 19:21:02.601 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 12345 是否已分配工作机器ID
2025-05-22 19:21:02.601 [worker-id-reassign] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 找到实例 12345 已分配的工作机器ID: 42，当前线程: worker-id-reassign
2025-05-22 19:21:02.601 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: worker-id-reassign
2025-05-22 19:21:02.601 [worker-id-reassign] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取工作机器ID: 42，重置重试计数器，当前线程: worker-id-reassign
2025-05-22 19:21:04.407 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.550993887S
2025-05-22 19:21:04.408 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 19:21:04.409 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-10 - Starting...
2025-05-22 19:21:04.429 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-10 - Added connection org.postgresql.jdbc.PgConnection@6ec3d8e4
2025-05-22 19:21:04.430 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-10 - Start completed.
2025-05-22 19:21:04.438 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-22 19:21:04.441 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: test_schema
2025-05-22 19:21:04.455 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: non_existent_schema
2025-05-22 19:21:04.460 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-11 - Starting...
2025-05-22 19:21:04.477 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-11 - Added connection org.postgresql.jdbc.PgConnection@542ff147
2025-05-22 19:21:04.477 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-11 - Start completed.
2025-05-22 19:21:04.477 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-22 19:21:04.480 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-22 19:21:04.485 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.non_existent_table
2025-05-22 19:21:04.489 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-12 - Starting...
2025-05-22 19:21:04.507 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-12 - Added connection org.postgresql.jdbc.PgConnection@72f3f14c
2025-05-22 19:21:04.508 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-12 - Start completed.
2025-05-22 19:21:04.508 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-22 19:21:04.509 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 验证表结构是否包含所需的列: test_schema.test_table
2025-05-22 19:21:04.509 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-22 19:21:04.513 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table
2025-05-22 19:21:04.519 [main] WARN  o.x.c.c.u.m.PostgreSQLMetadataService - 表 test_schema.test_table 缺少必需的列: name
2025-05-22 19:21:04.522 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-13 - Starting...
2025-05-22 19:21:04.538 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-13 - Added connection org.postgresql.jdbc.PgConnection@52657d5f
2025-05-22 19:21:04.538 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-13 - Start completed.
2025-05-22 19:21:04.538 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-22 19:21:04.540 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 数据库连接验证通过
2025-05-22 19:21:04.540 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查Schema是否存在: test_schema
2025-05-22 19:21:04.542 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - Schema test_schema 验证通过
2025-05-22 19:21:04.542 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-22 19:21:04.545 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 test_schema.test_table 验证通过
2025-05-22 19:21:04.545 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 检查表是否存在: test_schema.test_table
2025-05-22 19:21:04.547 [main] INFO  o.x.c.c.u.s.i.UidValidationServiceImpl - 表 test_schema.test_table 验证通过
2025-05-22 19:21:04.547 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table
2025-05-22 19:21:04.552 [main] ERROR o.x.c.c.u.s.i.UidValidationServiceImpl - 表结构验证失败: 表 test_schema.test_table 缺少必需的列 name
2025-05-22 19:21:04.554 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-14 - Starting...
2025-05-22 19:21:04.570 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-14 - Added connection org.postgresql.jdbc.PgConnection@70485aa
2025-05-22 19:21:04.570 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-14 - Start completed.
2025-05-22 19:21:04.570 [main] INFO  o.x.c.c.u.m.PostgreSQLMetadataService - PostgreSQL元数据服务已初始化，已注册3个查询策略
2025-05-22 19:21:04.572 [main] DEBUG o.x.c.c.u.m.PostgreSQLMetadataService - 获取表中的所有列名: test_schema.test_table

Test run finished after 44162 ms
[         6 containers found      ]
[         0 containers skipped    ]
[         6 containers started    ]
[         0 containers aborted    ]
[         6 containers successful ]
[         0 containers failed     ]
[        14 tests found           ]
[         0 tests skipped         ]
[        14 tests started         ]
[         0 tests aborted         ]
[         6 tests successful      ]
[         8 tests failed          ]


===== 运行简化测试 =====

1. 基本功能测试
2025-05-22 19:21:06.368 [main] INFO  tc.postgres:17.4 - Creating container for image: postgres:17.4
2025-05-22 19:21:06.937 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 is starting: 5936bbeb554ca0f03c30594d472d9ae300b40d825f685f086580fa8a14510a41
2025-05-22 19:21:11.462 [main] INFO  tc.postgres:17.4 - Container postgres:17.4 started in PT5.093950174S
2025-05-22 19:21:11.462 [main] INFO  tc.postgres:17.4 - Container is started (JDBC URL: ******************************************************)
2025-05-22 19:21:11.462 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-15 - Starting...
2025-05-22 19:21:11.484 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-15 - Added connection org.postgresql.jdbc.PgConnection@9dc782d
2025-05-22 19:21:11.484 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-15 - Start completed.
2025-05-22 19:21:11.484 [main] INFO  o.x.c.c.u.test.PostgresTestContainer - 正在执行数据库初始化脚本: init-schema.sql
2025-05-22 19:21:11.532 [main] INFO  o.x.c.c.u.test.PostgresTestContainer - 数据库初始化脚本执行完成
2025-05-22 19:21:11.533 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-16 - Starting...
2025-05-22 19:21:11.549 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-16 - Added connection org.postgresql.jdbc.PgConnection@74bdfa0b
2025-05-22 19:21:11.549 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-16 - Start completed.
2025-05-22 19:21:11.549 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 19:21:11.550 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 19:21:11.550 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 19:21:11.551 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 19:21:11.551 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 19:21:11.553 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 19:21:11.553 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:21:11.554 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 19:21:11.554 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:21:11.555 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 19:21:11.555 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 19:21:11.556 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 19:21:11.556 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:21:11.557 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 19:21:11.557 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:21:11.558 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 19:21:11.558 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 19:21:11.559 [main] INFO  o.x.c.c.uid.util.UidTableManager - 创建表 infra_uid.encryption_key
2025-05-22 19:21:11.566 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 19:21:11.566 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 创建成功
2025-05-22 19:21:11.566 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 19:21:11.588 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 1
2025-05-22 19:21:11.589 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 1 在数据库中不存在
2025-05-22 19:21:11.589 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 19:21:11.591 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 19:21:11.591 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 19:21:11.592 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 1
2025-05-22 19:21:11.596 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/test-instance-id.dat
实例ID: 1
2025-05-22 19:21:11.597 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 19:21:11.597 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 19:21:11.597 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 19:21:11.597 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 1 分配工作机器ID，当前线程: main
2025-05-22 19:21:11.597 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 1 是否已分配工作机器ID
2025-05-22 19:21:11.598 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 19:21:11.603 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功插入新的工作机器ID: 0，实例ID: 1
2025-05-22 19:21:11.603 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 1 分配了新的工作机器ID: 0，当前线程: main
2025-05-22 19:21:11.603 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 19:21:11.603 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
工作机器ID: 0
2025-05-22 19:21:11.604 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放成功

2. 实例恢复测试
2025-05-22 19:21:11.606 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-17 - Starting...
2025-05-22 19:21:11.622 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-17 - Added connection org.postgresql.jdbc.PgConnection@fd4459b
2025-05-22 19:21:11.623 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-17 - Start completed.
2025-05-22 19:21:11.623 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 19:21:11.623 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 19:21:11.623 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 19:21:11.625 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 19:21:11.625 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 19:21:11.626 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 19:21:11.626 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:21:11.630 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 19:21:11.630 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:21:11.632 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 19:21:11.632 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 19:21:11.633 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 19:21:11.633 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:21:11.633 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 19:21:11.634 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:21:11.635 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 19:21:11.635 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 19:21:11.636 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 19:21:11.636 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:21:11.636 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 19:21:11.636 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:21:11.637 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 19:21:11.637 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 19:21:11.637 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
===== 第一次运行 =====
2025-05-22 19:21:11.639 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 2
2025-05-22 19:21:11.640 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 2 在数据库中不存在
2025-05-22 19:21:11.640 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 19:21:11.641 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到任何候选实例
2025-05-22 19:21:11.641 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 19:21:11.642 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 2
2025-05-22 19:21:11.644 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
第一次运行 - 实例ID: 2

===== 第二次运行 (从文件恢复) =====
2025-05-22 19:21:11.645 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 2
2025-05-22 19:21:11.646 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 2 在数据库中存在，更新实例信息
2025-05-22 19:21:11.647 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 2 的最后活动时间
第二次运行 - 实例ID: 2
从文件恢复结果: 成功

===== 删除文件后运行 (从特征码恢复) =====
2025-05-22 19:21:11.648 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 实例ID文件不存在: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
2025-05-22 19:21:11.648 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 19:21:11.648 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 通过指纹哈希精确匹配找到实例ID: 2
2025-05-22 19:21:11.649 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 成功恢复实例ID: 2
2025-05-22 19:21:11.649 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已更新实例 2 的最后活动时间
2025-05-22 19:21:11.651 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/instance-id.dat
删除文件后运行 - 实例ID: 2
从特征码恢复结果: 成功

3. 租约管理测试
2025-05-22 19:21:11.654 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - ===== 租约管理测试开始 =====
2025-05-22 19:21:11.654 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1. 准备测试环境 - 初始化数据源
2025-05-22 19:21:11.654 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.1 检查Docker是否正常运行
2025-05-22 19:21:11.780 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - Docker正常运行
2025-05-22 19:21:11.780 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.2 创建PostgresTestContainer实例
2025-05-22 19:21:11.780 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - PostgresTestContainer实例创建成功
2025-05-22 19:21:11.780 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.3 检查容器是否正在运行
2025-05-22 19:21:11.783 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 容器运行状态: true
2025-05-22 19:21:11.783 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.4 获取JDBC连接信息
2025-05-22 19:21:11.783 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - JDBC URL: ******************************************************
2025-05-22 19:21:11.783 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 用户名: test
2025-05-22 19:21:11.783 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 密码: test
2025-05-22 19:21:11.783 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.5 获取数据源
2025-05-22 19:21:11.784 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-18 - Starting...
2025-05-22 19:21:11.799 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-18 - Added connection org.postgresql.jdbc.PgConnection@b86cb18
2025-05-22 19:21:11.799 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-18 - Start completed.
2025-05-22 19:21:11.799 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据源获取成功
2025-05-22 19:21:11.799 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.6 测试数据库连接
2025-05-22 19:21:11.799 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库连接成功
2025-05-22 19:21:11.799 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库产品名称: PostgreSQL
2025-05-22 19:21:11.799 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库产品版本: 17.4 (Debian 17.4-1.pgdg120+2)
2025-05-22 19:21:11.799 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库驱动名称: PostgreSQL JDBC Driver
2025-05-22 19:21:11.799 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 数据库驱动版本: 42.7.5
2025-05-22 19:21:11.799 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.7 创建JdbcTemplate和TransactionTemplate
2025-05-22 19:21:11.800 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - JdbcTemplate和TransactionTemplate创建成功
2025-05-22 19:21:11.800 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.8 初始化表结构
2025-05-22 19:21:11.800 [main] INFO  o.x.c.c.uid.util.UidTableManager - 开始初始化UID相关表
2025-05-22 19:21:11.800 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 数据库连接验证通过
2025-05-22 19:21:11.800 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查Schema是否存在
2025-05-22 19:21:11.802 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - Schema infra_uid 验证通过
2025-05-22 19:21:11.802 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记Schema验证通过: infra_uid
2025-05-22 19:21:11.803 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.instance_registry 已存在
2025-05-22 19:21:11.803 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:21:11.804 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 验证通过
2025-05-22 19:21:11.804 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:21:11.805 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.instance_registry 结构验证通过
2025-05-22 19:21:11.806 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.instance_registry
2025-05-22 19:21:11.806 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.worker_id_assignment 已存在
2025-05-22 19:21:11.806 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:21:11.807 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 验证通过
2025-05-22 19:21:11.807 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:21:11.808 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.worker_id_assignment 结构验证通过
2025-05-22 19:21:11.808 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.worker_id_assignment
2025-05-22 19:21:11.808 [main] INFO  o.x.c.c.uid.util.UidTableManager - 表 infra_uid.encryption_key 已存在
2025-05-22 19:21:11.808 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法检查表是否存在
2025-05-22 19:21:11.809 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 验证通过
2025-05-22 19:21:11.809 [main] DEBUG o.x.c.c.uid.util.UidValidationUtils - 元数据服务不可用，使用备用方法获取列名列表
2025-05-22 19:21:11.810 [main] INFO  o.x.c.c.uid.util.UidValidationUtils - 表 infra_uid.encryption_key 结构验证通过
2025-05-22 19:21:11.810 [main] DEBUG o.x.c.c.u.v.ValidationResultCache - 已标记表验证通过: infra_uid.encryption_key
2025-05-22 19:21:11.810 [main] INFO  o.x.c.c.uid.util.UidTableManager - UID相关表初始化完成
2025-05-22 19:21:11.810 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 1.2 验证表结构是否创建成功
2025-05-22 19:21:11.817 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 已创建的表: [{table_name=instance_registry}, {table_name=worker_id_assignment}, {table_name=encryption_key}]
2025-05-22 19:21:11.817 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 2. 创建 KeyManagementService
2025-05-22 19:21:11.817 [main] INFO  o.x.c.c.u.c.KeyManagementService - 实例ID文件加密未启用，应用: lease-test, 环境: test, Schema: infra_uid
2025-05-22 19:21:11.817 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - KeyManagementService 创建成功，加密状态: false
2025-05-22 19:21:11.817 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 3. 创建实例管理器
2025-05-22 19:21:11.818 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 从本地文件加载实例ID: 3
2025-05-22 19:21:11.820 [main] WARN  o.x.c.c.u.i.PersistentInstanceManager - 实例ID 3 在数据库中不存在
2025-05-22 19:21:11.820 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 尝试基于机器特征码恢复实例ID
2025-05-22 19:21:11.821 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 未找到精确匹配，且恢复策略为创建新实例，跳过模糊匹配
2025-05-22 19:21:11.821 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 注册新实例
2025-05-22 19:21:11.822 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已注册新实例，ID: 3
2025-05-22 19:21:11.823 [main] INFO  o.x.c.c.u.i.PersistentInstanceManager - 已将实例ID保存到文件: /media/sf_ExchangeWorks/xkong/xkongcloud/./data/uid/lease-test-instance-id.dat
2025-05-22 19:21:11.824 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 2.1 实例管理器创建成功，实例ID: 3
2025-05-22 19:21:11.828 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 已注册的实例: [{instance_unique_id=3, application_name=lease-test, environment=test, instance_group=default, status=ACTIVE, first_registered_at=2025-05-22 19:21:11.821761, last_seen_at=2025-05-22 19:21:11.821761, custom_metadata={"os_arch": "amd64", "os_name": "Linux", "hostname": "long-VirtualBox", "os_version": "5.4.0-91-generic", "mac_addresses": ["7E:21:C9:1A:E8:3D", "08:00:27:D6:3A:87"], "fingerprint_hash": "2c974415142f2dfb2c262abd914df33384b81aa504f53b02d0658bc18c767b85"}}]
2025-05-22 19:21:11.829 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 3. 创建工作机器ID分配器
2025-05-22 19:21:11.829 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 4. 开始分配工作机器ID
2025-05-22 19:21:11.829 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始获取工作机器ID，当前线程: main
2025-05-22 19:21:11.829 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试获取锁，当前线程: main
2025-05-22 19:21:11.829 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功获取锁，当前线程: main
2025-05-22 19:21:11.829 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 尝试为实例 3 分配工作机器ID，当前线程: main
2025-05-22 19:21:11.829 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 查询实例 3 是否已分配工作机器ID
2025-05-22 19:21:11.830 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 未找到已分配的工作机器ID，尝试分配新的工作机器ID，当前线程: main
2025-05-22 19:21:11.835 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 成功更新未使用的工作机器ID: 0，实例ID: 3
2025-05-22 19:21:11.835 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 为实例 3 分配了新的工作机器ID: 0，当前线程: main
2025-05-22 19:21:11.836 [main] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID租约续约已启动，工作机器ID: 0
2025-05-22 19:21:11.836 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 释放锁，当前线程: main
2025-05-22 19:21:11.836 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 分配的工作机器ID: 0
2025-05-22 19:21:11.837 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 工作机器ID分配记录: [{worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:11.831396, lease_expires_at=2025-05-22 19:22:11.831396, released_at=2025-05-22 19:21:11.604285}]
2025-05-22 19:21:11.837 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5. 测试租约续约 - 通过心跳线程自动续约
2025-05-22 19:21:11.837 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.1 等待心跳线程自动续约前，当前状态
2025-05-22 19:21:11.838 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:11.831396, lease_expires_at=2025-05-22 19:22:11.831396, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:11.838 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-22 19:21:12.841 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.1 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:11.831396, lease_expires_at=2025-05-22 19:22:11.831396, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:12.841 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.2 等待心跳线程自动续约前，当前状态
2025-05-22 19:21:12.842 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:11.831396, lease_expires_at=2025-05-22 19:22:11.831396, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:12.842 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-22 19:21:13.848 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.2 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:11.831396, lease_expires_at=2025-05-22 19:22:11.831396, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:13.848 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.3 等待心跳线程自动续约前，当前状态
2025-05-22 19:21:13.854 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:11.831396, lease_expires_at=2025-05-22 19:22:11.831396, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:13.855 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒，让心跳线程有机会执行续约
2025-05-22 19:21:14.858 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 5.3 心跳线程自动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:11.831396, lease_expires_at=2025-05-22 19:22:11.831396, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:14.859 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6. 测试手动续约
2025-05-22 19:21:14.859 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.1 手动续约前，当前状态
2025-05-22 19:21:14.860 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:11.831396, lease_expires_at=2025-05-22 19:22:11.831396, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:14.860 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-22 19:21:14.860 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-22 19:21:14.860 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-22 19:21:14.862 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-22 19:21:14.862 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-22 19:21:14.863 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-22 19:21:14.864 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-22 19:21:14.866 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.1 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:14.86295, lease_expires_at=2025-05-22 19:22:14.86295, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:14.866 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-22 19:21:15.867 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.2 手动续约前，当前状态
2025-05-22 19:21:15.871 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:14.86295, lease_expires_at=2025-05-22 19:22:14.86295, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:15.871 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-22 19:21:15.871 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-22 19:21:15.871 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-22 19:21:15.872 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-22 19:21:15.873 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-22 19:21:15.874 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-22 19:21:15.875 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-22 19:21:15.876 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.2 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:15.873699, lease_expires_at=2025-05-22 19:22:15.873699, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:15.877 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-22 19:21:16.877 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.3 手动续约前，当前状态
2025-05-22 19:21:16.879 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 续约前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:15.873699, lease_expires_at=2025-05-22 19:22:15.873699, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:16.879 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 执行手动续约
2025-05-22 19:21:16.879 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 开始续约租约，工作机器ID: 0，当前线程: main
2025-05-22 19:21:16.879 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 获取实例ID: 3，schemaName: infra_uid，当前线程: main
2025-05-22 19:21:16.880 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 当前状态: ACTIVE，当前线程: main
2025-05-22 19:21:16.880 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 执行SQL: UPDATE infra_uid.worker_id_assignment SET last_renewed_at = NOW(), lease_expires_at = NOW() + INTERVAL '60 SECONDS' WHERE worker_id = ? AND instance_id = ? AND status = 'ACTIVE'，参数: [0, 3]，当前线程: main
2025-05-22 19:21:16.880 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - SQL执行结果: 影响行数 = 1，当前线程: main
2025-05-22 19:21:16.881 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 租约续约成功，当前线程: main
2025-05-22 19:21:16.881 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 6.3 手动续约后，当前状态: {worker_id=0, instance_id=3, status=ACTIVE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:16.88046, lease_expires_at=2025-05-22 19:22:16.88046, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:16.881 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 等待1秒
2025-05-22 19:21:17.882 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7. 测试租约失效后的重新分配
2025-05-22 19:21:17.882 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.1 模拟租约失效 - 将工作机器ID状态设置为AVAILABLE
2025-05-22 19:21:17.885 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 更新结果: 影响行数 = 1
2025-05-22 19:21:17.886 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 更新后状态: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:16.88046, lease_expires_at=2025-05-22 19:22:16.88046, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:17.887 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.2 等待2秒，让租约续约线程发现租约失效
2025-05-22 19:21:19.888 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 7.3 尝试重新获取工作机器ID
2025-05-22 19:21:19.888 [main] DEBUG o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 直接返回已缓存的工作机器ID: 0
2025-05-22 19:21:19.888 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 重新分配的工作机器ID: 0
2025-05-22 19:21:19.890 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 新工作机器ID记录: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:16.88046, lease_expires_at=2025-05-22 19:22:16.88046, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:19.890 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 8. 关闭心跳线程
2025-05-22 19:21:19.891 [main] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放失败
2025-05-22 19:21:19.892 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - 关闭后状态: {worker_id=0, instance_id=3, status=AVAILABLE, assigned_at=2025-05-22 19:21:11.831396, last_renewed_at=2025-05-22 19:21:16.88046, lease_expires_at=2025-05-22 19:22:16.88046, released_at=2025-05-22 19:21:11.604285}
2025-05-22 19:21:19.892 [main] INFO  o.x.c.c.u.s.LeaseManagementTest - ===== 租约管理测试完成 =====
2025-05-22 19:21:21.860 [Thread-10] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 19:21:21.861 [Thread-15] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 19:21:21.862 [Thread-11] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 19:21:21.862 [Thread-2] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 19:21:21.862 [Thread-19] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-18 - Failed to validate connection org.postgresql.jdbc.PgConnection@b86cb18 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-22 19:21:21.863 [Thread-4] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 19:21:21.863 [Thread-0] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 19:21:21.863 [Thread-4] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 19:21:21.863 [Thread-0] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 19:21:21.865 [Thread-8] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 19:21:21.865 [Thread-16] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 19:21:21.865 [Thread-8] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 19:21:21.865 [Thread-14] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 19:21:21.865 [Thread-7] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 19:21:21.866 [Thread-3] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 19:21:21.866 [Thread-6] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 19:21:21.866 [Thread-6] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 19:21:21.866 [Thread-5] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 19:21:21.866 [Thread-5] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 19:21:21.866 [Thread-19] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-18 - Failed to validate connection org.postgresql.jdbc.PgConnection@4f46cac6 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-22 19:21:21.868 [temp-file-cleanup-hook] DEBUG o.x.c.commons.uid.util.TempFileUtils - 执行临时文件清理关闭钩子
2025-05-22 19:21:21.868 [temp-file-cleanup-hook] DEBUG o.x.c.commons.uid.util.TempFileUtils - 开始清理临时文件，当前注册的临时文件数量: 0
2025-05-22 19:21:21.869 [Thread-13] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 19:21:21.869 [Thread-18] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 19:21:21.869 [Thread-12] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: -1
2025-05-22 19:21:21.870 [Thread-1] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 42 释放失败
2025-05-22 19:21:21.870 [Thread-1] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 42
2025-05-22 19:21:51.870 [Thread-19] WARN  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 工作机器ID 0 释放失败: Failed to obtain JDBC Connection
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:653)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:972)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1016)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1026)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.releaseWorkerId(PersistentInstanceWorkerIdAssigner.java:525)
	at org.xkong.cloud.commons.uid.worker.PersistentInstanceWorkerIdAssigner.lambda$initialize$1(PersistentInstanceWorkerIdAssigner.java:85)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.sql.SQLTransientConnectionException: HikariPool-18 - Connection is not available, request timed out after 30002ms (total=0, active=0, idle=0, waiting=0)
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:686)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:179)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:144)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:99)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 7 common frames omitted
Caused by: org.postgresql.util.PSQLException: Connection to localhost:32800 refused. Check that the hostname and port are correct and that the postmaster is accepting TCP/IP connections.
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:352)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273)
	at org.postgresql.Driver.makeConnection(Driver.java:446)
	at org.postgresql.Driver.connect(Driver.java:298)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:724)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:703)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	... 1 common frames omitted
Caused by: java.net.ConnectException: 拒绝连接
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260)
	at org.postgresql.core.PGStream.<init>(PGStream.java:121)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268)
	... 14 common frames omitted
2025-05-22 19:21:51.870 [Thread-19] INFO  o.x.c.c.u.w.PersistentInstanceWorkerIdAssigner - 已释放工作机器ID: 0

进程已结束，退出代码为 0
