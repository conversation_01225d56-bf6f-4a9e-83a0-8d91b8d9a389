# Nexus gRPC Ecosystem: 开发者快速入门指南 (v1)

文档ID: NX-GRPC-GUIDE-06
版本: v1.0.0
状态: 设计中

---

## 核心定位

本项目的核心定位是为开发者提供 Nexus gRPC 生态系统的完整使用指南和最佳实践。通过详细的步骤说明和代码示例，帮助开发者快速上手并正确使用 Nexus 框架构建生产级的 gRPC 微服务应用。

**技术栈**: Java 21、Spring Boot 3.4.5、gRPC-Java 1.73.0、Nacos 2.3.0、Maven 3.9.6
复杂度等级: L1

## 包含范围
- Maven 依赖配置指导
- Proto 文件定义和代码生成
- gRPC 服务实现和发布
- gRPC 客户端调用和注入
- 服务发现配置和使用
- 完整示例项目构建

## 排除范围
- 复杂的高级特性配置
- 生产环境性能调优
- 自定义插件开发
- 详细的架构设计理论

## 设计哲学

本项目遵循以下设计哲学：
1. **简单易用** - 最小化配置，最大化开发效率
2. **约定优于配置** - 提供合理的默认值和约定
3. **渐进式学习** - 从简单示例到复杂场景的学习路径
4. **实践驱动** - 通过完整可运行的示例展示用法

## 实施约束

### 强制性要求
- **依赖版本管理**: 必须使用 nexus-grpc-bom 统一管理版本
  - 约束细节：禁止在项目模块中声明具体版本号，所有版本由BOM管理
  - 违规后果：可能导致版本冲突和运行时异常
  - 验证方式：通过Maven Enforcer Plugin检查版本一致性
- **注解使用规范**: 服务实现必须使用 @GrpcService，客户端注入必须使用 @InjectGrpcClient
  - 约束细节：不得使用原生gRPC的手动注册方式
  - 违规后果：框架无法自动识别和管理服务/客户端生命周期
  - 验证方式：通过ArchUnit架构测试验证注解使用
- **配置规范**: 配置参数必须遵循 nexus.grpc.* 命名空间
  - 约束细节：禁止使用grpc.* 或其他命名空间的配置
  - 违规后果：配置无法生效，导致功能异常
  - 验证方式：通过Spring Boot Configuration Processor验证
- **启动类要求**: 主启动类必须添加 @EnableNexusGrpc 注解
  - 约束细节：必须在@SpringBootApplication类上添加该注解
  - 违规后果：Nexus框架功能无法启用
  - 验证方式：通过集成测试验证框架功能正常启用

### 性能要求
- **编译时间约束**: 项目编译时间不得超过 5 分钟
  - 测量方式：从mvn clean compile开始到完成的总耗时
  - 影响因素：Proto文件数量、Maven插件配置、机器性能
  - 验证方式：CI/CD流水线中设置超时阈值
- **启动时间约束**: 应用启动时间不得超过 30 秒
  - 测量标准：从main方法执行到Spring ApplicationContext完全启动
  - 优化策略：懒加载、启动时依赖检查优化
  - 验证方式：通过Spring Boot Actuator监控启动时间
- **调用延迟要求**: gRPC 调用延迟不得超过 100ms（本地网络环境）
  - 测量范围：客户端发起调用到收到响应的端到端延迟
  - 网络环境：同一网段内的本地调用（RTT < 1ms）
  - 验证方式：通过集成测试和性能测试验证

### 开发环境约束
- **Java版本**: 必须使用Java 21或更高版本
- 兼容范围：Java 21+，优先使用LTS版本
- 特性依赖：使用了Java 21的虚拟线程、record、sealed class等特性
  - 验证方式：Maven Compiler Plugin版本检查
- **Maven版本**: 必须使用Maven 3.9.6或更高版本
- 最低要求：3.9.6（支持Java 21完整特性）
  - 推荐版本：3.9.6+（性能和稳定性提升）
  - 验证方式：通过Maven Wrapper统一版本
- **IDE插件要求**: 强烈建议安装Protocol Buffers插件
  - 支持IDE：IntelliJ IDEA、Eclipse、VS Code
  - 功能支持：语法高亮、代码补全、错误检查
  - 安装验证：检查.proto文件语法高亮是否正常

### 代码质量约束
- **代码覆盖率**: 业务逻辑代码覆盖率不得低于80%
  - 测量工具：JaCoCo
  - 覆盖范围：不包括自动生成的Proto代码
  - 验证方式：Sonar质量门禁检查
- **静态代码分析**: 必须通过SonarQube质量门禁
  - 阻断规则：无Critical和Major级别的问题
  - 检查内容：潜在Bug、安全漏洞、代码异味
  - 验证方式：CI/CD流水线集成SonarQube扫描
- **代码风格**: 必须符合Google Java Style Guide
  - 格式化工具：google-java-format
  - 检查工具：Checkstyle
  - 验证方式：通过Maven Checkstyle Plugin检查

### 安全约束
- **依赖安全扫描**: 必须通过依赖安全漏洞扫描
  - 扫描工具：OWASP Dependency Check
  - 阻断级别：High和Critical级别漏洞
  - 验证方式：CI/CD流水线中集成安全扫描
- **敏感信息保护**: 禁止在代码和配置中硬编码敏感信息
  - 保护内容：API密钥、数据库密码、证书等
  - 替代方案：使用环境变量或配置中心
  - 验证方式：通过git-secrets工具扫描

### 部署约束
- **容器化要求**: 生产部署必须使用Docker容器
  - 基础镜像：openjdk:17-jre-slim或更新版本
  - 资源限制：内存限制不超过1GB，CPU限制不超过1核
  - 验证方式：通过Docker健康检查验证
- **配置外部化**: 环境相关配置必须外部化
  - 配置来源：环境变量、ConfigMap、外部配置中心
  - 禁止内容：环境特定的硬编码配置
  - 验证方式：多环境部署测试验证

### 验证锚点
- **编译验证**: 通过 maven clean compile 验证代码生成正确性
  - 验证内容：Proto文件编译、Java代码生成、依赖解析
  - 自动化程度：每次代码提交都自动执行
  - 失败处理：编译失败时阻止代码合并
- **启动验证**: 通过 SpringBootTest 验证应用启动成功
  - 测试覆盖：Spring Context加载、Bean注入、端口绑定
  - 测试策略：使用@SpringBootTest(webEnvironment = RANDOM_PORT)
  - 验证频率：每次构建都执行完整启动测试
- **功能验证**: 通过集成测试验证 gRPC 调用正常
  - 测试场景：正常调用、异常处理、超时重试
  - 测试工具：TestContainers + gRPC测试客户端
  - 验证标准：调用成功率100%，响应时间符合要求
- **端到端验证**: 通过完整链路测试验证系统集成
  - 测试环境：模拟生产环境的测试环境
  - 测试流程：从REST API到gRPC调用的完整链路
  - 验证频率：每个迭代结束前执行

## 架构蓝图

### 应用分层架构设计

#### 层次划分与职责定义
系统采用标准的分层架构模式，确保关注点分离和代码的可维护性：

```
┌─────────────────────────────────────────┐
│        Web Controller Layer           │  ← 接入层
│  • REST API暴露                       │  • 请求路由和参数验证
│  • 参数校验和转换                       │  • 协议转换（HTTP/gRPC）
│  • 响应格式化                         │  • 异常处理和错误码映射
├─────────────────────────────────────────┤
│         Service Layer                 │  ← 业务逻辑层
│  • 业务逻辑编排                       │  • 事务管理和一致性保证
│  • 数据聚合和处理                     │  • 业务规则实现
│  • 外部服务协调                       │  • 领域模型操作
├─────────────────────────────────────────┤
│       gRPC Client Layer              │  ← gRPC客户端层
│  ┌─────────────────────────────────┐  │  • 远程服务调用
│  │   @InjectGrpcClient Stubs       │  │  • 客户端负载均衡
│  │   • Blocking/Async Stubs       │  │  • 连接池管理
│  │   • Custom Interceptors        │  │  • 超时和重试控制
│  └─────────────────────────────────┘  │
├─────────────────────────────────────────┤
│         Nexus Framework              │  ← Nexus治理层
│  ┌──────────┬──────────┬──────────┐  │  • 横切关注点管理
│  │Discovery │Security  │Monitoring│  │  • 插件生命周期管理
│  │• 服务注册 │• 认证鉴权  │• 指标收集 │  │  • 配置统一管理
│  │• 实例发现 │• 传输加密  │• 链路追踪 │  │  • 自动装配和注入
│  └──────────┴──────────┴──────────┘  │
├─────────────────────────────────────────┤
│         gRPC Transport               │  ← 传输层
│  • 网络通信管理                       │  • 连接管理和复用
│  • 协议编解码                         │  • 流控和背压处理
│  • 序列化/反序列化                     │  • 网络异常处理
└─────────────────────────────────────────┘
```

#### 依赖关系与约束
- **向下依赖原则**: 上层可以依赖下层，严禁反向依赖
- **层内隔离原则**: 同层组件间保持松耦合，通过接口通信
- **跨层访问控制**: 禁止跨层直接访问，必须通过相邻层的接口

#### 组件交互模式
```
HTTP Request → Controller → Service → gRPC Client → Nexus Framework → Transport
     ↓              ↓           ↓            ↓              ↓              ↓
Response ← DTO ← Domain ← gRPC Response ← Framework ← Network Response
```

### 开发流程规范

#### 标准开发流程 (Standard Development Workflow)
1. **契约优先设计 (Contract-First Design)**
   - 编写 .proto 文件定义服务接口
   - 团队评审接口设计的合理性
   - 确保向后兼容性和扩展性
   
2. **代码生成与验证 (Code Generation & Verification)**
   - 使用 protobuf-maven-plugin 生成 Java 代码
   - 执行 `mvn clean compile` 验证生成代码正确性
   - 检查生成的接口是否符合预期
   
3. **服务端实现 (Server Implementation)**
   - 继承生成的服务基类
   - 使用 @GrpcService 注解标记服务实现
   - 实现业务逻辑并处理异常情况
   
4. **客户端集成 (Client Integration)**
   - 使用 @InjectGrpcClient 注解注入客户端存根
   - 配置客户端调用参数（超时、重试等）
   - 实现客户端异常处理和降级逻辑
   
5. **配置管理 (Configuration Management)**
   - 配置服务发现参数
   - 配置安全认证机制
   - 配置监控和日志参数
   
6. **测试验证 (Testing & Validation)**
   - 编写单元测试验证业务逻辑
   - 编写集成测试验证gRPC调用
   - 执行端到端测试验证完整链路

#### 演进式开发模式 (Evolutionary Development)
- **最小可用产品 (MVP)**: 先实现核心功能，确保基本可用
- **渐进增强**: 逐步添加高级特性（安全、监控、优化）
- **持续重构**: 定期重构代码，保持架构清晰和代码质量

### 关键配置项详解

#### 服务端配置 (Server Configuration)
```yaml
nexus:
  grpc:
    server:
      port: 9090                    # gRPC服务监听端口
      max-inbound-message-size: 4MB # 最大入站消息大小
      max-outbound-message-size: 4MB # 最大出站消息大小
      keep-alive-time: 30s          # 连接保活时间
      keep-alive-timeout: 5s        # 保活超时时间
      permit-keep-alive-without-calls: true # 允许无调用时保活
```

#### 客户端配置 (Client Configuration)  
```yaml
nexus:
  grpc:
    client:
      default-deadline: 30s         # 默认调用超时时间
      max-retry-attempts: 3         # 最大重试次数
      retry-delay: 1s              # 重试延迟
      keep-alive-time: 30s         # 客户端保活时间
      idle-timeout: 5m             # 连接空闲超时
```

#### 服务发现配置 (Service Discovery Configuration)
```yaml
nexus:
  grpc:
    discovery:
      type: nacos                   # 服务发现类型 (nacos/consul/kubernetes)
      health-check-interval: 10s    # 健康检查间隔
      instance-weight: 1.0          # 实例权重
      metadata:                     # 实例元数据
        version: "1.0.0"
        zone: "default"
```

#### 安全配置 (Security Configuration)
```yaml
nexus:
  grpc:
    security:
      enabled: true                 # 启用安全功能
      tls:
        enabled: true               # 启用TLS传输加密
        cert-chain-file: cert.pem   # 证书链文件
        private-key-file: key.pem   # 私钥文件
      authentication:
        type: jwt                   # 认证类型 (jwt/oauth2)
        jwt:
          secret-key: "your-secret" # JWT密钥
          token-expiration: 3600s   # Token过期时间
```

#### 监控配置 (Monitoring Configuration)
```yaml
nexus:
  grpc:
    monitoring:
      enabled: true                 # 启用监控功能
      metrics:
        enabled: true               # 启用指标收集
        step: 10s                   # 指标收集间隔
      tracing:
        enabled: true               # 启用链路追踪
        sampling-rate: 0.1          # 采样率 (10%)
      logging:
        level: INFO                 # 日志级别
        include-payload: false      # 是否记录请求载荷
```

### 部署架构模式

#### 单体部署模式 (Monolithic Deployment)
- **适用场景**: 开发测试环境、小规模应用
- **架构特点**: 所有服务部署在同一个进程中
- **优势**: 部署简单、调试方便
- **劣势**: 扩展性有限、技术栈绑定

#### 微服务部署模式 (Microservices Deployment)
- **适用场景**: 生产环境、大规模分布式系统
- **架构特点**: 每个服务独立部署和扩展
- **优势**: 高可用性、独立演进、技术多样性
- **注意事项**: 需要完善的服务治理和监控体系

#### 容器化部署模式 (Containerized Deployment)
- **基础镜像**: 基于Alpine Linux的轻量级JRE镜像
- **资源配置**: 根据服务特性配置CPU和内存限制
- **健康检查**: 配置gRPC健康检查和就绪探针
- **日志管理**: 统一日志收集和分析

## 1. 目标

本指南将引导您完成一个完整的 `Nexus gRPC` 应用的创建过程。我们将构建两个微服务：

- **`user-service`**: 一个 gRPC 服务，提供查询用户基本信息的功能。
- **`api-gateway`**: 一个普通的 Spring Boot 服务，它作为客户端调用 `user-service`。

通过本指南，您将学会：

- 如何配置 Maven 依赖。
- 如何定义 `.proto` 文件并生成代码。
- 如何使用 `@GrpcService` 发布一个 gRPC 服务。
- 如何使用 `@InjectGrpcClient` 调用一个 gRPC 服务。
- 如何配置 `application.yml` 以启用服务发现。

## 2. 步骤一：项目初始化与依赖配置

### 2.1. 定义 `pom.xml`

首先，在您的父 `pom.xml` 中，使用 `dependencyManagement` 统一管理 `Nexus` 生态的版本。

```xml
<!-- 父 POM -->
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.xkong.cloud</groupId>
            <artifactId>nexus-grpc-bom</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>
```

在 **`user-service`** 模块的 `pom.xml` 中，添加以下依赖：

```xml
<!-- user-service/pom.xml -->
<dependencies>
    <!-- 1. Nexus Starter: 包含了所有核心能力和自动配置 -->
    <dependency>
        <groupId>org.xkong.cloud</groupId>
        <artifactId>nexus-grpc-starter</artifactId>
    </dependency>

    <!-- 2. 服务发现插件: 我们选择 Nacos -->
    <dependency>
        <groupId>org.xkong.cloud</groupId>
        <artifactId>nexus-grpc-discovery-nacos</artifactId>
    </dependency>

    <!-- 3. Protobuf 和 gRPC 相关的编译时依赖 -->
    <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-protobuf</artifactId>
    </dependency>
    <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-stub</artifactId>
    </dependency>
</dependencies>
```

在 **`api-gateway`** 模块的 `pom.xml` 中，添加同样的依赖。

## 3. 步骤二：定义服务契约 (`.proto`)

在 `src/main/proto` 目录下创建一个 `user_service.proto` 文件。

```protobuf
syntax = "proto3";

package org.xkong.cloud.userservice.api;

option java_multiple_files = true;
option java_package = "org.xkong.cloud.userservice.api";

// 定义 User 服务
service UserService {
  // 根据 ID 查找用户
  rpc FindUserById(FindUserByIdRequest) returns (UserResponse);
}

// 请求体
message FindUserByIdRequest {
  int64 user_id = 1;
}

// 响应体
message UserResponse {
  int64 user_id = 1;
  string name = 2;
  string email = 3;
}
```

使用 `protobuf-maven-plugin` 编译此文件，生成 Java 代码。

## 4. 步骤三：实现并发布 gRPC 服务 (`user-service`)

### 4.1. 编写服务实现

创建一个类 `UserServiceImpl`，继承自生成的 `UserServiceGrpc.UserServiceImplBase`，并使用 `@GrpcService` 注解。

```java
package org.xkong.cloud.userservice;

import org.xkong.cloud.nexus.grpc.api.annotation.GrpcService;
import org.xkong.cloud.userservice.api.*;
import io.grpc.stub.StreamObserver;

@GrpcService // <-- 只需这一个注解，Nexus 就会自动发布此服务
public class UserServiceImpl extends UserServiceGrpc.UserServiceImplBase {

    @Override
    public void findUserById(FindUserByIdRequest request, StreamObserver<UserResponse> responseObserver) {
        // 模拟业务逻辑
        UserResponse user = UserResponse.newBuilder()
                .setUserId(request.getUserId())
                .setName("Test User")
                .setEmail("<EMAIL>")
                .build();

        responseObserver.onNext(user);
        responseObserver.onCompleted();
    }
}
```

### 4.2. 配置并启动应用

在 `src/main/resources/application.yml` 中进行配置：

```yaml
server:
  port: 8081 # Spring Boot Web 端口

spring:
  application:
    name: user-service # 服务名，将用于服务注册

nexus:
  grpc:
    server:
      port: 9090 # gRPC 服务监听的端口
    discovery:
      type: nacos # 指定使用 nacos 进行服务发现

# Nacos 服务器地址
nacos:
  discovery:
    server-addr: 127.0.0.1:8848
```

创建主启动类：

```java
package org.xkong.cloud.userservice;

import org.xkong.cloud.nexus.grpc.api.annotation.EnableNexusGrpc;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
@EnableNexusGrpc // <-- 激活 Nexus gRPC 生态
public class UserServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(UserServiceApplication.class, args);
    }
}
```

现在，启动 `UserServiceApplication`。应用启动后，它会自动将自己注册到 Nacos，服务名为 `user-service`。

## 5. 步骤四：调用 gRPC 服务 (`api-gateway`)

### 5.1. 编写客户端调用逻辑

在 `api-gateway` 服务中，创建一个 REST Controller，并使用 `@InjectGrpcClient` 注入 `user-service` 的存根。

```java
package org.xkong.cloud.apigateway;

import org.xkong.cloud.nexus.grpc.api.annotation.InjectGrpcClient;
import org.xkong.cloud.userservice.api.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class GatewayController {

    // <-- 只需这一个注解，Nexus 负责所有客户端复杂性
    @InjectGrpcClient("user-service") 
    private UserServiceGrpc.UserServiceBlockingStub userStub;

    @GetMapping("/users/{id}")
    public String getUser(@PathVariable("id") long id) {
        FindUserByIdRequest request = FindUserByIdRequest.newBuilder().setUserId(id).build();
        
        // 直接调用，就像调用一个本地方法一样简单
        UserResponse response = userStub.findUserById(request);

        return response.toString();
    }
}
```

### 5.2. 配置并启动应用

`api-gateway` 的 `application.yml` 配置非常相似，只是不需要 `nexus.grpc.server` 部分。

```yaml
server:
  port: 8080

spring:
  application:
    name: api-gateway

nexus:
  grpc:
    discovery:
      type: nacos

nacos:
  discovery:
    server-addr: 127.0.0.1:8848
```

创建并启动 `ApiGatewayApplication` (同样需要 `@EnableNexusGrpc`)。

## 6. 验证

1.  确保 Nacos 正在运行。
2.  启动 `UserServiceApplication`。
3.  启动 `ApiGatewayApplication`。
4.  在浏览器或使用 curl 访问 `http://localhost:8080/users/123`。

您应该能看到 `user-service` 返回的用户信息。整个过程中，您没有写一行关于 Channel、负载均衡或服务发现的代码，所有这些都由 `Nexus` 自动完成。

---

**下一步**: [07-migration-plan.md](./07-migration-plan.md)
