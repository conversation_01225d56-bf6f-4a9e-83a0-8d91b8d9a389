# gRPC傻瓜式入门教程 - 第二部分：gRPC流式通信与错误处理

## 前言

在第一部分中，我们学习了gRPC的基础知识和Spring Boot集成方法。本部分将深入探讨gRPC的流式通信和错误处理机制，这是gRPC的两个重要特性，能够帮助你构建更强大、更可靠的分布式系统。

## 1. gRPC流式通信

### 1.1 流式通信概述

gRPC支持四种类型的服务调用，其中三种涉及流式通信：

1. **一元RPC(Unary RPC)**：传统的请求-响应模式
2. **服务器流式RPC(Server Streaming RPC)**：客户端发送一个请求，服务器返回一个响应流
3. **客户端流式RPC(Client Streaming RPC)**：客户端发送一个请求流，服务器返回一个响应
4. **双向流式RPC(Bidirectional Streaming RPC)**：客户端和服务器都可以发送消息流

> **通俗解释**：流式通信就像是持续的对话，而不是单次问答。服务器流就像广播，客户端流就像录音，双向流就像电话通话。

### 1.2 服务器流式RPC

服务器流式RPC允许服务器向客户端发送多个响应消息，这在需要持续接收数据的场景中非常有用，如配置变更监听、实时数据更新等。

#### 在.proto文件中定义服务器流

```protobuf
// 监听KV参数变更请求
message WatchKVParamsRequest {
  string key_prefix = 1; // 键前缀，用于过滤要监听的键
}

// KV参数变更事件
message KVParamChangeEvent {
  enum ChangeType {
    UPDATE = 0;  // 更新或新增
    DELETE = 1;  // 删除
  }
  ChangeType type = 1;  // 变更类型
  string key = 2;       // 参数键
  string value = 3;     // 参数值（当type为DELETE时可能为空）
  int64 timestamp = 4;  // 变更时间戳
}

// 在服务定义中添加流式方法
service KVService {
  // ... 其他方法
  
  // 监听KV参数变更（服务器流式RPC）
  rpc WatchKVParams (WatchKVParamsRequest) returns (stream KVParamChangeEvent);
}
```

> **通俗解释**：
> - **returns (stream KVParamChangeEvent)**：表示服务器将返回一系列KV参数变更事件，而不是单个响应。
> - **key_prefix**：允许客户端指定只监听特定前缀的键，就像订阅特定主题的新闻。

#### 服务器端实现

```java
/**
 * 实现KV参数变更监听（服务器流式RPC）
 */
@Override
public void watchKVParams(WatchKVParamsRequest request, StreamObserver<KVParamChangeEvent> responseObserver) {
    // 从Context中获取客户端IP
    String clientIp = ClientIpInterceptor.CLIENT_IP_CONTEXT_KEY.get();
    log.info("收到KV参数变更监听请求: clientIp={}", clientIp);

    // 将responseObserver转换为ServerCallStreamObserver，以便设置取消处理器
    ServerCallStreamObserver<KVParamChangeEvent> serverCallObserver = 
            (ServerCallStreamObserver<KVParamChangeEvent>) responseObserver;

    // 设置取消处理器，在客户端断开连接时清理资源
    serverCallObserver.setOnCancelHandler(() -> {
        log.info("客户端取消了KV参数变更监听: clientIp={}", clientIp);
        // 清理资源
        removeStreamObserverByIp(clientIp, responseObserver);
    });

    // 存储IP和观察者的映射关系（一个IP只能有一个连接）
    StreamObserver<KVParamChangeEvent> oldObserver = activeStreamsByIp.put(clientIp, responseObserver);
    if (oldObserver != null) {
        log.info("同一IP地址的旧连接被新连接替换: clientIp={}", clientIp);
        try {
            // 通知旧连接已被替换
            oldObserver.onCompleted();
        } catch (Exception e) {
            log.warn("通知旧连接关闭时发生错误: {}", e.getMessage());
        }
    }

    // 发送一个初始事件，表示监听已建立
    try {
        KVParamChangeEvent initialEvent = KVParamChangeEvent.newBuilder()
                .setType(KVParamChangeEvent.ChangeType.UPDATE)
                .setKey("system.monitor.status")
                .setValue("connected")
                .setTimestamp(System.currentTimeMillis())
                .build();

        responseObserver.onNext(initialEvent);
        log.info("KV参数变更监听已建立: clientIp={}", clientIp);
    } catch (Exception e) {
        log.error("发送初始事件失败", e);
        responseObserver.onError(e);
        // 从活跃观察者中移除
        removeStreamObserverByIp(clientIp, responseObserver);
    }

    // 注意：这里不调用onCompleted，因为这是一个长连接流
}

/**
 * 向所有客户端推送KV参数变更事件
 */
public void pushKVParamChangeEvent(String key, String value, KVParamChangeEvent.ChangeType type) {
    // 创建变更事件
    KVParamChangeEvent event = KVParamChangeEvent.newBuilder()
            .setType(type)
            .setKey(key)
            .setValue(value)
            .setTimestamp(System.currentTimeMillis())
            .build();

    // 向所有活跃的客户端推送事件
    for (Map.Entry<String, StreamObserver<KVParamChangeEvent>> entry : activeStreamsByIp.entrySet()) {
        String clientIp = entry.getKey();
        StreamObserver<KVParamChangeEvent> observer = entry.getValue();
        
        try {
            observer.onNext(event);
            log.debug("向客户端推送KV参数变更事件: clientIp={}, key={}", clientIp, key);
        } catch (Exception e) {
            log.warn("向客户端推送KV参数变更事件失败: clientIp={}, key={}, error={}", 
                    clientIp, key, e.getMessage());
            
            // 移除失效的观察者
            removeStreamObserverByIp(clientIp, observer);
        }
    }
}
```

> **通俗解释**：
> - **setOnCancelHandler**：设置客户端断开连接时的处理逻辑，就像设置电话挂断时的清理工作。
> - **onNext**：向客户端发送一个事件，就像在通话中说一句话。
> - **onCompleted**：结束流，表示不再发送更多消息，就像结束通话。
> - **onError**：发送错误，表示流出现问题，就像通话中断。

#### 客户端实现

```java
/**
 * 启动配置变更监听
 */
private void startWatchingParams() {
    logger.info("启动KV参数变更监听");

    // 创建请求
    WatchKVParamsRequest request = WatchKVParamsRequest.newBuilder()
            .build();

    // 创建响应观察者
    StreamObserver<KVParamChangeEvent> responseObserver = new StreamObserver<>() {
        @Override
        public void onNext(KVParamChangeEvent event) {
            handleParamChangeEvent(event);
        }

        @Override
        public void onError(Throwable t) {
            handleStreamError(t);
        }

        @Override
        public void onCompleted() {
            logger.info("配置变更监听流由服务端正常关闭");
            // 设置流为非活跃状态
            streamActive.set(false);
            // 不再尝试重连，因为这是服务端正常关闭
        }
    };

    // 发送请求
    asyncStub.watchKVParams(request, responseObserver);
    streamActive.set(true);

    logger.info("配置变更监听已启动");
}

/**
 * 处理参数变更事件
 */
private void handleParamChangeEvent(KVParamChangeEvent event) {
    String key = event.getKey();
    String value = event.getValue();
    KVParamChangeEvent.ChangeType type = event.getType();
    
    logger.debug("收到参数变更事件: key={}, type={}", key, type);
    
    // 更新本地缓存
    if (type == KVParamChangeEvent.ChangeType.UPDATE) {
        // 更新缓存
        updateCache(key, value);
        
        // 通知监听器
        notifyListeners(key, value);
    } else if (type == KVParamChangeEvent.ChangeType.DELETE) {
        // 从缓存中移除
        removeFromCache(key);
        
        // 通知监听器
        notifyListeners(key, null);
    }
}
```

> **通俗解释**：
> - **StreamObserver**：用于接收服务器发送的消息流，就像电话接收器。
> - **onNext**：当收到一个新消息时调用，就像听到对方说的一句话。
> - **onError**：当流出现错误时调用，就像通话中断。
> - **onCompleted**：当流正常结束时调用，就像对方说"再见"并挂断电话。

### 1.3 客户端流式RPC

客户端流式RPC允许客户端向服务器发送多个请求消息，服务器返回一个响应。这在需要上传大量数据的场景中非常有用，如文件上传、批量数据处理等。

#### 在.proto文件中定义客户端流

```protobuf
// 批量上传KV参数请求（单个参数）
message UploadKVParamRequest {
  string key = 1;   // 参数键
  string value = 2; // 参数值
}

// 批量上传KV参数响应
message UploadKVParamsResponse {
  int32 success_count = 1; // 成功上传的参数数量
  repeated string failed_keys = 2; // 上传失败的参数键
}

// 在服务定义中添加客户端流式方法
service KVService {
  // ... 其他方法
  
  // 批量上传KV参数（客户端流式RPC）
  rpc UploadKVParams (stream UploadKVParamRequest) returns (UploadKVParamsResponse);
}
```

> **通俗解释**：
> - **rpc UploadKVParams (stream UploadKVParamRequest)**：表示客户端将发送一系列KV参数，而不是单个请求。
> - **returns (UploadKVParamsResponse)**：服务器将返回一个汇总响应，包含成功和失败的信息。

## 2. gRPC错误处理

### 2.1 gRPC错误模型

gRPC使用状态码和错误详情来表示错误，这提供了一种标准化的错误处理机制。

#### gRPC状态码

gRPC定义了一组标准状态码，每个状态码代表一种特定类型的错误：

| 状态码 | 名称 | 描述 | 通俗解释 |
|-------|------|------|---------|
| 0 | OK | 成功 | 一切正常，请求成功完成 |
| 1 | CANCELLED | 操作被取消 | 就像你取消了一个电话呼叫 |
| 2 | UNKNOWN | 未知错误 | 出了问题，但不知道具体是什么问题 |
| 3 | INVALID_ARGUMENT | 客户端指定了无效参数 | 就像你给餐厅点菜时点了菜单上没有的菜 |
| 4 | DEADLINE_EXCEEDED | 操作在完成前超时 | 就像等外卖太久，超过了约定的送达时间 |
| 5 | NOT_FOUND | 请求的实体不存在 | 就像你拨打了一个不存在的电话号码 |
| 6 | ALREADY_EXISTS | 尝试创建的实体已存在 | 就像你尝试注册一个已被使用的用户名 |
| 7 | PERMISSION_DENIED | 没有操作权限 | 就像你试图进入一个没有通行证的区域 |
| 8 | RESOURCE_EXHAUSTED | 资源已耗尽 | 就像你的手机存储空间已满 |
| 9 | FAILED_PRECONDITION | 操作被拒绝，系统不在可接受操作的状态 | 就像你试图在关机的电脑上安装软件 |
| 10 | ABORTED | 操作被中止 | 就像你的银行转账被系统中断 |
| 11 | OUT_OF_RANGE | 操作尝试超出有效范围 | 就像你试图访问数组中不存在的元素 |
| 12 | UNIMPLEMENTED | 操作未实现或不支持 | 就像你使用了一个尚未开发的功能 |
| 13 | INTERNAL | 内部错误 | 系统内部出现了问题，就像餐厅厨房设备故障 |
| 14 | UNAVAILABLE | 服务当前不可用 | 就像你打电话时对方暂时无法接通 |
| 15 | DATA_LOSS | 不可恢复的数据丢失或损坏 | 就像你的文件被损坏且无法恢复 |
| 16 | UNAUTHENTICATED | 请求没有有效的身份验证凭据 | 就像你试图使用过期的会员卡 |

### 2.2 服务器端错误处理

在服务器端，可以使用`Status`类和`StatusRuntimeException`来返回错误：

```java
/**
 * 获取KV参数
 */
@Override
public void getKVParam(GetKVParamRequest request, StreamObserver<GetKVParamResponse> responseObserver) {
    String key = request.getKey();
    log.debug("获取参数请求: key={}", key);

    try {
        // 参数键为空
        if (key == null || key.isEmpty()) {
            // 创建INVALID_ARGUMENT状态
            Status status = Status.INVALID_ARGUMENT
                    .withDescription("参数键不能为空");
            // 使用状态完成响应（出错）
            responseObserver.onError(status.asRuntimeException());
            return;
        }

        // 获取参数值
        String value = getParamValue(key);

        // 参数不存在
        if (value == null) {
            // 创建NOT_FOUND状态
            Status status = Status.NOT_FOUND
                    .withDescription("参数不存在: " + key);
            // 使用状态完成响应（出错）
            responseObserver.onError(status.asRuntimeException());
            return;
        }

        // 构建响应
        GetKVParamResponse response = GetKVParamResponse.newBuilder()
                .setValue(value)
                .build();

        // 发送响应
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    } catch (Exception e) {
        log.error("获取参数时发生错误: key={}, error={}", key, e.getMessage(), e);
        
        // 创建INTERNAL状态
        Status status = Status.INTERNAL
                .withDescription("服务器内部错误")
                .withCause(e); // 注意：cause不会序列化到客户端
        
        // 使用状态完成响应（出错）
        responseObserver.onError(status.asRuntimeException());
    }
}
```

> **通俗解释**：
> - **Status.INVALID_ARGUMENT**：表示客户端提供了无效参数，就像餐厅服务员告诉你"这个菜单项不存在"。
> - **Status.NOT_FOUND**：表示请求的资源不存在，就像餐厅服务员告诉你"我们没有这道菜"。
> - **Status.INTERNAL**：表示服务器内部错误，就像餐厅服务员告诉你"厨房出了问题，无法完成你的订单"。
> - **withDescription**：添加错误描述，提供更详细的错误信息。
> - **withCause**：添加原因异常，但这只在服务器端可见，不会发送给客户端。

### 2.3 客户端错误处理

在客户端，可以捕获`StatusRuntimeException`来处理错误：

```java
/**
 * 获取KV参数
 */
public String getParam(String key) {
    try {
        // 构建请求
        GetKVParamRequest request = GetKVParamRequest.newBuilder()
                .setKey(key)
                .build();

        // 设置超时并调用gRPC服务
        GetKVParamResponse response = blockingStub
                .withDeadlineAfter(5, TimeUnit.SECONDS)
                .getKVParam(request);

        // 获取响应中的值
        String value = response.getValue();
        logger.debug("获取参数成功: key={}, value={}", key, value);
        return value;
    } catch (StatusRuntimeException e) {
        // 获取状态
        Status status = e.getStatus();
        // 获取状态码
        Status.Code code = status.getCode();
        // 获取描述
        String description = status.getDescription();

        // 根据状态码处理不同类型的错误
        if (code == Status.Code.INVALID_ARGUMENT) {
            logger.warn("参数无效: key={}, error={}", key, description);
            return null;
        } else if (code == Status.Code.NOT_FOUND) {
            logger.info("参数不存在: key={}", key);
            return null;
        } else if (code == Status.Code.DEADLINE_EXCEEDED) {
            logger.warn("请求超时: key={}", key);
            return null;
        } else if (code == Status.Code.UNAVAILABLE) {
            logger.error("服务不可用: key={}", key);
            // 可以在这里添加重试逻辑
            return null;
        } else {
            logger.error("获取参数失败: key={}, code={}, error={}", 
                    key, code, description, e);
            return null;
        }
    } catch (Exception e) {
        logger.error("获取参数时发生未知错误: key={}, error={}", 
                key, e.getMessage(), e);
        return null;
    }
}
```

> **通俗解释**：
> - **StatusRuntimeException**：gRPC客户端收到的错误异常，包含状态码和描述。
> - **status.getCode()**：获取状态码，用于判断错误类型。
> - **status.getDescription()**：获取错误描述，提供更详细的错误信息。
> - **withDeadlineAfter**：设置请求超时时间，防止请求无限期等待。

## 专业名词总结

1. **流式RPC**：允许客户端和服务器发送多个消息的RPC调用
2. **服务器流式RPC**：客户端发送单个请求，服务器返回多个响应
3. **客户端流式RPC**：客户端发送多个请求，服务器返回单个响应
4. **双向流式RPC**：客户端和服务器都可以发送多个消息
5. **StreamObserver**：用于接收流式消息的接口
6. **Status**：表示gRPC调用结果的状态
7. **StatusRuntimeException**：包含状态信息的运行时异常
8. **Deadline**：请求的最后期限，超过后请求会被取消
9. **ServerCallStreamObserver**：服务器端的流观察者，提供额外的控制功能
10. **onNext**：发送或接收单个消息的方法
