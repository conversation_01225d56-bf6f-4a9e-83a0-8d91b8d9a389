---
title: xkongcloud-commons-uid 公共库设计方案
document_id: F004-DESIGN-001
document_type: 设计文档
category: 公共库
scope: 全局
keywords: [公共库, UID, 分布式ID, xkongcloud-commons, 持久化ID, 特征码恢复, Baidu UID, Schema命名规范, 构建器模式, 验证职责, 解耦, 并发控制, 线程安全]
created_date: 2025-05-11
updated_date: 2025-06-30
status: 草稿
version: 1.8
authors: [AI助手, Cline]
affected_features:
  - F004 # This new library itself
related_docs:
  - ../../../common/middleware/integration/postgresql-persistent-id-fingerprint-recovery.md
  - ../../../common/middleware/integration/baidu-uid-generator-postgresql-implementation.md
  - ../plan/uid-library-refactoring-plan.md
---

# `xkongcloud-commons-uid` 公共库设计方案

## 1. 目的与定位

`xkongcloud-commons-uid` 库旨在提供一套标准化的、可重用的组件，用于在 `xkongcloud` 项目的各个子服务中实现基于百度UID生成器的分布式唯一ID生成功能。该库的核心特性是支持持久化的实例身份管理、通过机器特征码进行实例身份的自动恢复，以及基于租约的Worker ID分配机制，以增强系统在动态环境（尤其是未来可能的云环境迁移）中的稳定性和可维护性。

## 2. 模块结构与命名

*   **模块名 (ArtifactID)**: `xkongcloud-commons-uid`
*   **父模块**: `xkongcloud-commons` (作为 `xkongcloud-commons` 多模块父项目下的一个子模块)
*   **包结构 (示例)**: `org.xkong.cloud.commons.uid.*`

## 3. 核心组件与职责

以下核心Java类将包含在 `xkongcloud-commons-uid` 库中：

*   **`org.xkong.cloud.commons.uid.MachineFingerprints`**:
    *   **职责**: 提供静态方法或服务，用于收集当前运行环境的各种机器特征码（如BIOS UUID, 系统序列号, MAC地址, OS主机名, 以及可扩展的云平台特有标识符如Cloud Instance ID, K8s Pod UID等）。
    *   **设计**: 应能适应不同操作系统（至少Linux, Windows），并为云环境特征码收集提供可插拔或可配置的扩展点。
*   **`org.xkong.cloud.commons.uid.KeyManagementService`**:
    *   **职责**: 管理实例ID文件加密所需的密钥。
        *   与PostgreSQL的 `infra_uid.encryption_key` 表交互，获取或创建加密密钥。
        *   提供密钥缓存机制，减少数据库访问。
        *   支持密钥版本管理，为未来的密钥轮换提供基础。
        *   提供检查加密是否启用的方法。
    *   **依赖**: `JdbcTemplate`, `TransactionTemplate`, 以及相关的配置参数（应用名、环境、加密启用状态、Schema名等）。
    *   **重要说明**: `KeyManagementService`应作为独立的Spring Bean进行管理，而不是在`PersistentInstanceManager`内部实例化。这样可以更好地遵循依赖注入原则，便于单元测试，并提高代码的可维护性和灵活性。

*   **`org.xkong.cloud.commons.uid.PersistentInstanceManagerBuilder`**:
    *   **职责**: 使用构建器模式创建`PersistentInstanceManager`实例。
        *   提供流畅的API，支持链式调用设置各种参数。
        *   在构建前验证必要参数的有效性。
        *   创建并返回配置完成的`PersistentInstanceManager`实例。
    *   **设计**: 遵循构建器设计模式，提供清晰的参数设置方法，使配置过程更加直观和灵活。

*   **`org.xkong.cloud.commons.uid.PersistentInstanceManager`**:
    *   **职责**: 管理应用实例的持久身份 (`instance_unique_id`)。
        *   处理 `instance_unique_id` 的本地文件存储与读取，支持加密和解密。
        *   与PostgreSQL的 `instance_registry` 表交互，进行新实例的注册（包含其初始特征码）。
        *   在本地 `instance_unique_id` 丢失时，调用 `MachineFingerprints` 收集当前特征码，并查询 `instance_registry` 表尝试自动恢复身份。
        *   提供获取当前实例 `instance_unique_id` 的方法。
        *   支持实例ID文件的加密存储，使用AES-256-GCM算法和外部工具包 `org.xkong.xkongkit.utils.EncryptionUtils`。
        *   **实现文件操作的并发控制**:
            *   使用文件锁（如Java的`FileLock`）确保对实例ID文件的独占访问
            *   使用临时文件和原子性重命名操作确保文件写入的原子性
            *   实现健壮的错误处理机制，包括重试逻辑和降级策略
            *   处理多进程环境下的文件访问冲突
        *   **实现实例注册的并发控制**:
            *   使用数据库事务和唯一约束确保实例注册的并发安全
            *   处理多实例同时注册的冲突情况
            *   实现特征码匹配的并发处理策略
    *   **依赖**: `JdbcTemplate`, `TransactionTemplate`, `KeyManagementService`, 以及相关的配置参数（应用名、环境、本地存储路径、恢复策略参数、加密启用状态、Schema名等）。
    *   **设计**: 推荐通过`PersistentInstanceManagerBuilder`创建实例，而不是直接使用构造函数。

*   **`org.xkong.cloud.commons.uid.PersistentInstanceWorkerIdAssigner`**:
    *   **职责**: 实现百度UID库的 `com.baidu.fsg.uid.worker.WorkerIdAssigner` 接口。
        *   使用从 `PersistentInstanceManager` 获取到的 `instance_unique_id`。
        *   与PostgreSQL的 `worker_id_assignment` 表交互，为该 `instance_unique_id` 原子性地请求、分配或续约一个具体的 `worker_id` (例如18位)。
        *   实现基于租约的 `worker_id` 管理，包括租约的获取、续约和（间接的）过期处理。
        *   **实现安全检查机制**:
            *   在每次生成ID前验证租约有效性，防止使用过期的Worker ID
            *   定期检查数据库中的Worker ID分配状态，确保没有冲突
            *   实现自我修复机制，在检测到异常时自动重新获取Worker ID
            *   记录详细的操作日志，便于问题排查
        *   **实现并发控制机制**:
            *   使用数据库事务和行锁确保Worker ID分配的原子性
            *   使用条件更新防止并发冲突
            *   实现冲突检测和处理策略
            *   处理多实例同时请求Worker ID的场景
    *   **依赖**: `JdbcTemplate`, `TransactionTemplate`, `PersistentInstanceManager`, 以及租约相关的配置参数和Schema名。

*   **`org.xkong.cloud.commons.uid.util.UidTableManager`**:
    *   **职责**: 提供表结构管理工具类，用于管理UID生成器所需的数据库表。
        *   提供静态方法用于创建、删除、验证和管理 `infra_uid.instance_registry` 和 `infra_uid.worker_id_assignment` 表。
        *   支持不同的DDL策略（create, create-drop, update, validate, none）。
        *   提供表结构检查和验证功能。
        *   提供 `infra_uid.worker_id_assignment` 表的预填充功能。
        *   **验证Schema是否存在**，而非创建Schema。根据PostgreSQL最佳实践（C024 Sec 5.1.5），Schema应由DBA或应用方通过迁移工具（如Flyway）手动管理，而非由应用程序自动创建。
    *   **设计**: 工具类设计，所有方法均为静态方法，接受 `JdbcTemplate` 和Schema名作为参数，不依赖于特定的Spring上下文。
    *   **依赖**: 仅依赖于 `JdbcTemplate` 和标准Java库。

*   **`org.xkong.cloud.commons.uid.util.UidValidationUtils`**:
    *   **职责**: 提供验证工具类，用于验证UID生成器所需的环境和配置。
        *   提供静态方法验证Schema是否存在。
        *   提供静态方法验证表是否存在。
        *   提供静态方法验证数据库连接是否可用。
    *   **设计**: 工具类设计，所有方法均为静态方法，不自动执行验证，由调用者决定何时验证。
    *   **依赖**: 仅依赖于 `JdbcTemplate` 和标准Java库。

*   **`org.xkong.cloud.commons.uid.WorkerNodeType` (枚举)**:
    *   **职责**: 定义工作节点的类型（如 `CONTAINER`, `ACTUAL`）。可用于 `instance_registry` 的 `custom_metadata` 中，或在特征码收集中辅助判断环境。

*   **`org.xkong.cloud.commons.uid.ValidationResultCache`**:
    *   **职责**: 缓存验证结果，避免重复验证。
        *   记录已验证的Schema、表和参数。
        *   提供线程安全的方法检查和标记验证状态。
    *   **设计**: 使用`ConcurrentHashMap`实现线程安全的集合，确保在多线程环境下的正确性。
    *   **依赖**: 仅依赖于标准Java库。

## 4. `xkongcloud-commons-uid` 库的依赖 (Maven/Gradle)

```xml
<!-- 核心依赖 -->
<dependency>
    <groupId>com.xfvape.uid</groupId>
    <artifactId>uid-generator</artifactId>
    <version>0.0.4-RELEASE</version> <!-- 或项目统一版本 -->
</dependency>
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-context</artifactId>
    <!-- 版本由Spring Boot或父POM管理 -->
</dependency>
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-jdbc</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-tx</artifactId>
</dependency>
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
</dependency>
<dependency>
    <groupId>org.slf4j</groupId>
    <artifactId>slf4j-api</artifactId>
</dependency>

<!-- 加密工具包依赖 -->
<dependency>
    <groupId>org.xkong</groupId>
    <artifactId>xkongkit-utils</artifactId>
    <version>1.0.0</version> <!-- 或项目统一版本 -->
</dependency>

<!-- 可选的JPA API依赖，如果库中定义了JPA实体 -->
<dependency>
    <groupId>jakarta.persistence</groupId>
    <artifactId>jakarta.persistence-api</artifactId>
    <optional>true</optional> <!-- 设为optional，使用方按需引入JPA实现 -->
</dependency>
```

## 5. 配置与集成方式：应用方手动定义Bean

`xkongcloud-commons-uid` 库不采用Spring Boot Auto-Configuration。而是：
*   库提供上述核心逻辑类。这些类设计为完全解耦于KV参数服务，通过构造函数、构建器模式或setter方法接收所有必要的依赖和配置参数。
*   使用此库的各个子项目（应用方）需要在其各自的Spring `@Configuration` 类（例如，`XkongBusinessInternalCoreUidConfig.java`）中：
    1.  显式创建 `ValidationResultCache` 的Bean，用于避免重复验证。
    2.  显式创建 `KeyManagementService` 的Bean，作为独立的Bean管理，而不是在`PersistentInstanceManager`内部实例化。
    3.  使用构建器模式创建 `PersistentInstanceManager` 的Bean，并注入 `KeyManagementService`。
    4.  显式创建 `PersistentInstanceWorkerIdAssigner` 的Bean，并注入 `PersistentInstanceManager`。
    5.  显式创建百度 `CachedUidGenerator` 的Bean，并注入 `PersistentInstanceWorkerIdAssigner`。
    6.  为这些Bean注入应用方提供的 `DataSource` (间接通过 `JdbcTemplate`/`TransactionTemplate`)。
    7.  从应用方的 `KVParamService` 或配置文件中读取所有必要的配置参数，并通过构建器模式或构造函数参数传递给这些Bean。
    8.  应用方负责基础环境验证（如数据库连接可用性、Schema存在性和必需参数完整性验证）。

## 6. 库组件所需的输入/依赖 (由应用方提供)

**`ValidationResultCache` 需要：**
*   无特殊依赖，应作为独立Bean创建

**`KeyManagementService` 需要：**
*   `JdbcTemplate`
*   `TransactionTemplate`
*   `applicationName` (String) - 通过构造函数注入，推荐使用`@Value("${xkong.kv.cluster-id}")`注入的`clusterId`变量
*   `environment` (String) - 通过构造函数注入
*   `encryptionEnabled` (boolean) - 通过构造函数注入
*   `schemaName` (String) - 通过构造函数注入，默认为"infra_uid"

**`PersistentInstanceManagerBuilder` 需要：**
*   `JdbcTemplate` - 通过`withJdbcTemplate()`方法设置
*   `TransactionTemplate` - 通过`withTransactionTemplate()`方法设置
*   `applicationName` (String) - 通过`withApplicationName()`方法设置，推荐使用`clusterId`
*   `environment` (String) - 通过`withEnvironment()`方法设置
*   `instanceGroup` (String, 可选) - 通过`withInstanceGroup()`方法设置
*   `localStoragePath` (String) - 通过`withLocalStoragePath()`方法设置，默认为".instance_id"
*   `recoveryEnabled` (boolean) - 通过`withRecoveryEnabled()`方法设置，默认为true
*   `highConfidenceThreshold` (int) - 通过`withHighConfidenceThreshold()`方法设置，默认为150
*   `minimumAcceptableScore` (int) - 通过`withMinimumAcceptableScore()`方法设置，默认为70
*   `recoveryStrategy` (String) - 通过`withRecoveryStrategy()`方法设置，默认为"ALERT_AUTO_WITH_TIMEOUT"
*   `recoveryTimeoutSeconds` (int) - 通过`withRecoveryTimeoutSeconds()`方法设置，默认为300
*   `instanceIdOverride` (Long, 可选) - 通过`withInstanceIdOverride()`方法设置
*   `encryptionEnabled` (boolean) - 通过`withEncryptionEnabled()`方法设置，默认为false
*   `schemaName` (String) - 通过`withSchemaName()`方法设置，默认为"infra_uid"
*   `KeyManagementService` (Bean引用) - 通过`withKeyManagementService()`方法设置

**`PersistentInstanceWorkerIdAssigner` 需要：**
*   `JdbcTemplate`
*   `TransactionTemplate`
*   `PersistentInstanceManager` (Bean引用)
*   `leaseDurationSeconds` (int) - 通过构造函数注入
*   `schemaName` (String) - 通过构造函数注入，默认为"infra_uid"

**`CachedUidGenerator` (百度库的类) 需要 (由应用方配置并注入到此Bean)：**
*   `WorkerIdAssigner` (即 `PersistentInstanceWorkerIdAssigner` Bean引用)
*   `epochStr` (String)
*   `timeBits` (int)
*   `workerBits` (int)
*   `seqBits` (int)
*   (可选) `boostPower` (int), `paddingFactor` (int), `scheduleInterval` (long)

**外部调用者负责的验证：**
*   **基础环境验证**：
    *   数据库连接可用性验证 - 使用`UidValidationUtils.validateDatabaseConnection()`
    *   Schema存在性验证 - 使用`UidValidationUtils.validateSchemaExists()`
    *   必需参数完整性验证 - 在获取参数时进行验证，确保必需参数不为空
*   **参数获取与验证**：
    *   从KV参数服务或其他配置源获取参数
    *   验证参数值的有效性
    *   决定使用哪个Schema和表名
*   **DDL策略执行**：
    *   根据DDL策略决定是否创建、验证或更新表结构
    *   调用UidTableManager的相应方法

## 7. 数据库Schema (DDL)

*   `xkongcloud-commons-uid` 库的文档（例如 `README.md` 或专门的数据库脚本目录）必须提供 `instance_registry` 和 `worker_id_assignment` 表的标准创建SQL脚本 (DDL)，包括索引和约束。
*   **强调**: 库本身不负责执行DDL。各应用项目在部署或通过数据库迁移工具（如Flyway, Liquibase）管理其数据库schema时，需要确保这些表已按规范创建。
*   文档中还应包含 `worker_id_assignment` 表的预填充脚本（如果采用预填充策略）。

**DDL 示例 (已在 `postgresql-persistent-id-fingerprint-recovery.md` 中详细定义):**
*   `infra_uid.instance_registry` 表结构 (包含 `instance_unique_id`, `application_name`, `environment`, `status`, `custom_metadata` 等字段)。
*   `infra_uid.worker_id_assignment` 表结构 (包含 `worker_id`, `assigned_instance_unique_id`, `assignment_status`, `lease_expires_at` 等字段)。

> **重要说明**：根据PostgreSQL最佳实践（参考`schema-planning-guide.md`），我们将UID生成器相关的表放在专门的`infra_uid` Schema中，而不是默认的"public" Schema。这符合多Schema组织方式和`infra_<组件类型>`格式的命名规范，有助于更好地组织数据，提高安全性，并为未来的扩展提供灵活性。

### 7.1 Schema管理职责

根据PostgreSQL最佳实践（参考`docs/common/middleware/postgresql/integration-guide.md`），Schema的创建和管理应由DBA或应用方通过数据库迁移工具（如Flyway、Liquibase）手动管理，而非由应用程序自动创建。

`UidTableManager.createSchema`方法已更新，不再自动创建Schema，而是验证Schema是否存在。如果Schema不存在，应用将无法启动，并显示明确的错误消息。

**生产环境建议**：
1. 使用Flyway或类似工具管理Schema和表结构
2. 将DDL脚本纳入版本控制和变更管理流程
3. 在应用启动前确保所需的Schema已由DBA创建

**DDL脚本示例**：
```sql
-- 创建infra_uid Schema
CREATE SCHEMA IF NOT EXISTS infra_uid;
```

应用方应将此脚本纳入其数据库迁移流程，确保在应用启动前执行。

## 8. JPA实体 (可选，在库中定义)

*   如果决定在 `xkongcloud-commons-uid` 中定义 `InstanceRegistryEntry` 和 `WorkerIdAssignment` 的JPA实体类：
    *   这些实体类应放在如 `org.xkong.cloud.commons.uid.entity` 包下。
    *   使用这些实体的应用项目，需要在其JPA配置中通过 `@EntityScan("org.xkong.cloud.commons.uid.entity")` 来包含这些公共实体。
    *   即使定义了JPA实体，核心的并发敏感操作（如 `worker_id` 分配）仍推荐使用 `JdbcTemplate`。JPA实体主要用于简化数据对象表示或简单的非核心查询。

## 9. 库的API与使用

*   **主要使用者**: 应用方的Spring配置类，用于组装和配置UID生成服务。
*   **最终API**: 应用的业务代码将通过 `@Autowired UidGenerator uidGenerator;` 注入并使用 `uidGenerator.getUID()`。
*   `PersistentInstanceManager` 也可以被应用方注入，如果应用需要直接访问其 `getInstanceUniqueId()` 方法（尽管通常这被封装在 `WorkerIdAssigner` 内部）。
*   `MachineFingerprints.collectFingerprints()` 是一个静态工具方法，可按需调用。

## 10. 应用方配置示例 (`UidGeneratorConfigApplicationSpecific.java`)

```java
package org.xkong.cloud.business.internal.core.config; // 应用方的包

import com.baidu.fsg.uid.UidGenerator;
import com.baidu.fsg.uid.impl.CachedUidGenerator;
import com.baidu.fsg.uid.worker.WorkerIdAssigner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.support.TransactionTemplate;
import org.xkong.cloud.commons.uid.KeyManagementService;     // 来自公共库
import org.xkong.cloud.commons.uid.PersistentInstanceManager;     // 来自公共库
import org.xkong.cloud.commons.uid.PersistentInstanceManagerBuilder;     // 来自公共库
import org.xkong.cloud.commons.uid.PersistentInstanceWorkerIdAssigner; // 来自公共库
import org.xkong.cloud.commons.uid.ValidationResultCache; // 来自公共库
import org.xkong.cloud.commons.uid.util.UidTableManager;          // 来自公共库
import org.xkong.cloud.commons.uid.util.UidValidationUtils;          // 来自公共库
import org.xkong.cloud.business.internal.core.service.KVParamService;   // 应用方的KV服务

import javax.annotation.PostConstruct;

@Configuration
@EnableScheduling // 如果 PersistentInstanceWorkerIdAssigner 中的续约任务使用 @Scheduled
@DependsOn("kvParamService")
public class UidGeneratorConfigApplicationSpecific {

    private static final Logger logger = LoggerFactory.getLogger(UidGeneratorConfigApplicationSpecific.class);

    @Autowired
    private KVParamService kvParamService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Value("${xkong.kv.cluster-id}")
    private String clusterId;

    /**
     * 创建线程安全的验证结果缓存
     */
    @Bean
    public ValidationResultCache validationResultCache() {
        return new ValidationResultCache();
    }

    /**
     * 初始化UID生成器所需的数据库表
     * 根据postgresql.ddl-auto参数决定表结构管理策略
     */
    @PostConstruct
    public void initUidTables() {
        // 验证数据库连接
        UidValidationUtils.validateDatabaseConnection(jdbcTemplate);

        // 从KVParamService获取DDL策略
        String ddlAuto = kvParamService.getParam("postgresql.ddl-auto");
        if (ddlAuto == null || ddlAuto.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'postgresql.ddl-auto'未配置");
        }
        logger.info("初始化UID生成器相关表，当前DDL模式: {}", ddlAuto);

        // 获取worker_id位数，用于填充worker_id_assignment表
        String workerBitsStr = kvParamService.getParam("uid.workerBits");
        if (workerBitsStr == null || workerBitsStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.workerBits'未配置");
        }
        int workerBits = Integer.parseInt(workerBitsStr);

        // 获取Schema名称
        String schemaName = kvParamService.getParam("postgresql.uid.schema");
        if (schemaName == null || schemaName.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'postgresql.uid.schema'未配置");
        }
        logger.info("使用Schema: {}", schemaName);

        // 首先验证Schema是否存在
        UidValidationUtils.validateSchemaExists(jdbcTemplate, schemaName);

        // 根据DDL策略调用UidTableManager的相应方法
        switch (ddlAuto.toLowerCase()) {
            case "create":
                // 先删除再创建表
                UidTableManager.dropTables(jdbcTemplate, schemaName);
                UidTableManager.createTables(jdbcTemplate, schemaName);
                UidTableManager.fillWorkerIdAssignments(jdbcTemplate, workerBits, schemaName);
                break;
            case "create-drop":
                // 应用启动时创建表，应用关闭时删除表（需要另外配置关闭钩子）
                UidTableManager.dropTables(jdbcTemplate, schemaName);
                UidTableManager.createTables(jdbcTemplate, schemaName);
                UidTableManager.fillWorkerIdAssignments(jdbcTemplate, workerBits, schemaName);
                // 注册关闭钩子在应用关闭时删除表
                registerShutdownHook(schemaName);
                break;
            case "update":
                // 如果表不存在则创建
                UidTableManager.createTablesIfNotExist(jdbcTemplate, schemaName);
                break;
            case "validate":
                // 验证表结构
                UidTableManager.validateTables(jdbcTemplate, schemaName);
                break;
            case "none":
                // 不执行任何操作
                logger.info("DDL模式为none，不执行任何表操作");
                break;
            default:
                logger.warn("未知的DDL模式: {}", ddlAuto);
        }
    }

    /**
     * 注册应用关闭钩子，在应用关闭时删除表（仅在create-drop模式下使用）
     */
    private void registerShutdownHook(String schemaName) {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            String ddlAuto = kvParamService.getParam("postgresql.ddl-auto");
            if ("create-drop".equalsIgnoreCase(ddlAuto)) {
                logger.info("应用关闭，DDL模式为create-drop，删除UID生成器相关表");
                UidTableManager.dropTables(jdbcTemplate, schemaName);
            }
        }));
    }

    /**
     * 创建密钥管理服务
     * 作为独立Bean管理，而不是在PersistentInstanceManager内部实例化
     */
    @Bean
    public KeyManagementService keyManagementService() {
        // 获取必需参数（无默认值，缺少时应用无法启动）
        String schemaName = kvParamService.getParam("postgresql.uid.schema");
        if (schemaName == null || schemaName.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'postgresql.uid.schema'未配置");
        }

        String environment = kvParamService.getParam("uid.instance.environment");
        if (environment == null || environment.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.environment'未配置");
        }

        String encryptionEnabledStr = kvParamService.getParam("uid.instance.encryption.enabled");
        if (encryptionEnabledStr == null || encryptionEnabledStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.encryption.enabled'未配置");
        }
        boolean encryptionEnabled = Boolean.parseBoolean(encryptionEnabledStr);

        // 创建KeyManagementService实例
        return new KeyManagementService(
            jdbcTemplate,
            transactionTemplate,
            clusterId, // 使用@Value("${xkong.kv.cluster-id}")注入的clusterId变量
            environment,
            encryptionEnabled,
            schemaName
        );
    }

    /**
     * 创建持久化实例管理器
     * 使用构建器模式创建PersistentInstanceManager
     */
    @Bean
    public PersistentInstanceManager persistentInstanceManager(
            ValidationResultCache validationResultCache,
            KeyManagementService keyManagementService) {
        // 获取必需参数（无默认值，缺少时应用无法启动）
        String schemaName = kvParamService.getParam("postgresql.uid.schema");
        if (schemaName == null || schemaName.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'postgresql.uid.schema'未配置");
        }

        String environment = kvParamService.getParam("uid.instance.environment");
        if (environment == null || environment.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.environment'未配置");
        }

        String instanceGroup = kvParamService.getParam("uid.instance.group");
        if (instanceGroup == null || instanceGroup.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.group'未配置");
        }

        String localStoragePath = kvParamService.getParam("uid.instance.local-storage-path");
        if (localStoragePath == null || localStoragePath.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.local-storage-path'未配置");
        }

        String recoveryEnabledStr = kvParamService.getParam("uid.instance.recovery.enabled");
        if (recoveryEnabledStr == null || recoveryEnabledStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.recovery.enabled'未配置");
        }
        boolean recoveryEnabled = Boolean.parseBoolean(recoveryEnabledStr);

        String highConfThresholdStr = kvParamService.getParam("uid.instance.recovery.high-confidence-threshold");
        if (highConfThresholdStr == null || highConfThresholdStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.recovery.high-confidence-threshold'未配置");
        }
        int highConfThreshold = Integer.parseInt(highConfThresholdStr);

        String minAcceptScoreStr = kvParamService.getParam("uid.instance.recovery.minimum-acceptable-score");
        if (minAcceptScoreStr == null || minAcceptScoreStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.recovery.minimum-acceptable-score'未配置");
        }
        int minAcceptScore = Integer.parseInt(minAcceptScoreStr);

        String recoveryStrategy = kvParamService.getParam("uid.instance.recovery.strategy");
        if (recoveryStrategy == null || recoveryStrategy.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.recovery.strategy'未配置");
        }

        String recoveryTimeoutSecondsStr = kvParamService.getParam("uid.instance.recovery.timeout-seconds");
        if (recoveryTimeoutSecondsStr == null || recoveryTimeoutSecondsStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.recovery.timeout-seconds'未配置");
        }
        int recoveryTimeoutSeconds = Integer.parseInt(recoveryTimeoutSecondsStr);

        String encryptionEnabledStr = kvParamService.getParam("uid.instance.encryption.enabled");
        if (encryptionEnabledStr == null || encryptionEnabledStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.encryption.enabled'未配置");
        }
        boolean encryptionEnabled = Boolean.parseBoolean(encryptionEnabledStr);

        // 检查Schema是否已验证
        if (!validationResultCache.isSchemaValidated(schemaName)) {
            // 验证Schema
            UidValidationUtils.validateSchemaExists(jdbcTemplate, schemaName);
            validationResultCache.markSchemaValidated(schemaName);
        }

        // 使用构建器模式创建PersistentInstanceManager
        return new PersistentInstanceManagerBuilder()
            .withJdbcTemplate(jdbcTemplate)
            .withTransactionTemplate(transactionTemplate)
            .withApplicationName(clusterId)
            .withEnvironment(environment)
            .withInstanceGroup(instanceGroup)
            .withLocalStoragePath(localStoragePath)
            .withRecoveryEnabled(recoveryEnabled)
            .withHighConfidenceThreshold(highConfThreshold)
            .withMinimumAcceptableScore(minAcceptScore)
            .withRecoveryStrategy(recoveryStrategy)
            .withRecoveryTimeoutSeconds(recoveryTimeoutSeconds)
            .withEncryptionEnabled(encryptionEnabled)
            .withSchemaName(schemaName)
            .withKeyManagementService(keyManagementService)
            .build();
    }

    /**
     * 创建Worker ID分配器
     */
    @Bean
    public WorkerIdAssigner workerIdAssigner(
            PersistentInstanceManager persistentInstanceManager) {
        // 获取必需参数（无默认值，缺少时应用无法启动）
        String schemaName = kvParamService.getParam("postgresql.uid.schema");
        if (schemaName == null || schemaName.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'postgresql.uid.schema'未配置");
        }

        String leaseDurationStr = kvParamService.getParam("uid.worker.lease-duration-seconds");
        if (leaseDurationStr == null || leaseDurationStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.worker.lease-duration-seconds'未配置");
        }
        int leaseDuration = Integer.parseInt(leaseDurationStr);

        // 创建PersistentInstanceWorkerIdAssigner实例
        return new PersistentInstanceWorkerIdAssigner(
            jdbcTemplate,
            transactionTemplate,
            persistentInstanceManager,
            leaseDuration,
            schemaName
        );
    }

    /**
     * 创建UID生成器
     */
    @Bean
    public UidGenerator uidGenerator(WorkerIdAssigner workerIdAssigner) {
        // 获取必需参数（无默认值，缺少时应用无法启动）
        String epochStr = kvParamService.getParam("uid.epochStr");
        if (epochStr == null || epochStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.epochStr'未配置");
        }

        String timeBitsStr = kvParamService.getParam("uid.timeBits");
        if (timeBitsStr == null || timeBitsStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.timeBits'未配置");
        }
        int timeBits = Integer.parseInt(timeBitsStr);

        String workerBitsStr = kvParamService.getParam("uid.workerBits");
        if (workerBitsStr == null || workerBitsStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.workerBits'未配置");
        }
        int workerBits = Integer.parseInt(workerBitsStr);

        String seqBitsStr = kvParamService.getParam("uid.seqBits");
        if (seqBitsStr == null || seqBitsStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.seqBits'未配置");
        }
        int seqBits = Integer.parseInt(seqBitsStr);

        // 创建CachedUidGenerator实例
        CachedUidGenerator cachedUidGenerator = new CachedUidGenerator();
        cachedUidGenerator.setWorkerIdAssigner(workerIdAssigner);
        cachedUidGenerator.setEpochStr(epochStr);
        cachedUidGenerator.setTimeBits(timeBits);
        cachedUidGenerator.setWorkerBits(workerBits);
        cachedUidGenerator.setSeqBits(seqBits);

        // 设置必需参数（无默认值，缺少时应用无法启动）
        String boostPowerStr = kvParamService.getParam("uid.boostPower");
        if (boostPowerStr == null || boostPowerStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.boostPower'未配置");
        }
        cachedUidGenerator.setBoostPower(Integer.parseInt(boostPowerStr));

        String paddingFactorStr = kvParamService.getParam("uid.paddingFactor");
        if (paddingFactorStr == null || paddingFactorStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.paddingFactor'未配置");
        }
        cachedUidGenerator.setPaddingFactor(Integer.parseInt(paddingFactorStr));

        String scheduleIntervalStr = kvParamService.getParam("uid.scheduleInterval");
        if (scheduleIntervalStr == null || scheduleIntervalStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.scheduleInterval'未配置");
        }
        cachedUidGenerator.setScheduleInterval(Long.parseLong(scheduleIntervalStr));

        logger.info("百度UID生成器 (CachedUidGenerator) 配置完成。");
        return cachedUidGenerator;
    }
}
```

## 11. `xkongcloud-commons-uid` 库的测试策略

*   **单元测试**:
    *   对 `MachineFingerprints` 进行单元测试，mock系统调用和环境变量，验证不同平台特征码的收集逻辑。
    *   对 `PersistentInstanceManager` 进行单元测试，mock `JdbcTemplate`, `TransactionTemplate`, 文件IO，测试ID加载、恢复（不同匹配场景）、注册逻辑。
    *   对 `PersistentInstanceWorkerIdAssigner` 进行单元测试，mock `JdbcTemplate`, `TransactionTemplate`, `PersistentInstanceManager`，测试 `worker_id` 分配、续约、并发控制（通过验证SQL和参数）。
    *   对 `UidTableManager` 进行单元测试，mock `JdbcTemplate`，测试表创建、删除、验证等功能，以及不同DDL策略下的行为。
*   **集成测试 (在库的测试范围, 可选)**: 如果库的测试环境能方便地连接到一个测试PostgreSQL实例，可以编写集成测试来验证数据库交互的正确性。

## 12. `xkongcloud-commons-uid` 库的文档

库应提供以下文档：
*   `README.md`：介绍库的用途、特性、如何集成（Maven/Gradle依赖）、应用方如何配置Bean。
*   API文档 (JavaDoc)。
*   必需的数据库表DDL脚本 (`instance_registry`, `worker_id_assignment`) 及预填充脚本。
*   所有可配置参数的详细说明及其默认值（如果有），以及这些参数如何通过应用方的配置（如KVParamService）传递给库组件。
*   特征码收集的说明（支持哪些，如何扩展）。
*   表结构管理工具类的使用说明，包括不同DDL策略的支持和示例代码。
*   故障排除指南。

---

## 变更历史

| 版本 | 日期       | 变更内容 | 变更人      |
|------|------------|----------|-------------|
| 1.8  | 2025-06-30 | 修改UidTableManager.createSchema方法，去掉自动创建Schema的功能，符合PostgreSQL标准要求Schema应由DBA手动管理 | AI助手 |
| 1.7  | 2025-06-25 | 增强并发安全性：添加Worker ID分配、租约续约、实例身份恢复和本地文件操作的并发控制详细说明 | AI助手 |
| 1.6  | 2025-06-20 | 重构设计：添加构建器模式、明确验证职责、完全解耦KV参数服务、添加ValidationResultCache和UidValidationUtils | AI助手 |
| 1.5  | 2025-06-13 | 添加ALERT_AUTO_WITH_TIMEOUT恢复策略和超时参数 | AI助手 |
| 1.4  | 2025-06-09 | 添加实例ID文件加密存储功能，增加KeyManagementService类，更新PersistentInstanceManager | AI助手 |
| 1.3  | 2025-06-05 | 更新Schema命名规范：将uid_generator改为infra_uid，符合infra_<组件类型>格式 | AI助手 |
| 1.2  | 2025-06-02 | 修改UidTableManager描述和应用方配置示例，明确指定使用专门的Schema | AI助手 |
| 1.1  | 2025-05-27 | 修改方案，使用已有的`xkong.kv.cluster-id`变量作为`application_name` | AI助手 |
| 1.0  | 2025-05-11 | 初始草稿   | AI助手, Cline |

---

这份详细方案概述了 `xkongcloud-commons-uid` 公共库的设计要点、集成方式和相关考虑。请您审查。
