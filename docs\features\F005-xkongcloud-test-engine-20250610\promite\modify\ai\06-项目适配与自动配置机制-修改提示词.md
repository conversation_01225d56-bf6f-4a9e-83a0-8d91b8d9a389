# 项目适配与自动配置机制修改提示词

**目标文件**: `06-项目适配与自动配置机制.md`  
**修改原则**: 将"智能自动配置"重新设计为"规则化项目分析 + 外部AI服务增强"  
**核心理念**: 明确项目适配的规则化处理和AI服务的辅助作用

---

## 🎯 项目适配机制重新定位

### 自动配置机制重新定义
```pseudocode
// 修改前：混淆的"智能自动配置"
❌ 基于AI的智能项目分析和自动配置生成
❌ 智能项目类型识别和能力激活

// 修改后：明确的"规则化项目适配"
✅ 基于规则的项目分析和配置生成
✅ 规则化项目类型识别 + 外部AI服务增强

DEFINE ProjectAdaptationPhilosophy:
    // 核心职责划分
    规则引擎职责:
        - 项目结构规则分析
        - 依赖关系规则识别
        - 标准配置模板生成
        
    外部AI服务职责:
        - 复杂项目架构分析
        - 自定义配置优化建议
        - 跨项目最佳实践推荐
        
    人工决策职责:
        - 项目适配策略制定
        - 复杂配置决策确认
        - 项目特殊需求定义
END DEFINE
```

## 🔧 项目类型识别器重新设计

### 规则化项目分析引擎
```pseudocode
COMPONENT RuleBasedProjectAnalyzer:
    DEPENDENCIES:
        projectStructureAnalyzer: ProjectStructureAnalyzer
        dependencyRuleAnalyzer: DependencyRuleAnalyzer
        annotationRuleScanner: AnnotationRuleScanner
        configurationRuleDetector: ConfigurationRuleDetector
        externalAIClient: ExternalAIServiceClient
    
    FUNCTION analyzeProject(projectPath):
        // 1. 规则化项目结构分析
        structureAnalysis = projectStructureAnalyzer.analyze(projectPath)
        
        // 2. 规则化依赖关系分析
        dependencyAnalysis = dependencyRuleAnalyzer.analyzeDependencies(
            structureAnalysis.buildFiles)
        
        // 3. 规则化注解扫描
        annotationAnalysis = annotationRuleScanner.scanAnnotations(
            structureAnalysis.sourceFiles)
        
        // 4. 规则化配置检测
        configurationAnalysis = configurationRuleDetector.detectConfigurations(
            structureAnalysis.configFiles)
        
        // 5. 规则化项目类型推断
        projectTypeInference = inferProjectType(
            structureAnalysis, dependencyAnalysis, annotationAnalysis, configurationAnalysis)
        
        // 6. 复杂项目的AI增强分析（可选）
        aiProjectAnalysis = NULL
        IF projectTypeInference.complexity > PROJECT_COMPLEXITY_THRESHOLD:
            aiRequest = buildProjectAnalysisRequest(
                structureAnalysis, dependencyAnalysis, annotationAnalysis, configurationAnalysis)
            aiProjectAnalysis = externalAIClient.enhanceProjectAnalysis(aiRequest)
        
        RETURN ProjectAnalysisResult(
            structureAnalysis: structureAnalysis,
            dependencyAnalysis: dependencyAnalysis,
            annotationAnalysis: annotationAnalysis,
            configurationAnalysis: configurationAnalysis,
            projectTypeInference: projectTypeInference,
            aiProjectAnalysis: aiProjectAnalysis
        )
    END FUNCTION
    
    FUNCTION inferProjectType(structureAnalysis, dependencyAnalysis, annotationAnalysis, configurationAnalysis):
        // 基于规则的项目类型推断
        projectTypeScores = Map()
        
        // 微服务项目规则检查
        microserviceScore = calculateMicroserviceScore(
            dependencyAnalysis.hasSpringBoot,
            annotationAnalysis.hasRestController,
            configurationAnalysis.hasApplicationYml,
            structureAnalysis.hasDockerfile
        )
        projectTypeScores.put(ProjectType.MICROSERVICE, microserviceScore)
        
        // 库项目规则检查
        libraryScore = calculateLibraryScore(
            structureAnalysis.hasLibraryStructure,
            dependencyAnalysis.hasMinimalDependencies,
            configurationAnalysis.hasLibraryConfig
        )
        projectTypeScores.put(ProjectType.LIBRARY, libraryScore)
        
        // 工具项目规则检查
        utilityScore = calculateUtilityScore(
            structureAnalysis.hasUtilityStructure,
            dependencyAnalysis.hasUtilityDependencies,
            annotationAnalysis.hasUtilityAnnotations
        )
        projectTypeScores.put(ProjectType.UTILITY, utilityScore)
        
        // 选择最高分的项目类型
        primaryProjectType = projectTypeScores.getMaxScoreEntry()
        
        RETURN ProjectTypeInference(
            primaryType: primaryProjectType.key,
            confidence: primaryProjectType.value,
            allScores: projectTypeScores,
            complexity: calculateProjectComplexity(structureAnalysis, dependencyAnalysis)
        )
    END FUNCTION
END COMPONENT
```

### 项目结构规则分析器
```pseudocode
COMPONENT ProjectStructureAnalyzer:
    DEPENDENCIES:
        fileSystemScanner: FileSystemScanner
        structureRuleRepository: StructureRuleRepository
        pathPatternMatcher: PathPatternMatcher
    
    FUNCTION analyze(projectPath):
        // 1. 文件系统扫描
        fileSystemStructure = fileSystemScanner.scanDirectory(projectPath)
        
        // 2. 规则化结构模式识别
        structurePatterns = identifyStructurePatterns(fileSystemStructure)
        
        // 3. 规则化文件类型分类
        fileClassification = classifyFiles(fileSystemStructure)
        
        RETURN ProjectStructureAnalysis(
            projectPath: projectPath,
            fileSystemStructure: fileSystemStructure,
            structurePatterns: structurePatterns,
            fileClassification: fileClassification,
            sourceFiles: fileClassification.sourceFiles,
            buildFiles: fileClassification.buildFiles,
            configFiles: fileClassification.configFiles,
            hasDockerfile: fileClassification.hasDockerfile,
            hasLibraryStructure: structurePatterns.hasLibraryPattern,
            hasUtilityStructure: structurePatterns.hasUtilityPattern
        )
    END FUNCTION
    
    FUNCTION identifyStructurePatterns(fileSystemStructure):
        patterns = StructurePatterns()
        
        // Maven项目结构规则
        IF fileSystemStructure.hasPath("src/main/java") AND 
           fileSystemStructure.hasFile("pom.xml"):
            patterns.hasMavenPattern = TRUE
        
        // Spring Boot项目结构规则
        IF fileSystemStructure.hasPath("src/main/resources") AND
           fileSystemStructure.hasFile("src/main/resources/application.yml"):
            patterns.hasSpringBootPattern = TRUE
        
        // 库项目结构规则
        IF fileSystemStructure.hasPath("src/main/java") AND
           NOT fileSystemStructure.hasPath("src/main/resources/static"):
            patterns.hasLibraryPattern = TRUE
        
        // 工具项目结构规则
        IF fileSystemStructure.hasPath("src/main/java") AND
           fileSystemStructure.directoryCount < 5:
            patterns.hasUtilityPattern = TRUE
        
        RETURN patterns
    END FUNCTION
END COMPONENT
```

## 🔧 配置生成器重新设计

### 规则化配置模板引擎
```pseudocode
COMPONENT RuleBasedConfigurationGenerator:
    DEPENDENCIES:
        configTemplateRepository: ConfigurationTemplateRepository
        ruleBasedParameterizer: RuleBasedParameterizer
        configurationValidator: ConfigurationValidator
        externalAIClient: ExternalAIServiceClient
    
    FUNCTION generateConfiguration(projectAnalysis, engineCapabilities):
        // 1. 规则化模板选择
        configTemplate = selectConfigurationTemplate(
            projectAnalysis.projectTypeInference.primaryType,
            engineCapabilities
        )
        
        // 2. 规则化参数化处理
        parameterizedConfig = ruleBasedParameterizer.parameterize(
            configTemplate, projectAnalysis)
        
        // 3. 规则化配置验证
        validationResult = configurationValidator.validate(
            parameterizedConfig, projectAnalysis)
        
        IF NOT validationResult.isValid():
            THROW ConfigurationValidationException(validationResult.errors)
        
        // 4. 复杂配置的AI优化（可选）
        aiConfigOptimization = NULL
        IF projectAnalysis.projectTypeInference.complexity > CONFIG_COMPLEXITY_THRESHOLD:
            aiRequest = buildConfigOptimizationRequest(
                parameterizedConfig, projectAnalysis, engineCapabilities)
            aiConfigOptimization = externalAIClient.optimizeConfiguration(aiRequest)
        
        RETURN ConfigurationGenerationResult(
            baseConfiguration: parameterizedConfig,
            validationResult: validationResult,
            aiConfigOptimization: aiConfigOptimization,
            generationMetadata: createGenerationMetadata(configTemplate, projectAnalysis)
        )
    END FUNCTION
    
    FUNCTION selectConfigurationTemplate(projectType, engineCapabilities):
        // 基于项目类型和引擎能力的规则化模板选择
        templateSelectionRules = configTemplateRepository.getSelectionRules(projectType)
        
        applicableTemplates = []
        FOR rule IN templateSelectionRules:
            IF rule.matches(projectType, engineCapabilities):
                template = configTemplateRepository.getTemplate(rule.templateId)
                applicableTemplates.add(template)
        
        // 选择最匹配的模板
        IF applicableTemplates.isEmpty():
            RETURN configTemplateRepository.getDefaultTemplate(projectType)
        ELSE:
            RETURN selectBestMatchingTemplate(applicableTemplates, engineCapabilities)
    END FUNCTION
    
    FUNCTION selectBestMatchingTemplate(templates, engineCapabilities):
        bestTemplate = NULL
        highestScore = 0
        
        FOR template IN templates:
            matchScore = calculateTemplateMatchScore(template, engineCapabilities)
            IF matchScore > highestScore:
                highestScore = matchScore
                bestTemplate = template
        
        RETURN bestTemplate
    END FUNCTION
END COMPONENT
```

### 规则化参数化处理器
```pseudocode
COMPONENT RuleBasedParameterizer:
    DEPENDENCIES:
        parameterRuleRepository: ParameterRuleRepository
        valueExtractor: ProjectValueExtractor
        parameterValidator: ParameterValidator
    
    FUNCTION parameterize(configTemplate, projectAnalysis):
        parameterizedConfig = configTemplate.copy()
        
        // 1. 提取项目特定值
        projectValues = valueExtractor.extractValues(projectAnalysis)
        
        // 2. 规则化参数替换
        parameterRules = parameterRuleRepository.getRulesForTemplate(configTemplate.templateId)
        
        FOR rule IN parameterRules:
            IF rule.condition.matches(projectAnalysis):
                parameterValue = rule.valueProvider.getValue(projectValues)
                
                // 参数验证
                validationResult = parameterValidator.validate(
                    rule.parameterName, parameterValue, rule.constraints)
                
                IF validationResult.isValid():
                    parameterizedConfig.setParameter(rule.parameterName, parameterValue)
                ELSE:
                    THROW ParameterizationException(
                        "参数验证失败: " + rule.parameterName + " - " + validationResult.error)
        
        RETURN parameterizedConfig
    END FUNCTION
END COMPONENT
```

## 🔧 能力激活器重新设计

### 规则化引擎能力激活
```pseudocode
COMPONENT RuleBasedCapabilityActivator:
    DEPENDENCIES:
        capabilityRuleRepository: CapabilityRuleRepository
        dependencyAnalyzer: ProjectDependencyAnalyzer
        featureDetector: ProjectFeatureDetector
        externalAIClient: ExternalAIServiceClient
    
    FUNCTION activateCapabilities(projectAnalysis, availableCapabilities):
        // 1. 规则化能力需求分析
        capabilityRequirements = analyzeCapabilityRequirements(projectAnalysis)
        
        // 2. 规则化能力匹配
        activatedCapabilities = []
        FOR requirement IN capabilityRequirements:
            matchingCapabilities = findMatchingCapabilities(
                requirement, availableCapabilities)
            
            FOR capability IN matchingCapabilities:
                activationRule = capabilityRuleRepository.getActivationRule(
                    capability, projectAnalysis.projectTypeInference.primaryType)
                
                IF activationRule.shouldActivate(projectAnalysis):
                    activatedCapabilities.add(capability)
        
        // 3. 规则化能力冲突检测
        conflictDetectionResult = detectCapabilityConflicts(activatedCapabilities)
        
        IF conflictDetectionResult.hasConflicts():
            resolvedCapabilities = resolveCapabilityConflicts(
                activatedCapabilities, conflictDetectionResult.conflicts)
        ELSE:
            resolvedCapabilities = activatedCapabilities
        
        // 4. 复杂能力组合的AI优化（可选）
        aiCapabilityOptimization = NULL
        IF resolvedCapabilities.size() > CAPABILITY_COMPLEXITY_THRESHOLD:
            aiRequest = buildCapabilityOptimizationRequest(
                resolvedCapabilities, projectAnalysis, conflictDetectionResult)
            aiCapabilityOptimization = externalAIClient.optimizeCapabilityActivation(aiRequest)
        
        RETURN CapabilityActivationResult(
            activatedCapabilities: resolvedCapabilities,
            capabilityRequirements: capabilityRequirements,
            conflictDetectionResult: conflictDetectionResult,
            aiCapabilityOptimization: aiCapabilityOptimization
        )
    END FUNCTION
    
    FUNCTION analyzeCapabilityRequirements(projectAnalysis):
        requirements = []
        
        // 基于项目依赖的能力需求分析
        IF projectAnalysis.dependencyAnalysis.hasSpringDataJPA:
            requirements.add(CapabilityRequirement.PERSISTENCE_RECONSTRUCTION)
        
        IF projectAnalysis.dependencyAnalysis.hasSpringCloud:
            requirements.add(CapabilityRequirement.SERVICE_PARAMETRIC_EXECUTION)
        
        IF projectAnalysis.annotationAnalysis.hasGRPCAnnotations:
            requirements.add(CapabilityRequirement.INTERFACE_ADAPTIVE_TESTING)
        
        IF projectAnalysis.configurationAnalysis.hasKVConfiguration:
            requirements.add(CapabilityRequirement.KV_PARAMETER_SIMULATION)
        
        // 基于项目特征的能力需求分析
        IF projectAnalysis.structureAnalysis.hasComplexDataStructures:
            requirements.add(CapabilityRequirement.DATA_CONSISTENCY_VERIFICATION)
        
        RETURN requirements
    END FUNCTION
END COMPONENT
```

## 🔧 环境适配策略重新设计

### 规则化环境策略选择
```pseudocode
COMPONENT RuleBasedEnvironmentAdapter:
    DEPENDENCIES:
        environmentRuleRepository: EnvironmentRuleRepository
        projectComplexityAnalyzer: ProjectComplexityAnalyzer
        resourceRequirementCalculator: ResourceRequirementCalculator
        externalAIClient: ExternalAIServiceClient
    
    FUNCTION adaptEnvironmentStrategy(projectAnalysis, targetEnvironment):
        // 1. 规则化项目复杂度评估
        complexityAssessment = projectComplexityAnalyzer.assess(projectAnalysis)
        
        // 2. 规则化资源需求计算
        resourceRequirements = resourceRequirementCalculator.calculate(
            projectAnalysis, complexityAssessment)
        
        // 3. 规则化环境策略选择
        environmentStrategy = selectEnvironmentStrategy(
            targetEnvironment, complexityAssessment, resourceRequirements)
        
        // 4. 复杂环境需求的AI优化（可选）
        aiEnvironmentOptimization = NULL
        IF complexityAssessment.overallComplexity > ENVIRONMENT_COMPLEXITY_THRESHOLD:
            aiRequest = buildEnvironmentOptimizationRequest(
                projectAnalysis, environmentStrategy, resourceRequirements)
            aiEnvironmentOptimization = externalAIClient.optimizeEnvironmentStrategy(aiRequest)
        
        RETURN EnvironmentAdaptationResult(
            environmentStrategy: environmentStrategy,
            complexityAssessment: complexityAssessment,
            resourceRequirements: resourceRequirements,
            aiEnvironmentOptimization: aiEnvironmentOptimization
        )
    END FUNCTION
    
    FUNCTION selectEnvironmentStrategy(targetEnvironment, complexityAssessment, resourceRequirements):
        strategyRules = environmentRuleRepository.getStrategyRules(targetEnvironment)
        
        FOR rule IN strategyRules:
            IF rule.matches(complexityAssessment, resourceRequirements):
                RETURN rule.createStrategy(complexityAssessment, resourceRequirements)
        
        // 默认策略
        RETURN createDefaultEnvironmentStrategy(targetEnvironment, complexityAssessment)
    END FUNCTION
END COMPONENT
```

## 📋 修改检查清单

### 必须删除的混淆概念
- [ ] 删除所有"智能项目分析"表述
- [ ] 删除所有"AI自动配置生成"声明
- [ ] 删除所有"智能能力激活"描述
- [ ] 删除所有项目适配的AI能力声明

### 必须添加的明确组件
- [ ] RuleBasedProjectAnalyzer规则化项目分析器
- [ ] RuleBasedConfigurationGenerator规则化配置生成器
- [ ] RuleBasedCapabilityActivator规则化能力激活器
- [ ] ExternalAIServiceClient外部AI服务客户端
- [ ] RuleBasedEnvironmentAdapter规则化环境适配器

### 必须明确的职责边界
- [ ] 规则引擎：标准项目分析和配置生成
- [ ] 外部AI服务：复杂项目架构分析和配置优化
- [ ] 人工决策：项目适配策略制定和复杂配置确认
- [ ] 环境感知：基于项目特征的环境策略选择

这个修改提示词确保了项目适配与自动配置机制的正确设计，明确区分了规则处理与AI服务的职责边界。
