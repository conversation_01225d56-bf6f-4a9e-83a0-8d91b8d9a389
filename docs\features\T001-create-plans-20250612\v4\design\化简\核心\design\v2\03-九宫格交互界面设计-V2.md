# V4.2方案：九宫格交互界面设计 (虚拟项目经理版)

## 1. 文档信息

- **文档版本**: V2.0
- **创建日期**: 2025-07-31
- **核心思想**: **为V4.2的异步任务框架设计一个简洁、高效、响应及时的交互界面。**
- **设计目标**: 设计一个以动态弹窗和实时日志流为核心的交互界面，通过HTTP轮询机制，准确反映后端三段式异步任务（创建、监控、执行）的完整生命周期。

## 2. 核心设计原则

- **架构映射**: 界面的交互流程必须严格反映后端的**三段式API** (`/get_and_create`, `/status/{id}`, `/start-review/{id}`) 和**HTTP轮询**状态同步模型。
- **实时透明**: 必须通过**HTTP轮询**技术，准确、及时地展示后端任务的执行状态和日志。
- **项目经理角色**: 界面应体现虚拟项目经理的身份，使用项目经理的工作语言和思维模式。
- **深度可追溯**: 用户应能从宏观的流程概览，下钻到最微观的"原子约束"的详细信息。
- **工作流邻近性 (Workflow Proximity)**: 核心交互区域（区域8：用户输入）与其最直接、最重要的产出区域（区域7：知识库、区域9：交付结果）在物理布局上保持邻近，符合用户的操作直觉，形成“输入->处理->输出”的清晰空间流线。

## 3. V4.2 交互模型：三段式异步任务流

V4.2的界面交互模型放弃了复杂的九宫格布局，转而采用一个更简洁、更聚焦于核心工作流的动态交互模式。

### 3.1. 核心交互流程

交互流程严格遵循后端的**三段式API**模型，形成一个清晰的用户操作闭环。

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 前端界面
    participant Server as 后端服务

    User->>UI: 1. 点击“打开项目”按钮
    UI->>UI: 2. 弹出动态输入框
    User->>UI: 3. 输入项目路径并点击“确定”
    
    UI->>Server: 4. POST /api/pm_v2/get_and_create
    Server-->>UI: 5. 返回 { manager_id: "xyz" }
    
    UI->>UI: 6. 页面跳转至 /pm_v2/{manager_id}
    UI->>Server: 7. (启动轮询) GET /api/pm_v2/status/{id}
    Server-->>UI: 8. 返回 { status: "initialized", ... }
    
    loop 状态轮询 (每2秒)
        UI->>Server: GET /api/pm_v2/status/{id}
        Server-->>UI: 返回最新状态
        UI->>UI: 更新状态显示
    end

    User->>UI: 9. 点击“开始审查”按钮
    UI->>Server: 10. POST /api/pm_v2/start-review/{id}
    Server-->>UI: 11. 返回 { message: "Task started" }
    
    Note right of Server: 后台线程开始执行<br/>并持续更新TaskLogbook
    
    Note left of UI: 前端通过持续轮询<br/>实时获取并展示后台进度
```

### 3.2. 核心UI组件

#### 3.2.1. 任务触发器 (Task Trigger)
- **功能**: 作为整个流程的入口。
- **实现**: 一个简单的按钮，例如“打开项目目录”或“开始新任务”。
- **交互**: 点击后，通过 `AppManager` 触发动作，调用 `HumanInputComponent` 显示动态输入框。

#### 3.2.2. 动态输入组件 (`HumanInputComponent`)
- **功能**: 负责接收用户的核心输入，如项目路径。
- **实现**: 一个模态弹窗（Modal Dialog），包含一个文本输入框和一个“确定”按钮。
- **交互**: 用户输入路径后点击“确定”，组件将输入内容传递给 `DataManager`，由 `DataManager` 负责调用后端的 `/get_and_create` API。

#### 3.2.3. 状态与日志显示区 (Status & Log Display)
- **功能**: 实时展示后端任务的当前状态和详细日志。
- **实现**: 一个简单的 `<div>` 或 `<pre>` 标签，用于显示从 `/status/{id}` API获取的状态和消息。
- **交互**: `DataManager` 在HTTP轮询成功后，会将获取到的新数据分发给此组件进行UI更新。

### 3.3. 【未来愿景】高级交互界面
本文档中原有的九宫格设计，代表了对系统未来能力的宏大构想。其中关于风险评估、知识图谱可视化、人机协同决策等设计，将在当前V4.2的异步任务框架稳定运行后，作为未来的迭代目标逐步探索和实现。

## 5. 前端实现规划

- **框架**: 无需重型框架，使用原生JavaScript (ES6+) 即可。
- **核心架构**: **统一协调器模式 (The Coordinator Pattern)**。
    - **协调者 (`AppManager`)**: 作为应用中枢，负责路由和分发所有跨组件的动作。
    - **数据管理器 (`DataManager`)**: 专门负责与后端API的所有通信，包括发起HTTP请求和管理轮询逻辑。
    - **UI组件 (`BaseComponent`的子类)**: 负责具体的UI渲染和用户交互，被动地由`AppManager`调用。
- **核心技术**:
    - **交互**: Fetch API 用于发送HTTP请求，DOM API 用于更新界面。
    - **实时通信**: **HTTP轮询**。由`DataManager`实现，周期性调用后端的`/status/{id}`接口。
- **文件结构**:
    - `.../templates/project_manager_v2.html`: 界面主HTML结构。
    - `.../static/js/unified/app-manager.js`: 协调器核心。
    - `.../static/js/unified/data-manager.js`: 数据管理器核心。
    - `.../static/js/unified/base-component.js`: 所有UI组件的基类。
    - `.../static/js/components/`: 存放具体的UI组件，如`HumanInputComponent`。

## 6. 接口对接说明

### 6.1 后端三段式API
- **第一段：创建任务**: `POST /api/pm_v2/get_and_create`
  - **功能**: 验证用户输入（如目录路径），创建`ProjectManager`实例，初始化`TaskLogbook`，并返回唯一的`manager_id`。
- **第二段：轮询状态**: `GET /api/pm_v2/status/{manager_id}`
  - **功能**: 根据`manager_id`，从`TaskLogbook`中查询并返回指定任务的当前最新状态（如 "initialized", "running", "completed"）和相关日志信息。
- **第三段：启动执行**: `POST /api/pm_v2/start-review/{manager_id}`
  - **功能**: 触发后端在一个新的后台线程中，开始执行与`manager_id`绑定的`ProjectManager`实例所承载的实际业务逻辑（如治理蓝图）。
