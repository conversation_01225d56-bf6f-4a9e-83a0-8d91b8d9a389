# Context Engineering

**11 min read · Jul 2, 2025**

---

### TL;DR

Agents need context to perform tasks. Context engineering is the art and science of filling the context window with just the right information at each step of an agent’s trajectory. In this post, we break down some common strategies — write, select, compress, and isolate — for context engineering by reviewing various popular agents and papers. We then explain how LangGraph is designed to support them!

Also, see our video on context engineering [here](https://youtu.be).

[Image: “Context Engineering” title banner]

[Image: General categories of context engineering — a diagram showing the four high‑level strategies: write, select, compress, isolate]

---

## Context Engineering

As <PERSON><PERSON> puts it, LLMs are like a “new kind of operating system.” The LLM is like the CPU and its [context window](https://docs.anthropic.com) is like the RAM, serving as the model’s working memory. Just like RAM, the LLM context window has limited [capacity](https://lilianweng.github.io) to handle various sources of context. And just as an operating system curates what fits into a CPU’s RAM, we can think about “context engineering” playing a similar role.

<PERSON><PERSON><PERSON> summarizes this well:

> [Context engineering is the] “…delicate art and science of filling the context window with just the right information for the next step.”

[Image: Context types commonly used in LLM applications — a chart showing Instructions, Knowledge, and Tools as three categories]

What are the types of context that we need to manage when building LLM applications? Context engineering is an umbrella that applies across a few different context types:

- **Instructions** – prompts, memories, few‑shot examples, tool descriptions, etc.
- **Knowledge** – facts, memories, etc.
- **Tools** – feedback from tool calls.

---

## Context Engineering for Agents

This year, interest in [agents](https://www.anthropic.com) has grown tremendously as LLMs get better at [reasoning](https://platform.openai.com) and [tool calling](https://www.anthropic.com). Agents interleave LLM invocations and tool calls, often for long‑running tasks, using tool feedback to decide each next step.

[Image: Diagram of agent loop showing alternating LLM calls and tool calls, with feedback arrows]

However, long‑running tasks and accumulating feedback from tool calls mean that agents often utilize a large number of tokens. This can cause problems:

- Exceeding the size of the context window  
- Ballooning cost / latency  
- Degrading agent performance  

Drew Breunig nicely outlined specific failure modes when context gets too long:

- **Context Poisoning:** When a hallucination makes it into the context  
- **Context Distraction:** When the context overwhelms the training  
- **Context Confusion:** When superfluous context influences the response  
- **Context Clash:** When parts of the context disagree  

[Image: Illustration of context from tool calls accumulating over multiple agent turns]

With this in mind, Cognition called out the importance of context engineering:

> “Context engineering … is effectively the #1 job of engineers building AI agents.”

Anthropic also noted:

> Agents often engage in conversations spanning hundreds of turns, requiring careful context management strategies.

So, how are people tackling this challenge today? We group common strategies for agent context engineering into four buckets — **write**, **select**, **compress**, and **isolate** — and give examples of each from popular agent products and papers. We then explain how LangGraph is designed to support them!

[Image: General categories of context engineering repeated for emphasis]

---

### 1. Write Context

**Writing context** means saving it outside the context window to help an agent perform a task.

#### Scratchpads

When humans solve tasks, we take notes and remember things for future related tasks. Agents are also gaining these capabilities! Note‑taking via a “scratchpad” is one approach to persist information while an agent is performing a task. The idea is to save information outside of the context window so that it’s available to the agent. Anthropics’s multi‑agent researcher illustrates a clear example:

> The LeadResearcher begins by thinking through the approach and saving its plan to Memory to persist the context, since if the context window exceeds 200,000 tokens it will be truncated and it is important to retain the plan.

Scratchpads can be implemented in a few different ways:

- As a tool call that writes to a file.  
- As a field in a runtime state object that persists during the session.  

In either case, scratchpads let agents save useful information to help them accomplish a task.

#### Memories

Scratchpads help agents solve a task within a given session (or thread), but sometimes agents benefit from remembering things across many sessions! Reflexion introduced the idea of reflection following each agent turn and re‑using these self‑generated memories. Generative Agents created memories synthesized periodically from collections of past agent feedback.

[Image: Diagram showing an LLM updating or creating memories]

These concepts made their way into popular products like ChatGPT, Cursor, and Windsurf, which all have mechanisms to auto‑generate long‑term memories that can persist across sessions based on user‑agent interactions.

---

### 2. Select Context

**Selecting context** means pulling it into the context window to help an agent perform a task.

#### From Scratchpad

The mechanism for selecting context from a scratchpad depends upon its implementation. If it’s a tool, an agent reads it via a tool call. If it’s part of runtime state, the developer can choose which parts to expose each step, providing fine‑grained control.

#### From Memories

If agents can save memories, they need the ability to select relevant ones. This might include:

- Episodic few‑shot examples  
- Procedural instructions  
- Semantic facts  

Some popular agents simply pull in a narrow set of files every time (e.g., Claude Code uses a `CLAUDE.md` file). Others select from large collections via embeddings or knowledge‑graph indexing. But selection remains challenging: at an AI Engineer World’s Fair, Simon Willison shared an example where ChatGPT unexpectedly fetched his location from memory and injected it into an image prompt — making users feel the context window “no longer belongs to them.”

#### Tools & Knowledge

- Too many overlapping tool descriptions can confuse the model. One solution is to apply RAG to fetch only the most relevant tool descriptions.  
- Retrieval‑augmented generation (RAG) is central for knowledge retrieval, with code agents often combining embedding search, grep/file search, knowledge‑graph retrieval, and re‑ranking to find the most relevant snippets.

[Image: Blank placeholder indicating tool/knowledge selection challenges]

---

### 3. Compress Context

**Compressing context** involves retaining only the tokens required to perform a task.

#### Context Summarization

Agent interactions can span hundreds of turns and use token‑heavy tool calls. Summarization is a common way to manage this. For example, Claude Code automatically runs “auto‑compact” after you exceed 95% of the context window, summarizing the full trajectory of interactions.

This can use strategies like recursive or hierarchical summarization.

[Image: Diagram showing points in an agent’s workflow where summarization can be applied]

You can also add summarization at specific points (e.g., post‑processing search tool results, summarizing at agent‑agent boundaries). Summarization can be challenging if you need to capture specific events or decisions. Cognition uses a fine‑tuned model for this step.

#### Context Trimming

Whereas summarization uses an LLM to distill context, trimming filters it via heuristics (e.g., removing older messages) or trained pruners (e.g., Provence for QA).

---

### 4. Isolate Context

**Isolating context** involves splitting it up to help an agent perform a task.

#### Multi‑Agent

One popular way is to split work across sub‑agents. OpenAI’s Swarm, for instance, lets a team of agents each handle specific subtasks with their own context windows. Subagents can explore different parts of a problem in parallel, though this may use up to 15× more tokens and requires careful orchestration.

[Image: Illustration of splitting context across multiple agents]

#### Context Isolation via Environments

HuggingFace’s CodeAgent runs tool calls in a sandbox environment, returning only the tool feedback to the LLM. Heavy objects (images, audio) stay in the sandbox or state, not in the context window.

[Image: Sandbox diagram showing code agents isolating tool outputs from LLM]

#### State Objects

An agent’s runtime state object can also isolate context. You define a schema with multiple fields, exposing only selected fields (e.g., `messages`) to the LLM each turn while keeping other fields hidden until needed.

---

## Context Engineering with LangSmith / LangGraph

So, how can you apply these ideas? Before you start, ensure you have:

1. **Observability & Tracing**  
   Use LangSmith to trace token usage and inspect your agent’s data.  

2. **Evaluation & Testing**  
   Use LangSmith’s agent evaluation tools to measure how context engineering changes performance.

### Write Context in LangGraph

LangGraph provides both:

- **Short‑term (thread‑scoped) memory** using checkpointing to persist agent state across steps (acting as a scratchpad).
- **Long‑term memory** allowing you to persist files or larger collections of memories across sessions, with high‑level abstractions via LangMem.

### Select Context in LangGraph

Within each node of a LangGraph agent, you can fetch exactly the state you need. Long‑term memory supports both file retrieval and embedding‑based searches, giving you fine‑grained control over what goes into the LLM’s context window.

For more on long‑term memory in LangGraph, see:
- [Deeplearning.ai course on memory](https://www.deeplearning.ai)
- [Ambient Agents course](https://academy.langchain.com)

This lets you build long‑running agents (e.g., email assistants) that manage their own context and learn from your feedback.

---

*End of converted markdown.*```
