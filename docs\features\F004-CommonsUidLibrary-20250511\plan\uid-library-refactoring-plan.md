---
id: F004-P001
title: xkongcloud-commons-uid 库重构计划
description: 本文档描述了xkongcloud-commons-uid库的重构计划，包括与KV参数服务解耦、验证职责明确化和启动检查机制的实现
author: AI助手
created_date: 2025-06-16
updated_date: 2025-06-20
version: 1.4
status: 审核中
category: 公共库
scope: 全局
keywords: [公共库, UID, 分布式ID, 重构, 解耦, 验证策略, 启动检查]
affected_features:
  - F004 # CommonsUidLibrary
related_docs:
  - ../design/commons-uid-library-design.md
  - ../design/postgresql-persistent-id-fingerprint-recovery.md
  - ../design/instance-id-encryption-design.md
  - ../uid-generation-process-flowchart.md
---

# xkongcloud-commons-uid 库重构计划

## 1. 背景与目的

当前的`xkongcloud-commons-uid`库设计中存在以下问题：

1. **与KV参数服务耦合**：库直接依赖于`KVParamService`获取配置参数，限制了库的通用性和可重用性
2. **验证职责不明确**：在调用UID库之前，外部调用者可能已经进行了一些验证，导致重复验证
3. **启动检查不完善**：当前实现中，应用启动时没有先检查数据库可用性和参数完整性

本重构计划旨在解决上述问题，将UID库与KV参数服务完全解耦，明确验证职责，并实现更全面的启动检查机制。

## 2. 重构目标

1. **完全解耦UID库与KV参数服务**：
   - 通过构造函数或setter方法接收所有必要参数
   - 不在库内部直接引用`KVParamService`

2. **明确验证职责**：
   - 外部调用者负责基础环境验证和参数获取
   - UID库负责业务逻辑验证和提供验证工具

3. **实现分层验证策略**：
   - 基础验证层：验证数据库连接、Schema存在性和必需参数
   - 组件验证层：验证表结构细节、数据一致性和组件特定配置

4. **避免重复验证**：
   - 明确验证职责边界
   - 使用验证标志记录已完成的验证

## 3. 重构内容

### 3.1 重构UID库的接口设计

#### 3.1.1 使用构建器模式重构`PersistentInstanceManager`

```java
/**
 * 持久化实例管理器构建器
 * 用于构建PersistentInstanceManager实例
 */
public class PersistentInstanceManagerBuilder {
    private JdbcTemplate jdbcTemplate;
    private TransactionTemplate transactionTemplate;
    private String applicationName;
    private String environment = "default";
    private String instanceGroup;
    private String localStoragePath = ".instance_id";
    private boolean recoveryEnabled = true;
    private int highConfidenceThreshold = 150;
    private int minimumAcceptableScore = 70;
    private String recoveryStrategy = "ALERT_AUTO_WITH_TIMEOUT";
    private int recoveryTimeoutSeconds = 300;
    private Long instanceIdOverride;
    private boolean encryptionEnabled = false; // 默认不启用加密，与instance-id-encryption-design.md保持一致
    private String schemaName = "infra_uid"; // 默认Schema名，遵循PostgreSQL Schema命名规范（参见docs/common/middleware/postgresql/development-standards-guide.md）
    private KeyManagementService keyManagementService; // 密钥管理服务

    // 设置方法，返回this以支持链式调用
    public PersistentInstanceManagerBuilder withJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        return this;
    }

    public PersistentInstanceManagerBuilder withTransactionTemplate(TransactionTemplate transactionTemplate) {
        this.transactionTemplate = transactionTemplate;
        return this;
    }

    public PersistentInstanceManagerBuilder withApplicationName(String applicationName) {
        this.applicationName = applicationName;
        return this;
    }

    public PersistentInstanceManagerBuilder withEnvironment(String environment) {
        this.environment = environment;
        return this;
    }

    public PersistentInstanceManagerBuilder withInstanceGroup(String instanceGroup) {
        this.instanceGroup = instanceGroup;
        return this;
    }

    public PersistentInstanceManagerBuilder withLocalStoragePath(String localStoragePath) {
        this.localStoragePath = localStoragePath;
        return this;
    }

    public PersistentInstanceManagerBuilder withRecoveryEnabled(boolean recoveryEnabled) {
        this.recoveryEnabled = recoveryEnabled;
        return this;
    }

    public PersistentInstanceManagerBuilder withHighConfidenceThreshold(int highConfidenceThreshold) {
        this.highConfidenceThreshold = highConfidenceThreshold;
        return this;
    }

    public PersistentInstanceManagerBuilder withMinimumAcceptableScore(int minimumAcceptableScore) {
        this.minimumAcceptableScore = minimumAcceptableScore;
        return this;
    }

    public PersistentInstanceManagerBuilder withRecoveryStrategy(String recoveryStrategy) {
        this.recoveryStrategy = recoveryStrategy;
        return this;
    }

    public PersistentInstanceManagerBuilder withRecoveryTimeoutSeconds(int recoveryTimeoutSeconds) {
        this.recoveryTimeoutSeconds = recoveryTimeoutSeconds;
        return this;
    }

    public PersistentInstanceManagerBuilder withInstanceIdOverride(Long instanceIdOverride) {
        this.instanceIdOverride = instanceIdOverride;
        return this;
    }

    public PersistentInstanceManagerBuilder withEncryptionEnabled(boolean encryptionEnabled) {
        this.encryptionEnabled = encryptionEnabled;
        return this;
    }

    public PersistentInstanceManagerBuilder withSchemaName(String schemaName) {
        this.schemaName = schemaName;
        return this;
    }

    /**
     * 设置密钥管理服务
     * 推荐作为独立Bean管理，而不是在PersistentInstanceManager内部实例化
     */
    public PersistentInstanceManagerBuilder withKeyManagementService(KeyManagementService keyManagementService) {
        this.keyManagementService = keyManagementService;
        return this;
    }

    /**
     * 构建PersistentInstanceManager实例
     * 在构建前验证必要参数
     */
    public PersistentInstanceManager build() {
        // 验证必要参数
        if (jdbcTemplate == null) {
            throw new IllegalStateException("jdbcTemplate不能为空");
        }
        if (applicationName == null || applicationName.trim().isEmpty()) {
            throw new IllegalStateException("applicationName不能为空");
        }
        if (encryptionEnabled && keyManagementService == null) {
            throw new IllegalStateException("启用加密时keyManagementService不能为空");
        }
        // 其他验证...

        return new PersistentInstanceManager(
            jdbcTemplate,
            transactionTemplate,
            applicationName,
            environment,
            instanceGroup,
            localStoragePath,
            recoveryEnabled,
            highConfidenceThreshold,
            minimumAcceptableScore,
            recoveryStrategy,
            recoveryTimeoutSeconds,
            instanceIdOverride,
            encryptionEnabled,
            schemaName,
            keyManagementService
        );
    }
}
```

#### 3.1.2 重构`KeyManagementService`

**重要说明**：`KeyManagementService`应作为独立的Spring Bean进行管理，而不是在`PersistentInstanceManager`内部实例化。这样可以更好地遵循依赖注入原则，便于单元测试，并提高代码的可维护性和灵活性。

```java
/**
 * 密钥管理服务
 * 负责加密密钥的生成、存储和获取
 */
public class KeyManagementService {
    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;
    private final String applicationName;
    private final String environment;
    private final boolean encryptionEnabled;
    private final String schemaName;

    // 密钥缓存
    private String cachedKey;

    /**
     * 构造函数
     */
    public KeyManagementService(
            JdbcTemplate jdbcTemplate,
            TransactionTemplate transactionTemplate,
            String applicationName,
            String environment,
            boolean encryptionEnabled,
            String schemaName) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
        this.applicationName = applicationName;
        this.environment = environment;
        this.encryptionEnabled = encryptionEnabled;
        this.schemaName = schemaName;
    }

    // 其他方法...
}
```

**外部调用者应该这样定义KeyManagementService Bean**：

```java
@Bean
public KeyManagementService keyManagementService() {
    // 从KV参数服务获取参数
    String schemaName = kvParamService.getParam("postgresql.uid.schema", "infra_uid");
    String environment = kvParamService.getParam("uid.instance.environment", "default");
    boolean encryptionEnabled = Boolean.parseBoolean(kvParamService.getParam("uid.instance.encryption.enabled", "false"));

    // 创建KeyManagementService实例
    return new KeyManagementService(
        jdbcTemplate,
        transactionTemplate,
        clusterId, // 使用@Value("${xkong.kv.cluster-id}")注入的clusterId变量
        environment,
        encryptionEnabled,
        schemaName
    );
}
```

#### 3.1.3 重构`PersistentInstanceWorkerIdAssigner`

```java
/**
 * 持久化实例Worker ID分配器
 * 实现百度UID库的WorkerIdAssigner接口
 */
public class PersistentInstanceWorkerIdAssigner implements WorkerIdAssigner {
    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;
    private final PersistentInstanceManager persistentInstanceManager;
    private final int leaseDurationSeconds;
    private final String schemaName;

    /**
     * 构造函数
     */
    public PersistentInstanceWorkerIdAssigner(
            JdbcTemplate jdbcTemplate,
            TransactionTemplate transactionTemplate,
            PersistentInstanceManager persistentInstanceManager,
            int leaseDurationSeconds,
            String schemaName) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
        this.persistentInstanceManager = persistentInstanceManager;
        this.leaseDurationSeconds = leaseDurationSeconds;
        this.schemaName = schemaName;
    }

    // 其他方法...
}
```

#### 3.1.4 重构`UidTableManager`工具类

```java
/**
 * UID表管理工具类
 * 提供表结构管理功能
 */
public class UidTableManager {

    /**
     * 创建Schema
     */
    public static void createSchema(JdbcTemplate jdbcTemplate, String schemaName) {
        // 实现...
    }

    /**
     * 创建表
     */
    public static void createTables(JdbcTemplate jdbcTemplate, String schemaName) {
        // 实现...
    }

    /**
     * 验证表结构
     */
    public static void validateTables(JdbcTemplate jdbcTemplate, String schemaName) {
        // 实现...
    }

    // 其他方法...
}
```

### 3.2 创建验证工具类

```java
/**
 * UID库验证工具类
 * 提供验证方法，但不自动执行验证
 */
public class UidValidationUtils {

    /**
     * 验证Schema是否存在
     */
    public static void validateSchemaExists(JdbcTemplate jdbcTemplate, String schemaName) {
        // 实现...
    }

    /**
     * 验证UID相关表是否存在
     */
    public static void validateTablesExist(JdbcTemplate jdbcTemplate, String schemaName) {
        // 实现...
    }

    /**
     * 验证数据库连接是否可用
     */
    public static void validateDatabaseConnection(JdbcTemplate jdbcTemplate) {
        // 实现...
    }

    // 其他验证方法...
}
```

### 3.3 实现分层验证策略

#### 3.3.1 创建基础验证组件

```java
/**
 * UID生成器配置验证运行器
 * 在应用启动早期阶段验证基础环境
 */
@Component
@Order(1) // 确保在早期执行
public class UidGeneratorConfigValidationRunner implements ApplicationRunner {

    private final JdbcTemplate jdbcTemplate;
    private final ValidationResultCache validationResultCache;
    private final String schemaName;
    private final Logger log = LoggerFactory.getLogger(UidGeneratorConfigValidationRunner.class);

    // 必需的参数列表
    private static final List<String> REQUIRED_PARAMS = Arrays.asList(
        "uid.epochStr", "uid.timeBits", "uid.workerBits", "uid.seqBits",
        "uid.instance.environment", "uid.instance.group"
    );

    @Autowired
    public UidGeneratorConfigValidationRunner(
            JdbcTemplate jdbcTemplate,
            ValidationResultCache validationResultCache,
            @Value("${postgresql.uid.schema:infra_uid}") String schemaName) {
        this.jdbcTemplate = jdbcTemplate;
        this.validationResultCache = validationResultCache;
        this.schemaName = schemaName;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始验证UID生成器配置...");

        // 1. 验证数据库连接
        validateDatabaseConnection();

        // 2. 验证Schema存在
        validateSchemaExists();

        log.info("UID生成器配置验证通过");
    }

    private void validateDatabaseConnection() {
        // 实现...
    }

    private void validateSchemaExists() {
        // 实现...
    }
}
```

#### 3.3.2 创建验证结果缓存

**重要说明**：`ValidationResultCache`需要确保线程安全，因为它可能会被多个线程同时访问和修改。推荐使用`ConcurrentHashMap`来实现线程安全的集合，它提供了良好的并发性能，同时代码简洁易读。

```java
/**
 * 验证结果缓存
 * 用于记录已完成的验证，避免重复验证
 * 使用ConcurrentHashMap实现线程安全
 */
@Component
public class ValidationResultCache {
    // 使用ConcurrentHashMap.newKeySet()创建线程安全的Set
    private final Set<String> validatedSchemas = ConcurrentHashMap.newKeySet();
    private final Set<String> validatedTables = ConcurrentHashMap.newKeySet();
    private final Set<String> validatedParams = ConcurrentHashMap.newKeySet();

    /**
     * 标记Schema已验证
     * 线程安全方法
     */
    public void markSchemaValidated(String schema) {
        validatedSchemas.add(schema);
    }

    /**
     * 检查Schema是否已验证
     * 线程安全方法
     */
    public boolean isSchemaValidated(String schema) {
        return validatedSchemas.contains(schema);
    }

    /**
     * 标记表已验证
     * 线程安全方法
     */
    public void markTableValidated(String table) {
        validatedTables.add(table);
    }

    /**
     * 检查表是否已验证
     * 线程安全方法
     */
    public boolean isTableValidated(String table) {
        return validatedTables.contains(table);
    }

    /**
     * 标记参数已验证
     * 线程安全方法
     */
    public void markParamValidated(String param) {
        validatedParams.add(param);
    }

    /**
     * 检查参数是否已验证
     * 线程安全方法
     */
    public boolean isParamValidated(String param) {
        return validatedParams.contains(param);
    }

    /**
     * 清除所有验证缓存
     * 线程安全方法
     */
    public void clearAll() {
        validatedSchemas.clear();
        validatedTables.clear();
        validatedParams.clear();
    }
}
```

### 3.4 外部调用者的使用方式

```java
@Configuration
@EnableScheduling
public class UidGeneratorConfig {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private KVParamService kvParamService; // 只在调用者使用，不传入库

    @Value("${xkong.kv.cluster-id}")
    private String clusterId;

    @Bean
    public PersistentInstanceManager persistentInstanceManager() {
        // 从KV参数服务获取参数
        String schemaName = kvParamService.getParam("postgresql.uid.schema", "infra_uid");
        String environment = kvParamService.getParam("uid.instance.environment", "default");
        // 获取其他参数...

        // 使用构建器模式创建PersistentInstanceManager
        return new PersistentInstanceManagerBuilder()
            .withJdbcTemplate(jdbcTemplate)
            .withTransactionTemplate(transactionTemplate)
            .withApplicationName(clusterId)
            .withEnvironment(environment)
            .withSchemaName(schemaName)
            // 设置其他参数...
            .build();
    }

    // 其他Bean定义...
}
```

## 4. 实施计划

### 4.1 阶段一：接口重构（第1周）

1. **重构`PersistentInstanceManager`类**：
   - 添加构建器模式
   - 移除KV参数服务依赖
   - 添加Schema名参数
   - 修改构造函数，接收所有必要参数

2. **重构`KeyManagementService`类**：
   - 移除KV参数服务依赖
   - 添加Schema名参数
   - 修改密钥表访问逻辑，使用传入的Schema名

3. **重构`PersistentInstanceWorkerIdAssigner`类**：
   - 移除KV参数服务依赖
   - 添加Schema名参数
   - 修改Worker ID分配逻辑，使用传入的Schema名

4. **重构`UidTableManager`工具类**：
   - 修改所有方法，支持自定义Schema名
   - 添加表结构验证方法
   - 确保所有SQL语句使用传入的Schema名

## 5. 验证职责划分

为了避免重复验证，我们需要明确划分验证职责：

### 5.1 外部调用者负责的验证

1. **基础环境验证**：
   - 数据库连接可用性验证
   - Schema存在性验证
   - 必需参数完整性验证

2. **参数获取与验证**：
   - 从KV参数服务或其他配置源获取参数
   - 验证参数值的有效性
   - 决定使用哪个Schema和表名

3. **DDL策略执行**：
   - 根据DDL策略决定是否创建、验证或更新表结构
   - 调用UidTableManager的相应方法

### 5.2 UID库负责的验证

1. **参数有效性验证**：
   - 在构建器的build()方法中验证传入参数的有效性
   - 确保必需参数不为空

2. **表结构验证**：
   - 提供验证表结构的方法
   - 但不自动执行验证，由调用者决定何时验证

3. **业务逻辑验证**：
   - 验证实例ID恢复过程中的业务规则
   - 验证Worker ID分配过程中的业务规则

### 5.3 验证流程图

```mermaid
flowchart TD
    subgraph "应用启动阶段"
        A[应用启动] --> B[UidGeneratorConfigValidationRunner执行]
        B --> C{数据库连接\n是否可用?}
        C -->|否| D[启动失败]
        C -->|是| E{Schema是否\n存在?}
        E -->|否| D
        E -->|是| F[标记基础验证通过]
    end

    subgraph "Bean初始化阶段"
        F --> G[创建PersistentInstanceManager]
        G --> H{基础验证是否\n已通过?}
        H -->|否| I[执行基础验证]
        H -->|是| J[执行组件特定验证]
        I --> J
        J --> K[标记组件验证通过]
    end

    subgraph "运行时阶段"
        K --> L[执行业务操作]
        L --> M{需要访问\n数据库?}
        M -->|是| N{相关验证是否\n已通过?}
        N -->|否| O[执行最小化验证]
        N -->|是| P[直接执行操作]
        O --> P
    end
```

## 6. 风险与缓解措施

| 风险 | 影响 | 缓解措施 |
|------|------|---------|
| 验证策略过于复杂 | 增加维护难度 | 清晰文档化验证职责，提供简单的API |
| 实现引入bug | 影响系统稳定性 | 全面的单元测试和集成测试 |
| 性能下降 | 影响系统响应时间 | 使用缓存机制，避免重复验证 |
| 验证失败处理不当 | 可能导致系统不稳定 | 提供明确的错误信息和恢复机制 |

## 7. 示例代码

### 7.1 外部调用者使用示例

```java
@Configuration
@EnableScheduling
public class UidGeneratorConfig {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private KVParamService kvParamService;

    @Value("${xkong.kv.cluster-id}")
    private String clusterId;

    /**
     * 创建线程安全的验证结果缓存
     */
    @Bean
    public ValidationResultCache validationResultCache() {
        return new ValidationResultCache();
    }

    /**
     * 创建密钥管理服务
     * 作为独立Bean管理，而不是在PersistentInstanceManager内部实例化
     */
    @Bean
    public KeyManagementService keyManagementService() {
        // 获取必需参数（无默认值，缺少时应用无法启动）
        String schemaName = kvParamService.getParam("postgresql.uid.schema");
        if (schemaName == null || schemaName.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'postgresql.uid.schema'未配置");
        }

        String environment = kvParamService.getParam("uid.instance.environment");
        if (environment == null || environment.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.environment'未配置");
        }

        String encryptionEnabledStr = kvParamService.getParam("uid.instance.encryption.enabled");
        if (encryptionEnabledStr == null || encryptionEnabledStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.encryption.enabled'未配置");
        }
        boolean encryptionEnabled = Boolean.parseBoolean(encryptionEnabledStr);

        // 创建KeyManagementService实例
        return new KeyManagementService(
            jdbcTemplate,
            transactionTemplate,
            clusterId, // 使用@Value("${xkong.kv.cluster-id}")注入的clusterId变量
            environment,
            encryptionEnabled,
            schemaName
        );
    }

    /**
     * 创建持久化实例管理器
     * 注入KeyManagementService和ValidationResultCache
     */
    @Bean
    public PersistentInstanceManager persistentInstanceManager(
            ValidationResultCache validationResultCache,
            KeyManagementService keyManagementService) {
        // 获取必需参数（无默认值，缺少时应用无法启动）
        // 注意：根据业务要求，从center获取的KV参数如果没有获取到则没有默认值，应导致启动失败
        String schemaName = kvParamService.getParam("postgresql.uid.schema");
        if (schemaName == null || schemaName.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'postgresql.uid.schema'未配置");
        }

        String environment = kvParamService.getParam("uid.instance.environment");
        if (environment == null || environment.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.environment'未配置");
        }

        String instanceGroup = kvParamService.getParam("uid.instance.group");
        if (instanceGroup == null || instanceGroup.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.group'未配置");
        }

        String localStoragePath = kvParamService.getParam("uid.instance.local-storage-path");
        if (localStoragePath == null || localStoragePath.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.local-storage-path'未配置");
        }

        String recoveryEnabledStr = kvParamService.getParam("uid.instance.recovery.enabled");
        if (recoveryEnabledStr == null || recoveryEnabledStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.recovery.enabled'未配置");
        }
        boolean recoveryEnabled = Boolean.parseBoolean(recoveryEnabledStr);

        String highConfThresholdStr = kvParamService.getParam("uid.instance.recovery.high-confidence-threshold");
        if (highConfThresholdStr == null || highConfThresholdStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.recovery.high-confidence-threshold'未配置");
        }
        int highConfThreshold = Integer.parseInt(highConfThresholdStr);

        String minAcceptScoreStr = kvParamService.getParam("uid.instance.recovery.minimum-acceptable-score");
        if (minAcceptScoreStr == null || minAcceptScoreStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.recovery.minimum-acceptable-score'未配置");
        }
        int minAcceptScore = Integer.parseInt(minAcceptScoreStr);

        String recoveryStrategy = kvParamService.getParam("uid.instance.recovery.strategy");
        if (recoveryStrategy == null || recoveryStrategy.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.recovery.strategy'未配置");
        }

        String recoveryTimeoutSecondsStr = kvParamService.getParam("uid.instance.recovery.timeout-seconds");
        if (recoveryTimeoutSecondsStr == null || recoveryTimeoutSecondsStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.recovery.timeout-seconds'未配置");
        }
        int recoveryTimeoutSeconds = Integer.parseInt(recoveryTimeoutSecondsStr);

        String encryptionEnabledStr = kvParamService.getParam("uid.instance.encryption.enabled");
        if (encryptionEnabledStr == null || encryptionEnabledStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.instance.encryption.enabled'未配置");
        }
        boolean encryptionEnabled = Boolean.parseBoolean(encryptionEnabledStr);

        // 检查Schema是否已验证
        if (!validationResultCache.isSchemaValidated(schemaName)) {
            // 验证Schema
            UidValidationUtils.validateSchemaExists(jdbcTemplate, schemaName);
            validationResultCache.markSchemaValidated(schemaName);
        }

        // 使用构建器模式创建PersistentInstanceManager
        return new PersistentInstanceManagerBuilder()
            .withJdbcTemplate(jdbcTemplate)
            .withTransactionTemplate(transactionTemplate)
            .withApplicationName(clusterId)
            .withEnvironment(environment)
            .withInstanceGroup(instanceGroup)
            .withLocalStoragePath(localStoragePath)
            .withRecoveryEnabled(recoveryEnabled)
            .withHighConfidenceThreshold(highConfThreshold)
            .withMinimumAcceptableScore(minAcceptScore)
            .withRecoveryStrategy(recoveryStrategy)
            .withRecoveryTimeoutSeconds(recoveryTimeoutSeconds)
            .withEncryptionEnabled(encryptionEnabled)
            .withSchemaName(schemaName)
            .withKeyManagementService(keyManagementService) // 注入KeyManagementService
            .build();
    }

    /**
     * 创建Worker ID分配器
     */
    @Bean
    public WorkerIdAssigner workerIdAssigner(
            PersistentInstanceManager persistentInstanceManager) {
        // 获取必需参数（无默认值，缺少时应用无法启动）
        String schemaName = kvParamService.getParam("postgresql.uid.schema");
        if (schemaName == null || schemaName.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'postgresql.uid.schema'未配置");
        }

        String leaseDurationStr = kvParamService.getParam("uid.worker.lease-duration-seconds");
        if (leaseDurationStr == null || leaseDurationStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.worker.lease-duration-seconds'未配置");
        }
        int leaseDuration = Integer.parseInt(leaseDurationStr);

        // 创建PersistentInstanceWorkerIdAssigner实例
        return new PersistentInstanceWorkerIdAssigner(
            jdbcTemplate,
            transactionTemplate,
            persistentInstanceManager,
            leaseDuration,
            schemaName
        );
    }

    /**
     * 创建UID生成器
     */
    @Bean
    public UidGenerator uidGenerator(WorkerIdAssigner workerIdAssigner) {
        // 获取必需参数（无默认值，缺少时应用无法启动）
        String epochStr = kvParamService.getParam("uid.epochStr");
        if (epochStr == null || epochStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.epochStr'未配置");
        }

        String timeBitsStr = kvParamService.getParam("uid.timeBits");
        if (timeBitsStr == null || timeBitsStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.timeBits'未配置");
        }
        int timeBits = Integer.parseInt(timeBitsStr);

        String workerBitsStr = kvParamService.getParam("uid.workerBits");
        if (workerBitsStr == null || workerBitsStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.workerBits'未配置");
        }
        int workerBits = Integer.parseInt(workerBitsStr);

        String seqBitsStr = kvParamService.getParam("uid.seqBits");
        if (seqBitsStr == null || seqBitsStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.seqBits'未配置");
        }
        int seqBits = Integer.parseInt(seqBitsStr);

        // 创建CachedUidGenerator实例
        CachedUidGenerator cachedUidGenerator = new CachedUidGenerator();
        cachedUidGenerator.setWorkerIdAssigner(workerIdAssigner);
        cachedUidGenerator.setEpochStr(epochStr);
        cachedUidGenerator.setTimeBits(timeBits);
        cachedUidGenerator.setWorkerBits(workerBits);
        cachedUidGenerator.setSeqBits(seqBits);

        // 设置必需参数（无默认值，缺少时应用无法启动）
        String boostPowerStr = kvParamService.getParam("uid.boostPower");
        if (boostPowerStr == null || boostPowerStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.boostPower'未配置");
        }
        cachedUidGenerator.setBoostPower(Integer.parseInt(boostPowerStr));

        String paddingFactorStr = kvParamService.getParam("uid.paddingFactor");
        if (paddingFactorStr == null || paddingFactorStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.paddingFactor'未配置");
        }
        cachedUidGenerator.setPaddingFactor(Integer.parseInt(paddingFactorStr));

        String scheduleIntervalStr = kvParamService.getParam("uid.scheduleInterval");
        if (scheduleIntervalStr == null || scheduleIntervalStr.trim().isEmpty()) {
            throw new IllegalStateException("必需的参数'uid.scheduleInterval'未配置");
        }
        cachedUidGenerator.setScheduleInterval(Long.parseLong(scheduleIntervalStr));

        return cachedUidGenerator;
    }
}
```

## 8. 总结

本重构计划通过将UID库与KV参数服务完全解耦，明确验证职责，并实现分层验证策略，解决了当前设计中的问题。重构后的库将更加通用、灵活，并且避免了重复验证的问题。

主要改进点包括：

1. **完全解耦**：UID库不再依赖KV参数服务，通过构造函数或setter方法接收所有必要参数
2. **明确验证职责**：外部调用者负责基础环境验证，UID库负责业务逻辑验证
3. **分层验证策略**：基础验证层和组件验证层分工明确，避免重复验证
4. **提高灵活性**：支持自定义Schema名和表名，适应不同环境的需求

通过这些改进，UID库将更加符合单一职责原则和依赖倒置原则，提高了代码的可维护性和可测试性。

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 1.4 | 2025-06-20 | 移除uid.instance.override参数的使用，确保与phase1-detailed-implementation-guide.md文档一致 | AI助手 |
| 1.3 | 2025-06-19 | 修改代码示例，将所有参数设置为必需参数，移除所有默认值，确保所有参数未设置时应用启动失败 | AI助手 |
| 1.2 | 2025-06-18 | 修改代码示例，移除必需参数的默认值，确保符合业务要求 | AI助手 |
| 1.1 | 2025-06-17 | 更新元数据格式，添加文档更新计划和全局索引引用，确保与加密设计文档一致 | AI助手 |
| 1.0 | 2025-06-16 | 初始版本 | AI助手 |
