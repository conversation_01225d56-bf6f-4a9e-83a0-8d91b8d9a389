---
title: PostgreSQL迁移第2阶段执行方案 - xkongcloud-commons-uid公共库开发
document_id: F003-PLAN-002 # 保持不变
document_type: 实现文档 # 保持不变
category: 数据库迁移 # 保持不变
scope: XKC-CORE # 保持不变
keywords: [PostgreSQL, 数据库迁移, 百度UID生成器, 持久化实例ID, 特征码恢复, 公共库开发, UID库, 分布式ID, Schema命名规范, 表命名规范] # 扩展关键词
created_date: 2025-06-14 # 保持不变
updated_date: 2025-06-15 # 更新为当前日期
status: 修订稿 # 更新状态
version: 1.2 # 版本升级
authors: [AI助手] # 保持不变
change_history:
  - version: 1.0
    date: 2025-06-14
    author: AI助手
    changes: 初始版本
  - version: 1.1
    date: 2025-05-17
    author: AI助手
    changes: 更新配置参数说明，添加恢复策略说明
  - version: 1.2
    date: 2025-06-15
    author: AI助手
    changes: 修改表名和Schema名，确保符合PostgreSQL开发规范指南中的命名约定（表名使用单数形式和小写形式）
affected_features:
  - F003 # PostgreSQL迁移
  - F004 # CommonsUidLibrary
related_docs:
  - ../../../features/F003-PostgreSQL迁移-20250508/plan/implementation-plan.md
  - ../../../features/F004-CommonsUidLibrary-20250511/plan/implementation-plan.md # CommonsUidLibrary实施计划
  - ../../../features/F004-CommonsUidLibrary-20250511/design/commons-uid-library-design.md # CommonsUidLibrary设计文档
  - ../../../features/F004-CommonsUidLibrary-20250511/design/instance-id-encryption-design.md # 实例ID加密设计
  - ../../../features/F004-CommonsUidLibrary-20250511/design/postgresql-persistent-id-fingerprint-recovery.md # 持久化实例ID和特征码恢复设计
---

# PostgreSQL迁移第2阶段执行方案 - xkongcloud-commons-uid公共库开发

## 1. 概述

本文档详细描述了PostgreSQL迁移项目第2阶段的执行方案，主要涉及xkongcloud-commons-uid公共库的开发。该公共库将提供基于百度UID生成器的分布式唯一ID生成功能，并实现持久化实例ID和特征码恢复功能。

### 1.1 背景

根据[PostgreSQL迁移实施计划](../../../features/F003-PostgreSQL迁移-20250508/plan/implementation-plan.md)和[xkongcloud-commons-uid公共库实施计划](../../../features/F004-CommonsUidLibrary-20250511/plan/implementation-plan.md)，第2阶段的主要任务是开发xkongcloud-commons-uid公共库，为后续的PostgreSQL迁移提供基础支持。

**业务背景**：我们的系统正在从传统数据库迁移到PostgreSQL，这需要重新设计ID生成机制。之前的ID生成方式依赖于原数据库的自增特性，无法满足分布式环境的需求。新的ID生成机制将支持跨数据库、跨服务器的全局唯一ID，为未来的微服务架构奠定基础。

### 1.2 目标

1.  创建xkongcloud-commons父模块和xkongcloud-commons-uid子模块。
2.  实现以下核心组件，按照依赖关系顺序：
    *   **基础类**：
        *   **WorkerNodeType枚举**：定义工作节点类型。
        *   **ValidationResultCache类**：缓存验证结果。
        *   **MachineFingerprints类**：收集和管理机器特征信息。
    *   **工具类**：
        *   **UidValidationUtils工具类**：验证数据库连接、Schema和表。
    *   **服务类**：
        *   **KeyManagementService类**：管理加密密钥。
    *   **管理类**：
        *   **PersistentInstanceManagerBuilder类**：构建PersistentInstanceManager实例。
        *   **PersistentInstanceManager类**：管理应用实例的持久化ID。
    *   **分配器和表管理器**：
        *   **PersistentInstanceWorkerIdAssigner类**：为每个实例分配唯一的工作机器ID。
        *   **UidTableManager工具类**：管理数据库中的UID相关表。
3.  编写单元测试和文档。
4.  确保公共库能够被xkongcloud-business-internal-core项目正确引用和使用。

**业务关联**：这些组件共同构成了分布式ID生成的基础设施，确保在多服务器、多实例环境下生成的ID不会冲突。这对以下业务系统至关重要：交易系统、订单系统、用户系统、支付系统、日志系统等。

### 1.3 实施范围

本阶段的实施范围仅限于xkongcloud-commons-uid公共库的开发，不包括在xkongcloud-business-internal-core项目中的集成和测试，后者将在第3阶段进行。

**实施策略说明**：我们采用分阶段实施策略，先完成基础组件的开发和测试，再进行业务系统集成。这种方式可以降低风险，确保基础组件的质量和稳定性。

### 1.4 前提条件

1.  **PostgreSQL 17.4**：已安装并配置完成。
2.  **配置参数**：已在xkongcloud-service-center中设置必要的键值对参数。
3.  **数据库结构**：已创建了必要的Schema，特别是`infra_uid`。

### 1.5 名词解释

*   **PostgreSQL**：一种强大的开源关系型数据库系统。
*   **xkongcloud-commons-uid**：我们开发的公共库，用于生成分布式环境下的唯一ID。
*   **百度UID生成器**：百度开源的一款分布式ID生成工具。
*   **分布式唯一ID**：在分布式系统中保证全局唯一的标识符。
*   **持久化实例ID**：将应用实例的唯一标识持久保存，即使重启也能恢复相同的ID。
*   **特征码恢复**：通过收集服务器硬件和环境信息，在实例重启后尝试恢复原有身份的技术。
*   **工作机器ID（Worker ID）**：在分布式ID生成算法中，分配给每个服务实例的唯一标识。
*   **Schema**：PostgreSQL中的命名空间，用于组织和隔离数据库对象。
*   **模块 (Maven)**：Maven项目中的一个独立单元。
*   **父模块 (Maven)**：包含多个子模块的顶级模块。
*   **pom.xml (Maven)**：Maven项目的配置文件。
*   **Maven**：一个流行的Java项目构建和依赖管理工具。
*   **依赖管理 (Maven)**：控制项目所需的外部库和框架。
*   **枚举（Enum）**：Java中的一种特殊类型，用于定义一组固定的常量。
*   **构建器模式（Builder Pattern）**：一种创建对象的设计模式。
*   **密钥管理服务（Key Management Service）**：负责创建、存储、分发和管理加密密钥的系统。
*   **租约机制（Lease Mechanism）**：通过定期续约确保工作机器ID的唯一性和有效性。

## 2. 模块与核心组件设计

### 2.1 模块创建

#### 2.1.1 xkongcloud-commons父模块

1.  在项目根目录下创建`xkongcloud-commons`目录（已存在但为空）。
2.  在`xkongcloud-commons`目录中创建`pom.xml`文件，配置为多模块父项目。

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.xkong.cloud</groupId>
        <artifactId>xkongcloud</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>xkongcloud-commons</artifactId>
    <packaging>pom</packaging>
    <name>XKong Cloud Commons</name>
    <description>XKong Cloud 公共组件库</description>

    <modules>
        <module>xkongcloud-commons-uid</module>
        <!-- 未来可能的其他公共模块 -->
    </modules>
</project>
```

#### 2.1.2 xkongcloud-commons-uid子模块

1.  在`xkongcloud-commons`目录下创建`xkongcloud-commons-uid`目录。
2.  创建标准的Maven项目结构。
3.  创建`pom.xml`文件，添加必要的依赖。

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.xkong.cloud</groupId>
        <artifactId>xkongcloud-commons</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>xkongcloud-commons-uid</artifactId>
    <name>XKong Cloud Commons UID</name>
    <description>分布式唯一ID生成公共库</description>

    <dependencies>
        <!-- 百度UID生成器 -->
        <dependency>
            <groupId>com.xfvape.uid</groupId>
            <artifactId>uid-generator</artifactId>
            <version>0.0.4-RELEASE</version>
        </dependency>
        <!-- Spring相关依赖 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <!-- 其他依赖 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
```
4.  创建`README.md`文件。

```markdown
# XKong Cloud Commons UID

分布式唯一ID生成公共库，基于百度UID生成器，提供持久化实例ID和特征码恢复功能。

## 功能特性

- 基于百度UID生成器的分布式唯一ID生成
- 持久化实例ID管理
- 基于机器特征码的实例身份自动恢复
- 支持实例ID文件加密存储
- 提供表结构管理工具

## 使用方法

### Maven依赖

```xml
<dependency>
    <groupId>org.xkong.cloud</groupId>
    <artifactId>xkongcloud-commons-uid</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```
(详细配置和使用见主文档)
```

### 2.2 核心类实现

以下是核心类的实现，按照依赖关系顺序排列：

#### 2.2.1 `WorkerNodeType` 枚举
在`org.xkong.cloud.commons.uid`包中创建`WorkerNodeType`枚举，用于区分不同类型的工作节点。

```java
package org.xkong.cloud.commons.uid;

/**
 * 工作节点类型枚举
 * 用于区分不同类型的工作节点，支持容器节点和物理节点
 */
public enum WorkerNodeType {
    /**
     * 容器节点，如Docker容器、Kubernetes Pod等
     */
    CONTAINER(1),

    /**
     * 物理节点，如物理服务器、虚拟机等
     */
    ACTUAL(2);

    private final int value;

    WorkerNodeType(int value) {
        this.value = value;
    }

    public int value() {
        return value;
    }

    public static WorkerNodeType of(int value) {
        for (WorkerNodeType type : values()) {
            if (type.value == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid WorkerNodeType value: " + value);
    }
}
```

#### 2.2.2 `ValidationResultCache` 类
在`org.xkong.cloud.commons.uid`包中创建`ValidationResultCache`类。

```java
package org.xkong.cloud.commons.uid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 验证结果缓存
 */
public class ValidationResultCache {
    private static final Logger log = LoggerFactory.getLogger(ValidationResultCache.class);
    private final Set<String> validatedSchemas = ConcurrentHashMap.newKeySet();
    private final Set<String> validatedTables = ConcurrentHashMap.newKeySet();
    private final Set<String> validatedParams = ConcurrentHashMap.newKeySet();

    public void markSchemaValidated(String schema) { validatedSchemas.add(schema); log.debug("已标记Schema验证通过: {}", schema); }
    public boolean isSchemaValidated(String schema) { return validatedSchemas.contains(schema); }
    public void markTableValidated(String table) { validatedTables.add(table); log.debug("已标记表验证通过: {}", table); }
    public boolean isTableValidated(String table) { return validatedTables.contains(table); }
    public void markParamValidated(String param) { validatedParams.add(param); log.debug("已标记参数验证通过: {}", param); }
    public boolean isParamValidated(String param) { return validatedParams.contains(param); }
    public void clearAll() { validatedSchemas.clear(); validatedTables.clear(); validatedParams.clear(); log.info("已清除所有验证缓存"); }
}
```

#### 2.2.3 `MachineFingerprints` 类
在`org.xkong.cloud.commons.uid`包中创建`MachineFingerprints`类，用于收集机器特征码。

```java
package org.xkong.cloud.commons.uid;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;
import java.util.stream.Collectors;
import java.net.HttpURLConnection; // Added for cloud metadata
import java.net.URL; // Added for cloud metadata

/**
 * 机器特征码收集工具类
 */
public class MachineFingerprints {
    private static final Logger log = LoggerFactory.getLogger(MachineFingerprints.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    private static volatile Map<String, Object> cachedFingerprints = null;

    public static Map<String, Object> getFingerprints() {
        if (cachedFingerprints == null) {
            synchronized (MachineFingerprints.class) {
                if (cachedFingerprints == null) {
                    Map<String, Object> fingerprints = new HashMap<>();
                    fingerprints.put("hostname", getHostname());
                    fingerprints.put("os_name", System.getProperty("os.name"));
                    fingerprints.put("os_version", System.getProperty("os.version"));
                    fingerprints.put("os_arch", System.getProperty("os.arch"));
                    List<String> macAddresses = getMacAddresses();
                    if (!macAddresses.isEmpty()) {
                        fingerprints.put("mac_addresses", macAddresses);
                    }
                    String biosUuid = getBiosUuid();
                    if (biosUuid != null) {
                        fingerprints.put("bios_uuid", biosUuid);
                    }
                    String systemSerialNumber = getSystemSerialNumber();
                    if (systemSerialNumber != null) {
                        fingerprints.put("system_serial", systemSerialNumber);
                    }
                    Map<String, String> cloudMetadata = getCloudMetadata();
                    if (!cloudMetadata.isEmpty()) {
                        fingerprints.put("cloud_metadata", cloudMetadata);
                    }
                    String fingerprintHash = calculateFingerprintHash(fingerprints);
                    fingerprints.put("fingerprint_hash", fingerprintHash);
                    cachedFingerprints = Collections.unmodifiableMap(fingerprints);
                    log.info("已收集机器特征码: {}", maskSensitiveInfo(fingerprints));
                }
            }
        }
        return cachedFingerprints;
    }

    private static String getHostname() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            log.warn("无法获取主机名: {}", e.getMessage());
            return "unknown";
        }
    }

    private static List<String> getMacAddresses() {
        List<String> macAddresses = new ArrayList<>();
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                if (networkInterface.isLoopback() || networkInterface.isVirtual() || !networkInterface.isUp()) {
                    continue;
                }
                byte[] mac = networkInterface.getHardwareAddress();
                if (mac != null) {
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < mac.length; i++) {
                        sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? ":" : ""));
                    }
                    macAddresses.add(sb.toString());
                }
            }
        } catch (SocketException e) {
            log.warn("无法获取MAC地址: {}", e.getMessage());
        }
        return macAddresses;
    }

    private static String getBiosUuid() {
        String osName = System.getProperty("os.name").toLowerCase();
        try {
            if (osName.contains("win")) {
                Process process = Runtime.getRuntime().exec("wmic csproduct get uuid");
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        line = line.trim();
                        if (!line.isEmpty() && !line.equalsIgnoreCase("uuid")) return line;
                    }
                }
            } else if (osName.contains("linux")) {
                Process process = Runtime.getRuntime().exec("dmidecode -s system-uuid");
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                    String line = reader.readLine();
                    if (line != null) {
                        line = line.trim();
                        if (!line.isEmpty() && !line.equalsIgnoreCase("not available") && !line.contains("Permission denied")) return line;
                    }
                }
                Process altProcess = Runtime.getRuntime().exec("cat /sys/class/dmi/id/product_uuid");
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(altProcess.getInputStream(), StandardCharsets.UTF_8))) {
                    String line = reader.readLine();
                    if (line != null) {
                        line = line.trim();
                        if (!line.isEmpty()) return line;
                    }
                }
            }
        } catch (IOException e) {
            log.warn("无法获取BIOS UUID: {}", e.getMessage());
        }
        return null;
    }

    private static String getSystemSerialNumber() {
         String osName = System.getProperty("os.name").toLowerCase();
        try {
            if (osName.contains("win")) {
                Process process = Runtime.getRuntime().exec("wmic bios get serialnumber");
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        line = line.trim();
                        if (!line.isEmpty() && !line.equalsIgnoreCase("serialnumber")) return line;
                    }
                }
            } else if (osName.contains("linux")) {
                Process process = Runtime.getRuntime().exec("dmidecode -s system-serial-number");
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                    String line = reader.readLine();
                    if (line != null) {
                        line = line.trim();
                        if (!line.isEmpty() && !line.equalsIgnoreCase("not available") && !line.contains("Permission denied")) return line;
                    }
                }
                 Process altProcess = Runtime.getRuntime().exec("cat /sys/class/dmi/id/product_serial");
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(altProcess.getInputStream(), StandardCharsets.UTF_8))) {
                    String line = reader.readLine();
                    if (line != null) {
                        line = line.trim();
                        if (!line.isEmpty()) return line;
                    }
                }
            }
        } catch (IOException e) {
            log.warn("无法获取系统序列号: {}", e.getMessage());
        }
        return null;
    }

    private static Map<String, String> getCloudMetadata() {
        Map<String, String> metadata = new HashMap<>();
        // AWS
        try {
            URL awsMetadataUrl = new URL("http://169.254.169.254/latest/meta-data/instance-id");
            HttpURLConnection connection = (HttpURLConnection) awsMetadataUrl.openConnection();
            connection.setConnectTimeout(1000); connection.setReadTimeout(1000);
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                String instanceId = reader.readLine();
                if (instanceId != null && !instanceId.isEmpty()) metadata.put("aws_instance_id", instanceId);
            }
        } catch (Exception e) { /* ignore */ }
        // Azure
        try {
            URL azureMetadataUrl = new URL("http://169.254.169.254/metadata/instance/compute/vmId?api-version=2021-01-01&format=text");
            HttpURLConnection connection = (HttpURLConnection) azureMetadataUrl.openConnection();
            connection.setConnectTimeout(1000); connection.setReadTimeout(1000); connection.setRequestProperty("Metadata", "true");
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                String vmId = reader.readLine();
                if (vmId != null && !vmId.isEmpty()) metadata.put("azure_vm_id", vmId);
            }
        } catch (Exception e) { /* ignore */ }
        // GCP
        try {
            URL gcpMetadataUrl = new URL("http://metadata.google.internal/computeMetadata/v1/instance/id");
            HttpURLConnection connection = (HttpURLConnection) gcpMetadataUrl.openConnection();
            connection.setConnectTimeout(1000); connection.setReadTimeout(1000); connection.setRequestProperty("Metadata-Flavor", "Google");
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                String instanceId = reader.readLine();
                if (instanceId != null && !instanceId.isEmpty()) metadata.put("gcp_instance_id", instanceId);
            }
        } catch (Exception e) { /* ignore */ }
        return metadata;
    }

    private static String calculateFingerprintHash(Map<String, Object> fingerprints) {
        try {
            Map<String, Object> hashSource = new HashMap<>();
            if (fingerprints.containsKey("bios_uuid")) hashSource.put("bios_uuid", fingerprints.get("bios_uuid"));
            if (fingerprints.containsKey("system_serial")) hashSource.put("system_serial", fingerprints.get("system_serial"));
            if (fingerprints.containsKey("mac_addresses")) hashSource.put("mac_addresses", fingerprints.get("mac_addresses"));
            if (fingerprints.containsKey("hostname")) hashSource.put("hostname", fingerprints.get("hostname"));
            if (fingerprints.containsKey("cloud_metadata")) hashSource.put("cloud_metadata", fingerprints.get("cloud_metadata"));
            String json = objectMapper.writeValueAsString(hashSource);
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(json.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            log.warn("计算特征码指纹哈希失败: {}", e.getMessage());
            return UUID.randomUUID().toString().replace("-", "");
        }
    }

    private static String maskSensitiveInfo(Map<String, Object> fingerprints) {
        Map<String, Object> maskedFingerprints = new HashMap<>(fingerprints);
        if (maskedFingerprints.containsKey("bios_uuid")) maskedFingerprints.put("bios_uuid", maskString((String) maskedFingerprints.get("bios_uuid")));
        if (maskedFingerprints.containsKey("system_serial")) maskedFingerprints.put("system_serial", maskString((String) maskedFingerprints.get("system_serial")));
        if (maskedFingerprints.containsKey("mac_addresses")) {
            List<String> maskedMacs = ((List<String>) maskedFingerprints.get("mac_addresses")).stream().map(MachineFingerprints::maskString).collect(Collectors.toList());
            maskedFingerprints.put("mac_addresses", maskedMacs);
        }
        if (maskedFingerprints.containsKey("cloud_metadata")) {
            Map<String, String> maskedCloud = ((Map<String, String>) maskedFingerprints.get("cloud_metadata")).entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> maskString(e.getValue())));
            maskedFingerprints.put("cloud_metadata", maskedCloud);
        }
        try {
            return objectMapper.writeValueAsString(maskedFingerprints);
        } catch (Exception e) {
            return "无法序列化掩盖后的特征码: " + e.getMessage();
        }
    }

    private static String maskString(String input) {
        if (input == null || input.length() <= 8) return input;
        return input.substring(0, 4) + "****" + input.substring(input.length() - 4);
    }
}
```

#### 2.2.4 `UidValidationUtils` 工具类
在`org.xkong.cloud.commons.uid.util`包中创建`UidValidationUtils`工具类。

```java
package org.xkong.cloud.commons.uid.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * UID库验证工具类
 */
public class UidValidationUtils {
    private static final Logger log = LoggerFactory.getLogger(UidValidationUtils.class);
    private UidValidationUtils() { throw new IllegalStateException("工具类不应该被实例化"); }

    public static void validateDatabaseConnection(JdbcTemplate jdbcTemplate) {
        if (jdbcTemplate == null) throw new IllegalArgumentException("jdbcTemplate不能为空");
        try {
            jdbcTemplate.queryForObject("SELECT 1", Integer.class);
            log.info("数据库连接验证通过");
        } catch (DataAccessException e) {
            log.error("数据库连接验证失败: {}", e.getMessage());
            throw new IllegalStateException("数据库连接不可用", e);
        }
    }

    public static void validateSchemaExists(JdbcTemplate jdbcTemplate, String schemaName) {
        if (jdbcTemplate == null) throw new IllegalArgumentException("jdbcTemplate不能为空");
        if (schemaName == null || schemaName.trim().isEmpty()) throw new IllegalArgumentException("schemaName不能为空");
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, schemaName);
            if (count == null || count == 0) {
                log.error("Schema验证失败: Schema {} 不存在", schemaName);
                throw new IllegalStateException("Schema " + schemaName + " 不存在");
            }
            log.info("Schema {} 验证通过", schemaName);
        } catch (DataAccessException e) {
            log.error("Schema验证失败: {}", e.getMessage());
            throw new IllegalStateException("无法验证Schema " + schemaName + " 是否存在", e);
        }
    }

    public static void validateTableExists(JdbcTemplate jdbcTemplate, String schemaName, String tableName) {
        if (jdbcTemplate == null) throw new IllegalArgumentException("jdbcTemplate不能为空");
        if (schemaName == null || schemaName.trim().isEmpty()) throw new IllegalArgumentException("schemaName不能为空");
        if (tableName == null || tableName.trim().isEmpty()) throw new IllegalArgumentException("tableName不能为空");
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ? AND table_name = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, schemaName, tableName);
            if (count == null || count == 0) {
                log.error("表验证失败: 表 {}.{} 不存在", schemaName, tableName);
                throw new IllegalStateException("表 " + schemaName + "." + tableName + " 不存在");
            }
            log.info("表 {}.{} 验证通过", schemaName, tableName);
        } catch (DataAccessException e) {
            log.error("表验证失败: {}", e.getMessage());
            throw new IllegalStateException("无法验证表 " + schemaName + "." + tableName + " 是否存在", e);
        }
    }

    public static void validateTableStructure(JdbcTemplate jdbcTemplate, String schemaName, String tableName, List<String> requiredColumns) {
        if (jdbcTemplate == null) throw new IllegalArgumentException("jdbcTemplate不能为空");
        if (schemaName == null || schemaName.trim().isEmpty()) throw new IllegalArgumentException("schemaName不能为空");
        if (tableName == null || tableName.trim().isEmpty()) throw new IllegalArgumentException("tableName不能为空");
        if (requiredColumns == null || requiredColumns.isEmpty()) throw new IllegalArgumentException("requiredColumns不能为空");
        try {
            validateTableExists(jdbcTemplate, schemaName, tableName);
            String sql = "SELECT column_name FROM information_schema.columns WHERE table_schema = ? AND table_name = ?";
            List<String> existingColumns = jdbcTemplate.queryForList(sql, String.class, schemaName, tableName);
            for (String requiredColumn : requiredColumns) {
                if (!existingColumns.contains(requiredColumn.toLowerCase())) { // Ensure case-insensitivity for column names
                    log.error("表结构验证失败: 表 {}.{} 缺少必需的列 {}", schemaName, tableName, requiredColumn);
                    throw new IllegalStateException("表 " + schemaName + "." + tableName + " 缺少必需的列 " + requiredColumn);
                }
            }
            log.info("表 {}.{} 结构验证通过", schemaName, tableName);
        } catch (DataAccessException e) {
            log.error("表结构验证失败: {}", e.getMessage());
            throw new IllegalStateException("无法验证表 " + schemaName + "." + tableName + " 的结构", e);
        }
    }
}
```

#### 2.2.5 `KeyManagementService` 类
在`org.xkong.cloud.commons.uid`包中创建`KeyManagementService`类，用于管理加密密钥。

```java
package org.xkong.cloud.commons.uid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.support.TransactionTemplate;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 密钥管理服务
 */
public class KeyManagementService {
    private static final Logger log = LoggerFactory.getLogger(KeyManagementService.class);
    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;
    private final String applicationName;
    private final String environment;
    private final boolean encryptionEnabled;
    private final Map<String, String> keyCache = new HashMap<>();

    public KeyManagementService(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate,
                               String applicationName, String environment, boolean encryptionEnabled) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
        this.applicationName = applicationName;
        this.environment = environment;
        this.encryptionEnabled = encryptionEnabled;
        if (encryptionEnabled) log.info("实例ID文件加密已启用，应用: {}, 环境: {}", applicationName, environment);
        else log.info("实例ID文件加密未启用，应用: {}, 环境: {}", applicationName, environment);
    }

    public boolean isEncryptionEnabled() {
        return encryptionEnabled;
    }

    public String getEncryptionKey(String keyType) {
        if (!encryptionEnabled) throw new IllegalStateException("加密未启用，无法获取密钥");
        String cacheKey = applicationName + ":" + environment + ":" + keyType;
        synchronized (keyCache) {
            if (keyCache.containsKey(cacheKey)) return keyCache.get(cacheKey);
        }
        String key = transactionTemplate.execute(status -> {
            try {
                String sql = "SELECT encryption_key FROM infra_uid.encryption_key " +
                             "WHERE application_name = ? AND environment = ? AND key_type = ?";
                String existingKey = jdbcTemplate.query(sql, (rs, rowNum) -> rs.getString("encryption_key"),
                                                       applicationName, environment, keyType)
                                                 .stream().findFirst().orElse(null);
                if (existingKey != null) return existingKey;
                String newKey = generateEncryptionKey();
                String insertSql = "INSERT INTO infra_uid.encryption_key " +
                                   "(application_name, environment, key_type, encryption_key, created_at) " +
                                   "VALUES (?, ?, ?, ?, NOW())";
                jdbcTemplate.update(insertSql, applicationName, environment, keyType, newKey);
                log.info("已为应用 {} 环境 {} 创建新的 {} 类型密钥", applicationName, environment, keyType);
                return newKey;
            } catch (Exception e) {
                log.error("获取加密密钥失败: {}", e.getMessage(), e);
                status.setRollbackOnly();
                throw new RuntimeException("获取加密密钥失败", e);
            }
        });
        synchronized (keyCache) {
            keyCache.put(cacheKey, key);
        }
        return key;
    }

    private String generateEncryptionKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
            keyGenerator.init(256, SecureRandom.getInstanceStrong());
            SecretKey secretKey = keyGenerator.generateKey();
            return Base64.getEncoder().encodeToString(secretKey.getEncoded());
        } catch (NoSuchAlgorithmException e) {
            log.error("生成加密密钥失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成加密密钥失败", e);
        }
    }

    public void clearKeyCache() {
        synchronized (keyCache) {
            keyCache.clear();
        }
        log.info("已清除密钥缓存");
    }
}
```

#### 2.2.6 `PersistentInstanceManagerBuilder` 类
在`org.xkong.cloud.commons.uid`包中创建`PersistentInstanceManagerBuilder`类。

```java
package org.xkong.cloud.commons.uid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 持久化实例管理器构建器
 */
public class PersistentInstanceManagerBuilder {
    private static final Logger log = LoggerFactory.getLogger(PersistentInstanceManagerBuilder.class);
    private JdbcTemplate jdbcTemplate;
    private TransactionTemplate transactionTemplate;
    private String applicationName;
    private String environment;
    private String instanceGroup;
    private String localStoragePath;
    private Boolean recoveryEnabled;
    private Integer highConfidenceThreshold;
    private Integer minimumAcceptableScore;
    private String recoveryStrategy;
    private Integer recoveryTimeoutSeconds;
    private Boolean encryptionEnabled;
    private String schemaName;
    private Long instanceIdOverride;
    private KeyManagementService keyManagementService;

    public PersistentInstanceManagerBuilder withJdbcTemplate(JdbcTemplate jdbcTemplate) { this.jdbcTemplate = jdbcTemplate; return this; }
    public PersistentInstanceManagerBuilder withTransactionTemplate(TransactionTemplate transactionTemplate) { this.transactionTemplate = transactionTemplate; return this; }
    public PersistentInstanceManagerBuilder withApplicationName(String applicationName) { this.applicationName = applicationName; return this; }
    public PersistentInstanceManagerBuilder withEnvironment(String environment) { this.environment = environment; return this; }
    public PersistentInstanceManagerBuilder withInstanceGroup(String instanceGroup) { this.instanceGroup = instanceGroup; return this; }
    public PersistentInstanceManagerBuilder withLocalStoragePath(String localStoragePath) { this.localStoragePath = localStoragePath; return this; }
    public PersistentInstanceManagerBuilder withRecoveryEnabled(boolean recoveryEnabled) { this.recoveryEnabled = recoveryEnabled; return this; }
    public PersistentInstanceManagerBuilder withHighConfidenceThreshold(int highConfidenceThreshold) { this.highConfidenceThreshold = highConfidenceThreshold; return this; }
    public PersistentInstanceManagerBuilder withMinimumAcceptableScore(int minimumAcceptableScore) { this.minimumAcceptableScore = minimumAcceptableScore; return this; }
    public PersistentInstanceManagerBuilder withRecoveryStrategy(String recoveryStrategy) { this.recoveryStrategy = recoveryStrategy; return this; }
    public PersistentInstanceManagerBuilder withRecoveryTimeoutSeconds(int recoveryTimeoutSeconds) { this.recoveryTimeoutSeconds = recoveryTimeoutSeconds; return this; }
    public PersistentInstanceManagerBuilder withInstanceIdOverride(Long instanceIdOverride) { this.instanceIdOverride = instanceIdOverride; return this; }
    public PersistentInstanceManagerBuilder withEncryptionEnabled(boolean encryptionEnabled) { this.encryptionEnabled = encryptionEnabled; return this; }
    public PersistentInstanceManagerBuilder withSchemaName(String schemaName) { this.schemaName = schemaName; return this; }
    public PersistentInstanceManagerBuilder withKeyManagementService(KeyManagementService keyManagementService) { this.keyManagementService = keyManagementService; return this; }

    public PersistentInstanceManager build() {
        if (jdbcTemplate == null) throw new IllegalStateException("jdbcTemplate不能为空");
        if (transactionTemplate == null) throw new IllegalStateException("transactionTemplate不能为空");
        if (applicationName == null || applicationName.trim().isEmpty()) throw new IllegalStateException("applicationName不能为空");
        if (environment == null || environment.trim().isEmpty()) throw new IllegalStateException("environment不能为空");
        if (instanceGroup == null || instanceGroup.trim().isEmpty()) throw new IllegalStateException("instanceGroup不能为空");
        if (localStoragePath == null || localStoragePath.trim().isEmpty()) throw new IllegalStateException("localStoragePath不能为空");
        if (recoveryEnabled == null) throw new IllegalStateException("recoveryEnabled不能为空");
        if (highConfidenceThreshold == null) throw new IllegalStateException("highConfidenceThreshold不能为空");
        if (minimumAcceptableScore == null) throw new IllegalStateException("minimumAcceptableScore不能为空");
        if (recoveryStrategy == null || recoveryStrategy.trim().isEmpty()) throw new IllegalStateException("recoveryStrategy不能为空");
        if (recoveryTimeoutSeconds == null) throw new IllegalStateException("recoveryTimeoutSeconds不能为空");
        if (encryptionEnabled == null) throw new IllegalStateException("encryptionEnabled不能为空");
        if (schemaName == null || schemaName.trim().isEmpty()) throw new IllegalStateException("schemaName不能为空");
        if (encryptionEnabled && keyManagementService == null) throw new IllegalStateException("启用加密时keyManagementService不能为空");

        if (!PersistentInstanceManager.RECOVERY_STRATEGY_ALERT_AND_NEW.equals(recoveryStrategy) &&
            !PersistentInstanceManager.RECOVERY_STRATEGY_ALERT_AND_MANUAL.equals(recoveryStrategy) &&
            !PersistentInstanceManager.RECOVERY_STRATEGY_ALERT_AUTO_WITH_TIMEOUT.equals(recoveryStrategy)) {
            throw new IllegalStateException("无效的恢复策略: " + recoveryStrategy);
        }

        return new PersistentInstanceManager(jdbcTemplate, transactionTemplate, applicationName, environment, instanceGroup,
                                           localStoragePath, recoveryEnabled, highConfidenceThreshold, minimumAcceptableScore,
                                           recoveryStrategy, recoveryTimeoutSeconds, instanceIdOverride, encryptionEnabled,
                                           schemaName, keyManagementService);
    }
}
```

#### 2.2.7 `PersistentInstanceManager` 类
在`org.xkong.cloud.commons.uid`包中创建`PersistentInstanceManager`类。

```java
package org.xkong.cloud.commons.uid;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.support.TransactionTemplate;
// import org.xkong.xkongkit.utils.EncryptionUtils; // Assuming this utility exists or will be created

import java.io.IOException;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 持久化实例管理器
 */
public class PersistentInstanceManager {
    private static final Logger log = LoggerFactory.getLogger(PersistentInstanceManager.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final AtomicLong instanceUniqueId = new AtomicLong(-1);
    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;
    private final KeyManagementService keyManagementService;
    private final String applicationName;
    private final String environment;
    private final String instanceGroup;
    private final String localStoragePath;
    private final boolean recoveryEnabled;
    private final int highConfidenceThreshold;
    private final int minimumAcceptableScore;
    private final String recoveryStrategy;
    private final int recoveryTimeoutSeconds;
    private final Long instanceIdOverride;
    private final boolean encryptionEnabled;
    private final String schemaName;

    public static final String RECOVERY_STRATEGY_ALERT_AND_NEW = "ALERT_AND_NEW";
    public static final String RECOVERY_STRATEGY_ALERT_AND_MANUAL = "ALERT_AND_MANUAL";
    public static final String RECOVERY_STRATEGY_ALERT_AUTO_WITH_TIMEOUT = "ALERT_AUTO_WITH_TIMEOUT";

    // Placeholder for EncryptionUtils - replace with actual or mock implementation
    private static class EncryptionUtils {
        public static String encrypt(String data, String key) { return "encrypted:" + data; }
        public static String decrypt(String data, String key) { return data.startsWith("encrypted:") ? data.substring("encrypted:".length()) : data; }
    }


    public PersistentInstanceManager(
            JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate, String applicationName, String environment,
            String instanceGroup, String localStoragePath, boolean recoveryEnabled, int highConfidenceThreshold,
            int minimumAcceptableScore, String recoveryStrategy, int recoveryTimeoutSeconds, Long instanceIdOverride,
            boolean encryptionEnabled, String schemaName, KeyManagementService keyManagementService) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
        this.applicationName = applicationName;
        this.environment = environment;
        this.instanceGroup = instanceGroup;
        this.localStoragePath = localStoragePath;
        this.recoveryEnabled = recoveryEnabled;
        this.highConfidenceThreshold = highConfidenceThreshold;
        this.minimumAcceptableScore = minimumAcceptableScore;
        this.recoveryStrategy = recoveryStrategy;
        this.recoveryTimeoutSeconds = recoveryTimeoutSeconds;
        this.instanceIdOverride = instanceIdOverride;
        this.encryptionEnabled = encryptionEnabled;
        this.schemaName = schemaName;
        this.keyManagementService = keyManagementService;
        validateRecoveryStrategy(recoveryStrategy);
        initializeInstanceId();
    }

    private void validateRecoveryStrategy(String strategy) {
        if (strategy == null || (!RECOVERY_STRATEGY_ALERT_AND_NEW.equals(strategy) &&
            !RECOVERY_STRATEGY_ALERT_AND_MANUAL.equals(strategy) &&
            !RECOVERY_STRATEGY_ALERT_AUTO_WITH_TIMEOUT.equals(strategy))) {
            throw new IllegalArgumentException("无效的恢复策略: " + strategy);
        }
    }

    public long getInstanceId() {
        long id = instanceUniqueId.get();
        if (id <= 0) throw new IllegalStateException("实例ID尚未初始化");
        return id;
    }

    private void initializeInstanceId() {
        if (instanceIdOverride != null) {
            log.warn("使用指定的实例ID覆盖值: {}", instanceIdOverride);
            instanceUniqueId.set(instanceIdOverride);
            if (!checkInstanceExists(instanceIdOverride)) {
                log.warn("指定的实例ID覆盖值 {} 在数据库中不存在，将注册新实例", instanceIdOverride);
                registerInstance(instanceIdOverride);
            } else {
                log.info("指定的实例ID覆盖值 {} 在数据库中存在，更新实例信息", instanceIdOverride);
                updateInstanceLastSeen(instanceIdOverride);
            }
            saveInstanceIdToFile(instanceIdOverride);
            return;
        }

        Long loadedId = loadInstanceIdFromFile();
        if (loadedId != null) {
            log.info("从本地文件加载实例ID: {}", loadedId);
            if (checkInstanceExists(loadedId)) {
                log.info("实例ID {} 在数据库中存在，更新实例信息", loadedId);
                instanceUniqueId.set(loadedId);
                updateInstanceLastSeen(loadedId);
                return;
            } else {
                log.warn("实例ID {} 在数据库中不存在", loadedId);
            }
        }

        if (recoveryEnabled) {
            log.info("尝试基于机器特征码恢复实例ID");
            Long recoveredId = recoverInstanceId();
            if (recoveredId != null) {
                log.info("成功恢复实例ID: {}", recoveredId);
                instanceUniqueId.set(recoveredId);
                updateInstanceLastSeen(recoveredId);
                saveInstanceIdToFile(recoveredId);
                return;
            }
        } else {
            log.info("实例ID恢复功能已禁用");
        }

        log.info("注册新实例");
        Long newId = registerNewInstance();
        instanceUniqueId.set(newId);
        saveInstanceIdToFile(newId);
    }

    private Long loadInstanceIdFromFile() {
        Path path = Paths.get(localStoragePath);
        if (!Files.exists(path)) {
            log.info("实例ID文件不存在: {}", path);
            return null;
        }
        try (FileChannel channel = FileChannel.open(path, StandardOpenOption.READ);
             FileLock lock = channel.lock(0L, Long.MAX_VALUE, true)) {
            byte[] bytes = Files.readAllBytes(path);
            String content = new String(bytes, StandardCharsets.UTF_8);
            if (content.startsWith("{") && content.endsWith("}")) { // JSON
                Map<String, Object> data = objectMapper.readValue(content, Map.class);
                return Long.parseLong(data.get("instance_id").toString());
            } else if (encryptionEnabled && keyManagementService != null) {
                try {
                    String key = keyManagementService.getEncryptionKey("instance_id");
                    String decrypted = EncryptionUtils.decrypt(content, key);
                    Map<String, Object> data = objectMapper.readValue(decrypted, Map.class);
                    return Long.parseLong(data.get("instance_id").toString());
                } catch (Exception e) {
                    log.warn("解密实例ID文件失败: {}", e.getMessage());
                    try { return Long.parseLong(content.trim()); } // Fallback to plain text
                    catch (NumberFormatException nfe) { log.error("无法解析实例ID文件内容: {}", content); return null; }
                }
            } else {
                 try { return Long.parseLong(content.trim()); }
                 catch (NumberFormatException e) { log.error("无法解析实例ID文件内容: {}", content); return null; }
            }
        } catch (IOException e) {
            log.error("读取实例ID文件失败: {}", e.getMessage(), e);
        }
        return null;
    }

    private void saveInstanceIdToFile(Long instanceId) {
        Path path = Paths.get(localStoragePath);
        Path tempPath = Paths.get(localStoragePath + ".tmp");
        try {
            Path parent = path.getParent();
            if (parent != null && !Files.exists(parent)) Files.createDirectories(parent);
            Map<String, Object> data = new HashMap<>();
            data.put("instance_id", instanceId);
            data.put("application_name", applicationName);
            data.put("environment", environment);
            data.put("instance_group", instanceGroup);
            data.put("schema_name", schemaName);
            data.put("saved_at", LocalDateTime.now().toString());
            String json = objectMapper.writeValueAsString(data);
            Files.deleteIfExists(tempPath);
            try (FileChannel channel = FileChannel.open(tempPath, StandardOpenOption.CREATE, StandardOpenOption.WRITE, StandardOpenOption.TRUNCATE_EXISTING);
                 FileLock lock = channel.lock()) {
                String contentToSave = json;
                if (encryptionEnabled && keyManagementService != null) {
                    String key = keyManagementService.getEncryptionKey("instance_id");
                    contentToSave = EncryptionUtils.encrypt(json, key);
                }
                Files.write(tempPath, contentToSave.getBytes(StandardCharsets.UTF_8), StandardOpenOption.TRUNCATE_EXISTING);
            }
            Files.move(tempPath, path, java.nio.file.StandardCopyOption.REPLACE_EXISTING, java.nio.file.StandardCopyOption.ATOMIC_MOVE);
            log.info("已将实例ID {} 保存到文件: {}", instanceId, path);
        } catch (IOException e) {
            log.error("保存实例ID到文件失败: {}", e.getMessage(), e);
            try { Files.deleteIfExists(tempPath); } catch (IOException ex) { log.warn("清理临时文件失败: {}", ex.getMessage()); }
        }
    }

    private boolean checkInstanceExists(Long instanceId) {
        String sql = "SELECT COUNT(*) FROM " + schemaName + ".instance_registry WHERE instance_unique_id = ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, instanceId);
        return count != null && count > 0;
    }

    private void updateInstanceLastSeen(Long instanceId) {
        String sql = "UPDATE " + schemaName + ".instance_registry SET last_seen_at = NOW(), status = 'ACTIVE' WHERE instance_unique_id = ?";
        jdbcTemplate.update(sql, instanceId);
    }

    private void registerInstance(Long instanceId) {
        transactionTemplate.execute(status -> {
            try {
                Map<String, Object> fingerprints = MachineFingerprints.getFingerprints();
                String fingerprintsJson = objectMapper.writeValueAsString(fingerprints);
                String sql = "INSERT INTO " + schemaName + ".instance_registry " +
                             "(instance_unique_id, application_name, environment, instance_group, status, " +
                             "first_registered_at, last_seen_at, custom_metadata) " +
                             "VALUES (?, ?, ?, ?, 'ACTIVE', NOW(), NOW(), ?::jsonb)";
                jdbcTemplate.update(sql, instanceId, applicationName, environment, instanceGroup, fingerprintsJson);
                log.info("已注册指定ID的实例: {}", instanceId);
                return null;
            } catch (Exception e) {
                log.error("注册指定ID的实例失败: {}", e.getMessage(), e);
                status.setRollbackOnly();
                throw new RuntimeException("注册指定ID的实例失败", e);
            }
        });
    }

    private Long registerNewInstance() {
        return transactionTemplate.execute(status -> {
            try {
                Map<String, Object> fingerprints = MachineFingerprints.getFingerprints();
                String fingerprintsJson = objectMapper.writeValueAsString(fingerprints);
                String sql = "INSERT INTO " + schemaName + ".instance_registry " +
                             "(application_name, environment, instance_group, status, " +
                             "first_registered_at, last_seen_at, custom_metadata) " +
                             "VALUES (?, ?, ?, 'ACTIVE', NOW(), NOW(), ?::jsonb) " +
                             "RETURNING instance_unique_id";
                Long newId = jdbcTemplate.queryForObject(sql, Long.class, applicationName, environment, instanceGroup, fingerprintsJson);
                log.info("已注册新实例，ID: {}", newId);
                return newId;
            } catch (Exception e) {
                log.error("注册新实例失败: {}", e.getMessage(), e);
                status.setRollbackOnly();
                throw new RuntimeException("注册新实例失败", e);
            }
        });
    }

    private Long recoverInstanceId() {
        return transactionTemplate.execute(status -> {
            try {
                Map<String, Object> currentFingerprints = MachineFingerprints.getFingerprints();
                String sql = "SELECT instance_unique_id, custom_metadata FROM " + schemaName + ".instance_registry " +
                             "WHERE application_name = ? AND environment = ? AND status != 'DELETED'";
                List<Map<String, Object>> instances = jdbcTemplate.query(sql, (rs, rowNum) -> {
                    Map<String, Object> instance = new HashMap<>();
                    instance.put("instance_unique_id", rs.getLong("instance_unique_id"));
                    instance.put("custom_metadata", rs.getString("custom_metadata"));
                    return instance;
                }, applicationName, environment);

                if (instances.isEmpty()) { log.info("没有找到可恢复的实例"); return null; }

                Long bestMatchId = null; int bestMatchScore = 0;
                for (Map<String, Object> instance : instances) {
                    Long instanceId = (Long) instance.get("instance_unique_id");
                    String metadataJson = (String) instance.get("custom_metadata");
                    if (metadataJson != null && !metadataJson.isEmpty()) {
                        try {
                            Map<String, Object> storedFingerprints = objectMapper.readValue(metadataJson, Map.class);
                            int score = calculateMatchScore(currentFingerprints, storedFingerprints);
                            if (score > bestMatchScore) { bestMatchScore = score; bestMatchId = instanceId; }
                        } catch (Exception e) { log.warn("解析实例 {} 的特征码失败: {}", instanceId, e.getMessage()); }
                    }
                }

                if (bestMatchId == null) { log.info("没有找到匹配的实例"); return null; }
                log.info("最佳匹配实例ID: {}, 匹配分数: {}", bestMatchId, bestMatchScore);

                if (bestMatchScore >= highConfidenceThreshold) {
                    log.info("匹配分数高于高置信度阈值 {}, 自动恢复实例ID", highConfidenceThreshold); return bestMatchId;
                } else if (bestMatchScore >= minimumAcceptableScore) {
                    if (RECOVERY_STRATEGY_ALERT_AND_NEW.equals(recoveryStrategy)) {
                        log.warn("匹配分数 {} 低于高置信度阈值但高于最低可接受分数, 根据策略创建新实例", bestMatchScore); return null;
                    } else if (RECOVERY_STRATEGY_ALERT_AND_MANUAL.equals(recoveryStrategy)) {
                        log.warn("匹配分数 {} 低于高置信度阈值但高于最低可接受分数, 根据策略等待人工干预", bestMatchScore);
                        throw new IllegalStateException("需要人工干预恢复实例ID，请设置uid.instance.override参数");
                    } else if (RECOVERY_STRATEGY_ALERT_AUTO_WITH_TIMEOUT.equals(recoveryStrategy)) {
                        log.warn("匹配分数 {} 低于高置信度阈值但高于最低可接受分数, 根据策略等待 {} 秒后自动恢复", bestMatchScore, recoveryTimeoutSeconds);
                        try { Thread.sleep(1000); log.info("等待超时，自动恢复实例ID: {}", bestMatchId); return bestMatchId; } // Simplified wait
                        catch (InterruptedException e) { Thread.currentThread().interrupt(); log.warn("等待超时被中断"); }
                    }
                } else {
                    log.warn("匹配分数 {} 低于最低可接受分数, 无法恢复实例ID", bestMatchScore);
                }
                return null;
            } catch (Exception e) {
                if (e instanceof IllegalStateException && e.getMessage().contains("需要人工干预")) throw e;
                log.error("恢复实例ID失败: {}", e.getMessage(), e); status.setRollbackOnly(); return null;
            }
        });
    }

    private int calculateMatchScore(Map<String, Object> currentFingerprints, Map<String, Object> storedFingerprints) {
        int totalScore = 0; int maxPossibleScore = 0;
        if (currentFingerprints.containsKey("bios_uuid") && storedFingerprints.containsKey("bios_uuid")) {
            maxPossibleScore += 50; if (currentFingerprints.get("bios_uuid").equals(storedFingerprints.get("bios_uuid"))) totalScore += 50;
        }
        if (currentFingerprints.containsKey("system_serial") && storedFingerprints.containsKey("system_serial")) {
            maxPossibleScore += 30; if (currentFingerprints.get("system_serial").equals(storedFingerprints.get("system_serial"))) totalScore += 30;
        }
        if (currentFingerprints.containsKey("mac_addresses") && storedFingerprints.containsKey("mac_addresses")) {
            maxPossibleScore += 20;
            List<String> currentMacs = (List<String>) currentFingerprints.get("mac_addresses");
            List<String> storedMacs = (List<String>) storedFingerprints.get("mac_addresses");
            if (!currentMacs.isEmpty() && !storedMacs.isEmpty()) {
                int intersectionSize = 0; for (String mac : currentMacs) if (storedMacs.contains(mac)) intersectionSize++;
                int unionSize = currentMacs.size() + storedMacs.size() - intersectionSize;
                if (unionSize > 0) totalScore += (int) (((double) intersectionSize / unionSize) * 20);
            }
        }
        if (currentFingerprints.containsKey("hostname") && storedFingerprints.containsKey("hostname")) {
            maxPossibleScore += 10; if (currentFingerprints.get("hostname").equals(storedFingerprints.get("hostname"))) totalScore += 10;
        }
        if (maxPossibleScore == 0) return 0;
        return (int) ((double) totalScore / maxPossibleScore * 100);
    }
}
```

#### 2.2.8 `PersistentInstanceWorkerIdAssigner` 类
在`org.xkong.cloud.commons.uid`包中创建`PersistentInstanceWorkerIdAssigner`类。

```java
package org.xkong.cloud.commons.uid;

import com.baidu.fsg.uid.worker.WorkerIdAssigner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 持久化实例Worker ID分配器
 */
public class PersistentInstanceWorkerIdAssigner implements WorkerIdAssigner {
    private static final Logger log = LoggerFactory.getLogger(PersistentInstanceWorkerIdAssigner.class);
    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;
    private final PersistentInstanceManager persistentInstanceManager;
    private final String schemaName;
    private final int leaseDurationSeconds;
    private final int leaseRenewalIntervalSeconds;
    private final AtomicLong workerId = new AtomicLong(-1);
    private final ScheduledExecutorService leaseRenewalScheduler;

    public PersistentInstanceWorkerIdAssigner(
            JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate,
            PersistentInstanceManager persistentInstanceManager, int leaseDurationSeconds, String schemaName) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
        this.persistentInstanceManager = persistentInstanceManager;
        this.leaseDurationSeconds = leaseDurationSeconds;
        this.leaseRenewalIntervalSeconds = leaseDurationSeconds / 3;
        this.schemaName = schemaName;
        this.leaseRenewalScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "worker-id-lease-renewal");
            thread.setDaemon(true);
            return thread;
        });
        initialize();
    }

    private void initialize() {
        leaseRenewalScheduler.scheduleAtFixedRate(this::renewLease, leaseRenewalIntervalSeconds, leaseRenewalIntervalSeconds, TimeUnit.SECONDS);
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                releaseWorkerId();
                leaseRenewalScheduler.shutdown();
                log.info("已释放工作机器ID: {}", workerId.get());
            } catch (Exception e) {
                log.warn("释放工作机器ID失败: {}", e.getMessage());
            }
        }));
    }

    @Override
    public long assignWorkerId() {
        if (workerId.get() >= 0) return workerId.get();
        long instanceId = persistentInstanceManager.getInstanceId();
        Long assignedWorkerId = transactionTemplate.execute(status -> {
            try {
                String querySql = "SELECT worker_id FROM " + schemaName + ".worker_id_assignment " +
                                  "WHERE instance_id = ? AND status = 'ACTIVE'";
                Long existingWorkerId = jdbcTemplate.query(querySql, (rs, rowNum) -> rs.getLong("worker_id"), instanceId)
                                                  .stream().findFirst().orElse(null);
                if (existingWorkerId != null) {
                    String updateSql = "UPDATE " + schemaName + ".worker_id_assignment " +
                                       "SET lease_expires_at = NOW() + INTERVAL '" + leaseDurationSeconds + " SECONDS', " +
                                       "last_renewed_at = NOW() " +
                                       "WHERE instance_id = ? AND worker_id = ?";
                    jdbcTemplate.update(updateSql, instanceId, existingWorkerId);
                    log.info("已更新工作机器ID租约: {}", existingWorkerId);
                    return existingWorkerId;
                }
                String findSql = "SELECT worker_id FROM " + schemaName + ".worker_id_assignment " +
                                 "WHERE status = 'AVAILABLE' ORDER BY worker_id LIMIT 1 FOR UPDATE SKIP LOCKED";
                Long availableWorkerId = jdbcTemplate.query(findSql, (rs, rowNum) -> rs.getLong("worker_id"))
                                                   .stream().findFirst().orElse(null);
                if (availableWorkerId == null) {
                    log.error("没有可用的工作机器ID");
                    throw new IllegalStateException("没有可用的工作机器ID");
                }
                String assignSql = "UPDATE " + schemaName + ".worker_id_assignment " +
                                   "SET instance_id = ?, status = 'ACTIVE', assigned_at = NOW(), last_renewed_at = NOW(), " +
                                   "lease_expires_at = NOW() + INTERVAL '" + leaseDurationSeconds + " SECONDS' " +
                                   "WHERE worker_id = ? AND status = 'AVAILABLE'";
                int updated = jdbcTemplate.update(assignSql, instanceId, availableWorkerId);
                if (updated == 0) {
                    log.warn("工作机器ID {} 已被其他实例分配，重试分配", availableWorkerId);
                    status.setRollbackOnly(); return null;
                }
                log.info("已分配工作机器ID: {}", availableWorkerId);
                return availableWorkerId;
            } catch (Exception e) {
                log.error("分配工作机器ID失败: {}", e.getMessage(), e);
                status.setRollbackOnly(); throw new RuntimeException("分配工作机器ID失败", e);
            }
        });
        if (assignedWorkerId == null) {
            log.info("重试分配工作机器ID"); return assignWorkerId();
        }
        workerId.set(assignedWorkerId);
        return assignedWorkerId;
    }

    private void renewLease() {
        long currentWorkerId = workerId.get();
        if (currentWorkerId < 0) return;
        long instanceId = persistentInstanceManager.getInstanceId();
        try {
            String sql = "UPDATE " + schemaName + ".worker_id_assignment " +
                         "SET lease_expires_at = NOW() + INTERVAL '" + leaseDurationSeconds + " SECONDS', " +
                         "last_renewed_at = NOW() " +
                         "WHERE instance_id = ? AND worker_id = ? AND status = 'ACTIVE'";
            int updated = jdbcTemplate.update(sql, instanceId, currentWorkerId);
            if (updated == 0) {
                log.warn("工作机器ID {} 租约续约失败，可能已过期或被释放", currentWorkerId);
                workerId.set(-1); assignWorkerId();
            } else {
                log.debug("工作机器ID {} 租约续约成功", currentWorkerId);
            }
        } catch (Exception e) {
            log.warn("工作机器ID {} 租约续约失败: {}", currentWorkerId, e.getMessage());
        }
    }

    private void releaseWorkerId() {
        long currentWorkerId = workerId.get();
        if (currentWorkerId < 0) return;
        long instanceId = persistentInstanceManager.getInstanceId();
        try {
            String sql = "UPDATE " + schemaName + ".worker_id_assignment " +
                         "SET instance_id = NULL, status = 'AVAILABLE', released_at = NOW(), lease_expires_at = NULL " +
                         "WHERE instance_id = ? AND worker_id = ? AND status = 'ACTIVE'";
            int updated = jdbcTemplate.update(sql, instanceId, currentWorkerId);
            if (updated == 0) log.warn("工作机器ID {} 释放失败", currentWorkerId);
            else { log.info("工作机器ID {} 释放成功", currentWorkerId); workerId.set(-1); }
        } catch (Exception e) {
            log.warn("工作机器ID {} 释放失败: {}", currentWorkerId, e.getMessage());
        }
    }

    public long getWorkerId() {
        long id = workerId.get();
        if (id < 0) throw new IllegalStateException("尚未分配工作机器ID");
        return id;
    }
}
```

#### 2.2.9 `UidTableManager` 工具类
在`org.xkong.cloud.commons.uid.util`包中创建`UidTableManager`工具类。

```java
package org.xkong.cloud.commons.uid.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * UID表管理工具类
 */
public class UidTableManager {
    private static final Logger log = LoggerFactory.getLogger(UidTableManager.class);
    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;
    private final String schemaName;

    public UidTableManager(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate, String schemaName) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
        this.schemaName = schemaName;
    }

    public boolean initInstanceRegistry() {
        return transactionTemplate.execute(status -> {
            try {
                if (isTableExists("instance_registry")) {
                    log.info("表 {}.instance_registry 已存在", schemaName); return true;
                }
                String createTableSql = "CREATE TABLE " + schemaName + ".instance_registry (" +
                                        "instance_unique_id BIGSERIAL PRIMARY KEY, application_name VARCHAR(100) NOT NULL, " +
                                        "environment VARCHAR(50) NOT NULL, instance_group VARCHAR(100) NOT NULL, " +
                                        "status VARCHAR(20) NOT NULL, first_registered_at TIMESTAMP NOT NULL, " +
                                        "last_seen_at TIMESTAMP NOT NULL, custom_metadata JSONB, " +
                                        "CONSTRAINT instance_registry_status_check CHECK (status IN ('ACTIVE', 'INACTIVE', 'DELETED')))";
                jdbcTemplate.execute(createTableSql);
                String createIndexSql = "CREATE INDEX idx_instance_registry_app_env_group ON " +
                                        schemaName + ".instance_registry(application_name, environment, instance_group)";
                jdbcTemplate.execute(createIndexSql);
                log.info("已成功创建表 {}.instance_registry", schemaName); return true;
            } catch (DataAccessException e) {
                log.error("初始化实例注册表失败: {}", e.getMessage(), e); status.setRollbackOnly(); return false;
            }
        });
    }

    public boolean initWorkerIdAssignments(long maxWorkerId) {
        return transactionTemplate.execute(status -> {
            try {
                if (isTableExists("worker_id_assignment")) {
                    log.info("表 {}.worker_id_assignment 已存在", schemaName); return true;
                }
                String createTableSql = "CREATE TABLE " + schemaName + ".worker_id_assignment (" +
                                        "worker_id BIGINT PRIMARY KEY, instance_id BIGINT, status VARCHAR(20) NOT NULL, " +
                                        "assigned_at TIMESTAMP, last_renewed_at TIMESTAMP, lease_expires_at TIMESTAMP, released_at TIMESTAMP, " +
                                        "CONSTRAINT worker_id_assignment_status_check CHECK (status IN ('AVAILABLE', 'ACTIVE', 'RESERVED')), " +
                                        "CONSTRAINT worker_id_assignment_instance_fk FOREIGN KEY (instance_id) " +
                                        "REFERENCES " + schemaName + ".instance_registry(instance_unique_id) ON DELETE SET NULL)";
                jdbcTemplate.execute(createTableSql);
                String createIndexSql = "CREATE INDEX idx_worker_id_assignment_instance_id ON " +
                                        schemaName + ".worker_id_assignment(instance_id)";
                jdbcTemplate.execute(createIndexSql);
                preAllocateWorkerIds(maxWorkerId);
                log.info("已成功创建表 {}.worker_id_assignment", schemaName); return true;
            } catch (DataAccessException e) {
                log.error("初始化工作机器ID分配表失败: {}", e.getMessage(), e); status.setRollbackOnly(); return false;
            }
        });
    }

    private void preAllocateWorkerIds(long maxWorkerId) {
        log.info("开始预分配工作机器ID，最大ID: {}", maxWorkerId);
        int batchSize = 1000; List<Object[]> batch = new ArrayList<>(batchSize);
        String insertSql = "INSERT INTO " + schemaName + ".worker_id_assignment (worker_id, status) VALUES (?, 'AVAILABLE')";
        for (long i = 0; i <= maxWorkerId; i++) {
            batch.add(new Object[]{i});
            if (batch.size() >= batchSize || i == maxWorkerId) {
                jdbcTemplate.batchUpdate(insertSql, batch);
                log.debug("已预分配工作机器ID批次，当前ID: {}", i); batch.clear();
            }
        }
        log.info("已成功预分配 {} 个工作机器ID", maxWorkerId + 1);
    }

    private boolean isTableExists(String tableName) {
        String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ? AND table_name = ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, schemaName, tableName);
        return count != null && count > 0;
    }

    public boolean validateAllTables() {
        try {
            UidValidationUtils.validateSchemaExists(jdbcTemplate, schemaName);
            UidValidationUtils.validateTableExists(jdbcTemplate, schemaName, "instance_registry");
            List<String> instanceRegistryCols = List.of("instance_unique_id", "application_name", "environment", "instance_group", "status", "first_registered_at", "last_seen_at", "custom_metadata");
            UidValidationUtils.validateTableStructure(jdbcTemplate, schemaName, "instance_registry", instanceRegistryCols);
            UidValidationUtils.validateTableExists(jdbcTemplate, schemaName, "worker_id_assignment");
            List<String> workerIdAssignmentCols = List.of("worker_id", "instance_id", "status", "assigned_at", "last_renewed_at", "lease_expires_at", "released_at");
            UidValidationUtils.validateTableStructure(jdbcTemplate, schemaName, "worker_id_assignment", workerIdAssignmentCols);
            log.info("所有UID相关表验证通过"); return true;
        } catch (Exception e) {
            log.error("验证UID相关表失败: {}", e.getMessage()); return false;
        }
    }
}
```

## 3. 配置与使用指南

### 3.1 配置参数说明

以下是UID库相关的配置参数说明，这些参数需要在应用的配置文件中设置。根据[xkongcloud-commons-uid公共库实施计划](../../../features/F004-CommonsUidLibrary-20250511/plan/implementation-plan.md)的要求，所有参数都必须配置，没有默认值。这确保了系统的稳定性和可预测性，避免了因默认值不适合特定环境而导致的问题。详细的设计原理和技术细节可参考[commons-uid-library-design.md](../../../features/F004-CommonsUidLibrary-20250511/design/commons-uid-library-design.md)和[postgresql-persistent-id-fingerprint-recovery.md](../../../features/F004-CommonsUidLibrary-20250511/design/postgresql-persistent-id-fingerprint-recovery.md)文档。

#### 3.1.1 基础配置参数

| 参数名                               | 说明                         | 是否必需 | 开发环境值   | 生产环境推荐值 |
| ------------------------------------ | ---------------------------- | -------- | ------------ | -------------- |
| `uid.schema.name`                    | UID相关表所在的Schema名称    | 是       | `infra_uid`  | `infra_uid`    |
| `uid.table.auto-create`              | 是否自动创建UID相关表        | 是       | `true`       | `false`        |
| `uid.worker-id.max`                  | 最大工作机器ID               | 是       | `262143`     | `262143`       |
| `uid.worker-id.lease-duration-seconds` | 工作机器ID租约持续时间（秒） | 是       | `3600`       | `3600`         |

#### 3.1.2 实例配置参数

| 参数名                            | 说明             | 是否必需 | 开发环境值              | 生产环境推荐值                 |
| --------------------------------- | ---------------- | -------- | ----------------------- | ------------------------------ |
| `uid.environment`                 | 环境名称         | 是       | `development`           | `production`                   |
| `uid.instance.group`              | 实例分组         | 是       | `default`               | 根据业务分组                   |
| `uid.instance.local-storage-path` | 本地存储路径     | 是       | `./data/uid/instance-id` | `/var/lib/xkong/uid/instance-id` |

#### 3.1.3 实例恢复配置参数

| 参数名                                          | 说明                 | 是否必需 | 开发环境值       | 生产环境推荐值   |
| ----------------------------------------------- | -------------------- | -------- | ---------------- | ---------------- |
| `uid.instance.recovery.enabled`                 | 是否启用实例恢复     | 是       | `true`           | `true`           |
| `uid.instance.recovery.high-confidence-threshold` | 高置信度阈值         | 是       | `80`             | `80`             |
| `uid.instance.recovery.minimum-acceptable-score`  | 最低可接受分数       | 是       | `50`             | `50`             |
| `uid.instance.recovery.strategy`                | 恢复策略             | 是       | `ALERT_AND_NEW`  | `ALERT_AND_NEW`  |
| `uid.instance.recovery.timeout-seconds`         | 恢复超时时间（秒）   | 是       | `60`             | `60`             |

#### 3.1.4 安全配置参数

| 参数名                              | 说明             | 是否必需 | 开发环境值 | 生产环境推荐值     |
| ----------------------------------- | ---------------- | -------- | ---------- | ------------------ |
| `uid.instance.encryption.enabled`   | 是否启用加密     | 是       | `false`    | `true`             |
| `uid.security.master-key`           | 主密钥           | 是       | 无         | 随机生成的强密钥   |
| `uid.security.key-rotation-days`    | 密钥轮换天数     | 是       | `90`       | `90`               |

#### 3.1.5 UID生成器配置参数

| 参数名                 | 说明                 | 是否必需 | 开发环境值 | 生产环境推荐值 |
| ---------------------- | -------------------- | -------- | ---------- | -------------- |
| `uid.time-bits`        | 时间位数             | 是       | `31`       | `31`           |
| `uid.worker-bits`      | 工作机器ID位数       | 是       | `18`       | `18`           |
| `uid.seq-bits`         | 序列号位数           | 是       | `14`       | `14`           |
| `uid.time-unit`        | 时间单位（秒）       | 是       | `1`        | `1`            |
| `uid.boost-power`      | 缓存提升倍数         | 是       | `3`        | `3`            |
| `uid.padding-factor`   | 填充因子             | 是       | `50`       | `50`           |

### 3.2 恢复策略说明

UID库支持以下三种恢复策略：

1.  **ALERT_AND_NEW**：当特征码匹配分数低于高置信度阈值但高于最低可接受分数时，记录警告日志并创建新实例。
2.  **ALERT_AND_MANUAL**：当特征码匹配分数低于高置信度阈值但高于最低可接受分数时，记录警告日志并等待人工干预。
3.  **ALERT_AUTO_WITH_TIMEOUT**：当特征码匹配分数低于高置信度阈值但高于最低可接受分数时，记录警告日志并等待一段时间后自动恢复。

### 3.3 使用建议

1.  **环境隔离**：不同环境使用不同的`uid.environment`值。
2.  **实例分组**：不同业务系统使用不同的`uid.instance.group`值。
3.  **安全考虑**：生产环境建议启用加密并使用强密钥。
4.  **恢复策略选择**：一般业务推荐`ALERT_AND_NEW`；核心业务可考虑`ALERT_AND_MANUAL`；不建议`ALERT_AUTO_WITH_TIMEOUT`。
5.  **监控**：监控UID生成器运行状态。

### 3.4 外部调用者使用示例

以下是如何在Spring Boot应用中配置和使用UID库的示例：
(此部分引用原文档2.2.14节的 `UidGeneratorConfig` 类代码)
```java
package org.xkong.cloud.business.internal.core.config;

import com.baidu.fsg.uid.UidGenerator;
import com.baidu.fsg.uid.impl.CachedUidGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.support.TransactionTemplate;
import org.xkong.cloud.commons.uid.PersistentInstanceManager;
import org.xkong.cloud.commons.uid.PersistentInstanceManagerBuilder;
import org.xkong.cloud.commons.uid.PersistentInstanceWorkerIdAssigner;
import org.xkong.cloud.commons.uid.security.KeyManagementService; // Assuming this is the correct KeyManagementService
import org.xkong.cloud.commons.uid.util.UidTableManager;

/**
 * UID生成器配置类
 */
@Configuration
public class UidGeneratorConfig {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TransactionTemplate transactionTemplate;

    // Assuming KeyManagementService from org.xkong.cloud.commons.uid.security
    // If it's the one from org.xkong.cloud.commons.uid, adjust the import and potentially the bean creation
    @Autowired
    private KeyManagementService keyManagementService;


    @Value("${spring.application.name}")
    private String applicationName;

    @Value("${uid.environment}")
    private String environment;

    @Value("${uid.instance.group}")
    private String instanceGroup;

    @Value("${uid.instance.local-storage-path}")
    private String localStoragePath;

    @Value("${uid.instance.recovery.enabled}")
    private boolean recoveryEnabled;

    @Value("${uid.instance.recovery.high-confidence-threshold}")
    private int highConfidenceThreshold;

    @Value("${uid.instance.recovery.minimum-acceptable-score}")
    private int minimumAcceptableScore;

    @Value("${uid.instance.recovery.strategy}")
    private String recoveryStrategy;

    @Value("${uid.instance.recovery.timeout-seconds}")
    private int recoveryTimeoutSeconds;

    @Value("${uid.instance.encryption.enabled}")
    private boolean encryptionEnabled;

    @Value("${uid.schema.name}")
    private String schemaName;

    @Value("${uid.worker-id.lease-duration-seconds}")
    private int leaseDurationSeconds;

    /**
     * 创建验证结果缓存
     */
    @Bean
    public ValidationResultCache validationResultCache() {
        return new ValidationResultCache();
    }

    /**
     * 创建持久化实例管理器
     * 使用构建器模式创建PersistentInstanceManager实例
     * 所有参数必须配置，没有默认值
     */
    @Bean
    public PersistentInstanceManager persistentInstanceManager() {
        // 验证所有必需参数
        if (applicationName == null || applicationName.trim().isEmpty()) {
            throw new IllegalStateException("spring.application.name参数未配置");
        }
        if (environment == null || environment.trim().isEmpty()) {
            throw new IllegalStateException("uid.environment参数未配置");
        }
        if (instanceGroup == null || instanceGroup.trim().isEmpty()) {
            throw new IllegalStateException("uid.instance.group参数未配置");
        }
        if (localStoragePath == null || localStoragePath.trim().isEmpty()) {
            throw new IllegalStateException("uid.instance.local-storage-path参数未配置");
        }
        if (!PersistentInstanceManager.RECOVERY_STRATEGY_ALERT_AND_NEW.equals(recoveryStrategy) &&
            !PersistentInstanceManager.RECOVERY_STRATEGY_ALERT_AND_MANUAL.equals(recoveryStrategy) &&
            !PersistentInstanceManager.RECOVERY_STRATEGY_ALERT_AUTO_WITH_TIMEOUT.equals(recoveryStrategy)) {
            throw new IllegalStateException("uid.instance.recovery.strategy参数无效: " + recoveryStrategy);
        }
        if (schemaName == null || schemaName.trim().isEmpty()) {
            throw new IllegalStateException("uid.schema.name参数未配置");
        }
        if (encryptionEnabled && keyManagementService == null) {
            throw new IllegalStateException("启用加密时KeyManagementService不能为空");
        }

        // 确保使用正确类型的KeyManagementService
        org.xkong.cloud.commons.uid.KeyManagementService uidKeyManagementService =
            (keyManagementService instanceof org.xkong.cloud.commons.uid.KeyManagementService) ?
            (org.xkong.cloud.commons.uid.KeyManagementService)keyManagementService :
            new org.xkong.cloud.commons.uid.KeyManagementService(jdbcTemplate, transactionTemplate, applicationName, environment, encryptionEnabled);

        // 使用构建器模式创建PersistentInstanceManager实例
        return new PersistentInstanceManagerBuilder()
                .withJdbcTemplate(jdbcTemplate)
                .withTransactionTemplate(transactionTemplate)
                .withApplicationName(applicationName)
                .withEnvironment(environment)
                .withInstanceGroup(instanceGroup)
                .withLocalStoragePath(localStoragePath)
                .withRecoveryEnabled(recoveryEnabled)
                .withHighConfidenceThreshold(highConfidenceThreshold)
                .withMinimumAcceptableScore(minimumAcceptableScore)
                .withRecoveryStrategy(recoveryStrategy)
                .withRecoveryTimeoutSeconds(recoveryTimeoutSeconds)
                .withEncryptionEnabled(encryptionEnabled)
                .withSchemaName(schemaName)
                .withKeyManagementService(uidKeyManagementService)
                .build();
    }

    @Bean
    public PersistentInstanceWorkerIdAssigner persistentInstanceWorkerIdAssigner(
            PersistentInstanceManager persistentInstanceManager) {
        return new PersistentInstanceWorkerIdAssigner(
                jdbcTemplate,
                transactionTemplate,
                persistentInstanceManager,
                leaseDurationSeconds,
                schemaName);
    }

    @Bean
    public UidTableManager uidTableManager() {
        return new UidTableManager(jdbcTemplate, transactionTemplate, schemaName);
    }

    /**
     * 创建UID生成器
     * 所有参数必须配置，没有默认值
     */
    @Bean
    public UidGenerator uidGenerator(PersistentInstanceWorkerIdAssigner workerIdAssigner,
                                     @Value("${uid.time-bits}") int timeBits,
                                     @Value("${uid.worker-bits}") int workerBits,
                                     @Value("${uid.seq-bits}") int seqBits,
                                     @Value("${uid.time-unit}") int timeUnit,
                                     @Value("${uid.boost-power}") int boostPower,
                                     @Value("${uid.padding-factor}") int paddingFactor) {
        // 验证参数
        if (timeBits <= 0) {
            throw new IllegalStateException("uid.time-bits参数必须大于0");
        }
        if (workerBits <= 0) {
            throw new IllegalStateException("uid.worker-bits参数必须大于0");
        }
        if (seqBits <= 0) {
            throw new IllegalStateException("uid.seq-bits参数必须大于0");
        }
        if (timeUnit <= 0) {
            throw new IllegalStateException("uid.time-unit参数必须大于0");
        }
        if (boostPower < 0) {
            throw new IllegalStateException("uid.boost-power参数不能为负数");
        }
        if (paddingFactor <= 0 || paddingFactor >= 100) {
            throw new IllegalStateException("uid.padding-factor参数必须在(0, 100)范围内");
        }

        // 创建并配置CachedUidGenerator
        CachedUidGenerator cachedUidGenerator = new CachedUidGenerator();
        cachedUidGenerator.setWorkerIdAssigner(workerIdAssigner);
        cachedUidGenerator.setTimeBits(timeBits);
        cachedUidGenerator.setWorkerBits(workerBits);
        cachedUidGenerator.setSeqBits(seqBits);
        cachedUidGenerator.setTimeUnit(TimeUnit.SECONDS);
        cachedUidGenerator.setBoostPower(boostPower);
        cachedUidGenerator.setPaddingFactor(paddingFactor);

        return cachedUidGenerator;
    }
}
```

## 4. 数据库设计

### 4.1 数据库表结构

#### `infra_uid.instance_registry`
实例注册表，用于记录所有注册的实例信息。

| 列名                | 类型         | 说明                                   |
| ------------------- | ------------ | -------------------------------------- |
| instance_unique_id  | BIGSERIAL    | 实例唯一ID，主键                       |
| application_name    | VARCHAR(100) | 应用名称                               |
| environment         | VARCHAR(50)  | 环境名称                               |
| instance_group      | VARCHAR(100) | 实例分组                               |
| status              | VARCHAR(20)  | 实例状态，如ACTIVE、INACTIVE、DELETED |
| first_registered_at | TIMESTAMP    | 首次注册时间                           |
| last_seen_at        | TIMESTAMP    | 最后活跃时间                           |
| custom_metadata     | JSONB        | 自定义元数据，包含机器特征码等信息     |

#### `infra_uid.worker_id_assignment`
工作机器ID分配表，用于记录工作机器ID的分配情况。

| 列名                      | 类型        | 说明                                                       |
| ------------------------- | ----------- | ---------------------------------------------------------- |
| worker_id                 | BIGINT      | 工作机器ID，主键                                           |
| instance_id               | BIGINT      | 分配的实例唯一ID，外键，关联`instance_registry`            |
| status                    | VARCHAR(20) | 分配状态，如AVAILABLE、ACTIVE、RESERVED                    |
| assigned_at               | TIMESTAMP   | 分配时间                                                   |
| last_renewed_at           | TIMESTAMP   | 最后续约时间                                               |
| lease_expires_at          | TIMESTAMP   | 租约过期时间                                               |
| released_at               | TIMESTAMP   | 释放时间                                                   |

#### `infra_uid.encryption_key`
加密密钥表，用于存储实例ID文件加密密钥。

| 列名             | 类型         | 说明             |
| ---------------- | ------------ | ---------------- |
| id               | BIGSERIAL    | 主键             |
| application_name | VARCHAR(100) | 应用名称         |
| environment      | VARCHAR(50)  | 环境名称         |
| key_type         | VARCHAR(50)  | 密钥类型         |
| encryption_key   | TEXT         | 加密密钥         |
| created_at       | TIMESTAMP    | 创建时间         |

### 4.2 DDL脚本
DDL脚本位于 `xkongcloud-commons-uid/src/main/resources/sql/schema.sql`:
```sql
-- 创建Schema
CREATE SCHEMA IF NOT EXISTS infra_uid;

-- 实例注册表
CREATE TABLE IF NOT EXISTS infra_uid.instance_registry (
    instance_unique_id BIGSERIAL PRIMARY KEY,
    application_name VARCHAR(100) NOT NULL,
    environment VARCHAR(50) NOT NULL,
    instance_group VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL,
    first_registered_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_seen_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 0,
    custom_metadata JSONB,
    CONSTRAINT ck_instance_registry_status CHECK (status IN ('ACTIVE', 'INACTIVE', 'DELETED'))
);

-- 实例注册表索引
CREATE INDEX IF NOT EXISTS idx_instance_registry_app_env_group
ON infra_uid.instance_registry (application_name, environment, instance_group);

-- 工作机器ID分配表
CREATE TABLE IF NOT EXISTS infra_uid.worker_id_assignment (
    worker_id BIGINT PRIMARY KEY,
    instance_id BIGINT,
    status VARCHAR(20) NOT NULL,
    assigned_at TIMESTAMP WITH TIME ZONE,
    last_renewed_at TIMESTAMP WITH TIME ZONE,
    lease_expires_at TIMESTAMP WITH TIME ZONE,
    released_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 0,
    CONSTRAINT ck_worker_id_assignment_status CHECK (status IN ('AVAILABLE', 'ACTIVE', 'RESERVED')),
    CONSTRAINT fk_worker_id_assignment_instance_registry FOREIGN KEY (instance_id)
    REFERENCES infra_uid.instance_registry(instance_unique_id) ON DELETE SET NULL
);

-- 工作机器ID分配表索引
CREATE INDEX IF NOT EXISTS idx_worker_id_assignment_instance_id
ON infra_uid.worker_id_assignment (instance_id);

-- 加密密钥表
CREATE TABLE IF NOT EXISTS infra_uid.encryption_key (
    id BIGSERIAL PRIMARY KEY,
    application_name VARCHAR(100) NOT NULL,
    environment VARCHAR(50) NOT NULL,
    key_type VARCHAR(50) NOT NULL,
    encryption_key TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 0,
    CONSTRAINT uk_encryption_key_app_env_type UNIQUE(application_name, environment, key_type)
);

-- 加密密钥表索引
CREATE INDEX IF NOT EXISTS idx_encryption_key_app_env_type
ON infra_uid.encryption_key (application_name, environment, key_type);
```

## 5. 质量保证与风险管理

### 5.1 测试方法

#### 5.1.1 单元测试
为每个核心类编写单元测试，使用JUnit 5和Mockito框架，确保覆盖正常和异常场景。测试代码示例已在原文档中提供，此处不再赘述。目标测试覆盖率80%以上。

#### 5.1.2 集成测试
在有PostgreSQL数据库的环境中进行，验证与数据库的交互和多组件协同工作。建议使用TestContainers框架自动管理测试数据库环境。

#### 5.1.3 手动测试
构建模块后，在测试应用中引入依赖，配置并启动，观察日志，测试实例ID恢复、Worker ID分配和ID生成功能。

### 5.2 人工审查点

#### 5.2.1 业务风险点
*   **MachineFingerprints类**：容器环境中特征码不稳定性。
*   **PersistentInstanceManager类**：实例ID丢失风险。
*   **PersistentInstanceWorkerIdAssigner类**：Worker ID重复分配风险。
*   **KeyManagementService类**：数据库中密钥存储的安全性。

#### 5.2.2 性能考虑点
*   **MachineFingerprints类**：特征码收集可能较慢。
*   **PersistentInstanceManager类**：实例ID加载和恢复可能较慢。
*   **PersistentInstanceWorkerIdAssigner类**：租约续约的数据库压力。
*   **KeyManagementService类**：频繁查库获取密钥。

### 5.3 风险和缓解措施

| 风险                       | 可能性 | 影响 | 缓解措施                                                                                                                               |
| -------------------------- | ------ | ---- | -------------------------------------------------------------------------------------------------------------------------------------- |
| 实例ID恢复失败             | 中     | 高   | 多种恢复机制、详细日志、故障排除指南、充分测试。                                                                                         |
| 工作机器ID重复分配         | 低     | 高   | 健壮的租约机制、定期清理过期分配、启动时唯一性验证、监控告警。                                                                             |
| 生成的ID不唯一             | 低     | 高   | 确保Worker ID唯一、时钟回拨检测处理、高并发测试、ID冲突检测（若可能）。                                                                     |
| 性能问题                   | 中     | 中   | 代码优化、多级缓存、批量操作、性能测试、监控。                                                                                             |
| 容器环境兼容性问题         | 中     | 中   | 容器环境充分测试、提供特定配置选项、使用稳定标识、编写容器部署指南。                                                                       |
| 数据库迁移风险             | 中     | 高   | 详细迁移与回滚脚本、测试环境充分验证、详细迁移计划与应急预案、确保数据备份恢复。                                                           |

## 6. 实施计划

### 6.1 实施步骤

1.  创建`xkongcloud-commons`父模块和`xkongcloud-commons-uid`子模块。
2.  实现核心类，按照以下依赖顺序：
    - 基础类：`WorkerNodeType`, `ValidationResultCache`, `MachineFingerprints`
    - 工具类：`UidValidationUtils`
    - 服务类：`KeyManagementService`
    - 管理类：`PersistentInstanceManagerBuilder`, `PersistentInstanceManager`
    - 分配器和表管理器：`PersistentInstanceWorkerIdAssigner`, `UidTableManager`
3.  编写单元测试。
4.  编写文档 (README, DDL, 配置说明, 故障排除)。
5.  构建和发布。

### 6.2 时间安排

| 任务             | 预计时间 | 负责人   |
| ---------------- | -------- | -------- |
| 模块结构创建     | 1天      | 开发人员 |
| 核心类实现       | 5天      | 开发人员 |
| 单元测试编写     | 3天      | 开发人员 |
| 文档编写         | 2天      | 开发人员 |
| 代码审查         | 2天      | 审查人员 |
| 测试和修复       | 3天      | 测试人员 |
| 构建和发布       | 1天      | 开发人员 |
| **总计**         | **17天** |          |

## 7. 总结

### 7.1 主要成果

1.  **xkongcloud-commons-uid公共库**：功能完整、测试充分的分布式ID生成库。
2.  **持久化实例ID机制**：确保应用实例重启后恢复原有身份。
3.  **特征码恢复功能**：提高实例ID恢复的准确性。
4.  **工作机器ID分配机制**：确保每个实例使用唯一的工作机器ID。
5.  **完善的文档**：便于使用和维护。

### 7.2 业务价值

1.  **数据一致性**：通过全局唯一ID确保分布式环境数据一致性。
2.  **系统稳定性**：持久化实例ID和特征码恢复提高系统稳定性。
3.  **业务连续性**：保持ID生成连续性，避免业务中断。
4.  **扩展性支持**：为微服务架构提供基础支持。
5.  **性能优化**：优化的ID生成算法和缓存机制支持高并发。

### 7.3 后续步骤

1.  在`xkongcloud-business-internal-core`项目中集成`xkongcloud-commons-uid`。
2.  迁移现有ID生成逻辑。
3.  进行全面的系统测试。
4.  制定生产环境部署计划。
5.  培训运维人员。
