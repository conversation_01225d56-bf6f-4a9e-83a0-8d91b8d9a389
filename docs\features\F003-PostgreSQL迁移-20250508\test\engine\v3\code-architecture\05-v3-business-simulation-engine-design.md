# V3参数化业务推演引擎架构设计

**文档版本**: V3-PARAMETRIC-SIMULATION-ENGINE-CORE
**创建时间**: 2025年6月10日
**架构专家**: 顶级架构师
**核心目标**: 设计基于参数的弹性模拟引擎，通过JSON配置驱动真实业务代码执行

---

## 🎯 设计目标

### 架构目标
- **参数化引擎设计**：基于参数的通用模拟引擎，不绑定具体业务逻辑
- **真实代码注入机制**：通过参数注入调用任何真实业务Service
- **弹性配置系统**：参数弹性 + 执行规则弹性 + 场景编排弹性
- **通用推演框架**：支持任意业务场景的参数化推演

### 技术目标
- **零业务耦合**：引擎本身不包含任何具体业务逻辑
- **最大复用性**：一套引擎支持所有业务场景的参数化推演
- **动态扩展性**：通过JSON配置扩展新的参数类型和执行规则

## 🏗️ 核心架构设计

### V3参数化推演引擎总体架构
```java
/**
 * V3参数化业务推演引擎
 * 基于参数配置的通用模拟引擎，通过反射机制调用任意真实业务代码
 */
@Component
@NeuralUnit(layer = "V3", type = "PARAMETRIC_SIMULATION_ENGINE", description = "参数化通用推演引擎")
public class V3ParametricSimulationEngine {

    // 核心引擎组件（通用，不绑定具体业务）
    @Autowired
    private V3ParameterInjectionManager parameterInjectionManager;

    @Autowired
    private V3DynamicExecutionEngine dynamicExecutionEngine;

    @Autowired
    private V3FlexibleRuleEngine flexibleRuleEngine;

    @Autowired
    private V3ScenarioOrchestrationEngine scenarioOrchestrationEngine;

    // 复用V2统一管理系统
    @Autowired
    private UniversalReportOutputInterface reportOutput;
    
    /**
     * 执行参数化推演
     * 基于JSON参数配置驱动任意真实业务代码执行
     */
    public V3ParametricSimulationResult executeParametricSimulation(
            V3IntelligentAnalysis analysis,
            V3ParametricScenarioConfig scenarioConfig) {

        log.info("开始执行参数化推演，场景: {}", scenarioConfig.getScenarioId());

        try {
            // Step 1: 参数注入准备（解析JSON参数配置）
            V3ParameterContext parameterContext = parameterInjectionManager.prepareParameterContext(
                scenarioConfig.getParameterConfig());

            // Step 2: 动态规则加载（基于JSON规则配置）
            V3FlexibleRuleSet ruleSet = flexibleRuleEngine.loadFlexibleRules(
                scenarioConfig.getRuleConfig());

            // Step 3: 场景编排（基于JSON场景配置）
            V3OrchestrationPlan orchestrationPlan = scenarioOrchestrationEngine.createOrchestrationPlan(
                scenarioConfig, parameterContext, ruleSet);

            // Step 4: 执行参数化推演（通过反射调用真实业务代码）
            V3ParametricExecutionResult executionResult = dynamicExecutionEngine.executeParametricScenario(
                orchestrationPlan, parameterContext);

            // Step 5: 直接使用V2统一系统输出推演结果（无需转换）
            outputParametricSimulationResult(executionResult, scenarioConfig);

            return V3ParametricSimulationResult.builder()
                .scenarioId(scenarioConfig.getScenarioId())
                .parameterContext(parameterContext)
                .ruleSet(ruleSet)
                .executionResult(executionResult)
                .executionTimestamp(LocalDateTime.now())
                .build();

        } catch (Exception e) {
            log.error("参数化推演执行失败", e);
            return handleParametricSimulationFailure(scenarioConfig, e);
        }
    }
    
}
```

## 🔧 参数注入管理器

### 核心设计（伪代码）
```java
/**
 * V3参数注入管理器
 * 负责解析JSON参数配置，准备参数注入上下文
 */
@Component
public class V3ParameterInjectionManager {

    /**
     * 准备参数上下文
     * 解析JSON配置，构建参数注入上下文
     */
    public V3ParameterContext prepareParameterContext(V3ParameterConfig parameterConfig) {
        // 伪代码：解析参数配置
        V3ParameterContext context = new V3ParameterContext();

        // 解析角色参数
        context.setRoleParameters(parseRoleParameters(parameterConfig.getRoles()));

        // 解析业务参数
        context.setBusinessParameters(parseBusinessParameters(parameterConfig.getBusinessParams()));

        // 解析执行参数
        context.setExecutionParameters(parseExecutionParameters(parameterConfig.getExecutionParams()));

        return context;
    }

    /**
     * 动态注入参数到目标Service
     * 通过反射机制将参数注入到任意真实业务Service
     */
    public Object injectParametersToService(String serviceName, String methodName, V3ParameterSet parameters) {
        // 伪代码：通过反射调用真实业务Service
        Object serviceBean = applicationContext.getBean(serviceName);
        Method targetMethod = findTargetMethod(serviceBean, methodName, parameters);
        Object[] methodArgs = buildMethodArguments(targetMethod, parameters);

        return targetMethod.invoke(serviceBean, methodArgs);
    }
}
```

## ⚙️ 动态执行引擎

### 核心设计（伪代码）
```java
/**
 * V3动态执行引擎
 * 基于参数配置动态执行任意真实业务代码
 */
@Component
public class V3DynamicExecutionEngine {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private V3ParameterInjectionManager parameterInjectionManager;

    /**
     * 执行参数化场景
     * 通过反射机制调用任意真实业务Service
     */
    public V3ParametricExecutionResult executeParametricScenario(
            V3OrchestrationPlan orchestrationPlan,
            V3ParameterContext parameterContext) {

        V3ParametricExecutionResult result = new V3ParametricExecutionResult();

        // 伪代码：按编排计划执行各个阶段
        for (V3ExecutionPhase phase : orchestrationPlan.getPhases()) {
            V3PhaseExecutionResult phaseResult = executePhase(phase, parameterContext);
            result.addPhaseResult(phaseResult);
        }

        return result;
    }

    /**
     * 执行单个阶段
     * 根据参数配置动态调用真实业务方法
     */
    private V3PhaseExecutionResult executePhase(V3ExecutionPhase phase, V3ParameterContext parameterContext) {
        // 伪代码：动态执行
        V3PhaseExecutionResult phaseResult = new V3PhaseExecutionResult();

        for (V3ExecutionAction action : phase.getActions()) {
            // 从参数上下文获取执行参数
            V3ParameterSet actionParameters = parameterContext.getParametersForAction(action.getActionId());

            // 动态调用真实业务Service
            Object executionResult = parameterInjectionManager.injectParametersToService(
                action.getTargetService(),
                action.getTargetMethod(),
                actionParameters
            );

            phaseResult.addActionResult(action.getActionId(), executionResult);
        }

        return phaseResult;
    }
    }

}

## 🔀 弹性规则引擎

### 核心设计（伪代码）
```java
/**
 * V3弹性规则引擎
 * 基于JSON配置的通用规则执行引擎，不绑定具体业务逻辑
 */
@Component
public class V3FlexibleRuleEngine {

    @Autowired
    private V3RuleExpressionEvaluator expressionEvaluator;

    @Autowired
    private V3RuleActionExecutor actionExecutor;

    /**
     * 加载弹性规则
     * 解析JSON规则配置，构建通用规则集
     */
    public V3FlexibleRuleSet loadFlexibleRules(V3RuleConfig ruleConfig) {
        // 伪代码：解析通用规则配置
        V3FlexibleRuleSet ruleSet = new V3FlexibleRuleSet();

        // 解析条件规则
        for (V3ConditionRuleConfig conditionConfig : ruleConfig.getConditionRules()) {
            V3FlexibleRule rule = parseConditionRule(conditionConfig);
            ruleSet.addRule(rule);
        }

        // 解析工作流规则
        for (V3WorkflowConfig workflowConfig : ruleConfig.getWorkflows()) {
            V3FlexibleWorkflow workflow = parseWorkflow(workflowConfig);
            ruleSet.addWorkflow(workflow);
        }

        return ruleSet;
    }

    /**
     * 验证业务规则
     * 调用真实业务Service进行验证
     */
    public V3BusinessRuleValidationResult validateBusinessRules(
            V3BusinessOperation operation,
            V3BusinessRuleContext ruleContext) {

        V3BusinessRuleValidationResult result = new V3BusinessRuleValidationResult();

        try {
            // 根据操作类型执行对应的业务规则验证
            switch (operation.getOperationType()) {
                case ORDER_CREATION:
                    validateOrderCreationRules(operation, ruleContext, result);
                    break;

                case USER_REGISTRATION:
                    validateUserRegistrationRules(operation, ruleContext, result);
                    break;

                case PAYMENT_PROCESSING:
                    validatePaymentProcessingRules(operation, ruleContext, result);
                    break;

                default:
                    result.setValid(true);
                    break;
            }

        } catch (Exception e) {
            log.error("业务规则验证失败", e);
            result.setValid(false);
            result.addViolation("规则验证异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 验证订单创建规则
     * 调用真实的OrderService和InventoryService
     */
    private void validateOrderCreationRules(
            V3BusinessOperation operation,
            V3BusinessRuleContext ruleContext,
            V3BusinessRuleValidationResult result) {

        // 规则1: 检查库存（调用真实InventoryService）
        String productId = operation.getParameter("productId");
        Integer quantity = operation.getParameter("quantity");

        InventoryStatus inventoryStatus = inventoryService.checkInventory(productId);
        if (inventoryStatus.getAvailableStock() < quantity) {
            result.setValid(false);
            result.addViolation("库存不足，可用库存: " + inventoryStatus.getAvailableStock() + ", 需求数量: " + quantity);
            return;
        }

        // 规则2: 检查用户权限和订单金额限制
        V3VirtualUser user = ruleContext.getCurrentUser();
        BigDecimal orderAmount = operation.getParameter("amount");

        if (user.hasRole("user") && orderAmount.compareTo(new BigDecimal("10000")) > 0) {
            // 需要经理审批
            result.setValid(false);
            result.addViolation("订单金额超过10000元，需要经理审批");
            result.setRequiredAction("require_manager_approval");
            return;
        }

        result.setValid(true);
    }
}

## 🔄 业务场景编排器

### 核心设计
```java
/**
 * V3业务场景编排器
 * 基于JSON配置编排复杂业务场景
 */
@Component
public class V3BusinessScenarioOrchestrator {

    private static final Logger log = LoggerFactory.getLogger(V3BusinessScenarioOrchestrator.class);

    /**
     * 编排业务场景
     */
    public V3BusinessScenarioExecution orchestrateScenario(
            V3BusinessScenarioConfig scenarioConfig,
            List<V3VirtualUser> virtualUsers,
            V3BusinessRuleSet ruleSet) {

        V3BusinessScenarioExecution execution = new V3BusinessScenarioExecution();
        execution.setScenarioId(scenarioConfig.getScenarioId());
        execution.setDurationMinutes(scenarioConfig.getDurationMinutes());
        execution.setVirtualUsers(virtualUsers);
        execution.setRuleSet(ruleSet);

        // 编排各个阶段的执行计划
        List<V3PhaseExecutionPlan> phaseExecutionPlans = new ArrayList<>();

        for (V3PhaseConfig phaseConfig : scenarioConfig.getPhases()) {
            V3PhaseExecutionPlan phasePlan = createPhaseExecutionPlan(
                phaseConfig, virtualUsers, ruleSet);
            phaseExecutionPlans.add(phasePlan);
        }

        execution.setPhaseExecutionPlans(phaseExecutionPlans);

        // 配置混沌工程（如果启用）
        if (scenarioConfig.getChaosEngineering() != null && scenarioConfig.getChaosEngineering().isEnabled()) {
            execution.setChaosEngineeringConfig(scenarioConfig.getChaosEngineering());
        }

        log.info("业务场景编排完成，场景: {}, 阶段数: {}, 虚拟用户数: {}",
                scenarioConfig.getScenarioId(), phaseExecutionPlans.size(), virtualUsers.size());

        return execution;
    }
}

## 🚀 深度推演执行器

### 核心设计
```java
/**
 * V3深度推演执行器
 * 执行多阶段、多用户、多业务流程的时序推演
 */
@Component
public class V3DeepSimulationExecutor {

    private static final Logger log = LoggerFactory.getLogger(V3DeepSimulationExecutor.class);

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    /**
     * 执行深度推演
     */
    public V3DeepSimulationResult executeDeepSimulation(
            V3BusinessScenarioExecution scenarioExecution,
            RealBusinessOperationExecutor realBusinessExecutor) {

        V3DeepSimulationResult result = new V3DeepSimulationResult();
        result.setScenarioId(scenarioExecution.getScenarioId());
        result.setStartTime(LocalDateTime.now());

        List<V3PhaseExecutionResult> phaseResults = new ArrayList<>();

        try {
            // 按阶段执行推演
            for (V3PhaseExecutionPlan phasePlan : scenarioExecution.getPhaseExecutionPlans()) {
                V3PhaseExecutionResult phaseResult = executePhase(phasePlan, realBusinessExecutor);
                phaseResults.add(phaseResult);

                // 如果阶段执行失败，根据策略决定是否继续
                if (!phaseResult.isSuccessful() && phasePlan.isFailFast()) {
                    log.warn("阶段执行失败，停止推演: {}", phasePlan.getPhase());
                    break;
                }
            }

            result.setPhaseResults(phaseResults);
            result.setEndTime(LocalDateTime.now());
            result.setSuccessful(phaseResults.stream().allMatch(V3PhaseExecutionResult::isSuccessful));

        } catch (Exception e) {
            log.error("深度推演执行失败", e);
            result.setSuccessful(false);
            result.setErrorMessage(e.getMessage());
        }

        return result;
    }

    /**
     * 执行单个阶段
     */
    private V3PhaseExecutionResult executePhase(
            V3PhaseExecutionPlan phasePlan,
            RealBusinessOperationExecutor realBusinessExecutor) {

        V3PhaseExecutionResult phaseResult = new V3PhaseExecutionResult();
        phaseResult.setPhase(phasePlan.getPhase());
        phaseResult.setStartTime(LocalDateTime.now());

        List<CompletableFuture<V3UserActionResult>> futures = new ArrayList<>();

        // 并发执行用户操作
        for (V3UserActionPlan actionPlan : phasePlan.getUserActionPlans()) {
            CompletableFuture<V3UserActionResult> future = CompletableFuture.supplyAsync(() -> {
                return executeUserActions(actionPlan, realBusinessExecutor);
            }, taskExecutor);

            futures.add(future);
        }

        // 等待所有用户操作完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
            futures.toArray(new CompletableFuture[0]));

        try {
            allFutures.get(phasePlan.getDurationMinutes(), TimeUnit.MINUTES);

            // 收集结果
            List<V3UserActionResult> actionResults = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

            phaseResult.setActionResults(actionResults);
            phaseResult.setSuccessful(actionResults.stream().allMatch(V3UserActionResult::isSuccessful));

        } catch (Exception e) {
            log.error("阶段执行失败: {}", phasePlan.getPhase(), e);
            phaseResult.setSuccessful(false);
            phaseResult.setErrorMessage(e.getMessage());
        }

        phaseResult.setEndTime(LocalDateTime.now());
        return phaseResult;
    }

    /**
     * 执行用户操作
     */
    private V3UserActionResult executeUserActions(
            V3UserActionPlan actionPlan,
            RealBusinessOperationExecutor realBusinessExecutor) {

        V3UserActionResult result = new V3UserActionResult();
        result.setAction(actionPlan.getAction());
        result.setVirtualUser(actionPlan.getVirtualUser());
        result.setStartTime(LocalDateTime.now());

        List<RealBusinessOperationResult> operationResults = new ArrayList<>();

        // 根据频率执行业务操作
        int operationCount = calculateOperationCount(actionPlan.getFrequency(), actionPlan.getDurationMinutes());

        for (int i = 0; i < operationCount; i++) {
            try {
                // 创建业务操作
                V3BusinessOperation operation = createBusinessOperation(actionPlan);

                // 创建业务规则上下文
                V3BusinessRuleContext ruleContext = new V3BusinessRuleContext();
                ruleContext.setCurrentUser(actionPlan.getVirtualUser());
                ruleContext.setOperationIndex(i);
                ruleContext.setTotalOperations(operationCount);

                // 执行真实业务操作
                RealBusinessOperationResult operationResult = realBusinessExecutor.execute(
                    actionPlan.getVirtualUser(), operation, ruleContext);

                operationResults.add(operationResult);

                // 模拟用户操作间隔
                Thread.sleep(calculateOperationInterval(actionPlan.getFrequency()));

            } catch (Exception e) {
                log.error("用户操作执行失败: {}", actionPlan.getAction(), e);
                RealBusinessOperationResult errorResult = new RealBusinessOperationResult();
                errorResult.setSuccess(false);
                errorResult.setErrorMessage(e.getMessage());
                operationResults.add(errorResult);
            }
        }

        result.setOperationResults(operationResults);
        result.setSuccessful(operationResults.stream().allMatch(RealBusinessOperationResult::isSuccess));
        result.setEndTime(LocalDateTime.now());

        return result;
    }
}

## 📋 参数化配置示例

### 通用参数化推演配置
```json
{
  "scenario_id": "parametric_simulation_test",
  "scenario_name": "参数化通用推演测试",
  "duration_minutes": 30,
  "description": "基于参数配置的通用业务推演，可适配任意业务场景",

  "parameter_config": {
    "roles": [
      {
        "role_id": "role_001",
        "role_name": "角色1",
        "parameters": {
          "permission_level": 3,
          "concurrent_limit": 5,
          "custom_attributes": {
            "max_amount": 10000,
            "approval_required": true
          }
        }
      },
      {
        "role_id": "role_002",
        "role_name": "角色2",
        "parameters": {
          "permission_level": 1,
          "concurrent_limit": 2,
          "custom_attributes": {
            "max_amount": 50000,
            "approval_required": false
          }
        }
      }
    ],
    "business_params": {
      "target_services": [
        {
          "service_name": "userService",
          "methods": [
            {
              "method_name": "createUser",
              "parameter_mapping": {
                "name": "${user.name}",
                "email": "${user.email}",
                "role": "${user.role}"
              }
            }
          ]
        },
        {
          "service_name": "orderService",
          "methods": [
            {
              "method_name": "createOrder",
              "parameter_mapping": {
                "userId": "${user.id}",
                "amount": "${order.amount}",
                "productId": "${order.productId}"
              }
            }
          ]
        }
      ]
    },
    "execution_params": {
      "virtual_actors": [
        {
          "actor_id": "actor_001",
          "role_id": "role_001",
          "behavior_pattern": "pattern_001",
          "concurrent_sessions": 2,
          "parameter_values": {
            "user.name": "测试用户1",
            "user.email": "<EMAIL>",
            "order.amount": 5000
          }
        },
        {
          "actor_id": "actor_002",
          "role_id": "role_002",
          "behavior_pattern": "pattern_002",
          "concurrent_sessions": 1,
          "parameter_values": {
            "user.name": "测试用户2",
            "user.email": "<EMAIL>",
            "order.amount": 30000
          }
        }
      ]
    }
  },

  "rule_config": {
    "condition_rules": [
      {
        "rule_id": "rule_001",
        "rule_name": "通用条件规则1",
        "condition_expression": "${actor.permission_level} < 2 && ${parameter.amount} > ${role.max_amount}",
        "action_config": {
          "action_type": "invoke_service",
          "target_service": "approvalService",
          "target_method": "requireApproval",
          "parameter_mapping": {
            "requestId": "${execution.request_id}",
            "amount": "${parameter.amount}",
            "userId": "${actor.id}"
          }
        },
        "priority": 1
      },
      {
        "rule_id": "rule_002",
        "rule_name": "通用条件规则2",
        "condition_expression": "${parameter.stock} < ${parameter.min_stock}",
        "action_config": {
          "action_type": "invoke_service",
          "target_service": "inventoryService",
          "target_method": "triggerRestock",
          "parameter_mapping": {
            "productId": "${parameter.product_id}",
            "currentStock": "${parameter.stock}"
          }
        },
        "priority": 2
      }
    ],
    "workflows": [
      {
        "workflow_id": "workflow_001",
        "workflow_name": "通用业务工作流",
        "steps": [
          {
            "step_id": "step_001",
            "step_name": "参数验证",
            "action_config": {
              "action_type": "invoke_service",
              "target_service": "validationService",
              "target_method": "validateParameters",
              "parameter_mapping": {
                "parameters": "${execution.all_parameters}"
              }
            },
            "timeout_ms": 1000
          },
          {
            "step_id": "step_002",
            "step_name": "业务执行",
            "action_config": {
              "action_type": "invoke_service",
              "target_service": "${parameter.target_service}",
              "target_method": "${parameter.target_method}",
              "parameter_mapping": "${parameter.method_parameters}"
            },
            "timeout_ms": 5000
          }
        ]
      }
    ]
  },

  "orchestration_config": {
    "phases": [
      {
        "phase_id": "phase_001",
        "phase_name": "初始化阶段",
        "duration_minutes": 5,
        "actions": [
          {
            "action_id": "init_actors",
            "target_service": "actorService",
            "target_method": "initializeActors",
            "frequency": 1,
            "actor_filter": "all"
          }
        ]
      },
      {
        "phase_id": "phase_002",
        "phase_name": "执行阶段",
        "duration_minutes": 20,
        "actions": [
          {
            "action_id": "execute_business_operations",
            "target_service": "${actor.target_service}",
            "target_method": "${actor.target_method}",
            "frequency": 10,
            "actor_filter": "active"
          }
        ]
      }
    ]
  }
}
```

## 🎯 架构设计总结

### 核心设计原则

#### 1. **参数化引擎设计**
- **零业务耦合**：引擎本身不包含任何具体业务逻辑
- **通用性**：一套引擎支持所有业务场景的参数化推演
- **弹性配置**：通过JSON配置实现参数弹性和执行规则弹性

#### 2. **真实代码注入机制**
- **反射调用**：通过反射机制动态调用任意真实业务Service
- **参数映射**：JSON配置中的参数自动映射到真实方法参数
- **服务发现**：基于Spring容器的服务自动发现和注入

#### 3. **弹性设计架构**
- **参数弹性**：支持任意参数类型和参数组合
- **规则弹性**：支持任意条件表达式和动作配置
- **场景弹性**：支持任意业务场景的编排和执行

### 关键技术特点

#### 1. **V2兼容性保证**
- **数据格式直接兼容V2**：推演结果直接符合V2格式，无需转换
- **复用V2统一系统**：直接使用V2的报告输出和索引管理
- **零修改集成**：V3引擎作为V2的L4智慧层，无需修改V2代码

#### 2. **TestContainers集成**
- **真实环境执行**：在TestContainers提供的真实环境中执行推演
- **环境感知**：根据环境类型调整推演策略和参数配置
- **故障隔离**：推演故障不影响真实业务环境

#### 3. **AI增强能力**
- **智能参数生成**：基于AI分析生成最优参数配置
- **自适应规则调整**：根据执行结果智能调整规则配置
- **故障预测**：基于推演结果预测潜在的系统故障点

### 实施验证要点

#### ✅ **架构验证清单**
- [ ] 参数注入管理器正常工作
- [ ] 动态执行引擎成功调用真实业务Service
- [ ] 弹性规则引擎正确执行条件判断和动作
- [ ] 场景编排引擎按配置执行多阶段推演
- [ ] 推演结果与V2格式完全兼容
- [ ] TestContainers环境集成正常
- [ ] 故障场景重现功能有效

#### 🎯 **性能目标**
- **参数解析性能**：JSON配置解析时间 < 100ms
- **反射调用性能**：单次Service调用时间 < 10ms
- **并发推演能力**：支持100+虚拟角色并发执行
- **内存使用效率**：推演过程内存占用 < 512MB

#### 🔒 **安全边界**
- **参数验证**：所有JSON参数必须经过安全验证
- **服务调用限制**：只能调用白名单内的业务Service
- **执行时间限制**：单次推演最大执行时间30分钟
- **资源使用限制**：推演过程资源使用受到严格控制
