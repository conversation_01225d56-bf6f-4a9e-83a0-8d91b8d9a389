# 04-多API并发控制（DRY重构版）

## 📋 文档信息

**文档ID**: F007-FOUR-LAYER-MEETING-CONCURRENT-004  
**依赖配置**: 引用 `00-共同配置.json`  
**前置依赖**: 02-API管理核心模块.md, 03-双向协作机制实现.md  
**AI负载等级**: 中等（≤8个概念，≤600行代码，≤90分钟）  
**置信度目标**: 88%+  
**执行优先级**: 4  

## 🎯 统一模型池管家实现

```python
# 【AI自动创建】tools/ace/src/api_management/account_management/unified_model_pool_butler.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一模型池管家 - 只管模型状态，不管API提供商
引用: 00-共同配置.json 的 api_model_configurations
"""

import sys
import os
import asyncio
import threading
from datetime import datetime
import time

# 确保可以导入common模块
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', '..')
sys.path.insert(0, src_dir)

from common_config_loader import CommonConfigLoader

class UnifiedModelPoolButler:
    """统一模型池管家（只管模型状态，不管API提供商）"""
    
    def __init__(self, api_db):
        self.api_db = api_db
        self.config = CommonConfigLoader()
        
        # 统一模型池（能力标签驱动，模型可替换）
        self.unified_model_pool = self._init_model_pool()
        
        # 并发控制配置
        self.max_concurrent_requests = 8  # 基于CPU核心数×2
        self.request_semaphore = asyncio.Semaphore(self.max_concurrent_requests)
        self.active_requests = {}
        
        # 负载均衡配置
        self.load_balancer = LoadBalancer()
        
    def _init_model_pool(self):
        """初始化统一模型池"""
        model_configs = self.config.config.get("api_model_configurations", {})
        pool = {}
        
        # 合并主力和备用API到统一池
        for api_type in ["primary_apis", "backup_apis"]:
            for api_key, config in model_configs.get(api_type, {}).items():
                pool[api_key] = {
                    "model_name": config["model_name"],
                    "role": config["role"],
                    "api_type": api_type.replace("_apis", ""),
                    "capabilities": self._extract_capabilities(config),
                    "status": "active",
                    "current_load": 0,
                    "performance_score": config.get("performance_score", 80.0),
                    "last_updated": datetime.now().isoformat()
                }
        
        return pool
    
    def _extract_capabilities(self, config):
        """提取模型能力标签"""
        role = config.get("role", "")
        capabilities = {}
        
        if "架构" in role:
            capabilities["system_design"] = 0.95
            capabilities["architecture_planning"] = 0.90
        if "代码" in role:
            capabilities["code_generation"] = 0.90
            capabilities["debugging"] = 0.85
        if "逻辑" in role:
            capabilities["logical_reasoning"] = 0.88
            capabilities["problem_solving"] = 0.85
        
        return capabilities
    
    async def allocate_model(self, task_requirements):
        """智能分配模型（基于任务需求和模型状态）"""
        async with self.request_semaphore:
            # 1. 根据任务需求筛选合适的模型
            suitable_models = self._filter_suitable_models(task_requirements)
            
            # 2. 负载均衡选择最优模型
            selected_model = self.load_balancer.select_optimal_model(suitable_models)
            
            # 3. 更新模型负载状态
            if selected_model:
                self.unified_model_pool[selected_model]["current_load"] += 1
                self.active_requests[task_requirements.get("request_id")] = selected_model
            
            return selected_model
    
    def _filter_suitable_models(self, task_requirements):
        """根据任务需求筛选合适的模型"""
        required_capability = task_requirements.get("capability", "general")
        min_confidence = task_requirements.get("min_confidence", 0.8)
        
        suitable_models = []
        for model_id, model_info in self.unified_model_pool.items():
            # 检查模型状态
            if model_info["status"] != "active":
                continue
            
            # 检查能力匹配
            capabilities = model_info["capabilities"]
            if required_capability in capabilities:
                if capabilities[required_capability] >= min_confidence:
                    suitable_models.append((model_id, model_info))
        
        return suitable_models
    
    def release_model(self, request_id):
        """释放模型资源"""
        if request_id in self.active_requests:
            model_id = self.active_requests[request_id]
            self.unified_model_pool[model_id]["current_load"] -= 1
            del self.active_requests[request_id]
    
    def get_pool_status(self):
        """获取模型池状态"""
        return {
            "total_models": len(self.unified_model_pool),
            "active_models": sum(1 for m in self.unified_model_pool.values() if m["status"] == "active"),
            "total_load": sum(m["current_load"] for m in self.unified_model_pool.values()),
            "active_requests": len(self.active_requests),
            "pool_details": self.unified_model_pool
        }

class LoadBalancer:
    """负载均衡器"""
    
    def __init__(self):
        self.selection_history = []
    
    def select_optimal_model(self, suitable_models):
        """选择最优模型（加权轮询算法）"""
        if not suitable_models:
            return None
        
        # 计算每个模型的权重分数
        weighted_models = []
        for model_id, model_info in suitable_models:
            # 权重 = 性能分数 × 可用性 × 负载反比
            performance_weight = model_info["performance_score"] / 100.0
            availability_weight = 1.0 if model_info["status"] == "active" else 0.0
            load_weight = 1.0 / (model_info["current_load"] + 1)  # 避免除零
            
            total_weight = performance_weight * availability_weight * load_weight
            weighted_models.append((model_id, total_weight))
        
        # 选择权重最高的模型
        best_model = max(weighted_models, key=lambda x: x[1])
        return best_model[0]

class ConcurrentAPIManager:
    """并发API管理器"""
    
    def __init__(self, model_pool_butler):
        self.butler = model_pool_butler
        self.config = CommonConfigLoader()
        
        # 并发配置
        self.max_workers = 4
        self.executor = None
        
    async def execute_concurrent_requests(self, request_batch):
        """执行并发请求批次"""
        tasks = []
        
        for request in request_batch:
            task = asyncio.create_task(self._execute_single_request(request))
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return results
    
    async def _execute_single_request(self, request):
        """执行单个请求"""
        try:
            # 1. 分配模型
            model_id = await self.butler.allocate_model(request["requirements"])
            if not model_id:
                return {"status": "error", "message": "无可用模型"}
            
            # 2. 执行请求
            start_time = time.time()
            result = await self._call_api(model_id, request)
            execution_time = time.time() - start_time
            
            # 3. 释放模型
            self.butler.release_model(request["requirements"].get("request_id"))
            
            # 4. 更新性能指标
            self._update_performance_metrics(model_id, execution_time, result.get("success", False))
            
            return result
            
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    async def _call_api(self, model_id, request):
        """调用API（模拟实现）"""
        # 这里应该是实际的API调用逻辑
        await asyncio.sleep(0.1)  # 模拟API调用延迟
        
        return {
            "status": "success",
            "model_id": model_id,
            "result": f"处理完成: {request.get('task', 'unknown')}",
            "success": True
        }
    
    def _update_performance_metrics(self, model_id, execution_time, success):
        """更新性能指标"""
        # 更新模型池中的性能数据
        if model_id in self.butler.unified_model_pool:
            model_info = self.butler.unified_model_pool[model_id]
            
            # 简单的移动平均更新
            if "avg_response_time" not in model_info:
                model_info["avg_response_time"] = execution_time
            else:
                model_info["avg_response_time"] = (model_info["avg_response_time"] * 0.8 + execution_time * 0.2)
            
            # 更新成功率
            if "success_rate" not in model_info:
                model_info["success_rate"] = 1.0 if success else 0.0
            else:
                current_rate = model_info["success_rate"]
                model_info["success_rate"] = current_rate * 0.9 + (1.0 if success else 0.0) * 0.1
```

## ✅ 多API并发控制完成验证

### 验证脚本
```python
# 【AI自动执行】多API并发控制验证
python -c "
import sys
sys.path.insert(0, 'tools/ace/src')
import asyncio

try:
    from api_management.sqlite_storage.api_account_database import APIAccountDatabase
    from api_management.account_management.unified_model_pool_butler import UnifiedModelPoolButler, ConcurrentAPIManager
    
    # 初始化组件
    db = APIAccountDatabase()
    butler = UnifiedModelPoolButler(db)
    manager = ConcurrentAPIManager(butler)
    
    print(f'✅ 统一模型池初始化成功: {butler.get_pool_status()[\"total_models\"]} 个模型')
    print(f'✅ 并发管理器初始化成功: 最大并发数 {butler.max_concurrent_requests}')
    print('✅ 多API并发控制验证完成')
    
except Exception as e:
    print(f'❌ 多API并发控制验证失败: {str(e)}')
    exit(1)
"
```

## 📊 阶段完成标准

### 成功标准
- ✅ 统一模型池管家实现完成
- ✅ 负载均衡算法实现
- ✅ 并发控制机制建立
- ✅ API性能监控功能实现

### 输出文件清单
- `tools/ace/src/api_management/account_management/unified_model_pool_butler.py`

### 下一步依赖
- 06-Web界面功能实现.md 依赖此模块
- 07-集成测试和验证.md 依赖此模块

**预期执行时间**: 90分钟  
**AI负载等级**: 中等  
**置信度**: 88%+  
**人类参与**: 无需人类参与（AI自主执行）
