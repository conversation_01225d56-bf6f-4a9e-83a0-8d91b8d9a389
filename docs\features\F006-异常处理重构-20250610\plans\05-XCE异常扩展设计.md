# XCE异常扩展设计

**文档编号**: F006-05
**创建时间**: 2025-01-15
**设计目标**: 为xkongcloud Commons Exception (XCE) 按技术类别扩展异常处理能力

## 🚨 实施范围边界（必读）

### 📋 包含范围（设计覆盖）
- ✅ 按技术类别设计异常分类体系
- ✅ 定义XCE错误码分配规范
- ✅ 设计V3测试引擎集成机制
- ✅ 制定权重分类原则

### 🚫 排除范围（设计不涉及）
- ❌ 具体的业务逻辑实现
- ❌ 现有异常处理机制的修改
- ❌ Spring Boot框架的核心配置
- ❌ 数据库schema的变更

### 🔍 认知负载控制
- **概念数量**: ≤5个技术类别
- **设计层级**: ≤3层继承关系
- **错误码范围**: 按50个一组分配

## 🧠 AI认知约束激活

### 强制激活命令
```bash
@L1:global-constraints                    # 全局约束和命名规范
@L1:ai-implementation-design-principles  # AI实施设计原则
@AI_COGNITIVE_CONSTRAINTS                # AI认知约束激活
@ARCHITECTURAL_EVOLUTION_CHECK           # 架构演进检查
```

## 🎯 XCE异常体系设计

### 异常分类架构（按技术类别）
```
ServiceException (基类)
├── BusinessException (业务异常)
│   ├── NetworkBusinessException         # 网络业务异常
│   ├── DatabaseBusinessException       # 数据库业务异常
│   ├── FileBusinessException           # 文件业务异常
│   ├── ValidationBusinessException     # 验证业务异常
│   └── SecurityBusinessException       # 安全业务异常
└── SystemException (系统异常)  
    ├── NetworkSystemException          # 网络系统异常
    ├── DatabaseSystemException         # 数据库系统异常
    ├── FileSystemException             # 文件系统异常
    └── AlgorithmProcessingException     # 算法处理异常（L2-L4层）
```

### 错误码分配策略（按技术类别）
```java
// 现有错误码范围
// 100-199: 通用业务异常
// 200-299: 集群异常  
// 300-399: KV参数异常
// 500-599: 系统异常

// XCE扩展错误码范围（按技术类别）
// 600-649: 网络类异常
// 650-699: 数据库类异常
// 700-749: 文件操作类异常
// 750-799: 验证类异常
// 800-849: 安全类异常
// 850-899: 算法处理类异常
```

## 📋 具体异常类设计（按技术类别）

### 1. 网络类异常设计

#### 1.1 NetworkBusinessException
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\network\NetworkBusinessException.java`

```java
/**
 * 网络业务异常
 * 用于处理网络调用相关的业务逻辑异常
 */
public class NetworkBusinessException extends BusinessException {
    
    private NetworkBusinessException(String errorCode, String message) {
        super(errorCode, message);
    }
    
    // 服务不可用异常
    public static NetworkBusinessException serviceUnavailable(String serviceName) {
        return new NetworkBusinessException(
            NetworkErrorCodes.SERVICE_UNAVAILABLE,
            "服务不可用: " + serviceName
        ).addMetadata("serviceName", serviceName)
         .addMetadata("category", "network-business");
    }
    
    // 连接超时异常
    public static NetworkBusinessException connectionTimeout(String host, int port) {
        return new NetworkBusinessException(
            NetworkErrorCodes.CONNECTION_TIMEOUT,
            "连接超时: " + host + ":" + port
        ).addMetadata("host", host)
         .addMetadata("port", port)
         .addMetadata("category", "network-business");
    }
}
```

#### 1.2 MockEnvironmentException (XCE扩展)
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\network\MockEnvironmentException.java`

```java
/**
 * Mock环境异常 (XCE扩展)
 * 用于处理测试Mock环境创建和管理异常
 */
public class MockEnvironmentException extends NetworkSystemException {
    
    public static MockEnvironmentException creationFailed(String mockType, String reason) {
        return new MockEnvironmentException(
            NetworkErrorCodes.MOCK_ENVIRONMENT_CREATION_FAILED,
            "Mock环境创建失败: " + mockType + ", " + reason
        ).addMetadata("mockType", mockType)
         .addMetadata("reason", reason)
         .addMetadata("category", "xce-testing-network");
    }
}
```

### 2. 文件操作类异常设计

#### 2.1 FileBusinessException
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\file\FileBusinessException.java`

#### 2.2 TestDataInjectionException (XCE扩展)
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\file\TestDataInjectionException.java`

```java
/**
 * 测试数据注入异常 (XCE扩展)
 * 用于处理测试数据文件注入相关异常
 */
public class TestDataInjectionException extends FileBusinessException {
    
    public static TestDataInjectionException injectionFailed(String dataFile, String reason) {
        return new TestDataInjectionException(
            FileErrorCodes.TEST_DATA_INJECTION_FAILED,
            "测试数据注入失败: " + dataFile + ", " + reason
        ).addMetadata("dataFile", dataFile)
         .addMetadata("reason", reason)
         .addMetadata("category", "xce-testing-file");
    }
}
```

### 3. 验证类异常设计

#### 3.1 ValidationBusinessException
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\validation\ValidationBusinessException.java`

#### 3.2 ParametricExecutionException (XCE扩展)
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\validation\ParametricExecutionException.java`

```java
/**
 * 参数化推演异常 (XCE扩展)
 * 用于处理参数化推演执行相关异常
 */
public class ParametricExecutionException extends ValidationBusinessException {
    
    public static ParametricExecutionException executionFailed(String parameterName, String reason) {
        return new ParametricExecutionException(
            ValidationErrorCodes.PARAMETRIC_EXECUTION_FAILED,
            "参数化推演执行失败: " + parameterName + ", " + reason
        ).addMetadata("parameterName", parameterName)
         .addMetadata("reason", reason)
         .addMetadata("category", "xce-testing-validation");
    }
}
```

#### 3.3 L1PerceptionException (XCE扩展)
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\validation\L1PerceptionException.java`

```java
/**
 * L1感知层验证异常 (XCE扩展)
 * 用于处理L1感知层数据验证异常
 */
public class L1PerceptionException extends ValidationBusinessException {
    
    public static L1PerceptionException processingFailed(String inputData, String reason) {
        return new L1PerceptionException(
            ValidationErrorCodes.L1_PERCEPTION_VALIDATION_FAILED,
            "L1感知层数据验证失败: " + reason
        ).addMetadata("inputData", inputData)
         .addMetadata("layer", "L1")
         .addMetadata("category", "xce-algorithm-validation");
    }
}
```

### 4. 算法处理异常设计

#### 4.1 AlgorithmProcessingException (XCE扩展)
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\core\AlgorithmProcessingException.java`

```java
/**
 * 算法处理异常 (XCE扩展)
 * 用于处理L2-L4层算法处理异常
 */
public class AlgorithmProcessingException extends SystemException {
    
    // L2认知层算法异常
    public static AlgorithmProcessingException l2CognitionFailed(String algorithmName, String reason) {
        return new AlgorithmProcessingException(
            ErrorCodes.ALGORITHM_L2_COGNITION_FAILED,
            "L2认知层算法处理失败: " + algorithmName + ", " + reason
        ).addMetadata("layer", "L2")
         .addMetadata("algorithmName", algorithmName)
         .addMetadata("category", "xce-algorithm-cognition");
    }
    
    // L3理解层算法异常
    public static AlgorithmProcessingException l3UnderstandingFailed(String context, String reason) {
        return new AlgorithmProcessingException(
            ErrorCodes.ALGORITHM_L3_UNDERSTANDING_FAILED,
            "L3理解层推理失败: " + reason
        ).addMetadata("layer", "L3")
         .addMetadata("context", context)
         .addMetadata("category", "xce-algorithm-reasoning");
    }
    
    // L4智慧层算法异常
    public static AlgorithmProcessingException l4WisdomFailed(String decisionContext, String reason) {
        return new AlgorithmProcessingException(
            ErrorCodes.ALGORITHM_L4_WISDOM_FAILED,
            "L4智慧层决策失败: " + reason
        ).addMetadata("layer", "L4")
         .addMetadata("decisionContext", decisionContext)
         .addMetadata("category", "xce-algorithm-decision");
    }
}
```

## 🔢 错误码常量扩展

### 各类别错误码文件
1. **NetworkErrorCodes.java** - 网络错误码 (600-649)
2. **DatabaseErrorCodes.java** - 数据库错误码 (650-699)  
3. **FileErrorCodes.java** - 文件错误码 (700-749)
4. **ValidationErrorCodes.java** - 验证错误码 (750-799)
5. **SecurityErrorCodes.java** - 安全错误码 (800-849)

### ErrorCodes.java 扩展
**文件路径**: `c:\ExchangeWorks\xkong\xkongcloud\xkongcloud-commons\xkongcloud-commons-exception\src\main\java\org\xkong\cloud\commons\exception\model\ErrorCodes.java`

```java
public class ErrorCodes {
    // ... 现有错误码 ...
    
    // ==================== XCE扩展错误码（按技术类别） ====================
    
    // 网络类异常 (600-649)
    public static final String NETWORK_CONNECTION_TIMEOUT = "XCE_NET_600";
    public static final String NETWORK_SERVICE_UNAVAILABLE = "XCE_NET_601";
    public static final String MOCK_ENVIRONMENT_CREATION_FAILED = "XCE_NET_602";
    
    // 数据库类异常 (650-699)
    public static final String DATABASE_CONNECTION_POOL_EXHAUSTED = "XCE_DB_650";
    public static final String DATABASE_TRANSACTION_TIMEOUT = "XCE_DB_651";
    
    // 文件操作类异常 (700-749)
    public static final String FILE_NOT_FOUND = "XCE_FILE_700";
    public static final String FILE_PERMISSION_DENIED = "XCE_FILE_701";
    public static final String TEST_DATA_INJECTION_FAILED = "XCE_FILE_702";
    
    // 验证类异常 (750-799)
    public static final String VALIDATION_PARAMETER_INVALID = "XCE_VAL_750";
    public static final String PARAMETRIC_EXECUTION_FAILED = "XCE_VAL_751";
    public static final String L1_PERCEPTION_VALIDATION_FAILED = "XCE_VAL_752";
    
    // 安全类异常 (800-849)
    public static final String SECURITY_AUTHENTICATION_FAILED = "XCE_SEC_800";
    public static final String SECURITY_AUTHORIZATION_DENIED = "XCE_SEC_801";
    
    // 算法处理类异常 (850-899)
    public static final String ALGORITHM_L2_COGNITION_FAILED = "XCE_ALG_850";
    public static final String ALGORITHM_L3_UNDERSTANDING_FAILED = "XCE_ALG_851";
    public static final String ALGORITHM_L4_WISDOM_FAILED = "XCE_ALG_852";
}
```

## 🎯 XCE扩展的核心价值

### 1. 技术类别分类的优势
- **真实复用性**: 网络、数据库、文件异常确实会被多个项目使用
- **处理一致性**: 相同技术类别的异常使用相同的处理策略
- **开发直观性**: 开发者按操作类型思考异常，而不是按项目领域

### 2. 权重分类原则
- **主要诱发因素**: 按异常的根本原因分类
- **上下文保留**: 次要因素通过metadata保留
- **分类清晰**: 避免跨类别异常的分类争议

### 3. 平台统一性
- **一致的异常体系**: 整个xkongcloud平台使用统一的异常分类
- **统一的错误码管理**: 按技术类别分段管理错误码
- **统一的处理策略**: 相同技术类别的异常有相似的处理逻辑