# XKongCloud Commons Nexus V1.0: 扩展点与服务发现

## 文档元数据

- **文档ID**: `F007-NEXUS-ARCHITECTURE-DESIGN-004`
- **版本**: `V1.0`
- **创建日期**: `2025-06-12`
- **状态**: `设计稿`
- **技术栈**: `Java 21, Spring Boot 3.4.5, Virtual Threads`
- **复杂度等级**: L2

## 实施约束标注

### 🔒 强制性技术约束
- **Java版本**: 必须使用Java 21或更高版本，支持反射和注解处理
- **Spring Boot版本**: 必须使用Spring Boot 3.4.5+，确保注解扫描机制兼容
- **扩展点接口**: 必须标注@ExtensionPoint注解，接口必须是public的
- **扩展实现**: 必须标注@Extension注解，实现类必须有无参构造函数

### ⚡ 性能指标约束
- **扩展扫描时间**: ≤200ms（单个插件的扩展扫描时间）
- **服务注册时间**: ≤50ms（单个服务的注册时间）
- **服务查找延迟**: ≤0.1ms（服务发现的响应时间）
- **扩展实例化时间**: ≤10ms（单个扩展的实例化时间）
- **内存占用**: 每个扩展点基础内存≤5MB，每个扩展实例≤2MB

### 🔄 兼容性要求
- **接口向后兼容**: 扩展点接口保证向后兼容，方法只能新增不能删除
- **注解兼容**: 支持@Extension和@Component两种注册方式
- **版本兼容**: 支持扩展的版本声明和兼容性检查

### ⚠️ 违规后果定义
- **技术约束违规**: 扩展注册失败，记录ERROR级别日志
- **性能指标超标**: 记录WARN级别日志，触发性能监控告警
- **兼容性问题**: 降级处理，记录兼容性问题报告

### 🎯 验证锚点设置
- **编译验证**: `mvn clean compile -Djava.version=21`
- **单元测试**: `mvn test -Dtest=ExtensionPointTest`
- **集成测试**: `mvn verify -Dtest=ServiceDiscoveryTest`
- **性能测试**: `mvn test -Dtest=ExtensionPerformanceTest`

## 核心定位

Nexus 的扩展机制是实现插件间能力共享和组合的核心基础设施。它的核心定位是提供**面向契约的扩展框架**，通过标准化的接口定义和自动化的服务发现机制，实现插件间的松耦合协作。

## 设计哲学

Nexus 的扩展机制遵循 **"面向契约而非面向实现"** 的设计哲学。插件之间是完全解耦的，它们不直接感知对方的存在，而是通过一个共享的"契约"（Java接口）来交互。这个模型是构建一个真正可扩展、可维护系统的核心。

### 核心概念定义
- **扩展点 (Extension Point)**: 一个公开的、定义了特定功能的Java接口。它是一个等待被实现的"插座"
- **扩展 (Extension)**: 对扩展点接口的一个具体实现。它是一个可以插入"插座"的"插头"
- **服务提供者接口 (SPI)**: 标准化的扩展点接口，定义了插件间协作的契约
- **服务发现 (Service Discovery)**: 运行时动态查找和获取扩展实现的机制

### 架构设计原则
- **依赖倒置原则**: 高层模块不依赖低层模块，都依赖于抽象接口
- **接口隔离原则**: 扩展点接口应该小而专一，避免臃肿的接口
- **开放封闭原则**: 对扩展开放，对修改封闭
- **单一职责原则**: 每个扩展点只负责一个特定的功能领域

## 包含范围

本文档包含以下核心内容：

- **扩展点注解设计**: @ExtensionPoint和@Extension注解定义
- **服务注册机制**: 自动服务注册和生命周期管理
- **服务发现机制**: 主动查找和事件驱动发现
- **SPI接口定义**: 标准化的服务提供者接口
- **服务事件模型**: 服务状态变更事件设计
- **扩展加载顺序**: 扩展优先级和加载策略

## 排除范围

本文档明确不包含以下内容：

- **插件生命周期**: 由微内核管理，在其他文档中描述
- **服务总线实现**: 通信机制在服务总线文档中描述
- **具体扩展实现**: 不涉及DB、Cache等具体扩展的实现
- **安全控制**: 扩展访问控制在安全文档中描述
- **性能监控**: 服务性能监控在运维文档中描述
- **配置管理**: 扩展配置在配置文档中描述

## 扩展点架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "扩展点定义层"
        EP1[DataAccessProvider]
        EP2[CacheProvider]
        EP3[MessageProvider]
        EP4[SecurityProvider]
    end

    subgraph "扩展实现层"
        E1[MySQL Extension]
        E2[PostgreSQL Extension]
        E3[Redis Extension]
        E4[Kafka Extension]
        E5[OAuth Extension]
    end

    subgraph "服务注册层"
        SR[Service Registry]
        ES[Extension Scanner]
        LM[Lifecycle Manager]
    end

    subgraph "服务发现层"
        SD[Service Discovery]
        EB[Event Bus]
        SF[Service Filter]
    end

    subgraph "插件层"
        P1[Plugin A]
        P2[Plugin B]
        P3[Plugin C]
    end

    EP1 -.->|implements| E1
    EP1 -.->|implements| E2
    EP2 -.->|implements| E3
    EP3 -.->|implements| E4
    EP4 -.->|implements| E5

    E1 --> SR
    E2 --> SR
    E3 --> SR
    E4 --> SR
    E5 --> SR

    ES --> SR
    LM --> SR

    SR --> SD
    SD --> EB
    SD --> SF

    P1 --> SD
    P2 --> SD
    P3 --> SD
```

### 扩展点生命周期图

```mermaid
stateDiagram-v2
    [*] --> DISCOVERED : 扫描发现
    DISCOVERED --> VALIDATED : 验证注解
    VALIDATED --> INSTANTIATED : 实例化
    INSTANTIATED --> REGISTERED : 注册服务
    REGISTERED --> ACTIVE : 激活可用
    ACTIVE --> DEACTIVATED : 停用
    DEACTIVATED --> UNREGISTERED : 注销服务
    UNREGISTERED --> [*]

    VALIDATED --> FAILED : 验证失败
    INSTANTIATED --> FAILED : 实例化失败
    REGISTERED --> FAILED : 注册失败
    FAILED --> [*]
```

### 服务发现时序图

```mermaid
sequenceDiagram
    participant P as Plugin
    participant ES as Extension Scanner
    participant SR as Service Registry
    participant SD as Service Discovery
    participant EB as Event Bus
    participant C as Consumer Plugin

    Note over P,C: 扩展注册阶段
    P->>ES: 插件启动
    ES->>ES: 扫描@Extension注解
    ES->>SR: 注册扩展服务
    SR->>EB: 发布ServiceRegisteredEvent

    Note over P,C: 服务发现阶段
    C->>SD: getServices(ExtensionPoint.class)
    SD->>SR: 查询服务实例
    SR->>SD: 返回服务列表
    SD->>C: 返回扩展实例

    Note over P,C: 事件驱动发现
    EB->>C: ServiceRegisteredEvent
    C->>C: 动态处理新服务
```

## 核心API: `@ExtensionPoint` 与 `@Extension`

为了使扩展机制更加清晰和自动化，Nexus 提供了一套基于注解的声明式API。

### `@ExtensionPoint` 注解定义

此注解用于标记一个接口，将其正式声明为一个可被外部实现的扩展点。

```java
package org.xkong.cloud.commons.nexus.api.annotation;

import java.lang.annotation.*;

/**
 * 扩展点注解，用于标记可被插件实现的接口。
 *
 * <p>被标记的接口必须满足以下约束：</p>
 * <ul>
 *   <li>接口必须是public的</li>
 *   <li>接口方法不能有默认实现（除非明确标记为可选）</li>
 *   <li>接口应该遵循单一职责原则</li>
 *   <li>接口应该保持向后兼容</li>
 * </ul>
 *
 * @since 1.0.0
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ExtensionPoint {

    /**
     * 扩展点的描述，用于文档和工具。
     *
     * @return 扩展点描述
     */
    String value();

    /**
     * 扩展点的版本。
     *
     * @return 版本号，默认为"1.0"
     * @since 1.1.0
     */
    String version() default "1.0";

    /**
     * 扩展点的分类。
     *
     * @return 分类名称，用于扩展点的组织和管理
     * @since 1.1.0
     */
    String category() default "general";

    /**
     * 是否允许多个实现。
     *
     * @return true表示允许多个实现，false表示只允许一个实现
     */
    boolean multiple() default true;

    /**
     * 是否为必需的扩展点。
     *
     * @return true表示系统必须有此扩展点的实现才能正常运行
     * @since 1.2.0
     */
    boolean required() default false;
}
```

### 扩展点接口设计规范

```java
/**
 * 数据源提供者扩展点示例
 */
@ExtensionPoint(
    value = "提供数据源连接的核心服务",
    version = "1.0",
    category = "data-access",
    multiple = true,
    required = false
)
public interface DataSourceProvider {

    /**
     * 获取提供者名称。
     *
     * @return 提供者名称，必须唯一
     */
    String getProviderName();

    /**
     * 检查是否支持指定的数据源类型。
     *
     * @param dataSourceType 数据源类型
     * @return true如果支持，false如果不支持
     */
    boolean supports(String dataSourceType);

    /**
     * 创建数据源实例。
     *
     * @param config 数据源配置
     * @return 数据源实例
     * @throws DataSourceException 如果创建失败
     */
    DataSource createDataSource(DataSourceConfig config) throws DataSourceException;

    /**
     * 获取数据库方言。
     *
     * @return 数据库方言标识
     */
    String getDialect();

    /**
     * 获取支持的特性列表。
     *
     * @return 特性列表
     * @since 1.1.0
     */
    default Set<String> getSupportedFeatures() {
        return Set.of("basic");
    }

    /**
     * 获取提供者的优先级。
     *
     * @return 优先级，数值越小优先级越高
     * @since 1.1.0
     */
    default int getPriority() {
        return 0;
    }
}
```

### `@Extension` 注解定义

此注解用于标记一个类，声明它是某个扩展点接口的具体实现。

```java
package org.xkong.cloud.commons.nexus.api.annotation;

import java.lang.annotation.*;

/**
 * 扩展实现注解，用于标记扩展点接口的具体实现类。
 *
 * <p>被标记的类必须满足以下约束：</p>
 * <ul>
 *   <li>类必须是public的</li>
 *   <li>类必须有无参构造函数</li>
 *   <li>类必须实现至少一个@ExtensionPoint标记的接口</li>
 *   <li>类应该是无状态的或线程安全的</li>
 * </ul>
 *
 * @since 1.0.0
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface Extension {

    /**
     * 扩展的名称，必须在同一扩展点内唯一。
     *
     * @return 扩展名称
     */
    String name();

    /**
     * 扩展的加载顺序，值越小优先级越高。
     *
     * @return 加载顺序，默认为0
     */
    int order() default 0;

    /**
     * 扩展的版本。
     *
     * @return 版本号，默认为"1.0"
     * @since 1.1.0
     */
    String version() default "1.0";

    /**
     * 扩展的描述信息。
     *
     * @return 描述信息
     * @since 1.1.0
     */
    String description() default "";

    /**
     * 扩展的提供者。
     *
     * @return 提供者名称
     * @since 1.1.0
     */
    String provider() default "";

    /**
     * 扩展是否默认启用。
     *
     * @return true表示默认启用，false表示需要手动启用
     * @since 1.2.0
     */
    boolean enabled() default true;

    /**
     * 扩展的依赖列表。
     *
     * @return 依赖的扩展名称数组
     * @since 1.2.0
     */
    String[] dependencies() default {};
}
```

### 扩展实现示例

```java
/**
 * PostgreSQL数据源提供者实现
 */
@Extension(
    name = "PostgreSQL Provider",
    order = 100,
    version = "1.0.0",
    description = "PostgreSQL数据库连接提供者",
    provider = "XKongCloud",
    enabled = true,
    dependencies = {}
)
@Component
public class PostgresDataSourceProvider implements DataSourceProvider {

    private static final Logger logger = LoggerFactory.getLogger(PostgresDataSourceProvider.class);

    @Override
    public String getProviderName() {
        return "postgresql";
    }

    @Override
    public boolean supports(String dataSourceType) {
        return "postgresql".equalsIgnoreCase(dataSourceType) ||
               "postgres".equalsIgnoreCase(dataSourceType);
    }

    @Override
    public DataSource createDataSource(DataSourceConfig config) throws DataSourceException {
        try {
            HikariConfig hikariConfig = new HikariConfig();
            hikariConfig.setJdbcUrl(config.getUrl());
            hikariConfig.setUsername(config.getUsername());
            hikariConfig.setPassword(config.getPassword());
            hikariConfig.setDriverClassName("org.postgresql.Driver");

            // PostgreSQL特定配置
            hikariConfig.addDataSourceProperty("stringtype", "unspecified");
            hikariConfig.addDataSourceProperty("prepareThreshold", "0");

            return new HikariDataSource(hikariConfig);

        } catch (Exception e) {
            logger.error("创建PostgreSQL数据源失败: {}", e.getMessage(), e);
            throw new DataSourceException("创建PostgreSQL数据源失败", e);
        }
    }

    @Override
    public String getDialect() {
        return "postgresql";
    }

    @Override
    public Set<String> getSupportedFeatures() {
        return Set.of("basic", "transaction", "batch", "streaming", "json");
    }

    @Override
    public int getPriority() {
        return 100; // 中等优先级
    }
}

/**
 * MySQL数据源提供者实现
 */
@Extension(
    name = "MySQL Provider",
    order = 200,
    version = "1.0.0",
    description = "MySQL数据库连接提供者",
    provider = "XKongCloud"
)
@Component
public class MySqlDataSourceProvider implements DataSourceProvider {

    @Override
    public String getProviderName() {
        return "mysql";
    }

    @Override
    public boolean supports(String dataSourceType) {
        return "mysql".equalsIgnoreCase(dataSourceType);
    }

    @Override
    public DataSource createDataSource(DataSourceConfig config) throws DataSourceException {
        try {
            HikariConfig hikariConfig = new HikariConfig();
            hikariConfig.setJdbcUrl(config.getUrl());
            hikariConfig.setUsername(config.getUsername());
            hikariConfig.setPassword(config.getPassword());
            hikariConfig.setDriverClassName("com.mysql.cj.jdbc.Driver");

            // MySQL特定配置
            hikariConfig.addDataSourceProperty("useSSL", "false");
            hikariConfig.addDataSourceProperty("serverTimezone", "UTC");

            return new HikariDataSource(hikariConfig);

        } catch (Exception e) {
            throw new DataSourceException("创建MySQL数据源失败", e);
        }
    }

    @Override
    public String getDialect() {
        return "mysql";
    }

    @Override
    public Set<String> getSupportedFeatures() {
        return Set.of("basic", "transaction", "batch");
    }

    @Override
    public int getPriority() {
        return 200; // 较低优先级
    }
}
```

## 服务注册与发现机制

服务的生命周期与插件的生命周期紧密绑定，由内核和服务总线自动管理。

### 扩展扫描器设计

```java
package org.xkong.cloud.commons.nexus.servicebus;

import org.xkong.cloud.commons.nexus.api.ServiceBusPublisher;
import org.xkong.cloud.commons.nexus.api.Extension;
import org.xkong.cloud.commons.nexus.api.ExtensionPoint;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 扩展扫描器，负责发现和注册插件中的扩展实现
 *
 * 修复循环依赖：只依赖ServiceBusPublisher进行服务注册，不依赖ServiceRegistry
 */
@Component
public class ExtensionScanner {

    private static final Logger logger = LoggerFactory.getLogger(ExtensionScanner.class);

    private final ServiceBusPublisher serviceBusPublisher;
    private final ApplicationContext applicationContext;

    public ExtensionScanner(ServiceBusPublisher serviceBusPublisher, ApplicationContext applicationContext) {
        this.serviceBusPublisher = serviceBusPublisher;
        this.applicationContext = applicationContext;
    }

    /**
     * 扫描插件中的扩展实现
     *
     * @param pluginClassLoader 插件类加载器
     * @param pluginId 插件ID
     * @return 扫描到的扩展数量
     */
    public int scanExtensions(ClassLoader pluginClassLoader, String pluginId) {
        long startTime = System.nanoTime();
        int extensionCount = 0;

        try {
            // 1. 扫描@Extension注解的类
            Set<Class<?>> extensionClasses = findExtensionClasses(pluginClassLoader);

            // 2. 验证和实例化扩展
            for (Class<?> extensionClass : extensionClasses) {
                try {
                    if (validateExtension(extensionClass)) {
                        Object extensionInstance = instantiateExtension(extensionClass);
                        registerExtension(extensionInstance, extensionClass, pluginId);
                        extensionCount++;
                    }
                } catch (Exception e) {
                    logger.error("注册扩展失败: {}", extensionClass.getName(), e);
                }
            }

            long duration = System.nanoTime() - startTime;
            logger.info("插件 {} 扫描完成，发现 {} 个扩展，耗时 {}ms",
                       pluginId, extensionCount, duration / 1_000_000.0);

            return extensionCount;

        } catch (Exception e) {
            logger.error("扫描插件扩展失败: {}", pluginId, e);
            return 0;
        }
    }

    private Set<Class<?>> findExtensionClasses(ClassLoader classLoader) {
        // 使用反射扫描@Extension注解的类
        // 实现细节...
        return Set.of();
    }

    private boolean validateExtension(Class<?> extensionClass) {
        Extension extension = extensionClass.getAnnotation(Extension.class);
        if (extension == null) {
            return false;
        }

        // 1. 检查类是否public
        if (!Modifier.isPublic(extensionClass.getModifiers())) {
            logger.error("扩展类必须是public的: {}", extensionClass.getName());
            return false;
        }

        // 2. 检查是否有无参构造函数
        try {
            extensionClass.getDeclaredConstructor();
        } catch (NoSuchMethodException e) {
            logger.error("扩展类必须有无参构造函数: {}", extensionClass.getName());
            return false;
        }

        // 3. 检查是否实现了@ExtensionPoint接口
        boolean implementsExtensionPoint = false;
        for (Class<?> interfaceClass : extensionClass.getInterfaces()) {
            if (interfaceClass.isAnnotationPresent(ExtensionPoint.class)) {
                implementsExtensionPoint = true;
                break;
            }
        }

        if (!implementsExtensionPoint) {
            logger.error("扩展类必须实现@ExtensionPoint接口: {}", extensionClass.getName());
            return false;
        }

        return true;
    }

    private Object instantiateExtension(Class<?> extensionClass) throws Exception {
        long startTime = System.nanoTime();

        try {
            Object instance = extensionClass.getDeclaredConstructor().newInstance();

            long duration = System.nanoTime() - startTime;
            if (duration > 10_000_000) { // 10ms
                logger.warn("扩展实例化耗时过长: {} ({}ms)",
                           extensionClass.getName(), duration / 1_000_000.0);
            }

            return instance;

        } catch (Exception e) {
            logger.error("扩展实例化失败: {}", extensionClass.getName(), e);
            throw e;
        }
    }

    private void registerExtension(Object extensionInstance, Class<?> extensionClass, String pluginId) {
        Extension extension = extensionClass.getAnnotation(Extension.class);

        // 为每个实现的@ExtensionPoint接口注册服务
        // 修复循环依赖：只通过ServiceBusPublisher注册，不直接访问ServiceRegistry
        for (Class<?> interfaceClass : extensionClass.getInterfaces()) {
            if (interfaceClass.isAnnotationPresent(ExtensionPoint.class)) {
                Properties properties = new Properties();
                properties.setProperty("extension.name", extension.name());
                properties.setProperty("extension.order", String.valueOf(extension.order()));
                properties.setProperty("extension.version", extension.version());
                properties.setProperty("extension.provider", extension.provider());
                properties.setProperty("plugin.id", pluginId);

                // 使用ServiceBusPublisher进行注册，避免循环依赖
                serviceBusPublisher.registerService(interfaceClass, extensionInstance, properties);

                logger.debug("注册扩展服务: {} -> {}", interfaceClass.getName(), extension.name());
            }
        }
    }
}
```

### 自动服务注册流程（已修复循环依赖）

1. **插件启动触发**: 当内核启动一个插件时，触发扩展扫描流程
2. **注解扫描**: 扫描该插件JAR包中所有被 `@Extension` 注解的类
3. **验证约束**: 验证扩展类是否满足技术约束要求
4. **实例化**: 对于每一个找到的扩展类，自动实例化它
5. **服务注册**: 调用 `ServiceBusPublisher.registerService()`，将实例注册到发布者
6. **事件分发**: ServiceBusPublisher通过服务总线的事件分发机制分发注册事件
7. **订阅者处理**: ServiceBusSubscriber接收事件并更新ServiceRegistry
8. **通知完成**: 发布 `ServiceRegisteredEvent` 事件通知其他插件

**关键改进**: 通过服务总线的事件机制消除了ExtensionScanner与ServiceRegistry的直接依赖，实现了单向依赖流。

### 服务发现机制

其他插件可以通过多种方式来发现和使用这些服务。

#### 方式一：主动查找 (Lookup)

```java
package org.xkong.cloud.commons.nexus.example;

import org.xkong.cloud.commons.nexus.api.ServiceBusSubscriber;
import org.springframework.stereotype.Component;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Optional;
import java.util.List;

/**
 * 数据访问服务管理器示例（已修复循环依赖）
 */
@Component
public class DataAccessManager {

    private final ServiceBusSubscriber serviceBusSubscriber;
    private final Map<String, DataSourceProvider> providerCache = new ConcurrentHashMap<>();

    public DataAccessManager(ServiceBusSubscriber serviceBusSubscriber) {
        this.serviceBusSubscriber = serviceBusSubscriber;
    }

    /**
     * 获取指定类型的数据源提供者
     */
    public Optional<DataSourceProvider> getProvider(String dataSourceType) {
        // 1. 从缓存中查找
        DataSourceProvider cachedProvider = providerCache.get(dataSourceType);
        if (cachedProvider != null) {
            return Optional.of(cachedProvider);
        }

        // 2. 从服务总线订阅者中查找（单向依赖）
        List<DataSourceProvider> providers = serviceBusSubscriber.getServices(DataSourceProvider.class);

        // 3. 按优先级排序并查找支持的提供者
        Optional<DataSourceProvider> provider = providers.stream()
            .filter(p -> p.supports(dataSourceType))
            .sorted(Comparator.comparingInt(DataSourceProvider::getPriority))
            .findFirst();

        // 4. 缓存结果
        provider.ifPresent(p -> providerCache.put(dataSourceType, p));

        return provider;
    }

    /**
     * 获取所有可用的数据源提供者
     */
    public List<DataSourceProvider> getAllProviders() {
        return serviceBusSubscriber.getServices(DataSourceProvider.class).stream()
            .sorted(Comparator.comparingInt(DataSourceProvider::getPriority))
            .collect(Collectors.toList());
    }

    /**
     * 根据特性查找提供者
     */
    public List<DataSourceProvider> getProvidersByFeature(String feature) {
        return serviceBusSubscriber.getServices(DataSourceProvider.class).stream()
            .filter(p -> p.getSupportedFeatures().contains(feature))
            .sorted(Comparator.comparingInt(DataSourceProvider::getPriority))
            .collect(Collectors.toList());
    }
}
```

#### 方式二：事件驱动 (Event-Driven)

为了构建高度动态和弹性的系统，服务总线会在服务状态变更时发布事件。

```java
/**
 * 服务注册事件
 */
public class ServiceRegisteredEvent<T> implements Event {
    private final String eventId;
    private final Instant timestamp;
    private final String sourcePluginId;
    private final Class<T> serviceType;
    private final T serviceInstance;
    private final String serviceName;
    private final Properties serviceProperties;

    public ServiceRegisteredEvent(String sourcePluginId, Class<T> serviceType,
                                 T serviceInstance, String serviceName,
                                 Properties serviceProperties) {
        this.eventId = UUID.randomUUID().toString();
        this.timestamp = Instant.now();
        this.sourcePluginId = sourcePluginId;
        this.serviceType = serviceType;
        this.serviceInstance = serviceInstance;
        this.serviceName = serviceName;
        this.serviceProperties = serviceProperties;
    }

    // Getter方法...
    @Override
    public String getEventId() { return eventId; }
    @Override
    public Instant getTimestamp() { return timestamp; }
    @Override
    public String getSourcePluginId() { return sourcePluginId; }

    public Class<T> getServiceType() { return serviceType; }
    public T getServiceInstance() { return serviceInstance; }
    public String getServiceName() { return serviceName; }
    public Properties getServiceProperties() { return serviceProperties; }
}

/**
 * 服务注销事件
 */
public class ServiceUnregisteredEvent<T> implements Event {
    private final String eventId;
    private final Instant timestamp;
    private final String sourcePluginId;
    private final Class<T> serviceType;
    private final String serviceName;
    private final String reason;

    // 构造函数和getter方法...
}
```

#### 事件驱动服务监控示例

```java
/**
 * 动态服务监控器
 */
@Component
public class DynamicServiceMonitor {

    private static final Logger logger = LoggerFactory.getLogger(DynamicServiceMonitor.class);

    private final Map<String, DataSourceProvider> activeProviders = new ConcurrentHashMap<>();
    private final Map<String, HealthStatus> providerHealth = new ConcurrentHashMap<>();

    @Subscribe(
        priority = 1,
        async = true,
        description = "监控数据源提供者的注册"
    )
    public void onDataSourceProviderRegistered(ServiceRegisteredEvent<DataSourceProvider> event) {
        DataSourceProvider provider = event.getServiceInstance();
        String providerName = provider.getProviderName();

        logger.info("新的数据源提供者上线: {} (插件: {})",
                   providerName, event.getSourcePluginId());

        // 1. 添加到活跃提供者列表
        activeProviders.put(providerName, provider);

        // 2. 初始化健康状态
        providerHealth.put(providerName, HealthStatus.UP);

        // 3. 开始健康检查
        startHealthCheck(provider);

        // 4. 通知其他组件
        notifyProviderAvailable(provider);
    }

    @Subscribe(
        priority = 1,
        async = true,
        description = "监控数据源提供者的注销"
    )
    public void onDataSourceProviderUnregistered(ServiceUnregisteredEvent<DataSourceProvider> event) {
        String serviceName = event.getServiceName();

        logger.info("数据源提供者下线: {} (原因: {})",
                   serviceName, event.getReason());

        // 1. 从活跃列表中移除
        DataSourceProvider provider = activeProviders.remove(serviceName);
        providerHealth.remove(serviceName);

        // 2. 停止健康检查
        stopHealthCheck(serviceName);

        // 3. 通知其他组件
        if (provider != null) {
            notifyProviderUnavailable(provider);
        }
    }

    private void startHealthCheck(DataSourceProvider provider) {
        // 启动定期健康检查
    }

    private void stopHealthCheck(String providerName) {
        // 停止健康检查
    }

    private void notifyProviderAvailable(DataSourceProvider provider) {
        // 通知其他组件有新的提供者可用
    }

    private void notifyProviderUnavailable(DataSourceProvider provider) {
        // 通知其他组件提供者不可用
    }

    /**
     * 获取所有活跃的提供者
     */
    public Map<String, DataSourceProvider> getActiveProviders() {
        return Map.copyOf(activeProviders);
    }

    /**
     * 获取提供者健康状态
     */
    public Map<String, HealthStatus> getProviderHealth() {
        return Map.copyOf(providerHealth);
    }
}
```

这种事件驱动的发现机制，是构建能够优雅地处理插件热插拔、故障转移等高级场景的基础。

## SPI接口定义

### 标准扩展点接口体系

Nexus定义了一系列标准的扩展点接口，为常见功能提供统一的契约：

```java
/**
 * 数据访问服务提供者扩展点
 */
@ExtensionPoint(
    value = "数据访问服务提供者",
    version = "1.0",
    category = "data-access",
    multiple = true,
    required = false
)
public interface DataAccessProvider {

    /**
     * 获取提供者名称
     */
    String getProviderName();

    /**
     * 检查是否支持指定的数据源类型
     */
    boolean supports(String dataSourceType);

    /**
     * 创建数据访问服务实例
     */
    DataAccessService createService(DataSourceConfig config) throws DataAccessException;

    /**
     * 获取支持的操作类型
     */
    Set<OperationType> getSupportedOperations();

    /**
     * 获取连接池配置
     */
    ConnectionPoolConfig getDefaultPoolConfig();
}

/**
 * 缓存服务提供者扩展点
 */
@ExtensionPoint(
    value = "缓存服务提供者",
    version = "1.0",
    category = "cache",
    multiple = true,
    required = false
)
public interface CacheProvider {

    /**
     * 获取提供者名称
     */
    String getProviderName();

    /**
     * 检查是否支持指定的缓存类型
     */
    boolean supports(String cacheType);

    /**
     * 创建缓存服务实例
     */
    CacheService createService(CacheConfig config) throws CacheException;

    /**
     * 获取支持的缓存策略
     */
    Set<CacheStrategy> getSupportedStrategies();

    /**
     * 获取默认过期策略
     */
    ExpirationPolicy getDefaultExpirationPolicy();
}

/**
 * 消息队列提供者扩展点
 */
@ExtensionPoint(
    value = "消息队列提供者",
    version = "1.0",
    category = "messaging",
    multiple = true,
    required = false
)
public interface MessageQueueProvider {

    /**
     * 获取提供者名称
     */
    String getProviderName();

    /**
     * 检查是否支持指定的消息队列类型
     */
    boolean supports(String queueType);

    /**
     * 创建消息队列服务实例
     */
    MessageQueueService createService(MessageQueueConfig config) throws MessageQueueException;

    /**
     * 获取支持的消息模式
     */
    Set<MessagePattern> getSupportedPatterns();

    /**
     * 是否支持事务
     */
    boolean supportsTransaction();
}

/**
 * 安全认证提供者扩展点
 */
@ExtensionPoint(
    value = "安全认证提供者",
    version = "1.0",
    category = "security",
    multiple = true,
    required = true
)
public interface SecurityProvider {

    /**
     * 获取提供者名称
     */
    String getProviderName();

    /**
     * 检查是否支持指定的认证类型
     */
    boolean supports(AuthenticationType authType);

    /**
     * 创建认证服务实例
     */
    AuthenticationService createService(SecurityConfig config) throws SecurityException;

    /**
     * 获取支持的认证方式
     */
    Set<AuthenticationMethod> getSupportedMethods();

    /**
     * 获取安全级别
     */
    SecurityLevel getSecurityLevel();
}
```

### 扩展加载策略详解

#### 1. 优先级排序机制

```java
/**
 * 扩展优先级管理器
 */
@Component
public class ExtensionPriorityManager {

    /**
     * 按优先级排序扩展列表
     */
    public <T> List<T> sortByPriority(List<T> extensions) {
        return extensions.stream()
            .sorted((e1, e2) -> {
                int order1 = getExtensionOrder(e1);
                int order2 = getExtensionOrder(e2);

                // 1. 按order值排序（值越小优先级越高）
                int orderComparison = Integer.compare(order1, order2);
                if (orderComparison != 0) {
                    return orderComparison;
                }

                // 2. 如果order相同，按名称排序保证稳定性
                String name1 = getExtensionName(e1);
                String name2 = getExtensionName(e2);
                return name1.compareTo(name2);
            })
            .collect(Collectors.toList());
    }

    private int getExtensionOrder(Object extension) {
        Extension annotation = extension.getClass().getAnnotation(Extension.class);
        return annotation != null ? annotation.order() : Integer.MAX_VALUE;
    }

    private String getExtensionName(Object extension) {
        Extension annotation = extension.getClass().getAnnotation(Extension.class);
        return annotation != null ? annotation.name() : extension.getClass().getSimpleName();
    }
}
```

#### 2. 名称唯一性验证

```java
/**
 * 扩展名称唯一性验证器
 */
@Component
public class ExtensionNameValidator {

    private final Map<Class<?>, Set<String>> registeredNames = new ConcurrentHashMap<>();

    /**
     * 验证扩展名称是否唯一
     */
    public boolean validateUniqueName(Class<?> extensionPointType, String extensionName) {
        Set<String> names = registeredNames.computeIfAbsent(extensionPointType, k -> ConcurrentHashMap.newKeySet());

        if (names.contains(extensionName)) {
            logger.error("扩展名称冲突: {} 在扩展点 {} 中已存在",
                        extensionName, extensionPointType.getSimpleName());
            return false;
        }

        names.add(extensionName);
        return true;
    }

    /**
     * 移除扩展名称
     */
    public void removeName(Class<?> extensionPointType, String extensionName) {
        Set<String> names = registeredNames.get(extensionPointType);
        if (names != null) {
            names.remove(extensionName);
        }
    }
}
```

#### 3. 依赖解析机制

```java
/**
 * 扩展依赖解析器
 */
@Component
public class ExtensionDependencyResolver {

    /**
     * 解析扩展依赖关系
     */
    public List<String> resolveDependencies(List<Object> extensions) {
        Map<String, Object> extensionMap = new HashMap<>();
        Map<String, Set<String>> dependencyGraph = new HashMap<>();

        // 1. 构建扩展映射和依赖图
        for (Object extension : extensions) {
            Extension annotation = extension.getClass().getAnnotation(Extension.class);
            if (annotation != null) {
                String name = annotation.name();
                extensionMap.put(name, extension);
                dependencyGraph.put(name, Set.of(annotation.dependencies()));
            }
        }

        // 2. 拓扑排序
        return topologicalSort(dependencyGraph);
    }

    private List<String> topologicalSort(Map<String, Set<String>> graph) {
        List<String> result = new ArrayList<>();
        Set<String> visited = new HashSet<>();
        Set<String> visiting = new HashSet<>();

        for (String node : graph.keySet()) {
            if (!visited.contains(node)) {
                if (!dfs(node, graph, visited, visiting, result)) {
                    throw new IllegalStateException("检测到循环依赖");
                }
            }
        }

        Collections.reverse(result);
        return result;
    }

    private boolean dfs(String node, Map<String, Set<String>> graph,
                       Set<String> visited, Set<String> visiting, List<String> result) {
        if (visiting.contains(node)) {
            return false; // 循环依赖
        }

        if (visited.contains(node)) {
            return true;
        }

        visiting.add(node);

        Set<String> dependencies = graph.get(node);
        if (dependencies != null) {
            for (String dependency : dependencies) {
                if (!dfs(dependency, graph, visited, visiting, result)) {
                    return false;
                }
            }
        }

        visiting.remove(node);
        visited.add(node);
        result.add(node);

        return true;
    }
}
```

#### 4. 生命周期绑定策略

- **插件启动**: 自动扫描和注册插件中的所有扩展
- **插件停止**: 自动注销插件中的所有扩展
- **插件重启**: 先注销再重新注册所有扩展
- **热更新**: 支持扩展的动态替换和更新

## 监控与统计

### 关键性能指标 (KPI)

| 指标类别 | 指标名称 | 目标值 | 监控方式 | 告警阈值 |
|----------|----------|--------|----------|----------|
| **扫描性能** | 扩展扫描时间 | ≤200ms | 方法耗时统计 | >500ms |
| **注册性能** | 服务注册时间 | ≤50ms | 方法耗时统计 | >100ms |
| **查找性能** | 服务查找延迟 | ≤0.1ms | 方法耗时统计 | >1ms |
| **实例化性能** | 扩展实例化时间 | ≤10ms | 方法耗时统计 | >50ms |
| **成功率** | 扩展注册成功率 | ≥99% | 成功/失败计数 | <95% |
| **资源使用** | 扩展内存占用 | 每个≤2MB | JVM监控 | >5MB |

## 总结与架构价值

### 核心架构价值

1. **面向契约**: 通过标准化接口实现插件间的松耦合协作
2. **自动化管理**: 基于注解的自动扫描、注册和发现机制
3. **动态扩展**: 支持运行时的服务注册、注销和替换
4. **类型安全**: 基于Java泛型的类型安全服务发现

### 设计模式应用

- **服务定位器模式**: ServiceBus作为服务定位器
- **策略模式**: 扩展点接口定义不同的策略实现
- **工厂模式**: 扩展提供者创建服务实例
- **观察者模式**: 服务状态变更事件通知
- **模板方法模式**: 标准化的扩展点接口模板

### 技术创新点

1. **注解驱动**: 基于@ExtensionPoint和@Extension的声明式扩展机制
2. **优先级排序**: 支持扩展的优先级排序和选择策略
3. **依赖解析**: 支持扩展间的依赖关系声明和自动解析
4. **事件驱动发现**: 基于事件的动态服务发现机制
5. **多维度过滤**: 支持基于特性、优先级、版本等多维度的服务过滤

### 性能特征

- **扩展扫描时间**: ≤200ms
- **服务注册时间**: ≤50ms
- **服务查找延迟**: ≤0.1ms
- **扩展实例化时间**: ≤10ms
- **扩展注册成功率**: ≥99%

### 未来演进方向

- **远程扩展**: 支持跨JVM的远程扩展调用
- **扩展市场**: 支持扩展的动态下载和安装
- **智能路由**: 基于负载和性能的智能扩展选择
- **版本管理**: 支持扩展的版本管理和兼容性检查

这个扩展点与服务发现机制为XKongCloud Commons Nexus框架提供了强大的插件协作能力，确保了系统的高度可扩展性和灵活性。
