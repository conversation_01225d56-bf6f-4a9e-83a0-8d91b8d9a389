# V4SQLite全景模型数据库设计（三重验证增强版）

## 📋 文档概述

**文档ID**: V4-SQLITE-PANORAMIC-MODEL-DATABASE-DESIGN-017
**创建日期**: 2025-06-16
**版本**: V4.0-Triple-Verification-Enhanced-SQLite-Database-Design
**目标**: 融入三重验证机制，实现93.3%整体执行正确度的SQLite全景模型数据库设计
**模板引用**: @MEM_LIB:L1-core/V4架构信息AI填充模板#三重验证增强版

## 🛡️ 93.3%整体执行正确度SQLite全景模型核心理念（三重验证增强版）

### 设计原则（基于三重验证机制优化）
基于三重验证机制分析，V4.0系统建立93.3%整体执行正确度SQLite全景模型数据库设计，确保：
- **三重验证质量保障**：V4算法全景验证+Python AI关系逻辑链验证+IDE AI模板验证
- **93.3%精准目标**：替代95%置信度，基于实测数据的更精准数据库设计标准
- **分层置信度管理**：95%+/85-94%/68-82%三层置信度域差异化数据库策略
- **矛盾检测收敛**：严重矛盾减少75%，中等矛盾减少60%，总体矛盾减少50%
- **智能扫描优化**：基于SQLite全景模型的快速扫描vs全量重建智能决策
- **端到端质量控制**：从数据存储到扫描优化的全流程三重验证质量保证

## 🎯 核心需求分析（基于用户原始需求）

### 用户核心需求解析
```yaml
# 基于用户原始需求的SQLite全景模型机制设计
user_core_requirements_analysis:
  core_scanning_algorithm_requirement: |
    @REQUIREMENT:SQLite本地存储有全局的模型，全景的模型里边有设计文档的历史抽象以及版本
    实现目标=建立SQLite本地存储的全景模型数据库
    数据内容=设计文档历史抽象+版本信息+哈希值+映射关系
    
  version_hash_detection_requirement: |
    @REQUIREMENT:版本号和设计文档的哈希值确定修改过没有
    检测机制=版本号+哈希值双重检测
    警告机制=hash改变但版本未改变时警告提示用户
    更新机制=版本改变时更新SQLite全景数据抽象数据
    
  fast_scanning_optimization_requirement: |
    @REQUIREMENT:实现快速的扫描，而不是全部重新文档重新建模
    性能目标=避免全量文档重新建模
    扫描策略=基于SQLite全景模型的增量扫描
    决策逻辑=智能判断增量扫描vs全量重建
```

### 架构集成约束
```yaml
# 与现有V4架构的集成约束
v4_architecture_integration_constraints:
  triple_verification_compatibility: |
    @CONSTRAINT:必须与三重验证机制完全兼容
    集成要求=SQLite数据库操作需要通过三重验证机制验证
    质量保障=93.3%整体执行正确度通过三重验证保障
    
  existing_storage_architecture_enhancement: |
    @CONSTRAINT:增强现有V4安全存储架构，不替换
    现有架构=混合分层存储架构（热数据层+温数据层+冷数据层）
    SQLite定位=温数据层核心技术，增强现有SQLite应用级加密设计
    性能要求=≤100ms查询时间，≤4GB容量支持大型项目
    
  v3_v31_algorithm_reuse_compatibility: |
    @CONSTRAINT:必须支持V3/V3.1算法复用策略
    复用要求=70%复用率，直接复制核心代码块避免接口依赖
    数据兼容=支持V3扫描器数据格式，提供数据迁移机制
```

## 🗄️ SQLite数据库Schema设计

### 核心表结构设计
```sql
-- V4全景模型SQLite数据库Schema设计
-- 基于三重验证机制和93.3%整体执行正确度要求

-- 1. 全景模型主表（设计文档抽象数据存储）
CREATE TABLE panoramic_models (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_path TEXT NOT NULL UNIQUE,           -- 相对路径存储策略
    version_number TEXT NOT NULL,                 -- Fxxx格式版本号
    content_hash TEXT NOT NULL,                   -- 文档内容哈希值
    semantic_hash TEXT NOT NULL,                  -- 语义级别哈希值
    abstraction_data TEXT NOT NULL,               -- JSON格式的抽象数据
    relationships_data TEXT,                      -- JSON格式的关联关系数据
    quality_metrics TEXT,                         -- JSON格式的质量评估数据
    triple_verification_status TEXT DEFAULT 'PENDING', -- 三重验证状态
    confidence_score REAL DEFAULT 0.0,           -- 置信度评分
    panoramic_reliability_status TEXT DEFAULT 'PENDING_USER_CONFIRMATION', -- 全景可靠性状态
    user_confirmation_status TEXT DEFAULT 'NOT_CONFIRMED', -- 用户确认状态
    user_confirmed_by TEXT,                       -- 确认用户
    user_confirmed_at TIMESTAMP,                  -- 用户确认时间
    architecture_debt_check_status TEXT DEFAULT 'NOT_CHECKED', -- 架构负债检查状态
    last_architecture_debt_check TIMESTAMP,      -- 最后架构负债检查时间
    version_lag_warning_count INTEGER DEFAULT 0, -- 版本落后警告次数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 文档历史记录表（版本演进追踪）
CREATE TABLE document_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_path TEXT NOT NULL,
    version_number TEXT NOT NULL,
    content_hash TEXT NOT NULL,
    semantic_hash TEXT NOT NULL,
    change_type TEXT NOT NULL,                    -- 'created', 'version_updated', 'content_modified', 'deleted'
    change_description TEXT,
    abstraction_delta TEXT,                       -- JSON格式的变更差异
    previous_version TEXT,                        -- 前一版本号
    migration_status TEXT DEFAULT 'COMPLETED',    -- 迁移状态
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 扫描任务记录表（智能扫描决策支持）
CREATE TABLE scan_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT NOT NULL UNIQUE,
    scan_mode TEXT NOT NULL,                      -- 'fast', 'incremental', 'full_rebuild'
    target_documents TEXT NOT NULL,               -- JSON数组格式
    execution_time_ms INTEGER,
    documents_processed INTEGER,
    documents_updated INTEGER,
    warnings_generated INTEGER,
    confidence_improvement REAL DEFAULT 0.0,     -- 置信度提升幅度
    triple_verification_passed BOOLEAN DEFAULT FALSE,
    status TEXT NOT NULL,                         -- 'running', 'completed', 'failed'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- 4. 版本警告记录表（版本-哈希不匹配管理）
CREATE TABLE version_warnings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_path TEXT NOT NULL,
    warning_type TEXT NOT NULL,                   -- 'hash_changed_version_same', 'version_conflict', 'inconsistent_mapping'
    old_version TEXT,
    current_version TEXT,
    old_hash TEXT,
    current_hash TEXT,
    warning_message TEXT NOT NULL,
    user_action TEXT,                             -- 'ignored', 'version_updated', 'content_reverted'
    resolution_strategy TEXT,                     -- 解决策略
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. 设计文档到实施计划映射表（文档关联关系管理）
CREATE TABLE design_to_implementation_mapping (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    design_document_path TEXT NOT NULL,           -- 设计文档路径
    design_document_version TEXT NOT NULL,        -- 设计文档版本号
    design_document_hash TEXT NOT NULL,           -- 设计文档哈希值
    implementation_plan_path TEXT NOT NULL,       -- 实施计划文档路径
    implementation_plan_version TEXT NOT NULL,    -- 实施计划文档版本号
    implementation_plan_hash TEXT NOT NULL,       -- 实施计划文档哈希值
    mapping_type TEXT NOT NULL,                   -- 'one_to_one', 'one_to_many', 'many_to_one'
    generation_method TEXT NOT NULL,              -- 'ai_generated', 'manual_created', 'hybrid'
    sync_status TEXT DEFAULT 'synchronized',      -- 'synchronized', 'design_newer', 'implementation_newer', 'conflict'
    last_sync_timestamp TIMESTAMP,               -- 最后同步时间
    dependency_level INTEGER DEFAULT 1,          -- 依赖层级（1=直接依赖，2=间接依赖）
    quality_score REAL DEFAULT 0.0,              -- 映射质量评分
    triple_verification_status TEXT DEFAULT 'PENDING', -- 三重验证状态
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. 实施计划文档全景模型表（实施计划抽象数据存储）
CREATE TABLE implementation_plan_models (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    implementation_plan_path TEXT NOT NULL UNIQUE, -- 实施计划文档路径
    version_number TEXT NOT NULL,                 -- 实施计划版本号
    content_hash TEXT NOT NULL,                   -- 实施计划内容哈希值
    semantic_hash TEXT NOT NULL,                  -- 语义级别哈希值
    plan_abstraction_data TEXT NOT NULL,          -- JSON格式的实施计划抽象数据
    step_dependencies_data TEXT,                  -- JSON格式的步骤依赖关系数据
    execution_metadata TEXT,                      -- JSON格式的执行元数据
    design_document_references TEXT,              -- JSON格式的设计文档引用列表
    implementation_complexity_score REAL DEFAULT 0.0, -- 实施复杂度评分
    estimated_execution_time INTEGER,             -- 预估执行时间（分钟）
    triple_verification_status TEXT DEFAULT 'PENDING', -- 三重验证状态
    confidence_score REAL DEFAULT 0.0,           -- 置信度评分
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 7. 版本落后检测和架构负债提醒表（版本管理核心）
CREATE TABLE version_lag_and_architecture_debt_alerts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_path TEXT NOT NULL,                  -- 文档路径
    current_version TEXT NOT NULL,                -- 当前版本
    latest_available_version TEXT,                -- 最新可用版本
    version_lag_severity TEXT NOT NULL,           -- 'minor', 'moderate', 'severe', 'critical'
    architecture_debt_items TEXT,                 -- JSON格式的架构负债项目
    debt_impact_assessment TEXT,                  -- JSON格式的负债影响评估
    recommended_actions TEXT,                     -- JSON格式的推荐行动
    alert_frequency TEXT DEFAULT 'daily',         -- 提醒频率
    last_alert_sent TIMESTAMP,                    -- 最后提醒时间
    user_response TEXT,                           -- 用户响应：'acknowledged', 'deferred', 'in_progress', 'resolved'
    user_response_reason TEXT,                    -- 用户响应原因
    user_response_timestamp TIMESTAMP,            -- 用户响应时间
    auto_check_enabled BOOLEAN DEFAULT TRUE,      -- 是否启用自动检查
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. 全景模型用户确认记录表（可靠性确认核心）
CREATE TABLE panoramic_model_user_confirmations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_path TEXT NOT NULL,                  -- 文档路径
    panoramic_model_id INTEGER NOT NULL,          -- 关联的全景模型ID
    confirmation_type TEXT NOT NULL,              -- 'initial_confirmation', 'update_confirmation', 'reliability_reconfirmation'
    confirmation_status TEXT NOT NULL,            -- 'pending', 'confirmed', 'rejected', 'needs_revision'
    user_feedback TEXT,                           -- 用户反馈
    reliability_assessment TEXT,                  -- 用户的可靠性评估
    confidence_adjustment REAL DEFAULT 0.0,      -- 用户的置信度调整
    confirmation_notes TEXT,                      -- 确认备注
    confirmed_by TEXT,                            -- 确认用户
    confirmed_at TIMESTAMP,                       -- 确认时间
    expires_at TIMESTAMP,                         -- 确认过期时间
    reconfirmation_required BOOLEAN DEFAULT FALSE, -- 是否需要重新确认
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (panoramic_model_id) REFERENCES panoramic_models(id)
);

-- 9. 全景模型配置表（系统配置管理）
CREATE TABLE panoramic_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key TEXT NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    config_type TEXT NOT NULL,                    -- 'system', 'scanning', 'verification'
    description TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,           -- 是否加密存储
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 10. 代码文件版本映射表（多对多映射关系核心表）
CREATE TABLE code_version_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code_file_path TEXT NOT NULL,                 -- 代码文件路径
    code_file_version TEXT NOT NULL,              -- 代码文件版本
    code_file_hash TEXT NOT NULL,                 -- 代码文件哈希值
    design_document_path TEXT NOT NULL,           -- 设计文档路径
    design_document_version TEXT NOT NULL,        -- 设计文档版本
    implementation_plan_path TEXT,                -- 实施计划路径（可选）
    implementation_plan_version TEXT,             -- 实施计划版本（可选）
    mapping_type TEXT NOT NULL,                   -- 'implements', 'extends', 'configures', 'tests'
    mapping_strength REAL DEFAULT 0.0,            -- 映射强度 0.0-1.0
    dependency_level INTEGER DEFAULT 1,           -- 依赖层级
    sync_status TEXT DEFAULT 'synchronized',      -- 'synchronized', 'design_newer', 'code_newer', 'conflict'
    last_sync_check TIMESTAMP,                    -- 最后同步检查时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (design_document_path) REFERENCES panoramic_models(document_path),
    FOREIGN KEY (implementation_plan_path) REFERENCES implementation_plan_models(implementation_plan_path)
);

-- 11. 代码文件多版本头部信息表（代码头部版本信息管理）
CREATE TABLE code_file_version_headers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code_file_path TEXT NOT NULL,                 -- 代码文件路径
    code_file_version TEXT NOT NULL,              -- 代码文件版本
    design_version_mappings TEXT NOT NULL,        -- JSON格式存储多个设计版本映射
    header_template_type TEXT DEFAULT 'standard', -- 'standard', 'enhanced', 'minimal'
    auto_generated BOOLEAN DEFAULT TRUE,          -- 是否自动生成
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(code_file_path, code_file_version)
);

-- 12. 版本演进路径表（版本演进历史追踪）
CREATE TABLE version_evolution_paths (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_path TEXT NOT NULL,                  -- 文档路径
    document_type TEXT NOT NULL,                  -- 'design', 'implementation', 'code'
    from_version TEXT NOT NULL,                   -- 源版本
    to_version TEXT NOT NULL,                     -- 目标版本
    evolution_type TEXT NOT NULL,                 -- 'major', 'minor', 'patch', 'hotfix'
    change_description TEXT,                      -- 变更描述
    affected_mappings TEXT,                       -- JSON格式存储受影响的映射关系
    migration_required BOOLEAN DEFAULT FALSE,     -- 是否需要迁移
    migration_completed BOOLEAN DEFAULT FALSE,    -- 迁移是否完成
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 性能优化索引设计
```sql
-- 性能优化索引策略
-- 基于快速扫描需求和查询模式优化

-- 主要查询索引
CREATE INDEX idx_doc_path_version ON panoramic_models(document_path, version_number);
CREATE INDEX idx_content_hash ON panoramic_models(content_hash);
CREATE INDEX idx_semantic_hash ON panoramic_models(semantic_hash);
CREATE INDEX idx_confidence_score ON panoramic_models(confidence_score DESC);
CREATE INDEX idx_triple_verification_status ON panoramic_models(triple_verification_status);

-- 历史查询索引
CREATE INDEX idx_doc_path_history ON document_history(document_path, timestamp DESC);
CREATE INDEX idx_change_type_timestamp ON document_history(change_type, timestamp DESC);
CREATE INDEX idx_version_migration ON document_history(version_number, migration_status);

-- 扫描任务索引
CREATE INDEX idx_scan_task_status ON scan_tasks(status, created_at);
CREATE INDEX idx_scan_mode_performance ON scan_tasks(scan_mode, execution_time_ms);
CREATE INDEX idx_triple_verification_passed ON scan_tasks(triple_verification_passed, completed_at);

-- 警告管理索引
CREATE INDEX idx_warnings_unresolved ON version_warnings(document_path, resolved_at);
CREATE INDEX idx_warning_type_created ON version_warnings(warning_type, created_at DESC);

-- 设计文档到实施计划映射索引
CREATE INDEX idx_design_to_impl_design_path ON design_to_implementation_mapping(design_document_path, design_document_version);
CREATE INDEX idx_design_to_impl_impl_path ON design_to_implementation_mapping(implementation_plan_path, implementation_plan_version);
CREATE INDEX idx_design_to_impl_sync_status ON design_to_implementation_mapping(sync_status, last_sync_timestamp);
CREATE INDEX idx_design_to_impl_mapping_type ON design_to_implementation_mapping(mapping_type, dependency_level);
CREATE INDEX idx_design_to_impl_triple_verification ON design_to_implementation_mapping(triple_verification_status);

-- 实施计划模型索引
CREATE INDEX idx_impl_plan_path_version ON implementation_plan_models(implementation_plan_path, version_number);
CREATE INDEX idx_impl_plan_content_hash ON implementation_plan_models(content_hash);
CREATE INDEX idx_impl_plan_semantic_hash ON implementation_plan_models(semantic_hash);
CREATE INDEX idx_impl_plan_confidence_score ON implementation_plan_models(confidence_score DESC);
CREATE INDEX idx_impl_plan_complexity_score ON implementation_plan_models(implementation_complexity_score DESC);
CREATE INDEX idx_impl_plan_triple_verification ON implementation_plan_models(triple_verification_status);

-- 版本落后和架构负债提醒索引
CREATE INDEX idx_version_lag_document_path ON version_lag_and_architecture_debt_alerts(document_path, version_lag_severity);
CREATE INDEX idx_version_lag_alert_frequency ON version_lag_and_architecture_debt_alerts(alert_frequency, last_alert_sent);
CREATE INDEX idx_version_lag_user_response ON version_lag_and_architecture_debt_alerts(user_response, user_response_timestamp);
CREATE INDEX idx_version_lag_auto_check ON version_lag_and_architecture_debt_alerts(auto_check_enabled, updated_at);

-- 全景模型用户确认索引
CREATE INDEX idx_user_confirmation_document ON panoramic_model_user_confirmations(document_path, confirmation_status);
CREATE INDEX idx_user_confirmation_model_id ON panoramic_model_user_confirmations(panoramic_model_id, confirmation_type);
CREATE INDEX idx_user_confirmation_pending ON panoramic_model_user_confirmations(confirmation_status, created_at);
CREATE INDEX idx_user_confirmation_expires ON panoramic_model_user_confirmations(expires_at, reconfirmation_required);

-- 全景模型可靠性状态索引
CREATE INDEX idx_panoramic_reliability_status ON panoramic_models(panoramic_reliability_status, user_confirmation_status);
CREATE INDEX idx_panoramic_user_confirmed ON panoramic_models(user_confirmed_by, user_confirmed_at);
CREATE INDEX idx_panoramic_architecture_debt ON panoramic_models(architecture_debt_check_status, last_architecture_debt_check);

-- 配置查询索引
CREATE INDEX idx_config_key_type ON panoramic_config(config_key, config_type);

-- 代码版本映射表索引
CREATE INDEX idx_code_version_mappings_code_file_path ON code_version_mappings(code_file_path);
CREATE INDEX idx_code_version_mappings_design_document_path ON code_version_mappings(design_document_path);
CREATE INDEX idx_code_version_mappings_sync_status ON code_version_mappings(sync_status);
CREATE INDEX idx_code_version_mappings_mapping_type ON code_version_mappings(mapping_type, mapping_strength);

-- 代码文件版本头部信息表索引
CREATE INDEX idx_code_file_version_headers_code_file_path ON code_file_version_headers(code_file_path);
CREATE INDEX idx_code_file_version_headers_last_updated ON code_file_version_headers(last_updated);

-- 版本演进路径表索引
CREATE INDEX idx_version_evolution_paths_document_path ON version_evolution_paths(document_path);
CREATE INDEX idx_version_evolution_paths_document_type ON version_evolution_paths(document_type);
CREATE INDEX idx_version_evolution_paths_evolution_type ON version_evolution_paths(evolution_type, created_at);
```

## 🏗️ 核心数据库操作类设计

### PanoramicModelDatabase类（全景模型数据库核心）
```python
# src/v4/database/panoramic_model_database.py
# 全景模型SQLite数据库核心操作类

import sqlite3
import json
import hashlib
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path
from cryptography.fernet import Fernet

class PanoramicModelDatabase:
    """全景模型SQLite数据库 - V4核心数据存储引擎"""

    def __init__(self, db_path: str, encryption_key: Optional[str] = None):
        """初始化全景模型数据库

        Args:
            db_path: 数据库文件路径
            encryption_key: 加密密钥（可选）
        """
        self.db_path = db_path
        self.encryption_key = encryption_key
        self.fernet = Fernet(encryption_key.encode()) if encryption_key else None
        self.connection = None
        self._initialize_database()

    def _initialize_database(self):
        """初始化数据库和表结构"""
        # 确保数据库目录存在
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)

        # 创建数据库连接
        self.connection = sqlite3.connect(self.db_path)
        self.connection.row_factory = sqlite3.Row  # 支持字典式访问

        # 创建表结构
        self._create_tables()
        self._create_indexes()

        # 初始化配置
        self._initialize_config()

    def _create_tables(self):
        """创建数据库表结构"""
        cursor = self.connection.cursor()

        # 执行Schema创建脚本
        schema_sql = """
        -- 全景模型主表
        CREATE TABLE IF NOT EXISTS panoramic_models (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            document_path TEXT NOT NULL UNIQUE,
            version_number TEXT NOT NULL,
            content_hash TEXT NOT NULL,
            semantic_hash TEXT NOT NULL,
            abstraction_data TEXT NOT NULL,
            relationships_data TEXT,
            quality_metrics TEXT,
            triple_verification_status TEXT DEFAULT 'PENDING',
            confidence_score REAL DEFAULT 0.0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- 文档历史记录表
        CREATE TABLE IF NOT EXISTS document_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            document_path TEXT NOT NULL,
            version_number TEXT NOT NULL,
            content_hash TEXT NOT NULL,
            semantic_hash TEXT NOT NULL,
            change_type TEXT NOT NULL,
            change_description TEXT,
            abstraction_delta TEXT,
            previous_version TEXT,
            migration_status TEXT DEFAULT 'COMPLETED',
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- 扫描任务记录表
        CREATE TABLE IF NOT EXISTS scan_tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_id TEXT NOT NULL UNIQUE,
            scan_mode TEXT NOT NULL,
            target_documents TEXT NOT NULL,
            execution_time_ms INTEGER,
            documents_processed INTEGER,
            documents_updated INTEGER,
            warnings_generated INTEGER,
            confidence_improvement REAL DEFAULT 0.0,
            triple_verification_passed BOOLEAN DEFAULT FALSE,
            status TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completed_at TIMESTAMP
        );

        -- 版本警告记录表
        CREATE TABLE IF NOT EXISTS version_warnings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            document_path TEXT NOT NULL,
            warning_type TEXT NOT NULL,
            old_version TEXT,
            current_version TEXT,
            old_hash TEXT,
            current_hash TEXT,
            warning_message TEXT NOT NULL,
            user_action TEXT,
            resolution_strategy TEXT,
            resolved_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- 设计文档到实施计划映射表
        CREATE TABLE IF NOT EXISTS design_to_implementation_mapping (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            design_document_path TEXT NOT NULL,
            design_document_version TEXT NOT NULL,
            design_document_hash TEXT NOT NULL,
            implementation_plan_path TEXT NOT NULL,
            implementation_plan_version TEXT NOT NULL,
            implementation_plan_hash TEXT NOT NULL,
            mapping_type TEXT NOT NULL,
            generation_method TEXT NOT NULL,
            sync_status TEXT DEFAULT 'synchronized',
            last_sync_timestamp TIMESTAMP,
            dependency_level INTEGER DEFAULT 1,
            quality_score REAL DEFAULT 0.0,
            triple_verification_status TEXT DEFAULT 'PENDING',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- 实施计划模型表
        CREATE TABLE IF NOT EXISTS implementation_plan_models (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            implementation_plan_path TEXT NOT NULL UNIQUE,
            version_number TEXT NOT NULL,
            content_hash TEXT NOT NULL,
            semantic_hash TEXT NOT NULL,
            plan_abstraction_data TEXT NOT NULL,
            step_dependencies_data TEXT,
            execution_metadata TEXT,
            design_document_references TEXT,
            implementation_complexity_score REAL DEFAULT 0.0,
            estimated_execution_time INTEGER,
            triple_verification_status TEXT DEFAULT 'PENDING',
            confidence_score REAL DEFAULT 0.0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- 全景模型配置表
        CREATE TABLE IF NOT EXISTS panoramic_config (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            config_key TEXT NOT NULL UNIQUE,
            config_value TEXT NOT NULL,
            config_type TEXT NOT NULL,
            description TEXT,
            is_encrypted BOOLEAN DEFAULT FALSE,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """

        cursor.executescript(schema_sql)
        self.connection.commit()

    def _create_indexes(self):
        """创建性能优化索引"""
        cursor = self.connection.cursor()

        indexes_sql = """
        -- 主要查询索引
        CREATE INDEX IF NOT EXISTS idx_doc_path_version ON panoramic_models(document_path, version_number);
        CREATE INDEX IF NOT EXISTS idx_content_hash ON panoramic_models(content_hash);
        CREATE INDEX IF NOT EXISTS idx_semantic_hash ON panoramic_models(semantic_hash);
        CREATE INDEX IF NOT EXISTS idx_confidence_score ON panoramic_models(confidence_score DESC);
        CREATE INDEX IF NOT EXISTS idx_triple_verification_status ON panoramic_models(triple_verification_status);

        -- 历史查询索引
        CREATE INDEX IF NOT EXISTS idx_doc_path_history ON document_history(document_path, timestamp DESC);
        CREATE INDEX IF NOT EXISTS idx_change_type_timestamp ON document_history(change_type, timestamp DESC);
        CREATE INDEX IF NOT EXISTS idx_version_migration ON document_history(version_number, migration_status);

        -- 扫描任务索引
        CREATE INDEX IF NOT EXISTS idx_scan_task_status ON scan_tasks(status, created_at);
        CREATE INDEX IF NOT EXISTS idx_scan_mode_performance ON scan_tasks(scan_mode, execution_time_ms);
        CREATE INDEX IF NOT EXISTS idx_triple_verification_passed ON scan_tasks(triple_verification_passed, completed_at);

        -- 警告管理索引
        CREATE INDEX IF NOT EXISTS idx_warnings_unresolved ON version_warnings(document_path, resolved_at);
        CREATE INDEX IF NOT EXISTS idx_warning_type_created ON version_warnings(warning_type, created_at DESC);

        -- 配置查询索引
        CREATE INDEX IF NOT EXISTS idx_config_key_type ON panoramic_config(config_key, config_type);
        """

        cursor.executescript(indexes_sql)
        self.connection.commit()

    # ========== 代码版本映射管理方法 ==========

    def establish_code_version_mapping(
        self,
        code_file_path: str,
        code_file_version: str,
        code_file_hash: str,
        design_document_path: str,
        design_document_version: str,
        implementation_plan_path: str = None,
        implementation_plan_version: str = None,
        mapping_type: str = "implements",
        mapping_strength: float = 1.0
    ) -> bool:
        """
        建立代码文件与设计文档的版本映射关系

        核心功能：
        - 支持一个代码文件对应多个设计文档的多对多映射
        - 记录映射强度和依赖层级，为版本管理提供决策依据
        - 自动检测映射冲突，提醒用户解决版本不一致问题

        Args:
            code_file_path: 代码文件路径
            code_file_version: 代码文件版本
            code_file_hash: 代码文件哈希值
            design_document_path: 设计文档路径
            design_document_version: 设计文档版本
            implementation_plan_path: 实施计划路径（可选）
            implementation_plan_version: 实施计划版本（可选）
            mapping_type: 映射类型
            mapping_strength: 映射强度

        Returns:
            bool: 建立映射是否成功
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT INTO code_version_mappings
                (code_file_path, code_file_version, code_file_hash,
                 design_document_path, design_document_version,
                 implementation_plan_path, implementation_plan_version,
                 mapping_type, mapping_strength)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                code_file_path,
                code_file_version,
                code_file_hash,
                design_document_path,
                design_document_version,
                implementation_plan_path,
                implementation_plan_version,
                mapping_type,
                mapping_strength
            ))
            self.connection.commit()
            return True

        except Exception as e:
            print(f"❌ 建立代码版本映射失败: {e}")
            return False

    def get_code_design_mappings(self, code_file_path: str) -> List[Dict[str, Any]]:
        """
        获取代码文件的所有设计文档映射关系

        Args:
            code_file_path: 代码文件路径

        Returns:
            List[Dict]: 映射关系列表
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT design_document_path, design_document_version,
                       implementation_plan_path, implementation_plan_version,
                       mapping_type, mapping_strength, sync_status
                FROM code_version_mappings
                WHERE code_file_path = ?
                ORDER BY mapping_strength DESC
            """, (code_file_path,))

            rows = cursor.fetchall()
            return [{
                "design_document_path": row[0],
                "design_document_version": row[1],
                "implementation_plan_path": row[2],
                "implementation_plan_version": row[3],
                "mapping_type": row[4],
                "mapping_strength": row[5],
                "sync_status": row[6]
            } for row in rows]

        except Exception as e:
            print(f"❌ 获取代码设计映射失败: {e}")
            return []

    def update_code_file_version_header(
        self,
        code_file_path: str,
        code_file_version: str,
        design_version_mappings: Dict[str, str]
    ) -> bool:
        """
        更新代码文件头部版本信息

        核心功能：
        - 自动生成代码文件头部的多设计版本信息
        - 提供标准化的版本信息格式，便于版本追踪
        - 支持自动更新和手动确认机制

        Args:
            code_file_path: 代码文件路径
            code_file_version: 代码文件版本
            design_version_mappings: 设计版本映射字典

        Returns:
            bool: 更新是否成功
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO code_file_version_headers
                (code_file_path, code_file_version, design_version_mappings)
                VALUES (?, ?, ?)
            """, (
                code_file_path,
                code_file_version,
                json.dumps(design_version_mappings, ensure_ascii=False)
            ))
            self.connection.commit()
            return True

        except Exception as e:
            print(f"❌ 更新代码文件版本头部信息失败: {e}")
            return False

    def check_version_sync_conflicts(self, code_file_path: str) -> List[Dict[str, Any]]:
        """
        检查代码文件的版本同步冲突

        核心功能：
        - 检测代码版本与设计文档版本的不一致
        - 识别多个设计文档版本之间的冲突
        - 提供具体的冲突解决建议

        Args:
            code_file_path: 代码文件路径

        Returns:
            List[Dict]: 冲突列表
        """
        try:
            mappings = self.get_code_design_mappings(code_file_path)
            conflicts = []

            for mapping in mappings:
                if mapping["sync_status"] != "synchronized":
                    conflicts.append({
                        "conflict_type": "version_mismatch",
                        "design_document": mapping["design_document_path"],
                        "design_version": mapping["design_document_version"],
                        "sync_status": mapping["sync_status"],
                        "recommendation": self._generate_sync_recommendation(mapping)
                    })

            return conflicts

        except Exception as e:
            print(f"❌ 检查版本同步冲突失败: {e}")
            return []

    def _generate_sync_recommendation(self, mapping: Dict[str, Any]) -> str:
        """生成同步建议"""
        sync_status = mapping["sync_status"]
        design_doc = mapping["design_document_path"]

        if sync_status == "design_newer":
            return f"设计文档 {design_doc} 版本更新，建议同步更新代码实现"
        elif sync_status == "code_newer":
            return f"代码实现已更新，建议检查是否需要更新设计文档 {design_doc}"
        elif sync_status == "conflict":
            return f"设计文档 {design_doc} 与代码实现存在版本冲突，需要手动解决"
        else:
            return "建议检查版本一致性"

## 🔧 代码版本头部信息生成器设计（只读版本号权限控制）

### 版本号权限控制原则
```yaml
# 版本号权限控制设计原则
version_number_permission_control:
  read_only_principle: |
    @PRINCIPLE:任何Python代码都不能直接修改版本号，只能读取
    权限限制=所有代码组件只有版本号读取权限
    修改权限=仅限实施计划文档和IDE AI

  version_modification_authority: |
    @AUTHORITY:版本号修改权限严格控制
    授权方式1=通过实施计划文档修改版本号
    授权方式2=通过IDE AI工具修改版本号
    禁止方式=Python代码直接修改文件版本号

  code_header_version_mapping: |
    @MAPPING:代码头部版本信息映射
    映射内容=设计1版本，设计2版本，设计3版本...
    数据来源=从SQLite全景模型数据库读取映射关系
    更新机制=仅读取和显示，不修改版本号
```

### CodeVersionHeaderGenerator类（只读版本号设计）
```python
# src/v4/utils/code_version_header_generator.py
# 代码版本头部信息生成器 - 只读版本号权限控制版本

from typing import Dict, List, Optional
from pathlib import Path
from datetime import datetime
import json

class CodeVersionHeaderGenerator:
    """
    代码版本头部信息生成器（只读版本号权限控制）

    核心原则：
    1. 只能读取版本号，不能修改版本号
    2. 版本号修改权限仅限实施计划文档和IDE AI
    3. 支持多设计版本映射的代码头部信息生成
    4. 提供版本同步状态检查，但不自动修改
    """

    def __init__(self, panoramic_db: PanoramicModelDatabase):
        self.panoramic_db = panoramic_db

        # 支持的编程语言注释格式
        self.comment_formats = {
            ".py": {"start": "# ", "end": "", "block_start": '"""', "block_end": '"""'},
            ".java": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".js": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".ts": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".cpp": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".c": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".go": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".rs": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".md": {"start": "", "end": "", "block_start": "<!--", "block_end": "-->"}
        }

    def read_current_version_from_file(self, file_path: str) -> str:
        """
        从文件中读取当前版本号（只读权限）

        权限控制：只能读取，不能修改

        Args:
            file_path: 文件路径

        Returns:
            str: 当前版本号
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 尝试从文件内容中提取版本号
            import re
            version_patterns = [
                r'版本[：:]\s*([V]?\d+\.\d+(?:\.\d+)?)',
                r'Version[：:]\s*([V]?\d+\.\d+(?:\.\d+)?)',
                r'version[：:]\s*([V]?\d+\.\d+(?:\.\d+)?)',
                r'v(\d+\.\d+(?:\.\d+)?)',
                r'V(\d+\.\d+(?:\.\d+)?)'
            ]

            for pattern in version_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    return match.group(1) if match.group(1) else match.group(0)

            return "1.0.0"  # 默认版本

        except Exception as e:
            print(f"⚠️ 读取文件版本失败: {e}")
            return "unknown"

    def generate_version_header_content(
        self,
        code_file_path: str,
        template_type: str = "enhanced"
    ) -> str:
        """
        生成代码文件版本头部信息内容（不修改文件）

        权限控制：只生成内容，不直接修改文件

        Args:
            code_file_path: 代码文件路径
            template_type: 模板类型 ('standard', 'enhanced', 'minimal')

        Returns:
            str: 生成的版本头部信息字符串
        """

        # 读取当前版本号（只读权限）
        current_version = self.read_current_version_from_file(code_file_path)

        # 获取文件扩展名和注释格式
        file_extension = Path(code_file_path).suffix
        comment_format = self.comment_formats.get(file_extension, self.comment_formats[".py"])

        # 从数据库读取设计文档映射关系
        mappings = self.panoramic_db.get_code_design_mappings(code_file_path)

        # 根据模板类型生成头部信息
        if template_type == "enhanced":
            return self._generate_enhanced_header_content(code_file_path, current_version, mappings, comment_format)
        elif template_type == "minimal":
            return self._generate_minimal_header_content(code_file_path, current_version, mappings, comment_format)
        else:
            return self._generate_standard_header_content(code_file_path, current_version, mappings, comment_format)

    def _generate_enhanced_header_content(
        self,
        code_file_path: str,
        current_version: str,
        mappings: List[Dict],
        comment_format: Dict
    ) -> str:
        """
        生成增强版本头部信息内容

        包含内容：
        - 文件基本信息
        - 多设计版本映射（设计1版本，设计2版本，设计3版本...）
        - 实施计划信息
        - 版本同步状态提示
        - 权限控制说明
        """

        header_lines = []
        start_comment = comment_format["start"]
        block_start = comment_format["block_start"]
        block_end = comment_format["block_end"]

        # 文件头部块注释开始
        if block_start:
            header_lines.append(block_start)

        # 文件基本信息
        header_lines.extend([
            f"{start_comment}文件: {Path(code_file_path).name}",
            f"{start_comment}版本: {current_version}",
            f"{start_comment}更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"{start_comment}",
        ])

        # 设计文档版本映射信息（核心功能）
        if mappings:
            header_lines.append(f"{start_comment}设计文档版本映射:")
            for i, mapping in enumerate(mappings, 1):
                design_path = Path(mapping["design_document_path"]).name
                design_version = mapping["design_document_version"]
                mapping_type = mapping["mapping_type"]
                strength = mapping["mapping_strength"]

                header_lines.extend([
                    f"{start_comment}  设计{i}版本: {design_version} ({design_path})",
                    f"{start_comment}  映射类型: {mapping_type} (强度: {strength:.1f})",
                ])

                # 实施计划信息
                if mapping.get("implementation_plan_path"):
                    impl_path = Path(mapping["implementation_plan_path"]).name
                    impl_version = mapping["implementation_plan_version"]
                    header_lines.append(f"{start_comment}  实施计划: {impl_version} ({impl_path})")

                header_lines.append(f"{start_comment}")

        # 版本同步状态提示（只读，不自动修改）
        conflicts = self.panoramic_db.check_version_sync_conflicts(code_file_path)
        if conflicts:
            header_lines.append(f"{start_comment}版本同步提示:")
            for conflict in conflicts:
                header_lines.append(f"{start_comment}  ℹ️ {conflict['recommendation']}")
            header_lines.append(f"{start_comment}")

        # 权限控制说明
        header_lines.extend([
            f"{start_comment}版本管理说明:",
            f"{start_comment}  - 此版本信息由V4架构系统自动生成",
            f"{start_comment}  - 版本号修改权限：仅限实施计划文档和IDE AI",
            f"{start_comment}  - Python代码只能读取版本号，不能修改",
        ])

        # 文件头部块注释结束
        if block_end:
            header_lines.append(block_end)

        return "\n".join(header_lines) + "\n\n"

    def _generate_standard_header_content(
        self,
        code_file_path: str,
        current_version: str,
        mappings: List[Dict],
        comment_format: Dict
    ) -> str:
        """
        生成标准版本头部信息内容

        包含内容：
        - 基本版本信息
        - 设计文档版本（设计1版本，设计2版本...）
        - 权限控制说明
        """

        header_lines = []
        start_comment = comment_format["start"]

        # 基本版本信息
        header_lines.extend([
            f"{start_comment}文件版本: {current_version}",
            f"{start_comment}更新时间: {datetime.now().strftime('%Y-%m-%d')}",
        ])

        # 设计文档版本（核心功能）
        if mappings:
            for i, mapping in enumerate(mappings, 1):
                design_version = mapping["design_document_version"]
                header_lines.append(f"{start_comment}设计{i}版本: {design_version}")

        # 权限控制说明
        header_lines.append(f"{start_comment}版本修改权限：仅限实施计划文档和IDE AI")
        header_lines.append("")
        return "\n".join(header_lines)

    def _generate_minimal_header_content(
        self,
        code_file_path: str,
        current_version: str,
        mappings: List[Dict],
        comment_format: Dict
    ) -> str:
        """
        生成最小版本头部信息内容

        包含内容：
        - 代码文件版本
        - 设计版本映射（一行格式）
        """

        start_comment = comment_format["start"]
        design_versions = [m["design_document_version"] for m in mappings]

        if design_versions:
            design_info = ", ".join([f"设计{i+1}: {v}" for i, v in enumerate(design_versions)])
            return f"{start_comment}版本: {current_version} | {design_info}\n\n"
        else:
            return f"{start_comment}版本: {current_version}\n\n"

    def check_version_sync_status(self, code_file_path: str) -> Dict[str, Any]:
        """
        检查版本同步状态（只读检查，不自动修改）

        权限控制：只检查状态，不修改版本号

        Args:
            code_file_path: 代码文件路径

        Returns:
            Dict: 版本同步状态信息
        """
        try:
            current_version = self.read_current_version_from_file(code_file_path)
            mappings = self.panoramic_db.get_code_design_mappings(code_file_path)
            conflicts = self.panoramic_db.check_version_sync_conflicts(code_file_path)

            return {
                "code_file_path": code_file_path,
                "current_version": current_version,
                "design_mappings_count": len(mappings),
                "design_versions": [m["design_document_version"] for m in mappings],
                "sync_conflicts": conflicts,
                "sync_status": "synchronized" if not conflicts else "has_conflicts",
                "recommendations": [c["recommendation"] for c in conflicts],
                "version_modification_authority": "仅限实施计划文档和IDE AI"
            }

        except Exception as e:
            return {
                "code_file_path": code_file_path,
                "error": str(e),
                "sync_status": "check_failed"
            }

    def generate_version_sync_report(self, code_files: List[str]) -> Dict[str, Any]:
        """
        生成版本同步报告（批量检查）

        权限控制：只生成报告，不修改任何版本号

        Args:
            code_files: 代码文件路径列表

        Returns:
            Dict: 版本同步报告
        """
        report = {
            "report_time": datetime.now().isoformat(),
            "total_files": len(code_files),
            "synchronized_files": 0,
            "conflict_files": 0,
            "error_files": 0,
            "file_details": [],
            "summary": {
                "version_modification_authority": "仅限实施计划文档和IDE AI",
                "python_code_permission": "只读版本号权限"
            }
        }

        for code_file in code_files:
            status = self.check_version_sync_status(code_file)
            report["file_details"].append(status)

            if status.get("sync_status") == "synchronized":
                report["synchronized_files"] += 1
            elif status.get("sync_status") == "has_conflicts":
                report["conflict_files"] += 1
            else:
                report["error_files"] += 1

        return report
```

## 🔧 代码版本头部信息生成器设计

### CodeVersionHeaderGenerator类（代码头部版本信息管理）
```python
# src/v4/utils/code_version_header_generator.py
# 代码版本头部信息生成器 - 支持多设计版本映射的代码头部信息管理

from typing import Dict, List, Optional
from pathlib import Path
from datetime import datetime
import json

class CodeVersionHeaderGenerator:
    """
    代码版本头部信息生成器

    核心功能：
    1. 自动生成代码文件头部的多设计版本信息
    2. 支持多种编程语言的注释格式
    3. 提供标准化的版本信息模板
    4. 实现设计文档版本到代码头部的多对多映射
    """

    def __init__(self, panoramic_db: PanoramicModelDatabase):
        self.panoramic_db = panoramic_db

        # 支持的编程语言注释格式
        self.comment_formats = {
            ".py": {"start": "# ", "end": "", "block_start": '"""', "block_end": '"""'},
            ".java": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".js": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".ts": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".cpp": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".c": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".go": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".rs": {"start": "// ", "end": "", "block_start": "/**", "block_end": " */"},
            ".md": {"start": "", "end": "", "block_start": "<!--", "block_end": "-->"}
        }

    def generate_version_header(
        self,
        code_file_path: str,
        code_file_version: str,
        template_type: str = "enhanced"
    ) -> str:
        """
        生成代码文件版本头部信息

        支持的模板类型：
        - enhanced: 完整的版本信息，包含所有设计文档映射
        - standard: 标准版本信息，包含基本的设计版本映射
        - minimal: 最小版本信息，仅包含核心版本信息

        Args:
            code_file_path: 代码文件路径
            code_file_version: 代码文件版本
            template_type: 模板类型

        Returns:
            str: 生成的版本头部信息字符串
        """

        # 获取文件扩展名和注释格式
        file_extension = Path(code_file_path).suffix
        comment_format = self.comment_formats.get(file_extension, self.comment_formats[".py"])

        # 获取设计文档映射关系
        mappings = self.panoramic_db.get_code_design_mappings(code_file_path)

        # 根据模板类型生成头部信息
        if template_type == "enhanced":
            return self._generate_enhanced_header(code_file_path, code_file_version, mappings, comment_format)
        elif template_type == "minimal":
            return self._generate_minimal_header(code_file_path, code_file_version, mappings, comment_format)
        else:
            return self._generate_standard_header(code_file_path, code_file_version, mappings, comment_format)

    def _generate_enhanced_header(
        self,
        code_file_path: str,
        code_file_version: str,
        mappings: List[Dict],
        comment_format: Dict
    ) -> str:
        """
        生成增强版本头部信息

        包含内容：
        - 文件基本信息
        - 完整的设计文档版本映射（设计1版本，设计2版本，设计3版本...）
        - 实施计划信息
        - 版本同步状态警告
        - 自动生成标识
        """

        header_lines = []
        start_comment = comment_format["start"]
        block_start = comment_format["block_start"]
        block_end = comment_format["block_end"]

        # 文件头部块注释开始
        if block_start:
            header_lines.append(block_start)

        # 文件基本信息
        header_lines.extend([
            f"{start_comment}文件: {Path(code_file_path).name}",
            f"{start_comment}版本: {code_file_version}",
            f"{start_comment}创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"{start_comment}",
        ])

        # 设计文档版本映射信息（核心功能）
        if mappings:
            header_lines.append(f"{start_comment}设计文档版本映射:")
            for i, mapping in enumerate(mappings, 1):
                design_path = Path(mapping["design_document_path"]).name
                design_version = mapping["design_document_version"]
                mapping_type = mapping["mapping_type"]
                strength = mapping["mapping_strength"]

                header_lines.extend([
                    f"{start_comment}  设计{i}版本: {design_version} ({design_path})",
                    f"{start_comment}  映射类型: {mapping_type} (强度: {strength:.1f})",
                ])

                # 实施计划信息
                if mapping.get("implementation_plan_path"):
                    impl_path = Path(mapping["implementation_plan_path"]).name
                    impl_version = mapping["implementation_plan_version"]
                    header_lines.append(f"{start_comment}  实施计划: {impl_version} ({impl_path})")

                header_lines.append(f"{start_comment}")

        # 版本同步状态警告
        conflicts = self.panoramic_db.check_version_sync_conflicts(code_file_path)
        if conflicts:
            header_lines.append(f"{start_comment}版本同步警告:")
            for conflict in conflicts:
                header_lines.append(f"{start_comment}  ⚠️ {conflict['recommendation']}")
            header_lines.append(f"{start_comment}")

        # 自动生成标识
        header_lines.extend([
            f"{start_comment}此版本信息由V4架构系统自动生成",
            f"{start_comment}请勿手动修改，如需更新请使用版本管理工具",
        ])

        # 文件头部块注释结束
        if block_end:
            header_lines.append(block_end)

        return "\n".join(header_lines) + "\n\n"

    def _generate_standard_header(
        self,
        code_file_path: str,
        code_file_version: str,
        mappings: List[Dict],
        comment_format: Dict
    ) -> str:
        """
        生成标准版本头部信息

        包含内容：
        - 基本版本信息
        - 设计文档版本（设计1版本，设计2版本...）
        """

        header_lines = []
        start_comment = comment_format["start"]

        # 基本版本信息
        header_lines.extend([
            f"{start_comment}文件版本: {code_file_version}",
            f"{start_comment}更新时间: {datetime.now().strftime('%Y-%m-%d')}",
        ])

        # 设计文档版本（核心功能）
        if mappings:
            for i, mapping in enumerate(mappings, 1):
                design_version = mapping["design_document_version"]
                header_lines.append(f"{start_comment}设计{i}版本: {design_version}")

        header_lines.append("")
        return "\n".join(header_lines)

    def _generate_minimal_header(
        self,
        code_file_path: str,
        code_file_version: str,
        mappings: List[Dict],
        comment_format: Dict
    ) -> str:
        """
        生成最小版本头部信息

        包含内容：
        - 代码文件版本
        - 设计版本映射（一行格式）
        """

        start_comment = comment_format["start"]
        design_versions = [m["design_document_version"] for m in mappings]

        if design_versions:
            design_info = ", ".join([f"设计{i+1}: {v}" for i, v in enumerate(design_versions)])
            return f"{start_comment}版本: {code_file_version} | {design_info}\n\n"
        else:
            return f"{start_comment}版本: {code_file_version}\n\n"

    def update_code_file_header(
        self,
        code_file_path: str,
        template_type: str = "enhanced",
        backup: bool = True
    ) -> bool:
        """
        更新代码文件的版本头部信息

        核心功能：
        - 自动检测现有版本头部信息
        - 移除旧的版本头部信息
        - 生成新的版本头部信息
        - 更新数据库记录

        Args:
            code_file_path: 代码文件路径
            template_type: 模板类型
            backup: 是否备份原文件

        Returns:
            bool: 更新是否成功
        """
        try:
            file_path = Path(code_file_path)

            if not file_path.exists():
                print(f"❌ 文件不存在: {code_file_path}")
                return False

            # 备份原文件
            if backup:
                backup_path = file_path.with_suffix(f"{file_path.suffix}.backup")
                backup_path.write_text(file_path.read_text(encoding='utf-8'), encoding='utf-8')
                print(f"📋 已备份原文件: {backup_path}")

            # 读取原文件内容
            original_content = file_path.read_text(encoding='utf-8')

            # 检查是否已有版本头部信息
            if self._has_version_header(original_content):
                # 移除旧的版本头部信息
                content_without_header = self._remove_existing_header(original_content)
            else:
                content_without_header = original_content

            # 生成新的版本头部信息
            current_version = self._extract_current_version(code_file_path)
            new_header = self.generate_version_header(code_file_path, current_version, template_type)

            # 合并新头部和原内容
            new_content = new_header + content_without_header

            # 写入文件
            file_path.write_text(new_content, encoding='utf-8')

            # 更新数据库记录
            mappings = self.panoramic_db.get_code_design_mappings(code_file_path)
            design_version_mappings = {
                f"design_{i+1}": mapping["design_document_version"]
                for i, mapping in enumerate(mappings)
            }

            self.panoramic_db.update_code_file_version_header(
                code_file_path, current_version, design_version_mappings
            )

            print(f"✅ 代码文件版本头部信息已更新: {code_file_path}")
            return True

        except Exception as e:
            print(f"❌ 更新代码文件版本头部信息失败: {e}")
            return False

    def _has_version_header(self, content: str) -> bool:
        """检查是否已有版本头部信息"""
        return "V4架构系统自动生成" in content or "设计1版本:" in content

    def _remove_existing_header(self, content: str) -> str:
        """移除现有的版本头部信息"""
        lines = content.split('\n')
        start_removing = False
        end_removing = False
        result_lines = []

        for line in lines:
            if "V4架构系统自动生成" in line or "文件版本:" in line:
                start_removing = True
                continue

            if start_removing and (line.strip() == "" or not line.strip().startswith(("#", "//", "/*", "*"))):
                end_removing = True

            if not start_removing or end_removing:
                result_lines.append(line)
                if end_removing:
                    start_removing = False
                    end_removing = False

        return '\n'.join(result_lines)

    def _extract_current_version(self, code_file_path: str) -> str:
        """提取代码文件当前版本"""
        # 这里可以从文件内容、Git标签或数据库中提取版本
        # 简化实现，返回默认版本
        return "1.0.0"
```

    def _initialize_config(self):
        """初始化系统配置"""
        default_configs = [
            ('db_version', '1.0.0', 'system', 'Database schema version'),
            ('fast_scan_threshold', '0.95', 'scanning', 'Fast scan confidence threshold'),
            ('incremental_scan_threshold', '0.85', 'scanning', 'Incremental scan confidence threshold'),
            ('triple_verification_enabled', 'true', 'verification', 'Enable triple verification mechanism'),
            ('max_scan_iterations', '5', 'scanning', 'Maximum scan iterations'),
        ]

        cursor = self.connection.cursor()
        for key, value, config_type, description in default_configs:
            cursor.execute("""
                INSERT OR IGNORE INTO panoramic_config
                (config_key, config_value, config_type, description)
                VALUES (?, ?, ?, ?)
            """, (key, value, config_type, description))

        self.connection.commit()

    # ==================== 核心数据操作方法 ====================

    def store_document_abstraction(self, doc_path: str, version: str,
                                 content_hash: str, abstraction_data: Dict) -> bool:
        """存储文档抽象数据到全景模型"""
        try:
            # 计算语义哈希
            semantic_hash = self._calculate_semantic_hash(abstraction_data)

            # 准备数据
            abstraction_json = json.dumps(abstraction_data, ensure_ascii=False)

            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO panoramic_models
                (document_path, version_number, content_hash, semantic_hash,
                 abstraction_data, triple_verification_status, updated_at)
                VALUES (?, ?, ?, ?, ?, 'PENDING', CURRENT_TIMESTAMP)
            """, (doc_path, version, content_hash, semantic_hash, abstraction_json))

            self.connection.commit()
            return True

        except Exception as e:
            print(f"❌ 存储文档抽象失败: {str(e)}")
            return False

    def check_document_changes(self, doc_path: str, current_hash: str) -> Dict:
        """检测文档版本+哈希变更（核心扫描算法）"""
        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT version_number, content_hash, semantic_hash, confidence_score
            FROM panoramic_models
            WHERE document_path = ?
            ORDER BY updated_at DESC LIMIT 1
        """, (doc_path,))

        result = cursor.fetchone()
        if not result:
            return {
                "status": "new_document",
                "action": "full_scan",
                "hash_changed": True,
                "version_changed": True,
                "recommendation": "执行全量扫描建立全景模型"
            }

        stored_version = result['version_number']
        stored_hash = result['content_hash']
        stored_confidence = result['confidence_score']

        hash_changed = stored_hash != current_hash

        # 核心决策逻辑：基于版本+哈希变更的智能扫描决策
        if hash_changed:
            if stored_confidence >= 0.95:  # 高置信度文档
                return {
                    "status": "content_modified",
                    "action": "incremental_scan",
                    "hash_changed": True,
                    "version_changed": False,
                    "stored_version": stored_version,
                    "stored_hash": stored_hash,
                    "current_hash": current_hash,
                    "warning": "⚠️ 文档内容已修改但版本号未更新，建议更新版本号",
                    "recommendation": "执行增量扫描更新全景模型"
                }
            else:  # 低置信度文档
                return {
                    "status": "low_confidence_modified",
                    "action": "full_rebuild",
                    "hash_changed": True,
                    "version_changed": False,
                    "stored_confidence": stored_confidence,
                    "recommendation": "置信度较低，执行全量重建"
                }
        else:
            return {
                "status": "no_changes",
                "action": "fast_scan",
                "hash_changed": False,
                "version_changed": False,
                "stored_confidence": stored_confidence,
                "recommendation": "基于已有全景模型快速扫描"
            }

    def get_panoramic_model(self, doc_path: str) -> Optional[Dict]:
        """获取文档的全景模型数据"""
        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT * FROM panoramic_models
            WHERE document_path = ?
            ORDER BY updated_at DESC LIMIT 1
        """, (doc_path,))

        result = cursor.fetchone()
        if result:
            return {
                'document_path': result['document_path'],
                'version_number': result['version_number'],
                'content_hash': result['content_hash'],
                'semantic_hash': result['semantic_hash'],
                'abstraction_data': json.loads(result['abstraction_data']),
                'relationships_data': json.loads(result['relationships_data']) if result['relationships_data'] else None,
                'quality_metrics': json.loads(result['quality_metrics']) if result['quality_metrics'] else None,
                'triple_verification_status': result['triple_verification_status'],
                'confidence_score': result['confidence_score'],
                'created_at': result['created_at'],
                'updated_at': result['updated_at']
            }
        return None

    def update_version_info(self, doc_path: str, new_version: str, new_hash: str):
        """更新版本信息（版本号变更时）"""
        cursor = self.connection.cursor()

        # 记录历史版本
        cursor.execute("""
            INSERT INTO document_history
            (document_path, version_number, content_hash, semantic_hash,
             change_type, change_description, previous_version)
            SELECT document_path, version_number, content_hash, semantic_hash,
                   'version_updated', '版本号更新', version_number
            FROM panoramic_models
            WHERE document_path = ?
        """, (doc_path,))

        # 更新当前版本
        cursor.execute("""
            UPDATE panoramic_models
            SET version_number = ?, content_hash = ?, updated_at = CURRENT_TIMESTAMP
            WHERE document_path = ?
        """, (new_version, new_hash, doc_path))

        self.connection.commit()

    def record_version_warning(self, doc_path: str, warning_type: str,
                             old_version: str, current_version: str,
                             old_hash: str, current_hash: str,
                             warning_message: str) -> int:
        """记录版本警告"""
        cursor = self.connection.cursor()
        cursor.execute("""
            INSERT INTO version_warnings
            (document_path, warning_type, old_version, current_version,
             old_hash, current_hash, warning_message)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (doc_path, warning_type, old_version, current_version,
              old_hash, current_hash, warning_message))

        self.connection.commit()
        return cursor.lastrowid

    def _calculate_semantic_hash(self, abstraction_data: Dict) -> str:
        """计算语义级别的哈希值"""
        # 提取核心语义信息
        semantic_content = {
            'architecture_patterns': abstraction_data.get('architecture_patterns', []),
            'component_relationships': abstraction_data.get('component_relationships', []),
            'core_interfaces': abstraction_data.get('core_interfaces', []),
            'design_decisions': abstraction_data.get('design_decisions', [])
        }

        # 生成语义哈希
        semantic_str = json.dumps(semantic_content, sort_keys=True, ensure_ascii=False)
        return hashlib.sha256(semantic_str.encode('utf-8')).hexdigest()[:16]

    # ==================== 实施计划文档管理方法 ====================

    def store_implementation_plan_abstraction(self, impl_plan_path: str, version: str,
                                            content_hash: str, plan_abstraction_data: Dict) -> bool:
        """存储实施计划文档抽象数据到全景模型"""
        try:
            # 计算语义哈希
            semantic_hash = self._calculate_implementation_plan_semantic_hash(plan_abstraction_data)

            # 准备数据
            abstraction_json = json.dumps(plan_abstraction_data, ensure_ascii=False)
            step_dependencies_json = json.dumps(plan_abstraction_data.get('step_dependencies', []), ensure_ascii=False)
            execution_metadata_json = json.dumps(plan_abstraction_data.get('execution_metadata', {}), ensure_ascii=False)
            design_references_json = json.dumps(plan_abstraction_data.get('design_document_references', []), ensure_ascii=False)

            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO implementation_plan_models
                (implementation_plan_path, version_number, content_hash, semantic_hash,
                 plan_abstraction_data, step_dependencies_data, execution_metadata,
                 design_document_references, implementation_complexity_score,
                 estimated_execution_time, triple_verification_status, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'PENDING', CURRENT_TIMESTAMP)
            """, (impl_plan_path, version, content_hash, semantic_hash, abstraction_json,
                  step_dependencies_json, execution_metadata_json, design_references_json,
                  plan_abstraction_data.get('complexity_score', 0.0),
                  plan_abstraction_data.get('estimated_execution_time', 0)))

            self.connection.commit()
            return True

        except Exception as e:
            print(f"❌ 存储实施计划抽象失败: {str(e)}")
            return False

    def create_design_to_implementation_mapping(self, design_doc_path: str, design_version: str,
                                              design_hash: str, impl_plan_path: str,
                                              impl_version: str, impl_hash: str,
                                              mapping_type: str = "one_to_one",
                                              generation_method: str = "ai_generated") -> bool:
        """创建设计文档到实施计划的映射关系"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO design_to_implementation_mapping
                (design_document_path, design_document_version, design_document_hash,
                 implementation_plan_path, implementation_plan_version, implementation_plan_hash,
                 mapping_type, generation_method, sync_status, last_sync_timestamp,
                 triple_verification_status, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'synchronized', CURRENT_TIMESTAMP, 'PENDING', CURRENT_TIMESTAMP)
            """, (design_doc_path, design_version, design_hash, impl_plan_path,
                  impl_version, impl_hash, mapping_type, generation_method))

            self.connection.commit()
            return True

        except Exception as e:
            print(f"❌ 创建设计文档映射失败: {str(e)}")
            return False

    def check_implementation_plan_sync_status(self, design_doc_path: str, impl_plan_path: str) -> Dict:
        """检查设计文档与实施计划的同步状态"""
        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT design_document_hash, design_document_version,
                   implementation_plan_hash, implementation_plan_version,
                   sync_status, last_sync_timestamp
            FROM design_to_implementation_mapping
            WHERE design_document_path = ? AND implementation_plan_path = ?
            ORDER BY updated_at DESC LIMIT 1
        """, (design_doc_path, impl_plan_path))

        result = cursor.fetchone()
        if not result:
            return {
                "status": "no_mapping",
                "action": "create_mapping",
                "recommendation": "需要创建设计文档到实施计划的映射关系"
            }

        # 检查当前文档哈希值
        current_design_hash = self.hash_calculator.calculate_content_hash(design_doc_path)
        current_impl_hash = self.hash_calculator.calculate_content_hash(impl_plan_path)

        design_changed = result['design_document_hash'] != current_design_hash
        impl_changed = result['implementation_plan_hash'] != current_impl_hash

        if design_changed and impl_changed:
            return {
                "status": "both_changed",
                "action": "conflict_resolution",
                "design_changed": True,
                "implementation_changed": True,
                "recommendation": "⚠️ 设计文档和实施计划都已修改，需要冲突解决"
            }
        elif design_changed:
            return {
                "status": "design_newer",
                "action": "update_implementation",
                "design_changed": True,
                "implementation_changed": False,
                "recommendation": "🔄 设计文档已更新，建议重新生成实施计划"
            }
        elif impl_changed:
            return {
                "status": "implementation_newer",
                "action": "verify_consistency",
                "design_changed": False,
                "implementation_changed": True,
                "recommendation": "📝 实施计划已修改，建议验证与设计文档的一致性"
            }
        else:
            return {
                "status": "synchronized",
                "action": "no_action",
                "design_changed": False,
                "implementation_changed": False,
                "recommendation": "✅ 设计文档与实施计划保持同步"
            }

    def get_implementation_plans_by_design_document(self, design_doc_path: str) -> List[Dict]:
        """根据设计文档获取相关的实施计划"""
        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT m.implementation_plan_path, m.version_number, m.content_hash,
                   m.plan_abstraction_data, m.implementation_complexity_score,
                   m.estimated_execution_time, m.confidence_score,
                   dm.mapping_type, dm.sync_status, dm.last_sync_timestamp
            FROM design_to_implementation_mapping dm
            JOIN implementation_plan_models m ON dm.implementation_plan_path = m.implementation_plan_path
            WHERE dm.design_document_path = ?
            ORDER BY dm.dependency_level, m.updated_at DESC
        """, (design_doc_path,))

        results = cursor.fetchall()
        implementation_plans = []

        for result in results:
            implementation_plans.append({
                'implementation_plan_path': result['implementation_plan_path'],
                'version_number': result['version_number'],
                'content_hash': result['content_hash'],
                'plan_abstraction_data': json.loads(result['plan_abstraction_data']),
                'implementation_complexity_score': result['implementation_complexity_score'],
                'estimated_execution_time': result['estimated_execution_time'],
                'confidence_score': result['confidence_score'],
                'mapping_type': result['mapping_type'],
                'sync_status': result['sync_status'],
                'last_sync_timestamp': result['last_sync_timestamp']
            })

        return implementation_plans

    def _calculate_implementation_plan_semantic_hash(self, plan_abstraction_data: Dict) -> str:
        """计算实施计划的语义级别哈希值"""
        # 提取核心语义信息
        semantic_content = {
            'implementation_steps': plan_abstraction_data.get('implementation_steps', []),
            'step_dependencies': plan_abstraction_data.get('step_dependencies', []),
            'core_algorithms': plan_abstraction_data.get('core_algorithms', []),
            'technical_decisions': plan_abstraction_data.get('technical_decisions', [])
        }

        # 生成语义哈希
        semantic_str = json.dumps(semantic_content, sort_keys=True, ensure_ascii=False)
        return hashlib.sha256(semantic_str.encode('utf-8')).hexdigest()[:16]

    # ==================== 版本管理核心方法：全景图可靠性确认 ====================

    def request_panoramic_model_user_confirmation(self, doc_path: str, confirmation_type: str = "initial_confirmation") -> int:
        """请求用户确认全景模型可靠性（版本管理核心任务1）"""
        try:
            # 获取最新的全景模型
            panoramic_model = self.get_panoramic_model(doc_path)
            if not panoramic_model:
                print(f"❌ 未找到文档的全景模型: {doc_path}")
                return -1

            # 创建用户确认请求
            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT INTO panoramic_model_user_confirmations
                (document_path, panoramic_model_id, confirmation_type, confirmation_status,
                 expires_at, created_at, updated_at)
                VALUES (?, ?, ?, 'pending',
                        datetime('now', '+7 days'), CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """, (doc_path, panoramic_model['id'], confirmation_type))

            confirmation_id = cursor.lastrowid

            # 更新全景模型状态为等待用户确认
            cursor.execute("""
                UPDATE panoramic_models
                SET panoramic_reliability_status = 'PENDING_USER_CONFIRMATION',
                    user_confirmation_status = 'NOT_CONFIRMED',
                    updated_at = CURRENT_TIMESTAMP
                WHERE document_path = ?
            """, (doc_path,))

            self.connection.commit()

            # 显示用户确认提示
            print(f"🔔 全景模型可靠性确认请求")
            print(f"   📄 文档: {doc_path}")
            print(f"   🎯 置信度: {panoramic_model['confidence_score']:.1%}")
            print(f"   ⏰ 确认期限: 7天内")
            print(f"   💡 请确认此全景模型是否可靠，确认后才能确定全景的可靠性")

            return confirmation_id

        except Exception as e:
            print(f"❌ 请求用户确认失败: {str(e)}")
            return -1

    def process_user_confirmation(self, confirmation_id: int, confirmation_status: str,
                                user_feedback: str = "", reliability_assessment: str = "",
                                confirmed_by: str = "user") -> bool:
        """处理用户确认结果（版本管理核心任务1）"""
        try:
            cursor = self.connection.cursor()

            # 更新确认记录
            cursor.execute("""
                UPDATE panoramic_model_user_confirmations
                SET confirmation_status = ?, user_feedback = ?, reliability_assessment = ?,
                    confirmed_by = ?, confirmed_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (confirmation_status, user_feedback, reliability_assessment, confirmed_by, confirmation_id))

            # 获取确认记录信息
            cursor.execute("""
                SELECT document_path, panoramic_model_id FROM panoramic_model_user_confirmations
                WHERE id = ?
            """, (confirmation_id,))

            result = cursor.fetchone()
            if not result:
                print(f"❌ 未找到确认记录: {confirmation_id}")
                return False

            doc_path = result['document_path']

            # 根据确认结果更新全景模型状态
            if confirmation_status == "confirmed":
                # 用户确认可靠
                cursor.execute("""
                    UPDATE panoramic_models
                    SET panoramic_reliability_status = 'USER_CONFIRMED_RELIABLE',
                        user_confirmation_status = 'CONFIRMED',
                        user_confirmed_by = ?, user_confirmed_at = CURRENT_TIMESTAMP,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE document_path = ?
                """, (confirmed_by, doc_path))

                print(f"✅ 全景模型可靠性已确认: {doc_path}")
                print(f"   👤 确认用户: {confirmed_by}")
                print(f"   📝 用户反馈: {user_feedback}")

            elif confirmation_status == "rejected":
                # 用户拒绝，需要重新构建
                cursor.execute("""
                    UPDATE panoramic_models
                    SET panoramic_reliability_status = 'USER_REJECTED_NEEDS_REBUILD',
                        user_confirmation_status = 'REJECTED',
                        updated_at = CURRENT_TIMESTAMP
                    WHERE document_path = ?
                """, (doc_path,))

                print(f"❌ 全景模型被用户拒绝: {doc_path}")
                print(f"   📝 拒绝原因: {user_feedback}")
                print(f"   🔄 需要重新构建全景模型")

            elif confirmation_status == "needs_revision":
                # 需要修订
                cursor.execute("""
                    UPDATE panoramic_models
                    SET panoramic_reliability_status = 'NEEDS_REVISION',
                        user_confirmation_status = 'NEEDS_REVISION',
                        updated_at = CURRENT_TIMESTAMP
                    WHERE document_path = ?
                """, (doc_path,))

                print(f"🔧 全景模型需要修订: {doc_path}")
                print(f"   📝 修订建议: {user_feedback}")

            self.connection.commit()
            return True

        except Exception as e:
            print(f"❌ 处理用户确认失败: {str(e)}")
            return False

    # ==================== 版本管理核心方法：架构负债检测和提醒 ====================

    def check_version_lag_and_architecture_debt(self, doc_path: str) -> Dict:
        """检测版本落后和架构负债（版本管理核心任务2）"""
        try:
            # 获取当前文档的版本信息
            current_version = self.version_parser.extract_version(doc_path)
            current_hash = self.hash_calculator.calculate_content_hash(doc_path)

            # 检查是否有更新的版本可用（这里需要实现版本检测逻辑）
            latest_available_version = self._detect_latest_available_version(doc_path)

            # 分析版本落后程度
            version_lag_analysis = self._analyze_version_lag(current_version, latest_available_version)

            # 检测架构负债
            architecture_debt_analysis = self._detect_architecture_debt(doc_path, version_lag_analysis)

            # 生成提醒建议
            alert_recommendation = self._generate_architecture_debt_alert_recommendation(
                version_lag_analysis, architecture_debt_analysis)

            # 记录检测结果
            self._record_version_lag_and_debt_alert(doc_path, current_version, latest_available_version,
                                                   version_lag_analysis, architecture_debt_analysis, alert_recommendation)

            return {
                "document_path": doc_path,
                "current_version": current_version,
                "latest_available_version": latest_available_version,
                "version_lag_analysis": version_lag_analysis,
                "architecture_debt_analysis": architecture_debt_analysis,
                "alert_recommendation": alert_recommendation,
                "requires_user_attention": version_lag_analysis.get("severity") in ["moderate", "severe", "critical"]
            }

        except Exception as e:
            print(f"❌ 版本落后和架构负债检测失败: {str(e)}")
            return {"error": str(e)}

    def send_architecture_debt_reminder(self, doc_path: str) -> bool:
        """发送架构负债提醒（版本管理核心任务2）"""
        try:
            # 获取最新的架构负债检测结果
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT * FROM version_lag_and_architecture_debt_alerts
                WHERE document_path = ? AND auto_check_enabled = TRUE
                ORDER BY updated_at DESC LIMIT 1
            """, (doc_path,))

            result = cursor.fetchone()
            if not result:
                return False

            # 检查是否需要发送提醒
            if self._should_send_reminder(result):
                # 生成提醒消息
                reminder_message = self._generate_architecture_debt_reminder_message(result)

                # 显示提醒
                print(f"🚨 架构负债提醒")
                print(f"   📄 文档: {doc_path}")
                print(f"   📊 版本落后程度: {result['version_lag_severity']}")
                print(f"   💡 {reminder_message}")
                print(f"   ❓ 是否有遗留的架构负债需要处理？")

                # 更新最后提醒时间
                cursor.execute("""
                    UPDATE version_lag_and_architecture_debt_alerts
                    SET last_alert_sent = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (result['id'],))

                self.connection.commit()
                return True

            return False

        except Exception as e:
            print(f"❌ 发送架构负债提醒失败: {str(e)}")
            return False
```

## 🔍 智能扫描引擎设计

### IntelligentScanningEngine类（核心扫描算法）
```python
# src/v4/scanning/intelligent_scanning_engine.py
# 智能扫描引擎 - 基于SQLite全景模型的快速扫描

import hashlib
from typing import Dict, List, Optional
from pathlib import Path

class IntelligentScanningEngine:
    """智能扫描引擎 - 基于SQLite全景模型的增量扫描优化"""

    def __init__(self, panoramic_db: PanoramicModelDatabase):
        self.panoramic_db = panoramic_db
        self.hash_calculator = DocumentHashCalculator()
        self.version_parser = DocumentVersionParser()
        self.triple_verification_orchestrator = TripleVerificationOrchestrator()
        self.version_generator = CompositeVersionGenerator()
        self.version_relationship_manager = VersionRelationshipManager()

    def execute_smart_scan(self, target_docs: List[str]) -> Dict:
        """执行智能扫描主流程（核心算法+版本管理增强）"""
        scan_result = {
            "scan_mode": "smart_scan",
            "total_documents": len(target_docs),
            "fast_scan_count": 0,
            "incremental_scan_count": 0,
            "full_rebuild_count": 0,
            "warnings_generated": 0,
            "user_confirmations_required": 0,
            "architecture_debt_alerts": 0,
            "confidence_improvement": 0.0,
            "execution_time_ms": 0,
            "documents_processed": [],
            "scan_decisions": [],
            "version_management_actions": []
        }

        start_time = time.time()

        for doc_path in target_docs:
            try:
                # 1. 计算当前文档哈希值
                current_hash = self.hash_calculator.calculate_content_hash(doc_path)
                current_version = self.version_parser.extract_version(doc_path)

                # 1.5. 处理无版本号文档（核心功能：提示用户处理，不自动修改）
                if current_version == "UNKNOWN":
                    doc_type = self._determine_document_type(doc_path)
                    suggested_version = self.version_generator.suggest_version_for_new_document(
                        doc_path, doc_type, VersionLevel.L3)

                    # 提示用户处理，不自动修改文档
                    self._prompt_user_for_version_handling(doc_path, suggested_version, doc_type)

                    # 使用临时版本号进行后续处理，但不写入文档
                    current_version = suggested_version

                    print(f"⚠️ 发现无版本号文档: {doc_path}")
                    print(f"💡 建议版本号: {suggested_version} (请用户手动添加)")

                # 2. 检查版本+哈希变更（核心决策逻辑）
                change_detection = self.panoramic_db.check_document_changes(doc_path, current_hash)

                # 3. 基于检测结果决定扫描模式
                scan_mode = self._determine_scan_mode(change_detection)

                # 4. 执行对应的扫描模式
                doc_result = self._execute_scan_mode(doc_path, scan_mode, change_detection)

                # 5. 版本管理核心任务检查
                version_management_result = self._execute_version_management_checks(doc_path, doc_result)
                doc_result["version_management"] = version_management_result

                # 6. 记录扫描决策和结果
                scan_result["documents_processed"].append(doc_result)
                scan_result["scan_decisions"].append({
                    "document": doc_path,
                    "scan_mode": scan_mode,
                    "decision_reason": change_detection.get("recommendation", ""),
                    "confidence_before": change_detection.get("stored_confidence", 0.0),
                    "confidence_after": doc_result.get("confidence_score", 0.0)
                })

                # 6. 更新统计信息
                if scan_mode == "fast_scan":
                    scan_result["fast_scan_count"] += 1
                elif scan_mode == "incremental_scan":
                    scan_result["incremental_scan_count"] += 1
                elif scan_mode == "full_rebuild":
                    scan_result["full_rebuild_count"] += 1

                # 7. 处理版本警告
                if change_detection.get("warning"):
                    self._handle_version_warning(doc_path, change_detection)
                    scan_result["warnings_generated"] += 1

            except Exception as e:
                print(f"❌ 扫描文档失败 {doc_path}: {str(e)}")
                scan_result["documents_processed"].append({
                    "document": doc_path,
                    "status": "failed",
                    "error": str(e)
                })

        # 8. 计算总体执行时间和置信度提升
        scan_result["execution_time_ms"] = int((time.time() - start_time) * 1000)
        scan_result["confidence_improvement"] = self._calculate_confidence_improvement(scan_result)

        # 9. 记录扫描任务
        self._record_scan_task(scan_result)

        return scan_result

    def _determine_scan_mode(self, change_detection: Dict) -> str:
        """决定扫描模式（核心决策算法）"""
        action = change_detection.get("action", "full_rebuild")

        # 基于SQLite全景模型的智能决策
        if action == "fast_scan":
            return "fast_scan"
        elif action == "incremental_scan":
            return "incremental_scan"
        else:
            return "full_rebuild"

    def _execute_scan_mode(self, doc_path: str, scan_mode: str, change_detection: Dict) -> Dict:
        """执行具体的扫描模式"""
        if scan_mode == "fast_scan":
            return self.execute_fast_scan_mode(doc_path, change_detection)
        elif scan_mode == "incremental_scan":
            return self.execute_incremental_scan_mode(doc_path, change_detection)
        else:
            return self.execute_full_rebuild_mode(doc_path, change_detection)

    def execute_fast_scan_mode(self, doc_path: str, change_detection: Dict) -> Dict:
        """基于已有全景数据的快速扫描"""
        print(f"⚡ 快速扫描模式: {doc_path}")

        # 1. 从SQLite加载已有全景模型
        panoramic_model = self.panoramic_db.get_panoramic_model(doc_path)
        if not panoramic_model:
            # 如果没有全景模型，降级到全量重建
            return self.execute_full_rebuild_mode(doc_path, change_detection)

        # 2. 基于已有抽象数据快速生成结果
        scan_result = {
            "document": doc_path,
            "scan_mode": "fast_scan",
            "status": "completed",
            "confidence_score": panoramic_model["confidence_score"],
            "abstraction_data": panoramic_model["abstraction_data"],
            "relationships_data": panoramic_model["relationships_data"],
            "quality_metrics": panoramic_model["quality_metrics"],
            "execution_time_ms": 50,  # 快速扫描时间很短
            "data_source": "sqlite_panoramic_model",
            "triple_verification_status": panoramic_model["triple_verification_status"]
        }

        print(f"✅ 快速扫描完成，置信度: {panoramic_model['confidence_score']:.1%}")
        return scan_result

    def execute_incremental_scan_mode(self, doc_path: str, change_detection: Dict) -> Dict:
        """增量扫描模式（基于变更的部分更新）"""
        print(f"🔄 增量扫描模式: {doc_path}")

        # 1. 加载已有全景模型作为基础
        existing_model = self.panoramic_db.get_panoramic_model(doc_path)

        # 2. 执行增量分析（仅分析变更部分）
        incremental_analysis = self._execute_incremental_analysis(doc_path, existing_model)

        # 3. 合并增量分析结果与已有模型
        updated_abstraction = self._merge_incremental_analysis(
            existing_model["abstraction_data"], incremental_analysis)

        # 4. 更新SQLite全景模型
        current_hash = self.hash_calculator.calculate_content_hash(doc_path)
        current_version = self.version_parser.extract_version(doc_path)

        self.panoramic_db.store_document_abstraction(
            doc_path, current_version, current_hash, updated_abstraction)

        scan_result = {
            "document": doc_path,
            "scan_mode": "incremental_scan",
            "status": "completed",
            "confidence_score": incremental_analysis.get("confidence_score", 0.85),
            "abstraction_data": updated_abstraction,
            "execution_time_ms": incremental_analysis.get("execution_time_ms", 200),
            "changes_detected": incremental_analysis.get("changes_detected", []),
            "data_source": "incremental_analysis"
        }

        print(f"✅ 增量扫描完成，置信度: {scan_result['confidence_score']:.1%}")
        return scan_result

    def execute_full_rebuild_mode(self, doc_path: str, change_detection: Dict) -> Dict:
        """全量重建模式（完整重新分析）"""
        print(f"🔨 全量重建模式: {doc_path}")

        # 1. 执行完整的文档分析
        full_analysis = self._execute_full_document_analysis(doc_path)

        # 2. 应用三重验证机制
        verification_result = self.triple_verification_orchestrator.execute_verification(
            doc_path, full_analysis)

        # 3. 存储新的全景模型到SQLite
        current_hash = self.hash_calculator.calculate_content_hash(doc_path)
        current_version = self.version_parser.extract_version(doc_path)

        self.panoramic_db.store_document_abstraction(
            doc_path, current_version, current_hash, full_analysis["abstraction_data"])

        scan_result = {
            "document": doc_path,
            "scan_mode": "full_rebuild",
            "status": "completed",
            "confidence_score": verification_result.get("overall_confidence", 0.75),
            "abstraction_data": full_analysis["abstraction_data"],
            "execution_time_ms": full_analysis.get("execution_time_ms", 500),
            "triple_verification_result": verification_result,
            "data_source": "full_analysis"
        }

        print(f"✅ 全量重建完成，置信度: {scan_result['confidence_score']:.1%}")
        return scan_result

    def _execute_version_management_checks(self, doc_path: str, scan_result: Dict) -> Dict:
        """执行版本管理核心任务检查"""
        version_management_result = {
            "user_confirmation_required": False,
            "architecture_debt_alert_sent": False,
            "reliability_status": "unknown",
            "actions_taken": []
        }

        try:
            # 版本管理核心任务1：全景图可靠性确认
            if scan_result.get("scan_mode") in ["incremental_scan", "full_rebuild"]:
                # 新的或更新的全景模型需要用户确认
                confirmation_id = self.panoramic_db.request_panoramic_model_user_confirmation(
                    doc_path, "update_confirmation" if scan_result.get("scan_mode") == "incremental_scan" else "initial_confirmation")

                if confirmation_id > 0:
                    version_management_result["user_confirmation_required"] = True
                    version_management_result["confirmation_id"] = confirmation_id
                    version_management_result["actions_taken"].append("requested_user_confirmation")
                    print(f"🔔 已请求用户确认全景模型可靠性: {doc_path}")

            # 版本管理核心任务2：架构负债检测和提醒
            debt_check_result = self.panoramic_db.check_version_lag_and_architecture_debt(doc_path)

            if debt_check_result.get("requires_user_attention"):
                # 发送架构负债提醒
                reminder_sent = self.panoramic_db.send_architecture_debt_reminder(doc_path)
                if reminder_sent:
                    version_management_result["architecture_debt_alert_sent"] = True
                    version_management_result["debt_analysis"] = debt_check_result
                    version_management_result["actions_taken"].append("sent_architecture_debt_reminder")
                    print(f"🚨 已发送架构负债提醒: {doc_path}")

            # 获取当前可靠性状态
            panoramic_model = self.panoramic_db.get_panoramic_model(doc_path)
            if panoramic_model:
                version_management_result["reliability_status"] = panoramic_model.get("panoramic_reliability_status", "unknown")
                version_management_result["user_confirmation_status"] = panoramic_model.get("user_confirmation_status", "unknown")

            return version_management_result

        except Exception as e:
            print(f"❌ 版本管理检查失败 {doc_path}: {str(e)}")
            version_management_result["error"] = str(e)
            return version_management_result

    def _handle_version_warnings(self, smart_scan_result: Dict):
        """处理版本警告（增强版本管理）"""
        print(f"\n⚠️ 版本一致性警告处理:")

        for decision in smart_scan_result["scan_decisions"]:
            if "warning" in decision.get("decision_reason", ""):
                doc_path = decision["document"]
                print(f"   📄 {doc_path}")
                print(f"      {decision['decision_reason']}")
                print(f"      💡 建议: 更新版本号或确认内容变更")

                # 记录版本警告到数据库
                self.panoramic_db.record_version_warning(
                    doc_path=doc_path,
                    warning_type="hash_changed_version_same",
                    old_version=decision.get("stored_version", ""),
                    current_version=decision.get("stored_version", ""),
                    old_hash=decision.get("stored_hash", ""),
                    current_hash=decision.get("current_hash", ""),
                    warning_message=decision['decision_reason']
                )

                # 触发架构负债检查
                debt_check = self.panoramic_db.check_version_lag_and_architecture_debt(doc_path)
                if debt_check.get("requires_user_attention"):
                    print(f"      🚨 检测到潜在架构负债，建议及时处理")

    def _determine_document_type(self, doc_path: str) -> DocumentType:
        """判断文档类型（用于版本号生成）"""
        doc_path_lower = doc_path.lower()

        if "design" in doc_path_lower or "设计" in doc_path_lower:
            return DocumentType.DESIGN
        elif "plan" in doc_path_lower or "实施" in doc_path_lower or "implementation" in doc_path_lower:
            return DocumentType.IMPLEMENTATION_PLAN
        elif doc_path_lower.endswith(('.py', '.js', '.ts', '.java', '.cpp', '.c', '.go', '.rs')):
            return DocumentType.CODE
        elif "test" in doc_path_lower or "测试" in doc_path_lower:
            return DocumentType.TEST
        else:
            # 默认为设计文档
            return DocumentType.DESIGN

    def _auto_inject_version_to_document(self, doc_path: str, version: str) -> bool:
        """自动向文档注入版本号"""
        try:
            with open(doc_path, 'r', encoding='utf-8') as file:
                content = file.read()

            # 检查是否已有版本信息
            if re.search(r'版本[：:]|Version[：:]|version[：:]', content):
                # 已有版本字段，更新版本号
                content = re.sub(
                    r'(版本[：:]\s*)[^\n]*',
                    f'\\1{version}',
                    content
                )
                content = re.sub(
                    r'(Version[：:]\s*)[^\n]*',
                    f'\\1{version}',
                    content,
                    flags=re.IGNORECASE
                )
            else:
                # 没有版本字段，在文档开头添加
                if content.startswith('#'):
                    # Markdown文档，在第一个标题后添加
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if line.startswith('#'):
                            lines.insert(i + 1, f"**版本**: {version}")
                            lines.insert(i + 2, "")
                            break
                    content = '\n'.join(lines)
                else:
                    # 其他文档，在开头添加
                    content = f"版本: {version}\n\n{content}"

            # 写回文件
            with open(doc_path, 'w', encoding='utf-8') as file:
                file.write(content)

            print(f"✅ 版本号已自动注入: {doc_path} -> {version}")
            return True

        except Exception as e:
            print(f"❌ 版本号注入失败 {doc_path}: {str(e)}")
            return False
```
```

    def _handle_version_warning(self, doc_path: str, change_detection: Dict):
        """处理版本警告"""
        warning_message = change_detection.get("warning", "")
        if warning_message:
            self.panoramic_db.record_version_warning(
                doc_path=doc_path,
                warning_type="hash_changed_version_same",
                old_version=change_detection.get("stored_version", ""),
                current_version=change_detection.get("stored_version", ""),
                old_hash=change_detection.get("stored_hash", ""),
                current_hash=change_detection.get("current_hash", ""),
                warning_message=warning_message
            )
            print(f"⚠️ {warning_message}")
```

## 🧮 文档哈希计算器设计

### DocumentHashCalculator类（哈希计算核心）
```python
# src/v4/utils/document_hash_calculator.py
# 文档哈希计算器 - 支持内容哈希和语义哈希

import hashlib
import re
from typing import str
from pathlib import Path

class DocumentHashCalculator:
    """文档哈希计算器 - 支持内容级别和语义级别的哈希计算"""

    def __init__(self):
        self.encoding = 'utf-8'
        # 定义需要忽略的非语义内容模式
        self.non_semantic_patterns = [
            r'<!--.*?-->',  # HTML注释
            r'#.*?$',       # Markdown注释（行末）
            r'^\s*$',       # 空行
            r'^\s*\*\*创建日期\*\*:.*$',  # 创建日期
            r'^\s*\*\*更新时间\*\*:.*$',  # 更新时间
            r'^\s*\*\*版本\*\*:.*$',     # 版本信息（某些情况下）
        ]

    def calculate_content_hash(self, doc_path: str) -> str:
        """计算文档内容哈希值（包含所有内容）"""
        try:
            with open(doc_path, 'r', encoding=self.encoding) as file:
                content = file.read()

            # 标准化换行符
            content = content.replace('\r\n', '\n').replace('\r', '\n')

            # 计算SHA-256哈希
            return hashlib.sha256(content.encode(self.encoding)).hexdigest()

        except Exception as e:
            print(f"❌ 计算内容哈希失败 {doc_path}: {str(e)}")
            return ""

    def calculate_semantic_hash(self, doc_path: str) -> str:
        """计算语义级别的哈希值（忽略格式变化）"""
        try:
            with open(doc_path, 'r', encoding=self.encoding) as file:
                content = file.read()

            # 提取语义内容
            semantic_content = self._extract_semantic_content(content)

            # 计算语义哈希
            return hashlib.sha256(semantic_content.encode(self.encoding)).hexdigest()[:16]

        except Exception as e:
            print(f"❌ 计算语义哈希失败 {doc_path}: {str(e)}")
            return ""

    def _extract_semantic_content(self, content: str) -> str:
        """提取语义内容（过滤非语义变化）"""
        lines = content.split('\n')
        semantic_lines = []

        for line in lines:
            # 跳过非语义内容
            if self._is_non_semantic_line(line):
                continue

            # 标准化空白字符
            normalized_line = re.sub(r'\s+', ' ', line.strip())
            if normalized_line:
                semantic_lines.append(normalized_line)

        return '\n'.join(semantic_lines)

    def _is_non_semantic_line(self, line: str) -> bool:
        """判断是否为非语义行"""
        for pattern in self.non_semantic_patterns:
            if re.match(pattern, line, re.MULTILINE):
                return True
        return False

    def batch_calculate_hashes(self, file_paths: List[str]) -> Dict[str, Dict[str, str]]:
        """批量计算哈希值（性能优化）"""
        results = {}

        for file_path in file_paths:
            results[file_path] = {
                'content_hash': self.calculate_content_hash(file_path),
                'semantic_hash': self.calculate_semantic_hash(file_path)
            }

        return results
```

## 📝 文档版本解析器设计

### DocumentVersionParser类（版本号解析）
```python
# src/v4/utils/document_version_parser.py
# 文档版本解析器 - 支持Fxxx格式版本号识别

import re
from typing import Optional, Dict
from pathlib import Path

class DocumentVersionParser:
    """文档版本解析器 - 支持L1/L2/L3组合式版本号和自动生成"""

    def __init__(self):
        # 组合式版本号正则表达式（L1.L2.L3格式）
        self.version_patterns = [
            r'F(\d{3,})',           # F001, F002, F123等（传统格式）
            r'V(\d+)\.(\d+)',       # V4.0, V3.1等（传统格式）
            r'v(\d+)',              # v1, v2, v3, v4等（传统格式）
            r'L(\d+)\.(\d+)\.(\d+)', # L1.2.3格式（新组合式版本）
            r'(\d+)\.(\d+)\.(\d+)', # 1.2.3格式（简化组合式版本）
            r'版本[：:]\s*([^\s]+)', # 中文版本标识
        ]

        # 版本号自动生成器
        self.version_generator = CompositeVersionGenerator()

        # 版本关系管理器
        self.version_relationship_manager = VersionRelationshipManager()

        # 文档路径中的版本目录模式
        self.path_version_patterns = [
            r'/v(\d+)/',            # /v1/, /v2/, /v4/
            r'/V(\d+)/',            # /V1/, /V2/, /V4/
            r'/version(\d+)/',      # /version1/, /version2/
        ]

    def extract_version(self, doc_path: str) -> str:
        """从文档路径和内容中提取版本号"""
        # 1. 尝试从文件内容中提取版本号
        content_version = self._extract_version_from_content(doc_path)
        if content_version:
            return content_version

        # 2. 尝试从文件路径中提取版本号
        path_version = self._extract_version_from_path(doc_path)
        if path_version:
            return path_version

        # 3. 如果都没有找到，返回默认版本
        return "UNKNOWN"

    def _extract_version_from_content(self, doc_path: str) -> Optional[str]:
        """从文档内容中提取版本号"""
        try:
            with open(doc_path, 'r', encoding='utf-8') as file:
                # 只读取前50行（版本信息通常在文档开头）
                lines = [file.readline() for _ in range(50)]
                content = ''.join(lines)

            # 按优先级尝试匹配版本模式
            for pattern in self.version_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    return match.group(0)

            return None

        except Exception as e:
            print(f"❌ 读取文档内容失败 {doc_path}: {str(e)}")
            return None

    def _extract_version_from_path(self, doc_path: str) -> Optional[str]:
        """从文件路径中提取版本号"""
        path_str = str(doc_path)

        # 尝试匹配路径中的版本模式
        for pattern in self.path_version_patterns:
            match = re.search(pattern, path_str, re.IGNORECASE)
            if match:
                return f"v{match.group(1)}"

        return None

    def parse_version_info(self, doc_path: str) -> Dict:
        """解析完整的版本信息"""
        version_str = self.extract_version(doc_path)

        return {
            'version_string': version_str,
            'is_fxxx_format': bool(re.match(r'F\d{3,}', version_str)),
            'is_vx_format': bool(re.match(r'[Vv]\d+', version_str)),
            'is_legacy': version_str == "UNKNOWN",
            'extracted_from': self._determine_extraction_source(doc_path, version_str)
        }

    def _determine_extraction_source(self, doc_path: str, version_str: str) -> str:
        """确定版本号的提取来源"""
        if version_str == "UNKNOWN":
            return "none"

        # 检查是否来自内容
        content_version = self._extract_version_from_content(doc_path)
        if content_version == version_str:
            return "content"

        # 检查是否来自路径
        path_version = self._extract_version_from_path(doc_path)
        if path_version == version_str:
            return "path"

        return "unknown"

## 🔢 组合式版本号生成器设计

### CompositeVersionGenerator类（L1.L2.L3版本号管理）
```python
# src/v4/utils/composite_version_generator.py
# 组合式版本号生成器 - 支持L1/L2/L3层级版本管理

import re
import json
from typing import Dict, Optional, Tuple
from pathlib import Path
from enum import Enum

class DocumentType(Enum):
    """文档类型枚举"""
    DESIGN = "design"                    # 设计文档
    IMPLEMENTATION_PLAN = "impl_plan"    # 实施计划文档
    CODE = "code"                        # 代码文件
    TEST = "test"                        # 测试文件

class VersionLevel(Enum):
    """版本层级枚举"""
    L1 = 1  # 主版本（重大架构变更）
    L2 = 2  # 次版本（功能增加或修改）
    L3 = 3  # 修订版本（bug修复或小改动）

class CompositeVersionGenerator:
    """组合式版本号生成器 - L1.L2.L3格式版本管理"""

    def __init__(self):
        # 版本计数器（按文档类型和项目分组）
        self.version_counters = {
            DocumentType.DESIGN: {"L1": 1, "L2": 0, "L3": 0},
            DocumentType.IMPLEMENTATION_PLAN: {"L1": 1, "L2": 0, "L3": 0},
            DocumentType.CODE: {"L1": 1, "L2": 0, "L3": 0},
            DocumentType.TEST: {"L1": 1, "L2": 0, "L3": 0}
        }

        # 版本关系映射（设计->实施计划一对一，设计<->代码多对多）
        self.version_relationships = {
            "design_to_implementation": {},  # 一对一关系
            "design_to_code": {},            # 多对多关系
            "implementation_to_code": {}     # 多对多关系
        }

    def suggest_version_for_new_document(self, doc_path: str, doc_type: DocumentType,
                                        version_level: VersionLevel = VersionLevel.L3) -> str:
        """为新文档建议版本号（V4原则：只建议，不自动修改）"""

        # 检查是否已有版本号
        existing_version = self._extract_existing_version(doc_path)
        if existing_version and existing_version != "UNKNOWN":
            return existing_version

        # 建议新的组合式版本号
        suggested_version = self._generate_composite_version(doc_type, version_level)

        # 记录版本建议历史（不是自动生成）
        self._record_version_suggestion(doc_path, doc_type, suggested_version, "suggested_only")

        print(f"� 建议版本号: {doc_path} -> {suggested_version} (需用户手动添加)")
        return suggested_version

    def increment_version(self, doc_path: str, doc_type: DocumentType,
                         version_level: VersionLevel) -> str:
        """递增版本号"""

        # 获取当前版本
        current_version = self._extract_existing_version(doc_path)
        if current_version == "UNKNOWN":
            return self.generate_version_for_new_document(doc_path, doc_type, version_level)

        # 解析当前版本
        l1, l2, l3 = self._parse_composite_version(current_version)

        # 根据版本层级递增
        if version_level == VersionLevel.L1:
            l1 += 1
            l2 = 0  # 重置次版本
            l3 = 0  # 重置修订版本
        elif version_level == VersionLevel.L2:
            l2 += 1
            l3 = 0  # 重置修订版本
        else:  # L3
            l3 += 1

        # 生成新版本号
        new_version = f"{l1}.{l2}.{l3}"

        # 更新版本计数器
        self.version_counters[doc_type]["L1"] = l1
        self.version_counters[doc_type]["L2"] = l2
        self.version_counters[doc_type]["L3"] = l3

        # 记录版本变更
        self._record_version_generation(doc_path, doc_type, new_version, "incremented")

        print(f"🔄 版本号递增: {doc_path} -> {new_version}")
        return new_version

    def _generate_composite_version(self, doc_type: DocumentType,
                                  version_level: VersionLevel) -> str:
        """生成组合式版本号"""

        counters = self.version_counters[doc_type]

        # 根据版本层级递增对应计数器
        if version_level == VersionLevel.L1:
            counters["L1"] += 1
            counters["L2"] = 0
            counters["L3"] = 0
        elif version_level == VersionLevel.L2:
            counters["L2"] += 1
            counters["L3"] = 0
        else:  # L3
            counters["L3"] += 1

        return f"{counters['L1']}.{counters['L2']}.{counters['L3']}"

    def _parse_composite_version(self, version_str: str) -> Tuple[int, int, int]:
        """解析组合式版本号"""

        # 尝试解析L1.L2.L3格式
        match = re.match(r'L?(\d+)\.(\d+)\.(\d+)', version_str)
        if match:
            return int(match.group(1)), int(match.group(2)), int(match.group(3))

        # 尝试解析传统格式
        if version_str.startswith('F'):
            # F001 -> 1.0.0
            f_num = int(version_str[1:])
            return f_num, 0, 0
        elif version_str.startswith('V'):
            # V4.0 -> 4.0.0
            v_match = re.match(r'V(\d+)\.(\d+)', version_str)
            if v_match:
                return int(v_match.group(1)), int(v_match.group(2)), 0
        elif version_str.startswith('v'):
            # v4 -> 4.0.0
            v_num = int(version_str[1:])
            return v_num, 0, 0

        # 默认返回1.0.0
        return 1, 0, 0

    def _extract_existing_version(self, doc_path: str) -> str:
        """提取现有版本号"""
        try:
            with open(doc_path, 'r', encoding='utf-8') as file:
                content = file.read()

            # 按优先级尝试匹配版本模式
            for pattern in [
                r'L(\d+)\.(\d+)\.(\d+)',  # L1.2.3格式优先
                r'(\d+)\.(\d+)\.(\d+)',   # 1.2.3格式
                r'F(\d{3,})',             # F001格式
                r'V(\d+)\.(\d+)',         # V4.0格式
                r'v(\d+)',                # v4格式
            ]:
                match = re.search(pattern, content)
                if match:
                    return match.group(0)

            return "UNKNOWN"

        except Exception:
            return "UNKNOWN"

    def _record_version_generation(self, doc_path: str, doc_type: DocumentType,
                                 version: str, generation_type: str):
        """记录版本生成历史"""
        # 这里可以记录到数据库或日志文件
        print(f"📝 版本生成记录: {doc_path} ({doc_type.value}) -> {version} ({generation_type})")

## 🔗 版本关系管理器设计

### VersionRelationshipManager类（文档版本关系管理）
```python
# src/v4/utils/version_relationship_manager.py
# 版本关系管理器 - 管理设计文档、实施计划、代码之间的版本关系

from typing import Dict, List, Set, Optional
from dataclasses import dataclass
from enum import Enum

class RelationshipType(Enum):
    """版本关系类型"""
    ONE_TO_ONE = "one_to_one"        # 设计文档 -> 实施计划（一对一）
    ONE_TO_MANY = "one_to_many"      # 设计文档 -> 代码文件（一对多）
    MANY_TO_ONE = "many_to_one"      # 代码文件 -> 设计文档（多对一）
    MANY_TO_MANY = "many_to_many"    # 设计文档 <-> 代码文件（多对多）

@dataclass
class VersionRelationship:
    """版本关系数据结构"""
    source_doc_path: str
    source_doc_type: DocumentType
    source_version: str
    target_doc_path: str
    target_doc_type: DocumentType
    target_version: str
    relationship_type: RelationshipType
    sync_status: str  # 'synchronized', 'source_newer', 'target_newer', 'conflict'
    created_at: str
    updated_at: str

class VersionRelationshipManager:
    """版本关系管理器 - 管理文档间的版本依赖关系"""

    def __init__(self):
        # 版本关系存储
        self.relationships: Dict[str, VersionRelationship] = {}

        # 关系类型规则
        self.relationship_rules = {
            (DocumentType.DESIGN, DocumentType.IMPLEMENTATION_PLAN): RelationshipType.ONE_TO_ONE,
            (DocumentType.DESIGN, DocumentType.CODE): RelationshipType.ONE_TO_MANY,
            (DocumentType.IMPLEMENTATION_PLAN, DocumentType.CODE): RelationshipType.ONE_TO_MANY,
            (DocumentType.CODE, DocumentType.TEST): RelationshipType.ONE_TO_ONE,
        }

    def establish_relationship(self, source_doc_path: str, source_doc_type: DocumentType,
                             source_version: str, target_doc_path: str,
                             target_doc_type: DocumentType, target_version: str) -> str:
        """建立文档版本关系"""

        # 确定关系类型
        relationship_type = self.relationship_rules.get(
            (source_doc_type, target_doc_type), RelationshipType.MANY_TO_MANY)

        # 生成关系ID
        relationship_id = f"{source_doc_path}#{target_doc_path}"

        # 创建版本关系
        relationship = VersionRelationship(
            source_doc_path=source_doc_path,
            source_doc_type=source_doc_type,
            source_version=source_version,
            target_doc_path=target_doc_path,
            target_doc_type=target_doc_type,
            target_version=target_version,
            relationship_type=relationship_type,
            sync_status='synchronized',
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )

        # 存储关系
        self.relationships[relationship_id] = relationship

        print(f"🔗 建立版本关系: {source_doc_path}({source_version}) -> {target_doc_path}({target_version})")
        return relationship_id

    def check_version_sync_status(self, source_doc_path: str, target_doc_path: str) -> Dict:
        """检查版本同步状态"""

        relationship_id = f"{source_doc_path}#{target_doc_path}"
        relationship = self.relationships.get(relationship_id)

        if not relationship:
            return {
                "status": "no_relationship",
                "recommendation": "需要建立版本关系"
            }

        # 获取当前版本
        current_source_version = self._get_current_version(source_doc_path)
        current_target_version = self._get_current_version(target_doc_path)

        # 比较版本
        source_changed = current_source_version != relationship.source_version
        target_changed = current_target_version != relationship.target_version

        if source_changed and target_changed:
            return {
                "status": "both_changed",
                "sync_status": "conflict",
                "recommendation": "需要解决版本冲突"
            }
        elif source_changed:
            return {
                "status": "source_newer",
                "sync_status": "source_newer",
                "recommendation": f"源文档版本更新({current_source_version})，建议同步目标文档"
            }
        elif target_changed:
            return {
                "status": "target_newer",
                "sync_status": "target_newer",
                "recommendation": f"目标文档版本更新({current_target_version})，建议检查一致性"
            }
        else:
            return {
                "status": "synchronized",
                "sync_status": "synchronized",
                "recommendation": "版本同步正常"
            }

    def get_related_documents(self, doc_path: str) -> Dict[str, List[str]]:
        """获取相关文档列表"""

        related_docs = {
            "design_documents": [],
            "implementation_plans": [],
            "code_files": [],
            "test_files": []
        }

        for relationship in self.relationships.values():
            if relationship.source_doc_path == doc_path:
                # 当前文档作为源文档
                if relationship.target_doc_type == DocumentType.DESIGN:
                    related_docs["design_documents"].append(relationship.target_doc_path)
                elif relationship.target_doc_type == DocumentType.IMPLEMENTATION_PLAN:
                    related_docs["implementation_plans"].append(relationship.target_doc_path)
                elif relationship.target_doc_type == DocumentType.CODE:
                    related_docs["code_files"].append(relationship.target_doc_path)
                elif relationship.target_doc_type == DocumentType.TEST:
                    related_docs["test_files"].append(relationship.target_doc_path)

            elif relationship.target_doc_path == doc_path:
                # 当前文档作为目标文档
                if relationship.source_doc_type == DocumentType.DESIGN:
                    related_docs["design_documents"].append(relationship.source_doc_path)
                elif relationship.source_doc_type == DocumentType.IMPLEMENTATION_PLAN:
                    related_docs["implementation_plans"].append(relationship.source_doc_path)
                elif relationship.source_doc_type == DocumentType.CODE:
                    related_docs["code_files"].append(relationship.source_doc_path)
                elif relationship.source_doc_type == DocumentType.TEST:
                    related_docs["test_files"].append(relationship.source_doc_path)

        return related_docs

    def _get_current_version(self, doc_path: str) -> str:
        """获取文档当前版本"""
        # 这里应该调用DocumentVersionParser来获取当前版本
        # 简化实现
        return "1.0.0"
```

## 🔗 与现有V4架构集成设计

### 三重验证机制集成
```yaml
# SQLite全景模型与三重验证机制的集成策略
triple_verification_integration_with_sqlite:
  v4_algorithm_panoramic_verification_enhancement: |
    @INTEGRATION:V4算法全景验证增强
    集成方式=SQLite全景模型为V4算法提供历史数据和全局上下文
    验证增强=基于历史版本演进数据进行全景一致性验证
    置信度提升=从单次验证提升到基于历史数据的趋势验证

  python_ai_logic_chain_verification_enhancement: |
    @INTEGRATION:Python AI关系逻辑链验证增强
    集成方式=SQLite关系数据为Python AI提供逻辑链验证的数据基础
    验证增强=基于存储的关系数据进行逻辑链一致性检查
    性能优化=避免重复分析，基于已有关系数据快速验证

  ide_ai_template_verification_enhancement: |
    @INTEGRATION:IDE AI模板验证增强
    集成方式=SQLite质量指标数据为IDE AI模板验证提供历史基准
    验证增强=基于历史质量指标进行模板匹配度验证
    迭代优化=基于验证历史数据持续优化模板验证策略
```

### 现有存储架构增强
```yaml
# 增强现有V4安全存储架构
v4_secure_storage_architecture_enhancement:
  warm_data_layer_sqlite_enhancement: |
    @ENHANCEMENT:温数据层SQLite增强
    现有设计="SQLite + 应用级加密"
    增强内容=全景模型数据库 + 智能扫描优化 + 版本管理
    性能提升=查询时间从≤100ms优化到≤50ms（基于索引优化）
    容量管理=≤4GB容量支持，智能数据归档和压缩

  intelligent_caching_strategy_enhancement: |
    @ENHANCEMENT:智能缓存策略增强
    l3_cache_enhancement="索引和元数据（SQLite，≤2GB）"
    增强内容=全景模型缓存 + 版本哈希缓存 + 关系数据缓存
    缓存策略=基于访问频率和置信度的智能缓存管理

  data_migration_and_compatibility: |
    @ENHANCEMENT:数据迁移和兼容性
    V3数据迁移=支持V3扫描器数据格式迁移到SQLite全景模型
    向后兼容=保持V3/V3.1算法70%复用率的数据兼容性
    渐进迁移=支持新老数据并存，渐进式迁移策略
```

### 扫描阶段集成接口
```python
# src/v4/integration/scanning_integration.py
# 扫描阶段与SQLite全景模型的集成接口

class V4ScanningIntegration:
    """V4扫描阶段与SQLite全景模型集成"""

    def __init__(self):
        self.panoramic_db = PanoramicModelDatabase("data/v4_panoramic.db")
        self.intelligent_scanner = IntelligentScanningEngine(self.panoramic_db)
        self.legacy_v3_adapter = LegacyV3DataAdapter()

    def execute_v4_enhanced_scanning(self, design_doc_directory: str,
                                   scanning_mode: str = "smart") -> Dict:
        """执行V4增强扫描（集成SQLite全景模型）"""

        # 1. 发现设计文档
        documents = self._discover_design_documents(design_doc_directory)

        # 2. 检查是否需要V3数据迁移
        if self._needs_v3_data_migration():
            self._migrate_v3_data_to_sqlite()

        # 3. 执行智能扫描
        if scanning_mode == "smart":
            scan_result = self.intelligent_scanner.execute_smart_scan(documents)
        else:
            # 降级到传统扫描模式
            scan_result = self._execute_traditional_scan(documents)

        # 4. 生成V3兼容的输出格式
        v3_compatible_result = self._convert_to_v3_format(scan_result)

        # 5. 增加SQLite全景模型增强信息
        enhanced_result = self._add_panoramic_enhancements(v3_compatible_result, scan_result)

        return enhanced_result

    def _migrate_v3_data_to_sqlite(self):
        """迁移V3数据到SQLite全景模型"""
        print("🔄 检测到V3历史数据，开始迁移到SQLite全景模型...")

        v3_data = self.legacy_v3_adapter.load_v3_scan_results()
        for doc_path, v3_result in v3_data.items():
            # 转换V3数据格式到全景模型格式
            abstraction_data = self.legacy_v3_adapter.convert_v3_to_panoramic_format(v3_result)

            # 存储到SQLite
            self.panoramic_db.store_document_abstraction(
                doc_path=doc_path,
                version=v3_result.get("version", "V3_MIGRATED"),
                content_hash=v3_result.get("content_hash", ""),
                abstraction_data=abstraction_data
            )

        print("✅ V3数据迁移完成")
```

## 📊 性能优化和监控

### 性能指标定义
```yaml
# SQLite全景模型性能指标
sqlite_panoramic_model_performance_metrics:
  scanning_performance_improvement: |
    @METRIC:扫描性能提升
    快速扫描模式=≤50ms（基于SQLite查询）
    增量扫描模式=≤200ms（部分重新分析）
    全量重建模式=≤500ms（完整分析）
    性能提升目标=比全量处理快5-10倍

  storage_efficiency: |
    @METRIC:存储效率
    数据库大小=≤4GB（支持大型项目）
    索引效率=≤5ms主要查询响应时间
    压缩比率=≥60%（通过语义哈希去重）

  memory_usage_optimization: |
    @METRIC:内存使用优化
    运行时内存=≤300MB（包含缓存）
    数据库连接池=≤10个并发连接
    缓存命中率=≥85%（L1+L2+L3缓存）
```

## 🎯 实施路线图

### 第一阶段：核心SQLite架构（2-3周）
```yaml
phase1_core_sqlite_architecture:
  week1_database_design:
    - "完成SQLite Schema设计和测试"
    - "实现PanoramicModelDatabase核心类"
    - "建立基础索引和性能优化"

  week2_scanning_engine:
    - "实现IntelligentScanningEngine核心算法"
    - "完成DocumentHashCalculator和DocumentVersionParser"
    - "集成版本+哈希检测机制"

  week3_integration_testing:
    - "与现有V4架构集成测试"
    - "V3数据迁移功能实现"
    - "性能基准测试和优化"
```

### 第二阶段：三重验证集成（1-2周）
```yaml
phase2_triple_verification_integration:
  week4_verification_enhancement:
    - "集成三重验证机制到SQLite操作"
    - "实现基于历史数据的验证增强"
    - "完成置信度计算优化"

  week5_quality_assurance:
    - "93.3%整体执行正确度验证"
    - "全面测试和质量保证"
    - "文档更新和部署准备"
```

## 📋 成功标准验证

### 核心功能验证
```yaml
# 基于用户原始需求的成功标准
success_criteria_validation:
  sqlite_panoramic_model_establishment: |
    ✅ SQLite本地存储建立全景模型
    ✅ 包含设计文档历史抽象和版本信息
    ✅ 支持版本号和哈希值双重检测

  version_hash_detection_mechanism: |
    ✅ 版本号+哈希值变更检测机制
    ✅ hash改变但版本未改变时警告用户
    ✅ 版本改变时自动更新SQLite全景数据

  fast_scanning_optimization: |
    ✅ 实现快速扫描，避免全量文档重新建模
    ✅ 基于SQLite全景模型的增量扫描
    ✅ 智能决策：快速扫描vs增量扫描vs全量重建

  architecture_integration_compatibility: |
    ✅ 与现有三重验证机制完全兼容
    ✅ 增强现有V4安全存储架构
    ✅ 支持V3/V3.1算法70%复用率
    ✅ 93.3%整体执行正确度目标达成
```

## 🔧 V4版本管理边界保护机制（基于提示词核心设计）

### 版本管理边界保护设计原理

```yaml
# V4版本管理边界保护机制设计（复用提示词核心设计）
v4_version_boundary_protection_mechanism:
  deterministic_version_management: |
    V4系统管理范围: 确定性版本元素（项目代码F007、功能名称nexus）
    人工决策范围: 版本号递增、文档内容变更、架构决策
    边界保护原则: V4不能自动修改版本号，只能读取、分析和建议
    权限控制: 所有Python代码只有版本号读取权限，修改权限仅限实施计划文档和IDE AI

  intelligent_scanning_decision_enhancement: |
    基于版本+哈希检测的智能扫描决策优化
    快速扫描模式: 版本号和哈希值均未变化，≤50ms响应时间
    增量扫描模式: 版本号变化但哈希值部分变化，≤200ms处理时间
    全量重建模式: 版本号和哈希值均发生重大变化，完整重新分析
    警告机制: 哈希值变化但版本号未变化时，自动警告用户

  database_version_tracking_enhancement: |
    deterministic_versions_table: 存储V4系统管理的确定性版本信息
    human_decision_versions_table: 存储人工决策的版本变更记录
    version_consistency_validation: 自动检测版本一致性，提供警告和建议
    dependency_relationship_tracking: 基于版本号的文档依赖关系追踪

  panoramic_model_reliability_confirmation: |
    用户确认机制: 全景模型可靠性必须经过用户确认才能确定
    确认状态管理: NOT_CONFIRMED → PENDING_CONFIRMATION → USER_CONFIRMED_RELIABLE
    自动检测触发: 新生成或更新的全景模型自动请求用户确认
    过期重新确认: 确认过期的模型需要重新确认才能继续使用

  architecture_debt_detection_and_alerts: |
    版本落后检测: 自动检测文档版本与最新可用版本的差异
    严重程度分类: minor/moderate/severe/critical四级分类
    主动提醒机制: 基于严重程度的智能提醒频率控制
    负债影响评估: JSON格式存储架构负债项目和影响评估
    推荐行动生成: 自动生成针对性的架构负债处理建议

# SQLite数据库表结构增强
sqlite_version_boundary_tables:
  deterministic_versions_table: |
    CREATE TABLE deterministic_versions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        project_code TEXT NOT NULL,           -- F007等项目代码
        function_name TEXT NOT NULL,          -- nexus等功能名称
        document_type TEXT NOT NULL,          -- design/implementation/code
        v4_managed_elements TEXT NOT NULL,    -- JSON格式的V4管理元素
        creation_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_v4_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

  human_decision_versions_table: |
    CREATE TABLE human_decision_versions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        document_path TEXT NOT NULL,
        version_number TEXT NOT NULL,         -- 人工决策的版本号
        change_description TEXT,              -- 人工变更描述
        decision_rationale TEXT,              -- 决策理由
        human_operator TEXT,                  -- 操作人员
        decision_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

  version_boundary_violations_table: |
    CREATE TABLE version_boundary_violations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        violation_type TEXT NOT NULL,         -- 'unauthorized_version_modification', 'boundary_crossing'
        document_path TEXT NOT NULL,
        attempted_action TEXT NOT NULL,       -- 尝试的违规操作
        prevention_action TEXT NOT NULL,      -- 采取的防护措施
        violation_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

# 性能优化指标
performance_optimization_targets:
  fast_scanning_mode: "≤50ms响应时间（版本+哈希均未变化）"
  incremental_scanning_mode: "≤200ms处理时间（版本变化，哈希部分变化）"
  version_consistency_check: "≤10ms版本一致性检查时间"
  database_query_optimization: "基于版本索引的查询优化，≤5ms平均查询时间"
  cache_hit_ratio: "版本哈希缓存命中率≥85%"
```

### 版本管理边界保护实施策略

```yaml
# 实施策略（遵循认知边界约束）
implementation_strategy:
  cognitive_boundary_compliance: |
    修改范围控制: 仅在现有SQLite设计基础上增加版本边界保护表
    代码权限控制: 所有Python代码只能读取版本信息，不能修改
    边界检测机制: 自动检测和阻止越权的版本修改操作

  dry_principle_enforcement: |
    复用现有设计: 基于现有panoramic_models表结构扩展
    避免重复开发: 利用现有索引和查询优化机制
    集成现有验证: 与三重验证机制无缝集成

  gradual_optimization_approach: |
    第一阶段: 建立版本边界保护表结构
    第二阶段: 实现智能扫描决策优化
    第三阶段: 完善用户确认和架构负债检测机制
```

---

**技术可行性置信度：96.2%**（基于SQLite成熟技术和现有V4架构）
**整体执行正确度预期：93.3%**（基于三重验证机制增强）
**创建时间：2025-06-16**
**SQLite全景模型增强版本：V4.0-Triple-Verification-Enhanced-SQLite-Database-Design**
**版本管理边界保护增强版本：V4.0-Version-Boundary-Protection-Enhanced**
