---
title: PostgreSQL演进架构Schema规划指南
document_id: C024
document_type: 共享文档
category: 中间件
scope: PostgreSQL
keywords: [PostgreSQL, 演进架构, Schema演进, 数据库设计, 服务边界, 微服务准备, 分布式数据管理]
created_date: 2025-05-30
updated_date: 2025-01-15
status: 草稿
version: 2.0
authors: [AI助手]
affected_features:
  - F003 # PostgreSQL迁移
  - F004 # CommonsUidLibrary
related_docs:
  - ../integration-guide.md
  - ./development-standards-guide.md
  - ../../integration/postgresql-persistent-id-fingerprint-recovery.md
  - ../../integration/baidu-uid-generator-postgresql-implementation.md
  - ../../../features/F003-PostgreSQL迁移-20250508/design/migration-design.md
  - ../../../features/F004-CommonsUidLibrary-20250511/design/commons-uid-library-design.md
  - ../../architecture/patterns/postgresql-evolution-implementation-guide.md
  - ../../features/F003-PostgreSQL迁移-20250508/design/postgresql-evolution-architecture-integration.md
---

# PostgreSQL演进架构Schema规划指南

## 摘要

本文档提供了PostgreSQL数据库在演进架构模式下的Schema规划全面指南。文档涵盖了从单体架构到微服务架构的Schema演进策略，包括服务边界设计、数据分离策略、Schema版本管理、分布式数据管理等内容，旨在帮助开发团队构建支持架构演进的数据库结构。

## 演进架构整合概述

本指南基于持续演进架构设计原则，通过以下核心机制支持Schema规划的架构演进：

1. **服务边界感知设计**：Schema设计时考虑未来的服务拆分边界
2. **数据分离策略**：支持从共享数据库到独立数据库的演进
3. **Schema版本管理**：建立Schema演进的版本控制机制
4. **分布式数据准备**：为未来的分布式数据管理做准备

### 演进架构Schema设计原则

- **边界清晰原则**：Schema设计应反映清晰的服务边界
- **演进友好原则**：Schema结构应便于未来的拆分和演进
- **数据自治原则**：每个业务域应拥有自己的数据管理权
- **兼容性原则**：Schema演进应保持向后兼容性

## 文档关系说明

本文档是PostgreSQL演进架构相关文档体系的一部分，与其他文档的关系如下：

- [PostgreSQL演进架构集成指南](./integration-guide.md)：提供演进架构的配置和集成细节，包括Schema演进管理器
- [PostgreSQL演进架构开发规范指南](./development-standards-guide.md)：提供演进架构的编码规范，包括Schema相关的命名约定
- [PostgreSQL演进架构实施指南](../../architecture/patterns/postgresql-evolution-implementation-guide.md)：提供通用的演进架构实施模式

本文档专注于演进架构的Schema设计和规划，是数据库结构演进的指导性文档。

## 背景与目标

### 背景

随着项目从Cassandra迁移到PostgreSQL，以及多个业务模块的开发，需要一套清晰的Schema规划指南，以确保数据库结构的一致性和可维护性。特别是在同时使用百度UID生成器等基础设施组件和多种业务表的情况下，合理的Schema规划变得尤为重要。

### 目标

1. 提供清晰的PostgreSQL Schema设计原则
2. 定义业务表与基础设施表的分离策略
3. 建立多业务场景下的Schema规划方法
4. 制定Schema演进策略，支持业务发展
5. 总结最佳实践和推荐模式
6. 提供大规模和水平扩展的Schema规划策略
7. 指导项目初期如何平衡性能、性价比和复杂度

## 1. Schema设计原则

### 1.1 单一数据库，多Schema组织

**规则**：对于一个独立的应用或一组紧密耦合的服务，优先使用单个逻辑数据库，并通过多个Schema来组织不同的业务领域或模块。

**理由**：
- 简化连接管理、备份恢复、跨Schema查询和事务
- 为未来按Schema进行逻辑分割或迁移到独立数据库（微服务演进）提供可能性
- 初期避免了跨物理数据库的复杂性

**实现示例**：
```sql
-- 创建不同业务领域的Schema
CREATE SCHEMA IF NOT EXISTS identity_core;    -- 身份认证与授权
CREATE SCHEMA IF NOT EXISTS content_catalog;  -- 内容管理
CREATE SCHEMA IF NOT EXISTS security_audit;   -- 安全审计
CREATE SCHEMA IF NOT EXISTS infra_common;     -- 基础设施组件
```

### 1.2 Schema命名约定

**规则**：使用清晰、一致且有意义的命名约定，反映业务领域或技术功能。

**命名模式**：
- 业务Schema：`<业务领域>_<可选子域>`，如`user_management`、`order_processing`、`identity_core`
- 基础设施Schema：`infra_<组件类型>`，如`infra_uid`、`infra_audit`、`infra_config`
- 通用功能Schema：`common_<功能类型>`，如`common_lookup`、`common_config`

**命名规范**：
- 使用小写字母和下划线
- 避免使用特殊字符和空格
- 保持简洁但具有描述性
- 避免使用PostgreSQL保留关键字

### 1.3 Schema权限管理

**规则**：为每个Schema定义适当的权限，遵循最小权限原则。

**权限策略**：
- 为应用创建专用的数据库用户，而非使用超级用户
- 为不同的业务模块创建不同的数据库角色
- 只授予必要的Schema权限给相应的角色

**实现示例**：
```sql
-- 创建业务模块角色
CREATE ROLE user_module_role;
CREATE ROLE content_module_role;

-- 授予Schema权限
GRANT USAGE ON SCHEMA user_management TO user_module_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA user_management TO user_module_role;

GRANT USAGE ON SCHEMA content_catalog TO content_module_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA content_catalog TO content_module_role;

-- 创建应用用户并分配角色
CREATE USER app_user WITH PASSWORD 'secure_password';
GRANT user_module_role, content_module_role TO app_user;
```

## 2. 业务与基础设施Schema分离

### 2.1 基础设施Schema

**规则**：将基础设施组件（如百度UID生成器、审计日志、配置管理等）的表放在专用的Schema中，与业务Schema分离。

**基础设施Schema分类**：
- `infra_uid`：存放百度UID生成器相关表
- `infra_audit`：存放审计日志表
- `infra_config`：存放配置管理表
- `infra_job`：存放任务调度表

**百度UID生成器表示例**：
```sql
-- 创建基础设施Schema
CREATE SCHEMA IF NOT EXISTS infra_uid;

-- 在infra_uid Schema中创建表
CREATE TABLE infra_uid.instance_registry (
    instance_unique_id BIGSERIAL PRIMARY KEY,
    application_name VARCHAR(255) NOT NULL,
    environment VARCHAR(100) DEFAULT 'default',
    instance_group VARCHAR(255),
    status VARCHAR(50) DEFAULT 'ACTIVE',
    first_registered_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_seen_at TIMESTAMP WITH TIME ZONE,
    custom_metadata JSONB
);

CREATE TABLE infra_uid.worker_id_assignment (
    worker_id INT PRIMARY KEY,
    assigned_instance_unique_id BIGINT UNIQUE,
    assignment_status VARCHAR(50) DEFAULT 'AVAILABLE',
    assigned_at TIMESTAMP WITH TIME ZONE,
    lease_duration_seconds INT DEFAULT 300,
    lease_expires_at TIMESTAMP WITH TIME ZONE,
    last_heartbeat_at TIMESTAMP WITH TIME ZONE,

    CONSTRAINT fk_assigned_instance
        FOREIGN KEY(assigned_instance_unique_id)
        REFERENCES infra_uid.INSTANCE_REGISTRY(instance_unique_id)
        ON DELETE SET NULL
);
```

### 2.2 业务Schema

**规则**：根据业务领域或功能模块创建业务Schema，每个Schema包含相关的业务表。

**业务Schema示例**：
```sql
-- 创建用户管理Schema
CREATE SCHEMA IF NOT EXISTS user_management;

-- 在user_management Schema中创建表
CREATE TABLE user_management.user (
    user_id BIGINT PRIMARY KEY,  -- 使用百度UID生成器生成的ID
    name VARCHAR(100) NOT NULL,
    mail_address VARCHAR(255),
    location INTEGER,
    regist_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE user_management.user_auth (
    user_id BIGINT PRIMARY KEY REFERENCES user_management.user(user_id),
    password VARCHAR(255),
    status SMALLINT,
    auth_level INTEGER,
    last_login_time TIMESTAMP WITH TIME ZONE
);
```

### 2.3 跨Schema关系处理

**规则**：在处理跨Schema的表关系时，使用完全限定的表名引用。

**实现方式**：
- 在外键约束中使用完全限定的表名
- 在查询中使用Schema前缀
- 考虑使用视图来简化跨Schema查询

**示例**：
```sql
-- 跨Schema外键引用
CREATE TABLE content_catalog.article (
    article_id BIGINT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    author_id BIGINT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_author
        FOREIGN KEY(author_id)
        REFERENCES user_management.user(user_id)
);

-- 创建视图简化跨Schema查询
CREATE VIEW content_catalog.article_with_author AS
SELECT a.article_id, a.title, a.content, a.created_at,
       u.user_id, u.name as author_name
FROM content_catalog.article a
JOIN user_management.user u ON a.author_id = u.user_id;
```

## 3. 多业务场景的Schema规划

### 3.1 业务领域划分

**规则**：根据业务领域的自然边界划分Schema，每个主要业务领域对应一个Schema。

**常见业务领域Schema**：
- `user_management`：用户管理、认证和授权
- `content_catalog`：内容管理和目录
- `order_processing`：订单处理和支付
- `notification`：通知和消息
- `analytics`：分析和报表

**考虑因素**：
- 业务领域的独立性和内聚性
- 数据访问模式和频率
- 团队组织结构和责任划分
- 未来可能的微服务拆分

### 3.2 跨业务功能处理

**规则**：对于跨多个业务领域的功能，可以创建专门的Schema或在主要相关的业务Schema中实现。

**方案1：专用Schema**
```sql
-- 创建跨业务功能的专用Schema
CREATE SCHEMA IF NOT EXISTS tagging;

-- 标签定义表
CREATE TABLE tagging.tag (
    tag_id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 标签关联表（可关联任何实体）
CREATE TABLE tagging.entity_tag (
    entity_type VARCHAR(50) NOT NULL,
    entity_id BIGINT NOT NULL,
    tag_id BIGINT NOT NULL REFERENCES tagging.tag(tag_id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (entity_type, entity_id, tag_id)
);
```

**方案2：在主要相关业务Schema中实现**
```sql
-- 在用户管理Schema中添加标签功能
CREATE TABLE user_management.user_tag (
    user_id BIGINT NOT NULL REFERENCES user_management.user(user_id),
    tag_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, tag_name)
);

-- 在内容目录Schema中添加标签功能
CREATE TABLE content_catalog.article_tag (
    article_id BIGINT NOT NULL REFERENCES content_catalog.article(article_id),
    tag_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (article_id, tag_name)
);
```

### 3.3 通用功能Schema

**规则**：对于多个业务领域共享的通用功能，创建专门的通用Schema。

**通用Schema示例**：
```sql
-- 创建通用功能Schema
CREATE SCHEMA IF NOT EXISTS common_lookup;

-- 地区表
CREATE TABLE common_lookup.region (
    region_id INTEGER PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    parent_id INTEGER REFERENCES common_lookup.region(region_id),
    level SMALLINT NOT NULL,
    code VARCHAR(50)
);

-- 行业表
CREATE TABLE common_lookup.industry (
    industry_id INTEGER PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    parent_id INTEGER REFERENCES common_lookup.industry(industry_id),
    level SMALLINT NOT NULL,
    code VARCHAR(50)
);
```

## 4. Schema演进策略

### 4.1 初始阶段

**规则**：在项目初始阶段，可以采用简化的Schema结构，随着业务的发展逐步细化和扩展。

**初始Schema规划**：
1. 创建基础设施Schema（`infra_uid`）
2. 创建核心业务Schema（如`user_management`）
3. 创建通用功能Schema（如`common_lookup`）

**示例**：
```sql
-- 初始阶段的Schema创建
CREATE SCHEMA IF NOT EXISTS infra_uid;        -- 基础设施：UID生成器
CREATE SCHEMA IF NOT EXISTS user_management;  -- 核心业务：用户管理
CREATE SCHEMA IF NOT EXISTS common_lookup;    -- 通用功能：查询表
```

### 4.2 增长阶段

**规则**：随着业务的增长，根据需要添加新的业务Schema，并细化现有Schema的表结构。

**增长阶段Schema扩展**：
1. 添加新的业务Schema（如`content_catalog`、`order_processing`）
2. 在现有Schema中添加新表或修改表结构
3. 添加更多的基础设施Schema（如`infra_audit`）

**示例**：
```sql
-- 增长阶段的Schema扩展
CREATE SCHEMA IF NOT EXISTS content_catalog;  -- 新业务：内容管理
CREATE SCHEMA IF NOT EXISTS order_processing; -- 新业务：订单处理
CREATE SCHEMA IF NOT EXISTS infra_audit;      -- 新基础设施：审计日志
```

### 4.3 成熟阶段

**规则**：在业务成熟阶段，可能需要重构Schema结构，以适应更复杂的业务需求和更高的性能要求。

**成熟阶段Schema重构**：
1. 拆分过大的Schema为更细粒度的Schema
2. 优化表结构和索引
3. 考虑分区表和水平分片
4. 准备向微服务架构演进

**示例**：
```sql
-- 成熟阶段的Schema重构
-- 拆分user_management为更细粒度的Schema
CREATE SCHEMA IF NOT EXISTS user_profile;     -- 用户资料
CREATE SCHEMA IF NOT EXISTS user_auth;        -- 用户认证
CREATE SCHEMA IF NOT EXISTS user_preference;  -- 用户偏好

-- 将数据从原Schema迁移到新Schema
INSERT INTO user_profile.user
SELECT user_id, name, mail_address, location, regist_time, created_at, updated_at
FROM user_management.user;

INSERT INTO user_auth.user_credential
SELECT user_id, password, status, auth_level, last_login_time
FROM user_management.user_auth;
```

## 5. 最佳实践和推荐模式

### 5.1 Schema设计最佳实践

1. **明确分离基础设施表和业务表**：
   - 将百度UID生成器表放在`infra_uid` Schema中
   - 将审计日志表放在`infra_audit` Schema中
   - 将业务表按领域放在相应的业务Schema中

2. **统一命名约定**：
   - Schema名：使用小写字母和下划线，反映业务领域或技术功能
     - 业务Schema：`<业务领域>_<可选子域>`，如`user_management`、`identity_core`
     - 基础设施Schema：`infra_<组件类型>`，如`infra_uid`、`infra_audit`
     - 通用功能Schema：`common_<功能类型>`，如`common_lookup`、`common_config`
   - 表名：使用小写字母和下划线，采用单数形式（如`user`而非`users`）
   - 列名：使用小写字母和下划线，避免缩写
   - 约束名：使用`<约束类型>_<表名>_<列名>`格式（如`pk_user`、`fk_order_user`）

3. **统一审计字段**：
   - 在所有业务表中添加统一的审计字段：
     ```sql
     created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
     updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
     created_by BIGINT,
     updated_by BIGINT,
     version INTEGER NOT NULL DEFAULT 0
     ```

4. **合理使用JSONB类型**：
   - 对于动态属性、非结构化或半结构化数据，使用JSONB类型
   - 为JSONB字段创建GIN索引，优化查询性能

5. **强制指定Schema（强制性要求）**：
   - 所有实体类必须使用`@Table(name = "表名", schema = "schema名")`明确指定Schema
   - 不允许依赖任何默认Schema设置
   - 这是一项强制性要求，没有例外情况
   - 代码审查过程必须确保所有实体类都遵循此规范
   - **Schema结构必须由DBA或开发人员手动管理**，应用程序只负责验证Schema是否存在，不会尝试创建Schema

   **正确的实体类示例**：
   ```java
   @Entity
   @Table(name = "user", schema = "user_management")
   public class User {
       @Id
       private Long userId;

       @Column(name = "username", nullable = false)
       private String username;

       // 其他字段和方法...
   }
   ```

   **错误的实体类示例**：
   ```java
   @Entity
   @Table(name = "user")  // 错误：没有指定schema
   public class User {
       @Id
       private Long userId;

       @Column(name = "username", nullable = false)
       private String username;

       // 其他字段和方法...
   }
   ```

   **为什么这是强制性要求**：
   - 明确指定Schema提高了代码的可读性和明确性
   - 防止表被错误地创建在默认Schema中
   - 确保在多Schema环境中表的位置是明确的
   - 简化了配置，不需要依赖全局默认Schema设置
   - 避免因默认Schema设置变化而导致的问题

   **Schema管理职责划分**：
   - 数据库Schema结构由DBA或开发人员手动管理
   - 应用程序只负责验证Schema是否存在，不尝试创建
   - 如果验证发现任何必需的Schema不存在，应用程序将无法启动，并显示明确的错误信息
   - 所有Schema变更必须通过手动SQL脚本执行
   - 变更脚本应该被版本控制并记录在项目文档中
   - 禁止应用程序自动创建或修改Schema结构

   这种明确的职责划分确保了数据库结构的可控性和一致性，避免了自动化和手动操作混合导致的冲突，符合生产环境的最佳实践。

### 5.2 多业务场景推荐模式

1. **共享ID生成器**：
   - 所有业务Schema共享`infra_uid` Schema中的UID生成器
   - 在服务层注入`UidGenerator`，为所有业务实体生成ID

2. **跨Schema关系处理**：
   - 使用外键维护数据完整性
   - 创建视图简化跨Schema查询
   - 考虑使用物化视图优化复杂的跨Schema查询性能

3. **Schema权限管理**：
   - 创建业务模块角色，授予相应的Schema权限
   - 应用使用最小权限的数据库用户
   - 定期审计数据库权限

### 5.3 Schema演进推荐模式

1. **使用数据库迁移工具**：
   - 使用Flyway或Liquibase管理Schema变更
   - 为每个Schema创建独立的迁移脚本目录
   - 版本化所有Schema变更

2. **增量变更原则**：
   - 优先使用增量变更而非重建表
   - 避免在生产环境中进行破坏性变更
   - 使用临时表和批处理进行大规模数据迁移

3. **向后兼容原则**：
   - 添加新列时设置默认值或允许NULL
   - 使用视图保持API兼容性
   - 分阶段弃用和移除旧功能

## 6. 实际应用示例

### 6.1 电子商务平台Schema规划

**业务需求**：
- 用户管理：用户注册、认证和个人资料
- 商品目录：商品分类、属性和库存
- 订单处理：购物车、订单和支付
- 评价系统：商品评价和评分

**Schema规划**：
```sql
-- 基础设施Schema
CREATE SCHEMA IF NOT EXISTS infra_uid;        -- UID生成器
CREATE SCHEMA IF NOT EXISTS infra_audit;      -- 审计日志

-- 业务Schema
CREATE SCHEMA IF NOT EXISTS user_management;  -- 用户管理
CREATE SCHEMA IF NOT EXISTS product_catalog;  -- 商品目录
CREATE SCHEMA IF NOT EXISTS order_processing; -- 订单处理
CREATE SCHEMA IF NOT EXISTS review_system;    -- 评价系统

-- 通用功能Schema
CREATE SCHEMA IF NOT EXISTS common_lookup;    -- 查询表
```

**表结构示例**：
```sql
-- 用户管理Schema
CREATE TABLE user_management.user (
    user_id BIGINT PRIMARY KEY,  -- 使用UID生成器
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 商品目录Schema
CREATE TABLE product_catalog.product (
    product_id BIGINT PRIMARY KEY,  -- 使用UID生成器
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price NUMERIC(10, 2) NOT NULL,
    category_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 订单处理Schema
CREATE TABLE order_processing.order (
    order_id BIGINT PRIMARY KEY,  -- 使用UID生成器
    user_id BIGINT NOT NULL,
    order_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) NOT NULL,
    total_amount NUMERIC(10, 2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_order_user
        FOREIGN KEY(user_id)
        REFERENCES user_management.user(user_id)
);

-- 评价系统Schema
CREATE TABLE review_system.product_review (
    review_id BIGINT PRIMARY KEY,  -- 使用UID生成器
    product_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_review_product
        FOREIGN KEY(product_id)
        REFERENCES product_catalog.products(product_id),
    CONSTRAINT fk_review_user
        FOREIGN KEY(user_id)
        REFERENCES user_management.users(user_id)
);
```

## 7. 大规模和水平扩展支持

随着业务的增长，数据库需要支持更大规模的数据和更高的并发访问。本节提供了PostgreSQL在大规模场景下的Schema规划策略。

### 7.1 分区表策略

**规则**：对于大型表（通常超过100万行或10GB），应考虑使用表分区来提高性能和可管理性。

**分区类型**：
- **范围分区**：基于时间范围、ID范围等连续值
- **列表分区**：基于离散值，如地区、状态等
- **哈希分区**：基于哈希值，均匀分布数据

**适用场景**：
- **时间序列数据**：日志、事件、交易记录等按时间范围分区
- **多租户应用**：按租户ID进行列表或哈希分区
- **地理分布数据**：按地区或国家进行列表分区

**实现示例**：
```sql
-- 创建按月分区的订单表
CREATE TABLE order_processing.order (
    order_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    order_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(50) NOT NULL,
    total_amount NUMERIC(10, 2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (order_date);

-- 创建月度分区
CREATE TABLE order_processing.order_y2025m01 PARTITION OF order_processing.order
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE order_processing.order_y2025m02 PARTITION OF order_processing.order
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

-- 添加约束和索引
ALTER TABLE order_processing.order ADD PRIMARY KEY (order_id, order_date);
CREATE INDEX idx_order_user_date ON order_processing.order (user_id, order_date);
```

**性能考虑**：
- 分区裁剪（Partition Pruning）可显著提高查询性能
- 分区维护（添加/删除分区）不会锁定整个表
- 可以对不同分区应用不同的存储参数

### 7.2 水平分片架构

**规则**：当单个PostgreSQL实例无法满足性能或存储需求时，考虑水平分片（Sharding）架构。

**分片策略**：
- **功能分片**：按业务功能将不同Schema部署到不同数据库实例
- **数据分片**：按分片键（如用户ID、地区）将同一Schema的数据分布到多个实例
- **混合分片**：结合功能分片和数据分片

**实现方式**：
1. **应用层分片**：应用程序负责路由查询到正确的分片
2. **中间件分片**：使用如Citus、Postgres-XL等中间件管理分片
3. **代理层分片**：使用如ProxySQL、PgBouncer等代理进行查询路由

**分片键选择原则**：
- 选择数据分布均匀的字段
- 避免频繁跨分片查询的字段
- 优先考虑业务自然边界（如租户ID、地区）

**跨分片操作处理**：
- 使用分布式事务管理器（如XA）处理跨分片事务
- 实现应用层数据聚合处理跨分片查询
- 考虑使用消息队列实现最终一致性

### 7.3 读写分离配置

**规则**：通过配置主从复制和读写分离，提高系统的读取性能和可用性。

**架构设计**：
- 一个主库（Master）处理所有写操作
- 多个从库（Replica）处理读操作
- 使用连接池和负载均衡分发读请求

**实现方式**：
1. **配置PostgreSQL流复制**
2. **设置应用层读写分离**：
   ```java
   @Configuration
   public class DataSourceConfig {
       @Bean
       @Primary
       @ConfigurationProperties("postgresql.master")
       public DataSource masterDataSource() {
           return DataSourceBuilder.create().build();
       }

       @Bean
       @ConfigurationProperties("postgresql.replica")
       public DataSource replicaDataSource() {
           return DataSourceBuilder.create().build();
       }

       @Bean
       public DataSource routingDataSource() {
           Map<Object, Object> targetDataSources = new HashMap<>();
           targetDataSources.put(DataSourceType.MASTER, masterDataSource());
           targetDataSources.put(DataSourceType.REPLICA, replicaDataSource());

           RoutingDataSource routingDataSource = new RoutingDataSource();
           routingDataSource.setTargetDataSources(targetDataSources);
           routingDataSource.setDefaultTargetDataSource(masterDataSource());
           return routingDataSource;
       }
   }
   ```

**注意事项**：
- 考虑复制延迟对业务的影响
- 实现故障检测和自动切换
- 区分只读事务和读写事务

### 7.4 分布式ID和全局一致性

**规则**：在分布式环境中，使用全局唯一ID生成器确保跨分片的数据一致性。

**ID生成策略**：
- 使用百度UID生成器生成全局唯一ID
- 确保ID生成器本身的高可用性
- 考虑ID生成的性能和批量预分配

**全局一致性保证**：
- 使用两阶段提交（2PC）实现强一致性
- 使用SAGA模式实现最终一致性
- 实现分布式锁避免并发冲突

### 7.5 大规模数据的索引策略

**规则**：在大规模数据环境中，索引策略对性能影响更为显著，需要更加谨慎。

**索引类型选择**：
- **B-tree索引**：适用于等值、范围和排序查询
- **GIN索引**：适用于全文搜索和JSONB查询
- **BRIN索引**：适用于有序大表的低精度查询，空间效率高
- **部分索引**：只为满足特定条件的行创建索引

**索引优化策略**：
- 对大表使用并行索引创建
- 考虑索引的存储和维护成本
- 定期重建索引减少膨胀
- 使用覆盖索引避免表访问

**示例**：
```sql
-- 为大型日志表创建部分索引（只索引最近30天的数据）
CREATE INDEX idx_log_recent ON infra_audit.system_log (created_at, log_level)
WHERE created_at > CURRENT_DATE - INTERVAL '30 days';

-- 为JSONB字段创建GIN索引
CREATE INDEX idx_user_preference_gin ON user_management.user_preference
USING GIN (preferences jsonb_path_ops);

-- 为大型有序表创建BRIN索引
CREATE INDEX idx_event_brin ON analytics.event
USING BRIN (event_time) WITH (pages_per_range = 128);
```

### 7.6 连接池和资源管理

**规则**：在大规模环境中，合理配置连接池和资源限制对系统稳定性至关重要。

**连接池配置**：
- 根据硬件资源和并发需求设置最大连接数
- 设置合理的连接获取超时和空闲超时
- 实现连接验证和泄漏检测

**资源限制**：
- 设置语句超时防止长时间运行的查询
- 配置每个查询的内存限制
- 使用连接池隔离不同业务的资源使用

**监控和告警**：
- 监控连接池使用情况
- 跟踪慢查询和资源密集型查询
- 设置关键指标的告警阈值

## 8. 项目初期的性能、性价比和复杂度平衡

在项目初期，需要平衡性能需求、成本控制和系统复杂度，避免过度设计或性能不足。

### 8.1 初期架构简化策略

**规则**：项目初期应优先采用简化的架构，满足基本需求的同时为未来扩展留出空间。

**简化策略**：
- **单一数据库实例**：避免初期引入分片复杂性
- **最小Schema集**：只创建核心业务和基础设施Schema
- **基本索引策略**：只为关键查询创建索引
- **简化表结构**：避免过早引入复杂的表关系和约束

**渐进式复杂度**：
- 从单一实例开始，随着负载增加再考虑读写分离
- 从基本表结构开始，随着需求明确再细化
- 从简单查询开始，随着性能需求增加再优化

### 8.2 渐进式功能实现路径

**规则**：区分核心功能和高级功能，按优先级渐进实现，避免一次性引入过多复杂性。

**功能分级**：
- **P0（必要）**：基本数据存储和查询，核心业务表
- **P1（重要）**：基本索引，简单的跨表关系，审计字段
- **P2（有用）**：高级索引，复杂查询优化，读写分离
- **P3（锦上添花）**：分区表，物化视图，高级监控

**实施路径示例**：
```
阶段1（初始）：
- 创建核心Schema（infra_uid, user_management）
- 实现基本表结构和主键
- 配置基本连接池

阶段2（基础优化）：
- 添加常用查询的索引
- 实现基本的外键关系
- 添加审计字段和触发器

阶段3（功能扩展）：
- 添加更多业务Schema
- 实现更复杂的查询和关系
- 配置读写分离

阶段4（性能优化）：
- 实现表分区
- 创建物化视图
- 优化大型查询
```

### 8.3 资源需求和成本估算

**规则**：根据数据规模和访问模式，估算资源需求和成本，选择最具性价比的配置。

**资源估算因素**：
- 数据量（行数、大小）
- 并发用户数
- 查询复杂度和频率
- 写入频率和批量大小

**不同规模的配置建议**：

| 规模 | 数据量 | 并发用户 | CPU | 内存 | 存储 | 估算月成本 |
|-----|-------|---------|-----|------|------|----------|
| 小型 | <10GB | <100 | 2核 | 8GB | 100GB SSD | $50-100 |
| 中型 | 10-100GB | 100-1000 | 4-8核 | 16-32GB | 500GB SSD | $200-500 |
| 大型 | 100GB-1TB | 1000-5000 | 16-32核 | 64-128GB | 1-2TB SSD | $1000-2000 |
| 超大型 | >1TB | >5000 | 32+核 | 128GB+ | 分片+复制 | $3000+ |

**成本优化策略**：
- 使用自动扩展配置，根据负载调整资源
- 考虑使用预留实例降低云服务成本
- 实现数据生命周期管理，归档冷数据

### 8.4 性能监控和优化时机

**规则**：建立性能基准，定期监控关键指标，在适当时机进行优化。

**关键监控指标**：
- 查询响应时间（平均、95百分位、最大）
- 数据库CPU和内存使用率
- 磁盘I/O和IOPS
- 连接池使用情况
- 缓存命中率

**优化触发条件**：
- 查询响应时间超过预定阈值（如200ms）
- 数据库CPU持续高于70%
- 连接池使用率持续高于80%
- 用户反馈系统响应慢

**渐进式优化策略**：
1. 查询优化（改写SQL、添加索引）
2. 应用层优化（缓存、批处理）
3. 数据库配置优化（内存、连接池）
4. 架构优化（读写分离、分区）
5. 硬件升级或分片

### 8.5 复杂度管理和技术债务控制

**规则**：控制系统复杂度，及时偿还技术债务，保持系统的可维护性和可扩展性。

**复杂度控制策略**：
- 遵循"足够好"原则，避免过度工程
- 使用标准模式和约定，减少特殊情况
- 保持文档和代码的一致性
- 定期重构和优化
- **强制实体类指定Schema**：这是减少复杂度和避免配置问题的重要措施，所有实体类必须明确指定Schema，不依赖默认Schema设置

**技术债务管理**：
- 记录和跟踪已知的技术债务
- 分配固定比例的开发时间用于偿还技术债务
- 在添加新功能前评估现有技术债务的影响
- 定期进行架构和代码审查

**决策框架**：
```
当面临架构决策时，考虑以下因素：
1. 当前需求：这个功能现在真的需要吗？
2. 未来扩展：这个决策会限制未来的选择吗？
3. 复杂度成本：这个决策会增加多少复杂度？
4. 性能影响：这个决策会如何影响性能？
5. 维护成本：这个决策会增加多少维护负担？
```

## 参考资料

1. PostgreSQL官方文档：[Schema](https://www.postgresql.org/docs/current/ddl-schemas.html)
2. PostgreSQL官方文档：[Table Partitioning](https://www.postgresql.org/docs/current/ddl-partitioning.html)
3. 《PostgreSQL 9.0 High Performance》by Gregory Smith
4. 《PostgreSQL 10 Administration Cookbook》by Simon Riggs, Gianni Ciolli
5. 《The Art of PostgreSQL》by Dimitri Fontaine
6. 《PostgreSQL 14 Internals》by Egor Rogov
7. 项目内部文档：
   - [PostgreSQL集成指南](../integration-guide.md)
   - [基于PostgreSQL的持久化实例ID及特征码恢复方案](../../integration/postgresql-persistent-id-fingerprint-recovery.md)
   - [PostgreSQL迁移设计](../../../features/F003-PostgreSQL迁移-20250508/design/migration-design.md)

## 8. 演进架构Schema规划总结

### 8.1 Schema演进路径规划

根据不同的架构阶段，推荐以下Schema演进策略：

```mermaid
graph LR
    A[阶段1: 单体架构] --> B[阶段2: 模块化架构]
    B --> C[阶段3: 混合架构]
    C --> D[阶段4: 微服务架构]

    A1[共享数据库<br/>业务Schema分离<br/>基础设施Schema] --> A
    B1[模块边界清晰<br/>数据访问隔离<br/>Schema版本管理] --> B
    C1[部分数据分离<br/>跨服务数据协调<br/>数据一致性策略] --> C
    D1[完全数据自治<br/>独立数据库<br/>分布式数据管理] --> D
```

**阶段1：单体架构Schema设计**
- Schema策略：共享数据库，业务Schema分离
- 数据管理：集中式数据管理
- 演进准备：清晰的业务边界设计

**阶段2：模块化架构Schema设计**
- Schema策略：模块边界对应Schema边界
- 数据管理：模块内数据自治
- 演进准备：数据访问抽象层

**阶段3：混合架构Schema设计**
- Schema策略：部分数据分离，部分共享
- 数据管理：混合数据管理模式
- 演进准备：分布式数据协调机制

**阶段4：微服务架构Schema设计**
- Schema策略：完全数据自治
- 数据管理：独立数据库实例
- 演进准备：分布式数据一致性

### 8.2 演进架构Schema管理器

#### 8.2.1 Schema演进管理器实现

```java
/**
 * Schema演进管理器
 * 管理Schema的版本演进和架构适配
 */
@Component
public class SchemaEvolutionManager {

    @Autowired
    private ServiceConfiguration serviceConfiguration;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private SchemaVersionRepository schemaVersionRepository;

    /**
     * 初始化Schema演进环境
     */
    @PostConstruct
    public void initializeSchemaEvolution() {
        ServiceConfiguration.ArchitectureMode mode = serviceConfiguration.getArchitectureMode();
        log.info("初始化Schema演进环境，架构模式: {}", mode);

        switch (mode) {
            case MONOLITHIC:
                setupMonolithicSchemas();
                break;
            case MODULAR:
                setupModularSchemas();
                break;
            case HYBRID:
                setupHybridSchemas();
                break;
            case MICROSERVICES:
                setupMicroserviceSchemas();
                break;
            default:
                log.warn("未知的架构模式: {}", mode);
        }
    }

    /**
     * 设置单体架构Schema
     */
    private void setupMonolithicSchemas() {
        log.info("设置单体架构Schema");

        // 基础设施Schema
        createSchemaIfNotExists("infra_uid");
        createSchemaIfNotExists("infra_config");
        createSchemaIfNotExists("infra_audit");

        // 业务Schema
        createSchemaIfNotExists("user_management");
        createSchemaIfNotExists("content_catalog");
        createSchemaIfNotExists("order_processing");

        // 通用Schema
        createSchemaIfNotExists("common_lookup");
        createSchemaIfNotExists("common_config");

        log.info("单体架构Schema设置完成");
    }

    /**
     * 设置微服务架构Schema
     */
    private void setupMicroserviceSchemas() {
        log.info("设置微服务架构Schema");

        // 为每个微服务创建独立的Schema
        createSchemaIfNotExists("user_service");
        createSchemaIfNotExists("content_service");
        createSchemaIfNotExists("order_service");
        createSchemaIfNotExists("notification_service");

        // 共享的基础设施Schema
        createSchemaIfNotExists("shared_infra");
        createSchemaIfNotExists("shared_config");

        log.info("微服务架构Schema设置完成");
    }

    /**
     * 执行Schema演进
     */
    public void evolveSchema(String schemaName, String targetVersion) {
        SchemaVersion currentVersion = schemaVersionRepository.findBySchemaName(schemaName);

        if (currentVersion == null) {
            throw new SchemaEvolutionException("Schema不存在: " + schemaName);
        }

        if (currentVersion.getVersion().equals(targetVersion)) {
            log.info("Schema {} 已经是目标版本 {}", schemaName, targetVersion);
            return;
        }

        List<SchemaEvolutionStep> steps = planEvolutionSteps(
            currentVersion.getVersion(), targetVersion);

        executeEvolutionSteps(schemaName, steps);

        // 更新版本记录
        currentVersion.setVersion(targetVersion);
        currentVersion.setUpdatedAt(LocalDateTime.now());
        schemaVersionRepository.save(currentVersion);

        log.info("Schema {} 演进到版本 {} 完成", schemaName, targetVersion);
    }

    /**
     * 创建Schema（如果不存在）
     */
    private void createSchemaIfNotExists(String schemaName) {
        try {
            jdbcTemplate.execute("CREATE SCHEMA IF NOT EXISTS " + schemaName);
            log.debug("Schema {} 已准备就绪", schemaName);

            // 记录Schema版本
            SchemaVersion schemaVersion = schemaVersionRepository.findBySchemaName(schemaName);
            if (schemaVersion == null) {
                schemaVersion = new SchemaVersion();
                schemaVersion.setSchemaName(schemaName);
                schemaVersion.setVersion("1.0.0");
                schemaVersion.setCreatedAt(LocalDateTime.now());
                schemaVersion.setUpdatedAt(LocalDateTime.now());
                schemaVersionRepository.save(schemaVersion);
            }

        } catch (Exception e) {
            log.warn("创建Schema {} 失败: {}", schemaName, e.getMessage());
        }
    }
}
```

#### 8.2.2 Schema版本实体

```java
/**
 * Schema版本实体
 * 跟踪Schema的演进版本
 */
@Entity
@Table(name = "schema_version", schema = "infra_config")
public class SchemaVersion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "schema_name", nullable = false, unique = true)
    private String schemaName;

    @Column(name = "version", nullable = false)
    private String version;

    @Column(name = "description")
    private String description;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "architecture_mode")
    @Enumerated(EnumType.STRING)
    private ServiceConfiguration.ArchitectureMode architectureMode;

    // Getters and Setters
}
```

### 8.3 数据分离策略

#### 8.3.1 服务边界数据分离

```java
/**
 * 数据分离策略管理器
 * 管理不同架构模式下的数据分离策略
 */
@Service
public class DataSeparationStrategy {

    @Autowired
    private ServiceConfiguration serviceConfiguration;

    /**
     * 获取数据访问策略
     */
    public DataAccessStrategy getDataAccessStrategy(String serviceName) {
        ServiceConfiguration.ArchitectureMode mode = serviceConfiguration.getArchitectureMode();

        switch (mode) {
            case MICROSERVICES:
                return getMicroserviceDataStrategy(serviceName);
            case HYBRID:
                return getHybridDataStrategy(serviceName);
            case MODULAR:
                return getModularDataStrategy(serviceName);
            default:
                return getMonolithicDataStrategy();
        }
    }

    private DataAccessStrategy getMicroserviceDataStrategy(String serviceName) {
        return DataAccessStrategy.builder()
            .schemaName(serviceName + "_service")
            .dataIsolation(DataIsolation.COMPLETE)
            .crossServiceAccess(CrossServiceAccess.VIA_API)
            .consistencyLevel(ConsistencyLevel.EVENTUAL)
            .build();
    }

    private DataAccessStrategy getHybridDataStrategy(String serviceName) {
        return DataAccessStrategy.builder()
            .schemaName(getHybridSchemaName(serviceName))
            .dataIsolation(DataIsolation.PARTIAL)
            .crossServiceAccess(CrossServiceAccess.MIXED)
            .consistencyLevel(ConsistencyLevel.STRONG)
            .build();
    }

    private DataAccessStrategy getModularDataStrategy(String serviceName) {
        return DataAccessStrategy.builder()
            .schemaName(serviceName + "_management")
            .dataIsolation(DataIsolation.LOGICAL)
            .crossServiceAccess(CrossServiceAccess.DIRECT)
            .consistencyLevel(ConsistencyLevel.STRONG)
            .build();
    }

    private DataAccessStrategy getMonolithicDataStrategy() {
        return DataAccessStrategy.builder()
            .schemaName("shared")
            .dataIsolation(DataIsolation.NONE)
            .crossServiceAccess(CrossServiceAccess.DIRECT)
            .consistencyLevel(ConsistencyLevel.STRONG)
            .build();
    }
}
```

### 8.4 最佳实践总结

#### 8.4.1 Schema设计原则

1. **单体架构**：业务Schema分离，为未来演进做准备
2. **模块化架构**：模块边界对应Schema边界
3. **混合架构**：部分数据分离，保持灵活性
4. **微服务架构**：完全数据自治，独立数据库

#### 8.4.2 演进策略

- **渐进演进**：分阶段进行Schema演进，避免大爆炸式变更
- **版本管理**：建立完善的Schema版本管理机制
- **兼容性保证**：确保Schema演进的向后兼容性
- **监控验证**：建立Schema演进的监控和验证机制

#### 8.4.3 演进路径指南

1. **阶段1**：建立清晰的业务Schema边界
2. **阶段2**：引入数据访问抽象层
3. **阶段3**：实现部分数据分离
4. **阶段4**：完全数据自治和独立数据库

### 8.5 常见陷阱和避免方法

1. **过早分离**：不要在单体阶段就实现复杂的数据分离
2. **边界模糊**：确保Schema边界与服务边界一致
3. **版本混乱**：建立严格的Schema版本管理制度
4. **兼容性破坏**：避免破坏性的Schema变更

## 9. 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 2.0 | 2025-01-15 | 重构为演进架构Schema规划指南，增加Schema演进管理器、数据分离策略、服务边界设计等演进架构特性 | AI助手 |
| 1.3 | 2025-06-07 | 明确Schema由人工管理，添加Schema管理职责划分 | AI助手 |
| 1.2 | 2025-06-06 | 添加强制指定Schema作为强制性要求 | AI助手 |
| 1.1 | 2025-05-31 | 添加大规模支持和项目初期平衡章节 | AI助手 |
| 1.0 | 2025-05-30 | 初始版本 | AI助手 |
