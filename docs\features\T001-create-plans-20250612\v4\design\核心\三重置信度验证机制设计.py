#!/usr/bin/env python3
"""
三重置信度验证机制设计（完整版）
基于IDE AI自评 + Python AI关系逻辑链推理验证 + V4算法全景验证的完整三重验证系统

核心创新：
1. Python AI关系逻辑链矛盾推理验证 - 基于语义关系和逻辑链条的深度推理验证
2. V4算法全景验证 - 基于V4掌握的全景知识库进行一致性和完整性验证
3. 完整三重验证工作流 - 三个验证层次的融合和校正机制

目标：实现93.3%整体执行正确度，超越简单的v3.1算法验证
"""

from typing import Dict, List, Tuple, Optional, Set, Any
from dataclasses import dataclass, field
from enum import Enum
import json
import math
import re
from collections import defaultdict
import networkx as nx  # 用于关系图分析

class ValidationMode(Enum):
    """验证模式枚举"""
    FULL_TRIPLE = "FULL_TRIPLE"                    # 完整三重验证
    FAST_ITERATION = "FAST_ITERATION"              # 快速迭代模式（关闭Python AI）
    ALGORITHM_ONLY = "ALGORITHM_ONLY"              # 仅算法验证
    CUSTOM = "CUSTOM"                              # 自定义模式

@dataclass
class ValidationConfig:
    """验证配置"""
    mode: ValidationMode = ValidationMode.FULL_TRIPLE

    # AI开关配置
    enable_ide_ai_validation: bool = True
    enable_python_ai_logic_validation: bool = True
    enable_v4_panoramic_validation: bool = True

    # 快速迭代模式配置
    fast_iteration_focus: str = "ide_ai_only"     # "ide_ai_only", "algorithm_only", "minimal"

    # 验证阈值配置
    confidence_threshold: float = 0.7
    contradiction_threshold: float = 0.15
    severe_contradiction_threshold: float = 0.25

    # 性能配置
    enable_detailed_logging: bool = True
    enable_performance_metrics: bool = False
    max_validation_time_seconds: int = 30

    # 输出配置
    output_level: str = "DETAILED"                 # "MINIMAL", "STANDARD", "DETAILED"
    save_intermediate_results: bool = False

class ConfidenceLevel(Enum):
    """置信度等级"""
    VERY_HIGH = "VERY_HIGH"  # ≥95%
    HIGH = "HIGH"            # 85-94%
    MEDIUM = "MEDIUM"        # 70-84%
    LOW = "LOW"              # 50-69%
    VERY_LOW = "VERY_LOW"    # <50%

@dataclass
class RelationshipLogicChain:
    """关系逻辑链数据结构"""
    chain_id: str
    source_field: str
    target_field: str
    relationship_type: str  # "depends_on", "conflicts_with", "supports", "implements"
    logic_strength: float   # 逻辑关系强度 0-1
    semantic_similarity: float  # 语义相似度 0-1
    contradiction_risk: float   # 矛盾风险 0-1

@dataclass
class V4KnowledgeReference:
    """V4知识库参考数据结构"""
    knowledge_id: str
    knowledge_category: str  # "architecture_pattern", "technology_stack", "best_practice"
    knowledge_content: str
    confidence_score: float
    last_updated: str
    validation_status: str   # "validated", "pending", "deprecated"

@dataclass
class FieldConfidence:
    """增强的字段置信度数据结构"""
    field_name: str
    ide_ai_confidence: float
    python_algorithm_confidence: float
    python_ai_combined_confidence: float
    content: str
    field_type: str

    # 新增：Python AI关系逻辑链验证结果
    relationship_logic_validation: Optional[Dict] = None
    semantic_consistency_score: float = 0.0
    logic_chain_contradictions: List[str] = field(default_factory=list)

    # 新增：V4算法全景验证结果
    v4_panoramic_validation: Optional[Dict] = None
    v4_knowledge_alignment_score: float = 0.0
    v4_completeness_score: float = 0.0
    v4_consistency_violations: List[str] = field(default_factory=list)

    # 新增：综合验证元数据
    validation_timestamp: str = ""
    validation_version: str = "v4.0"
    final_confidence_score: float = 0.0

@dataclass
class TripleValidationResult:
    """三重验证结果数据结构"""
    field_name: str

    # 第一层：IDE AI自评结果
    ide_ai_result: Dict

    # 第二层：Python AI关系逻辑链验证结果
    python_ai_logic_result: Dict

    # 第三层：V4算法全景验证结果
    v4_panoramic_result: Dict

    # 综合验证结果
    overall_validation_status: str  # "PASSED", "WARNING", "FAILED"
    confidence_convergence_score: float
    critical_issues: List[str] = field(default_factory=list)
    optimization_recommendations: List[str] = field(default_factory=list)

class IDEAIConfidenceTracker:
    """IDE AI置信度追踪器"""
    
    def __init__(self):
        self.field_confidences = {}
        self.base_confidence_map = {
            "architecture_pattern": 0.90,
            "technology_stack": 0.93,
            "interface_design": 0.88,
            "performance_metrics": 0.85,
            "implementation_details": 0.70,
            "business_logic": 0.60
        }
    
    def fill_with_confidence(self, field_name: str, content: str, field_type: str) -> str:
        """填写内容并记录置信度"""
        confidence = self.assess_self_confidence(field_type, len(content.split()))
        self.field_confidences[field_name] = confidence
        return f"{content} [AI_CONFIDENCE={confidence:.2f}]"
    
    def assess_self_confidence(self, field_type: str, content_complexity: int) -> float:
        """AI自我评估置信度"""
        base_confidence = self.base_confidence_map.get(field_type, 0.75)
        
        # 基于内容复杂度调整
        complexity_factor = min(1.0, content_complexity / 50.0)  # 50词为基准
        
        # 基于字段类型的特定调整
        type_adjustments = {
            "architecture_pattern": 0.05,  # 架构模式相对确定
            "technology_stack": 0.08,      # 技术栈有明确约束
            "business_logic": -0.15,       # 业务逻辑不确定性高
            "implementation_details": -0.10 # 实现细节需要验证
        }
        
        adjustment = type_adjustments.get(field_type, 0)
        final_confidence = base_confidence + adjustment + (complexity_factor * 0.1)
        
        return min(0.98, max(0.30, final_confidence))

class PythonAlgorithmConfidenceCalculator:
    """Python算法置信度计算器"""
    
    def calculate_python_confidence(self, filled_template: Dict) -> Dict[str, float]:
        """基于数学模型计算置信度"""
        field_confidences = {}
        
        for field_name, field_data in filled_template.items():
            if isinstance(field_data, dict) and 'content' in field_data:
                logic_score = self._analyze_logical_consistency(field_data['content'])
                completeness_score = self._check_field_completeness(field_data)
                format_compliance = self._validate_format_compliance(field_data)
                
                confidence = (logic_score * 0.5 + completeness_score * 0.3 + format_compliance * 0.2)
                field_confidences[field_name] = confidence
        
        return field_confidences
    
    def _analyze_logical_consistency(self, content: str) -> float:
        """分析逻辑一致性"""
        # 基于关键词密度、句法结构等分析
        keywords_density = len([w for w in content.split() if len(w) > 5]) / max(1, len(content.split()))
        structure_score = min(1.0, len(content.split('.')) / 10.0)  # 句子结构复杂度
        return min(0.95, (keywords_density * 0.6 + structure_score * 0.4))
    
    def _check_field_completeness(self, field_data: Dict) -> float:
        """检查字段完整性"""
        required_keys = ['content', 'type', 'category']
        present_keys = sum(1 for key in required_keys if key in field_data)
        return present_keys / len(required_keys)
    
    def _validate_format_compliance(self, field_data: Dict) -> float:
        """验证格式合规性"""
        content = field_data.get('content', '')
        if not content:
            return 0.0
        
        # 基本格式检查
        has_proper_structure = len(content) > 10 and '.' in content
        has_technical_terms = any(term in content.lower() for term in 
                                ['interface', 'class', 'method', 'service', 'plugin'])
        
        return 0.8 if has_proper_structure and has_technical_terms else 0.5

class PythonAIRelationshipLogicValidator:
    """Python AI关系逻辑链矛盾推理验证器"""

    def __init__(self):
        self.relationship_graph = nx.DiGraph()
        self.semantic_keywords = {
            "architecture_pattern": ["微内核", "服务总线", "插件", "模块", "组件", "接口"],
            "technology_stack": ["Spring", "Java", "Maven", "Docker", "数据库"],
            "interface_design": ["接口", "API", "方法", "参数", "返回值", "协议"],
            "performance_metrics": ["性能", "延迟", "吞吐量", "响应时间", "资源消耗"],
            "implementation_details": ["实现", "算法", "数据结构", "流程", "逻辑"],
            "business_logic": ["业务", "流程", "规则", "需求", "功能", "场景"]
        }
        self.contradiction_patterns = [
            (r"同步.*异步", "同步异步矛盾"),
            (r"单例.*多例", "实例化模式矛盾"),
            (r"有状态.*无状态", "状态管理矛盾"),
            (r"阻塞.*非阻塞", "阻塞模式矛盾"),
            (r"集中式.*分布式", "架构模式矛盾")
        ]

    def validate_relationship_logic_chains(self, field_confidences: List[FieldConfidence]) -> Dict:
        """验证关系逻辑链的一致性和矛盾"""

        # 构建关系图
        self._build_relationship_graph(field_confidences)

        # 执行逻辑链分析
        logic_chain_results = {}
        for field_conf in field_confidences:
            result = self._analyze_field_logic_chains(field_conf, field_confidences)
            logic_chain_results[field_conf.field_name] = result

            # 更新字段的关系逻辑验证结果
            field_conf.relationship_logic_validation = result
            field_conf.semantic_consistency_score = result['semantic_consistency_score']
            field_conf.logic_chain_contradictions = result['contradictions']

        # 全局逻辑链一致性分析
        global_consistency = self._analyze_global_logic_consistency(field_confidences)

        return {
            "validation_type": "PYTHON_AI_RELATIONSHIP_LOGIC",
            "individual_field_results": logic_chain_results,
            "global_consistency_analysis": global_consistency,
            "critical_logic_contradictions": self._identify_critical_contradictions(logic_chain_results),
            "semantic_coherence_score": self._calculate_semantic_coherence(field_confidences),
            "logic_chain_optimization_suggestions": self._generate_logic_optimization_suggestions(logic_chain_results)
        }

    def _build_relationship_graph(self, field_confidences: List[FieldConfidence]):
        """构建字段间关系图"""
        self.relationship_graph.clear()

        for field_conf in field_confidences:
            self.relationship_graph.add_node(field_conf.field_name,
                                           content=field_conf.content,
                                           field_type=field_conf.field_type)

        # 分析字段间的语义关系
        for i, field1 in enumerate(field_confidences):
            for j, field2 in enumerate(field_confidences):
                if i != j:
                    relationship = self._analyze_semantic_relationship(field1, field2)
                    if relationship['strength'] > 0.3:  # 关系强度阈值
                        self.relationship_graph.add_edge(
                            field1.field_name, field2.field_name,
                            relationship_type=relationship['type'],
                            strength=relationship['strength'],
                            semantic_similarity=relationship['semantic_similarity']
                        )

    def _analyze_semantic_relationship(self, field1: FieldConfidence, field2: FieldConfidence) -> Dict:
        """分析两个字段间的语义关系"""
        content1 = field1.content.lower()
        content2 = field2.content.lower()

        # 计算语义相似度
        semantic_similarity = self._calculate_semantic_similarity(content1, content2, field1.field_type, field2.field_type)

        # 识别关系类型
        relationship_type = self._identify_relationship_type(content1, content2, field1.field_type, field2.field_type)

        # 计算关系强度
        strength = self._calculate_relationship_strength(content1, content2, semantic_similarity, relationship_type)

        return {
            "type": relationship_type,
            "strength": strength,
            "semantic_similarity": semantic_similarity
        }

    def _calculate_semantic_similarity(self, content1: str, content2: str, type1: str, type2: str) -> float:
        """计算语义相似度"""
        # 基于关键词重叠度
        keywords1 = set(self.semantic_keywords.get(type1, []))
        keywords2 = set(self.semantic_keywords.get(type2, []))

        content1_keywords = set(word for word in content1.split() if word in keywords1)
        content2_keywords = set(word for word in content2.split() if word in keywords2)

        if not content1_keywords and not content2_keywords:
            return 0.0

        intersection = len(content1_keywords.intersection(content2_keywords))
        union = len(content1_keywords.union(content2_keywords))

        return intersection / max(1, union)

    def _identify_relationship_type(self, content1: str, content2: str, type1: str, type2: str) -> str:
        """识别关系类型"""
        # 依赖关系识别
        if any(dep_word in content1 for dep_word in ["依赖", "基于", "使用", "调用"]):
            if any(word in content2 for word in content1.split()):
                return "depends_on"

        # 冲突关系识别
        for pattern, conflict_type in self.contradiction_patterns:
            if re.search(pattern, content1 + " " + content2):
                return "conflicts_with"

        # 支持关系识别
        if type1 == "architecture_pattern" and type2 == "implementation_details":
            return "supports"

        # 实现关系识别
        if type1 == "interface_design" and type2 == "implementation_details":
            return "implements"

        return "related_to"

    def _calculate_relationship_strength(self, content1: str, content2: str, semantic_similarity: float, relationship_type: str) -> float:
        """计算关系强度"""
        base_strength = semantic_similarity

        # 根据关系类型调整强度
        type_multipliers = {
            "depends_on": 1.2,
            "conflicts_with": 1.5,
            "supports": 1.1,
            "implements": 1.3,
            "related_to": 1.0
        }

        multiplier = type_multipliers.get(relationship_type, 1.0)
        return min(1.0, base_strength * multiplier)

    def _analyze_field_logic_chains(self, target_field: FieldConfidence, all_fields: List[FieldConfidence]) -> Dict:
        """分析单个字段的逻辑链"""
        field_name = target_field.field_name

        # 获取相关的逻辑链
        incoming_chains = []
        outgoing_chains = []

        if field_name in self.relationship_graph:
            # 入边（依赖此字段的其他字段）
            for predecessor in self.relationship_graph.predecessors(field_name):
                edge_data = self.relationship_graph[predecessor][field_name]
                incoming_chains.append({
                    "source": predecessor,
                    "target": field_name,
                    "relationship_type": edge_data['relationship_type'],
                    "strength": edge_data['strength']
                })

            # 出边（此字段依赖的其他字段）
            for successor in self.relationship_graph.successors(field_name):
                edge_data = self.relationship_graph[field_name][successor]
                outgoing_chains.append({
                    "source": field_name,
                    "target": successor,
                    "relationship_type": edge_data['relationship_type'],
                    "strength": edge_data['strength']
                })

        # 检测逻辑矛盾
        contradictions = self._detect_logic_contradictions(target_field, incoming_chains, outgoing_chains)

        # 计算语义一致性得分
        semantic_score = self._calculate_field_semantic_consistency(target_field, incoming_chains, outgoing_chains)

        return {
            "field_name": field_name,
            "incoming_logic_chains": incoming_chains,
            "outgoing_logic_chains": outgoing_chains,
            "contradictions": contradictions,
            "semantic_consistency_score": semantic_score,
            "logic_chain_health": "HEALTHY" if not contradictions and semantic_score > 0.7 else "PROBLEMATIC"
        }

    def _detect_logic_contradictions(self, field: FieldConfidence, incoming: List[Dict], outgoing: List[Dict]) -> List[str]:
        """检测逻辑矛盾"""
        contradictions = []
        content = field.content.lower()

        # 检测内容内部矛盾
        for pattern, contradiction_type in self.contradiction_patterns:
            if re.search(pattern, content):
                contradictions.append(f"内容内部矛盾: {contradiction_type}")

        # 检测与相关字段的矛盾
        for chain in incoming + outgoing:
            if chain['relationship_type'] == 'conflicts_with' and chain['strength'] > 0.6:
                contradictions.append(f"与字段 {chain['source'] if chain['target'] == field.field_name else chain['target']} 存在冲突")

        # 检测循环依赖
        if self._has_circular_dependency(field.field_name):
            contradictions.append("存在循环依赖关系")

        return contradictions

    def _calculate_field_semantic_consistency(self, field: FieldConfidence, incoming: List[Dict], outgoing: List[Dict]) -> float:
        """计算字段的语义一致性得分"""
        if not incoming and not outgoing:
            return 0.8  # 独立字段默认较高一致性

        total_strength = 0.0
        total_chains = len(incoming) + len(outgoing)

        for chain in incoming + outgoing:
            if chain['relationship_type'] in ['supports', 'implements', 'depends_on']:
                total_strength += chain['strength']
            elif chain['relationship_type'] == 'conflicts_with':
                total_strength -= chain['strength'] * 0.5  # 冲突关系降低一致性

        return max(0.0, min(1.0, total_strength / max(1, total_chains)))

    def _has_circular_dependency(self, field_name: str) -> bool:
        """检测是否存在循环依赖"""
        try:
            cycles = list(nx.simple_cycles(self.relationship_graph))
            return any(field_name in cycle for cycle in cycles)
        except:
            return False

    def _analyze_global_logic_consistency(self, field_confidences: List[FieldConfidence]) -> Dict:
        """分析全局逻辑一致性"""
        total_fields = len(field_confidences)
        consistent_fields = sum(1 for fc in field_confidences
                              if fc.semantic_consistency_score > 0.7)

        global_consistency_score = consistent_fields / max(1, total_fields)

        # 检测全局矛盾模式
        global_contradictions = []
        architecture_fields = [fc for fc in field_confidences if fc.field_type == "architecture_pattern"]
        tech_fields = [fc for fc in field_confidences if fc.field_type == "technology_stack"]

        # 架构一致性检查
        if len(architecture_fields) > 1:
            arch_contents = [fc.content.lower() for fc in architecture_fields]
            if any("微内核" in content for content in arch_contents) and any("单体" in content for content in arch_contents):
                global_contradictions.append("架构模式不一致：同时存在微内核和单体架构")

        return {
            "global_consistency_score": global_consistency_score,
            "consistent_fields_count": consistent_fields,
            "total_fields_count": total_fields,
            "global_contradictions": global_contradictions,
            "relationship_graph_metrics": {
                "nodes_count": self.relationship_graph.number_of_nodes(),
                "edges_count": self.relationship_graph.number_of_edges(),
                "density": nx.density(self.relationship_graph) if self.relationship_graph.number_of_nodes() > 0 else 0
            }
        }

    def _identify_critical_contradictions(self, logic_results: Dict) -> List[Dict]:
        """识别关键矛盾"""
        critical_contradictions = []

        for field_name, result in logic_results.items():
            if result['contradictions']:
                for contradiction in result['contradictions']:
                    if "冲突" in contradiction or "矛盾" in contradiction or "循环依赖" in contradiction:
                        critical_contradictions.append({
                            "field_name": field_name,
                            "contradiction": contradiction,
                            "severity": "HIGH" if "循环依赖" in contradiction else "MEDIUM"
                        })

        return critical_contradictions

    def _calculate_semantic_coherence(self, field_confidences: List[FieldConfidence]) -> float:
        """计算整体语义连贯性"""
        if not field_confidences:
            return 0.0

        total_score = sum(fc.semantic_consistency_score for fc in field_confidences)
        return total_score / len(field_confidences)

    def _generate_logic_optimization_suggestions(self, logic_results: Dict) -> List[str]:
        """生成逻辑优化建议"""
        suggestions = []

        for field_name, result in logic_results.items():
            if result['semantic_consistency_score'] < 0.6:
                suggestions.append(f"{field_name}: 语义一致性较低，建议重新审查内容表述")

            if result['contradictions']:
                suggestions.append(f"{field_name}: 存在逻辑矛盾，需要解决: {', '.join(result['contradictions'])}")

            if len(result['incoming_logic_chains']) == 0 and len(result['outgoing_logic_chains']) == 0:
                suggestions.append(f"{field_name}: 缺乏与其他字段的逻辑关联，可能需要增强关系定义")

        return suggestions

class V4PanoramicValidator:
    """V4算法全景验证器 - 基于V4掌握的全景知识库进行验证"""

    def __init__(self):
        # 模拟V4算法的知识库（实际应用中这将是V4算法的真实知识库接口）
        self.v4_knowledge_base = self._initialize_v4_knowledge_base()
        self.panoramic_completeness_threshold = 0.85
        self.knowledge_alignment_threshold = 0.80

    def _initialize_v4_knowledge_base(self) -> Dict[str, List[V4KnowledgeReference]]:
        """初始化V4知识库（模拟）"""
        return {
            "architecture_patterns": [
                V4KnowledgeReference(
                    knowledge_id="arch_001",
                    knowledge_category="architecture_pattern",
                    knowledge_content="微内核架构模式：核心系统 + 插件系统，支持动态扩展",
                    confidence_score=0.95,
                    last_updated="2025-06-15",
                    validation_status="validated"
                ),
                V4KnowledgeReference(
                    knowledge_id="arch_002",
                    knowledge_category="architecture_pattern",
                    knowledge_content="服务总线模式：组件间通过事件总线进行解耦通信",
                    confidence_score=0.92,
                    last_updated="2025-06-15",
                    validation_status="validated"
                )
            ],
            "technology_stacks": [
                V4KnowledgeReference(
                    knowledge_id="tech_001",
                    knowledge_category="technology_stack",
                    knowledge_content="Spring Boot 3.4.5+ 与 Java 21 的最佳实践组合",
                    confidence_score=0.98,
                    last_updated="2025-06-15",
                    validation_status="validated"
                )
            ],
            "best_practices": [
                V4KnowledgeReference(
                    knowledge_id="bp_001",
                    knowledge_category="best_practice",
                    knowledge_content="插件生命周期管理：初始化 -> 激活 -> 运行 -> 停止 -> 销毁",
                    confidence_score=0.90,
                    last_updated="2025-06-15",
                    validation_status="validated"
                )
            ]
        }

    def validate_panoramic_consistency(self, field_confidences: List[FieldConfidence]) -> Dict:
        """验证与V4全景知识的一致性"""

        panoramic_results = {}
        for field_conf in field_confidences:
            result = self._validate_field_against_v4_knowledge(field_conf)
            panoramic_results[field_conf.field_name] = result

            # 更新字段的V4验证结果
            field_conf.v4_panoramic_validation = result
            field_conf.v4_knowledge_alignment_score = result['knowledge_alignment_score']
            field_conf.v4_completeness_score = result['completeness_score']
            field_conf.v4_consistency_violations = result['consistency_violations']

        # 全景完整性分析
        panoramic_completeness = self._analyze_panoramic_completeness(field_confidences)

        # V4知识库覆盖度分析
        knowledge_coverage = self._analyze_knowledge_coverage(field_confidences)

        return {
            "validation_type": "V4_PANORAMIC_VALIDATION",
            "individual_field_results": panoramic_results,
            "panoramic_completeness_analysis": panoramic_completeness,
            "v4_knowledge_coverage_analysis": knowledge_coverage,
            "critical_consistency_violations": self._identify_critical_violations(panoramic_results),
            "panoramic_optimization_suggestions": self._generate_panoramic_optimization_suggestions(panoramic_results),
            "v4_confidence_calibration": self._calibrate_v4_confidence(field_confidences)
        }

    def _validate_field_against_v4_knowledge(self, field_conf: FieldConfidence) -> Dict:
        """验证单个字段与V4知识库的一致性"""
        field_content = field_conf.content.lower()
        field_type = field_conf.field_type

        # 查找相关的V4知识
        relevant_knowledge = self._find_relevant_v4_knowledge(field_content, field_type)

        # 计算知识对齐度
        alignment_score = self._calculate_knowledge_alignment(field_content, relevant_knowledge)

        # 计算完整性得分
        completeness_score = self._calculate_field_completeness_against_v4(field_content, field_type, relevant_knowledge)

        # 检测一致性违规
        consistency_violations = self._detect_v4_consistency_violations(field_content, field_type, relevant_knowledge)

        # 生成V4建议
        v4_suggestions = self._generate_v4_suggestions(field_conf, relevant_knowledge, alignment_score, completeness_score)

        return {
            "field_name": field_conf.field_name,
            "relevant_v4_knowledge": [kr.knowledge_id for kr in relevant_knowledge],
            "knowledge_alignment_score": alignment_score,
            "completeness_score": completeness_score,
            "consistency_violations": consistency_violations,
            "v4_suggestions": v4_suggestions,
            "validation_confidence": min(alignment_score, completeness_score),
            "v4_knowledge_gaps": self._identify_knowledge_gaps(field_content, field_type, relevant_knowledge)
        }

    def _find_relevant_v4_knowledge(self, content: str, field_type: str) -> List[V4KnowledgeReference]:
        """查找相关的V4知识"""
        relevant_knowledge = []

        # 根据字段类型查找对应的知识类别
        knowledge_categories = {
            "architecture_pattern": ["architecture_patterns", "best_practices"],
            "technology_stack": ["technology_stacks", "best_practices"],
            "interface_design": ["best_practices"],
            "performance_metrics": ["best_practices"],
            "implementation_details": ["best_practices"],
            "business_logic": ["best_practices"]
        }

        categories = knowledge_categories.get(field_type, ["best_practices"])

        for category in categories:
            if category in self.v4_knowledge_base:
                for knowledge_ref in self.v4_knowledge_base[category]:
                    # 计算内容相似度
                    similarity = self._calculate_content_similarity(content, knowledge_ref.knowledge_content.lower())
                    if similarity > 0.3:  # 相似度阈值
                        relevant_knowledge.append(knowledge_ref)

        # 按置信度排序
        relevant_knowledge.sort(key=lambda x: x.confidence_score, reverse=True)
        return relevant_knowledge[:5]  # 返回最相关的5个知识点

    def _calculate_content_similarity(self, content1: str, content2: str) -> float:
        """计算内容相似度"""
        words1 = set(content1.split())
        words2 = set(content2.split())

        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        return intersection / max(1, union)

    def _calculate_knowledge_alignment(self, content: str, relevant_knowledge: List[V4KnowledgeReference]) -> float:
        """计算与V4知识的对齐度"""
        if not relevant_knowledge:
            return 0.5  # 无相关知识时的默认对齐度

        total_alignment = 0.0
        total_weight = 0.0

        for knowledge_ref in relevant_knowledge:
            similarity = self._calculate_content_similarity(content, knowledge_ref.knowledge_content.lower())
            weight = knowledge_ref.confidence_score
            total_alignment += similarity * weight
            total_weight += weight

        return total_alignment / max(0.1, total_weight)

    def _calculate_field_completeness_against_v4(self, content: str, field_type: str, relevant_knowledge: List[V4KnowledgeReference]) -> float:
        """计算字段相对于V4知识的完整性"""
        if not relevant_knowledge:
            return 0.6  # 无相关知识时的默认完整性

        # 基于V4知识检查关键要素的覆盖度
        key_elements = self._extract_key_elements_from_v4_knowledge(relevant_knowledge)
        covered_elements = sum(1 for element in key_elements if element.lower() in content)

        completeness = covered_elements / max(1, len(key_elements))
        return min(1.0, completeness)

    def _extract_key_elements_from_v4_knowledge(self, knowledge_refs: List[V4KnowledgeReference]) -> List[str]:
        """从V4知识中提取关键要素"""
        key_elements = []
        for knowledge_ref in knowledge_refs:
            # 简单的关键词提取（实际应用中可能使用更复杂的NLP技术）
            content = knowledge_ref.knowledge_content
            elements = [word for word in content.split() if len(word) > 3 and word not in ['的', '和', '与', '或', '但是', '然而']]
            key_elements.extend(elements[:3])  # 取前3个关键词

        return list(set(key_elements))  # 去重

    def _detect_v4_consistency_violations(self, content: str, field_type: str, relevant_knowledge: List[V4KnowledgeReference]) -> List[str]:
        """检测与V4知识的一致性违规"""
        violations = []

        for knowledge_ref in relevant_knowledge:
            if knowledge_ref.validation_status == "deprecated":
                violations.append(f"使用了已废弃的知识: {knowledge_ref.knowledge_id}")

            # 检测内容冲突
            if self._detect_content_conflict(content, knowledge_ref.knowledge_content):
                violations.append(f"与V4知识 {knowledge_ref.knowledge_id} 存在内容冲突")

        # 检测版本一致性
        if field_type == "technology_stack":
            version_violations = self._check_version_consistency(content)
            violations.extend(version_violations)

        return violations

    def _detect_content_conflict(self, content: str, v4_content: str) -> bool:
        """检测内容冲突"""
        # 简单的冲突检测逻辑
        conflict_patterns = [
            ("同步", "异步"),
            ("单例", "多例"),
            ("有状态", "无状态"),
            ("集中式", "分布式")
        ]

        for pattern1, pattern2 in conflict_patterns:
            if pattern1 in content and pattern2 in v4_content:
                return True
            if pattern2 in content and pattern1 in v4_content:
                return True

        return False

    def _check_version_consistency(self, content: str) -> List[str]:
        """检查版本一致性"""
        violations = []

        # 检查Spring Boot版本
        if "spring boot" in content.lower():
            if "3.4.5" not in content and "3.4" not in content:
                violations.append("Spring Boot版本可能不是推荐的3.4.5+版本")

        # 检查Java版本
        if "java" in content.lower():
            if "21" not in content and "17" not in content:
                violations.append("Java版本可能不是推荐的LTS版本")

        return violations

    def _generate_v4_suggestions(self, field_conf: FieldConfidence, relevant_knowledge: List[V4KnowledgeReference],
                                alignment_score: float, completeness_score: float) -> List[str]:
        """生成V4建议"""
        suggestions = []

        if alignment_score < self.knowledge_alignment_threshold:
            suggestions.append(f"知识对齐度较低({alignment_score:.2f})，建议参考V4最佳实践")
            if relevant_knowledge:
                best_knowledge = max(relevant_knowledge, key=lambda x: x.confidence_score)
                suggestions.append(f"推荐参考: {best_knowledge.knowledge_content}")

        if completeness_score < self.panoramic_completeness_threshold:
            suggestions.append(f"完整性不足({completeness_score:.2f})，建议补充关键要素")
            missing_elements = self._identify_missing_elements(field_conf.content, relevant_knowledge)
            if missing_elements:
                suggestions.append(f"缺失要素: {', '.join(missing_elements)}")

        return suggestions

    def _identify_missing_elements(self, content: str, relevant_knowledge: List[V4KnowledgeReference]) -> List[str]:
        """识别缺失的要素"""
        all_elements = self._extract_key_elements_from_v4_knowledge(relevant_knowledge)
        present_elements = [elem for elem in all_elements if elem.lower() in content.lower()]
        missing_elements = [elem for elem in all_elements if elem not in present_elements]
        return missing_elements[:3]  # 返回前3个缺失要素

    def _identify_knowledge_gaps(self, content: str, field_type: str, relevant_knowledge: List[V4KnowledgeReference]) -> List[str]:
        """识别知识缺口"""
        gaps = []

        if not relevant_knowledge:
            gaps.append(f"V4知识库中缺少 {field_type} 类型的相关知识")

        if len(relevant_knowledge) < 2:
            gaps.append(f"{field_type} 类型的V4知识覆盖度不足")

        # 检查知识时效性
        outdated_knowledge = [kr for kr in relevant_knowledge
                            if kr.validation_status == "pending" or kr.validation_status == "deprecated"]
        if outdated_knowledge:
            gaps.append(f"存在过时的V4知识: {[kr.knowledge_id for kr in outdated_knowledge]}")

        return gaps

    def _analyze_panoramic_completeness(self, field_confidences: List[FieldConfidence]) -> Dict:
        """分析全景完整性"""
        total_fields = len(field_confidences)
        complete_fields = sum(1 for fc in field_confidences
                            if fc.v4_completeness_score >= self.panoramic_completeness_threshold)

        completeness_ratio = complete_fields / max(1, total_fields)

        # 分析覆盖的知识领域
        covered_categories = set()
        for fc in field_confidences:
            if fc.v4_panoramic_validation and fc.v4_panoramic_validation.get('relevant_v4_knowledge'):
                for knowledge_id in fc.v4_panoramic_validation['relevant_v4_knowledge']:
                    # 从知识ID推断类别
                    if knowledge_id.startswith('arch_'):
                        covered_categories.add('architecture_patterns')
                    elif knowledge_id.startswith('tech_'):
                        covered_categories.add('technology_stacks')
                    elif knowledge_id.startswith('bp_'):
                        covered_categories.add('best_practices')

        total_categories = len(self.v4_knowledge_base)
        category_coverage = len(covered_categories) / max(1, total_categories)

        return {
            "overall_completeness_ratio": completeness_ratio,
            "complete_fields_count": complete_fields,
            "total_fields_count": total_fields,
            "knowledge_category_coverage": category_coverage,
            "covered_categories": list(covered_categories),
            "panoramic_health_status": "EXCELLENT" if completeness_ratio > 0.9 else
                                     "GOOD" if completeness_ratio > 0.7 else
                                     "NEEDS_IMPROVEMENT"
        }

    def _analyze_knowledge_coverage(self, field_confidences: List[FieldConfidence]) -> Dict:
        """分析V4知识库覆盖度"""
        used_knowledge_ids = set()
        for fc in field_confidences:
            if fc.v4_panoramic_validation and fc.v4_panoramic_validation.get('relevant_v4_knowledge'):
                used_knowledge_ids.update(fc.v4_panoramic_validation['relevant_v4_knowledge'])

        total_knowledge_count = sum(len(knowledge_list) for knowledge_list in self.v4_knowledge_base.values())
        coverage_ratio = len(used_knowledge_ids) / max(1, total_knowledge_count)

        # 分析未使用的知识
        all_knowledge_ids = set()
        for knowledge_list in self.v4_knowledge_base.values():
            all_knowledge_ids.update(kr.knowledge_id for kr in knowledge_list)

        unused_knowledge_ids = all_knowledge_ids - used_knowledge_ids

        return {
            "knowledge_coverage_ratio": coverage_ratio,
            "used_knowledge_count": len(used_knowledge_ids),
            "total_knowledge_count": total_knowledge_count,
            "unused_knowledge_ids": list(unused_knowledge_ids),
            "coverage_assessment": "HIGH" if coverage_ratio > 0.8 else
                                  "MEDIUM" if coverage_ratio > 0.5 else
                                  "LOW"
        }

    def _identify_critical_violations(self, panoramic_results: Dict) -> List[Dict]:
        """识别关键违规"""
        critical_violations = []

        for field_name, result in panoramic_results.items():
            violations = result.get('consistency_violations', [])
            for violation in violations:
                if "冲突" in violation or "废弃" in violation:
                    critical_violations.append({
                        "field_name": field_name,
                        "violation": violation,
                        "severity": "HIGH" if "废弃" in violation else "MEDIUM"
                    })

        return critical_violations

    def _generate_panoramic_optimization_suggestions(self, panoramic_results: Dict) -> List[str]:
        """生成全景优化建议"""
        suggestions = []

        low_alignment_fields = []
        low_completeness_fields = []

        for field_name, result in panoramic_results.items():
            if result['knowledge_alignment_score'] < self.knowledge_alignment_threshold:
                low_alignment_fields.append(field_name)
            if result['completeness_score'] < self.panoramic_completeness_threshold:
                low_completeness_fields.append(field_name)

        if low_alignment_fields:
            suggestions.append(f"以下字段需要提升V4知识对齐度: {', '.join(low_alignment_fields)}")

        if low_completeness_fields:
            suggestions.append(f"以下字段需要补充完整性: {', '.join(low_completeness_fields)}")

        return suggestions

    def _calibrate_v4_confidence(self, field_confidences: List[FieldConfidence]) -> Dict:
        """校准V4置信度"""
        total_alignment = sum(fc.v4_knowledge_alignment_score for fc in field_confidences)
        total_completeness = sum(fc.v4_completeness_score for fc in field_confidences)
        field_count = len(field_confidences)

        avg_alignment = total_alignment / max(1, field_count)
        avg_completeness = total_completeness / max(1, field_count)

        # V4算法整体置信度校准
        v4_calibrated_confidence = (avg_alignment * 0.6 + avg_completeness * 0.4)

        return {
            "v4_calibrated_confidence": v4_calibrated_confidence,
            "average_knowledge_alignment": avg_alignment,
            "average_completeness": avg_completeness,
            "calibration_quality": "HIGH" if v4_calibrated_confidence > 0.85 else
                                  "MEDIUM" if v4_calibrated_confidence > 0.70 else
                                  "LOW"
        }

class EnhancedTripleConfidenceAnalyzer:
    """增强的三重置信度分析器 - 支持AI开关的灵活验证工作流"""

    def __init__(self, config: Optional[ValidationConfig] = None):
        # 验证配置
        self.config = config or ValidationConfig()

        # 从配置更新阈值
        self.contradiction_threshold = self.config.contradiction_threshold
        self.severe_contradiction_threshold = self.config.severe_contradiction_threshold

        # 初始化验证器（根据配置决定是否初始化）
        self.ide_ai_tracker = IDEAIConfidenceTracker() if self.config.enable_ide_ai_validation else None
        self.python_ai_validator = PythonAIRelationshipLogicValidator() if self.config.enable_python_ai_logic_validation else None
        self.v4_panoramic_validator = V4PanoramicValidator() if self.config.enable_v4_panoramic_validation else None

        # 根据验证模式动态调整权重
        self.validation_weights = self._calculate_dynamic_weights()

        # 性能监控
        self.performance_metrics = {} if self.config.enable_performance_metrics else None

    def _calculate_dynamic_weights(self) -> Dict[str, float]:
        """根据启用的验证器动态计算权重"""
        enabled_validators = []

        if self.config.enable_ide_ai_validation:
            enabled_validators.append("ide_ai")
        if self.config.enable_python_ai_logic_validation:
            enabled_validators.append("python_ai_logic")
        if self.config.enable_v4_panoramic_validation:
            enabled_validators.append("v4_panoramic")

        if not enabled_validators:
            return {"ide_ai": 1.0, "python_ai_logic": 0.0, "v4_panoramic": 0.0}

        # 根据验证模式分配权重
        if self.config.mode == ValidationMode.FAST_ITERATION:
            if self.config.fast_iteration_focus == "ide_ai_only":
                return {"ide_ai": 1.0, "python_ai_logic": 0.0, "v4_panoramic": 0.0}
            elif self.config.fast_iteration_focus == "algorithm_only":
                return {"ide_ai": 0.0, "python_ai_logic": 0.0, "v4_panoramic": 1.0}
            else:  # minimal
                return {"ide_ai": 0.7, "python_ai_logic": 0.0, "v4_panoramic": 0.3}

        elif self.config.mode == ValidationMode.ALGORITHM_ONLY:
            return {"ide_ai": 0.0, "python_ai_logic": 0.0, "v4_panoramic": 1.0}

        elif self.config.mode == ValidationMode.FULL_TRIPLE:
            return {"ide_ai": 0.3, "python_ai_logic": 0.35, "v4_panoramic": 0.35}

        else:  # CUSTOM mode
            # 平均分配给启用的验证器
            weight_per_validator = 1.0 / len(enabled_validators)
            weights = {"ide_ai": 0.0, "python_ai_logic": 0.0, "v4_panoramic": 0.0}
            for validator in enabled_validators:
                weights[validator] = weight_per_validator
            return weights
    
    def execute_adaptive_validation(self, field_confidences: List[FieldConfidence]) -> Dict:
        """执行自适应验证工作流（支持AI开关）"""

        import time
        start_time = time.time()

        if self.config.enable_detailed_logging:
            print(f"🔍 开始执行 {self.config.mode.value} 验证模式...")
            print(f"📋 验证配置: IDE AI={self.config.enable_ide_ai_validation}, "
                  f"Python AI={self.config.enable_python_ai_logic_validation}, "
                  f"V4算法={self.config.enable_v4_panoramic_validation}")

        # 初始化结果容器
        ide_ai_results = None
        python_ai_logic_results = None
        v4_panoramic_results = None

        # 第一层：IDE AI自评验证（如果启用）
        if self.config.enable_ide_ai_validation:
            if self.config.enable_detailed_logging:
                print("📝 第一层：IDE AI自评验证")
            ide_ai_results = self._execute_ide_ai_validation(field_confidences)
        else:
            if self.config.enable_detailed_logging:
                print("⏭️ 跳过IDE AI自评验证（已关闭）")
            ide_ai_results = self._create_empty_ide_ai_results(field_confidences)

        # 第二层：Python AI关系逻辑链验证（如果启用）
        if self.config.enable_python_ai_logic_validation and self.python_ai_validator:
            if self.config.enable_detailed_logging:
                print("🔗 第二层：Python AI关系逻辑链验证")
            python_ai_logic_results = self.python_ai_validator.validate_relationship_logic_chains(field_confidences)
        else:
            if self.config.enable_detailed_logging:
                print("⏭️ 跳过Python AI逻辑链验证（已关闭或快速迭代模式）")
            python_ai_logic_results = self._create_empty_python_ai_results(field_confidences)

        # 第三层：V4算法全景验证（如果启用）
        if self.config.enable_v4_panoramic_validation and self.v4_panoramic_validator:
            if self.config.enable_detailed_logging:
                print("🌐 第三层：V4算法全景验证")
            v4_panoramic_results = self.v4_panoramic_validator.validate_panoramic_consistency(field_confidences)
        else:
            if self.config.enable_detailed_logging:
                print("⏭️ 跳过V4全景验证（已关闭）")
            v4_panoramic_results = self._create_empty_v4_results(field_confidences)

        # 融合验证结果
        if self.config.enable_detailed_logging:
            print("⚖️ 融合验证结果")
        fusion_results = self._fuse_adaptive_validation_results(
            field_confidences, ide_ai_results, python_ai_logic_results, v4_panoramic_results
        )

        # 生成最终验证报告
        if self.config.enable_detailed_logging:
            print("📊 生成验证报告")
        final_report = self._generate_adaptive_validation_report(
            field_confidences, ide_ai_results, python_ai_logic_results, v4_panoramic_results, fusion_results
        )

        # 记录性能指标
        if self.config.enable_performance_metrics:
            execution_time = time.time() - start_time
            final_report["performance_metrics"] = {
                "execution_time_seconds": execution_time,
                "validation_mode": self.config.mode.value,
                "enabled_validators": [
                    name for name, enabled in [
                        ("ide_ai", self.config.enable_ide_ai_validation),
                        ("python_ai_logic", self.config.enable_python_ai_logic_validation),
                        ("v4_panoramic", self.config.enable_v4_panoramic_validation)
                    ] if enabled
                ]
            }

        if self.config.enable_detailed_logging:
            print(f"✅ {self.config.mode.value} 验证完成")

        return final_report

    def _execute_ide_ai_validation(self, field_confidences: List[FieldConfidence]) -> Dict:
        """执行IDE AI自评验证"""
        ide_results = {}

        for field_conf in field_confidences:
            # 重新评估IDE AI置信度（如果需要）
            if field_conf.ide_ai_confidence == 0:
                field_conf.ide_ai_confidence = self.ide_ai_tracker.assess_self_confidence(
                    field_conf.field_type, len(field_conf.content.split())
                )

            ide_results[field_conf.field_name] = {
                "confidence_score": field_conf.ide_ai_confidence,
                "field_type": field_conf.field_type,
                "content_length": len(field_conf.content),
                "assessment_quality": "HIGH" if field_conf.ide_ai_confidence > 0.85 else
                                    "MEDIUM" if field_conf.ide_ai_confidence > 0.70 else "LOW"
            }

        return {
            "validation_layer": "IDE_AI_SELF_ASSESSMENT",
            "individual_results": ide_results,
            "average_confidence": sum(fc.ide_ai_confidence for fc in field_confidences) / len(field_confidences),
            "high_confidence_fields": [fc.field_name for fc in field_confidences if fc.ide_ai_confidence > 0.85],
            "low_confidence_fields": [fc.field_name for fc in field_confidences if fc.ide_ai_confidence < 0.70]
        }

    def _create_empty_ide_ai_results(self, field_confidences: List[FieldConfidence]) -> Dict:
        """创建空的IDE AI验证结果"""
        return {
            "validation_layer": "IDE_AI_SELF_ASSESSMENT",
            "individual_results": {fc.field_name: {"confidence_score": 0.5, "assessment_quality": "DISABLED"}
                                 for fc in field_confidences},
            "average_confidence": 0.5,
            "high_confidence_fields": [],
            "low_confidence_fields": [fc.field_name for fc in field_confidences],
            "status": "DISABLED"
        }

    def _create_empty_python_ai_results(self, field_confidences: List[FieldConfidence]) -> Dict:
        """创建空的Python AI验证结果"""
        # 设置默认的空验证结果
        for fc in field_confidences:
            fc.relationship_logic_validation = {"status": "DISABLED"}
            fc.semantic_consistency_score = 0.5
            fc.logic_chain_contradictions = []

        return {
            "validation_type": "PYTHON_AI_RELATIONSHIP_LOGIC",
            "individual_field_results": {fc.field_name: {"status": "DISABLED"} for fc in field_confidences},
            "global_consistency_analysis": {"status": "DISABLED"},
            "critical_logic_contradictions": [],
            "semantic_coherence_score": 0.5,
            "logic_chain_optimization_suggestions": [],
            "status": "DISABLED"
        }

    def _create_empty_v4_results(self, field_confidences: List[FieldConfidence]) -> Dict:
        """创建空的V4验证结果"""
        # 设置默认的空验证结果
        for fc in field_confidences:
            fc.v4_panoramic_validation = {"status": "DISABLED"}
            fc.v4_knowledge_alignment_score = 0.5
            fc.v4_completeness_score = 0.5
            fc.v4_consistency_violations = []

        return {
            "validation_type": "V4_PANORAMIC_VALIDATION",
            "individual_field_results": {fc.field_name: {"status": "DISABLED"} for fc in field_confidences},
            "panoramic_completeness_analysis": {"status": "DISABLED"},
            "v4_knowledge_coverage_analysis": {"status": "DISABLED"},
            "critical_consistency_violations": [],
            "panoramic_optimization_suggestions": [],
            "v4_confidence_calibration": {"v4_calibrated_confidence": 0.5, "calibration_quality": "DISABLED"},
            "status": "DISABLED"
        }

    def _fuse_adaptive_validation_results(self, field_confidences: List[FieldConfidence],
                                        ide_ai_results: Dict, python_ai_results: Dict, v4_results: Dict) -> Dict:
        """自适应融合验证结果（支持部分验证器关闭）"""

        fusion_results = {}

        for field_conf in field_confidences:
            field_name = field_conf.field_name

            # 获取各层验证的置信度（如果验证器被关闭，使用默认值）
            ide_confidence = field_conf.ide_ai_confidence if self.config.enable_ide_ai_validation else 0.5
            python_logic_confidence = field_conf.semantic_consistency_score if self.config.enable_python_ai_logic_validation else 0.5
            v4_confidence = field_conf.v4_knowledge_alignment_score if self.config.enable_v4_panoramic_validation else 0.5

            # 计算自适应融合置信度
            fused_confidence = (
                ide_confidence * self.validation_weights["ide_ai"] +
                python_logic_confidence * self.validation_weights["python_ai_logic"] +
                v4_confidence * self.validation_weights["v4_panoramic"]
            )

            # 更新字段的最终置信度
            field_conf.final_confidence_score = fused_confidence

            # 分析验证一致性（只考虑启用的验证器）
            enabled_confidences = []
            if self.config.enable_ide_ai_validation:
                enabled_confidences.append(ide_confidence)
            if self.config.enable_python_ai_logic_validation:
                enabled_confidences.append(python_logic_confidence)
            if self.config.enable_v4_panoramic_validation:
                enabled_confidences.append(v4_confidence)

            confidence_variance = self._calculate_confidence_variance(enabled_confidences) if enabled_confidences else 0.0
            consistency_level = "HIGH" if confidence_variance < 0.1 else "MEDIUM" if confidence_variance < 0.2 else "LOW"

            # 收集启用验证器的问题
            all_issues = []
            if self.config.enable_python_ai_logic_validation:
                all_issues.extend(field_conf.logic_chain_contradictions)
            if self.config.enable_v4_panoramic_validation:
                all_issues.extend(field_conf.v4_consistency_violations)

            # 生成自适应建议
            adaptive_suggestions = self._generate_adaptive_suggestions(
                field_conf, ide_confidence, python_logic_confidence, v4_confidence, all_issues
            )

            fusion_results[field_name] = {
                "fused_confidence": fused_confidence,
                "confidence_variance": confidence_variance,
                "consistency_level": consistency_level,
                "individual_confidences": {
                    "ide_ai": ide_confidence,
                    "python_logic": python_logic_confidence,
                    "v4_panoramic": v4_confidence
                },
                "enabled_validators": [
                    name for name, enabled in [
                        ("ide_ai", self.config.enable_ide_ai_validation),
                        ("python_ai_logic", self.config.enable_python_ai_logic_validation),
                        ("v4_panoramic", self.config.enable_v4_panoramic_validation)
                    ] if enabled
                ],
                "all_issues": all_issues,
                "adaptive_suggestions": adaptive_suggestions,
                "validation_status": self._determine_validation_status(fused_confidence, all_issues)
            }

        return {
            "fusion_method": f"ADAPTIVE_{self.config.mode.value}",
            "fusion_weights": self.validation_weights,
            "individual_field_fusions": fusion_results,
            "overall_fused_confidence": sum(result["fused_confidence"] for result in fusion_results.values()) / len(fusion_results),
            "consistency_distribution": self._analyze_consistency_distribution(fusion_results),
            "validation_mode": self.config.mode.value
        }

    def _generate_adaptive_suggestions(self, field_conf: FieldConfidence,
                                     ide_conf: float, logic_conf: float, v4_conf: float, issues: List[str]) -> List[str]:
        """生成自适应建议（根据启用的验证器）"""
        suggestions = []

        # 根据验证模式生成不同的建议
        if self.config.mode == ValidationMode.FAST_ITERATION:
            if self.config.fast_iteration_focus == "ide_ai_only":
                if ide_conf < 0.7:
                    suggestions.append("快速迭代模式：IDE AI置信度较低，建议重新审查内容")
                suggestions.append("快速迭代模式：Python AI验证已关闭，适合快速修改阶段")
            elif self.config.fast_iteration_focus == "algorithm_only":
                if v4_conf < 0.7:
                    suggestions.append("算法验证模式：V4算法置信度较低，建议检查与知识库的一致性")
                suggestions.append("算法验证模式：AI验证已关闭，专注算法验证")

        elif self.config.mode == ValidationMode.ALGORITHM_ONLY:
            if v4_conf < 0.7:
                suggestions.append("仅算法验证：置信度较低，建议参考V4知识库最佳实践")
            suggestions.append("仅算法验证模式：AI验证已关闭，专注V4算法验证")

        else:  # FULL_TRIPLE or CUSTOM
            # 基于启用的验证器生成建议
            if self.config.enable_ide_ai_validation and ide_conf < 0.7:
                suggestions.append("IDE AI置信度较低，建议重新审查内容表述")

            if self.config.enable_python_ai_logic_validation and logic_conf < 0.7:
                suggestions.append("逻辑链一致性较低，建议检查内容逻辑关系")

            if self.config.enable_v4_panoramic_validation and v4_conf < 0.7:
                suggestions.append("V4知识对齐度较低，建议参考V4最佳实践")

        # 基于具体问题的建议
        if issues and (self.config.enable_python_ai_logic_validation or self.config.enable_v4_panoramic_validation):
            suggestions.append(f"发现 {len(issues)} 个具体问题，需要逐一解决")

        return suggestions

    def _generate_adaptive_validation_report(self, field_confidences: List[FieldConfidence],
                                           ide_ai_results: Dict, python_ai_results: Dict,
                                           v4_results: Dict, fusion_results: Dict) -> Dict:
        """生成自适应验证报告"""

        # 计算整体指标
        total_fields = len(field_confidences)
        excellent_fields = sum(1 for fc in field_confidences if fc.final_confidence_score >= 0.9)
        good_fields = sum(1 for fc in field_confidences if 0.8 <= fc.final_confidence_score < 0.9)
        acceptable_fields = sum(1 for fc in field_confidences if 0.7 <= fc.final_confidence_score < 0.8)
        problematic_fields = sum(1 for fc in field_confidences if fc.final_confidence_score < 0.7)

        # 计算目标达成度（根据验证模式调整）
        target_achievement = (excellent_fields + good_fields) / total_fields

        # 根据验证模式调整目标
        if self.config.mode == ValidationMode.FAST_ITERATION:
            # 快速迭代模式目标相对宽松
            target_933_achievement = min(1.0, target_achievement / 0.80)
        elif self.config.mode == ValidationMode.ALGORITHM_ONLY:
            # 算法验证模式目标适中
            target_933_achievement = min(1.0, target_achievement / 0.85)
        else:
            # 完整验证模式保持93.3%目标
            target_933_achievement = min(1.0, target_achievement / 0.933)

        # 收集启用验证器的关键问题
        all_critical_issues = []
        if self.config.enable_python_ai_logic_validation:
            all_critical_issues.extend(python_ai_results.get('critical_logic_contradictions', []))
        if self.config.enable_v4_panoramic_validation:
            all_critical_issues.extend(v4_results.get('critical_consistency_violations', []))

        # 生成模式特定的优化建议
        mode_specific_recommendations = self._generate_mode_specific_recommendations()

        return {
            "validation_summary": {
                "validation_timestamp": "2025-06-16T12:00:00Z",
                "validation_version": "v4.0-adaptive-ai-switch",
                "validation_mode": self.config.mode.value,
                "enabled_validators": [
                    name for name, enabled in [
                        ("ide_ai", self.config.enable_ide_ai_validation),
                        ("python_ai_logic", self.config.enable_python_ai_logic_validation),
                        ("v4_panoramic", self.config.enable_v4_panoramic_validation)
                    ] if enabled
                ],
                "total_fields_validated": total_fields,
                "target_achievement_rate": target_933_achievement
            },

            "confidence_distribution": {
                "excellent_fields": excellent_fields,
                "good_fields": good_fields,
                "acceptable_fields": acceptable_fields,
                "problematic_fields": problematic_fields,
                "overall_confidence_score": sum(fc.final_confidence_score for fc in field_confidences) / total_fields
            },

            "validation_layer_results": {
                "layer_1_ide_ai": ide_ai_results,
                "layer_2_python_logic": python_ai_results,
                "layer_3_v4_panoramic": v4_results
            },

            "fusion_analysis": fusion_results,

            "critical_issues_summary": {
                "total_critical_issues": len(all_critical_issues),
                "critical_issues_details": all_critical_issues,
                "severity_distribution": self._analyze_severity_distribution(all_critical_issues)
            },

            "mode_specific_analysis": {
                "validation_mode": self.config.mode.value,
                "mode_effectiveness": self._assess_mode_effectiveness(field_confidences),
                "mode_recommendations": mode_specific_recommendations,
                "switch_suggestions": self._generate_mode_switch_suggestions(field_confidences)
            },

            "recommendations": {
                "immediate_actions": self._generate_immediate_actions(field_confidences, all_critical_issues),
                "mode_specific_improvements": mode_specific_recommendations,
                "validation_mode_optimization": self._suggest_validation_mode_optimization(field_confidences)
            }
        }

    def _generate_mode_specific_recommendations(self) -> List[str]:
        """生成模式特定的建议"""
        recommendations = []

        if self.config.mode == ValidationMode.FAST_ITERATION:
            recommendations.append("快速迭代模式：适合IDE AI快速修改阶段，Python AI验证已优化关闭")
            recommendations.append("建议：完成快速迭代后，切换到完整验证模式进行最终质量检查")

        elif self.config.mode == ValidationMode.ALGORITHM_ONLY:
            recommendations.append("仅算法验证模式：专注V4算法验证，AI验证已关闭")
            recommendations.append("建议：算法验证完成后，可启用AI验证进行全面质量检查")

        elif self.config.mode == ValidationMode.FULL_TRIPLE:
            recommendations.append("完整三重验证模式：提供最全面的质量保障")
            recommendations.append("建议：如需快速迭代，可临时切换到快速模式")

        return recommendations

    def _assess_mode_effectiveness(self, field_confidences: List[FieldConfidence]) -> str:
        """评估验证模式有效性"""
        avg_confidence = sum(fc.final_confidence_score for fc in field_confidences) / len(field_confidences)

        if self.config.mode == ValidationMode.FAST_ITERATION:
            return "HIGH" if avg_confidence > 0.75 else "MEDIUM"
        elif self.config.mode == ValidationMode.ALGORITHM_ONLY:
            return "HIGH" if avg_confidence > 0.80 else "MEDIUM"
        else:  # FULL_TRIPLE
            return "HIGH" if avg_confidence > 0.85 else "MEDIUM"

    def _generate_mode_switch_suggestions(self, field_confidences: List[FieldConfidence]) -> List[str]:
        """生成模式切换建议"""
        suggestions = []
        avg_confidence = sum(fc.final_confidence_score for fc in field_confidences) / len(field_confidences)

        if self.config.mode == ValidationMode.FAST_ITERATION:
            if avg_confidence > 0.85:
                suggestions.append("快速迭代效果良好，可考虑切换到完整验证模式进行最终确认")
            elif avg_confidence < 0.65:
                suggestions.append("快速迭代效果不佳，建议启用Python AI验证进行深度分析")

        elif self.config.mode == ValidationMode.ALGORITHM_ONLY:
            if avg_confidence < 0.70:
                suggestions.append("算法验证发现问题，建议启用AI验证进行全面分析")

        return suggestions

    def _suggest_validation_mode_optimization(self, field_confidences: List[FieldConfidence]) -> List[str]:
        """建议验证模式优化"""
        suggestions = []

        problematic_fields = [fc for fc in field_confidences if fc.final_confidence_score < 0.7]

        if len(problematic_fields) > len(field_confidences) * 0.3:
            suggestions.append("问题字段较多，建议使用完整三重验证模式")
        elif len(problematic_fields) == 0:
            suggestions.append("质量良好，可使用快速迭代模式进行后续修改")
        else:
            suggestions.append("质量适中，建议根据开发阶段选择合适的验证模式")

        return suggestions

    def _fuse_triple_validation_results(self, field_confidences: List[FieldConfidence],
                                       ide_ai_results: Dict, python_ai_results: Dict, v4_results: Dict) -> Dict:
        """融合三重验证结果"""

        fusion_results = {}

        for field_conf in field_confidences:
            field_name = field_conf.field_name

            # 获取三层验证的置信度
            ide_confidence = field_conf.ide_ai_confidence
            python_logic_confidence = field_conf.semantic_consistency_score
            v4_confidence = field_conf.v4_knowledge_alignment_score

            # 计算融合置信度
            fused_confidence = (
                ide_confidence * self.validation_weights["ide_ai"] +
                python_logic_confidence * self.validation_weights["python_ai_logic"] +
                v4_confidence * self.validation_weights["v4_panoramic"]
            )

            # 更新字段的最终置信度
            field_conf.final_confidence_score = fused_confidence

            # 分析验证一致性
            confidence_variance = self._calculate_confidence_variance([ide_confidence, python_logic_confidence, v4_confidence])
            consistency_level = "HIGH" if confidence_variance < 0.1 else "MEDIUM" if confidence_variance < 0.2 else "LOW"

            # 收集所有问题
            all_issues = []
            all_issues.extend(field_conf.logic_chain_contradictions)
            all_issues.extend(field_conf.v4_consistency_violations)

            # 生成综合建议
            comprehensive_suggestions = self._generate_comprehensive_suggestions(
                field_conf, ide_confidence, python_logic_confidence, v4_confidence, all_issues
            )

            fusion_results[field_name] = {
                "fused_confidence": fused_confidence,
                "confidence_variance": confidence_variance,
                "consistency_level": consistency_level,
                "individual_confidences": {
                    "ide_ai": ide_confidence,
                    "python_logic": python_logic_confidence,
                    "v4_panoramic": v4_confidence
                },
                "all_issues": all_issues,
                "comprehensive_suggestions": comprehensive_suggestions,
                "validation_status": self._determine_validation_status(fused_confidence, all_issues)
            }

        return {
            "fusion_method": "WEIGHTED_AVERAGE",
            "fusion_weights": self.validation_weights,
            "individual_field_fusions": fusion_results,
            "overall_fused_confidence": sum(result["fused_confidence"] for result in fusion_results.values()) / len(fusion_results),
            "consistency_distribution": self._analyze_consistency_distribution(fusion_results)
        }

    def _calculate_confidence_variance(self, confidences: List[float]) -> float:
        """计算置信度方差"""
        if not confidences:
            return 0.0

        mean = sum(confidences) / len(confidences)
        variance = sum((c - mean) ** 2 for c in confidences) / len(confidences)
        return math.sqrt(variance)

    def _generate_comprehensive_suggestions(self, field_conf: FieldConfidence,
                                          ide_conf: float, logic_conf: float, v4_conf: float, issues: List[str]) -> List[str]:
        """生成综合建议"""
        suggestions = []

        # 基于置信度差异的建议
        if abs(ide_conf - logic_conf) > 0.2:
            suggestions.append("IDE AI自评与逻辑链验证存在较大差异，需要重新审查内容逻辑")

        if abs(ide_conf - v4_conf) > 0.2:
            suggestions.append("IDE AI自评与V4知识库验证存在差异，建议参考V4最佳实践")

        if abs(logic_conf - v4_conf) > 0.2:
            suggestions.append("逻辑链验证与V4验证存在差异，需要检查知识一致性")

        # 基于具体问题的建议
        if issues:
            suggestions.append(f"发现 {len(issues)} 个具体问题，需要逐一解决")

        # 基于整体置信度的建议
        overall_conf = (ide_conf + logic_conf + v4_conf) / 3
        if overall_conf < 0.7:
            suggestions.append("整体置信度较低，建议全面重新审查和优化")
        elif overall_conf < 0.85:
            suggestions.append("置信度中等，建议针对性改进")

        return suggestions

    def _determine_validation_status(self, fused_confidence: float, issues: List[str]) -> str:
        """确定验证状态"""
        if fused_confidence >= 0.9 and not issues:
            return "EXCELLENT"
        elif fused_confidence >= 0.8 and len(issues) <= 1:
            return "GOOD"
        elif fused_confidence >= 0.7 and len(issues) <= 3:
            return "ACCEPTABLE"
        elif fused_confidence >= 0.6:
            return "NEEDS_IMPROVEMENT"
        else:
            return "CRITICAL"

    def _analyze_consistency_distribution(self, fusion_results: Dict) -> Dict:
        """分析一致性分布"""
        consistency_levels = [result["consistency_level"] for result in fusion_results.values()]

        return {
            "high_consistency_count": consistency_levels.count("HIGH"),
            "medium_consistency_count": consistency_levels.count("MEDIUM"),
            "low_consistency_count": consistency_levels.count("LOW"),
            "overall_consistency_health": "GOOD" if consistency_levels.count("HIGH") > len(consistency_levels) * 0.7 else "MODERATE"
        }

    def _generate_final_validation_report(self, field_confidences: List[FieldConfidence],
                                         ide_ai_results: Dict, python_ai_results: Dict,
                                         v4_results: Dict, fusion_results: Dict) -> Dict:
        """生成最终验证报告"""

        # 计算整体指标
        total_fields = len(field_confidences)
        excellent_fields = sum(1 for fc in field_confidences if fc.final_confidence_score >= 0.9)
        good_fields = sum(1 for fc in field_confidences if 0.8 <= fc.final_confidence_score < 0.9)
        acceptable_fields = sum(1 for fc in field_confidences if 0.7 <= fc.final_confidence_score < 0.8)
        problematic_fields = sum(1 for fc in field_confidences if fc.final_confidence_score < 0.7)

        # 计算93.3%目标达成度
        target_achievement = (excellent_fields + good_fields) / total_fields
        target_933_achievement = min(1.0, target_achievement / 0.933)

        # 收集所有关键问题
        all_critical_issues = []
        all_critical_issues.extend(python_ai_results.get('critical_logic_contradictions', []))
        all_critical_issues.extend(v4_results.get('critical_consistency_violations', []))

        # 生成优化路径
        optimization_roadmap = self._generate_optimization_roadmap(field_confidences, fusion_results)

        # 生成验证质量评估
        validation_quality = self._assess_validation_quality(ide_ai_results, python_ai_results, v4_results)

        return {
            "validation_summary": {
                "validation_timestamp": "2025-06-16T12:00:00Z",
                "validation_version": "v4.0-complete-triple",
                "total_fields_validated": total_fields,
                "validation_method": "COMPLETE_TRIPLE_VALIDATION",
                "target_933_achievement_rate": target_933_achievement
            },

            "confidence_distribution": {
                "excellent_fields": excellent_fields,
                "good_fields": good_fields,
                "acceptable_fields": acceptable_fields,
                "problematic_fields": problematic_fields,
                "overall_confidence_score": sum(fc.final_confidence_score for fc in field_confidences) / total_fields
            },

            "validation_layer_results": {
                "layer_1_ide_ai": ide_ai_results,
                "layer_2_python_logic": python_ai_results,
                "layer_3_v4_panoramic": v4_results
            },

            "fusion_analysis": fusion_results,

            "critical_issues_summary": {
                "total_critical_issues": len(all_critical_issues),
                "critical_issues_details": all_critical_issues,
                "severity_distribution": self._analyze_severity_distribution(all_critical_issues)
            },

            "optimization_roadmap": optimization_roadmap,

            "validation_quality_assessment": validation_quality,

            "recommendations": {
                "immediate_actions": self._generate_immediate_actions(field_confidences, all_critical_issues),
                "medium_term_improvements": self._generate_medium_term_improvements(fusion_results),
                "long_term_strategy": self._generate_long_term_strategy(target_933_achievement)
            },

            "next_validation_cycle": {
                "recommended_interval": "7_days",
                "focus_areas": self._identify_next_cycle_focus_areas(field_confidences),
                "expected_improvements": self._predict_next_cycle_improvements(field_confidences)
            }
        }

    def _generate_optimization_roadmap(self, field_confidences: List[FieldConfidence], fusion_results: Dict) -> Dict:
        """生成优化路径图"""

        # 按优先级分组字段
        critical_fields = [fc for fc in field_confidences if fc.final_confidence_score < 0.6]
        improvement_fields = [fc for fc in field_confidences if 0.6 <= fc.final_confidence_score < 0.8]
        optimization_fields = [fc for fc in field_confidences if 0.8 <= fc.final_confidence_score < 0.9]

        return {
            "phase_1_critical_fixes": {
                "fields": [fc.field_name for fc in critical_fields],
                "estimated_effort": "HIGH",
                "expected_confidence_gain": 0.3,
                "timeline": "1-2_weeks"
            },
            "phase_2_improvements": {
                "fields": [fc.field_name for fc in improvement_fields],
                "estimated_effort": "MEDIUM",
                "expected_confidence_gain": 0.15,
                "timeline": "2-3_weeks"
            },
            "phase_3_optimization": {
                "fields": [fc.field_name for fc in optimization_fields],
                "estimated_effort": "LOW",
                "expected_confidence_gain": 0.08,
                "timeline": "1_week"
            }
        }

    def _assess_validation_quality(self, ide_ai_results: Dict, python_ai_results: Dict, v4_results: Dict) -> Dict:
        """评估验证质量"""

        # 评估各层验证的有效性
        ide_ai_quality = "HIGH" if ide_ai_results.get('average_confidence', 0) > 0.8 else "MEDIUM"
        python_logic_quality = "HIGH" if python_ai_results.get('semantic_coherence_score', 0) > 0.8 else "MEDIUM"
        v4_panoramic_quality = v4_results.get('v4_confidence_calibration', {}).get('calibration_quality', 'MEDIUM')

        return {
            "ide_ai_validation_quality": ide_ai_quality,
            "python_logic_validation_quality": python_logic_quality,
            "v4_panoramic_validation_quality": v4_panoramic_quality,
            "overall_validation_reliability": "HIGH" if all(q == "HIGH" for q in [ide_ai_quality, python_logic_quality, v4_panoramic_quality]) else "MEDIUM",
            "validation_coverage_completeness": "COMPLETE",
            "validation_method_effectiveness": "SUPERIOR_TO_V3_1"
        }

    def _analyze_severity_distribution(self, critical_issues: List) -> Dict:
        """分析严重程度分布"""
        high_severity = sum(1 for issue in critical_issues if isinstance(issue, dict) and issue.get('severity') == 'HIGH')
        medium_severity = sum(1 for issue in critical_issues if isinstance(issue, dict) and issue.get('severity') == 'MEDIUM')
        low_severity = len(critical_issues) - high_severity - medium_severity

        return {
            "high_severity_count": high_severity,
            "medium_severity_count": medium_severity,
            "low_severity_count": low_severity
        }

    def _generate_immediate_actions(self, field_confidences: List[FieldConfidence], critical_issues: List) -> List[str]:
        """生成立即行动建议"""
        actions = []

        critical_fields = [fc for fc in field_confidences if fc.final_confidence_score < 0.6]
        if critical_fields:
            actions.append(f"立即处理 {len(critical_fields)} 个关键置信度不足的字段")

        if len(critical_issues) > 5:
            actions.append("优先解决高严重性问题，建议专家介入")

        return actions

    def _generate_medium_term_improvements(self, fusion_results: Dict) -> List[str]:
        """生成中期改进建议"""
        improvements = []

        low_consistency_fields = [name for name, result in fusion_results['individual_field_fusions'].items()
                                if result['consistency_level'] == 'LOW']

        if low_consistency_fields:
            improvements.append(f"改善 {len(low_consistency_fields)} 个字段的验证一致性")

        return improvements

    def _generate_long_term_strategy(self, target_achievement: float) -> List[str]:
        """生成长期策略建议"""
        strategies = []

        if target_achievement < 0.8:
            strategies.append("建立系统性的质量改进流程")
            strategies.append("增强V4知识库的覆盖度和准确性")

        strategies.append("持续优化三重验证机制的权重和阈值")

        return strategies

    def _identify_next_cycle_focus_areas(self, field_confidences: List[FieldConfidence]) -> List[str]:
        """识别下一轮验证的重点领域"""
        focus_areas = []

        # 按字段类型分析问题分布
        type_issues = defaultdict(int)
        for fc in field_confidences:
            if fc.final_confidence_score < 0.8:
                type_issues[fc.field_type] += 1

        # 找出问题最多的字段类型
        if type_issues:
            most_problematic_type = max(type_issues, key=type_issues.get)
            focus_areas.append(f"重点关注 {most_problematic_type} 类型字段")

        return focus_areas

    def _predict_next_cycle_improvements(self, field_confidences: List[FieldConfidence]) -> Dict:
        """预测下一轮改进效果"""
        current_avg = sum(fc.final_confidence_score for fc in field_confidences) / len(field_confidences)

        # 基于当前问题分布预测改进潜力
        improvement_potential = 0.1 if current_avg > 0.8 else 0.2 if current_avg > 0.6 else 0.3

        return {
            "predicted_confidence_improvement": improvement_potential,
            "predicted_target_achievement": min(1.0, (current_avg + improvement_potential) / 0.933),
            "confidence_level": "HIGH" if improvement_potential > 0.15 else "MEDIUM"
        }
    
    def _detect_field_contradictions(self, field_conf: FieldConfidence) -> Dict:
        """检测单个字段的置信度矛盾"""
        ide_ai = field_conf.ide_ai_confidence
        python_algo = field_conf.python_algorithm_confidence
        python_ai = field_conf.python_ai_combined_confidence
        
        # 计算两两之间的差异
        ide_python_gap = abs(ide_ai - python_algo)
        ide_combined_gap = abs(ide_ai - python_ai)
        algo_combined_gap = abs(python_algo - python_ai)
        
        max_gap = max(ide_python_gap, ide_combined_gap, algo_combined_gap)
        
        contradiction_type = self._identify_contradiction_type(
            ide_ai, python_algo, python_ai, max_gap
        )
        
        return {
            "field_name": field_conf.field_name,
            "has_contradiction": max_gap > self.contradiction_threshold,
            "contradiction_severity": self._assess_severity(max_gap),
            "contradiction_type": contradiction_type,
            "confidence_gaps": {
                "ide_ai_vs_python_algo": ide_python_gap,
                "ide_ai_vs_python_combined": ide_combined_gap,
                "python_algo_vs_combined": algo_combined_gap
            },
            "confidence_values": {
                "ide_ai": ide_ai,
                "python_algorithm": python_algo,
                "python_ai_combined": python_ai
            }
        }
    
    def _identify_contradiction_type(self, ide_ai: float, python_algo: float, 
                                   python_ai: float, max_gap: float) -> str:
        """识别矛盾类型"""
        if max_gap < self.contradiction_threshold:
            return "NO_CONTRADICTION"
        
        # AI过度自信：AI评估高，算法评估低
        if ide_ai > python_algo + self.contradiction_threshold:
            return "AI_OVERCONFIDENCE"
        
        # AI过度谨慎：AI评估低，算法评估高
        if python_algo > ide_ai + self.contradiction_threshold:
            return "AI_UNDERCONFIDENCE"
        
        # 综合评估矛盾：前两者一致，但综合评估差异大
        if abs(ide_ai - python_algo) < 0.1 and abs(python_ai - ide_ai) > self.contradiction_threshold:
            return "COMBINED_ASSESSMENT_CONTRADICTION"
        
        return "COMPLEX_CONTRADICTION"
    
    def _assess_severity(self, gap: float) -> str:
        """评估矛盾严重程度"""
        if gap >= self.severe_contradiction_threshold:
            return "SEVERE"
        elif gap >= self.contradiction_threshold:
            return "MODERATE"
        else:
            return "MINOR"
    
    def _generate_optimization_suggestions(self, field_conf: FieldConfidence, 
                                         contradiction: Dict) -> List[str]:
        """生成优化建议"""
        suggestions = []
        contradiction_type = contradiction['contradiction_type']
        field_name = field_conf.field_name
        
        if contradiction_type == "AI_OVERCONFIDENCE":
            suggestions.append(f"{field_name}: AI过度自信，需要更严格的自我评估标准")
            suggestions.append(f"{field_name}: 建议增加算法验证步骤，降低AI主观判断权重")
        
        elif contradiction_type == "AI_UNDERCONFIDENCE":
            suggestions.append(f"{field_name}: AI过度谨慎，可能低估了自身能力")
            suggestions.append(f"{field_name}: 建议基于算法验证结果提升AI自信度")
        
        elif contradiction_type == "COMBINED_ASSESSMENT_CONTRADICTION":
            suggestions.append(f"{field_name}: 综合评估存在系统性偏差")
            suggestions.append(f"{field_name}: 需要重新校准Python+AI组合评估算法")
        
        return suggestions
    
    def _calculate_improvement_potential(self, field_confidences: List[FieldConfidence]) -> float:
        """计算整体改进潜力"""
        if not field_confidences:
            return 0.0
        
        current_avg = sum(fc.ide_ai_confidence for fc in field_confidences) / len(field_confidences)
        target_confidence = 0.95  # 目标95%置信度
        
        return max(0.0, target_confidence - current_avg)
    
    def _identify_priority_fields(self, contradictions: List[Dict]) -> List[str]:
        """识别优先优化字段"""
        severe_contradictions = [c for c in contradictions if c['contradiction_severity'] == 'SEVERE']
        return [c['field_name'] for c in severe_contradictions[:5]]  # 返回前5个严重矛盾字段

# 使用示例和测试
def main():
    """主函数：演示支持AI开关的三重置信度验证机制"""

    print("🚀 启动支持AI开关的三重置信度验证机制演示")
    print("=" * 70)

    # 创建更丰富的模拟数据
    sample_field_confidences = [
        FieldConfidence(
            field_name="architecture_pattern",
            ide_ai_confidence=0.90,
            python_algorithm_confidence=0.95,
            python_ai_combined_confidence=0.85,
            content="微内核架构模式：核心系统负责基础功能，插件系统提供扩展能力，通过服务总线进行通信",
            field_type="architecture_pattern"
        ),
        FieldConfidence(
            field_name="technology_stack",
            ide_ai_confidence=0.95,
            python_algorithm_confidence=0.92,
            python_ai_combined_confidence=0.88,
            content="Spring Boot 3.4.5 + Java 21 + Maven 3.9.0 技术栈组合",
            field_type="technology_stack"
        ),
        FieldConfidence(
            field_name="interface_design",
            ide_ai_confidence=0.85,
            python_algorithm_confidence=0.78,
            python_ai_combined_confidence=0.82,
            content="PluginActivator接口定义插件生命周期管理方法：activate(), deactivate(), getStatus()",
            field_type="interface_design"
        ),
        FieldConfidence(
            field_name="business_logic_mapping",
            ide_ai_confidence=0.65,
            python_algorithm_confidence=0.45,
            python_ai_combined_confidence=0.55,
            content="业务逻辑通过插件接口映射，支持动态加载和热插拔",
            field_type="business_logic"
        ),
        FieldConfidence(
            field_name="performance_metrics",
            ide_ai_confidence=0.80,
            python_algorithm_confidence=0.85,
            python_ai_combined_confidence=0.75,
            content="系统启动时间 ≤ 1000ms，插件加载时间 ≤ 500ms，内存占用 ≤ 512MB",
            field_type="performance_metrics"
        )
    ]

    # 演示不同的验证模式
    validation_modes = [
        ("完整三重验证", ValidationConfig(mode=ValidationMode.FULL_TRIPLE)),
        ("快速迭代模式（仅IDE AI）", ValidationConfig(
            mode=ValidationMode.FAST_ITERATION,
            fast_iteration_focus="ide_ai_only",
            enable_python_ai_logic_validation=False,
            enable_v4_panoramic_validation=False,
            enable_detailed_logging=False
        )),
        ("算法验证模式（仅V4）", ValidationConfig(
            mode=ValidationMode.ALGORITHM_ONLY,
            enable_ide_ai_validation=False,
            enable_python_ai_logic_validation=False,
            enable_detailed_logging=False
        ))
    ]

    for mode_name, config in validation_modes:
        print(f"\n{'='*20} {mode_name} {'='*20}")

        analyzer = EnhancedTripleConfidenceAnalyzer(config)

        try:
            results = analyzer.execute_adaptive_validation(sample_field_confidences)

            print(f"\n📊 {mode_name} 验证结果摘要:")
            print(f"验证模式: {results['validation_summary']['validation_mode']}")
            print(f"启用验证器: {', '.join(results['validation_summary']['enabled_validators'])}")
            print(f"总字段数: {results['validation_summary']['total_fields_validated']}")
            print(f"目标达成率: {results['validation_summary']['target_achievement_rate']:.1%}")
            print(f"整体置信度: {results['confidence_distribution']['overall_confidence_score']:.3f}")

            print(f"\n🎯 置信度分布:")
            print(f"优秀字段: {results['confidence_distribution']['excellent_fields']}")
            print(f"良好字段: {results['confidence_distribution']['good_fields']}")
            print(f"问题字段: {results['confidence_distribution']['problematic_fields']}")

            # 显示模式特定分析
            if 'mode_specific_analysis' in results:
                mode_analysis = results['mode_specific_analysis']
                print(f"\n🔧 模式特定分析:")
                print(f"模式有效性: {mode_analysis['mode_effectiveness']}")
                for rec in mode_analysis['mode_recommendations'][:2]:
                    print(f"• {rec}")

            # 显示性能指标（如果启用）
            if 'performance_metrics' in results:
                perf = results['performance_metrics']
                print(f"\n⚡ 性能指标:")
                print(f"执行时间: {perf['execution_time_seconds']:.2f}秒")
                print(f"启用验证器: {', '.join(perf['enabled_validators'])}")

        except Exception as e:
            print(f"❌ {mode_name} 验证过程中出现错误: {e}")

    print(f"\n🎉 AI开关验证机制演示完成！")
    print("=" * 70)

    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def demonstrate_ai_switch_capabilities():
    """演示AI开关功能和验证模式"""

    print("\n🔄 AI开关功能和验证模式对比:")
    print("=" * 50)

    validation_modes = {
        "完整三重验证模式": {
            "描述": "IDE AI + Python AI + V4算法全面验证",
            "适用场景": "最终质量检查、正式发布前验证",
            "优势": ["最全面的质量保障", "93.3%执行正确度目标", "深度矛盾检测"],
            "性能": "较慢但最准确"
        },
        "快速迭代模式": {
            "描述": "仅IDE AI验证，Python AI关闭",
            "适用场景": "IDE AI快速修改阶段、频繁迭代开发",
            "优势": ["快速响应", "适合迭代开发", "减少验证开销"],
            "性能": "快速但覆盖度有限"
        },
        "算法验证模式": {
            "描述": "仅V4算法验证，AI验证关闭",
            "适用场景": "算法逻辑验证、知识库一致性检查",
            "优势": ["专注算法验证", "V4知识库对齐", "客观性强"],
            "性能": "中等速度，算法准确"
        },
        "自定义模式": {
            "描述": "灵活配置各验证器开关",
            "适用场景": "特定需求场景、调试分析",
            "优势": ["高度灵活", "按需配置", "精准控制"],
            "性能": "根据配置而定"
        }
    }

    for mode_name, details in validation_modes.items():
        print(f"\n📋 {mode_name}:")
        print(f"  描述: {details['描述']}")
        print(f"  适用场景: {details['适用场景']}")
        print(f"  性能特点: {details['性能']}")
        print("  主要优势:")
        for advantage in details['优势']:
            print(f"    • {advantage}")

    print(f"\n🎯 AI开关核心价值:")
    print("  1. 🚀 快速迭代支持 - IDE AI修改阶段关闭Python AI，提升响应速度")
    print("  2. 🎛️ 灵活验证控制 - 根据开发阶段选择合适的验证强度")
    print("  3. ⚡ 性能优化 - 避免不必要的验证开销，提升开发效率")
    print("  4. 🔧 调试友好 - 可单独启用特定验证器进行问题定位")
    print("  5. 📊 渐进式质量 - 从快速验证到完整验证的平滑过渡")

def demonstrate_validation_improvements():
    """演示相比v3.1的验证改进"""

    print("\n🔄 验证机制改进对比:")
    print("=" * 40)

    improvements = {
        "v3.1验证": [
            "简单的算法验证",
            "单一维度分析",
            "基础矛盾检测",
            "有限的上下文理解",
            "固定验证流程"
        ],
        "v4.0智能AI开关验证": [
            "IDE AI自评 + Python AI逻辑链推理 + V4全景验证",
            "多维度深度分析",
            "关系逻辑链矛盾推理验证",
            "V4知识库全景一致性验证",
            "智能验证结果融合",
            "预测性优化建议",
            "🆕 AI开关灵活控制",
            "🆕 多种验证模式",
            "🆕 快速迭代支持",
            "🆕 性能优化配置"
        ]
    }

    for version, features in improvements.items():
        print(f"\n{version}:")
        for feature in features:
            print(f"  • {feature}")

    print(f"\n🎯 核心创新:")
    print("  1. 基于关系逻辑链的深度推理验证")
    print("  2. V4算法全景知识库一致性验证")
    print("  3. 三重验证结果的智能融合机制")
    print("  4. 93.3%执行正确度目标的系统性追踪")
    print("  5. 🆕 AI开关机制 - 支持快速迭代和灵活验证控制")
    print("  6. 🆕 多模式验证 - 适应不同开发阶段的验证需求")

def demonstrate_usage_examples():
    """演示具体使用示例"""

    print("\n📖 具体使用示例:")
    print("=" * 30)

    print("\n🔧 示例1: IDE AI快速迭代开发")
    print("```python")
    print("# 快速迭代配置 - 关闭Python AI，专注IDE AI快速修改")
    print("config = ValidationConfig(")
    print("    mode=ValidationMode.FAST_ITERATION,")
    print("    fast_iteration_focus='ide_ai_only',")
    print("    enable_python_ai_logic_validation=False,")
    print("    enable_v4_panoramic_validation=False")
    print(")")
    print("analyzer = EnhancedTripleConfidenceAnalyzer(config)")
    print("results = analyzer.execute_adaptive_validation(field_confidences)")
    print("```")

    print("\n🔧 示例2: 算法专项验证")
    print("```python")
    print("# 仅算法验证配置 - 专注V4算法验证")
    print("config = ValidationConfig(")
    print("    mode=ValidationMode.ALGORITHM_ONLY,")
    print("    enable_ide_ai_validation=False,")
    print("    enable_python_ai_logic_validation=False")
    print(")")
    print("analyzer = EnhancedTripleConfidenceAnalyzer(config)")
    print("results = analyzer.execute_adaptive_validation(field_confidences)")
    print("```")

    print("\n🔧 示例3: 完整质量检查")
    print("```python")
    print("# 完整验证配置 - 最终质量检查")
    print("config = ValidationConfig(mode=ValidationMode.FULL_TRIPLE)")
    print("analyzer = EnhancedTripleConfidenceAnalyzer(config)")
    print("results = analyzer.execute_adaptive_validation(field_confidences)")
    print("```")

if __name__ == "__main__":
    # 执行支持AI开关的三重验证机制演示
    main()

    # 演示AI开关功能
    demonstrate_ai_switch_capabilities()

    # 演示验证改进对比
    demonstrate_validation_improvements()

    # 演示具体使用示例
    demonstrate_usage_examples()

    print(f"\n📝 核心特性总结:")
    print("1. 🎯 完整三重验证机制 - 超越简单的v3.1算法验证")
    print("2. 🔗 Python AI关系逻辑链矛盾推理验证")
    print("3. 🌐 V4算法全景知识库一致性验证")
    print("4. ⚖️ 智能验证结果融合和优化建议")
    print("5. 📊 93.3%执行正确度目标的系统性追踪")
    print("6. 🎛️ AI开关机制 - 支持快速迭代和灵活验证控制")
    print("7. 🚀 多种验证模式 - 适应不同开发阶段需求")
    print("8. ⚡ 性能优化 - 可关闭Python AI进行快速迭代")
