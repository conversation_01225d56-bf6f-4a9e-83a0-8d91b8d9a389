# V4.3方案：宏观语义地图构建流程 (全局知识图谱)

## 1. 文档信息

- **文档版本**: V4.3
- **创建日期**: 2025-08-13
- **目的**: 详细阐述V4.3治理引擎如何通过确定性算法，从设计文档提取、解析并构建“宏观语义地图”（即全局知识图谱）的流程和技术细节。

## 2. 宏观语义地图的定义与重要性

**宏观语义地图 (Macro Semantic Map)** V4.3治理引擎的核心数据结构，它是一个由所有设计文档中提取的`AtomicConstraint`、模块关系、数据流、交互界面定义等构成的**全局知识图谱**。

-   **重要性**:
    *   **冲突预防**: 它是进行“阶段零”确定性校验的基础，确保所有原子约束在全局范围内无内部冲突。
    *   **上下文感知**: 为Py AI提供丰富的上下文信息，使其能够生成更准确、更智能的“富报告”和`new_task`提示词。
    *   **可追溯性**: 确保任何一个架构决策或约束都可以在这个图谱中找到其来源和关联。
    *   **自动化验证**: 为后续的代码与设计一致性验证提供基准。

## 3. 构建流程概览

宏观语义地图的构建是一个多阶段的确定性算法过程，主要包括：**文档扫描与解析**、**结构化信息提取**、**语义关联与图谱构建**、**确定性校验**。

```mermaid
graph TD
    A[1. 触发扫描] --> B[2. 文档遍历与加载]
    B --> C[3. Markdown解析]
    C --> D{4. 识别结构化区块<br/>(AtomicConstraint JSON, Pseudocode, Mermaid)}
    D --> E[5. 提取AtomicConstraint JSON]
    D --> F[6. 提取伪代码块]
    D --> G[7. 提取Mermaid图定义]
    E --> H[8. AtomicConstraint Schema校验]
    F --> I[9. 伪代码语法与覆盖率校验]
    G --> J[10. Mermaid语法校验]
    H & I & J --> K[11. 构建全局知识图谱<br/>(节: 约束, 模块, 接口; 边: 依赖, 继承, 调用)]
    K --> L[12. 知识图谱内部一致性校验<br/>(算法)]
    L --> M[13. 宏观语义地图构建完成]
    M --> N[14. 交付Py AI进行分析]
```

## 4. 各阶段详细描述

### 4.1. 阶段1: 文档遍历与加载

-   **输入**: 用户指定的待审计设计文档路径列表（例如 `docs/features/T001/design/01-overall-architecture.md`）。
-   **操作**:
    *   递归遍历指定路径下的所有Markdown文件（`*.md`）。
    *   加载每个文件的内容到内存。
-   **输出**: 待处理的Markdown文件内容合。

### 4.2. 阶段2: Markdown解析与结构化信息识别

-   **工具**: 使用Python的Markdown解析库（如`markdown-it-py`或`mistune`），结合自定义的AST（抽象语法树）遍历逻辑。
-   **操作**:
    *   将Markdown文本解析为AST。
    *   遍历AST，识别特定的代码块和自定义标记。
    *   **识别目标**:
        *   **`AtomicConstraint` JSON块**: 寻找以特定Markdown注释（如 `<!-- ATOMIC_CONSTRAINT_START -->` 和 `<!-- ATOMIC_CONSTRAINT_END -->`）包围的JSON代码块。
        *   **伪代码块**: 寻找以`pseudocode`语言标记的代码块（例如 ````pseudocode`）。
        *   **Mermaid图定义**: 寻找以`mermaid`语言标记的代码块（例如 ````mermaid`）。
-   **输出**: 结构化的数据片段（JSON字符串、伪代码文本、Mermaid定义文本），以及它们在原始文档中的位置信息（文件路径、行号）。

### 4.3. 阶段3: 结构化信息提取与初步校验

针对识别出的每种结构化区块，进行初步的语法校验和数据提取。

#### 4.3.1. `AtomicConstraint` JSON提取与Schema校验

-   **输入**: 识别出的JSON字符串。
-   **操作**:
    *   尝将JSON字符串解析为Python字典。
    *   使用`jsonschema`库，根据`02-架构数据型标准-V3.md`中定义的`AtomicConstraint` JSON Schema进行严格校验。
    *   **确定性校验**: 确保`id`字段的唯一性，`parent_id`指向的束存在，`category`和`type`符合枚举值。
-   **输出**: 合法的`AtomicConstraint`对象列表，以及任何校验失败的错误信息。

#### 4.3.2. 伪代码块提取与语法/覆盖率校验

-   **输入**: 识别出的伪代码文本
-   **操作**:
    *   **语法校验**: 对伪代码进行基本的语法检查（例如，是否符合定义的伪代码规范）。
    *   **逻辑覆盖率校验**: 这是V4.3新增的关键算法。通过静态分析伪代码，评估其对描述逻辑的覆盖程度。例如，对于一个描述API调用的伪代码，法会检查是否包含了请求参数、响应处理、异常处理等关键步骤。
        *   **覆盖率标准**: 伪代码块的逻辑覆盖率必须达到**60%以上**。
-   **输出**: 伪代码文本及其逻辑覆盖率数，以及任何校验失败的错误信息。

#### 4.3.3. Mermaid图定义提取与语法校验

-   **输入**: 识别出的Mermaid定义文本。
-   **操作**:
    *   使用Mermaid解析器（或单的正则匹配）进行语法校验，确保其是合法的Mermaid语法。
-   **输出**: 合法的Mermaid图定义，以及任何校验失败的错误信息。

### 4.4. 阶段4: 语义关联与全局知识图谱构建

-   **工具**: 使用图数据库（如`Neo4j`或Python的`networkx`库）来表示和操作图谱。
-   **操作**:
    *   将所有通过校验的`AtomicConstraint`、伪代码逻辑、Mermaid图中的实体（模块、组件、接口）作为**节点**添加到图谱中。
    *   根据`AtomicConstraint`中的`parent_id`字段、伪代码中的调用关系、Mermaid图中的连接线，构建**边**来表示它们之间的语义关系（如派生自”、“依赖于”、“调用”、“包含”）。
    *   **关键关联**:
        *   `AtomicConstraint`与它所约束的模块/接口。
        *   伪代码与它所实现的`AtomicConstraint`。
        *   Mermaid图中的组件与际代码模块的映射。
-   **输出**: 一个完整的、机器可读的“宏观语义地图”（全局知识图谱）。

### 4.5. 阶段5: 知识图谱内部一致性校验 (确定性算法)

-   **输入**: 构建完成的全局知识图谱。
-   **操作**:
    *   **唯一性检查**: 确保所有`id`（包括`AtomicConstraint ID`、模块ID等）在全局范围内唯一。
    *   **循环依赖检测**: 检查模块、约束之间是否存在循环依赖。
    *   **孤立节点检测**: 识别图中是否存在未任何其他节点引用或关联的`AtomicConstraint`或模块。
    *   **逻辑冲突检测**: 检查是否存在相互矛盾的`AtomicConstraint`（例如，一个约束要求“必须异步”，另一个约束要求“必须同步”）。这需要更复杂的语义分析。
-   **输出**: 内部一致性校验结果，任何发现的冲突或不一致性都会被标记。

## 5. 交付与后续流程

构建完成并经过内部一致性校验的“宏观语义地图”将作为核心数据结构，用于后续的**算法驱动规划**和**PyAI辅助增强**流程。

1.  **交付双重验证引擎**: 地图将首先被用于与代码现实（微观图）进行比对，以识别差异。
2.  **交付算法规划器 (`plan_generator.py`)**: 在识别出差异后，算法规划器将以此地图为基础，进行图查询，**确定性地**推导出解决该差异所需的多步骤“计划骨架”。
3.  **交付Py AI进行增强**: 最终，算法生成的“计划骨架”将被交付给**Py AI（首席架构分析师）**，由其进行丰富和翻译，生成最终的“富报告”。

## 6. 文档 AST 标记规范（无二义识别）

为确保解析确定性，所有结构化区块必须遵循如下标记：

- AtomicConstraint JSON：
  - 起止标记：`<!-- ATOMIC_CONSTRAINT_START -->` 与 `<!-- ATOMIC_CONSTRAINT_END -->`
  - 代码围栏语言：`json`
  - 示例：

```markdown
<!-- ATOMIC_CONSTRAINT_START -->
```json
{
  "id": "GC-PERF-001",
  "category": "performance",
  "type": "http_client_policy",
  "params": { "timeout_ms": 300, "retries": 2 },
  "description": "外呼HTTP必须设置超时与重试"
}
```
<!-- ATOMIC_CONSTRAINT_END -->
```

- 伪代码块：
  - 代码围栏语言：`pseudocode`
  - 建议语法元素：`PROCEDURE/INPUT/OUTPUT/IF/ELSE/ELSE IF/TRY/CATCH/FINALLY/RETURN`
  - 每段伪代码建议含：输入/输出声明、主流程、异常/超时处理、日志或指标。

- Mermaid 图：
  - 代码围栏语言：`mermaid`
  - 推荐图式：`graph TD` 或 `sequenceDiagram`
  - 节点命名须可回指到文档中的模块/接口名；禁止与约束ID冲突。

## 7. 伪代码覆盖率评分器（确定性）

评分器以 100 分制，四项等权各 25 分，满足即得分；不足不给分；总分四舍五入到整数。

- I/O 明确性（25）
  - 存在 INPUT 与 OUTPUT 段，字段/含义清晰可读。
- 控制流完整性（25）
  - 存在关键分支（IF/ELSE 或循环）覆盖主要路径。
- 异常与时序健壮性（25）
  - 存在 TRY/CATCH 或等效异常处理；外部交互含超时/重试/降级之一定义。
- 观测与副作用声明（25）
  - 关键步骤含日志/指标/审计之一；对外部副作用（DB/IO/队列）有显式标注。

通过阈值：总分 ≥ 60 记为通过（COMPLIANT）。

正规化规则以确保可复现计算：
- 忽略空白差异；关键字大小写不敏感；同义词表（e.g. TRY-CATCH=EXCEPT）保持固定版本。

## 8. Mermaid 约束与名称映射

- 语法：须通过 Mermaid 解析；禁用自定义扩展。 
- 名称映射：图中组件/接口名应与文档章节或 `AtomicConstraint.params.target` 可解析；否则标记为孤立节点提醒修订。
- 关系约束：跨层调用需在设计中有明确依赖；若图示中存在未定义依赖，进入宏观图一致性校验的冲突列表。

## 9. 核心应用：算法驱动的计划骨架生成

宏观语义地图最重要的应用，是作为“算法驱动内核”的数据基础，确定性地生成任务计划的骨架。

### 9.1. 工作原理
`plan_generator.py` 模块将严格遵循以下流程：

1.  **输入**: 一个结构化的“差异点”，由双重验证引擎产出（例如，`{type: "MISSING_IMPLEMENTATION", entity: "UserService", constraint: "GC-PERF-001"}`）。
2.  **核心算法**: 算法以差异点实体（`UserService`）为起点，在内存中的知识图谱（宏观语义地图）上执行一次**限定深度的广度优先搜索（BFS）**，遍历其**一到两度**的依赖关系（如 `DEPENDS_ON`, `CALLS`）。
3.  **依赖分析**:
    *   **上游影响**: 算法通过查询图谱中的**入边**，识别出所有直接或间接依赖于 `UserService` 的实体（如 `OrderController`）。
    *   **下游需求**: 算法通过查询图谱中的**出边**，识别出 `GC-PERF-001` 约束本身所依赖的其他约束或配置（例如，一个要求记录特定监控指标的 `AC-METRIC-005` 约束）。
4.  **输出**: 一个确定性的、结构化的“计划骨架” YAML，清晰地列出所有受影响的实体和需要对它们执行的标准化操作（`UPDATE_CALL_SITE`, `ADD_METRIC` 等）。

### 9.2. 数据确定性保障
为确保规划的可靠性，必须遵循以下核心规则：

-   **数据闭环**: `plan_generator.py` 进行依赖分析时，其数据源**必须且只能**是当前审计周期内，由 `DocumentScanner` 从 `.md` 文件中实时构建的“宏观语义地图”。**严禁**算法模块跨边界调用代码分析工具（如 `serena`）或访问任何外部数据库。
-   **图谱质量护栏**: Mermaid 图中的节点名称**必须**与文档中其他地方定义的实体名称（如 `AtomicConstraint` 的 `target_entity`）保持严格一致。在图谱构建阶段，任何无法关联的“孤立节点”都将被识别，并作为高优先级问题在富报告中提出，强制要求修复设计文档本身的一致性。
