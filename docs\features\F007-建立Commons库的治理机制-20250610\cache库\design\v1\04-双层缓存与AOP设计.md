# F007 Cache库-双层缓存与AOP设计

## 文档元数据

- **文档ID**: `F007-CACHE-DUALLAYER-AOP-DESIGN-004`
- **版本**: `V1.0`
- **模块**: `commons-cache-core`
- **技术栈**: Java 21, Spring Boot 3.4.5, Spring AOP 6.1.3, AspectJ 1.9.22, Maven 3.9
- **构建工具**: Maven 3.9.6
- **数据库技术栈**: N/A (协调层，无直接数据存储)
复杂度等级: L3

## 核心定位

双层缓存与AOP模块是缓存库的智能协调中心，负责实现L1本地缓存与L2远程缓存的自动协调、一致性管理和注解驱动的缓存逻辑。它是"易用性"承诺的核心实现。

## 设计哲学

本模块遵循以下设计哲学：
1. **透明化复杂性**: 将复杂的双层缓存逻辑对开发者完全透明，开发者无感知缓存层级
2. **智能协调**: 自动处理L1/L2之间的读取顺序和一致性，一致性窗口≤100ms
3. **声明式编程**: 通过注解实现缓存逻辑的声明式配置，代码侵入性≤5%
4. **高级特性集成**: 充分利用Valkey 8的客户端缓存特性，性能提升≥40%

## 包含范围

**核心功能模块**：
- 双层缓存管理器（DualLayerCacheManager）
- AOP切面处理（CacheAspect）
- 缓存一致性协调机制
- SpEL表达式解析和缓存键生成

## 排除范围

**不包含功能**：
- 具体的缓存实现逻辑
- 监控指标收集
- Spring Boot自动配置
- 缓存序列化机制

## 1. 分层架构设计

### 1.1 架构层次结构

```
┌─────────────────────────────────────────────────────────────────┐
│                      业务应用层                                  │
│                  @XkCacheable 注解                              │
├─────────────────────────────────────────────────────────────────┤
│                   AOP切面层 (本层)                               │
│                    CacheAspect                                  │
├─────────────────────────────────────────────────────────────────┤
│                 双层缓存协调层 (本层)                             │
│                DualLayerCacheManager                            │
├─────────────────────────────────────────────────────────────────┤
│                    缓存实现层                                    │
│  LocalCacheTemplate  |  ValkeyCacheTemplate                   │
├─────────────────────────────────────────────────────────────────┤
│                    底层存储层                                    │
│     Caffeine 3.1.8    |      Valkey 8.0                      │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 模块依赖关系

**核心依赖图**:
- `DualLayerCacheManager` → `LocalCacheTemplate` + `ValkeyCacheTemplate`
- `CacheAspect` → `DualLayerCacheManager` + `SpelExpressionParser`
- **边界定义**: AOP层只处理切面逻辑，缓存协调委托给管理器
- **职责分离**: 切面处理与缓存管理完全解耦，便于独立测试和扩展

### 1.3 接口契约定义

**DualLayerCacheManager契约**:
```java
// 核心接口契约 - 双层缓存协调
public interface DualLayerCacheManager {
    <T> T get(String cacheName, String key, boolean useL1Cache, Supplier<T> loader);  // 智能读取
    void put(String cacheName, String key, Object value, Duration ttl);               // 双层写入
    void evict(String cacheName, String key);                                        // 双层驱逐
    void clear(String cacheName);                                                    // 全量清理
    CacheStats getStats(String cacheName, CacheLayer layer);                        // 分层统计
}
```

**CacheAspect契约**:
```java
// 核心接口契约 - AOP切面处理
@Aspect
public class CacheAspect {
    @Around("@annotation(XkCacheable)")
    Object aroundCacheable(ProceedingJoinPoint joinPoint, XkCacheable cacheable);   // 缓存读取切面
    
    @Around("@annotation(XkCachePut)")  
    Object aroundCachePut(ProceedingJoinPoint joinPoint, XkCachePut cachePut);      // 缓存更新切面
    
    @Around("@annotation(XkCacheEvict)")
    Object aroundCacheEvict(ProceedingJoinPoint joinPoint, XkCacheEvict cacheEvict); // 缓存驱逐切面
}
```

## 2. 核心职责

本模块是实现"自动模式"的核心，通过AOP和智能的缓存管理器，将复杂的双层缓存逻辑对开发者透明化。

## 3. `DualLayerCacheManager` 设计

**架构职责**: 
- 协调L1（Caffeine）和L2（Valkey）的读写操作，确保数据一致性
- 实现Cache-Aside模式的变体，优化读写性能
- 提供智能降级策略，保证服务可用性

**内部状态**: 
```java
@Component
public class DualLayerCacheManager {
    private final LocalCacheTemplate localCache;          // L1本地缓存
    private final ValkeyCacheTemplate remoteCache;        // L2远程缓存
    private final CacheConsistencyManager consistencyMgr; // 一致性管理器
    private final CacheMetrics metrics;                   // 性能指标收集
    private final CircuitBreaker circuitBreaker;          // 熔断器
}
```

### 3.1 读操作流程

```mermaid
flowchart TD
    A[读请求] --> B{查 L1 (Caffeine)};
    B -- 命中 --> C[返回结果];
    B -- 未命中 --> D{查 L2 (Valkey)};
    D -- 命中 --> E{写入 L1}; 
    E --> C;
    D -- 未命中 --> F{回源执行方法};
    F --> G{写入 L2};
    G --> E;
```

**详细实现逻辑**:
```java
public <T> T get(String cacheName, String key, boolean useL1Cache, Supplier<T> loader) {
    CacheOperation operation = new CacheOperation(cacheName, key, "GET");
    
    try {
        // 阶段1: L1缓存查询 (性能优先)
        if (useL1Cache) {
            Optional<T> l1Result = localCache.get(key);
            if (l1Result.isPresent()) {
                metrics.recordL1Hit(cacheName);
                return l1Result.get();
            }
            metrics.recordL1Miss(cacheName);
        }
        
        // 阶段2: L2缓存查询 (可靠性优先)
        Optional<T> l2Result = remoteCache.getAsync(key)
            .orTimeout(5, TimeUnit.SECONDS)  // 超时控制
            .exceptionally(throwable -> {
                circuitBreaker.recordFailure();
                return Optional.empty();     // 降级处理
            })
            .get();
            
        if (l2Result.isPresent()) {
            // 回填L1缓存
            if (useL1Cache) {
                localCache.put(key, l2Result.get());
            }
            metrics.recordL2Hit(cacheName);
            return l2Result.get();
        }
        
        // 阶段3: 回源数据加载
        T sourceData = loader.get();
        if (sourceData != null) {
            // 双层写入
            CompletableFuture.allOf(
                remoteCache.putAsync(key, sourceData),           // L2优先
                useL1Cache ? localCache.putAsync(key, sourceData) : CompletableFuture.completedFuture(null)
            ).orTimeout(3, TimeUnit.SECONDS).join();
        }
        
        metrics.recordSourceLoad(cacheName);
        return sourceData;
        
    } catch (Exception e) {
        metrics.recordError(cacheName, operation, e);
                    throw SystemException.internalError("XCE_SYS_801", "双层缓存读取失败: " + e.getMessage(), e);
    }
}
```

### 3.2 写/驱逐操作流程

采用Cache-Aside模式的变体，保证L2的优先更新。

**写入策略**:
- **`@XkCachePut`**: 先更新数据库，然后**同时驱逐L1和L2**的缓存，下次读取时会自动加载新值
- **`@XkCacheEvict`**: **同时驱逐L1和L2**的缓存，确保数据一致性

```java
public void evict(String cacheName, String key) {
    try {
        // 并行驱逐 - 最大化性能
        CompletableFuture<Void> l2Eviction = remoteCache.evictAsync(key);
        CompletableFuture<Void> l1Eviction = CompletableFuture.runAsync(() -> 
            localCache.evict(key)
        );
        
        // 等待完成，确保一致性
        CompletableFuture.allOf(l2Eviction, l1Eviction)
            .orTimeout(2, TimeUnit.SECONDS)
            .join();
            
        metrics.recordEviction(cacheName, "dual-layer");
        
    } catch (Exception e) {
        metrics.recordError(cacheName, new CacheOperation(cacheName, key, "EVICT"), e);
                    throw SystemException.internalError("XCE_SYS_802", "双层缓存驱逐失败: " + e.getMessage(), e);
    }
}
```

### 3.3 L1/L2 一致性关键：Valkey客户端缓存

这是本设计的技术亮点。我们将利用Valkey 8的服务端辅助的客户端缓存（Server-Assisted Client-Side Caching）特性。

**实现机制**:
1. **订阅**: `DualLayerCacheManager`在初始化时，向Valkey发送`CLIENT TRACKING ON`命令，并订阅`__keyspace@*__:*`失效通知频道
2. **监听**: 当任何客户端修改了Valkey中的一个key时，Valkey服务器会主动向所有订阅的客户端推送失效消息
3. **驱逐L1**: `DualLayerCacheManager`的监听器收到消息后，立即从其持有的`LocalCacheTemplate`中驱逐对应的本地缓存项

```java
@PostConstruct
public void initializeClientTracking() {
    // 启用客户端追踪
    remoteCache.clientTracking(true);
    
    // 订阅失效通知
    remoteCache.subscribe("__keyspace@*__:*", (channel, message) -> {
        String invalidatedKey = extractKeyFromMessage(message);
        
        // 异步驱逐L1缓存，避免阻塞通知处理
        CompletableFuture.runAsync(() -> {
            localCache.evict(invalidatedKey);
            metrics.recordL1Invalidation(invalidatedKey);
            log.debug("L1缓存被自动失效: key={}", invalidatedKey);
        });
    });
}
```

**优势**: 这种机制将缓存一致性的复杂工作下沉到了Valkey和Lettuce客户端，应用层逻辑大大简化，且性能极高。

## 4. `CacheAspect` AOP设计

**架构职责**: 
- 拦截`@XkCacheable`等注解，实现声明式缓存
- 委托给`DualLayerCacheManager`处理缓存逻辑
- 提供SpEL表达式解析和缓存键生成能力

**切点定义**: 
```java
@Pointcut("@annotation(org.xkong.cloud.commons.cache.api.annotation.XkCacheable)")
public void cacheablePointcut() {}

@Pointcut("@annotation(org.xkong.cloud.commons.cache.api.annotation.XkCachePut)")  
public void cachePutPointcut() {}

@Pointcut("@annotation(org.xkong.cloud.commons.cache.api.annotation.XkCacheEvict)")
public void cacheEvictPointcut() {}
```

**核心实现**:
```java
@Aspect
@Component
@Order(Ordered.HIGHEST_PRECEDENCE + 1000)  // 确保在事务切面之后执行
public class CacheAspect {
    private final DualLayerCacheManager cacheManager;
    private final SpelExpressionParser spelParser;
    private final CacheKeyGenerator keyGenerator;

    @Around("cacheablePointcut() && @annotation(cacheable)")
    public Object aroundCacheable(ProceedingJoinPoint joinPoint, XkCacheable cacheable) {
        CacheOperation operation = buildCacheOperation(joinPoint, cacheable);
        
        try {
            // 解析SpEL表达式，生成最终的cache key
            String resolvedKey = keyGenerator.generate(
                cacheable.key(), 
                joinPoint.getArgs(), 
                joinPoint.getTarget(), 
                joinPoint.getSignature().getMethod()
            );
            
            String cacheName = cacheable.cacheName();
            
            // 委托给缓存管理器
            return cacheManager.get(
                cacheName, 
                resolvedKey, 
                cacheable.useL1Cache(), 
                () -> {
                    try {
                        return joinPoint.proceed(); // 回源方法调用
                    } catch (Throwable throwable) {
                        throw SystemException.internalError("XCE_SYS_803", "回源方法执行失败: " + throwable.getMessage(), throwable);
                    }
                }
            );
            
        } catch (Exception e) {
            // 缓存异常不应影响业务逻辑
            log.warn("缓存操作失败，降级为直接调用: method={}", joinPoint.getSignature(), e);
            try {
                return joinPoint.proceed();
            } catch (Throwable throwable) {
                throw SystemException.internalError("XCE_SYS_804", "业务方法执行失败: " + throwable.getMessage(), throwable);
            }
        }
    }
}
```

### 4.1 SpEL表达式解析

**键生成策略**:
```java
@Component
public class CacheKeyGenerator {
    private final SpelExpressionParser spelParser = new SpelExpressionParser();
    
    public String generate(String keyExpression, Object[] args, Object target, Method method) {
        if (!keyExpression.startsWith("#")) {
            return keyExpression; // 静态键
        }
        
        // 构建SpEL上下文
        EvaluationContext context = new StandardEvaluationContext();
        
        // 方法参数注册
        ParameterNameDiscoverer paramNameDiscoverer = new DefaultParameterNameDiscoverer();
        String[] paramNames = paramNameDiscoverer.getParameterNames(method);
        
        if (paramNames != null) {
            for (int i = 0; i < paramNames.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
        }
        
        // 目标对象注册
        context.setVariable("target", target);
        context.setVariable("method", method);
        
        // 表达式求值
        Expression expression = spelParser.parseExpression(keyExpression);
        Object keyValue = expression.getValue(context);
        
        return keyValue != null ? keyValue.toString() : "null";
    }
}
```

## 5. 数据流设计

### 5.1 完整请求数据流

```
@XkCacheable注解方法调用
    ↓
CacheAspect.aroundCacheable() → SpEL解析生成Key
    ↓  
DualLayerCacheManager.get() → L1查询(Caffeine)
    ↓ [Miss]
L2查询(Valkey) → [Hit] → L1回填 → 返回结果
    ↓ [Miss]  
回源方法执行 → 双层写入(L2优先) → 返回结果
```

### 5.2 一致性保证数据流

```
外部缓存更新 → Valkey失效通知
    ↓
Client Tracking监听器 → 解析失效Key  
    ↓
异步L1驱逐 → 一致性恢复完成
```

## 6. 边界护栏机制

### 6.1 复杂度控制边界
- **AOP切面层级**: 限制切面嵌套深度≤3层，避免调用栈过深
- **SpEL表达式复杂度**: 禁止复杂方法调用，只允许属性访问和简单运算
- **缓存键长度**: 限制缓存键长度≤255字符，避免存储开销
- **并发操作控制**: 单个缓存实例并发操作≤1000/s，防止资源耗尽

### 6.2 架构演进策略
- **接口稳定性**: 保持DualLayerCacheManager接口向后兼容
- **扩展点设计**: 提供CacheCustomizer接口支持自定义扩展
- **性能监控**: 关键路径全链路监控，支持性能调优
- **降级机制**: 多级降级策略，确保系统稳定性

## 实施约束

### 强制性技术要求
- **Java版本**: 必须使用Java 21+，确保虚拟线程和现代语法特性可用
- **Spring AOP版本**: 必须使用Spring AOP 6.1.3，确保切面处理的稳定性
- **Valkey版本**: 必须使用Valkey 8.0+，确保客户端缓存追踪功能可用
- **AspectJ版本**: 必须使用AspectJ 1.9.22，确保编译时织入的兼容性
- **Maven版本**: 必须使用Maven 3.9.6+，确保编译和依赖管理

### 架构模式约束
- **一致性策略**: 必须使用Cache-Aside模式，禁止Write-Through模式
- **L1/L2协调**: L2优先更新，L1被动失效，严禁反向操作
- **事务边界**: 缓存操作必须在事务边界外执行，避免事务回滚影响缓存状态
- **切面顺序**: 缓存切面必须在事务切面之后执行，Order值≥1000

### 性能指标要求
- **AOP切面开销**: 切面处理开销≤原方法执行时间的5%
- **双层协调延迟**: L1/L2协调总延迟≤15ms (P95)
- **一致性时间窗**: 缓存不一致窗口≤100ms
- **内存开销**: AOP代理对象内存开销≤原对象的10%
- **SpEL解析性能**: 表达式解析时间≤1ms

### 兼容性要求
- **Spring集成**: 与Spring Boot 3.4.5+完全兼容
- **事务管理**: 与Spring事务管理无冲突，支持@Transactional
- **序列化兼容**: 支持方法参数和返回值的序列化

### 错误处理约束
- **异常隔离**: 缓存异常不能影响业务逻辑正常执行
- **降级策略**: 缓存不可用时自动降级为直接方法调用
- **重试机制**: 网络异常时实现指数退避重试，最大重试3次
- **熔断保护**: 连续失败≥5次时启动熔断器，保护系统稳定性

### 约束违规后果
- **架构违反**: 数据一致性问题，缓存击穿风险，系统稳定性风险
- **性能不达标**: 响应时间劣化≥30%，用户体验下降
- **兼容性问题**: 与现有Spring应用集成失败，启动异常
- **事务冲突**: 缓存状态与数据库状态不一致

### 验证锚点
- **AOP性能测试**: `mvn test -Dtest=CacheAspectPerformanceTest`
- **一致性集成测试**: `mvn test -Dtest=DualLayerConsistencyTest`
- **事务兼容性测试**: `mvn test -Dtest=CacheTransactionCompatibilityTest`
- **错误处理测试**: `mvn test -Dtest=CacheErrorHandlingTest`
- **SpEL解析测试**: `mvn test -Dtest=SpelExpressionTest`
