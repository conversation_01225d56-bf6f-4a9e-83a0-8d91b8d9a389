✅ 测试环境清理完成
🔧 [DEBUG] 开始生成测试报告...
🔧 [DEBUG] 测试报告生成完成: {'status': 'completed', 'test_summary': {'total_tests': 59, 'passed_tests': 51, 'failed_tests': 8, 'success_rate': 86.4406779661017, 'test_duration': 3.37019}, 'api_breakdown': {'insert_line': {'total': 5, 'success': 3, 'success_rate': 60.0}, 'read_line': {'total': 3, 'success': 3, 'success_rate': 100.0}, 'update_line': {'total': 3, 'success': 3, 'success_rate': 100.0}, 'delete_line': {'total': 3, 'success': 2, 'success_rate': 66.66666666666666}, 'replace_all': {'total': 3, 'success': 3, 'success_rate': 100.0}, 'listdir': {'total': 4, 'success': 4, 'success_rate': 100.0}, 'find_in_files': {'total': 3, 'success': 3, 'success_rate': 100.0}, 'create_directory': {'total': 2, 'success': 2, 'success_rate': 100.0}, 'delete_directory': {'total': 2, 'success': 0, 'success_rate': 0.0}, 'delete_file': {'total': 3, 'success': 1, 'success_rate': 33.33333333333333}, 'copy_file': {'total': 2, 'success': 2, 'success_rate': 100.0}, 'create_file': {'total': 2, 'success': 2, 'success_rate': 100.0}, 'append_content': {'total': 2, 'success': 2, 'success_rate': 100.0}, 'prepend_content': {'total': 2, 'success': 1, 'success_rate': 50.0}, 'insert_multiple_lines': {'total': 2, 'success': 2, 'success_rate': 100.0}, 'delete_range': {'total': 2, 'success': 2, 'success_rate': 100.0}, 'duplicate_line': {'total': 2, 'success': 2, 'success_rate': 100.0}, 'clear_file': {'total': 1, 'success': 1, 'success_rate': 100.0}, 'read_full_content': {'total': 1, 'success': 1, 'success_rate': 100.0}, 'get_file_info': {'total': 1, 'success': 1, 'success_rate': 100.0}, 'replace_in_line': {'total': 2, 'success': 2, 'success_rate': 100.0}, 'replace_range': {'total': 1, 'success': 1, 'success_rate': 100.0}, 'insert_at_position': {'total': 1, 'success': 1, 'success_rate': 100.0}, 'find_text': {'total': 2, 'success': 2, 'success_rate': 100.0}, 'move_line': {'total': 1, 'success': 1, 'success_rate': 100.0}, 'search_content': {'total': 1, 'success': 1, 'success_rate': 100.0}, 'move_file': {'total': 1, 'success': 1, 'success_rate': 100.0}, 'truncate_file': {'total': 2, 'success': 2, 'success_rate': 100.0}}, 'client_id': 'mcp_client_1751637229_8684', 'timestamp': '2025-07-04T21:54:16.918162', 'detailed_results': [{'api': 'insert_line', 'test_cases': [{'api': 'insert_line', 'description': '插入到行后', 'status': 'error', 'execution_time': 0.023981, 'error_message': '第二重验证失败：抽象层验证未通过', 'verification_results': {'api_call': True, 'abstraction_verify': False, 'logs': False}}, {'api': 'insert_line', 'description': '插入 到行前', 'status': 'success', 'execution_time': 0.025904, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'insert_line', 'description': '插入空内容', 'status': 'success', 'execution_time': 0.023531, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'insert_line', 'description': '插入多行内容', 'status': 'success', 'execution_time': 0.028223, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'insert_line', 'description': '插入特殊字符', 'status': 'error', 'execution_time': 0.030472, 'error_message': '第 二重验证失败：抽象层验证未通过', 'verification_results': {'api_call': True, 'abstraction_verify': False, 'logs': False}}], 'success_count': 3, 'total_count': 5}, {'api': 'read_line', 'test_cases': [{'api': 'read_line', 'description': '读取单行', 'status': 'success', 'execution_time': 0.014166, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'read_line', 'description': '读取范围', 'status': 'success', 'execution_time': 0.010024, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'read_line', 'description': '读取全部', 'status': 'success', 'execution_time': 0.013112, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 3, 'total_count': 3}, {'api': 'update_line', 'test_cases': [{'api': 'update_line', 'description': '替换模式更新', 'status': 'success', 'execution_time': 0.028362, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'update_line', 'description': '合并模式更新', 'status': 'success', 'execution_time': 0.041265, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'update_line', 'description': '更新为空', 'status': 'success', 'execution_time': 0.035009, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 3, 'total_count': 3}, {'api': 'delete_line', 'test_cases': [{'api': 'delete_line', 'description': '删除单行', 'status': 'success', 'execution_time': 0.022603, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'delete_line', 'description': '删除多行', 'status': 'success', 'execution_time': 0.029532, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'delete_line', 'description': '删除超出范围', 'status': 'error', 'execution_time': 0.016617, 'error_message': '第二重验证失败：抽象层验证未通过', 'verification_results': {'api_call': True, 'abstraction_verify': False, 'logs': False}}], 'success_count': 2, 'total_count': 3}, {'api': 'replace_all', 'test_cases': [{'api': 'replace_all', 'description': '普通替换', 'status': 'success', 'execution_time': 0.096114, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'replace_all', 'description': '正则替换', 'status': 'success', 'execution_time': 0.038379, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'replace_all', 'description': '大小写不敏感', 'status': 'success', 'execution_time': 0.02676, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 3, 'total_count': 3}, {'api': 'listdir', 'test_cases': [{'api': 'listdir', 'description': '列出当前目录', 'status': 'success', 'execution_time': 0.00782, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'listdir', 'description': '递归列出', 'status': 'success', 'execution_time': 0.007485, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'listdir', 'description': '仅文件', 'status': 'success', 'execution_time': 0.007453, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'listdir', 'description': '仅目录', 'status': 'success', 'execution_time': 0.008449, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 4, 'total_count': 4}, {'api': 'find_in_files', 'test_cases': [{'api': 'find_in_files', 'description': '普通搜索', 'status': 'success', 'execution_time': 0.008449, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'find_in_files', 'description': '正则搜索', 'status': 'success', 'execution_time': 0.059883, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'find_in_files', 'description': '限制结果数', 'status': 'success', 'execution_time': 0.009855, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 3, 'total_count': 3}, {'api': 'create_directory', 'test_cases': [{'api': 'create_directory', 'description': '创建新目录', 'status': 'success', 'execution_time': 0.020693, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'create_directory', 'description': '创建嵌套目录', 'status': 'success', 'execution_time': 0.020921, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 2, 'total_count': 2}, {'api': 'delete_directory', 'test_cases': [{'api': 'delete_directory', 'description': '删除空目录', 'status': 'error', 'execution_time': 0.032982, 'error_message': '第二重验证失败：抽象层验证未通过', 'verification_results': {'api_call': True, 'abstraction_verify': False, 'logs': False}}, {'api': 'delete_directory', 'description': '递归删除', 'status': 'error', 'execution_time': 0.023771, 'error_message': '第二重验证失败：抽象层验证未通过', 'verification_results': {'api_call': True, 'abstraction_verify': False, 'logs': False}}], 'success_count': 0, 'total_count': 2}, {'api': 'delete_file', 'test_cases': [{'api': 'delete_file', 'description': '删除存在的文件(带备 份)', 'status': 'error', 'execution_time': 0.022698, 'error_message': '第二重验证失败：抽象层验证未通 过', 'verification_results': {'api_call': True, 'abstraction_verify': False, 'logs': False}}, {'api': 'delete_file', 'description': '删除存在的文件(不备份)', 'status': 'error', 'execution_time': 0.020885, 'error_message': '第二重验证失败：抽象层验证未通过', 'verification_results': {'api_call': True, 'abstraction_verify': False, 'logs': False}}, {'api': 'delete_file', 'description': '删除不存在的文件', 'status': 'success', 'execution_time': 0.014404, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 1, 'total_count': 3}, {'api': 'copy_file', 'test_cases': [{'api': 'copy_file', 'description': '复制文件', 'status': 'success', 'execution_time': 0.024632, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'copy_file', 'description': '覆盖复制', 'status': 'success', 'execution_time': 0.041205, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 2, 'total_count': 2}, {'api': 'create_file', 'test_cases': [{'api': 'create_file', 'description': '创建空文件', 'status': 'success', 'execution_time': 0.030243, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'create_file', 'description': '创建带内容文件', 'status': 'success', 'execution_time': 0.032088, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 2, 'total_count': 2}, {'api': 'append_content', 'test_cases': [{'api': 'append_content', 'description': '追加普通内容', 'status': 'success', 'execution_time': 0.043831, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'append_content', 'description': '追加多行内容', 'status': 'success', 'execution_time': 0.065661, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 2, 'total_count': 2}, {'api': 'prepend_content', 'test_cases': [{'api': 'prepend_content', 'description': '前置普通内容', 'status': 'success', 'execution_time': 0.02807, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'prepend_content', 'description': '前置多行内容', 'status': 'error', 'execution_time': 0.026944, 'error_message': '第二重验证失败：抽象层验证未通过', 'verification_results': {'api_call': True, 'abstraction_verify': False, 'logs': False}}], 'success_count': 1, 'total_count': 2}, {'api': 'insert_multiple_lines', 'test_cases': [{'api': 'insert_multiple_lines', 'description': ' 批量插入行', 'status': 'success', 'execution_time': 0.034322, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'insert_multiple_lines', 'description': '批量插入到前面', 'status': 'success', 'execution_time': 0.040044, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 2, 'total_count': 2}, {'api': 'delete_range', 'test_cases': [{'api': 'delete_range', 'description': '删除行范围', 'status': 'success', 'execution_time': 0.023038, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'delete_range', 'description': '删除单行范围', 'status': 'success', 'execution_time': 0.043015, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 2, 'total_count': 2}, {'api': 'duplicate_line', 'test_cases': [{'api': 'duplicate_line', 'description': '复制指定行', 'status': 'success', 'execution_time': 0.037396, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'duplicate_line', 'description': '复制中间行', 'status': 'success', 'execution_time': 0.075301, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 2, 'total_count': 2}, {'api': 'clear_file', 'test_cases': [{'api': 'clear_file', 'description': '清空文件', 'status': 'success', 'execution_time': 0.081899, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 1, 'total_count': 1}, {'api': 'read_full_content', 'test_cases': [{'api': 'read_full_content', 'description': '读取完整内 容', 'status': 'success', 'execution_time': 0.034782, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 1, 'total_count': 1}, {'api': 'get_file_info', 'test_cases': [{'api': 'get_file_info', 'description': '获取文件信息', 'status': 'success', 'execution_time': 0.01225, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 1, 'total_count': 1}, {'api': 'replace_in_line', 'test_cases': [{'api': 'replace_in_line', 'description': '行内普通替换', 'status': 'success', 'execution_time': 0.542871, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'replace_in_line', 'description': '行内正则替换', 'status': 'success', 'execution_time': 0.398824, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 2, 'total_count': 2}, {'api': 'replace_range', 'test_cases': [{'api': 'replace_range', 'description': '替换行范围', 'status': 'success', 'execution_time': 0.038725, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 1, 'total_count': 1}, {'api': 'insert_at_position', 'test_cases': [{'api': 'insert_at_position', 'description': '字符位置插入', 'status': 'success', 'execution_time': 0.057805, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 1, 'total_count': 1}, {'api': 'find_text', 'test_cases': [{'api': 'find_text', 'description': '普通文本查找', 'status': 'success', 'execution_time': 0.014752, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'find_text', 'description': '正则文本查找', 'status': 'success', 'execution_time': 0.017128, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 2, 'total_count': 2}, {'api': 'move_line', 'test_cases': [{'api': 'move_line', 'description': '移动行位置', 'status': 'success', 'execution_time': 0.053136, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 1, 'total_count': 1}, {'api': 'search_content', 'test_cases': [{'api': 'search_content', 'description': '多文档搜索', 'status': 'success', 'execution_time': 0.019056, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 1, 'total_count': 1}, {'api': 'move_file', 'test_cases': [{'api': 'move_file', 'description': '移动文件', 'status': 'success', 'execution_time': 0.037858, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 1, 'total_count': 1}, {'api': 'truncate_file', 'test_cases': [{'api': 'truncate_file', 'description': '截断文件', 'status': 'success', 'execution_time': 0.014874, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}, {'api': 'truncate_file', 'description': '截断为空', 'status': 'success', 'execution_time': 0.017912, 'error_message': None, 'verification_results': {'api_call': True, 'abstraction_verify': True, 'logs': True}}], 'success_count': 2, 'total_count': 2}], 'conclusion': '✅ 良好！V45抽象层基本实现，51/59个API测试通过，少量问题需要修复'}
✅ 测试完成！成功率: 86.4%
✅ [V45架构] V45测试使用主事件循环执行
✅ V45抽象层测试完成: 86.4%
[DEBUG_LOG] 21:54:20 [INFO] V45_TEST: V45抽象层测试完成，成功率: 86.4%
127.0.0.1 - - [04/Jul/2025 21:54:20] "POST /api/test_v45_abstraction HTTP/1.1" 200 -
127.0.0.1 - - [04/Jul/2025 21:54:21] "GET /api/strategy-routes HTTP/1.1" 200 -
127.0.0.1 - - [04/Jul/2025 21:54:23] "GET /api/cognitive-load HTTP/1.1" 200 -
127.0.0.1 - - [04/Jul/2025 21:54:23] "GET /api/intelligence-emergence HTTP/1.1" 200 -
127.0.0.1 - - [04/Jul/2025 21:54:26] "GET /api/cognitive-load HTTP/1.1" 200 -
127.0.0.1 - - [04/Jul/2025 21:54:27] "GET /api/strategy-routes HTTP/1.1" 200 -
127.0.0.1 - - [04/Jul/2025 21:54:27] "GET /api/intelligence-emergence HTTP/1.1" 200 -
127.0.0.1 - - [04/Jul/2025 21:54:31] "GET /api/cognitive-load HTTP/1.1" 200 -
127.0.0.1 - - [04/Jul/2025 21:54:33] "GET /api/intelligence-emergence HTTP/1.1" 200 -
127.0.0.1 - - [04/Jul/2025 21:54:35] "GET /api/strategy-routes HTTP/1.1" 200 -