你是一家大型电商公司的首席架构师，公司现有的订单系统面临以下挑战：

当前系统：单体架构，MySQL主从，Redis缓存，日处理订单100万
业务增长：预计明年双11峰值将达到1000万订单/小时
技术债务：代码耦合严重，部署困难，故障恢复时间长
新需求：需要支持海外业务、实时推荐、智能定价
任务要求
请完成以下5个核心任务，每个任务都有具体的评估维度：

任务1：架构设计文档生成
测试维度：结构化思维、技术深度、文档质量、多维度分析

题目：
设计一个支持千万级订单处理的新一代电商订单系统架构，要求：

系统架构设计：
从单体架构迁移到微服务架构的演进路径
分析3种不同的架构方案（如事件驱动、CQRS、六边形架构）
说明每种方案的优缺点和适用场景
技术选型决策：
数据库：分库分表策略 vs NewSQL vs 分布式数据库
缓存：多级缓存设计
消息队列：Kafka vs RocketMQ vs Pulsar
服务治理：Spring Cloud vs Istio vs Dapr
非功能性需求：
性能：如何达到1000万订单/小时的处理能力
可用性：如何实现99.99%的系统可用性
一致性：分布式事务处理策略
安全性：数据安全和访问控制
评估重点：

🔍 逻辑推理：架构决策的因果关系分析
📋 问题识别：能否发现现有架构的问题
🎯 方案对比：多种方案的系统性对比
📖 文档完整性：是否包含所有必要章节
🎨 格式规范性：文档结构和格式是否标准
任务2：API设计文档
测试维度：RESTful设计、参数描述、错误处理、文档标准

题目：
为新订单系统设计核心API接口，包括：

订单管理API：
POST /api/v1/orders - 创建订单
GET /api/v1/orders/{orderId} - 查询订单详情
PUT /api/v1/orders/{orderId}/status - 更新订单状态
GET /api/v1/orders - 查询订单列表（支持复杂筛选）
要求详细说明：
请求/响应参数的完整定义
HTTP状态码和错误处理机制
分页、排序、筛选的实现
幂等性设计
限流和安全控制
提供具体示例：
完整的请求/响应JSON示例
错误响应的格式和错误码定义
客户端调用的代码示例
评估重点：

🎯 RESTful设计：是否符合REST最佳实践
📋 参数描述：参数说明是否详细准确
⚠️ 错误处理：错误响应设计是否完善
📖 文档标准：是否符合OpenAPI等标准
任务3：性能分析与优化方案
测试维度：分析深度、数据驱动、优化建议具体性

题目：
当前订单系统在压测中发现以下性能问题：

性能数据：

订单创建接口：平均响应时间800ms，P99响应时间2.5s
订单查询接口：平均响应时间300ms，P99响应时间1.2s
数据库连接池：峰值使用率95%，频繁等待
Redis缓存：命中率65%，内存使用率85%
JVM GC：Full GC频率每小时3次，每次停顿200ms
分析要求：

根本原因分析：
分析每个性能问题的根本原因
识别系统瓶颈点和关键路径
分析问题之间的关联关系
优化方案设计：
提供具体的优化措施
预估每个优化措施的性能提升效果
制定优化实施的优先级和时间计划
监控体系设计：
设计关键性能指标（KPI）
制定监控告警策略
建立性能基线和目标
评估重点：

🔍 分析深度：能否深入分析性能瓶颈
📊 数据驱动：是否基于具体指标分析
🎯 优化建议：建议是否具体可执行
📈 监控方案：监控体系是否完整
任务4：安全威胁分析
测试维度：威胁识别、风险评估、防护方案

题目：
对新订单系统进行全面的安全威胁分析：

系统特点：

微服务架构，服务间通过HTTP/gRPC通信
使用JWT进行用户认证
敏感数据包括：用户信息、支付信息、订单数据
部署在公有云环境，使用Kubernetes
分析要求：

威胁建模：
识别系统面临的主要安全威胁
分析攻击向量和攻击路径
评估每种威胁的风险等级
安全防护设计：
设计多层次的安全防护体系
制定数据加密和脱敏策略
设计访问控制和权限管理
合规性考虑：
分析需要满足的合规要求（如GDPR、PCI DSS）
制定数据保护和隐私保护措施
评估重点：

🔍 威胁识别：能否全面识别安全威胁
📊 风险评估：风险等级评估是否准确
🛡️ 防护方案：安全防护是否完整
📋 合规性：是否考虑合规要求
任务5：系统重构计划
测试维度：重构策略、风险控制、实施步骤

题目：
制定从现有单体架构到微服务架构的重构计划：

现状分析：

单体应用：50万行Java代码，200+类
数据库：单一MySQL实例，100+张表
部署方式：传统物理机部署
团队规模：30人开发团队
重构要求：

重构策略设计：
制定分阶段的重构计划
设计服务拆分策略
制定数据迁移方案
风险控制措施：
识别重构过程中的主要风险
制定风险缓解措施
设计回滚策略
实施计划：
制定详细的实施时间表
分配团队资源和责任
设计测试和验证策略
评估重点：

🎯 重构策略：策略是否清晰可行
⚠️ 风险控制：风险识别和控制是否充分
📅 实施步骤：步骤是否详细具体
🧪 测试策略：测试保障是否完整