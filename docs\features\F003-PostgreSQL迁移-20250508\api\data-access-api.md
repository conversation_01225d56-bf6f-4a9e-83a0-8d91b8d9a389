---
title: PostgreSQL演进架构数据访问层API
document_id: F003-API-001
document_type: API文档
category: 数据库迁移
scope: XKC-CORE
keywords: [PostgreSQL, 演进架构, 数据访问层, API, 服务抽象层, 配置驱动]
created_date: 2025-05-08
updated_date: 2025-01-15
status: 已批准
version: 2.0
authors: [系统架构组, AI助手]
affected_features:
  - F003
related_docs:
  - ../requirements/migration-requirements.md
  - ../design/migration-design.md
  - ../design/postgresql-evolution-architecture-integration.md
  - ../../../common/architecture/patterns/postgresql-evolution-implementation-guide.md
  - ../../../common/middleware/postgresql/integration-guide.md
---

# PostgreSQL演进架构数据访问层API

## API概述

本文档描述了XKC-CORE项目中支持持续演进架构的PostgreSQL数据访问层API。该API设计基于服务抽象层和配置驱动原则，支持从单体架构到微服务架构的平滑演进。数据访问层包括服务接口、数据访问抽象、实体类和配置类等组件，提供统一的数据访问能力，支持本地和远程数据访问的透明切换。

## 基本信息

- **API类型**: 演进架构内部服务
- **版本**: 2.0
- **状态**: 已批准
- **负责人**: 系统架构组
- **联系方式**: <EMAIL>
- **架构模式**: 支持MONOLITHIC、MODULAR、HYBRID、MICROSERVICES
- **演进能力**: 支持配置驱动的架构模式切换

## 架构设计原则

### 核心设计原则

1. **透明演进原则**: 业务代码在架构演进过程中保持不变
2. **配置驱动原则**: 通过配置文件控制架构模式和服务调用方式
3. **渐进实施原则**: 分阶段引入抽象层，避免初期复杂度过高
4. **PostgreSQL优化原则**: 充分利用PostgreSQL特性，同时保持演进能力

### 分层架构

```
业务层 (Business Layer)
    ↓
服务抽象层 (Service Abstraction Layer)
    ↓
数据访问抽象层 (Data Access Abstraction Layer)
    ↓
实现层 (Implementation Layer)
    ↓
数据层 (Data Layer - PostgreSQL)
```

## 服务抽象层API

### ServiceInterface注解

**注解名**: `@ServiceInterface`
**包名**: `org.xkong.cloud.business.internal.core.annotation`
**描述**: 服务接口标记注解，用于标识可演进的服务接口

#### 属性

| 属性名 | 类型 | 描述 | 默认值 |
|-------|------|------|-------|
| value | String | 服务名称 | 无 |
| description | String | 服务描述 | "" |
| remoteCapable | boolean | 是否支持远程调用 | true |

#### 使用示例

```java
@ServiceInterface("user-management")
public interface UserManagementService {
    // 服务方法定义
}
```

### UserManagementService接口

**接口名**: `UserManagementService`
**包名**: `org.xkong.cloud.business.internal.core.service`
**描述**: 用户管理服务接口，支持本地和远程调用的透明切换

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| createUser | CreateUserRequest request | User | 创建用户 |
| getUserById | Long userId | Optional<User> | 根据ID获取用户 |
| getUserByUsername | String username | Optional<User> | 根据用户名获取用户 |
| getUsersByStatus | String status | List<User> | 根据状态获取用户列表 |
| updateUser | User user | User | 更新用户信息 |
| deleteUser | Long userId | void | 删除用户 |
| userExists | Long userId | boolean | 检查用户是否存在 |
| getUserCount | 无 | long | 获取用户总数 |

## 数据访问抽象层API

### DataAccessService接口

**接口名**: `DataAccessService<T, ID>`
**包名**: `org.xkong.cloud.business.internal.core.service`
**描述**: 统一的数据访问服务接口，支持本地和远程数据访问的透明切换

#### 泛型参数

- `T`: 实体类型
- `ID`: 主键类型

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| save | T entity | T | 保存实体 |
| saveAll | List<T> entities | List<T> | 批量保存实体 |
| findById | ID id | Optional<T> | 根据ID查找实体 |
| findAll | 无 | List<T> | 查找所有实体 |
| findByCondition | QueryCondition condition | List<T> | 根据条件查找实体 |
| deleteById | ID id | void | 根据ID删除实体 |
| deleteAll | List<ID> ids | void | 批量删除实体 |
| count | 无 | long | 统计实体数量 |
| existsById | ID id | boolean | 检查实体是否存在 |

### QueryCondition类

**类名**: `QueryCondition`
**包名**: `org.xkong.cloud.business.internal.core.service`
**描述**: 查询条件封装类，提供统一的查询条件构建接口

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| builder | 无 | QueryCondition | 创建查询条件构建器 |
| eq | String field, Object value | QueryCondition | 等于条件 |
| like | String field, String value | QueryCondition | 模糊查询条件 |
| gt | String field, Object value | QueryCondition | 大于条件 |
| lt | String field, Object value | QueryCondition | 小于条件 |
| in | String field, List<?> values | QueryCondition | 在列表中条件 |
| orderByAsc | String field | QueryCondition | 升序排序 |
| orderByDesc | String field | QueryCondition | 降序排序 |
| limit | int limit | QueryCondition | 限制结果数量 |
| offset | int offset | QueryCondition | 偏移量 |

## 实体类API

### User实体

**类名**: `User`
**包名**: `org.xkong.cloud.business.internal.core.entity`
**描述**: 用户实体类，表示系统中的用户，支持演进架构的数据访问
**表名**: `user_management.user` (注意使用单数形式，符合项目规范，并放在`user_management` Schema中)

#### 属性

| 属性名 | 类型 | 描述 | 注解 |
|-------|------|------|------|
| userId | Long | 用户ID (使用UID生成器) | @Id |
| username | String | 用户名 | @Column(name = "username", nullable = false, length = 100, unique = true) |
| email | String | 邮箱地址 | @Column(name = "email", nullable = false, length = 255) |
| status | String | 用户状态 | @Column(name = "status", nullable = false, length = 20) |
| createdAt | LocalDateTime | 创建时间 | @Column(name = "created_at", nullable = false) |
| updatedAt | LocalDateTime | 更新时间 | @Column(name = "updated_at", nullable = false) |
| location | Integer | 位置 | @Column(name = "location") |
| mainIndustry | Integer | 主要行业 | @Column(name = "main_industry") |
| mainCareer | Integer | 主要职业 | @Column(name = "main_career") |
| messages | List<UserMessage> | 用户消息 | @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true) |
| careIndustries | Set<Industry> | 关注的行业 | @ManyToMany, @JoinTable(name = "user_industry", schema = "user_management") |
| careCareers | Set<Career> | 关注的职业 | @ManyToMany, @JoinTable(name = "user_career", schema = "user_management") |

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| getId | 无 | String | 获取用户ID |
| setId | String id | void | 设置用户ID |
| getName | 无 | String | 获取用户名称 |
| setName | String name | void | 设置用户名称 |
| getMailAddress | 无 | String | 获取邮箱地址 |
| setMailAddress | String mailAddress | void | 设置邮箱地址 |
| getLocation | 无 | Integer | 获取位置 |
| setLocation | Integer location | void | 设置位置 |
| getRegistTime | 无 | LocalDateTime | 获取注册时间 |
| setRegistTime | LocalDateTime registTime | void | 设置注册时间 |
| getMainIndustry | 无 | Integer | 获取主要行业 |
| setMainIndustry | Integer mainIndustry | void | 设置主要行业 |
| getMainCareer | 无 | Integer | 获取主要职业 |
| setMainCareer | Integer mainCareer | void | 设置主要职业 |
| getMessages | 无 | List<UserMessage> | 获取用户消息 |
| setMessages | List<UserMessage> messages | void | 设置用户消息 |
| getCareIndustries | 无 | Set<Industry> | 获取关注的行业 |
| setCareIndustries | Set<Industry> careIndustries | void | 设置关注的行业 |
| getCareCareers | 无 | Set<Career> | 获取关注的职业 |
| setCareCareers | Set<Career> careCareers | void | 设置关注的职业 |
| addMessage | UserMessage message | void | 添加用户消息 |
| removeMessage | UserMessage message | void | 移除用户消息 |
| addCareIndustry | Industry industry | void | 添加关注的行业 |
| removeCareIndustry | Industry industry | void | 移除关注的行业 |
| addCareCareer | Career career | void | 添加关注的职业 |
| removeCareCareer | Career career | void | 移除关注的职业 |

### Industry实体

**类名**: `Industry`
**包名**: `org.xkong.cloud.business.internal.core.entity`
**描述**: 行业实体类，表示系统中的行业
**表名**: `user_management.industry` (注意使用单数形式，符合项目规范，并放在`user_management` Schema中)

#### 属性

| 属性名 | 类型 | 描述 | 注解 |
|-------|------|------|------|
| id | Integer | 行业ID | @Id, @GeneratedValue(strategy = GenerationType.IDENTITY) |
| name | String | 行业名称 | @Column(name = "name", nullable = false, length = 100) |
| description | String | 行业描述 | @Column(name = "description") |
| users | Set<User> | 关注该行业的用户 | @ManyToMany(mappedBy = "careIndustries") | <!-- 注意：关联表名应为user_management.user_industry，符合单数形式命名规范，并放在user_management Schema中 -->

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| getId | 无 | Integer | 获取行业ID |
| setId | Integer id | void | 设置行业ID |
| getName | 无 | String | 获取行业名称 |
| setName | String name | void | 设置行业名称 |
| getDescription | 无 | String | 获取行业描述 |
| setDescription | String description | void | 设置行业描述 |
| getUsers | 无 | Set<User> | 获取关注该行业的用户 |
| setUsers | Set<User> users | void | 设置关注该行业的用户 |

### Career实体

**类名**: `Career`
**包名**: `org.xkong.cloud.business.internal.core.entity`
**描述**: 职业实体类，表示系统中的职业
**表名**: `user_management.career` (注意使用单数形式，符合项目规范，并放在`user_management` Schema中)

#### 属性

| 属性名 | 类型 | 描述 | 注解 |
|-------|------|------|------|
| id | Integer | 职业ID | @Id, @GeneratedValue(strategy = GenerationType.IDENTITY) |
| name | String | 职业名称 | @Column(name = "name", nullable = false, length = 100) |
| description | String | 职业描述 | @Column(name = "description") |
| users | Set<User> | 关注该职业的用户 | @ManyToMany(mappedBy = "careCareers") | <!-- 注意：关联表名应为user_management.user_career，符合单数形式命名规范，并放在user_management Schema中 -->

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| getId | 无 | Integer | 获取职业ID |
| setId | Integer id | void | 设置职业ID |
| getName | 无 | String | 获取职业名称 |
| setName | String name | void | 设置职业名称 |
| getDescription | 无 | String | 获取职业描述 |
| setDescription | String description | void | 设置职业描述 |
| getUsers | 无 | Set<User> | 获取关注该职业的用户 |
| setUsers | Set<User> users | void | 设置关注该职业的用户 |

## 实现层API

### 本地服务实现

#### LocalUserManagementService类

**类名**: `LocalUserManagementService`
**包名**: `org.xkong.cloud.business.internal.core.service.impl`
**描述**: 用户管理服务本地实现，基于数据访问抽象层
**条件**: `@ConditionalOnProperty(name = "xkong.services.user-management.mode", havingValue = "LOCAL", matchIfMissing = true)`

#### LocalUserDataAccessService类

**类名**: `LocalUserDataAccessService`
**包名**: `org.xkong.cloud.business.internal.core.service.impl`
**描述**: 基于JPA的本地用户数据访问服务实现
**条件**: `@ConditionalOnProperty(name = "xkong.services.user-management.data-access", havingValue = "LOCAL", matchIfMissing = true)`

### 远程服务实现（预留）

#### RemoteUserManagementService类

**类名**: `RemoteUserManagementService`
**包名**: `org.xkong.cloud.business.internal.core.service.impl`
**描述**: 用户管理服务远程实现，基于gRPC调用
**条件**: `@ConditionalOnProperty(name = "xkong.services.user-management.mode", havingValue = "REMOTE")`

### 传统Repository接口（兼容性保留）

#### UserRepository接口

**接口名**: `UserRepository`
**包名**: `org.xkong.cloud.business.internal.core.repository`
**描述**: 用户仓库接口，提供用户实体的数据访问方法（仅在本地模式下使用）

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| findByEmail | String email | Optional<User> | 根据邮箱地址查找用户 |
| findByUsername | String username | Optional<User> | 根据用户名查找用户 |
| findByStatus | String status | List<User> | 根据状态查找用户 |
| findByCreatedAtAfter | LocalDateTime date | List<User> | 查找指定日期之后创建的用户 |
| findByMainIndustry | Integer industryId | List<User> | 根据主要行业查找用户 |
| findByMainCareer | Integer careerId | List<User> | 根据主要职业查找用户 |
| findByCareIndustriesId | Integer industryId | List<User> | 查找关注指定行业的用户 |

### IndustryRepository接口

**接口名**: `IndustryRepository`
**包名**: `org.xkong.cloud.business.internal.core.repository`
**描述**: 行业仓库接口，提供行业实体的数据访问方法

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| findByName | String name | Optional<Industry> | 根据名称查找行业 |
| findByNameContaining | String keyword | List<Industry> | 查找名称包含指定关键字的行业 |
| findByUsersId | String userId | List<Industry> | 查找指定用户关注的行业 |

### CareerRepository接口

**接口名**: `CareerRepository`
**包名**: `org.xkong.cloud.business.internal.core.repository`
**描述**: 职业仓库接口，提供职业实体的数据访问方法

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| findByName | String name | Optional<Career> | 根据名称查找职业 |
| findByNameContaining | String keyword | List<Career> | 查找名称包含指定关键字的职业 |
| findByUsersId | String userId | List<Career> | 查找指定用户关注的职业 |

## 配置层API

### ServiceConfiguration类

**类名**: `ServiceConfiguration`
**包名**: `org.xkong.cloud.business.internal.core.config`
**描述**: 服务配置类，控制服务的部署和调用方式

#### 枚举类型

| 枚举名 | 值 | 描述 |
|-------|---|------|
| ArchitectureMode | MONOLITHIC, MODULAR, HYBRID, MICROSERVICES | 架构模式 |
| DeploymentMode | LOCAL, REMOTE, HYBRID | 部署模式 |
| DataAccessMode | LOCAL, REMOTE, DISTRIBUTED | 数据访问模式 |
| Protocol | LOCAL_CALL, GRPC, HTTP | 通信协议 |

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| isLocal | String serviceName | boolean | 判断服务是否为本地模式 |
| getDataAccessMode | String serviceName | DataAccessMode | 获取数据访问模式 |
| getArchitectureMode | 无 | ArchitectureMode | 获取当前架构模式 |

### PostgreSQLConfig类

**类名**: `PostgreSQLConfig`
**包名**: `org.xkong.cloud.business.internal.core.config`
**描述**: PostgreSQL配置类，提供数据源和JPA相关的配置，支持演进架构

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| dataSource | 无 | DataSource | 创建并配置数据源 |
| entityManagerFactory | EntityManagerFactoryBuilder builder, DataSource dataSource | LocalContainerEntityManagerFactoryBean | 创建并配置EntityManagerFactory |
| transactionManager | EntityManagerFactory entityManagerFactory | PlatformTransactionManager | 创建并配置TransactionManager |
| jpaProperties | 无 | Map<String, Object> | 获取JPA属性 |
| getArchitectureMode | 无 | ArchitectureMode | 获取当前架构模式 |
| isEvolutionArchitectureEnabled | 无 | boolean | 判断是否启用演进架构支持 |

### PostgreSQLSchemaEvolutionManager类

**类名**: `PostgreSQLSchemaEvolutionManager`
**包名**: `org.xkong.cloud.business.internal.core.config`
**描述**: PostgreSQL Schema演进管理器，支持从单体到微服务的Schema演进

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| prepareForEvolution | 无 | void | 根据架构模式准备Schema |
| schemaExists | String schemaName | boolean | 检查Schema是否存在 |
| getAllSchemas | 无 | List<String> | 获取当前所有Schema |

## 使用示例

### 演进架构服务使用

```java
@RestController
@RequestMapping("/api/users")
public class UserController {

    @Autowired
    private UserManagementService userManagementService; // 自动注入，支持本地/远程透明切换

    @PostMapping
    public ResponseEntity<User> createUser(@RequestBody CreateUserRequest request) {
        User user = userManagementService.createUser(request);
        return ResponseEntity.ok(user);
    }

    @GetMapping("/{id}")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        Optional<User> user = userManagementService.getUserById(id);
        return user.map(ResponseEntity::ok)
                  .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/status/{status}")
    public ResponseEntity<List<User>> getUsersByStatus(@PathVariable String status) {
        List<User> users = userManagementService.getUsersByStatus(status);
        return ResponseEntity.ok(users);
    }
}
```

### 数据访问抽象层使用

```java
@Service
@ConditionalOnProperty(name = "xkong.services.user-management.mode", havingValue = "LOCAL", matchIfMissing = true)
public class LocalUserManagementService implements UserManagementService {

    @Autowired
    private DataAccessService<User, Long> userDataAccess; // 抽象的数据访问服务

    @Autowired
    private UidGenerator uidGenerator;

    @Override
    @Transactional
    public User createUser(CreateUserRequest request) {
        User user = new User();
        user.setUserId(uidGenerator.getUID());
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setStatus("ACTIVE");
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        return userDataAccess.save(user);
    }

    @Override
    public Optional<User> getUserByUsername(String username) {
        QueryCondition condition = QueryCondition.builder()
            .eq("username", username)
            .limit(1);

        List<User> users = userDataAccess.findByCondition(condition);
        return users.isEmpty() ? Optional.empty() : Optional.of(users.get(0));
    }
}
```

### 配置驱动示例

```yaml
# application-evolution.yml
xkong:
  services:
    architecture-mode: MONOLITHIC  # 当前为单体架构

    user-management:
      mode: LOCAL                   # 本地服务模式
      data-access: LOCAL           # 本地数据访问
      protocol: LOCAL_CALL         # 本地方法调用

    # 未来微服务配置示例
    # user-management:
    #   mode: REMOTE
    #   data-access: REMOTE
    #   protocol: GRPC
    #   address: "user-service:8081"
```

### 传统Repository使用（兼容性）

```java
@Service
public class LegacyUserService {
    @Autowired
    private UserRepository userRepository; // 传统JPA Repository

    public List<User> findUsersByStatus(String status) {
        return userRepository.findByStatus(status);
    }

    public List<User> findRecentUsers(LocalDateTime date) {
        return userRepository.findByCreatedAtAfter(date);
    }
}
```

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|-------|-----|---------|
| 10001 | 数据库连接失败 | 检查数据库连接参数是否正确，数据库服务是否正常运行 |
| 10002 | 必需的KV参数缺失 | 确保在xkongcloud-service-center中配置了所有必需的PostgreSQL参数 |
| 10003 | 实体验证失败 | 检查实体属性是否符合约束条件 |
| 10004 | 查询执行超时 | 优化查询，添加适当的索引 |
| 10005 | 数据库锁等待超时 | 检查是否存在长事务或死锁情况 |
| 10006 | 服务配置错误 | 检查xkong.services配置是否正确 |
| 10007 | 架构模式不支持 | 确认当前架构模式是否支持所请求的操作 |
| 10008 | 远程服务调用失败 | 检查远程服务地址和网络连接 |
| 10009 | Schema演进失败 | 检查数据库权限和Schema创建权限 |

## 注意事项

### 演进架构相关

- **配置驱动**: 所有服务模式切换必须通过配置文件控制，严禁在代码中硬编码架构模式
- **透明演进**: 业务代码应该只依赖服务接口，不直接依赖具体实现
- **渐进实施**: 建议从单体架构开始，逐步演进到微服务架构
- **测试覆盖**: 每种架构模式都需要有对应的测试用例

### 数据库相关

- 所有数据库连接参数必须通过KVParamService从xkongcloud-service-center获取，严禁在代码或本地配置文件中硬编码
- 生产环境必须设置DDL自动生成策略为none或validate，所有数据库结构变更应通过数据库迁移工具管理
- Schema演进管理器会根据架构模式自动创建相应的Schema，确保数据库用户有足够的权限
- 在迁移过程中，需要仔细设计数据类型映射，确保Cassandra和PostgreSQL的数据类型兼容

### 性能考虑

- 本地模式下优先使用JPA Repository的批量操作
- 远程模式下注意网络延迟，合理使用批量操作和缓存
- 根据架构模式调整连接池大小和超时设置
- 定期监控服务调用模式和性能指标

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 2.0 | 2025-01-15 | 重构为演进架构兼容的API设计，增加服务抽象层和配置驱动机制 | AI助手 |
| 1.1 | 2025-06-02 | 修改实体类表名定义，明确指定Schema | AI助手 |
| 1.0 | 2025-05-08 | 初始版本 | AI助手 |
