---
title: XKongCloud-Business-Internal-Core 微服务演进包重构方案
document_id: F003-DESIGN-003
document_type: 设计文档
category: 数据库迁移
scope: F003-PostgreSQL迁移
keywords: [微服务演进, 包重构, 业务组织, core重构, PostgreSQL迁移]
created_date: 2025-01-15
updated_date: 2025-01-15
status: 实施方案
version: 1.0
authors: [AI助手]
affected_features:
  - F003 # PostgreSQL迁移
related_docs:
  - ./migration-design.md
  - ./postgresql-evolution-architecture-integration.md
  - ../plan/implementation-plan.md
  - ../../../common/architecture/principles/continuous-evolution-architecture.md
  - ../../../common/architecture/patterns/service-evolution-patterns.md
  - ../../../common/architecture/diagrams/architecture-evolution-roadmap.md
---

# XKongCloud-Business-Internal-Core 微服务演进包重构方案

## 摘要

本文档定义了`xkongcloud-business-internal-core`项目的完整包重构方案，支持10+业务组演进的包架构设计。该重构从零开始构建，采用四层技术架构，支持从单机部署到大规模分布式集群的平滑演进，同时为未来万亿级数据处理预留扩展能力。

## 设计原则

### 1. 技术优先原则
- 不预设任何具体业务逻辑
- 提供纯技术架构框架
- 支持业务持续迭代和动态添加

### 2. 演进友好原则
- 从第1步开始就具备完整架构
- 支持渐进式复杂度增长
- 保持向后兼容性

### 3. 扩展性原则
- 支持10+业务组独立演进
- 预留大规模数据处理能力
- 支持分库分表架构演进

## XKongCloud-Business-Internal-Core 包重构设计

### 当前项目状态

**项目名称**：`xkongcloud-business-internal-core`
**当前状态**：从零开始，无现有业务代码需要迁移
**重构目标**：建立支持10+业务组演进的完整包架构
**技术栈**：Spring Boot 3.4.5, PostgreSQL(外部), RabbitMQ, Valkey, JPA, gRPC

### 四层包重构架构

```
xkongcloud-business-internal-core/
src/main/java/org.xkong.cloud.business.internal.core/
├── shared/                           # L1: 共享基础设施层
│   ├── config/                       # 配置管理
│   │   ├── database/                 # 数据库配置
│   │   │   ├── PostgreSQLConfig.java
│   │   │   ├── DataSourceConfig.java
│   │   │   └── JpaConfig.java
│   │   ├── middleware/               # 中间件配置
│   │   │   ├── GrpcClientConfig.java
│   │   │   └── UidGeneratorConfig.java
│   │   └── evolution/                # 演进架构配置
│   │       ├── ServiceConfiguration.java
│   │       └── EvolutionConfig.java
│   ├── exception/                    # 异常处理
│   │   ├── GlobalExceptionHandler.java
│   │   ├── BusinessException.java
│   │   └── SystemException.java
│   ├── validator/                    # 验证框架
│   │   ├── ParameterValidator.java
│   │   └── annotation/
│   │       └── ValidParameter.java
│   ├── evolution/                    # 演进架构核心
│   │   ├── annotation/               # 演进注解
│   │   │   ├── ServiceInterface.java
│   │   │   ├── BusinessGroup.java
│   │   │   └── EvolutionReady.java
│   │   ├── proxy/                    # 服务代理
│   │   │   ├── ServiceProxy.java
│   │   │   ├── ServiceLocator.java
│   │   │   └── EvolutionManager.java
│   │   └── coordination/             # 协调机制
│   │       ├── CrossGroupCoordinator.java
│   │       └── EventBus.java
│   ├── infrastructure/               # 基础设施组件
│   │   ├── uid/                      # UID生成器
│   │   ├── cache/                    # 缓存服务
│   │   ├── messaging/                # 消息服务
│   │   └── monitoring/               # 监控服务
│   └── common/                       # 通用工具
│       ├── util/                     # 工具类
│       ├── constant/                 # 常量定义
│       ├── enums/                    # 枚举类型
│       └── dto/                      # 通用DTO
├── platform/                         # L2: 平台核心（现有集成结构）
│   ├── controller/                   # REST控制器
│   │   └── MiddlewareController.java # 现有的中间件控制器
│   ├── service/                      # 业务服务
│   │   ├── KVParamService.java       # 现有的KV参数服务
│   │   └── impl/
│   │       └── KVParamServiceImpl.java
│   ├── repository/                   # 数据访问
│   │   └── custom/                   # 自定义查询
│   └── entity/                       # 实体类
│       └── base/                     # 基础实体
│           ├── BaseEntity.java
│           ├── AuditableEntity.java
│           └── VersionedEntity.java
├── groups/                           # L3: 业务组容器（动态扩展）
│   ├── README.md                     # 说明文档
│   └── template/                     # 业务组模板
│       ├── controller/
│       ├── service/
│       ├── repository/
│       ├── entity/
│       └── domain/
├── api/                              # L4: API接口层
│   ├── gateway/                      # API网关
│   │   ├── RouteConfiguration.java
│   │   └── SecurityConfiguration.java
│   ├── external/                     # 外部API
│   ├── internal/                     # 内部API
│   └── dto/                          # API数据传输对象
│       ├── request/                  # 请求DTO
│       ├── response/                 # 响应DTO
│       └── converter/                # DTO转换器
└── integration/                      # L5: 集成层
    ├── external/                     # 外部系统集成
    ├── messaging/                    # 消息集成
    └── monitoring/                   # 监控集成
```

## 数据库Schema技术设计

### Schema组织策略

```sql
-- 基础设施Schema
CREATE SCHEMA IF NOT EXISTS infra_uid;          -- UID生成器
CREATE SCHEMA IF NOT EXISTS infra_cache;        -- 缓存相关
CREATE SCHEMA IF NOT EXISTS infra_messaging;    -- 消息队列相关
CREATE SCHEMA IF NOT EXISTS infra_monitoring;   -- 监控相关

-- 通用功能Schema
CREATE SCHEMA IF NOT EXISTS common_config;      -- 系统配置
CREATE SCHEMA IF NOT EXISTS common_audit;       -- 审计日志
CREATE SCHEMA IF NOT EXISTS common_security;    -- 安全相关

-- 平台核心Schema
CREATE SCHEMA IF NOT EXISTS business_platform;  -- 平台核心（现有集成结构）

-- 业务组Schema容器（预留，等待业务迭代时动态创建）
-- 命名规范：business_{group_name}
-- 示例：CREATE SCHEMA IF NOT EXISTS business_group_a;
-- 示例：CREATE SCHEMA IF NOT EXISTS business_group_b;

-- 协调Schema
CREATE SCHEMA IF NOT EXISTS coordination_events;    -- 跨组事件
CREATE SCHEMA IF NOT EXISTS coordination_process;   -- 业务流程协调
```

## 演进架构核心实现

### 1. 服务接口注解系统

```java
/**
 * 完整的服务接口注解
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ServiceInterface {
    String value();                                    // 服务名称
    String businessGroup() default "platform";        // 业务组
    EvolutionLevel evolutionLevel() default EvolutionLevel.BUSINESS_SERVICE;
    boolean remoteCapable() default true;             // 是否支持远程调用
    String[] dependencies() default {};               // 依赖的其他服务
}

/**
 * 业务组注解
 */
@Target(ElementType.PACKAGE)
@Retention(RetentionPolicy.RUNTIME)
public @interface BusinessGroup {
    String value();                                    // 业务组名称
    String description() default "";                  // 描述
    String[] schemas() default {};                    // 关联的数据库Schema
    boolean independentEvolution() default true;      // 是否支持独立演进
}
```

### 2. 业务组动态管理

```java
/**
 * 业务组管理器
 */
@Component
public class BusinessGroupManager {
    
    @Autowired
    private DataSource dataSource;
    
    /**
     * 动态创建业务组
     */
    public void createBusinessGroup(String groupName, BusinessGroupConfig config) {
        // 1. 创建数据库Schema
        createDatabaseSchema(groupName);
        
        // 2. 创建包结构
        createPackageStructure(groupName);
        
        // 3. 注册到配置中心
        registerGroupConfiguration(groupName, config);
        
        // 4. 初始化基础组件
        initializeGroupComponents(groupName);
    }
    
    private void createDatabaseSchema(String groupName) {
        String schemaName = "business_" + groupName.toLowerCase();
        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute("CREATE SCHEMA IF NOT EXISTS " + schemaName);
        } catch (SQLException e) {
            throw new RuntimeException("Failed to create schema: " + schemaName, e);
        }
    }
}
```

## 配置驱动的架构管理

### 1. 动态配置获取机制（生产环境）

```java
/**
 * 从XKC-CENTER动态获取配置的架构配置
 */
@Component
public class DynamicArchitectureConfiguration {

    @Autowired
    private KVParamService kvParamService;

    /**
     * 动态获取业务组配置
     */
    public BusinessGroupConfig getBusinessGroupConfig(String groupName) {
        BusinessGroupConfig config = new BusinessGroupConfig();

        // 从center获取业务组配置
        config.setEnabled(kvParamService.getBooleanParam("business.groups." + groupName + ".enabled", false));
        config.setMode(kvParamService.getStringParam("business.groups." + groupName + ".mode", "LOCAL"));
        config.setSchemas(kvParamService.getListParam("business.groups." + groupName + ".schemas"));

        return config;
    }

    /**
     * 动态获取演进架构配置
     */
    public EvolutionConfig getEvolutionConfig() {
        EvolutionConfig config = new EvolutionConfig();

        config.setEnabled(kvParamService.getBooleanParam("xkong.evolution.enabled", true));
        config.setMode(kvParamService.getStringParam("xkong.evolution.mode", "MONOLITHIC"));

        return config;
    }
}
```

### 2. 配置热更新支持

```java
/**
 * 配置变更监听器
 */
@Component
public class ArchitectureConfigurationListener {

    @EventListener
    public void onConfigurationChange(ConfigurationChangeEvent event) {
        if (event.getKey().startsWith("business.groups.")) {
            // 业务组配置变更
            handleBusinessGroupConfigChange(event);
        } else if (event.getKey().startsWith("xkong.evolution.")) {
            // 演进架构配置变更
            handleEvolutionConfigChange(event);
        }
    }

    private void handleBusinessGroupConfigChange(ConfigurationChangeEvent event) {
        String groupName = extractGroupName(event.getKey());
        BusinessGroupManager.getInstance().reloadGroupConfiguration(groupName);
    }
}
```

### 3. 外部PostgreSQL配置

```yaml
# 生产环境配置（从center获取）
# 实际配置通过KVParamService动态获取，以下仅为示例格式
postgresql:
  host: ${POSTGRESQL_HOST}  # 从center获取
  port: ${POSTGRESQL_PORT}  # 从center获取
  database: ${POSTGRESQL_DATABASE}  # 从center获取
  username: ${POSTGRESQL_USERNAME}  # 从center获取
  password: ${POSTGRESQL_PASSWORD}  # 从center获取
```

## 远程Docker测试策略

### 1. 测试环境约束

**环境特点**：
- Windows开发环境 + 远程Linux Docker (sb.sn.cn)
- 每次测试都是全新Docker容器环境
- 无法直接访问外部center服务
- 需要通过SSH隧道进行连接

### 2. 测试配置模拟策略

```java
/**
 * 模拟KVParamService用于测试
 */
@TestConfiguration
@Profile("remote-docker-test")
public class MockKVParamServiceConfiguration {

    @Bean
    @Primary
    public KVParamService mockKVParamService() {
        return new MockKVParamService();
    }

    @Component
    public static class MockKVParamService implements KVParamService {

        private final Map<String, String> mockParams = new HashMap<>();

        @PostConstruct
        public void initMockParams() {
            // 初始化测试用的配置参数
            mockParams.put("business.groups.user.enabled", "true");
            mockParams.put("business.groups.user.mode", "LOCAL");
            mockParams.put("business.groups.order.enabled", "true");
            mockParams.put("business.groups.order.mode", "LOCAL");
            mockParams.put("xkong.evolution.enabled", "true");
            mockParams.put("xkong.evolution.mode", "MONOLITHIC");
        }

        @Override
        public String getStringParam(String key, String defaultValue) {
            return mockParams.getOrDefault(key, defaultValue);
        }

        @Override
        public Boolean getBooleanParam(String key, Boolean defaultValue) {
            String value = mockParams.get(key);
            return value != null ? Boolean.valueOf(value) : defaultValue;
        }

        @Override
        public List<String> getListParam(String key) {
            String value = mockParams.get(key);
            return value != null ? Arrays.asList(value.split(",")) : Collections.emptyList();
        }
    }
}
```

### 3. 业务组远程测试管理器

```java
/**
 * 远程Docker环境下的业务组测试管理器
 */
@Component
public class RemoteDockerBusinessGroupTestManager {

    /**
     * 单业务组远程Docker测试
     */
    public TestResult runSingleGroupRemoteTest(String groupName, TestConfiguration config) {
        // 1. 准备测试配置（模拟center配置）
        MockCenterConfiguration mockConfig = prepareMockCenterConfig(groupName, config);

        // 2. 启动远程Docker测试环境
        RemoteDockerTestEnvironment testEnv = RemoteDockerTestEnvironment.builder()
            .sshHost("sb.sn.cn")
            .sshUser("long")
            .dockerPort(2375)
            .mockCenterConfig(mockConfig)
            .build();

        // 3. 执行测试
        return executeRemoteTest(testEnv, groupName);
    }

    /**
     * 混合业务组远程Docker测试
     */
    public TestResult runMixedGroupRemoteTest(List<String> groupNames, TestScenario scenario) {
        TestSuite testSuite = TestSuite.builder()
            .groupNames(groupNames)
            .scenario(scenario)
            .includeCrossGroupTests(true)
            .includeCoordinationTests(true)
            .includeDataConsistencyTests(true)
            .remoteDockerEnvironment(true)
            .build();

        return testExecutor.execute(testSuite);
    }

    /**
     * 演进架构兼容性测试
     */
    public TestResult testEvolutionCompatibility(EvolutionPlan plan) {
        for (EvolutionStep step : plan.getSteps()) {
            TestResult stepResult = testEvolutionStepInRemoteDocker(step);
            if (!stepResult.isSuccess()) {
                // 回滚到上一个稳定状态
                rollbackToStableState(step.getPreviousState());
                return stepResult;
            }
        }
        return TestResult.success();
    }
}
```

### 4. 远程Docker测试套件

```java
/**
 * 业务组远程Docker测试套件
 */
@SpringBootTest
@ActiveProfiles("remote-docker-test")
@TestMethodOrder(OrderAnnotation.class)
public class BusinessGroupRemoteTestSuite {

    @Autowired
    private BusinessGroupManager businessGroupManager;

    @Autowired
    private RemoteDockerBusinessGroupTestManager testManager;

    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:13")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test")
            .withStartupTimeout(Duration.ofMinutes(3));

    @Container
    static GenericContainer<?> valkey = new GenericContainer<>("valkey/valkey:7.2")
            .withExposedPorts(6379)
            .withStartupTimeout(Duration.ofMinutes(2));

    @Test
    @Order(1)
    public void testPlatformCoreInRemoteDocker() {
        // 测试平台核心功能在远程Docker环境中的运行
        TestResult result = testManager.runSingleGroupRemoteTest("platform",
            TestConfiguration.builder()
                .includeBasicTests(true)
                .includeIntegrationTests(true)
                .mockExternalServices(true)
                .build());

        assertThat(result.isSuccess()).isTrue();
    }

    @Test
    @Order(2)
    public void testBusinessGroupCreationInRemoteDocker() {
        // 测试业务组动态创建在远程Docker环境中的功能
        String testGroupName = "test_group_" + System.currentTimeMillis();

        BusinessGroupConfig config = BusinessGroupConfig.builder()
            .name(testGroupName)
            .enabled(true)
            .mode("LOCAL")
            .schemas(Arrays.asList("business_" + testGroupName))
            .build();

        // 在远程Docker环境中创建业务组
        businessGroupManager.createBusinessGroup(testGroupName, config);

        // 验证创建成功
        assertThat(businessGroupManager.groupExists(testGroupName)).isTrue();
    }

    @Test
    @Order(3)
    public void testCrossGroupCoordinationInRemoteDocker() {
        // 测试跨业务组协调在远程Docker环境中的功能
        TestResult result = testManager.runMixedGroupRemoteTest(
            Arrays.asList("platform", "test_group_" + System.currentTimeMillis()),
            TestScenario.CROSS_GROUP_COORDINATION);

        assertThat(result.isSuccess()).isTrue();
    }
}
```

### 5. AI执行测试命令

```bash
# 测试平台核心
./tools/test-in-remote-docker/scripts/test-business-group-remote.bat platform single

# 测试单个业务组
./tools/test-in-remote-docker/scripts/test-business-group-remote.bat user single

# 测试混合业务组
./tools/test-in-remote-docker/scripts/test-business-group-remote.bat user,order mixed

# 测试演进架构兼容性
./tools/test-in-remote-docker/scripts/test-business-group-remote.bat platform evolution
```

### 6. 测试配置文件

```yaml
# application-remote-docker-test.yml
spring:
  profiles:
    active: remote-docker-test
  datasource:
    # 使用TestContainers动态配置
    url: ${TESTCONTAINERS_POSTGRES_URL:***************************************}
    username: ${TESTCONTAINERS_POSTGRES_USERNAME:test}
    password: ${TESTCONTAINERS_POSTGRES_PASSWORD:test}
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

# 模拟center配置
xkong:
  evolution:
    enabled: true
    mode: MONOLITHIC
  business:
    platform:
      enabled: true
      mode: LOCAL
      schemas: ["business_platform"]
    groups: {} # 动态添加

# TestContainers配置
testcontainers:
  reuse:
    enable: false # 每次都使用新容器
```

## 大规模扩展预留

### 1. 分片架构预留

```java
/**
 * 分片策略接口（预留）
 */
public interface ShardingStrategy {
    String calculateShardingKey(Object entity);
    List<String> getTargetShards(QueryCondition condition);
}

/**
 * 分布式数据访问服务（预留）
 */
@Service
public class DistributedDataAccessService<T, ID> implements DataAccessService<T, ID> {
    // 未来实现分库分表逻辑
}
```

### 2. 集群管理预留

```yaml
# 未来集群配置预留
xkong:
  cluster:
    enabled: false  # 当前阶段不启用
    mode: SINGLE_NODE
    # 未来支持：CITUS, SHARDING_SPHERE, HYBRID

  sharding:
    enabled: false  # 当前阶段不启用
    # 未来配置分片策略
```

## 实施路径

### 第1步：基础架构搭建（1周）
- [ ] 创建完整包结构
- [ ] 实现基础的演进架构注解
- [ ] 配置PostgreSQL连接
- [ ] 创建基础Schema
- [ ] 配置远程Docker测试环境

### 第2步：平台核心迁移（1-2周）
- [ ] 迁移现有MiddlewareController
- [ ] 迁移现有KVParamService
- [ ] 更新包引用
- [ ] 验证功能正常
- [ ] 实现MockKVParamService测试配置
- [ ] 执行平台核心远程Docker测试

### 第3步：业务组框架（1周）
- [ ] 实现BusinessGroupManager
- [ ] 实现模板生成器
- [ ] 测试动态创建机制
- [ ] 完善配置管理
- [ ] 实现RemoteDockerBusinessGroupTestManager
- [ ] 执行业务组创建远程Docker测试

### 第4步：演进准备（持续）
- [ ] 完善ServiceInterface注解
- [ ] 实现基础的EvolutionManager
- [ ] 准备远程调用框架
- [ ] 建立监控体系
- [ ] 实现演进架构兼容性测试
- [ ] 建立持续集成测试流水线

### 第5步：测试体系完善（并行进行）
- [ ] 创建BusinessGroupRemoteTestSuite
- [ ] 实现单业务组测试用例
- [ ] 实现混合业务组测试用例
- [ ] 实现跨组协调测试用例
- [ ] 实现演进架构兼容性测试用例
- [ ] 配置TestContainers环境
- [ ] 优化远程Docker测试性能

## 与现有架构文档的关系

本文档与现有架构文档体系的关系：

1. **继承关系**：基于`docs/common/architecture/principles/continuous-evolution-architecture.md`的演进原则
2. **实现关系**：具体实现`docs/common/architecture/patterns/service-evolution-patterns.md`中的模式
3. **路线图关系**：遵循`docs/common/architecture/diagrams/architecture-evolution-roadmap.md`的演进路径
4. **补充关系**：专注于包架构设计，补充现有文档的技术实现细节

## 总结

本`xkongcloud-business-internal-core`重构方案提供了一个完整的、面向未来的技术框架，支持：

1. **零业务预设**：纯技术架构，适应任何业务迭代
2. **完整演进能力**：从单机到分布式的平滑演进
3. **动态扩展**：支持10+业务组的独立发展
4. **大规模预留**：为万亿级数据处理预留技术基础
5. **实施可控**：分步实施，风险可控，每步都可验证
6. **配置现实性**：所有配置从center动态获取，支持热更新
7. **测试完整性**：基于远程Docker的完整测试策略，覆盖单业务组、混合业务组、演进架构的各种测试场景
8. **环境适配性**：完美适配Windows开发环境+远程Linux Docker的实际约束

### 核心特色

- **配置驱动**：生产环境所有配置从XKC-CENTER动态获取
- **测试隔离**：每次远程Docker测试都是全新环境，通过Mock替代无法访问的外部服务
- **AI友好**：完全支持AI自主执行测试，无需人工干预
- **演进安全**：每个演进步骤都有对应的兼容性测试保障

该重构方案为`xkongcloud-business-internal-core`项目的长期发展奠定了坚实的技术基础，既满足当前的开发需求，又为未来的大规模扩展提供了完整的技术预留。
