# V4.0生产级代码生成提示词

## 💎 生产级代码生成核心提示词

```
设计生产级代码生成机制（基于V4.0测试验证结果）：

质量标准（基于实际测试调整）：
- 可直接编译运行（75-80分质量，已验证可达）
- 完整异常处理和日志记录
- 符合Spring Boot最佳实践
- 包含单元测试模板
- 生产环境就绪（监控、配置、安全）

全DeepSeek生态AI协同策略（经测试验证最优）：
- DeepSeek V3 0324：主力架构师，负责整体架构设计和核心代码生成
- DeepSeek R1 0528：备用快速生成，负责快速响应和容错处理
- DeepCoder-14B：代码专家，负责代码质量优化和最佳实践

🛡️ 95%置信度验证机制：
- 语法正确性检查（javac编译验证）
- 架构模式验证（重点提升架构准确性，当前43.8%需要改进）
- 性能基准测试（响应时间、内存使用）
- 安全漏洞扫描（静态代码分析）
- **95%置信度质量门禁**（核心验证机制）

**质量门禁流程**：
1. **代码生成阶段置信度评估**：
   - AI生成代码 → 7维度置信度计算 → ≥95%通过 / <95%回退V3.1原始策略

2. **人工介入机制**：
   - 置信度<95%时自动触发人工介入
   - 通知主力IDE AI进行专业处理
   - 提供详细的质量分析报告

3. **回退策略**：
   - 优先保证代码质量，而非功能完整性
   - 回退到V3.1生成器的原始代码生成逻辑
   - 确保在任何情况下都有可编译的代码输出

请设计完整的代码生成和验证流程，包含95%置信度质量门禁机制。
```

## 🔧 代码模板生成提示词

```
生成V4.0代码模板系统：

模板类型：
1. 核心业务类模板（Service、Repository、Controller）
2. 配置类模板（Configuration、Properties）
3. 异常处理模板（自定义异常、全局异常处理器）
4. 测试类模板（单元测试、集成测试）
5. 部署配置模板（Docker、Kubernetes、CI/CD）

模板特性：
- 基于JSON配置动态生成
- 包含完整的注释和文档
- 遵循代码规范和最佳实践
- 支持自定义扩展点

生成策略（基于全DeepSeek生态）：
- 使用DeepSeek V3 0324进行架构设计和主体代码生成
- 使用DeepCoder-14B进行代码质量优化和最佳实践应用
- 基于项目特定配置进行定制（75%JSON使用率）
- 包含错误处理和边界条件
- 提供多种实现选项

质量要求（基于实际测试调整）：
- 代码覆盖率>65%（基于实际测试的现实目标）
- 性能基准达标
- 安全最佳实践
- 可维护性评分>7/10
- 架构准确性>50%（重点改进领域）

请设计代码模板生成系统和质量标准。
```

## 🧪 测试代码生成提示词

```
设计V4.0测试代码生成机制：

测试类型：
1. 单元测试（JUnit 5 + Mockito）
2. 集成测试（Spring Boot Test）
3. 性能测试（JMeter配置）
4. 安全测试（OWASP检查清单）

测试覆盖：
- 核心业务逻辑100%覆盖
- 异常场景和边界条件
- 并发和性能测试
- 安全漏洞验证

AI生成策略：
- DeepCoder-14B：生成专业测试代码
- 基于接口定义自动生成测试用例
- 包含Mock对象和测试数据
- 生成测试报告和覆盖率分析

测试质量：
- 测试用例覆盖率>90%
- 测试执行时间<5分钟
- 测试稳定性>99%
- 测试维护成本最小化

请设计完整的测试代码生成和执行框架。
```

## 📦 配置文件生成提示词

```
设计V4.0配置文件生成系统：

配置类型：
1. 应用配置（application.yml/properties）
2. 数据库配置（连接池、事务管理）
3. 缓存配置（Redis、本地缓存）
4. 监控配置（Actuator、Micrometer）
5. 安全配置（Spring Security、OAuth2）
6. 部署配置（Docker、K8s、环境变量）

配置特性：
- 环境特定配置（dev、test、prod）
- 配置验证和类型安全
- 敏感信息加密
- 配置热更新支持

生成策略：
- 基于JSON配置自动生成
- 包含详细的配置说明
- 提供默认值和推荐设置
- 支持配置模板和继承

质量要求：
- 配置完整性100%
- 环境兼容性验证
- 性能优化配置
- 安全配置最佳实践

请设计配置文件生成和管理系统。
```

## 🔍 代码质量验证提示词

```
设计V4.0代码质量验证系统：

验证维度：
1. 语法正确性（编译通过率100%）
2. 代码规范（Checkstyle、PMD、SpotBugs）
3. 架构一致性（与设计文档匹配度）
4. 性能指标（响应时间、内存使用）
5. 安全标准（OWASP Top 10检查）

验证流程：
- 静态代码分析（SonarQube集成）
- 动态测试验证（自动化测试执行）
- 架构合规检查（依赖分析、模式验证）
- 性能基准测试（负载测试、压力测试）

质量门禁：
- 编译成功率：100%
- 代码覆盖率：>80%
- 安全漏洞：0个高危
- 性能基准：满足SLA要求

自动化集成：
- CI/CD流水线集成
- 质量报告自动生成
- 问题自动分类和优先级
- 修复建议和最佳实践推荐

请设计完整的代码质量验证和持续改进系统。
```

## 🚀 部署就绪代码生成提示词

```
生成V4.0部署就绪代码：

部署组件：
1. Dockerfile和容器配置
2. Kubernetes部署清单
3. CI/CD流水线配置
4. 监控和日志配置
5. 健康检查和探针
6. 服务发现和负载均衡

代码特性：
- 云原生架构支持
- 微服务通信机制
- 配置外部化
- 优雅启动和关闭
- 故障恢复和重试

生成策略：
- 基于项目配置自动生成
- 包含最佳实践和安全配置
- 支持多环境部署
- 提供运维脚本和文档

运维支持：
- 自动化部署脚本
- 监控告警配置
- 日志聚合和分析
- 性能调优建议

请设计部署就绪代码生成和运维支持系统。
```

---

*基于Spring Boot 3.4.5和Java 21最佳实践*  
*确保生成的代码达到生产级质量标准*  
*创建时间：2025-06-14*
