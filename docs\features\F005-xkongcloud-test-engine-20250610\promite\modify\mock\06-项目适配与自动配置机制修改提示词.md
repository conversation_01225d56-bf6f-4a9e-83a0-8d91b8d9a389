# 06-项目适配与自动配置机制修改提示词

**文档版本**: MODIFY-PROJECT-ADAPTATION-AUTOCONFIG  
**创建时间**: 2025年6月10日  
**修改目标**: 在项目适配中增加Mock配置生成和双阶段执行策略

---

## 🎯 修改目标

在项目适配与自动配置机制中增加Mock配置的自动生成，并在自适应执行链路中增加双阶段执行策略。

## 📝 具体修改内容

### **修改位置1：智能配置生成器 - 增加Mock配置生成**

**在IntelligentConfigGenerator类中增加Mock配置生成方法**：
```java
/**
 * 智能配置生成器（增加Mock配置生成）
 * 基于项目分析结果自动生成最优配置，包括Mock配置
 */
@Component
public class IntelligentConfigGenerator {
    
    @Autowired
    private MockConfigurationGenerator mockConfigGenerator;
    
    @Autowired
    private DualPhaseConfigurationManager dualPhaseConfigManager;
    
    /**
     * 生成智能配置（增加Mock配置生成）
     */
    public UniversalEngineConfig generateIntelligentConfig(ProjectAnalysisResult analysisResult) {
        log.info("开始生成智能配置，项目类型: {}", analysisResult.getProjectType());
        
        try {
            // Step 1: 获取项目类型模板
            UniversalEngineConfigTemplate template = templateManager.getTemplate(analysisResult.getProjectType());
            
            // Step 2: 基于项目特征定制配置
            UniversalEngineConfig baseConfig = configCustomizer.customizeConfig(template, analysisResult);
            
            // Step 3: 生成Mock配置
            MockConfiguration mockConfig = generateMockConfiguration(analysisResult);
            baseConfig.setMockConfiguration(mockConfig);
            
            // Step 4: 配置双阶段执行策略
            DualPhaseExecutionConfig dualPhaseConfig = configureDualPhaseExecution(analysisResult);
            baseConfig.setDualPhaseExecutionConfig(dualPhaseConfig);
            
            // Step 5: 能力映射和激活
            Set<UniversalEngineCapability> capabilities = capabilityMapper.mapCapabilities(analysisResult);
            baseConfig.setEnabledCapabilities(capabilities);
            
            // Step 6: 参数智能推断
            inferConfigurationParameters(baseConfig, analysisResult);
            
            // Step 7: 验证配置完整性
            validateConfigurationCompleteness(baseConfig, analysisResult);
            
            return baseConfig;
            
        } catch (Exception e) {
            log.error("智能配置生成失败", e);
            throw new ConfigGenerationException("智能配置生成失败", e);
        }
    }
    
    /**
     * 生成Mock配置
     * 为不同Mock环境类型生成专门的配置
     */
    private MockConfiguration generateMockConfiguration(ProjectAnalysisResult analysisResult) {
        MockConfiguration mockConfig = new MockConfiguration();
        
        // 开发阶段Mock配置：快速验证程序逻辑
        DevelopmentMockConfig developmentConfig = generateDevelopmentMockConfig(analysisResult);
        mockConfig.setDevelopmentConfig(developmentConfig);
        
        // 诊断Mock配置：故障诊断时的Mock环境配置
        DiagnosticMockConfig diagnosticConfig = generateDiagnosticMockConfig(analysisResult);
        mockConfig.setDiagnosticConfig(diagnosticConfig);
        
        // 保护Mock配置：TestContainers失败时的保护配置
        ProtectionMockConfig protectionConfig = generateProtectionMockConfig(analysisResult);
        mockConfig.setProtectionConfig(protectionConfig);
        
        // gRPC接口Mock配置：外部接口模拟的配置生成
        GrpcInterfaceMockConfig grpcMockConfig = generateGrpcInterfaceMockConfig(analysisResult);
        mockConfig.setGrpcInterfaceMockConfig(grpcMockConfig);
        
        return mockConfig;
    }
    
    /**
     * 生成开发阶段Mock配置
     * 快速验证程序逻辑的Mock配置
     */
    private DevelopmentMockConfig generateDevelopmentMockConfig(ProjectAnalysisResult analysisResult) {
        DevelopmentMockConfig config = new DevelopmentMockConfig();
        
        ProjectProfile profile = analysisResult.getProjectProfile();
        
        // 基于项目特征配置Mock
        if (profile.isHasKVDependency()) {
            config.setMockConfigCenter(MockConfigCenterConfig.builder()
                .startupTime(Duration.ofSeconds(2))
                .memoryMode(true)
                .basicConfigurationOnly(true)
                .purpose("开发阶段快速验证配置注入逻辑")
                .build());
        }
        
        if (profile.isHasPersistence()) {
            config.setMockDatabase(MockDatabaseConfig.builder()
                .inMemoryDatabase(true)
                .schemaAutoGeneration(true)
                .testDataPreloading(true)
                .purpose("开发阶段快速验证数据访问逻辑")
                .build());
        }
        
        if (profile.isHasExternalInterface()) {
            config.setMockExternalServices(MockExternalServicesConfig.builder()
                .grpcMockEnabled(true)
                .restMockEnabled(true)
                .responseTemplateGeneration(true)
                .purpose("开发阶段快速验证接口调用逻辑")
                .build());
        }
        
        return config;
    }
    
    /**
     * 生成gRPC接口Mock配置
     * 外部接口模拟的配置生成
     */
    private GrpcInterfaceMockConfig generateGrpcInterfaceMockConfig(ProjectAnalysisResult analysisResult) {
        GrpcInterfaceMockConfig config = new GrpcInterfaceMockConfig();
        
        // 基于注解扫描结果生成gRPC Mock配置
        AnnotationScanResult annotationResult = analysisResult.getAnnotationResult();
        
        List<GrpcServiceMockConfig> serviceMockConfigs = new ArrayList<>();
        for (ClassAnnotationInfo grpcServiceClass : annotationResult.getGrpcServiceClasses()) {
            GrpcServiceMockConfig serviceMockConfig = GrpcServiceMockConfig.builder()
                .serviceName(grpcServiceClass.getClassName())
                .serviceInterface(grpcServiceClass.getClassType())
                .mockResponseGeneration(true)
                .dataConsistencyCheck(true)
                .purpose("gRPC接口模拟，验证接口调用逻辑和数据一致性")
                .build();
            serviceMockConfigs.add(serviceMockConfig);
        }
        
        config.setServiceMockConfigs(serviceMockConfigs);
        
        return config;
    }
    
    /**
     * 配置双阶段执行策略
     * Mock先行验证 → TestContainers完整验证
     */
    private DualPhaseExecutionConfig configureDualPhaseExecution(ProjectAnalysisResult analysisResult) {
        DualPhaseExecutionConfig config = new DualPhaseExecutionConfig();
        
        // 第一阶段：Mock快速验证
        PhaseOneConfig phaseOneConfig = PhaseOneConfig.builder()
            .phaseName("Mock快速验证阶段")
            .environmentType(EnvironmentType.MOCK_DEVELOPMENT)
            .executionTimeout(Duration.ofMinutes(2))
            .purpose("快速验证程序逻辑正确性")
            .successCriteria("Mock环境下基础功能验证通过")
            .build();
        
        // 第二阶段：TestContainers完整验证
        PhaseTwoConfig phaseTwoConfig = PhaseTwoConfig.builder()
            .phaseName("TestContainers完整验证阶段")
            .environmentType(EnvironmentType.REAL_TESTCONTAINERS)
            .executionTimeout(Duration.ofMinutes(10))
            .purpose("真实环境完整验证")
            .successCriteria("真实环境下完整功能验证通过")
            .prerequisite("第一阶段Mock验证必须通过")
            .build();
        
        // 故障降级策略
        FailoverStrategy failoverStrategy = FailoverStrategy.builder()
            .testContainersFailureAction(FailoverAction.SWITCH_TO_MOCK_PROTECTION)
            .mockFailureAction(FailoverAction.REPORT_AND_ABORT)
            .maxRetryAttempts(3)
            .retryInterval(Duration.ofSeconds(30))
            .build();
        
        config.setPhaseOneConfig(phaseOneConfig);
        config.setPhaseTwoConfig(phaseTwoConfig);
        config.setFailoverStrategy(failoverStrategy);
        
        return config;
    }
}
```

### **修改位置2：自适应执行链路 - 增加双阶段执行**

**在AdaptiveExecutionChainCoordinator类中增加双阶段执行方法**：
```java
/**
 * 自适应执行链路协调器（增加双阶段执行）
 * 基于项目特征和环境状态动态调整执行策略
 */
@Component
public class AdaptiveExecutionChainCoordinator {
    
    @Autowired
    private DualPhaseExecutionManager dualPhaseExecutionManager;
    
    @Autowired
    private MockProtectionManager mockProtectionManager;
    
    /**
     * 执行自适应测试链路（增加双阶段执行）
     */
    public AdaptiveExecutionResult executeAdaptiveChain(String projectPath) {
        log.info("开始执行自适应测试链路: {}", projectPath);
        
        try {
            // Step 1-4: 项目分析和配置生成（保持原有逻辑）
            ProjectAnalysisResult analysisResult = projectAnalysisService.analyzeProject(projectPath);
            Set<UniversalEngineCapability> capabilities = analysisResult.getRequiredCapabilities();
            UniversalEngineConfig config = configGenerator.generateIntelligentConfig(analysisResult);
            ProjectAdapter adapter = generateProjectAdapter(analysisResult);
            config = adapter.enhanceConfiguration(config);
            
            // Step 5: 双阶段执行策略
            DualPhaseExecutionResult dualPhaseResult = executeDualPhaseStrategy(config, analysisResult);
            
            // Step 6: 版本一致性检查
            FieldVersionCheckResult versionResult = versionChecker.checkFieldVersionConsistency(
                config, environmentAwareness.getCurrentAwareness());
            
            // Step 7: 故障降级处理
            FailoverHandlingResult failoverResult = handleFailoverScenarios(dualPhaseResult, config);
            
            // Step 8: 神经可塑性分析执行
            NeuralPlasticityAnalysisResult neuralResult = executeNeuralPlasticityAnalysis(config, analysisResult);
            
            // Step 9: 结果聚合分析
            AggregatedAnalysisResult aggregatedResult = aggregateResults(
                analysisResult, dualPhaseResult, versionResult, neuralResult);
            
            // Step 10: 智能报告生成
            generateIntelligentReport(aggregatedResult, config);
            
            return AdaptiveExecutionResult.builder()
                .projectAnalysisResult(analysisResult)
                .dualPhaseExecutionResult(dualPhaseResult)
                .versionCheckResult(versionResult)
                .failoverHandlingResult(failoverResult)
                .neuralAnalysisResult(neuralResult)
                .aggregatedResult(aggregatedResult)
                .executionStatus(ExecutionStatus.COMPLETED)
                .build();
                
        } catch (Exception e) {
            log.error("自适应执行链路失败", e);
            return AdaptiveExecutionResult.failure(e.getMessage());
        }
    }
    
    /**
     * 执行双阶段策略
     * Mock先行验证 → TestContainers完整验证
     */
    private DualPhaseExecutionResult executeDualPhaseStrategy(
            UniversalEngineConfig config, 
            ProjectAnalysisResult analysisResult) {
        
        DualPhaseExecutionResult result = new DualPhaseExecutionResult();
        
        try {
            // 第一阶段：Mock快速验证
            PhaseOneExecutionResult phaseOneResult = executePhaseOneMockValidation(config, analysisResult);
            result.setPhaseOneResult(phaseOneResult);
            
            if (!phaseOneResult.isSuccessful()) {
                result.setOverallStatus(DualPhaseStatus.PHASE_ONE_FAILED);
                result.setFailureReason("第一阶段Mock验证失败");
                return result;
            }
            
            // 第二阶段：TestContainers完整验证
            PhaseTwoExecutionResult phaseTwoResult = executePhaseTwoTestContainersValidation(config, analysisResult);
            result.setPhaseTwoResult(phaseTwoResult);
            
            if (phaseTwoResult.isSuccessful()) {
                result.setOverallStatus(DualPhaseStatus.BOTH_PHASES_SUCCESSFUL);
            } else {
                // TestContainers失败，启动Mock保护模式
                MockProtectionResult protectionResult = mockProtectionManager.activateProtectionMode(
                    config, phaseTwoResult.getFailureReason());
                result.setMockProtectionResult(protectionResult);
                result.setOverallStatus(DualPhaseStatus.PHASE_TWO_FAILED_MOCK_PROTECTION);
            }
            
        } catch (Exception e) {
            log.error("双阶段执行失败", e);
            result.setOverallStatus(DualPhaseStatus.EXECUTION_ERROR);
            result.setFailureReason(e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 第一阶段：Mock快速验证程序逻辑
     */
    private PhaseOneExecutionResult executePhaseOneMockValidation(
            UniversalEngineConfig config, 
            ProjectAnalysisResult analysisResult) {
        
        log.info("开始第一阶段：Mock快速验证程序逻辑");
        
        PhaseOneExecutionResult result = new PhaseOneExecutionResult();
        result.setStartTime(LocalDateTime.now());
        
        try {
            // 切换到Mock开发环境
            environmentAwareness.switchToEnvironment(EnvironmentType.MOCK_DEVELOPMENT);
            
            // 启动Mock引擎
            MockEngineStartupResult mockStartupResult = startMockEngines(config.getMockConfiguration());
            result.setMockStartupResult(mockStartupResult);
            
            // 执行Mock环境下的测试
            MockTestExecutionResult mockTestResult = executeMockTests(config, analysisResult);
            result.setMockTestResult(mockTestResult);
            
            // 验证程序逻辑正确性
            ProgramLogicValidationResult logicValidation = validateProgramLogic(mockTestResult);
            result.setLogicValidationResult(logicValidation);
            
            result.setSuccessful(mockStartupResult.isSuccessful() && 
                               mockTestResult.isSuccessful() && 
                               logicValidation.isValid());
            result.setEndTime(LocalDateTime.now());
            result.setExecutionDuration(Duration.between(result.getStartTime(), result.getEndTime()));
            
        } catch (Exception e) {
            log.error("第一阶段Mock验证失败", e);
            result.setSuccessful(false);
            result.setFailureReason(e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 第二阶段：TestContainers完整验证环境集成
     */
    private PhaseTwoExecutionResult executePhaseTwoTestContainersValidation(
            UniversalEngineConfig config, 
            ProjectAnalysisResult analysisResult) {
        
        log.info("开始第二阶段：TestContainers完整验证环境集成");
        
        PhaseTwoExecutionResult result = new PhaseTwoExecutionResult();
        result.setStartTime(LocalDateTime.now());
        
        try {
            // 切换到TestContainers环境
            environmentAwareness.switchToEnvironment(EnvironmentType.REAL_TESTCONTAINERS);
            
            // 启动TestContainers环境
            TestContainersStartupResult containersStartupResult = startTestContainers(config);
            result.setContainersStartupResult(containersStartupResult);
            
            if (!containersStartupResult.isSuccessful()) {
                result.setSuccessful(false);
                result.setFailureReason("TestContainers启动失败: " + containersStartupResult.getFailureReason());
                return result;
            }
            
            // 执行完整环境测试
            CompleteEnvironmentTestResult completeTestResult = executeCompleteEnvironmentTests(config, analysisResult);
            result.setCompleteTestResult(completeTestResult);
            
            // 验证环境集成完整性
            EnvironmentIntegrationValidationResult integrationValidation = validateEnvironmentIntegration(completeTestResult);
            result.setIntegrationValidationResult(integrationValidation);
            
            result.setSuccessful(completeTestResult.isSuccessful() && integrationValidation.isValid());
            result.setEndTime(LocalDateTime.now());
            result.setExecutionDuration(Duration.between(result.getStartTime(), result.getEndTime()));
            
        } catch (Exception e) {
            log.error("第二阶段TestContainers验证失败", e);
            result.setSuccessful(false);
            result.setFailureReason(e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 处理故障降级场景
     * TestContainers失败时自动切换到Mock保护模式
     */
    private FailoverHandlingResult handleFailoverScenarios(
            DualPhaseExecutionResult dualPhaseResult, 
            UniversalEngineConfig config) {
        
        FailoverHandlingResult result = new FailoverHandlingResult();
        
        if (dualPhaseResult.getOverallStatus() == DualPhaseStatus.PHASE_TWO_FAILED_MOCK_PROTECTION) {
            // TestContainers失败，已启动Mock保护模式
            result.setFailoverTriggered(true);
            result.setFailoverType(FailoverType.TESTCONTAINERS_TO_MOCK_PROTECTION);
            result.setMockProtectionResult(dualPhaseResult.getMockProtectionResult());
            result.setFailoverReason("TestContainers失败，自动切换到Mock保护模式");
        } else {
            // 无需故障降级
            result.setFailoverTriggered(false);
            result.setFailoverType(FailoverType.NO_FAILOVER_NEEDED);
        }
        
        return result;
    }
}
```

## 🎯 修改原则

1. **Mock配置自动生成**：基于项目特征自动生成适合的Mock配置
2. **双阶段执行策略**：Mock先行验证 → TestContainers完整验证
3. **故障降级机制**：TestContainers失败时自动切换到Mock保护模式
4. **gRPC接口Mock支持**：专门的gRPC接口Mock配置生成

## 📋 验证要点

修改完成后，文档应该能够让AI清晰理解：
- Mock配置的自动生成机制和策略
- 双阶段执行的流程和优势
- 故障降级的触发条件和处理方式
- gRPC接口Mock的配置和使用方法
