# Commons DB V3 分层实施计划 (核心优先模式)

## 文档信息
- **文档ID**: F007-DB-IMPL-PLAN-001
- **版本**: v1.0
- **实施模式**: 方案A - 核心优先，分层构建
- **执行原则**: 逐层构建，每层代码修改后立即编译验证，确保每一步都建立在坚实的基础上。
- **AI控制**: 严格遵循`08-db-dependency-map.json`和`09-db-config-map.json`中定义的边界和规范。

## 总体目标
按照`核心优先模式`，自底向上、分层构建`xkongcloud-commons-db`框架，确保架构清晰、质量可靠、易于扩展。

---

## 阶段1：核心抽象层 (`commons-db-core`)
**认知单元**: 定义整个数据访问框架的通用语言和标准接口。
**操作边界**: 仅允许在`08-db-dependency-map.json` -> `modules[name=commons-db-core]`定义的模块和文件内操作。
**验证锚点**: 模块编译成功，接口定义符合`01-架构总览与设计哲学.md`的设计。

#### 步骤1.1：创建`pom.xml`
- **目标**: 建立`commons-db-core`模块，仅包含最基础的依赖（如`lombok`, `slf4j-api`）。
- **验证**: `mvn clean install`成功。

#### 步骤1.2：定义核心API
- **目标**: 创建`DataAccessTemplate.java`等核心数据访问接口。
- **文件位置**: 参考`08-db-dependency-map.json` -> `critical_files`
- **验证**: 接口定义完整，编译通过。

#### 步骤1.3：定义SPI
- **目标**: 创建`DataSourceProvider.java`等服务提供者接口，用于解耦具体实现。
- **文件位置**: 参考`08-db-dependency-map.json` -> `critical_files`
- **验证**: SPI接口定义正确，编译通过。

#### 步骤1.4：定义方言基础
- **目标**: 创建`Dialect.java`接口，为不同数据库的特性差异提供抽象。
- **文件位置**: 参考`08-db-dependency-map.json` -> `critical_files`
- **验证**: 方言接口定义正确，编译通过。

---

## 阶段2：并行实现层 (`-jpa`, `-querydsl`, `-jdbc`)
**认知单元**: 并行开发三个核心数据访问方式的实现。
**操作边界**: 分别在各自的模块内操作，均依赖`commons-db-core`。
**验证锚点**: 各模块单元测试通过，模块可独立编译打包。

#### 步骤2.1：实现`commons-db-jpa`
- **目标**: 集成Spring Data JPA，提供基于JPA的`DataAccessTemplate`实现。
- **验证**: 单元测试通过，可连接内存数据库（如H2）完成基本CRUD。

#### 步骤2.2：实现`commons-db-querydsl`
- **目标**: 集成Querydsl，提供类型安全的查询构建能力。
- **验证**: 单元测试通过，可构建复杂的类型安全查询。

#### 步骤2.3：实现`commons-db-jdbc`
- **目标**: 封装Spring `JdbcTemplate`，提供轻量级的JDBC操作能力。
- **验证**: 单元测试通过，可执行原生SQL并映射结果。

---

## 阶段3：基础设施层 (`-migration`, `-monitoring`, `-dialect`)
**认知单元**: 构建数据库运维和管理所需的基础设施。
**操作边界**: 分别在各自的模块内操作，依赖`commons-db-core`。
**验证锚点**: 各模块功能通过集成测试验证。

#### 步骤3.1：实现`commons-db-migration`
- **目标**: 集成Flyway，提供数据库Schema的版本控制和自动迁移能力。
- **验证**: 能够执行Flyway迁移脚本，更新数据库Schema。

#### 步骤3.2：实现`commons-db-monitoring`
- **目标**: 集成Micrometer，暴露连接池性能指标和健康检查端点。
- **验证**: 可以在Actuator端点看到数据源相关的监控信息。

#### 步骤3.3：实现`commons-db-dialect`
- **目标**: 提供`PostgreSQLDialect`和`MySQLDialect`的具体实现。
- **验证**: 针对特定数据库的SQL语法或函数可以被正确处理。

---

## 阶段4：自动配置层 (`commons-db-starter`)
**认知单元**: 与Spring Boot生态无缝集成，实现“开箱即用”。
**操作边界**: `commons-db-starter`模块，依赖所有其他`commons-db-*`模块。
**验证锚点**: Spring Boot应用启动时，所有DB相关Bean能被自动配置和启动。

#### 步骤4.1：实现`CommonsDbProperties`
- **目标**: 创建与`09-db-config-map.json`对应的配置属性类。
- **验证**: `application.yml`中的配置能被正确读取。

#### 步骤4.2：实现`CommonsDbAutoConfiguration`
- **目标**: 创建自动配置类，根据配置动态装配数据源、JPA、Querydsl、Flyway等Bean。
- **验证**: 在一个测试Spring Boot应用中，可以通过修改配置来启用/禁用不同的数据访问层和功能。
