# AI严格生成实施计划文档的完整提示词

## 使用说明
这是用于指导AI严格按照模板生成实施计划文档的完整提示词。基于F007成功案例和记忆库约束，确保AI生成高质量、可执行的实施计划文档。

---

## 🎯 标准执行提示词

```
请严格按照以下完整流程为项目生成高质量的实施计划文档：

## 📋 执行任务
1. **深度解析设计文档**，提取架构哲学、技术约束和设计原则（新增）
2. 学习设计文档内容，理解项目需求和架构设计
3. 深度调研当前项目的真实代码架构（强制执行）
4. **将设计约束转化为实施指导原则**（新增）
5. 基于设计文档和架构调研，在指定目录生成完整的实施计划文档

## 🧠 AI认知约束强制激活

### 必须激活的核心命令
```bash
@L1:global-constraints                    # 全局约束检查
@L1:ai-implementation-design-principles  # AI实施设计原则
@AI_COGNITIVE_CONSTRAINTS                # AI认知约束激活
@MEMORY_BOUNDARY_CHECK                  # 记忆边界检查
@HALLUCINATION_PREVENTION               # 幻觉防护激活
@ATOMIC_OPERATION_VALIDATION            # 原子操作验证
@COGNITIVE_GRANULARITY_CONTROL          # 认知粒度控制
@BOUNDARY_GUARD_ACTIVATION              # 边界护栏激活
@ARCHITECTURAL_EVOLUTION_CHECK          # 架构演进检查（新增）
@DESIGN_CONSTRAINT_VALIDATION           # 设计约束验证（新增）
```

### 认知约束参数
- **单步骤代码限制**: 每个步骤限制在50行代码以内
- **认知复杂度控制**: 单次处理概念数量≤5个
- **记忆边界管理**: 总操作文件数≤3个/阶段
- **幻觉防护**: 每个步骤都有具体验证锚点
- **原子操作**: 每个操作必须独立可验证
- **设计约束一致性**: 每个实施步骤都必须符合设计文档约束（新增）

## 🔍 第零步：设计文档深度解析与架构约束提取（新增关键环节）

在执行任何其他步骤前，必须先深度解析设计文档，提取核心架构约束：

### 0.1 设计哲学提取
**使用工具**: `read_file`
**目标**: 深度理解设计文档的核心哲学和指导原则
**执行要求**:
- 完整阅读设计文档的"架构总览与设计哲学"章节
- 提取核心设计哲学（如"务实架构"、"组合优化"、"技术特性协同"等）
- 识别设计驱动因子（性能、可维护性、扩展性等优先级）
- 理解业务价值导向和技术选型逻辑

**验证锚点**: 准确理解设计文档的价值主张和技术理念

### 0.2 架构约束识别
**使用工具**: `grep_search` + `read_file`
**目标**: 识别所有强制性架构约束和技术限制
**执行要求**:
- 提取"架构范围边界"的包含范围和排除范围
- 识别"技术基石"的核心技术选型和组合要求
- 分析"分层架构"的层次职责和交互约束
- 提取"风险评估"中的技术风险和缓解措施
- 识别"成功标准"中的量化指标要求

**关键约束类别**:
```
技术约束类：
- 技术栈限制（如必须使用HikariCP、Spring Boot 3.4等）
- 性能要求（如性能损失<5%、P95响应时间<100ms）
- 架构模式约束（如分层架构、SPI机制等）

业务约束类：
- 功能边界限制（包含范围vs排除范围）
- 兼容性要求（如完全兼容Spring Data JPA）
- 演进策略约束（如渐进式迁移、向后兼容）

质量约束类：
- 代码质量指标（测试覆盖率≥80%、Bug率减少60%）
- 开发效率指标（开发时间减少50%、学习成本≤2天）
- 运维指标（可用性≥99.9%、监控完整性）
```

**验证锚点**: 形成完整的架构约束清单和技术限制矩阵

### 0.3 实施原则转化
**目标**: 将设计约束转化为可执行的实施指导原则
**执行要求**:
- 将每个架构约束转化为具体的实施检查点
- 为每个技术选型建立验证标准
- 将设计哲学转化为代码实施规范
- 建立设计-实施一致性验证机制

**转化示例**:
```
设计约束: "基于成熟的Spring生态"
↓ 转化为实施原则:
- 实施检查点1: 所有依赖必须来自Spring官方推荐技术栈
- 实施检查点2: 禁止引入非Spring生态的ORM框架
- 验证标准: 依赖分析报告中Spring依赖占比≥80%

设计约束: "性能损失<5%"
↓ 转化为实施原则:
- 实施检查点1: 每层抽象都必须进行性能基准测试
- 实施检查点2: 核心路径代码禁止过度封装
- 验证标准: 与原生Spring Data JPA的性能对比报告

设计约束: "L1(JPA) + L2(Querydsl) + L3(JDBC)分层"
↓ 转化为实施原则:
- 实施检查点1: 严格按照三层架构组织代码
- 实施检查点2: 禁止跨层直接调用
- 验证标准: 架构依赖图符合分层约束
```

**验证锚点**: 生成完整的"设计约束→实施原则→验证标准"映射表

### 0.4 技术债务与风险传承
**目标**: 识别设计文档中的技术债务和风险控制要求
**执行要求**:
- 分析设计文档中识别的P0/P1/P2级别风险
- 提取风险缓解措施和应对策略
- 识别技术债务和未来规划影响
- 建立风险监控和预警机制

**风险传承矩阵**:
```
P0级风险 → 实施计划P0风险控制 → 立即回滚机制
P1级风险 → 实施计划风险监控 → 1小时决策机制  
P2级风险 → 实施计划持续改进 → 计划处理策略
```

**验证锚点**: 风险控制方案与设计文档风险评估100%对应

### 0.5 设计关键点与实施关键点映射建立（新增核心环节）
**目标**: 建立设计文档关键点与实施计划关键点的精准对应关系
**执行要求**:
- 提取设计文档的所有关键决策点、技术选型点、架构设计点
- 为每个设计关键点分配唯一ID（D001、D002等）
- 规划对应的实施关键点ID（I001、I002等）
- 建立"关键点映射矩阵"，确保一一对应关系

**关键点分类体系**:
```
业务关键点: D001-D099 ↔ I001-I099 (业务目标、功能边界)
技术关键点: D101-D199 ↔ I101-I199 (技术选型、技术栈)
架构关键点: D201-D299 ↔ I201-I299 (分层设计、组件设计)
性能关键点: D301-D399 ↔ I301-I399 (性能目标、优化策略)
风险关键点: D401-D499 ↔ I401-I499 (风险识别、应对策略)
成功标准关键点: D501-D599 ↔ I501-I599 (验收标准、质量指标)
```

**关键决策链传承要求**:
- 技术选型决策链：问题→分析→选择→实施方案
- 架构设计决策链：需求→约束→架构模式→组件设计
- 风险控制决策链：风险识别→影响分析→应对策略→监控方案
- 性能优化决策链：性能要求→瓶颈分析→优化策略→验证方案

**验证锚点**: 建立完整的关键点映射矩阵，确保设计→实施100%精准对齐

## 🔍 第一步：深度代码架构调研（强制执行）

在生成任何文档前，必须先深度调研当前项目的真实架构：

### 1.1 项目整体结构扫描
**使用工具**: `codebase_search`
**目标**: 理解项目整体结构、模块分布、技术栈
**验证**: 获得准确的项目结构认知

### 1.2 核心模块依赖分析  
**使用工具**: `grep_search`
**目标**: 分析核心模块间的依赖关系、接口设计、数据流
**验证**: 建立准确的依赖关系图

### 1.3 配置和接口详查
**使用工具**: `read_file`
**目标**: 详细了解配置文件、核心接口、数据模型
**验证**: 确认技术栈和配置方式

### 1.4 关键代码逻辑理解
**使用工具**: `file_search`
**目标**: 定位关键业务逻辑、设计模式、代码组织方式
**验证**: 建立对现有代码的准确理解

### 1.5 现状真实性验证
**强制检查**: 每个架构分析结论都必须基于实际代码验证
**禁止假设**: 严格禁止基于推测进行架构分析
**验证锚点**: 所有分析结果都有具体的代码文件支撑

## 📊 第二步：项目复杂度智能评估

基于架构调研结果和设计文档，使用AI专业度量参数系统进行多维度评估：

### AI专业度量参数系统（0-100分）

**维度1：认知复杂度等级** (25分权重)
- L1低复杂度：0-8分 (≤3概念，单一概念，直接操作)
- L2中复杂度：9-17分 (4-7概念，多概念协调，需要分析)  
- L3高复杂度：18-25分 (≥8概念，架构决策，权威引用)

**维度2：记忆边界压力** (20分权重)
- 低压力：0-6分 (≤30%，信息量小，上下文简单)
- 中压力：7-14分 (40-60%，信息量中等，需要分块处理)
- 高压力：15-20分 (≥70%，信息量大，强制分解)

**维度3：幻觉风险系数** (20分权重)
- 低风险：0-6分 (≤0.15，已知领域，充分验证锚点)
- 中风险：7-14分 (0.2-0.3，部分未知，需要额外验证)
- 高风险：15-20分 (≥0.4，高度未知，强制人工确认)

**维度4：上下文切换成本** (20分权重)
- 低成本：0-6分 (≤2次切换，线性处理)
- 中成本：7-14分 (3-5次切换，需要状态管理)
- 高成本：15-20分 (≥6次切换，强制简化)

**维度5：验证锚点密度** (15分权重)
- 中密度：0-5分 (3个锚点/步骤，标准验证)
- 高密度：6-10分 (4-5个锚点/步骤，重要操作)
- 超高密度：11-15分 (≥7个锚点/步骤，关键架构)

### AI度量参数应用规则
- **任务分解评估**: 每个任务分解必须评估5个AI度量参数
- **度量驱动规划**: 基于度量参数结果调整任务粒度和验证密度
- **自动复杂度评估**: 自动评估认知复杂度等级，决定处理策略
- **压力阈值管理**: 记忆边界压力超过60%时强制分解任务
- **风险基础验证**: 根据幻觉风险系数调整验证锚点密度

### 复杂度等级判定
- **L1简单项目**：0-30分 (认知复杂度≤3，记忆压力≤30%，幻觉风险≤0.15)
- **L2中等项目**：31-70分 (认知复杂度4-7，记忆压力40-60%，幻觉风险0.2-0.3)
- **L3复杂项目**：71-100分 (认知复杂度≥8，记忆压力≥70%，幻觉风险≥0.4)

## 📝 第三步：严格按照模板生成实施计划文档

根据复杂度评估结果，生成对应的实施计划文档：

### L1简单项目 - 生成单一实施计划文档

**文件**: `{输出目录}/01-{项目名称}-实施计划.md`

```markdown
# {项目名称} - AI执行实施计划

## 文档元数据
- **文档ID**: [自动生成唯一ID]
- **文档类型**: AI实施计划
- **创建日期**: [当前日期]
- **更新日期**: [当前日期]
- **版本**: v1.0
- **状态**: 草稿
- **复杂度等级**: L1简单项目 (评估总分: [X]/100分)
- **设计文档依赖**: [设计文档路径和版本]（新增）
- **架构约束合规**: 已验证设计约束100%转化（新增）
- **执行原则**: 确认当前状态，分批验证，风险优先识别
- **质量标准**: 每个步骤限制在50行代码以内，立即编译验证

## 🧠 AI认知约束激活

### 强制激活命令
```bash
@L1:global-constraints                    # 全局约束检查
@L1:ai-implementation-design-principles  # AI实施设计原则
@AI_COGNITIVE_CONSTRAINTS                # AI认知约束激活
@MEMORY_BOUNDARY_CHECK                  # 记忆边界检查
@HALLUCINATION_PREVENTION               # 幻觉防护激活
@ATOMIC_OPERATION_VALIDATION            # 原子操作验证
@COGNITIVE_GRANULARITY_CONTROL          # 认知粒度控制
@BOUNDARY_GUARD_ACTIVATION              # 边界护栏激活
@ARCHITECTURAL_EVOLUTION_CHECK          # 架构演进检查（新增）
@DESIGN_CONSTRAINT_VALIDATION           # 设计约束验证（新增）
```

### 🛡️ 护栏机制检查
- **认知负载控制**: 单次操作≤50行代码，≤5个概念，≤3个操作步骤，≤2层依赖
- **幻觉防护**: 每个步骤基于实际代码状态验证，现实锚点验证率100%
- **原子操作**: 每个操作独立可验证，立即回滚机制，原子操作成功率≥95%
- **边界护栏**: 严格遵循实施范围边界定义，边界违规率=0%
- **设计约束护栏**: 每个实施步骤都必须通过设计约束验证，约束违规率=0%（新增）
- **智能编译验证**: 风险基础验证策略，避免机械化每步验证，验证精准率≥90%
- **强制架构分析**: 开始执行前100%完成真实项目架构分析
- **强制代码清理**: 执行完成后100%完成冗余代码检查和清理

## 🎯 设计文档架构约束转化结果（新增核心章节）

### 核心设计哲学传承
[基于设计文档第0步解析的核心哲学]
- **架构理念**: [从设计文档提取的核心架构思想]
- **技术价值观**: [从设计文档提取的技术选型逻辑]
- **设计权衡**: [从设计文档提取的设计权衡和取舍逻辑]

**实施指导原则**:
```
设计哲学: [具体的设计哲学描述]
↓ 转化为实施约束:
- 代码组织约束: [具体约束条件]
- 技术选型约束: [具体技术限制]
- 质量标准约束: [具体质量要求]
```

### 强制性架构约束清单
[基于设计文档深度解析的约束矩阵]

#### A类约束：技术栈强制性要求
| 约束ID | 约束内容 | 来源章节 | 验证标准 | 违规后果 |
|-------|---------|----------|----------|----------|
| A001 | [具体技术约束] | [设计文档章节] | [验证方法] | [处理措施] |
| A002 | [具体技术约束] | [设计文档章节] | [验证方法] | [处理措施] |

#### B类约束：架构模式强制性要求
| 约束ID | 约束内容 | 来源章节 | 验证标准 | 违规后果 |
|-------|---------|----------|----------|----------|
| B001 | [具体架构约束] | [设计文档章节] | [验证方法] | [处理措施] |
| B002 | [具体架构约束] | [设计文档章节] | [验证方法] | [处理措施] |

#### C类约束：性能与质量强制性要求
| 约束ID | 约束内容 | 来源章节 | 验证标准 | 违规后果 |
|-------|---------|----------|----------|----------|
| C001 | [具体性能约束] | [设计文档章节] | [验证方法] | [处理措施] |
| C002 | [具体质量约束] | [设计文档章节] | [验证方法] | [处理措施] |

### 设计-实施一致性验证矩阵
[确保实施计划与设计文档100%一致]

#### 设计目标 → 实施验证映射
```
设计目标1: [从设计文档提取的具体目标]
├── 实施验证点1: [具体验证方法]
├── 实施验证点2: [具体验证方法]
└── 一致性检查: [检查标准]

设计目标2: [从设计文档提取的具体目标]
├── 实施验证点1: [具体验证方法]
├── 实施验证点2: [具体验证方法]
└── 一致性检查: [检查标准]
```

#### 技术选型 → 实施约束映射
```
技术选型1: [从设计文档提取的技术选择]
├── 实施约束1: [具体实施限制]
├── 实施约束2: [具体实施限制]
└── 验证机制: [验证方法]

技术选型2: [从设计文档提取的技术选择]
├── 实施约束1: [具体实施限制]
├── 实施约束2: [具体实施限制]
└── 验证机制: [验证方法]
```

## 🎯 设计关键点与实施关键点精准对齐（新增核心章节）

### 设计关键点提取与分类
[基于第零步的关键点映射建立结果]

#### 业务关键点清单
| 关键点ID | 关键点内容 | 来源章节 | 重要等级 | 对应实施点 |
|----------|----------|----------|----------|----------|
| D001 | [具体业务关键点] | [设计文档章节] | 高/中/低 | I001 |
| D002 | [具体业务关键点] | [设计文档章节] | 高/中/低 | I002 |

#### 技术关键点清单
| 关键点ID | 关键点内容 | 来源章节 | 重要等级 | 对应实施点 |
|----------|----------|----------|----------|----------|
| D101 | [具体技术关键点] | [设计文档章节] | 高/中/低 | I101 |
| D102 | [具体技术关键点] | [设计文档章节] | 高/中/低 | I102 |

#### 架构关键点清单
| 关键点ID | 关键点内容 | 来源章节 | 重要等级 | 对应实施点 |
|----------|----------|----------|----------|----------|
| D201 | [具体架构关键点] | [设计文档章节] | 高/中/低 | I201 |
| D202 | [具体架构关键点] | [设计文档章节] | 高/中/低 | I202 |

#### 性能关键点清单
| 关键点ID | 关键点内容 | 来源章节 | 重要等级 | 对应实施点 |
|----------|----------|----------|----------|----------|
| D301 | [具体性能关键点] | [设计文档章节] | 高/中/低 | I301 |
| D302 | [具体性能关键点] | [设计文档章节] | 高/中/低 | I302 |

#### 风险关键点清单
| 关键点ID | 关键点内容 | 来源章节 | 重要等级 | 对应实施点 |
|----------|----------|----------|----------|----------|
| D401 | [具体风险关键点] | [设计文档章节] | 高/中/低 | I401 |
| D402 | [具体风险关键点] | [设计文档章节] | 高/中/低 | I402 |

#### 成功标准关键点清单
| 关键点ID | 关键点内容 | 来源章节 | 重要等级 | 对应实施点 |
|----------|----------|----------|----------|----------|
| D501 | [具体成功标准关键点] | [设计文档章节] | 高/中/低 | I501 |
| D502 | [具体成功标准关键点] | [设计文档章节] | 高/中/低 | I502 |

### 关键点映射矩阵（完整映射表）
[确保设计文档每个关键点都在实施计划中有精准对应]

| 关键点类别 | 设计关键点ID | 设计关键点内容 | 实施关键点ID | 实施关键点内容 | 映射状态 | 验证方法 |
|------------|-------------|-------------|-------------|-------------|----------|----------|
| 业务关键点 | D001 | [从设计文档提取] | I001 | [实施具体方案] | ✅完整 | [验证方法] |
| 技术关键点 | D101 | [从设计文档提取] | I101 | [实施具体方案] | ✅完整 | [验证方法] |
| 架构关键点 | D201 | [从设计文档提取] | I201 | [实施具体方案] | ✅完整 | [验证方法] |
| 性能关键点 | D301 | [从设计文档提取] | I301 | [实施具体方案] | ✅完整 | [验证方法] |
| 风险关键点 | D401 | [从设计文档提取] | I401 | [实施具体方案] | ✅完整 | [验证方法] |
| 成功标准关键点 | D501 | [从设计文档提取] | I501 | [实施具体方案] | ✅完整 | [验证方法] |

### 关键决策链传承验证
[确保设计文档的关键决策逻辑在实施计划中完整传承]

#### 技术选型决策链验证
```
设计决策链: [问题识别] → [方案分析] → [技术选择] → [验证方案]
                ↓             ↓            ↓            ↓
实施决策链: [实施问题分析] → [实施方案对比] → [实施技术确认] → [实施验证计划]
关键点对应: D101-技术选型 → I101-技术实施 (100%传承)
```

#### 架构设计决策链验证
```
设计决策链: [需求分析] → [约束识别] → [架构模式] → [组件设计]
                ↓            ↓            ↓            ↓
实施决策链: [实施需求确认] → [实施约束验证] → [实施架构确认] → [实施组件开发]
关键点对应: D201-架构设计 → I201-架构实施 (100%传承)
```

#### 风险控制决策链验证
```
设计决策链: [风险识别] → [影响分析] → [应对策略] → [监控方案]
                ↓            ↓            ↓            ↓
实施决策链: [风险确认] → [影响评估] → [控制措施] → [监控实施]
关键点对应: D401-风险分析 → I401-风险控制 (100%传承)
```

#### 性能优化决策链验证
```
设计决策链: [性能要求] → [瓶颈分析] → [优化策略] → [验证方案]
                ↓            ↓            ↓            ↓
实施决策链: [性能目标] → [瓶颈识别] → [优化实施] → [性能验证]
关键点对应: D301-性能设计 → I301-性能实施 (100%传承)
```

### 双向验证锚点机制
[建立设计→实施和实施→设计的双向验证]

#### 正向验证（设计→实施）检查点
- [ ] **设计目标覆盖验证**: 设计文档的每个核心目标都在实施计划中有对应实现
- [ ] **设计约束体现验证**: 设计文档的每个约束都在实施步骤中有明确体现
- [ ] **设计风险应对验证**: 设计文档识别的每个风险都在实施计划中有应对措施
- [ ] **设计成功标准对应验证**: 设计文档的成功标准都在实施验收中有对应指标

#### 反向验证（实施→设计）检查点
- [ ] **实施目标追溯验证**: 实施计划的每个核心目标都能追溯到设计文档依据
- [ ] **实施步骤依据验证**: 实施计划的每个关键步骤都有明确的设计文档支撑
- [ ] **实施验证依据验证**: 实施计划的每个验证标准都基于设计文档要求
- [ ] **实施边界依据验证**: 实施计划的范围边界都有设计文档的明确定义

## 🚨 实施范围边界（必读）

### ✅ 包含范围
[基于设计文档和架构调研的明确实施内容]
- [具体功能模块]: [详细描述] - 位置: `[实际路径]` - **设计约束**: [对应的设计文档约束ID]
- [具体技术栈]: [基于架构调研的实际技术栈] - **设计约束**: [对应的设计文档约束ID]
- [具体文件范围]: [明确的文件、类、方法范围] - **设计约束**: [对应的设计文档约束ID]
- **修改限制**: 单次修改 ≤ 50行代码，立即验证

### ❌ 排除范围  
[基于架构分析和设计文档排除范围的明确排除内容]
- [禁止修改的模块]: [具体原因] - 位置: `[实际路径]` - **设计依据**: [设计文档排除范围]
- [不涉及的技术栈]: [明确不涉及的中间件、第三方服务] - **设计依据**: [设计文档排除范围]
- [排除的扩展功能]: [明确排除的相关需求] - **设计依据**: [设计文档排除范围]
- **禁止操作**: 跨模块全局性修改、生产环境配置修改

### 🚧 边界护栏
- **范围检查点1**：[构思阶段] 确认方案不超出包含范围 + **设计约束检查**
- **范围检查点2**：[计划阶段] 验证步骤清单符合边界定义 + **架构约束检查**
- **范围检查点3**：[执行阶段] 每个关键步骤后确认范围一致性 + **设计一致性检查**
- **强制确认机制**：任何边界外需求必须停止执行并明确询问用户

## 🔍 现有架构调研结果

### 项目结构分析
[基于 codebase_search 的实际项目结构]
```
[实际的目录树结构]
```

### 核心模块识别
[基于架构调研识别的关键模块]
- **[模块1名称]**: [功能职责] - 位置: `[实际路径]`
- **[模块2名称]**: [功能职责] - 位置: `[实际路径]`

### 技术架构栈
[基于实际代码分析的技术栈]
- **后端框架**: [实际使用的框架及版本]
- **数据存储**: [实际的数据库和缓存方案]
- **配置管理**: [实际的配置文件组织方式]

### 接口设计现状
[基于代码调研的API接口分析]
- **现有接口**: [实际存在的关键接口]
- **数据模型**: [实际的数据结构定义]
- **依赖关系**: [实际的模块依赖关系图]

### 复用机会识别 (DRY原则强制执行)
[基于架构调研的可复用组件分析]
- **现有组件分析**: [检查每个新组件创建前是否存在可复用逻辑]
- **可复用组件**: [已存在的相似功能组件] - ref:[文件路径]#[组件锚点]
- **可复用模式**: [已使用的设计模式] - ref:[文件路径]#[模式锚点]
- **可复用配置**: [已有的配置模板] - ref:[文件路径]#[配置锚点]
- **模式提取**: [从具体实现中提取的可复用通用模式]
- **知识整合**: [整合分散知识点到统一记忆库的计划]

## 🎯 具体实施目标

### 功能目标
[基于设计文档提取的具体功能要求]
1. [功能点1]: [详细描述]
2. [功能点2]: [详细描述]  
3. [功能点3]: [详细描述]

### 技术目标
[基于设计文档和架构分析的技术实现目标]
- [技术实现点1]
- [技术实现点2]

### 验收标准
[明确的可验证标准]
- [验收标准1]: [具体验证方法]
- [验收标准2]: [具体验证方法]
- [性能指标要求]: [具体性能目标]

### 复杂度分析（基于智能评估算法）
- **评估总分**: [X]/100分
- **复杂度等级**: L1简单项目
- **主要复杂因素**: [具体分析各维度得分]
- **预估工作量**: [基于复杂度的时间估算]

## 📅 详细实施步骤

### 阶段1: 环境准备和基础设施 (预估1天)
**认知单元**: 开发环境配置和基础验证
**操作边界**: 仅限环境检查和基础配置，不涉及业务代码修改
**验证锚点**: 开发环境正常，基础编译通过
**设计约束验证**: 环境配置符合设计文档技术栈要求（新增）

#### 1.1 开发环境检查
**目标**: 验证开发环境和工具链完整性
**设计约束合规**: 验证环境配置符合设计文档A类约束（新增）

**执行指引**:
- [ ] **环境版本验证** - **约束检查**: [对应的设计文档约束ID]
  ```bash
  # 验证Java版本 (基于设计文档技术栈要求)
  java -version
  # 验证构建工具版本
  [实际构建工具] --version
  # 验证其他必要工具
  [基于设计文档实际需要的工具检查]
  ```

- [ ] **项目代码准备** - **约束检查**: [对应的设计文档约束ID]
  ```bash
  # 创建功能分支
  git checkout -b feature/[基于设计文档的功能名称]
  # 验证基础编译
  [实际的编译命令]
  # 验证现有测试
  [实际的测试命令]
  ```

**设计约束验证**:
- [ ] 技术栈版本符合设计文档A类约束要求
- [ ] 开发工具配置符合设计文档规范
- [ ] 环境依赖满足设计文档最小化要求

**关键点对齐验证**（新增）:
- [ ] 实施关键点I101（环境准备）与设计关键点D101（技术栈要求）100%对应
- [ ] 环境配置决策完全遵循设计文档的技术选型决策链
- [ ] 环境验证标准与设计文档成功标准关键点D501精准匹配

**验证**: 所有环境检查通过，基础编译成功，设计约束100%符合，关键点对齐100%

#### 1.2 依赖配置更新
**目标**: 更新项目依赖和配置
**设计约束合规**: 严格按照设计文档的技术选型和版本要求（新增）

**执行指引**:
- [ ] **依赖管理** (修改 `[实际的依赖配置文件]`) - **约束检查**: [对应约束ID]
  ```[实际的配置格式]
  # 基于设计文档需求添加的具体依赖
  [具体的依赖配置]
  ```

- [ ] **配置文件更新** (修改 `[实际的配置文件路径]`) - **约束检查**: [对应约束ID]
  ```[实际的配置格式]
  # 基于功能需求的具体配置
  [具体的配置内容]
  ```

**设计约束验证**:
- [ ] 依赖版本符合设计文档技术栈矩阵
- [ ] 配置参数符合设计文档性能要求
- [ ] 不包含设计文档排除范围的依赖

**验证**: 依赖解析成功，配置文件语法正确，设计约束验证通过

### 阶段2: 核心功能实施 (预估[基于AI度量参数的合理天数])
**认知单元**: 核心业务逻辑实现 (≤5个概念)
**操作边界**: 每次只修改一个文件，单次修改限制在50行以内，≤3个操作步骤
**验证锚点**: 智能编译验证(风险基础)，功能验证通过，冗余代码检查
**AI度量监控**: 实时监控认知复杂度、记忆边界压力、幻觉风险系数
**设计约束监控**: 实时验证架构分层、技术选型、性能指标符合设计文档（新增）

#### 2.1 [功能模块1名称] 实施 (≤50行代码)
**目标**: [从设计文档提取的具体目标]
**修改文件**: `[基于架构调研的实际文件路径]`
**设计约束合规**: 严格按照设计文档的[具体约束ID]实施（新增）

**执行指引**:
  **现有代码** (基于 read_file 调研):
  ```[实际代码语言]
  [基于架构调研的实际现有代码片段]
  ```

  **修改为**:
  ```[实际代码语言]
  // 添加必要的导入语句 (基于设计文档技术栈)
  [实际需要的导入语句]
  
  [基于设计文档的具体实现代码]
  // 包含完整的异常处理和日志记录
  ```

**设计约束验证检查点**:
- [ ] **A类约束检查**: 技术栈使用符合设计文档要求
- [ ] **B类约束检查**: 架构分层符合设计文档规范  
- [ ] **C类约束检查**: 代码质量满足设计文档标准

**智能编译验证** (风险基础策略):
  **风险评估**: [评估当前修改的风险等级] + **设计偏离风险评估**（新增）
  - 高风险触发器: 导入语句变更、异常类型替换、接口签名修改、核心架构组件变更、设计约束违规
  - 中风险触发器: 方法内部逻辑修改、配置文件更新、工具类方法添加、设计文档未明确定义区域
  - 低风险触发器: 注释更新、日志语句修改、变量重命名、设计文档明确允许的调整
  
  **智能验证点**:
  ```bash
  # 强制编译检查 (高风险变更后)
  [实际编译命令]
  # 强制设计约束检查 (任何变更后)
  [设计约束验证命令]
  # 可选编译检查 (中风险变更后)
  [有选择的验证命令]
  # 跳过编译检查 (低风险变更)
  # 注释: 纯文档更新、测试用例修改、注释格式化可跳过
  
  # 功能验证 (基于具体功能的验证方法)
  [具体验证步骤]
  # 冗余代码检查
  [检查多余import、无用函数、未使用变量]
  # 设计一致性验证
  [验证实施结果与设计文档的一致性]
  ```

#### 2.2 [功能模块2名称] 实施 (≤50行代码)
[按照相同格式，基于设计文档和架构调研的具体实施内容]

### 阶段3: 集成测试与验证 (预估1天)
**认知单元**: 系统集成和完整性验证
**操作边界**: 仅限测试验证，不修改业务逻辑代码
**验证锚点**: 所有测试通过，功能完整验收
**设计目标验证**: 验证实施结果符合设计文档的成功标准（新增）

#### 3.1 完整功能测试
**目标**: 验证实施功能的完整性和正确性
**设计验收**: 按照设计文档的验收标准进行验证（新增）

**执行指引**:
- [ ] **单元测试完善** - **设计标准**: [对应的设计文档测试覆盖率要求]
  ```[实际测试代码语言]
  // 基于实际测试框架的测试用例
  [具体的测试实现]
  ```

- [ ] **集成测试验证** - **设计标准**: [对应的设计文档性能要求]
  ```bash
  # 启动应用 (使用实际启动命令)
  [实际启动命令]
  # 功能验证 (基于设计文档的验收标准)
  [具体验证步骤]
  ```

**设计文档验收检查**:
- [ ] 功能完整性符合设计文档功能范围定义
- [ ] 性能指标达到设计文档量化要求
- [ ] 架构实现符合设计文档分层要求
- [ ] 技术选型100%符合设计文档约束

**验证**: 所有测试用例通过，功能按预期工作，设计文档验收标准达成

#### 3.2 性能与稳定性验证
**目标**: 确保实施不影响系统性能和稳定性
**设计基准**: 基于设计文档的性能指标要求（新增）

**执行指引**:
- [ ] **性能指标验证**: [基于设计文档的具体性能要求和基准]
- [ ] **稳定性测试**: [基于设计文档和系统特点的稳定性验证]

**设计文档性能验收**:
- [ ] 性能损失控制在设计文档规定范围内
- [ ] 响应时间符合设计文档P95要求
- [ ] 吞吐量达到设计文档QPS指标
- [ ] 可用性满足设计文档SLA要求

**验证**: 性能指标达标，系统运行稳定，设计文档性能要求达成

## 🚧 操作边界控制

### ✅ 允许修改的文件范围
[基于架构调研的实际可修改文件]
- `[实际业务代码路径]` - 业务逻辑实现
- `[实际配置文件路径]` - 功能配置
- `[实际测试代码路径]` - 测试用例
- **修改限制**: 单次修改 ≤ 50行代码

### ❌ 严格禁止修改  
[基于架构分析的禁止修改范围]
- `[实际核心模块路径]` - 其他模块的核心接口
- `[实际生产配置路径]` - 生产环境配置
- `[实际第三方依赖]` - 第三方库源码
- **禁止操作**: 跨模块全局性修改

## ✅ 验证锚点系统

### 每步验证标准
1. **编译验证**: `[实际编译命令]` 必须成功
2. **测试验证**: `[实际测试命令]` 所有测试通过  
3. **功能验证**: [基于设计文档的功能验证方法]
4. **性能验证**: [基于设计文档的性能要求]
5. **设计约束验证**: [基于设计文档约束清单的验证]（新增）
6. **架构一致性验证**: [基于设计文档架构要求的验证]（新增）

### 质量门禁
- **编译成功率**: 100%
- **测试通过率**: 100%  
- **代码覆盖率**: >80%
- **功能验收**: 符合设计文档要求
- **性能达标**: 满足设计文档性能指标要求
- **设计约束合规率**: 100%（新增）
- **架构一致性**: 100%符合设计文档架构规范（新增）

## ⚠️ 风险评估与应对方案

### 🔴 P0级别风险 (立即回滚)
| 风险场景 | 触发条件 | 立即回滚操作 | 设计文档依据 |
|---------|----------|-------------|-------------|
| 编译失败 | 编译命令执行失败且10分钟无法修复 | `git checkout -f [上一稳定commit]` | [设计文档风险控制策略] |
| 服务启动失败 | 应用无法正常启动 | 立即回滚代码并验证服务恢复 | [设计文档可用性要求] |
| 核心功能异常 | 影响主要业务流程 | 停止实施，回滚到稳定版本 | [设计文档功能边界] |
| 数据损坏 | 测试数据或配置损坏 | 立即停止，恢复备份数据 | [设计文档数据安全要求] |
| 设计约束严重违规 | 违反A类强制性约束 | 立即停止实施，重新设计方案 | [设计文档约束等级定义] |

### 🟡 P1级别风险 (1小时内决策)  
| 风险场景 | 触发条件 | 处理策略 | 设计文档依据 |
|---------|----------|----------|-------------|
| 测试失败 | 测试通过率 < 80% | 分析失败原因，修复或回滚 | [设计文档质量标准] |
| 性能下降 | 响应时间增加 > 设计文档阈值 | 性能调优或功能回滚 | [设计文档性能要求] |
| 集成异常 | 与其他模块集成出现问题 | 修复集成问题或隔离变更 | [设计文档集成策略] |
| 依赖冲突 | 新依赖与现有依赖冲突 | 调整依赖版本或寻找替代方案 | [设计文档技术栈约束] |
| 设计约束轻微违规 | 违反B类或C类约束 | 1小时内修复或申请例外 | [设计文档约束等级定义] |

### 🟢 P2级别风险 (计划处理)
| 风险场景 | 触发条件 | 处理策略 | 设计文档依据 |
|---------|----------|----------|-------------|
| 代码规范问题 | 静态检查发现问题 | 规范修复，不阻塞主流程 | [设计文档代码标准] |
| 文档不同步 | 代码与文档不一致 | 更新文档，保持同步 | [设计文档维护要求] |
| 配置优化 | 配置参数需要调优 | 后续优化，不影响主功能 | [设计文档配置策略] |

### 回滚执行命令
```bash
# P0级别：立即回滚（5分钟内）
git log --oneline -5
git checkout -f [稳定commit hash]  
[实际的重新构建命令]
[实际的服务重启命令]

# P1级别：计划回滚（1小时内）
git stash push -m "备份当前修改"
git checkout [稳定分支]
[实际的验证命令]
# 分析问题后决定是修复还是回滚
```

## 📋 执行检查清单

### 实施前检查
- [ ] 设计文档已完全理解
- [ ] 设计约束已100%提取和转化（新增）
- [ ] 架构约束清单已建立（新增）
- [ ] 代码架构已深度调研完成
- [ ] AI认知约束已激活：`@AI_COGNITIVE_CONSTRAINTS`
- [ ] 边界护栏已激活：`@BOUNDARY_GUARD_ACTIVATION`
- [ ] 设计约束护栏已激活：`@DESIGN_CONSTRAINT_VALIDATION`（新增）
- [ ] 开发环境已验证正常
- [ ] Git分支已创建: `feature/[功能名称]`
- [ ] 回滚方案已准备

### 每步实施检查
- [ ] 操作在边界范围内：验证不超出包含范围
- [ ] 设计约束合规：验证符合设计文档约束清单（新增）
- [ ] 架构一致性：验证符合设计文档架构规范（新增）
- [ ] 代码修改 ≤ 50行：单次修改量控制
- [ ] 导入语句完整：所有必要依赖已添加
- [ ] 异常处理完善：包含完整的错误处理逻辑
- [ ] 编译验证通过: `[实际编译命令]`
- [ ] 单元测试通过：相关测试用例执行成功
- [ ] 功能验证符合预期：按设计文档要求验证
- [ ] 验证锚点达成：当前步骤目标完全实现

### 完成验收检查
- [ ] 所有单元测试通过: `[实际测试命令]`
- [ ] 集成测试验证完成：系统整体功能正常
- [ ] 功能验收符合设计文档：所有设计要求实现
- [ ] 性能指标达到设计文档要求：满足性能基准
- [ ] 设计约束100%合规：无违规项（新增）
- [ ] 架构一致性验证通过：符合设计文档架构（新增）
- [ ] 代码审核完成：代码质量符合标准
- [ ] 文档更新同步：相关文档已更新
- [ ] 回滚机制测试：确认回滚方案可用

## 📊 执行状态跟踪

### 实施进度
- [ ] 阶段0完成: 设计文档解析与约束转化 ✓/✗（新增）
- [ ] 阶段1完成: 环境准备 ✓/✗
- [ ] 阶段2完成: 核心功能开发 ✓/✗  
- [ ] 阶段3完成: 集成测试验证 ✓/✗
- [ ] 整体验收: 通过设计文档要求 ✓/✗

### 质量指标监控
- **代码覆盖率**: ____% (目标: >80%)
- **测试通过率**: ____% (目标: 100%)
- **功能完成度**: ____% (基于设计文档)
- **性能达标率**: ____% (基于设计文档要求)
- **边界合规率**: ____% (基于范围边界检查)
- **设计约束合规率**: ____% (基于设计文档约束清单)（新增）
- **架构一致性**: ____% (基于设计文档架构规范)（新增）

---

**严格执行原则**:
1. **设计驱动**: 严格按照设计文档的架构思想和约束条件实施（新增）
2. **约束优先**: 设计约束违规时立即停止并重新规划（新增）
3. **架构优先**: 基于真实架构调研制定实施方案
4. **边界控制**: 每步修改≤50行，禁止跨模块修改
5. **渐进验证**: 每步完成立即验证，确保质量
6. **风险优先**: 遇到P0风险立即回滚，不犹豫
7. **认知约束**: 严格遵循AI认知能力限制
8. **原子操作**: 每个操作独立可验证
9. **设计一致性**: 确保实施结果与设计文档100%一致（新增）

**请严格按照上述实施计划执行，每完成一个步骤立即进行验证！**
```

### L2中等项目 - 生成扩展文档体系

当评估为中等项目时，额外生成以下文档：

**文件**: `{输出目录}/02-{项目名称}-架构设计方案.md`
**文件**: `{输出目录}/03-{项目名称}-模块集成计划.md`
**文件**: `{输出目录}/04-{项目名称}-风险控制方案.md`

**内容要求**: 
- 包含与L1版本相同的所有核心要素
- 增加详细的架构设计分析
- 增加模块间集成策略说明
- 增加专门的风险控制文档

### L3复杂项目 - 生成完整文档矩阵

当评估为复杂项目时，按模块分别生成：

**总体规划文档**:
- `{输出目录}/10-{项目名称}-总体架构方案.md`
- `{输出目录}/11-{项目名称}-实施策略规划.md`

**模块实施文档** (按实际模块生成):
- `{输出目录}/20-{模块1名称}-详细实施计划.md`
- `{输出目录}/21-{模块1名称}-测试验证方案.md`
- `{输出目录}/22-{模块2名称}-详细实施计划.md`
- `{输出目录}/23-{模块2名称}-测试验证方案.md`

**集成验证文档**:
- `{输出目录}/40-{项目名称}-系统集成测试.md`
- `{输出目录}/41-{项目名称}-端到端验证.md`

**内容要求**:
- 每个文档都包含完整的AI认知约束激活
- 每个文档都有严格的实施范围边界定义
- 每个文档都包含详细的风险控制方案
- 所有文档都遵循步骤语义结构要求

## 🎯 严格执行要求

1. **必须先调研**: 使用工具深度调研项目架构，不能依赖文档描述
2. **基于真实架构**: 所有实施计划必须基于实际代码结构制定  
3. **设计文档驱动**: 功能目标和验收标准严格基于设计文档
4. **AI认知约束**: 所有文档必须激活AI认知约束命令
5. **边界护栏**: 所有文档必须包含🚨实施范围边界（必读）章节
6. **验证锚点**: 每个步骤都必须有具体的验证锚点
7. **风险控制**: 所有文档必须包含完整的风险评估和回滚方案
8. **输出目录准确**: 所有文档必须生成到用户指定的输出目录
9. **格式严格一致**: 严格按照上述模板格式生成文档内容
10. **内容具体可执行**: 所有步骤都必须包含具体的命令、代码、验证方法

**执行顺序: 架构调研 → 复杂度评估 → 按模板生成文档 → 保存到指定目录**
```

---

## 🎯 用户使用方式

根据您的使用习惯，标准工作流程是：

1. **提供设计文档链接**: "设计文档: [链接]"
2. **提供提示词链接**: "提示词: [本文件链接]"  
3. **指定输出目录**: "输出目录: docs/xxx/实施计划/"
4. **AI自动执行**: 调研架构 → 学习设计 → 严格按模板生成文档

## 🌟 完善后的提示词特点

✅ **基于F007成功案例**: 集成了F007版本的所有成功要素
✅ **记忆库约束完整**: 整合了记忆库中的所有关键要求
✅ **AI认知约束**: 强制激活AI认知约束和边界护栏
✅ **风险控制完整**: 包含P0/P1/P2三级风险控制体系
✅ **验证锚点系统**: 每个步骤都有具体的验证标准
✅ **操作边界控制**: 明确的允许和禁止操作范围
✅ **执行检查清单**: 实施前、实施中、完成后的完整检查
✅ **代码实施模板**: 现有代码→修改为的具体格式
✅ **部署配置管理**: 构建、运行、数据库变更的完整支持
✅ **执行状态跟踪**: 进度和质量指标的监控机制
✅ **步骤语义结构**: 认知单元+操作边界+验证锚点
✅ **智能复杂度适配**: 根据项目复杂度自动选择文档生成策略

**这样AI就会严格按照完善的提示词，生成高质量、完整、可执行的实施计划文档！**

---

## 📖 模仿参照示例

### 实际使用示例1：F007项目成功案例

**用户输入**:
```
设计文档: docs/features/F007-建立Commons库的治理机制-20250610/设计文档.md
提示词: docs/ai-memory/templates/AI严格生成实施计划文档的提示词.md
输出目录: docs/features/F007-建立Commons库的治理机制-20250610/实施计划/
```

**AI执行流程**:
1. ✅ **架构调研**: 使用codebase_search分析commons库项目结构
2. ✅ **度量评估**: AI专业度量参数评估 = 42分 (L2中等项目)
3. ✅ **文档生成**: 生成单一实施计划文档，包含完整的风险控制体系
4. ✅ **质量验收**: 包含P0/P1/P2三级风险表格，完整验证锚点系统

**生成的文档示例**:
```markdown
# Commons库治理机制 - AI执行实施计划

## 📋 项目概览
- **项目名称**: Commons库治理机制
- **技术栈**: Java 11, Maven, Spring Boot 2.7.x (基于架构调研)
- **复杂度评估**: L2中等项目 (AI度量参数评估总分: 42/100分)
- **AI度量详情**: 
  - 认知复杂度: 15分 (L2级，5个核心概念)
  - 记忆边界压力: 8分 (40%压力，需要分块处理)
  - 幻觉风险系数: 7分 (0.25风险，部分未知领域)
  - 上下文切换成本: 9分 (4次切换，需要状态管理)
  - 验证锚点密度: 3分 (标准密度验证)

## 🧠 AI认知约束激活
@L1:global-constraints
@L1:ai-implementation-design-principles
@AI_COGNITIVE_CONSTRAINTS
@BOUNDARY_GUARD_ACTIVATION

## 🔍 现有架构调研结果
### 项目结构分析
commons-lib/
├── src/main/java/com/company/commons/
│   ├── util/          # 工具类模块
│   ├── exception/     # 异常处理模块  
│   └── validation/    # 验证模块
├── pom.xml           # Maven配置
└── README.md         # 项目文档

### 核心模块识别
- **异常处理模块**: 统一异常体系 - 位置: `src/main/java/com/company/commons/exception/`
- **工具类模块**: 通用工具方法 - 位置: `src/main/java/com/company/commons/util/`

## 📅 详细实施步骤

### 阶段1: 环境准备和基础设施 (预估1天)
**认知单元**: 开发环境配置和基础验证 (≤3个概念)
**操作边界**: 仅限环境检查和基础配置，不涉及业务代码修改

#### 1.1 开发环境检查
- [x] **环境版本验证**
  ```bash
  java -version  # 验证Java 11
  mvn --version  # 验证Maven版本
  ```

### 阶段2: 核心功能实施 (预估2天)
#### 2.1 异常体系统一实施 (≤50行代码)
**目标**: 建立统一的异常处理机制
**修改文件**: `src/main/java/com/company/commons/exception/CommonException.java`

**现有代码** (基于read_file调研):
```java
// 当前不存在统一异常类
```

**修改为**:
```java
package com.company.commons.exception;

public class CommonException extends RuntimeException {
    private final String errorCode;
    
    public CommonException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}
```

**智能编译验证** (风险基础策略):
**风险评估**: 中风险 (新文件创建，方法添加)
**智能验证点**:
```bash
# 强制编译检查 (新文件创建属于中风险)
mvn compile
# 功能验证
mvn test -Dtest=CommonExceptionTest
# 冗余代码检查: 新文件无冗余import问题
```

## ⚠️ 风险评估与应对方案

### 🔴 P0级别风险 (立即回滚)
| 风险场景 | 触发条件 | 立即回滚操作 |
|---------|----------|-------------|
| 编译失败 | mvn compile失败且10分钟无法修复 | `git checkout -f HEAD~1` |
| 依赖冲突 | Maven依赖解析失败 | 立即回滚pom.xml修改 |

### 🟡 P1级别风险 (1小时内决策)  
| 风险场景 | 触发条件 | 处理策略 |
|---------|----------|----------|
| 测试失败 | 单元测试通过率 < 80% | 分析失败原因，修复或回滚 |

## 📋 执行检查清单

### 实施前检查
- [x] 设计文档已完全理解
- [x] 设计约束已100%提取和转化（新增）
- [x] 架构约束清单已建立（新增）
- [x] 代码架构已深度调研完成
- [x] AI认知约束已激活
- [x] 开发环境已验证正常

### 每步实施检查
- [x] 代码修改 ≤ 50行
- [x] AI度量参数监控: 认知复杂度≤5个概念
- [x] 智能编译验证: 风险基础策略应用
- [x] 冗余代码检查: import语句、无用函数检查

### 完成验收检查
- [x] 强制代码清理: 冗余检测覆盖率≥95%
- [x] 功能验收符合设计文档
- [x] AI认知约束合规率=100%
```

**成功要素分析**:
✅ **AI度量参数**: 准确评估了5个维度得分
✅ **智能编译验证**: 应用风险基础策略，避免机械化验证
✅ **强制架构分析**: 开始前深入调研真实项目结构
✅ **DRY原则执行**: 引用语法规范，可复用组件分析
✅ **风险控制完整**: P0/P1/P2三级风险表格
✅ **强制代码清理**: 执行完成后冗余代码检查

### 实际使用示例2：简单功能开发

**用户输入**:
```
设计文档: docs/features/simple-crud/设计文档.md  
提示词: docs/ai-memory/templates/AI严格生成实施计划文档的提示词.md
输出目录: docs/features/simple-crud/实施计划/
```

**AI执行特点**:
- 📊 **度量评估**: 18分 (L1简单项目)，生成单一实施计划文档
- 🧠 **认知约束**: 激活基础AI认知约束，低风险处理
- 🔍 **架构调研**: 简化但必要的架构分析
- ⚡ **智能验证**: 批次验证策略，减少验证频次

### 实际使用示例3：复杂架构项目

**用户输入**:
```
设计文档: docs/features/microservice-migration/设计文档.md
提示词: docs/ai-memory/templates/AI严格生成实施计划文档的提示词.md  
输出目录: docs/features/microservice-migration/实施计划/
```

**AI执行特点**:
- 📊 **度量评估**: 85分 (L3复杂项目)，生成完整文档矩阵
- 📁 **文档体系**: 总体规划 + 模块实施 + 集成验证文档
- 🧠 **认知约束**: 强化AI认知约束，高风险防护
- 🔄 **分解策略**: 强制分解任务，多点验证确认

## 🎯 提示词使用要点

1. **标准三元组输入**: "设计文档 + 提示词 + 输出目录"
2. **AI自动化执行**: 无需手动指导，AI严格按流程执行
3. **智能复杂度适配**: 根据项目复杂度自动选择文档生成策略
4. **完整质量保障**: 集成F007成功要素和记忆库约束要求
5. **可执行性保证**: 所有步骤都有具体命令、代码、验证方法