# V3.1生成器代码留白标准格式定义

## 文档信息
- **文档ID**: T001-V3.1-CODE-PLACEHOLDER-STANDARD-FORMAT
- **创建日期**: 2025-06-14
- **版本**: v3.1-enhanced
- **目标**: 定义实施计划文档中AI代码填充区域的标准格式（对标标准实施计划严格约束）
- **适用范围**: 所有V3.1生成器生成的实施计划文档
- **置信度策略**: 75-80%置信度输出 + {{AI_FILL_REQUIRED}}标记补充

## 标准代码留白格式（基于标准实施计划约束）

### 1. 基础格式模板（增强版）

```markdown
### {步骤编号} {功能描述}

**实施指导**:
- 参考: @{json_file} → {json_path}
- 约束: {具体约束条件}
- 验证: {验证方法}
- **修改边界**: {{AI_FILL_REQUIRED}} // 需要AI分析具体修改范围，如"仅修改异常处理相关代码，保持其他逻辑不变"

**原代码**:
```java
{{AI_FILL_REQUIRED}} // 需要AI提供具体的原始代码片段
```

**替换为**:
```java
// 【AI代码填充区域】- {具体功能描述}
// 📋 JSON约束引用: @{json_file} → {json_path}
// 🧠 记忆库约束: @{memory_constraint_path}
// ⚡ AI质量约束:
//   - 认知复杂度: {complexity_score:.2f} (目标: ≤0.7)
//   - 记忆压力: {memory_pressure:.2f} (目标: ≤0.6)
//   - 幻觉风险: {hallucination_risk:.2f} (目标: ≤0.3)
//   - 代码行数: ≤{max_lines}行 (强制限制≤50行)
// 🎯 验证锚点: {validation_anchors}

// TODO: AI在此处填入{具体功能}的实现代码
// 实施约束:
// - {约束条件1}
// - {约束条件2}
// - {约束条件3}
// - **立即验证**: 每次修改后立即编译验证

{{AI_FILL_REQUIRED}} // 需要AI提供精确的替换代码
```

**关键修改点**:
- 第{{AI_FILL_REQUIRED}}行 - {{AI_FILL_REQUIRED}} // 需要AI提供具体行号和修改描述
- 第{{AI_FILL_REQUIRED}}行 - {{AI_FILL_REQUIRED}} // 需要AI提供具体行号和修改描述

**验证命令**: `mvn compile -pl {{AI_FILL_REQUIRED}}` // 需要AI填入具体模块名
**成功标准**: {{AI_FILL_REQUIRED}} // 需要AI定义具体成功标准，如"编译成功，接口方法签名正确，无语法错误"
```

### 2. 具体示例格式（对标标准实施计划）

#### 2.1 接口实现代码留白（精确约束版）

```markdown
### 2.1 Plugin接口实现

**实施指导**:
- 参考: @01-architecture-overview.json → interface_system.core_interfaces[0]
- 约束: 必须实现start()、stop()、getContext()方法
- 验证: 编译成功且接口完整实现
- **修改边界**: 仅创建新接口文件，不修改现有代码

**文件路径**: {{AI_FILL_REQUIRED}} // 需要AI提供精确文件路径，如"src/main/java/org/xkong/cloud/commons/nexus/api/Plugin.java"

**原代码**:
```java
{{AI_FILL_REQUIRED}} // 如果是新建文件，此处为"// 新建文件，无原代码"
```

**替换为**:
```java
// 【AI代码填充区域】- Plugin接口实现
// 📋 JSON约束引用: @01-architecture-overview.json → interface_system.core_interfaces[0].method_signature_full
// 🧠 记忆库约束: @L1:ai-implementation-design-principles → interface_implementation_rules
// ⚡ AI质量约束:
//   - 认知复杂度: 0.4 (目标: ≤0.7)
//   - 记忆压力: 0.3 (目标: ≤0.6)
//   - 幻觉风险: 0.2 (目标: ≤0.3)
//   - 代码行数: ≤50行 (强制限制)
// 🎯 验证锚点: 编译验证、接口完整性检查、方法签名验证

// TODO: AI在此处实现Plugin接口
// 实施约束:
// - 包名: org.xkong.cloud.commons.nexus.api
// - 方法签名: 严格按照JSON中的method_signature_full
// - 异常处理: 必须包含PluginException处理
// - 生命周期: 实现完整的插件生命周期管理
// - **立即验证**: 创建后立即编译验证

{{AI_FILL_REQUIRED}} // 需要AI提供完整的接口定义代码
```

**关键实现点**:
- {{AI_FILL_REQUIRED}} // 需要AI分析start方法的具体签名和异常处理
- {{AI_FILL_REQUIRED}} // 需要AI分析stop方法的具体签名和异常处理
- {{AI_FILL_REQUIRED}} // 需要AI分析getContext方法的具体签名

**验证命令**: `mvn compile -pl {{AI_FILL_REQUIRED}}` // 需要AI填入具体模块名
**成功标准**: {{AI_FILL_REQUIRED}} // 需要AI定义具体标准，如"编译成功，接口方法签名正确，无语法错误，通过接口完整性检查"
```

#### 2.2 服务类实现代码留白（精确约束版）

```markdown
### 3.2 ServiceBus核心实现

**实施指导**:
- 参考: @03-service-bus-and-communication.json → service_bus_implementation
- 约束: 异步事件处理，性能≥10,000 events/second
- 验证: 性能测试通过，事件处理正确
- **修改边界**: {{AI_FILL_REQUIRED}} // 需要AI分析具体修改范围

**文件路径**: {{AI_FILL_REQUIRED}} // 需要AI提供精确文件路径

**执行策略**: {{AI_FILL_REQUIRED}} // 需要AI制定分批处理策略，如"分3批实现：1.核心接口 2.事件处理 3.性能优化"

**原代码**:
```java
{{AI_FILL_REQUIRED}} // 需要AI提供现有ServiceBus实现的原始代码片段
```

**替换为**:
```java
// 【AI代码填充区域】- ServiceBus核心实现
// 📋 JSON约束引用: @03-service-bus-and-communication.json → service_bus_implementation.core_methods
// 🧠 记忆库约束: @L1:ai-implementation-design-principles → service_implementation_rules
// ⚡ AI质量约束:
//   - 认知复杂度: 0.6 (目标: ≤0.7)
//   - 记忆压力: 0.5 (目标: ≤0.6)
//   - 幻觉风险: 0.25 (目标: ≤0.3)
//   - 代码行数: ≤50行 (强制限制)
// 🎯 验证锚点: 编译验证、单元测试、性能测试

// TODO: AI在此处实现ServiceBus核心功能
// 实施约束:
// - 异步处理: 使用CompletableFuture或Virtual Threads
// - 性能要求: ≥10,000 events/second
// - 线程安全: 必须是线程安全的实现
// - 错误处理: 完整的异常处理和重试机制
// - **立即验证**: 每个方法实现后立即编译和单元测试验证

{{AI_FILL_REQUIRED}} // 需要AI提供完整的ServiceBus实现代码
```

**关键修改点**:
- 第{{AI_FILL_REQUIRED}}行 - {{AI_FILL_REQUIRED}} // 需要AI提供publish方法的具体修改
- 第{{AI_FILL_REQUIRED}}行 - {{AI_FILL_REQUIRED}} // 需要AI提供subscribe方法的具体修改
- 第{{AI_FILL_REQUIRED}}行 - {{AI_FILL_REQUIRED}} // 需要AI提供unsubscribe方法的具体修改

**验证命令**: `mvn test -Dtest={{AI_FILL_REQUIRED}}` // 需要AI填入具体测试类名
**成功标准**: {{AI_FILL_REQUIRED}} // 需要AI定义具体标准，如"所有测试通过，性能测试达到10,000 events/second，无内存泄漏"
```

#### 2.3 配置类代码留白

```markdown
### 4.1 Nexus配置类实现

**实施指导**:
- 参考: @01-architecture-overview.json → configuration_schema.spring_boot_configuration
- 约束: Spring Boot 3.4.5兼容，支持条件装配
- 验证: 配置加载成功，条件装配正确

```java
// 【AI代码填充区域】- Nexus配置类实现
// 📋 JSON约束引用: @01-architecture-overview.json → configuration_schema.spring_boot_configuration
// 🧠 记忆库约束: @L1:ai-implementation-design-principles → configuration_implementation_rules
// ⚡ AI质量约束: 
//   - 认知复杂度: 0.3 (目标: ≤0.7)
//   - 记忆压力: 0.2 (目标: ≤0.6)
//   - 幻觉风险: 0.1 (目标: ≤0.3)
//   - 代码行数: ≤30行
// 🎯 验证锚点: 配置加载验证、条件装配测试

// TODO: AI在此处实现Nexus自动配置
// 实施约束:
// - Spring Boot版本: 3.4.5+
// - 条件装配: @ConditionalOnProperty(name = "nexus.enabled", havingValue = "true")
// - 配置属性: 使用@ConfigurationProperties绑定
// - Bean定义: 定义核心Bean的创建逻辑

@Configuration
@EnableConfigurationProperties(NexusProperties.class)
@ConditionalOnProperty(name = "nexus.enabled", havingValue = "true", matchIfMissing = true)
public class NexusAutoConfiguration {
    
    // AI填充：配置属性注入
    // 约束：private final NexusProperties properties;
    
    // AI填充：构造函数
    // 约束：public NexusAutoConfiguration(NexusProperties properties)
    
    // AI填充：ServiceBus Bean定义
    // 约束：@Bean @ConditionalOnMissingBean
    
    // AI填充：PluginManager Bean定义
    // 约束：@Bean @ConditionalOnMissingBean
}
```

**验证命令**: `mvn spring-boot:run -Dspring-boot.run.arguments="--nexus.enabled=true"`
**成功标准**: 应用启动成功，配置属性正确加载，Bean创建成功
```

### 3. 代码留白格式规范（对标标准实施计划）

#### 3.1 必须包含的元素（增强版）

1. **区域标识**: `// 【AI代码填充区域】- {功能描述}`
2. **JSON约束引用**: `// 📋 JSON约束引用: @{file} → {path}`
3. **记忆库约束**: `// 🧠 记忆库约束: @{constraint_path}`
4. **AI质量约束**: 包含认知复杂度、记忆压力、幻觉风险、代码行数限制
5. **验证锚点**: `// 🎯 验证锚点: {具体验证点}`
6. **TODO注释**: `// TODO: AI在此处填入{具体功能}的实现代码`
7. **实施约束**: 具体的技术约束和要求
8. **立即验证要求**: `// - **立即验证**: 每次修改后立即编译验证`
9. **原代码对比**: 必须提供原代码和替换代码的对比
10. **修改边界定义**: 明确指定修改范围和边界
11. **执行策略**: 分批处理策略和具体步骤
12. **{{AI_FILL_REQUIRED}}标记**: 标记需要AI补充的内容

#### 3.2 质量约束标准值（严格版）

- **认知复杂度**: 目标 ≤0.7，简单任务≤0.3，中等任务≤0.5，复杂任务≤0.7
- **记忆压力**: 目标 ≤0.6，基于代码行数和概念数量计算
- **幻觉风险**: 目标 ≤0.3，基于抽象概念和未定义引用计算
- **代码行数**: **强制限制≤50行**，简单功能≤30行，**绝对不能超过**
- **立即验证**: **每个步骤修改后必须立即编译验证**
- **分批处理**: 复杂修改必须分批进行，每批≤50行

#### 3.3 验证锚点类型（精确版）

- **编译验证**: `mvn compile -pl {{AI_FILL_REQUIRED}}` // 需要AI填入具体模块
- **单元测试**: `mvn test -Dtest={{AI_FILL_REQUIRED}}` // 需要AI填入具体测试类
- **集成测试**: `mvn verify -pl {{AI_FILL_REQUIRED}}` // 需要AI填入具体模块
- **性能测试**: {{AI_FILL_REQUIRED}} // 需要AI提供具体性能验证命令
- **功能验证**: {{AI_FILL_REQUIRED}} // 需要AI提供具体功能验证步骤

#### 3.4 {{AI_FILL_REQUIRED}}标记使用规范

- **文件路径**: `{{AI_FILL_REQUIRED}}` - 需要AI提供精确的文件路径
- **行号信息**: `第{{AI_FILL_REQUIRED}}行` - 需要AI提供具体行号
- **原代码片段**: `{{AI_FILL_REQUIRED}}` - 需要AI提供原始代码
- **替换代码**: `{{AI_FILL_REQUIRED}}` - 需要AI提供替换代码
- **修改边界**: `{{AI_FILL_REQUIRED}}` - 需要AI定义修改范围
- **验证命令**: `{{AI_FILL_REQUIRED}}` - 需要AI提供具体验证命令
- **成功标准**: `{{AI_FILL_REQUIRED}}` - 需要AI定义成功标准

### 4. 使用指南（对标标准实施计划）

#### 4.1 V3.1生成器实现要求（增强版）

```python
class CodePlaceholderGenerator:
    """代码占位符生成器（对标标准实施计划严格约束）"""

    def generate_placeholder(self, code_info: Dict) -> str:
        """生成标准格式的代码占位符（75-80%置信度 + AI_FILL_REQUIRED标记）"""
        return f"""
### {code_info['step_number']} {code_info['function_description']}

**实施指导**:
- 参考: @{code_info['json_file']} → {code_info['json_path']}
- 约束: {code_info['constraints']}
- 验证: {code_info['validation_method']}
- **修改边界**: {{{{AI_FILL_REQUIRED}}}} // 需要AI分析具体修改范围

**文件路径**: {{{{AI_FILL_REQUIRED}}}} // 需要AI提供精确文件路径

**执行策略**: {{{{AI_FILL_REQUIRED}}}} // 需要AI制定分批处理策略

**原代码**:
```java
{{{{AI_FILL_REQUIRED}}}} // 需要AI提供具体的原始代码片段
```

**替换为**:
```java
// 【AI代码填充区域】- {code_info['function_description']}
// 📋 JSON约束引用: @{code_info['json_file']} → {code_info['json_path']}
// 🧠 记忆库约束: @{code_info['memory_constraint']}
// ⚡ AI质量约束:
//   - 认知复杂度: {code_info['cognitive_complexity']:.2f} (目标: ≤0.7)
//   - 记忆压力: {code_info['memory_pressure']:.2f} (目标: ≤0.6)
//   - 幻觉风险: {code_info['hallucination_risk']:.2f} (目标: ≤0.3)
//   - 代码行数: ≤{code_info['max_lines']}行 (强制限制≤50行)
// 🎯 验证锚点: {code_info['validation_anchors']}

// TODO: AI在此处填入{code_info['function_description']}的实现代码
// 实施约束:
{self._format_constraints(code_info['implementation_constraints'])}
// - **立即验证**: 每次修改后立即编译验证

{{{{AI_FILL_REQUIRED}}}} // 需要AI提供精确的替换代码
```

**关键修改点**:
- 第{{{{AI_FILL_REQUIRED}}}}行 - {{{{AI_FILL_REQUIRED}}}} // 需要AI提供具体行号和修改描述

**验证命令**: `{{{{AI_FILL_REQUIRED}}}}` // 需要AI填入具体验证命令
**成功标准**: {{{{AI_FILL_REQUIRED}}}} // 需要AI定义具体成功标准
"""
```

#### 4.2 AI填充时的处理规则（严格版）

1. **严格遵循约束**: AI必须严格按照JSON约束和记忆库约束实现
2. **行数限制**: **绝对不能超过50行**，这是硬性限制
3. **立即验证**: **每次修改后必须立即编译验证**，不能跳过
4. **分批处理**: 复杂修改必须分批进行，每批处理后立即验证
5. **质量标准**: 必须满足认知复杂度、记忆压力、幻觉风险的要求
6. **原代码对比**: 必须提供精确的原代码和替换代码对比
7. **边界控制**: 严格按照修改边界定义，不能超出范围
8. **{{AI_FILL_REQUIRED}}处理**: 遇到此标记时，AI必须提供具体、精确的内容

#### 4.3 置信度管理策略

- **75-80%置信度输出**: V3.1生成器基于JSON信息生成基础框架
- **{{AI_FILL_REQUIRED}}标记**: 标记需要AI补充的20-25%内容
- **渐进式完善**: AI逐步填充标记内容，达到100%完整度
- **质量验证**: 每次填充后进行质量检查和验证

#### 4.4 实施文档长度控制机制（800行限制）

##### 4.4.1 长度估算算法

```python
class DocumentLengthController:
    """实施文档长度控制器（800行限制）"""

    # 基础组件行数估算
    BASE_COMPONENTS = {
        "document_header": 15,           # 文档头部信息
        "project_overview": 25,          # 项目概述
        "implementation_phases": 30,     # 实施阶段框架
        "risk_control": 20,             # 风险控制
        "success_criteria": 15,         # 成功标准
        "ai_constraints": 25,           # AI执行约束
        "document_footer": 10           # 文档尾部
    }

    # 代码块行数估算（不可分割）
    CODE_BLOCK_ESTIMATES = {
        "interface_definition": 45,      # 接口定义代码块
        "class_implementation": 60,      # 类实现代码块
        "configuration_class": 35,      # 配置类代码块
        "exception_handling": 40,       # 异常处理代码块
        "annotation_definition": 25,    # 注解定义代码块
        "test_implementation": 50,      # 测试实现代码块
        "maven_configuration": 30,      # Maven配置代码块
        "spring_configuration": 35      # Spring配置代码块
    }

    # 辅助组件行数估算
    AUXILIARY_COMPONENTS = {
        "json_reference": 3,            # JSON约束引用
        "validation_command": 2,        # 验证命令
        "success_standard": 3,          # 成功标准
        "ai_fill_required_marker": 1,   # AI填充标记
        "modification_boundary": 5,     # 修改边界说明
        "execution_strategy": 8         # 执行策略
    }

    def estimate_document_length(self, selected_components: Dict) -> int:
        """估算文档总长度"""
        total_lines = 0

        # 基础组件（必须包含）
        total_lines += sum(self.BASE_COMPONENTS.values())

        # 代码块组件（按优先级选择）
        for block_type, count in selected_components['code_blocks'].items():
            total_lines += self.CODE_BLOCK_ESTIMATES[block_type] * count

        # 辅助组件
        for aux_type, count in selected_components['auxiliary'].items():
            total_lines += self.AUXILIARY_COMPONENTS[aux_type] * count

        return total_lines
```

##### 4.4.2 基于阶段逻辑的分割策略（对标标准实施计划）

```python
def phase_based_document_splitting(self, implementation_plan: Dict, max_lines: int = 800) -> List[Dict]:
    """基于标准实施计划阶段逻辑的文档分割策略"""

    # 标准实施计划阶段定义（基于标准实施计划文档结构）
    STANDARD_PHASES = {
        "phase_0": {
            "name": "前置准备阶段",
            "description": "异常分类优化、依赖配置",
            "components": ["exception_classification", "dependency_configuration"],
            "estimated_lines": 180,  # 基础框架 + 2个阶段内容
            "complexity": "medium",
            "mandatory": True  # 必须包含的阶段
        },
        "phase_1": {
            "name": "设计映射阶段",
            "description": "异常类映射设计、错误码定义",
            "components": ["exception_mapping", "error_code_definition"],
            "estimated_lines": 160,
            "complexity": "low",
            "mandatory": True
        },
        "phase_2": {
            "name": "核心实现阶段",
            "description": "核心组件异常切换、代码修改",
            "components": ["core_component_modification", "exception_replacement"],
            "estimated_lines": 280,  # 最复杂的阶段，包含大量代码修改
            "complexity": "high",
            "mandatory": True
        },
        "phase_3": {
            "name": "集成配置阶段",
            "description": "异常处理器集成、配置文件修改",
            "components": ["exception_handler_integration", "configuration_update"],
            "estimated_lines": 140,
            "complexity": "medium",
            "mandatory": True
        },
        "phase_4": {
            "name": "验证测试阶段",
            "description": "测试验证、文档更新",
            "components": ["test_verification", "documentation_update"],
            "estimated_lines": 120,
            "complexity": "medium",
            "mandatory": False  # 可选阶段，可单独成文档
        },
        "phase_5": {
            "name": "项目适配阶段",
            "description": "Core项目配套修改",
            "components": ["core_project_adaptation"],
            "estimated_lines": 100,
            "complexity": "low",
            "mandatory": False
        }
    }

    documents = []
    current_document = {
        "document_type": "implementation_plan",
        "phases": [],
        "estimated_lines": sum(self.BASE_COMPONENTS.values()),  # 基础框架
        "document_index": 1
    }

    # 按阶段顺序处理（保持逻辑连贯性）
    for phase_id, phase_info in STANDARD_PHASES.items():
        phase_lines = phase_info["estimated_lines"]

        # 检查是否能加入当前文档
        if current_document["estimated_lines"] + phase_lines <= max_lines:
            current_document["phases"].append({
                "phase_id": phase_id,
                "phase_name": phase_info["name"],
                "phase_description": phase_info["description"],
                "components": phase_info["components"],
                "estimated_lines": phase_lines
            })
            current_document["estimated_lines"] += phase_lines
        else:
            # 当前文档已满，创建新文档
            if current_document["phases"]:  # 确保当前文档不为空
                documents.append(current_document)

            # 创建新文档
            current_document = {
                "document_type": "implementation_plan",
                "phases": [{
                    "phase_id": phase_id,
                    "phase_name": phase_info["name"],
                    "phase_description": phase_info["description"],
                    "components": phase_info["components"],
                    "estimated_lines": phase_lines
                }],
                "estimated_lines": sum(self.BASE_COMPONENTS.values()) + phase_lines,
                "document_index": len(documents) + 2
            }

    # 添加最后一个文档
    if current_document["phases"]:
        documents.append(current_document)

    return documents
```

##### 4.4.3 阶段内容细化策略

```python
def generate_phase_content(self, phase_info: Dict, json_data: Dict) -> str:
    """基于阶段信息和JSON数据生成具体的实施计划内容"""

    phase_templates = {
        "phase_0": {
            "sections": [
                "### 阶段0：前置准备",
                "#### 步骤0.1：异常分类优化",
                "#### 步骤0.2：依赖配置"
            ],
            "code_blocks_per_section": [0, 1, 1],  # 每个section的代码块数量
            "ai_markers_per_section": [2, 3, 2]   # 每个section的AI标记数量
        },
        "phase_1": {
            "sections": [
                "### 阶段1：设计映射阶段",
                "#### 步骤1.1：异常类映射设计",
                "#### 步骤1.2：错误码定义"
            ],
            "code_blocks_per_section": [0, 1, 2],
            "ai_markers_per_section": [1, 4, 3]
        },
        "phase_2": {
            "sections": [
                "### 阶段2：核心实现阶段",
                "#### 步骤2.1：核心组件修改",
                "#### 步骤2.2：异常替换实现"
            ],
            "code_blocks_per_section": [0, 3, 2],  # 最多代码块的阶段
            "ai_markers_per_section": [2, 6, 4]
        },
        "phase_3": {
            "sections": [
                "### 阶段3：集成配置阶段",
                "#### 步骤3.1：异常处理器集成",
                "#### 步骤3.2：配置文件更新"
            ],
            "code_blocks_per_section": [0, 2, 1],
            "ai_markers_per_section": [1, 3, 2]
        },
        "phase_4": {
            "sections": [
                "### 阶段4：验证测试阶段",
                "#### 步骤4.1：测试验证",
                "#### 步骤4.2：文档更新"
            ],
            "code_blocks_per_section": [0, 2, 0],
            "ai_markers_per_section": [1, 4, 2]
        },
        "phase_5": {
            "sections": [
                "### 阶段5：项目适配阶段",
                "#### 步骤5.1：Core项目配套修改"
            ],
            "code_blocks_per_section": [0, 1],
            "ai_markers_per_section": [1, 3]
        }
    }

    phase_id = phase_info["phase_id"]
    template = phase_templates.get(phase_id, phase_templates["phase_0"])

    content_lines = []

    for i, section in enumerate(template["sections"]):
        content_lines.append(section)
        content_lines.append("")  # 空行

        # 添加实施指导
        content_lines.extend([
            "**实施指导**:",
            f"- 参考: @{{{{AI_FILL_REQUIRED}}}} → {{{{AI_FILL_REQUIRED}}}}",
            f"- 约束: {{{{AI_FILL_REQUIRED}}}}",
            f"- 验证: {{{{AI_FILL_REQUIRED}}}}",
            f"- **修改边界**: {{{{AI_FILL_REQUIRED}}}}",
            ""
        ])

        # 添加代码块（如果该section有代码块）
        code_blocks_count = template["code_blocks_per_section"][i]
        for j in range(code_blocks_count):
            content_lines.extend([
                f"**文件路径**: {{{{AI_FILL_REQUIRED}}}}",
                "",
                "**原代码**:",
                "```java",
                "{{AI_FILL_REQUIRED}} // 需要AI提供具体的原始代码片段",
                "```",
                "",
                "**替换为**:",
                "```java",
                "// 【AI代码填充区域】- {{AI_FILL_REQUIRED}}",
                "// 📋 JSON约束引用: @{{AI_FILL_REQUIRED}} → {{AI_FILL_REQUIRED}}",
                "// 🧠 记忆库约束: @{{AI_FILL_REQUIRED}}",
                "// ⚡ AI质量约束:",
                "//   - 认知复杂度: {{AI_FILL_REQUIRED}} (目标: ≤0.7)",
                "//   - 记忆压力: {{AI_FILL_REQUIRED}} (目标: ≤0.6)",
                "//   - 幻觉风险: {{AI_FILL_REQUIRED}} (目标: ≤0.3)",
                "//   - 代码行数: ≤50行 (强制限制)",
                "// 🎯 验证锚点: {{AI_FILL_REQUIRED}}",
                "",
                "// TODO: AI在此处填入{{AI_FILL_REQUIRED}}的实现代码",
                "// 实施约束:",
                "// - {{AI_FILL_REQUIRED}}",
                "// - **立即验证**: 每次修改后立即编译验证",
                "",
                "{{AI_FILL_REQUIRED}} // 需要AI提供精确的替换代码",
                "```",
                ""
            ])

        # 添加验证部分
        content_lines.extend([
            "**关键修改点**:",
            f"- 第{{{{AI_FILL_REQUIRED}}}}行 - {{{{AI_FILL_REQUIRED}}}}",
            "",
            "**验证命令**: `{{{{AI_FILL_REQUIRED}}}}`",
            "**成功标准**: {{{{AI_FILL_REQUIRED}}}}",
            ""
        ])

    return "\n".join(content_lines)
```

##### 4.4.4 文档生成主控制器

```python
def generate_implementation_plan_documents(self, json_data: Dict, max_lines: int = 800) -> List[str]:
    """生成符合标准实施计划逻辑的实施文档（800行限制）"""

    # 1. 基于阶段逻辑分割文档
    document_plans = self.phase_based_document_splitting(json_data, max_lines)

    generated_documents = []

    for doc_plan in document_plans:
        document_content = []

        # 2. 添加文档头部（标准格式）
        document_content.extend([
            f"# 实施计划文档 - 第{doc_plan['document_index']}部分",
            "",
            "## 文档信息",
            "- **文档ID**: {{AI_FILL_REQUIRED}}",
            "- **创建日期**: {{AI_FILL_REQUIRED}}",
            "- **版本**: v3.1",
            "- **实施方式**: 基于标准实施计划逻辑的阶段化实施",
            "- **执行原则**: 确认当前状态，分批验证，风险优先识别",
            "- **质量标准**: 每个步骤限制在50行代码以内，立即编译验证",
            "- **ACE优化**: 选择性ACE触发，平衡代码理解精度与执行效率",
            "",
            "## 项目概述",
            "",
            "### 目标",
            "{{AI_FILL_REQUIRED}} // 需要AI基于JSON数据填入具体目标",
            "",
            "### 当前状态分析",
            "{{AI_FILL_REQUIRED}} // 需要AI基于JSON数据分析当前状态",
            "",
            "### 实施范围",
            "{{AI_FILL_REQUIRED}} // 需要AI基于JSON数据定义实施范围",
            "",
            "## 实施计划",
            ""
        ])

        # 3. 按阶段生成内容（保持标准实施计划逻辑）
        for phase in doc_plan["phases"]:
            phase_content = self.generate_phase_content(phase, json_data)
            document_content.append(phase_content)

        # 4. 添加文档尾部（标准格式）
        document_content.extend([
            "",
            "## 风险控制",
            "",
            "### 回滚准备",
            "**风险优先原则**: 识别潜在风险点，制定预防措施",
            "**执行指引**: {{AI_FILL_REQUIRED}} // 需要AI提供具体回滚策略",
            "",
            "### 验证检查点",
            "**质量门禁**: 每个检查点必须100%通过才能继续",
            "**执行指引**: {{AI_FILL_REQUIRED}} // 需要AI提供具体检查清单",
            "",
            "## 成功标准",
            "",
            "### 功能标准",
            "{{AI_FILL_REQUIRED}} // 需要AI定义功能验证标准",
            "",
            "### 技术标准",
            "{{AI_FILL_REQUIRED}} // 需要AI定义技术验证标准",
            "",
            "## AI执行约束",
            "",
            "### 认知复杂度管理",
            "**执行指引**: @AI_COGNITIVE_CONSTRAINTS, @MEMORY_BOUNDARY_CHECK, @HALLUCINATION_PREVENTION",
            "",
            "### 关键执行原则",
            "- 每个步骤限制在50行代码以内",
            "- 每个文件修改后立即编译验证",
            "- 高复杂度阶段需要分批处理",
            "- 所有假设必须有对应的代码状态验证",
            "",
            "### Interactive Feedback使用策略",
            "**最佳用户体验原则**: 最小化对人类工作的打扰，最大化AI自主执行能力",
            "- **正常执行**: AI完全自主执行所有阶段，无需中间确认",
            "- **遇到问题**: AI无法解决的问题时自动触发interactive_feedback寻求帮助",
            "- **项目完成**: 必须使用interactive_feedback提供完整的项目执行报告"
        ])

        # 5. 生成最终文档
        final_document = "\n".join(document_content)

        # 6. 验证长度
        validation_result = self.validate_document_length(final_document)
        if not validation_result["within_limit"]:
            # 如果超出限制，需要进一步分割或优化
            final_document = self.optimize_document_length(final_document, max_lines)

        generated_documents.append(final_document)

    return generated_documents

def validate_document_length(self, generated_document: str) -> Dict:
    """验证生成文档的实际长度（基于标准实施计划结构）"""

    lines = generated_document.split('\n')
    actual_lines = len(lines)

    # 分析行数分布（按标准实施计划结构）
    analysis = {
        "total_lines": actual_lines,
        "within_limit": actual_lines <= 800,
        "utilization_rate": actual_lines / 800,
        "phase_distribution": {},  # 各阶段行数分布
        "line_distribution": {
            "document_header": 0,
            "phase_content": 0,
            "code_blocks": 0,
            "ai_markers": 0,
            "validation_commands": 0,
            "document_footer": 0
        }
    }

    # 统计各类型行数（基于标准实施计划结构）
    current_section = "header"
    in_code_block = False
    current_phase = None

    for line in lines:
        if line.strip().startswith('### 阶段'):
            current_section = "phase"
            current_phase = line.strip()
            analysis["phase_distribution"][current_phase] = 0
        elif line.strip().startswith('## 风险控制'):
            current_section = "footer"
        elif line.strip().startswith('```'):
            in_code_block = not in_code_block

        # 统计行数
        if in_code_block:
            analysis["line_distribution"]["code_blocks"] += 1
        elif '{{AI_FILL_REQUIRED}}' in line:
            analysis["line_distribution"]["ai_markers"] += 1
        elif line.strip().startswith('**验证命令**'):
            analysis["line_distribution"]["validation_commands"] += 1
        elif current_section == "header":
            analysis["line_distribution"]["document_header"] += 1
        elif current_section == "phase":
            analysis["line_distribution"]["phase_content"] += 1
            if current_phase:
                analysis["phase_distribution"][current_phase] += 1
        elif current_section == "footer":
            analysis["line_distribution"]["document_footer"] += 1

    return analysis
```

## 成功标准（对标标准实施计划 + 长度控制）

### 格式标准（严格版）
- [ ] 包含所有必须元素（区域标识、约束引用、质量约束等）
- [ ] 包含原代码和替换代码对比
- [ ] 包含修改边界和执行策略定义
- [ ] 包含{{AI_FILL_REQUIRED}}标记的合理使用
- [ ] 质量约束数值合理且符合标准范围
- [ ] 验证锚点具体可执行且包含具体命令
- [ ] 代码框架结构清晰且符合50行限制
- [ ] **文档总长度严格控制在800行以内**
- [ ] **代码块完整性保持（不可分割）**

### 功能标准（严格版）
- [ ] AI能够理解并正确填充{{AI_FILL_REQUIRED}}标记
- [ ] 填充后的代码能通过所有验证命令
- [ ] 符合所有约束条件和质量要求
- [ ] 实现的功能与描述完全一致
- [ ] 修改范围严格控制在边界内
- [ ] 分批处理策略得到正确执行
- [ ] 立即验证要求得到严格遵循
- [ ] **长度估算精确度≥90%**
- [ ] **组件选择优先级正确执行**

### 长度控制标准（新增）
- [ ] **基础组件行数估算准确**（误差≤10%）
- [ ] **代码块行数估算准确**（误差≤15%）
- [ ] **智能组件选择算法正确执行**
- [ ] **优先级排序符合重要性和复杂度**
- [ ] **分文档策略在超出限制时正确触发**
- [ ] **单个代码块超大时的特殊处理正确**
- [ ] **长度验证机制准确统计各类型行数**

### 对标标准（与标准实施计划一致性）
- [ ] 精确性：达到标准实施计划的文件路径、行号、代码对比精确度
- [ ] 验证约束：达到标准实施计划的50行限制、立即验证等严格要求
- [ ] 代码模板：达到标准实施计划的原代码→替换代码精确模板
- [ ] 边界控制：达到标准实施计划的明确修改边界和执行策略
- [ ] **长度管理：达到标准实施计划的AI记忆边界管理要求（800行限制）**
