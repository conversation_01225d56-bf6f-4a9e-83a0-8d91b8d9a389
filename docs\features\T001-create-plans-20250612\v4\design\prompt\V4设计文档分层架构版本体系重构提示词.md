# V4设计文档版本生成机制补充提示词
版本管理是V4全景拼图认知构建的"神经网络连接器"，通过提供优质上下文、增强三重验证、优化全局管理来支撑V4的核心推导系统。

版本管理贡献:
  上下文质量提升: "F007-nexus-design-v1.0.L1.2.0 → 提供精确的文档定位上下文"
  关系映射清晰: "设计↔实施↔代码版本映射 → 构建完整上下文关系网"
  演进路径追踪: "F005→F007→F008 → 提供历史演进上下文"
  依赖关系明确: "版本依赖链 → 消除上下文歧义"

  版本管理优化:
  确定性范围缩小: "V4只管理项目代码(F007)、功能名(nexus)、数字版本号 → 推导范围精确缩小"
  AI推导简化: "AI只需推导小范围确定性内容 → 而非复杂的全局决策"
  算法管理全局: "V4算法管理全景版本关系 → AI专注局部推导"
  边界清晰: "人工决策(稳定性、分支)与AI推导(版本号)明确分离"

  全局管理增强:
  全景版本视图: "V4掌握所有文档版本关系 → 全局版本管理"
  智能版本推导: "基于全景知识推导最优版本号 → 算法智能化"
  一致性保障: "确保整个项目版本一致性 → 全局质量控制"
  演进路径规划: "基于全景分析规划版本演进 → 战略级管理"

  认知增强机制:
  版本维度扩展: "增加版本管理作为全景拼图的新维度 → 认知深度提升"
  关系发现能力: "通过版本依赖发现隐藏的文档关系 → 全景理解增强"
  演进模式识别: "识别项目演进模式 → 预测性认知能力"
  上下文完整性: "版本信息补全上下文缺失 → 认知完整性保障"

  验证增强:
  V4算法验证: "版本一致性检查 → 提升全景验证准确性"
  Python AI验证: "版本依赖逻辑链验证 → 增强关系推理"
  IDE AI验证: "标准化版本格式验证 → 提高模板合规性"
  收敛质量提升: "版本管理提供额外验证维度 → 93.3%执行正确度保障"

  置信度提升:
  确定性内容增加: "版本号生成100%确定性 → 提升整体置信度"
  上下文质量因子: "版本一致性作为置信度计算因子 → 精度提升"
  验证锚点增加: "版本信息作为验证锚点 → 可靠性增强"
  不确定性降低: "明确版本关系 → 减少推导不确定性"

  策略对齐:
  版本缺失检测: "自动检测缺失的版本信息 → 精准补充"
  智能版本推导: "基于上下文推导合适版本号 → 智能补全"
  渐进式完善: "逐步完善版本信息 → 渐进式认知构建"

  约束遵循:
  认知边界管理: "V4管理确定性版本内容 → 符合AI认知边界"
  推导范围限制: "AI只推导小范围版本号 → 避免认知过载"
  分层处理: "版本管理分层实施 → 符合AI记忆管理策略"

## 📋 文档概述

**文档ID**: V4-DESIGN-VERSION-GENERATION-SUPPLEMENT-PROMPT-004
**创建日期**: 2025-06-17
**版本**: V4-design-version-generation-v1.0.L1.2.0
**目标**: 在V4设计文档中补充版本生成机制的设计描述，明确V4如何基于业务识别规则生成版本号
**DRY引用**: @MEM_LIB:L1-core/分层架构版本体系设计-完整版.md
**核心任务**: 修改V4设计文档，补充版本生成流程的设计原理和算法逻辑描述
**修改重点**: 在设计文档中添加V4版本生成机制的完整设计说明，让IDE AI理解版本生成的业务规则

## 🔄 V4版本生成机制设计原理（需要在设计文档中补充的内容）

### V4版本号生成算法设计流程
```yaml
v4_version_generation_design_principles:

  # 第1步：V4扫描检测设计原理
  v4_scanning_detection_design:
    project_code_detection_principle: "设计原理：从文档路径模式识别项目代码（如docs/features/F007-* → F007）≥98%置信度"
    function_name_extraction_principle: "设计原理：从文档内容语义分析提取功能名（如'nexus万用插座' → nexus）≥95%置信度"
    document_type_identification_principle: "设计原理：基于文档结构和内容识别文档类型（design/impl/plan等）≥95%置信度"
    layer_analysis_principle: "设计原理：基于文档内容特征分析层级归属（L1-L4）≥90%置信度"
    existing_version_detection_principle: "设计原理：检测现有版本号格式是否符合V4标准"
    confidence_threshold_design: "设计原理：所有检测项≥95%置信度才进入版本生成阶段"

  # 第2步：V4版本号组合生成设计原理
  v4_version_composition_design:
    deterministic_version_assembly_principle: "设计原理：组合确定性版本格式：{项目代码}-{功能名}-{文档类型}-v{数字版本}.L{层级版本}"
    version_increment_logic_principle: "设计原理：基于现有版本进行数学递增（v1.0→v1.1→v2.0）100%准确率"
    format_standardization_principle: "设计原理：标准化为V4格式：F007-nexus-design-v1.0.L1.2.0"
    confidence_calculation_principle: "设计原理：计算版本生成置信度（≥95%才输出指导）"
    boundary_protection_principle: "设计原理：严格限制在确定性内容，禁止生成稳定性、分支等人工决策内容"

  # 第3步：ai-prompt-batch-improvement.md生成设计原理
  ai_prompt_batch_improvement_generation_design:
    version_guidance_creation_principle: "设计原理：生成具体版本修改指导格式：**缺失**: 版本标识 - 需要添加: `**版本**: F007-nexus-design-v1.0.L1.2.0`"
    missing_version_detection_principle: "设计原理：检测缺失的版本信息并提供准确补充指导"
    correction_instructions_principle: "设计原理：提供准确的版本号修正指令（基于V4算法生成的标准版本）"
    ide_ai_modification_guidance_principle: "设计原理：为IDE AI提供明确的修改指导格式和具体操作步骤"
    confidence_verification_principle: "设计原理：确保所有版本指导≥95%置信度才写入报告"

  # 第4步：IDE AI版本修改协作设计原理
  ide_ai_version_modification_design:
    guidance_interpretation_principle: "设计原理：IDE AI解读ai-prompt-batch-improvement.md中的版本指导"
    design_document_update_principle: "设计原理：IDE AI根据指导修改设计文档版本字段"
    version_field_standardization_principle: "设计原理：更新为标准V4版本格式（**版本**: F007-nexus-design-v1.0.L1.2.0）"
    modification_verification_principle: "设计原理：验证修改后的版本格式正确性"
    feedback_loop_principle: "设计原理：修改完成后可重新运行V4扫描验证版本合规性"
```

### V4版本生成的业务识别规则设计（需要在设计文档中描述的规则）
```yaml
v4_business_identification_rules_design:

  # 项目代码识别规则设计（≥98%置信度）
  project_code_identification_design_rules:
    primary_path_pattern_design: "设计规则：从文档路径模式识别项目代码（docs/features/F007-* → F007）"
    secondary_content_pattern_design: "设计规则：从文档标题内容识别项目标识（'F007'、'V4'等 → 提取项目代码）"
    tertiary_feature_pattern_design: "设计规则：基于功能特征推导项目归属（nexus万用插座 → F007项目）"
    confidence_requirement_design: "设计规则：≥98%置信度才确定项目代码"
    fallback_handling_design: "设计规则：置信度<98%时不生成项目代码，等待人工确认"

  # 功能名提取规则设计（≥95%置信度）
  function_name_extraction_design_rules:
    keyword_exact_matching_design: "设计规则：精确关键词匹配（'nexus万用插座' → nexus功能）"
    semantic_analysis_matching_design: "设计规则：语义分析匹配（'全景拼图' → panoramic-puzzle功能）"
    title_extraction_design: "设计规则：从文档标题提取核心功能名（去除修饰词）"
    content_analysis_design: "设计规则：从文档核心定位章节提取功能名"
    confidence_requirement_design: "设计规则：≥95%置信度才确定功能名"

  # 层级判断规则设计（≥90%置信度）
  layer_detection_design_rules:
    l1_detection_indicators_design: "设计规则：L1层指标（'底层'、'核心组件'、'nexus万用插座'、'基础设施'）"
    l2_detection_indicators_design: "设计规则：L2层指标（'中间件'、'缓存'、'消息队列'、'数据访问'）"
    l3_detection_indicators_design: "设计规则：L3层指标（'平台服务'、'服务治理'、'配置管理'、'监控'）"
    l4_detection_indicators_design: "设计规则：L4层指标（'业务逻辑'、'API接口'、'应用服务'、'用户界面'）"
    confidence_requirement_design: "设计规则：≥90%置信度才确定层级"
    multi_indicator_validation_design: "设计规则：需要多个指标同时支持才确定层级"

  # 数字版本递增规则设计（100%准确率）
  version_increment_design_rules:
    major_version_increment_design: "设计规则：主版本递增（v1.0 → v2.0，重大架构变更，破坏性更新）"
    minor_version_increment_design: "设计规则：次版本递增（v1.0 → v1.1，功能增强，向后兼容）"
    patch_version_increment_design: "设计规则：补丁版本递增（v1.1.0 → v1.1.1，bug修复，小幅改进）"
    new_document_default_design: "设计规则：新文档默认版本（无现有版本时默认v1.0）"
    increment_logic_design: "设计规则：基于现有最高版本进行数学递增"
    accuracy_requirement_design: "设计规则：100%准确率（数学规则，无歧义）"
```

### V4扫描报告中版本指导的标准格式（ai-prompt-batch-improvement.md）
```yaml
v4_version_guidance_standard_format:

  # 版本缺失检测格式
  version_missing_detection_format: |
    **缺失**: 版本标识 - 需要添加: `**版本**: F007-nexus-design-v1.0.L1.2.0`
    **缺失**: 项目代码 - 需要添加: `**项目代码**: F007`
    **缺失**: 功能名称 - 需要添加: `**功能名称**: nexus`
    **缺失**: 层级版本 - 需要添加: `**层级版本**: L1.2.0`

  # 版本格式错误修正格式
  version_format_correction_format: |
    **格式错误**: 版本标识格式不符合V4标准
    - 当前格式: `**版本**: V1.0`
    - 标准格式: `**版本**: F007-nexus-design-v1.0.L1.2.0`
    - 修改指导: 更新为完整的V4组合式版本号格式

  # IDE AI修改指导格式
  ide_ai_modification_guidance_format: |
    ## 🎯 IDE AI版本修改指导

    ### 第1步：定位版本字段
    在设计文档中找到版本相关字段（**版本**:、**项目代码**:、**功能名称**:等）

    ### 第2步：按照V4标准格式修改
    - **版本**: F007-nexus-design-v1.0.L1.2.0
    - **项目代码**: F007
    - **功能名称**: nexus
    - **层级版本**: L1.2.0

    ### 第3步：验证修改结果
    确保所有版本字段符合V4标准格式，重新运行V4扫描验证合规性
```

### V4版本管理边界保护机制（严格限制确定性内容）
```yaml
v4_version_management_boundary_protection:

  # V4确定性管理范围（严格限制）
  v4_deterministic_management_scope:
    allowed_content: "项目代码(F007)、功能名(nexus)、文档类型(design)、数字版本号(v1.0)、层级版本(L1.2.0)"
    management_method: "检测合规性 + 生成修改指导（ai-prompt-batch-improvement.md）"
    confidence_threshold: "≥95%置信度才提供版本指导"
    output_format: "标准修改指导格式，不直接修改设计文档"

  # 人工决策领域（V4严格禁止参与）
  human_decision_boundary_strict_protection:
    prohibited_content: "稳定性状态(stable/rc/beta)、分支策略(main/develop)、依赖链设计、质量评估"
    protection_mechanism: "V4算法中硬编码边界检查，禁止生成人工决策相关内容"
    human_input_position: "在设计文档中提供人工填写位置，但V4不参与填写"
    decision_reference_only: "人工决策信息仅作为V4全景分析的置信度参考"

  # V4与IDE AI协作边界
  v4_ide_ai_collaboration_boundary:
    v4_responsibility: "生成准确的版本号和修改指导"
    ide_ai_responsibility: "根据指导修改设计文档版本字段"
    collaboration_interface: "ai-prompt-batch-improvement.md作为标准接口"
    feedback_mechanism: "修改完成后可重新运行V4验证"
```

### V4版本生成实际案例（基于F007-nexus项目）
```yaml
v4_version_generation_real_case_example:

  # 案例1：新设计文档版本生成
  case_1_new_design_document:
    input_document_path: "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/nexus万用插座设计文档.md"
    v4_detection_results:
      project_code: "F007（从路径docs/features/F007-*提取）≥98%置信度"
      function_name: "nexus（从'nexus万用插座'提取）≥95%置信度"
      document_type: "design（从文档类型判断）≥95%置信度"
      layer_analysis: "L1（从'底层核心组件'判断）≥90%置信度"
      version_increment: "v1.0（新文档默认版本）100%准确率"
    v4_generated_version: "F007-nexus-design-v1.0.L1.2.0"
    ai_prompt_guidance: |
      **缺失**: 版本标识 - 需要添加: `**版本**: F007-nexus-design-v1.0.L1.2.0`
      **缺失**: 项目代码 - 需要添加: `**项目代码**: F007`
      **缺失**: 功能名称 - 需要添加: `**功能名称**: nexus`

  # 案例2：现有文档版本更新
  case_2_existing_document_update:
    current_version: "**版本**: V1.0"
    v4_detection_results:
      format_compliance: "不符合V4标准格式"
      missing_components: "缺少项目代码、功能名、层级版本"
      confidence_level: "≥95%置信度确定需要更新"
    v4_generated_guidance: |
      **格式错误**: 版本标识格式不符合V4标准
      - 当前格式: `**版本**: V1.0`
      - 标准格式: `**版本**: F007-nexus-design-v1.0.L1.2.0`
      - 修改指导: 更新为完整的V4组合式版本号格式

  # 案例3：置信度不足的处理
  case_3_insufficient_confidence_handling:
    detection_results:
      project_code: "F007（≥98%置信度）✅"
      function_name: "未明确（<95%置信度）❌"
      layer_analysis: "可能L1或L2（<90%置信度）❌"
    v4_handling_strategy: "不生成版本指导，等待人工确认或文档内容补充"
    ai_prompt_guidance: |
      **检测结果**: 版本信息置信度不足，无法生成准确版本号
      **需要补充**: 明确功能名称和层级定位信息
      **建议**: 完善文档内容后重新运行V4扫描
```

## 🎯 精准优化核心原则（基于现有成熟设计）

### V4确定性内容识别精度提升（严格遵循现有边界设计）
```yaml
# 基于现有成熟V4设计，专注确定性识别算法精度优化，严格保持边界设计
v4_precision_optimization_principles:

  # V4确定性管理边界严格保持（现有设计完全正确）
  v4_deterministic_boundary_strict_preservation:
    verified_scope: "项目代码识别(F007)、功能名提取(nexus)、层级判断(L1-L4)、数字版本递增"
    human_decision_boundary: "稳定性状态、分支策略、依赖链设计严格保持人工决策"
    boundary_protection: "严格禁止V4越界到人工决策领域"
    existing_design_preservation: "完全保持现有三重验证机制、全景拼图认知构建、多维拼接引擎设计"

  # V4版本管理全景推导算法精度提升（基于现有成熟设计）
  v4_version_management_panoramic_inference_precision_enhancement:
    # 版本管理核心全景推导能力（保持现有V4算法架构）
    version_management_panoramic_inference_capabilities:
      project_code_identification: "项目代码识别精度从95%提升到98%+（基于IDE AI模板识别+V4算法全景验证+Python AI逻辑链推导+版本管理全景维度）"
      function_name_extraction: "功能名提取精度从90%提升到95%+（基于IDE AI语义分析+V4多维拼接引擎+Python AI认知增强+版本管理全景维度）"
      layer_detection_accuracy: "层级判断通过版本管理全景推导认知构建（基于文档内容特征分析+版本管理全景维度实现架构理解）"
      version_increment_reliability: "数字版本递增准确率保持100%（数学规则+三重验证+全景模型验证+版本管理全景推导）"

    # 版本管理全景维度扩展机制
    version_management_panoramic_dimension_expansion:
      panoramic_version_analysis: "版本管理通过全景拼图认知构建实现多维度版本分析（超越单一版本号的全景理解）"
      cross_document_version_inference: "版本管理通过全景推导实现跨文档版本关联推理（设计↔实施↔代码版本全景映射）"
      intelligent_version_gap_detection: "版本管理通过全景认知构建实现智能版本缺口识别（缺啥补啥的版本管理全景机制）"
      version_evolution_path_analysis: "版本管理通过全景推导分析版本演进路径（F005→F007→F008全景演进理解）"

    # 版本管理性能可控机制
    version_management_performance_control:
      core_version_management_protection: "版本管理核心全景维度保护（现有V4算法的版本管理全景维度必须保持）"
      adaptive_version_analysis_scaling: "版本管理全景分析自适应伸缩（根据性能动态调整版本分析深度）"
      graceful_version_degradation: "版本管理性能不足时优雅降级到核心版本功能"
      version_quality_assurance: "确保版本管理核心全景功能在任何条件下都能正常工作"

  # V4版本管理全景数据融合增强（基于现有设计）
  v4_version_management_panoramic_data_fusion_enhancement:
    # 版本管理核心数据融合
    version_management_core_data_fusion:
      real_document_foundation: "基于真实设计文档内容作为版本管理全景认知构建基础"
      ide_ai_template_verification: "通过IDE AI模板验证+版本管理全景维度实现95%+置信度域的精准版本填充"
      v4_algorithm_panoramic_verification: "通过V4算法全景验证+版本管理全景维度实现版本关联发现"
      python_ai_logical_chain_inference: "通过Python AI关系逻辑链推导+版本管理全景维度实现版本认知增强"

    # 版本管理全景认知构建
    version_management_panoramic_construction:
      version_confidence_calculation: "基于版本管理全景拼图模型的综合版本置信度计算（93.3%整体执行正确度+版本管理全景保障）"
      version_gap_identification: "智能版本缺口识别与补充（缺啥补啥的版本管理全景认知机制）"
      version_relationship_analysis: "版本依赖关系分析和版本架构理解（通过版本管理全景维度）"
      cross_layer_version_mapping: "跨层级版本映射分析（L1-L4版本关联通过版本管理全景推导）"
      version_evolution_tracking: "版本演进路径跟踪（F005→F007→F008通过版本管理全景认知构建）"

  # 严格边界保护机制（避免过度优化）
  strict_boundary_protection_mechanism:
    no_human_decision_automation: "严格禁止自动化稳定性评估、分支管理、依赖链构建"
    no_over_optimization: "避免过度复杂化简单的确定性识别任务"
    preserve_existing_capabilities: "保持现有全景拼图认知构建和多维拼接引擎的完整功能"
    focus_on_precision_only: "仅专注确定性内容识别精度提升，不添加新的复杂功能"
```

## 🔄 V4设计文档版本生成机制补充任务清单（95%置信度评估）

### V4设计文档修改任务（补充版本生成机制设计描述）
```yaml
v4_design_document_version_generation_supplement_tasks:

  # P0优先级：核心算法设计层（必须修改）
  p0_core_algorithm_design_supplement:

    # 1. V4扫描检测算法设计补充
    v4_scanning_detection_algorithm_design_supplement:
      target_file: "docs/features/T001-create-plans-20250612/v4/design/02-扫描阶段设计.md"
      current_status: "扫描阶段设计（已有基础扫描机制）"
      supplement_target: "V4-SCANNING-DETECTION-algorithm-enhanced（扫描检测算法设计增强版）"
      design_supplement_actions:
        - "补充项目代码识别算法设计：F007路径模式识别的设计原理（≥98%置信度）"
        - "补充功能名提取算法设计：nexus语义分析的设计逻辑（≥95%置信度）"
        - "补充层级判断算法设计：L1-L4内容分析的设计方法（≥90%置信度）"
        - "补充置信度计算算法设计：≥95%阈值的计算逻辑和验证机制"

    # 2. 版本组合生成算法设计补充
    v4_version_composition_algorithm_design_supplement:
      target_file: "docs/features/T001-create-plans-20250612/v4/design/03-多维拼接引擎设计.md"
      current_status: "多维拼接引擎设计（已有多维分析机制）"
      supplement_target: "V4-VERSION-COMPOSITION-algorithm-enhanced（版本组合算法设计增强版）"
      design_supplement_actions:
        - "补充版本号组合算法设计：F007-nexus-design-v1.0.L1.2.0格式的组合逻辑"
        - "补充数字版本递增算法设计：v1.0→v1.1→v2.0的递增规则和数学逻辑"
        - "补充版本格式标准化算法设计：统一版本格式的标准化处理机制"

    # 3. ai-prompt-batch-improvement.md生成算法设计补充
    v4_ai_prompt_generation_algorithm_design_supplement:
      target_file: "docs/features/T001-create-plans-20250612/v4/design/04-AI协作引擎设计.md"
      current_status: "AI协作引擎设计（已有AI协作机制）"
      supplement_target: "V4-AI-PROMPT-GENERATION-algorithm-enhanced（AI指导生成算法设计增强版）"
      design_supplement_actions:
        - "补充版本指导格式生成算法设计：ai-prompt-batch-improvement.md的格式生成逻辑"
        - "补充缺失版本检测算法设计：版本信息缺失的检测和识别机制"
        - "补充修改指导生成算法设计：具体修改指导的生成逻辑和格式标准"
        - "补充IDE AI协作接口设计：V4与IDE AI的协作接口和通信机制"

  # P1优先级：质量保障设计层（必须修改）
  p1_quality_assurance_design_supplement:

    # 4. 版本生成置信度计算设计补充
    v4_version_confidence_calculation_design_supplement:
      target_file: "docs/features/T001-create-plans-20250612/v4/design/12-95%置信度计算与验证标准.md"
      current_status: "95%置信度计算与验证标准（已建立完整机制）"
      supplement_target: "V4-VERSION-CONFIDENCE-calculation-enhanced（版本生成置信度计算设计增强版）"
      design_supplement_actions:
        - "补充版本生成置信度计算设计：版本生成过程中的置信度计算逻辑"
        - "补充业务识别置信度验证设计：项目代码、功能名、层级判断的置信度验证机制"
        - "补充版本生成失败处理设计：置信度不足时的处理机制和回退策略"

    # 5. 版本生成边界控制设计补充
    v4_version_boundary_control_design_supplement:
      target_file: "docs/features/T001-create-plans-20250612/v4/design/05-质量门禁机制设计.md"
      current_status: "质量门禁机制设计（已有质量控制机制）"
      supplement_target: "V4-VERSION-BOUNDARY-control-enhanced（版本生成边界控制设计增强版）"
      design_supplement_actions:
        - "补充确定性内容边界设计：V4管理范围的明确界定（项目代码、功能名、数字版本号）"
        - "补充人工决策边界保护设计：人工决策领域的边界保护机制（稳定性、分支策略等）"
        - "补充越界检测和防护设计：V4越界行为的检测和防护机制"

  # P2优先级：数据存储设计层（必须修改）
  p2_data_storage_design_supplement:

    # 6. 版本数据管理设计补充
    v4_version_data_management_design_supplement:
      target_file: "docs/features/T001-create-plans-20250612/v4/design/17-SQLite全景模型数据库设计.md"
      current_status: "SQLite全景模型数据库设计（已有数据库设计）"
      supplement_target: "V4-VERSION-DATA-management-enhanced（版本数据管理设计增强版）"
      design_supplement_actions:
        - "补充确定性版本存储设计：V4生成的确定性版本信息的存储结构和管理机制"
        - "补充人工决策版本关联设计：人工决策版本与确定性版本的关联关系设计"
        - "补充版本查询和追溯设计：版本信息的查询接口和历史追溯机制"
        - "补充置信度计算数据设计：置信度计算相关数据的存储和管理机制"
```

### V4现有算法性能优化（基于现有成熟算法，避免重复设计）
```yaml
v4_existing_algorithm_performance_optimization:

  # 1. V4三重验证机制性能优化（基于现有成熟三重验证算法）
  v4_triple_verification_performance_optimization:
    target_file: "docs/features/T001-create-plans-20250612/v4/design/12-95%置信度计算与验证标准.md"
    current_status: "95%置信度计算与验证标准（三重验证机制已成熟完整）"
    optimization_target: "V4-OPTIMIZED-triple-verification-performance（性能优化版）"
    optimization_actions:
      - "优化现有95%置信度计算算法的执行效率（保持现有算法逻辑）"
      - "增强现有三重验证机制的矛盾检测精度（保持75%严重矛盾检测率）"
      - "优化现有置信度计算的收敛速度（基于现有收敛机制）"
      - "提升现有验证标准的准确性和可靠性（不改变验证逻辑）"

  # 2. V4全景拼图认知构建性能优化（基于现有全景拼图算法）
  v4_panoramic_puzzle_cognition_performance_optimization:
    target_file: "docs/features/T001-create-plans-20250612/v4/design/14-全景拼图认知构建指引.md"
    current_status: "全景拼图认知构建指引（已有完整的认知构建机制）"
    optimization_target: "V4-OPTIMIZED-panoramic-cognition-performance（认知性能优化版）"
    optimization_actions:
      - "优化现有全景拼图认知构建算法的分析效率（保持现有认知逻辑）"
      - "增强现有认知构建的版本映射准确性（基于现有映射机制）"
      - "优化现有算法的依赖关系分析效率（不改变分析逻辑）"
      - "提升现有全景认知的系统理解性能（保持理解深度）"

  # 3. V4多维分析引擎性能优化（基于现有多维分析引擎）
  v4_multi_dimensional_analysis_performance_optimization:
    target_file: "docs/features/T001-create-plans-20250612/v4/design/03-多维拼接引擎设计.md"
    current_status: "多维拼接引擎设计（已有完整的多维分析机制）"
    optimization_target: "V4-OPTIMIZED-multi-dimensional-performance（多维性能优化版）"
    optimization_actions:
      - "优化现有多维分析算法的数据处理效率（保持现有分析逻辑）"
      - "增强现有拼接引擎的模式识别准确性（基于现有识别机制）"
      - "优化现有算法的架构分析效率（不改变分析深度）"
      - "提升现有多维分析的综合判断性能（保持判断逻辑）"
```

## 📝 精准优化提示词模板

### V4确定性内容识别精度提升标准格式提示词（95%置信度边界保护）
```markdown
# 优化指令：V4确定性内容识别精度提升标准格式

## V4版本管理全景推导工作模式（基于现有成熟设计）
V4的版本管理全景推导核心功能：

### 版本管理核心全景功能（基于现有V4算法）：
1. **项目代码全景识别**：IDE AI模板识别+V4算法全景验证+Python AI逻辑链推导+版本管理全景维度（F007、V4等）
2. **功能名全景提取**：IDE AI语义分析+V4多维拼接引擎+Python AI认知增强+版本管理全景维度（nexus、panoramic-puzzle等）
3. **层级版本全景判断**：通过版本管理全景推导认知构建实现架构理解（L1-L4+版本管理全景维度）
4. **数字版本全景递增**：数学规则+三重验证+全景模型验证+版本管理全景推导的版本递增
5. **版本管理全景认知构建**：IDE AI+算法+Python AI三重验证+版本管理全景维度实现版本全知推导能力

### 版本管理全景质量保障机制：
6. **版本关联关系全景发现**：发现设计↔代码↔业务↔测试版本关联关系，构建完整版本系统全景图
7. **版本智能全景推导能力**：基于文档内容，通过版本管理全景算法实现版本智能推导和认知增强
8. **版本边界保护机制**：严格限制在确定性版本内容范围内，不越界到人工决策领域
9. **版本管理全景质量保障**：确保版本管理核心全景功能达到95%置信度要求

## V4版本管理全景推导执行的标准格式
✅ **V4版本管理全景推导（IDE AI + 算法 + Python AI 三重验证+版本管理全景维度）**：

**V4版本管理全景推导核心识别**:
- **项目代码全景识别**: V4通过IDE AI模板识别+算法全景验证+Python AI逻辑链推导+版本管理全景维度: `**项目代码**: F007`（≥98%置信度，版本管理全景推导算法）
- **功能名全景提取**: V4通过IDE AI语义分析+多维拼接引擎+Python AI认知增强+版本管理全景维度: `**功能名称**: nexus`（≥95%置信度，版本管理全景推导提取）
- **层级版本全景判断**: V4通过版本管理全景推导认知构建: `**层级版本**: L1.2.0`（≥90%置信度，版本管理全景维度全知推导）
- **数字版本全景递增**: V4基于版本管理全景推导模型: `**版本**: v1.0.L1.2.0`（≥98%置信度，数学规则+版本管理全景推导）

**V4版本管理全景推导技术分析**（IDE AI + 算法 + Python AI 三重验证+版本管理全景维度）:
- **技术栈全景解析**: V4通过IDE AI模板验证+算法全景验证+Python AI逻辑链推导+版本管理全景维度: `**技术栈**: Java 21, Spring Boot 3.4.5+`（≥95%置信度，版本管理全景推导技术栈分析）
- **复杂度全景评估**: V4通过版本管理全景推导认知构建: `**复杂度等级**: L2 (中等复杂度)`（≥90%置信度，版本管理全景维度全知推导复杂度理解）
- **依赖关系全景分析**: V4基于版本管理全景拼接引擎+全景推导: `**依赖关系**: L3平台服务层设计 → L2中间件层设计`（≥90%置信度，版本管理全景维度关联发现）

**V4版本管理全景推导数据验证**（IDE AI + 算法 + Python AI 三重验证+版本管理全景维度）:
- **文档状态全景验证**: F007-nexus-design（IDE AI模板验证+算法全景验证+Python AI认知增强+版本管理全景维度的状态分析）
- **项目关系全景验证**: F007依赖F005（基于真实关系+版本管理全景拼接引擎关联发现+全景推导认知构建，≥85%置信度）
- **演进路径全景验证**: 基于F005→F007→F008（通过版本管理全景推导模型的演进路径分析，≥80%置信度）

⚠️ **V4边界保护机制（严格限制确定性内容）**：

**人工决策领域（V4严格禁止参与）**:
- **稳定性状态**: 稳定/非稳定状态需要人工专业判断，V4不提供自动评估
- **分支策略**: 分支管理需要项目管理决策，V4不提供自动建议
- **依赖链设计**: 具体依赖链需要架构决策，V4不提供自动构建

## V4简化人工决策信息参考模型（边界保护优先）
```yaml
v4_simplified_human_decision_reference_model:
  binary_stability_detection: "V4检测设计文档中的'稳定'或'非稳定'文本标记"
  limited_confidence_boost: "仅'稳定'文本标记可提升V4置信度5%（从85%提升到90%）"
  default_handling: "无稳定性标记时，V4使用确定性版本，置信度固定为85%"
  verified_handling: "'稳定'标记存在且验证后，V4置信度固定为90%"

  strict_boundary_protection: "V4严格限制在确定性内容，不越界处理人工决策内容"
  preserve_existing_design: "完全保持现有三重验证机制和全景拼图认知构建设计"
```
```

### 版本映射关系建立提示词
```markdown
# 重构指令：版本映射关系建立

## 多维版本映射体系集成
基于分层架构版本体系设计，在设计文档中添加以下版本映射关系章节：

## 📊 版本映射关系

### 设计文档版本映射
```yaml
document_version_mapping:
  # 设计文档 ←→ 实施计划 (一对一关系)
  design_to_implementation:
    design_version: "{当前设计文档版本号}"
    implementation_version: "{对应实施计划版本号}"
    mapping_type: "一对一同步版本"
  
  # 设计文档 ←→ 代码 (多对多关系)  
  design_to_code:
    design_version: "{当前设计文档版本号}"
    related_code_files:
      - "代码文件1版本号"
      - "代码文件2版本号"
      - "代码文件3版本号"
    mapping_type: "一对多映射关系"
  
  # 版本依赖链
  version_dependency_chain:
    current_layer: "{当前文档层级}"
    dependency_chain: "{依赖的下层版本} → {当前层版本}"
    upstream_dependencies: [依赖的上游文档版本列表]
    downstream_impacts: [影响的下游文档版本列表]
```

### 版本一致性检测点
```yaml
version_consistency_checkpoints:
  design_consistency: "设计文档间版本一致性检查"
  implementation_alignment: "设计与实施计划版本对齐检查"  
  code_mapping_validation: "代码文件版本映射验证"
  dependency_chain_verification: "依赖链版本一致性验证"
```
```

### V4数据库版本管理机制设计补充提示词
```markdown
# 设计文档修改指令：V4数据库版本管理机制设计补充

## V4数据库版本管理机制设计原理（需要在设计文档中补充的内容）
基于分层架构版本体系设计，在17-SQLite全景模型数据库设计.md中补充V4版本管理机制的设计原理：

### V4版本管理数据库设计原理
```yaml
v4_database_version_management_design_principles:

  # V4确定性版本管理表设计原理
  deterministic_versions_table_design:
    design_purpose: "存储V4算法生成的确定性版本信息"
    core_fields_design:
      project_code_field: "项目代码字段设计（F007、V4等，V4确定性识别）"
      function_name_field: "功能名字段设计（nexus、panoramic-puzzle等，V4确定性提取）"
      document_type_field: "文档类型字段设计（design、impl、plan等，V4确定性判断）"
      functional_version_field: "功能版本字段设计（v1.0、v1.1、v2.0等，V4确定性递增）"
      layer_version_field: "层级版本字段设计（L1.2.0、L2.1.0等，V4确定性分析）"
      deterministic_version_full_field: "完整确定性版本字段设计（F007-nexus-design-v1.0.L1.2.0格式）"
      v4_confidence_score_field: "V4识别置信度字段设计（≥95%置信度记录）"
    management_boundary: "V4独立管理的确定性内容存储"

  # 人工决策版本管理表设计原理
  human_decision_versions_table_design:
    design_purpose: "存储人工决策的版本信息（稳定性状态等）"
    core_fields_design:
      stability_status_field: "稳定性状态字段设计（稳定/非稳定二元模型）"
      human_decision_full_field: "完整人工决策字段设计（.稳定格式）"
      decision_maker_field: "决策人员字段设计（记录决策责任人）"
      confidence_impact_verified_field: "置信度影响验证字段设计（验证稳定性对V4置信度的影响）"
    management_boundary: "人工决策领域，V4严格禁止参与"

  # V4置信度参考表设计原理
  v4_confidence_references_table_design:
    design_purpose: "存储V4置信度计算和参考信息"
    core_fields_design:
      base_confidence_field: "基础置信度字段设计（85%基础置信度）"
      stability_boost_field: "稳定性提升字段设计（仅稳定状态提升5%）"
      final_confidence_field: "最终置信度字段设计（综合计算结果）"
      used_for_panoramic_assembly_field: "全景图拼接使用字段设计（标记是否用于全景分析）"
    calculation_principle: "基于现有95%置信度计算与验证标准"

  # 版本组合视图设计原理
  complete_versions_view_design:
    design_purpose: "提供完整版本号的组合视图"
    combination_logic: "确定性版本 + 人工决策版本的组合逻辑设计"
    operation_mode_design: "enhanced_mode（有人工决策）vs independent_mode（纯V4管理）"
    final_version_format: "F007-nexus-design-v1.0.L1.2.0.稳定（完整组合格式）"
```

### 版本查询功能设计
```sql
-- 从代码文件查找所有相关设计文档
SELECT dd.document_name, dd.version, cdm.mapping_type, cdm.influence_level
FROM code_files cf
JOIN code_design_mapping cdm ON cf.id = cdm.code_file_id
JOIN design_documents dd ON cdm.design_document_id = dd.id
WHERE cf.file_path = ?;

-- 从设计文档查找所有相关代码文件
SELECT cf.file_path, cf.version, cdm.mapping_type, cdm.influence_level
FROM design_documents dd
JOIN code_design_mapping cdm ON dd.id = cdm.design_document_id
JOIN code_files cf ON cdm.code_file_id = cf.id
WHERE dd.id = ?;

-- 查找版本不一致的映射关系
SELECT cf.file_path, cf.version as code_version, dd.version as design_version
FROM code_files cf
JOIN code_design_mapping cdm ON cf.id = cdm.code_file_id
JOIN design_documents dd ON cdm.design_document_id = dd.id
WHERE cf.layer_version != dd.layer_version OR cf.stability != dd.stability;
```
```

### @标记系统简化优化提示词（确定性版本管理标记体系）
```markdown
# 优化指令：@标记系统确定性版本管理优化

## 简化@标记体系（基于确定性内容管理）
基于V4架构信息AI填充模板，建立简化的确定性版本管理@标记体系：

### 核心确定性@标记（V4管理）
```yaml
# V4确定性管理标记（必需）
v4_deterministic_tags:
  project_code: "@PROJECT_CODE:[F007/V4等项目标识]" # V4确定性识别
  function_name: "@FUNCTION_NAME:[nexus/panoramic-puzzle等功能名]" # V4确定性提取
  layer_mapping: "@LAYER:[L1/L2/L3/L4]" # V4确定性判断
  version_id: "@DETERMINISTIC_VERSION:[完整确定性版本号]" # V4确定性生成

# 确定性管理@标记示例
deterministic_management_examples:
  v4_managed_tags:
    - "@DETERMINISTIC_VERSION:F007-nexus-design-v1.0.L1.2.0" # V4自动管理
    - "@PROJECT_CODE:F007" # V4确定性识别（≥98%置信度）
    - "@FUNCTION_NAME:nexus" # V4确定性提取（≥95%置信度）
    - "@LAYER:L1_底层核心" # V4确定性判断（≥90%置信度）

# 简化人工决策标记（二元模型）
simplified_human_decision_tags:
  human_input_position:
    - "@HUMAN_STABILITY_INPUT:[人工填写: 稳定/非稳定]" # 设计文档填写位置
    - "@CONFIDENCE_NOTE:稳定状态将作为V4全景图拼接的置信度参考（需验证）" # 说明标记

  human_decision_when_filled:
    - "@HUMAN_STABILITY:稳定" # 人工填写后的标记（仅稳定状态影响V4）
    - "@CONFIDENCE_IMPACT_VERIFIED:是" # 置信度影响验证状态

  final_result:
    - "@FINAL_VERSION:F007-nexus-design-v1.0.L1.2.0.稳定" # 组合结果（仅稳定状态）
    - "@V4_CONFIDENCE:90%" # V4全景图拼接置信度（稳定状态提升）
```

### V4确定性版本管理算法（优化版）
```yaml
v4_deterministic_version_management_optimization:
  management_boundary: "V4严格限制在确定性内容：项目代码(F007)、功能名(nexus)、数字版本号"

  core_responsibility: "检测版本号合规性并提供修改指导，严格禁止越界到人工决策领域"

  algorithm_trigger: "文档扫描时自动触发检测和指导生成"

  deterministic_detection_precision_enhancement:
    - "项目代码确定性识别：从文档路径/内容提取F007等项目标识（≥98%准确率提升）"
    - "功能名称确定性提取：从文档标题/内容识别nexus等功能名（≥95%准确率提升）"
    - "文档类型确定性判断：design/impl/plan等类型识别（≥95%准确率保持）"
    - "层级版本确定性分析：基于文档内容判断L1-L4层级（≥90%准确率提升）"
    - "数字版本确定性递增：基于现有版本进行数学递增（≥98%准确率保持）"

  optimized_guidance_rules:
    project_code_detection_optimization: |
      基于设计文档路径和内容的项目代码识别算法设计：
      - 路径模式分析：docs/features/F007-* → 识别项目代码F007
      - 文档标题分析：标题包含项目标识符 → 提取项目代码
      - 修改指导：**缺失**: 项目代码 - 需要添加: `**项目代码**: F007`

    function_name_detection_optimization: |
      基于设计文档内容的功能名识别算法设计：
      - 关键词匹配：文档包含"nexus万用插座" → 识别为nexus功能
      - 关键词匹配：文档包含"全景拼图" → 识别为panoramic-puzzle功能
      - 修改指导：**缺失**: 功能名称 - 需要添加: `**功能名称**: nexus`

    layer_detection_optimization: |
      基于文档内容确定性分析层级并提供修改指导（具体规则）：
      - 文档包含"nexus万用插座"、"核心组件"、"底层" → 检测为L1层
      - 文档包含"中间件"、"缓存"、"消息" → 检测为L2层
      - 文档包含"平台服务"、"服务治理"、"配置管理" → 检测为L3层
      - 文档包含"业务"、"API"、"应用" → 检测为L4层
      修改指导：**缺失**: 层级版本 - 需要添加: `**层级版本**: L1.2.0`

  high_confidence_technical_analysis:
    tech_stack_detection_optimization: |
      基于设计文档技术约束的技术栈识别算法设计：
      - 技术约束章节分析：提取Java版本、Spring Boot版本等核心技术栈
      - 性能指标章节分析：提取Virtual Threads、GraalVM等性能技术
      - 修改指导：**缺失**: 技术栈 - 需要添加: `**技术栈**: Java 21, Spring Boot 3.4.5+`

    complexity_level_assessment_optimization: |
      基于设计文档架构复杂度的评估算法设计：
      - 架构模式分析：微内核+插件化 → L2复杂度（中等复杂度）
      - 功能复杂度分析：多维拼接引擎 → L3复杂度（高复杂度）
      - 组件数量评估：≤5个组件 → L1，6-15个组件 → L2，≥16个组件 → L3
      修改指导：**缺失**: 复杂度等级 - 需要添加: `**复杂度等级**: L2 (中等复杂度)`

  strict_boundary_protection:
    stability_assessment_strict_protection: |
      稳定性状态严格禁止V4参与（保持现有正确设计）：
      - 稳定/非稳定需要人工专业判断
      - V4严格禁止提供稳定性自动评估
      - 只在设计文档中提供人工填写位置

  simplified_confidence_reference_mechanism:
    binary_stability_detection: "V4检测设计文档中的'稳定'或'非稳定'文本标记"
    limited_confidence_boost: "仅'稳定'文本标记可提升V4置信度5%（从85%提升到90%）"
    default_handling: "无稳定性标记时，V4使用确定性版本，置信度固定为85%"
    verified_handling: "'稳定'标记存在且验证后，V4置信度固定为90%"
```

## V4基于现实数据的确定性算法优化指导（95%置信度现实边界）

### V4确定性内容识别算法优化要求（基于真实项目状态）
```yaml
# V4确定性内容识别算法优化 - 基于真实项目状态、现有成熟算法、严格边界保护

v4_deterministic_algorithm_optimization:

  # 基于现有V4算法的确定性内容识别能力优化
  existing_algorithm_optimization:
    confidence_calculator: "现有三重验证算法性能优化"
    panoramic_analyzer: "现有全景拼图认知构建精度提升"
    multi_dimensional_engine: "现有多维分析算法效率优化"
    reality_data_validator: "现实数据验证器准确性增强"

  # V4确定性内容识别范围（基于现有成熟算法）
  v4_deterministic_content_identification_scope:
    project_code_identification: "项目代码识别（F007、V4等）≥98%置信度"
    function_name_extraction: "功能名提取（nexus、panoramic-puzzle等）≥95%置信度"
    layer_version_detection: "层级版本检测（L1-L4基于文档内容）≥90%置信度"
    numerical_version_increment: "数字版本递增（v1.0→v1.1→v2.0）≥98%置信度"
    tech_stack_analysis: "技术栈分析（Java 21实际使用）≥95%置信度"
    architecture_pattern_recognition: "架构模式识别（基于真实架构）≥95%置信度"

  # V4现实数据验证范围（基于真实项目状态）
  v4_reality_data_validation_scope:
    real_document_content_analysis: "真实文档内容分析≥95%置信度"
    real_project_status_tracking: "真实项目状态跟踪≥90%置信度"
    real_dependency_relationship_verification: "真实依赖关系验证≥90%置信度"
    real_version_history_tracking: "真实版本历史追踪≥98%置信度"

  # 严格边界保护范围（避免过度自动化）
  strict_boundary_protection_scope:
    stability_assessment_prohibition: "严格禁止稳定性自动评估"
    branch_management_prohibition: "严格禁止分支策略自动管理"
    dependency_chain_construction_prohibition: "严格禁止依赖链自动构建"
    human_decision_boundary_enforcement: "强制执行人工决策边界保护"

  # V4确定性算法核心功能（基于现有成熟算法）
  v4_deterministic_algorithm_core_functions:
    project_code_extraction: |
      从文档路径确定性提取项目代码：
      - 路径模式匹配：docs/features/F007-* → F007
      - 路径模式匹配：docs/features/V4-* → V4
      - 置信度要求：≥98%

    function_name_identification: |
      基于设计文档内容的功能名识别算法设计：
      - 关键词匹配：文档包含"nexus万用插座" → nexus功能
      - 关键词匹配：文档包含"全景拼图" → panoramic-puzzle功能
      - 置信度要求：≥95%

    layer_version_detection: |
      基于设计文档内容的层级判断算法设计：
      - L1层判断：文档描述底层核心组件（如nexus万用插座） → L1层
      - L2层判断：文档描述中间件服务 → L2层
      - L3层判断：文档描述平台服务 → L3层
      - L4层判断：文档描述业务功能 → L4层
      - 置信度要求：≥90%

    numerical_version_increment: |
      基于现有版本确定性递增：
      - 数学规则：v1.0 → v1.1 → v2.0
      - 数学规则：L1.2.0 → L1.2.1 → L1.3.0
      - 置信度要求：≥98%

  # V4现实数据验证核心功能（基于真实项目状态）
  v4_reality_data_validation_core_functions:
    real_document_content_analysis: |
      基于真实设计文档内容进行分析：
      - 避免假设性推导
      - 基于实际存在的文档内容
      - 置信度要求：≥95%

    real_project_status_tracking: |
      跟踪真实项目状态：
      - F007设计文档存在但代码未完成
      - F005项目已完成状态
      - 置信度要求：≥90%

    real_dependency_relationship_verification: |
      验证真实依赖关系：
      - F007依赖F005的实际关系
      - 基于真实项目演进路径
      - 置信度要求：≥90%

  # 简化置信度计算（基于现有验证机制）
  simplified_confidence_calculation:
    base_confidence: "85%（V4确定性内容基础置信度）"

    # 确定性识别精度提升
    project_code_precision_boost: "项目代码识别精度提升3%（95%→98%）"
    function_name_precision_boost: "功能名提取精度提升5%（90%→95%）"
    layer_detection_precision_boost: "层级判断精度提升5%（85%→90%）"

    # 人工决策信息简化利用
    stability_reference_boost: "稳定状态作为置信度参考提升5-10%（需验证）"

    maximum_confidence: "95%（基于现有三重验证机制）"
    calculation_model: "基于现有95%置信度计算与验证标准"
```

### V4算法重构实施要点
```yaml
v4_algorithm_refactoring_guidelines:

  # 从V4中移除（严格禁止AI参与）
  remove_from_v4:
    - "所有稳定性自动评估逻辑（stable/rc/beta/alpha）"
    - "所有分支自动管理逻辑（main/develop/feature）"
    - "所有质量自动评估逻辑（需要专业判断）"
    - "所有依赖链自动构建逻辑（+L3.1.0.stable等）"
    - "所有发布时机自动决策逻辑（需要项目管理）"
    - "所有版本号直接生成逻辑（改为检测和指导）"

  # 在V4中增强（基于设计文档的确定性管理精度提升）
  enhance_in_v4:
    - "项目代码识别算法：路径模式分析docs/features/F007-*→F007（精度98%+）"
    - "功能名提取算法：关键词匹配'nexus万用插座'→nexus（精度95%+）"
    - "层级判断算法：内容分析'底层核心组件'→L1层（精度90%+）"
    - "数字版本递增算法：数学规则v1.0→v1.1→v2.0（准确率100%）"
    - "版本格式标准化：F007-nexus-design-v1.0.L1.2.0格式（准确率100%）"
    - "修改指导生成：ai-prompt-batch-improvement.md格式输出（精度95%+）"

  # 在V4中新增（边界保护）
  add_to_v4:
    - "确定性管理边界检查机制（严格限制V4管理范围）"
    - "人工决策接口清晰定义（设计文档人工填写位置）"
    - "确定性内容验证算法（项目代码、功能名、数字版本）"
    - "版本冲突检测和避免机制（数学递增规则）"
    - "AI高置信度填写检测（技术栈、复杂度、依赖关系）"
    - "人工决策边界保护（禁止AI参与稳定性、分支、依赖链）"
    - "置信度参考机制（人工填写信息提升全景置信度）"

  # V4职责精准定位
  core_responsibility_definition:
    specific_responsibility: "检测项目代码(F007)、功能名(nexus)、层级(L1-L4)、数字版本号合规性"
    output_format: "ai-prompt-batch-improvement.md格式的具体修改指导"
    confidence_threshold: "≥95%置信度才提供具体修改指导"
    boundary_protection: "严格限制在确定性内容：项目代码、功能名、层级、数字版本号"
    prohibited_areas: "严格禁止：稳定性评估、分支管理、依赖链构建、质量评估"
```
```

---

**创建时间**: 2025-06-17
**文档状态**: V4精准优化提示词（基于现有成熟设计，专注确定性识别精度提升）
**核心优化内容**:
- ✅ **V4边界设计严格保持**：完全维持现有正确的确定性管理vs人工决策的清晰边界
- ✅ **确定性识别精度提升**：专注项目代码、功能名、层级判断、数字版本递增的算法精度优化
- ✅ **现有设计完整性保护**：严格保持现有三重验证机制、全景拼图认知构建、多维拼接引擎设计
- ✅ **现实数据验证增强**：基于真实项目状态和文档内容进行验证，避免假设性推导
- ✅ **过度优化防护**：避免过度复杂化简单的确定性识别任务，防止概念混乱
- ✅ **现有算法性能优化**：基于现有成熟算法进行性能优化，不改变算法逻辑
- ✅ **边界保护强化**：严格防止V4越界到人工决策领域，避免系统风险
- ✅ **系统稳定性维护**：保持现有成熟设计的稳定性和可预测性
**关键优化重点**:
- ✅ 精度提升：项目代码识别（95%→98%+）路径模式匹配优化
- ✅ 精度提升：功能名提取（90%→95%+）关键词匹配优化
- ✅ 精度提升：层级判断（85%→90%+）文档内容分析优化
- ✅ 精度保持：数字版本递增（100%）数学规则准确性保持
- ✅ 现实验证：基于真实项目状态验证（F007设计存在但代码未完成）
- ✅ 边界保护：严格限制在确定性内容范围，禁止越界到人工决策
- ✅ 设计保护：完全保持现有三重验证、全景拼图、多维分析设计
**下一步**: 按照确定性识别精度提升提示词实施V4算法优化
**版本**: V4-precision-optimization-prompt-v1.0.L1.2.0（V4确定性精度优化版）
**优化重点**: 确定性识别精度提升+现有设计保护+边界保护强化+过度优化防护
