---
title: xkongcloud-commons-uid 公共库实施计划
document_id: F004-PLAN-001
document_type: 实现文档
category: 公共库
scope: 全局
keywords: [公共库, UID, 分布式ID, xkongcloud-commons, 持久化ID, 特征码恢复, Baidu UID, 实施计划, <PERSON>hem<PERSON>命名规范, 构建器模式, 解耦, 并发控制, 线程安全]
created_date: 2025-05-11
updated_date: 2025-06-30
status: 草稿
version: 2.2
authors: [AI助手]
affected_features:
  - F004 # This new library itself
  - F003 # PostgreSQL迁移
related_docs:
  - ../design/commons-uid-library-design.md
  - ../design/instance-id-encryption-design.md
  - ../design/postgresql-persistent-id-fingerprint-recovery.md
  - ../plan/uid-library-refactoring-plan.md
  - ../../../common/middleware/integration/baidu-uid-generator-postgresql-implementation.md
  - ../../../features/F003-PostgreSQL迁移-20250508/plan/implementation-plan.md
---

# xkongcloud-commons-uid 公共库实施计划

## 实现概述

本文档描述了创建 `xkongcloud-commons-uid` 公共库的实施计划。该公共库旨在提供一套标准化的、可重用的组件，用于在 `xkongcloud` 项目的各个子服务中实现基于百度UID生成器的分布式唯一ID生成功能。实施计划包括模块创建、代码实现、单元测试、文档编写等方面。

本计划的核心是基于设计文档中的规范，从头实现UID相关功能，并将其作为独立的公共库，使其能够被多个项目复用，提高代码质量和开发效率。需要注意的是，这些UID相关类目前在 `xkongcloud-business-internal-core` 项目中尚未实际实现，本计划将直接在新的公共库中实现这些功能。

## 与PostgreSQL迁移计划的关系

本实施计划与PostgreSQL迁移计划（F003）密切相关。两个计划之间存在以下依赖关系：

1. **基础设施依赖**：xkongcloud-commons-uid公共库依赖于PostgreSQL迁移计划中的基础设施准备，包括PostgreSQL数据库的安装、配置和UID生成器所需的数据库表（infra_uid.instance_registry和infra_uid.worker_id_assignment）的创建。这些表应该放在专门的`infra_uid` Schema中，符合PostgreSQL最佳实践中推荐的多Schema组织方式，遵循`infra_<组件类型>`格式的命名规范。

2. **实施顺序**：
   - 首先，完成PostgreSQL基础设施准备（F003，第1周）
   - 然后，开始xkongcloud-commons-uid公共库的开发（F004，第1-2周）
   - 最后，在PostgreSQL迁移的集成测试阶段（F003，第6周）验证xkongcloud-commons-uid公共库的功能

3. **集成点**：
   - xkongcloud-business-internal-core项目将依赖xkongcloud-commons-uid公共库
   - UidGeneratorConfig类将使用xkongcloud-commons-uid公共库中的组件
   - 实体类将使用UID生成器生成唯一ID

详细的实施顺序和依赖关系请参考[PostgreSQL迁移实施计划](../../../features/F003-PostgreSQL迁移-20250508/plan/implementation-plan.md#实施顺序与依赖关系)中的"实施顺序与依赖关系"章节。

## 基本信息

- **功能ID**: F004
- **功能名称**: CommonsUidLibrary
- **版本**: 1.0
- **状态**: 草稿
- **负责人**: 系统架构组
- **联系方式**: <EMAIL>

## 实现详情

### 1. 模块创建

1. **创建xkongcloud-commons父模块**（如果不存在）
   - 在项目根目录下创建 `xkongcloud-commons` 目录
   - 创建 `pom.xml` 文件，配置为多模块父项目

   ```xml
   <?xml version="1.0" encoding="UTF-8"?>
   <project xmlns="http://maven.apache.org/POM/4.0.0"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
       <modelVersion>4.0.0</modelVersion>
       <parent>
           <groupId>org.xkong.cloud</groupId>
           <artifactId>xkongcloud</artifactId>
           <version>1.0.0-SNAPSHOT</version>
       </parent>

       <artifactId>xkongcloud-commons</artifactId>
       <packaging>pom</packaging>
       <name>XKong Cloud Commons</name>
       <description>XKong Cloud 公共组件库</description>

       <modules>
           <module>xkongcloud-commons-uid</module>
           <!-- 未来可能的其他公共模块 -->
       </modules>
   </project>
   ```

2. **创建xkongcloud-commons-uid子模块**
   - 在 `xkongcloud-commons` 目录下创建 `xkongcloud-commons-uid` 目录
   - 创建标准的Maven项目结构
   - 创建 `pom.xml` 文件，添加必要的依赖

   ```xml
   <?xml version="1.0" encoding="UTF-8"?>
   <project xmlns="http://maven.apache.org/POM/4.0.0"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
       <modelVersion>4.0.0</modelVersion>
       <parent>
           <groupId>org.xkong.cloud</groupId>
           <artifactId>xkongcloud-commons</artifactId>
           <version>1.0.0-SNAPSHOT</version>
       </parent>

       <artifactId>xkongcloud-commons-uid</artifactId>
       <name>XKong Cloud Commons UID</name>
       <description>分布式唯一ID生成公共库</description>

       <dependencies>
           <!-- 百度UID生成器 -->
           <dependency>
               <groupId>com.xfvape.uid</groupId>
               <artifactId>uid-generator</artifactId>
               <version>0.0.4-RELEASE</version>
           </dependency>

           <!-- Spring相关依赖 -->
           <dependency>
               <groupId>org.springframework</groupId>
               <artifactId>spring-context</artifactId>
           </dependency>
           <dependency>
               <groupId>org.springframework</groupId>
               <artifactId>spring-jdbc</artifactId>
           </dependency>
           <dependency>
               <groupId>org.springframework</groupId>
               <artifactId>spring-tx</artifactId>
           </dependency>

           <!-- 其他依赖 -->
           <dependency>
               <groupId>com.fasterxml.jackson.core</groupId>
               <artifactId>jackson-databind</artifactId>
           </dependency>
           <dependency>
               <groupId>org.slf4j</groupId>
               <artifactId>slf4j-api</artifactId>
           </dependency>

           <!-- 测试依赖 -->
           <dependency>
               <groupId>org.springframework.boot</groupId>
               <artifactId>spring-boot-starter-test</artifactId>
               <scope>test</scope>
           </dependency>
       </dependencies>
   </project>
   ```

3. **更新根pom.xml**
   - 在根 `pom.xml` 的 `<modules>` 部分添加 `xkongcloud-commons` 模块

### 2. 代码实现

以下是在`xkongcloud-commons-uid`公共库中实现UID相关功能的计划。在实现过程中，我们将设计API使其支持使用已有的`@Value("${xkong.kv.cluster-id}")`注入的`clusterId`变量初始化`PersistentInstanceManager`，而不是引入新的`uid.instance.application-name`参数。同时，我们将使用构建器模式和参数传递，确保UID库与KV参数服务完全解耦。

1. **创建包结构**
   - 创建 `org.xkong.cloud.commons.uid` 包及其子包

2. **实现WorkerNodeType枚举**
   - 在 `org.xkong.cloud.commons.uid` 包中创建 `WorkerNodeType` 枚举
   - 参考设计文档和百度UID生成器文档中的规范实现
   - 实现 `CONTAINER` 和 `ACTUAL` 两种节点类型及相关方法

3. **实现ValidationResultCache类**
   - 在 `org.xkong.cloud.commons.uid` 包中创建 `ValidationResultCache` 类
   - 实现线程安全的验证结果缓存，使用`ConcurrentHashMap`
   - 提供记录和检查已验证的Schema、表和参数的方法
   - 实现清除缓存的方法

4. **实现UidValidationUtils工具类**
   - 在 `org.xkong.cloud.commons.uid.util` 包中创建 `UidValidationUtils` 工具类
   - 提供静态方法验证数据库连接是否可用
   - 提供静态方法验证Schema是否存在
   - 提供静态方法验证表是否存在
   - 设计方法接受JdbcTemplate和schemaName作为参数，不自动执行验证

5. **实现MachineFingerprints类**
   - 在 `org.xkong.cloud.commons.uid` 包中创建 `MachineFingerprints` 类
   - 实现收集各种机器特征码的功能，包括BIOS UUID、系统序列号、MAC地址、OS主机名等
   - 确保实现对不同操作系统（Linux、Windows）的支持
   - 实现对云环境特征码的收集支持

6. **实现KeyManagementService类**
   - 在 `org.xkong.cloud.commons.uid` 包中创建 `KeyManagementService` 类
   - 实现与PostgreSQL的 `infra_uid. encryption_key` 表交互的功能
   - 实现密钥的获取、创建和缓存功能
   - 实现检查加密是否启用的方法
   - 添加Schema名称参数，支持灵活的Schema配置
   - 设计为独立的Spring Bean，而不是在PersistentInstanceManager内部实例化
   - 设计支持构造函数注入的依赖注入方式

7. **实现PersistentInstanceManagerBuilder类**
   - 在 `org.xkong.cloud.commons.uid` 包中创建 `PersistentInstanceManagerBuilder` 类
   - 实现构建器模式，提供流畅的API，支持链式调用设置各种参数
   - 提供设置所有必要参数的方法，包括：
     - `withJdbcTemplate(JdbcTemplate jdbcTemplate)`
     - `withTransactionTemplate(TransactionTemplate transactionTemplate)`
     - `withApplicationName(String applicationName)`
     - `withEnvironment(String environment)`
     - `withInstanceGroup(String instanceGroup)`
     - `withLocalStoragePath(String localStoragePath)`
     - `withRecoveryEnabled(boolean recoveryEnabled)`
     - `withHighConfidenceThreshold(int highConfidenceThreshold)`
     - `withMinimumAcceptableScore(int minimumAcceptableScore)`
     - `withRecoveryStrategy(String recoveryStrategy)`
     - `withRecoveryTimeoutSeconds(int recoveryTimeoutSeconds)`
     - `withInstanceIdOverride(Long instanceIdOverride)`
     - `withEncryptionEnabled(boolean encryptionEnabled)`
     - `withSchemaName(String schemaName)`
     - `withKeyManagementService(KeyManagementService keyManagementService)`
   - 实现`build()`方法，在构建前验证必要参数的有效性
   - 设置合理的默认值，如：
     - `environment` = "default"
     - `localStoragePath` = ".instance_id"
     - `recoveryEnabled` = true
     - `highConfidenceThreshold` = 150
     - `minimumAcceptableScore` = 70
     - `recoveryStrategy` = "ALERT_AUTO_WITH_TIMEOUT"
     - `recoveryTimeoutSeconds` = 300
     - `encryptionEnabled` = false
     - `schemaName` = "infra_uid"

8. **实现PersistentInstanceManager类**
   - 在 `org.xkong.cloud.commons.uid` 包中创建 `PersistentInstanceManager` 类
   - 实现本地文件存储与读取 `instance_unique_id` 的功能，支持加密和解密
   - 实现与PostgreSQL的 `instance_registry` 表交互的功能
   - 实现基于机器特征码的实例身份自动恢复功能
   - 集成 `KeyManagementService` 用于加密密钥管理
   - 使用外部工具包 `org.xkong.xkongkit.utils.EncryptionUtils` 进行加密和解密
   - 添加Schema名称参数，支持灵活的Schema配置
   - 设计支持构造函数注入的依赖注入方式
   - 确保API设计允许使用 `@Value("${xkong.kv.cluster-id}")` 注入的 `clusterId` 变量作为 `application_name`
   - 推荐通过`PersistentInstanceManagerBuilder`创建实例，而不是直接使用构造函数
   - **实现文件操作的并发控制**:
     - 使用文件锁（如Java的`FileLock`）确保对实例ID文件的独占访问
     - 使用临时文件和原子性重命名操作确保文件写入的原子性
     - 实现健壮的错误处理机制，包括重试逻辑和降级策略
     - 处理多进程环境下的文件访问冲突
   - **实现实例注册的并发控制**:
     - 使用数据库事务和唯一约束确保实例注册的并发安全
     - 处理多实例同时注册的冲突情况
     - 实现特征码匹配的并发处理策略

9. **实现PersistentInstanceWorkerIdAssigner类**
   - 在 `org.xkong.cloud.commons.uid` 包中创建 `PersistentInstanceWorkerIdAssigner` 类
   - 实现百度UID库的 `com.baidu.fsg.uid.worker.WorkerIdAssigner` 接口
   - 实现基于 `PersistentInstanceManager` 获取的 `instance_unique_id` 的工作机器ID分配逻辑
   - 实现基于租约的 `worker_id` 管理，包括租约的获取、续约和过期处理
   - 添加Schema名称参数，支持灵活的Schema配置
   - 设计支持构造函数注入的依赖注入方式
   - **实现Worker ID分配的并发控制机制**:
     - 使用数据库事务和行锁确保Worker ID分配的原子性
     - 使用条件更新防止并发冲突
     - 实现冲突检测和处理策略
     - 处理多实例同时请求Worker ID的场景
   - **实现安全检查机制**:
     - 在每次生成ID前验证租约有效性，防止使用过期的Worker ID
     - 定期检查数据库中的Worker ID分配状态，确保没有冲突
     - 实现自我修复机制，在检测到异常时自动重新获取Worker ID
     - 记录详细的操作日志，便于问题排查

10. **实现UidTableManager工具类**
    - 在 `org.xkong.cloud.commons.uid.util` 包中创建 `UidTableManager` 工具类
    - 提供静态方法用于管理UID生成器所需的数据库表（infra_uid.instance_registry、infra_uid.worker_id_assignment和infra_uid.encryption_key）
    - 实现Schema验证功能：
      - `createSchema(JdbcTemplate jdbcTemplate, String schemaName)`: 验证Schema是否存在，而非创建Schema。根据PostgreSQL最佳实践（C024 Sec 5.1.5），Schema应由DBA或应用方通过迁移工具（如Flyway）手动管理，而非由应用程序自动创建
    - 实现支持不同DDL策略的表结构管理功能：
      - `createTables(JdbcTemplate jdbcTemplate, String schemaName)`: 创建表和索引
      - `dropTables(JdbcTemplate jdbcTemplate, String schemaName)`: 删除表
      - `createTablesIfNotExist(JdbcTemplate jdbcTemplate, String schemaName)`: 如果表不存在则创建
      - `validateTables(JdbcTemplate jdbcTemplate, String schemaName)`: 验证表结构
      - `fillWorkerIdAssignments(JdbcTemplate jdbcTemplate, int workerBits, String schemaName)`: 填充worker_id_assignment表
    - 设计方法接受JdbcTemplate和schemaName作为参数，使工具类不依赖于特定的Spring上下文，并支持灵活的Schema配置
    - 提供详细的日志记录，便于排查问题
    - 实现表结构检查功能，确保表结构符合要求

### 3. 单元测试

1. **为ValidationResultCache类编写单元测试**
   - 创建 `ValidationResultCache` 的测试类
   - 测试线程安全性，使用多线程并发访问
   - 测试记录和检查已验证的Schema、表和参数的方法
   - 测试清除缓存的方法
   - 测试在高并发环境下的性能和正确性

2. **为UidValidationUtils工具类编写单元测试**
   - 创建 `UidValidationUtils` 的测试类
   - 使用mock技术模拟 `JdbcTemplate`
   - 测试数据库连接验证功能
   - 测试Schema存在性验证功能
   - 测试表存在性验证功能
   - 测试错误处理和异常情况

3. **为MachineFingerprints类编写单元测试**
   - 创建 `MachineFingerprints` 的测试类
   - 使用mock技术模拟系统调用和环境变量
   - 测试不同平台特征码的收集逻辑
   - 测试在不同操作系统环境下的行为（使用模拟环境）
   - 测试在云环境中的行为（使用模拟环境）
   - 测试特征码收集失败的情况下的错误处理

4. **为KeyManagementService类编写单元测试**
   - 创建 `KeyManagementService` 的测试类
   - 使用mock技术模拟 `JdbcTemplate`, `TransactionTemplate`
   - 测试密钥获取、创建和缓存功能
   - 测试加密启用状态检查功能
   - 测试Schema名称参数的使用
   - 测试错误处理和异常情况
   - 测试配置参数验证功能

5. **为PersistentInstanceManagerBuilder类编写单元测试**
   - 创建 `PersistentInstanceManagerBuilder` 的测试类
   - 测试构建器模式的链式调用
   - 测试所有参数设置方法
   - 测试默认值的正确设置
   - 测试build()方法的参数验证功能
   - 测试缺少必要参数时的异常处理
   - 测试构建出的PersistentInstanceManager实例的正确性

6. **为PersistentInstanceManager类编写单元测试**
   - 创建 `PersistentInstanceManager` 的测试类
   - 使用mock技术模拟 `JdbcTemplate`, `TransactionTemplate`, `KeyManagementService`, 文件IO
   - 测试ID加载、恢复（不同匹配场景）、注册逻辑
   - 测试使用`clusterId`作为`application_name`的功能
   - 测试加密和解密功能
   - 测试处理加密和未加密文件的兼容性
   - 测试Schema名称参数的使用
   - 测试错误处理和异常情况
   - 测试配置参数验证功能
   - 测试与构建器模式的集成
   - **测试文件操作的并发安全性**:
     - 模拟多线程同时读写实例ID文件的场景
     - 验证文件锁机制的有效性
     - 测试临时文件和原子性重命名操作的可靠性
     - 验证错误处理和重试机制的健壮性
   - **测试实例注册的并发安全性**:
     - 模拟多线程同时注册实例的场景
     - 验证数据库事务和唯一约束的有效性
     - 测试冲突处理策略的正确性

7. **为PersistentInstanceWorkerIdAssigner类编写单元测试**
   - 创建 `PersistentInstanceWorkerIdAssigner` 的测试类
   - 使用mock技术模拟 `JdbcTemplate`, `TransactionTemplate`, `PersistentInstanceManager`
   - 测试 `worker_id` 分配、续约、并发控制（通过验证SQL和参数）
   - 测试租约续约和过期处理
   - 测试并发分配情况下的行为
   - 测试Schema名称参数的使用
   - 测试错误处理和异常情况
   - **测试Worker ID分配的并发安全性**:
     - 模拟多线程同时请求Worker ID的场景
     - 验证数据库事务和行锁机制的有效性
     - 测试条件更新在并发环境下的正确性
     - 验证冲突检测和处理策略的可靠性
   - **测试租约续约的并发安全性**:
     - 模拟多线程同时续约的场景
     - 验证条件更新在并发环境下的正确性
     - 测试续约失败处理机制的健壮性
   - **测试安全检查机制**:
     - 测试租约有效性验证功能
     - 验证自我修复机制的正确性
     - 测试异常情况下的日志记录功能

8. **为UidTableManager工具类编写单元测试**
   - 创建 `UidTableManager` 的测试类
   - 使用mock技术模拟 `JdbcTemplate`
   - 测试表创建、删除、验证等功能
   - 测试表存在性检查功能
   - 测试worker_id_assignment表填充功能
   - 测试 encryption_key表创建和验证功能
   - 测试Schema名称参数的使用
   - 测试错误处理和异常情况

### 4. 文档编写

1. **创建README.md文件**
   - 介绍库的用途、特性
   - 说明如何集成（Maven/Gradle依赖）
   - 说明应用方如何配置Bean
   - 强调构建器模式的使用和KV参数服务解耦的优势
   - 提供完整的配置示例，包括使用构建器模式创建实例

2. **编写API文档（JavaDoc）**
   - 为所有公共类和方法添加JavaDoc注释
   - 使用Maven JavaDoc插件生成API文档
   - 特别详细说明构建器模式的使用方法
   - 说明验证职责的分配和解耦设计的优势

3. **提供数据库表DDL脚本**
   - 创建 `sql` 目录，包含 `instance_registry`、`worker_id_assignment` 和 ` encryption_key` 表的DDL脚本
   - 提供 `worker_id_assignment` 表的预填充脚本
   - 提供 ` encryption_key` 表的创建脚本
   - 确保所有脚本支持Schema名称参数化

4. **编写配置参数说明**
   - 详细说明所有可配置参数及其默认值
   - 说明这些参数如何通过应用方的配置传递给库组件
   - 特别说明Schema相关参数：
     - `postgresql.uid.schema`: UID生成器表所在的Schema名称，默认为"infra_uid"
   - 特别说明加密相关参数：
     - `uid.instance.encryption.enabled`: 是否启用实例ID文件加密，默认为"false"
     - `uid.instance.encryption.algorithm`: 加密算法，默认为"AES-256-GCM"
   - 特别说明恢复策略参数：
     - `uid.instance.recovery.strategy`: 恢复策略，可选值为"ALERT_AND_MANUAL"、"ALERT_AND_NEW"、"ALERT_AUTO_WITH_TIMEOUT"，默认为"ALERT_AUTO_WITH_TIMEOUT"
     - `uid.instance.recovery.timeout-seconds`: 自动恢复超时时间，默认为300秒
   - 说明验证相关参数和职责分配

5. **编写构建器模式使用指南**
   - 详细说明构建器模式的使用方法
   - 提供完整的示例代码
   - 说明各参数的默认值和设置方法
   - 说明参数验证逻辑

6. **编写验证职责分配指南**
   - 说明外部调用者负责的验证
   - 说明库组件内部的验证
   - 提供验证最佳实践
   - 说明如何避免重复验证

7. **编写故障排除指南**
   - 提供常见问题及解决方案
   - 提供故障排除步骤
   - 特别说明构建器模式相关的常见问题
   - 特别说明验证失败的常见原因和解决方法

## 关键流程

### 流程1: 模块创建与代码实现（第1周）

**流程描述**: 创建xkongcloud-commons-uid模块并实现代码

**流程图**:

```mermaid
sequenceDiagram
    participant Dev as 开发人员
    participant Commons as xkongcloud-commons
    participant CommonsUid as xkongcloud-commons-uid
    participant Docs as 设计文档

    Dev->>Commons: 创建xkongcloud-commons父模块
    Dev->>CommonsUid: 创建xkongcloud-commons-uid子模块
    Dev->>CommonsUid: 配置pom.xml
    Dev->>Docs: 参考设计文档和百度UID生成器文档
    Dev->>CommonsUid: 实现WorkerNodeType枚举
    Dev->>CommonsUid: 实现ValidationResultCache类
    Dev->>CommonsUid: 实现UidValidationUtils工具类
    Dev->>CommonsUid: 实现MachineFingerprints类
    Dev->>CommonsUid: 实现KeyManagementService类
    Dev->>CommonsUid: 实现PersistentInstanceManagerBuilder类
    Dev->>CommonsUid: 实现PersistentInstanceManager类
    Dev->>CommonsUid: 实现PersistentInstanceWorkerIdAssigner类
    Dev->>CommonsUid: 实现UidTableManager工具类
```

### 流程2: 单元测试与文档编写（第2周）

**流程描述**: 编写单元测试和文档

**流程图**:

```mermaid
sequenceDiagram
    participant Dev as 开发人员
    participant Test as 单元测试
    participant Doc as 文档

    Dev->>Test: 编写ValidationResultCache测试
    Dev->>Test: 编写UidValidationUtils测试
    Dev->>Test: 编写MachineFingerprints测试
    Dev->>Test: 编写KeyManagementService测试
    Dev->>Test: 编写PersistentInstanceManagerBuilder测试
    Dev->>Test: 编写PersistentInstanceManager测试
    Dev->>Test: 编写PersistentInstanceWorkerIdAssigner测试
    Dev->>Test: 编写UidTableManager测试
    Dev->>Doc: 创建README.md
    Dev->>Doc: 编写JavaDoc
    Dev->>Doc: 提供数据库表DDL脚本
    Dev->>Doc: 编写配置参数说明
    Dev->>Doc: 编写构建器模式使用指南
    Dev->>Doc: 编写验证职责分配指南
    Dev->>Doc: 编写故障排除指南
```

### 流程3: 集成与测试（第3周）

**流程描述**: 在xkongcloud-business-internal-core项目中集成公共库并测试

**流程图**:

```mermaid
sequenceDiagram
    participant Dev as 开发人员
    participant Core as xkongcloud-business-internal-core
    participant Test as 集成测试

    Dev->>Core: 添加对xkongcloud-commons-uid的依赖
    Dev->>Core: 创建配置类
    Dev->>Core: 创建ValidationResultCache Bean
    Dev->>Core: 创建KeyManagementService Bean
    Dev->>Core: 使用构建器模式创建PersistentInstanceManager Bean
    Dev->>Core: 创建PersistentInstanceWorkerIdAssigner Bean
    Dev->>Core: 配置使用公共库中的UID生成器组件
    Dev->>Test: 测试xkongcloud-business-internal-core与xkongcloud-commons-uid的集成
    Dev->>Test: 验证构建器模式和解耦设计的正确性
    Dev->>Test: 验证验证职责分配的正确性
    Dev->>Test: 验证功能正常工作
```

## 部署说明

### 环境要求

- **JDK版本**: JDK 21
- **依赖服务**:
  - PostgreSQL 17.4版本
- **构建工具**: Maven 3.9.9或更高版本

### 部署步骤

1. **构建xkongcloud-commons-uid**
   - 在项目根目录执行 `mvn clean install -pl xkongcloud-commons,xkongcloud-commons/xkongcloud-commons-uid`
   - 或在 `xkongcloud-commons/xkongcloud-commons-uid` 目录执行 `mvn clean install`

2. **更新依赖项目**
   - 在依赖项目的 `pom.xml` 中添加对 `xkongcloud-commons-uid` 的依赖
   - 重新构建依赖项目

### 验证方法

- 检查依赖项目是否能够正确引用 `xkongcloud-commons-uid` 中的类
- 执行依赖项目的单元测试和集成测试
- 验证UID生成功能是否正常工作

## 注意事项

- 确保实现符合设计文档中的规范和要求
- 设计API时考虑未来的扩展性和可维护性
- 保持API的清晰性和易用性
- 确保文档的完整性和准确性
- 确保单元测试的覆盖率
- 确保与依赖项目的集成测试通过

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 2.2 | 2025-06-30 | 修改UidTableManager.createSchema方法，去掉自动创建Schema的功能，符合PostgreSQL标准要求Schema应由DBA手动管理 | AI助手 |
| 2.1 | 2025-06-25 | 增强并发安全性：添加Worker ID分配、租约续约、实例身份恢复和本地文件操作的并发控制详细说明，完善单元测试计划 | AI助手 |
| 2.0 | 2025-06-20 | 重构设计：添加构建器模式、明确验证职责、完全解耦KV参数服务、添加ValidationResultCache和UidValidationUtils | AI助手 |
| 1.9 | 2025-06-13 | 添加ALERT_AUTO_WITH_TIMEOUT恢复策略和超时参数 | AI助手 |
| 1.8 | 2025-06-09 | 添加实例ID文件加密存储功能，增加KeyManagementService类的实现，更新PersistentInstanceManager实现 | AI助手 |
| 1.7 | 2025-06-05 | 更新Schema命名规范：将uid_generator改为infra_uid，符合infra_<组件类型>格式 | AI助手 |
| 1.6 | 2025-06-02 | 明确指定UID生成器相关表应该放在专门的Schema中，修改UidTableManager工具类描述，添加Schema配置参数说明 | AI助手 |
| 1.5 | 2025-05-27 | 添加UidTableManager工具类的实现细节，提供表结构管理功能，支持不同DDL策略 | AI助手 |
| 1.4 | 2025-05-26 | 添加与PostgreSQL迁移计划的关系章节，明确两个计划之间的依赖关系和实施顺序 | AI助手 |
| 1.3 | 2025-05-26 | 移除JPA实体类相关内容，包括依赖和实现步骤，采用纯JDBC方式实现数据库交互 | AI助手 |
| 1.2 | 2025-05-26 | 更新实施计划，将"代码迁移"改为"代码实现"，明确指出需要从头实现相关类而非迁移现有代码 | AI助手 |
| 1.1 | 2025-05-25 | 修改方案，使用已有的`xkong.kv.cluster-id`变量作为`application_name`，而不是引入新的`uid.instance.application-name`参数 | AI助手 |
| 1.0 | 2025-05-11 | 初始版本 | AI助手 |
