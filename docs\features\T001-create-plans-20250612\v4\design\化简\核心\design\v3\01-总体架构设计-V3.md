# V4.3方案：总体架构设计 (智能治理引擎与IDE协同版)

## 1. 文档信息

- **文档版本**: V4.3
- **创建日期**: 2025-08-13
- **核心思想**: **构建个以"设计文档"为核心"法典"的智能治理引擎，通过"富报告"驱动与IDE内置AI（如RooCode）的高效协同，实现设计与代码的自动化同步与质量保障。**
- **设计目标**: 建立一个能够对设计文档进行前置完备性检查、智能分析、并能委托外部AI执行文档修订和代码重构的闭环系统，最终通过自动化验证确保设计与代码的100%一致性。

## 2. 核心设计哲学：信任始于标准化，成功源于协同与验证

V4.3方案的设计哲学是V4.2的进一步升华：**真正的可靠性，源于在流程开始前，就将模糊的"设计思想"和"现有架构隐性知识"，转化为统一的、有丰富语义的、可扩展的、经过预验证的"原子知识单元" (`AtomicConstraint`)。**

我们不再依赖于在流程后期去"检测"冲突，而是在"阶段零"就通过确定性算法来"预防"冲突。所有后续的治理，都建立在这个100%无内部冲突的、由统一模型构成的"**全局知识库**"之上。

在此基础上，V4.3强调**专业分工与智能协同**：我们的治理引擎专注于**"治理"和"确保正确性"**，而IDE内置AI（如RooCode）专注于**"探索"和"生成"**。

## 3. 核心架构：以"富报告"为核心的迭代治理闭环

系统整体架构分为**V4.3治理引擎**和**IDE环境**两大部分，通过"富报告"和"`new_task`指令"实现高效的人与AI协同。

```mermaid
graph TD
    subgraph "人类领域"
        A[1. 启动审计]
        E[4. 审查富报告 & 复制指令]
        F[5. 委托RooCode]
        H[7. 确认修订]
    end

    subgraph "V4.3 治理引擎 (于 project_manager_v2 内部)"
        B["2. 算法: 全局扫描<br/>构建知识图谱"]
        B2["3. 算法: 图查询<br/>生成<b>计划骨架</b>"]
        C["4. PyAI (辅助):<br/>丰富计划骨架"]
        D["5. 生成<b>富报告</b><br/>(含结构化计划)"]
        I["8. 算法: 最终确定性校验"]
        J["9. 写入修订"]
        K["10. 再次触发审计"]
        subgraph "核心智能迭代环节"
            M["11. Py AI: **收敛性检查**"]
            N["12. Py AI: **提示词优化**"]
        end
        O["13. 生成'富报告' v2"]
    end

    subgraph "IDE 环境"
        G["6. RooCode (执行官)<br/>执行结构化计划"]
    end

    subgraph "外部服务"
        S["Serena (代码分析专家)"]
    end

    A --> B --> B2 --> C --> D
    D --> E --> F --> G
    G --> H --> I --> J
    J --> K --> M --> N --> O
    O --> E
    M -- "所有问题已修复" --> L[完成]
    
    %% 调用链
    G -- "调用" --> S
    S -- "响应" --> G
    G -- "返回结果给" --> B
```
**架构核心**: 系统的核心交互由 **V4.3治理引擎**、**人类开发者**和**IDE环境（RooCode）**构成。其核心是**算法驱动规划**：引擎内的确定性算法首先基于知识图谱生成任务的“计划骨架”，再由PyAI辅助增强为完整的“富报告”，最后委托RooCode执行。治理引擎的逻辑将作为对 `project_manager_v2` 内部 `DesignValidationWorkflow` 的重构和升级来实现。

-   **V4.3治理引擎**: 作为核心指挥和质量保障中心，负责：
    *   **算法**: 全局扫描设计文档，构建知识图谱；**并基于知识图谱进行图查询，确定性地推导出多步骤的‘计划骨架’**；执行最终的确定性校验。
    *   **Py AI (首席架构分析师)**: **接收算法生成的‘计划骨架’，为其填充自然语言指令和伪代码，最终生成完整的‘富报告’**；执行"收敛性检查"；优化给RooCode的提示词。
-   **人类开发者**: 扮演关键的决策者和"物理总线"，负责：
    *   启动审计任务。
    *   审查"富报告"并决定下一步行动。
    *   将引擎生成的`new_task`指令复制粘贴给RooCode。
    *   审查RooCode的修订结果并批准写入。
-   **IDE环境 (RooCode)**: 作为强大的执行者，负责：
    *   执行由`new_task`指令委托的复杂任务（如代码考、文档撰写、代码修改）。
    *   在设计阶段，其输出始终是对设计文档的修订内容。
-   **Serena (代码分析服务)**: 作为外部依赖，响应 RooCode 的调用，提供编译器级的代码事实，为治理引擎的双重验证提供微观图数据。

### 3.1. 核心交付物：唯一的交互产出——"富报告" (Rich Report)

我们的系统的**唯一**核心产出物，就是一份由我们的算法和Py AI联合生成的"**富报告**" (`rich_report.yaml`)。这份报告将作为文件，直接输出到被审计文档所在目录的工作区内，UI界面仅提供状态摘要和文件链接。

这份"富报告"必须包含以下**标准化**的区块：

-   **`report_metadata`**: 报告ID，生成时间，针对的设计文档（可以是`01~xx`号中的个）。
-   **`summary`**: 对本次审计的核心发现和建议的自然语言总结。
-   **`findings`**: 一个结构化的列表，每一项都是一个独立的"发现点"。每个"发现点"包含：
    *   **`finding_id`**: 发现点的唯一ID。
    *   **`description`**: 对问题的描述。
    *   **`severity`**: 严重等级 (`CRITICAL`, `HIGH`, `MEDIUM`)。
    *   **`atomic_constraint_draft`**: 一个由我们的Py AI生成的、与此发现点相关的`AtomicConstraint` **JSON草案**。
    *   **`pseudocode_block`**: 一个由我们的Py AI生成的**伪代码块**。其逻辑覆盖率必须达到**60%以上**（由算法校验）。
    *   **`recommended_task`**: 一个由**算法规划、PyAI增强**生成的、**给RooCode编排器的结构化 `SuperTaskInstruction` 任务计划**，明确指示RooCode需要分步执行的所有任务。

### 3.2. 核心工作流：设计文档的"审计-修订"迭代循环

1.  **启动审计**: 人类在UI中选择设计文档，启动审计。
2.  **引擎审计**: 算法扫描所有文档构建知识图谱，Py AI分析并生成"富报告"。
3.  **人类委托**: 人类审查报告，复制`new_task`指令给RooCode。
4.  **RooCode修订**: RooCode执行任务，输出修订后的文档内容。
5.  **人类确认**: 人类审查RooCode的修订，批准写入。
6.  **引擎校验与写入**: 算法执行最终确定性校验，将修订写入设计文档。
7.  **智能迭代**: 擎自动再次触发审计，Py AI执行"收敛性检查"并"提示词优化"，生成优化后的"富报告"，继续循环，直到所有问题收敛。

## 4. 架构价值

V4.3方案通过其创新的"设计驱动、智能协同"设计，达到了前所未有的可靠性和智能水平：

-   **冲突预防，而非检测**: 从根本上避免了在流程后期处理复杂语义冲突的难题，实现了"无垃圾输入"。
-   **规划的确定性与AI创造性的完美结合**: 通过‘算法驱动规划，PyAI辅助增强’的模式，系统的核心规划能力建立在确定性的图算法之上，确保了计划的可靠性和可复现性；同时，利用PyAI和RooCode的创造力来填充细节和执行任务，实现了两者的优势互补。
-   **职责清晰的AI-算法-人协同**: 算法负责确定性校验和规划，Py AI负责辅助增强，RooCode负责执行，人类负责最终决策。各司其职，风险最小化。
-   **文档即法典，文档亦可治理**: 设计文档不再是静的，而是活的、可被系统自动审计和修订的核心资产。
-   **自我优化与收敛**: 引入"收敛性检查"和"提示词优化"机制，使系统具备自我学习和迭代的能力，逐步提升治理效率。
-   **极高的可追溯性**: 任何一个最终的执行约束，都可以通过`id`和`parent_id`链条，清晰地追溯到其在`01号`文档中的根源。

## 5. 工程实现蓝图 (Engineering Blueprint)

为了将上述架构思想转化为可落地、可维护的工程实践，我们定义了以下的代码组结构和模块职责。

### 5.1. 代码目录结构

V4.3 的能力将作为一次升级，无缝融入现有的 `project_manager_v2` 架构中，而不是创建一个独立的包。所有修改都将在 `tools/ace/src/project_manager_v2/` 目录下进行。

```
tools/ace/src/
└── project_manager_v2/
    ├── ... (现有目录)
    ├── adapters/               # (新增) 外部工具适配器
    │   └── roocode_adapter.py  # (重构) 负责生成委托Serena调用的指令
    ├── workflows/
    │   └── design_validation_workflow.py # (重构) 升级为V4.3核心治理逻辑
    ├── services/
    │   └── ... (可能新增分析服务)
    └── data_models/
        └── v3_models.py        # (新增) V4.3核心数据模型
```

### 5.2. 关键模块职责

-   **`project_manager_v2/workflows/design_validation_workflow.py`**: **(重构)** 作为核心业务流程，负责协调整个审计和治理流程，调用分析师、校验器和适配器。
-   **`project_manager_v2/analysts/chief_architect_analyst.py`**: **(新增)** 封装了Py AI的核心逻辑，**接收‘计划骨架’**，并调用大语言模型为其填充丰富的自然语言描述、伪代码和上下文，最终生成完整的 `SuperTaskInstruction` 对象。同时负责**执行“收敛性检查”和“提示词优化”**。
-   **`project_manager_v2/validators/`**: **(重构)** 包含所有确定性算法，如`ConstraintPreprocessor`和`DocumentScanner`。**新增一个 `plan_generator.py` 模块，负责接收差异报告，在宏观语义地图上进行图查询，并输出结构化的‘计划骨架’。**
-   **`project_manager_v2/adapters/roocode_adapter.py`**: **(重构)** 负责将 V4.3 引擎内部的**高层治理意图**，转换为结构化的、给 RooCode 的 `new_task` 指令。**这些指令将明确地委托 RooCode 去调用 Serena MCP 服务**以获取代码事实，或执行代码/文档的修改。此适配器是 V4.3 引擎与 IDE 环境进行**间接**协作的关键桥梁。
-   **`project_manager_v2/validators/assertion_engine.py`**: **(新增)** 断言引擎；实现"约束 → 证据谓词 → 断言（MUST/SHOULD/FORBID）"，输出判定与证据。
-   **`project_manager_v2/data_models/v3_models.py`**: **(新增)** 定义所有核心数据结构，包括`AtomicConstraint`, `RichReport`, `SuperTaskInstruction`等。
-   **`web_interface/governance_blueprint.py`**: **(不变)** 定义Web接口，负责接收前端请求，调用`governance_service`，并将"富报告"渲染到九宫格UI的指定区域。

> **实现状态备注**: 本蓝图是V4.3的完整设计，所有模块均为新增或重构。

### 5.3. 算法驱动内核实现要点

为确保“算法驱动规划”的简单与可靠，其实现遵循以下要点：

-   **图数据结构**: 明确使用 `networkx` 库在内存中表示和查询知识图谱。
-   **查询策略**: 采用“限定深度的依赖遍历”（如BFS）作为核心查询策略，以避免“查询爆炸”并保证性能。
-   **计划骨架Schema**: 定义“计划骨架”的 YAML 结构，确保其稳定和可扩展，作为算法与PyAI之间清晰的契约。

## 6. 两阶段工作流：边界 / 交付 / 门禁（V4.3-S）

### 6.1 阶段一：仅设计文档（Docs-Only）
- 边界：只读取 `docs/**.md`；不触达代码仓；由 `DocumentScanner` 与 `ConstraintPreprocessor` 执行。
- 交付：`RichReport v1`（summary/findings/overall_status）、宏观语义地图（设计意图）。
- 门禁：Schema 0 违规；伪代码覆盖率≥60；图谱一致性冲突=0；`overall_status=COMPLIANT` 方可进入阶段二。

### 6.2 阶段二：代码对齐设计（Code-Align）
- 边界：通过委托 RooCode 调用 Serena 获取"微观图（代码事实）"；`assertion_engine` 执行"约束 → 证据谓词 → 断言"。
- 交付：`assertion_results.yaml`、`RichReport v2`（含 `recommended_task` 与证据），可选 `micro_graph.yaml`（调试）。
- 门禁：`MISSING=0`、`CONFLICT=0`、`LEGACY=0`，所有断言 `OK`；`overall_status=COMPLIANT` 才允许写入/合并。

## 6.3 核心机制：自我优化与收敛
- 收敛：**每一次**再次审计，无论是来自第一阶段的手动触发，还是第二阶段的自动触发，Py AI **都必须**执行收敛性检查。它通过对比新旧两份 `rich_report.yaml` 来评估进展，并在必要时优化 `recommended_task`，确保整个治理流程最终能达到100%合规的目标。
- 复现：同一 commit/配置重跑，报告哈希（排除时间戳）一致；每条断言附 `serena.request_id` 追溯证据。
