# 02-API管理核心模块（DRY重构版）

## 📋 文档信息

**文档ID**: F007-FOUR-LAYER-MEETING-API-MGMT-002  
**依赖配置**: 引用 `00-共同配置.json`  
**前置依赖**: 01-环境准备和基础配置.md  
**AI负载等级**: 中等（≤8个概念，≤600行代码，≤90分钟）  
**置信度目标**: 92%+  
**执行优先级**: 2  

## 🎯 API管理核心功能实现

### SQLite全景模型数据库管理器
```python
# 【AI自动创建】tools/ace/src/api_management/sqlite_storage/api_account_database.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API账号SQLite数据库管理器（基于V4设计文档）
引用: 00-共同配置.json 的 database_config 和 api_model_configurations
"""

import sqlite3
import json
import os
from datetime import datetime
from cryptography.fernet import Fernet
import sys
import os

# 确保可以导入common模块
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', '..')
sys.path.insert(0, src_dir)

from common_config_loader import CommonConfigLoader

class APIAccountDatabase:
    """API账号SQLite数据库管理器（基于V4设计文档）"""
    
    def __init__(self):
        # 加载共同配置
        self.config = CommonConfigLoader()
        self.db_config = self.config.get_database_config()
        self.api_configs = self.config.get_api_config()
        self.model_configs = self.config.config.get("api_model_configurations", {})
        
        # 数据库配置
        self.db_path = self.db_config.get("sqlite_path", "data/v4_panoramic_model.db")
        self.encryption_key = self._generate_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key.encode()[:44].ljust(44, b'='))
        
        # 初始化数据库
        self._init_database()
    
    def _generate_encryption_key(self):
        """生成加密密钥"""
        key_length = self.db_config.get("encryption_key_length", 44)
        return Fernet.generate_key().decode()[:key_length]
    
    def _init_database(self):
        """初始化数据库表结构"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # API配置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS api_configurations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    api_key TEXT UNIQUE NOT NULL,
                    model_name TEXT NOT NULL,
                    role TEXT NOT NULL,
                    api_type TEXT NOT NULL,
                    encrypted_config TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # API性能监控表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS api_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    api_key TEXT NOT NULL,
                    response_time REAL,
                    success_rate REAL,
                    confidence_score REAL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (api_key) REFERENCES api_configurations (api_key)
                )
            ''')
            
            conn.commit()
    
    def store_api_configuration(self, api_key, config_data):
        """存储API配置到SQLite数据库（加密存储）"""
        encrypted_config = self.cipher_suite.encrypt(json.dumps(config_data).encode())
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO api_configurations 
                (api_key, model_name, role, api_type, encrypted_config, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                api_key,
                config_data.get('model_name'),
                config_data.get('role'),
                config_data.get('api_type', 'primary'),
                encrypted_config.decode(),
                datetime.now().isoformat()
            ))
            conn.commit()
    
    def get_api_configuration(self, api_key):
        """获取API配置（解密）"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT encrypted_config FROM api_configurations WHERE api_key = ?
            ''', (api_key,))
            
            result = cursor.fetchone()
            if result:
                decrypted_data = self.cipher_suite.decrypt(result[0].encode())
                return json.loads(decrypted_data.decode())
            return None
    
    def get_primary_api_config(self, role):
        """获取主力API配置"""
        primary_apis = self.model_configs.get("primary_apis", {})
        
        role_mapping = {
            "architecture": "gmi_deepseek_r1_0528",
            "code_generation": "gmi_deepseek_v3_0324",
            "logic_optimization": "gmi_deepseek_v3_0324"
        }
        
        api_key = role_mapping.get(role)
        if api_key and api_key in primary_apis:
            config = primary_apis[api_key].copy()
            config.update({
                "api_endpoint": self.api_configs.get("gmi_base_url"),
                "api_key": self.api_configs.get("gmi_api_key")
            })
            return config
        return None
    
    def get_backup_api_config(self, role):
        """获取备用API配置"""
        backup_apis = self.model_configs.get("backup_apis", {})
        
        role_mapping = {
            "architecture": "chutes_deepseek_r1",
            "code_generation": "chutes_deepcoder_14b",
            "logic_optimization": "chutes_deepseek_v3_0324"
        }
        
        api_key = role_mapping.get(role)
        if api_key and api_key in backup_apis:
            config = backup_apis[api_key].copy()
            config.update({
                "api_endpoint": self.api_configs.get("chutes_base_url"),
                "api_key": self.api_configs.get("chutes_api_key")
            })
            return config
        return None
    
    def update_performance_metrics(self, api_key, response_time, success_rate, confidence_score):
        """更新API性能指标"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO api_performance 
                (api_key, response_time, success_rate, confidence_score)
                VALUES (?, ?, ?, ?)
            ''', (api_key, response_time, success_rate, confidence_score))
            conn.commit()
    
    def get_performance_summary(self, api_key, hours=24):
        """获取API性能摘要"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT 
                    AVG(response_time) as avg_response_time,
                    AVG(success_rate) as avg_success_rate,
                    AVG(confidence_score) as avg_confidence_score,
                    COUNT(*) as total_requests
                FROM api_performance 
                WHERE api_key = ? AND timestamp > datetime('now', '-{} hours')
            '''.format(hours), (api_key,))
            
            result = cursor.fetchone()
            if result and result[3] > 0:  # 有数据
                return {
                    "avg_response_time": result[0],
                    "avg_success_rate": result[1],
                    "avg_confidence_score": result[2],
                    "total_requests": result[3]
                }
            return None
```

## 🔄 API故障转移管理器

```python
# 【AI自动创建】tools/ace/src/api_management/account_management/api_failover_manager.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API故障转移管理器（基于DRY原则）
引用: 00-共同配置.json 的 api_model_configurations
"""

import sys
import os
from datetime import datetime

# 确保可以导入common模块
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', '..')
sys.path.insert(0, src_dir)

from common_config_loader import CommonConfigLoader

class APIFailoverManager:
    """API故障转移管理器（基于DRY原则）"""
    
    def __init__(self, api_db):
        self.api_db = api_db
        self.config = CommonConfigLoader()
        self.failover_history = []
        
        # 当前API状态跟踪（基于共同配置）
        self.current_api_status = self._init_api_status()
    
    def _init_api_status(self):
        """初始化API状态（基于共同配置）"""
        primary_apis = self.config.config.get("api_model_configurations", {}).get("primary_apis", {})
        
        status = {}
        for api_key, api_config in primary_apis.items():
            status[api_config["role"]] = {
                "current_api": api_key,
                "api_type": "primary",
                "model_name": api_config["model_name"],
                "api_provider": "GMI" if "gmi" in api_key else "Chutes",
                "status": "active",
                "last_response_time": api_config.get("response_time"),
                "success_rate": api_config.get("confidence_target", 0.90),
                "last_updated": datetime.now().isoformat()
            }
        
        return status
    
    def execute_api_failover(self, failed_api_role, error_info):
        """执行API故障转移（主力→备用）"""
        backup_config = self.api_db.get_backup_api_config(failed_api_role)
        if backup_config:
            # 记录故障转移历史
            failover_record = {
                "timestamp": datetime.now().isoformat(),
                "failed_api": failed_api_role,
                "failed_model": self.current_api_status[failed_api_role]["model_name"],
                "backup_api": backup_config["model_name"],
                "error_info": error_info,
                "failover_reason": f"主力API {self.current_api_status[failed_api_role]['model_name']} 失效"
            }
            self.failover_history.append(failover_record)
            
            # 更新当前API状态
            self.current_api_status[failed_api_role].update({
                "current_api": self._get_backup_api_key(failed_api_role),
                "api_type": "backup",
                "model_name": backup_config["model_name"],
                "status": "failover_active",
                "failover_timestamp": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat()
            })
            
            return backup_config
        return None
    
    def get_current_api_status(self):
        """获取当前API状态（用于Web界面显示）"""
        return {
            "api_status_summary": {
                "total_apis": len(self.current_api_status),
                "primary_active": sum(1 for status in self.current_api_status.values() if status["api_type"] == "primary"),
                "backup_active": sum(1 for status in self.current_api_status.values() if status["api_type"] == "backup"),
                "failover_count": len(self.failover_history),
                "last_updated": datetime.now().isoformat()
            },
            "detailed_status": self.current_api_status,
            "recent_failovers": self.failover_history[-5:] if self.failover_history else []
        }
    
    def _get_backup_api_key(self, failed_api_role):
        """获取备用API的键名"""
        backup_mapping = {
            "架构专家（GMI 0528性能更好）": "chutes_deepseek_r1",
            "代码生成和逻辑优化": "chutes_deepcoder_14b"
        }
        return backup_mapping.get(failed_api_role, "chutes_deepseek_r1")
```

## 🔧 API管理调试功能（DRY优化）

### 创建调试目录
```bash
# 【AI自动执行】创建调试目录
mkdir -p tools/ace/src/api_management/debug
echo "✅ API调试目录创建完成"
```

### API调试监控器
```python
# 【AI自动创建】tools/ace/src/api_management/debug/api_debug_monitor.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API调试监控器 - 基于MCP约束的调试设计
引用: 00-共同配置.json 的 mcp_debugging_constraints
"""

import sys
import os
from datetime import datetime
import asyncio

# 确保可以导入common模块
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', '..')
sys.path.insert(0, src_dir)

from common_config_loader import CommonConfigLoader
from common_error_handler import CommonErrorHandler

class APIDebugMonitor:
    """API调试监控器（基于MCP约束）"""

    def __init__(self):
        self.config = CommonConfigLoader()
        self.error_handler = CommonErrorHandler()
        self.debug_logs = []
        self.api_performance_data = {}

    def log_api_call(self, api_name, request_data, response_data, execution_time):
        """记录API调用（不使用print，基于MCP约束）"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "api_name": api_name,
            "request_data": request_data,
            "response_data": response_data,
            "execution_time": execution_time,
            "status": "success" if response_data.get("status") == "success" else "error"
        }

        self.debug_logs.append(log_entry)

        # 更新性能数据
        if api_name not in self.api_performance_data:
            self.api_performance_data[api_name] = {
                "total_calls": 0,
                "success_calls": 0,
                "total_time": 0,
                "avg_time": 0
            }

        perf_data = self.api_performance_data[api_name]
        perf_data["total_calls"] += 1
        perf_data["total_time"] += execution_time
        perf_data["avg_time"] = perf_data["total_time"] / perf_data["total_calls"]

        if log_entry["status"] == "success":
            perf_data["success_calls"] += 1

        return log_entry

    def get_debug_summary(self):
        """获取调试摘要（用于Web界面显示）"""
        return {
            "total_logs": len(self.debug_logs),
            "recent_logs": self.debug_logs[-10:] if self.debug_logs else [],
            "api_performance": self.api_performance_data,
            "debug_url": self.error_handler.web_config.get("debug_url"),
            "last_updated": datetime.now().isoformat()
        }

    def validate_api_health(self):
        """验证API健康状态"""
        health_report = {
            "timestamp": datetime.now().isoformat(),
            "api_health": {},
            "overall_status": "healthy"
        }

        for api_name, perf_data in self.api_performance_data.items():
            success_rate = perf_data["success_calls"] / perf_data["total_calls"] if perf_data["total_calls"] > 0 else 0
            avg_time = perf_data["avg_time"]

            if success_rate >= 0.9 and avg_time <= 5.0:
                status = "healthy"
            elif success_rate >= 0.7 and avg_time <= 10.0:
                status = "warning"
            else:
                status = "critical"
                health_report["overall_status"] = "critical"

            health_report["api_health"][api_name] = {
                "status": status,
                "success_rate": success_rate,
                "avg_response_time": avg_time,
                "total_calls": perf_data["total_calls"]
            }

        return health_report

# 全局API调试监控器
api_debug_monitor = APIDebugMonitor()
```

## ✅ API管理模块完成验证

### 第一步：验证文件创建
```bash
# 【AI自动执行】验证API管理模块文件创建
python -c "
import os

required_files = [
    'tools/ace/src/api_management/sqlite_storage/api_account_database.py',
    'tools/ace/src/api_management/account_management/api_failover_manager.py',
    'tools/ace/src/api_management/debug/api_debug_monitor.py'
]

required_dirs = [
    'tools/ace/src/api_management/sqlite_storage',
    'tools/ace/src/api_management/account_management',
    'tools/ace/src/api_management/debug'
]

# 验证目录
for dir_path in required_dirs:
    if os.path.exists(dir_path):
        print(f'✅ 目录存在: {dir_path}')
    else:
        print(f'❌ 目录缺失: {dir_path}')
        exit(1)

# 验证文件
for file_path in required_files:
    if os.path.exists(file_path):
        print(f'✅ 文件存在: {file_path}')
    else:
        print(f'❌ 文件缺失: {file_path}')
        exit(1)

print('✅ 所有API管理模块文件验证通过')
"
```

### 第二步：功能验证脚本
```bash
# 【AI自动执行】API管理模块功能验证
python -c "
import sys
import os
sys.path.insert(0, 'tools/ace/src')

try:
    # 验证基础配置加载
    from common_config_loader import CommonConfigLoader
    config = CommonConfigLoader()
    print('✅ 基础配置加载成功')

    # 验证API数据库模块
    from api_management.sqlite_storage.api_account_database import APIAccountDatabase
    db = APIAccountDatabase()
    print('✅ API数据库初始化成功')

    # 测试配置获取
    arch_config = db.get_primary_api_config('architecture')
    if arch_config:
        print(f'✅ 架构API配置获取成功: {arch_config.get(\"model_name\", \"未知模型\")}')
    else:
        print('⚠️ 架构API配置获取为空，但模块加载成功')

    # 验证故障转移管理器
    from api_management.account_management.api_failover_manager import APIFailoverManager
    failover = APIFailoverManager(db)
    status = failover.get_current_api_status()
    print(f'✅ 故障转移管理器初始化成功: {status[\"api_status_summary\"][\"total_apis\"]} 个API')

    # 验证调试监控器
    from api_management.debug.api_debug_monitor import APIDebugMonitor
    debug_monitor = APIDebugMonitor()
    debug_summary = debug_monitor.get_debug_summary()
    print(f'✅ API调试监控器初始化成功: {debug_summary.get(\"debug_url\", \"调试URL未设置\")}')

    print('✅ API管理核心模块验证完成（包含调试功能）')

except ImportError as e:
    print(f'❌ 模块导入失败: {str(e)}')
    import traceback
    traceback.print_exc()
    exit(1)
except Exception as e:
    print(f'❌ API管理模块验证失败: {str(e)}')
    import traceback
    traceback.print_exc()
    exit(1)
"
```

## 📊 阶段完成标准

### 成功标准
- ✅ SQLite数据库管理器创建并初始化成功
- ✅ API配置加密存储功能实现
- ✅ 主力和备用API配置获取功能正常
- ✅ API故障转移管理器实现完成
- ✅ 性能监控功能实现

### 输出文件清单
- `tools/ace/src/api_management/sqlite_storage/api_account_database.py`
- `tools/ace/src/api_management/account_management/api_failover_manager.py`

### 下一步依赖
- 03-双向协作机制实现.md 可以开始执行
- 04-多API并发控制.md 依赖此模块

**预期执行时间**: 90分钟  
**AI负载等级**: 中等  
**置信度**: 92%+  
**人类参与**: 无需人类参与（AI自主执行）
