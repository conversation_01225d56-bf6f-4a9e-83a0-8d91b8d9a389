# 06-ValidationLoop语义验证引擎细化设计（伪代码级）

## 1. 现有实现溯源
- 文件：`tools/ace/src/executors/validation_driven_executor.py`
- 主要类/方法：
  - `class ValidationLoop`（第729行）
  - `async def multi_dimensional_validation(...)`（第738行）
- 现状：
  - 语义验证逻辑与格式/结构验证混杂，未完全聚焦于高层业务语义
  - 缺乏AI辅助批判者机制、复杂约束插件化等高级能力

## 2. 目标结构与接口（伪代码级）

### 2.1 ValidationLoop类结构
```python
class ValidationLoop:
    def __init__(self, ai_service_manager, validation_ai_config=None):
        self.ai_service_manager = ai_service_manager
        self.validation_ai_config = validation_ai_config
        self.custom_rule_plugins = []  # 复杂约束插件

    async def semantic_validation(self, execution_result: Dict) -> ValidationResult:
        issues = []
        # 1. 上下文一致性
        if not self._check_context_consistency(execution_result):
            issues.append('上下文不一致')
        # 2. 护栏合规性
        if not self._check_guardrail_compliance(execution_result):
            issues.append('违反护栏')
        # 3. 复杂约束插件
        for plugin in self.custom_rule_plugins:
            if not plugin.validate(execution_result):
                issues.append(f'复杂约束失败: {plugin.name}')
        # 4. AI批判者（可选）
        if self.validation_ai_config:
            ai_critique = await self._ai_critic(execution_result)
            if not ai_critique['passed']:
                issues.append('AI批判者未通过')
        return ValidationResult(
            confidence=1.0 if not issues else 0.0,
            py_results=[], ai_results=[], issues=issues, passed=(not issues)
        )

    def _check_context_consistency(self, execution_result):
        # 检查参数与context一致性
        ...
        return True

    def _check_guardrail_compliance(self, execution_result):
        # 检查是否有高危操作
        ...
        return True

    async def _ai_critic(self, execution_result):
        # 调用AI服务进行批判性分析
        ...
        return {'passed': True}

    def register_plugin(self, plugin):
        self.custom_rule_plugins.append(plugin)
```

### 2.2 复杂约束插件接口
```python
class CustomRulePlugin:
    def __init__(self, name):
        self.name = name
    def validate(self, execution_result):
        # 业务自定义校验逻辑
        ...
        return True
```

### 2.3 单元测试建议
```python
def test_semantic_validation_pass():
    loop = ValidationLoop(None)
    result = {'some': 'result'}
    v = asyncio.run(loop.semantic_validation(result))
    assert v.passed

def test_semantic_validation_fail():
    class FailPlugin:
        name = 'fail'
        def validate(self, _): return False
    loop = ValidationLoop(None)
    loop.register_plugin(FailPlugin())
    result = {'some': 'result'}
    v = asyncio.run(loop.semantic_validation(result))
    assert not v.passed
```

## 3. 需变更/新建/删除内容（精确到代码）
- 修改：
  - ValidationLoop类，剥离格式/结构验证，聚焦语义层面
  - 增加AI批判者、复杂约束插件机制
- 新建：
  - CustomRulePlugin接口及实现
- 单元测试：
  - `test_validation_loop.py`，覆盖所有分支

## 4. 兼容性与测试建议
- 语义验证升级为增量改造，旧有格式/结构验证可保留兜底
- 建议：
  - 单元测试：上下文一致性、护栏合规性、复杂约束、AI批判者等分支
  - 集成测试：端到端流程回归

## 5. 代码引用与路径
- `tools/ace/src/executors/validation_driven_executor.py`（ValidationLoop类、主流程调用点） 