# 项目管理器核心设计文档

## 1. 文档元数据
- **文档ID**: PM-CORE-DESIGN-001
- **版本**: V1.0
- **创建日期**: 2025-08-11
- **状态**: 设计稿
- **技术栈**: Python 3.8+, Flask, SQLite, Mermaid, AtomicConstraint数据模型

## 2. 核心定位与设计哲学

### 核心定位
项目管理器是V4多维立体脚手架系统的核心组件，负责协调和管理整个项目治理流程。它作为"服务-经理"多实例管理模式的实现，确保每个项目的上下文和状态完全隔离，支持并行处理多个项目。

### 核心设计原则
1.  **"微核+插件" (Microkernel + Plugins)**: 核心引擎极其简单，具体验证逻辑由独立插件实现。
2.  **"声明式治理" (Declarative Governance)**: 通过声明式的引用机制完成系统治理和知识传递。
3.  **"状态隔离" (State Isolation)**: 每个项目实例的状态完全隔离，确保并行处理的安全性。
4.  **"可观测性优先" (Observability-First)**: 所有操作都可观察、可追踪、可审计。

## 3. 全局约束与规则

### ⚡ 性能指标约束
- **项目初始化时间**: ≤1000ms
- **状态转换响应时间**: ≤200ms
- **并发处理能力**: ≥100 projects/second
- **内存使用**: ≤100MB per project

### 🔐 安全约束
- **数据加密**: 所有敏感数据必须加密存储
- **访问控制**: 所有操作必须经过权限验证
- **审计日志**: 所有操作必须记录审计日志
- **API限流**: 防止恶意请求和过载

### 🏗️ 架构约束
- **单例服务**: ProjectManagerService必须是单例
- **多实例管理**: ProjectManager必须支持多实例
- **被动日志**: TaskLogbook必须是被动日志记录器
- **HTTP轮询**: 状态同步必须使用HTTP轮询

## 4. 宏观架构视图

### 核心模块架构
```mermaid
graph TB
    PMService[ProjectManagerService] --> PMFactory[ProjectManager Factory]
    PMService --> StatusAPI[Status API]
    PMFactory --> PMInstance1[ProjectManager Instance 1]
    PMFactory --> PMInstance2[ProjectManager Instance 2]
    PMFactory --> PMInstanceN[ProjectManager Instance N]
    
    PMInstance1 --> TaskLogbook1[TaskLogbook 1]
    PMInstance1 --> ValidationLoop1[ValidationLoop 1]
    PMInstance1 --> PluginRegistry1[Plugin Registry 1]
    
    PMInstance2 --> TaskLogbook2[TaskLogbook 2]
    PMInstance2 --> ValidationLoop2[ValidationLoop 2]
    PMInstance2 --> PluginRegistry2[Plugin Registry 2]
    
    ValidationLoop1 --> ConstraintValidator[Constraint Validator]
    ValidationLoop1 --> StateMachineValidator[State Machine Validator]
    ValidationLoop1 --> BoundaryValidator[Boundary Validator]
    
    PluginRegistry1 --> ConstraintPlugin[Constraint Plugin]
    PluginRegistry1 --> StateMachinePlugin[State Machine Plugin]
    PluginRegistry1 --> BoundaryPlugin[Boundary Plugin]
```

### 分层架构
```mermaid
graph BT
    API[API Layer<br/>(Flask REST API)] --> Service[Service Layer<br/>(ProjectManagerService)]
    Service --> Manager[Manager Layer<br/>(ProjectManager)]
    Manager --> Validation[Validation Layer<br/>(ValidationLoop + Plugins)]
    Validation --> Storage[Storage Layer<br/>(SQLite + Memory)]
```

## 5. 核心流程与交互

### 项目创建与管理流程
```mermaid
sequenceDiagram
    participant Client as Client
    participant API as PM API
    participant Service as ProjectManagerService
    participant Factory as PM Factory
    participant PM as ProjectManager
    participant Logbook as TaskLogbook
    
    Client->>API: POST /get_and_create
    API->>Service: get_or_create_manager(project_path)
    Service->>Factory: create_instance(project_path)
    Factory->>PM: new ProjectManager(project_path)
    PM->>Logbook: initialize_logbook()
    PM-->>Service: manager_instance
    Service-->>API: manager_instance
    API-->>Client: manager_id
```

### 状态查询流程
```mermaid
sequenceDiagram
    participant Client as Client
    participant API as PM API
    participant Service as ProjectManagerService
    participant PM as ProjectManager
    participant Logbook as TaskLogbook
    
    Client->>API: GET /status/{manager_id}
    API->>Service: get_manager_status(manager_id)
    Service->>PM: get_status()
    PM->>Logbook: read_status()
    Logbook-->>PM: current_status
    PM-->>Service: current_status
    Service-->>API: current_status
    API-->>Client: current_status
```

### 验证执行流程
```mermaid
sequenceDiagram
    participant PM as ProjectManager
    participant Loop as ValidationLoop
    participant Registry as PluginRegistry
    participant Plugin as ValidatorPlugin
    participant Constraint as AtomicConstraint
    
    PM->>Loop: execute_validation(validation_type, params)
    Loop->>Registry: get_validator(validation_type)
    Registry-->>Loop: validator_plugin
    Loop->>Plugin: validate(constraint_params)
    Plugin->>Constraint: apply_validation_rules()
    Constraint-->>Plugin: validation_result
    Plugin-->>Loop: validation_result
    Loop-->>PM: validation_result
```

## 6. 边界条件与核心状态机

### 6.1. 关键参数与临界值分析
| 关键参数 | 正常范围 | 临界值 | 达到临界值时的行为 |
| :--- | :--- | :--- | :--- |
| `max_concurrent_projects` | 1-1000 | > 1000 | 触发限流，返回 `HTTP 429 Too Many Requests` |
| `project_init_timeout` | 1-5秒 | > 5秒 | 触发初始化超时，标记项目为失败 |
| `validation_timeout` | 1-30秒 | > 30秒 | 触发验证超时，标记验证为失败 |
| `memory_usage_per_project` | 10-100MB | > 100MB | 触发内存警告，暂停非关键操作 |
| `max_status_history` | 10-1000条 | > 1000条 | 触发历史记录清理，保留最近500条 |

### 6.2. 核心实体状态机
```mermaid
stateDiagram-v2
    [*] --> CREATED: 创建项目管理器
    CREATED --> INITIALIZING: 开始初始化
    INITIALIZING --> READY: 初始化完成
    READY --> PROCESSING: 开始处理
    PROCESSING --> VALIDATING: 验证阶段
    VALIDATING --> PROCESSING: 验证完成
    PROCESSING --> COMPLETING: 处理完成
    COMPLETING --> COMPLETED: 项目完成
    COMPLETED --> [*]
    
    CREATED --> ERROR: 创建失败
    INITIALIZING --> ERROR: 初始化失败
    PROCESSING --> ERROR: 处理异常
    VALIDATING --> ERROR: 验证失败
    COMPLETING --> ERROR: 完成失败
    ERROR --> RETRYING: 自动重试
    RETRYING --> PROCESSING: 重试成功
    RETRYING --> FAILED: 重试失败
    FAILED --> [*]
    
    READY --> CANCELLED: 用户取消
    PROCESSING --> CANCELLED: 用户取消
    VALIDATING --> CANCELLED: 用户取消
    CANCELLED --> [*]
```

## 7. 排除范围
- **不包含具体业务逻辑实现**: 项目管理器只负责协调和管理流程，不包含具体业务逻辑
- **不包含UI组件**: 项目管理器不直接处理用户界面交互
- **不包含第三方系统集成**: 项目管理器不直接与外部系统集成
- **不包含数据持久化实现**: 项目管理器不负责具体的数据存储实现

## 8. 未来演进方向
- **分布式项目管理**: 支持跨节点的项目管理器协调
- **智能调度优化**: 基于历史数据和资源使用情况优化项目调度
- **自适应验证策略**: 根据项目特征动态调整验证策略
- **可视化监控面板**: 提供实时项目状态监控和分析仪表板
- **机器学习优化**: 使用机器学习算法优化项目处理流程