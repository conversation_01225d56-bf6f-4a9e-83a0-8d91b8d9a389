# Spring Boot gRPC集成测试最佳实践指南

## 概述

本文档提供Spring Boot应用中gRPC依赖集成测试的标准解决方案，解决常见的Connection refused错误和依赖注入问题。

## 核心问题

### 问题描述
在Spring Boot集成测试中，当应用依赖gRPC服务时，常出现以下问题：
- `Connection refused: localhost:19090` - gRPC服务连接失败
- `UidGeneratorConfig无法创建Bean` - 依赖链中断
- `KVParamService @PostConstruct失败` - 初始化阶段连接外部服务

### 根本原因
1. **外部依赖不可用**: 测试环境中gRPC服务未启动
2. **配置硬编码**: 配置类中硬编码了外部服务地址
3. **初始化时机**: @PostConstruct在Bean创建时立即执行连接

## 标准解决方案

### 方案：Mock gRPC Stubs

**核心思路**: 使用@MockBean模拟gRPC Stubs，保留配置类但避免真实连接

#### 1. 测试配置结构
```java
@SpringBootTest(classes = {
    PostgreSQLConfig.class,           // 保留数据库配置
    UidGeneratorConfig.class,         // 保留业务配置
    TestMockConfiguration.class,      // 测试Mock配置
    GrpcClientConfig.class           // 保留gRPC配置类
})
@ActiveProfiles("test")
public class IntegrationTest {
    
    // Mock gRPC Stubs，防止真实连接
    @MockBean
    private KVServiceGrpc.KVServiceBlockingStub kvServiceBlockingStub;
    
    @MockBean
    private KVServiceGrpc.KVServiceStub kvServiceStub;
    
    // 确保依赖服务使用Mock对象
    @Autowired
    private KVParamService kvParamService;
}
```

#### 2. 关键技术要点

**保留配置类**:
- 包含GrpcClientConfig以创建必要的Bean定义
- 维持Spring容器的完整性

**@MockBean覆盖策略**:
- @MockBean会覆盖Spring容器中的真实Bean
- KVParamService的@PostConstruct方法会使用Mock的Stub对象
- 避免Connection refused错误

**测试范围简化**:
- 专注于数据库连接验证
- 避免复杂的UID生成逻辑依赖
- 测试核心功能而非外部集成

#### 3. 测试覆盖范围
```java
@Test
public void testDatabaseConnection() {
    // 验证数据库连接正常
    Integer result = jdbcTemplate.queryForObject("SELECT 1", Integer.class);
    assertEquals(1, result);
}

@Test
public void testGrpcMockConfiguration() {
    // 验证gRPC Mock配置成功
    assertNotNull(kvParamService);
    assertNotNull(kvServiceBlockingStub);
    assertNotNull(kvServiceStub);
}

@Test
public void testTableCreationAndBasicOperations() {
    // 验证数据库操作能力
    // 创建表、插入数据、查询验证
}
```

## 实施步骤

### 步骤1: 识别gRPC依赖
- 查找项目中的gRPC Stub注入点
- 识别@PostConstruct中的gRPC调用
- 确定依赖链路

### 步骤2: 创建Mock配置
- 使用@MockBean模拟所有gRPC Stub
- 保留原有配置类结构
- 设置test profile

### 步骤3: 简化测试范围
- 专注于核心功能测试
- 避免复杂的外部依赖
- 验证Mock配置有效性

### 步骤4: 验证解决方案
- 运行测试确保通过
- 检查日志无Connection refused错误
- 验证业务逻辑正常

## 常见问题处理

### @MockBean过时警告
```
WARNING: @MockBean已过时，且标记为待删除
```
**处理方式**: 警告不影响测试功能，可在后续Spring Boot版本升级时更新

### Spring容器冲突
**问题**: 多个@SpringBootConfiguration冲突
**解决**: 明确指定classes参数，避免自动扫描冲突

### Mock对象未生效
**问题**: 仍然尝试真实连接
**解决**: 确保@MockBean在正确的测试类中声明，检查Bean名称匹配

## 适用场景

### 适用情况
- Spring Boot集成测试
- 依赖外部gRPC服务的应用
- 测试环境无法访问外部服务
- 需要快速验证核心功能

### 不适用情况
- 需要测试gRPC通信协议本身
- 端到端集成测试
- 性能测试（需要真实网络延迟）

## 最佳实践

1. **配置分离**: 测试配置与生产配置分离
2. **Mock精确**: 只Mock必要的外部依赖
3. **测试专注**: 每个测试专注于特定功能
4. **文档记录**: 记录Mock的行为和限制

## 相关技术

- Spring Boot Test
- Mockito @MockBean
- gRPC Java
- JUnit 5
- Spring Profiles

---
**文档版本**: 1.0  
**创建日期**: 2025-06-04  
**适用版本**: Spring Boot 3.x, gRPC Java  
**维护状态**: 活跃维护
