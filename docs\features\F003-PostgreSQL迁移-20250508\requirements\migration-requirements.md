---
title: PostgreSQL演进架构迁移需求
document_id: F003-REQ-001
document_type: 需求文档
category: 数据库迁移
scope: XKC-CORE
keywords: [PostgreSQL, 演进架构, 数据库迁移, 服务抽象层, 配置驱动, 需求规格, 恢复策略, 超时]
created_date: 2025-05-08
updated_date: 2025-01-15
status: 已批准
version: 2.0
authors: [系统架构组, AI助手]
affected_features:
  - F003
related_docs:
  - ../design/postgresql-evolution-architecture-integration.md
  - ../../../plans/2-PostgreSQL/postgresql_migration_plan.md
  - ../../../common/architecture/patterns/postgresql-evolution-implementation-guide.md
  - ../../../common/middleware/postgresql/integration-guide.md
---

# PostgreSQL演进架构迁移需求

## 1. 需求概述

本文档描述了将XKC-CORE项目中的数据库从Cassandra迁移到PostgreSQL的演进架构需求。迁移的主要目标是获得更好的关系型数据库支持、事务处理能力和查询灵活性，同时建立支持持续演进的架构基础设施，为系统从单体架构到微服务架构的平滑演进做好准备。

## 2. 背景

当前系统使用Cassandra作为主要数据库，通过Spring Data Cassandra进行访问。随着项目的发展，我们发现关系型数据库更适合当前的数据模型和业务需求。PostgreSQL作为一个成熟的开源关系型数据库，提供了完整的ACID事务支持、复杂查询能力和丰富的索引选项，是替代Cassandra的理想选择。

### 2.1 当前阶段说明

项目当前处于一个特殊的初始阶段：
- Cassandra环境已搭建完成，底层的Cassandra数据库集群已经配置并可运行
- 数据库中尚未包含任何生产环境的业务数据
- 应用程序中尚未实现依赖于特定数据库的复杂业务逻辑

这一阶段的特殊性意味着，本次从Cassandra到PostgreSQL的"迁移"更多的是一次技术栈的切换和新数据库的初始化，而非传统意义上涉及复杂数据迁移和业务逻辑兼容性调整的迁移。

## 3. 功能需求

### 3.1 演进架构基础设施需求

- **FR-001**: 系统必须建立服务抽象层，支持本地和远程服务的透明切换
- **FR-002**: 系统必须实现配置驱动机制，通过配置文件控制架构模式和服务调用方式
- **FR-003**: 系统必须建立数据访问抽象层，支持本地和远程数据访问的透明切换
- **FR-004**: 系统必须实现Schema演进管理，根据架构模式自动创建相应的Schema

### 3.2 服务抽象层需求

- **FR-005**: 系统必须提供ServiceInterface注解，用于标识可演进的服务接口
- **FR-006**: 系统必须实现UserManagementService接口，支持用户管理功能的抽象
- **FR-007**: 系统必须提供LocalUserManagementService实现，基于本地数据访问
- **FR-008**: 系统必须预留RemoteUserManagementService接口，为未来远程调用做准备

### 3.3 数据访问抽象层需求

- **FR-009**: 系统必须提供DataAccessService接口，统一数据访问操作
- **FR-010**: 系统必须提供QueryCondition类，封装查询条件构建
- **FR-011**: 系统必须实现LocalUserDataAccessService，基于JPA的本地数据访问
- **FR-012**: 系统必须预留分布式数据访问策略，为未来微服务架构做准备

### 3.4 配置驱动需求

- **FR-013**: 系统必须提供ServiceConfiguration类，控制服务的部署和调用方式
- **FR-014**: 系统必须支持架构模式配置（MONOLITHIC、MODULAR、HYBRID、MICROSERVICES）
- **FR-015**: 系统必须支持服务模式配置（LOCAL、REMOTE、HYBRID）
- **FR-016**: 系统必须支持数据访问模式配置（LOCAL、REMOTE、DISTRIBUTED）

### 3.5 数据库连接与配置

- **FR-017**: 系统必须能够连接到PostgreSQL数据库，并通过KVParamService从xkongcloud-service-center获取所有必要的配置参数
- **FR-018**: 系统必须支持通过KVParamService配置的连接池参数，包括最大连接数、最小空闲连接数、连接超时时间等
- **FR-019**: 系统必须支持通过KVParamService配置的JPA参数，包括DDL自动生成策略、SQL显示和格式化、批处理大小等
- **FR-020**: 系统必须支持根据架构模式调整JPA属性，优化不同架构模式下的性能

### 3.6 数据访问层转换

- **FR-021**: 系统必须将Spring Data Cassandra依赖替换为Spring Data JPA和PostgreSQL驱动依赖
- **FR-022**: 系统必须将CassandraConfig替换为支持演进架构的PostgreSQLConfig类
- **FR-023**: 系统必须将Cassandra特有的注解替换为JPA注解，并调整数据类型映射
- **FR-024**: 系统必须将CassandraRepository替换为JpaRepository，同时保留兼容性接口

### 3.3 数据模型转换

- **FR-009**: 系统必须将Cassandra的非规范化数据模型转换为PostgreSQL的规范化关系模型
- **FR-010**: 系统必须建立适当的表关系（一对一、一对多、多对多）
- **FR-011**: 系统必须将Cassandra的分区键和聚类列转换为PostgreSQL的主键和索引
- **FR-012**: 系统必须重新设计查询以利用SQL的JOIN功能和添加适当的索引以提高查询性能

### 3.4 数据初始化

- **FR-013**: 系统必须支持在应用启动时初始化数据库表结构
- **FR-014**: 系统必须支持在应用启动时检查并添加初始数据
- **FR-015**: 系统必须支持不同的DDL策略，包括RECREATE和CREATE_IF_NOT_EXISTS
- **FR-016**: 系统必须支持带超时的自动恢复策略（ALERT_AUTO_WITH_TIMEOUT），在超时后自动选择最佳匹配或创建新实例

## 4. 非功能需求

### 4.1 性能需求

- **NFR-001**: 系统必须能够处理与当前Cassandra实现相当或更好的查询性能
- **NFR-002**: 系统必须能够处理与当前Cassandra实现相当或更好的写入性能
- **NFR-003**: 系统必须能够处理与当前Cassandra实现相当或更好的并发性能

### 4.2 可靠性需求

- **NFR-004**: 系统必须提供完整的ACID事务支持
- **NFR-005**: 系统必须能够在数据库连接失败时提供明确的错误信息
- **NFR-006**: 系统必须能够在配置参数缺失时提供明确的错误信息

### 4.3 可维护性需求

- **NFR-007**: 系统必须使用标准的JPA注解和查询方法，以便于维护和扩展
- **NFR-008**: 系统必须提供清晰的日志记录，以便于调试和问题排查
- **NFR-009**: 系统必须支持通过配置参数调整数据库连接和性能参数，而无需修改代码

### 4.4 兼容性需求

- **NFR-010**: 系统必须与Spring Boot 3.4.3兼容
- **NFR-011**: 系统必须与PostgreSQL 17.4版本兼容
- **NFR-012**: 系统必须与现有的KVParamService机制兼容

## 5. 约束与假设

### 5.1 约束

- 所有数据库连接参数必须通过KVParamService从xkongcloud-service-center获取
- 不允许在代码或本地配置文件中硬编码任何生产环境敏感或可能变化的配置值
- 生产环境必须设置DDL自动生成策略为none或validate

### 5.2 假设

- PostgreSQL服务器已经安装并可用
- xkongcloud-service-center已经运行并可用
- 项目处于初始阶段，没有真实业务数据和复杂业务逻辑依赖

## 6. 验收标准

- 系统能够成功连接到PostgreSQL数据库
- 系统能够通过KVParamService获取所有必要的配置参数
- 系统能够创建和管理数据库表结构
- 系统能够执行基本的CRUD操作
- 系统能够执行复杂的查询操作
- 系统能够提供完整的ACID事务支持
- 系统能够在应用启动时初始化数据

## 7. 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 2.0 | 2025-01-15 | 重构为演进架构迁移需求，增加服务抽象层和配置驱动需求 | AI助手 |
| 1.1 | 2025-06-13 | 添加恢复策略超时需求 | AI助手 |
| 1.0 | 2025-05-08 | 初始版本 | AI助手 |
