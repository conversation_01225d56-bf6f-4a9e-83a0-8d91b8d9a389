# AI实施计划标准模板

## 文档元数据
- **文档ID**: [唯一文档标识符，如F001-FEATURE-PLAN-001]
- **文档类型**: AI实施计划
- **创建日期**: [YYYY-MM-DD]
- **更新日期**: [YYYY-MM-DD]  
- **版本**: [v1.0]
- **状态**: [草稿|审核中|已批准|已执行|已归档]
- **负责人**: [负责人姓名]

## AI认知约束
- **单步骤代码限制**: 每个步骤限制在50行代码以内
- **认知复杂度控制**: 单次处理概念数量≤5个
- **记忆边界管理**: 总操作文件数≤3个/阶段
- **幻觉防护激活**: 每个步骤都有具体验证锚点
- **边界护栏激活**: `@BOUNDARY_GUARD_ACTIVATION`

## 🚨 实施范围边界（必读）

### ✅ 包含范围
- [明确列出本实施计划包含的功能、组件、模块]
- [具体的技术栈、依赖、配置项]
- [明确的文件、类、方法范围]

### ❌ 排除范围  
- [明确列出不包含的功能、组件、模块]
- [不涉及的技术栈、中间件、第三方服务]
- [明确排除的扩展功能或相关需求]

### 🚧 边界护栏
- **范围检查点1**：[构思阶段] 确认方案不超出包含范围
- **范围检查点2**：[计划阶段] 验证步骤清单符合边界定义  
- **范围检查点3**：[执行阶段] 每个关键步骤后确认范围一致性
- **强制确认机制**：任何边界外需求必须停止执行并明确询问用户

## 实施概述

### 目标
[简要描述实施目标和预期结果]

### 复杂度分析
- **预估修改文件数**: [数量]个文件
- **预估代码行数**: [数量]行代码 
- **复杂度等级**: [低|中|高]
- **是否需要分批处理**: [是|否]

### 量化分析（复杂任务必需）
**基于实际调查的具体数据**：
- **[组件名1]**: [N]个[操作类型]点，复杂度[等级]
- **[组件名2]**: [N]个[操作类型]点，复杂度[等级]  
- **[其他组件]**: [总体描述和数量统计]

**分批处理策略**（当单个组件>20个操作点时）：
- **[组件名]处理策略**: 分[N]批处理，每批[N]个操作点
- **批次验证节点**: 每批完成后立即编译验证
- **异常分组原则**: 按[分组标准]分组处理

### 依赖关系
- **前置条件**: [列出必须满足的前置条件]
- **依赖项目**: [列出依赖的其他项目或组件]
- **配置文件**: ref:[相关配置文件路径]#[具体配置项]

## 分阶段步骤

### 阶段[N]：[阶段名称]
**认知单元**: [该阶段AI需要理解的核心概念]
**操作边界**: [该阶段允许操作的范围和限制]
**验证锚点**: [该阶段的成功验证标准]

#### 步骤[N.M]：[步骤名称]
**目标**: [步骤具体目标]

**执行指引**:
- [具体操作指导]
- [参数配置参考]: ref:[配置文件路径]#[JSON路径或锚点]
- [代码模板参考]: ref:[模板文件路径]#[锚点]

**关键信息**（来自 ref:[映射文件路径]#[JSON路径]）:
- [关键信息1]：[具体数据或配置]
- [关键信息2]：[具体数据或配置]
- [关键信息3]：[具体数据或配置]

**关键操作**（基于上述关键信息）:
- [关键操作1描述] → [对应错误码或配置]
- [关键操作2描述] → [对应错误码或配置]

**执行策略**: 
- [如果复杂] 分[N]批处理，每批[N]个操作点
- [分组原则] 按[异常类型|功能模块|技术类别]分组处理
- [验证节点] 每批完成后立即编译验证
- [边界控制] 单批处理不超过50行代码修改

**验证**: [具体验证标准和方法]

## 关键流程

### 流程[N]：[流程名称]
**流程描述**: [流程的主要功能和目的]

**实现步骤**:
1. [步骤1描述]
2. [步骤2描述]  
3. [步骤3描述]

**验证点**: [关键验证点和标准]

## 测试策略

### 测试范围
- [单元测试范围]
- [集成测试范围]
- [功能验证范围]

### 验证标准
- [编译验证]: [具体编译命令]
- [功能验证]: [具体验证步骤]
- [回归验证]: [回归测试标准]

## 回滚机制

### 回滚准备
- [回滚条件定义]
- [回滚步骤说明]
- [数据备份要求]

### 紧急处理
- [紧急情况处理流程]
- [人工介入标准]

## 执行检查清单

### 执行前准备
- [ ] 确认工作目录位置
- [ ] 激活AI认知约束：`@AI_COGNITIVE_CONSTRAINTS`
- [ ] 激活边界护栏：`@BOUNDARY_GUARD_ACTIVATION`
- [ ] 验证前置条件满足

### 执行中检查（每个步骤后）
- [ ] 验证操作在边界范围内
- [ ] 执行编译验证
- [ ] 确认验证锚点达成
- [ ] 清理临时代码

### 执行后验证
- [ ] 完整功能验证
- [ ] 依赖关系验证
- [ ] 文档更新确认
- [ ] 回滚机制测试

---

## 模板使用说明

### 引用语法规范
使用格式：`ref:[文件路径]#[JSONPath或锚点]`

**示例**:
- JSON路径引用：`ref:config/mappings.json#$.exception_mapping.rules`
- Markdown锚点引用：`ref:docs/guide.md#操作指南`
- 代码模板引用：`ref:templates/code-template.md#异常替换模板`

### 语义结构要求
每个步骤必须包含：
- **认知单元**: AI需要理解的核心概念（≤5个）
- **操作边界**: 明确的操作范围和限制
- **验证锚点**: 具体可验证的成功标准

### 量化分批原则
- 预估修改量>50行代码时，必须进行量化分析
- 复杂任务必须设计分批处理策略
- 每批完成后立即验证
- 单个步骤代码修改≤50行 