package org.xkong.cloud.business.internal.core.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.xkong.cloud.business.internal.core.service.KVParamService;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PostgreSQL演进架构配置类
 * 配置数据源、EntityManagerFactory和TransactionManager
 * 支持持续演进架构的配置驱动机制
 */
@Configuration
@EnableJpaRepositories(basePackages = "org.xkong.cloud.business.internal.core.repository")
@DependsOn("kvParamService")
public class PostgreSQLConfig {

    private static final Logger log = LoggerFactory.getLogger(PostgreSQLConfig.class);

    @Autowired
    private KVParamService kvParamService;

    @Autowired
    private ServiceConfiguration serviceConfiguration;

    private String ddlAuto;
    private List<String> schemaList;

    /**
     * 获取DDL自动生成策略
     * 从KV参数服务获取postgresql.ddl-auto参数
     *
     * @return DDL自动生成策略
     */
    public String getDdlAuto() {
        if (ddlAuto == null) {
            // 使用KVParamService获取参数
            log.info("准备获取PostgreSQL DDL自动生成策略");

            // 使用KVParamService获取参数，不提供默认值
            ddlAuto = kvParamService.getParam("postgresql.ddl-auto");
            if (ddlAuto == null || ddlAuto.trim().isEmpty()) {
                // 如果KV服务未提供DDL策略，则记录错误并抛出异常，阻止启动
                log.error("PostgreSQL配置错误: 必需的'postgresql.ddl-auto'参数未在KV服务中找到。");
                throw new IllegalStateException("PostgreSQL DDL auto strategy ('postgresql.ddl-auto') must be configured in the KV service.");
            }

            // 验证DDL策略值的有效性
            List<String> validValues = Arrays.asList("none", "validate", "update", "create", "create-drop");
            if (!validValues.contains(ddlAuto.toLowerCase())) {
                log.error("PostgreSQL配置错误: 无效的DDL策略 '{}', 有效值为: {}", ddlAuto, validValues);
                throw new IllegalStateException("Invalid PostgreSQL DDL auto strategy: " + ddlAuto);
            }

            log.info("使用PostgreSQL DDL自动生成策略: {}", ddlAuto);
        }
        return ddlAuto;
    }

    /**
     * 获取Schema列表
     * 从KV参数服务获取postgresql.schema.list参数
     *
     * @return Schema列表
     */
    public List<String> getSchemaList() {
        if (schemaList == null) {
            // 使用KVParamService获取参数
            log.info("准备获取PostgreSQL Schema列表");

            // 使用KVParamService获取参数，提供默认值
            String schemaListStr = kvParamService.getParam("postgresql.schema.list", "user_management,common_config,infra_uid");
            schemaList = Arrays.asList(schemaListStr.split(","));

            log.info("使用PostgreSQL Schema列表: {}", schemaList);
        }
        return schemaList;
    }

    /**
     * 验证Schema是否存在
     * 检查数据库中是否存在所需的Schema
     * 如果任何必需的Schema不存在，则抛出异常，导致应用启动失败
     */
    public void verifySchemas(JdbcTemplate jdbcTemplate) {
        List<String> schemas = getSchemaList();
        List<String> missingSchemas = new ArrayList<>();

        for (String schema : schemas) {
            try {
                log.info("验证Schema是否存在: {}", schema);
                // 查询Schema是否存在
                Integer count = jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name = ?",
                    Integer.class,
                    schema
                );

                if (count == null || count == 0) {
                    // 记录缺失的Schema
                    missingSchemas.add(schema);
                    log.error("PostgreSQL配置错误: 必需的Schema '{}' 不存在。", schema);
                } else {
                    log.info("Schema验证成功: {}", schema);
                }
            } catch (Exception e) {
                log.error("验证Schema时发生错误: {}, 错误: {}", schema, e.getMessage(), e);
                throw new IllegalStateException("Error verifying PostgreSQL Schema '" + schema + "': " + e.getMessage(), e);
            }
        }

        // 如果有任何Schema缺失，抛出异常，导致应用启动失败
        if (!missingSchemas.isEmpty()) {
            String errorMessage = String.format(
                "应用启动失败: 以下必需的PostgreSQL Schema不存在: %s。这些Schema必须由DBA或开发人员手动创建，应用程序不会自动创建Schema。",
                String.join(", ", missingSchemas)
            );
            log.error(errorMessage);
            throw new IllegalStateException(errorMessage);
        }
    }

    @Bean
    @Primary
    public DataSource dataSource() {
        HikariConfig config = new HikariConfig();

        // 从KVParamService获取基础连接参数
        String url = kvParamService.getParam("postgresql.url");
        String username = kvParamService.getParam("postgresql.username");
        String password = kvParamService.getParam("postgresql.password");

        // 验证必需参数
        if (url == null || url.trim().isEmpty()) {
            log.error("PostgreSQL配置错误: 必需的'postgresql.url'参数未在KV服务中找到。");
            throw new IllegalStateException("PostgreSQL URL ('postgresql.url') must be configured in the KV service.");
        }

        // 设置基础连接参数
        config.setJdbcUrl(url);
        config.setUsername(username);
        config.setPassword(password);

        // 从KVParamService获取连接池参数
        // postgresql.pool.max-size：开发环境推荐10，生产环境推荐CPU核心数×2-4
        int maxSize = Integer.parseInt(kvParamService.getParam("postgresql.pool.max-size", "10"));
        // postgresql.pool.min-idle：开发环境推荐5，生产环境推荐max-size的30%-50%
        int minIdle = Integer.parseInt(kvParamService.getParam("postgresql.pool.min-idle", "5"));
        // postgresql.pool.connection-timeout：开发环境推荐30000(30秒)，生产环境推荐5000-10000(5-10秒)
        int connectionTimeout = Integer.parseInt(kvParamService.getParam("postgresql.pool.connection-timeout", "30000"));
        // postgresql.pool.idle-timeout：开发环境推荐600000(10分钟)，生产环境推荐300000-900000(5-15分钟)
        int idleTimeout = Integer.parseInt(kvParamService.getParam("postgresql.pool.idle-timeout", "600000"));
        // postgresql.pool.max-lifetime：开发环境推荐1800000(30分钟)，生产环境推荐1800000-3600000(30-60分钟)
        int maxLifetime = Integer.parseInt(kvParamService.getParam("postgresql.pool.max-lifetime", "1800000"));

        // 设置连接池参数
        config.setMaximumPoolSize(maxSize);
        config.setMinimumIdle(minIdle);
        config.setConnectionTimeout(connectionTimeout);
        config.setIdleTimeout(idleTimeout);
        config.setMaxLifetime(maxLifetime);

        // 添加PostgreSQL特定属性
        config.addDataSourceProperty("stringtype", "unspecified"); // 处理未知类型字符串
        config.addDataSourceProperty("reWriteBatchedInserts", "true"); // 优化批量插入

        HikariDataSource dataSource = new HikariDataSource(config);

        // 创建JdbcTemplate用于验证Schema
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);

        // 验证Schema是否存在
        verifySchemas(jdbcTemplate);

        return dataSource;
    }

    /**
     * 获取当前架构模式
     */
    public ServiceConfiguration.ArchitectureMode getArchitectureMode() {
        return serviceConfiguration.getArchitectureMode();
    }

    /**
     * 判断是否启用演进架构支持
     */
    public boolean isEvolutionArchitectureEnabled() {
        return serviceConfiguration.getArchitectureMode() != ServiceConfiguration.ArchitectureMode.MONOLITHIC;
    }

    /**
     * 根据架构模式调整JPA属性
     */
    private Map<String, Object> getEvolutionAwareJpaProperties() {
        Map<String, Object> props = jpaProperties(); // 调用原有方法

        // 根据架构模式调整属性
        ServiceConfiguration.ArchitectureMode mode = getArchitectureMode();
        switch (mode) {
            case MICROSERVICES:
                // 微服务模式下的优化配置
                props.put("hibernate.jdbc.batch_size", "20"); // 减少批处理大小
                props.put("hibernate.connection.pool_size", "5"); // 减少连接池大小
                break;
            case HYBRID:
                // 混合模式下的平衡配置
                props.put("hibernate.jdbc.batch_size", "30");
                props.put("hibernate.connection.pool_size", "10");
                break;
            default:
                // 单体模式保持原有配置
                break;
        }

        return props;
    }

    @Bean
    @Primary
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(DataSource dataSource) {
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource);
        em.setPackagesToScan("org.xkong.cloud.business.internal.core.entity");

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);

        // 使用演进感知的JPA属性
        em.setJpaPropertyMap(getEvolutionAwareJpaProperties());

        return em;
    }

    @Bean
    @Primary
    public PlatformTransactionManager transactionManager(EntityManagerFactory entityManagerFactory) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(entityManagerFactory);
        return transactionManager;
    }

    /**
     * 获取JPA属性
     */
    public Map<String, Object> jpaProperties() {
        Map<String, Object> props = new HashMap<>();

        // 从KV参数服务获取JPA配置
        String showSql = kvParamService.getParam("postgresql.show-sql", "false");
        String formatSql = kvParamService.getParam("postgresql.format-sql", "true");

        // 基础JPA属性
        // 启用JDBC元数据访问，让Hibernate自动检测PostgreSQL方言
        // 这样可以消除hibernate.dialect的弃用警告，同时保持功能完整性
        props.put("hibernate.boot.allow_jdbc_metadata_access", "true");
        props.put("hibernate.hbm2ddl.auto", getDdlAuto());
        props.put("hibernate.show_sql", showSql);
        props.put("hibernate.format_sql", formatSql);
        props.put("hibernate.use_sql_comments", "true");

        // PostgreSQL特定优化
        props.put("hibernate.jdbc.batch_size", "50");
        props.put("hibernate.order_inserts", "true");
        props.put("hibernate.order_updates", "true");
        props.put("hibernate.jdbc.batch_versioned_data", "true");

        // 连接池配置
        props.put("hibernate.connection.provider_disables_autocommit", "true");

        return props;
    }
}
