---
title: UID数据流程图
description: 展示xkongcloud-commons-uid库中数据流动和处理过程
created_date: 2025-05-18
updated_date: 2025-05-18
version: 1.0
status: 草稿
author: AI助手
---

# UID数据流程图

此图展示了xkongcloud-commons-uid库中数据的流动和处理过程，包括实例注册、ID生成和租约管理。

```mermaid
flowchart TD
    subgraph 应用初始化
        A1[应用启动] --> A2[加载配置参数]
        A2 --> A3[创建UID组件]
        A3 --> A4[初始化实例身份]
    end
    
    subgraph 实例身份管理
        B1[尝试加载本地实例ID] --> B2{本地ID存在?}
        B2 -->|是| B3[验证实例ID有效性]
        B2 -->|否| B4[收集机器特征码]
        B4 --> B5[查询匹配实例]
        B5 --> B6{找到匹配?}
        B6 -->|是| B7[恢复实例身份]
        B6 -->|否| B8[注册新实例]
        B3 -->|有效| B9[使用现有实例ID]
        B3 -->|无效| B4
        B7 --> B10[保存实例ID到本地]
        B8 --> B10
        B9 --> B11[实例身份确认]
        B10 --> B11
    end
    
    subgraph Worker ID分配
        C1[获取实例ID] --> C2[查询Worker ID分配]
        C2 --> C3{已分配?}
        C3 -->|是| C4[续约Worker ID]
        C3 -->|否| C5[分配新Worker ID]
        C4 --> C6[更新租约时间]
        C5 --> C6
        C6 --> C7[Worker ID确认]
    end
    
    subgraph UID生成
        D1[接收生成请求] --> D2[获取Worker ID]
        D2 --> D3[获取当前时间戳]
        D3 --> D4[获取序列号]
        D4 --> D5[组合生成UID]
        D5 --> D6[返回UID]
    end
    
    subgraph 数据存储
        DB1[(instance_registry表)]
        DB2[(worker_id_assignment表)]
        DB3[(encryption_key表)]
        FS1[本地实例ID文件]
    end
    
    %% 流程连接
    A4 --> B1
    B11 --> C1
    C7 --> D1
    
    %% 数据存储连接
    B5 -.-> DB1
    B7 -.-> DB1
    B8 -.-> DB1
    B10 -.-> FS1
    C2 -.-> DB2
    C4 -.-> DB2
    C5 -.-> DB2
    
    %% 加密相关
    subgraph 加密管理
        E1[检查加密启用状态] --> E2{启用加密?}
        E2 -->|是| E3[获取加密密钥]
        E2 -->|否| E4[跳过加密]
        E3 --> E5[加密/解密数据]
    end
    
    B10 -.-> E1
    E3 -.-> DB3
    
    %% 样式设置
    classDef process fill:#222,stroke:#82b366,stroke-width:2px;
    classDef decision fill:#222,stroke:#d6b656,stroke-width:2px;
    classDef data fill:#dae8fc,stroke:#6c8ebf,stroke-width:2px;
    classDef subgraphStyle fill:#f5f5f5,stroke:#666666,stroke-width:1px;
    
    class A1,A2,A3,A4,B1,B3,B4,B5,B7,B8,B9,B10,B11,C1,C2,C4,C5,C6,C7,D1,D2,D3,D4,D5,D6,E1,E3,E4,E5 process;
    class B2,B6,C3,E2 decision;
    class DB1,DB2,DB3,FS1 data;
```
