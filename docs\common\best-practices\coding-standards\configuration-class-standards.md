# Configuration Class Standards

**Authority Source**: docs/common/best-practices/coding-standards/configuration-class-standards.md  
**Version**: 2.0 (Dynamic Parameter Architecture)  
**Last Updated**: 2025-01-15  
**Status**: Active Standard

## Overview

This document defines the coding standards for configuration classes in XKongCloud projects, implementing the new dynamic parameter management architecture using @Parameter and @RequiredParameters annotations.

## Core Architecture Principles

### 1. Annotation-Driven Parameter Management

**REQUIRED**: All configuration classes must use annotation-driven parameter declarations:

```java
@Component
@RequiredParameters({
    "component.url",
    "component.timeout"
})
public class ComponentConfiguration {
    
    @Parameter("component.url")
    private String componentUrl;
    
    @Parameter("component.timeout") 
    private Integer timeoutSeconds;
}
```

### 2. Dynamic Parameter Analyzer Dependency

**REQUIRED**: All configuration classes must depend on DynamicParameterAnalyzer:

```java
private final DynamicParameterAnalyzer dynamicParameterAnalyzer;

@Autowired
public ComponentConfiguration(DynamicParameterAnalyzer dynamicParameterAnalyzer) {
    this.dynamicParameterAnalyzer = dynamicParameterAnalyzer;
}
```

**PROHIBITED**: Hardcoded parameter validation logic:

```java
// ❌ WRONG - Hardcoded validation
@PostConstruct
public void validate() {
    if (componentUrl == null || componentUrl.isEmpty()) {
        throw new IllegalArgumentException("component.url is required");
    }
}

// ✅ CORRECT - Dynamic validation
@PostConstruct
public void validate() {
    dynamicParameterAnalyzer.validateRequiredParameters(this.getClass());
}
```

### 3. Parameter Usage Tracking Integration

**REQUIRED**: All configuration classes must integrate ParameterUsageTracker:

```java
private final ParameterUsageTracker parameterUsageTracker;

public String getComponentUrl() {
    parameterUsageTracker.trackParameterAccess(this.getClass(), "component.url");
    return componentUrl;
}
```

## Mandatory Standards

### 1. Class Structure Requirements

- **@Component annotation**: All configuration classes must be Spring-managed beans
- **@RequiredParameters annotation**: Must declare all required parameters
- **Constructor injection**: Use constructor-based dependency injection
- **@PostConstruct validation**: Use dynamic parameter analyzer for validation

### 2. Parameter Declaration Standards

- **@Parameter annotation**: Every configuration parameter must have @Parameter annotation
- **Consistent naming**: Parameter names must follow kebab-case convention
- **Type safety**: Use appropriate Java types for parameter values
- **Documentation**: Each parameter must have JavaDoc documentation

### 3. Validation Standards

- **Dynamic validation only**: Use DynamicParameterAnalyzer.validateRequiredParameters()
- **No hardcoded checks**: Prohibited to implement manual parameter validation
- **Runtime validation**: Support runtime parameter validation through analyzer
- **Error handling**: Let analyzer handle validation errors and messaging

### 4. Usage Tracking Standards

- **Track all access**: Every parameter getter must track usage
- **Real usage only**: Parameters must have actual usage in application logic
- **No fake usage**: Prohibited to add parameters just for logging without real usage
- **Statistics support**: Provide parameter usage statistics through tracker

## Implementation Examples

### Basic Configuration Class

```java
@Component
@RequiredParameters({
    "database.url",
    "database.username", 
    "database.password"
})
public class DatabaseConfiguration {
    
    @Parameter("database.url")
    private String databaseUrl;
    
    @Parameter("database.username")
    private String username;
    
    @Parameter("database.password")
    private String password;
    
    @Parameter("database.pool.max-size")
    private Integer maxPoolSize;
    
    private final DynamicParameterAnalyzer dynamicParameterAnalyzer;
    private final ParameterUsageTracker parameterUsageTracker;
    
    @Autowired
    public DatabaseConfiguration(
            DynamicParameterAnalyzer dynamicParameterAnalyzer,
            ParameterUsageTracker parameterUsageTracker) {
        this.dynamicParameterAnalyzer = dynamicParameterAnalyzer;
        this.parameterUsageTracker = parameterUsageTracker;
    }
    
    @PostConstruct
    public void validateAndInitialize() {
        dynamicParameterAnalyzer.validateRequiredParameters(this.getClass());
        initializeConnectionPool();
    }
    
    public String getDatabaseUrl() {
        parameterUsageTracker.trackParameterAccess(this.getClass(), "database.url");
        return databaseUrl;
    }
    
    // Additional getters with tracking...
}
```

### Complex Configuration with Nested Properties

```java
@Component
@RequiredParameters({
    "service.endpoint.url",
    "service.security.enabled",
    "service.retry.max-attempts"
})
public class ServiceConfiguration {
    
    @Parameter("service.endpoint.url")
    private String endpointUrl;
    
    @Parameter("service.security.enabled")
    private Boolean securityEnabled;
    
    @Parameter("service.retry.max-attempts")
    private Integer maxRetryAttempts;
    
    @Parameter("service.timeout.connection")
    private Integer connectionTimeout;
    
    @Parameter("service.timeout.read")
    private Integer readTimeout;
    
    // Dependencies and implementation following standards...
}
```

## Compliance Validation

### Automated Checks

The following automated checks validate compliance with these standards:

1. **@Parameter Annotation Check**: Verify all configuration fields have @Parameter annotation
2. **DynamicParameterAnalyzer Dependency**: Verify dependency injection of analyzer
3. **Usage Tracking Integration**: Verify ParameterUsageTracker integration
4. **No Hardcoded Validation**: Verify absence of manual validation logic
5. **Required Parameters Declaration**: Verify @RequiredParameters annotation completeness

### Manual Review Points

1. **Parameter Naming Consistency**: Review parameter naming follows project conventions
2. **Real Usage Verification**: Ensure parameters have actual usage in business logic
3. **Documentation Completeness**: Verify JavaDoc documentation for all parameters
4. **Error Handling Appropriateness**: Review error handling strategy alignment

## Migration from Legacy Configuration

### Step 1: Add Annotations

Replace hardcoded parameter access with @Parameter annotations:

```java
// Before
private String componentUrl = System.getProperty("component.url");

// After  
@Parameter("component.url")
private String componentUrl;
```

### Step 2: Add Dependencies

Inject required dependencies:

```java
@Autowired
public ComponentConfiguration(
        DynamicParameterAnalyzer dynamicParameterAnalyzer,
        ParameterUsageTracker parameterUsageTracker) {
    // Store dependencies
}
```

### Step 3: Replace Validation Logic

Replace manual validation with dynamic analyzer:

```java
// Before
if (componentUrl == null) throw new IllegalArgumentException("URL required");

// After
dynamicParameterAnalyzer.validateRequiredParameters(this.getClass());
```

### Step 4: Add Usage Tracking

Add tracking to all parameter access:

```java
public String getComponentUrl() {
    parameterUsageTracker.trackParameterAccess(this.getClass(), "component.url");
    return componentUrl;
}
```

## Quality Assurance

### Code Review Checklist

- [ ] All configuration parameters use @Parameter annotation
- [ ] @RequiredParameters annotation lists all required parameters
- [ ] DynamicParameterAnalyzer dependency is properly injected
- [ ] ParameterUsageTracker is integrated for all parameter access
- [ ] No hardcoded parameter validation logic exists
- [ ] All parameters have real usage in application logic
- [ ] Parameter naming follows project conventions
- [ ] JavaDoc documentation is complete

### Testing Requirements

- [ ] Unit tests verify parameter injection works correctly
- [ ] Integration tests verify dynamic validation functions
- [ ] Usage tracking tests verify parameter access is tracked
- [ ] Error handling tests verify analyzer error messages
- [ ] Performance tests verify tracking overhead is acceptable

## Related Standards

- [Dynamic Parameter Architecture Guide](./dynamic-parameter-architecture-guide.md)
- [Spring Bean Initialization Guide](./spring-bean-initialization-guide.md)
- [Parameter Management Pattern](../../architecture/patterns/parameter-management-pattern.md)
- [Configuration Class Template](../../templates/config-class-template.java)

## Enforcement

These standards are enforced through:

1. **Automated validation**: @DYNAMIC_PARAMETER_ARCHITECTURE_CHECK attention command
2. **Code review process**: Manual review using provided checklist
3. **CI/CD pipeline**: Automated compliance checking in build process
4. **Architecture governance**: Regular architecture compliance audits
