# 远程Docker测试时序优化指南

## 问题背景

在远程Docker环境中，容器启动后需要额外时间完成初始化，特别是数据库服务。立即尝试连接可能导致"Connection refused"错误。

## 优化策略

### 1. TestContainers等待策略

#### 基础等待策略
```java
@Component
public class PostgresTestContainer {
    
    private PostgreSQLContainer<?> container;
    
    public void startContainer() {
        container = new PostgreSQLContainer<>("postgres:17.4")
            .withDatabaseName("test")
            .withUsername("test")
            .withPassword("test")
            // 关键：添加等待策略
            .waitingFor(Wait.forLogMessage(".*database system is ready to accept connections.*", 2))
            .withStartupTimeout(Duration.ofMinutes(3)); // 远程环境需要更长时间
            
        container.start();
    }
}
```

#### 高级等待策略
```java
// 组合等待策略
.waitingFor(Wait.forAll(
    Wait.forLogMessage(".*database system is ready to accept connections.*", 2),
    Wait.forListeningPort(),
    Wait.forHealthcheck()
).withStartupTimeout(Duration.ofMinutes(3)))
```

### 2. 连接重试机制

#### 数据源连接重试
```java
public class PostgresTestContainer {
    
    private static final Logger log = LoggerFactory.getLogger(PostgresTestContainer.class);
    
    public DataSource getDataSource() {
        return getDataSourceWithRetry(5, 2000);
    }
    
    private DataSource getDataSourceWithRetry(int maxRetries, int retryDelayMs) {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                HikariConfig config = new HikariConfig();
                config.setJdbcUrl(container.getJdbcUrl());
                config.setUsername(container.getUsername());
                config.setPassword(container.getPassword());
                config.setDriverClassName(container.getDriverClassName());
                
                // 远程环境优化配置
                config.setConnectionTimeout(30000);        // 30秒连接超时
                config.setInitializationFailTimeout(60000); // 60秒初始化超时
                config.setValidationTimeout(5000);          // 5秒验证超时
                config.setMaximumPoolSize(5);               // 限制连接池大小
                
                HikariDataSource dataSource = new HikariDataSource(config);
                
                // 测试连接
                try (Connection conn = dataSource.getConnection()) {
                    log.info("数据库连接成功，尝试次数: {}", attempt);
                    return dataSource;
                }
                
            } catch (Exception e) {
                lastException = e;
                
                if (attempt < maxRetries) {
                    log.warn("数据库连接失败，{}秒后重试 ({}/{}): {}", 
                        retryDelayMs/1000, attempt, maxRetries, e.getMessage());
                    
                    try {
                        Thread.sleep(retryDelayMs);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("连接重试被中断", ie);
                    }
                } else {
                    log.error("数据库连接失败，已达到最大重试次数: {}", maxRetries);
                }
            }
        }
        
        throw new RuntimeException("无法建立数据库连接", lastException);
    }
}
```

### 3. 应用级重试配置

#### Spring Boot配置
```properties
# application-test.properties
# 数据库连接配置
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.initialization-fail-timeout=60000
spring.datasource.hikari.validation-timeout=5000
spring.datasource.hikari.leak-detection-threshold=60000
spring.datasource.hikari.maximum-pool-size=5

# JPA配置
spring.jpa.properties.hibernate.connection.provider_disables_autocommit=true
spring.jpa.properties.hibernate.connection.autocommit=false
```

#### 重试注解配置
```java
@Configuration
@EnableRetry
public class RetryConfig {
    
    @Bean
    public RetryTemplate retryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();
        
        FixedBackOffPolicy backOffPolicy = new FixedBackOffPolicy();
        backOffPolicy.setBackOffPeriod(2000); // 2秒间隔
        retryTemplate.setBackOffPolicy(backOffPolicy);
        
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(5);
        retryTemplate.setRetryPolicy(retryPolicy);
        
        return retryTemplate;
    }
}

// 使用重试注解
@Service
public class DatabaseService {
    
    @Retryable(value = {SQLException.class, DataAccessException.class}, 
               maxAttempts = 5, 
               backoff = @Backoff(delay = 2000))
    public void initializeDatabase() {
        // 数据库初始化逻辑
    }
}
```

### 4. 容器预热策略

#### 容器预热
```java
public class PostgresTestContainer {
    
    public void warmUpContainer() {
        // 启动后执行简单查询预热
        try (Connection conn = getDataSource().getConnection();
             Statement stmt = conn.createStatement()) {
            
            stmt.execute("SELECT 1");
            log.info("容器预热完成");
            
        } catch (SQLException e) {
            log.warn("容器预热失败: {}", e.getMessage());
        }
    }
}
```

### 5. 监控和诊断

#### 连接状态监控
```java
public class ConnectionMonitor {
    
    private static final Logger log = LoggerFactory.getLogger(ConnectionMonitor.class);
    
    public void monitorConnection(DataSource dataSource) {
        try (Connection conn = dataSource.getConnection()) {
            DatabaseMetaData metaData = conn.getMetaData();
            
            log.info("数据库连接信息:");
            log.info("- URL: {}", metaData.getURL());
            log.info("- 驱动: {}", metaData.getDriverName());
            log.info("- 版本: {}", metaData.getDatabaseProductVersion());
            log.info("- 连接有效: {}", conn.isValid(5));
            
        } catch (SQLException e) {
            log.error("连接监控失败: {}", e.getMessage());
        }
    }
}
```

## 最佳实践总结

### 1. 远程环境配置
- 增加启动超时时间（3分钟）
- 使用组合等待策略
- 配置合理的连接池参数

### 2. 重试机制
- 实现指数退避重试
- 设置最大重试次数（5次）
- 记录详细的重试日志

### 3. 监控诊断
- 添加连接状态监控
- 记录容器启动时间
- 监控连接池状态

### 4. 性能优化
- 限制连接池大小
- 使用连接验证
- 实现容器预热

这些优化措施可以显著提高远程Docker测试的稳定性和可靠性。
