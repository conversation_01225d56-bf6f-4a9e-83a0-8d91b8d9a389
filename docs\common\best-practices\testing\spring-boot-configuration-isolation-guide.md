# Spring Boot测试配置隔离标准指南

## 概述

本指南提供Spring Boot测试中配置冲突问题的根本性解决方案，基于Spring Boot官方推荐的测试配置隔离最佳实践。

## 问题描述

### 核心问题
Spring Boot测试中主应用类（@SpringBootApplication）被自动扫描，导致生产配置与测试配置冲突：

- **多个@Primary Bean冲突**：生产DataSource与测试DataSource冲突
- **外部依赖连接错误**：gRPC、Redis等真实连接导致测试失败
- **配置类加载冲突**：PostgreSQLConfig等生产配置被意外加载

### 典型错误信息
```
No qualifying bean of type 'javax.sql.DataSource' available: 
more than one 'primary' bean found among candidates: [dataSource, testDataSource]
```

## 根本性解决方案

### 1. 专用TestApplication类

创建完全独立的测试启动类，替代主应用类：

```java
@SpringBootConfiguration
@EnableAutoConfiguration(exclude = {
    DataSourceAutoConfiguration.class,
    HibernateJpaAutoConfiguration.class,
    RedisAutoConfiguration.class
})
@ComponentScan(
    basePackages = {"org.xkong.cloud.business.internal.core.config"},
    includeFilters = {
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, 
                            classes = {IsolatedTestConfiguration.class})
    },
    excludeFilters = {
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {
            XkongcloudBusinessInternalCoreApplication.class,
            PostgreSQLConfig.class,
            UidGeneratorConfig.class,
            GrpcClientConfig.class,
            MiddlewareConfig.class,
            TestGrpcConfiguration.class,
            TestMockConfiguration.class
        })
    }
)
public class TestApplication {
    // 空的测试启动类，仅用于配置Spring Boot测试环境
}
```

### 2. 完全独立的测试配置类

```java
@TestConfiguration
public class IsolatedTestConfiguration {

    // Mock外部依赖
    @MockBean
    private KVServiceGrpc.KVServiceBlockingStub kvServiceBlockingStub;
    
    @MockBean
    private KVServiceGrpc.KVServiceStub kvServiceStub;
    
    @MockBean
    private RedisConnectionFactory redisConnectionFactory;

    // 测试专用DataSource配置
    @Bean
    @Primary
    public DataSource testDataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;MODE=PostgreSQL");
        config.setUsername("sa");
        config.setPassword("");
        config.setDriverClassName("org.h2.Driver");
        return new HikariDataSource(config);
    }

    // JPA配置
    @Bean
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(DataSource dataSource) {
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource);
        em.setPackagesToScan("org.xkong.cloud.business.internal.core");
        
        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);
        
        Properties properties = new Properties();
        properties.setProperty("hibernate.hbm2ddl.auto", "create-drop");
        properties.setProperty("hibernate.dialect", "org.hibernate.dialect.H2Dialect");
        em.setJpaProperties(properties);
        return em;
    }

    // 事务管理器
    @Bean
    public PlatformTransactionManager transactionManager(
            LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(entityManagerFactory.getObject());
        return transactionManager;
    }

    // Mock参数服务
    @Bean
    @Primary
    public KVParamService testKVParamService() {
        KVParamService mockService = Mockito.mock(KVParamService.class);
        // 配置Mock返回值
        Map<String, String> params = createTestParameters();
        params.forEach((key, value) -> {
            Mockito.when(mockService.getParam(key)).thenReturn(value);
        });
        return mockService;
    }
}
```

### 3. 测试类配置

```java
@SpringBootTest(
    classes = {TestApplication.class},
    webEnvironment = SpringBootTest.WebEnvironment.NONE,
    properties = {"spring.main.allow-bean-definition-overriding=true"}
)
@ActiveProfiles("test")
public class IsolatedConfigurationTest {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private KVParamService kvParamService;

    @Test
    @Transactional
    public void testH2PostgreSQLCompatibility() {
        // 测试H2 PostgreSQL兼容模式
        String result = jdbcTemplate.queryForObject("SELECT 1", String.class);
        assertEquals("1", result);
        
        // 创建表和插入数据
        jdbcTemplate.execute("CREATE TABLE test_table (id BIGINT PRIMARY KEY, name VARCHAR(100))");
        int rowsAffected = jdbcTemplate.update("INSERT INTO test_table (id, name) VALUES (?, ?)", 1L, "test_name");
        assertEquals(1, rowsAffected);
        
        // 验证数据
        Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM test_table", Integer.class);
        assertEquals(1, count.intValue());
    }
}
```

## 核心技术要点

### 1. 配置隔离策略
- **专用TestApplication**：完全替代主应用类
- **精确excludeFilters**：排除所有生产配置类
- **独立测试配置**：提供完整的测试基础设施

### 2. 数据库配置
- **H2 PostgreSQL兼容模式**：`MODE=PostgreSQL`
- **内存数据库**：`DB_CLOSE_DELAY=-1`确保测试期间数据保持
- **事务支持**：使用`@Transactional`确保数据一致性

### 3. Mock策略
- **外部依赖Mock**：gRPC、Redis等所有外部服务
- **参数服务Mock**：提供完整的测试参数映射
- **Bean覆盖**：使用`@Primary`覆盖生产Bean

## 验证标准

成功的配置隔离应该通过以下验证：

1. ✅ **DataSource配置正确**：无Bean冲突
2. ✅ **Mock服务正常工作**：无真实连接尝试
3. ✅ **H2 PostgreSQL兼容模式正常工作**：数据库操作正常
4. ✅ **JPA配置基础验证通过**：事务管理正常
5. ✅ **测试环境完全隔离**：无配置冲突
6. ✅ **无外部依赖**：所有外部服务被正确Mock

## 常见问题解决

### 1. 多个@Primary DataSource Bean冲突
**解决方案**：使用excludeFilters排除生产DataSource配置

### 2. H2 SQL语法错误
**解决方案**：使用H2兼容的SQL语法，避免`AUTO_INCREMENT`等不支持的语法

### 3. 事务数据不可见
**解决方案**：在测试方法上添加`@Transactional`注解

### 4. 主应用类自动发现
**解决方案**：使用专用TestApplication + excludeFilters

## 最佳实践总结

1. **根本性解决**：使用专用TestApplication类完全隔离配置
2. **标准化架构**：符合Spring Boot官方测试规范
3. **完全Mock**：避免所有真实外部连接
4. **事务管理**：确保测试数据一致性
5. **可复用模式**：其他测试可基于此成功模式

## 参考资料

- [Spring Boot官方文档 - Testing Spring Boot Applications](https://docs.spring.io/spring-boot/reference/testing/spring-boot-applications.html)
- [Spring Boot测试配置排除指南](https://docs.spring.io/spring-boot/reference/testing/spring-boot-applications.html#testing.spring-boot-applications.excluding-configuration)

---

**创建时间**: 2025-01-16  
**解决方案**: 根本性解决Spring Boot测试配置冲突  
**技术架构**: 专用TestApplication + IsolatedTestConfiguration  
**验证状态**: 100%测试通过 ✅
