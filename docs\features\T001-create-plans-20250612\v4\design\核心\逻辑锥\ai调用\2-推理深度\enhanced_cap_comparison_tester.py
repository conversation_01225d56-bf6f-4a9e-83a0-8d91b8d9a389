#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版CAP方法对比测试器
解决推理深度评估问题并添加第三种算法极限方案

测试目标：
1. 方案A：内容嵌入式CAP（两次AI调用）
2. 方案B：外部头部式CAP（头部规则嵌入）
3. 方案C：语义分析增强CAP（基于V3语义分析算法）
4. 中英文双语评估，解决推理深度评估问题

作者：AI助手
日期：2025-01-10
"""

import json
import re
import time
import urllib.request
import urllib.parse
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional

# ==================== 配置信息 ====================
API_CONFIG = {
    "url": "https://api.gmi-serving.com/v1/chat/completions",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjA4ZDMxOTBjLWNjNzItNDc4ZS1hOGYwLTY3NmEwMGY1MDY2ZCIsInR5cGUiOiJpZV9tb2RlbCJ9.q7zRm2BAySo2zOi2QL5m6ukgmbhMZm2Ig4ITmJ3ZsM8",
    "models": [
        "deepseek-ai/DeepSeek-V3-0324",
        "deepseek-ai/DeepSeek-R1-0528"
    ]
}

# ==================== 实际任务测试集 ====================
REAL_WORLD_TASKS = [
    {
        "id": "code_review",
        "name": "代码审查任务",
        "base_task": "请审查以下Python代码的质量，找出潜在问题并提供改进建议",
        "context": """
def process_user_data(data):
    result = []
    for item in data:
        if item['status'] == 'active':
            result.append(item['name'].upper())
    return result
""",
        "expected_aspects": ["代码质量", "性能优化", "错误处理", "可读性"]
    },
    {
        "id": "system_design",
        "name": "系统设计任务", 
        "base_task": "设计一个支持10万并发用户的在线聊天系统架构",
        "context": "需要考虑实时性、可扩展性、数据一致性和成本控制",
        "expected_aspects": ["架构设计", "技术选型", "扩展性", "成本分析"]
    },
    {
        "id": "problem_solving",
        "name": "问题解决任务",
        "base_task": "分析并解决数据库查询性能问题",
        "context": "用户反馈系统响应缓慢，初步排查发现某些查询耗时超过5秒",
        "expected_aspects": ["问题诊断", "解决方案", "优化策略", "预防措施"]
    }
]

# ==================== 结构化提示词工程模板 ====================
class StructuredPromptTemplate:
    """结构化提示词工程模板生成器"""
    
    @staticmethod
    def generate_structured_prompt(task_description: str, context: str = "", 
                                 role: str = "专业顾问", 
                                 expected_aspects: List[str] = None,
                                 language: str = "chinese") -> str:
        """生成结构化提示词"""
        
        if language == "english":
            return StructuredPromptTemplate._generate_english_prompt(
                task_description, context, role, expected_aspects
            )
        else:
            return StructuredPromptTemplate._generate_chinese_prompt(
                task_description, context, role, expected_aspects
            )
    
    @staticmethod
    def _generate_chinese_prompt(task_description: str, context: str = "", 
                               role: str = "专业顾问", 
                               expected_aspects: List[str] = None) -> str:
        """生成中文结构化提示词"""
        
        instructions = [
            "深入理解任务的核心要求和约束条件",
            "运用专业知识和最佳实践进行分析",
            "提供具体、可操作的建议和解决方案",
            "考虑多个角度和潜在的风险因素",
            "确保回答的逻辑性和结构化",
            "使用清晰、专业的语言表达",
            "提供具体的示例或案例支持",
            "考虑实际实施的可行性",
            "评估解决方案的优缺点",
            "提供量化的评估指标",
            "考虑长期影响和可持续性",
            "确保方案的完整性和系统性",
            "提供备选方案和应急预案",
            "考虑成本效益和资源配置",
            "确保符合行业标准和最佳实践"
        ]
        
        format_template = """
**分析概述**
[插入问题核心分析]

**详细方案**
[插入具体解决方案]

**实施建议**
[插入实施步骤和注意事项]

**风险评估**
[插入潜在风险和缓解措施]

**总结建议**
[插入最终建议和关键要点]
"""
        
        example = f"""
原始任务示例：优化网站加载速度
回答示例：
**分析概述**
网站加载速度问题主要涉及前端资源优化、服务器性能和网络传输三个层面...

**详细方案**
1. 前端优化：压缩CSS/JS文件，优化图片格式...
2. 服务器优化：启用缓存机制，优化数据库查询...
"""
        
        structured_prompt = f"""===
Role: {role}

===
Task: {task_description}

===
Context: {context}

===
Instructions:
{chr(10).join([f"{i+1}. {instruction}" for i, instruction in enumerate(instructions)])}

===
Format: {format_template}

===
Example: {example}

===
What's Next:
请按照上述结构化要求完成任务分析，确保回答全面、专业、可操作。
"""
        
        return structured_prompt
    
    @staticmethod
    def _generate_english_prompt(task_description: str, context: str = "", 
                               role: str = "Professional Consultant", 
                               expected_aspects: List[str] = None) -> str:
        """生成英文结构化提示词"""
        
        instructions = [
            "Deeply understand the core requirements and constraints of the task",
            "Apply professional knowledge and best practices in your analysis",
            "Provide specific, actionable recommendations and solutions",
            "Consider multiple perspectives and potential risk factors",
            "Ensure logical structure and organization in your response",
            "Use clear, professional language",
            "Provide concrete examples or case studies for support",
            "Consider practical implementation feasibility",
            "Evaluate the advantages and disadvantages of solutions",
            "Provide quantitative assessment metrics",
            "Consider long-term impacts and sustainability",
            "Ensure completeness and systematic approach in your solution",
            "Provide alternative solutions and contingency plans",
            "Consider cost-effectiveness and resource allocation",
            "Ensure compliance with industry standards and best practices"
        ]
        
        format_template = """
**Analysis Overview**
[Insert core problem analysis]

**Detailed Solution**
[Insert specific solution details]

**Implementation Recommendations**
[Insert implementation steps and considerations]

**Risk Assessment**
[Insert potential risks and mitigation measures]

**Summary Recommendations**
[Insert final recommendations and key points]
"""
        
        example = f"""
Original task example: Optimize website loading speed
Response example:
**Analysis Overview**
Website loading speed issues primarily involve frontend resource optimization, server performance, and network transmission across three layers...

**Detailed Solution**
1. Frontend optimization: Compress CSS/JS files, optimize image formats...
2. Server optimization: Enable caching mechanisms, optimize database queries...
"""
        
        structured_prompt = f"""===
Role: {role}

===
Task: {task_description}

===
Context: {context}

===
Instructions:
{chr(10).join([f"{i+1}. {instruction}" for i, instruction in enumerate(instructions)])}

===
Format: {format_template}

===
Example: {example}

===
What's Next:
Please complete the task analysis according to the structured requirements above, ensuring your response is comprehensive, professional, and actionable.
"""
        
        return structured_prompt

# ==================== 双语言LogicDepthDetector ====================
class EnhancedLogicDepthDetector:
    """增强版逻辑深度检测器 - 支持中英文双语评估"""
    
    def __init__(self):
        self.weights = {
            "reasoning_depth": 0.35,
            "logical_structure": 0.25, 
            "concept_complexity": 0.20,
            "practical_value": 0.20
        }
    
    def detect_logic_depth(self, content: str) -> Dict[str, Any]:
        """检测内容的逻辑深度"""
        
        # 检测内容语言
        is_english = self._detect_language(content)
        
        # 根据语言选择分析方法
        if is_english:
            reasoning_result = self._analyze_reasoning_depth_english(content)
            structure_result = self._analyze_logical_structure_english(content)
            complexity_result = self._analyze_concept_complexity_english(content)
            practical_result = self._analyze_practical_value_english(content)
        else:
            reasoning_result = self._analyze_reasoning_depth_chinese(content)
            structure_result = self._analyze_logical_structure_chinese(content)
            complexity_result = self._analyze_concept_complexity_chinese(content)
            practical_result = self._analyze_practical_value_chinese(content)
        
        overall_score = (
            reasoning_result["score"] * self.weights["reasoning_depth"] +
            structure_result["score"] * self.weights["logical_structure"] +
            complexity_result["score"] * self.weights["concept_complexity"] +
            practical_result["score"] * self.weights["practical_value"]
        )
        
        return {
            "overall_score": overall_score,
            "dimension_scores": {
                "reasoning_depth": reasoning_result["score"],
                "logical_structure": structure_result["score"],
                "concept_complexity": complexity_result["score"],
                "practical_value": practical_result["score"]
            },
            "quality_grade": self._calculate_quality_grade(overall_score),
            "language": "english" if is_english else "chinese",
            "detailed_analysis": {
                "reasoning": reasoning_result,
                "structure": structure_result,
                "complexity": complexity_result,
                "practical": practical_result
            }
        }
    
    def _detect_language(self, content: str) -> bool:
        """检测内容语言是否为英文"""
        # 简单检测：如果英文单词比例超过50%，则认为是英文
        english_words = re.findall(r'\b[a-zA-Z]{2,}\b', content)
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', content)
        
        return len(english_words) > len(chinese_chars)
    
    def _analyze_reasoning_depth_chinese(self, content: str) -> Dict[str, Any]:
        """分析中文推理深度"""
        depth_patterns = {
            "因果推理": [r'因为.*所以', r'由于.*导致', r'基于.*可以'],
            "层次分析": [r'首先.*其次.*最后', r'第一.*第二.*第三'],
            "对比论证": [r'相比.*而言', r'与.*不同', r'优于.*在于'],
            "假设验证": [r'假设.*那么', r'如果.*则'],
            "归纳演绎": [r'综上所述', r'总结.*规律', r'可以得出']
        }
        
        return self._analyze_reasoning_patterns(content, depth_patterns)
    
    def _analyze_reasoning_depth_english(self, content: str) -> Dict[str, Any]:
        """分析英文推理深度"""
        depth_patterns = {
            "causal_reasoning": [r'because.*therefore', r'since.*leads to', r'due to.*results in'],
            "hierarchical_analysis": [r'first.*second.*finally', r'initially.*subsequently.*lastly'],
            "comparative_reasoning": [r'compared to', r'in contrast to', r'differs from'],
            "hypothetical_reasoning": [r'if.*then', r'assuming.*would', r'suppose.*could'],
            "inductive_deductive": [r'in conclusion', r'to summarize', r'we can infer']
        }
        
        return self._analyze_reasoning_patterns(content, depth_patterns)
    
    def _analyze_reasoning_patterns(self, content: str, patterns: Dict[str, List[str]]) -> Dict[str, Any]:
        """分析推理模式"""
        detected_patterns = []
        total_score = 0
        
        for category, pattern_list in patterns.items():
            count = sum(1 for pattern in pattern_list if re.search(pattern, content, re.IGNORECASE))
            if count > 0:
                detected_patterns.append(f"{category}: {count}")
                total_score += count * 12
        
        # 分析推理链长度
        reasoning_chain_length = 0
        for category, pattern_list in patterns.items():
            for pattern in pattern_list:
                reasoning_chain_length += len(re.findall(pattern, content, re.IGNORECASE))
        
        chain_score = min(reasoning_chain_length * 8, 40)
        total_score += chain_score
        
        final_score = min(total_score, 100)
        
        return {
            "score": final_score,
            "patterns_detected": detected_patterns,
            "reasoning_chain_length": reasoning_chain_length,
            "analysis": f"检测到{len(detected_patterns)}类推理模式，推理链长度{reasoning_chain_length}"
        }
    
    def _analyze_logical_structure_chinese(self, content: str) -> Dict[str, Any]:
        """分析中文逻辑结构"""
        structure_indicators = {
            "结构化标记": [r'\d+\.', r'[一二三四五六七八九十]+、', r'[ABCDEFG]\.', r'##', r'###'],
            "逻辑连接词": [r'然而', r'但是', r'因此', r'所以', r'另外', r'此外', r'同时'],
            "论证结构": [r'分析.*', r'方案.*', r'建议.*'],
            "层次递进": [r'进一步', r'更深层次', r'深入分析', r'具体而言']
        }
        
        return self._analyze_structure_indicators(content, structure_indicators)
    
    def _analyze_logical_structure_english(self, content: str) -> Dict[str, Any]:
        """分析英文逻辑结构"""
        structure_indicators = {
            "structured_markers": [r'\d+\.', r'[ABCDEFG]\.', r'##', r'###'],
            "logical_connectors": [r'however', r'therefore', r'thus', r'furthermore', r'moreover', r'in addition'],
            "argument_structure": [r'analysis', r'solution', r'recommendation'],
            "hierarchical_progression": [r'furthermore', r'in more detail', r'specifically']
        }
        
        return self._analyze_structure_indicators(content, structure_indicators)
    
    def _analyze_structure_indicators(self, content: str, indicators: Dict[str, List[str]]) -> Dict[str, Any]:
        """分析结构指标"""
        structure_score = 0
        detected_structures = []
        
        for category, patterns in indicators.items():
            count = sum(1 for pattern in patterns if re.search(pattern, content, re.IGNORECASE))
            if count > 0:
                detected_structures.append(f"{category}: {count}个")
                structure_score += count * 10
        
        paragraphs = content.split('\n\n')
        paragraph_score = min(len(paragraphs) * 5, 30)
        structure_score += paragraph_score
        
        final_score = min(structure_score, 100)
        
        return {
            "score": final_score,
            "structures_detected": detected_structures,
            "paragraph_count": len(paragraphs)
        }
    
    def _analyze_concept_complexity_chinese(self, content: str) -> Dict[str, Any]:
        """分析中文概念复杂度"""
        complexity_indicators = {
            "技术概念": [r'架构', r'算法', r'协议', r'框架', r'模式', r'机制'],
            "抽象概念": [r'原理', r'本质', r'规律', r'模型', r'理论', r'方法论'],
            "系统概念": [r'系统', r'平台', r'生态', r'环境', r'基础设施'],
            "实践概念": [r'实施', r'部署', r'优化', r'监控', r'维护']
        }
        
        return self._analyze_complexity_indicators(content, complexity_indicators)
    
    def _analyze_concept_complexity_english(self, content: str) -> Dict[str, Any]:
        """分析英文概念复杂度"""
        complexity_indicators = {
            "technical_concepts": [r'architecture', r'algorithm', r'protocol', r'framework', r'pattern', r'mechanism'],
            "abstract_concepts": [r'principle', r'essence', r'theory', r'model', r'methodology'],
            "system_concepts": [r'system', r'platform', r'ecosystem', r'environment', r'infrastructure'],
            "practical_concepts": [r'implementation', r'deployment', r'optimization', r'monitoring', r'maintenance']
        }
        
        return self._analyze_complexity_indicators(content, complexity_indicators)
    
    def _analyze_complexity_indicators(self, content: str, indicators: Dict[str, List[str]]) -> Dict[str, Any]:
        """分析复杂度指标"""
        concept_score = 0
        detected_concepts = []
        
        for category, patterns in indicators.items():
            count = sum(1 for pattern in patterns if re.search(pattern, content, re.IGNORECASE))
            if count > 0:
                detected_concepts.append(f"{category}: {count}个")
                concept_score += count * 8
        
        content_length = len(content)
        term_density = sum(len(concepts) for concepts in detected_concepts) / max(content_length, 1) * 1000
        density_score = min(term_density * 20, 40)
        concept_score += density_score
        
        final_score = min(concept_score, 100)
        
        return {
            "score": final_score,
            "concepts_detected": detected_concepts,
            "term_density": term_density
        }
    
    def _analyze_practical_value_chinese(self, content: str) -> Dict[str, Any]:
        """分析中文实用价值"""
        practical_patterns = {
            "具体建议": [r'建议.*', r'推荐.*', r'应该.*', r'可以.*'],
            "实施步骤": [r'步骤.*', r'流程.*', r'过程.*', r'阶段.*'],
            "量化指标": [r'\d+%', r'\d+倍', r'\d+秒', r'\d+个'],
            "风险控制": [r'风险.*', r'注意.*', r'避免.*', r'防止.*']
        }
        
        return self._analyze_practical_patterns(content, practical_patterns)
    
    def _analyze_practical_value_english(self, content: str) -> Dict[str, Any]:
        """分析英文实用价值"""
        practical_patterns = {
            "specific_recommendations": [r'recommend', r'suggest', r'should', r'can'],
            "implementation_steps": [r'step', r'process', r'procedure', r'phase'],
            "quantitative_metrics": [r'\d+%', r'\d+ times', r'\d+ seconds', r'\d+ items'],
            "risk_control": [r'risk', r'caution', r'avoid', r'prevent']
        }
        
        return self._analyze_practical_patterns(content, practical_patterns)
    
    def _analyze_practical_patterns(self, content: str, patterns: Dict[str, List[str]]) -> Dict[str, Any]:
        """分析实用模式"""
        practical_score = 0
        detected_practical = []
        
        for category, pattern_list in patterns.items():
            count = sum(1 for pattern in pattern_list if re.search(pattern, content, re.IGNORECASE))
            if count > 0:
                detected_practical.append(f"{category}: {count}个")
                practical_score += count * 12
        
        # 检查是否包含具体示例
        example_indicators = ['例如', '比如', '举例', '案例', 'example', 'instance', 'case study', 'for instance']
        example_count = sum(1 for indicator in example_indicators if indicator.lower() in content.lower())
        practical_score += example_count * 15
        
        final_score = min(practical_score, 100)
        
        return {
            "score": final_score,
            "practical_elements": detected_practical,
            "example_count": example_count
        }
    
    def _calculate_quality_grade(self, score: float) -> str:
        """计算质量等级"""
        if score >= 90:
            return "A+ (卓越)"
        elif score >= 80:
            return "A (优秀)"
        elif score >= 70:
            return "B (良好)"
        elif score >= 60:
            return "C (及格)"
        else:
            return "D (需改进)"

# ==================== V3语义分析引擎 ====================
class SemanticAnalysisEngine:
    """V3语义分析引擎 - 模拟tools/doc/design/v3的语义分析算法"""

    def __init__(self):
        """初始化语义分析引擎"""
        self.architecture_patterns = {
            "microservices": [
                "微服务", "服务拆分", "服务发现", "服务注册", "API网关",
                "microservices", "service discovery", "service registry", "API gateway"
            ],
            "event_driven": [
                "事件驱动", "消息队列", "发布订阅", "异步处理", "事件总线",
                "event-driven", "message queue", "pub/sub", "asynchronous", "event bus"
            ],
            "layered_architecture": [
                "分层架构", "表示层", "业务层", "数据层", "持久层",
                "layered architecture", "presentation layer", "business layer", "data layer"
            ],
            "domain_driven": [
                "领域驱动", "领域模型", "限界上下文", "聚合根", "实体",
                "domain-driven", "domain model", "bounded context", "aggregate root"
            ]
        }

        self.design_patterns = {
            "singleton": [
                "单例模式", "全局唯一", "私有构造函数", "静态实例",
                "singleton pattern", "globally unique", "private constructor", "static instance"
            ],
            "factory": [
                "工厂模式", "对象创建", "实例化", "抽象工厂",
                "factory pattern", "object creation", "instantiation", "abstract factory"
            ],
            "observer": [
                "观察者模式", "发布订阅", "事件监听", "通知机制",
                "observer pattern", "publish-subscribe", "event listener", "notification"
            ],
            "strategy": [
                "策略模式", "算法族", "行为封装", "动态切换",
                "strategy pattern", "algorithm family", "behavior encapsulation"
            ]
        }

        self.cognitive_patterns = {
            "clarity": [
                "清晰", "明确", "易懂", "直观", "简洁",
                "clear", "explicit", "understandable", "intuitive", "concise"
            ],
            "consistency": [
                "一致", "统一", "规范", "标准", "协调",
                "consistent", "uniform", "standardized", "coherent"
            ],
            "completeness": [
                "完整", "全面", "详尽", "无遗漏", "周全",
                "complete", "comprehensive", "thorough", "exhaustive"
            ],
            "correctness": [
                "正确", "准确", "精确", "无误", "可靠",
                "correct", "accurate", "precise", "reliable"
            ]
        }

    def analyze_content(self, content: str) -> Dict[str, Any]:
        """分析内容的语义特征"""
        result = {
            "architecture_patterns": self._analyze_architecture_patterns(content),
            "design_patterns": self._analyze_design_patterns(content),
            "cognitive_friendliness": self._analyze_cognitive_friendliness(content),
            "semantic_completeness": 0
        }

        # 计算语义完整性得分
        result["semantic_completeness"] = self._calculate_semantic_completeness(result)

        return result

    def _analyze_architecture_patterns(self, content: str) -> Dict[str, Any]:
        """分析架构模式"""
        patterns_detected = {}

        for pattern_name, keywords in self.architecture_patterns.items():
            matches = sum(1 for keyword in keywords if keyword.lower() in content.lower())
            if matches > 0:
                completeness = min(matches * 20, 100)
                patterns_detected[pattern_name] = {
                    "matches": matches,
                    "completeness_score": completeness,
                    "keywords_matched": [k for k in keywords if k.lower() in content.lower()]
                }

        return patterns_detected

    def _analyze_design_patterns(self, content: str) -> Dict[str, Any]:
        """分析设计模式"""
        patterns_detected = {}

        for pattern_name, keywords in self.design_patterns.items():
            matches = sum(1 for keyword in keywords if keyword.lower() in content.lower())
            if matches > 0:
                quality = min(matches * 25, 100)
                patterns_detected[pattern_name] = {
                    "matches": matches,
                    "quality_score": quality,
                    "keywords_matched": [k for k in keywords if k.lower() in content.lower()]
                }

        return patterns_detected

    def _analyze_cognitive_friendliness(self, content: str) -> Dict[str, Any]:
        """分析认知友好性"""
        cognitive_scores = {}

        for aspect, keywords in self.cognitive_patterns.items():
            matches = sum(1 for keyword in keywords if keyword.lower() in content.lower())
            score = min(matches * 20, 100)
            cognitive_scores[aspect] = {
                "score": score,
                "keywords_matched": [k for k in keywords if k.lower() in content.lower()]
            }

        # 计算总体认知友好性得分
        if cognitive_scores:
            total_score = sum(aspect["score"] for aspect in cognitive_scores.values())
            cognitive_scores["overall"] = total_score / len(cognitive_scores)
        else:
            cognitive_scores["overall"] = 0

        return cognitive_scores

    def _calculate_semantic_completeness(self, analysis_result: Dict[str, Any]) -> float:
        """计算语义完整性得分"""
        # 架构模式得分
        arch_patterns = analysis_result["architecture_patterns"]
        arch_score = sum(pattern["completeness_score"] for pattern in arch_patterns.values()) if arch_patterns else 0
        if arch_patterns:
            arch_score /= len(arch_patterns)

        # 设计模式得分
        design_patterns = analysis_result["design_patterns"]
        design_score = sum(pattern["quality_score"] for pattern in design_patterns.values()) if design_patterns else 0
        if design_patterns:
            design_score /= len(design_patterns)

        # 认知友好性得分
        cognitive_score = analysis_result["cognitive_friendliness"].get("overall", 0)

        # 综合得分 (架构40% + 设计30% + 认知30%)
        weights = {"arch": 0.4, "design": 0.3, "cognitive": 0.3}
        semantic_completeness = (
            arch_score * weights["arch"] +
            design_score * weights["design"] +
            cognitive_score * weights["cognitive"]
        )

        return semantic_completeness

    def generate_semantic_enhanced_cap(self, base_prompt: str, content_to_analyze: str = None) -> str:
        """生成语义增强的CAP提示词"""
        # 如果没有提供分析内容，则使用基础提示词
        if not content_to_analyze:
            content_to_analyze = base_prompt

        # 分析内容
        semantic_analysis = self.analyze_content(content_to_analyze)

        # 提取关键架构和设计模式
        arch_patterns = list(semantic_analysis["architecture_patterns"].keys())
        design_patterns = list(semantic_analysis["design_patterns"].keys())

        # 生成语义增强CAP
        semantic_cap = f"""
<SEMANTIC_ENHANCED_CAP>
请在回答以下问题时，应用V3语义分析引擎识别的关键模式和概念：

**架构思维框架**：
{self._generate_architecture_guidance(arch_patterns)}

**设计模式应用**：
{self._generate_design_pattern_guidance(design_patterns)}

**认知优化指南**：
- 保持概念清晰度和一致性
- 使用系统化的分析方法
- 确保逻辑推理的完整性
- 提供具体的实施细节和示例

请基于以上语义增强框架，以最高专业水准回答以下问题。
</SEMANTIC_ENHANCED_CAP>

{base_prompt}
"""

        return semantic_cap

    def _generate_architecture_guidance(self, patterns: List[str]) -> str:
        """生成架构指导"""
        if not patterns:
            return "- 采用系统化的架构思维，考虑组件间的交互和依赖关系\n- 关注系统的可扩展性、可维护性和性能特性"

        guidance = ""

        if "microservices" in patterns:
            guidance += "- 应用微服务架构思维，关注服务边界、独立部署和弹性扩展\n"

        if "event_driven" in patterns:
            guidance += "- 运用事件驱动架构原则，考虑异步通信、解耦和响应性\n"

        if "layered_architecture" in patterns:
            guidance += "- 采用分层架构方法，确保关注点分离和责任边界清晰\n"

        if "domain_driven" in patterns:
            guidance += "- 使用领域驱动设计思想，关注业务领域模型和限界上下文\n"

        if not guidance:
            guidance = "- 根据问题特性选择合适的架构模式，确保系统结构清晰\n- 考虑系统的质量属性，如性能、可靠性和安全性"

        return guidance

    def _generate_design_pattern_guidance(self, patterns: List[str]) -> str:
        """生成设计模式指导"""
        if not patterns:
            return "- 识别并应用适当的设计模式解决常见问题\n- 避免过度设计，保持解决方案的简洁性"

        guidance = ""

        if "singleton" in patterns:
            guidance += "- 考虑单例模式确保全局唯一性，但注意避免过度使用导致的耦合\n"

        if "factory" in patterns:
            guidance += "- 应用工厂模式处理对象创建逻辑，提高代码灵活性\n"

        if "observer" in patterns:
            guidance += "- 利用观察者模式实现事件通知机制，保持组件间松耦合\n"

        if "strategy" in patterns:
            guidance += "- 使用策略模式封装算法变化，支持运行时行为切换\n"

        if not guidance:
            guidance = "- 选择合适的设计模式解决特定问题，提高代码质量\n- 平衡设计模式使用与代码简洁性"

        return guidance

# ==================== API客户端 ====================
class SimpleAPIClient:
    """简单API客户端"""

    def __init__(self):
        self.api_url = API_CONFIG["url"]
        self.api_token = API_CONFIG["token"]
        self.models = API_CONFIG["models"]

    def call_api(self, model: str, prompt: str, max_retries: int = 2) -> Dict[str, Any]:
        """调用API获取响应"""

        for attempt in range(max_retries + 1):
            if attempt > 0:
                print(f"🔄 第{attempt + 1}次尝试调用API...")
                time.sleep(3)

            try:
                data = {
                    "model": model,
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0.7,
                    "max_tokens": 4000
                }

                json_data = json.dumps(data).encode('utf-8')

                req = urllib.request.Request(
                    self.api_url,
                    data=json_data,
                    headers={
                        'Authorization': f'Bearer {self.api_token}',
                        'Content-Type': 'application/json',
                        'User-Agent': 'Enhanced-CAP-Comparison-Tester/1.0'
                    }
                )

                timeout = 300 if "R1" in model else 120
                print(f"⏱️ 调用{model}，超时时间: {timeout}秒")

                with urllib.request.urlopen(req, timeout=timeout) as response:
                    response_data = response.read().decode('utf-8')

                    if response.status == 200:
                        result = json.loads(response_data)

                        if "choices" in result and len(result["choices"]) > 0:
                            message = result["choices"][0]["message"]
                            content = message.get("content", "")
                            reasoning = message.get("reasoning_content") or ""

                            print(f"✅ API调用成功")
                            print(f"📝 响应内容长度: {len(content)} 字符")
                            if reasoning:
                                print(f"🧠 推理内容长度: {len(reasoning)} 字符")

                            return {
                                "success": True,
                                "content": content,
                                "reasoning_content": reasoning,
                                "model": model,
                                "timestamp": datetime.now().isoformat(),
                                "token_usage": result.get("usage", {})
                            }
                        else:
                            return {
                                "success": False,
                                "error": "响应格式不正确",
                                "model": model
                            }
                    else:
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}",
                            "model": model
                        }

            except Exception as e:
                print(f"❌ 调用失败 (第{attempt + 1}次尝试): {str(e)}")
                if attempt < max_retries:
                    continue
                else:
                    return {
                        "success": False,
                        "error": f"所有{max_retries + 1}次尝试都失败: {str(e)}",
                        "model": model
                    }

        return {
            "success": False,
            "error": f"API调用失败，已重试{max_retries + 1}次",
            "model": model
        }

# ==================== 增强版CAP方法测试器 ====================
class EnhancedCAPApproachTester:
    """增强版CAP方法对比测试器"""

    def __init__(self):
        self.api_client = SimpleAPIClient()
        self.logic_detector = EnhancedLogicDepthDetector()
        self.prompt_template = StructuredPromptTemplate()
        self.semantic_engine = SemanticAnalysisEngine()

    def test_approach_a_embedded_cap(self, task: Dict, model: str, language: str = "chinese") -> Dict[str, Any]:
        """方案A：内容嵌入式CAP（两次AI调用）"""
        print(f"🔄 测试方案A - 内容嵌入式CAP ({language})")

        # 第一步：生成结构化提示词
        base_structured_prompt = self.prompt_template.generate_structured_prompt(
            task["base_task"],
            task["context"],
            "专业技术顾问" if language == "chinese" else "Professional Technical Consultant",
            task["expected_aspects"],
            language
        )

        # 第二步：使用AI优化提示词（嵌入CAP）
        if language == "chinese":
            cap_optimization_prompt = f"""
请作为提示词工程专家，优化以下结构化提示词，使其能够引导AI产生更深度、更专业的回答。

原始结构化提示词：
{base_structured_prompt}

优化要求：
1. 保持原有结构化格式
2. 增强思维深度引导
3. 加强专业性要求
4. 提升逻辑严密性
5. 确保实用性导向

请输出优化后的完整提示词：
"""
        else:
            cap_optimization_prompt = f"""
As a prompt engineering expert, please optimize the following structured prompt to guide AI to produce more in-depth and professional responses.

Original structured prompt:
{base_structured_prompt}

Optimization requirements:
1. Maintain the original structured format
2. Enhance depth of thinking guidance
3. Strengthen professional requirements
4. Improve logical rigor
5. Ensure practical orientation

Please output the complete optimized prompt:
"""

        # 第一次API调用：优化提示词
        print("  📝 第一次调用：优化提示词...")
        optimization_result = self.api_client.call_api(model, cap_optimization_prompt)

        if not optimization_result["success"]:
            return {
                "success": False,
                "error": f"提示词优化失败: {optimization_result['error']}",
                "approach": "A_embedded",
                "language": language
            }

        optimized_prompt = optimization_result["content"]

        # 第二次API调用：执行优化后的任务
        print("  🎯 第二次调用：执行优化任务...")
        execution_result = self.api_client.call_api(model, optimized_prompt)

        if not execution_result["success"]:
            return {
                "success": False,
                "error": f"任务执行失败: {execution_result['error']}",
                "approach": "A_embedded",
                "language": language
            }

        # 分析最终结果
        final_content = execution_result["content"]
        if "R1" in model and execution_result.get("reasoning_content"):
            analysis_content = execution_result["reasoning_content"] + "\n\n" + final_content
        else:
            analysis_content = final_content

        logic_analysis = self.logic_detector.detect_logic_depth(analysis_content)

        return {
            "success": True,
            "approach": "A_embedded",
            "language": language,
            "api_calls": 2,
            "base_prompt": base_structured_prompt,
            "optimized_prompt": optimized_prompt,
            "final_result": final_content,
            "analysis_content": analysis_content,
            "logic_analysis": logic_analysis,
            "optimization_step": optimization_result,
            "execution_step": execution_result,
            "total_tokens": (optimization_result.get("token_usage", {}).get("total_tokens", 0) +
                           execution_result.get("token_usage", {}).get("total_tokens", 0))
        }

    def test_approach_b_header_cap(self, task: Dict, model: str, language: str = "chinese") -> Dict[str, Any]:
        """方案B：外部头部式CAP（头部规则嵌入）"""
        print(f"🔄 测试方案B - 外部头部式CAP ({language})")

        # 生成基础结构化提示词
        base_structured_prompt = self.prompt_template.generate_structured_prompt(
            task["base_task"],
            task["context"],
            "专业技术顾问" if language == "chinese" else "Professional Technical Consultant",
            task["expected_aspects"],
            language
        )

        # 在头部添加CAP优化规则
        if language == "chinese":
            cap_header = """
<CAP_OPTIMIZATION_RULES>
请在处理以下任务时严格遵循CAP优化框架：

**Chain-of-Thought (思维链优化)**：
- 运用系统性思维，从问题识别→深度分析→方案设计→实施规划的完整链条
- 每个推理步骤都要有明确的逻辑依据和证据支撑
- 主动寻找潜在的反例和边界情况进行验证

**Augmentation (增强优化)**：
- 整合多领域专业知识和最佳实践
- 提供具体的量化指标和评估标准
- 结合实际案例和行业经验进行论证
- 考虑长期影响和可扩展性

**Prompting (提示优化)**：
- 确保回答结构化、逻辑严密、层次清晰
- 提供可操作的具体建议和实施步骤
- 包含风险评估和应急预案
- 符合专业标准和行业规范

请以最高专业水准完成以下任务，展现深度思考和专业洞察。
</CAP_OPTIMIZATION_RULES>

"""
        else:
            cap_header = """
<CAP_OPTIMIZATION_RULES>
Please strictly follow the CAP optimization framework when processing the following task:

**Chain-of-Thought (Thinking Chain Optimization)**:
- Apply systematic thinking from problem identification → in-depth analysis → solution design → implementation planning
- Each reasoning step must have clear logical basis and evidence support
- Actively seek potential counterexamples and boundary cases for verification

**Augmentation (Enhancement Optimization)**:
- Integrate multi-domain professional knowledge and best practices
- Provide specific quantitative metrics and evaluation standards
- Combine actual cases and industry experience for argumentation
- Consider long-term impacts and scalability

**Prompting (Prompt Optimization)**:
- Ensure structured, logically rigorous, and clearly layered responses
- Provide actionable specific recommendations and implementation steps
- Include risk assessment and contingency plans
- Comply with professional standards and industry norms

Please complete the following task with the highest professional standards, demonstrating deep thinking and professional insights.
</CAP_OPTIMIZATION_RULES>

"""

        # 组合完整提示词
        full_prompt = cap_header + base_structured_prompt

        # 一次API调用完成任务
        print("  🎯 单次调用：执行CAP优化任务...")
        execution_result = self.api_client.call_api(model, full_prompt)

        if not execution_result["success"]:
            return {
                "success": False,
                "error": f"任务执行失败: {execution_result['error']}",
                "approach": "B_header",
                "language": language
            }

        # 分析结果
        final_content = execution_result["content"]
        if "R1" in model and execution_result.get("reasoning_content"):
            analysis_content = execution_result["reasoning_content"] + "\n\n" + final_content
        else:
            analysis_content = final_content

        logic_analysis = self.logic_detector.detect_logic_depth(analysis_content)

        return {
            "success": True,
            "approach": "B_header",
            "language": language,
            "api_calls": 1,
            "base_prompt": base_structured_prompt,
            "cap_header": cap_header,
            "full_prompt": full_prompt,
            "final_result": final_content,
            "analysis_content": analysis_content,
            "logic_analysis": logic_analysis,
            "execution_step": execution_result,
            "total_tokens": execution_result.get("token_usage", {}).get("total_tokens", 0)
        }

    def test_approach_c_semantic_enhanced_cap(self, task: Dict, model: str, language: str = "chinese") -> Dict[str, Any]:
        """方案C：语义分析增强CAP（基于V3语义分析算法）"""
        print(f"🔄 测试方案C - 语义分析增强CAP ({language})")

        # 生成基础结构化提示词
        base_structured_prompt = self.prompt_template.generate_structured_prompt(
            task["base_task"],
            task["context"],
            "专业技术顾问" if language == "chinese" else "Professional Technical Consultant",
            task["expected_aspects"],
            language
        )

        # 使用语义分析引擎生成增强CAP
        semantic_enhanced_prompt = self.semantic_engine.generate_semantic_enhanced_cap(
            base_structured_prompt,
            task["context"] + " " + task["base_task"]
        )

        # 一次API调用完成任务
        print("  🎯 单次调用：执行语义增强CAP任务...")
        execution_result = self.api_client.call_api(model, semantic_enhanced_prompt)

        if not execution_result["success"]:
            return {
                "success": False,
                "error": f"任务执行失败: {execution_result['error']}",
                "approach": "C_semantic",
                "language": language
            }

        # 分析结果
        final_content = execution_result["content"]
        if "R1" in model and execution_result.get("reasoning_content"):
            analysis_content = execution_result["reasoning_content"] + "\n\n" + final_content
        else:
            analysis_content = final_content

        logic_analysis = self.logic_detector.detect_logic_depth(analysis_content)

        # 额外的语义分析
        semantic_analysis = self.semantic_engine.analyze_content(analysis_content)

        return {
            "success": True,
            "approach": "C_semantic",
            "language": language,
            "api_calls": 1,
            "base_prompt": base_structured_prompt,
            "semantic_enhanced_prompt": semantic_enhanced_prompt,
            "final_result": final_content,
            "analysis_content": analysis_content,
            "logic_analysis": logic_analysis,
            "semantic_analysis": semantic_analysis,
            "execution_step": execution_result,
            "total_tokens": execution_result.get("token_usage", {}).get("total_tokens", 0)
        }

    def run_enhanced_comparison_test(self) -> Dict[str, Any]:
        """运行增强版对比测试"""
        print("🚀 增强版CAP方法对比测试器启动")
        print("=" * 80)
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 测试目标: 对比三种CAP方法 + 中英文双语评估")
        print(f"📊 测试任务: {len(REAL_WORLD_TASKS)}个实际任务场景")
        print(f"🤖 测试模型: {len(API_CONFIG['models'])}个模型")
        print(f"🌐 测试语言: 中文 + 英文")
        print()

        test_results = {
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "test_framework": "增强版CAP方法对比测试器",
                "models_tested": API_CONFIG["models"],
                "tasks_tested": [task["id"] for task in REAL_WORLD_TASKS],
                "languages_tested": ["chinese", "english"],
                "approaches_tested": ["A_embedded", "B_header", "C_semantic"]
            },
            "task_results": {},
            "language_comparison": {},
            "approach_comparison": {},
            "model_comparison": {}
        }

        # 对每个任务、模型和语言进行测试
        for task in REAL_WORLD_TASKS:
            print(f"📋 测试任务: {task['name']} ({task['id']})")
            print("-" * 60)

            task_result = {
                "task_info": task,
                "language_results": {}
            }

            # 测试中英文两种语言
            for language in ["chinese", "english"]:
                print(f"🌐 测试语言: {language}")

                language_result = {
                    "language": language,
                    "model_results": {}
                }

                for model in API_CONFIG["models"]:
                    print(f"🤖 测试模型: {model}")

                    model_result = {
                        "model": model,
                        "approach_a_result": {},
                        "approach_b_result": {},
                        "approach_c_result": {},
                        "comparison": {}
                    }

                    # 测试方案A
                    try:
                        approach_a_result = self.test_approach_a_embedded_cap(task, model, language)
                        model_result["approach_a_result"] = approach_a_result

                        if approach_a_result["success"]:
                            print(f"  ✅ 方案A完成 - 质量分数: {approach_a_result['logic_analysis']['overall_score']:.1f}")
                        else:
                            print(f"  ❌ 方案A失败: {approach_a_result['error']}")

                    except Exception as e:
                        print(f"  ❌ 方案A异常: {str(e)}")
                        model_result["approach_a_result"] = {"success": False, "error": str(e)}

                    time.sleep(3)  # 避免API限流

                    # 测试方案B
                    try:
                        approach_b_result = self.test_approach_b_header_cap(task, model, language)
                        model_result["approach_b_result"] = approach_b_result

                        if approach_b_result["success"]:
                            print(f"  ✅ 方案B完成 - 质量分数: {approach_b_result['logic_analysis']['overall_score']:.1f}")
                        else:
                            print(f"  ❌ 方案B失败: {approach_b_result['error']}")

                    except Exception as e:
                        print(f"  ❌ 方案B异常: {str(e)}")
                        model_result["approach_b_result"] = {"success": False, "error": str(e)}

                    time.sleep(3)  # 避免API限流

                    # 测试方案C
                    try:
                        approach_c_result = self.test_approach_c_semantic_enhanced_cap(task, model, language)
                        model_result["approach_c_result"] = approach_c_result

                        if approach_c_result["success"]:
                            print(f"  ✅ 方案C完成 - 质量分数: {approach_c_result['logic_analysis']['overall_score']:.1f}")
                            print(f"    语义完整性: {approach_c_result['semantic_analysis']['semantic_completeness']:.1f}")
                        else:
                            print(f"  ❌ 方案C失败: {approach_c_result['error']}")

                    except Exception as e:
                        print(f"  ❌ 方案C异常: {str(e)}")
                        model_result["approach_c_result"] = {"success": False, "error": str(e)}

                    # 对比分析
                    model_result["comparison"] = self._compare_three_approaches(
                        model_result["approach_a_result"],
                        model_result["approach_b_result"],
                        model_result["approach_c_result"]
                    )

                    language_result["model_results"][model] = model_result
                    print(f"  📊 三方案对比完成")
                    print()

                    time.sleep(2)  # 避免API限流

                task_result["language_results"][language] = language_result
                print(f"✅ 语言 {language} 测试完成")
                print()

            test_results["task_results"][task["id"]] = task_result
            print(f"✅ 任务 {task['name']} 测试完成")
            print()

        # 生成综合分析
        from enhanced_analysis_functions import (
            generate_language_comparison,
            generate_approach_comparison,
            generate_model_comparison,
            generate_enhanced_final_report
        )

        test_results["language_comparison"] = generate_language_comparison(test_results["task_results"])
        test_results["approach_comparison"] = generate_approach_comparison(test_results["task_results"])
        test_results["model_comparison"] = generate_model_comparison(test_results["task_results"])

        # 输出最终报告
        generate_enhanced_final_report(test_results)

        return test_results

    def _compare_three_approaches(self, approach_a: Dict, approach_b: Dict, approach_c: Dict) -> Dict[str, Any]:
        """对比三种方案的结果"""
        successful_approaches = []

        for approach_name, approach_data in [("A", approach_a), ("B", approach_b), ("C", approach_c)]:
            if approach_data.get("success"):
                successful_approaches.append({
                    "name": approach_name,
                    "score": approach_data["logic_analysis"]["overall_score"],
                    "tokens": approach_data.get("total_tokens", 0),
                    "calls": approach_data.get("api_calls", 0),
                    "language": approach_data.get("language", "unknown")
                })

        if len(successful_approaches) < 2:
            return {
                "comparison_available": False,
                "reason": "至少需要两个方案成功才能进行对比"
            }

        # 排序找出最佳方案
        best_quality = max(successful_approaches, key=lambda x: x["score"])
        best_efficiency = min(successful_approaches, key=lambda x: x["tokens"])

        return {
            "comparison_available": True,
            "successful_approaches": len(successful_approaches),
            "quality_ranking": sorted(successful_approaches, key=lambda x: x["score"], reverse=True),
            "efficiency_ranking": sorted(successful_approaches, key=lambda x: x["tokens"]),
            "best_quality": best_quality,
            "best_efficiency": best_efficiency,
            "quality_vs_efficiency": {
                "same_winner": best_quality["name"] == best_efficiency["name"],
                "quality_leader": best_quality["name"],
                "efficiency_leader": best_efficiency["name"]
            }
        }

# ==================== 主函数 ====================
def main():
    """主函数 - 执行增强版CAP方法对比测试"""

    print("🎯 增强版CAP方法对比测试器启动")
    print("目标：解决推理深度评估问题 + 添加语义分析增强方案")
    print("特性：中英文双语评估 + 三种CAP方法对比")
    print()

    # 创建测试器
    tester = EnhancedCAPApproachTester()

    # 运行测试
    try:
        results = tester.run_enhanced_comparison_test()

        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"enhanced_cap_comparison_report_{timestamp}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"\n💾 详细测试数据已保存: {filename}")
        print("🎉 增强版CAP方法对比测试完成！")

        return results

    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return None
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
