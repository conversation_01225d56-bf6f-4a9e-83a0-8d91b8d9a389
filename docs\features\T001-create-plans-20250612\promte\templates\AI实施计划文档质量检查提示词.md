# AI实施计划文档质量检查提示词

## 使用说明
这是用于检查AI生成的实施计划文档是否严格遵循《AI严格生成实施计划文档的提示词.md》模板要求的质量检查提示词。确保实施计划文档的完整性、一致性和可执行性。

---

## 🎯 质量检查执行提示词

```
请严格按照以下完整流程对指定的实施计划文档进行全面质量检查：

## 📋 检查任务
1. **读取并分析实施计划文档**，全面评估文档质量
2. **对照模板标准**，逐项检查是否符合《AI严格生成实施计划文档的提示词.md》要求
3. **生成详细的质量检查报告**，包含合规性评估和改进建议
4. **提供可执行的修复方案**，针对发现的问题给出具体改进措施

## 🧠 AI认知约束强制激活

### 必须激活的核心命令
```bash
@L1:global-constraints                    # 全局约束检查
@L1:ai-implementation-design-principles  # AI实施设计原则
@AI_COGNITIVE_CONSTRAINTS                # AI认知约束激活
@MEMORY_BOUNDARY_CHECK                  # 记忆边界检查
@HALLUCINATION_PREVENTION               # 幻觉防护激活
@BOUNDARY_GUARD_ACTIVATION              # 边界护栏激活
@DESIGN_CONSTRAINT_VALIDATION           # 设计约束验证
@TEMPLATE_COMPLIANCE_CHECK              # 模板合规性检查（新增）
@QUALITY_ASSURANCE_VALIDATION           # 质量保证验证（新增）
```

### 检查认知约束
- **文档完整性验证**: 所有必要章节和要素必须存在
- **内容一致性检查**: 文档内容必须逻辑一致，无矛盾
- **可执行性验证**: 所有步骤必须具体可执行
- **设计约束传承**: 设计文档约束必须100%传承到实施计划

## 🔍 第一步：文档结构完整性检查

### 1.1 核心章节存在性检查
**目标**: 验证实施计划文档包含所有必要的核心章节

**必须存在的章节清单**:
- [ ] **文档元数据**: 包含文档ID、类型、版本、状态、复杂度等级、设计文档依赖、架构约束合规
- [ ] **AI认知约束激活**: 包含强制激活命令和护栏机制检查
- [ ] **设计文档架构约束转化结果**: 核心设计哲学传承、强制性架构约束清单、设计-实施一致性验证矩阵
- [ ] **实施范围边界**: 包含范围、排除范围、边界护栏，每项都标注设计约束/设计依据
- [ ] **现有架构调研结果**: 项目结构分析、核心模块识别、技术架构栈、接口设计现状、复用机会识别
- [ ] **具体实施目标**: 功能目标、技术目标、验收标准、复杂度分析
- [ ] **详细实施步骤**: 至少3个阶段，每个阶段包含认知单元、操作边界、验证锚点、设计约束验证
- [ ] **操作边界控制**: 允许修改的文件范围、严格禁止修改范围
- [ ] **验证锚点系统**: 每步验证标准、质量门禁
- [ ] **风险评估与应对方案**: P0/P1/P2三级风险表格，包含设计文档依据列
- [ ] **执行检查清单**: 实施前检查、每步实施检查、完成验收检查
- [ ] **执行状态跟踪**: 实施进度、质量指标监控（包含设计约束合规率、架构一致性）

**检查方法**: 使用 `read_file` 工具逐一确认每个章节的存在性

**验证标准**: 
✅ **完全合规**: 所有12个核心章节都存在且结构完整
❌ **不合规**: 缺失任何一个核心章节或章节结构不完整

### 1.2 设计约束转化专项检查
**目标**: 验证是否正确执行了"第零步：设计文档深度解析与架构约束提取"

**必须存在的设计约束转化要素**:
- [ ] **设计哲学提取**: 明确描述了从设计文档提取的核心架构思想、技术价值观、设计权衡
- [ ] **强制性架构约束清单**: 包含A/B/C三类约束表格，每项约束都有约束ID、约束内容、来源章节、验证标准、违规后果
- [ ] **设计-实施一致性验证矩阵**: 包含"设计目标→实施验证映射"和"技术选型→实施约束映射"
- [ ] **约束传承证据**: 实施步骤中每个关键环节都标注了对应的设计文档约束ID或依据

**检查方法**: 使用 `grep_search` 搜索关键词"设计约束"、"约束ID"、"设计文档依据"等

**验证标准**:
✅ **完全合规**: 设计约束100%转化为实施约束，有完整的约束清单和验证机制
❌ **不合规**: 缺失设计约束转化环节或转化不完整

### 1.3 AI认知约束激活检查
**目标**: 验证是否正确激活了所有必要的AI认知约束命令

**必须激活的命令清单**:
- [ ] `@L1:global-constraints`
- [ ] `@L1:ai-implementation-design-principles`
- [ ] `@AI_COGNITIVE_CONSTRAINTS`
- [ ] `@MEMORY_BOUNDARY_CHECK`
- [ ] `@HALLUCINATION_PREVENTION`
- [ ] `@ATOMIC_OPERATION_VALIDATION`
- [ ] `@COGNITIVE_GRANULARITY_CONTROL`
- [ ] `@BOUNDARY_GUARD_ACTIVATION`
- [ ] `@ARCHITECTURAL_EVOLUTION_CHECK`
- [ ] `@DESIGN_CONSTRAINT_VALIDATION`

**检查方法**: 使用 `grep_search` 搜索每个约束命令是否在文档中存在

**验证标准**:
✅ **完全合规**: 所有10个约束命令都正确激活
❌ **不合规**: 缺失任何一个约束命令

## 🔍 第二步：内容质量深度检查

### 2.1 架构调研结果真实性检查
**目标**: 验证架构调研是否基于真实代码而非假设

**检查要点**:
- [ ] **项目结构分析**: 是否包含具体的目录树结构，而非泛化描述
- [ ] **核心模块识别**: 是否标注了具体的文件路径，而非抽象模块名
- [ ] **技术架构栈**: 是否基于实际代码分析得出，包含具体版本信息
- [ ] **接口设计现状**: 是否引用了具体的类名、方法名、接口定义
- [ ] **复用机会识别**: 是否包含具体的文件路径和组件锚点引用

**检查方法**: 
- 使用 `grep_search` 搜索文件路径模式（如 `src/main/java`、`.java`、`.xml`等）
- 检查是否包含具体的代码片段或接口定义

**验证标准**:
✅ **真实调研**: 包含具体的文件路径、代码结构、技术版本信息
❌ **假设描述**: 只有抽象描述，缺乏具体的代码证据

### 2.2 实施步骤可执行性检查
**目标**: 验证每个实施步骤是否具体可执行

**检查每个步骤的要素**:
- [ ] **认知单元**: 清楚描述了当前步骤处理的概念数量（≤5个）
- [ ] **操作边界**: 明确限制了修改范围（≤50行代码，≤3个操作步骤）
- [ ] **验证锚点**: 包含具体的验证命令和验证标准
- [ ] **设计约束验证**: 每个步骤都标注了对应的设计文档约束检查
- [ ] **具体执行指引**: 包含实际的命令、代码示例、配置内容
- [ ] **智能编译验证**: 包含风险评估和对应的验证策略

**检查方法**:
- 使用 `grep_search` 搜索每个阶段是否包含具体的命令（如 `java -version`、`mvn compile`等）
- 检查是否包含"约束检查"、"设计约束验证"等关键词

**验证标准**:
✅ **完全可执行**: 每个步骤都有具体的命令、代码、验证方法
❌ **抽象描述**: 步骤描述抽象，缺乏具体的执行指令

### 2.3 风险控制方案完整性检查
**目标**: 验证风险控制方案是否完整且与设计文档风险评估对应

**必须包含的风险控制要素**:
- [ ] **P0级别风险表格**: 包含风险场景、触发条件、立即回滚操作、设计文档依据四列
- [ ] **P1级别风险表格**: 包含风险场景、触发条件、处理策略、设计文档依据四列
- [ ] **P2级别风险表格**: 包含风险场景、触发条件、处理策略、设计文档依据四列
- [ ] **设计约束违规风险**: 专门针对设计约束违规的风险控制措施
- [ ] **回滚执行命令**: 具体的Git回滚命令和验证步骤

**检查方法**:
- 使用 `grep_search` 搜索"P0级别风险"、"P1级别风险"、"P2级别风险"
- 检查表格是否包含"设计文档依据"列
- 验证是否包含"设计约束违规"相关风险项

**验证标准**:
✅ **完整风险控制**: 三级风险都有完整表格，包含设计文档依据，有约束违规专项风险
❌ **风险控制不足**: 缺失风险级别或缺少设计文档依据列

## 🔍 第三步：设计约束传承一致性检查

### 3.1 设计约束清单完整性检查
**目标**: 验证是否建立了完整的设计约束清单

**必须包含的约束类别**:
- [ ] **A类约束：技术栈强制性要求**: 表格包含约束ID、约束内容、来源章节、验证标准、违规后果
- [ ] **B类约束：架构模式强制性要求**: 同样的表格结构
- [ ] **C类约束：性能与质量强制性要求**: 同样的表格结构
- [ ] **约束ID编号规范**: 使用A001、A002、B001等规范编号
- [ ] **来源章节追溯**: 每个约束都能追溯到设计文档的具体章节

**检查方法**:
- 使用 `grep_search` 搜索"A类约束"、"B类约束"、"C类约束"
- 检查约束ID格式是否规范（A001、B001等）
- 验证是否包含"来源章节"、"验证标准"、"违规后果"列

**验证标准**:
✅ **约束清单完整**: 三类约束都有完整表格，编号规范，追溯明确
❌ **约束清单不完整**: 缺失约束类别或表格结构不完整

### 3.2 实施步骤约束对应检查
**目标**: 验证每个实施步骤是否正确标注了对应的设计约束

**检查要点**:
- [ ] **包含范围约束标注**: 每个功能模块都标注"设计约束: [约束ID]"
- [ ] **排除范围约束标注**: 每个排除项都标注"设计依据: [设计文档章节]"
- [ ] **实施步骤约束检查**: 每个关键步骤都包含"约束检查: [约束ID]"
- [ ] **设计约束验证检查点**: 每个阶段都包含"设计约束验证"子章节
- [ ] **风险表格约束依据**: 风险表格的"设计文档依据"列有具体内容

**检查方法**:
- 使用 `grep_search` 搜索"设计约束:"、"约束检查:"、"设计依据:"
- 验证约束ID引用是否与约束清单中的ID一致
- 检查设计约束验证检查点是否存在

**验证标准**:
✅ **约束对应完整**: 所有关键环节都正确标注约束，ID引用一致
❌ **约束对应不完整**: 缺失约束标注或约束ID引用不一致

### 3.3 设计-实施一致性验证矩阵检查
**目标**: 验证是否建立了设计目标与实施验证的映射关系

**必须包含的映射要素**:
- [ ] **设计目标→实施验证映射**: 树状结构展示设计目标如何分解为验证点
- [ ] **技术选型→实施约束映射**: 树状结构展示技术选型如何转化为实施约束
- [ ] **一致性检查标准**: 每个映射都包含具体的检查标准
- [ ] **验证机制**: 每个映射都包含具体的验证方法

**检查方法**:
- 使用 `grep_search` 搜索"设计目标"、"技术选型"、"一致性检查"
- 检查是否包含树状结构（├── └──）
- 验证是否包含"验证机制"、"检查标准"

**验证标准**:
✅ **映射矩阵完整**: 包含完整的设计-实施映射，有具体的验证机制
❌ **映射矩阵不完整**: 缺失映射关系或验证机制不明确

## 🔍 第四步：架构思想遵从性深度检查

### 4.1 设计哲学传承一致性检查
**目标**: 验证实施计划是否真正遵循设计文档的核心架构思想

**核心架构思想检查要点**:
- [ ] **设计哲学传承**: 实施计划的技术选型是否体现设计文档的"务实架构"思想
- [ ] **技术价值观一致**: 实施步骤的技术决策是否符合设计文档的价值导向
- [ ] **架构演进理念**: 实施方案是否支持设计文档规划的架构演进方向
- [ ] **性能优先原则**: 实施细节是否体现设计文档的性能优化理念
- [ ] **可维护性平衡**: 实施方案是否在复杂性和可维护性间找到设计文档要求的平衡点

**检查方法**:
- 使用 `read_file` 深度对比设计文档的架构理念章节与实施计划的技术决策
- 使用 `grep_search` 搜索实施计划中体现设计哲学的关键词
- 分析实施步骤是否与设计文档的技术路线图一致

**验证标准**:
✅ **思想高度一致**: 实施计划完全体现设计文档的架构思想和技术理念
❌ **思想偏离**: 实施计划的技术决策与设计文档的架构思想不一致

### 4.2 技术选型决策逻辑检查
**目标**: 验证实施计划的技术选型是否遵循设计文档的决策逻辑

**技术选型逻辑检查要点**:
- [ ] **组合优化理念**: 实施方案是否体现设计文档的"技术特性协同效应"
- [ ] **成熟技术优先**: 技术选择是否遵循设计文档的"基于成熟Spring生态"原则
- [ ] **性能基准导向**: 技术配置是否以设计文档的性能指标为基准
- [ ] **渐进式演进**: 实施策略是否支持设计文档的渐进式迁移理念
- [ ] **风险控制一致**: 风险处理策略是否与设计文档的风险评估逻辑一致

**检查方法**:
- 对比设计文档的"技术基石与组合优化哲学"与实施计划的技术选型
- 验证实施方案的技术组合是否产生设计文档预期的协同效应
- 检查风险控制措施是否遵循设计文档的风险评估逻辑

**验证标准**:
✅ **决策逻辑一致**: 技术选型完全遵循设计文档的决策逻辑和优化理念
❌ **决策逻辑偏离**: 技术选型与设计文档的决策逻辑不符

## 🔍 第五步：AI认知步骤合理性检查

### 5.1 AI认知负载控制检查
**目标**: 验证实施步骤的AI认知负载设计是否合理

**认知负载控制要点**:
- [ ] **单步骤概念数量**: 每个步骤的概念数量是否≤5个，避免认知超载
- [ ] **代码修改粒度**: 单次修改是否≤50行代码，符合AI处理能力边界
- [ ] **依赖层级深度**: 操作的依赖层级是否≤2层，避免复杂性爆炸
- [ ] **上下文切换频率**: 步骤间的上下文切换是否控制在合理范围内
- [ ] **记忆边界管理**: 总操作文件数是否≤3个/阶段，避免记忆压力过大

**检查方法**:
- 使用 `grep_search` 统计每个步骤涉及的概念数量和文件数量
- 分析步骤间的依赖关系深度和复杂性
- 验证认知负载参数是否在AI能力边界内

**验证标准**:
✅ **认知负载合理**: 所有步骤的认知负载都在AI处理能力范围内
❌ **认知负载过高**: 存在超出AI认知能力边界的步骤设计

### 5.2 幻觉风险防护机制检查
**目标**: 验证实施计划是否建立了充分的幻觉防护机制

**幻觉防护机制要点**:
- [ ] **现实锚点密度**: 每个步骤是否有足够的现实验证锚点（≥3个/步骤）
- [ ] **具体代码引用**: 是否基于真实代码结构而非假设性描述
- [ ] **验证命令具体性**: 每个验证是否有具体可执行的命令
- [ ] **风险触发器明确**: 幻觉风险触发条件是否明确定义
- [ ] **回滚机制完整**: 是否有明确的幻觉检测和回滚机制

**检查方法**:
- 统计每个步骤的验证锚点数量和具体性程度
- 检查是否包含具体的文件路径、命令、代码片段
- 验证幻觉风险的识别和处理机制完整性

**验证标准**:
✅ **幻觉防护充分**: 每个步骤都有充分的现实锚点和验证机制
❌ **幻觉防护不足**: 缺乏足够的现实验证锚点或防护机制

### 5.3 Token使用效率检查
**目标**: 验证实施计划的token使用是否高效合理

**Token效率要点**:
- [ ] **信息密度优化**: 关键信息是否以高密度方式组织，避免冗余
- [ ] **分阶段处理**: 是否合理分解复杂任务，避免单次token消耗过大
- [ ] **上下文复用**: 是否充分复用已建立的上下文，避免重复描述
- [ ] **优先级导向**: 是否优先处理高价值信息，合理分配token资源
- [ ] **渐进式细化**: 是否采用渐进式细化策略，避免过度细节化

**检查方法**:
- 分析文档的信息组织结构和密度
- 评估任务分解的合理性和token效率
- 检查是否存在不必要的信息重复

**验证标准**:
✅ **Token使用高效**: 信息组织合理，token使用效率高
❌ **Token使用低效**: 存在信息冗余或分解不合理的问题

## 🔍 第六步：多维度完整性与准确性检查

### 6.1 可执行性准确度检查
**目标**: 从多个维度验证实施计划的可执行性准确度

**可执行性维度**:
- [ ] **环境依赖完整性**: 是否包含所有必要的环境依赖和配置要求
- [ ] **命令语法准确性**: 所有执行命令的语法是否正确且可执行
- [ ] **文件路径真实性**: 引用的文件路径是否基于真实项目结构
- [ ] **版本兼容性**: 技术栈版本是否兼容且经过验证
- [ ] **执行顺序逻辑性**: 步骤执行顺序是否符合逻辑依赖关系
- [ ] **错误处理完整性**: 是否为每个关键步骤提供了错误处理机制

**检查方法**:
- 逐一验证命令的语法正确性和可执行性
- 检查文件路径引用的真实性和准确性
- 分析步骤依赖关系的逻辑合理性

**验证标准**:
✅ **高度可执行**: 所有步骤都具备明确的执行条件和准确的操作指令
❌ **可执行性不足**: 存在模糊描述或不可执行的步骤

### 6.2 质量保证机制完整性检查
**目标**: 验证实施计划是否建立了完整的质量保证机制

**质量保证维度**:
- [ ] **多层次验证**: 是否建立了编译、测试、功能、性能的多层次验证
- [ ] **质量门禁**: 是否设置了明确的质量标准和通过条件
- [ ] **持续监控**: 是否建立了实施过程中的持续质量监控机制
- [ ] **问题诊断**: 是否提供了问题发现和诊断的具体方法
- [ ] **改进反馈**: 是否建立了质量问题的改进和反馈机制

**检查方法**:
- 统计验证层次的数量和覆盖范围
- 检查质量标准的明确性和可衡量性
- 验证监控和反馈机制的完整性

**验证标准**:
✅ **质量保证完整**: 建立了全面的质量保证机制和监控体系
❌ **质量保证不足**: 质量保证机制不完整或标准不明确

### 6.3 风险控制准确性检查
**目标**: 验证风险控制方案的准确性和有效性

**风险控制准确性维度**:
- [ ] **风险识别全面性**: 是否覆盖了技术、业务、操作、时间等多维度风险
- [ ] **风险评估准确性**: 风险等级划分是否准确合理
- [ ] **应对措施针对性**: 风险应对措施是否针对具体风险场景设计
- [ ] **回滚方案可操作性**: 回滚方案是否具体可操作且经过验证
- [ ] **风险监控实时性**: 是否建立了风险的实时监控和预警机制

**检查方法**:
- 对比风险清单与设计文档和实际项目的风险评估
- 验证风险应对措施的针对性和可操作性
- 检查回滚方案的完整性和可执行性

**验证标准**:
✅ **风险控制准确**: 风险识别全面，应对措施针对性强，回滚方案可操作
❌ **风险控制不准确**: 风险识别不全面或应对措施不具体

## 🔍 第七步：质量指标达标检查

### 7.1 AI度量参数应用检查
**目标**: 验证是否正确应用了AI专业度量参数系统

**必须包含的度量要素**:
- [ ] **复杂度评估**: 明确标注了项目复杂度等级（L1/L2/L3）和总评分
- [ ] **五维度评估**: 包含认知复杂度、记忆边界压力、幻觉风险系数、上下文切换成本、验证锚点密度的评分
- [ ] **度量驱动决策**: 基于度量结果选择了对应的文档生成策略
- [ ] **度量监控**: 在实施步骤中包含了AI度量监控机制

**检查方法**:
- 使用 `grep_search` 搜索"AI度量参数"、"复杂度等级"、"评估总分"
- 检查是否包含五个维度的具体评分
- 验证文档策略是否与复杂度等级匹配

**验证标准**:
✅ **度量应用正确**: 正确评估五维度，复杂度等级与文档策略匹配
❌ **度量应用不正确**: 缺失度量评估或度量结果与策略不匹配

### 7.2 验证锚点密度检查
**目标**: 验证每个步骤是否有足够的验证锚点

**验证锚点要求**:
- [ ] **每步验证标准**: 包含编译验证、测试验证、功能验证、性能验证、设计约束验证、架构一致性验证
- [ ] **质量门禁**: 包含编译成功率、测试通过率、代码覆盖率、功能验收、性能达标、设计约束合规率、架构一致性指标
- [ ] **智能编译验证**: 包含风险评估和对应的验证策略（高/中/低风险触发器）
- [ ] **验证命令具体性**: 每个验证都有具体的执行命令

**检查方法**:
- 使用 `grep_search` 搜索"验证标准"、"质量门禁"、"智能编译验证"
- 检查验证锚点是否包含设计约束验证和架构一致性验证
- 验证是否包含具体的验证命令

**验证标准**:
✅ **验证锚点充分**: 每步都有≥5个验证锚点，包含设计约束验证
❌ **验证锚点不足**: 验证锚点少于5个或缺失设计约束验证

### 7.3 执行检查清单完整性检查
**目标**: 验证是否包含完整的执行检查清单

**必须包含的检查清单**:
- [ ] **实施前检查**: 包含设计约束提取转化、架构约束清单建立、设计约束护栏激活等新增项
- [ ] **每步实施检查**: 包含设计约束合规、架构一致性验证等新增项
- [ ] **完成验收检查**: 包含设计约束100%合规、架构一致性验证通过等新增项
- [ ] **质量指标监控**: 包含设计约束合规率、架构一致性等新增指标

**检查方法**:
- 使用 `grep_search` 搜索"实施前检查"、"每步实施检查"、"完成验收检查"
- 检查是否包含设计约束相关的检查项
- 验证质量指标是否包含新增的设计约束指标

**验证标准**:
✅ **检查清单完整**: 三类检查清单都包含设计约束相关项目
❌ **检查清单不完整**: 缺失检查类别或缺少设计约束检查项

## 🔍 第八步：设计关键点与实施关键点精准对齐检查（新增核心维度）

### 8.1 关键点映射完整性检查
**目标**: 验证设计文档的每个关键点在实施计划中都有精准对应

**设计文档关键点类别**:
- [ ] **业务关键点**: 核心业务目标、功能边界、业务价值点
- [ ] **技术关键点**: 关键技术选型、技术栈决策、技术架构点
- [ ] **架构关键点**: 分层设计、组件划分、接口设计关键决策
- [ ] **性能关键点**: 性能目标、性能瓶颈、性能优化关键点
- [ ] **风险关键点**: 关键风险识别、风险等级、风险应对策略
- [ ] **成功标准关键点**: 验收标准、质量指标、交付标准

**实施计划对应关键点**:
- [ ] **实施业务目标关键点**: 与设计业务关键点一一对应
- [ ] **实施技术目标关键点**: 与设计技术关键点一一对应
- [ ] **实施架构验证关键点**: 与设计架构关键点一一对应
- [ ] **实施性能验证关键点**: 与设计性能关键点一一对应
- [ ] **实施风险控制关键点**: 与设计风险关键点一一对应
- [ ] **实施验收标准关键点**: 与设计成功标准关键点一一对应

**检查方法**:
1. 使用 `read_file` 提取设计文档中所有关键决策点和关键目标
2. 使用 `grep_search` 搜索实施计划中的对应关键点标注
3. 建立"关键点映射矩阵"，验证一一对应关系
4. 检查关键点ID引用的一致性和完整性

**验证标准**:
✅ **关键点映射完整**: 设计文档每个关键点都在实施计划中有明确对应
❌ **关键点映射不完整**: 存在设计关键点在实施计划中缺失或模糊对应

### 8.2 关键决策链传承完整性检查
**目标**: 验证设计文档中的关键决策逻辑在实施计划中完整传承

**关键决策链识别**:
- [ ] **技术选型决策链**: A问题→B分析→C技术选择→D实施方案
- [ ] **架构设计决策链**: A需求→B约束→C架构模式→D组件设计
- [ ] **风险控制决策链**: A风险识别→B影响分析→C应对策略→D监控方案
- [ ] **性能优化决策链**: A性能要求→B瓶颈分析→C优化策略→D验证方案

**实施计划决策传承检查**:
- [ ] **决策逻辑传承**: 实施方案的技术选择逻辑与设计决策逻辑一致
- [ ] **决策依据传承**: 实施步骤中能够追溯到设计文档的具体决策依据
- [ ] **决策结果传承**: 实施目标与设计决策的预期结果精准对应
- [ ] **决策约束传承**: 实施边界与设计决策的约束条件完全一致

**检查方法**:
- 使用 `grep_search` 搜索"决策依据"、"选择理由"、"设计考虑"等关键词
- 验证实施计划中的技术选择是否有明确的设计文档决策支撑
- 检查实施步骤的逻辑顺序是否符合设计决策的推理过程

**验证标准**:
✅ **决策链传承完整**: 实施计划完整传承设计文档的关键决策逻辑
❌ **决策链传承不完整**: 实施方案与设计决策逻辑存在偏离或断层

### 8.3 双向验证锚点精准对齐检查
**目标**: 建立设计关键点↔实施关键点的双向锚定验证机制

**正向验证（设计→实施）**:
- [ ] **设计目标覆盖**: 设计文档的每个核心目标都在实施计划中有对应实现
- [ ] **设计约束体现**: 设计文档的每个约束都在实施步骤中有明确体现
- [ ] **设计风险应对**: 设计文档识别的每个风险都在实施计划中有应对措施
- [ ] **设计成功标准对应**: 设计文档的成功标准都在实施验收中有对应指标

**反向验证（实施→设计）**:
- [ ] **实施目标追溯**: 实施计划的每个核心目标都能追溯到设计文档依据
- [ ] **实施步骤依据**: 实施计划的每个关键步骤都有明确的设计文档支撑
- [ ] **实施验证依据**: 实施计划的每个验证标准都基于设计文档要求
- [ ] **实施边界依据**: 实施计划的范围边界都有设计文档的明确定义

**关键点完整性矩阵检查**:
```
关键点映射矩阵示例：
┌─────────────────────┬─────────────────────┬──────────┬──────────┐
│ 设计关键点          │ 实施关键点          │ 映射状态 │ 证据标注 │
├─────────────────────┼─────────────────────┼──────────┼──────────┤
│ D001-数据层抽象设计 │ I001-JPA层实施步骤  │ ✅完整   │ 步骤2.1  │
│ D002-查询优化策略   │ I002-Querydsl集成   │ ✅完整   │ 步骤3.2  │
│ D003-性能监控要求   │ I003-监控实施方案   │ ❌缺失   │ 无对应   │
└─────────────────────┴─────────────────────┴──────────┴──────────┘
```

**检查方法**:
1. 构建设计关键点清单（使用关键点ID：D001、D002等）
2. 构建实施关键点清单（使用关键点ID：I001、I002等）
3. 建立映射关系矩阵，标注对应状态（完整/部分/缺失）
4. 验证每个映射关系的证据标注和追溯路径

**验证标准**:
✅ **双向对齐完整**: 设计↔实施双向映射100%完整，无遗漏无偏离
❌ **双向对齐不完整**: 存在设计关键点缺失对应或实施关键点缺乏依据

### 8.4 关键交付成果精准对应检查
**目标**: 验证设计文档规划的关键交付成果与实施计划的交付成果精准匹配

**设计文档交付成果清单**:
- [ ] **技术组件交付**: 具体的技术组件、接口、配置
- [ ] **功能模块交付**: 具体的功能实现、业务逻辑、数据模型
- [ ] **质量成果交付**: 测试报告、性能报告、文档输出
- [ ] **集成成果交付**: 集成验证、兼容性验证、部署方案

**实施计划交付成果清单**:
- [ ] **阶段性交付**: 每个实施阶段的具体交付清单
- [ ] **验收标准交付**: 每个交付成果的验收标准和验证方法
- [ ] **质量指标交付**: 每个交付成果的质量指标和测量方法
- [ ] **集成验证交付**: 交付成果的集成验证和系统测试方案

**交付成果精准对应矩阵**:
```
交付成果对应矩阵示例：
┌─────────────────────┬─────────────────────┬──────────┬──────────┐
│ 设计预期交付        │ 实施计划交付        │ 对应状态 │ 验收标准 │
├─────────────────────┼─────────────────────┼──────────┼──────────┤
│ Commons-DB核心库    │ 阶段1-核心组件实现  │ ✅精准   │ 100%兼容 │
│ 性能优化方案        │ 阶段2-性能调优验证  │ ✅精准   │ <5%损失  │
│ 监控治理机制        │ 阶段3-治理体系建设  │ ⚠️部分   │ 需补充   │
└─────────────────────┴─────────────────────┴──────────┴──────────┘
```

**检查方法**:
- 使用 `grep_search` 搜索"交付成果"、"阶段交付"、"验收标准"
- 对比设计文档的"成功标准"与实施计划的"验收标准"
- 验证交付时间节点、质量要求、验收方法的一致性

**验证标准**:
✅ **交付成果精准对应**: 设计规划与实施交付100%精准匹配
❌ **交付成果对应偏离**: 交付内容、质量标准或时间节点存在偏离

## 📊 第九步：生成质量检查报告

### 9.1 合规性评估统计
**评估维度**:

#### A. 结构完整性评估 (12分)
- 核心章节存在性: ___/9 (每项1分)
- 章节结构完整性: ___/3 (完整性评分)

#### B. 设计约束传承评估 (20分)
- 设计约束转化完整性: ___/7
- 约束清单建立质量: ___/7  
- 实施步骤约束对应: ___/6

#### C. 架构思想遵从性评估 (15分)
- 设计哲学传承一致性: ___/8
- 技术选型决策逻辑: ___/7

#### D. 设计关键点精准对齐评估 (25分) 【新增核心维度】
- 关键点映射完整性: ___/8
- 关键决策链传承完整性: ___/8
- 双向验证锚点精准对齐: ___/6
- 关键交付成果精准对应: ___/3

#### E. AI认知步骤合理性评估 (12分)
- AI认知负载控制: ___/4
- 幻觉风险防护机制: ___/4
- Token使用效率: ___/4

#### F. 多维度完整性评估 (11分)
- 可执行性准确度: ___/4
- 质量保证机制完整性: ___/4
- 风险控制准确性: ___/3

#### G. 质量标准达标评估 (5分)
- AI度量参数应用: ___/2
- 验证锚点密度: ___/2
- 执行检查清单: ___/1

**总分**: ___/100分

**等级评定**:
- 90-100分: 🟢 **优秀** - 完全符合模板要求
- 80-89分: 🟡 **良好** - 基本符合，有少量改进点
- 70-79分: 🟠 **及格** - 符合基本要求，需要改进
- <70分: 🔴 **不合格** - 严重偏离模板要求，需要重新生成

### 5.2 问题清单与修复方案

**🔴 严重问题（必须修复）**:
[列出所有评估为"不合规"的检查项]
1. 问题描述: [具体问题]
   - 检查依据: [对应的模板要求]
   - 修复方案: [具体的修复步骤]
   - 优先级: P0

**🟡 改进建议（建议修复）**:
[列出可以进一步完善的方面]
1. 改进点: [具体改进建议]
   - 当前状态: [现有情况]
   - 改进方案: [具体改进措施]
   - 优先级: P1

**🟢 优秀实践（值得保持）**:
[列出文档中值得肯定的优秀实践]
1. 优秀点: [具体优秀实践]
   - 模板符合度: [符合程度]
   - 建议: 继续保持

### 5.3 整体评估结论

**符合性结论**:
- ✅/❌ 该实施计划文档是否严格按照《AI严格生成实施计划文档的提示词.md》生成
- ✅/❌ 设计约束是否100%传承到实施计划
- ✅/❌ 实施步骤是否具体可执行
- ✅/❌ 风险控制方案是否完整
- ✅/❌ 质量标准是否达标

**总体建议**:
[基于检查结果的总体改进建议]

**后续行动**:
- 【必须执行】: [列出必须修复的严重问题]
- 【建议执行】: [列出建议改进的事项]
- 【质量确认】: [列出需要进一步确认的质量点]

---

## 🎯 检查执行标准

### 检查执行原则
1. **严格对标**: 严格按照《AI严格生成实施计划文档的提示词.md》模板要求检查
2. **证据导向**: 每个检查结论都必须有具体的文档证据支撑
3. **问题导向**: 重点识别偏离模板要求的问题并提供修复方案
4. **质量优先**: 确保实施计划文档的高质量和可执行性
5. **设计约束优先**: 特别关注设计约束的传承和执行

### 检查工具使用要求
- **必须使用read_file**: 完整读取实施计划文档内容
- **必须使用grep_search**: 搜索关键要素和约束标注
- **建议使用codebase_search**: 理解文档整体结构和上下文

### 检查报告要求
- **具体明确**: 每个问题都要指出具体的缺失内容和位置
- **可操作**: 每个修复方案都要提供具体的操作步骤
- **优先级明确**: 区分严重问题、改进建议和优秀实践
- **量化评估**: 提供具体的评分和等级评定

**请严格按照上述检查流程执行，确保质量检查的全面性和准确性！**
```

---

## 🎯 使用方式

### 标准使用流程
1. **提供实施计划文档路径**: "检查文档: [实施计划文档路径]"
2. **提供检查提示词**: "检查提示词: docs/ai-memory/templates/AI实施计划文档质量检查提示词.md"
3. **AI自动执行检查**: 读取文档 → 逐项检查 → 生成质量报告

### 检查输出示例
```markdown
# 实施计划文档质量检查报告

## 📊 合规性评估结果
- **总分**: 85/100分
- **等级**: 🟡 良好 - 基本符合，有少量改进点

## 🔴 严重问题（必须修复）
1. 缺失设计约束转化环节
   - 检查依据: 模板要求第零步"设计文档深度解析与架构约束提取"
   - 修复方案: 补充"设计文档架构约束转化结果"章节
   - 优先级: P0

## 🟡 改进建议（建议修复）
1. 实施步骤缺少设计约束标注
   - 当前状态: 实施步骤没有标注对应的约束ID
   - 改进方案: 为每个关键步骤添加"约束检查: [约束ID]"
   - 优先级: P1

## 🟢 优秀实践（值得保持）
1. AI认知约束激活完整
   - 模板符合度: 100%符合
   - 建议: 继续保持
```

## 🌟 检查提示词特点

✅ **全面覆盖**: 涵盖结构、内容、约束、质量四大维度  
✅ **严格对标**: 严格按照生成模板的要求进行检查  
✅ **问题导向**: 重点识别问题并提供具体修复方案  
✅ **量化评估**: 提供100分制评分和等级评定  
✅ **证据导向**: 每个检查结论都有具体文档证据  
✅ **可操作性**: 所有修复方案都具体可执行  
✅ **优先级管理**: 区分严重问题、改进建议和优秀实践  
✅ **设计约束专项**: 特别关注设计约束的传承检查

这样就形成了完整的"生成→检查→修复"的质量保证体系！ 