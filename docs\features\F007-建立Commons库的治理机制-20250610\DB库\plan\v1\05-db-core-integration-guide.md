# Commons DB V3: Core项目集成与迁移指南

## 文档元数据

- **文档ID**: `xkongcloud-commons-db-v3-integration-guide`
- **版本**: `V1.0`
- **关联设计**: `xkongcloud-commons-db-v3-pragmatic`
- **状态**: `待实施`

## 1. 核心目标

本指南为所有需要接入 `Commons DB V3` 的下游项目（特别是核心业务项目）提供一套清晰、可操作的集成与迁移方案。旨在帮助开发团队平滑地从旧有数据访问方式过渡到新的统一架构，并充分利用新库提供的能力。

## 2. 迁移前置检查

在开始迁移之前，请确保您的项目满足以下条件：

1.  **Spring Boot 版本**: 项目已升级至 `Spring Boot 3.2` 或更高版本。
2.  **Java 版本**: 项目已使用 `Java 17` 或更高版本。
3.  **依赖清理**: 已移除项目中其他非官方或老旧的数据访问、连接池相关依赖（如 Druid、旧版 `commons-db` 等）。

---

## 3. 迁移步骤详解

### 步骤 1: 更新 Maven/Gradle 依赖

将项目 `pom.xml` 或 `build.gradle` 文件中的旧有DB相关依赖替换为 `commons-db-starter`。

**Maven (`pom.xml`)**: 

```xml
<dependencies>
    <!-- 移除旧的 commons-db, druid, mybatis-plus 等依赖 -->
    <!-- <dependency>
        <groupId>com.xkong.xkongcloud</groupId>
        <artifactId>old-db-library</artifactId>
        <version>...</version>
    </dependency> -->

    <!-- 添加新的 Commons DB V3 Starter -->
    <dependency>
        <groupId>com.xkong.xkongcloud</groupId>
        <artifactId>commons-db-starter</artifactId>
        <version>3.0.0</version> <!-- 请使用最新版本 -->
    </dependency>
</dependencies>
```

### 步骤 2: 更新配置文件 (`application.yml`)

移除旧的数据库连接池配置（如 `spring.datasource.druid.*`），并更新为标准的 `spring.datasource.hikari.*` 配置。同时，可以配置 `Commons DB` 的特定行为。

```yaml
spring:
  datasource:
    url: *******************************************
    username: your-username
    password: your-password
    driver-class-name: org.postgresql.Driver
    hikari:
      # HikariCP 核心配置
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 30000
      connection-timeout: 20000
      max-lifetime: 1800000

xkong:
  commons:
    db:
      # 配置要激活的数据访问层，默认为 querydsl
      # 可选值: 'jpa', 'querydsl', 'jdbc'
      access-layer: querydsl 
      migration:
        # 是否启用 flyway, 默认 true
        enabled: true
      monitoring:
        # 是否启用监控, 默认 true
        enabled: true
```

### 步骤 3: 代码重构 - 拥抱 `DataAccessTemplate`

这是迁移的核心步骤。您需要将原有的 `JpaRepository`, `JdbcTemplate` 或 `MyBatis Mapper` 的使用方式，重构为使用我们统一的 `DataAccessTemplate`。

**重构前 (示例: 使用 Spring Data JPA Repository)**

```java
@Service
public class OldUserService {

    @Autowired
    private UserRepository userRepository;

    public User findUserById(Long id) {
        return userRepository.findById(id).orElse(null);
    }

    public User saveUser(User user) {
        return userRepository.save(user);
    }
}
```

**重构后 (示例: 使用 `DataAccessTemplate`)**

```java
@Service
public class NewUserService {

    // 注入统一的 DataAccessTemplate
    @Autowired
    private DataAccessTemplate<User, Long> userTemplate;

    public Optional<User> findUserById(Long id) {
        return userTemplate.findById(id);
    }

    public User saveUser(User user) {
        return userTemplate.save(user);
    }
}
```

**如何为特定实体注入 `DataAccessTemplate`?**

`CommonsDbAutoConfiguration` 会自动为您配置好泛型注入。您只需要在业务类中直接 `@Autowired` 即可。

```java
// 在Service或Component中直接注入
@Autowired
private DataAccessTemplate<Product, String> productTemplate;

@Autowired
private DataAccessTemplate<Order, Long> orderTemplate;
```

### 步骤 4: 处理复杂查询

对于复杂的动态查询，推荐使用 `Querydsl`。`access-layer` 配置为 `querydsl` 时，注入的 `DataAccessTemplate` 底层即为 `QuerydslDataAccessTemplate`。

**重构前 (示例: 使用 Specification)**

```java
// ...
Specification<User> spec = (root, query, cb) -> {
    return cb.and(
        cb.equal(root.get("status"), "ACTIVE"),
        cb.like(root.get("email"), "%@xkong.com")
    );
};
List<User> users = userRepository.findAll(spec);
// ...
```

**重构后 (示例: 使用 Querydsl Predicate)**

```java
// 注入 Querydsl 的 JPAQueryFactory
@Autowired
private JPAQueryFactory queryFactory;

// ...
QUser user = QUser.user;
Predicate predicate = user.status.eq("ACTIVE").and(user.email.endsWith("@xkong.com"));

// 直接使用 queryFactory 执行查询
List<User> users = queryFactory.selectFrom(user).where(predicate).fetch();
// ...
```

> **注意**: 为简化迁移，`DataAccessTemplate` 也支持 `Specification` 查询。但对于新功能，我们强烈建议使用类型更安全的 `Querydsl`。

## 4. 迁移验证

1.  **单元测试**: 确保所有重构后的 Service 和 Repository 层都有对应的单元测试覆盖。
2.  **集成测试**: 编写集成测试，启动完整的 Spring 上下文，验证数据库操作的正确性。
3.  **功能回归**: 在测试环境中，对涉及迁移的业务功能进行全面的回归测试。

---
