---
title: UID库组件依赖图
description: 展示xkongcloud-commons-uid库主要组件之间的依赖关系
created_date: 2025-05-18
updated_date: 2025-05-18
version: 1.0
status: 草稿
author: AI助手
---

# UID库组件依赖图

此图展示了xkongcloud-commons-uid库中主要组件之间的依赖关系。

```mermaid
graph TD
    subgraph 应用程序
        App[应用程序]
        Config[UidGeneratorConfig]
    end
    
    subgraph xkongcloud-commons-uid
        UG[UidGenerator]
        PIWA[PersistentInstanceWorkerIdAssigner]
        PIM[PersistentInstanceManager]
        PIMB[PersistentInstanceManagerBuilder]
        KMS[KeyManagementService]
        MF[MachineFingerprints]
        VRC[ValidationResultCache]
        UVU[UidValidationUtils]
        UTM[UidTableManager]
        WNT[WorkerNodeType]
    end
    
    subgraph 外部依赖
        DB[(PostgreSQL数据库)]
        FS[文件系统]
        Spring[Spring框架]
    end
    
    %% 应用程序与UID库的关系
    App -->|使用| Config
    Config -->|创建| UG
    Config -->|创建| PIWA
    Config -->|创建| PIM
    Config -->|创建| KMS
    Config -->|创建| VRC
    
    %% UID库内部组件关系
    UG -->|依赖| PIWA
    PIWA -->|依赖| PIM
    PIM -->|使用| MF
    PIM -->|使用| KMS
    PIMB -->|构建| PIM
    PIM -->|使用| UVU
    PIWA -->|使用| UVU
    KMS -->|使用| UVU
    UVU -->|使用| VRC
    UTM -->|使用| UVU
    
    %% 与外部依赖的关系
    PIM -->|读写| DB
    PIWA -->|读写| DB
    KMS -->|读写| DB
    PIM -->|读写| FS
    UVU -->|查询| DB
    UTM -->|管理表结构| DB
    Config -->|依赖| Spring
    
    %% 样式设置
    classDef appComponents fill:#222,stroke:#05a,stroke-width:2px;
    classDef uidComponents fill:#222,stroke:#d79b00,stroke-width:2px;
    classDef externalDeps fill:#222,stroke:#9673a6,stroke-width:2px;
    
    class App,Config appComponents;
    class UG,PIWA,PIM,PIMB,KMS,MF,VRC,UVU,UTM,WNT uidComponents;
    class DB,FS,Spring externalDeps;
```
