# 01号设计文档编写核心规范 (V2.0)

## 1. 引言：为何需要本规范

本文档是为V4.1自动化治理引擎定义的**最高层级设计文档（即`01号`文档）的编写标准**。

`01号`文档是整个自动化流程的“真理之源”，其内容的**质量、结构和明确性**直接决定了后续所有阶段（从约束提取、风险分析到代码生成）的成败。一个格式混乱、语义模糊的`01号`文档，是“垃圾输入，垃圾输出”的根源。

本规范旨在确保所有`01号`文档都以一种**对机器友好、对人类清晰**的方式编写，从而最大化自动化治理的效率和准确性。

**核心原则：结构化优于非结构化，明确优于模糊。**

---

## 2. 强制性文档结构

每一份`01号`设计文档**必须**包含以下二级标题（`##`）定义的章节，且**必须**遵循此顺序。

```
## 1. 文档元数据
## 2. 核心定位与设计哲学
## 3. 全局约束与规则
## 4. 宏观架构视图
## 5. 核心流程与交互
## 6. 边界条件与核心状态机
## 7. 排除范围
## 8. 未来演进方向
```

---

## 3. 详细章节规范

本章节将详细定义每个强制性章节的具体要求。

### 3.1. `## 1. 文档元数据`

-   **目的**: 提供文档的基本身份信息，便于追踪和管理。
-   **格式**: **必须**使用无序列表或表格。
-   **内容规则**:
    -   **必须包含**：`文档ID`, `版本`, `创建日期`, `状态` (如：设计稿, 评审中, 已批准), `技术栈`。
    -   **不能包含**：任何与元数据无关的描述性文字。

-   **正面示例 (✔)**:
    ```markdown
    - **版本**: `V1.0`
    - **状态**: `设计稿`
    - **技术栈**: `Java 21, Spring Boot 3.4.5, Virtual Threads`
    ```

-   **反面示例 (❌)**:
    ```markdown
    本文档是Nexus项目的第一个版本，技术栈我们选了最新的Java。
    ```
    *（错误：非结构化，信息难以提取）*

### 3.2. `## 2. 核心定位与设计哲学`

-   **目的**: 定义项目的“灵魂”，即它是什么，不是什么，以及最高指导原则。
-   **格式**: **必须**使用三级标题（`###`）来组织内容。
-   **内容规则**:
    -   **必须包含** `### 核心定位` 和 `### 核心设计原则` 两个子章节。
    -   `核心定位` **必须**用1-2段话清晰描述项目的核心价值和目标。
    -   `核心设计原则` **必须**以**加粗的短语**作为标题，后跟解释的列表形式呈现。
    -   **不能包含**：具体的实现细节或技术约束。

-   **正面示例 (✔)**:
    ```markdown
    ### 核心设计原则
    1.  **"组合优化" (Combination Optimization)**: Nexus不仅是一个插件容器，更是一个**能力组合平台**。
    2.  **"内置电池，并提供逃生舱口" (Batteries-included, with escape hatches)**: 为80%的场景提供一个高度自动化的环境。
    ```

-   **反面示例 (❌)**:
    ```markdown
    ### 设计理念
    我们认为应该把内核做小，插件之间的通信要解耦，而且要用异步事件。我们不应该在内核里写业务逻辑，这不符合单一职责。
    ```
    *（错误：未将原则提炼为明确的、可引用的标题，所有思想混杂在一起。）*

### 3.3. `## 3. 全局约束与规则`

-   **目的**: 定义整个项目必须遵守的、非黑即白的硬性规则。这是算法进行确定性预验证的核心输入。
-   **格式**: **必须**使用三级标题（`###`）按类别组织，每个类别下**必须**使用列表或表格。
-   **内容规则**:
    -   **必须**将约束按类别（如：`### 强制性技术约束`, `### 性能指标约束`）分开。
    -   所有约束**必须**是可量化的、可验证的。
    -   **不能包含**：“应该”、“可能”、“尽量”等模糊词汇。

-   **正面示例 (✔)**:
    ```markdown
    ### ⚡ 性能指标约束
    - **框架启动时间**: ≤1000ms
    - **并发处理能力**: ≥10,000 events/second
    ```

-   **反面示例 (❌)**:
    ```markdown
    ### 性能要求
    - 启动速度要快，最好一秒内。
    - 并发能力要强。
    ```
    *（错误：未量化，无可操作性，算法无法解析。）*

### 3.4. `## 4. 宏观架构视图`

-   **目的**: 展示系统最高层级的组件/模块及其相互关系。这是结构性分析（如循环依赖检测）的**最关键输入**。
-   **格式**: **强烈推荐**使用Mermaid语法。
-   **内容规则**:
    -   **必须包含**至少一张**依赖关系图 (Flowchart/Graph)**，用以展示核心模块/组件的静态依赖关系。节点名称**必须**与后续实现中的模块名或关键类名保持一致。
    -   **推荐包含**一张**分层架构图 (Layered Architecture)**，展示系统的垂直分层。
    -   **不能**仅用文字描述架构。如果必须用文字，也要在段落后附上总结性的依赖关系列表。

-   **正面示例 (✔)**:
    ```markdown
    ### 核心模块架构
    ```mermaid
    graph TB
        NexusStarter --> NexusKernel
        NexusStarter --> NexusServiceBus
        NexusKernel --> NexusAPI
        NexusServiceBus --> NexusAPI
    ```
    ```

-   **反面示例 (❌)**:
    ```markdown
    ### 我们的架构
    我们的启动器模块会用到内核和服务总线，然后内核和服务总线都定义了一些接口，放在API模块里。
    ```
    *（错误：非结构化，算法提取关系困难且易错。AI可以尝试提取，但风险和成本远高于直接解析图表。）*

### 3.5. `## 5. 核心流程与交互`

-   **目的**: 描述关键场景下，核心组件之间按时间顺序的动态交互。
-   **格式**: **强烈推荐**使用**序列图 (Sequence Diagram)**。
-   **内容规则**:
    -   **必须**覆盖1-3个最核心的业务场景（如：用户登录、订单创建、插件加载等）。
    -   **必须**明确标出参与交互的组件（`participant`）。
    -   **推荐**使用 `->>` 表示异步消息，`->` 表示同步调用。

-   **正面示例 (✔)**:
    ```markdown
    ### 通信协议架构
    ```mermaid
    sequenceDiagram
        participant App as Application
        participant Bus as Service Bus
        App->>Bus: publish(OrderCreatedEvent)
        Bus-->>App: ack
    ```
    ```

-   **反面示例 (❌)**:
    ```markdown
    应用会发一个订单创建事件给服务总线，然后总线收到后会异步处理，处理完就完事了。
    ```
    *（错误：流程描述不清，参与者不明确，调用关系模糊。）*

### 3.6. `## 6. 边界条件与核心状态机`

-   **章节要求**: **强制性 (Mandatory)**。每一份`01号`设计文档**都必须包含**此章节。
-   **目的**: 强制设计者明确定义模块在处理“正常”与“异常”、“边界内”与“边界外”输入时的行为，并清晰地描述其核心实体的生命周期和状态转换。这是消除“隐性漏点”的关键章节。

#### 7.1. 关键参数与临界值分析
-   **子章节要求**: **强制性 (Mandatory)**。
-   **内容要求**: 设计者**必须**在此提供临界值分析表。如果模块确实没有任何值得分析的临界值，则**必须**在此处明确写下：“**本模块无复杂的临界值或资源限制，不适用此分析。**”
-   **算法检查**: 算法会检查`### 7.1.`是否存在且内容不为空。
-   **格式**: **必须**使用表格。

-   **正面示例 (✔)**:
    ```markdown
    | 关键参数 | 正常范围 | 临界值 | 达到临界值时的行为 |
    | :--- | :--- | :--- | :--- |
    | `request_per_second` | 0-1000 | > 1000 | 触发限流，返回 `HTTP 429 Too Many Requests` |
    | `cache_memory_usage` | 0-512MB | > 512MB | 触发LRU淘汰机制，清理20%的冷数据 |
    ```

#### 7.2. 核心实体状态机
-   **子章节要求**: **条件性强制 (Conditional)**。
-   **内容要求**: **如果**模块的设计中包含一个具有多个状态的核心业务实体（如订单、任务等），则**必须**在此处提供状态机图或描述。如果模块不涉及此类实体，则**必须**在此处明确写下：“**本模块不包含多状态的核心实体，不适用此分析。**”
-   **算法检查**: 算法会检查`### 7.2.`是否存在且内容不为空。
-   **格式**: **强烈推荐**使用Mermaid的`stateDiagram`语法。

-   **正面示例 (✔)**:
    ```markdown
    ```mermaid
    stateDiagram-v2
        [*] --> PENDING_PAYMENT : 创建订单
        PENDING_PAYMENT --> PAID : 支付成功
        PENDING_PAYMENT --> CANCELLED : 超时未支付
    ```
    ```

### 3.7. `## 7. 排除范围`

-   **目的**: 明确定义本项目**不做**什么，防止范围蔓延。
-   **格式**: **必须**使用无序列表。
-   **内容规则**:
    -   每一项**必须**清晰、明确。
    -   **不能**使用含糊不清的描述。

-   **正面示例 (✔)**:
    ```markdown
    - **具体业务逻辑**: 不包含任何特定业务领域的实现。
    - **UI界面组件**: 纯后端框架，不提供前端界面。
    ```

-   **反面示例 (❌)**:
    ```markdown
    - 我们主要做后端，UI先不管。
    - 业务逻辑太复杂了，这个项目不考虑。
    ```
    *（错误：口语化，不专业，定义不清晰。）*

### 3.8. `## 8. 未来演进方向`

-   **目的**: 简要阐述项目可能的长期发展方向或计划中的重大升级。
-   **格式**: 无序列表或简短段落。
-   **内容规则**: 此章节为方向性描述，非硬性承诺。

---

## 4. 附录：总结

遵循本规范编写`01号`设计文档，将为V4.1自动化治理引擎提供最优质的“燃料”，从而实现从设计到代码的精准、高效、可靠的自动化流程。一份好的`01号`文档，本身就是项目成功的一半。
