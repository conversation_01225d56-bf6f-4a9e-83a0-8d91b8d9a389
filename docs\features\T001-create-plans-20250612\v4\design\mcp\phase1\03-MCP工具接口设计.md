# V4 MCP Server第一阶段工具接口设计

## 📋 文档概述

**文档ID**: V4-MCP-PHASE1-TOOLS-INTERFACE-003
**创建日期**: 2025-06-18
**版本**: F007-mcp-phase1-v1.0.L1.2.0
**目标**: 定义第一阶段MCP工具接口，确保IDE AI精准控制和第二阶段复用
**模板引用**: @TEMPLATE_REF:../核心/V4架构信息AI填充模板.md#接口设计规范

## 🔧 MCP工具接口定义

### 1. 主要工具接口
```python
# tools/ace/mcp/v4_context_guidance_server/tools/phase1_tools.py
# 基于标准MCP协议和V4设计模式

@mcp_tool
def execute_checkresult_v4_modification_task(checkresult_path: str) -> Dict:
    """
    执行checkresult-v4修改任务

    这是第一阶段的核心工具，IDE AI通过此工具启动修改任务
    支持灵活的目录参数输入，自动解析和验证路径

    Args:
        checkresult_path: checkresult-v4目录的路径（支持相对路径和绝对路径）

    Returns:
        Dict: 任务初始化结果和第一批修改指令

    Examples:
        IDE AI可以使用以下任意格式调用:
        1. "使用ace mcp指令，执行修改（docs\\features\\F007-建立Commons库的治理机制-20250610\\nexus万用插座\\design\\v1\\checkresult-v4）的任务"
        2. "使用ace mcp，执行修改（docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/checkresult-v4）"
        3. 直接传入路径: "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/checkresult-v4"
    """
    
    server = V4ContextGuidanceServer()

    # 1. 路径预处理和验证
    normalized_path = _normalize_and_validate_checkresult_path(checkresult_path)

    if "error" in normalized_path:
        return normalized_path

    # 2. 初始化任务（复用V4任务初始化模式）
    init_result = server.initialize_modification_task(normalized_path["validated_path"])
    
    if init_result["remaining_modifications"] == 0:
        return {
            "status": "task_already_completed",
            "message": "✅ 所有修改已完成",
            "summary": init_result
        }
    
    # 2. 获取第一批修改指令
    first_batch = server.get_next_modification_batch()
    
    return {
        "status": "task_initialized",
        "message": f"📋 任务已初始化，准备执行 {len(first_batch['modifications'])} 个修改",
        "task_summary": init_result,
        "current_batch": first_batch,
        "ide_ai_instructions": _format_ide_ai_instructions(first_batch["modifications"])
    }

@mcp_tool
def get_next_modification_batch(checkresult_path: str, batch_size: int = 3) -> Dict:
    """
    获取下一批修改指令
    
    支持MCP断线重连，IDE AI可以随时调用此工具继续任务
    
    Args:
        checkresult_path: checkresult-v4目录路径
        batch_size: 批次大小，默认3个修改
        
    Returns:
        Dict: 下一批修改指令或任务完成状态
    """
    
    progress_file = os.path.join(checkresult_path, "mcp_progress_tracker.json")
    
    if not os.path.exists(progress_file):
        return {
            "error": "❌ 任务未初始化",
            "suggestion": "请先调用 execute_checkresult_v4_modification_task 初始化任务"
        }
    
    # 加载进度（支持断线重连）
    tracker = ProgressTracker()
    tracker.initialize_or_load(progress_file, [])
    
    # 开始新会话（记录断线重连）
    session_id = tracker.start_new_session()
    
    next_batch = tracker.get_next_batch(batch_size)
    
    if not next_batch:
        return {
            "status": "all_completed",
            "message": "🎉 所有修改已完成！",
            "final_summary": tracker.get_progress_summary(),
            "session_id": session_id
        }
    
    return {
        "status": "batch_ready",
        "message": f"📝 准备执行第 {tracker.progress_data['current_batch']} 批修改",
        "modifications": next_batch,
        "batch_info": {
            "batch_number": tracker.progress_data["current_batch"],
            "batch_size": len(next_batch),
            "remaining_after_batch": tracker.get_progress_summary()["remaining_count"] - len(next_batch)
        },
        "progress_summary": tracker.get_progress_summary(),
        "session_id": session_id,
        "ide_ai_instructions": _format_ide_ai_instructions(next_batch)
    }

@mcp_tool
def validate_and_continue_modifications(
    checkresult_path: str, 
    completed_modifications: List[Dict],
    session_id: str = None
) -> Dict:
    """
    验证修改结果并继续下一批
    
    IDE AI完成修改后调用此工具进行验证和获取下一批指令
    
    Args:
        checkresult_path: checkresult-v4目录路径
        completed_modifications: 已完成的修改列表
        session_id: 会话ID（可选，用于跟踪）
        
    Returns:
        Dict: 验证结果和下一批修改指令
    """
    
    progress_file = os.path.join(checkresult_path, "mcp_progress_tracker.json")
    tracker = ProgressTracker()
    tracker.initialize_or_load(progress_file, [])
    
    # 验证修改结果（基于V4验证机制）
    validator = ModificationValidator()
    validation_results = []
    
    for mod in completed_modifications:
        result = validator.validate_modification(mod)
        validation_results.append(result)
    
    # 更新进度
    tracker.mark_batch_completed(completed_modifications, validation_results)
    
    # 统计验证结果
    successful_count = sum(1 for r in validation_results if r["success"])
    failed_count = len(validation_results) - successful_count
    
    # 获取下一批
    next_batch = tracker.get_next_batch()
    
    if not next_batch:
        return {
            "status": "task_completed",
            "message": "🎉 所有修改已完成！",
            "final_summary": tracker.get_progress_summary(),
            "validation_summary": {
                "total_validated": len(validation_results),
                "successful": successful_count,
                "failed": failed_count,
                "success_rate": f"{(successful_count/len(validation_results)*100):.1f}%" if validation_results else "0%"
            },
            "session_id": session_id
        }
    
    return {
        "status": "batch_completed_continue",
        "message": f"✅ 当前批次已完成，准备下一批 {len(next_batch)} 个修改",
        "validation_summary": {
            "total_validated": len(validation_results),
            "successful": successful_count,
            "failed": failed_count,
            "success_rate": f"{(successful_count/len(validation_results)*100):.1f}%" if validation_results else "0%"
        },
        "next_batch": {
            "modifications": next_batch,
            "batch_number": tracker.progress_data["current_batch"],
            "batch_size": len(next_batch)
        },
        "progress_summary": tracker.get_progress_summary(),
        "session_id": session_id,
        "ide_ai_instructions": _format_ide_ai_instructions(next_batch)
    }
```

### 2. 辅助工具接口
```python
@mcp_tool
def get_modification_progress(checkresult_path: str) -> Dict:
    """
    获取修改进度信息
    
    IDE AI可以随时查询当前任务进度
    """
    
    progress_file = os.path.join(checkresult_path, "mcp_progress_tracker.json")
    
    if not os.path.exists(progress_file):
        return {
            "error": "任务未初始化",
            "suggestion": "请先调用 execute_checkresult_v4_modification_task"
        }
    
    tracker = ProgressTracker()
    tracker.initialize_or_load(progress_file, [])
    
    return {
        "status": "progress_retrieved",
        "progress": tracker.get_progress_summary(),
        "detailed_status": tracker.get_detailed_status()
    }

@mcp_tool
def reset_modification_task(checkresult_path: str, confirm: bool = False) -> Dict:
    """
    重置修改任务
    
    在需要重新开始任务时使用（需要确认）
    """
    
    if not confirm:
        return {
            "error": "需要确认重置操作",
            "message": "重置将清除所有进度，请设置 confirm=True 确认操作"
        }
    
    progress_file = os.path.join(checkresult_path, "mcp_progress_tracker.json")
    
    if os.path.exists(progress_file):
        # 备份现有进度
        backup_file = f"{progress_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(progress_file, backup_file)
        
        # 删除进度文件
        os.remove(progress_file)
        
        return {
            "status": "task_reset",
            "message": "任务已重置",
            "backup_file": backup_file
        }
    else:
        return {
            "status": "no_task_found",
            "message": "没有找到需要重置的任务"
        }
```

## 📋 IDE AI指令格式化

### 指令格式化函数
```python
def _format_ide_ai_instructions(modifications: List[Dict]) -> str:
    """
    为IDE AI格式化精确的修改指令
    基于V4精准指导原则
    """
    
    if not modifications:
        return "没有需要执行的修改。"
    
    instructions = "## 🎯 精确修改指令\n\n"
    instructions += "请严格按照以下指令执行修改，不要偏离指定范围：\n\n"
    
    for i, mod in enumerate(modifications, 1):
        instructions += f"### 修改 {i}: {mod.get('description', '未知修改')}\n"
        instructions += f"**文件路径**: `{mod['file_path']}`\n"
        
        if mod.get('line_number'):
            instructions += f"**目标行号**: {mod['line_number']}\n"
        
        instructions += f"**操作类型**: {mod.get('action', 'replace')}\n"
        
        if mod.get('find_text'):
            instructions += f"**查找内容**: `{mod['find_text']}`\n"
        
        if mod.get('replace_text'):
            instructions += f"**替换为**: `{mod['replace_text']}`\n"
        
        if mod.get('context'):
            instructions += f"**上下文**: {mod['context']}\n"
        
        instructions += f"**置信度**: {mod.get('confidence', 0.9)}\n"
        instructions += f"**修改ID**: {mod['id']}\n\n"
    
    instructions += "## ⚠️ 重要提醒\n\n"
    instructions += "1. **严格边界控制**: 只修改指定的内容，不要添加、删除或修改其他内容\n"
    instructions += "2. **精确匹配**: 查找内容必须完全匹配才能替换\n"
    instructions += "3. **保持格式**: 保持原有的缩进、换行和格式\n"
    instructions += "4. **完成确认**: 修改完成后，请调用 `validate_and_continue_modifications` 工具\n"
    instructions += "5. **错误处理**: 如果遇到无法修改的情况，请在结果中详细说明\n\n"
    
    instructions += "## 📞 完成后调用\n\n"
    instructions += "```python\n"
    instructions += "validate_and_continue_modifications(\n"
    instructions += f"    checkresult_path=\"{modifications[0].get('checkresult_path', 'CHECKRESULT_PATH')}\",\n"
    instructions += "    completed_modifications=[\n"
    instructions += "        # 请填入实际完成的修改结果\n"
    instructions += "    ]\n"
    instructions += ")\n"
    instructions += "```\n"
    
    return instructions


def _normalize_and_validate_checkresult_path(checkresult_path: str) -> Dict:
    """
    标准化和验证checkresult路径

    支持多种输入格式：
    1. 完整路径: "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/checkresult-v4"
    2. 带括号的路径: "（docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/checkresult-v4）"
    3. 相对路径和绝对路径

    Args:
        checkresult_path: 用户输入的路径

    Returns:
        Dict: 验证结果，包含validated_path或error信息
    """

    import os
    import re

    try:
        # 1. 清理路径字符串
        cleaned_path = checkresult_path.strip()

        # 移除可能的括号
        if cleaned_path.startswith('（') and cleaned_path.endswith('）'):
            cleaned_path = cleaned_path[1:-1]
        elif cleaned_path.startswith('(') and cleaned_path.endswith(')'):
            cleaned_path = cleaned_path[1:-1]

        # 移除引号
        cleaned_path = cleaned_path.strip('"\'')

        # 2. 路径格式标准化
        # 统一使用正斜杠
        normalized_path = cleaned_path.replace('\\', '/')

        # 3. 验证路径是否存在
        if not os.path.exists(normalized_path):
            # 尝试相对于当前工作目录
            if not os.path.isabs(normalized_path):
                abs_path = os.path.abspath(normalized_path)
                if os.path.exists(abs_path):
                    normalized_path = abs_path
                else:
                    return {
                        "error": f"路径不存在: {normalized_path}",
                        "suggestion": "请检查路径是否正确，确保checkresult-v4目录存在",
                        "original_input": checkresult_path
                    }

        # 4. 验证是否为checkresult目录
        if not normalized_path.endswith('checkresult-v4') and 'checkresult' not in normalized_path:
            return {
                "error": f"路径似乎不是checkresult目录: {normalized_path}",
                "suggestion": "请确保路径指向checkresult-v4目录",
                "original_input": checkresult_path
            }

        # 5. 验证目录内容
        if not os.path.isdir(normalized_path):
            return {
                "error": f"路径不是目录: {normalized_path}",
                "suggestion": "请确保路径指向一个目录而不是文件",
                "original_input": checkresult_path
            }

        # 检查是否包含检查报告文件
        import glob
        report_files = glob.glob(os.path.join(normalized_path, "*_检查报告.md"))

        if not report_files:
            return {
                "error": f"目录中没有找到检查报告文件: {normalized_path}",
                "suggestion": "请确保目录中包含*_检查报告.md文件",
                "original_input": checkresult_path
            }

        return {
            "validated_path": normalized_path,
            "original_input": checkresult_path,
            "found_reports": len(report_files),
            "report_files": [os.path.basename(f) for f in report_files]
        }

    except Exception as e:
        return {
            "error": f"路径验证失败: {str(e)}",
            "suggestion": "请检查路径格式是否正确",
            "original_input": checkresult_path
        }
```

---

**创建时间**: 2025-06-18
**维护说明**: 基于MCP协议标准和V4设计原则，确保接口的稳定性和可扩展性
