---
title: UID初始化时序图
description: 展示xkongcloud-commons-uid库初始化过程的时序图
created_date: 2025-05-18
updated_date: 2025-05-18
version: 1.0
status: 草稿
author: AI助手
---

# UID初始化时序图

此时序图展示了xkongcloud-commons-uid库在应用启动时的初始化过程，包括组件创建、验证和实例身份管理。

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant Config as UidGeneratorConfig
    participant VRC as ValidationResultCache
    participant K<PERSON> as KeyManagementService
    participant PIMB as PersistentInstanceManagerBuilder
    participant PIM as PersistentInstanceManager
    participant MF as MachineFingerprints
    participant DB as 数据库(PostgreSQL)
    participant FS as 文件系统
    participant PIWA as PersistentInstanceWorkerIdAssigner
    participant UG as UidGenerator

    App->>Config: 应用启动
    Config->>VRC: 创建ValidationResultCache
    Config->>KMS: 创建KeyManagementService
    KMS->>DB: 检查加密密钥表
    DB-->>KMS: 返回加密状态
    
    Config->>PIMB: 使用构建器模式创建PersistentInstanceManager
    PIMB->>PIMB: 设置必要参数(applicationName, environment等)
    PIMB->>PIM: 构建PersistentInstanceManager
    
    PIM->>FS: 尝试加载本地实例ID
    alt 本地实例ID存在
        FS-->>PIM: 返回实例ID
        PIM->>DB: 验证实例ID有效性
        DB-->>PIM: 返回验证结果
    else 本地实例ID不存在或无效
        PIM->>MF: 收集机器特征码
        MF-->>PIM: 返回特征码集合
        PIM->>DB: 查询匹配的实例
        DB-->>PIM: 返回匹配结果
        alt 找到高置信度匹配
            PIM->>PIM: 恢复实例身份
        else 未找到匹配或置信度低
            PIM->>DB: 注册新实例
            DB-->>PIM: 返回新实例ID
        end
        PIM->>FS: 保存实例ID到本地
    end
    
    Config->>PIWA: 创建PersistentInstanceWorkerIdAssigner
    PIWA->>PIM: 获取实例ID
    PIM-->>PIWA: 返回实例ID
    PIWA->>DB: 分配WorkerID
    DB-->>PIWA: 返回分配结果
    
    Config->>UG: 创建UidGenerator
    UG->>PIWA: 获取WorkerID
    PIWA-->>UG: 返回WorkerID
    
    App->>UG: 初始化完成，可以生成UID
```
