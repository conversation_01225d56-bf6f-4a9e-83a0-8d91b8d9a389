# Spring Boot测试隔离模式标准库

## 核心模式

### 1. 专用TestApplication模式
**问题**：Spring Boot自动扫描主应用类导致配置冲突  
**解决方案**：创建专用测试启动类完全替代主应用类

```java
@SpringBootConfiguration
@EnableAutoConfiguration(exclude = {DataSourceAutoConfiguration.class, ...})
@ComponentScan(excludeFilters = {@ComponentScan.Filter(...)})
public class TestApplication {}
```

**核心价值**：完全隔离测试环境与生产环境

### 2. IsolatedTestConfiguration模式
**问题**：测试需要独立的基础设施配置  
**解决方案**：提供完整的测试专用配置

```java
@TestConfiguration
public class IsolatedTestConfiguration {
    @Bean @Primary
    public DataSource testDataSource() { /* H2 PostgreSQL兼容模式 */ }
    
    @MockBean
    private ExternalService externalService; // Mock所有外部依赖
}
```

**核心价值**：提供完整独立的测试基础设施

### 3. 精确excludeFilters模式
**问题**：生产配置类被意外加载  
**解决方案**：精确排除所有冲突的配置类

```java
excludeFilters = {
    @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {
        MainApplication.class,
        ProductionConfig.class,
        ConflictingTestConfig.class
    })
}
```

**核心价值**：精确控制配置加载范围

## 技术要点

### H2 PostgreSQL兼容配置
```java
config.setJdbcUrl("jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;MODE=PostgreSQL");
```

### 事务管理
```java
@Test
@Transactional  // 确保测试数据一致性
public void testMethod() {}
```

### Mock策略
```java
@MockBean  // Spring容器级别Mock
private ExternalService service;
```

## 验证标准

✅ **配置隔离**：无Bean定义冲突  
✅ **外部依赖Mock**：无真实连接尝试  
✅ **数据库兼容**：H2 PostgreSQL模式正常  
✅ **事务支持**：数据操作一致性  
✅ **环境独立**：测试与生产完全隔离

## 常见错误模式

| 错误 | 原因 | 解决方案 |
|------|------|----------|
| `more than one 'primary' bean found` | 多个@Primary Bean冲突 | 使用excludeFilters排除生产配置 |
| `Connection refused` | 真实外部连接 | 使用@MockBean Mock外部服务 |
| `H2 SQL syntax error` | SQL语法不兼容 | 使用H2兼容语法 |
| `EmptyResultDataAccessException` | 事务数据不可见 | 添加@Transactional注解 |

## 最佳实践

1. **根本性解决**：使用专用TestApplication，不是临时修复
2. **完全隔离**：测试配置与生产配置零依赖
3. **标准化**：符合Spring Boot官方测试规范
4. **可复用**：建立标准模式供其他测试使用

## 成功案例

**项目**：XKongCloud PostgreSQL迁移第3阶段  
**问题**：主应用类自动扫描导致配置冲突  
**解决方案**：TestApplication + IsolatedTestConfiguration  
**结果**：6个测试100%通过，完全配置隔离

## 参考标准

- [Spring Boot官方测试文档](https://docs.spring.io/spring-boot/reference/testing/)
- [Spring Boot配置排除指南](https://docs.spring.io/spring-boot/reference/testing/spring-boot-applications.html#testing.spring-boot-applications.excluding-configuration)
- [AI记忆库：testing-tasks.json](docs/ai-memory/L2-context/task-types/testing-tasks.json)

---
**标准库版本**: 1.0  
**创建时间**: 2025-01-16  
**验证状态**: ✅ 生产验证通过
