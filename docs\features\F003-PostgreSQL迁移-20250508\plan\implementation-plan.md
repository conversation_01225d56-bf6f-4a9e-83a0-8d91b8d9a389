---
title: PostgreSQL迁移实施计划
document_id: F003-PLAN-001
document_type: 实现文档
category: 数据库迁移
scope: XKC-CORE
keywords: [PostgreSQL, 数据库迁移, <PERSON>, 实施计划, 迁移步骤, 持久化实例ID, 特征码恢复, <PERSON>hem<PERSON>命名规范]
created_date: 2025-05-08
updated_date: 2025-06-13
status: 已批准
version: 2.6
authors: [系统架构组, AI助手]
affected_features:
  - F003
  - F004 # CommonsUidLibrary
related_docs:
  - ../requirements/migration-requirements.md
  - ../design/migration-design.md
  - ../../../plans/2-PostgreSQL/postgresql_migration_plan.md
  - ../../../common/middleware/postgresql/integration-guide.md
  - ../../../common/middleware/integration/baidu-uid-generator-postgresql-implementation.md
  - ../../../common/middleware/integration/postgresql-persistent-id-fingerprint-recovery.md
  - ../../../features/F004-CommonsUidLibrary-20250511/design/commons-uid-library-design.md
  - ../../../features/F004-CommonsUidLibrary-20250511/plan/implementation-plan.md
---

# PostgreSQL迁移实施计划

## 实现概述

本文档描述了将XKC-CORE项目中的数据库从Cassandra迁移到PostgreSQL的实施计划。实施计划包括准备工作、实施步骤、测试计划和回滚计划等方面。本计划旨在确保迁移过程平稳、有序，并最小化对系统运行的影响。

**特别说明**：由于项目当前处于初始阶段，Cassandra数据库中尚未包含任何生产环境的业务数据，应用程序中也尚未实现依赖于特定数据库的复杂业务逻辑，因此本次"迁移"实质上是一次技术栈的切换和新数据库的初始化，而非传统意义上涉及复杂数据迁移和业务逻辑兼容性调整的迁移。这意味着：

- 不需要进行数据迁移和转换
- 不需要进行实体类转换和适配
- 不需要考虑Cassandra和PostgreSQL之间的数据类型兼容性
- 不需要实施双写双读等过渡期策略

本实施计划将基于这一特殊情况，专注于新数据库环境的搭建、配置和初始化，而非传统的数据迁移工作。

## 实施顺序与依赖关系

本实施计划与xkongcloud-commons-uid公共库（F004）的实施计划密切相关。以下是两个计划的实施顺序和依赖关系：

### 实施顺序

1. **第一阶段：PostgreSQL基础设施准备（F003，第1周）**
   - 安装PostgreSQL 17.4
   - 创建数据库用户和数据库
   - 配置数据库访问权限
   - 在xkongcloud-service-center中配置PostgreSQL相关KV参数

2. **第二阶段：xkongcloud-commons-uid公共库开发（F004，第1-2周）**
   - 创建xkongcloud-commons父模块和xkongcloud-commons-uid子模块
   - 实现WorkerNodeType枚举、MachineFingerprints类、PersistentInstanceManager类和PersistentInstanceWorkerIdAssigner类
   - 编写单元测试和文档

3. **第三阶段：PostgreSQL依赖调整和配置（F003，第2周）**
   - 修改pom.xml，添加Spring Data JPA和PostgreSQL相关依赖
   - 添加对xkongcloud-commons-uid的依赖
   - 创建PostgreSQLConfig类和UidGeneratorConfig类

4. **第四阶段：演进架构基础实施（F003，第3周）**
   - 建立服务抽象层基础（ServiceInterface注解、DataAccessService接口）
   - 实现配置驱动机制（ServiceConfiguration类、架构模式枚举）
   - 创建Schema演进管理器（PostgreSQLSchemaEvolutionManager）
   - 实现用户管理服务的演进架构示例

5. **第五阶段：实体类和仓库接口设计（F003，第4-5周）**
   - 设计JPA实体类（集成演进架构抽象层）
   - 设计JpaRepository接口
   - 实现数据初始化逻辑

6. **第六阶段：集成测试（F003和F004，第6周）**
   - 测试xkongcloud-business-internal-core与xkongcloud-commons-uid的集成
   - 测试PostgreSQL数据库功能
   - 验证UID生成功能
   - 测试演进架构基础设施

### 依赖关系

- **F003依赖F004**：PostgreSQL迁移计划依赖于xkongcloud-commons-uid公共库，因为它需要使用公共库中的UID生成器组件。
- **F004依赖F003的基础设施**：xkongcloud-commons-uid公共库依赖于PostgreSQL数据库基础设施，包括数据库表和KV参数配置。

### 并行开发策略

为了提高开发效率，可以采取以下并行开发策略：

1. 在完成PostgreSQL基础设施准备后，可以同时开始xkongcloud-commons-uid公共库的开发和PostgreSQL依赖调整。
2. 在xkongcloud-commons-uid公共库开发的同时，可以开始设计JPA实体类和仓库接口。
3. 在完成xkongcloud-commons-uid公共库开发后，再进行集成配置和测试。

这种策略可以最大限度地减少等待时间，提高开发效率。

### 实施流程图

以下流程图展示了PostgreSQL迁移和xkongcloud-commons-uid公共库开发的实施顺序和依赖关系：

```mermaid
gantt
    title PostgreSQL迁移和xkongcloud-commons-uid公共库实施计划
    dateFormat  YYYY-MM-DD
    section PostgreSQL迁移(F003)
    基础设施准备           :f003-1, 2025-06-01, 7d
    依赖调整和配置         :f003-2, after f003-1, 7d
    演进架构基础实施       :f003-3, after f003-2, 7d
    实体类设计             :f003-4, after f003-3, 7d
    仓库接口设计           :f003-5, after f003-4, 7d
    集成测试               :f003-6, after f004-3, 7d
    section xkongcloud-commons-uid(F004)
    模块创建               :f004-1, after f003-1, 7d
    代码实现               :f004-2, after f004-1, 7d
    单元测试和文档         :f004-3, after f004-2, 7d
```

```mermaid
flowchart TD
    subgraph F003[PostgreSQL迁移]
        F003_1[基础设施准备]
        F003_2[依赖调整和配置]
        F003_3[演进架构基础实施]
        F003_4[实体类设计]
        F003_5[仓库接口设计]
        F003_6[集成测试]

        F003_1 --> F003_2
        F003_2 --> F003_3
        F003_3 --> F003_4
        F003_4 --> F003_5
        F003_5 --> F003_6
    end

    subgraph F004[xkongcloud-commons-uid公共库]
        F004_1[模块创建]
        F004_2[代码实现]
        F004_3[单元测试和文档]

        F004_1 --> F004_2
        F004_2 --> F004_3
    end

    F003_1 --> F004_1
    F004_3 --> F003_6

    style F003_1 fill:#f9f,stroke:#333,stroke-width:2px
    style F004_3 fill:#f9f,stroke:#333,stroke-width:2px
```

上图中，粉色节点表示关键依赖点：
- PostgreSQL基础设施准备(F003_1)是xkongcloud-commons-uid公共库开发的前提
- xkongcloud-commons-uid公共库的单元测试和文档完成(F004_3)是PostgreSQL迁移集成测试的前提

## 基本信息

- **功能ID**: F003
- **功能名称**: PostgreSQL迁移
- **版本**: 1.0
- **状态**: 已批准
- **负责人**: 系统架构组
- **联系方式**: <EMAIL>

## 实现详情

### 环境准备

1. **PostgreSQL服务器安装与配置**
   - 安装PostgreSQL 17.4版本
   - 创建数据库用户和数据库
   - 配置数据库访问权限
   - 配置数据库连接参数

2. **KV参数配置**
   - 在xkongcloud-service-center中配置PostgreSQL相关的KV参数
   - 配置连接池参数
   - 配置JPA参数
   - 配置高级参数

3. **依赖准备**
   - 准备Spring Data JPA依赖
   - 准备PostgreSQL JDBC驱动依赖
   - 准备jOOQ依赖（可选）
   - 准备QueryDSL依赖（可选）

### 团队准备

1. **技术培训**
   - Spring Data JPA基础培训
   - PostgreSQL基础培训
   - jOOQ基础培训（可选）

2. **角色分配**
   - 数据库管理员：负责PostgreSQL服务器的安装、配置和管理
   - 开发人员：负责代码迁移和测试
   - 测试人员：负责功能测试和性能测试
   - 运维人员：负责部署和监控

### 模块结构

```
src/
├── main/
│   ├── java/
│   │   └── org/
│   │       └── xkong/
│   │           └── cloud/
│   │               └── business/
│   │                   └── internal/
│   │                       └── core/
│   │                           ├── annotation/
│   │                           │   └── ServiceInterface.java
│   │                           ├── config/
│   │                           │   ├── PostgreSQLConfig.java
│   │                           │   ├── UidGeneratorConfig.java
│   │                           │   ├── ServiceConfiguration.java
│   │                           │   └── PostgreSQLSchemaEvolutionManager.java
│   │                           ├── dto/
│   │                           │   └── CreateUserRequest.java
│   │                           ├── entity/
│   │                           │   ├── User.java
│   │                           │   ├── Industry.java
│   │                           │   └── Career.java
│   │                           ├── repository/
│   │                           │   ├── UserRepository.java
│   │                           │   ├── IndustryRepository.java
│   │                           │   └── CareerRepository.java
│   │                           ├── service/
│   │                           │   ├── DataAccessService.java
│   │                           │   ├── QueryCondition.java
│   │                           │   ├── UserManagementService.java
│   │                           │   ├── DataInitializer.java
│   │                           │   └── impl/
│   │                           │       ├── LocalUserDataAccessService.java
│   │                           │       └── LocalUserManagementService.java
│   │                           └── utils/
│   │                               └── ...
│   └── resources/
│       ├── application.properties
│       └── application-evolution.yml
└── test/
    └── java/
        └── org/
            └── xkong/
                └── cloud/
                    └── business/
                        └── internal/
                            └── core/
                                ├── config/
                                │   ├── PostgreSQLConfigTest.java
                                │   ├── UidGeneratorConfigTest.java
                                │   └── ServiceConfigurationTest.java
                                ├── repository/
                                │   ├── UserRepositoryTest.java
                                │   ├── IndustryRepositoryTest.java
                                │   └── CareerRepositoryTest.java
                                ├── service/
                                │   ├── DataInitializerTest.java
                                │   ├── UserManagementServiceTest.java
                                │   └── DataAccessServiceTest.java
                                └── utils/
                                    └── ...
```

## 关键流程

### 流程1: 基础设施准备（第1周）

**流程描述**: 准备PostgreSQL数据库环境和配置KV参数

**流程图**:

```mermaid
sequenceDiagram
    participant DBA as 数据库管理员
    participant DevOps as 运维人员
    participant KVCenter as xkongcloud-service-center

    DBA->>DBA: 安装PostgreSQL 17.4
    DBA->>DBA: 创建数据库用户和数据库
    DBA->>DBA: 配置数据库访问权限
    DBA->>DevOps: 提供数据库连接信息
    DevOps->>KVCenter: 配置PostgreSQL相关KV参数
    DevOps->>DevOps: 验证KV参数是否可以正确获取
```

**实现步骤**:

1. **安装PostgreSQL服务器**
   - 安装PostgreSQL 17.4版本
   - 创建数据库用户和数据库
   - 配置数据库访问权限
   - 测试数据库连接
   - **注意**：在此阶段**不需要**手动创建百度UID生成器所需的表结构。表结构将在流程2中由UidTableManagerService使用xkongcloud-commons-uid公共库中的UidTableManager工具类自动创建和管理。

2. **创建Schema**
   - 根据业务领域划分创建多个Schema，以提高数据组织的清晰度和安全性
   - 初始阶段创建以下Schema（严格遵循Schema命名规范）：
     - `user_management`：存放用户管理相关的业务数据表，符合`<业务领域>_<可选子域>`格式
     - `common_config`：存放系统配置相关表，符合`common_<功能类型>`格式
     - `infra_uid`：存放UID生成器相关表，符合`infra_<组件类型>`格式
   - 为每个Schema配置适当的访问权限
   - 在PostgreSQL中创建Schema的SQL示例：
     ```sql
     CREATE SCHEMA IF NOT EXISTS user_management;
     CREATE SCHEMA IF NOT EXISTS common_config;
     CREATE SCHEMA IF NOT EXISTS infra_uid;
     ```
   - **注意**：在开发环境中，可以通过JPA自动创建Schema（如果不存在）；在生产环境中，应使用数据库迁移工具（如Flyway）管理Schema的创建和变更。

   > **Schema命名规范说明**：
   >
   > 为了提高数据组织的清晰度和可维护性，我们采用以下Schema命名规范：
   >
   > 1. **业务Schema**：采用`<业务领域>_<可选子域>`格式
   >    - 例如：`user_management`表示用户管理业务领域
   >    - 其他可能的业务Schema：`identity_core`、`payment_processing`、`order_management`等
   >
   > 2. **基础设施Schema**：采用`infra_<组件类型>`格式
   >    - 例如：`infra_uid`表示UID生成器基础设施组件
   >    - 其他可能的基础设施Schema：`infra_audit`、`infra_cache`、`infra_messaging`等
   >
   > 3. **通用功能Schema**：采用`common_<功能类型>`格式
   >    - 例如：`common_config`表示系统配置通用功能
   >    - 其他可能的通用功能Schema：`common_logging`、`common_security`、`common_reference`等
   >
   > 这种命名规范有助于：
   > - 清晰区分不同类型的数据和功能
   > - 提高数据库结构的可读性和可维护性
   > - 支持更精细的权限控制和安全管理
   > - 为未来的微服务架构演进提供基础

   > **说明**：
   >
   > 百度UID生成器需要以下表结构，这些表将在流程2中由UidTableManagerService自动创建在`infra_uid` Schema中：
   >
   > **infra_uid.instance_registry表**：
   > - `instance_unique_id`：主键，自增ID，用作持久化实例ID
   > - `application_name`：应用/服务名称，用于标识不同的应用
   > - `environment`：部署环境，如dev、prod、lan等
   > - `instance_group`：实例分组或逻辑单元名
   > - `status`：实例状态，如ACTIVE、INACTIVE、DECOMMISSIONED等
   > - `first_registered_at`：首次注册时间
   > - `last_seen_at`：最后心跳或活跃时间
   > - `custom_metadata`：存储机器特征码的JSONB字段，包含BIOS UUID、系统序列号、MAC地址等
   >
   > **infra_uid.worker_id_assignment表**：
   > - `worker_id`：主键，工作机器ID，对应百度UID生成器中的workerId
   > - `assigned_instance_unique_id`：当前占用此worker_id的持久实例ID，外键关联infra_uid.instance_registry表
   > - `assignment_status`：分配状态，如AVAILABLE、ASSIGNED、RESERVED、EXPIRED等
   > - `assigned_at`：分配时间
   > - `lease_duration_seconds`：租约时长，默认300秒
   > - `lease_expires_at`：租约到期时间
   > - `last_heartbeat_at`：最后心跳时间
   >
   > 在xkongcloud-commons-uid公共库中，UidTableManager工具类提供了以下功能：
   > - 创建和管理`infra_uid` Schema
   > - 创建infra_uid.instance_registry和infra_uid.worker_id_assignment表及其索引
   > - 支持不同的DDL策略（create, create-drop, update, validate, none）
   > - 预填充infra_uid.worker_id_assignment表
   > - 验证表结构
   >
   > 这种设计将实例身份与工作机器ID分离，通过持久化实例ID和机器特征码实现更可靠的工作机器ID分配和恢复机制。详细实现请参考 [基于PostgreSQL的持久化实例ID及特征码恢复方案](../../../common/middleware/integration/postgresql-persistent-id-fingerprint-recovery.md)。

3. **配置KV参数**
   - 在xkongcloud-service-center中配置PostgreSQL相关的KV参数
   - 配置Schema相关参数：
     - `postgresql.schema.list`：Schema列表，用逗号分隔（可选，默认为"user_management,common_config,infra_uid"）。此参数仅作为应用程序验证Schema是否存在的依据，不会自动创建Schema。开发环境、测试环境和生产环境应保持一致，以确保环境一致性

   > **重要说明**：Schema结构必须由DBA或开发人员手动管理，应用程序只负责验证Schema是否存在，不会尝试创建Schema。如果验证发现任何必需的Schema不存在，应用程序将无法启动，并显示明确的错误信息。这确保了数据库结构的可控性和一致性，避免了自动化和手动操作混合导致的冲突。
   - 在xkongcloud-service-center中为百度UID生成器配置核心参数，确保这些参数必须配置，无默认值，缺少时应用无法启动。详细配置说明请参考 [基于PostgreSQL的百度UID生成器实现方案](../../../common/middleware/integration/baidu-uid-generator-postgresql-implementation.md#配置和使用指南)：

     | 参数名 | 说明 | 推荐值 | 是否必需 |
     |-------|------|-------|---------|
     | `uid.epochStr` | 时间基点，格式为"yyyy-MM-dd"，从此时间开始计算秒数 | "2025-01-01" | 是 |
     | `uid.timeBits` | 时间戳位数，决定ID可用的时间跨度 | 31 (约68年) | 是 |
     | `uid.workerBits` | 工作机器ID位数，决定最大支持的机器数量 | 18 (约26万台) | 是 |
     | `uid.seqBits` | 序列号位数，决定每秒可生成的ID数量 | 14 (每秒约1.6万个) | 是 |
     | `uid.boostPower` | RingBuffer扩容参数，取值为2的幂次方 | 3 (RingBuffer大小为2^3=8倍) | 否 |
     | `uid.paddingFactor` | RingBuffer填充因子，取值为百分比(0, 100) | 50 (剩余50%时填充) | 否 |
     | `uid.scheduleInterval` | RingBuffer调度时间间隔(秒) | 60 (每分钟调度一次) | 否 |

   - 在xkongcloud-service-center中配置以下参数，用于持久化实例ID及特征码恢复方案。详细配置说明请参考 [基于PostgreSQL的持久化实例ID及特征码恢复方案](../../../common/middleware/integration/postgresql-persistent-id-fingerprint-recovery.md)：

     | 参数名 | 说明 | 示例值 | 是否必需 |
     |-------|------|-------|---------|
     | `uid.instance.application-name` | 应用/服务名称 | "business-internal-core" | 是 |
     | `uid.instance.environment` | 部署环境 | "dev", "prod", "lan" | 是 |
     | `uid.instance.group` | 实例分组或逻辑单元名 | "group1" | 否 |
     | `uid.instance.local-storage-path` | 本地实例ID存储路径 | "./instance.id" | 否，默认为应用工作目录下的.instance_id |
     | `uid.instance.recovery.enabled` | 是否启用特征码恢复 | "true" | 否，默认为true |
     | `uid.instance.recovery.high-confidence-threshold` | 高置信度匹配阈值 | "150" | 否，默认为150 |
     | `uid.instance.recovery.minimum-acceptable-score` | 最低可接受匹配分数 | "70" | 否，默认为70 |
     | `uid.instance.recovery.strategy` | 低置信度匹配策略 | "ALERT_AND_NEW", "ALERT_AND_MANUAL", "ALERT_AUTO_WITH_TIMEOUT" | 否，默认为ALERT_AUTO_WITH_TIMEOUT |
     | `uid.instance.recovery.timeout-seconds` | 恢复策略超时时间（秒） | "300" | 否，默认为300，仅在使用ALERT_AUTO_WITH_TIMEOUT策略时有效 |
     | `uid.worker.lease-duration-seconds` | 工作机器ID租约时长(秒) | "300" | 否，默认为300 |
     | `uid.worker.lease-renewal-interval-seconds` | 租约续约间隔(秒) | "100" | 否，默认为租约时长的1/3 |
     | `uid.instance.override` | 强制指定实例ID（仅用于人工恢复） | "123" | 否 |
     | `uid.instance.encryption.enabled` | 是否启用实例ID文件加密 | "false" | 否，默认为false |
     | `uid.instance.encryption.algorithm` | 加密算法 | "AES-256-GCM" | 否，默认为AES-256-GCM |

   > **重要说明**：
   > 1. 位数分配建议：符号位(1位) + 时间戳(31位) + 工作机器ID(18位) + 序列号(14位) = 64位
   > 2. 时间戳使用秒级精度，31位可以使用约68年
   > 3. 工作机器ID为18位，最多支持约262,144个工作节点，足够大型分布式系统使用
   > 4. 序列号为14位，每秒最多生成16,384个ID，满足大多数高并发场景
   > 5. 持久化实例ID方案不再依赖IP地址和端口号，而是通过机器特征码（如BIOS UUID、系统序列号、MAC地址等）来识别实例身份
   > 6. 特征码恢复功能可以在实例迁移或重建后自动恢复原有的工作机器ID，提高系统稳定性

   - 验证KV参数是否可以正确获取

### 流程2: 依赖调整（第2周）

**流程描述**: 修改项目依赖，添加Spring Data JPA和PostgreSQL相关依赖，创建配置类

**流程图**:

```mermaid
sequenceDiagram
    participant Dev as 开发人员
    participant POM as pom.xml
    participant Config as PostgreSQLConfig

    Dev->>POM: 移除Spring Data Cassandra依赖
    Dev->>POM: 添加Spring Data JPA依赖
    Dev->>POM: 添加PostgreSQL JDBC驱动依赖
    Dev->>POM: 添加jOOQ依赖(可选)
    Dev->>Config: 创建PostgreSQLConfig类
    Dev->>Config: 配置数据源
    Dev->>Config: 配置EntityManagerFactory
    Dev->>Config: 配置TransactionManager
```

**实现步骤**:

1. **修改pom.xml**
   - 移除Spring Data Cassandra依赖
   - 添加Spring Data JPA依赖
   - 添加PostgreSQL JDBC驱动依赖
   - 添加jOOQ依赖（可选）
   - 添加QueryDSL依赖（可选）
   - 添加百度UID生成器依赖和相关依赖（详细依赖说明请参考 [基于PostgreSQL的百度UID生成器实现方案](../../../common/middleware/integration/baidu-uid-generator-postgresql-implementation.md#依赖配置)）：

   ```xml
   <!-- xkongcloud-commons-uid 公共库 -->
   <dependency>
       <groupId>org.xkong.cloud</groupId>
       <artifactId>xkongcloud-commons-uid</artifactId>
       <version>${project.version}</version>
   </dependency>

   <!-- PostgreSQL驱动 -->
   <dependency>
       <groupId>org.postgresql</groupId>
       <artifactId>postgresql</artifactId>
       <version>42.7.5</version>
   </dependency>

   <!-- Spring JDBC，用于JdbcTemplate -->
   <dependency>
       <groupId>org.springframework.boot</groupId>
       <artifactId>spring-boot-starter-jdbc</artifactId>
   </dependency>

   <!-- Spring事务管理，用于TransactionTemplate -->
   <dependency>
       <groupId>org.springframework</groupId>
       <artifactId>spring-tx</artifactId>
       <!-- 版本由Spring Boot管理，无需显式指定 -->
   </dependency>
   ```

   > **依赖说明**：
   > - `org.xkong.cloud:xkongcloud-commons-uid`：公共UID生成库，封装了百度UID生成器的实现，提供持久化实例ID和特征码恢复功能
   > - `org.postgresql:postgresql:42.7.5`：PostgreSQL JDBC驱动，用于数据库连接
   > - `spring-boot-starter-jdbc`：提供JdbcTemplate，用于数据库操作
   > - `spring-tx`：提供TransactionTemplate，用于事务管理
   >
   > 注意：xkongcloud-commons-uid依赖于JdbcTemplate和TransactionTemplate，因此必须添加相关依赖。

2. **创建配置类**
   - 创建PostgreSQLConfig类
   - 配置数据源
   - 配置EntityManagerFactory
   - 配置TransactionManager

3. **创建演进架构基础组件**
   - 创建ServiceInterface注解（用于标识可演进的服务接口）
   - 创建ServiceConfiguration类（配置驱动的架构控制）
   - 创建PostgreSQLSchemaEvolutionManager类（Schema演进管理）
   - 为后续阶段的服务抽象层奠定基础

4. **创建UID生成器配置类和表管理器**

   a. 创建 `org.xkong.cloud.business.internal.core.config.UidGeneratorConfig.java` 配置类，使用xkongcloud-commons-uid公共库的UidGeneratorFacade门面模式：

   ```java
   package org.xkong.cloud.business.internal.core.config;

   import com.baidu.fsg.uid.UidGenerator;
   import org.slf4j.Logger;
   import org.slf4j.LoggerFactory;
   import org.springframework.beans.factory.DisposableBean;
   import org.springframework.beans.factory.annotation.Autowired;
   import org.springframework.beans.factory.annotation.Value;
   import org.springframework.context.annotation.Bean;
   import org.springframework.context.annotation.Configuration;
   import org.springframework.context.annotation.DependsOn;
   import org.springframework.context.annotation.Primary;
   import org.xkong.cloud.business.internal.core.service.KVParamService;
   import org.xkong.cloud.commons.uid.facade.UidGeneratorFacade;
   import org.xkong.cloud.commons.uid.facade.UidGeneratorFacadeBuilder;
   import org.xkong.cloud.commons.uid.instance.PersistentInstanceManager;

   import javax.annotation.PreDestroy;
   import javax.sql.DataSource;

   /**
    * UID生成器配置类
    * 使用xkongcloud-commons-uid公共库的UidGeneratorFacade门面模式
    */
   @Configuration
   @DependsOn("kvParamService")
   public class UidGeneratorConfig {

       private static final Logger log = LoggerFactory.getLogger(UidGeneratorConfig.class);

       @Autowired
       private KVParamService kvParamService;

       @Autowired
       private DataSource dataSource;

       @Value("${xkong.kv.cluster-id}")
       private String clusterId;

       /**
        * 创建UID生成器门面
        */
       @Bean
       @Primary
       public UidGeneratorFacade uidGeneratorFacade() {
           // 从KVParamService获取参数，所有参数都必需，缺少时启动失败
           String appName = clusterId; // 使用@Value("${xkong.kv.cluster-id}")注入的clusterId变量

           // 获取必需参数，缺少时启动失败
           String environment = kvParamService.getParam("uid.instance.environment");
           if (environment == null || environment.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.instance.environment'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.instance.environment'参数未在KV服务中找到。");
           }

           String instanceGroup = kvParamService.getParam("uid.instance.group");
           if (instanceGroup == null || instanceGroup.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.instance.group'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.instance.group'参数未在KV服务中找到。");
           }

           String schemaName = kvParamService.getParam("uid.schema.name");
           if (schemaName == null || schemaName.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.schema.name'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.schema.name'参数未在KV服务中找到。");
           }

           String localStoragePath = kvParamService.getParam("uid.instance.local-storage-path");
           if (localStoragePath == null || localStoragePath.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.instance.local-storage-path'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.instance.local-storage-path'参数未在KV服务中找到。");
           }

           String recoveryEnabledStr = kvParamService.getParam("uid.instance.recovery.enabled");
           if (recoveryEnabledStr == null || recoveryEnabledStr.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.instance.recovery.enabled'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.instance.recovery.enabled'参数未在KV服务中找到。");
           }
           boolean recoveryEnabled = Boolean.parseBoolean(recoveryEnabledStr);

           String highConfThresholdStr = kvParamService.getParam("uid.instance.recovery.high-confidence-threshold");
           if (highConfThresholdStr == null || highConfThresholdStr.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.instance.recovery.high-confidence-threshold'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.instance.recovery.high-confidence-threshold'参数未在KV服务中找到。");
           }
           int highConfThreshold = Integer.parseInt(highConfThresholdStr);

           String minAcceptScoreStr = kvParamService.getParam("uid.instance.recovery.minimum-acceptable-score");
           if (minAcceptScoreStr == null || minAcceptScoreStr.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.instance.recovery.minimum-acceptable-score'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.instance.recovery.minimum-acceptable-score'参数未在KV服务中找到。");
           }
           int minAcceptScore = Integer.parseInt(minAcceptScoreStr);

           String recoveryStrategy = kvParamService.getParam("uid.instance.recovery.strategy");
           if (recoveryStrategy == null || recoveryStrategy.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.instance.recovery.strategy'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.instance.recovery.strategy'参数未在KV服务中找到。");
           }

           String recoveryTimeoutSecondsStr = kvParamService.getParam("uid.instance.recovery.timeout-seconds");
           if (recoveryTimeoutSecondsStr == null || recoveryTimeoutSecondsStr.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.instance.recovery.timeout-seconds'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.instance.recovery.timeout-seconds'参数未在KV服务中找到。");
           }
           int recoveryTimeoutSeconds = Integer.parseInt(recoveryTimeoutSecondsStr);

           String encryptionEnabledStr = kvParamService.getParam("uid.instance.encryption.enabled");
           if (encryptionEnabledStr == null || encryptionEnabledStr.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.instance.encryption.enabled'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.instance.encryption.enabled'参数未在KV服务中找到。");
           }
           boolean encryptionEnabled = Boolean.parseBoolean(encryptionEnabledStr);

           String leaseDurationSecondsStr = kvParamService.getParam("uid.worker.lease-duration-seconds");
           if (leaseDurationSecondsStr == null || leaseDurationSecondsStr.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.worker.lease-duration-seconds'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.worker.lease-duration-seconds'参数未在KV服务中找到。");
           }
           int leaseDurationSeconds = Integer.parseInt(leaseDurationSecondsStr);

           // 获取UID生成器参数，所有参数都必需，缺少时启动失败
           String epochStr = kvParamService.getParam("uid.epochStr");
           if (epochStr == null || epochStr.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.epochStr'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.epochStr'参数未在KV服务中找到。");
           }
           String timeBitsStr = kvParamService.getParam("uid.timeBits");
           if (timeBitsStr == null || timeBitsStr.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.timeBits'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.timeBits'参数未在KV服务中找到。");
           }
           int timeBits = Integer.parseInt(timeBitsStr);

           String workerBitsStr = kvParamService.getParam("uid.workerBits");
           if (workerBitsStr == null || workerBitsStr.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.workerBits'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.workerBits'参数未在KV服务中找到。");
           }
           int workerBits = Integer.parseInt(workerBitsStr);

           String seqBitsStr = kvParamService.getParam("uid.seqBits");
           if (seqBitsStr == null || seqBitsStr.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.seqBits'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.seqBits'参数未在KV服务中找到。");
           }
           int seqBits = Integer.parseInt(seqBitsStr);

           String boostPowerStr = kvParamService.getParam("uid.boostPower");
           if (boostPowerStr == null || boostPowerStr.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.boostPower'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.boostPower'参数未在KV服务中找到。");
           }
           int boostPower = Integer.parseInt(boostPowerStr);

           String paddingFactorStr = kvParamService.getParam("uid.paddingFactor");
           if (paddingFactorStr == null || paddingFactorStr.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.paddingFactor'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.paddingFactor'参数未在KV服务中找到。");
           }
           int paddingFactor = Integer.parseInt(paddingFactorStr);

           String scheduleIntervalStr = kvParamService.getParam("uid.scheduleInterval");
           if (scheduleIntervalStr == null || scheduleIntervalStr.trim().isEmpty()) {
               log.error("UID生成器配置错误: 必需的'uid.scheduleInterval'参数未在KV服务中找到。");
               throw new IllegalArgumentException("UID生成器配置错误: 必需的'uid.scheduleInterval'参数未在KV服务中找到。");
           }
           long scheduleInterval = Long.parseLong(scheduleIntervalStr);

           // 使用构建器模式创建UidGeneratorFacade
           return new UidGeneratorFacadeBuilder()
               .withDataSource(dataSource)
               .withSchemaName(schemaName)
               .withApplicationName(appName)
               .withEnvironment(environment)
               .withInstanceGroup(instanceGroup)
               .withLocalStoragePath(localStoragePath)
               .withRecoveryEnabled(recoveryEnabled)
               .withHighConfidenceThreshold(highConfThreshold)
               .withMinimumAcceptableScore(minAcceptScore)
               .withRecoveryStrategy(recoveryStrategy)
               .withRecoveryTimeoutSeconds(recoveryTimeoutSeconds)
               .withEncryptionEnabled(encryptionEnabled)
               .withLeaseDurationSeconds(leaseDurationSeconds)
               .withEpochStr(epochStr)
               .withTimeBits(timeBits)
               .withWorkerBits(workerBits)
               .withSeqBits(seqBits)
               .withBoostPower(boostPower)
               .withPaddingFactor(paddingFactor)
               .withScheduleInterval(scheduleInterval)
               .build();
       }

       /**
        * 创建UidGenerator适配器
        * 这个Bean适配UidGeneratorFacade到百度UidGenerator接口，以便与现有代码兼容
        */
       @Bean
       public UidGenerator uidGenerator(UidGeneratorFacade facade) {
           return new UidGenerator() {
               @Override
               public long getUID() {
                   return facade.getUID();
               }

               @Override
               public String parseUID(long uid) {
                   throw new UnsupportedOperationException("parseUID方法未实现");
               }
           };
       }

       /**
        * 配置关闭顺序，确保UID库在数据库连接池之前关闭
        */
       @Bean
       @DependsOn("uidGeneratorFacade") // 确保facade先创建
       public UidShutdownOrderBean uidShutdownOrder(UidGeneratorFacade facade) {
           return new UidShutdownOrderBean(facade);
       }

       /**
        * 内部类，用于确保UidGeneratorFacade在应用关闭时正确关闭
        */
       public static class UidShutdownOrderBean implements DisposableBean {
           private final UidGeneratorFacade facade;

           public UidShutdownOrderBean(UidGeneratorFacade facade) {
               this.facade = facade;
           }

           @Override
           @PreDestroy // 确保在数据库连接池关闭前执行
           public void destroy() {
               if (facade != null) {
                   facade.close();
               }
           }
       }
   }
   ```

   > **说明**：
   > - 此配置类使用xkongcloud-commons-uid公共库提供的组件，而不是直接实现UID生成器相关类
   > - 从KVParamService获取配置参数，并传递给公共库组件
   > - 配置类依赖于KVParamService，因此使用@DependsOn注解确保KVParamService先初始化

   b. 创建 `org.xkong.cloud.business.internal.core.service.UidTableManager.java` 类，使用xkongcloud-commons-uid公共库中的UidTableManager工具类：

   ```java
   package org.xkong.cloud.business.internal.core.service;

   import org.slf4j.Logger;
   import org.slf4j.LoggerFactory;
   import org.springframework.beans.factory.InitializingBean;
   import org.springframework.beans.factory.annotation.Autowired;
   import org.springframework.context.annotation.DependsOn;
   import org.springframework.jdbc.core.JdbcTemplate;
   import org.springframework.stereotype.Component;
   import org.xkong.cloud.business.internal.core.config.PostgreSQLConfig;
   import org.xkong.cloud.commons.uid.util.UidTableManager;

   /**
    * UID生成器相关表管理器
    * 根据postgresql.ddl-auto参数管理instance_registry和worker_id_assignment表
    * 使用xkongcloud-commons-uid公共库中的UidTableManager工具类
    */
   @Component
   @DependsOn("postgreSQLConfig")
   public class UidTableManagerService implements InitializingBean {

       private static final Logger log = LoggerFactory.getLogger(UidTableManagerService.class);

       @Autowired
       private JdbcTemplate jdbcTemplate;

       @Autowired
       private PostgreSQLConfig postgreSQLConfig;

       @Override
       public void afterPropertiesSet() throws Exception {
           String ddlAuto = postgreSQLConfig.getDdlAuto();
           log.info("初始化UID生成器相关表，当前DDL模式: {}", ddlAuto);

           // 获取worker_id位数，用于填充worker_id_assignment表
           int workerBits = 18;
           try {
               String workerBitsStr = postgreSQLConfig.getKvParamService().getParam("uid.workerBits");
               if (workerBitsStr != null && !workerBitsStr.isEmpty()) {
                   workerBits = Integer.parseInt(workerBitsStr);
               }
           } catch (Exception e) {
               log.warn("获取uid.workerBits参数失败，使用默认值18", e);
           }

           // 获取Schema名称，默认为infra_uid
           String schemaName = "infra_uid";
           log.info("使用Schema: {}", schemaName);

           // 根据DDL策略调用UidTableManager工具类的相应方法
           switch (ddlAuto.toLowerCase()) {
               case "create":
                   // 先删除再创建表
                   UidTableManager.dropTables(jdbcTemplate, schemaName);
                   UidTableManager.createSchema(jdbcTemplate, schemaName); // 确保Schema存在
                   UidTableManager.createTables(jdbcTemplate, schemaName);
                   UidTableManager.fillWorkerIdAssignments(jdbcTemplate, workerBits, schemaName);
                   break;
               case "create-drop":
                   // 应用启动时创建表，应用关闭时删除表（需要另外配置关闭钩子）
                   UidTableManager.dropTables(jdbcTemplate, schemaName);
                   UidTableManager.createSchema(jdbcTemplate, schemaName); // 确保Schema存在
                   UidTableManager.createTables(jdbcTemplate, schemaName);
                   UidTableManager.fillWorkerIdAssignments(jdbcTemplate, workerBits, schemaName);
                   // 注册关闭钩子在应用关闭时删除表
                   registerShutdownHook(schemaName);
                   break;
               case "update":
                   // 如果表不存在则创建
                   UidTableManager.createSchema(jdbcTemplate, schemaName); // 确保Schema存在
                   UidTableManager.createTablesIfNotExist(jdbcTemplate, schemaName);
                   break;
               case "validate":
                   // 验证表结构
                   UidTableManager.validateTables(jdbcTemplate, schemaName);
                   break;
               case "none":
                   // 不执行任何操作
                   log.info("DDL模式为none，不执行任何表操作");
                   break;
               default:
                   log.warn("未知的DDL模式: {}", ddlAuto);
           }
       }

       /**
        * 注册应用关闭钩子，在应用关闭时删除表（仅在create-drop模式下使用）
        */
       private void registerShutdownHook(String schemaName) {
           Runtime.getRuntime().addShutdownHook(new Thread(() -> {
               String ddlAuto = postgreSQLConfig.getDdlAuto();
               if ("create-drop".equalsIgnoreCase(ddlAuto)) {
                   log.info("应用关闭，DDL模式为create-drop，删除UID生成器相关表");
                   UidTableManager.dropTables(jdbcTemplate, schemaName);
               }
           }));
       }
   }
   ```

   c. 创建 `org.xkong.cloud.business.internal.core.service.UidTableCleanupListener.java` 类，使用xkongcloud-commons-uid公共库中的UidTableManager工具类：

   ```java
   package org.xkong.cloud.business.internal.core.service;

   import org.slf4j.Logger;
   import org.slf4j.LoggerFactory;
   import org.springframework.beans.factory.annotation.Autowired;
   import org.springframework.context.ApplicationListener;
   import org.springframework.context.event.ContextClosedEvent;
   import org.springframework.jdbc.core.JdbcTemplate;
   import org.springframework.stereotype.Component;
   import org.xkong.cloud.business.internal.core.config.PostgreSQLConfig;
   import org.xkong.cloud.commons.uid.util.UidTableManager;

   /**
    * UID生成器相关表清理监听器
    * 在应用关闭时，如果DDL模式为create-drop，则删除UID生成器相关表
    * 使用xkongcloud-commons-uid公共库中的UidTableManager工具类
    */
   @Component
   public class UidTableCleanupListener implements ApplicationListener<ContextClosedEvent> {

       private static final Logger log = LoggerFactory.getLogger(UidTableCleanupListener.class);

       @Autowired
       private JdbcTemplate jdbcTemplate;

       @Autowired
       private PostgreSQLConfig postgreSQLConfig;

       @Override
       public void onApplicationEvent(ContextClosedEvent event) {
           String ddlAuto = postgreSQLConfig.getDdlAuto();

           // 只有在create-drop模式下才删除表
           if ("create-drop".equalsIgnoreCase(ddlAuto)) {
               log.info("应用关闭，DDL模式为create-drop，删除UID生成器相关表");
               try {
                   // 获取Schema名称，默认为infra_uid
                   String schemaName = "infra_uid";
                   // 使用UidTableManager工具类删除表
                   UidTableManager.dropTables(jdbcTemplate, schemaName);
                   log.info("UID生成器相关表删除成功");
               } catch (Exception e) {
                   log.error("删除UID生成器相关表时发生错误", e);
                   // 不抛出异常，避免影响应用关闭
               }
           }
       }
   }






4. **更新模块结构**

   更新后的模块结构如下：

   ```
   src/
   ├── main/
   │   ├── java/
   │   │   └── org/
   │   │       └── xkong/
   │   │           └── cloud/
   │   │               └── business/
   │   │                   └── internal/
   │   │                       └── core/
   │   │                           ├── config/
   │   │                           │   ├── PostgreSQLConfig.java
   │   │                           │   └── UidGeneratorConfig.java
   │   │                           ├── entity/
   │   │                           │   ├── User.java
   │   │                           │   ├── Industry.java
   │   │                           │   └── Career.java
   │   │                           ├── repository/
   │   │                           │   ├── UserRepository.java
   │   │                           │   ├── IndustryRepository.java
   │   │                           │   └── CareerRepository.java
   │   │                           ├── service/
   │   │                           │   ├── DataInitializer.java
   │   │                           │   ├── UidTableManagerService.java
   │   │                           │   └── UidTableCleanupListener.java
   │   │                           └── utils/
   │   │                               └── ...
   │   └── resources/
   │       └── application.properties
   └── test/
       └── java/
           └── org/
               └── xkong/
                   └── cloud/
                       └── business/
                           └── internal/
                               └── core/
                                   ├── config/
                                   │   ├── PostgreSQLConfigTest.java
                                   │   └── UidGeneratorConfigTest.java // (或通过集成测试覆盖)
                                   ├── repository/
                                   │   ├── UserRepositoryTest.java
                                   │   ├── IndustryRepositoryTest.java
                                   │   └── CareerRepositoryTest.java
                                   ├── service/
                                   │   ├── DataInitializerTest.java
                                   │   └── UidTableManagerTest.java
                                   └── utils/
                                       └── ...
   ```

   > **说明**：
   > - 移除了`uid`包，因为相关类已经迁移到`xkongcloud-commons-uid`公共库
   > - 保留了`config/UidGeneratorConfig.java`，用于配置和初始化UID生成器
   > - 移除了`utils/MachineFingerprints.java`，因为该类已经迁移到公共库
   > - 添加了`service/UidTableManager.java`，用于管理百度UID生成器相关表的DDL策略
   > - 添加了`service/UidTableManagerTest.java`，用于测试UidTableManager
















5. **更新变更历史**

   在文档末尾的变更历史部分添加以下内容：

   | 版本 | 日期 | 变更内容 | 变更人 |
   |-----|-----|---------|-------|
   | 1.5 | 2025-05-23 | 更新实施计划，使用xkongcloud-commons-uid公共库替代直接实现UID生成器相关类 | AI助手 |

### 流程3: 演进架构基础实施（第3周）

**流程描述**: 建立持续演进架构的基础设施，为未来从单体架构演进到微服务架构奠定基础

**流程图**:

```mermaid
sequenceDiagram
    participant Dev as 开发人员
    participant ServiceLayer as 服务抽象层
    participant ConfigLayer as 配置驱动层
    participant SchemaManager as Schema演进管理器

    Dev->>ServiceLayer: 创建ServiceInterface注解
    Dev->>ServiceLayer: 创建DataAccessService接口
    Dev->>ServiceLayer: 创建QueryCondition查询条件类
    Dev->>ConfigLayer: 创建ServiceConfiguration配置类
    Dev->>ConfigLayer: 定义架构模式枚举
    Dev->>SchemaManager: 创建PostgreSQLSchemaEvolutionManager
    Dev->>ServiceLayer: 实现用户管理服务演进架构示例
```

**实现步骤**:

1. **建立服务抽象层基础**
   - 创建ServiceInterface注解，用于标识可演进的服务接口
   - 创建DataAccessService通用数据访问接口
   - 创建QueryCondition查询条件封装类
   - 为后续的服务代理和工厂模式奠定基础

2. **实现配置驱动机制**
   - 创建ServiceConfiguration类，支持架构模式配置
   - 定义ArchitectureMode枚举（MONOLITHIC, MODULAR, HYBRID, MICROSERVICES）
   - 定义DeploymentMode、DataAccessMode、Protocol等枚举
   - 创建application-evolution.yml配置模板

3. **创建Schema演进管理器**
   - 创建PostgreSQLSchemaEvolutionManager类
   - 支持根据架构模式自动管理Schema
   - 实现从单体到微服务的Schema演进路径
   - 提供Schema存在性检查和创建功能

4. **实现用户管理服务演进架构示例**
   - 创建UserManagementService接口（使用@ServiceInterface注解）
   - 创建LocalUserDataAccessService本地数据访问实现
   - 创建LocalUserManagementService本地服务实现
   - 演示如何使用演进架构基础设施

**关键设计原则**:
- **透明演进原则**：业务代码在架构演进过程中保持不变
- **配置驱动原则**：通过配置文件控制架构模式和服务调用方式
- **渐进实施原则**：分阶段引入抽象层，避免初期复杂度过高
- **PostgreSQL优化原则**：充分利用PostgreSQL特性，同时保持演进能力

### 流程4: 实体类设计（第4-5周）

**特别说明**：由于项目当前处于初始阶段，Cassandra数据库中尚未包含任何生产环境的业务数据，应用程序中也尚未实现依赖于特定数据库的复杂业务逻辑，因此本流程不是传统意义上的"实体类转换"，而是为PostgreSQL设计全新的实体类。这意味着我们可以直接设计最适合关系型数据库的实体模型，而不需要考虑与现有Cassandra模型的兼容性。

**流程描述**: 为PostgreSQL设计JPA实体类，建立实体间的关系

**流程图**:

```mermaid
sequenceDiagram
    participant Dev as 开发人员
    participant Entity as 实体类
    participant Relation as 实体关系

    Dev->>Entity: 分析业务需求和数据模型
    Dev->>Entity: 设计关系型数据模型
    Dev->>Entity: 创建带@Entity和@Table注解的实体类
    Dev->>Entity: 添加@Id和@Column注解
    Dev->>Relation: 设计并添加@OneToOne关系
    Dev->>Relation: 设计并添加@OneToMany关系
    Dev->>Relation: 设计并添加@ManyToOne关系
    Dev->>Relation: 设计并添加@ManyToMany关系
```

**实现步骤**:

1. **分析业务需求和数据模型**
   - 理解业务需求和数据关系
   - 设计规范化的关系型数据模型
   - 确定实体之间的关系（一对一、一对多、多对多）

2. **创建JPA实体类**
   - 创建带@Entity和@Table注解的实体类
   - 添加@Id注解定义主键
   - 添加@Column注解定义列属性
   - 添加关系注解（@OneToOne, @OneToMany, @ManyToOne, @ManyToMany）
   - 选择适当的数据类型
   - 集成百度UID生成器，为实体类生成ID
   - **强制要求**：所有实体类必须使用`@Table(name = "表名", schema = "schema名")`明确指定Schema
   - **强制要求**：不允许依赖任何默认Schema设置，这是一项没有例外情况的强制性要求

   **使用UID生成器的实体类示例**：

   ```java
   @Entity
   @Table(name = "user", schema = "user_management")
   public class User {
       @Id
       private Long userId;  // 使用Long类型存储UID生成器生成的ID

       @Column(name = "name", nullable = false, length = 100)
       private String name;

       @Column(name = "mail_address")
       private String mailAddress;

       @Column(name = "regist_time")
       private LocalDateTime registTime;

       // 其他字段...

       // 关系映射...
       @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
       private List<UserMessage> messages = new ArrayList<>();

       // 默认构造函数
       public User() {
       }

       // Getter和Setter方法...
       public Long getUserId() {
           return userId;
       }

       public void setUserId(Long userId) {
           this.userId = userId;
       }

       // 其他Getter和Setter方法...
   }
   ```

   **在服务层使用UID生成器示例**：

   ```java
   @Service
   public class UserService {
       @Autowired
       private UserRepository userRepository;

       @Autowired
       private UidGenerator uidGenerator;  // 注入UID生成器

       @Transactional
       public User createUser(String name, String email) {
           User user = new User();

           // 使用UID生成器生成ID
           long userId = uidGenerator.getUID();
           user.setUserId(userId);

           // 设置其他属性
           user.setName(name);
           user.setMailAddress(email);
           user.setRegistTime(LocalDateTime.now());

           // 保存用户
           return userRepository.save(user);
       }

       // 其他方法...
   }
   ```

   > **说明**：
   > 1. 实体类的ID字段类型为Long，用于存储UID生成器生成的64位ID
   > 2. 不使用@GeneratedValue注解，因为ID由UID生成器生成
   > 3. 在服务层注入UidGenerator，调用getUID()方法生成ID
   > 4. 使用UID生成器生成的ID具有全局唯一性、趋势递增性和高性能特性
   > 5. **必须**使用`@Table(name = "表名", schema = "schema名")`明确指定Schema，这是强制性要求
   >
   > **错误的实体类示例（不要这样做）**：
   > ```java
   > @Entity
   > @Table(name = "user")  // 错误：没有指定schema
   > public class User {
   >     @Id
   >     private Long userId;
   >
   >     @Column(name = "username", nullable = false)
   >     private String username;
   >
   >     // 其他字段和方法...
   > }
   > ```
   >
   > **为什么明确指定Schema是强制性要求**：
   > - 明确指定Schema提高了代码的可读性和明确性
   > - 防止表被错误地创建在默认Schema中
   > - 确保在多Schema环境中表的位置是明确的
   > - 简化了配置，不需要依赖全局默认Schema设置
   > - 避免因默认Schema设置变化而导致的问题

### 流程5: 仓库接口设计（第5周）

**特别说明**：由于项目当前处于初始阶段，Cassandra数据库中尚未包含任何生产环境的业务数据，应用程序中也尚未实现依赖于特定数据库的复杂业务逻辑，因此本流程不是传统意义上的"仓库接口转换"，而是为PostgreSQL设计全新的仓库接口。这意味着我们可以直接设计最适合JPA的仓库接口，而不需要考虑与现有Cassandra仓库接口的兼容性。

**流程描述**: 为PostgreSQL设计JpaRepository接口，实现查询方法

**流程图**:

```mermaid
sequenceDiagram
    participant Dev as 开发人员
    participant Repo as 仓库接口
    participant Query as 查询方法

    Dev->>Repo: 分析业务查询需求
    Dev->>Repo: 创建JpaRepository接口
    Dev->>Query: 设计基于方法名的查询
    Dev->>Query: 添加@Query注解的JPQL查询
    Dev->>Query: 添加原生SQL查询
```

**实现步骤**:

1. **分析业务查询需求**
   - 理解业务需要的查询功能
   - 设计查询方法
   - 确定查询参数和返回类型

2. **创建仓库接口**
   - 创建继承JpaRepository的接口
   - 实现基于方法名的查询
   - 添加@Query注解的JPQL查询（如需要）
   - 添加原生SQL查询（如需要）

### 流程6: 数据初始化设计（第6周）

**特别说明**：由于项目当前处于初始阶段，Cassandra数据库中尚未包含任何生产环境的业务数据，因此本流程不涉及数据迁移，而是专注于为新的PostgreSQL数据库设计初始化逻辑。这意味着我们可以直接设计最适合PostgreSQL的数据初始化机制，而不需要考虑从Cassandra迁移数据。

**流程描述**: 设计数据初始化逻辑，支持不同的DDL策略，实现开发模式下的数据库表自动重置功能

**流程图**:

```mermaid
sequenceDiagram
    participant Dev as 开发人员
    participant Init as PostgreSQLDataInitializer
    participant Config as PostgreSQLConfig
    participant KVService as KVParamService
    participant Repo as 仓库接口

    Dev->>Init: 设计数据初始化需求
    Dev->>Init: 创建PostgreSQLDataInitializer类
    Dev->>Init: 实现JPA兼容的初始化逻辑
    Dev->>Init: 添加对不同DDL策略的支持
    Config->>KVService: 获取postgresql.ddl-auto参数
    KVService-->>Config: 返回DDL策略（无默认值）
    Config->>Init: 提供当前DDL策略
    Init->>Init: 根据DDL策略决定初始化行为
    Init->>Repo: 检查表是否为空
    Init->>Repo: 添加初始数据
```

**实现步骤**:

1. **设计数据初始化需求**
   - 确定需要初始化的数据类型和内容
   - 设计初始化逻辑
   - 确定不同环境下的初始化策略

2. **设计PostgreSQL配置类**
   - 创建PostgreSQLConfig类
   - 实现从KVParamService获取postgresql.ddl-auto参数的逻辑
   - 添加参数缺失时的错误处理，确保无默认值且缺少参数时应用无法启动
   - 提供获取DDL策略的方法供PostgreSQLDataInitializer使用
   - 实现Schema管理逻辑，根据配置自动创建Schema
   - 提供获取Schema列表的方法，用于Schema创建和管理

3. **设计PostgreSQL数据初始化器**
   - 创建PostgreSQLDataInitializer类
   - 实现类似Cassandra的RECREATE模式的功能
   - 支持不同的DDL策略（create, create-drop, update, validate, none）
   - 从PostgreSQLConfig获取DDL策略，而非使用@Value注解

4. **实现开发模式数据库表重置功能**
   - 从PostgreSQLConfig获取DDL策略
   - 根据策略值决定初始化行为：
     - 当ddl-auto=create或create-drop时：直接初始化数据（表已被重建）
     - 当ddl-auto=update或validate时：检查表是否为空，如果为空则初始化数据
     - 当ddl-auto=none时：不执行初始化操作
   - 添加异常处理，确保初始化过程不会中断应用启动

5. **PostgreSQLConfig类示例实现**:

```java
@Configuration
@EnableJpaRepositories(basePackages = "org.xkong.cloud.business.internal.core.repository")
@DependsOn("kvParamService")
public class PostgreSQLConfig {

    private static final Logger log = LoggerFactory.getLogger(PostgreSQLConfig.class);

    @Autowired
    private KVParamService kvParamService;

    private String ddlAuto;
    private List<String> schemaList;
    private boolean createSchemaAutomatically;

    /**
     * 获取DDL自动生成策略
     * 从KV参数服务获取postgresql.ddl-auto参数
     *
     * @return DDL自动生成策略
     */
    public String getDdlAuto() {
        if (ddlAuto == null) {
            // 使用KVParamService获取参数
            log.info("准备获取PostgreSQL DDL自动生成策略");

            // 使用KVParamService获取参数，不提供默认值
            ddlAuto = kvParamService.getParam("postgresql.ddl-auto");
            if (ddlAuto == null || ddlAuto.trim().isEmpty()) {
                // 如果KV服务未提供DDL策略，则记录错误并抛出异常，阻止启动
                log.error("PostgreSQL配置错误: 必需的'postgresql.ddl-auto'参数未在KV服务中找到。");
                throw new IllegalStateException("PostgreSQL DDL auto strategy ('postgresql.ddl-auto') must be configured in the KV service.");
            }

            // 验证DDL策略值的有效性
            List<String> validValues = Arrays.asList("none", "validate", "update", "create", "create-drop");
            if (!validValues.contains(ddlAuto.toLowerCase())) {
                log.error("PostgreSQL配置错误: 无效的DDL策略 '{}', 有效值为: {}", ddlAuto, validValues);
                throw new IllegalStateException("Invalid PostgreSQL DDL auto strategy: " + ddlAuto);
            }

            log.info("使用PostgreSQL DDL自动生成策略: {}", ddlAuto);
        }
        return ddlAuto;
    }

    /**
     * 获取Schema列表
     * 从KV参数服务获取postgresql.schema.list参数
     *
     * @return Schema列表
     */
    public List<String> getSchemaList() {
        if (schemaList == null) {
            // 使用KVParamService获取参数
            log.info("准备获取PostgreSQL Schema列表");

            // 使用KVParamService获取参数，提供默认值
            String schemaListStr = kvParamService.getParam("postgresql.schema.list", "user_management,common_config,infra_uid");
            schemaList = Arrays.asList(schemaListStr.split(","));

            log.info("使用PostgreSQL Schema列表: {}", schemaList);
        }
        return schemaList;
    }

    /**
     * 是否自动创建Schema
     * 从KV参数服务获取postgresql.schema.create-automatically参数
     *
     * @return 是否自动创建Schema
     */
    public boolean isCreateSchemaAutomatically() {
        // 使用KVParamService获取参数，提供默认值
        createSchemaAutomatically = Boolean.parseBoolean(
            kvParamService.getParam("postgresql.schema.create-automatically", "true"));
        return createSchemaAutomatically;
    }

    /**
     * 创建Schema
     * 根据配置自动创建Schema
     */
    public void createSchemas(JdbcTemplate jdbcTemplate) {
        if (isCreateSchemaAutomatically()) {
            List<String> schemas = getSchemaList();
            for (String schema : schemas) {
                try {
                    log.info("创建Schema: {}", schema);
                    jdbcTemplate.execute("CREATE SCHEMA IF NOT EXISTS " + schema);
                } catch (Exception e) {
                    log.warn("创建Schema失败: {}, 错误: {}", schema, e.getMessage());
                }
            }
        }
    }

    @Bean
    @Primary
    public DataSource dataSource() {
        HikariConfig config = new HikariConfig();

        // 从KVParamService获取基础连接参数
        String url = kvParamService.getParam("postgresql.url");
        String username = kvParamService.getParam("postgresql.username");
        String password = kvParamService.getParam("postgresql.password");

        // 验证必需参数
        if (url == null || url.trim().isEmpty()) {
            log.error("PostgreSQL配置错误: 必需的'postgresql.url'参数未在KV服务中找到。");
            throw new IllegalStateException("PostgreSQL URL ('postgresql.url') must be configured in the KV service.");
        }

        // 设置基础连接参数
        config.setJdbcUrl(url);
        config.setUsername(username);
        config.setPassword(password);

        // 从KVParamService获取连接池参数
        // postgresql.pool.max-size：开发环境推荐10，生产环境推荐CPU核心数×2-4
        int maxSize = Integer.parseInt(kvParamService.getParam("postgresql.pool.max-size", "10"));
        // postgresql.pool.min-idle：开发环境推荐5，生产环境推荐max-size的30%-50%
        int minIdle = Integer.parseInt(kvParamService.getParam("postgresql.pool.min-idle", "5"));
        // postgresql.pool.connection-timeout：开发环境推荐30000(30秒)，生产环境推荐5000-10000(5-10秒)
        int connectionTimeout = Integer.parseInt(kvParamService.getParam("postgresql.pool.connection-timeout", "30000"));
        // postgresql.pool.idle-timeout：开发环境推荐600000(10分钟)，生产环境推荐300000-900000(5-15分钟)
        int idleTimeout = Integer.parseInt(kvParamService.getParam("postgresql.pool.idle-timeout", "600000"));
        // postgresql.pool.max-lifetime：开发环境推荐1800000(30分钟)，生产环境推荐1800000-3600000(30-60分钟)
        int maxLifetime = Integer.parseInt(kvParamService.getParam("postgresql.pool.max-lifetime", "1800000"));

        // 设置连接池参数
        config.setMaximumPoolSize(maxSize);
        config.setMinimumIdle(minIdle);
        config.setConnectionTimeout(connectionTimeout);
        config.setIdleTimeout(idleTimeout);
        config.setMaxLifetime(maxLifetime);

        // 添加PostgreSQL特定属性
        config.addDataSourceProperty("stringtype", "unspecified"); // 处理未知类型字符串
        config.addDataSourceProperty("reWriteBatchedInserts", "true"); // 优化批量插入

        HikariDataSource dataSource = new HikariDataSource(config);

        // 创建JdbcTemplate用于创建Schema
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);

        // 创建Schema
        createSchemas(jdbcTemplate);

        return dataSource;
    }

    @Bean
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder builder, DataSource dataSource) {

        return builder
                .dataSource(dataSource)
                .packages("org.xkong.cloud.business.internal.core.entity")
                .persistenceUnit("postgresql")
                .properties(jpaProperties())
                .build();
    }

    private Map<String, Object> jpaProperties() {
        Map<String, Object> props = new HashMap<>();

        // 获取DDL自动生成策略
        String ddlAuto = getDdlAuto();

        // 从KVParamService获取其他JPA配置
        // postgresql.show-sql：开发环境推荐true以便调试，生产环境必须设置为false以提高性能和减少日志量
        boolean showSql = Boolean.parseBoolean(kvParamService.getParam("postgresql.show-sql", "false"));
        // postgresql.format-sql：开发环境推荐true以提高可读性，生产环境推荐false
        boolean formatSql = Boolean.parseBoolean(kvParamService.getParam("postgresql.format-sql", "true"));
        // postgresql.batch-size：开发环境默认30通常合适，生产环境建议根据性能测试结果调整为30-50
        int batchSize = Integer.parseInt(kvParamService.getParam("postgresql.batch-size", "30"));
        // postgresql.fetch-size：开发环境默认100通常合适，生产环境可根据查询特性调整为100-500
        int fetchSize = Integer.parseInt(kvParamService.getParam("postgresql.fetch-size", "100"));

        // 设置JPA属性
        props.put("hibernate.hbm2ddl.auto", ddlAuto);
        props.put("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
        props.put("hibernate.show_sql", showSql);
        props.put("hibernate.format_sql", formatSql);
        props.put("hibernate.jdbc.batch_size", batchSize);
        props.put("hibernate.jdbc.fetch_size", fetchSize);

        // 性能优化配置
        props.put("hibernate.jdbc.lob.non_contextual_creation", true);
        props.put("hibernate.order_inserts", true);
        props.put("hibernate.order_updates", true);
        props.put("hibernate.jdbc.batch_versioned_data", true);

        return props;
    }

    @Bean
    public PlatformTransactionManager transactionManager(
            EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
```

6. **PostgreSQLDataInitializer类示例实现**:

```java
@Component
public class PostgreSQLDataInitializer implements InitializingBean {

    private static final Logger log = LoggerFactory.getLogger(PostgreSQLDataInitializer.class);

    @Autowired
    private UserRepository userRepository;
    // 其他仓库...

    @Autowired
    private PostgreSQLConfig postgreSQLConfig;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 从PostgreSQLConfig获取DDL策略，而非使用@Value注解
        String ddlAuto = postgreSQLConfig.getDdlAuto();
        log.info("初始化数据，当前DDL模式: {}", ddlAuto);

        if ("create".equals(ddlAuto) || "create-drop".equals(ddlAuto)) {
            // 表已被重建，直接添加初始数据
            log.info("检测到create或create-drop模式，执行数据初始化");
            initializeData();
        } else if ("update".equals(ddlAuto) || "validate".equals(ddlAuto)) {
            // 其他模式：检查表是否为空，如果为空则添加初始数据
            try {
                if (userRepository.count() == 0) {
                    log.info("检测到空表，执行数据初始化");
                    initializeData();
                } else {
                    log.info("表中已有数据，跳过初始化");
                }
            } catch (Exception e) {
                log.warn("检查表数据时发生异常: {}", e.getMessage());
            }
        } else {
            log.info("当前DDL模式不执行数据初始化");
        }
    }

    private void initializeData() {
        log.info("开始添加初始数据...");
        try {
            // 初始化用户数据
            User admin = new User();
            admin.setName("管理员");
            admin.setMailAddress("<EMAIL>");
            admin.setRegistTime(LocalDateTime.now());
            userRepository.save(admin);

            // 初始化其他数据...

            log.info("初始数据添加完成");
        } catch (Exception e) {
            log.error("添加初始数据时发生错误: {}", e.getMessage(), e);
            // 不抛出异常，避免影响应用启动
        }
    }
}
```

7. **配置KV参数**
   - 在xkongcloud-service-center中配置PostgreSQL和百度UID生成器相关的KV参数
   - 采用模块化参数检查方式，确保应用只有在所有必需参数都正确配置的情况下才能启动
   - 不再修改application.properties中的kv.param.required-keys配置

   a. **必需的KV参数**

   以下是必需配置的KV参数列表：

   **PostgreSQL基础连接参数**：
   - `postgresql.url`：数据库连接URL，格式为jdbc:postgresql://主机:端口/数据库名（**必需，无默认值**）
   - `postgresql.username`：数据库用户名（**必需，无默认值**）
   - `postgresql.password`：数据库密码（**必需，无默认值**）
   - `postgresql.ddl-auto`：Hibernate DDL自动生成策略，可选值：none、validate、update、create、create-drop（**必需，无默认值**）

   **PostgreSQL Schema参数**：
   - `postgresql.schema.create-automatically`：是否自动创建Schema（可选，默认为"true"）
   - `postgresql.schema.list`：需要创建的Schema列表，用逗号分隔（可选，默认为"user_management,common_config,infra_uid"）

   **百度UID生成器核心参数**：
   - `uid.epochStr`：UID生成器的纪元时间，推荐值"2025-01-01"（**必需，无默认值**）
   - `uid.timeBits`：时间戳占用的位数，推荐值31（**必需，无默认值**）
   - `uid.workerBits`：工作机器ID占用的位数，推荐值18（**必需，无默认值**）
   - `uid.seqBits`：序列号占用的位数，推荐值14（**必需，无默认值**）

   **持久化实例ID及特征码恢复参数**：
   - `uid.instance.environment`：部署环境，如dev、prod、lan等（可选，默认为default）
   - `uid.instance.group`：实例分组或逻辑单元名（可选）
   - 其他可选参数...

   b. **参数检查机制**

   在PostgreSQLConfig和UidGeneratorConfig类中实现了模块化参数检查机制：
   - 每个配置类负责检查自己所需的必需参数
   - 如果缺少任何必需参数，应用将无法启动，并显示明确的错误信息
   - 这种方式比使用kv.param.required-keys配置更安全、更模块化

   c. **不同环境下的参数配置示例**

   **开发环境**：
   ```properties
   # PostgreSQL基础连接参数
   postgresql.url=**********************************************
   postgresql.username=postgres
   postgresql.password=password
   postgresql.schema=user_management
   postgresql.ddl-auto=create

   # PostgreSQL Schema参数
   postgresql.schema.create-automatically=true
   postgresql.schema.list=user_management,common_config,infra_uid

   # PostgreSQL连接池参数
   postgresql.pool.max-size=10
   postgresql.pool.min-idle=5
   postgresql.pool.connection-timeout=30000
   postgresql.pool.idle-timeout=600000
   postgresql.pool.max-lifetime=1800000

   # PostgreSQL JPA参数
   postgresql.show-sql=true
   postgresql.format-sql=true
   postgresql.batch-size=30
   postgresql.fetch-size=100

   # 百度UID生成器核心参数
   uid.epochStr=2025-01-01
   uid.timeBits=31
   uid.workerBits=18
   uid.seqBits=14
   ```

   **生产环境**：
   ```properties
   # PostgreSQL基础连接参数
   postgresql.url=********************************************
   postgresql.username=postgres
   postgresql.password=password
   postgresql.schema=core_business
   postgresql.ddl-auto=validate

   # PostgreSQL Schema参数
   postgresql.schema.create-automatically=false
   postgresql.schema.list=core_business,system_config,uid_generator

   # PostgreSQL连接池参数
   postgresql.pool.max-size=32
   postgresql.pool.min-idle=12
   postgresql.pool.connection-timeout=10000
   postgresql.pool.idle-timeout=300000
   postgresql.pool.max-lifetime=1800000

   # PostgreSQL JPA参数
   postgresql.show-sql=false
   postgresql.format-sql=false
   postgresql.batch-size=50
   postgresql.fetch-size=200

   # 百度UID生成器核心参数
   uid.epochStr=2025-01-01
   uid.timeBits=31
   uid.workerBits=18
   uid.seqBits=14
   ```

   > **警告**：在生产环境中，必须将`postgresql.ddl-auto`设置为`none`或`validate`，严禁使用`create`、`create-drop`或`update`，以防止意外删除或修改生产数据。

### 流程6: 测试与优化（第7-8周）

**流程描述**: 对迁移后的系统进行测试和性能优化

**流程图**:

```mermaid
sequenceDiagram
    participant Dev as 开发人员
    participant Test as 测试人员
    participant Unit as 单元测试
    participant Integ as 集成测试
    participant Perf as 性能测试

    Dev->>Unit: 编写实体类测试
    Dev->>Unit: 编写仓库接口测试
    Dev->>Unit: 编写数据初始化逻辑测试
    Dev->>Integ: 编写数据库连接测试
    Dev->>Integ: 编写CRUD操作测试
    Dev->>Integ: 编写复杂查询测试
    Test->>Perf: 执行查询性能测试
    Test->>Perf: 执行写入性能测试
    Test->>Perf: 执行并发性能测试
    Dev->>Dev: 优化性能瓶颈
```

**实现步骤**:

1. **单元测试**
   - 测试实体类
   - 测试仓库接口
   - 测试数据初始化逻辑
   - 测试百度UID生成器相关类：

     a. **测试WorkerNodeType枚举**

     ```java
     @Test
     public void testWorkerNodeTypeValues() {
         assertEquals(1, WorkerNodeType.CONTAINER.value());
         assertEquals(2, WorkerNodeType.ACTUAL.value());
     }

     @Test
     public void testWorkerNodeTypeOf() {
         assertEquals(WorkerNodeType.CONTAINER, WorkerNodeType.of(1));
         assertEquals(WorkerNodeType.ACTUAL, WorkerNodeType.of(2));
     }

     @Test
     public void testWorkerNodeTypeOfInvalid() {
         assertThrows(IllegalArgumentException.class, () -> WorkerNodeType.of(99));
     }
     ```

     b. **测试NetUtils工具类**

     ```java
     @Test
     public void testGetLocalAddress() {
         // 测试正常情况
         String ipAddress = NetUtils.getLocalAddress();
         assertNotNull(ipAddress);
         assertTrue(isValidIpAddress(ipAddress));

         // 测试环境变量优先级
         try {
             // 使用反射设置环境变量（仅用于测试）
             setEnv("HOST_IP", "*************");
             assertEquals("*************", NetUtils.getLocalAddress());
         } finally {
             // 清理环境变量
             setEnv("HOST_IP", null);
         }
     }

     // 辅助方法：验证IP地址格式
     private boolean isValidIpAddress(String ip) {
         String regex = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
         return ip.matches(regex);
     }
     ```

     c. **测试ConfigurableWorkerIdAssigner**

     ```java
     @Test
     public void testAssignWorkerId() {
         // 准备测试数据
         JdbcTemplate jdbcTemplate = mock(JdbcTemplate.class);
         TransactionTemplate transactionTemplate = mock(TransactionTemplate.class);

         ConfigurableWorkerIdAssigner assigner = new ConfigurableWorkerIdAssigner();
         ReflectionTestUtils.setField(assigner, "jdbcTemplate", jdbcTemplate);
         ReflectionTestUtils.setField(assigner, "transactionTemplate", transactionTemplate);

         // 模拟事务执行
         when(transactionTemplate.execute(any())).thenAnswer(invocation -> {
             TransactionCallback<?> callback = invocation.getArgument(0);
             return callback.doInTransaction(null);
         });

         // 模拟首次分配（未找到记录）
         when(jdbcTemplate.queryForList(anyString(), eq(Long.class), anyString(), anyString()))
             .thenReturn(Collections.emptyList());
         when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), anyString(), anyString(), anyInt(), any(), any(), any()))
             .thenReturn(123L);

         // 执行测试
         long workerId = assigner.assignWorkerId();

         // 验证结果
         assertEquals(123L, workerId);
         verify(jdbcTemplate).queryForList(contains("SELECT ID"), eq(Long.class), anyString(), anyString());
         verify(jdbcTemplate).queryForObject(contains("INSERT INTO WORKER_NODE"), eq(Long.class), anyString(), anyString(), anyInt(), any(), any(), any());
     }

     @Test
     public void testAssignWorkerIdReuse() {
         // 准备测试数据
         JdbcTemplate jdbcTemplate = mock(JdbcTemplate.class);
         TransactionTemplate transactionTemplate = mock(TransactionTemplate.class);

         ConfigurableWorkerIdAssigner assigner = new ConfigurableWorkerIdAssigner();
         ReflectionTestUtils.setField(assigner, "jdbcTemplate", jdbcTemplate);
         ReflectionTestUtils.setField(assigner, "transactionTemplate", transactionTemplate);

         // 模拟事务执行
         when(transactionTemplate.execute(any())).thenAnswer(invocation -> {
             TransactionCallback<?> callback = invocation.getArgument(0);
             return callback.doInTransaction(null);
         });

         // 模拟复用（找到记录）
         when(jdbcTemplate.queryForList(anyString(), eq(Long.class), anyString(), anyString()))
             .thenReturn(Arrays.asList(456L));

         // 执行测试
         long workerId = assigner.assignWorkerId();

         // 验证结果
         assertEquals(456L, workerId);
         verify(jdbcTemplate).queryForList(contains("SELECT ID"), eq(Long.class), anyString(), anyString());
         verify(jdbcTemplate, never()).queryForObject(contains("INSERT INTO WORKER_NODE"), eq(Long.class), any());
     }
     ```

2. **测试计划**

   参考[PostgreSQL迁移第3阶段测试计划](./phase3-testing-plan.md)进行全面的参数化测试，包括：
   - 配置验证测试
   - 连接稳定性测试
   - UID生成器集成测试
   - 演进架构基础测试
   - 参数化测试体系验证

### 流程7: 部署与监控（第9周）

**流程描述**: 将迁移后的系统部署到生产环境并进行监控

**流程图**:

```mermaid
sequenceDiagram
    participant Dev as 开发人员
    participant Ops as 运维人员
    participant Test as 测试环境
    participant Prod as 生产环境
    participant Monitor as 监控系统

    Dev->>Dev: 准备部署脚本
    Dev->>Dev: 准备回滚脚本
    Dev->>Ops: 提供部署文档
    Ops->>Test: 部署到测试环境
    Ops->>Test: 验证功能
    Ops->>Prod: 部署到生产环境
    Ops->>Monitor: 配置数据库连接监控
    Ops->>Monitor: 配置查询性能监控
    Ops->>Monitor: 配置系统资源监控
```

**实现步骤**:

1. **部署准备**
   - 准备部署脚本
   - 准备回滚脚本
   - 准备监控脚本

2. **部署**
   - 部署到测试环境
   - 验证功能
   - 部署到生产环境

3. **监控**
   - 监控数据库连接
   - 监控查询性能
   - 监控系统资源使用情况
   - 监控百度UID生成器（详细监控建议请参考 [基于PostgreSQL的百度UID生成器实现方案](../../../common/middleware/integration/baidu-uid-generator-postgresql-implementation.md#并发安全性考虑)）：

     a. **监控WORKER_NODE表**

     ```sql
     -- 查询WORKER_NODE表记录数
     SELECT COUNT(*) FROM WORKER_NODE;

     -- 查询最近添加的工作节点
     SELECT * FROM WORKER_NODE ORDER BY CREATED DESC LIMIT 10;

     -- 查询可能的重复主机名（应该为0）
     SELECT HOST_NAME, PORT, COUNT(*)
     FROM WORKER_NODE
     GROUP BY HOST_NAME, PORT
     HAVING COUNT(*) > 1;

     -- 查询工作节点类型分布
     SELECT TYPE, COUNT(*)
     FROM WORKER_NODE
     GROUP BY TYPE;
     ```

     b. **设置监控指标**

     | 指标名称 | 说明 | 监控频率 | 告警阈值 |
     |---------|------|---------|---------|
     | worker_node_count | WORKER_NODE表记录总数 | 每小时 | >1000 |
     | worker_node_growth_rate | WORKER_NODE表记录增长率 | 每天 | >50/天 |
     | uid_generation_rate | UID生成速率 | 每分钟 | N/A (仅监控) |
     | uid_generation_errors | UID生成错误数 | 实时 | >0 |
     | worker_id_assignment_time | 工作机器ID分配耗时 | 每次分配 | >1秒 |

     c. **配置日志监控**

     监控以下日志模式：

     ```
     // 工作机器ID分配成功
     "成功创建新的工作机器ID: *"
     "复用已存在的工作机器ID: *"

     // 工作机器ID分配失败
     "分配工作机器ID时发生异常*"
     "无法分配工作机器ID*"

     // IP地址获取问题
     "获取本机IP地址失败*"

     // UID生成器配置问题
     "UID生成器配置错误*"

     // RingBuffer状态
     "RingBuffer已耗尽*"
     "RingBuffer填充率低于阈值*"
     ```

     d. **创建监控仪表板**

     创建包含以下内容的监控仪表板：

     - WORKER_NODE表记录数趋势图
     - 每日新增工作节点数柱状图
     - UID生成速率实时曲线图
     - UID生成错误计数器
     - 工作机器ID分配耗时分布图
     - RingBuffer填充率趋势图

     e. **设置告警规则**

     | 告警名称 | 条件 | 严重性 | 通知方式 |
     |---------|------|-------|---------|
     | worker_node_explosion | WORKER_NODE表记录数增长异常 | 高 | 邮件+短信 |
     | uid_generation_failure | UID生成错误 | 高 | 邮件+短信 |
     | worker_id_assignment_slow | 工作机器ID分配耗时超过1秒 | 中 | 邮件 |
     | ringbuffer_depletion | RingBuffer填充率低于20% | 中 | 邮件 |
     | ip_address_failure | 无法获取有效IP地址 | 高 | 邮件+短信 |

     f. **定期维护计划**

     - 每月检查WORKER_NODE表增长情况
     - 每季度清理不再使用的工作节点记录
     - 每半年评估工作机器ID位数是否足够
     - 每年评估时间戳位数是否足够

## 测试策略

### 单元测试

1. **实体类测试**
   - 测试实体类的注解
   - 测试实体类的关系
   - 测试实体类的验证

2. **仓库接口测试**
   - 测试基本CRUD操作
   - 测试查询方法
   - 测试自定义查询

### 集成测试

1. **数据库连接测试**
   - 测试数据库连接
   - 测试连接池
   - 测试事务支持

2. **功能测试**
   - 测试CRUD操作
   - 测试复杂查询
   - 测试批量操作
   - 测试事务操作

### 性能测试

1. **查询性能测试**
   - 测试简单查询性能
   - 测试复杂查询性能
   - 测试分页查询性能

2. **写入性能测试**
   - 测试单条插入性能
   - 测试批量插入性能
   - 测试更新性能
   - 测试删除性能

3. **并发性能测试**
   - 测试并发读取性能
   - 测试并发写入性能
   - 测试混合负载性能

## 异常处理

### 技术风险

1. **性能风险**
   - 风险：PostgreSQL的性能可能不如Cassandra
   - 缓解措施：优化查询、添加适当的索引、使用连接池、使用批处理

2. **兼容性风险**
   - 风险：JPA的功能可能不完全兼容Cassandra的功能
   - 缓解措施：识别不兼容的功能，设计替代方案

3. **数据类型风险**
   - 风险：Cassandra和PostgreSQL的数据类型映射可能不完全兼容
   - 缓解措施：仔细设计数据类型映射，进行充分测试

### 项目风险

1. **进度风险**
   - 风险：迁移可能需要比计划更长的时间
   - 缓解措施：设置合理的缓冲时间，优先迁移核心功能

2. **资源风险**
   - 风险：可能缺乏PostgreSQL或JPA的专业知识
   - 缓解措施：提供培训，聘请外部专家

3. **回滚风险**
   - 风险：迁移失败后可能难以回滚
   - 缓解措施：准备详细的回滚计划，保留Cassandra环境

## 部署说明

### 环境要求

- **JDK版本**: JDK 21
- **依赖服务**:
  - PostgreSQL 17.4版本
  - xkongcloud-service-center
- **配置文件**: application.properties
- **资源要求**: 4核CPU，8GB内存，50GB磁盘空间

#### PostgreSQL配置要求

- 确保PostgreSQL服务器已安装并正确配置
- 创建专用数据库用户，具有创建表和索引的权限
- 创建instance_registry和worker_id_assignment表及相关索引（见基础设施准备阶段的SQL脚本）
- 配置适当的连接池参数，建议最大连接数不少于20

#### 百度UID生成器配置要求（详细配置请参考 [基于PostgreSQL的持久化实例ID及特征码恢复方案](../../../common/middleware/integration/postgresql-persistent-id-fingerprint-recovery.md)）

- 在xkongcloud-service-center中配置以下必需的KV参数：
  - `uid.epochStr`: 时间基点，格式为"yyyy-MM-dd"，推荐值"2025-01-01"
  - `uid.timeBits`: 时间戳位数，推荐值31
  - `uid.workerBits`: 工作机器ID位数，推荐值18
  - `uid.seqBits`: 序列号位数，推荐值14
  - 注意：不需要配置`uid.instance.application-name`，将使用`xkong.kv.cluster-id`的值

- 在xkongcloud-service-center中配置以下可选的KV参数：
  - `uid.boostPower`: RingBuffer扩容参数，推荐值3
  - `uid.paddingFactor`: RingBuffer填充因子，推荐值50
  - `uid.scheduleInterval`: RingBuffer调度时间间隔(秒)，推荐值60
  - `uid.instance.environment`: 部署环境，如"dev"、"prod"、"lan"等
  - `uid.instance.group`: 实例分组或逻辑单元名
  - `uid.instance.local-storage-path`: 本地实例ID存储路径
  - `uid.instance.recovery.enabled`: 是否启用特征码恢复
  - `uid.worker.lease-duration-seconds`: 工作机器ID租约时长(秒)

#### 网络环境要求

- 确保应用服务器能够访问PostgreSQL服务器
- 确保应用服务器能够访问xkongcloud-service-center
- 在容器环境中，考虑使用环境变量`HOST_IP`注入主机IP地址
- 在NAT或代理环境中，考虑使用系统属性`-Dhost.ip`指定外部可访问IP地址

#### 监控要求

- 配置日志收集系统，收集UID生成器相关日志
- 配置数据库监控，监控instance_registry和worker_id_assignment表的状态
  - 监控instance_registry表中实例的注册和活跃情况
  - 监控worker_id_assignment表中工作机器ID的分配和租约状态
- 配置性能监控，监控UID生成速率和响应时间
- 配置告警系统，监控以下异常情况：
  - 实例ID恢复失败
  - 工作机器ID分配失败
  - 租约续约失败
  - 特征码匹配分数过低

### 回滚触发条件

1. 迁移后的系统性能明显下降
2. 迁移后的系统出现严重bug
3. 迁移后的系统不能满足业务需求

### 部署步骤

1. 确保PostgreSQL服务器已安装并配置
2. 在xkongcloud-service-center中配置所有必需的PostgreSQL参数
3. 部署新版本的xkongcloud-business-internal-core
4. 启动应用并验证数据库连接
5. 执行基本功能测试
6. 监控系统性能和稳定性

### 验证方法

- 检查应用日志，确认数据库连接成功
- 执行基本的CRUD操作，确认功能正常
- 监控数据库连接池使用情况
- 监控查询执行时间
- 监控系统资源使用情况

### 回滚步骤

1. **停止应用**
   - 停止使用PostgreSQL的应用

2. **恢复依赖**
   - 恢复pom.xml中的Cassandra依赖
   - 移除JPA和PostgreSQL依赖
   - 移除 `com.xfvape.uid:uid-generator` 依赖。

3. **恢复配置**
   - 恢复CassandraConfig类
   - 移除PostgreSQLConfig类
   - 移除 `UidGeneratorConfig` 类及相关的 `PersistentInstanceWorkerIdAssigner` 实现、`PersistentInstanceManager` 类和 `MachineFingerprints` 类。

4. **恢复实体类**
   - 恢复Cassandra实体类
   - 移除JPA实体类

5. **恢复仓库接口**
   - 恢复CassandraRepository接口
   - 移除JpaRepository接口

6. **恢复数据初始化逻辑**
   - 恢复DataInitializer类的Cassandra版本

7. **启动应用**
   - 启动使用Cassandra的应用
   - 验证功能

## 注意事项

**特别说明**：由于项目当前处于初始阶段，Cassandra数据库中尚未包含任何生产环境的业务数据，应用程序中也尚未实现依赖于特定数据库的复杂业务逻辑，因此本次技术栈切换的注意事项与传统数据迁移有所不同。

- 所有数据库连接参数必须通过KVParamService从xkongcloud-service-center获取，严禁在代码或本地配置文件中硬编码
- 生产环境必须设置DDL自动生成策略为none或validate，所有数据库结构变更应通过数据库迁移工具管理
- 切换后需要进行全面的测试，包括功能测试、性能测试和并发测试，确保系统稳定性
- 保留Cassandra环境直到确认PostgreSQL切换完全成功，虽然不需要数据迁移，但保留原环境作为备份仍然是必要的

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 2.6 | 2025-06-13 | 添加ALERT_AUTO_WITH_TIMEOUT恢复策略和超时参数 | AI助手 |
| 2.5 | 2025-06-10 | 添加UID加密功能相关配置和参数 | AI助手 |
| 2.4 | 2025-06-07 | 取消postgresql.schema.create-automatically参数，明确Schema由人工管理 | AI助手 |
| 2.3 | 2025-06-06 | 移除postgresql.schema参数，简化配置，强制所有实体类必须明确指定Schema | AI助手 |
| 2.2 | 2025-06-05 | 更新Schema命名规范：将core_business改为user_management，system_config改为common_config，uid_generator改为infra_uid，完全符合Schema命名规范 | AI助手 |
| 2.1 | 2025-06-02 | 修改UidTableManagerService和UidTableCleanupListener实现，明确指定使用Schema | AI助手 |
| 2.0 | 2025-05-28 | 修改参数管理方式，采用模块化参数检查替代kv.param.required-keys配置；更新Schema配置，符合PostgreSQL最佳实践，使用多Schema组织方式替代单一public schema | AI助手 |
| 1.9 | 2025-05-27 | 明确流程1中不需要手动创建UID生成器所需的表结构，表结构将在流程2中由UidTableManagerService自动创建和管理 | AI助手 |
| 1.8 | 2025-05-27 | 更新UidTableManager和UidTableCleanupListener实现，使用xkongcloud-commons-uid公共库中的UidTableManager工具类 | AI助手 |
| 1.7 | 2025-05-26 | 添加实施顺序与依赖关系章节，明确PostgreSQL迁移和xkongcloud-commons-uid公共库的实施顺序和依赖关系 | AI助手 |
| 1.6 | 2025-05-24 | 添加UidTableManager和UidTableCleanupListener，使百度UID生成器相关表支持与JPA实体表相同的DDL策略 | AI助手 |
| 1.5 | 2025-05-23 | 更新实施计划，使用xkongcloud-commons-uid公共库替代直接实现UID生成器相关类 | AI助手 |
| 1.4 | 2025-05-22 | 将基于IP和端口的百度UID生成器实现替换为基于持久化实例ID及特征码恢复的方案，增强实例迁移场景下的身份恢复能力 | AI助手 |
| 1.3 | 2025-05-21 | 添加Cassandra无数据说明，明确不需要实体类转换等 | AI助手 |
| 1.2 | 2025-05-21 | 添加百度UID生成器PostgreSQL实现文档的引用链接，完善文档关联关系 | AI助手 |
| 1.1 | 2025-05-20 | 集成百度UID生成器PostgreSQL实现方案，添加详细的实现步骤、测试方法和监控要求 | AI助手 |
| 1.0 | 2025-05-08 | 初始版本 | AI助手 |
