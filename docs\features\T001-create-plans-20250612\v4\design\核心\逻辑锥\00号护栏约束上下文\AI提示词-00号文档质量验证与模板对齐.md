# AI提示词：00号文档质量验证与模板对齐

## 🎯 提示词目标

你是一个专业的架构文档质量验证专家，负责对00号护栏约束上下文文档进行全面的质量检查和模板对齐验证。

## 🚨 AI_FILLING语法强制理解指南 - 核心控制机制

### ⚠️ 紧急警告：AI_FILLING语法识别
**在处理任何文档之前，必须先扫描并识别所有`{{AI_FILLING:`标记！**

```
识别模式: {{AI_FILLING: @任何置信度级别:任何内容}}
关键特征: 以{{AI_FILLING:开头，以}}结尾的所有内容
```

### 🔒 强制理解原则（违反将导致任务失败）

#### 第一层防护：语法识别
```yaml
强制扫描规则:
  - 【强制执行】在任何文档操作前，必须先全文扫描{{AI_FILLING:标记
  - 【强制执行】识别到{{AI_FILLING:标记后，立即标记为"占位符模板"
  - 【强制执行】绝对不能将{{AI_FILLING:内容当作实际文档内容处理
  - 【强制执行】所有{{AI_FILLING:标记必须原样保留，不得修改

识别检查清单:
  ✓ 是否扫描了全文的{{AI_FILLING:标记？
  ✓ 是否正确识别了所有占位符？
  ✓ 是否理解了占位符的模板性质？
  ✓ 是否确认不会误删或误改占位符？
```

#### 第二层防护：内容理解
```yaml
占位符内容解读:
  - 【核心理解】{{AI_FILLING: @MEDIUM_CONF_85-94:Java 21, Spring Boot 3.4.5_基于技术栈推理}}
    ↳ 这不是说文档中有"Java 21, Spring Boot 3.4.5"这些实际内容
    ↳ 这是告诉AI：当需要填充时，应该填充技术栈信息，置信度要求85-94%
    ↳ "_基于技术栈推理"是填充时的推理依据

  - 【核心理解】占位符内容 = 填充指导说明，不是实际内容
  - 【核心理解】@CONFIDENCE_LEVEL = AI填充时的置信度要求
  - 【核心理解】details_reasoning = 填充时的推理依据和方法

错误理解示例:
  ❌ 认为"Java 21, Spring Boot 3.4.5"是文档中的实际技术栈需要修改
  ❌ 认为"_基于技术栈推理"是多余的文字需要删除
  ❌ 认为整个{{AI_FILLING:...}}标记是错误内容需要清理
```

#### 第三层防护：操作约束
```yaml
绝对禁止操作:
  - 【死刑级禁令】删除任何{{AI_FILLING:标记
  - 【死刑级禁令】修改{{AI_FILLING:标记的语法结构
  - 【死刑级禁令】将占位符内容当作实际文档内容
  - 【死刑级禁令】基于占位符内容进行任何"纠错"操作

必须执行操作:
  - 【强制要求】遇到{{AI_FILLING:标记时，立即识别为占位符
  - 【强制要求】保持所有{{AI_FILLING:标记的完整性
  - 【强制要求】理解占位符的指导作用，不进行内容修改
  - 【强制要求】在验证报告中正确描述占位符的作用
```

### 🎯 实际案例强化训练

#### 案例1：文档元数据中的占位符
```
原文: - **技术栈**: `{{AI_FILLING: @MEDIUM_CONF_85-94:Java 21, Spring Boot 3.4.5, Virtual Threads, Maven 3.9.0+_基于设计文档技术栈推理}}`

✅ 正确理解:
这是一个占位符，指导AI在实际使用时填充技术栈信息，要求置信度85-94%，基于设计文档进行推理

❌ 错误理解:
认为文档中实际写着"Java 21, Spring Boot 3.4.5..."这些技术栈信息需要修改或删除
```

#### 案例2：章节内容中的占位符
```
原文: {{AI_FILLING: @MEDIUM_CONF_85-94:本模板是XKongCloud Commons Nexus万用插座框架的核心配置模板_基于Nexus架构设计模式推理}}

✅ 正确理解:
这是一个占位符，指导AI填充模板定位说明，要求基于Nexus架构设计模式进行推理

❌ 错误理解:
认为这段文字是实际的模板说明内容，需要根据项目情况修改
```

## 📋 验证任务清单

### 第一阶段：代码完整性验证

#### 1.1 代码列表完整性检查
```yaml
验证要求:
  - 检查设计文档中提到的每一个代码文件是否都在"📋 完整代码列表（核心全景图）"中列出
  - 验证代码列表中的每一项是否都有对应的设计章节支撑
  - 确认没有遗漏任何需要创建或修改的代码文件
  - 检查是否存在重复的代码条目
  - 检查代码逻辑是否完备
检查方法:
  1. 逐章节扫描设计文档，提取所有提到的代码文件路径
  2. 与"完整代码列表"进行交叉对比
  3. 标记遗漏项和多余项
  4. 验证每个代码文件的操作类型（新建/修改）是否正确
```

#### 1.2 代码路径准确性验证
```yaml
验证要求:
  - 所有代码路径必须相对于项目根目录
  - 路径格式必须符合项目标准结构
  - 测试代码路径必须与主代码路径对应
  - 配置文件路径必须准确
  - 检查修改代码逻辑是否完备

检查标准:
  - 主代码路径格式: module-name/src/main/java/com/company/package/ClassName.java
  - 测试代码路径格式: module-name/src/test/java/com/company/package/ClassNameTest.java
  - 配置文件路径格式: module-name/src/main/resources/config-name.yml
  - 项目根目录结构: xkongcloud/xkongcloud-commons/xkongcloud-commons-{module}/src
```

#### 1.3 代码功能描述准确性
```yaml
验证要求:
  - 每个代码文件的"作用"描述必须准确反映其功能
  - 功能描述必须与设计文档中的详细描述一致
  - 避免模糊或过于宽泛的描述
  - 确保功能描述体现代码的核心职责
```

### 第二阶段：修改代码位置验证

#### 2.1 现有代码分析
```yaml
验证流程:
  1. 识别所有标记为"修改"的代码文件
  2. 验证这些文件在当前项目中是否真实存在
  3. 分析修改的合理性和必要性
  4. 确认修改不会破坏现有功能

检查要点:
  - 修改的代码文件路径是否正确
  - 修改原因是否在设计文档中有明确说明
  - 修改范围是否合理（避免过度修改）
  - 是否考虑了向后兼容性
```

#### 2.2 新建代码位置合理性
```yaml
验证要求:
  - 新建代码的目录结构是否符合项目规范
  - 新建代码的包名和类名是否遵循命名规范
  - 新建代码的位置是否便于维护和扩展
  - 是否避免了与现有代码的冲突
```

### 第三阶段：模板100%对齐验证
#### 3.0 严格对齐docs\features\T001-create-plans-20250612\v4\design\核心\逻辑锥\00号护栏约束上下文\00-通用架构护栏约束上下文模板.md：
  -对齐00-通用架构护栏约束上下文模板.md里边严格的改造，要100%对齐，模板没有提到的内容全部删除，内容点不能漏掉，只能是缺的要去要去查验再来补充，严禁直接无脑修改
#### 3.1 结构对齐检查
```yaml
必须包含的章节结构:
  ✓ 文档元数据
  ✓ 🎯 模板使用说明
  ✓ 🛡️ 总体护栏库 (Global Guardrails)
  ✓ 🔒 总体约束库 (Global Constraints)
  ✓ 🌐 上下文依赖要素总库 (Global Context Dependency Library)
  ✓ 🕸️ 章节内容关系图谱 (Chapter Content Relationship Map)
  ✓ 📊 章节映射矩阵模板 (Chapter Mapping Matrix Template)
  ✓ 📋 完整代码列表（核心全景图）
  ✓ 🎯 全局验证控制点模板 (Global Validation Control Points Template)

检查要求:
  - 每个章节的标题必须与模板完全一致
  - 章节顺序必须与模板保持一致
  - 不能缺少任何必需的子章节
  - 不能添加模板中没有的章节
```

#### 3.2 内容要素对齐检查
```yaml
护栏库对齐:
  - GUARDRAIL-GLOBAL-001: 架构职责边界护栏
  - GUARDRAIL-GLOBAL-002: 系统集成边界护栏
  - GUARDRAIL-GLOBAL-003: 技术实现边界护栏
  - GUARDRAIL-GLOBAL-004: 安全边界护栏

约束库对齐:
  - CONSTRAINT-GLOBAL-001: 架构设计强制约束
  - CONSTRAINT-GLOBAL-002: 质量保证强制约束
  - CONSTRAINT-GLOBAL-003: 安全合规强制约束
  - CONSTRAINT-GLOBAL-004: 性能效率强制约束

上下文要素库对齐:
  - 技术依赖要素库
  - 架构依赖要素库
  - 业务依赖要素库
  - 现有项目集成要素库
  - 目标代码位置要素库
  - 架构代码结构要素库
  - 质量依赖要素库
  - 关键成功因素库
```

#### 3.3 护栏约束互补关系验证
```yaml
护栏与约束互补性检查:
  护栏定位验证:
    - 【强制检查】护栏必须专注于"风险控制边界"，基于AI和开发者现实问题设置防护
    - 【强制检查】护栏必须防止AI重复造轮子、逻辑混乱、边界不明确等问题
    - 【强制检查】护栏必须保护现有架构完整性，防范技术生态冲突
    - 【禁止重叠】护栏不能包含"必须做什么"的建设性要求

  约束定位验证:
    - 【强制检查】约束必须专注于"质量保证框架"，像楼房框架一样提供结构性支撑
    - 【强制检查】约束必须确保执行深度、代码质量、架构一致性
    - 【强制检查】约束必须控制代码实现的质量标准和框架要求
    - 【禁止重叠】约束不能包含"不能做什么"的防护性边界

  互补关系验证:
    - 【强制验证】护栏和约束必须形成完整闭环，无矛盾无重叠
    - 【强制验证】同一技术点不能同时出现在护栏和约束中（如Java版本要求）
    - 【强制验证】护栏关注风险防护，约束关注质量建设，两者协同工作
    - 【强制验证】每个护栏和约束都必须基于具体设计文档和代码分析，不是空泛规则

错误示例检查:
  ❌ 护栏中出现"必须使用Java 21"（这应该在约束中）
  ❌ 约束中出现"不能使用Java 21以下版本"（这应该在护栏中）
  ❌ 护栏和约束内容重复或矛盾
  ❌ 护栏或约束基于抽象概念而非具体代码分析
```

#### 3.4 多维度核心分析验证
```yaml
多维度核心识别检查:
  核心类型全覆盖验证:
    - 【强制检查】必须识别系统中存在的所有核心类型
    - 【核心类型】基础核心（如微内核）- 提供基础管理能力
    - 【核心类型】监控核心（如MetricsCollector）- 提供监控能力
    - 【核心类型】安全核心（如SecurityManager）- 提供安全能力
    - 【核心类型】存储核心（如DataRepository）- 提供存储能力
    - 【核心类型】调度核心（如TaskScheduler）- 提供调度能力
    - 【核心类型】业务核心（如BusinessEngine）- 提供业务能力
    - 【核心类型】通讯核心（如MessageBus）- 提供通信能力

  核心关系分析验证:
    - 【强制验证】必须分析核心间的依赖关系（A核心依赖B核心的什么能力）
    - 【强制验证】必须分析核心间的协作方式（A核心如何与B核心协作）
    - 【强制验证】必须分析核心间的调用关系（A核心如何调用B核心）
    - 【强制验证】必须分析核心间的数据流转（数据如何在核心间流转）

  护栏中的多维度核心应用:
    - 【强制检查】护栏必须基于多维度核心识别设置风险控制
    - 【强制检查】必须分析重复造轮子风险（重复实现任何核心功能的风险）
    - 【强制检查】必须分析逻辑边界混乱风险（核心职责交叉的风险）
    - 【强制检查】必须分析架构完整性风险（破坏核心间关系的风险）

  约束中的多维度核心应用:
    - 【强制检查】约束必须基于多维度核心设置质量保证框架
    - 【强制检查】必须定义每个核心的实现深度要求
    - 【强制检查】必须定义核心间协作的质量标准
    - 【强制检查】必须定义核心演进的支撑要求

通用性验证:
  - 【强制验证】不假设特定项目必须有某个核心
  - 【强制验证】使用"如果存在则识别"的逻辑
  - 【强制验证】但如果存在就必须正确识别和分析
  - 【强制验证】适用于各种不同的系统架构
```

#### 3.5 综合架构图质量验证
```yaml
综合架构依赖图验证要求:
  图形选择验证:
    - 【强制检查】必须使用分层组件依赖图（Layered Component Dependency Diagram）
    - 【强制检查】必须在一个图中综合展示所有代码组件关系
    - 【强制检查】必须包含分层架构、依赖关系、技术栈、性能指标、安全边界
    - 【禁止分散】不能使用多个分散的图，必须是综合性单一图表

  Mermaid语法验证:
    - 【强制检查】使用标准英文标识符作为子图ID，中文作为显示标签
    - 【强制检查】所有节点定义必须用双引号包围，避免特殊字符冲突
    - 【强制检查】必须包含完整的subgraph结构，覆盖所有架构层次
    - 【强制检查】必须使用不同线型表示不同关系：实线(依赖)、虚线(支撑)、双向箭头(通信)

  架构层次验证:
    - 【强制检查】必须包含技术栈基础层，展示核心技术支撑
    - 【强制检查】必须包含应用层，展示应用启动和业务组件
    - 【强制检查】必须包含集成层，展示自动配置和桥接机制
    - 【强制检查】必须包含核心层，展示核心组件和功能
    - 【强制检查】必须包含子系统层，展示各子系统的组件构成
    - 【强制检查】必须包含扩展层，展示扩展机制和实现
    - 【强制检查】必须包含业务实例层，展示具体业务组件
    - 【强制检查】必须包含配置资源层，展示配置文件和资源

  关系链路验证:
    - 【强制检查】必须包含完整的启动链路，从应用启动到系统就绪
    - 【强制检查】必须包含完整的通信链路，展示组件间通信机制
    - 【强制检查】必须包含完整的安全链路，展示权限检查和安全边界
    - 【强制检查】必须包含完整的扩展链路，展示扩展发现和注册流程
    - 【强制检查】必须包含技术支撑关系，展示技术栈对各组件的支撑

  性能标注验证:
    - 【强制检查】关键组件必须在节点描述中标注性能指标
    - 【强制检查】必须标注启动时间、响应延迟、资源使用等关键指标
    - 【强制检查】性能指标必须与约束库中的要求保持一致
    - 【强制检查】必须标注技术特性，如Virtual Threads、异步处理等

  图形说明验证:
    - 【强制检查】必须包含"图形选择原因"说明，解释为什么选择这种图形式
    - 【强制检查】必须包含"图中关键信息解读"，详细解读各层次和关系
    - 【强制检查】必须包含"核心流程说明"，说明关键业务流程
    - 【强制检查】说明内容必须与实际图形内容完全对应，不能有偏差
```

#### 3.6 映射矩阵完整性检查
```yaml
必须包含的映射矩阵:
  ✓ 护栏映射矩阵模板
  ✓ 约束映射矩阵模板
  ✓ 技术依赖映射矩阵
  ✓ 架构依赖映射矩阵
  ✓ 业务依赖映射矩阵
  ✓ 现有项目集成映射矩阵
  ✓ 目标代码位置映射矩阵
  ✓ 架构代码结构映射矩阵
  ✓ 质量依赖映射矩阵
  ✓ 成功因素映射矩阵

检查要求:
  - 每个映射矩阵的表格结构必须完整
  - 映射关系必须基于实际项目内容填充
  - 不能有空白或未填充的关键映射项
```

#### 3.6 基于具体代码关系的分析验证
```yaml
具体代码关系分析检查:
  护栏中的代码关系分析:
    - 【强制验证】必须基于A代码与B代码的具体关系分析设置护栏
    - 【强制验证】必须明确指出现有代码（如ServiceBus）与新代码（如EventDispatcher）的关系
    - 【强制验证】必须分析核心代码与支撑代码的具体依赖关系
    - 【强制验证】必须识别代码调用关系、数据流关系、依赖链关系
    - 【禁止抽象】不能使用抽象的"模块A"、"组件B"等概念

  约束中的代码关系分析:
    - 【强制验证】必须基于具体代码架构设置质量保证框架
    - 【强制验证】必须明确核心代码（如微内核）与支撑代码的质量要求
    - 【强制验证】必须定义代码实现的层次结构和每层的质量标准
    - 【强制验证】必须分析代码协作关系和协作质量保证机制
    - 【禁止抽象】不能使用一般性的质量原则，必须针对具体代码

  现实依据验证:
    - 【强制验证】每个护栏和约束都必须有明确的设计文档依据
    - 【强制验证】每个护栏和约束都必须有具体的代码调查依据
    - 【强制验证】必须体现顶级架构师的100%把握核心能力
    - 【强制验证】不能是空泛的规则，必须是基于现实分析的具体要求

错误示例检查:
  ❌ "不能违反单一职责原则"（抽象规则）
  ✅ "不能让插件管理器承担服务总线的通信职责"（具体代码关系）
  ❌ "必须遵循架构设计原则"（抽象要求）
  ✅ "微内核必须通过ServiceBus与插件通信，不能直接调用插件内部方法"（具体代码要求）
```


## 🔍 验证执行流程

### 步骤1：AI_FILLING语法强制扫描（必须第一步执行）
```
1. 【强制第一步】全文扫描目标文档，识别所有{{AI_FILLING:标记
2. 【强制验证】确认每个{{AI_FILLING:标记都被正确识别为占位符
3. 【强制检查】验证没有将任何占位符内容误解为实际文档内容
4. 【强制确认】所有{{AI_FILLING:标记将被原样保留，不进行任何修改
5. 生成AI_FILLING语法识别报告

AI_FILLING扫描检查清单:
□ 已扫描全文的{{AI_FILLING:标记
□ 已正确识别占位符性质
□ 已理解占位符内容为填充指导
□ 已确认不会误删或误改占位符
□ 已准备好进行后续文档结构分析
```

### 步骤2：文档结构扫描
```
1. 加载目标00号文档
2. 加载00-通用架构护栏约束上下文模板.md作为基准
3. 逐章节对比结构完整性（忽略{{AI_FILLING:占位符内容）
4. 生成结构对齐报告
```

### 步骤3：代码完整性验证
```
1. 提取设计文档中所有代码引用（排除{{AI_FILLING:占位符内容）
2. 提取"完整代码列表"中的所有条目
3. 执行交叉验证和完整性检查
4. 生成代码完整性报告
```

### 步骤4：综合架构图质量验证
```
1. 【强制验证】综合架构图必须使用分层组件依赖图形式
2. 【强制验证】Mermaid语法必须正确，能够正常渲染
3. 【强制验证】必须包含所有架构层次和组件关系
4. 【强制验证】必须包含完整的关系链路和性能标注
5. 【强制验证】必须包含详细的图形说明和流程解释

综合架构图检查清单:
□ 使用了分层组件依赖图（Layered Component Dependency Diagram）
□ Mermaid语法正确，节点定义用双引号包围
□ 包含了技术栈、应用层、集成层、核心层、子系统、扩展层、业务层、配置层
□ 包含了启动链路、通信链路、安全链路、扩展链路
□ 关键组件标注了性能指标和功能描述
□ 使用了不同线型表示不同关系类型
□ 包含了图形选择原因、关键信息解读、核心流程说明
□ 图形说明与实际图形内容完全对应

如果任何一项检查失败，必须重新设计综合架构图
```

### 步骤5：护栏约束互补关系验证
```
1. 【强制验证】护栏专注风险控制边界，约束专注质量保证框架
2. 【强制验证】护栏和约束形成完整闭环，无矛盾无重叠
3. 【强制验证】每个护栏和约束都基于具体代码关系分析
4. 【强制验证】多维度核心识别和分析的正确性

护栏约束互补性检查清单:
□ 护栏只包含"不能做什么"的风险控制边界
□ 约束只包含"必须做什么"的质量保证框架
□ 没有护栏约束内容重叠或矛盾
□ 基于具体代码关系而非抽象规则
□ 正确识别了多维度核心类型
□ 分析了核心间的具体关系
□ 体现了顶级架构师的专业水准

如果任何一项检查失败，必须重新分析护栏约束的定位和内容
```

### 步骤5：AI_FILLING占位符完整性验证
```
1. 【强制验证】确认所有{{AI_FILLING:标记都被正确保留
2. 【强制验证】确认没有将占位符内容当作实际内容处理
3. 【强制验证】确认占位符语法结构完整无损
4. 【强制验证】确认理解了占位符的指导填充作用

AI_FILLING验证检查清单:
□ 所有{{AI_FILLING:标记都完整保留
□ 没有误删或误改任何占位符
□ 正确理解占位符为填充指导，不是实际内容
□ 置信度级别标记完整
□ 推理依据描述完整
□ 语法结构{{...}}完整无损

如果任何一项检查失败，必须重新执行AI_FILLING语法扫描步骤
```

### 步骤6：多维度核心分析验证
```
1. 【强制验证】系统中存在的所有核心类型是否被正确识别
2. 【强制验证】核心间的依赖关系、协作方式、调用关系是否准确分析
3. 【强制验证】护栏中是否基于多维度核心设置了风险控制
4. 【强制验证】约束中是否基于多维度核心设置了质量保证框架

多维度核心分析检查清单:
□ 识别了所有存在的核心类型（基础、监控、安全、存储、调度、业务、通讯）
□ 分析了核心间的具体依赖关系
□ 分析了核心间的协作方式和调用关系
□ 护栏基于核心关系设置了风险控制边界
□ 约束基于核心关系设置了质量保证框架
□ 体现了通用性设计（不假设特定核心必须存在）
□ 达到了顶级架构师的分析水准

如果任何一项检查失败，必须重新进行多维度核心分析
```

### 步骤7：内容质量评估（排除AI_FILLING占位符）
```
1. 评估实际文档内容的准确性（忽略所有{{AI_FILLING:占位符）
2. 检查项目信息的一致性
3. 验证技术栈和架构信息的准确性
4. 验证护栏约束的互补性和基于代码关系的具体性
5. 生成内容质量报告
```

### 步骤8：修正建议生成（保护AI_FILLING占位符）
```
1. 基于验证结果生成具体的修正建议
2. 提供缺失内容的补充指导（特别是护栏约束和多维度核心分析）
3. 标记需要删除的多余内容（绝对不包括{{AI_FILLING:占位符）
4. 给出优先级排序的修正计划
5. 【强制要求】在所有修正建议中明确保护{{AI_FILLING:占位符不被修改
6. 【强制要求】确保护栏约束修正建议体现互补关系和具体代码分析
```

## ⚠️ 严格执行原则

### 🚫 绝对禁止行为
```yaml
严禁操作:
  - 直接无脑修改内容而不进行验证
  - 删除模板中要求的任何章节或要素
  - 添加模板中没有定义的内容结构
  - 忽略代码路径的准确性验证
  - 跳过映射矩阵的完整性检查
  - 【核心禁令】未经调研就直接修改00号护栏约束上下文文档
  - 【核心禁令】不查看对应设计文档就进行内容修改
  - 【核心禁令】不分析现实代码结构就修改代码位置信息
  - 【核心禁令】基于假设或推测进行文档修改
  - 【核心禁令】跳过验证步骤直接执行修改操作

护栏约束质量禁止行为:
  - 【严禁】护栏和约束内容重叠或矛盾
  - 【严禁】护栏中包含"必须做什么"的建设性要求
  - 【严禁】约束中包含"不能做什么"的防护性边界
  - 【严禁】基于抽象概念而非具体代码关系设置护栏约束
  - 【严禁】忽略多维度核心识别和分析
  - 【严禁】使用空泛规则而非基于现实代码调查的具体要求

AI_FILLING语法禁止行为:
  - 【严禁】将{{AI_FILLING: @CONFIDENCE_LEVEL:details_reasoning}}当作实际内容
  - 【严禁】删除或破坏AI_FILLING占位符的语法结构
  - 【严禁】修改占位符的置信度级别标记
  - 【严禁】将占位符内的描述文字当作需要删除的内容
  - 【严禁】误解占位符含义导致错误的文档修改
  - 【严禁】忽略占位符的填充指导作用
```

### ✅ 强制调研要求
```yaml
修改前必须调研:
  - 【必须】深度阅读对应的设计文档全文（不能只读前几十行）
  - 【必须】分析现有项目的实际代码结构和模块组织
  - 【必须】验证代码路径的真实存在性和准确性
  - 【必须】理解设计文档与现实代码的映射关系
  - 【必须】确认修改的必要性和合理性
  - 【必须】评估修改对整体架构的影响
  - 【必须】验证修改后的一致性和完整性
  - 【必须】正确理解AI_FILLING语法的占位符性质

护栏约束分析要求:
  - 【核心要求】基于具体代码关系分析设置护栏约束，不是抽象规则
  - 【核心要求】识别系统中的多维度核心类型和核心间关系
  - 【核心要求】护栏专注风险控制边界，约束专注质量保证框架
  - 【核心要求】确保护栏约束互补无重叠，形成完整闭环
  - 【核心要求】体现顶级架构师的100%把握核心能力
  - 【禁止误解】不能使用"模块A"、"组件B"等抽象概念
  - 【禁止误解】不能让护栏约束内容重叠或矛盾

AI_FILLING语法理解要求:
  - 【核心理解】{{AI_FILLING: @CONFIDENCE_LEVEL:details_reasoning}} 是占位符模板
  - 【核心理解】语法内容描述"应该填充什么"，不是实际填充的内容
  - 【核心理解】@CONFIDENCE_LEVEL 指定AI填充时的置信度要求
  - 【核心理解】details_reasoning 提供填充内容的指导和推理依据
  - 【禁止误解】不能将占位符内容当作实际的文档内容
  - 【禁止误解】不能删除或修改占位符的语法结构

调研方法:
  - 使用view工具完整阅读相关设计文档
  - 使用codebase-retrieval工具分析现有代码结构
  - 使用view工具查看现有项目目录结构
  - 交叉验证设计文档与实际代码的对应关系
  - 分析依赖关系和集成点
  - 识别多维度核心类型和核心间关系
  - 分析护栏约束的互补性和具体性
  - 识别和理解所有AI_FILLING占位符的含义
```

### 💪 必须行为
```yaml
强制要求:
  - 100%对齐模板结构和内容要求
  - 验证每一个代码文件的存在性和准确性
  - 正确理解AI_FILLING占位符的模板性质，不将其当作实际内容
  - 确保所有AI_FILLING标记的语法结构完整且含义明确
  - 保证映射矩阵的完整性和准确性
  - 提供详细的验证报告和修正建议
  - 【核心要求】基于充分调研的修改建议
  - 【核心要求】提供修改的详细依据和理由
  - 【核心要求】确保修改的可追溯性和可验证性
  - 【核心要求】维护AI_FILLING语法的完整性和正确性

AI_FILLING处理要求:
  - 【必须】保持{{AI_FILLING: @CONFIDENCE_LEVEL:details_reasoning}}语法结构不变
  - 【必须】理解占位符内容是填充指导，不是实际填充内容
  - 【必须】验证置信度级别设置的合理性
  - 【必须】确认推理依据的准确性和完整性
  - 【禁止】删除或修改占位符的语法标记
  - 【禁止】将占位符内容误解为实际的文档内容
```

### 🎯 AI控制机制（学习自AI提示词-生成真实00号文档.md）
```yaml
深度阅读控制:
  - 【强制】必须完整深度阅读所有设计文档，不能只读前几十行
  - 【强制】设计文档通常有几百行，包含大量重要的设计细节和约束
  - 【强制】使用view工具多次读取，确保覆盖文档的所有内容
  - 【禁止】浅层阅读：绝对不能只读文档的前几十行就开始建模

现有工程状态分析控制:
  - 【强制】必须首先分析现有项目结构
  - 【强制】使用view工具查看项目根目录，识别现有的模块和子项目
  - 【强制】分析项目的构建工具类型（Maven的pom.xml、Gradle的build.gradle等）
  - 【强制】识别项目的命名模式和组织方式
  - 【强制】确认项目的完整层次结构，包括父模块和子模块的嵌套关系

代码位置识别控制:
  - 【强制】第一轮：快速浏览所有文档，了解整体架构和涉及的代码范围
  - 【强制】第二轮：深度阅读每个文档，详细记录所有类、接口、配置文件的具体位置和作用
  - 【强制】第三轮：分析现有项目结构，确定哪些是新建、哪些是修改
  - 【强制】第四轮：整合所有信息，生成完整的代码列表和00号文档

验证检查控制:
  - 【强制】确认已识别每个章节涉及的所有代码文件
  - 【强制】确认代码列表覆盖了设计文档中提到的所有类和接口
  - 【强制】确认操作类型（新建/修改）判断准确
  - 【强制】确认代码列表完整性，不能遗漏任何设计文档中涉及的代码
```

## 🎯 执行指令

请按照以上验证清单和流程，对指定的00号护栏约束上下文文档进行全面的质量验证和模板对齐检查。

**执行要求**：
1. **严格遵循调研要求**：必须先调研清楚相应设计文档和现实代码才能提出修改建议
2. **参考标杆文档**：学习`docs\features\F007-建立Commons库的治理机制-20250610\nexus万用插座\design\v1\00-护栏约束上下文总览.md`的质量标准
3. **严禁直接修改**：绝对禁止未经充分调研就直接修改文档内容
4. **正确理解AI_FILLING语法**：
   - 理解`{{AI_FILLING: @CONFIDENCE_LEVEL:details_reasoning}}`是占位符模板
   - 不将占位符内容当作实际的文档内容
   - 保持占位符语法结构的完整性
   - 基于占位符指导进行正确的内容验证和质量评估

**AI_FILLING语法处理示例**：
```
✅ 正确理解：
{{AI_FILLING: @MEDIUM_CONF_85-94:Java 21, Spring Boot 3.4.5, Virtual Threads, Maven 3.9.0+_基于设计文档技术栈推理}}
→ 这是一个占位符，指导AI填充技术栈信息，置信度要求85-94%

❌ 错误理解：
认为"Java 21, Spring Boot 3.4.5, Virtual Threads, Maven 3.9.0+_基于设计文档技术栈推理"是实际内容需要删除或修改
```
