#!/usr/bin/env python3
"""
V4设计文档结构验证测试
基于实际测试验证哪种设计文档结构最能提升置信度
"""

import os
import json
import time
import asyncio
import requests
from dataclasses import dataclass
from typing import Dict, List, Any, Tuple
from pathlib import Path

@dataclass
class TestScenario:
    """测试场景配置"""
    name: str
    description: str
    design_doc_enhancements: List[str]
    expected_confidence: float
    test_focus: str

@dataclass
class TestResult:
    """测试结果"""
    scenario_name: str
    architecture_accuracy: float
    implementation_plan_quality: float
    json_usage_rate: float
    overall_confidence: float
    execution_time: float
    error_count: int
    hallucination_indicators: List[str]

class DesignDocStructureValidator:
    """设计文档结构验证器"""

    def __init__(self, api_key: str = None):
        self.base_design_path = "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1"
        self.test_results: List[TestResult] = []
        self.api_key = api_key or "cpk_3b6eb6d4ff254eec8d53bbb6d791acdb.4051c83fb6bd53adb8ea32923961cd47.RqEhaXfNmNEwbDzoQKlkp10y2BjL0jlP"
        self.api_url = "https://llm.chutes.ai/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # V4测试模型列表（基于之前的测试结果）
        self.test_models = [
            "deepseek-ai/DeepSeek-V3-0324",      # 主力架构师
            "deepseek-ai/DeepSeek-R1-0528",      # 备用快速生成
            "agentica-org/DeepCoder-14B-Preview" # 代码专家
        ]
        
        # 定义测试场景
        self.test_scenarios = [
            TestScenario(
                name="baseline",
                description="当前基线测试",
                design_doc_enhancements=[],
                expected_confidence=73.2,
                test_focus="建立基线对比"
            ),
            TestScenario(
                name="enhanced_architecture_understanding",
                description="增强架构设计理解",
                design_doc_enhancements=[
                    "微内核+服务总线架构清晰定义",
                    "核心组件设计和交互模型",
                    "接口契约和API定义",
                    "技术集成策略说明"
                ],
                expected_confidence=80.0,
                test_focus="验证架构理解清晰度对置信度的影响"
            ),
            TestScenario(
                name="enhanced_component_mapping",
                description="增强架构组件映射",
                design_doc_enhancements=[
                    "抽象组件→具体类映射",
                    "接口→实现类映射",
                    "Spring Bean配置映射",
                    "生命周期→方法映射"
                ],
                expected_confidence=85.0,
                test_focus="验证架构映射对置信度的影响"
            ),
            TestScenario(
                name="ai_cognitive_optimized",
                description="AI认知约束优化",
                design_doc_enhancements=[
                    "认知复杂度≤L2级别",
                    "记忆边界压力≤60%",
                    "幻觉风险系数≤0.3",
                    "验证锚点密度≥4个/组件"
                ],
                expected_confidence=90.0,
                test_focus="验证AI认知约束对置信度的影响"
            ),
            TestScenario(
                name="comprehensive",
                description="综合增强版本",
                design_doc_enhancements=[
                    "四维度完备度≥90%",
                    "AI认知约束合规性≥85%",
                    "强制性架构现状分析",
                    "95%置信度门禁机制"
                ],
                expected_confidence=95.0,
                test_focus="验证综合增强效果"
            )
        ]
    
    def load_design_document(self, doc_name: str) -> Tuple[str, str]:
        """加载设计文档和对应的JSON"""
        md_path = f"{self.base_design_path}/{doc_name}.md"
        json_path = f"{self.base_design_path}/json/{doc_name}.json"
        
        md_content = ""
        json_content = ""
        
        if os.path.exists(md_path):
            with open(md_path, 'r', encoding='utf-8') as f:
                md_content = f.read()
        
        if os.path.exists(json_path):
            with open(json_path, 'r', encoding='utf-8') as f:
                json_content = f.read()
        
        return md_content, json_content
    
    def create_enhanced_prompt(self, scenario: TestScenario, md_content: str, json_content: str) -> str:
        """根据测试场景创建增强提示词"""
        base_prompt = f"""基于以下Nexus万用插座设计文档，生成完整的实施计划：

设计文档内容：
{md_content[:3000]}

JSON配置：
{json_content[:2000]}

任务要求：
1. 生成NexusKernel核心类的完整实现
2. 包含详细的实施步骤和配置
3. 使用JSON中的具体配置参数
4. 确保代码可以直接编译运行
5. 包含完整的异常处理和日志

技术约束：
- Java 21 + Virtual Threads
- Spring Boot 3.4.5
- 包名：org.xkong.cloud.commons.nexus.kernel
- 性能要求：启动时间≤1000ms，事件处理≥10,000/s
"""
        
        # 根据场景添加特定的增强要求
        if scenario.name == "enhanced_architecture_understanding":
            base_prompt += """

架构理解增强要求：
1. 必须深入分析微内核+服务总线架构模式
2. 明确定义核心组件（NexusKernel、PluginManager、ServiceBus）的职责
3. 详细说明组件间的交互模型和通信机制
4. 提供清晰的接口契约和API定义
5. 说明Java 21 + Spring Boot 3.4.5的集成策略
"""
        
        elif scenario.name == "enhanced_component_mapping":
            base_prompt += """

组件映射增强要求：
1. 提供抽象组件到具体Java类的完整映射
2. 定义接口到实现类的映射关系
3. 包含Spring Bean配置和依赖注入映射
4. 说明组件生命周期到具体方法的映射
5. 确保每个映射都有具体的验证锚点
"""
        
        elif scenario.name == "ai_cognitive_optimized":
            base_prompt += """

AI认知约束优化要求：
1. 控制架构概念复杂度≤7个核心概念
2. 分解复杂组件关系，避免认知负载过重
3. 每个设计决策都有具体的验证锚点
4. 使用结构化格式，便于AI理解和处理
5. 避免过度抽象，提供具体的代码示例
"""
        
        elif scenario.name == "comprehensive":
            base_prompt += """

综合增强要求：
1. 架构情况分析完备度≥90%（基于实际代码分析）
2. 架构设计理解完备度≥90%（清晰的架构蓝图）
3. 架构组件实施映射完备度≥90%（具体代码映射）
4. 技术实施精确指导完备度≥90%（可编译运行）
5. AI认知约束合规性≥85%（符合AI认知特点）
6. 强制激活命令：@AI_COGNITIVE_CONSTRAINTS、@BOUNDARY_GUARD_ACTIVATION
"""
        
        return base_prompt

    def call_ai_api(self, prompt: str, model_name: str = None) -> Tuple[str, float]:
        """调用AI API"""
        # 使用指定模型或默认使用第一个模型
        model = model_name or self.test_models[0]
        start_time = time.time()

        payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 4000,
            "temperature": 0.1
        }

        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                timeout=180
            )

            execution_time = time.time() - start_time

            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                return content, execution_time
            else:
                print(f"API调用失败: {response.status_code} - {response.text}")
                return f"API调用失败: {response.status_code}", execution_time

        except Exception as e:
            execution_time = time.time() - start_time
            print(f"API调用异常: {str(e)}")
            return f"API调用异常: {str(e)}", execution_time

    def test_all_models_for_scenario(self, scenario: TestScenario, prompt: str) -> List[TestResult]:
        """为一个场景测试所有模型"""
        results = []

        for i, model in enumerate(self.test_models):
            print(f"   🤖 测试模型 {i+1}/{len(self.test_models)}: {model}")

            # 调用AI API
            ai_response, execution_time = self.call_ai_api(prompt, model)

            if "API调用失败" in ai_response or "API调用异常" in ai_response:
                print(f"   ❌ 模型失败: {ai_response}")
                continue

            # 分析响应质量
            test_result = self.analyze_response_quality(ai_response, scenario)
            test_result.execution_time = execution_time
            results.append(test_result)

            # 输出简要结果
            print(f"   ✅ 置信度: {test_result.overall_confidence:.1f}分, 时间: {execution_time:.2f}s")

            # 保存详细结果
            result_file = f"test_result_{scenario.name}_{model.replace('/', '_')}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'scenario': scenario.name,
                    'model': model,
                    'execution_time': execution_time,
                    'metrics': {
                        'architecture_accuracy': test_result.architecture_accuracy,
                        'implementation_plan_quality': test_result.implementation_plan_quality,
                        'json_usage_rate': test_result.json_usage_rate,
                        'overall_confidence': test_result.overall_confidence
                    },
                    'response_preview': ai_response[:500] + "..." if len(ai_response) > 500 else ai_response
                }, f, ensure_ascii=False, indent=2)

            # 避免API限制
            time.sleep(2)

        return results

    def analyze_response_quality(self, response: str, scenario: TestScenario) -> TestResult:
        """分析响应质量"""
        # 简化的质量分析（实际应该更复杂）
        architecture_accuracy = self._calculate_architecture_accuracy(response)
        implementation_plan_quality = self._calculate_implementation_quality(response)
        json_usage_rate = self._calculate_json_usage_rate(response)
        overall_confidence = self._calculate_overall_confidence(
            architecture_accuracy, implementation_plan_quality, json_usage_rate
        )
        
        hallucination_indicators = self._detect_hallucination_indicators(response)
        
        return TestResult(
            scenario_name=scenario.name,
            architecture_accuracy=architecture_accuracy,
            implementation_plan_quality=implementation_plan_quality,
            json_usage_rate=json_usage_rate,
            overall_confidence=overall_confidence,
            execution_time=0.0,  # 会在实际测试中填入
            error_count=len(hallucination_indicators),
            hallucination_indicators=hallucination_indicators
        )
    
    def _calculate_architecture_accuracy(self, response: str) -> float:
        """计算架构准确性"""
        # 检查关键架构概念的提及
        key_concepts = [
            "NexusKernel", "PluginManager", "ServiceBus", 
            "微内核", "服务总线", "插件生命周期",
            "Virtual Threads", "Spring Boot"
        ]
        
        mentioned_concepts = sum(1 for concept in key_concepts if concept in response)
        return (mentioned_concepts / len(key_concepts)) * 100
    
    def _calculate_implementation_quality(self, response: str) -> float:
        """计算实施计划质量"""
        quality_indicators = [
            "package org.xkong.cloud.commons.nexus",
            "@Component", "@Service", "@Configuration",
            "public class", "public interface",
            "try {", "catch (", "finally {",
            "logger.", "log."
        ]
        
        present_indicators = sum(1 for indicator in quality_indicators if indicator in response)
        return min((present_indicators / len(quality_indicators)) * 100, 100)
    
    def _calculate_json_usage_rate(self, response: str) -> float:
        """计算JSON使用率"""
        json_indicators = [
            "JSON", "json", "配置", "参数",
            "metadata", "properties", "settings"
        ]
        
        json_mentions = sum(response.count(indicator) for indicator in json_indicators)
        return min((json_mentions / 10) * 100, 100)  # 假设10次提及为100%
    
    def _calculate_overall_confidence(self, arch_acc: float, impl_qual: float, json_rate: float) -> float:
        """计算整体置信度"""
        return (arch_acc * 0.4 + impl_qual * 0.4 + json_rate * 0.2)
    
    def _detect_hallucination_indicators(self, response: str) -> List[str]:
        """检测幻觉指标"""
        indicators = []
        
        # 检查是否有明显的幻觉内容
        hallucination_patterns = [
            "我不确定", "可能需要", "建议考虑", "或许应该",
            "TODO", "FIXME", "待实现", "需要进一步"
        ]
        
        for pattern in hallucination_patterns:
            if pattern in response:
                indicators.append(f"不确定性表达: {pattern}")
        
        return indicators

if __name__ == "__main__":
    validator = DesignDocStructureValidator()
    
    # 加载架构概览文档进行测试
    md_content, json_content = validator.load_design_document("01-architecture-overview")
    
    print("🧪 V4设计文档结构验证测试开始")
    print("=" * 60)

    # 只测试前3个场景，避免API调用过多
    test_scenarios = validator.test_scenarios[:3]

    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📋 测试场景 {i}/{len(test_scenarios)}: {scenario.name}")
        print(f"描述: {scenario.description}")
        print(f"预期置信度: {scenario.expected_confidence}")

        # 创建增强提示词
        enhanced_prompt = validator.create_enhanced_prompt(scenario, md_content, json_content)
        print(f"✅ 提示词已生成，长度: {len(enhanced_prompt)} 字符")

        # 测试所有模型
        print("🔄 正在测试所有模型...")
        scenario_results = validator.test_all_models_for_scenario(scenario, enhanced_prompt)

        if not scenario_results:
            print(f"❌ 场景测试失败: 所有模型都失败")
            continue

        # 计算场景平均结果
        avg_architecture_accuracy = sum(r.architecture_accuracy for r in scenario_results) / len(scenario_results)
        avg_implementation_quality = sum(r.implementation_plan_quality for r in scenario_results) / len(scenario_results)
        avg_json_usage = sum(r.json_usage_rate for r in scenario_results) / len(scenario_results)
        avg_confidence = sum(r.overall_confidence for r in scenario_results) / len(scenario_results)
        avg_execution_time = sum(r.execution_time for r in scenario_results) / len(scenario_results)

        # 添加到总结果
        validator.test_results.extend(scenario_results)

        # 输出场景汇总
        print(f"📊 场景汇总 ({len(scenario_results)}个模型):")
        print(f"   🏗️  平均架构准确性: {avg_architecture_accuracy:.1f}%")
        print(f"   📋 平均实施计划质量: {avg_implementation_quality:.1f}分")
        print(f"   📊 平均JSON使用率: {avg_json_usage:.1f}%")
        print(f"   🎯 平均整体置信度: {avg_confidence:.1f}分")
        print(f"   ⏱️  平均执行时间: {avg_execution_time:.2f}秒")

        # 找出最佳模型
        best_result = max(scenario_results, key=lambda x: x.overall_confidence)
        print(f"   🏆 最佳模型: {validator.test_models[scenario_results.index(best_result)]}")
        print(f"   🎯 最佳置信度: {best_result.overall_confidence:.1f}分")

        # 避免API限制，添加延迟
        if i < len(test_scenarios):
            print("⏳ 等待10秒后继续下一个场景...")
            time.sleep(10)

    # 输出汇总结果
    print("\n" + "=" * 60)
    print("📊 V4测试结果汇总")
    print("=" * 60)

    if validator.test_results:
        for result in validator.test_results:
            print(f"\n🎯 {result.scenario_name}:")
            print(f"   架构准确性: {result.architecture_accuracy:.1f}% (目标: 43.8% → 75%+)")
            print(f"   实施计划质量: {result.implementation_plan_quality:.1f}分 (目标: 79.2 → 90+)")
            print(f"   整体置信度: {result.overall_confidence:.1f}分 (目标: 73.2 → 95+)")
            print(f"   执行时间: {result.execution_time:.2f}秒")

        # 找出最佳场景
        best_result = max(validator.test_results, key=lambda x: x.overall_confidence)
        print(f"\n🏆 最佳场景: {best_result.scenario_name}")
        print(f"   置信度: {best_result.overall_confidence:.1f}分")
        print(f"   架构准确性提升: {best_result.architecture_accuracy - 43.8:.1f}%")

    else:
        print("❌ 没有成功的测试结果")

    print("\n✅ V4设计文档结构验证测试完成")
