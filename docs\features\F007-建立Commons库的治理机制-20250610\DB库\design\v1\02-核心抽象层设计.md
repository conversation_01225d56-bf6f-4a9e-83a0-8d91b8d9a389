# F007 DB库核心抽象层详细设计

## 文档信息
- **文档ID**: F007-DB-CORE-DESIGN-002
- **创建日期**: 2025-01-17
- **版本**: v1.0
- **模块**: commons-db-core
- **依赖**: xkongcloud-commons-exception
- **状态**: 设计阶段
- **复杂度等级**: L3-高复杂度（8+概念，架构级变更）

## 核心定位
`commons-db-core` 是整个DB库的核心抽象层，提供：
- 统一的数据访问接口抽象
- SPI（Service Provider Interface）机制
- 异常处理体系
- 扩展点和插件机制
- 核心工具类和常量定义

## 设计哲学

本项目遵循以下设计哲学，专注解决核心抽象层的架构设计难点：

1. **抽象层次精确控制**：构建清晰的抽象层次体系，确保AI能够准确理解概念边界
   - **概念边界定义难点**：如何在抽象和具体之间找到合适的边界，避免过度抽象或抽象不足
   - **层次划分难点**：如何正确划分抽象层次，确保每层有明确的职责和合理的粒度
   - **一致性维护难点**：如何在多个抽象层次间保持概念一致性，避免语义冲突

2. **SPI机制精准实现**：构建灵活的服务提供者接口体系，支持可插拔的实现层
   - **接口设计难点**：如何设计既通用又灵活的SPI接口，支持不同技术栈的实现
   - **加载机制难点**：如何实现高效的SPI服务发现和加载机制，保证性能和可靠性
   - **扩展策略难点**：如何设计SPI扩展策略，支持热插拔和动态配置

3. **复杂性边界精确控制**：明确定义AI认知边界，确保抽象层复杂度可控
   - **模块划分原则**：按照职责和依赖关系进行清晰的模块划分
   - **职责分离策略**：每个抽象组件专注单一职责，避免功能耦合
   - **边界定义方法**：通过接口契约明确定义各抽象层的边界和交互协议

4. **接口优先与实现分离**：优先定义接口契约，具体实现由各层提供
5. **异常统一与错误处理**：基于XCE异常体系的统一异常处理机制
6. **扩展友好与插件支持**：提供丰富的扩展点和插件机制
7. **现代技术深度融合**：原生支持Java 21、Spring Boot 3.4等现代技术
8. **性能优先与云原生**：针对虚拟线程环境优化，支持云原生部署

## 包含范围

### 功能范围
- 统一数据访问接口抽象定义
- SPI（Service Provider Interface）机制
- 核心异常处理体系
- 扩展点和插件机制框架
- 查询规范和参数处理抽象
- 批量操作和事务管理抽象
- 现代技术特性集成抽象
- 配置和工具类库

### 技术范围
- Java SPI机制集成
- Spring Boot 3.4自动配置
- Java 21虚拟线程抽象支持
- XKong Cloud异常体系集成
- Spring Data抽象层集成
- 可观测性框架集成

### 抽象层次组件
- **核心接口层**：最基础的数据访问接口抽象
- **扩展接口层**：可插拔的功能扩展接口
- **SPI定义层**：服务提供者接口定义
- **工具抽象层**：通用工具和常量抽象

## 排除范围

### 功能排除
- 具体数据库实现（由实现模块负责）
- 业务逻辑处理（由业务层负责）
- 数据库连接管理（由连接模块负责）
- 缓存实现策略（由缓存模块负责）
- 监控指标收集（由监控模块负责）

### 技术排除
- 特定数据库的实现细节
- 具体ORM框架的集成
- 复杂的业务规则引擎
- 实时流处理框架

### 复杂性边界
- 不包含具体实现逻辑（保持抽象性质）
- 不支持动态接口生成（避免运行时复杂性）
- 不处理复杂的业务场景（专注基础抽象）

## 1. 模块概述

### 1.2 设计原则
- **接口优先**：所有功能通过接口抽象，具体实现由各层提供
- **SPI驱动**：通过SPI机制实现可插拔的实现层
- **异常统一**：基于XCE异常体系的统一异常处理
- **扩展友好**：提供丰富的扩展点和插件机制
- **现代技术融合**：原生支持Java 21虚拟线程、Spring Boot 3.4、PostgreSQL 17特性
- **性能优先**：针对HikariCP + 虚拟线程环境深度优化
- **云原生就绪**：支持GraalVM原生镜像、Kubernetes健康检查

## 2. 核心接口设计

### 2.1 DataAccessTemplate 主接口

```java
package org.xkong.cloud.commons.db.core.template;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.xkong.cloud.commons.db.core.query.QuerySpec;
import org.xkong.cloud.commons.db.core.query.QueryHint;

import java.util.List;
import java.util.Optional;

/**
 * 统一数据访问模板接口
 * 提供所有数据访问操作的抽象定义
 * 
 * 🔑 现代技术特性：
 * - 原生支持Java 21虚拟线程异步操作
 * - 集成Spring Boot 3.4观测性特性
 * - 优化HikariCP连接池性能
 * - 支持PostgreSQL 17 JSON_TABLE等新特性
 *
 * @param <T> 实体类型
 * @param <ID> 主键类型
 */
public interface DataAccessTemplate<T, ID> {
    
    // ============ 基础 CRUD 操作 ============
    
    /**
     * 保存实体
     * @param entity 实体对象
     * @return 保存后的实体（可能包含生成的ID）
     * @throws DataAccessException 数据访问异常
     */
    T save(T entity);
    
    /**
     * 批量保存实体
     * @param entities 实体集合
     * @return 保存后的实体列表
     * @throws DataAccessException 数据访问异常
     */
    List<T> saveAll(Iterable<T> entities);
    
    /**
     * 根据ID查找实体
     * @param id 主键ID
     * @return Optional包装的实体
     * @throws DataAccessException 数据访问异常
     */
    Optional<T> findById(ID id);
    
    /**
     * 查询所有实体
     * @return 实体列表
     * @throws DataAccessException 数据访问异常
     */
    List<T> findAll();
    
    /**
     * 分页查询所有实体
     * @param pageable 分页参数
     * @return 分页结果
     * @throws DataAccessException 数据访问异常
     */
    Page<T> findAll(Pageable pageable);
    
    /**
     * 根据ID删除实体
     * @param id 主键ID
     * @throws DataAccessException 数据访问异常
     */
    void deleteById(ID id);
    
    /**
     * 删除实体
     * @param entity 实体对象
     * @throws DataAccessException 数据访问异常
     */
    void delete(T entity);
    
    /**
     * 批量删除实体
     * @param entities 实体集合
     * @throws DataAccessException 数据访问异常
     */
    void deleteAll(Iterable<T> entities);
    
    // ============ 查询操作 ============
    
    /**
     * 根据查询规范查询
     * @param spec 查询规范
     * @param <R> 结果类型
     * @return 查询结果列表
     * @throws DataAccessException 数据访问异常
     */
    <R> List<R> query(QuerySpec<R> spec);
    
    /**
     * 根据查询规范分页查询
     * @param spec 查询规范
     * @param pageable 分页参数
     * @param <R> 结果类型
     * @return 分页查询结果
     * @throws DataAccessException 数据访问异常
     */
    <R> Page<R> queryWithPaging(QuerySpec<R> spec, Pageable pageable);
    
    /**
     * 查询单个结果
     * @param spec 查询规范
     * @param <R> 结果类型
     * @return Optional包装的单个结果
     * @throws DataAccessException 数据访问异常
     */
    <R> Optional<R> queryOne(QuerySpec<R> spec);
    
    // ============ 统计操作 ============
    
    /**
     * 统计实体总数
     * @return 实体总数
     * @throws DataAccessException 数据访问异常
     */
    long count();
    
    /**
     * 根据查询规范统计
     * @param spec 查询规范
     * @return 统计结果
     * @throws DataAccessException 数据访问异常
     */
    long count(QuerySpec<Long> spec);
    
    /**
     * 检查实体是否存在
     * @param id 主键ID
     * @return 是否存在
     * @throws DataAccessException 数据访问异常
     */
    boolean existsById(ID id);
    
    // ============ 批量操作 ============
    
    /**
     * 批量插入（高性能，虚拟线程优化）
     * @param entities 实体列表
     * @throws DataAccessException 数据访问异常
     */
    void batchInsert(List<T> entities);
    
    /**
     * 批量更新（高性能，虚拟线程优化）
     * @param entities 实体列表
     * @throws DataAccessException 数据访问异常
     */
    void batchUpdate(List<T> entities);
    
    /**
     * 批量插入或更新（upsert，PostgreSQL 17优化）
     * @param entities 实体列表
     * @throws DataAccessException 数据访问异常
     */
    void batchUpsert(List<T> entities);
    
    // ============ 异步操作（Java 21虚拟线程）============
    
    /**
     * 异步保存实体（虚拟线程）
     * @param entity 实体对象
     * @return CompletableFuture包装的保存结果
     */
    CompletableFuture<T> saveAsync(T entity);
    
    /**
     * 异步查询（虚拟线程）
     * @param spec 查询规范
     * @param <R> 结果类型
     * @return CompletableFuture包装的查询结果
     */
    <R> CompletableFuture<List<R>> queryAsync(QuerySpec<R> spec);
    
    /**
     * 异步批量操作（虚拟线程优化）
     * @param entities 实体列表
     * @return CompletableFuture包装的操作结果
     */
    CompletableFuture<Void> batchInsertAsync(List<T> entities);
    
    // ============ 元数据操作 ============
    
    /**
     * 获取实体类型
     * @return 实体类型
     */
    Class<T> getEntityType();
    
    /**
     * 获取主键类型
     * @return 主键类型
     */
    Class<ID> getIdType();
    
    /**
     * 获取数据源标识
     * @return 数据源标识
     */
    String getDataSourceName();
    
    /**
     * 获取提供者信息
     * @return 提供者信息
     */
    DataAccessProvider getProvider();

    // ============ 技术特性组合优化扩展点 ============

    /**
     * 启用技术特性组合优化
     * 根据业务场景自动选择最优的技术特性组合
     * @param scenario 业务场景（high-concurrency, json-intensive, batch-processing）
     * @param optimizer 组合优化器
     */
    default void enableComboOptimization(String scenario, TechStackOptimizer optimizer) {
        // V3.0 版本提供基础实现
        optimizer.optimizeForScenario(scenario, this);
    }

    /**
     * 获取当前技术特性组合配置
     * @return 组合配置信息
     */
    default ComboConfiguration getComboConfiguration() {
        return ComboConfiguration.defaultConfig();
    }

    // ============ 缓存扩展点（未来规划） ============

    /**
     * 启用缓存适配器（未来功能）
     * 当前版本暂不实现，预留接口
     * @param adapter 缓存适配器
     * @throws UnsupportedOperationException 当前版本不支持
     */
    default void enableCaching(CacheAdapter<T, ID> adapter) {
        throw new UnsupportedOperationException("Caching feature not yet implemented in V3.0");
    }

    /**
     * 检查是否启用了缓存
     * @return 是否启用缓存
     */
    default boolean isCachingEnabled() {
        return false; // V3.0 版本始终返回 false
    }
}
```

### 2.2 QuerySpec 查询规范接口

```java
package org.xkong.cloud.commons.db.core.query;

import java.util.Map;

/**
 * 查询规范接口
 * 定义查询的抽象规范，具体实现由各层提供
 *
 * @param <R> 查询结果类型
 */
public interface QuerySpec<R> {
    
    /**
     * 获取查询结果类型
     * @return 结果类型的Class对象
     */
    Class<R> getResultType();
    
    /**
     * 获取查询语句
     * 可能是SQL、HQL、或其他查询语言
     * @return 查询语句
     */
    String getQuery();
    
    /**
     * 获取查询参数
     * @return 参数映射
     */
    Map<String, Object> getParameters();
    
    /**
     * 获取查询提示
     * @return 查询提示信息
     */
    QueryHint getHint();
    
    /**
     * 获取查询类型
     * @return 查询类型枚举
     */
    QueryType getQueryType();
    
    /**
     * 是否为本地查询
     * @return true表示本地SQL查询，false表示对象查询
     */
    boolean isNativeQuery();
}
```

### 2.3 QueryHint 查询提示接口

```java
package org.xkong.cloud.commons.db.core.query;

/**
 * 查询提示接口
 * 为查询提供性能和行为提示
 */
public interface QueryHint {
    
    /**
     * 获取查询超时时间（毫秒）
     * @return 超时时间，null表示使用默认值
     */
    Integer getTimeoutMs();
    
    /**
     * 获取缓存策略
     * @return 缓存策略
     */
    CacheStrategy getCacheStrategy();
    
    /**
     * 获取读取策略
     * @return 读取策略（主库/从库）
     */
    ReadStrategy getReadStrategy();
    
    /**
     * 是否启用查询计划缓存
     * @return 是否启用
     */
    Boolean isQueryPlanCacheEnabled();
    
    /**
     * 获取预期结果集大小
     * 用于性能优化
     * @return 预期大小
     */
    Integer getExpectedResultSize();
    
    /**
     * 获取获取大小
     * 用于JDBC fetchSize优化
     * @return 获取大小
     */
    Integer getFetchSize();
}
```

### 2.4 TechStackOptimizer 技术特性组合优化器

```java
package org.xkong.cloud.commons.db.core.optimizer;

/**
 * 技术特性组合优化器
 * 根据业务场景自动选择和配置最优的技术特性组合
 */
public interface TechStackOptimizer {

    /**
     * 为指定场景优化技术特性组合
     * @param scenario 业务场景
     * @param template 数据访问模板
     */
    void optimizeForScenario(String scenario, DataAccessTemplate<?, ?> template);

    /**
     * 获取场景的最优组合配置
     * @param scenario 业务场景
     * @return 组合配置
     */
    ComboConfiguration getOptimalCombo(String scenario);

    /**
     * 监控组合性能并提供调优建议
     * @param metrics 性能指标
     * @return 调优建议
     */
    OptimizationSuggestion analyzeAndSuggest(ComboPerformanceMetrics metrics);
}

/**
 * 组合配置信息
 */
public interface ComboConfiguration {

    // HikariCP配置
    HikariComboConfig getHikariConfig();

    // PostgreSQL配置
    PostgreSQLComboConfig getPostgreSQLConfig();

    // Spring Boot配置
    SpringBootComboConfig getSpringBootConfig();

    // Java 21配置
    Java21ComboConfig getJava21Config();

    /**
     * 默认配置
     */
    static ComboConfiguration defaultConfig() {
        return new DefaultComboConfiguration();
    }
}

/**
 * 组合性能指标
 */
public record ComboPerformanceMetrics(
    double hikariPoolUtilization,      // HikariCP连接池利用率
    double postgresqlQueryEfficiency,  // PostgreSQL查询效率
    long springBootResponseTime,       // Spring Boot响应时间
    double javaVirtualThreadUtilization // Java Virtual Thread利用率
) {

    /**
     * 综合性能评分
     * @return 0-100的评分
     */
    public double getComboEffectivenessScore() {
        return (hikariPoolUtilization * 0.3 +
                postgresqlQueryEfficiency * 0.4 +
                (1000.0 / Math.max(springBootResponseTime, 1)) * 0.2 +
                javaVirtualThreadUtilization * 0.1) * 100;
    }
}

/**
 * 优化建议
 */
public interface OptimizationSuggestion {
    String getScenario();
    List<String> getRecommendations();
    ComboPerformanceMetrics getCurrentMetrics();
    ComboPerformanceMetrics getExpectedMetrics();
}
```

### 2.5 CacheAdapter 缓存适配器接口 🔮

```java
package org.xkong.cloud.commons.db.core.cache;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 缓存适配器接口（未来规划）
 * 提供DB与Cache之间的桥接功能
 *
 * 注意：此接口为V3.0版本的架构预留，当前版本不实现
 *
 * @param <T> 实体类型
 * @param <ID> 主键类型
 */
public interface CacheAdapter<T, ID> {

    // ============ 缓存查询操作 ============

    /**
     * 从缓存中查找单个实体
     * @param id 主键ID
     * @return Optional包装的实体
     */
    Optional<T> findFromCache(ID id);

    /**
     * 从缓存中批量查找实体
     * @param ids 主键ID集合
     * @return 实体列表
     */
    List<T> findAllFromCache(Collection<ID> ids);

    /**
     * 检查缓存中是否存在指定实体
     * @param id 主键ID
     * @return 是否存在
     */
    boolean existsInCache(ID id);

    // ============ 缓存更新操作 ============

    /**
     * 保存实体到缓存
     * @param entity 实体对象
     */
    void saveToCache(T entity);

    /**
     * 批量保存实体到缓存
     * @param entities 实体集合
     */
    void saveAllToCache(Collection<T> entities);

    // ============ 缓存失效操作 ============

    /**
     * 从缓存中移除指定实体
     * @param id 主键ID
     */
    void evictFromCache(ID id);

    /**
     * 从缓存中批量移除实体
     * @param ids 主键ID集合
     */
    void evictAllFromCache(Collection<ID> ids);

    /**
     * 清空所有缓存
     */
    void clearCache();

    // ============ 缓存一致性管理 ============

    /**
     * 与数据库同步指定实体
     * @param id 主键ID
     */
    void syncWithDatabase(ID id);

    /**
     * 验证缓存一致性
     * @return 一致性检查结果
     */
    CacheConsistencyResult validateConsistency();

    // ============ 缓存统计信息 ============

    /**
     * 获取缓存统计信息
     * @return 缓存统计
     */
    CacheStatistics getStatistics();

    /**
     * 获取缓存配置信息
     * @return 缓存配置
     */
    CacheConfig getConfig();
}

/**
 * 缓存一致性检查结果
 */
public interface CacheConsistencyResult {
    boolean isConsistent();
    List<String> getInconsistentKeys();
    String getReport();
}

/**
 * 缓存统计信息
 */
public interface CacheStatistics {
    long getHitCount();
    long getMissCount();
    double getHitRate();
    long getEvictionCount();
    long getCacheSize();
}

/**
 * 缓存配置信息
 */
public interface CacheConfig {
    String getCacheName();
    long getTimeToLive();
    long getMaxSize();
    String getEvictionPolicy();
}
```

## 3. SPI 机制设计

### 3.1 DataAccessProvider SPI接口

```java
package org.xkong.cloud.commons.db.core.provider;

import org.xkong.cloud.commons.db.core.template.DataAccessTemplate;
import org.xkong.cloud.commons.db.core.config.DataSourceConfig;

/**
 * 数据访问提供者SPI接口
 * 实现此接口以提供特定的数据访问实现
 */
public interface DataAccessProvider {
    
    /**
     * 获取提供者名称
     * @return 提供者唯一标识名称
     */
    String getName();
    
    /**
     * 获取提供者优先级
     * 数值越大优先级越高
     * @return 优先级
     */
    int getOrder();
    
    /**
     * 获取提供者版本
     * @return 版本号
     */
    String getVersion();
    
    /**
     * 获取提供者描述
     * @return 描述信息
     */
    String getDescription();
    
    /**
     * 检查是否支持指定的实体类型
     * @param entityType 实体类型
     * @return 是否支持
     */
    boolean supports(Class<?> entityType);
    
    /**
     * 检查是否支持指定的数据源配置
     * @param config 数据源配置
     * @return 是否支持
     */
    boolean supports(DataSourceConfig config);
    
    /**
     * 创建数据访问模板实例
     * @param entityType 实体类型
     * @param config 数据源配置
     * @param <T> 实体类型泛型
     * @param <ID> 主键类型泛型
     * @return 数据访问模板实例
     * @throws DataAccessException 创建失败时抛出异常
     */
    <T, ID> DataAccessTemplate<T, ID> createTemplate(
        Class<T> entityType, 
        Class<ID> idType,
        DataSourceConfig config
    );
    
    /**
     * 初始化提供者
     * 在Spring容器启动时调用
     * @param config 全局配置
     * @throws DataAccessException 初始化失败时抛出异常
     */
    void initialize(GlobalConfig config);
    
    /**
     * 销毁提供者
     * 在Spring容器关闭时调用
     */
    void destroy();
    
    /**
     * 健康检查
     * @return 健康状态
     */
    HealthStatus getHealthStatus();
}
```

### 3.2 DataAccessProviderRegistry 注册表

```java
package org.xkong.cloud.commons.db.core.provider;

import java.util.List;
import java.util.Optional;

/**
 * 数据访问提供者注册表
 * 管理所有的数据访问提供者
 */
public interface DataAccessProviderRegistry {
    
    /**
     * 注册提供者
     * @param provider 提供者实例
     */
    void registerProvider(DataAccessProvider provider);
    
    /**
     * 注销提供者
     * @param providerName 提供者名称
     */
    void unregisterProvider(String providerName);
    
    /**
     * 根据名称获取提供者
     * @param name 提供者名称
     * @return Optional包装的提供者
     */
    Optional<DataAccessProvider> getProvider(String name);
    
    /**
     * 获取所有提供者
     * @return 提供者列表（按优先级排序）
     */
    List<DataAccessProvider> getAllProviders();
    
    /**
     * 为指定实体类型选择最佳提供者
     * @param entityType 实体类型
     * @param config 数据源配置
     * @return Optional包装的最佳提供者
     */
    Optional<DataAccessProvider> selectProvider(
        Class<?> entityType, 
        DataSourceConfig config
    );
    
    /**
     * 获取默认提供者
     * @return Optional包装的默认提供者
     */
    Optional<DataAccessProvider> getDefaultProvider();
    
    /**
     * 设置默认提供者
     * @param providerName 提供者名称
     */
    void setDefaultProvider(String providerName);
}
```

## 4. 异常处理体系

### 4.1 异常层次结构

```java
package org.xkong.cloud.commons.db.core.exception;

import org.xkong.cloud.commons.exception.core.SystemException;
import org.xkong.cloud.commons.exception.core.BusinessException;

/**
 * 数据访问异常基类
 * 继承自XCE异常体系
 */
public class DataAccessException extends SystemException {
    
    public DataAccessException(String message) {
        super(message);
    }
    
    public DataAccessException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public DataAccessException(String errorCode, String message) {
        super(errorCode, message);
    }
    
    public DataAccessException(String errorCode, String message, Throwable cause) {
        super(errorCode, message, cause);
    }
}

/**
 * 实体未找到异常
 */
public class EntityNotFoundException extends BusinessException {
    
    public EntityNotFoundException(String entityType, Object id) {
        super("ENTITY_NOT_FOUND", 
              String.format("Entity %s with id %s not found", entityType, id));
    }
    
    public EntityNotFoundException(Class<?> entityType, Object id) {
        this(entityType.getSimpleName(), id);
    }
}

/**
 * 查询语法异常
 */
public class QuerySyntaxException extends DataAccessException {
    
    public QuerySyntaxException(String query, String reason) {
        super("QUERY_SYNTAX_ERROR", 
              String.format("Query syntax error in [%s]: %s", query, reason));
    }
    
    public QuerySyntaxException(String query, Throwable cause) {
        super("QUERY_SYNTAX_ERROR", 
              String.format("Query syntax error in [%s]", query), cause);
    }
}

/**
 * 数据完整性违反异常
 */
public class DataIntegrityViolationException extends DataAccessException {
    
    public DataIntegrityViolationException(String message) {
        super("DATA_INTEGRITY_VIOLATION", message);
    }
    
    public DataIntegrityViolationException(String message, Throwable cause) {
        super("DATA_INTEGRITY_VIOLATION", message, cause);
    }
}

/**
 * 乐观锁异常
 */
public class OptimisticLockException extends DataAccessException {
    
    public OptimisticLockException(String entityType, Object id) {
        super("OPTIMISTIC_LOCK_FAILURE", 
              String.format("Optimistic lock failure for %s with id %s", entityType, id));
    }
    
    public OptimisticLockException(String message, Throwable cause) {
        super("OPTIMISTIC_LOCK_FAILURE", message, cause);
    }
}

/**
 * 提供者未找到异常
 */
public class ProviderNotFoundException extends DataAccessException {
    
    public ProviderNotFoundException(String providerName) {
        super("PROVIDER_NOT_FOUND", 
              String.format("Data access provider [%s] not found", providerName));
    }
    
    public ProviderNotFoundException(Class<?> entityType) {
        super("PROVIDER_NOT_FOUND", 
              String.format("No suitable provider found for entity type [%s]", 
                           entityType.getName()));
    }
}
```

### 4.2 异常映射和转换

```java
package org.xkong.cloud.commons.db.core.exception;

/**
 * 异常转换器接口
 * 将底层异常转换为统一的数据访问异常
 */
public interface ExceptionTranslator {
    
    /**
     * 转换异常
     * @param ex 原始异常
     * @return 转换后的数据访问异常
     */
    DataAccessException translate(Throwable ex);
    
    /**
     * 检查是否支持转换指定异常
     * @param ex 异常
     * @return 是否支持
     */
    boolean supports(Throwable ex);
    
    /**
     * 获取转换器优先级
     * @return 优先级
     */
    int getOrder();
}

/**
 * 组合异常转换器
 * 管理多个异常转换器
 */
public class CompositeExceptionTranslator implements ExceptionTranslator {
    
    private final List<ExceptionTranslator> translators;
    
    public CompositeExceptionTranslator(List<ExceptionTranslator> translators) {
        this.translators = translators.stream()
            .sorted(Comparator.comparingInt(ExceptionTranslator::getOrder))
            .collect(Collectors.toList());
    }
    
    @Override
    public DataAccessException translate(Throwable ex) {
        for (ExceptionTranslator translator : translators) {
            if (translator.supports(ex)) {
                return translator.translate(ex);
            }
        }
        
        // 默认转换
        return new DataAccessException("Unexpected data access error", ex);
    }
    
    @Override
    public boolean supports(Throwable ex) {
        return translators.stream().anyMatch(t -> t.supports(ex));
    }
    
    @Override
    public int getOrder() {
        return Integer.MAX_VALUE; // 最低优先级
    }
}
```

## 5. 配置和常量定义

### 5.1 数据源配置

```java
package org.xkong.cloud.commons.db.core.config;

import org.xkong.cloud.commons.exception.validation.ValidationBusinessException;
import javax.sql.DataSource;
import java.util.Map;
import java.util.Properties;

/**
 * 数据源配置
 */
public class DataSourceConfig {
    
    private final String name;
    private final DataSource dataSource;
    private final String driverClassName;
    private final String url;
    private final String username;
    private final Properties properties;
    private final Map<String, Object> extensions;
    
    private DataSourceConfig(Builder builder) {
        this.name = builder.name;
        this.dataSource = builder.dataSource;
        this.driverClassName = builder.driverClassName;
        this.url = builder.url;
        this.username = builder.username;
        this.properties = new Properties(builder.properties);
        this.extensions = Map.copyOf(builder.extensions);
    }
    
    // Getters
    public String getName() { return name; }
    public DataSource getDataSource() { return dataSource; }
    public String getDriverClassName() { return driverClassName; }
    public String getUrl() { return url; }
    public String getUsername() { return username; }
    public Properties getProperties() { return new Properties(properties); }
    public Map<String, Object> getExtensions() { return Map.copyOf(extensions); }
    
    // Builder pattern
    public static class Builder {
        private String name;
        private DataSource dataSource;
        private String driverClassName;
        private String url;
        private String username;
        private Properties properties = new Properties();
        private Map<String, Object> extensions = new HashMap<>();
        
        public Builder name(String name) {
            this.name = name;
            return this;
        }
        
        public Builder dataSource(DataSource dataSource) {
            this.dataSource = dataSource;
            return this;
        }
        
        public Builder driverClassName(String driverClassName) {
            this.driverClassName = driverClassName;
            return this;
        }
        
        public Builder url(String url) {
            this.url = url;
            return this;
        }
        
        public Builder username(String username) {
            this.username = username;
            return this;
        }
        
        public Builder properties(Properties properties) {
            this.properties.putAll(properties);
            return this;
        }
        
        public Builder property(String key, String value) {
            this.properties.setProperty(key, value);
            return this;
        }
        
        public Builder extension(String key, Object value) {
            this.extensions.put(key, value);
            return this;
        }
        
        public DataSourceConfig build() {
            if (name == null || name.trim().isEmpty()) {
                throw ValidationBusinessException.invalidArgument("XCE_VAL_750", "DataSource name is required");
            }
            if (dataSource == null) {
                throw ValidationBusinessException.invalidArgument("XCE_VAL_751", "DataSource is required");
            }
            return new DataSourceConfig(this);
        }
    }
}
```

### 5.2 全局配置

```java
package org.xkong.cloud.commons.db.core.config;

import java.time.Duration;
import java.util.Map;

/**
 * 全局配置
 */
public class GlobalConfig {
    
    private final boolean enabled;
    private final String defaultProvider;
    private final Duration defaultQueryTimeout;
    private final int defaultBatchSize;
    private final boolean enableMetrics;
    private final boolean enableHealthCheck;
    private final Map<String, Object> providerConfigs;
    
    private GlobalConfig(Builder builder) {
        this.enabled = builder.enabled;
        this.defaultProvider = builder.defaultProvider;
        this.defaultQueryTimeout = builder.defaultQueryTimeout;
        this.defaultBatchSize = builder.defaultBatchSize;
        this.enableMetrics = builder.enableMetrics;
        this.enableHealthCheck = builder.enableHealthCheck;
        this.providerConfigs = Map.copyOf(builder.providerConfigs);
    }
    
    // Getters
    public boolean isEnabled() { return enabled; }
    public String getDefaultProvider() { return defaultProvider; }
    public Duration getDefaultQueryTimeout() { return defaultQueryTimeout; }
    public int getDefaultBatchSize() { return defaultBatchSize; }
    public boolean isEnableMetrics() { return enableMetrics; }
    public boolean isEnableHealthCheck() { return enableHealthCheck; }
    public Map<String, Object> getProviderConfigs() { return Map.copyOf(providerConfigs); }
    
    // Builder pattern
    public static class Builder {
        private boolean enabled = true;
        private String defaultProvider = "jpa";
        private Duration defaultQueryTimeout = Duration.ofSeconds(30);
        private int defaultBatchSize = 100;
        private boolean enableMetrics = true;
        private boolean enableHealthCheck = true;
        private Map<String, Object> providerConfigs = new HashMap<>();
        
        public Builder enabled(boolean enabled) {
            this.enabled = enabled;
            return this;
        }
        
        public Builder defaultProvider(String defaultProvider) {
            this.defaultProvider = defaultProvider;
            return this;
        }
        
        public Builder defaultQueryTimeout(Duration defaultQueryTimeout) {
            this.defaultQueryTimeout = defaultQueryTimeout;
            return this;
        }
        
        public Builder defaultBatchSize(int defaultBatchSize) {
            this.defaultBatchSize = defaultBatchSize;
            return this;
        }
        
        public Builder enableMetrics(boolean enableMetrics) {
            this.enableMetrics = enableMetrics;
            return this;
        }
        
        public Builder enableHealthCheck(boolean enableHealthCheck) {
            this.enableHealthCheck = enableHealthCheck;
            return this;
        }
        
        public Builder providerConfig(String provider, Object config) {
            this.providerConfigs.put(provider, config);
            return this;
        }
        
        public GlobalConfig build() {
            return new GlobalConfig(this);
        }
    }
    
    public static Builder builder() {
        return new Builder();
    }
}
```

### 5.3 常量定义

```java
package org.xkong.cloud.commons.db.core.constants;

/**
 * 数据访问常量
 */
public final class DataAccessConstants {
    
    private DataAccessConstants() {
        // 私有构造函数，防止实例化
    }
    
    // 提供者名称
    public static final String PROVIDER_JPA = "jpa";
    public static final String PROVIDER_QUERYDSL = "querydsl";
    public static final String PROVIDER_JDBC = "jdbc";
    public static final String PROVIDER_MYBATIS = "mybatis";
    
    // 配置键名
    public static final String CONFIG_PREFIX = "xkong.commons.db";
    public static final String CONFIG_ENABLED = CONFIG_PREFIX + ".enabled";
    public static final String CONFIG_DEFAULT_PROVIDER = CONFIG_PREFIX + ".default-provider";
    public static final String CONFIG_QUERY_TIMEOUT = CONFIG_PREFIX + ".query-timeout";
    public static final String CONFIG_BATCH_SIZE = CONFIG_PREFIX + ".batch-size";
    
    // 数据源配置
    public static final String DATASOURCE_PREFIX = CONFIG_PREFIX + ".datasources";
    public static final String DATASOURCE_PRIMARY = "primary";
    public static final String DATASOURCE_SECONDARY = "secondary";
    
    // 监控指标名称
    public static final String METRIC_QUERY_DURATION = "db.query.duration";
    public static final String METRIC_QUERY_COUNT = "db.query.count";
    public static final String METRIC_BATCH_SIZE = "db.batch.size";
    public static final String METRIC_ERROR_COUNT = "db.error.count";
    
    // 查询提示
    public static final String HINT_TIMEOUT = "javax.persistence.query.timeout";
    public static final String HINT_CACHE_MODE = "javax.persistence.cache.retrieveMode";
    public static final String HINT_FETCH_SIZE = "hibernate.jdbc.fetch_size";
    public static final String HINT_READ_ONLY = "org.hibernate.readOnly";
    
    // 错误码
    public static final String ERROR_ENTITY_NOT_FOUND = "ENTITY_NOT_FOUND";
    public static final String ERROR_QUERY_SYNTAX = "QUERY_SYNTAX_ERROR";
    public static final String ERROR_DATA_INTEGRITY = "DATA_INTEGRITY_VIOLATION";
    public static final String ERROR_OPTIMISTIC_LOCK = "OPTIMISTIC_LOCK_FAILURE";
    public static final String ERROR_PROVIDER_NOT_FOUND = "PROVIDER_NOT_FOUND";
    public static final String ERROR_UNSUPPORTED_OPERATION = "UNSUPPORTED_OPERATION";
}

/**
 * 查询类型枚举
 */
public enum QueryType {
    SELECT("SELECT"),
    INSERT("INSERT"),
    UPDATE("UPDATE"),
    DELETE("DELETE"),
    COUNT("COUNT"),
    EXISTS("EXISTS"),
    CUSTOM("CUSTOM");
    
    private final String value;
    
    QueryType(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
}

/**
 * 缓存策略枚举（未来规划）
 * 当前V3.0版本仅作为接口预留，不实现具体缓存功能
 */
public enum CacheStrategy {
    NONE("NONE"),        // 不使用缓存
    USE("USE"),          // 使用缓存（未来实现）
    BYPASS("BYPASS"),    // 绕过缓存（未来实现）
    REFRESH("REFRESH");  // 刷新缓存（未来实现）
    
    private final String value;
    
    CacheStrategy(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
}

/**
 * 读取策略枚举
 */
public enum ReadStrategy {
    PRIMARY("PRIMARY"),
    SECONDARY("SECONDARY"),
    ANY("ANY");
    
    private final String value;
    
    ReadStrategy(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
}

/**
 * 健康状态枚举
 */
public enum HealthStatus {
    UP("UP"),
    DOWN("DOWN"),
    UNKNOWN("UNKNOWN"),
    OUT_OF_SERVICE("OUT_OF_SERVICE");
    
    private final String value;
    
    HealthStatus(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
}
```

## 6. 扩展点设计

### 6.1 拦截器机制

```java
package org.xkong.cloud.commons.db.core.interceptor;

/**
 * 数据访问拦截器接口
 */
public interface DataAccessInterceptor {
    
    /**
     * 执行前拦截
     * @param context 执行上下文
     * @return 是否继续执行
     */
    boolean preHandle(DataAccessContext context);
    
    /**
     * 执行后拦截
     * @param context 执行上下文
     * @param result 执行结果
     */
    void postHandle(DataAccessContext context, Object result);
    
    /**
     * 异常拦截
     * @param context 执行上下文
     * @param ex 异常
     */
    void afterException(DataAccessContext context, Throwable ex);
    
    /**
     * 获取拦截器优先级
     * @return 优先级
     */
    int getOrder();
}

/**
 * 数据访问上下文
 */
public class DataAccessContext {
    
    private final String operation;
    private final Class<?> entityType;
    private final Object entity;
    private final QuerySpec<?> querySpec;
    private final Map<String, Object> attributes;
    private final long startTime;
    
    // 构造函数和getters...
}
```

### 6.2 插件机制

```java
package org.xkong.cloud.commons.db.core.plugin;

/**
 * 数据访问插件接口
 */
public interface DataAccessPlugin {
    
    /**
     * 获取插件名称
     * @return 插件名称
     */
    String getName();
    
    /**
     * 获取插件版本
     * @return 插件版本
     */
    String getVersion();
    
    /**
     * 初始化插件
     * @param config 配置
     */
    void initialize(PluginConfig config);
    
    /**
     * 销毁插件
     */
    void destroy();
    
    /**
     * 插件是否启用
     * @return 是否启用
     */
    boolean isEnabled();
    
    /**
     * 获取插件配置
     * @return 插件配置
     */
    PluginConfig getConfig();
}
```

## 7. 实现规范

### 7.1 编码规范
- 所有公开接口必须提供完整的JavaDoc文档
- 异常处理必须基于XCE异常体系
- 配置类必须使用Builder模式
- 常量必须定义在专门的常量类中

### 7.2 性能规范
- 所有查询操作必须支持超时设置
- 批量操作必须使用批处理优化
- 大结果集必须支持分页查询
- 重要操作必须提供监控指标

### 7.3 兼容性规范
- 接口设计必须向后兼容
- 新增功能通过扩展点实现
- 配置变更必须提供迁移指南
- API变更必须遵循语义化版本

## 8. XCE异常集成设计

### 8.1 XCE异常分类标准

**数据库类异常 (XCE_DB_xxx)**：
- 错误码段：650-699
- 包路径：org.xkong.cloud.commons.db.exception.database

```java
/**
 * 数据库系统异常
 */
public class DatabaseSystemException extends SystemException {

    // 连接相关异常 (650-659)
    public static final String CONNECTION_FAILED = "XCE_DB_650";
    public static final String CONNECTION_POOL_EXHAUSTED = "XCE_DB_651";
    public static final String CONNECTION_TIMEOUT = "XCE_DB_652";
    public static final String CONNECTION_LEAK_DETECTED = "XCE_DB_653";

    // 事务相关异常 (660-669)
    public static final String TRANSACTION_TIMEOUT = "XCE_DB_660";
    public static final String TRANSACTION_ROLLBACK_FAILED = "XCE_DB_661";
    public static final String DEADLOCK_DETECTED = "XCE_DB_662";
    public static final String TRANSACTION_ISOLATION_VIOLATION = "XCE_DB_663";

    // 查询相关异常 (670-679)
    public static final String QUERY_TIMEOUT = "XCE_DB_670";
    public static final String QUERY_EXECUTION_FAILED = "XCE_DB_671";
    public static final String RESULT_SET_TOO_LARGE = "XCE_DB_672";
    public static final String QUERY_PLAN_INVALID = "XCE_DB_673";

    // 约束相关异常 (680-689)
    public static final String CONSTRAINT_VIOLATION = "XCE_DB_680";
    public static final String FOREIGN_KEY_VIOLATION = "XCE_DB_681";
    public static final String UNIQUE_CONSTRAINT_VIOLATION = "XCE_DB_682";
    public static final String CHECK_CONSTRAINT_VIOLATION = "XCE_DB_683";

    // 便捷的异常创建方法
    public static DatabaseSystemException connectionPoolExhausted() {
        return new DatabaseSystemException(CONNECTION_POOL_EXHAUSTED, "数据库连接池已耗尽")
            .addMetadata("component", "commons-db")
            .addMetadata("category", "connection");
    }

    public static DatabaseSystemException queryTimeout(String sql, long timeoutMs) {
        return new DatabaseSystemException(QUERY_TIMEOUT,
            String.format("查询超时: %dms", timeoutMs))
            .addMetadata("sql_hash", generateSqlHash(sql))
            .addMetadata("timeout_ms", String.valueOf(timeoutMs))
            .addMetadata("component", "commons-db");
    }
}
```

**验证类异常 (XCE_VAL_xxx)**：
- 错误码段：760-769（Commons DB专用）
- 包路径：org.xkong.cloud.commons.db.exception.validation

```java
/**
 * 数据验证业务异常
 */
public class DataValidationException extends BusinessException {

    // 实体验证异常 (760-764)
    public static final String ENTITY_NOT_FOUND = "XCE_VAL_760";
    public static final String ENTITY_STATE_INVALID = "XCE_VAL_761";
    public static final String ENTITY_VERSION_CONFLICT = "XCE_VAL_762";
    public static final String ENTITY_ALREADY_EXISTS = "XCE_VAL_763";

    // 查询验证异常 (765-769)
    public static final String QUERY_SYNTAX_INVALID = "XCE_VAL_765";
    public static final String PARAMETER_INVALID = "XCE_VAL_766";
    public static final String PAGINATION_INVALID = "XCE_VAL_767";
    public static final String SORT_CRITERIA_INVALID = "XCE_VAL_768";

    // 便捷的异常创建方法
    public static DataValidationException entityNotFound(String entityType, Object id) {
        return new DataValidationException(ENTITY_NOT_FOUND,
            String.format("实体未找到: %s[id=%s]", entityType, id))
            .addMetadata("entity_type", entityType)
            .addMetadata("entity_id", String.valueOf(id))
            .addMetadata("component", "commons-db");
    }
}
```

### 8.2 异常转换机制

```java
@Component
public class CommonsDbExceptionTranslator {

    /**
     * Spring DataAccessException转换
     */
    public ServiceException translate(DataAccessException ex, String operation) {
        // 连接相关异常
        if (ex instanceof DataAccessResourceFailureException) {
            return DatabaseSystemException.connectionPoolExhausted();
        }

        // 查询超时异常
        if (ex instanceof QueryTimeoutException) {
            return DatabaseSystemException.queryTimeout("unknown", extractTimeout(ex));
        }

        // 约束违规异常
        if (ex instanceof DataIntegrityViolationException) {
            return translateConstraintViolation((DataIntegrityViolationException) ex);
        }

        // 死锁异常
        if (ex instanceof CannotAcquireLockException) {
            return DatabaseSystemException.deadlockDetected(operation);
        }

        // 实体未找到异常
        if (ex instanceof EmptyResultDataAccessException) {
            return DataValidationException.entityNotFound("unknown", "unknown");
        }

        // 默认转换为通用数据库异常
        return new DatabaseSystemException(DatabaseSystemException.QUERY_EXECUTION_FAILED,
            "数据库操作失败: " + operation, ex);
    }
}
```

## 9. 下一步计划

1. **完成接口定义**：实现所有核心接口的完整定义
2. **异常体系集成**：与XCE异常库完整集成
3. **配置体系设计**：完善配置管理和验证机制
4. **SPI机制实现**：实现完整的SPI注册和发现机制
5. **单元测试设计**：为所有接口设计单元测试框架