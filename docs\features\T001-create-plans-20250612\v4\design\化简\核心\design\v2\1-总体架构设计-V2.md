# V4.2方案：总体架构设计 (虚拟项目经理交互版)

## 1. 文档信息

- **文档版本**: V4.2.2
- **创建日期**: 2025-07-30
- **更新日期**: 2025-08-02
- **核心思想**: **构建一个健壮的、基于HTTP轮询的异步任务框架，作为承载"统一语义模型与插件化"治理引擎的基础。**
- **设计目标**: 构建一个由用户通过虚拟项目经理工作台触发，通过三段式API实现任务创建、状态监控和后台执行的解耦，为未来复杂的治理流程提供一个稳定、可靠且可交互的前端操作界面。

## 2. 核心设计哲学：信任始于标准化的统一语义模型

V4.2方案的设计哲学实现了关键的范式转变：**真正的可靠性，源于在流程开始前，就将模糊的、易产生冲突的“设计思想”，转化为统一的、带有丰富语义的、可扩展的、经过预验证的“原子知识单元” (`AtomicConstraint`)。**

我们不再依赖于在流程后期去“检测”冲突，而是在“阶段零”就通过确定性算法来“预防”冲突。所有后续的治理，都建立在这个100%无内部冲突的、由统一模型构成的“**全局知识库**”之上。

## 3. 核心架构：由虚拟项目经理交互驱动的四阶段治理流程

系统整体架构分为**Web交互层**和**后端服务层**两大部分。其核心是基于HTTP的**三段式异步任务模型**，实现了前后端的完全解耦。

```mermaid
graph TD
    subgraph "Web Interface (Client)"
        A[用户] -- 1. 输入目录, 点击确定 --> UI[动态输入框]
        UI -- 2. 调用创建API --> C_API[API Client: POST /get_and_create]
        C_API -- 3. 获得manager_id, 页面跳转 --> R_API[API Client: GET /status/{id}]
        R_API -- 4. 周期性轮询 --> R_API
        R_API -- 5. 更新UI --> S[状态/日志显示]
        A -- 6. 点击“开始审查” --> E_API[API Client: POST /start-review/{id}]
    end

    subgraph "Backend Engine (Server)"
        C_API -- 7. 请求 --> PMS[ProjectManagerService]
        PMS -- 8. 创建/获取实例 --> PM[ProjectManager]
        PM -- 9. 初始化状态 --> TL[TaskLogbook]
        
        R_API -- 10. 请求 --> PMS
        PMS -- 11. 查询状态 --> TL
        
        E_API -- 12. 请求 --> PMS
        PMS -- 13. 触发后台任务 --> PM
        PM -- 14. 在后台线程执行治理蓝图 --> GB[Governance Blueprints]
        GB -- 15. 持续更新状态 --> TL
    end
```
**架构核心**: 系统的核心交互由 **`ProjectManagerService` (项目经理服务)**、**`ProjectManager` (项目经理)** 和 **`TaskLogbook` (任务日志簿)** 构成，通过一套三段式HTTP API与前端通信。
- **`ProjectManagerService`**: 作为应用级的**单例服务**和API入口，负责根据前端请求，创建、管理并分发`ProjectManager`实例。
- **`ProjectManager`**: 作为**单一项目**的治理核心，每个实例与一个特定的项目绑定。它负责在**后台线程**中驱动治理蓝图(`GovernanceBlueprint`)的执行。
- **`TaskLogbook`**: 作为**状态管理中心**，每个`ProjectManager`实例都拥有一个独立的`TaskLogbook`实例。后端任务在执行时，将其进度和状态更新到`TaskLogbook`中。
- **三段式API与HTTP轮询**:
    1.  **创建任务**: 前端通过 `POST /api/pm_v2/get_and_create` 创建一个`ProjectManager`实例并获取唯一的`manager_id`。
    2.  **轮询状态**: 前端使用`manager_id`，通过 `GET /api/pm_v2/status/{manager_id}` 定期轮询任务状态，并更新UI。
    3.  **启动执行**: 用户通过 `POST /api/pm_v2/start-review/{manager_id}` 触发后台的实际治理任务。
这种基于HTTP轮询的异步模型取代了原有的WebSocket方案，实现了更简单、更健壮的前后端解耦。

### 3.1. 阶段零：全局约束库的标准化与预验证

此阶段是V4.2架构的可靠性基石，由用户点击"开始项目检测"按钮后**异步触发**。其详细逻辑在《4-微观图构建与双重验证引擎-V2.md》中定义。

- **核心理念**: **冲突预防优于冲突检测**。
- **过程**:
    1.  **意图识别与实体分类 (AI)**: `首席架构师AI`从顶层文档中识别设计意图，并对其进行**技术实体分类**（即为`AtomicConstraint`确定`category`）。
    2.  **结构化提取 (AI/人工)**: 在一个统一的、结构化的`AtomicConstraint`模板中，为不同`category`填充其专属的`params`结构。
    3.  **确定性预验证 (算法护栏)**: `ConstraintPreprocessor`算法作为**内核的第二道护栏**，在知识单元存入库前，进行确定性的逻辑和数值校验（如Schema验证、范围检查），从源头杜绝不合规的数据进入知识库。
- **产出**: (未来愿景) 一个100%无内部冲突的"**全局知识库**"，其中存储的都是`AtomicConstraint`对象。同时，系统可生成一份**文档健康报告 (`DocumentHealthReport`)**。
- **当前实现**: 在当前异步任务框架下，此阶段的核心是`ProjectManager`实例的创建。后台任务的执行状态和日志会通过`TaskLogbook`进行记录，前端通过HTTP轮询获取这些信息并更新UI。

### 3.2. 阶段一：全局契约生成

此阶段的目标是基于这个**高度可靠的**“全局约束库”，建立项目的“最高指导原则”。

- **输入**:
    1.  `01号`文档。
    2.  阶段零产出的“**全局知识库**”。
- **过程**: AI的任务被大大简化和可靠化。它不再需要从模糊的文本中猜测，而是将结构化的`AtomicConstraint`对象，根据其`category`，翻译成指向特定验证插件的、机器可读的`ValidationChainPoint`。
- **产出**: 一份极其可靠、可追溯的“**全局约束集**”。

### 3.3. 阶段二：引用式契约生成与审计

此阶段是V4.2“引用式分叉”思想的核心体现。

- **核心机制**: **引用 (Reference)** 与 **分叉 (Fork)**。
- **过程**:
    1.  **上下文提供**: “领域架构师AI”（逻辑层）会接收到一份上下文，其中包含了所有可用的“全局约束库”中的原子约束及其ID。
    2.  **AI决策**: AI根据当前的局部设计需求，进行决策：
        -   **直接引用**: 如果某个全局约束完全适用，AI只需引用其ID。
        -   **分叉与细化**: 如果全局约束过于宏观，AI会创建一个新的、更严格的本地版本，并使用`parent_id`字段指明其来源。
    3.  **契约审计**: `ContractAuditor`在审计时，会验证所有被引用的ID是否真实存在于全局约束库中，确保了引用链的完整性。
- **产出**: 一份上下文感知的局部契约，其约束要么直接引用自全局，要么清晰地“分叉”自全局，血统清晰，完全可追溯。

## 4. 核心原则与治理模型

V4.2架构的治理哲学建立在一个核心模型之上。

### 4.1. 核心模型：基于预验证的锥形治理 (Pre-validated Conical Governance)

整个治理流程依然构建为一个“锥形”模型，但其可靠性得到了根本性的增强。

-   **坚实的底座**: 锥体的底座不再是模糊的、可能存在风险的顶层文档，而是经过“阶段零”标准化与预验证的、100%无冲突的“**全局知识库**”。
-   **可靠的传递**: **所有类型**的知识单元都通过统一的`AtomicConstraint`模型和“引用式分叉”机制进行传递。这确保了顶层思想能够**无损耗、无歧义、可追溯**地传递到锥形的每一个层面。

### 4.2. 核心概念模型：两阶段知识提炼管道

为了确保系统概念的绝对清晰和高度一致，所有从`01号`文档到`AtomicConstraint`的转化，都必须严格遵循以下两阶段的知识提炼管道。

```mermaid
graph TD
    A["源头文本 (01号文档)"] --> B{"第一层：意图分类<br/>(宏观语义地图构建)"};
    B -- "这段文本的目的是？" --> C["产出：guideline_mapping<br/>(约束, 护栏, 上下文, 正文)"];
    
    C --> D{"第二层：实体分类<br/>(阶段零：标准化与预验证)"};
    D -- "这个'约束'的技术实体是？" --> E["产出：AtomicConstraint<br/>及其 category<br/>(performance, state_machine, etc.)"];

    subgraph "上游：黄金准则"
        B
        C
    end

    subgraph "下游：统一语义模型"
        D
        E
    end
```

### 4.3. 架构级护栏：守护内核的边界规则

为了确保V2内核的纯粹性和可靠性，防止其被上游（无论是AI还是人工）的模糊或错误输入所“污染”，我们定义了以下三条不可逾越的架构级护栏。这些规则由系统在流程中强制执行，是守护内核边界、确保数据质量一致性的关键防线。

1.  **护栏1：职责单一原则。** “宏观语义地图构建”流程的**唯一**职责是进行“意图分类”，产出`guideline_mapping`。它**严禁**尝试进行“实体分类”。
2.  **护栏2：阶段锁定原则。** “阶段零：标准化与预验证”流程的输入**必须**是已经完成了“意图分类”的语义块。它的职责是对这些意图进行“实体分类”，产出`AtomicConstraint`。它**严禁**修改上游传入的`guideline_mapping`。
3.  **护栏3：血统追溯原则。** 每一个`AtomicConstraint`实体，**必须**通过`source_block_id`字段（或其他机制），清晰地追溯到其唯一的上游“意图块”。

## 5. 整体工作流程

V4.2的整体工作流程，通过在最前端重塑“约束生成”步骤，并由用户交互触发，构建了一个更完美的质量闭环。

1.  **步骤一：用户交互与任务启动** (V1交互模式)
2.  **步骤二：全局约束库的标准化与预验证** (V2核心)
3.  **步骤三：全局约束提取** (V2核心)
4.  **步骤四：引用式契约生成与审计** (V2核心)
5.  **步骤五：宏观语义地图构建** (V2核心)
6.  **步骤六：契约履行与原子审计** (V2核心)
7.  **步骤七：最终产出整体性审计** (V2核心)

## 6. 架构价值

V4.2方案通过其创新的“先标准化、再治理”的设计，达到了前所未有的可靠性和智能水平：

- **冲突预防，而非检测**: 从根本上避免了在流程后期处理复杂语义冲突的难题，实现了“无垃圾输入”。
- **优雅的约束传递**: “引用式分叉”机制，兼具了顶层设计的权威性和局部场景的灵活性，是一种极其先进的治理模式。
- **职责清晰的AI-算法协同**: AI负责识别高级意图和进行上下文决策，算法负责所有确定性的、结构化的工作（标准化、预验证、ID管理），实现了完美的协同。
- **极高的可追溯性**: 任何一个最终的执行约束，都可以通过`id`和`parent_id`链条，清晰地追溯到其在`01号`文档中的根源。
- **优秀的人机交互**: 通过借鉴V1成熟的Web交互模式，为强大的后端内核提供了直观、易用的操作入口。

## 7. 工程实现蓝图 (Engineering Blueprint)

为了将上述架构思想转化为可落地、可维护的工程实践，我们定义了以下的代码组织结构和模块职责。

### 7.1. 代码目录结构

所有与本治理引擎相关的代码，都将被封装在一个名为 `governance_engine` 的独立Python包中，并置于 `tools/ace/src/` 目录下，以实现与现有工程的无缝集成。

```
tools/ace/
└── src/
    ├── project_manager_v2/             # <-- V2治理引擎的独立包
    │   ├── __init__.py
    │   ├── services/                   # <-- 服务层 (新增)
    │   │   └── project_manager_service.py # 负责管理PM实例的单例服务
    │   │
    │   ├── manager/                    # <-- 项目经理层 (新增)
    │   │   └── project_manager.py      # 负责驱动单一项目治理流程的实例
    │   │
    │   ├── blueprints/                 # <-- 治理蓝图层 (职责不变)
    │   │   ├── __init__.py
    │   │   ├── base_blueprint.py
    │   │   └── ...
    │   │
    │   └── data_models/                # V2核心数据模型 (职责不变)
    │       └── v2_models.py
    │
    ├── web_interface/                  # <-- Web接口层
    │   ├── __init__.py
    │   ├── app.py                      # <-- 主Flask应用，将注册蓝图并初始化Service
    │   ├── static/
    │   ├── templates/
    │   └── blueprints/                 # <-- Flask蓝图目录
    │       ├── __init__.py
    │       └── pm_v2_blueprint.py      # <-- 项目经理V2的路由和视图
    │
    └── ...
```

### 7.2. 关键模块职责

- **`web_interface/app.py`**: 作为Web应用主入口，负责初始化Flask应用，注册所有功能蓝图，并**初始化`ProjectManagerService`为全局单例**。
- **`web_interface/blueprints/pm_v2_blueprint.py`**: 定义与项目经理V2相关的所有Web接口。其职责是“轻量化”的，仅负责接收请求、解析参数，然后**调用全局的`ProjectManagerService`单例**来处理所有业务逻辑。
- **`project_manager_v2/services/`**: **(新增)** 应用的服务层。`ProjectManagerService`作为单例服务，负责创建和管理所有`ProjectManager`实例。
- **`project_manager_v2/manager/`**: **(新增)** 系统的项目治理核心。`ProjectManager`类定义于此，每个实例对应一个具体的项目治理流程。
- **`project_manager_v2/blueprints/`**: 系统的“工具箱”。职责不变，每个蓝图完成一项具体任务，并向其所属的`ProjectManager`实例返回结果。
- **`project_manager_v2/data_models/`**: 职责不变，作为系统的数据模型基石。

> **实现状态备注**: 当前 `ProjectManagerService` 和 `pm_v2_blueprint` 已初步实现并解耦。`ProjectManager` 和 `GovernanceBlueprint` 是后续开发阶段的核心任务。

## 8. 未来架构愿景：人机协同的架构重构工作流

**【注意：本章节描述的是一个未来的、尚未实现的架构愿景。当前V4.2的实现是一个更基础的异步任务框架，旨在为本章节描述的宏大目标奠定坚实的基础。】**

当V2引擎完全成熟后，其核心能力不仅在于从零创建符合设计的代码（“创造者”模式），更在于其作为一个**设计文档驱动的治理引擎**，能够安全、可控地对现有代码库进行大规模重构（“改造者”模式）。此工作流旨在解决将现有代码与新架构规范对齐的复杂问题，例如推广使用统一的异常库、日志库或更新核心依赖。

其核心思想是**“立法 -> 前置检查 -> 执法 -> 司法”**的治理闭环，确保所有变更都源于设计、可被审查、可被验证，且具备应对现实世界复杂性的鲁棒性。

```mermaid
graph TD
    subgraph "人类领域 (Human Domain)"
        A["第一步: 人工修订<br/><b>设计法典 (设计文档)</b>"]
        D_FIX["第三步 (失败路径):<br/>人工处理<b>缺失的外部条件</b><br/>(如添加依赖库)"]
        E["第五步: 人工进行<br/><b>司法审查 (审查变更集)</b>"]
    end

    subgraph "V2引擎领域 (Engine Domain)"
        B["第二步: V2进行<br/><b>前置条件检查</b><br/>(检查依赖、配置等是否存在)"]
        C["第三步 (成功路径):<br/>V2进行<b>差异审计</b><br/>(对比'法典'与'现实代码')"]
        D["第四步: V2规划<br/><b>修正案 (生成变更集)</b>"]
        F["第六步: V2执行变更<br/>并运行<b>集成测试</b>进行验证"]
    end

    A -- 触发 --> B
    B -- 成功 --> C
    B -- 失败, 通知人类 --> D_FIX
    D_FIX -- 修复后重新触发 --> B
    C -- 发现差异 --> D
    D -- 提交计划 --> E
    E -- 批准 --> F
    F -- 完成 --> G[产出: 符合新设计<br/>且通过测试的现实代码]
```

### 8.1. 工作流详解

1.  **步骤一：人工修订“设计法典” (立法)**
    -   **核心**: 所有重构任务的**唯一合法起点**。
    -   **动作**: 架构师在相关的设计文档中，明确增加或修改一条全局约束。例如，增加一条“所有模块必须使用`@xkong/unified-exception-library`”的`AtomicConstraint`定义。

2.  **步骤二：V2引擎进行“前置条件检查” (Pre-condition Check)**
    -   **核心**: 在盲目修改代码前，首先确保执行该设计所需的所有**外部条件**都已满足。这是V2架构鲁棒性的关键体现。
    -   **触发**: 人类在九宫格界面启动“检查”或“治理”任务。
    -   **动作**: 引擎解析设计文档中的约束后，会先检查其依赖的外部条件。例如，对于“使用统一异常库”的约束，引擎会扫描项目的依赖管理文件（如`pom.xml`, `package.json`），检查该库是否真实存在。
    -   **分支**:
        -   **如果检查失败**: 流程**立即中止**。引擎不会进行下一步的差异审计，而是直接在九宫格界面通知人类，明确指出“缺失的外部条件”（见步骤三，失败路径）。
        -   **如果检查成功**: 流程继续进行到下一步（步骤三，成功路径）。

3.  **步骤三：处理检查结果**
    -   **失败路径：人工处理缺失条件**
        -   **交互点**: 九宫格界面**区域5（项目经理决策中心）**会显示“前置条件检查失败”的警报，并清晰说明需要人工完成的任务（例如，“请先在`pom.xml`中添加`@xkong/unified-exception-library`依赖”）。
        -   **动作**: 人类开发者根据提示，手动完成外部条件的准备工作。完成后，可重新触发V2引擎。
    -   **成功路径：V2引擎进行“差异审计” (执法-调查)**
        -   **动作**: 前置条件满足后，V2引擎才会开始对比“法典”与“现实代码”，识别出所有不符合最新设计约束的代码点。

4.  **步骤四：V2引擎规划“修正案” (执法-规划)**
    -   **目标**: 基于“差异审计”的结果，生成一个能够弥合差异的、最小化的、可执行的变更计划。
    -   **产出**: 一个结构化的**“变更集” (`ChangeSet`)**，作为“中间结果”提交给人类审查。

5.  **步骤五：人工进行“司法审查”**
    -   **交互点**: “变更集”被呈现在九宫格界面的**区域5**。
    -   **动作**: 人类开发者审查这份修改计划，并拥有最终的批准或否决权。

6.  **步骤六：V2执行并通过“集成测试”验证**
    -   **触发**: 只有在人类批准“变更集”后，V2引擎才会启动代码修改。
    -   **动作**:
        -   使用`replace_in_file`等工具，精确执行变更。
        -   执行完毕后，**自动触发**该模块的编译、单元测试和集成测试。
    -   **原则**: 将代码修改与项目既有的质量保障体系（CI/CD）紧密结合，是确保重构安全性的最后一道防线。
