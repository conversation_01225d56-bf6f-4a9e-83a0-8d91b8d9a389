#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
架构级矛盾推理引擎 - 基于架构设计模式的高级矛盾检测和推理系统
实现从文档解析层面上升到架构思维层面的矛盾检测
"""

import asyncio
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class ArchitecturalContradictionType(Enum):
    """架构矛盾类型分类"""
    PATTERN_CONFLICT = "pattern_conflict"
    QUALITY_ATTRIBUTE_CONFLICT = "quality_attribute_conflict"
    ARCHITECTURAL_PRINCIPLE_VIOLATION = "principle_violation"
    TECHNOLOGY_STACK_INCONSISTENCY = "tech_stack_inconsistency"


class ContradictionSeverity(Enum):
    """矛盾严重程度"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class ArchitecturalContradiction:
    """架构矛盾数据结构"""
    contradiction_id: str
    contradiction_type: ArchitecturalContradictionType
    severity: ContradictionSeverity
    description: str
    root_cause: str
    impact_analysis: str
    involved_components: List[str]
    affected_quality_attributes: List[str]
    detection_confidence: float
    evidence: List[str]
    resolution_strategies: List[str] = field(default_factory=list)
    document_sources: List[str] = field(default_factory=list)


class ArchitecturalReasoningRule(ABC):
    """架构推理规则抽象基类"""
    
    @abstractmethod
    async def detect_contradiction(self, architectural_context: Dict[str, Any]) -> List[ArchitecturalContradiction]:
        """检测架构矛盾"""
        pass
    
    @abstractmethod
    def get_rule_priority(self) -> int:
        """获取规则优先级"""
        pass


class MicrokernelPatternReasoningRule(ArchitecturalReasoningRule):
    """微内核模式推理规则"""
    
    def __init__(self):
        self.contradiction_patterns = {
            "plugin_direct_dependency": {
                "pattern": r"插件.*直接.*依赖.*插件",
                "contradiction_type": ArchitecturalContradictionType.PATTERN_CONFLICT,
                "severity": ContradictionSeverity.HIGH
            },
            "kernel_bloat": {
                "pattern": r"内核.*包含.*业务逻辑",
                "contradiction_type": ArchitecturalContradictionType.ARCHITECTURAL_PRINCIPLE_VIOLATION,
                "severity": ContradictionSeverity.MEDIUM
            }
        }
    
    async def detect_contradiction(self, architectural_context: Dict[str, Any]) -> List[ArchitecturalContradiction]:
        """检测微内核模式相关矛盾"""
        contradictions = []
        documents = architectural_context.get("documents", [])
        
        for doc in documents:
            content = doc.get("content", "")
            doc_id = doc.get("doc_id", "unknown")
            
            for pattern_name, pattern_info in self.contradiction_patterns.items():
                if self._matches_pattern(content, pattern_info["pattern"]):
                    contradiction = ArchitecturalContradiction(
                        contradiction_id=f"microkernel_{pattern_name}_{hash(doc_id) % 1000}",
                        contradiction_type=pattern_info["contradiction_type"],
                        severity=pattern_info["severity"],
                        description=f"微内核模式违反: {pattern_name}",
                        root_cause=f"违反了微内核架构原则",
                        impact_analysis="影响系统架构质量和可维护性",
                        involved_components=["微内核", "插件系统"],
                        affected_quality_attributes=["maintainability", "modularity"],
                        detection_confidence=0.85,
                        evidence=[f"检测到模式: {pattern_info['pattern']}"],
                        resolution_strategies=["重构插件依赖关系", "优化内核设计"],
                        document_sources=[doc_id]
                    )
                    contradictions.append(contradiction)
        
        return contradictions
    
    def _matches_pattern(self, content: str, pattern: str) -> bool:
        """检查内容是否匹配矛盾模式"""
        import re
        return bool(re.search(pattern, content, re.IGNORECASE))
    
    def get_rule_priority(self) -> int:
        return 90


class ArchitecturalContradictionReasoningEngine:
    """架构级矛盾推理引擎"""
    
    def __init__(self):
        self.reasoning_rules: List[ArchitecturalReasoningRule] = [
            MicrokernelPatternReasoningRule()
        ]
        self.reasoning_rules.sort(key=lambda rule: rule.get_rule_priority(), reverse=True)
    
    async def detect_architectural_contradictions(self, architectural_context: Dict[str, Any]) -> List[ArchitecturalContradiction]:
        """检测架构矛盾"""
        all_contradictions = []
        
        for rule in self.reasoning_rules:
            try:
                rule_contradictions = await rule.detect_contradiction(architectural_context)
                all_contradictions.extend(rule_contradictions)
                logger.info(f"规则 {rule.__class__.__name__} 检测到 {len(rule_contradictions)} 个矛盾")
            except Exception as e:
                logger.error(f"推理规则执行失败 {rule.__class__.__name__}: {e}")
                continue
        
        all_contradictions.sort(key=lambda c: (c.severity.value, -c.detection_confidence))
        return all_contradictions
    
    async def generate_resolution_report(self, contradictions: List[ArchitecturalContradiction]) -> Dict[str, Any]:
        """生成解决方案报告"""
        return {
            "total_contradictions": len(contradictions),
            "severity_distribution": self._calculate_severity_distribution(contradictions),
            "resolution_recommendations": self._generate_recommendations(contradictions)
        }
    
    def _calculate_severity_distribution(self, contradictions: List[ArchitecturalContradiction]) -> Dict[str, int]:
        """计算严重程度分布"""
        distribution = {}
        for severity in ContradictionSeverity:
            count = sum(1 for c in contradictions if c.severity == severity)
            distribution[severity.value] = count
        return distribution
    
    def _generate_recommendations(self, contradictions: List[ArchitecturalContradiction]) -> List[str]:
        """生成建议"""
        recommendations = []
        if any(c.contradiction_type == ArchitecturalContradictionType.PATTERN_CONFLICT for c in contradictions):
            recommendations.append("建议建立清晰的架构设计模式规范")
        return recommendations


# 使用示例
async def main():
    """使用示例"""
    engine = ArchitecturalContradictionReasoningEngine()
    
    architectural_context = {
        "documents": [
            {
                "doc_id": "arch_001",
                "content": "微内核架构设计：插件A直接依赖插件B的内部实现，内核包含业务逻辑处理"
            }
        ]
    }
    
    contradictions = await engine.detect_architectural_contradictions(architectural_context)
    print(f"检测到 {len(contradictions)} 个架构矛盾")
    
    for contradiction in contradictions:
        print(f"- {contradiction.description} (严重程度: {contradiction.severity.value})")


if __name__ == "__main__":
    asyncio.run(main())