# Parameter Management Pattern

**Authority Source**: docs/common/architecture/patterns/parameter-management-pattern.md  
**Version**: 2.0 (Dynamic Parameter Architecture)  
**Last Updated**: 2025-01-15  
**Status**: Active Pattern

## Pattern Overview

The Parameter Management Pattern defines a standardized approach for handling configuration parameters across XKongCloud applications using annotation-driven dynamic parameter management with runtime tracking and validation.

## Problem Statement

Traditional parameter management approaches suffer from:

- **Hardcoded validation logic**: Each configuration class implements custom validation
- **Scattered parameter handling**: No centralized approach for parameter management
- **Limited runtime visibility**: Difficult to track which parameters are actually used
- **Maintenance overhead**: Adding new parameters requires boilerplate validation code
- **Testing complexity**: Hardcoded validation logic is difficult to mock and test

## Solution Architecture

### Core Components

```mermaid
graph TD
    A[Configuration Class] --> B[@Parameter Annotations]
    A --> C[@RequiredParameters]
    A --> D[DynamicParameterAnalyzer]
    A --> E[ParameterUsageTracker]
    
    D --> F[Parameter Validation]
    D --> G[Parameter Discovery]
    D --> H[Metadata Management]
    
    E --> I[Usage Tracking]
    E --> J[Statistics Collection]
    E --> K[Cleanup Identification]
    
    B --> L[Runtime Parameter Injection]
    C --> M[Required Parameter Validation]
```

### Pattern Structure

```java
// 1. Annotation-based parameter declaration
@Component
@RequiredParameters({"component.url", "component.timeout"})
public class ComponentConfiguration {
    
    // 2. Parameter field annotations
    @Parameter("component.url")
    private String componentUrl;
    
    @Parameter("component.timeout")
    private Integer timeoutSeconds;
    
    // 3. Dynamic analyzer dependency
    private final DynamicParameterAnalyzer analyzer;
    private final ParameterUsageTracker tracker;
    
    // 4. Constructor injection
    @Autowired
    public ComponentConfiguration(
            DynamicParameterAnalyzer analyzer,
            ParameterUsageTracker tracker) {
        this.analyzer = analyzer;
        this.tracker = tracker;
    }
    
    // 5. Dynamic validation
    @PostConstruct
    public void validate() {
        analyzer.validateRequiredParameters(this.getClass());
    }
    
    // 6. Tracked parameter access
    public String getComponentUrl() {
        tracker.trackParameterAccess(this.getClass(), "component.url");
        return componentUrl;
    }
}
```

## Pattern Implementation

### 1. Parameter Declaration Layer

**Annotation-Based Declaration**:
```java
@Parameter("database.connection.url")
private String databaseUrl;

@Parameter("database.connection.pool.max-size")
private Integer maxPoolSize;

@Parameter("database.connection.pool.min-size")
private Integer minPoolSize;
```

**Required Parameters Declaration**:
```java
@RequiredParameters({
    "database.connection.url",
    "database.username",
    "database.password"
})
```

### 2. Dynamic Validation Layer

**Centralized Validation**:
```java
@PostConstruct
public void validateConfiguration() {
    // Validate all required parameters
    analyzer.validateRequiredParameters(this.getClass());
    
    // Validate specific parameter constraints
    analyzer.validateParameterConstraints(this.getClass());
    
    // Perform custom business validation if needed
    validateBusinessRules();
}
```

**Runtime Validation**:
```java
public void revalidateConfiguration() {
    analyzer.validateAllParameters(this.getClass());
}
```

### 3. Usage Tracking Layer

**Parameter Access Tracking**:
```java
public String getDatabaseUrl() {
    tracker.trackParameterAccess(this.getClass(), "database.connection.url");
    return databaseUrl;
}

public Integer getMaxPoolSize() {
    tracker.trackParameterAccess(this.getClass(), "database.connection.pool.max-size");
    return maxPoolSize;
}
```

**Usage Statistics**:
```java
public ParameterUsageReport getUsageReport() {
    return tracker.generateUsageReport(this.getClass());
}
```

### 4. Discovery and Metadata Layer

**Parameter Discovery**:
```java
List<ParameterInfo> parameters = analyzer.discoverParameters(ConfigurationClass.class);
```

**Metadata Access**:
```java
ParameterMetadata metadata = analyzer.getParameterMetadata("database.connection.url");
```

## Pattern Variants

### 1. Simple Configuration Pattern

For basic configuration classes with minimal parameters:

```java
@Component
@RequiredParameters({"service.endpoint"})
public class SimpleServiceConfiguration {
    
    @Parameter("service.endpoint")
    private String endpoint;
    
    @Parameter("service.timeout")
    private Integer timeout;
    
    // Standard implementation...
}
```

### 2. Hierarchical Configuration Pattern

For complex configurations with parameter hierarchies:

```java
@Component
@RequiredParameters({
    "database.primary.url",
    "database.primary.username"
})
public class DatabaseConfiguration {
    
    // Primary database parameters
    @Parameter("database.primary.url")
    private String primaryUrl;
    
    @Parameter("database.primary.username")
    private String primaryUsername;
    
    @Parameter("database.primary.password")
    private String primaryPassword;
    
    // Secondary database parameters
    @Parameter("database.secondary.url")
    private String secondaryUrl;
    
    @Parameter("database.secondary.username")
    private String secondaryUsername;
    
    // Connection pool parameters
    @Parameter("database.pool.max-size")
    private Integer maxPoolSize;
    
    @Parameter("database.pool.min-size")
    private Integer minPoolSize;
    
    // Standard implementation...
}
```

### 3. Conditional Configuration Pattern

For configurations with conditional parameter requirements:

```java
@Component
@RequiredParameters({"feature.enabled"})
public class FeatureConfiguration {
    
    @Parameter("feature.enabled")
    private Boolean featureEnabled;
    
    @Parameter("feature.mode")
    private String featureMode;
    
    @Parameter("feature.cache.enabled")
    private Boolean cacheEnabled;
    
    @Parameter("feature.cache.size")
    private Integer cacheSize;
    
    @PostConstruct
    public void validate() {
        analyzer.validateRequiredParameters(this.getClass());
        
        // Conditional validation
        if (featureEnabled) {
            analyzer.validateParameter("feature.mode");
            
            if (cacheEnabled != null && cacheEnabled) {
                analyzer.validateParameter("feature.cache.size");
            }
        }
    }
}
```

## Pattern Benefits

### 1. Consistency and Standardization

- **Uniform approach**: All configuration classes follow the same pattern
- **Centralized validation**: Validation logic is centralized and reusable
- **Standard annotations**: Consistent annotation usage across the application
- **Predictable structure**: Developers know what to expect in configuration classes

### 2. Runtime Visibility and Monitoring

- **Usage tracking**: Monitor which parameters are actually used
- **Performance insights**: Identify frequently accessed parameters
- **Cleanup opportunities**: Discover unused parameters for removal
- **Runtime diagnostics**: Get real-time parameter usage statistics

### 3. Maintainability and Flexibility

- **Easy parameter addition**: New parameters require only annotation addition
- **No boilerplate code**: Validation logic is automatically provided
- **Testing support**: Easy to mock and test parameter validation
- **Runtime adaptability**: Parameter validation can be modified without code changes

### 4. Development Efficiency

- **Reduced development time**: No need to write custom validation for each parameter
- **IDE support**: Better IDE integration with annotation-based approach
- **Automatic documentation**: Parameter metadata is automatically discoverable
- **Error prevention**: Centralized validation prevents common parameter handling errors

## Implementation Guidelines

### 1. Parameter Naming

- Use kebab-case: `database.connection.pool.max-size`
- Be hierarchical: `service.security.authentication.enabled`
- Be descriptive: `retry.max-attempts` not `max-attempts`
- Group related parameters: `cache.redis.*`

### 2. Required vs Optional

- Use binary classification: required or not required
- Document requirements clearly in @RequiredParameters
- Provide sensible defaults for optional parameters
- Validate required parameters in @PostConstruct

### 3. Usage Tracking

- Track all parameter access through getter methods
- Ensure parameters have real usage in application logic
- Avoid fake usage just for tracking purposes
- Use tracking data for optimization decisions

### 4. Validation Strategy

- Use DynamicParameterAnalyzer for all validation
- Perform validation early in @PostConstruct
- Handle validation errors gracefully
- Support runtime validation for dynamic changes

## Testing Strategies

### 1. Unit Testing

```java
@Test
public void testRequiredParameterValidation() {
    assertThrows(ParameterValidationException.class, () -> {
        analyzer.validateRequiredParameters(TestConfiguration.class);
    });
}

@Test
public void testParameterUsageTracking() {
    configuration.getServiceUrl();
    verify(tracker).trackParameterAccess(TestConfiguration.class, "service.url");
}
```

### 2. Integration Testing

```java
@SpringBootTest
public class ConfigurationPatternIntegrationTest {
    
    @Test
    public void testConfigurationPatternWorks() {
        assertNotNull(configuration.getServiceUrl());
        assertTrue(configuration.isValid());
    }
}
```

## Migration Path

### From Hardcoded Validation

1. **Add annotations**: Add @Parameter and @RequiredParameters annotations
2. **Inject dependencies**: Add DynamicParameterAnalyzer and ParameterUsageTracker
3. **Replace validation**: Replace hardcoded validation with analyzer calls
4. **Add tracking**: Add tracking to parameter getter methods
5. **Remove old code**: Remove hardcoded validation logic

### From Property-Based Configuration

1. **Identify parameters**: List all configuration properties
2. **Add annotations**: Convert properties to @Parameter annotations
3. **Group requirements**: Identify required parameters for @RequiredParameters
4. **Implement pattern**: Follow standard pattern implementation
5. **Test migration**: Verify all parameters work correctly

## Related Patterns

- [Configuration Class Standards](../../best-practices/coding-standards/configuration-class-standards.md)
- [Dynamic Parameter Architecture Guide](../../best-practices/coding-standards/dynamic-parameter-architecture-guide.md)
- [Service Evolution Patterns](./service-evolution-patterns.md)

## Quality Metrics

- **Parameter coverage**: Percentage of parameters using the pattern
- **Usage tracking coverage**: Percentage of parameters with usage tracking
- **Validation consistency**: All configuration classes use dynamic validation
- **Documentation completeness**: All parameters have proper documentation
