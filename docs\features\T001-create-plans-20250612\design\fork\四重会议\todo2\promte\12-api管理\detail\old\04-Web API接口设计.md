# 04-Web API接口设计

## 📋 文档信息

**文档ID**: WEB-API-INTERFACE-DESIGN-V1.0
**基于总设计**: @DRY_REF: API管理核心驱动系统架构.md#TwelveStepIntegrationPreparator
**基于UI-UX设计**: @DRY_REF: API管理器UI-UX设计方案.md
**核心功能**: 支撑UI-UX设计的Web API接口、实时数据服务、智能解析接口
**权威保障**: 功能零损失、性能零退化(≥91.4分)、稳定性优先(100%成功率)

## 🎯 基于UI-UX设计的API接口架构

### 支撑UI-UX设计的API分层

**重要说明**: 本文档专门为现有的UI-UX设计方案提供Web API支撑，确保前后端完美配合。

```yaml
# === 基于UI-UX设计的Web API架构 ===
UX_Driven_Web_API_Architecture:

  # 智能输入解析API层
  smart_input_parsing_api:
    reference: "@DRY_REF: API管理器UI-UX设计方案.md#智能API输入区域"
    base_path: "/api/v1/smart-input"
    purpose: "支撑SmartAPIParser前端组件"
    endpoints:
      - "/parse"          # 智能解析API输入
      - "/validate"       # 实时验证解析结果
      - "/preview"        # 预览解析配置
      - "/suggestions"    # 智能建议和补全

  # 验证流程API层
  validation_flow_api:
    reference: "@DRY_REF: API管理器UI-UX设计方案.md#验证流程界面设计"
    base_path: "/api/v1/validation"
    purpose: "支撑4阶段验证流程界面"
    endpoints:
      - "/start"          # 启动验证流程
      - "/stage/{stage}"  # 获取特定阶段状态
      - "/progress"       # 实时验证进度
      - "/results"        # 验证结果详情

  # API状态总览API层
  status_overview_api:
    reference: "@DRY_REF: API管理器UI-UX设计方案.md#API状态总览设计"
    base_path: "/api/v1/status-overview"
    purpose: "支撑API状态总览界面"
    endpoints:
      - "/summary"        # 状态摘要统计
      - "/matrix"         # 能力矩阵数据
      - "/cleanup"        # 清理建议
      - "/performance"    # 性能概览

  # API列表管理API层
  list_management_api:
    reference: "@DRY_REF: API管理器UI-UX设计方案.md#API列表管理界面"
    base_path: "/api/v1/list-management"
    purpose: "支撑API列表管理界面"
    endpoints:
      - "/list"           # API列表数据
      - "/batch"          # 批量操作
      - "/filter"         # 过滤和搜索
      - "/sort"           # 排序和分组

  # 实时监控WebSocket层
  real_time_websocket:
    reference: "@DRY_REF: API管理器UI-UX设计方案.md#实时状态更新"
    protocol: "WebSocket"
    namespace: "/ws/real-time"
    purpose: "支撑实时状态更新"
    events:
      - "status_update"   # 状态变化推送
      - "validation_progress"  # 验证进度推送
      - "alert_notification"   # 告警通知推送
      - "performance_metrics"  # 性能指标推送
```

## 🔧 智能输入解析API实现

### SmartInputParsingController

```python
# === 智能输入解析控制器 ===
# @DRY_REF: API管理核心驱动系统架构.md#APICoreManagementDriveSystem
# @DRY_REF: API管理器UI-UX设计方案.md#SmartAPIParser

from flask import Flask, request, jsonify
from flask_restful import Api, Resource
from api_management.sqlite_storage.api_account_database import APIAccountDatabase

class SmartInputParsingController(Resource):
    """
    智能输入解析控制器

    支撑前端SmartAPIParser组件：
    1. 多格式API输入智能解析
    2. 实时验证和预览
    3. 智能建议和补全
    4. 解析结果标准化
    """

    def __init__(self):
        # @DRY_REF: 复用现有核心组件
        self.api_db = APIAccountDatabase()
        self.core_system = APICoreManagementDriveSystem()

        # 智能解析配置
        self.parsing_config = {
            'supported_formats': ['json', 'model_list', 'mixed', 'simple'],
            'validation_timeout': 30,
            'preview_enabled': True,
            'suggestions_enabled': True
        }

    def post(self):
        """智能解析API输入"""
        try:
            request_data = request.get_json()
            user_input = request_data.get('input', '')
            parsing_mode = request_data.get('mode', 'auto')  # auto, json, model_list, mixed, simple

            if not user_input.strip():
                return {
                    'success': False,
                    'error': '输入内容不能为空'
                }, 400

            # 执行智能解析
            parsing_result = self._execute_smart_parsing(user_input, parsing_mode)

            return {
                'success': True,
                'data': parsing_result,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'SMART_PARSING_ERROR'
            }, 500

    def _execute_smart_parsing(self, user_input: str, parsing_mode: str) -> Dict:
        """执行智能解析"""

        parsing_result = {
            'detected_format': '',
            'parsed_configs': [],
            'validation_status': 'pending',
            'suggestions': [],
            'preview_data': {}
        }

        try:
            # 1. 格式检测
            detected_format = self._detect_input_format(user_input)
            parsing_result['detected_format'] = detected_format

            # 2. 基于格式解析
            if detected_format == 'json':
                parsed_configs = self._parse_json_format(user_input)
            elif detected_format == 'model_list':
                parsed_configs = self._parse_model_list_format(user_input)
            elif detected_format == 'mixed':
                parsed_configs = self._parse_mixed_format(user_input)
            elif detected_format == 'simple':
                parsed_configs = self._parse_simple_format(user_input)
            else:
                parsed_configs = self._parse_fallback_format(user_input)

            parsing_result['parsed_configs'] = parsed_configs

            # 3. 生成预览数据
            preview_data = self._generate_preview_data(parsed_configs)
            parsing_result['preview_data'] = preview_data

            # 4. 生成智能建议
            suggestions = self._generate_parsing_suggestions(parsed_configs, detected_format)
            parsing_result['suggestions'] = suggestions

            # 5. 初步验证
            validation_status = self._perform_initial_validation(parsed_configs)
            parsing_result['validation_status'] = validation_status

        except Exception as e:
            parsing_result['error'] = str(e)

        return parsing_result

class ValidationFlowController(Resource):
    """
    验证流程控制器

    @DRY_REF: API管理器UI-UX设计方案.md#验证流程界面设计
    支撑4阶段验证流程界面：
    1. 基础连通性验证
    2. Token处理能力验证
    3. 业务场景验证
    4. 置信度综合验证
    """

    def __init__(self):
        # @DRY_REF: 复用现有核心组件
        self.api_db = APIAccountDatabase()
        self.thinking_auditor = ThinkingQualityAuditorEnhanced(self.api_db)
        self.devils_advocate = DevilsAdvocateValidatorDrive(self.api_db)
        self.confidence_validator = ConfidenceConvergenceValidator(self.api_db)

        # 验证流程配置
        self.validation_config = {
            'stage_timeout': 120,          # 每阶段2分钟超时
            'real_time_updates': True,     # 实时进度更新
            'detailed_logging': True,      # 详细日志记录
            'auto_next_stage': False       # 手动确认进入下一阶段
        }

        # 活跃验证会话
        self.active_validations = {}

    def post(self, action):
        """执行验证流程操作"""
        try:
            if action == 'start':
                return self._start_validation_flow()
            elif action == 'next-stage':
                return self._proceed_to_next_stage()
            elif action == 'retry-stage':
                return self._retry_current_stage()
            elif action == 'cancel':
                return self._cancel_validation()
            else:
                return {
                    'success': False,
                    'error': f'不支持的验证操作: {action}'
                }, 400

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'VALIDATION_FLOW_ERROR'
            }, 500

    def get(self, validation_id=None):
        """获取验证流程状态"""
        try:
            if validation_id:
                # 获取特定验证会话状态
                validation_status = self._get_validation_status(validation_id)
                return {
                    'success': True,
                    'data': validation_status,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                # 获取所有活跃验证会话
                active_sessions = self._get_active_validation_sessions()
                return {
                    'success': True,
                    'data': active_sessions,
                    'total_count': len(active_sessions),
                    'timestamp': datetime.now().isoformat()
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'VALIDATION_STATUS_ERROR'
            }, 500

    def _start_validation_flow(self) -> Dict:
        """启动验证流程"""

        request_data = request.get_json()
        api_config = request_data.get('api_config', {})

        validation_session = {
            'validation_id': f"validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'start_time': datetime.now().isoformat(),
            'api_config': api_config,
            'current_stage': 1,
            'stage_results': {},
            'overall_status': 'IN_PROGRESS',
            'progress_percentage': 0
        }

        # 注册验证会话
        self.active_validations[validation_session['validation_id']] = validation_session

        # 启动第一阶段验证
        stage_1_result = asyncio.run(self._execute_stage_1_validation(api_config))
        validation_session['stage_results']['stage_1'] = stage_1_result
        validation_session['progress_percentage'] = 25

        if stage_1_result['success']:
            validation_session['stage_status'] = 'STAGE_1_COMPLETED'
        else:
            validation_session['overall_status'] = 'FAILED'
            validation_session['failure_stage'] = 1

        return {
            'success': True,
            'validation_id': validation_session['validation_id'],
            'current_stage': validation_session['current_stage'],
            'stage_result': stage_1_result,
            'progress_percentage': validation_session['progress_percentage']
        }

class StatusOverviewController(Resource):
    """
    状态总览控制器

    @DRY_REF: API管理器UI-UX设计方案.md#API状态总览设计
    支撑API状态总览界面：
    1. 状态摘要统计
    2. 能力矩阵展示
    3. 清理建议生成
    4. 性能概览数据
    """

    def __init__(self):
        # @DRY_REF: 复用现有核心组件
        self.api_db = APIAccountDatabase()
        self.quality_monitor = APIQualityMonitor()
        self.scheduling_engine = UnifiedModelPoolButlerEnhanced()

    def get(self, data_type=None):
        """获取状态总览数据"""
        try:
            if data_type == 'summary':
                # 状态摘要统计
                summary_data = self._get_status_summary()
                return {
                    'success': True,
                    'data': summary_data,
                    'timestamp': datetime.now().isoformat()
                }

            elif data_type == 'matrix':
                # 能力矩阵数据
                matrix_data = self._get_capability_matrix()
                return {
                    'success': True,
                    'data': matrix_data,
                    'timestamp': datetime.now().isoformat()
                }

            elif data_type == 'cleanup':
                # 清理建议
                cleanup_suggestions = self._get_cleanup_suggestions()
                return {
                    'success': True,
                    'data': cleanup_suggestions,
                    'timestamp': datetime.now().isoformat()
                }

            elif data_type == 'performance':
                # 性能概览
                performance_overview = self._get_performance_overview()
                return {
                    'success': True,
                    'data': performance_overview,
                    'timestamp': datetime.now().isoformat()
                }

            else:
                # 完整状态总览
                complete_overview = self._get_complete_status_overview()
                return {
                    'success': True,
                    'data': complete_overview,
                    'timestamp': datetime.now().isoformat()
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'STATUS_OVERVIEW_ERROR'
            }, 500

class ListManagementController(Resource):
    """
    API列表管理控制器

    @DRY_REF: API管理器UI-UX设计方案.md#API列表管理界面
    支撑API列表管理界面：
    1. API列表数据获取
    2. 批量操作处理
    3. 过滤和搜索
    4. 排序和分组
    """

    def __init__(self):
        # @DRY_REF: 复用现有核心组件
        self.api_db = APIAccountDatabase()
        self.core_system = APICoreManagementDriveSystem()

        # 列表管理配置
        self.list_config = {
            'page_size': 20,               # 分页大小
            'max_batch_size': 50,          # 最大批量操作数
            'supported_filters': ['status', 'type', 'quality', 'last_used'],
            'supported_sorts': ['name', 'status', 'quality_score', 'created_time']
        }

    def get(self):
        """获取API列表数据"""
        try:
            # 获取查询参数
            page = request.args.get('page', 1, type=int)
            page_size = request.args.get('page_size', self.list_config['page_size'], type=int)
            filter_params = request.args.get('filters', '{}')
            sort_params = request.args.get('sort', 'name')
            search_query = request.args.get('search', '')

            # 解析过滤参数
            try:
                filters = json.loads(filter_params) if filter_params else {}
            except json.JSONDecodeError:
                filters = {}

            # 获取API列表数据
            list_data = self._get_api_list_data(page, page_size, filters, sort_params, search_query)

            return {
                'success': True,
                'data': list_data,
                'pagination': {
                    'current_page': page,
                    'page_size': page_size,
                    'total_items': list_data['total_count'],
                    'total_pages': (list_data['total_count'] + page_size - 1) // page_size
                },
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'LIST_RETRIEVAL_ERROR'
            }, 500

    def post(self):
        """执行批量操作"""
        try:
            request_data = request.get_json()
            operation_type = request_data.get('operation_type')
            target_apis = request_data.get('target_apis', [])
            operation_params = request_data.get('params', {})

            # 验证批量操作
            if len(target_apis) > self.list_config['max_batch_size']:
                return {
                    'success': False,
                    'error': f'批量操作超出限制: {len(target_apis)} > {self.list_config["max_batch_size"]}'
                }, 400

            # 执行批量操作
            if operation_type == 'delete':
                operation_result = self._execute_batch_delete(target_apis)
            elif operation_type == 'validate':
                operation_result = self._execute_batch_validate(target_apis)
            elif operation_type == 'update_status':
                operation_result = self._execute_batch_update_status(target_apis, operation_params)
            elif operation_type == 'export':
                operation_result = self._execute_batch_export(target_apis, operation_params)
            else:
                return {
                    'success': False,
                    'error': f'不支持的批量操作类型: {operation_type}'
                }, 400

            return {
                'success': True,
                'operation_type': operation_type,
                'operation_result': operation_result,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'BATCH_OPERATION_ERROR'
            }, 500
```

## 📡 实时WebSocket支持

### RealTimeWebSocketHandler

```python
# === 实时WebSocket处理器 ===
# @DRY_REF: API管理器UI-UX设计方案.md#实时状态更新

from flask_socketio import SocketIO, emit, join_room, leave_room
import asyncio

class RealTimeWebSocketHandler:
    """
    实时WebSocket处理器

    支撑UI-UX设计的实时更新需求：
    1. 状态变化实时推送
    2. 验证进度实时更新
    3. 告警通知实时推送
    4. 性能指标实时推送
    """

    def __init__(self, socketio: SocketIO):
        self.socketio = socketio
        self.api_db = APIAccountDatabase()
        self.quality_monitor = APIQualityMonitor()

        # WebSocket配置
        self.ws_config = {
            'update_interval': 5,          # 5秒更新间隔
            'max_connections': 100,        # 最大连接数
            'heartbeat_interval': 30,      # 30秒心跳
            'reconnect_attempts': 3        # 重连尝试次数
        }

        # 注册WebSocket事件
        self._register_websocket_events()

    def _register_websocket_events(self):
        """注册WebSocket事件处理器"""

        @self.socketio.on('connect', namespace='/ws/real-time')
        def handle_connect():
            """处理客户端连接"""
            client_id = request.sid
            print(f'客户端连接: {client_id}')

            # 发送初始状态数据
            initial_data = self._get_initial_status_data()
            emit('initial_data', {
                'type': 'initial_status',
                'data': initial_data,
                'timestamp': datetime.now().isoformat()
            })

        @self.socketio.on('disconnect', namespace='/ws/real-time')
        def handle_disconnect():
            """处理客户端断开连接"""
            client_id = request.sid
            print(f'客户端断开连接: {client_id}')

        @self.socketio.on('subscribe', namespace='/ws/real-time')
        def handle_subscribe(data):
            """处理订阅请求"""
            subscription_type = data.get('type', 'all')
            client_id = request.sid

            # 加入对应的房间
            if subscription_type == 'status_updates':
                join_room('status_room')
            elif subscription_type == 'validation_progress':
                join_room('validation_room')
            elif subscription_type == 'alerts':
                join_room('alert_room')
            elif subscription_type == 'performance':
                join_room('performance_room')
            else:
                # 订阅所有更新
                join_room('status_room')
                join_room('validation_room')
                join_room('alert_room')
                join_room('performance_room')

            emit('subscription_confirmed', {
                'success': True,
                'subscription_type': subscription_type,
                'client_id': client_id
            })

        @self.socketio.on('unsubscribe', namespace='/ws/real-time')
        def handle_unsubscribe(data):
            """处理取消订阅请求"""
            subscription_type = data.get('type', 'all')
            client_id = request.sid

            # 离开对应的房间
            if subscription_type == 'status_updates':
                leave_room('status_room')
            elif subscription_type == 'validation_progress':
                leave_room('validation_room')
            elif subscription_type == 'alerts':
                leave_room('alert_room')
            elif subscription_type == 'performance':
                leave_room('performance_room')
            else:
                # 取消所有订阅
                leave_room('status_room')
                leave_room('validation_room')
                leave_room('alert_room')
                leave_room('performance_room')

            emit('unsubscription_confirmed', {
                'success': True,
                'subscription_type': subscription_type,
                'client_id': client_id
            })

    def broadcast_status_update(self, status_data: Dict):
        """广播状态更新"""
        self.socketio.emit('status_update', {
            'type': 'status_change',
            'data': status_data,
            'timestamp': datetime.now().isoformat()
        }, room='status_room', namespace='/ws/real-time')

    def broadcast_validation_progress(self, validation_data: Dict):
        """广播验证进度更新"""
        self.socketio.emit('validation_progress', {
            'type': 'validation_update',
            'data': validation_data,
            'timestamp': datetime.now().isoformat()
        }, room='validation_room', namespace='/ws/real-time')

    def broadcast_alert_notification(self, alert_data: Dict):
        """广播告警通知"""
        self.socketio.emit('alert_notification', {
            'type': 'new_alert',
            'data': alert_data,
            'timestamp': datetime.now().isoformat()
        }, room='alert_room', namespace='/ws/real-time')

    def broadcast_performance_metrics(self, performance_data: Dict):
        """广播性能指标更新"""
        self.socketio.emit('performance_metrics', {
            'type': 'performance_update',
            'data': performance_data,
            'timestamp': datetime.now().isoformat()
        }, room='performance_room', namespace='/ws/real-time')
```

## 🚀 Flask应用配置

### UXDrivenAPIApplication

```python
# === 基于UI-UX设计的Flask应用配置 ===

from flask import Flask
from flask_restful import Api
from flask_socketio import SocketIO
from flask_cors import CORS

def create_ux_driven_api_app():
    """创建支撑UI-UX设计的API应用"""

    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'api-management-ux-driven-key'

    # 启用CORS（支持前端UI-UX组件）
    CORS(app, origins=['http://localhost:3000', 'http://localhost:8080', 'http://localhost:5173'])

    # 创建REST API
    api = Api(app)

    # 创建SocketIO（支持实时更新）
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

    # 注册基于UI-UX设计的API路由
    _register_ux_driven_api_routes(api)

    # 注册实时WebSocket事件
    _register_real_time_websocket_events(socketio)

    return app, socketio

def _register_ux_driven_api_routes(api: Api):
    """注册支撑UI-UX设计的API路由"""

    # 智能输入解析API（支撑SmartAPIParser）
    api.add_resource(SmartInputParsingController,
                    '/api/v1/smart-input/parse',
                    '/api/v1/smart-input/validate',
                    '/api/v1/smart-input/preview',
                    '/api/v1/smart-input/suggestions')

    # 验证流程API（支撑4阶段验证流程界面）
    api.add_resource(ValidationFlowController,
                    '/api/v1/validation/<string:action>',
                    '/api/v1/validation/status/<string:validation_id>',
                    '/api/v1/validation/progress')

    # 状态总览API（支撑API状态总览界面）
    api.add_resource(StatusOverviewController,
                    '/api/v1/status-overview',
                    '/api/v1/status-overview/<string:data_type>')

    # 列表管理API（支撑API列表管理界面）
    api.add_resource(ListManagementController,
                    '/api/v1/list-management/list',
                    '/api/v1/list-management/batch',
                    '/api/v1/list-management/filter',
                    '/api/v1/list-management/sort')

def _register_real_time_websocket_events(socketio: SocketIO):
    """注册实时WebSocket事件"""

    # 创建实时WebSocket处理器
    ws_handler = RealTimeWebSocketHandler(socketio)

    # WebSocket事件已在RealTimeWebSocketHandler中注册
    print("实时WebSocket事件已注册")

# API端点文档
API_ENDPOINTS_DOCUMENTATION = {
    "智能输入解析API": {
        "base_path": "/api/v1/smart-input",
        "purpose": "支撑SmartAPIParser前端组件",
        "endpoints": {
            "POST /parse": "智能解析API输入",
            "POST /validate": "实时验证解析结果",
            "POST /preview": "预览解析配置",
            "GET /suggestions": "获取智能建议"
        }
    },
    "验证流程API": {
        "base_path": "/api/v1/validation",
        "purpose": "支撑4阶段验证流程界面",
        "endpoints": {
            "POST /start": "启动验证流程",
            "POST /next-stage": "进入下一阶段",
            "GET /status/{id}": "获取验证状态",
            "GET /progress": "获取验证进度"
        }
    },
    "状态总览API": {
        "base_path": "/api/v1/status-overview",
        "purpose": "支撑API状态总览界面",
        "endpoints": {
            "GET /summary": "状态摘要统计",
            "GET /matrix": "能力矩阵数据",
            "GET /cleanup": "清理建议",
            "GET /performance": "性能概览"
        }
    },
    "列表管理API": {
        "base_path": "/api/v1/list-management",
        "purpose": "支撑API列表管理界面",
        "endpoints": {
            "GET /list": "API列表数据",
            "POST /batch": "批量操作",
            "GET /filter": "过滤和搜索",
            "GET /sort": "排序和分组"
        }
    },
    "实时WebSocket": {
        "namespace": "/ws/real-time",
        "purpose": "支撑实时状态更新",
        "events": {
            "status_update": "状态变化推送",
            "validation_progress": "验证进度推送",
            "alert_notification": "告警通知推送",
            "performance_metrics": "性能指标推送"
        }
    }
}

# 启动应用
if __name__ == '__main__':
    app, socketio = create_ux_driven_api_app()
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
```

## 📋 实施要求

### 基于UI-UX设计的API实施原则

1. **完美支撑前端UI-UX** - 每个API接口都精确对应UI-UX设计方案中的组件需求
2. **实时数据驱动** - 通过WebSocket提供5秒实时更新，确保UI界面数据的及时性
3. **智能解析支撑** - 为SmartAPIParser提供强大的后端解析和验证能力
4. **验证流程控制** - 精确支撑4阶段验证流程的进度控制和状态管理
5. **权威基准保障** - 确保功能零损失、性能零退化(≥91.4分)、稳定性优先(100%成功率)

### 与UI-UX设计的完美配合

#### 1. 智能输入解析配合
- **前端组件**: SmartAPIParser（支持多格式输入）
- **后端API**: `/api/v1/smart-input/parse`（智能解析）
- **配合方式**: 前端输入 → 后端解析 → 实时预览 → 智能建议

#### 2. 验证流程界面配合
- **前端组件**: 4阶段验证流程界面
- **后端API**: `/api/v1/validation/*`（流程控制）
- **配合方式**: 阶段控制 → 进度推送 → 结果展示 → 状态同步

#### 3. 状态总览界面配合
- **前端组件**: API状态总览设计
- **后端API**: `/api/v1/status-overview/*`（状态数据）
- **配合方式**: 状态统计 → 能力矩阵 → 清理建议 → 性能概览

#### 4. 列表管理界面配合
- **前端组件**: API列表管理界面
- **后端API**: `/api/v1/list-management/*`（列表操作）
- **配合方式**: 列表展示 → 批量操作 → 过滤搜索 → 排序分组

#### 5. 实时更新配合
- **前端需求**: 实时状态更新
- **后端支撑**: WebSocket `/ws/real-time`
- **配合方式**: 状态推送 → 进度更新 → 告警通知 → 性能指标

## 🎯 总结

本文档完全基于现有的UI-UX设计方案，提供了精确的Web API支撑：

1. **设计驱动开发** - 严格按照UI-UX设计方案设计API接口
2. **前后端完美配合** - 每个前端组件都有对应的后端API支撑
3. **实时数据支撑** - WebSocket实时推送，确保UI界面数据的及时性
4. **智能化增强** - 智能解析、智能建议、智能推荐等功能
5. **权威基准保障** - 功能零损失、性能零退化(≥91.4分)、稳定性优先(100%成功率)

通过这套Web API接口设计，确保了前端UI-UX设计方案能够得到强大的后端支撑，实现了完整的API管理系统前后端一体化解决方案。
```
    
    def post(self):
        """注册新API"""
        try:
            api_config = request.get_json()
            
            # 验证API配置
            validation_result = self._validate_api_config(api_config)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': 'API配置验证失败',
                    'validation_errors': validation_result['errors']
                }, 400
            
            # 执行API注册流程
            registration_result = await self.core_system.drive_complete_api_management_lifecycle(api_config)
            
            if registration_result['overall_qualification']:
                return {
                    'success': True,
                    'message': 'API注册成功',
                    'api_id': registration_result['api_key'],
                    'qualification_details': registration_result,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'message': 'API注册失败，未通过资格验证',
                    'qualification_details': registration_result,
                    'timestamp': datetime.now().isoformat()
                }, 422
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'API_REGISTRATION_ERROR'
            }, 500
    
    def put(self, api_id):
        """更新API配置"""
        try:
            api_config = request.get_json()
            api_config['api_key'] = api_id
            
            # 验证更新权限
            if not self._verify_update_permission(api_id):
                return {
                    'success': False,
                    'error': '无权限更新此API配置'
                }, 403
            
            # 执行配置更新
            update_result = await self._update_api_configuration(api_id, api_config)
            
            return {
                'success': True,
                'message': 'API配置更新成功',
                'update_details': update_result,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'API_UPDATE_ERROR'
            }, 500
    
    def delete(self, api_id):
        """删除API"""
        try:
            # 验证删除权限
            if not self._verify_delete_permission(api_id):
                return {
                    'success': False,
                    'error': '无权限删除此API'
                }, 403
            
            # 检查API是否正在使用
            usage_status = self._check_api_usage_status(api_id)
            if usage_status['in_use']:
                return {
                    'success': False,
                    'error': 'API正在使用中，无法删除',
                    'usage_details': usage_status
                }, 409
            
            # 执行API删除
            deletion_result = await self._delete_api_safely(api_id)
            
            return {
                'success': True,
                'message': 'API删除成功',
                'deletion_details': deletion_result,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'API_DELETION_ERROR'
            }, 500

class QualityMonitoringController(Resource):
    """质量监控控制器"""
    
    def __init__(self):
        self.quality_monitor = APIQualityMonitor()
        self.thinking_auditor = ThinkingQualityAuditorEnhanced()
        self.devils_advocate = DevilsAdvocateValidatorDrive()
    
    def get(self, metric_type=None):
        """获取质量监控数据"""
        try:
            if metric_type == 'thinking':
                # 获取thinking质量数据
                thinking_metrics = self._get_thinking_quality_metrics()
                return {
                    'success': True,
                    'metric_type': 'thinking_quality',
                    'data': thinking_metrics,
                    'baseline_score': 91.4,
                    'timestamp': datetime.now().isoformat()
                }
                
            elif metric_type == 'devils-advocate':
                # 获取魔鬼审问者质量数据
                devils_metrics = self._get_devils_advocate_metrics()
                return {
                    'success': True,
                    'metric_type': 'devils_advocate',
                    'data': devils_metrics,
                    'minimum_requirements': {
                        'counterexamples': 3,
                        'recursion_depth': 5
                    },
                    'timestamp': datetime.now().isoformat()
                }
                
            elif metric_type == 'confidence':
                # 获取置信度收敛数据
                confidence_metrics = self._get_confidence_convergence_metrics()
                return {
                    'success': True,
                    'metric_type': 'confidence_convergence',
                    'data': confidence_metrics,
                    'target_convergence': 0.98,
                    'improvement_requirement': 0.38,
                    'timestamp': datetime.now().isoformat()
                }
                
            else:
                # 获取综合质量概览
                quality_overview = self._get_quality_overview()
                return {
                    'success': True,
                    'metric_type': 'overview',
                    'data': quality_overview,
                    'authority_baselines': {
                        'functionality_completeness': 1.0,
                        'performance_baseline': 91.4,
                        'stability_baseline': 1.0
                    },
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'QUALITY_MONITORING_ERROR'
            }, 500

class SchedulingController(Resource):
    """调度管理控制器"""
    
    def __init__(self):
        self.scheduling_engine = UnifiedModelPoolButlerEnhanced()
        self.load_balancer = DynamicLoadBalancer()
        self.failover_manager = EnhancedFailoverManager()
    
    def get(self, action=None):
        """获取调度信息"""
        try:
            if action == 'status':
                # 获取当前调度状态
                scheduling_status = self._get_current_scheduling_status()
                return {
                    'success': True,
                    'data': scheduling_status,
                    'timestamp': datetime.now().isoformat()
                }
                
            elif action == 'performance':
                # 获取调度性能指标
                performance_metrics = self._get_scheduling_performance_metrics()
                return {
                    'success': True,
                    'data': performance_metrics,
                    'timestamp': datetime.now().isoformat()
                }
                
            elif action == 'load-balance':
                # 获取负载均衡状态
                load_balance_status = self._get_load_balance_status()
                return {
                    'success': True,
                    'data': load_balance_status,
                    'timestamp': datetime.now().isoformat()
                }
                
            else:
                # 获取调度概览
                scheduling_overview = self._get_scheduling_overview()
                return {
                    'success': True,
                    'data': scheduling_overview,
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'SCHEDULING_RETRIEVAL_ERROR'
            }, 500
    
    def post(self, action):
        """执行调度操作"""
        try:
            if action == 'optimize':
                # 执行调度优化
                optimization_request = request.get_json()
                optimization_result = await self._execute_scheduling_optimization(optimization_request)
                
                return {
                    'success': True,
                    'message': '调度优化执行成功',
                    'optimization_result': optimization_result,
                    'timestamp': datetime.now().isoformat()
                }
                
            elif action == 'rebalance':
                # 执行负载重新均衡
                rebalance_result = await self._execute_load_rebalancing()
                
                return {
                    'success': True,
                    'message': '负载重新均衡执行成功',
                    'rebalance_result': rebalance_result,
                    'timestamp': datetime.now().isoformat()
                }
                
            elif action == 'failover':
                # 执行手动故障转移
                failover_request = request.get_json()
                failover_result = await self._execute_manual_failover(failover_request)
                
                return {
                    'success': True,
                    'message': '手动故障转移执行成功',
                    'failover_result': failover_result,
                    'timestamp': datetime.now().isoformat()
                }
                
            else:
                return {
                    'success': False,
                    'error': f'不支持的调度操作: {action}'
                }, 400
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'SCHEDULING_OPERATION_ERROR'
            }, 500
```

## 📊 实时监控API实现

### RealTimeMonitoringController

```python
# === 实时监控控制器 ===
# @DRY_REF: API管理核心驱动系统架构.md#APIStatusTracker

from flask_socketio import SocketIO, emit
import asyncio

class RealTimeMonitoringController:
    """
    实时监控控制器
    
    提供WebSocket实时数据推送：
    1. API状态实时更新
    2. 性能指标实时监控
    3. 告警信息实时推送
    4. 日志流实时传输
    """
    
    def __init__(self, socketio: SocketIO):
        self.socketio = socketio
        self.api_tracker = APIStatusTracker()
        self.performance_monitor = SchedulingPerformanceMonitor()
        self.alert_manager = AlertManager()
        
        # 实时监控配置
        self.monitoring_config = {
            'status_update_interval': 5,      # 5秒更新一次状态
            'metrics_update_interval': 10,    # 10秒更新一次指标
            'log_stream_buffer_size': 100,    # 日志流缓冲区大小
            'alert_immediate_push': True      # 告警立即推送
        }
        
        # 启动实时监控任务
        self._start_monitoring_tasks()
    
    def _start_monitoring_tasks(self):
        """启动实时监控任务"""
        
        # 启动状态监控任务
        asyncio.create_task(self._status_monitoring_task())
        
        # 启动性能监控任务
        asyncio.create_task(self._performance_monitoring_task())
        
        # 启动告警监控任务
        asyncio.create_task(self._alert_monitoring_task())
    
    async def _status_monitoring_task(self):
        """状态监控任务"""
        
        while True:
            try:
                # 获取最新状态数据
                status_data = await self._collect_real_time_status()
                
                # 推送状态更新
                self.socketio.emit('status_update', {
                    'type': 'api_status',
                    'data': status_data,
                    'timestamp': datetime.now().isoformat()
                }, namespace='/monitoring')
                
                # 等待下次更新
                await asyncio.sleep(self.monitoring_config['status_update_interval'])
                
            except Exception as e:
                logger.error(f"状态监控任务错误: {e}")
                await asyncio.sleep(5)  # 错误时等待5秒后重试
    
    async def _performance_monitoring_task(self):
        """性能监控任务"""
        
        while True:
            try:
                # 收集性能指标
                performance_data = await self._collect_performance_metrics()
                
                # 推送性能更新
                self.socketio.emit('metrics_update', {
                    'type': 'performance_metrics',
                    'data': performance_data,
                    'timestamp': datetime.now().isoformat()
                }, namespace='/monitoring')
                
                # 等待下次更新
                await asyncio.sleep(self.monitoring_config['metrics_update_interval'])
                
            except Exception as e:
                logger.error(f"性能监控任务错误: {e}")
                await asyncio.sleep(10)  # 错误时等待10秒后重试
    
    async def _collect_real_time_status(self) -> Dict:
        """收集实时状态数据"""
        
        status_data = {
            'api_status': {},
            'scheduling_status': {},
            'system_health': {},
            'active_connections': 0
        }
        
        # 1. 收集API状态
        all_apis = await self._get_all_monitored_apis()
        for api_key in all_apis:
            api_status = await self.api_tracker.get_real_time_status(api_key)
            status_data['api_status'][api_key] = {
                'status': api_status.get('status', 'UNKNOWN'),
                'response_time': api_status.get('avg_response_time', 0),
                'success_rate': api_status.get('success_rate', 0),
                'current_load': api_status.get('current_load', 0),
                'last_check': api_status.get('last_check', '')
            }
        
        # 2. 收集调度状态
        scheduling_status = await self._get_current_scheduling_status()
        status_data['scheduling_status'] = {
            'current_primary_api': scheduling_status.get('primary_api', ''),
            'current_secondary_api': scheduling_status.get('secondary_api', ''),
            'active_failovers': scheduling_status.get('active_failovers', 0),
            'load_balance_score': scheduling_status.get('load_balance_score', 0)
        }
        
        # 3. 收集系统健康状态
        system_health = await self._assess_system_health()
        status_data['system_health'] = {
            'overall_health': system_health.get('overall_score', 0),
            'critical_issues': system_health.get('critical_issues', 0),
            'warning_issues': system_health.get('warning_issues', 0),
            'system_uptime': system_health.get('uptime', '')
        }
        
        # 4. 活跃连接数
        status_data['active_connections'] = len(self.socketio.server.manager.rooms.get('/monitoring', {}))
        
        return status_data
    
    @socketio.on('subscribe_monitoring')
    def handle_monitoring_subscription(self, data):
        """处理监控订阅请求"""
        
        subscription_type = data.get('type', 'all')
        client_id = request.sid
        
        try:
            # 记录订阅
            self._register_monitoring_subscription(client_id, subscription_type)
            
            # 发送初始数据
            if subscription_type in ['all', 'status']:
                initial_status = asyncio.run(self._collect_real_time_status())
                emit('initial_status', {
                    'type': 'initial_status',
                    'data': initial_status,
                    'timestamp': datetime.now().isoformat()
                })
            
            if subscription_type in ['all', 'metrics']:
                initial_metrics = asyncio.run(self._collect_performance_metrics())
                emit('initial_metrics', {
                    'type': 'initial_metrics',
                    'data': initial_metrics,
                    'timestamp': datetime.now().isoformat()
                })
            
            emit('subscription_confirmed', {
                'success': True,
                'subscription_type': subscription_type,
                'client_id': client_id
            })
            
        except Exception as e:
            emit('subscription_error', {
                'success': False,
                'error': str(e),
                'client_id': client_id
            })
```

## 🔗 12步集成API实现

### TwelveStepIntegrationController

```python
# === 12步集成控制器 ===
# @DRY_REF: API管理核心驱动系统架构.md#TwelveStepIntegrationPreparator

class TwelveStepIntegrationController(Resource):
    """
    12步集成控制器
    
    为四重会议系统提供12步集成接口：
    1. 12步准备接口
    2. 12步执行接口
    3. 12步验证接口
    4. 结果获取接口
    """
    
    def __init__(self):
        # @DRY_REF: 复用现有12步集成组件
        self.twelve_step_preparator = TwelveStepIntegrationPreparator()
        self.core_system = APICoreManagementDriveSystem()
        
        # 12步集成配置
        self.integration_config = {
            'thinking_approval_threshold': 0.95,
            'logical_coherence_minimum': 0.90,
            'confidence_target': 0.97,
            'devils_advocate_examples': 3,
            'pressure_test_depth': 5
        }
    
    def post(self, action):
        """执行12步集成操作"""
        try:
            if action == 'prepare':
                return self._handle_twelve_step_preparation()
            elif action == 'execute':
                return self._handle_twelve_step_execution()
            elif action == 'validate':
                return self._handle_twelve_step_validation()
            else:
                return {
                    'success': False,
                    'error': f'不支持的12步操作: {action}'
                }, 400
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'TWELVE_STEP_OPERATION_ERROR'
            }, 500
    
    def _handle_twelve_step_preparation(self):
        """处理12步准备请求"""
        
        preparation_request = request.get_json()
        
        # 验证请求格式
        if not self._validate_twelve_step_request(preparation_request):
            return {
                'success': False,
                'error': '12步请求格式验证失败',
                'required_format': self._get_twelve_step_format_specification()
            }, 400
        
        # 执行12步准备
        preparation_result = asyncio.run(
            self.twelve_step_preparator.prepare_twelve_step_integration(preparation_request)
        )
        
        return {
            'success': True,
            'message': '12步准备完成',
            'preparation_result': preparation_result,
            'integration_readiness': preparation_result.get('integration_readiness', False),
            'timestamp': datetime.now().isoformat()
        }
    
    def _validate_twelve_step_request(self, request_data: Dict) -> bool:
        """验证12步请求格式"""
        
        required_fields = [
            'task_metadata',
            'context_data', 
            'coordination_parameters'
        ]
        
        # 检查必需字段
        for field in required_fields:
            if field not in request_data:
                return False
        
        # 验证数据结构
        task_metadata = request_data.get('task_metadata', {})
        if not isinstance(task_metadata, dict):
            return False
        
        context_data = request_data.get('context_data', {})
        if not isinstance(context_data, dict):
            return False
        
        coordination_parameters = request_data.get('coordination_parameters', {})
        if not isinstance(coordination_parameters, dict):
            return False
        
        return True
    
    def get(self, action):
        """获取12步集成信息"""
        try:
            if action == 'status':
                # 获取12步集成状态
                integration_status = self._get_twelve_step_integration_status()
                return {
                    'success': True,
                    'data': integration_status,
                    'timestamp': datetime.now().isoformat()
                }
                
            elif action == 'results':
                # 获取12步执行结果
                session_id = request.args.get('session_id')
                if not session_id:
                    return {
                        'success': False,
                        'error': '缺少session_id参数'
                    }, 400
                
                execution_results = self._get_twelve_step_results(session_id)
                return {
                    'success': True,
                    'data': execution_results,
                    'timestamp': datetime.now().isoformat()
                }
                
            elif action == 'config':
                # 获取12步配置信息
                return {
                    'success': True,
                    'data': self.integration_config,
                    'format_specification': self._get_twelve_step_format_specification(),
                    'timestamp': datetime.now().isoformat()
                }
                
            else:
                return {
                    'success': False,
                    'error': f'不支持的12步查询操作: {action}'
                }, 400
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'TWELVE_STEP_QUERY_ERROR'
            }, 500
```

## 📋 Web API路由配置

### Flask应用配置

```python
# === Flask Web API应用配置 ===

from flask import Flask
from flask_restful import Api
from flask_socketio import SocketIO
from flask_cors import CORS

def create_api_management_app():
    """创建API管理Web应用"""
    
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'api-management-secret-key'
    
    # 启用CORS
    CORS(app, origins=['http://localhost:3000', 'http://localhost:8080'])
    
    # 创建REST API
    api = Api(app)
    
    # 创建SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*")
    
    # 注册API路由
    _register_api_routes(api)
    
    # 注册WebSocket事件
    _register_websocket_events(socketio)
    
    return app, socketio

def _register_api_routes(api: Api):
    """注册API路由"""
    
    # 核心管理API
    api.add_resource(APIManagementController, 
                    '/api/v1/management/apis',
                    '/api/v1/management/apis/<string:api_id>')
    
    api.add_resource(QualityMonitoringController,
                    '/api/v1/management/quality',
                    '/api/v1/management/quality/<string:metric_type>')
    
    api.add_resource(SchedulingController,
                    '/api/v1/management/scheduling',
                    '/api/v1/management/scheduling/<string:action>')
    
    # 12步集成API
    api.add_resource(TwelveStepIntegrationController,
                    '/api/v1/twelve-step/<string:action>')
    
    # 配置管理API
    api.add_resource(ConfigurationController,
                    '/api/v1/config/<string:config_type>')

def _register_websocket_events(socketio: SocketIO):
    """注册WebSocket事件"""
    
    # 创建实时监控控制器
    monitoring_controller = RealTimeMonitoringController(socketio)
    
    # 注册监控事件处理器
    @socketio.on('connect', namespace='/monitoring')
    def handle_monitoring_connect():
        print(f'客户端连接到监控: {request.sid}')
    
    @socketio.on('disconnect', namespace='/monitoring')
    def handle_monitoring_disconnect():
        print(f'客户端断开监控连接: {request.sid}')

# 启动应用
if __name__ == '__main__':
    app, socketio = create_api_management_app()
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
```

## 📋 实施要求

### 12步系统集成接口

```python
# === Web API的12步集成接口 ===

class TwelveStepAPIEndpoint(Resource):
    """
    12步系统API端点（全局服务）

    @DRY_REF: 01-质量驱动API角色管理架构.md#全局服务直接调用

    注意：Web API接口是全局服务，给用户使用，跟具体项目无关，
    因此可以直接调用API管理器，不需要通过容器。
    """

    def post(self):
        """为12步系统提供API优化服务（全局服务直接调用）"""
        try:
            request_data = request.get_json()

            prompt = request_data.get('prompt', '')
            task_type = request_data.get('task_type', 'problem_solving')
            complexity = request_data.get('complexity', 'medium')

            # 全局服务直接调用API管理器
            from api_management.core import get_ai_service
            ai_service = get_ai_service()

            # 直接调用（全局服务特权）
            result = await ai_service.request_ai_assistance(
                task_description=prompt,
                assistance_type=task_type,
                complexity_level=complexity
            )

            return {
                'success': True,
                'data': result,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'TWELVE_STEP_API_ERROR'
            }, 500

# 注册12步系统端点
def register_twelve_step_endpoints(api: Api):
    """注册12步系统端点"""
    api.add_resource(TwelveStepAPIEndpoint, '/api/v1/twelve-step/optimize')
```

### API接口标准

1. **RESTful设计** - 遵循REST架构原则，统一接口规范
2. **实时监控** - WebSocket实时数据推送，5秒状态更新
3. **12步集成** - 严格JSON结构，支持四重会议系统集成
4. **安全认证** - Bearer Token认证，API访问控制
5. **权威保障** - 功能零损失、性能零退化、稳定性优先

### 下一步文档

- **05-人工管理交互界面** - 用户界面、配置管理、状态监控
- **06-系统集成与部署** - 12步集成、测试验证、生产部署
