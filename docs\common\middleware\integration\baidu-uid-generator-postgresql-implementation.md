---
title: 基于PostgreSQL的百度UID生成器实现方案
document_id: C026
document_type: 设计文档
category: 中间件
scope: 全局
keywords: [百度UID生成器, PostgreSQL, 分布式ID, 高性能, 工作机器ID, 缓存, 并发安全, 网络环境]
created_date: 2025-05-15
updated_date: 2025-05-27
status: 草稿
version: 1.8
authors: [AI助手]
affected_features:
  - F003
related_docs:
  - docs/common/middleware/integration/snowflake-id-high-availability-design.md
  - docs/common/middleware/postgresql/integration-guide.md
---

# 基于PostgreSQL的百度UID生成器实现方案

## 摘要

本文档提供了一种基于PostgreSQL数据库的百度UID生成器实现方案，旨在为xkongcloud项目提供高性能、可靠的分布式ID生成服务。该方案充分利用了百度UID生成器的高性能特性和PostgreSQL的可靠性，实现了完全不依赖中心化服务的分布式ID生成机制，特别适合对性能要求高且希望降低对center服务依赖的场景。

## 背景与目标

### 背景

在分布式系统中，全局唯一ID是一项基础服务，几乎被所有业务模块依赖。当前xkongcloud项目已有基于Snowflake算法的高可用ID生成设计方案，但该方案仍然依赖于xkongcloud-service-center服务。百度开源的UID生成器提供了一种基于数据库的、完全去中心化的ID生成方案，具有以下特点：

1. **高性能**：通过RingBuffer缓存和多种优化技术，单机QPS可达600万
2. **去中心化**：基于数据库分配工作机器ID，不依赖中心化服务
3. **灵活配置**：支持自定义时间戳、工作机器ID和序列号位数
4. **容错能力**：内置多种容错机制，提高系统可靠性

### 目标

1. 设计一种基于PostgreSQL的百度UID生成器实现方案，完全不依赖center服务
2. 实现工作机器ID的自动分配和管理，支持多实例环境
3. 优化性能，提供高吞吐量的ID生成服务
4. 确保生成ID的单调递增性和唯一性
5. 与现有系统无缝集成，提供简单易用的API

## 设计方案

### 1. 整体架构

基于PostgreSQL的百度UID生成器实现方案采用完全去中心化的架构，每个应用实例直接与PostgreSQL数据库交互，获取工作机器ID并生成唯一ID。

```
+-------------------+    +-------------------+    +-------------------+
| 应用实例1          |    | 应用实例2          |    | 应用实例3          |
| UID生成器客户端     |    | UID生成器客户端     |    | UID生成器客户端     |
+--------+----------+    +--------+----------+    +--------+----------+
         |                        |                        |
         |                        |                        |
         v                        v                        v
+------------------------------------------------------------------+
|                        PostgreSQL数据库                           |
|                                                                  |
|  +------------------+    +------------------+                    |
|  | WORKER_NODE表     |    | 业务数据表         |                    |
|  | (工作机器ID分配)   |    | (使用生成的ID)     |                    |
|  +------------------+    +------------------+                    |
+------------------------------------------------------------------+
```

### 2. ID结构

百度UID生成器支持自定义ID结构，我们采用以下配置：

```
+-------------+-------------+----------------+----------------+
| 符号位(1位) | 时间戳(31位) | 工作机器ID(18位) | 序列号(14位)    |
+-------------+-------------+----------------+----------------+
```

- **符号位(1位)**: 恒为0，保证ID为正数
- **时间戳(31位)**: 以秒为单位，从2025-01-01 00:00:00开始计算，可支持约68年
- **工作机器ID(18位)**: 由PostgreSQL数据库分配，最多支持约262,144个工作节点
- **序列号(14位)**: 同一秒内的自增序列，每秒最多生成16,384个ID

### 3. 工作机器ID分配机制

工作机器ID分配是百度UID生成器的核心机制之一，我们基于PostgreSQL实现如下：

1. **创建WORKER_NODE表**：
   ```sql
   CREATE TABLE WORKER_NODE (
     ID BIGSERIAL PRIMARY KEY,
     HOST_NAME VARCHAR(64) NOT NULL,
     PORT VARCHAR(64) NOT NULL,
     TYPE INT NOT NULL,
     LAUNCH_DATE DATE NOT NULL,
     CREATED TIMESTAMP NOT NULL,
     MODIFIED TIMESTAMP NOT NULL
   );
   ```

2. **工作机器ID分配流程**：
   - 应用启动时，首先尝试查找与当前主机名和端口匹配的已有记录
   - 如果找到匹配记录，复用该记录的ID作为工作机器ID
   - 如果未找到匹配记录，则向WORKER_NODE表插入一条新记录，并使用PostgreSQL自动分配的自增ID
   - 采用"复用"策略，允许应用重启后复用之前的工作机器ID，避免资源浪费

3. **多实例环境处理**：
   - 每个应用实例独立获取工作机器ID
   - 通过主机名和端口的唯一组合确保不同实例获取不同的工作机器ID
   - 实现"复用"策略，使同一实例在重启后能够复用之前的工作机器ID，减少ID资源消耗

### 4. 高性能实现

百度UID生成器提供了两种实现：DefaultUidGenerator和CachedUidGenerator。我们推荐使用CachedUidGenerator以获得最佳性能：

1. **RingBuffer缓存**：
   - 使用环形数组缓存预生成的ID
   - 默认缓存8192个ID，可通过boostPower参数调整
   - 支持并发生产和消费ID

2. **借用未来时间**：
   - 当ID消耗速度超过每秒8192个时，使用未来的时间戳生成ID
   - 提高系统吞吐量，但可能影响ID的严格时间顺序

3. **缓存行填充**：
   - 解决"伪共享"问题，提高并发性能
   - 通过填充无用数据使关键变量占用完整的CPU缓存行

4. **填充策略**：
   - 初始化填充：启动时填充RingBuffer
   - 阈值填充：当可用ID低于阈值时填充
   - 周期性填充：定时任务补充缓存

## 实现细节

### 1. PostgreSQL表结构

```sql
-- 工作机器ID分配表
CREATE TABLE WORKER_NODE (
  ID BIGSERIAL PRIMARY KEY,
  HOST_NAME VARCHAR(64) NOT NULL,
  PORT VARCHAR(64) NOT NULL,
  TYPE INT NOT NULL,
  LAUNCH_DATE TIMESTAMP NOT NULL,
  CREATED TIMESTAMP NOT NULL,
  MODIFIED TIMESTAMP NOT NULL
);

-- 创建索引
CREATE INDEX IDX_WORKER_NODE_HOST_PORT ON WORKER_NODE(HOST_NAME, PORT);

-- 添加唯一约束，确保HOST_NAME和PORT组合的唯一性
ALTER TABLE WORKER_NODE ADD CONSTRAINT UK_WORKER_NODE_HOST_PORT UNIQUE (HOST_NAME, PORT);
```

### WORKER_NODE表字段详解

| 字段名 | 类型 | 说明 |
|-------|------|------|
| ID | BIGSERIAL | 主键，自增ID，用作工作机器ID |
| HOST_NAME | VARCHAR(64) | 主机名，通常为IP地址 |
| PORT | VARCHAR(64) | 端口号 |
| TYPE | INT | 节点类型：1-容器节点(CONTAINER)，2-实体机节点(ACTUAL) |
| LAUNCH_DATE | TIMESTAMP | 启动时间 |
| CREATED | TIMESTAMP | 创建时间 |
| MODIFIED | TIMESTAMP | 修改时间 |

#### 节点类型(TYPE)说明
- **CONTAINER(1)**: 表示在容器环境（如Docker、Kubernetes）中运行的节点
- **ACTUAL(2)**: 表示在物理机或虚拟机上直接运行的节点

节点类型主要用于区分不同运行环境的节点，便于管理和统计。在大多数情况下，使用CONTAINER类型即可。

WorkerNodeType枚举类定义如下：

```java
/**
 * 工作节点类型枚举
 * 用于标识不同类型的工作节点
 */
public enum WorkerNodeType {
    /**
     * 容器节点
     * 表示在容器环境（如Docker、Kubernetes）中运行的节点
     */
    CONTAINER(1),

    /**
     * 实体机节点
     * 表示在物理机或虚拟机上直接运行的节点
     */
    ACTUAL(2);

    /**
     * 类型值
     */
    private final int value;

    /**
     * 构造函数
     * @param value 类型值
     */
    WorkerNodeType(int value) {
        this.value = value;
    }

    /**
     * 获取类型值
     * @return 类型值
     */
    public int value() {
        return value;
    }

    /**
     * 根据类型值获取枚举实例
     * @param value 类型值
     * @return 对应的枚举实例，如果不存在则返回null
     */
    public static WorkerNodeType of(int value) {
        for (WorkerNodeType type : values()) {
            if (type.value == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的工作节点类型值: " + value);
    }
}
```

### 2. 工作机器ID分配器实现

```java
@Repository
public class ReusablePostgreSQLWorkerIdAssigner implements WorkerIdAssigner {

    private static final Logger logger = LoggerFactory.getLogger(ReusablePostgreSQLWorkerIdAssigner.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TransactionTemplate transactionTemplate;

    /**
     * 分配工作机器ID
     * 实现"复用"策略，允许应用重启后复用之前的工作机器ID
     * 使用事务和锁机制确保并发安全
     */
    @Override
    public long assignWorkerId() {
        // 获取主机名和端口
        String hostName = NetUtils.getLocalAddress();
        String port = System.getProperty("server.port", "8080");

        logger.info("开始为节点分配工作机器ID, 主机名: {}, 端口: {}", hostName, port);

        // 使用事务和锁确保并发安全
        return transactionTemplate.execute(status -> {
            try {
                // 使用FOR UPDATE锁定查询，防止其他事务同时查询和插入
                String querySql = "SELECT ID FROM WORKER_NODE WHERE HOST_NAME = ? AND PORT = ? FOR UPDATE";
                List<Long> ids = jdbcTemplate.queryForList(querySql, Long.class, hostName, port);

                // 如果找到记录，复用ID
                if (!ids.isEmpty()) {
                    Long workerId = ids.get(0);
                    logger.info("复用已存在的工作机器ID: {}", workerId);
                    return workerId;
                }

                logger.info("未找到匹配记录，创建新的工作机器ID");

                // 否则创建新记录
                String insertSql = "INSERT INTO WORKER_NODE(HOST_NAME, PORT, TYPE, LAUNCH_DATE, CREATED, MODIFIED) " +
                                  "VALUES (?, ?, ?, ?, ?, ?) RETURNING ID";

                Long workerId = jdbcTemplate.queryForObject(insertSql, Long.class,
                        hostName,
                        port,
                        WorkerNodeType.CONTAINER.value(),
                        new Date(),
                        new Timestamp(System.currentTimeMillis()),
                        new Timestamp(System.currentTimeMillis()));

                logger.info("成功创建新的工作机器ID: {}", workerId);
                return workerId;
            } catch (DataAccessException e) {
                // 处理可能的并发异常，例如唯一约束冲突
                logger.warn("分配工作机器ID时发生异常，尝试重新查询", e);

                // 如果发生冲突，重新尝试查询
                String retrySql = "SELECT ID FROM WORKER_NODE WHERE HOST_NAME = ? AND PORT = ?";
                List<Long> ids = jdbcTemplate.queryForList(retrySql, Long.class, hostName, port);
                if (!ids.isEmpty()) {
                    Long workerId = ids.get(0);
                    logger.info("通过重试查询获取到工作机器ID: {}", workerId);
                    return workerId;
                }

                // 如果仍然找不到，则抛出异常
                logger.error("无法分配工作机器ID", e);
                throw e;
            }
        });
    }
}
```

### 3. 网络工具类实现

```java
/**
 * 网络工具类
 * 提供获取本机IP地址等网络相关功能
 */
public class NetUtils {
    private static final Logger logger = LoggerFactory.getLogger(NetUtils.class);

    /**
     * 获取本机IP地址
     * 优先获取外部可访问的IP地址
     *
     * @return 本机IP地址
     */
    public static String getLocalAddress() {
        try {
            // 1. 尝试从环境变量获取（适用于容器环境）
            String envHost = System.getenv("HOST_IP");
            if (isValidIpAddress(envHost)) {
                logger.info("使用环境变量HOST_IP: {}", envHost);
                return envHost;
            }

            // 2. 尝试从Java系统属性获取
            String sysHost = System.getProperty("host.ip");
            if (isValidIpAddress(sysHost)) {
                logger.info("使用系统属性host.ip: {}", sysHost);
                return sysHost;
            }

            // 3. 尝试获取非回环、非内网的地址
            for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces(); en.hasMoreElements();) {
                NetworkInterface intf = en.nextElement();
                if (intf.isUp() && !intf.isLoopback() && !intf.isVirtual()) {
                    for (Enumeration<InetAddress> enumIpAddr = intf.getInetAddresses(); enumIpAddr.hasMoreElements();) {
                        InetAddress inetAddress = enumIpAddr.nextElement();
                        if (!inetAddress.isLoopbackAddress() && inetAddress instanceof Inet4Address) {
                            String ipAddress = inetAddress.getHostAddress();
                            // 优先使用非内网地址
                            if (!isInternalIp(ipAddress)) {
                                logger.info("使用外部IP地址: {}", ipAddress);
                                return ipAddress;
                            }
                        }
                    }
                }
            }

            // 4. 如果没有找到外部地址，使用内网地址
            for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces(); en.hasMoreElements();) {
                NetworkInterface intf = en.nextElement();
                if (intf.isUp() && !intf.isLoopback() && !intf.isVirtual()) {
                    for (Enumeration<InetAddress> enumIpAddr = intf.getInetAddresses(); enumIpAddr.hasMoreElements();) {
                        InetAddress inetAddress = enumIpAddr.nextElement();
                        if (!inetAddress.isLoopbackAddress() && inetAddress instanceof Inet4Address) {
                            String ipAddress = inetAddress.getHostAddress();
                            if (isInternalIp(ipAddress)) {
                                logger.info("使用内网IP地址: {}", ipAddress);
                                return ipAddress;
                            }
                        }
                    }
                }
            }

            // 5. 最后尝试使用本地主机地址
            String localHost = InetAddress.getLocalHost().getHostAddress();
            logger.info("使用本地主机地址: {}", localHost);
            return localHost;
        } catch (Exception e) {
            logger.error("获取本机IP地址失败", e);
            throw new RuntimeException("无法获取有效的主机标识，请通过配置明确指定主机标识。可通过以下方式配置：" +
                    "1. 配置文件: uid.worker.host=<主机IP> " +
                    "2. 环境变量: HOST_IP=<主机IP> " +
                    "3. 系统属性: -Dhost.ip=<主机IP>", e);
        }
    }

    /**
     * 判断是否为有效的IP地址
     */
    private static boolean isValidIpAddress(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        String regex = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        return ip.matches(regex);
    }

    /**
     * 判断是否为内网IP
     */
    private static boolean isInternalIp(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        // 10.0.0.0/8
        // **********/12
        // ***********/16
        return ip.startsWith("10.") ||
               (ip.startsWith("172.") && (Integer.parseInt(ip.split("\\.")[1]) >= 16 && Integer.parseInt(ip.split("\\.")[1]) <= 31)) ||
               ip.startsWith("192.168.");
    }
}
```

### 4. 可配置的工作机器ID分配器

```java
/**
 * 可配置的工作机器ID分配器
 * 支持自定义主机标识
 */
@Repository
public class ConfigurableWorkerIdAssigner implements WorkerIdAssigner {

    private static final Logger logger = LoggerFactory.getLogger(ConfigurableWorkerIdAssigner.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Value("${uid.worker.host:#{null}}")
    private String customHost;

    @Value("${uid.worker.port:#{null}}")
    private String customPort;

    /**
     * 分配工作机器ID
     * 实现"复用"策略，允许应用重启后复用之前的工作机器ID
     * 使用事务和锁机制确保并发安全
     */
    @Override
    public long assignWorkerId() {
        // 获取主机名和端口，优先使用配置的值
        String hostName = customHost != null ? customHost : NetUtils.getLocalAddress();
        String port = customPort != null ? customPort : System.getProperty("server.port", "8080");

        logger.info("开始为节点分配工作机器ID, 主机名: {}, 端口: {}", hostName, port);

        // 使用事务和锁确保并发安全
        return transactionTemplate.execute(status -> {
            try {
                // 使用FOR UPDATE锁定查询，防止其他事务同时查询和插入
                String querySql = "SELECT ID FROM WORKER_NODE WHERE HOST_NAME = ? AND PORT = ? FOR UPDATE";
                List<Long> ids = jdbcTemplate.queryForList(querySql, Long.class, hostName, port);

                // 如果找到记录，复用ID
                if (!ids.isEmpty()) {
                    Long workerId = ids.get(0);
                    logger.info("复用已存在的工作机器ID: {}", workerId);
                    return workerId;
                }

                logger.info("未找到匹配记录，创建新的工作机器ID");

                // 否则创建新记录
                String insertSql = "INSERT INTO WORKER_NODE(HOST_NAME, PORT, TYPE, LAUNCH_DATE, CREATED, MODIFIED) " +
                                  "VALUES (?, ?, ?, ?, ?, ?) RETURNING ID";

                Long workerId = jdbcTemplate.queryForObject(insertSql, Long.class,
                        hostName,
                        port,
                        WorkerNodeType.CONTAINER.value(),
                        new Date(),
                        new Timestamp(System.currentTimeMillis()),
                        new Timestamp(System.currentTimeMillis()));

                logger.info("成功创建新的工作机器ID: {}", workerId);
                return workerId;
            } catch (DataAccessException e) {
                // 处理可能的并发异常，例如唯一约束冲突
                logger.warn("分配工作机器ID时发生异常，尝试重新查询", e);

                // 如果发生冲突，重新尝试查询
                String retrySql = "SELECT ID FROM WORKER_NODE WHERE HOST_NAME = ? AND PORT = ?";
                List<Long> ids = jdbcTemplate.queryForList(retrySql, Long.class, hostName, port);
                if (!ids.isEmpty()) {
                    Long workerId = ids.get(0);
                    logger.info("通过重试查询获取到工作机器ID: {}", workerId);
                    return workerId;
                }

                // 如果仍然找不到，则抛出异常
                logger.error("无法分配工作机器ID", e);
                throw e;
            }
        });
    }
}
```

### 5. UID生成器配置

```java
@Configuration
public class UidGeneratorConfig {

    @Bean
    public WorkerIdAssigner workerIdAssigner() {
        return new ConfigurableWorkerIdAssigner();
    }

    @Bean
    public CachedUidGenerator cachedUidGenerator(WorkerIdAssigner workerIdAssigner) {
        CachedUidGenerator cachedUidGenerator = new CachedUidGenerator();
        cachedUidGenerator.setWorkerIdAssigner(workerIdAssigner);

        // 自定义参数
        // 时间戳位数
        cachedUidGenerator.setTimeBits(31);
        // 工作机器ID位数
        cachedUidGenerator.setWorkerBits(18);
        // 序列号位数
        cachedUidGenerator.setSeqBits(14);
        // 时间基点
        cachedUidGenerator.setEpochStr("2025-01-01");

        // RingBuffer大小扩容参数
        cachedUidGenerator.setBoostPower(3);
        // 指定何时向RingBuffer中填充UID, 取值为百分比(0, 100)
        cachedUidGenerator.setPaddingFactor(50);

        // 可选: 配置RingBuffer填充时机
        // 每隔多久填充一次
        cachedUidGenerator.setScheduleInterval(60L);

        return cachedUidGenerator;
    }
}
```


## 配置和使用指南

### 1. 依赖配置

在项目的`pom.xml`中添加以下依赖：

```xml
<!-- 百度UID生成器 -->
<dependency>
    <groupId>com.xfvape.uid</groupId>
    <artifactId>uid-generator</artifactId>
    <version>0.0.4-RELEASE</version>
</dependency>

<!-- PostgreSQL驱动 -->
<dependency>
    <groupId>org.postgresql</groupId>
    <artifactId>postgresql</artifactId>
    <version>42.7.3</version>
</dependency>

<!-- Spring JDBC -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-jdbc</artifactId>
</dependency>

<!-- Spring事务管理 -->
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-tx</artifactId>
    <!-- 版本由Spring Boot管理，无需显式指定 -->
</dependency>
```

### 2. 数据库初始化

#### 方法一：使用xkongcloud-commons-uid公共库中的UidTableManager工具类

如果您使用了xkongcloud-commons-uid公共库，可以使用其中的UidTableManager工具类来管理UID生成器所需的数据库表：

```java
// 创建表和索引
UidTableManager.createTables(jdbcTemplate);

// 填充WORKER_ID_ASSIGNMENTS表
UidTableManager.fillWorkerIdAssignments(jdbcTemplate, 18); // 18是workerBits位数

// 如果表不存在则创建
UidTableManager.createTablesIfNotExist(jdbcTemplate);

// 验证表结构
UidTableManager.validateTables(jdbcTemplate);

// 删除表
UidTableManager.dropTables(jdbcTemplate);
```

UidTableManager工具类支持不同的DDL策略（create, create-drop, update, validate, none），可以根据postgresql.ddl-auto参数调用相应的方法。

#### 方法二：手动执行SQL脚本

如果您没有使用xkongcloud-commons-uid公共库，可以手动执行以下SQL脚本：

```sql
-- 创建工作机器ID分配表
CREATE TABLE IF NOT EXISTS WORKER_NODE (
  ID BIGSERIAL PRIMARY KEY,
  HOST_NAME VARCHAR(64) NOT NULL,
  PORT VARCHAR(64) NOT NULL,
  TYPE INT NOT NULL,
  LAUNCH_DATE TIMESTAMP NOT NULL,
  CREATED TIMESTAMP NOT NULL,
  MODIFIED TIMESTAMP NOT NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS IDX_WORKER_NODE_HOST_PORT ON WORKER_NODE(HOST_NAME, PORT);

-- 添加唯一约束，确保HOST_NAME和PORT组合的唯一性
ALTER TABLE WORKER_NODE ADD CONSTRAINT IF NOT EXISTS UK_WORKER_NODE_HOST_PORT UNIQUE (HOST_NAME, PORT);
```

### 3. 应用配置

在`application.properties`或`application.yml`中添加以下配置：

```properties
# 数据库配置
spring.datasource.url=***********************************ur_database
spring.datasource.username=your_username
spring.datasource.password=your_password
spring.datasource.driver-class-name=org.postgresql.Driver

# UID生成器配置
# 自定义主机标识（可选）
# uid.worker.host=*************
# uid.worker.port=8080
```

#### 主机标识配置说明

为了解决在复杂网络环境（如多网卡、容器环境）中获取本机IP地址的问题，可以通过以下方式自定义主机标识：

1. **配置文件方式**：
   ```properties
   uid.worker.host=*************
   uid.worker.port=8080
   ```

2. **环境变量方式**：
   ```bash
   export HOST_IP=*************
   ```

3. **系统属性方式**：
   ```bash
   java -Dhost.ip=************* -jar your-application.jar
   ```

> **重要提示**：如果系统无法自动获取有效的主机标识（IP地址），应用将无法启动。这是一项安全措施，确保每个节点都有明确、可识别的主机标识，避免在`WORKER_NODE`表中出现难以区分的记录。在这种情况下，您必须通过上述方式之一明确配置主机标识。
>
> 这种"快速失败"的设计理念有助于提前发现配置问题，而不是在运行时产生难以排查的问题。

#### 主机标识配置优先级

系统按以下优先级获取主机标识：

1. 配置文件中的`uid.worker.host`属性（最高优先级）
2. 环境变量`HOST_IP`
3. 系统属性`host.ip`
4. 自动检测的外部IP地址
5. 自动检测的内网IP地址
6. 本地主机地址

如果以上所有方法都无法获取有效的主机标识，应用将无法启动，并显示明确的错误信息。

在以下情况下，建议手动配置主机标识：

- **多网卡环境**：系统可能选择错误的网卡IP
- **容器环境**：容器内获取的IP可能是容器内部IP，而非宿主机IP
- **代理或NAT环境**：获取的IP可能不是外部可访问的IP

### 3. 使用示例

```java
@Service
public class UserService {
    @Autowired
    private UidGenerator uidGenerator;

    @Autowired
    private UserRepository userRepository;

    /**
     * 创建用户
     */
    @Transactional
    public User createUser(UserCreateRequest request) {
        User user = new User();

        // 生成用户ID
        long userId = uidGenerator.getUID();
        user.setUserId(userId);

        // 设置其他属性
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        // ...

        // 保存用户
        return userRepository.save(user);
    }
}
```

## 并发安全性考虑

在分布式环境中，特别是当多个应用实例同时启动时，工作机器ID的分配需要特别注意并发安全性。本方案通过以下机制确保工作机器ID分配的并发安全：

### 1. 数据库唯一约束

通过在`WORKER_NODE`表的`HOST_NAME`和`PORT`字段上添加唯一约束，确保相同的主机名和端口组合只能在表中存在一条记录，从而防止重复分配。

```sql
ALTER TABLE WORKER_NODE ADD CONSTRAINT UK_WORKER_NODE_HOST_PORT UNIQUE (HOST_NAME, PORT);
```

### 2. 事务和锁机制

在`ConfigurableWorkerIdAssigner`的`assignWorkerId`方法中，使用事务和行级锁（`FOR UPDATE`）确保在高并发场景下的安全性：

1. 开启事务
2. 使用`SELECT ... FOR UPDATE`锁定查询，防止其他事务同时查询和插入
3. 如果找到匹配记录，复用ID并提交事务
4. 如果未找到匹配记录，插入新记录并提交事务

### 3. 异常处理和重试机制

即使有了唯一约束和锁机制，在极端情况下仍可能发生并发冲突。为此，实现了异常处理和重试机制：

1. 捕获可能的数据库异常（如唯一约束冲突）
2. 发生异常时，重新查询是否已存在匹配记录
3. 如果找到记录，复用ID
4. 如果仍未找到，则抛出异常

### 4. 日志记录

详细的日志记录有助于排查并发问题：

1. 记录工作机器ID分配的开始和结果
2. 记录主机名和端口信息
3. 记录异常情况和重试过程

通过以上机制，即使在大规模分布式环境中，也能确保工作机器ID分配的正确性和唯一性。

## 性能和可用性考虑

### 性能优势

1. **高吞吐量**：单机QPS可达600万，远高于普通Snowflake实现
2. **低延迟**：本地生成ID，无网络开销
3. **批量预生成**：通过RingBuffer缓存预生成的ID，进一步提高性能
4. **缓存行填充**：避免伪共享问题，优化多核CPU性能

### 可用性保障

1. **去中心化架构**：不依赖中心化服务，消除单点故障风险
2. **数据库可靠性**：利用PostgreSQL的高可靠性保障工作机器ID分配
3. **容错机制**：内置多种容错策略，提高系统稳定性
4. **可扩展性**：支持大规模分布式部署，工作机器ID位数(18位)可支持约262,144个工作节点
5. **网络环境适应性**：改进的IP地址获取机制，适应各种复杂网络环境

### 注意事项

1. **时钟依赖**：仍然依赖系统时钟，需要确保服务器时间同步
2. **数据库依赖**：启动时需要访问PostgreSQL数据库获取工作机器ID
3. **ID有序性**：使用CachedUidGenerator时，由于"借用未来时间"机制，ID可能不严格按时间顺序生成
4. **工作机器ID管理**：采用"复用"策略，允许应用重启后复用之前的工作机器ID，减少ID资源消耗

## 网络环境配置

在复杂的网络环境中，正确获取和配置主机标识对于工作机器ID的分配至关重要。本方案提供了多种方式来适应不同的网络环境。

### 1. 多网卡环境

在具有多个网卡的服务器上，系统可能无法自动选择正确的网卡IP地址。解决方案：

1. **手动配置主机标识**：
   ```properties
   uid.worker.host=*************
   ```

2. **优先级策略**：改进的`NetUtils.getLocalAddress()`方法会按以下优先级选择IP地址：
   - 非回环、非内网的外部IP地址
   - 非回环的内网IP地址
   - 本地主机地址

### 2. 容器环境

在Docker、Kubernetes等容器环境中，容器内获取的IP通常是容器内部IP，而非宿主机IP。解决方案：

1. **环境变量注入**：
   ```yaml
   # Docker运行示例
   docker run -e HOST_IP=************* -p 8080:8080 your-image

   # Kubernetes Pod配置示例
   env:
   - name: HOST_IP
     valueFrom:
       fieldRef:
         fieldPath: status.hostIP
   ```

2. **主机网络模式**：
   ```yaml
   # Docker运行示例
   docker run --network host your-image

   # Kubernetes Pod配置示例
   hostNetwork: true
   ```

### 3. 代理或NAT环境

在使用代理或NAT的网络环境中，服务器获取的IP可能不是外部可访问的IP。解决方案：

1. **系统属性配置**：
   ```bash
   java -Dhost.ip=外部可访问IP -jar your-application.jar
   ```

2. **配置文件设置**：
   ```properties
   uid.worker.host=外部可访问IP
   uid.worker.port=外部可访问端口
   ```


通过以上配置和实现，可以确保在各种复杂网络环境中正确获取和配置主机标识，从而保证工作机器ID分配的正确性和唯一性。

## 与现有高可用设计方案的对比

| 特性 | 百度UID生成器(PostgreSQL) | 现有Snowflake高可用方案 |
|-----|-------------------------|----------------------|
| 架构 | 完全去中心化，基于PostgreSQL | 分层架构，依赖center服务 |
| 性能 | 单机QPS可达600万 | 较低，受网络和center服务限制 |
| 可用性 | 仅依赖PostgreSQL数据库 | 依赖center服务，但有本地备份机制 |
| ID结构 | 可自定义，31位时间戳(秒) | 41位时间戳(毫秒) |
| 工作机器ID | 18位，支持约262,144个工作节点 | 10位，支持约1024台机器 |
| 序列号 | 14位，每秒最多16,384个ID | 12位，每毫秒最多4096个ID |
| 时间单位 | 秒 | 毫秒 |
| 有序性 | 趋势递增，不保证严格时序 | 严格按时间顺序 |
| 实现复杂度 | 中等 | 较高 |
| 集成难度 | 简单，仅依赖PostgreSQL | 中等，需要与center服务集成 |

## 建议使用场景

1. **高性能场景**：对ID生成性能要求极高的业务
2. **去中心化需求**：希望减少对center服务依赖的场景
3. **大规模部署**：需要支持大量机器启动的场景
4. **PostgreSQL环境**：已经使用PostgreSQL作为主数据库的项目

## 结论

基于PostgreSQL的百度UID生成器实现方案提供了一种高性能、去中心化的分布式ID生成解决方案。它完全不依赖center服务，通过PostgreSQL数据库分配工作机器ID，并利用多种优化技术实现极高的吞吐量。该方案特别适合对性能要求高且希望降低对center服务依赖的场景。

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|------|---------|-------|
| 1.8 | 2025-05-27 | 更新数据库初始化部分，添加使用xkongcloud-commons-uid公共库中的UidTableManager工具类的方法 | AI助手 |
| 1.7 | 2025-05-20 | 1. 修改NetUtils.getLocalAddress()方法，在无法获取有效IP地址时抛出异常而不是返回UNKNOWN-UUID<br>2. 添加主机标识配置优先级说明<br>3. 更新主机标识配置说明，强调"快速失败"设计理念 | AI助手 |
| 1.6 | 2025-05-19 | 1. 更新PostgreSQL驱动版本从42.7.5到42.7.3<br>2. 更新百度UID生成器版本从0.0.5-RELEASE到0.0.4-RELEASE<br>3. 优化spring-tx依赖配置，添加注释说明版本由Spring Boot管理 | AI助手 |
| 1.5 | 2025-05-18 | 将WORKER_NODE表的LAUNCH_DATE字段类型从DATE改为TIMESTAMP，以匹配实际插入的时间值类型 | AI助手 |
| 1.4 | 2025-05-18 | 1. 改进工作机器ID分配的并发安全性<br>2. 添加WorkerNodeType枚举类的详细说明<br>3. 改进NetUtils.getLocalAddress()实现<br>4. 添加ConfigurableWorkerIdAssigner支持自定义主机标识<br>5. 添加并发安全性考虑和网络环境配置章节 | AI助手 |
| 1.3 | 2025-05-18 | 添加PostgreSQL表唯一约束，确保HOST_NAME和PORT组合的唯一性 | AI助手 |
| 1.2 | 2025-05-17 | 更新性能测试数据，补充使用示例 | AI助手 |
| 1.1 | 2025-05-17 | 将工作机器ID复用策略从可选改为默认实现，允许应用重启后复用之前的工作机器ID | AI助手 |
| 1.0 | 2025-05-15 | 初始版本 | AI助手 |
