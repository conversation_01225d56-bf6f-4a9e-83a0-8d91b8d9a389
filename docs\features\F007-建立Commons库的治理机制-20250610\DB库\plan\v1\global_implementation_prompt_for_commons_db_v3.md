[Preamble: 项目背景与AI角色设定]
你是一位经验丰富的软件架构师和项目管理专家，熟悉Java生态、Spring Boot、PostgreSQL以及现代云原生架构。你的任务是为“XKongCloud Commons DB V3”项目制定一份全面且可执行的实施计划。

[Context: 项目核心文档与信息源]
此实施计划必须严格依据以下信息源：
1.  **12份核心设计文档**: 01-架构总览与设计哲学.md, 02-核心抽象层设计.md, 03-JPA实现层设计.md, 04-Querydsl集成设计.md, 05-JDBC封装设计.md, 06-数据库方言设计.md, 07-监控集成设计.md, 08-Schema管理设计.md, 09-自动配置设计.md, 10-API接口规范.md, 11-使用指南和最佳实践.md, 12-测试策略和验收标准.md, 13-现代技术特性集成设计.md (请根据实际文档列表调整，确保包含所有相关设计文档)。这些文档详细描述了项目的架构、模块设计、技术选型和功能需求。你必须彻底理解并消化这些文档的内容。
2.  **项目技术栈**: Java 21 (充分利用虚拟线程、Records、Sealed Classes等新特性), Spring Boot 3.4 (包括Actuator监控、自动配置、AOT编译优化), PostgreSQL 17 (深度集成JSON_TABLE、并行查询、流式I/O、高级窗口函数等新特性), HikariCP, Querydsl, Flyway/Liquibase (用于Schema管理), Micrometer (结合Prometheus/Grafana进行监控), OpenTelemetry (用于链路追踪), GraalVM (考虑原生镜像支持)。
3.  **关键设计原则**: 高性能、高可用性、高可扩展性、可维护性、可测试性、安全性、用户友好性、云原生适配、遵循SOLID原则、DRY原则。
4.  **生产环境特性**: 应用为单机单实例部署，但数据库是远程集群。这需要在数据库连接、迁移策略、容错性方面特别考虑。
5.  **配置管理要求**: 支持基于环境标识（如 `uid.instance.environment`, `uid.instance.group`）的动态配置加载和管理。

[Task: 实施计划的核心要求]
请生成一份详细的Markdown格式的实施计划，该计划应包含以下主要阶段和任务。对于每个任务，请明确：
    - **任务ID**: (例如 Phase1.Task1)
    - **任务名称**: (简洁明了)
    - **任务描述**: (详细说明该任务的目标和范围)
    - **关键实现步骤/子任务**: (分解任务为可操作的步骤)
    - **涉及的核心类/接口/注解/配置项**: (列出相关的技术关键点)
    *   **主要参考设计文档**: (明确指出此任务主要依据哪些设计文档章节)
    - **预计输出/交付物**: (例如：代码模块、API接口、配置文件、单元测试用例、集成测试用例、文档片段)
    - **前置依赖任务ID**: (明确任务间的依赖关系)
    - **验收标准**: (如何验证任务已完成并符合质量要求)
    - **负责人**: (使用占位符，如 "[待分配]")
    - **预估工时 (天)**: (使用占位符，如 "[待评估]")
    - **潜在风险与应对措施**: (识别可能的风险点)

[Structure: 实施计划的阶段划分]
请按照以下阶段组织实施计划：

**Phase 0: 项目初始化与环境准备**
    - Task0.1: 创建多模块项目结构 (Maven/Gradle: commons-db-core, commons-db-jpa, commons-db-jdbc, commons-db-querydsl, commons-db-dialect, commons-db-monitor, commons-db-migration, commons-db-autoconfigure, commons-db-examples, commons-db-starter)
    - Task0.2: 配置版本控制 (Git, 分支策略: main, develop, feature/xxx, release/xxx, hotfix/xxx)
    - Task0.3: 搭建基础CI/CD流水线 (例如使用GitHub Actions或Jenkins)
    - Task0.4: 准备开发、测试数据库环境 (PostgreSQL 17实例，确保与设计文档中提及的特性兼容)

**Phase 1: 核心抽象层设计与实现 (参考: 02-核心抽象层设计.md)**
    - Task1.1: 定义 `DataAccessTemplate<T, ID>` 核心接口及其所有方法签名 (CRUD, 分页 `Page<T>`, `Pageable`, 排序 `Sort`, 异步 `CompletableFuture<V>`, 批量操作)。
    - Task1.2: 设计并实现统一的异常体系 (如 `DataAccessException` 及其特定子类 `OptimisticLockingFailureException`, `DuplicateKeyException` 等)。
    - Task1.3: 规划并初步实现SPI (Service Provider Interface) 机制，用于后续不同实现（JPA, JDBC）的解耦和扩展。
    - Task1.4: 考虑并集成基础的日志切面 (AOP) 用于记录方法调用和耗时。

**Phase 2: JPA 实现层 (参考: 03-JPA实现层设计.md, 06-数据库方言设计.md - PostgreSQL部分)**
    - Task2.1: 集成Spring Data JPA，创建自定义的 `BaseJpaRepository<T, ID>` 继承 `JpaRepository` 和 `JpaSpecificationExecutor`。
    - Task2.2: 实现 `DataAccessTemplate` 的JPA版本 (`JpaDataAccessTemplate`)，将调用委托给内部的 `JpaRepository`。
    - Task2.3: 重点实现并优化异步数据库操作，确保使用Java 21虚拟线程池执行JPA调用。
    - Task2.4: 配置和调优HikariCP连接池参数，包括针对虚拟线程的优化。
    - Task2.5: 实现PostgreSQL 17方言的JPA层面支持，包括对JSONB操作、特定函数等的封装。
    - Task2.6: 定义实体 (`@Entity`) 设计规范，包括主键生成策略、乐观锁 (`@Version`)、审计字段 (`@CreatedDate`, `@LastModifiedDate`)。

**Phase 3: Querydsl 集成 (参考: 04-Querydsl集成设计.md)**
    - Task3.1: 引入Querydsl依赖，配置APT插件自动生成Q类型。
    - Task3.2: 在 `JpaDataAccessTemplate` 中集成Querydsl支持，提供基于 `Predicate` 的动态查询能力。
    - Task3.3: 实现Querydsl与Spring Data JPA分页和排序的无缝结合。
    - Task3.4: 提供使用Querydsl构建复杂查询（多表连接、子查询、聚合函数、CASE表达式）的示例和最佳实践。

**Phase 4: JDBC 封装层 (可选，根据优先级和场景需求) (参考: 05-JDBC封装设计.md)**
    - Task4.1: 设计并实现一个轻量级的、支持虚拟线程的JDBC操作模板 (类似 `JdbcTemplate` 但为异步设计)。
    - Task4.2: 实现高性能的批量插入/更新/删除操作 (使用 `PreparedStatement.addBatch()` 和 `executeBatch()`)。
    - Task4.3: 实现灵活的 `RowMapper` 接口和结果集流式处理。
    - Task4.4: (如果实现) 提供 `DataAccessTemplate` 的JDBC版本 (`JdbcDataAccessTemplate`)。

**Phase 5: 数据库方言深度支持 (参考: 06-数据库方言设计.md)**
    - Task5.1: 针对PostgreSQL 17，深入实现方言特定的SQL生成逻辑，充分利用其新特性 (如 `JSON_TABLE` 语法构造, `MERGE` 命令, 窗口函数优化)。
    - Task5.2: (如果需要支持其他数据库如MySQL) 实现MySQL方言，并设计方言动态加载和切换机制。
    - Task5.3: 考虑方言相关的SQL优化器提示或特定查询重写规则。

**Phase 6: 监控与可观测性集成 (参考: 07-监控集成设计.md)**
    - Task6.1: 集成Micrometer，定义并暴露核心性能指标 (例如：数据库连接池状态 - HikariCP metrics, SQL查询平均/最大/P95/P99耗时, 事务提交/回滚次数, 错误率)。
    - Task6.2: 通过Spring Boot Actuator暴露 `/metrics` 和 `/health` 端点，并确保健康检查能反映数据库连接状态。
    - Task6.3: 集成OpenTelemetry实现数据库操作的链路追踪，确保trace ID和span ID能正确传递。
    - Task6.4: 制定详细的日志规范，包括日志格式、级别控制，确保关键操作（如慢查询、错误）有详细日志。

**Phase 7: Schema 迁移与管理 (参考: 08-Schema管理设计.md)**
    - Task7.1: 集成Flyway (或Liquibase) 进行数据库Schema的版本控制和自动化迁移。
    - Task7.2: 制定迁移脚本的命名规范、版本号管理策略和编写最佳实践 (例如：幂等性、小步快跑、兼容旧版本)。
    - Task7.3: 实现自动化迁移流程，并严格测试迁移脚本的正确性和回滚机制（如果设计文档要求）。
    - Task7.4: 考虑生产环境远程DB集群的迁移策略，如蓝绿部署或滚动更新时的Schema变更处理。

**Phase 8: Spring Boot 自动化配置 (参考: 09-自动配置设计.md)**
    - Task8.1: 为 `commons-db-core`, `commons-db-jpa`, `commons-db-jdbc` (如果实现), `commons-db-querydsl` 等模块编写Spring Boot自动配置类 (`@Configuration`, `@EnableConfigurationProperties`)。
    - Task8.2: 使用 `@ConditionalOnClass`, `@ConditionalOnBean`, `@ConditionalOnProperty` 等注解实现智能的条件化装配。
    - Task8.3: 定义清晰的配置属性命名空间 (如 `xkong.commons.db.*`)，并通过 `@ConfigurationProperties` 实现类型安全的属性绑定。
    - Task8.4: 创建 `commons-db-starter` POM，聚合所有核心依赖和自动配置。

**Phase 9: API 接口规范最终化与文档、示例 (参考: 10-API接口规范.md, 11-使用指南和最佳实践.md)**
    - Task9.1: 严格审查并确保所有对外暴露的API（特别是 `DataAccessTemplate` 及其实现）完全遵循设计文档中定义的接口规范。
    - Task9.2: 编写高质量的JavaDoc，覆盖所有公开的类、接口和方法。
    - Task9.3: 在 `commons-db-examples` 模块中，创建全面的使用示例，覆盖所有核心功能点和典型应用场景 (如：基本CRUD, 分页查询, 异步操作, 批量处理, Querydsl动态查询, 特定PG特性使用)。
    - Task9.4: 撰写详细的用户手册/最佳实践文档 (Markdown格式)，指导开发者如何引入依赖、配置参数、以及高效使用本框架。

**Phase 10: 现代技术特性深度集成与优化 (参考: 13-现代技术特性集成设计.md)**
    - Task10.1: 针对PostgreSQL 17的 `JSON_TABLE`, 并行查询优化, 流式I/O, 高级窗口函数等，设计具体的应用场景并实现相关API或工具类。
    - Task10.2: 全面评估和应用Java 21虚拟线程在所有I/O密集型数据库操作中的正确性和性能收益。
    - Task10.3: (可选，根据项目优先级) 进行GraalVM原生镜像的构建实验，优化启动时间和内存占用，并进行性能对比测试。
    - Task10.4: 确保所有组件设计符合云原生十二要素应用原则，例如配置外部化、日志作为事件流、无状态性等。

**Phase 11: 全面测试与质量保证 (参考: 12-测试策略和验收标准.md)**
    - Task11.1: 编写单元测试 (JUnit 5/Mockito)，确保核心业务逻辑和工具类的代码覆盖率达到设计目标 (例如 >85%)。
    - Task11.2: 编写集成测试 (使用Testcontainers启动真实的PostgreSQL实例)，验证各模块与数据库的实际交互、事务管理、异步流程的正确性。
    - Task11.3: 设计并执行性能测试场景 (例如使用JMeter或k6)，评估在高并发、大数据量下的系统吞吐量、响应时间和资源利用率。
    - Task11.4: 实施代码审查流程，并集成静态代码分析工具 (如Checkstyle, PMD, SpotBugs) 以保证代码质量和规范性。

**Phase 12: 整体集成、文档完善、部署演练与最终验收**
    - Task12.1: 进行所有模块的整体集成测试，确保端到端流程的正确性。
    - Task12.2: 完善所有相关的技术文档、用户手册和API文档。
    - Task12.3: 模拟生产环境（单机单应用，远程DB集群）进行部署演练。
    - Task12.4: 执行最终的功能验收测试 (UAT) 和性能验收测试。
    - Task12.5: 准备最终的发布包和版本说明。

[General Instructions & Constraints for Plan Generation]
- **安全性**: 在涉及数据库交互的每个环节，都要强调SQL注入防护、数据访问权限控制、敏感数据处理（如日志中的数据脱敏）的重要性。
- **可扩展性**: 在设计API和内部组件时，主动识别并预留扩展点 (例如使用策略模式、模板方法模式、SPI等)。
- **错误处理**: 实施计划中应包含对各种预期和意外错误的详细处理策略，包括重试机制、熔断机制（如果适用）、以及友好的错误提示。
- **事务管理**: 明确事务边界的划分，以及在异步和批量操作中如何正确管理事务。
- **代码质量**: 强调代码注释、命名规范、模块化设计、单一职责原则。
- **迭代与反馈**: 建议在每个阶段结束后安排评审和反馈环节。

[Final Output Request]
请基于以上所有信息，生成一份结构清晰、内容详尽、专业且可操作的Markdown格式的项目实施计划。确保计划中的每个任务都具有明确的目标和可衡量的交付成果。在计划的开头，请包含一个简短的项目概述和目标声明。在计划的结尾，请建议一个初步的项目里程碑和时间表示例（使用占位符）。
