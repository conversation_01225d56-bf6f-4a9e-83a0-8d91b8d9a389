# 新对话AI助手指令

## 🎯 核心任务
修复AI管理器质量评估系统中的虚假质量评估问题，确保R1和V3模型使用真实的CAP质量评估。

## 🚨 关键问题
**QualityAssuranceGuard使用了简化版本的HighEfficiencyCAPQualityAssessment，导致：**
- 所有模型都得到固定的75.0分
- 都使用`expert_consultant`方法
- 缺少`model_type`、`peak_standard`等关键属性

## 📋 立即执行步骤

### 1. 运行诊断脚本
```bash
cd docs/features/T001-create-plans-20250612/design/fork/四重会议/todo2/promte/12-api管理/next/10
python 快速诊断脚本.py
```

### 2. 检查关键状态
查看输出中的：
- `LOGIC_DEPTH_AVAILABLE = ?` （应该是True）
- `使用真实CAP评估器` vs `使用简化版本CAP评估器`
- CAP评估器模块来源

### 3. 修复导入问题
如果`LOGIC_DEPTH_AVAILABLE = False`，检查：
```python
# 在 quality_assurance_guard.py 第28-50行
try:
    from logic_depth_detector import HighEfficiencyCAPQualityAssessment
    # 这里失败了
except ImportError as e:
    # 导致使用简化版本
```

### 4. 验证修复效果
修复后应该看到：
```
🧪 测试 deepseek-ai/DeepSeek-R1-0528:
CAP结果: 总分=84.6, 方法=logic_inquisitor, 模型类型=r1

🧪 测试 deepseek-ai/DeepSeek-V3-0324:  
CAP结果: 总分=61.8, 方法=efficiency_optimized, 模型类型=v3
```

## 🔧 关键文件位置
- **主要问题文件**: `tools/ace/src/api_management/core/quality_assurance_guard.py`
- **真实实现文件**: `tools/ace/src/api_management/core/logic_depth_detector.py`
- **测试入口**: `tools/ace/src/configuration_center/web_api.py`

## 📊 成功指标
1. ✅ `LOGIC_DEPTH_AVAILABLE = True`
2. ✅ R1模型使用`logic_inquisitor`方法，得分接近84.6
3. ✅ V3模型使用`efficiency_optimized`方法，得分接近61.8
4. ✅ 质量评估结果包含完整属性（model_type, peak_standard等）

## 🚫 避免的错误
- ❌ 不要重复造轮子，使用现有的真实实现
- ❌ 不要用`getattr()`掩盖属性缺失问题
- ❌ 不要修改简化版本，而是要修复导入问题

## 📞 如需帮助
参考完整的项目状态报告：`AI管理器质量评估系统修复项目状态报告.md`
