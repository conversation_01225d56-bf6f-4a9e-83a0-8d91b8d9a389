# 远程Docker测试标准指南

## 概述

本指南定义了在Windows开发环境中通过SSH连接到Linux Docker环境进行集成测试的标准化方法。该方法实现了完全自动化的测试环境，AI可以像本地开发一样直接执行测试，无需任何前置条件。

## 核心优势

- **零Docker依赖**：Windows环境无需安装Docker
- **完全自动化**：AI可直接运行测试，无需人工干预
- **环境隔离**：每次测试都是全新的Docker容器环境
- **多数据库支持**：支持PostgreSQL、Valkey、RabbitMQ等完整数据库栈
- **TestContainers兼容**：保持现有测试代码不变

---

## 人工与AI职责分工

### 👤 人工职责（一次性配置）

**仅需执行一次，后续无需人工参与**

#### 1. 远程服务器配置（5分钟）
```bash
# SSH登录到远程服务器
ssh <EMAIL>

# 上传并执行Docker API配置脚本
sudo ./tools/test-in-remote-docker/scripts/configure-docker-simple.sh
```

#### 2. SSH密钥配置（可选）
```bash
# 配置免密登录（推荐）
ssh-copy-id <EMAIL>
```

### 🤖 AI职责（完全自动化）

**AI可以完全自主执行，无需人工参与**

#### 1. 自动建立SSH隧道
```bash
# AI自动执行
ssh -N -L 2375:localhost:2375 <EMAIL> &
```

#### 2. 自动设置环境变量
```bash
# AI自动设置
set DOCKER_HOST=tcp://localhost:2375
set TESTCONTAINERS_RYUK_DISABLED=true
```

#### 3. 自动执行测试
```bash
# AI直接运行测试
cd xkongcloud-commons\xkongcloud-commons-uid
mvn exec:java -Dexec.mainClass=org.xkong.cloud.commons.uid.TestRunner
```

---

## 技术实现原理

### 架构图
```
Windows开发环境          SSH隧道           Linux Docker环境
┌─────────────────┐    ┌─────────────┐    ┌─────────────────┐
│   TestRunner    │───▶│ SSH Tunnel  │───▶│  Docker API     │
│   (AI执行)      │    │ :2375       │    │  :2375          │
└─────────────────┘    └─────────────┘    └─────────────────┘
         │                                          │
         ▼                                          ▼
┌─────────────────┐                        ┌─────────────────┐
│  TestContainers │                        │ PostgreSQL      │
│  Framework      │                        │ Valkey          │
│                 │                        │ RabbitMQ        │
└─────────────────┘                        └─────────────────┘
```

### 工作流程
1. **AI检测环境**：自动检查SSH隧道状态
2. **建立连接**：自动建立SSH隧道到远程Docker API
3. **环境配置**：自动设置TestContainers环境变量
4. **执行测试**：TestContainers自动管理远程Docker容器
5. **自动清理**：测试完成后自动清理所有资源

---

## 乱码问题解决方案

### 问题原因
Windows控制台默认使用GBK编码，而Java应用输出UTF-8编码，导致中文显示乱码。

### 解决方案
```bash
# AI执行测试前自动设置
chcp 65001
set JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8
mvn exec:java -Dexec.mainClass=org.xkong.cloud.commons.uid.TestRunner
```

**重要**：脚本文件本身必须使用英文，避免中文注释导致的编码问题。

---

## AI自动化脚本模板

### 完整自动化脚本
```bash
@echo off
echo ===== AI自动化远程Docker测试 =====

:: 1. 解决乱码问题
chcp 65001 >nul
set JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8

:: 2. 检查SSH隧道
echo 检查SSH隧道状态...
netstat -an | findstr :2375 >nul
if errorlevel 1 (
    echo 建立SSH隧道...
    start /min ssh -N -L 2375:localhost:2375 <EMAIL>
    timeout /t 3 >nul
)

:: 3. 设置环境变量
set DOCKER_HOST=tcp://localhost:2375
set TESTCONTAINERS_RYUK_DISABLED=true

:: 4. 执行测试
echo 开始执行远程Docker测试...
cd xkongcloud-commons\xkongcloud-commons-uid
mvn exec:java -Dexec.mainClass=org.xkong.cloud.commons.uid.TestRunner

echo ===== 测试完成 =====
```

---

## 使用指南

### AI一键执行（推荐）
```bash
# AI只需执行一条命令（默认测试UID库）
./tools/test-in-remote-docker/scripts/run-remote-docker-test.bat

# 测试其他项目示例
./tools/test-in-remote-docker/scripts/run-remote-docker-test.bat xkongcloud-service-center spring-boot
./tools/test-in-remote-docker/scripts/run-remote-docker-test.bat xkongcloud-business-internal-core spring-boot

# 查看帮助信息
./tools/test-in-remote-docker/scripts/run-remote-docker-test.bat help
```

### 手动执行步骤
```bash
# 1. 设置编码（解决乱码）
chcp 65001
set JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8

# 2. 建立SSH隧道
ssh -N -L 2375:localhost:2375 <EMAIL> &

# 3. 设置环境变量
set DOCKER_HOST=tcp://localhost:2375
set TESTCONTAINERS_RYUK_DISABLED=true

# 4. 运行测试
cd xkongcloud-commons\xkongcloud-commons-uid
mvn exec:java -Dexec.mainClass=org.xkong.cloud.commons.uid.TestRunner
```

### 检查清单
- [ ] 远程服务器已配置Docker API（一次性）：`sudo ./configure-docker-simple.sh`
- [ ] SSH隧道已建立：`netstat -an | findstr :2375`
- [ ] 环境变量已设置：`echo %DOCKER_HOST%`
- [ ] 编码已配置：中文显示正常

---

## 故障排除

| 问题 | 检查命令 | 解决方案 |
|------|---------|---------|
| SSH连接失败 | `ssh <EMAIL> "echo test"` | 检查网络和SSH配置 |
| 端口占用 | `netstat -an \| findstr :2375` | `taskkill /f /im ssh.exe` |
| Docker连接失败 | `curl http://localhost:2375/version` | 检查远程Docker服务 |
| 中文乱码 | `./tools/test-in-remote-docker/scripts/test-encoding.bat` | 运行编码测试脚本 |
| 数据库连接拒绝 | 容器启动后立即连接失败 | 添加连接重试和等待机制 |

## 连接时序问题优化

### 问题描述
远程Docker环境中，容器启动后可能需要额外时间完成初始化，立即连接可能失败。

### 优化方案

#### 1. 添加连接重试机制
```java
// 在PostgresTestContainer.java中添加重试逻辑
private DataSource getDataSourceWithRetry() {
    int maxRetries = 5;
    int retryDelay = 2000; // 2秒

    for (int i = 0; i < maxRetries; i++) {
        try {
            return getDataSource();
        } catch (Exception e) {
            if (i == maxRetries - 1) throw e;

            log.warn("数据库连接失败，{}秒后重试 ({}/{})",
                retryDelay/1000, i+1, maxRetries);
            Thread.sleep(retryDelay);
        }
    }
    return null;
}
```

#### 2. 容器健康检查
```java
// 等待容器完全就绪
container.waitingFor(Wait.forLogMessage(".*database system is ready to accept connections.*", 2)
    .withStartupTimeout(Duration.ofMinutes(2)));
```

#### 3. 连接池配置优化
```properties
# 在测试配置中添加
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.initialization-fail-timeout=60000
spring.datasource.hikari.validation-timeout=5000
```

#### 4. 详细优化指南
参考完整的时序优化文档：`docs/common/testing/remote-docker-timing-optimization.md`

---

## 最佳实践

- 使用SSH密钥认证，避免密码输入
- Docker API仅监听localhost，通过SSH隧道加密传输
- 定期清理过期的Docker镜像和容器

## 扩展支持

- **已支持**：PostgreSQL
- **计划支持**：Valkey、RabbitMQ
- **多环境**：sb.sn.cn（开发）、test.sn.cn（测试）

---

## 总结

### 核心优势
- **零Docker依赖**：Windows环境无需安装Docker
- **完全自动化**：AI一键执行，无需人工干预
- **环境隔离**：每次测试都是全新的Docker容器环境
- **多数据库支持**：支持PostgreSQL、Valkey、RabbitMQ等完整数据库栈
- **TestContainers兼容**：保持现有测试代码不变

### 实施要求
- **人工参与**：仅需一次性配置（5分钟）
- **AI自主**：完全自动化测试执行
- **技术成熟**：基于SSH隧道和TestContainers的稳定方案

### 适用场景
该方法已成为我们的**标准测试方式**，适用于：
- 所有需要集成测试的项目
- Windows开发环境
- 远程Docker环境部署
- 多数据库集成测试需求

### 文档结构
- `remote-docker-testing-guide.md`：完整标准指南（本文档）
- `remote-docker-timing-optimization.md`：连接时序优化详细指南
- `tools/test-in-remote-docker/PROJECT-EXAMPLES.md`：通用测试脚本项目配置示例

### 通用化支持（v2.0新特性）
测试脚本已升级为通用版本，支持测试所有XKongCloud子项目：

**支持的项目类型**：
- **库项目**：如 `xkongcloud-commons-uid`（使用自定义TestRunner）
- **Spring Boot服务**：如 `xkongcloud-service-center`、`xkongcloud-business-internal-core`
- **标准Maven项目**：支持标准Maven测试命令

**使用方式**：
```bash
# 语法：run-remote-docker-test.bat [PROJECT_PATH] [TEST_TYPE] [MAIN_CLASS] [ADDITIONAL_ARGS]

# 默认（向后兼容）
./tools/test-in-remote-docker/scripts/run-remote-docker-test.bat

# 测试Spring Boot项目
./tools/test-in-remote-docker/scripts/run-remote-docker-test.bat xkongcloud-service-center spring-boot

# 查看完整帮助和示例
./tools/test-in-remote-docker/scripts/run-remote-docker-test.bat help
```
