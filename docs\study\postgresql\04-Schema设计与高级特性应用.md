# PostgreSQL傻瓜式入门教程 - 第四部分：Schema设计与高级特性应用

## 前言

在前三部分中，我们学习了PostgreSQL的基础知识、Spring Boot集成方法、数据模型设计、JPA实体类映射以及Repository接口设计和高级查询技术。本部分将深入探讨Schema设计与管理以及PostgreSQL高级特性应用，这些是架构师和高级开发者需要掌握的重要内容。

## 1. Schema设计与管理

### 1.1 Schema的重要性

在PostgreSQL中，**Schema**是一个命名空间，它包含命名对象（如表、视图、索引、数据类型、函数和操作符）。使用Schema有以下优势：

> **通俗解释**：Schema就像是数据库中的文件夹系统，帮助你组织和管理数据库对象，避免混乱。想象一下，如果你把所有文件都放在电脑的根目录下，很快就会变得杂乱无章，而使用文件夹可以让一切井然有序。

1. **命名空间隔离**：不同Schema中可以有同名对象，避免命名冲突
   > **通俗解释**：就像不同城市可以有同名的街道，通过城市名来区分它们。例如，北京市的朝阳路和上海市的朝阳路是两条不同的街道。

2. **权限管理**：可以为不同Schema设置不同的访问权限
   > **通俗解释**：类似于给不同房间设置不同的门禁权限，比如财务部门的数据只有财务人员可以访问，而公共信息对所有人开放。

3. **组织结构清晰**：可以按业务功能或模块划分Schema
   > **通俗解释**：就像将家里的物品按用途分类存放，厨房用品放厨房，卧室用品放卧室，使得管理更有条理。

4. **简化迁移**：便于数据库结构的迁移和版本管理
   > **通俗解释**：类似于模块化的家具，便于搬家时拆卸和重新组装，或者只需要移动特定的模块而不影响其他部分。

5. **支持微服务架构**：为未来可能的微服务拆分提供基础
   > **通俗解释**：预先设计好接口，方便未来系统拆分，就像在建筑中预留电梯井和管道竖井，便于未来扩展。

### 1.2 Schema命名规范

在我们的项目中，我们采用以下**Schema命名规范**：

> **通俗解释**：命名规范就像是给街道命名的规则，让人一看名字就知道这条街属于哪个区域、有什么用途，使整个城市的街道系统更加清晰有序。

1. **业务Schema**：采用`<业务领域>_<可选子域>`格式
   - 例如：`user_management`表示用户管理业务领域
   - 其他例子：`identity_core`、`payment_processing`等
   > **通俗解释**：就像商业区的街道命名，例如"金融街-投资区"，一看就知道是金融业务中的投资相关区域。

2. **基础设施Schema**：采用`infra_<组件类型>`格式
   - 例如：`infra_uid`表示UID生成器基础设施组件
   - 其他例子：`infra_audit`、`infra_cache`等
   > **通俗解释**：类似于城市基础设施的命名，如"基础设施-供电系统"，表明这是支撑整个城市运行的基础组件。

3. **通用功能Schema**：采用`common_<功能类型>`格式
   - 例如：`common_config`表示系统配置通用功能
   - 其他例子：`common_logging`、`common_security`等
   > **通俗解释**：就像城市的公共设施命名，如"公共-图书馆"，表示这是所有人都可以使用的公共服务。

这种命名规范有助于：
- 清晰区分不同类型的数据和功能
- 提高数据库结构的可读性和可维护性
- 支持更精细的权限控制和安全管理
- 为未来的微服务架构演进提供基础

### 1.3 Schema创建与管理

#### 手动创建Schema

```sql
-- 创建Schema
-- 如果Schema不存在则创建，避免重复创建报错
-- 用户管理相关的Schema
CREATE SCHEMA IF NOT EXISTS user_management;
-- 分布式ID生成器相关的Schema
CREATE SCHEMA IF NOT EXISTS infra_uid;
-- 系统配置相关的Schema
CREATE SCHEMA IF NOT EXISTS common_config;

-- 设置Schema权限
-- 授予app_user用户对user_management Schema的使用权限
GRANT USAGE ON SCHEMA user_management TO app_user;
-- 授予app_user用户对user_management Schema中所有表的所有权限
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA user_management TO app_user;

-- 设置默认Schema搜索路径
-- 设置数据库默认的Schema搜索顺序，查询时会按此顺序查找表
-- 不指定Schema的查询会首先在user_management中查找，然后是common_config，最后是public
ALTER DATABASE xkong_main_db SET search_path TO user_management, common_config, public;
```

> **通俗解释**：
> - **CREATE SCHEMA**：创建一个新的Schema，就像在文件系统中创建一个新文件夹。
> - **IF NOT EXISTS**：如果不存在才创建，避免重复创建报错，就像检查文件夹是否已存在，不存在才创建。
> - **GRANT USAGE**：授予使用权限，就像给某人进入房间的钥匙，但不一定能动里面的东西。
> - **GRANT ALL PRIVILEGES**：授予所有权限，就像给某人完全访问权限，可以在房间内做任何事情。
> - **search_path**：搜索路径，就像设置默认查找顺序，类似于电脑查找文件时先查找哪个文件夹，再查找哪个文件夹。

#### 在Spring Boot中自动创建Schema

在Spring Boot应用中，可以通过以下方式自动创建Schema：

**方式一：使用JdbcTemplate**

```java
/**
 * Schema初始化器
 * 在应用启动时自动创建所需的Schema
 * 实现InitializingBean接口，在Spring容器初始化Bean后执行afterPropertiesSet方法
 */
@Component
public class SchemaInitializer implements InitializingBean {

    /**
     * Spring JDBC模板，用于执行SQL语句
     */
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * KV参数服务，用于获取配置参数
     */
    @Autowired
    private KVParamService kvParamService;

    /**
     * 在Bean属性设置完成后执行
     * 创建所需的Schema
     *
     * @throws Exception 如果创建Schema过程中发生异常
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        // 从KV参数服务获取Schema列表
        // 如果参数不存在，使用默认值"user_management,common_config,infra_uid"
        String schemaListStr = kvParamService.getParam("postgresql.schema.list", "user_management,common_config,infra_uid");
        // 将逗号分隔的字符串转换为列表
        List<String> schemas = Arrays.asList(schemaListStr.split(","));

        // 遍历Schema列表，创建每个Schema
        for (String schema : schemas) {
            try {
                // 执行创建Schema的SQL语句
                // 使用IF NOT EXISTS子句避免Schema已存在时报错
                jdbcTemplate.execute("CREATE SCHEMA IF NOT EXISTS " + schema);
                // 记录成功日志
                log.info("Schema created or already exists: {}", schema);
            } catch (Exception e) {
                // 记录失败日志，但不中断应用启动
                log.error("Failed to create schema: {}", schema, e);
            }
        }
    }
}
```

**方式二：使用Flyway或Liquibase**

对于生产环境，建议使用数据库迁移工具（如Flyway或Liquibase）来管理Schema的创建和变更。

**Flyway示例**：

```java
/**
 * Flyway配置类
 * 配置Flyway数据库迁移工具
 * Flyway用于管理数据库结构的版本控制和迁移
 */
@Configuration  // 标记为Spring配置类
public class FlywayConfig {

    /**
     * 创建并配置Flyway实例
     *
     * @param dataSource 数据源，由Spring自动注入
     * @return 配置好的Flyway实例
     */
    @Bean
    public Flyway flyway(DataSource dataSource) {
        // 配置Flyway实例
        Flyway flyway = Flyway.configure()
                // 设置数据源
                .dataSource(dataSource)
                // 设置迁移脚本的位置，在classpath的db/migration目录下
                .locations("classpath:db/migration")
                // 如果数据库不是空的，在首次迁移时自动创建baseline
                .baselineOnMigrate(true)
                // 指定要管理的Schema列表
                .schemas("user_management", "common_config", "infra_uid")
                // 加载配置，创建Flyway实例
                .load();

        // 执行迁移，应用所有未应用的迁移脚本
        flyway.migrate();

        // 返回Flyway实例
        return flyway;
    }
}
```

在`src/main/resources/db/migration`目录下创建SQL迁移脚本：

```sql
-- V1__Create_Schemas.sql
-- Flyway迁移脚本，版本号为1，用于创建初始Schema
-- 文件名格式：V<版本号>__<描述>.sql，Flyway根据版本号顺序执行

-- 创建用户管理Schema
CREATE SCHEMA IF NOT EXISTS user_management;
-- 创建系统配置Schema
CREATE SCHEMA IF NOT EXISTS common_config;
-- 创建分布式ID生成器Schema
CREATE SCHEMA IF NOT EXISTS infra_uid;

-- V2__Create_User_Tables.sql
-- Flyway迁移脚本，版本号为2，用于创建用户表
-- 在V1脚本执行成功后才会执行此脚本

-- 创建用户表，指定Schema为user_management
CREATE TABLE user_management.user (
    user_id BIGINT PRIMARY KEY,  -- 用户ID，使用BIGINT类型存储UID生成器生成的ID
    username VARCHAR(100) NOT NULL UNIQUE,  -- 用户名，不允许为空且必须唯一
    email VARCHAR(255) NOT NULL UNIQUE,  -- 电子邮箱，不允许为空且必须唯一
    -- 其他字段...
    -- 在实际应用中，这里会有更多字段，如密码哈希、状态、创建时间等
);
```

### 1.4 Schema权限管理

PostgreSQL提供了强大的权限管理系统，可以为不同Schema设置不同的访问权限：

```sql
-- 创建角色
-- 创建普通应用用户角色，具有登录权限和密码
CREATE ROLE app_user LOGIN PASSWORD 'password';
-- 创建管理员角色，具有登录权限和密码
CREATE ROLE app_admin LOGIN PASSWORD 'admin_password';
-- 创建只读角色，具有登录权限和密码
CREATE ROLE readonly LOGIN PASSWORD 'readonly_password';

-- 设置Schema权限
-- 授予app_user用户对user_management Schema的使用权限
GRANT USAGE ON SCHEMA user_management TO app_user;
-- 授予app_user用户对user_management Schema中所有表的所有权限（增删改查）
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA user_management TO app_user;
-- 授予app_user用户对user_management Schema中所有序列的使用权限（用于自增ID）
GRANT USAGE ON ALL SEQUENCES IN SCHEMA user_management TO app_user;

-- 授予app_user用户对common_config Schema的使用权限
GRANT USAGE ON SCHEMA common_config TO app_user;
-- 授予app_user用户对common_config Schema中所有表的查询权限（只读）
GRANT SELECT ON ALL TABLES IN SCHEMA common_config TO app_user;

-- 设置只读权限
-- 授予readonly用户对user_management Schema的使用权限
GRANT USAGE ON SCHEMA user_management TO readonly;
-- 授予readonly用户对user_management Schema中所有表的查询权限（只读）
GRANT SELECT ON ALL TABLES IN SCHEMA user_management TO readonly;

-- 设置管理员权限
-- 授予app_admin用户对整个数据库的所有权限
GRANT ALL PRIVILEGES ON DATABASE xkong_main_db TO app_admin;
-- 授予app_admin用户对user_management Schema中所有表的所有权限
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA user_management TO app_admin;
-- 授予app_admin用户对common_config Schema中所有表的所有权限
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA common_config TO app_admin;
-- 授予app_admin用户对infra_uid Schema中所有表的所有权限
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA infra_uid TO app_admin;
```

### 1.5 Schema演进策略

随着业务的发展，数据库结构也需要不断演进。以下是Schema演进的几种策略：

1. **增量变更**：使用数据库迁移工具（如Flyway或Liquibase）管理增量变更
2. **版本控制**：将数据库结构纳入版本控制系统
3. **兼容性保证**：确保变更向后兼容，不破坏现有功能
4. **分阶段实施**：重大变更分阶段实施，减少风险
5. **回滚计划**：为每个变更准备回滚计划

## 2. PostgreSQL高级特性应用

### 2.1 JSONB数据类型

> **[PostgreSQL特有功能]** JSONB是PostgreSQL特有的二进制JSON数据类型，提供了高效的存储和查询JSON数据的能力。虽然其他数据库也支持JSON，但PostgreSQL的JSONB实现提供了更丰富的操作符和索引支持，使其在处理JSON数据时性能更优。

PostgreSQL提供了强大的**JSONB数据类型**，支持存储和查询JSON数据：

> **通俗解释**：JSONB是PostgreSQL提供的一种特殊数据类型，用于存储JSON格式的数据，就像能够存储结构化文档的容器。与普通文本相比，JSONB经过二进制处理，支持索引和高效查询，就像一本带有详细索引的书，可以快速找到特定内容。

```sql
-- 创建带JSONB字段的表
-- JSONB是PostgreSQL提供的二进制JSON数据类型，支持索引和高效查询
CREATE TABLE user_management.user_profile (
    profile_id BIGINT PRIMARY KEY,  -- 配置文件ID，主键
    user_id BIGINT NOT NULL,  -- 用户ID，外键
    profile_data JSONB,  -- 用户配置文件数据，使用JSONB类型存储
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES user_management.user(user_id)  -- 外键约束，确保user_id存在于user表中
);

-- 插入JSONB数据
-- 直接使用JSON字符串插入JSONB字段
INSERT INTO user_management.user_profile (profile_id, user_id, profile_data)
VALUES (1, 1, '{"address": {"city": "Beijing", "country": "China"}, "interests": ["reading", "coding", "hiking"]}');

-- 查询JSONB数据
-- 使用->操作符获取JSON对象，使用->>操作符获取JSON值（转为文本）
-- 此查询返回用户地址中的城市字段
SELECT profile_data->'address'->>'city' FROM user_management.user_profile WHERE profile_id = 1;

-- 使用JSONB操作符
-- @>操作符：包含操作符，检查左侧JSONB是否包含右侧JSONB的所有键值对
-- 此查询查找兴趣包含"coding"的用户
SELECT * FROM user_management.user_profile WHERE profile_data @> '{"interests": ["coding"]}';
-- ?操作符：键存在操作符，检查JSONB是否包含指定的键
-- 此查询查找包含address字段的用户
SELECT * FROM user_management.user_profile WHERE profile_data ? 'address';

-- 创建JSONB索引
-- 使用GIN索引类型，适用于JSONB数据的高效查询
-- 支持@>、?、?|、?&等操作符的高效查询
-- [PostgreSQL特有功能] GIN(Generalized Inverted Index)是PostgreSQL特有的索引类型，专为处理包含多个值的列设计
CREATE INDEX idx_profile_data ON user_management.user_profile USING GIN (profile_data);
```

> **通俗解释**：
> - **->操作符**：获取JSON对象，就像从一个大箱子里取出一个小盒子。
> - **->>操作符**：获取JSON值并转为文本，就像从盒子里取出物品并展示出来。
> - **@>操作符**：包含操作符，检查是否包含特定内容，就像检查一个购物袋是否包含特定商品。
> - **?操作符**：键存在操作符，检查是否有特定的键，就像检查一本书是否有特定的章节。
> - **GIN索引**：一种特殊的索引类型，适用于包含多个值的列，就像一本书的多维度索引，可以从多个角度快速查找内容。

在JPA中使用JSONB：

```java
/**
 * 用户配置文件实体类
 * 映射到user_management.user_profile表
 * 演示如何在JPA中使用JSONB数据类型
 */
@Entity  // 标记为JPA实体类
@Table(name = "user_profile", schema = "user_management")  // 指定表名和Schema
public class UserProfile {
    /**
     * 配置文件ID，主键
     */
    @Id  // 标记为主键
    private Long profileId;

    /**
     * 用户ID，外键
     * 关联到user表的user_id字段
     */
    @Column(name = "user_id")  // 指定列名
    private Long userId;

    /**
     * 用户配置文件数据，使用JSONB类型存储
     * 使用Hibernate的@Type注解指定类型为jsonb
     * 使用columnDefinition属性指定列类型为jsonb
     * 在Java中使用String类型表示JSON数据
     */
    @Type(type = "jsonb")  // 指定Hibernate类型为jsonb
    @Column(name = "profile_data", columnDefinition = "jsonb")  // 指定列名和列类型
    private String profileData;  // 使用String类型存储JSON数据

    // 构造函数、Getter和Setter方法...
    // 这里省略了构造函数和访问器方法的实现
}
```

在Repository中查询JSONB数据：

```java
/**
 * 用户配置文件数据访问接口
 * 演示如何在Repository中查询JSONB数据
 */
@Repository  // 标记为Spring Repository组件
public interface UserProfileRepository extends JpaRepository<UserProfile, Long> {
    /**
     * 根据JSONB内容查询用户配置文件
     * 使用@>操作符查询包含指定JSON内容的记录
     *
     * @param criteria JSON查询条件，例如：{"interests": ["coding"]}
     * @return 匹配条件的用户配置文件列表
     */
    @Query(value = "SELECT * FROM user_management.user_profile WHERE profile_data @> CAST(:criteria AS jsonb)", nativeQuery = true)
    List<UserProfile> findByProfileDataContaining(@Param("criteria") String criteria);

    /**
     * 根据兴趣查询用户配置文件
     * 使用->>操作符获取interests字段的值，然后使用LIKE进行模糊匹配
     *
     * @param interest 兴趣关键词
     * @return 兴趣包含指定关键词的用户配置文件列表
     */
    @Query(value = "SELECT * FROM user_management.user_profile WHERE profile_data->>'interests' LIKE %:interest%", nativeQuery = true)
    List<UserProfile> findByInterest(@Param("interest") String interest);

    /**
     * 根据城市查询用户配置文件
     * 使用->和->>操作符组合获取嵌套JSON对象中的值
     *
     * @param city 城市名称
     * @return 地址中城市匹配的用户配置文件列表
     */
    @Query(value = "SELECT * FROM user_management.user_profile WHERE profile_data->'address'->>'city' = :city", nativeQuery = true)
    List<UserProfile> findByCity(@Param("city") String city);
}
```

### 2.2 数组类型

> **[PostgreSQL特有功能]** PostgreSQL对数组类型的原生支持是其特有功能，包括丰富的数组操作符和函数。虽然一些其他数据库也提供了类似功能，但PostgreSQL的数组实现更为完整和强大，支持多维数组、数组索引和丰富的数组操作。

PostgreSQL支持**数组类型**，可以在一个字段中存储多个值：

> **通俗解释**：数组类型就像是一个能装多个相同类型物品的盒子，而不是只能装一个物品的普通盒子。例如，一个产品可以有多个标签，一个用户可以有多个电话号码，使用数组可以将这些相关的多个值存储在一个字段中。

```sql
-- 创建带数组字段的表
-- PostgreSQL支持数组类型，可以在一个字段中存储多个值
CREATE TABLE product_catalog.product_tag (
    product_id BIGINT PRIMARY KEY,  -- 产品ID，主键
    tags TEXT[]  -- 标签数组，使用TEXT[]类型存储多个文本标签
);

-- 插入数组数据
-- 使用ARRAY构造函数创建文本数组
INSERT INTO product_catalog.product_tag (product_id, tags)
VALUES (1, ARRAY['electronics', 'smartphone', 'android']);  -- 插入包含三个标签的数组

-- 查询数组数据
-- 使用ANY操作符查询数组中包含特定元素的记录
-- 此查询查找标签包含'smartphone'的产品
SELECT * FROM product_catalog.product_tag WHERE 'smartphone' = ANY(tags);

-- 使用@>操作符查询数组包含另一个数组的所有元素的记录
-- 此查询查找同时包含'electronics'和'smartphone'标签的产品
SELECT * FROM product_catalog.product_tag WHERE tags @> ARRAY['electronics', 'smartphone'];

-- 数组操作
-- array_length函数：获取数组长度，第二个参数表示维度（1表示第一维）
-- 此查询返回产品ID为1的产品的标签数量
SELECT array_length(tags, 1) FROM product_catalog.product_tag WHERE product_id = 1;

-- unnest函数：将数组展开为多行
-- 此查询将产品ID为1的产品的标签数组展开为多行
SELECT unnest(tags) FROM product_catalog.product_tag WHERE product_id = 1;

-- 其他常用数组操作
-- 数组索引（PostgreSQL数组索引从1开始）
SELECT tags[1] FROM product_catalog.product_tag WHERE product_id = 1;  -- 获取第一个标签

-- 数组切片
SELECT tags[1:2] FROM product_catalog.product_tag WHERE product_id = 1;  -- 获取前两个标签

-- 数组连接
SELECT array_cat(tags, ARRAY['new_tag']) FROM product_catalog.product_tag WHERE product_id = 1;  -- 连接两个数组
```

> **通俗解释**：
> - **TEXT[]**：文本数组类型，就像一个可以存放多个文本字符串的容器。
> - **ARRAY构造函数**：创建数组的函数，就像把多个物品装进一个盒子。
> - **ANY操作符**：检查数组中是否包含特定元素，就像在一堆书中查找特定的一本书。
> - **@>操作符**：检查一个数组是否包含另一个数组的所有元素，就像检查一个书架是否包含特定的几本书。
> - **array_length**：获取数组长度，就像数一数盒子里有多少个物品。
> - **unnest**：将数组展开为多行，就像把盒子里的物品一个一个拿出来单独展示。
> - **数组索引**：访问数组中特定位置的元素，注意PostgreSQL的数组索引从1开始，而不是从0开始。

在JPA中使用数组：

```java
/**
 * 产品标签实体类
 * 映射到product_catalog.product_tag表
 * 演示如何在JPA中使用数组类型
 */
@Entity  // 标记为JPA实体类
@Table(name = "product_tag", schema = "product_catalog")  // 指定表名和Schema
public class ProductTag {
    /**
     * 产品ID，主键
     */
    @Id  // 标记为主键
    private Long productId;

    /**
     * 标签数组
     * 使用Hibernate的@Type注解指定类型为string-array
     * 使用columnDefinition属性指定列类型为text[]
     * 在Java中使用String[]类型表示文本数组
     */
    @Type(type = "string-array")  // 指定Hibernate类型为string-array
    @Column(name = "tags", columnDefinition = "text[]")  // 指定列名和列类型
    private String[] tags;  // 使用String[]类型存储文本数组

    /**
     * 默认构造函数
     * JPA要求实体类有一个无参构造函数
     */
    public ProductTag() {
    }

    /**
     * 带参数的构造函数
     *
     * @param productId 产品ID
     * @param tags 标签数组
     */
    public ProductTag(Long productId, String[] tags) {
        this.productId = productId;
        this.tags = tags;
    }

    // Getter和Setter方法

    /**
     * 获取产品ID
     *
     * @return 产品ID
     */
    public Long getProductId() {
        return productId;
    }

    /**
     * 设置产品ID
     *
     * @param productId 产品ID
     */
    public void setProductId(Long productId) {
        this.productId = productId;
    }

    /**
     * 获取标签数组
     *
     * @return 标签数组
     */
    public String[] getTags() {
        return tags;
    }

    /**
     * 设置标签数组
     *
     * @param tags 标签数组
     */
    public void setTags(String[] tags) {
        this.tags = tags;
    }
}
```

### 2.3 全文搜索

> **[PostgreSQL特有功能]** PostgreSQL的全文搜索功能是其特有的高级特性，包括专门的TSVECTOR和TSQUERY数据类型、全文搜索索引和丰富的操作符。虽然其他数据库也提供全文搜索功能，但PostgreSQL的实现更为灵活和强大，支持多语言、词干提取、排名和高亮等高级功能。

PostgreSQL提供了强大的**全文搜索**功能：

> **通俗解释**：全文搜索就像是一个智能的搜索引擎，不仅能找到完全匹配的词，还能找到相关的词、同义词、不同形式的词（如单复数、时态变化等）。它比简单的LIKE查询更智能、更高效，就像在图书馆使用专业的检索系统，而不是手动翻阅每本书。

```sql
-- 创建带全文搜索字段的表
-- 使用TSVECTOR类型存储全文搜索索引
CREATE TABLE product_catalog.product (
    product_id BIGINT PRIMARY KEY,  -- 产品ID，主键
    name VARCHAR(200) NOT NULL,  -- 产品名称，不允许为空
    description TEXT,  -- 产品描述，允许为空
    search_vector TSVECTOR  -- 全文搜索向量，存储预处理后的文本索引
);

-- 创建全文搜索索引
-- 使用GIN索引类型，适用于全文搜索
-- GIN索引支持快速的全文搜索查询
CREATE INDEX idx_product_search ON product_catalog.product USING GIN (search_vector);

-- 创建触发器函数，自动更新search_vector
-- 当产品名称或描述更新时，自动更新搜索向量
CREATE OR REPLACE FUNCTION product_catalog.product_search_vector_update() RETURNS TRIGGER AS $$
BEGIN
    -- 使用to_tsvector函数将文本转换为TSVECTOR类型
    -- 'english'参数指定使用英语词干提取和停用词过滤
    -- 连接产品名称和描述，如果描述为NULL则使用空字符串
    NEW.search_vector = to_tsvector('english', NEW.name || ' ' || COALESCE(NEW.description, ''));
    RETURN NEW;  -- 返回修改后的记录
END;
$$ LANGUAGE plpgsql;  -- 使用PL/pgSQL语言

-- 创建触发器，在插入或更新记录前自动更新search_vector
CREATE TRIGGER product_search_vector_update
BEFORE INSERT OR UPDATE ON product_catalog.product  -- 在插入或更新前触发
FOR EACH ROW  -- 对每一行记录执行
EXECUTE FUNCTION product_catalog.product_search_vector_update();  -- 执行触发器函数

-- 全文搜索查询
-- 使用@@操作符进行全文搜索匹配
-- to_tsquery函数将查询字符串转换为TSQUERY类型
-- 'smartphone & android'表示同时包含'smartphone'和'android'的记录
SELECT * FROM product_catalog.product
WHERE search_vector @@ to_tsquery('english', 'smartphone & android');

-- 其他常用全文搜索操作
-- 使用ts_rank函数对搜索结果进行排序，按相关性降序排列
SELECT *, ts_rank(search_vector, to_tsquery('english', 'smartphone & android')) AS rank
FROM product_catalog.product
WHERE search_vector @@ to_tsquery('english', 'smartphone & android')
ORDER BY rank DESC;

-- 使用ts_headline函数高亮显示匹配的文本
SELECT product_id, name,
       ts_headline('english', description, to_tsquery('english', 'smartphone & android')) AS highlighted_description
FROM product_catalog.product
WHERE search_vector @@ to_tsquery('english', 'smartphone & android');
```

> **通俗解释**：
> - **TSVECTOR**：全文搜索的向量类型，存储预处理后的文本索引，就像书的索引页，记录了每个关键词出现的位置。
> - **GIN索引**：适用于全文搜索的索引类型，就像一本书的详细索引，可以快速找到包含特定词的页面。
> - **触发器(Trigger)**：在特定数据库事件发生时自动执行的函数，就像设置了自动响应机制，当某事发生时自动执行预定的操作。
> - **to_tsvector**：将文本转换为搜索向量的函数，就像将一本书的内容处理成索引格式。
> - **to_tsquery**：将查询字符串转换为搜索查询的函数，就像将用户的搜索词转换成索引系统能理解的格式。
> - **@@操作符**：全文搜索匹配操作符，检查文档是否匹配查询，就像检查一本书是否包含特定主题。
> - **ts_rank**：计算搜索结果相关性的函数，就像给搜索结果打分，决定哪些结果更符合用户需求。

在Spring Boot中使用全文搜索：

```java
/**
 * 产品数据访问接口
 * 演示如何在Spring Boot中使用PostgreSQL全文搜索功能
 */
@Repository  // 标记为Spring Repository组件
public interface ProductRepository extends JpaRepository<Product, Long> {
    /**
     * 全文搜索产品
     * 使用PostgreSQL的全文搜索功能查询产品
     *
     * @param query 搜索查询字符串，格式如"word1 & word2"（AND）或"word1 | word2"（OR）
     * @return 匹配查询条件的产品列表
     */
    @Query(value = "SELECT * FROM product_catalog.product WHERE search_vector @@ to_tsquery('english', :query)", nativeQuery = true)
    List<Product> fullTextSearch(@Param("query") String query);

    /**
     * 带排序的全文搜索产品
     * 使用PostgreSQL的全文搜索功能查询产品，并按相关性排序
     *
     * @param query 搜索查询字符串，格式如"word1 & word2"（AND）或"word1 | word2"（OR）
     * @return 匹配查询条件的产品列表，按相关性降序排序
     */
    @Query(value = "SELECT * FROM product_catalog.product WHERE search_vector @@ to_tsquery('english', :query) ORDER BY ts_rank(search_vector, to_tsquery('english', :query)) DESC", nativeQuery = true)
    List<Product> fullTextSearchWithRanking(@Param("query") String query);

    /**
     * 带高亮的全文搜索产品
     * 使用PostgreSQL的全文搜索功能查询产品，并高亮显示匹配的文本
     *
     * @param query 搜索查询字符串，格式如"word1 & word2"（AND）或"word1 | word2"（OR）
     * @return 包含产品ID、名称和高亮描述的Map列表
     */
    @Query(value = "SELECT product_id, name, ts_headline('english', description, to_tsquery('english', :query)) AS highlighted_description FROM product_catalog.product WHERE search_vector @@ to_tsquery('english', :query)", nativeQuery = true)
    List<Map<String, Object>> fullTextSearchWithHighlighting(@Param("query") String query);

    /**
     * 模糊全文搜索产品
     * 使用PostgreSQL的全文搜索功能进行模糊查询，自动将空格转换为OR操作符
     *
     * @param query 搜索查询字符串，如"smartphone android"
     * @return 匹配查询条件的产品列表
     */
    @Query(value = "SELECT * FROM product_catalog.product WHERE search_vector @@ to_tsquery('english', regexp_replace(:query, '\\s+', ' | ', 'g'))", nativeQuery = true)
    List<Product> fuzzyFullTextSearch(@Param("query") String query);
}
```

### 2.4 分布式ID生成

在我们的项目中，我们使用基于PostgreSQL的**百度UID生成器**来生成分布式唯一ID。以下是相关表结构：

> **通俗解释**：分布式ID生成是在分布式系统中生成全局唯一ID的技术，就像在多个图书馆分支之间确保每本书有全球唯一的编号，即使不同图书馆同时编号也不会重复。百度UID生成器是一种高效的分布式ID生成方案，能够生成有序、高性能、全局唯一的ID。

```sql
-- 创建Schema
-- 用于存储分布式ID生成器相关的表
CREATE SCHEMA IF NOT EXISTS infra_uid;

-- 创建实例注册表
-- 用于注册和跟踪应用实例
CREATE TABLE infra_uid.instance_registry (
    instance_unique_id BIGSERIAL PRIMARY KEY,  -- 实例唯一ID，自增主键
    application_name VARCHAR(100) NOT NULL,  -- 应用名称，不允许为空
    environment VARCHAR(50) NOT NULL,  -- 环境名称（如dev、test、prod），不允许为空
    instance_group VARCHAR(100),  -- 实例组名称，允许为空
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',  -- 实例状态，默认为ACTIVE
    first_registered_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 首次注册时间，默认为当前时间
    last_seen_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 最后活跃时间，默认为当前时间
    custom_metadata JSONB  -- 自定义元数据，使用JSONB类型存储
);

-- 创建工作机器ID分配表
-- 用于分配和管理工作机器ID
CREATE TABLE infra_uid.worker_id_assignment (
    worker_id BIGINT PRIMARY KEY,  -- 工作机器ID，主键
    assigned_instance_unique_id BIGINT,  -- 分配给的实例ID，外键
    assignment_status VARCHAR(20) NOT NULL DEFAULT 'AVAILABLE',  -- 分配状态，默认为AVAILABLE
    assigned_at TIMESTAMP,  -- 分配时间，允许为空
    lease_duration_seconds INT DEFAULT 300,  -- 租约时长（秒），默认为300秒
    lease_expires_at TIMESTAMP,  -- 租约过期时间，允许为空
    last_heartbeat_at TIMESTAMP,  -- 最后心跳时间，允许为空
    CONSTRAINT fk_instance FOREIGN KEY (assigned_instance_unique_id) REFERENCES infra_uid.instance_registry(instance_unique_id)  -- 外键约束，确保assigned_instance_unique_id存在于instance_registry表中
);

-- 创建索引
-- 为应用名称和环境创建索引，提高按应用和环境查询的性能
CREATE INDEX idx_instance_app_env ON infra_uid.instance_registry(application_name, environment);
-- 为分配状态创建索引，提高按状态查询的性能
CREATE INDEX idx_worker_status ON infra_uid.worker_id_assignment(assignment_status);
-- 为分配给的实例ID创建索引，提高按实例查询的性能
CREATE INDEX idx_worker_instance ON infra_uid.worker_id_assignment(assigned_instance_unique_id);

-- 创建心跳更新函数
-- 用于更新实例的最后活跃时间和工作机器ID的租约
CREATE OR REPLACE FUNCTION infra_uid.update_heartbeat(p_instance_id BIGINT, p_worker_id BIGINT) RETURNS VOID AS $$
BEGIN
    -- 更新实例的最后活跃时间
    UPDATE infra_uid.instance_registry
    SET last_seen_at = CURRENT_TIMESTAMP
    WHERE instance_unique_id = p_instance_id;

    -- 更新工作机器ID的最后心跳时间和租约过期时间
    UPDATE infra_uid.worker_id_assignments
    SET last_heartbeat_at = CURRENT_TIMESTAMP,
        lease_expires_at = CURRENT_TIMESTAMP + (lease_duration_seconds * INTERVAL '1 second')
    WHERE worker_id = p_worker_id
      AND assigned_instance_unique_id = p_instance_id;
END;
$$ LANGUAGE plpgsql;
```

> **通俗解释**：
> - **BIGSERIAL**：自增的大整数类型，就像自动递增的编号，每次插入新记录时自动加1。
> - **实例注册表**：记录所有应用实例的信息，就像图书馆总部记录所有分馆的信息。
> - **工作机器ID**：每个应用实例的唯一标识，就像每个图书馆分馆的唯一编号。
> - **租约(lease)**：临时分配的使用权，有过期时间，就像借书的期限，到期需要续借或归还。
> - **心跳(heartbeat)**：定期发送的信号，表明实例仍然活跃，就像定期打卡，证明你还在工作。

在Spring Boot中使用UID生成器：

```java
/**
 * UID生成器配置类
 * 配置基于PostgreSQL的百度UID生成器
 * 使用持久化实例管理和工作机器ID分配
 */
@Configuration  // 标记为Spring配置类
@EnableScheduling  // 启用定时任务，用于心跳和租约续期
@DependsOn("kvParamService")  // 依赖于kvParamService，确保其先初始化
public class UidGeneratorConfig {

    /**
     * KV参数服务，用于获取配置参数
     */
    @Autowired
    private KVParamService kvParamService;

    /**
     * Spring JDBC模板，用于执行SQL语句
     */
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Spring事务模板，用于事务管理
     */
    @Autowired
    private TransactionTemplate transactionTemplate;

    /**
     * 集群ID，从配置文件中注入
     */
    @Value("${xkong.kv.cluster-id}")
    private String clusterId;

    /**
     * 创建持久化实例管理器
     * 用于管理应用实例的注册和心跳
     *
     * @return 配置好的PersistentInstanceManager实例
     */
    @Bean
    public PersistentInstanceManager persistentInstanceManager() {
        // 使用集群ID作为应用名称
        String appName = clusterId;
        // 从KV参数服务获取环境名称，默认为"default"
        String environment = kvParamService.getParam("uid.instance.environment", "default");
        // 其他参数...
        // 实际应用中可能还有更多参数，如实例组、心跳间隔等

        // 创建并返回PersistentInstanceManager实例
        return new PersistentInstanceManager(
            jdbcTemplate,  // 用于数据库操作
            transactionTemplate,  // 用于事务管理
            appName,  // 应用名称
            environment,  // 环境名称
            // 其他参数...
            // 实际应用中可能还有更多参数
        );
    }

    /**
     * 创建工作机器ID分配器
     * 用于为应用实例分配唯一的工作机器ID
     *
     * @param persistentInstanceManager 持久化实例管理器
     * @return 配置好的WorkerIdAssigner实例
     */
    @Bean
    public WorkerIdAssigner workerIdAssigner(PersistentInstanceManager persistentInstanceManager) {
        // 从KV参数服务获取租约时长，默认为300秒
        int leaseDuration = Integer.parseInt(kvParamService.getParam("uid.worker.lease-duration-seconds", "300"));

        // 创建并返回PersistentInstanceWorkerIdAssigner实例
        return new PersistentInstanceWorkerIdAssigner(
            jdbcTemplate,  // 用于数据库操作
            transactionTemplate,  // 用于事务管理
            persistentInstanceManager,  // 持久化实例管理器
            leaseDuration  // 租约时长（秒）
        );
    }

    /**
     * 创建UID生成器
     * 用于生成分布式唯一ID
     *
     * @param workerIdAssigner 工作机器ID分配器
     * @return 配置好的UidGenerator实例
     */
    @Bean
    public UidGenerator uidGenerator(WorkerIdAssigner workerIdAssigner) {
        // 创建缓存UID生成器
        CachedUidGenerator cachedUidGenerator = new CachedUidGenerator();
        // 设置工作机器ID分配器
        cachedUidGenerator.setWorkerIdAssigner(workerIdAssigner);

        // 从KV参数服务获取UID生成器参数
        // 设置起始时间（纪元）
        cachedUidGenerator.setEpochStr(kvParamService.getParam("uid.epochStr"));
        // 设置时间位数
        cachedUidGenerator.setTimeBits(Integer.parseInt(kvParamService.getParam("uid.timeBits")));
        // 设置工作机器ID位数
        cachedUidGenerator.setWorkerBits(Integer.parseInt(kvParamService.getParam("uid.workerBits")));
        // 设置序列号位数
        cachedUidGenerator.setSeqBits(Integer.parseInt(kvParamService.getParam("uid.seqBits")));

        // 其他参数...
        // 实际应用中可能还有更多参数，如RingBuffer大小、填充因子等

        // 返回配置好的UID生成器
        return cachedUidGenerator;
    }
}
```

## 3. 下一步学习计划

在掌握了Schema设计与管理以及PostgreSQL高级特性应用后，你可以继续学习以下内容：

1. **性能优化**：如何优化PostgreSQL查询性能
2. **事务管理**：如何在Spring Boot中管理事务
3. **高可用配置**：如何配置PostgreSQL高可用集群
4. **监控与维护**：如何监控和维护PostgreSQL数据库

这些内容将在后续教程中详细介绍。

## 语法规则总结

为了帮助你快速掌握PostgreSQL Schema设计与高级特性应用的语法，以下是本章涉及的主要语法规则总结：

### 1. Schema管理语法

#### 1.1 创建Schema语法

```sql
-- 基本语法
CREATE SCHEMA [IF NOT EXISTS] schema_name;

-- 示例
CREATE SCHEMA IF NOT EXISTS user_management;
CREATE SCHEMA IF NOT EXISTS infra_uid;
CREATE SCHEMA IF NOT EXISTS common_config;
```

> **语法说明**：
> - `CREATE SCHEMA`：创建Schema命令
> - `IF NOT EXISTS`：可选，如果Schema已存在则不会报错
> - `schema_name`：Schema名称，应遵循命名规范

#### 1.2 Schema权限管理语法

```sql
-- 基本语法
GRANT privilege ON SCHEMA schema_name TO role_name;
GRANT privilege ON ALL TABLES IN SCHEMA schema_name TO role_name;

-- 示例
-- 授予使用权限
GRANT USAGE ON SCHEMA user_management TO app_user;
-- 授予所有表的所有权限
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA user_management TO app_user;
-- 授予序列使用权限
GRANT USAGE ON ALL SEQUENCES IN SCHEMA user_management TO app_user;
```

> **语法说明**：
> - `GRANT`：授予权限命令
> - `privilege`：权限类型，如USAGE、SELECT、INSERT、UPDATE、DELETE、ALL PRIVILEGES等
> - `ON SCHEMA`：指定Schema
> - `ON ALL TABLES IN SCHEMA`：指定Schema中的所有表
> - `TO`：指定角色或用户

#### 1.3 设置Schema搜索路径语法

```sql
-- 基本语法
SET search_path TO schema1, schema2, ...;

-- 示例
-- 设置当前会话的搜索路径
SET search_path TO user_management, common_config, public;

-- 设置数据库默认搜索路径
ALTER DATABASE database_name SET search_path TO schema1, schema2, ...;

-- 示例
ALTER DATABASE xkong_main_db SET search_path TO user_management, common_config, public;
```

> **语法说明**：
> - `SET search_path TO`：设置Schema搜索路径
> - `schema1, schema2, ...`：Schema名称列表，按搜索优先级排序
> - `ALTER DATABASE`：修改数据库设置
> - `database_name`：数据库名称

### 2. JSONB数据类型语法

#### 2.1 创建JSONB字段语法

```sql
-- 基本语法
column_name JSONB [constraints]

-- 示例
CREATE TABLE user_management.user_profile (
    profile_id BIGINT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    profile_data JSONB,  -- JSONB类型字段
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES user_management.user(user_id)
);
```

> **语法说明**：
> - `JSONB`：PostgreSQL的二进制JSON数据类型
> - 与JSON类型相比，JSONB以二进制格式存储，支持索引和高效查询

#### 2.2 插入JSONB数据语法

```sql
-- 基本语法
INSERT INTO table_name (jsonb_column) VALUES ('json_string');

-- 示例
INSERT INTO user_management.user_profile (profile_id, user_id, profile_data)
VALUES (1, 1, '{"address": {"city": "Beijing", "country": "China"}, "interests": ["reading", "coding", "hiking"]}');
```

> **语法说明**：
> - JSON字符串必须是有效的JSON格式
> - 可以使用单引号包围JSON字符串
> - 也可以使用`jsonb_build_object()`、`jsonb_build_array()`等函数构建JSONB值

#### 2.3 JSONB操作符语法

```sql
-- 基本语法
-- 获取JSON对象
jsonb_column -> 'key'  -- 返回JSONB
jsonb_column ->> 'key'  -- 返回TEXT

-- 包含操作符
jsonb_column @> 'json_value'  -- 左侧JSONB是否包含右侧JSONB
jsonb_column <@ 'json_value'  -- 左侧JSONB是否被右侧JSONB包含

-- 键存在操作符
jsonb_column ? 'key'  -- JSONB是否包含指定的键
jsonb_column ?| ARRAY['key1', 'key2']  -- JSONB是否包含任一指定的键
jsonb_column ?& ARRAY['key1', 'key2']  -- JSONB是否包含所有指定的键

-- 示例
-- 获取城市字段
SELECT profile_data->'address'->>'city' FROM user_management.user_profile WHERE profile_id = 1;

-- 查找兴趣包含"coding"的用户
SELECT * FROM user_management.user_profile WHERE profile_data @> '{"interests": ["coding"]}';

-- 查找包含address字段的用户
SELECT * FROM user_management.user_profile WHERE profile_data ? 'address';
```

> **语法说明**：
> - `->` 操作符：获取JSON对象，返回JSONB类型
> - `->>` 操作符：获取JSON值，返回TEXT类型
> - `@>` 操作符：包含操作符，检查左侧JSONB是否包含右侧JSONB的所有键值对
> - `?` 操作符：键存在操作符，检查JSONB是否包含指定的键
> - `?|` 操作符：检查JSONB是否包含任一指定的键
> - `?&` 操作符：检查JSONB是否包含所有指定的键

#### 2.4 JSONB索引语法

```sql
-- 基本语法
CREATE INDEX index_name ON table_name USING GIN (jsonb_column);
CREATE INDEX index_name ON table_name USING GIN ((jsonb_column -> 'path'));

-- 示例
-- 为整个JSONB字段创建GIN索引
CREATE INDEX idx_profile_data ON user_management.user_profile USING GIN (profile_data);

-- 为JSONB字段的特定路径创建GIN索引
CREATE INDEX idx_profile_interests ON user_management.user_profile USING GIN ((profile_data -> 'interests'));
```

> **语法说明**：
> - `CREATE INDEX`：创建索引命令
> - `USING GIN`：使用GIN索引类型，适用于JSONB数据
> - `jsonb_column`：JSONB类型的列
> - `jsonb_column -> 'path'`：JSONB列的特定路径

### 3. 数组类型语法

#### 3.1 创建数组字段语法

```sql
-- 基本语法
column_name data_type[] [constraints]

-- 示例
CREATE TABLE product_catalog.product_tag (
    product_id BIGINT PRIMARY KEY,
    tags TEXT[]  -- 文本数组类型
);
```

> **语法说明**：
> - `data_type[]`：数组类型，如TEXT[]、INTEGER[]、BIGINT[]等
> - 可以存储任何基本数据类型的数组

#### 3.2 插入数组数据语法

```sql
-- 基本语法
-- 使用花括号
INSERT INTO table_name (array_column) VALUES ('{value1, value2, value3}');
-- 使用ARRAY构造函数
INSERT INTO table_name (array_column) VALUES (ARRAY[value1, value2, value3]);

-- 示例
-- 使用花括号
INSERT INTO product_catalog.product_tag (product_id, tags) VALUES (1, '{"electronics", "smartphone", "android"}');
-- 使用ARRAY构造函数
INSERT INTO product_catalog.product_tag (product_id, tags) VALUES (2, ARRAY['laptop', 'gaming', 'windows']);
```

> **语法说明**：
> - 花括号`{}`：PostgreSQL特有的数组文字语法
> - `ARRAY[]`：SQL标准的数组构造函数
> - 字符串值需要用单引号括起来

#### 3.3 数组操作符和函数语法

```sql
-- 基本语法
-- 数组包含操作符
array_column @> ARRAY[value]  -- 左侧数组是否包含右侧数组的所有元素
array_column <@ ARRAY[value]  -- 左侧数组是否被右侧数组包含
array_column && ARRAY[value]  -- 左侧数组是否与右侧数组有重叠元素

-- 数组元素操作符
array_column[index]  -- 获取指定索引的元素（索引从1开始）
array_column[start:end]  -- 获取数组切片

-- 数组函数
array_length(array_column, 1)  -- 获取数组长度
unnest(array_column)  -- 将数组展开为多行
array_to_string(array_column, delimiter)  -- 将数组转换为字符串
string_to_array(string, delimiter)  -- 将字符串转换为数组
array_append(array_column, value)  -- 向数组末尾添加元素
array_prepend(value, array_column)  -- 向数组开头添加元素
array_cat(array1, array2)  -- 连接两个数组

-- 示例
-- 查询标签包含'smartphone'的产品
SELECT * FROM product_catalog.product_tag WHERE 'smartphone' = ANY(tags);
-- 查询同时包含'electronics'和'smartphone'标签的产品
SELECT * FROM product_catalog.product_tag WHERE tags @> ARRAY['electronics', 'smartphone'];
-- 获取产品ID为1的产品的标签数量
SELECT array_length(tags, 1) FROM product_catalog.product_tag WHERE product_id = 1;
-- 将产品ID为1的产品的标签数组展开为多行
SELECT unnest(tags) FROM product_catalog.product_tag WHERE product_id = 1;
-- 获取第一个标签
SELECT tags[1] FROM product_catalog.product_tag WHERE product_id = 1;
-- 获取前两个标签
SELECT tags[1:2] FROM product_catalog.product_tag WHERE product_id = 1;
```

> **语法说明**：
> - `@>` 操作符：包含操作符，检查左侧数组是否包含右侧数组的所有元素
> - `<@` 操作符：被包含操作符，检查左侧数组是否被右侧数组包含
> - `&&` 操作符：重叠操作符，检查两个数组是否有共同元素
> - `ANY` 操作符：检查数组中是否有任何元素满足条件
> - `array[index]`：索引访问，PostgreSQL数组索引从1开始
> - `array[start:end]`：数组切片，包含start和end索引的元素
> - `array_length()`：获取数组长度，第二个参数表示维度（1表示第一维）
> - `unnest()`：将数组展开为多行

### 4. 全文搜索语法

#### 4.1 创建全文搜索字段语法

```sql
-- 基本语法
column_name TSVECTOR [constraints]

-- 示例
CREATE TABLE product_catalog.product (
    product_id BIGINT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    search_vector TSVECTOR  -- 全文搜索向量
);
```

> **语法说明**：
> - `TSVECTOR`：全文搜索向量类型，存储预处理后的文本索引
> - 通常使用触发器自动更新TSVECTOR字段

#### 4.2 创建全文搜索索引语法

```sql
-- 基本语法
CREATE INDEX index_name ON table_name USING GIN (tsvector_column);

-- 示例
CREATE INDEX idx_product_search ON product_catalog.product USING GIN (search_vector);
```

> **语法说明**：
> - `CREATE INDEX`：创建索引命令
> - `USING GIN`：使用GIN索引类型，适用于全文搜索
> - `tsvector_column`：TSVECTOR类型的列

#### 4.3 创建全文搜索触发器语法

```sql
-- 基本语法
CREATE OR REPLACE FUNCTION function_name() RETURNS TRIGGER AS $$
BEGIN
    NEW.tsvector_column = to_tsvector('configuration', text_to_index);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_name
BEFORE INSERT OR UPDATE ON table_name
FOR EACH ROW
EXECUTE FUNCTION function_name();

-- 示例
CREATE OR REPLACE FUNCTION product_catalog.product_search_vector_update() RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector = to_tsvector('english', NEW.name || ' ' || COALESCE(NEW.description, ''));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER product_search_vector_update
BEFORE INSERT OR UPDATE ON product_catalog.product
FOR EACH ROW
EXECUTE FUNCTION product_catalog.product_search_vector_update();
```

> **语法说明**：
> - `CREATE OR REPLACE FUNCTION`：创建或替换函数
> - `RETURNS TRIGGER`：指定函数返回触发器类型
> - `AS $$...$$`：函数体，使用美元引用符号
> - `to_tsvector()`：将文本转换为TSVECTOR类型
>   - 第一个参数：文本搜索配置，如'english'、'simple'等
>   - 第二个参数：要索引的文本
> - `LANGUAGE plpgsql`：使用PL/pgSQL语言
> - `CREATE TRIGGER`：创建触发器
> - `BEFORE INSERT OR UPDATE`：在插入或更新前触发
> - `FOR EACH ROW`：对每一行记录执行
> - `EXECUTE FUNCTION`：执行指定函数

#### 4.4 全文搜索查询语法

```sql
-- 基本语法
-- 使用@@操作符进行全文搜索匹配
tsvector_column @@ to_tsquery('configuration', 'query_string')

-- 示例
-- 查询包含'smartphone'和'android'的产品
SELECT * FROM product_catalog.product
WHERE search_vector @@ to_tsquery('english', 'smartphone & android');

-- 使用ts_rank函数对搜索结果进行排序
SELECT *, ts_rank(search_vector, to_tsquery('english', 'smartphone & android')) AS rank
FROM product_catalog.product
WHERE search_vector @@ to_tsquery('english', 'smartphone & android')
ORDER BY rank DESC;

-- 使用ts_headline函数高亮显示匹配的文本
SELECT product_id, name,
       ts_headline('english', description, to_tsquery('english', 'smartphone & android')) AS highlighted_description
FROM product_catalog.product
WHERE search_vector @@ to_tsquery('english', 'smartphone & android');
```

> **语法说明**：
> - `@@` 操作符：全文搜索匹配操作符，检查TSVECTOR是否匹配TSQUERY
> - `to_tsquery()`：将查询字符串转换为TSQUERY类型
>   - 第一个参数：文本搜索配置，如'english'、'simple'等
>   - 第二个参数：查询字符串，使用&（AND）、|（OR）、!（NOT）等操作符
> - `ts_rank()`：计算搜索结果的相关性排名
> - `ts_headline()`：生成带有高亮标记的文本摘要

### 5. 分布式ID生成语法

#### 5.1 创建实例注册表语法

```sql
-- 基本语法
CREATE TABLE schema_name.instance_registry (
    instance_unique_id BIGSERIAL PRIMARY KEY,
    application_name VARCHAR(100) NOT NULL,
    environment VARCHAR(50) NOT NULL,
    -- 其他字段...
);

-- 示例
CREATE TABLE infra_uid.instance_registry (
    instance_unique_id BIGSERIAL PRIMARY KEY,
    application_name VARCHAR(100) NOT NULL,
    environment VARCHAR(50) NOT NULL,
    instance_group VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    first_registered_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_seen_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    custom_metadata JSONB
);
```

> **语法说明**：
> - `BIGSERIAL`：自增的大整数类型，自动创建序列并设置默认值
> - `PRIMARY KEY`：主键约束
> - `NOT NULL`：非空约束
> - `DEFAULT`：默认值

#### 5.2 创建工作机器ID分配表语法

```sql
-- 基本语法
CREATE TABLE schema_name.worker_id_assignment (
    worker_id BIGINT PRIMARY KEY,
    assigned_instance_unique_id BIGINT,
    -- 其他字段...
    CONSTRAINT fk_instance FOREIGN KEY (assigned_instance_unique_id) REFERENCES schema_name.instance_registry(instance_unique_id)
);

-- 示例
CREATE TABLE infra_uid.worker_id_assignment (
    worker_id BIGINT PRIMARY KEY,
    assigned_instance_unique_id BIGINT,
    assignment_status VARCHAR(20) NOT NULL DEFAULT 'AVAILABLE',
    assigned_at TIMESTAMP,
    lease_duration_seconds INT DEFAULT 300,
    lease_expires_at TIMESTAMP,
    last_heartbeat_at TIMESTAMP,
    CONSTRAINT fk_instance FOREIGN KEY (assigned_instance_unique_id) REFERENCES infra_uid.instance_registry(instance_unique_id)
);
```

> **语法说明**：
> - `BIGINT`：大整数类型，用于存储工作机器ID
> - `CONSTRAINT fk_instance FOREIGN KEY`：外键约束，确保assigned_instance_unique_id存在于instance_registry表中

#### 5.3 创建心跳更新函数语法

```sql
-- 基本语法
CREATE OR REPLACE FUNCTION schema_name.function_name(param1 type1, param2 type2) RETURNS return_type AS $$
BEGIN
    -- 函数体...
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 示例
CREATE OR REPLACE FUNCTION infra_uid.update_heartbeat(p_instance_id BIGINT, p_worker_id BIGINT) RETURNS VOID AS $$
BEGIN
    -- 更新实例的最后活跃时间
    UPDATE infra_uid.instance_registry
    SET last_seen_at = CURRENT_TIMESTAMP
    WHERE instance_unique_id = p_instance_id;

    -- 更新工作机器ID的最后心跳时间和租约过期时间
    UPDATE infra_uid.worker_id_assignments
    SET last_heartbeat_at = CURRENT_TIMESTAMP,
        lease_expires_at = CURRENT_TIMESTAMP + (lease_duration_seconds * INTERVAL '1 second')
    WHERE worker_id = p_worker_id
      AND assigned_instance_unique_id = p_instance_id;
END;
$$ LANGUAGE plpgsql;
```

> **语法说明**：
> - `CREATE OR REPLACE FUNCTION`：创建或替换函数
> - `schema_name.function_name`：函数的完整名称，包括Schema
> - `(param1 type1, param2 type2)`：函数参数列表
> - `RETURNS return_type`：函数返回类型
> - `AS $$...$$`：函数体，使用美元引用符号
> - `LANGUAGE plpgsql`：使用PL/pgSQL语言

### 6. 常见错误和注意事项

1. **Schema名称区分大小写**：如果不使用双引号，Schema名称会被自动转换为小写。如果需要使用大写或混合大小写，必须使用双引号。

2. **JSONB路径操作符顺序**：使用`->`和`->>`操作符时，从左到右依次访问JSON对象的层次结构。

3. **数组索引从1开始**：PostgreSQL的数组索引从1开始，而不是从0开始，这与大多数编程语言不同。

4. **全文搜索配置**：使用`to_tsvector()`和`to_tsquery()`函数时，确保使用相同的文本搜索配置（如'english'、'simple'等）。

5. **触发器函数返回值**：在BEFORE触发器中，必须返回NEW才能继续执行操作；在AFTER触发器中，返回值被忽略。

### 7. 最佳实践

1. **使用Schema组织对象**：按业务功能或模块划分Schema，提高可维护性。

2. **为JSONB字段创建GIN索引**：如果需要频繁查询JSONB字段，创建GIN索引提高查询性能。

3. **使用数组函数而非循环**：PostgreSQL提供了丰富的数组函数，使用这些函数比在应用程序中循环处理数组更高效。

4. **使用触发器自动更新全文搜索向量**：使用触发器在插入或更新记录时自动更新TSVECTOR字段，确保搜索索引始终是最新的。

5. **定期维护分布式ID生成器**：定期清理过期的工作机器ID分配记录，避免资源浪费。

## 小结

本教程介绍了PostgreSQL的Schema设计与管理以及高级特性应用，包括：

- Schema的重要性和命名规范
- Schema创建与管理方法
- Schema权限管理
- Schema演进策略
- JSONB数据类型的使用
- 数组类型的使用
- 全文搜索功能
- 分布式ID生成

通过本教程，你应该已经掌握了如何设计和管理PostgreSQL Schema以及如何使用PostgreSQL的高级特性。在下一部分教程中，我们将深入探讨PostgreSQL性能优化和事务管理。

> **专业名词总结**：
>
> 1. **Schema**：数据库内的命名空间，用于组织对象并解决命名冲突
> 2. **命名空间隔离**：不同Schema中可以有同名对象，避免命名冲突
> 3. **Schema命名规范**：统一的Schema命名规则，提高可读性和可维护性
> 4. **JSONB数据类型**：PostgreSQL提供的二进制JSON数据类型，支持索引和高效查询
> 5. **数组类型**：在一个字段中存储多个值的数据类型
> 6. **全文搜索**：对文本内容进行高效搜索的功能
> 7. **TSVECTOR**：全文搜索的向量类型，存储预处理后的文本索引
> 8. **GIN索引**：适用于包含多个值的列的索引类型，如数组和JSONB
> 9. **触发器(Trigger)**：在特定数据库事件发生时自动执行的函数
> 10. **分布式ID生成**：在分布式系统中生成唯一ID的技术
> 11. **百度UID生成器**：一种高效的分布式ID生成方案
> 12. **心跳(heartbeat)**：定期发送的信号，表明实例仍然活跃
> 13. **租约(lease)**：临时分配的使用权，有过期时间
> 14. **工作机器ID**：每个应用实例的唯一标识
