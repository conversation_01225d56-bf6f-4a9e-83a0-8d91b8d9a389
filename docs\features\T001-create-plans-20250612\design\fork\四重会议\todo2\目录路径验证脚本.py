#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Directory Path Validation Script - Prevent AI path errors
WARNING: Directory paths are where AI makes the most mistakes! This script ensures 100% path accuracy
"""

import os
import json
import sys
from pathlib import Path

# 修复编码问题 - 保持功能完整性
def setup_encoding():
    """设置正确的编码，确保中文路径正常显示"""
    try:
        # 设置环境变量
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        # Windows系统特殊处理
        if sys.platform.startswith('win'):
            import codecs
            import locale

            # 尝试设置控制台编码
            try:
                import subprocess
                subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
            except:
                pass

            # 设置默认编码
            try:
                locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
            except:
                try:
                    locale.setlocale(locale.LC_ALL, 'C.UTF-8')
                except:
                    pass

    except Exception:
        # 编码设置失败时继续执行
        pass

# 安全打印函数，处理编码问题但保持功能完整
def safe_print(text):
    """安全打印函数，处理编码问题"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 尝试不同的编码方式
        try:
            print(text.encode('utf-8', 'replace').decode('utf-8'))
        except:
            try:
                print(text.encode('gbk', 'replace').decode('gbk'))
            except:
                # 最后的ASCII安全模式
                safe_text = ''.join(c if ord(c) < 128 else '?' for c in text)
                print(safe_text)
    except Exception:
        print("Output encoding error - check console settings")

# 立即设置编码
setup_encoding()

class DirectoryPathValidator:
    """目录路径验证器 - AI路径错误防护专用"""

    def __init__(self):
        self.project_root = os.path.abspath(".")
        self.errors = []
        self.warnings = []

        # 加载配置参数映射
        self.mapping_file = os.path.join("docs", "features", "F007-建立Commons库的治理机制-20250610",
                                        "nexus万用插座", "design", "v1", "fork", "四重会议", "todo2",
                                        "00-配置参数映射.json")
        self.load_path_mapping()

    def load_path_mapping(self):
        """加载路径映射配置"""
        try:
            safe_print(f"Loading config file: {self.mapping_file}")
            if not os.path.exists(self.mapping_file):
                safe_print(f"ERROR: Config file not found: {self.mapping_file}")
                sys.exit(1)

            with open(self.mapping_file, 'r', encoding='utf-8') as f:
                self.mapping = json.load(f)
            safe_print("SUCCESS: Path mapping config loaded")
        except json.JSONDecodeError as e:
            safe_print(f"ERROR: JSON format error: {str(e)}")
            sys.exit(1)
        except Exception as e:
            safe_print(f"ERROR: Path mapping config load failed: {str(e)}")
            safe_print(f"   File path: {self.mapping_file}")
            safe_print(f"   Current dir: {os.getcwd()}")
            sys.exit(1)

    def validate_working_directory(self):
        """验证工作目录"""
        safe_print("")
        safe_print("Validating working directory...")
        current_dir = os.getcwd()

        # 检查是否在正确的项目目录中
        if not (current_dir.endswith('xkongcloud') or 'xkongcloud' in current_dir):
            self.errors.append(f"Working directory error: {current_dir}")
            safe_print(f"ERROR: Working directory error: {current_dir}")
            safe_print(f"   Should contain: xkongcloud")
            safe_print(f"   Fix command: cd C:\\ExchangeWorks\\xkong\\xkongcloud")
            return False
        else:
            safe_print(f"SUCCESS: Working directory correct: {current_dir}")
            return True

    def validate_directory_structure(self):
        """验证目录结构"""
        safe_print("")
        safe_print("Validating directory structure...")

        # 从映射中获取目录创建序列
        directory_sequence = self.mapping["file_path_mapping"]["directory_creation_sequence"]

        missing_dirs = []
        existing_dirs = []

        for step, mkdir_command in directory_sequence.items():
            # 提取目录路径（去掉 mkdir -p 前缀）
            dir_path = mkdir_command.replace("mkdir -p ", "").strip()
            # 规范化路径分隔符
            dir_path = os.path.normpath(dir_path)

            if os.path.exists(dir_path):
                existing_dirs.append(dir_path)
                safe_print(f"SUCCESS: {dir_path}")
            else:
                missing_dirs.append(dir_path)
                safe_print(f"ERROR: Missing directory: {dir_path}")
                safe_print(f"   Create command: mkdir -p {dir_path}")

        if missing_dirs:
            self.errors.extend([f"Missing directory: {d}" for d in missing_dirs])
            safe_print(f"")
            safe_print(f"WARNING: Found {len(missing_dirs)} missing directories")
            safe_print("Fix commands:")
            for step, mkdir_command in directory_sequence.items():
                dir_path = os.path.normpath(mkdir_command.replace("mkdir -p ", "").strip())
                if dir_path in missing_dirs:
                    safe_print(f"   mkdir -p {dir_path}")
            return False
        else:
            safe_print(f"")
            safe_print(f"SUCCESS: All {len(existing_dirs)} directories exist")
            return True

    def validate_module_paths(self):
        """验证模块路径"""
        safe_print("")
        safe_print("Validating module paths...")

        module_paths = self.mapping["file_path_mapping"]["module_paths"]
        missing_files = []
        existing_files = []

        def check_path(path_info, path_name=""):
            if isinstance(path_info, dict):
                if "file" in path_info:
                    file_path = os.path.normpath(path_info["file"])
                    if os.path.exists(file_path):
                        existing_files.append(file_path)
                        safe_print(f"SUCCESS: {file_path}")
                    else:
                        missing_files.append(file_path)
                        safe_print(f"ERROR: Missing file: {file_path}")
                        if "directory" in path_info:
                            safe_print(f"   Directory: {os.path.normpath(path_info['directory'])}")
                        if "validation" in path_info:
                            safe_print(f"   Note: {path_info['validation']}")

                # 检查files字典中的文件
                if "files" in path_info and isinstance(path_info["files"], dict):
                    for file_key, file_path in path_info["files"].items():
                        normalized_path = os.path.normpath(file_path)
                        if os.path.exists(normalized_path):
                            existing_files.append(normalized_path)
                            safe_print(f"SUCCESS: {normalized_path}")
                        else:
                            missing_files.append(normalized_path)
                            safe_print(f"ERROR: Missing file: {normalized_path}")

                # 递归检查嵌套路径
                for key, value in path_info.items():
                    if key not in ["file", "directory", "validation", "files"] and isinstance(value, dict):
                        check_path(value, f"{path_name}.{key}")
            elif isinstance(path_info, str) and path_info.endswith('.py'):
                normalized_path = os.path.normpath(path_info)
                if os.path.exists(normalized_path):
                    existing_files.append(normalized_path)
                    safe_print(f"SUCCESS: {normalized_path}")
                else:
                    missing_files.append(normalized_path)
                    safe_print(f"ERROR: Missing file: {normalized_path}")

        for module_name, path_info in module_paths.items():
            if module_name == "path_validation_command":
                continue
            safe_print(f"")
            safe_print(f"Checking module: {module_name}")
            check_path(path_info, module_name)

        if missing_files:
            self.warnings.extend([f"Missing file: {f}" for f in missing_files])
            safe_print(f"")
            safe_print(f"WARNING: Found {len(missing_files)} missing files (will be created in later steps)")

        return len(missing_files) == 0

    def validate_config_files(self):
        """验证配置文件"""
        safe_print("")
        safe_print("Validating config files...")

        base_config_path = os.path.join("docs", "features", "F007-建立Commons库的治理机制-20250610",
                                       "nexus万用插座", "design", "v1", "fork", "四重会议", "todo2")

        config_files = [
            os.path.join(base_config_path, "00-共同配置.json"),
            os.path.join(base_config_path, "00-依赖关系映射.json"),
            os.path.join(base_config_path, "00-配置参数映射.json")
        ]

        all_exist = True
        for config_file in config_files:
            if os.path.exists(config_file):
                safe_print(f"SUCCESS: {config_file}")
                # 验证JSON格式
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        json.load(f)
                    safe_print(f"   SUCCESS: JSON format correct")
                except json.JSONDecodeError as e:
                    safe_print(f"   ERROR: JSON format error: {str(e)}")
                    self.errors.append(f"JSON format error: {config_file}")
                    all_exist = False
            else:
                safe_print(f"ERROR: Missing config file: {config_file}")
                self.errors.append(f"Missing config file: {config_file}")
                all_exist = False

        return all_exist

    def run_full_validation(self):
        """运行完整验证"""
        safe_print("Starting directory path validation (AI path error protection)")
        safe_print("=" * 60)

        validations = [
            ("Working Directory Validation", self.validate_working_directory),
            ("Directory Structure Validation", self.validate_directory_structure),
            ("Module Path Validation", self.validate_module_paths),
            ("Config File Validation", self.validate_config_files)
        ]

        passed_validations = 0
        total_validations = len(validations)

        for name, validator in validations:
            safe_print(f"")
            safe_print(f"{'='*20} {name} {'='*20}")
            if validator():
                passed_validations += 1
                safe_print(f"SUCCESS: {name} passed")
            else:
                safe_print(f"ERROR: {name} failed")

        # 生成验证报告
        safe_print("")
        safe_print("=" * 60)
        safe_print("Directory Path Validation Report")
        safe_print(f"Passed validations: {passed_validations}/{total_validations}")

        if self.errors:
            safe_print(f"")
            safe_print(f"ERROR: Found {len(self.errors)} errors:")
            for i, error in enumerate(self.errors, 1):
                safe_print(f"   {i}. {error}")

        if self.warnings:
            safe_print(f"")
            safe_print(f"WARNING: Found {len(self.warnings)} warnings:")
            for i, warning in enumerate(self.warnings, 1):
                safe_print(f"   {i}. {warning}")

        success_rate = passed_validations / total_validations
        safe_print(f"")
        safe_print(f"Validation success rate: {success_rate:.1%}")

        if success_rate >= 0.75:
            safe_print("SUCCESS: Directory path validation passed! AI can start execution")
            if self.warnings:
                safe_print("WARNING: There are warning items, please pay attention during execution")
            return True
        else:
            safe_print("ERROR: Directory path validation failed, must fix errors first")
            safe_print("")
            safe_print("Fix suggestions:")
            safe_print("1. Confirm working directory is correct")
            safe_print("2. Create missing directories as prompted")
            safe_print("3. Check JSON format of config files")
            safe_print("4. Re-run this validation script")
            return False

def main():
    """主函数"""
    try:
        safe_print("Starting validator initialization...")
        validator = DirectoryPathValidator()
        safe_print("Validator initialization complete, starting validation...")
        success = validator.run_full_validation()

        if success:
            safe_print("")
            safe_print("COMPLETE: Directory path validation finished! AI can execute safely")
        else:
            safe_print("")
            safe_print("FAILED: Directory path validation failed! Please fix issues first")
            sys.exit(1)
    except KeyboardInterrupt:
        safe_print("")
        safe_print("WARNING: User interrupted validation")
        sys.exit(1)
    except Exception as e:
        safe_print("")
        safe_print(f"ERROR: Validation process error: {str(e)}")
        safe_print(f"Error type: {type(e).__name__}")
        import traceback
        safe_print("Detailed error info:")
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()

class DirectoryPathValidator:
    """目录路径验证器 - AI路径错误防护专用"""
    
    def __init__(self):
        self.project_root = os.path.abspath(".")
        self.errors = []
        self.warnings = []

        # 加载配置参数映射
        self.mapping_file = os.path.join("docs", "features", "F007-建立Commons库的治理机制-20250610",
                                        "nexus万用插座", "design", "v1", "fork", "四重会议", "todo2",
                                        "00-配置参数映射.json")
        self.load_path_mapping()
    
    def load_path_mapping(self):
        """加载路径映射配置"""
        try:
            safe_print(f"Loading config file: {self.mapping_file}")
            if not os.path.exists(self.mapping_file):
                safe_print(f"ERROR: Config file not found: {self.mapping_file}")
                sys.exit(1)

            with open(self.mapping_file, 'r', encoding='utf-8') as f:
                self.mapping = json.load(f)
            safe_print("SUCCESS: Path mapping config loaded")
        except json.JSONDecodeError as e:
            safe_print(f"ERROR: JSON format error: {str(e)}")
            sys.exit(1)
        except Exception as e:
            safe_print(f"ERROR: Path mapping config load failed: {str(e)}")
            safe_print(f"   File path: {self.mapping_file}")
            safe_print(f"   Current dir: {os.getcwd()}")
            sys.exit(1)
    
    def validate_working_directory(self):
        """验证工作目录"""
        print("\n🔍 验证工作目录...")
        current_dir = os.getcwd()

        # 检查是否在正确的项目目录中
        if not (current_dir.endswith('xkongcloud') or 'xkongcloud' in current_dir):
            self.errors.append(f"工作目录错误: {current_dir}")
            print(f"❌ 工作目录错误: {current_dir}")
            print(f"   应该包含: xkongcloud")
            print(f"   修复命令: cd C:\\ExchangeWorks\\xkong\\xkongcloud")
            return False
        else:
            print(f"✅ 工作目录正确: {current_dir}")
            return True
    
    def validate_directory_structure(self):
        """验证目录结构"""
        print("\n🔍 验证目录结构...")

        # 从映射中获取目录创建序列
        directory_sequence = self.mapping["file_path_mapping"]["directory_creation_sequence"]

        missing_dirs = []
        existing_dirs = []

        for step, mkdir_command in directory_sequence.items():
            # 提取目录路径（去掉 mkdir -p 前缀）
            dir_path = mkdir_command.replace("mkdir -p ", "").strip()
            # 规范化路径分隔符
            dir_path = os.path.normpath(dir_path)

            if os.path.exists(dir_path):
                existing_dirs.append(dir_path)
                print(f"✅ {dir_path}")
            else:
                missing_dirs.append(dir_path)
                print(f"❌ 缺失目录: {dir_path}")
                print(f"   创建命令: mkdir -p {dir_path}")

        if missing_dirs:
            self.errors.extend([f"缺失目录: {d}" for d in missing_dirs])
            print(f"\n⚠️ 发现 {len(missing_dirs)} 个缺失目录")
            print("🔧 修复命令:")
            for step, mkdir_command in directory_sequence.items():
                dir_path = os.path.normpath(mkdir_command.replace("mkdir -p ", "").strip())
                if dir_path in missing_dirs:
                    print(f"   mkdir -p {dir_path}")
            return False
        else:
            print(f"\n✅ 所有 {len(existing_dirs)} 个目录都存在")
            return True
    
    def validate_module_paths(self):
        """验证模块路径"""
        print("\n🔍 验证模块路径...")
        
        module_paths = self.mapping["file_path_mapping"]["module_paths"]
        missing_files = []
        existing_files = []
        
        def check_path(path_info, path_name=""):
            if isinstance(path_info, dict):
                if "file" in path_info:
                    file_path = os.path.normpath(path_info["file"])
                    if os.path.exists(file_path):
                        existing_files.append(file_path)
                        print(f"✅ {file_path}")
                    else:
                        missing_files.append(file_path)
                        print(f"❌ 缺失文件: {file_path}")
                        if "directory" in path_info:
                            print(f"   目录: {os.path.normpath(path_info['directory'])}")
                        if "validation" in path_info:
                            print(f"   提示: {path_info['validation']}")

                # 检查files字典中的文件
                if "files" in path_info and isinstance(path_info["files"], dict):
                    for file_key, file_path in path_info["files"].items():
                        normalized_path = os.path.normpath(file_path)
                        if os.path.exists(normalized_path):
                            existing_files.append(normalized_path)
                            print(f"✅ {normalized_path}")
                        else:
                            missing_files.append(normalized_path)
                            print(f"❌ 缺失文件: {normalized_path}")

                # 递归检查嵌套路径
                for key, value in path_info.items():
                    if key not in ["file", "directory", "validation", "files"] and isinstance(value, dict):
                        check_path(value, f"{path_name}.{key}")
            elif isinstance(path_info, str) and path_info.endswith('.py'):
                normalized_path = os.path.normpath(path_info)
                if os.path.exists(normalized_path):
                    existing_files.append(normalized_path)
                    print(f"✅ {normalized_path}")
                else:
                    missing_files.append(normalized_path)
                    print(f"❌ 缺失文件: {normalized_path}")
        
        for module_name, path_info in module_paths.items():
            if module_name == "path_validation_command":
                continue
            print(f"\n📁 检查模块: {module_name}")
            check_path(path_info, module_name)
        
        if missing_files:
            self.warnings.extend([f"缺失文件: {f}" for f in missing_files])
            print(f"\n⚠️ 发现 {len(missing_files)} 个缺失文件（这些文件将在后续步骤中创建）")
        
        return len(missing_files) == 0
    
    def validate_config_files(self):
        """验证配置文件"""
        print("\n🔍 验证配置文件...")

        base_config_path = os.path.join("docs", "features", "F007-建立Commons库的治理机制-20250610",
                                       "nexus万用插座", "design", "v1", "fork", "四重会议", "todo2")

        config_files = [
            os.path.join(base_config_path, "00-共同配置.json"),
            os.path.join(base_config_path, "00-依赖关系映射.json"),
            os.path.join(base_config_path, "00-配置参数映射.json")
        ]
        
        all_exist = True
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"✅ {config_file}")
                # 验证JSON格式
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        json.load(f)
                    print(f"   ✅ JSON格式正确")
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON格式错误: {str(e)}")
                    self.errors.append(f"JSON格式错误: {config_file}")
                    all_exist = False
            else:
                print(f"❌ 缺失配置文件: {config_file}")
                self.errors.append(f"缺失配置文件: {config_file}")
                all_exist = False
        
        return all_exist
    
    def run_full_validation(self):
        """运行完整验证"""
        print("🚀 开始目录路径验证（AI路径错误防护）")
        print("=" * 60)
        
        validations = [
            ("工作目录验证", self.validate_working_directory),
            ("目录结构验证", self.validate_directory_structure),
            ("模块路径验证", self.validate_module_paths),
            ("配置文件验证", self.validate_config_files)
        ]
        
        passed_validations = 0
        total_validations = len(validations)
        
        for name, validator in validations:
            print(f"\n{'='*20} {name} {'='*20}")
            if validator():
                passed_validations += 1
                print(f"✅ {name} 通过")
            else:
                print(f"❌ {name} 失败")
        
        # 生成验证报告
        print("\n" + "=" * 60)
        print("📊 目录路径验证报告")
        print(f"通过验证: {passed_validations}/{total_validations}")
        
        if self.errors:
            print(f"\n❌ 发现 {len(self.errors)} 个错误:")
            for i, error in enumerate(self.errors, 1):
                print(f"   {i}. {error}")
        
        if self.warnings:
            print(f"\n⚠️ 发现 {len(self.warnings)} 个警告:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"   {i}. {warning}")
        
        success_rate = passed_validations / total_validations
        print(f"\n🎯 验证成功率: {success_rate:.1%}")
        
        if success_rate >= 0.75:
            print("✅ 目录路径验证基本通过！可以开始AI执行")
            if self.warnings:
                print("⚠️ 注意：存在警告项，请在执行过程中关注")
            return True
        else:
            print("❌ 目录路径验证未通过，必须先修复错误")
            print("\n🔧 修复建议:")
            print("1. 确认工作目录正确")
            print("2. 按照提示创建缺失的目录")
            print("3. 检查配置文件的JSON格式")
            print("4. 重新运行此验证脚本")
            return False

def simple_validation():
    """简化的验证函数，避免编码问题"""
    safe_print("=== Directory Path Validation ===")

    # 检查工作目录
    current_dir = os.getcwd()
    safe_print(f"Current directory: {current_dir}")

    if 'xkongcloud' in current_dir:
        safe_print("SUCCESS: Working directory correct")
    else:
        safe_print("ERROR: Working directory incorrect")
        return False

    # 检查关键目录
    required_dirs = [
        "tools/ace/src",
        "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/fork/四重会议/todo2"
    ]

    all_exist = True
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            safe_print(f"SUCCESS: {dir_path}")
        else:
            safe_print(f"ERROR: Missing directory: {dir_path}")
            all_exist = False

    # 检查配置文件
    config_file = "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/fork/四重会议/todo2/00-配置参数映射.json"
    if os.path.exists(config_file):
        safe_print("SUCCESS: Config file exists")
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                json.load(f)
            safe_print("SUCCESS: Config file format correct")
        except:
            safe_print("ERROR: Config file format error")
            all_exist = False
    else:
        safe_print("ERROR: Config file not found")
        all_exist = False

    if all_exist:
        safe_print("")
        safe_print("SUCCESS: Directory path validation passed! AI can start execution")
        return True
    else:
        safe_print("")
        safe_print("ERROR: Directory path validation failed")
        return False

def main():
    """主函数"""
    try:
        safe_print("Starting simplified validation...")
        success = simple_validation()

        if success:
            safe_print("")
            safe_print("COMPLETE: Directory path validation finished! AI can execute safely")
        else:
            safe_print("")
            safe_print("FAILED: Directory path validation failed! Please fix issues first")
            sys.exit(1)
    except KeyboardInterrupt:
        safe_print("")
        safe_print("WARNING: User interrupted validation")
        sys.exit(1)
    except Exception as e:
        safe_print("")
        safe_print(f"ERROR: Validation process error: {str(e)}")
        safe_print(f"Error type: {type(e).__name__}")
        import traceback
        safe_print("Detailed error info:")
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
