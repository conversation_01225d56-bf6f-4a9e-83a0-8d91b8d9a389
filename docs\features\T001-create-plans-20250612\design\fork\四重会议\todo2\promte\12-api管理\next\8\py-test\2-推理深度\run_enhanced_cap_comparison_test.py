#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版CAP方法对比测试运行器
解决推理深度评估问题 + 添加语义分析增强方案

使用方法：
python run_enhanced_cap_comparison_test.py

作者：AI助手
日期：2025-01-10
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from enhanced_cap_comparison_tester import EnhancedCAPApproachTester, REAL_WORLD_TASKS, API_CONFIG
    import json
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保 enhanced_cap_comparison_tester.py 文件在同一目录下")
    sys.exit(1)

def run_quick_test():
    """运行快速测试（1个任务，1个模型，1种语言）"""
    print("🚀 增强版CAP方法对比快速测试")
    print("=" * 60)
    
    # 只测试第一个任务、第一个模型、中文
    test_task = REAL_WORLD_TASKS[0]
    test_model = API_CONFIG["models"][0]
    test_language = "chinese"
    
    print(f"📋 测试任务: {test_task['name']}")
    print(f"🤖 测试模型: {test_model}")
    print(f"🌐 测试语言: {test_language}")
    print()
    
    tester = EnhancedCAPApproachTester()
    
    try:
        print("🔄 开始测试方案A（内容嵌入式CAP）...")
        result_a = tester.test_approach_a_embedded_cap(test_task, test_model, test_language)
        
        if result_a["success"]:
            print(f"✅ 方案A完成")
            print(f"   质量分数: {result_a['logic_analysis']['overall_score']:.1f}")
            print(f"   推理深度: {result_a['logic_analysis']['dimension_scores']['reasoning_depth']:.1f}")
            print(f"   API调用次数: {result_a['api_calls']}")
            print(f"   Token消耗: {result_a['total_tokens']}")
        else:
            print(f"❌ 方案A失败: {result_a['error']}")
            return
        
        print()
        print("🔄 开始测试方案B（外部头部式CAP）...")
        result_b = tester.test_approach_b_header_cap(test_task, test_model, test_language)
        
        if result_b["success"]:
            print(f"✅ 方案B完成")
            print(f"   质量分数: {result_b['logic_analysis']['overall_score']:.1f}")
            print(f"   推理深度: {result_b['logic_analysis']['dimension_scores']['reasoning_depth']:.1f}")
            print(f"   API调用次数: {result_b['api_calls']}")
            print(f"   Token消耗: {result_b['total_tokens']}")
        else:
            print(f"❌ 方案B失败: {result_b['error']}")
            return
        
        print()
        print("🔄 开始测试方案C（语义分析增强CAP）...")
        result_c = tester.test_approach_c_semantic_enhanced_cap(test_task, test_model, test_language)
        
        if result_c["success"]:
            print(f"✅ 方案C完成")
            print(f"   质量分数: {result_c['logic_analysis']['overall_score']:.1f}")
            print(f"   推理深度: {result_c['logic_analysis']['dimension_scores']['reasoning_depth']:.1f}")
            print(f"   语义完整性: {result_c['semantic_analysis']['semantic_completeness']:.1f}")
            print(f"   API调用次数: {result_c['api_calls']}")
            print(f"   Token消耗: {result_c['total_tokens']}")
        else:
            print(f"❌ 方案C失败: {result_c['error']}")
            return
        
        # 快速对比
        print()
        print("📊 快速对比结果:")
        print("-" * 40)
        
        scores = [
            ("方案A", result_a['logic_analysis']['overall_score']),
            ("方案B", result_b['logic_analysis']['overall_score']),
            ("方案C", result_c['logic_analysis']['overall_score'])
        ]
        
        reasoning_depths = [
            ("方案A", result_a['logic_analysis']['dimension_scores']['reasoning_depth']),
            ("方案B", result_b['logic_analysis']['dimension_scores']['reasoning_depth']),
            ("方案C", result_c['logic_analysis']['dimension_scores']['reasoning_depth'])
        ]
        
        tokens = [
            ("方案A", result_a['total_tokens']),
            ("方案B", result_b['total_tokens']),
            ("方案C", result_c['total_tokens'])
        ]
        
        print(f"质量对比:")
        best_quality = max(scores, key=lambda x: x[1])
        for name, score in sorted(scores, key=lambda x: x[1], reverse=True):
            marker = "🏆" if name == best_quality[0] else "  "
            print(f"  {marker} {name}: {score:.1f}分")
        
        print(f"\n推理深度对比:")
        best_reasoning = max(reasoning_depths, key=lambda x: x[1])
        for name, depth in sorted(reasoning_depths, key=lambda x: x[1], reverse=True):
            marker = "🧠" if name == best_reasoning[0] else "  "
            print(f"  {marker} {name}: {depth:.1f}分")
        
        print(f"\n效率对比:")
        best_efficiency = min(tokens, key=lambda x: x[1])
        for name, token_count in sorted(tokens, key=lambda x: x[1]):
            marker = "⚡" if name == best_efficiency[0] else "  "
            print(f"  {marker} {name}: {token_count} tokens")
        
        # 语义分析特殊展示
        if result_c["success"]:
            semantic_analysis = result_c['semantic_analysis']
            print(f"\n🧠 方案C语义分析详情:")
            print(f"   语义完整性: {semantic_analysis['semantic_completeness']:.1f}")
            
            if semantic_analysis['architecture_patterns']:
                print(f"   检测到架构模式: {len(semantic_analysis['architecture_patterns'])}个")
                for pattern, data in semantic_analysis['architecture_patterns'].items():
                    print(f"     - {pattern}: {data['completeness_score']:.0f}分")
            
            if semantic_analysis['design_patterns']:
                print(f"   检测到设计模式: {len(semantic_analysis['design_patterns'])}个")
                for pattern, data in semantic_analysis['design_patterns'].items():
                    print(f"     - {pattern}: {data['quality_score']:.0f}分")
        
        # 推理深度改善检查
        print(f"\n🔍 推理深度评估改善检查:")
        non_zero_reasoning = [depth for _, depth in reasoning_depths if depth > 0]
        if non_zero_reasoning:
            print(f"   ✅ 推理深度评估问题已解决！")
            print(f"   📈 非零推理深度: {len(non_zero_reasoning)}/3 个方案")
            print(f"   📊 平均推理深度: {sum(non_zero_reasoning)/len(non_zero_reasoning):.1f}分")
        else:
            print(f"   ⚠️ 推理深度评估仍需改进")
            print(f"   📉 所有方案推理深度均为0分")
        
        # 保存快速测试结果
        quick_result = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "enhanced_quick_test",
            "task": test_task,
            "model": test_model,
            "language": test_language,
            "approach_a_result": result_a,
            "approach_b_result": result_b,
            "approach_c_result": result_c,
            "quick_comparison": {
                "best_quality": best_quality[0],
                "best_reasoning": best_reasoning[0],
                "best_efficiency": best_efficiency[0],
                "reasoning_depth_fixed": len(non_zero_reasoning) > 0,
                "semantic_analysis_working": result_c["success"] and "semantic_analysis" in result_c
            }
        }
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"enhanced_cap_quick_test_result_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(quick_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 快速测试结果已保存: {filename}")
        print("🎉 增强版快速测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

def run_language_comparison_test():
    """运行语言对比测试（1个任务，1个模型，中英文对比）"""
    print("🚀 中英文语言对比测试")
    print("=" * 50)
    print("⚠️ 注意：此测试将对比中英文在推理深度评估上的差异")
    
    test_task = REAL_WORLD_TASKS[0]
    test_model = API_CONFIG["models"][0]
    
    print(f"📋 测试任务: {test_task['name']}")
    print(f"🤖 测试模型: {test_model}")
    print()
    
    tester = EnhancedCAPApproachTester()
    
    results = {}
    
    for language in ["chinese", "english"]:
        print(f"🌐 测试语言: {language}")
        print("-" * 30)
        
        try:
            # 只测试方案B（头部式CAP）进行语言对比
            result = tester.test_approach_b_header_cap(test_task, test_model, language)
            
            if result["success"]:
                logic_analysis = result['logic_analysis']
                print(f"✅ {language} 测试完成")
                print(f"   质量分数: {logic_analysis['overall_score']:.1f}")
                print(f"   推理深度: {logic_analysis['dimension_scores']['reasoning_depth']:.1f}")
                print(f"   逻辑结构: {logic_analysis['dimension_scores']['logical_structure']:.1f}")
                print(f"   概念复杂度: {logic_analysis['dimension_scores']['concept_complexity']:.1f}")
                print(f"   实用价值: {logic_analysis['dimension_scores']['practical_value']:.1f}")
                print(f"   检测语言: {logic_analysis['language']}")
                
                results[language] = result
            else:
                print(f"❌ {language} 测试失败: {result['error']}")
                
        except Exception as e:
            print(f"❌ {language} 测试异常: {str(e)}")
        
        print()
    
    # 语言对比分析
    if len(results) == 2:
        print("📊 语言对比分析:")
        print("-" * 30)
        
        chinese_result = results["chinese"]
        english_result = results["english"]
        
        chinese_scores = chinese_result['logic_analysis']['dimension_scores']
        english_scores = english_result['logic_analysis']['dimension_scores']
        
        print(f"推理深度对比:")
        print(f"  中文: {chinese_scores['reasoning_depth']:.1f}")
        print(f"  英文: {english_scores['reasoning_depth']:.1f}")
        print(f"  改善: {english_scores['reasoning_depth'] - chinese_scores['reasoning_depth']:.1f}")
        
        print(f"\n整体质量对比:")
        chinese_overall = chinese_result['logic_analysis']['overall_score']
        english_overall = english_result['logic_analysis']['overall_score']
        print(f"  中文: {chinese_overall:.1f}")
        print(f"  英文: {english_overall:.1f}")
        print(f"  改善: {english_overall - chinese_overall:.1f}")
        
        if english_scores['reasoning_depth'] > chinese_scores['reasoning_depth']:
            print(f"\n✅ 推理深度评估问题已通过英文评估解决！")
        else:
            print(f"\n⚠️ 推理深度评估问题仍需进一步改进")
    
    print("🎉 语言对比测试完成！")

def run_full_test():
    """运行完整测试"""
    print("🚀 增强版CAP方法对比完整测试")
    print("=" * 60)
    print("⚠️ 注意：完整测试将调用大量API，可能需要较长时间和费用")
    print("📊 测试内容：3个任务 × 2个模型 × 2种语言 × 3种方案 = 36次API调用")
    
    confirm = input("是否继续？(y/N): ").strip().lower()
    if confirm != 'y':
        print("测试已取消")
        return
    
    from enhanced_cap_comparison_tester import main
    main()

def show_menu():
    """显示菜单"""
    print("🎯 增强版CAP方法对比测试器")
    print("=" * 40)
    print("1. 快速测试（1个任务，3种方案对比）")
    print("2. 语言对比测试（中英文推理深度对比）")
    print("3. 完整测试（所有任务，所有方案）")
    print("4. 查看测试配置")
    print("5. 退出")
    print()

def show_config():
    """显示测试配置"""
    print("📋 增强版测试配置:")
    print("-" * 25)
    print(f"API地址: {API_CONFIG['url']}")
    print(f"测试模型: {', '.join(API_CONFIG['models'])}")
    print(f"测试任务数: {len(REAL_WORLD_TASKS)}")
    print(f"支持语言: 中文, 英文")
    print(f"测试方案: A(内容嵌入式), B(外部头部式), C(语义分析增强)")
    print("\n主要改进:")
    print("  ✅ 解决推理深度评估问题（中英文双语支持）")
    print("  ✅ 添加语义分析增强方案（基于V3算法）")
    print("  ✅ 更准确的LogicDepthDetector")
    print("  ✅ 架构模式和设计模式识别")
    print("\n测试任务列表:")
    for i, task in enumerate(REAL_WORLD_TASKS, 1):
        print(f"  {i}. {task['name']} ({task['id']})")
    print()

def main():
    """主函数"""
    while True:
        show_menu()
        choice = input("请选择操作 (1-5): ").strip()
        
        if choice == '1':
            print()
            run_quick_test()
            print()
        elif choice == '2':
            print()
            run_language_comparison_test()
            print()
        elif choice == '3':
            print()
            run_full_test()
            print()
        elif choice == '4':
            print()
            show_config()
        elif choice == '5':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")
            print()

if __name__ == "__main__":
    main()
