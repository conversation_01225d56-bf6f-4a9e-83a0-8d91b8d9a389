字段级版本一致性检查设计提示词（V2最佳实践继承版）
文档位置: docs\features\F004-CommonsUidLibrary-20250511\F005-xkongcloud-test-engine-20250610\promite\field-version-consistency-design-prompt.md

设计目标
继承V2版本管理的最佳实践和设计智慧，在xkongcloud-test-engine通用引擎中实现现代化的字段级版本一致性检查机制，确保所有子项目配置的严格一致性，支持V2核心理念的现代化重构实现。

核心设计要求
字段版本控制结构
配置元数据层：每个JSON配置文件包含config_metadata，声明配置版本、字段版本要求、依赖关系
字段版本要求层：定义field_version_requirements，包含各业务实体的字段版本、必需字段、可选字段、废弃字段
字段格式规范层：定义key_formats，指定每个字段的数据类型、格式要求、枚举值范围
版本依赖关系层：声明version_dependencies，包含数据库模式版本、gRPC协议版本、API契约版本
通用字段标准设计
基础实体标准：定义base_entity通用字段（id、created_at、updated_at等）
业务实体标准：定义user_entity、order_entity、business_entity等通用业务实体字段
接口标准规范：定义gRPC请求响应、REST API输入输出的标准字段格式
跨项目兼容性：确保字段标准在所有xkongcloud子项目中的一致性和兼容性
版本检查机制设计
启动时强制检查：引擎启动时自动执行字段版本一致性检查，版本不一致时启动失败
字段完整性验证：检查必需字段完整性、字段格式正确性、废弃字段使用警告
版本兼容性矩阵：维护引擎版本与配置版本、字段版本的兼容性矩阵
智能错误提示：提供详细的版本不一致错误信息和修复建议
字段格式验证设计
基础格式验证器：positive_integer、uuid、email_format、iso8601_datetime等标准格式
枚举格式支持：enum[VALUE1,VALUE2,VALUE3]格式的枚举值验证
复合格式支持：支持OR逻辑的多格式验证（如positive_integer|uuid）
自定义格式扩展：支持项目特定的自定义字段格式验证器
配置模板化设计
项目类型模板：为不同项目类型提供标准的字段版本配置模板
渐进式配置：支持从最小配置到完整配置的渐进式字段版本管理
继承机制：支持字段版本配置的继承和覆盖机制
自动生成工具：提供配置模板的自动生成和版本升级工具
版本管理策略设计（继承V2版本管理智慧）
V2版本管理最佳实践继承：深度分析V2版本管理的核心理念和成功经验
语义化版本控制现代化：基于V2版本管理智慧，采用现代语义化版本控制标准
向后兼容性保证机制：继承V2的兼容性设计思想，现代化实现兼容性保证
版本迁移智能化：继承V2的迁移策略，采用现代化自动迁移和升级工具
兼容性检查算法优化：基于V2的检查逻辑，现代化实现版本检查和冲突检测
严格一致性模式设计
strict_consistency开关：通过配置控制是否启用严格的字段版本检查
字段级检查粒度：支持到单个字段的版本检查粒度
检查策略配置：支持strict、warning、ignore三种检查策略
降级机制：版本检查失败时的优雅降级和错误处理机制
跨项目通用化设计（完全替换支持）
零耦合字段标准：字段标准与具体项目完全解耦，支持所有xkongcloud子项目完全替换现有测试代码
智能适配器映射：通过ProjectAdapter自动检测项目架构，智能映射项目特定字段到通用字段
弹性扩展机制：支持项目特定字段的弹性扩展，同时确保通用字段的严格一致性
生态标准化：通过字段版本检查推动整个xkongcloud生态的完全统一和标准化

### 历史架构经验引用
基于现有历史设计文档的版本管理智慧，通过引用方式继承最佳实践：

#### 历史版本管理机制引用
```java
// 参考：docs/features/F003-PostgreSQL迁移-20250508/test/engine/v3/code-architecture/04-v3-code-ai-data-contract.md
// 继承历史架构的AI数据契约版本管理机制
@Component
public class UniversalVersionConsistencyChecker {
    // 继承历史架构的字段级版本检查机制
    public VersionCheckResult validateFieldVersionConsistency(ConfigurationData config) {
        // 基于历史架构的版本一致性检查算法
        return performVersionCheck(config);
    }
}
```

#### 历史环境感知版本适配引用
```java
// 参考：docs/features/F003-PostgreSQL迁移-20250508/test/engine/v3/code-architecture/02-v3-ai-failure-triple-loop-processor.md
// 继承历史架构的环境感知版本适配机制
public void adaptVersionBasedOnEnvironment(VersionContext context, EnvironmentAwareness awareness) {
    // 基于历史架构的环境感知透明度设计，智能适配版本检查策略
    switch (awareness.getEnvironmentType()) {
        case MOCK_DIAGNOSTIC:
            context.setVersionCheckLevel(VersionCheckLevel.RELAXED);
            break;
        case REAL_TESTCONTAINERS:
            context.setVersionCheckLevel(VersionCheckLevel.STRICT);
            break;
        case PRODUCTION_LIKE:
            context.setVersionCheckLevel(VersionCheckLevel.ULTRA_STRICT);
            break;
    }
}
```
实施集成设计
Spring Boot集成：通过@PostConstruct在引擎启动时自动执行字段版本检查
配置绑定机制：通过@ConfigurationProperties实现字段版本配置的类型安全绑定
异常处理设计：定义专门的字段版本异常类型和处理机制
日志记录规范：提供详细的字段版本检查日志和调试信息
质量保证设计
自动化测试：为字段版本检查机制提供完整的自动化测试覆盖
性能优化：确保字段版本检查不影响引擎启动性能
错误恢复：提供字段版本问题的快速诊断和恢复机制
文档完整性：提供完整的字段版本管理使用文档和最佳实践
设计输出要求
核心检查器设计：UniversalFieldVersionValidator的完整设计规范
配置结构设计：JSON配置文件的字段版本控制结构设计
验证算法设计：字段格式验证和版本兼容性检查算法设计
集成机制设计：与xkongcloud-test-engine的无缝集成机制设计
使用指南设计：面向开发者的字段版本管理使用指南设计
成功标准
版本一致性保证：100%确保配置字段版本与引擎版本的一致性
跨项目通用性：支持所有xkongcloud子项目的字段版本管理
零配置错误：通过字段版本检查消除配置错误导致的运行时问题
标准化推进：推动整个生态的字段标准化和版本管理规范化