# V4核心工作流程配置（用户确认版）

## 📋 文档概述

**文档ID**: V4-WORKFLOW-CONFIG-000
**创建日期**: 2025-06-16
**版本**: V4.0-User-Confirmed-Workflow
**目标**: 明确V4核心工作流程，确保实施计划与用户要求一致

## 🎯 用户确认的核心运行目标

### 1. 首要运行目标
- **分析目标**：`docs\features\F007-建立Commons库的治理机制-20250610\nexus万用插座\design\v1`
- **输出目录**：`docs\features\F007-建立Commons库的治理机制-20250610\nexus万用插座\design\v1\checkresult-v4`

### 2. V4工作流程（简版程序+正式代码架构）

#### 简版程序一键运行流程
1. **一键启动**：
   - 运行命令：`python simple_scanner.py`
   - 无需参数，硬编码扫描目标：`docs\features\F007-建立Commons库的治理机制-20250610\nexus万用插座\design\v1`
   - 自动执行完整的结构扫描验证流程

2. **任务接口调用**：
   - 简版程序内部调用正式生产代码的任务接口
   - 调用顺序：`StructureScanTask` → `ArchitectureAnalysisTask` → `QualityValidationTask`
   - 硬编码输出路径：`docs\features\F007-建立Commons库的治理机制-20250610\nexus万用插座\design\v1\checkresult-v4`

3. **正式代码执行**：
   - 正式生产代码执行实际的扫描和分析算法
   - 自动创建输出目录并生成结构化报告
   - 包含：结构分析报告、架构评估报告、质量验证报告

4. **自动完成**：
   - 简版程序显示执行进度和结果摘要
   - 自动保存所有报告到checkresult-v4目录
   - 提示用户查看详细报告的路径

#### 任务接口标准定义
1. **结构扫描任务接口**：
   - 接口名：`StructureScanTask`
   - 输入参数：目标路径、扫描深度、输出格式
   - 输出：结构分析报告、问题清单、改进建议

2. **架构分析任务接口**：
   - 接口名：`ArchitectureAnalysisTask`
   - 输入参数：设计文档路径、分析维度、质量标准
   - 输出：架构评估报告、依赖关系图、优化建议

3. **质量验证任务接口**：
   - 接口名：`QualityValidationTask`
   - 输入参数：验证规则、质量阈值、检查范围
   - 输出：质量评分、问题详情、修复指导

4. **迭代扫描支持**：
   - 支持对现有程序结构的重复验证
   - 跟踪结构变更和质量改进
   - 提供历史对比和趋势分析

#### 迭代优化流程
1. **IDE AI + 人工修改**：使用报告指令（ai-prompt-batch-improvement.md）进行修改
2. **非AI扫描**：再次输出报告到checkresult-v4目录
3. **持续迭代**：重复修改→扫描→报告，直到满意
4. **后续开发**：完成迭代后进行后续开发

## ⚠️ 实施计划中需要修正的问题

### 1. 输出目录配置错误
- **错误配置**：通用的 `checkresult/` 目录
- **正确配置**：具体项目的 `checkresult-v4` 目录
- **修正说明**：实施计划中的checkresult目录仅用于开发环境测试，实际运行时输出到目标项目的checkresult-v4目录

### 2. 简版程序与正式代码分离架构
- **架构原则**：简版程序面向人类使用，通过任务接口调用正式生产代码
- **实施要求**：简版程序不包含临时代码，只负责人机交互和任务调度
- **修正说明**：需要明确简版程序和正式代码的职责边界

### 3. 任务接口标准化设计
- **缺失内容**：标准化的任务接口定义和调用规范
- **修正说明**：需要设计统一的任务接口，支持结构扫描、架构分析、质量验证等功能

### 4. 迭代扫描验证机制
- **核心需求**：对现有程序进行迭代扫描验证结构的正确性
- **实施要求**：支持重复扫描、结构对比、质量跟踪
- **修正说明**：需要实现结构变更检测和质量改进追踪机制

## 🔧 V4系统配置要求

### 输出路径配置
```python
# V4系统输出路径配置
class V4OutputConfig:
    """V4输出路径配置"""
    
    # 开发环境测试路径（实施计划中的checkresult目录）
    DEV_OUTPUT_PATH = "v4_core_algorithm_system/checkresult/"
    
    # 实际运行输出路径（用户确认的目标路径）
    RUNTIME_OUTPUT_PATH_TEMPLATE = "{target_project_path}/checkresult-v4"
    
    # 当前运行目标
    CURRENT_TARGET = "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1"
    CURRENT_OUTPUT = "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/checkresult-v4"
```

### 核心模板路径配置
```python
# V4核心模板路径配置
class V4TemplateConfig:
    """V4核心模板路径配置"""
    
    # V4架构信息AI填充模板
    ARCHITECTURE_INFO_TEMPLATE = "docs/features/T001-create-plans-20250612/v4/design/核心/V4架构信息AI填充模板.md"
    
    # V4扫描批量优化指令模板
    BATCH_OPTIMIZATION_TEMPLATE = "docs/features/T001-create-plans-20250612/v4/design/核心/V4扫描批量优化指令模板.md"
    
    # 生成的批量优化指令文件名
    GENERATED_BATCH_IMPROVEMENT = "ai-prompt-batch-improvement.md"
```

### 工作流程状态配置
```python
# V4工作流程状态配置
class V4WorkflowConfig:
    """V4工作流程状态配置"""
    
    # 工作流程阶段
    PHASE_FIRST_SCAN = "first_scan"           # 第一次扫描
    PHASE_ITERATIVE_OPTIMIZATION = "iterative_optimization"  # 迭代优化
    PHASE_SUBSEQUENT_DEVELOPMENT = "subsequent_development"   # 后续开发
    
    # 扫描类型
    SCAN_TYPE_AI = "ai_scan"                  # AI扫描
    SCAN_TYPE_NON_AI = "non_ai_scan"          # 非AI扫描
    
    # 修改类型
    MODIFICATION_IDE_AI = "ide_ai"            # IDE AI修改
    MODIFICATION_MANUAL = "manual"            # 人工修改
```

## ✅ 验收标准（用户确认版）

### 简版程序验收标准
- [ ] 简版程序支持一键运行：`python simple_scanner.py`
- [ ] 无需参数，硬编码扫描目标和输出路径
- [ ] 简版程序通过标准任务接口调用正式生产代码
- [ ] 能够分析指定目录：`docs\features\F007-建立Commons库的治理机制-20250610\nexus万用插座\design\v1`
- [ ] 正确输出到：`docs\features\F007-建立Commons库的治理机制-20250610\nexus万用插座\design\v1\checkresult-v4`
- [ ] 支持迭代扫描：修改设计文档 → 重新运行 `python simple_scanner.py` → 查看新报告
- [ ] 自动显示执行进度和结果摘要

### 任务接口验收标准
- [ ] 结构扫描任务接口：`StructureScanTask` 正常工作
- [ ] 架构分析任务接口：`ArchitectureAnalysisTask` 正常工作
- [ ] 质量验证任务接口：`QualityValidationTask` 正常工作
- [ ] 任务接口支持标准化的输入参数和输出格式
- [ ] 正式生产代码通过任务接口被正确调用
- [ ] 任务执行结果能够被简版程序正确接收和展示
- [ ] 生成详细的三重验证分析报告

### 输出路径验收标准
- [ ] 开发环境测试：输出到v4_core_algorithm_system/checkresult/
- [ ] 实际运行环境：输出到目标项目的checkresult-v4目录
- [ ] 路径配置可动态调整，支持不同目标项目
- [ ] 输出目录结构符合三重验证要求

### 核心模板验收标准
- [ ] V4架构信息AI填充模板检测机制正常
- [ ] V4扫描批量优化指令模板应用正常
- [ ] 模板不存在时的自动创建机制正常
- [ ] 生成的ai-prompt-batch-improvement.md格式正确

---

*V4核心工作流程配置 - 用户确认版*
*确保实施计划与用户要求完全一致*
*目标：建立正确的V4工作流程，避免配置错误*
*创建时间：2025-06-16*
