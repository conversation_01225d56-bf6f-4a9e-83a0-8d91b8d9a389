#!/usr/bin/env python3
"""
逻辑层次深度探测器测试 - MVP方案
Logic Depth Detector Test - MVP Solution

目标：验证CAP是否能提升AI输出的逻辑结构深度
基于真实技术栈：XKongCloud Commons Nexus微内核架构
测试对象：R1 thinking vs V3+CAP

核心创新：
1. 极简评估算法 - 只计算结构层次，不涉及复杂语义分析
2. 可解释结果 - 任何管理者都能理解的深度数值
3. 低成本验证 - 不超过50行的核心逻辑
4. 方向正确 - 与V4算法思想一致但极度简化
"""

import json
import re
import sys
import os
from datetime import datetime
from typing import Dict, List, Any

# 导入现有API客户端
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from thinking_quality_fusion_research import SimpleAPIClient

class LogicDepthDetector:
    """逻辑层次深度探测器 - 核心MVP算法"""
    
    def __init__(self):
        self.name = "逻辑层次深度探测器"
        self.version = "MVP-1.0"
    
    def detect_logic_depth(self, text: str) -> Dict[str, Any]:
        """
        核心算法：计算文本的逻辑层次深度
        规则（不超过50行）：
        - 一级标题 (# ): +1
        - 二级标题 (## ): +1  
        - 三级标题 (### ): +1
        - 有序列表 (1. ): +1
        - 无序列表 (- ): +1
        - 缩进列表 (  - ): +1
        - 代码块 (```): +1
        - 表格 (|): +1
        """
        if not text or not text.strip():
            return {"depth": 0, "details": [], "total_elements": 0}
        
        depth_elements = []
        lines = text.split('\n')
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            if not line_stripped:
                continue
                
            # 检测各种结构元素
            if re.match(r'^#{1,6}\s+', line_stripped):  # 标题
                level = len(re.match(r'^#+', line_stripped).group())
                depth_elements.append(f"L{i}: H{level}标题")
            elif re.match(r'^\d+\.\s+', line_stripped):  # 有序列表
                depth_elements.append(f"L{i}: 有序列表")
            elif re.match(r'^-\s+', line_stripped):  # 无序列表
                depth_elements.append(f"L{i}: 无序列表")
            elif re.match(r'^\s{2,}-\s+', line):  # 缩进列表
                indent_level = (len(line) - len(line.lstrip())) // 2
                depth_elements.append(f"L{i}: 缩进列表(L{indent_level})")
            elif re.match(r'^```', line_stripped):  # 代码块
                depth_elements.append(f"L{i}: 代码块")
            elif '|' in line_stripped and line_stripped.count('|') >= 2:  # 表格
                depth_elements.append(f"L{i}: 表格行")
        
        # 计算最终深度：结构元素数量
        total_depth = len(depth_elements)
        
        return {
            "depth": total_depth,
            "details": depth_elements,
            "total_elements": total_depth,
            "analysis": self._analyze_structure_quality(depth_elements)
        }
    
    def _analyze_structure_quality(self, elements: List[str]) -> Dict[str, Any]:
        """分析结构质量"""
        if not elements:
            return {"quality": "扁平", "score": 1}
        
        # 简单质量评估
        if len(elements) >= 15:
            return {"quality": "深度结构化", "score": 5}
        elif len(elements) >= 10:
            return {"quality": "良好结构化", "score": 4}
        elif len(elements) >= 5:
            return {"quality": "中等结构化", "score": 3}
        elif len(elements) >= 2:
            return {"quality": "基础结构化", "score": 2}
        else:
            return {"quality": "扁平结构", "score": 1}

class LogicDepthTestFramework:
    """逻辑深度测试框架"""
    
    def __init__(self):
        self.api_client = SimpleAPIClient()
        self.detector = LogicDepthDetector()
        
        # 基于真实技术栈的测试提示
        self.base_prompt = """基于XKongCloud Commons Nexus微内核架构，设计一个插件化的API质量评估系统。

技术约束：
- Java 21 + Spring Boot 3.4.5 + Virtual Threads
- 微内核架构，插件热加载
- 性能要求：启动时间≤1000ms，处理能力≥10000 events/s
- 内存限制：框架≤50MB，每插件≤20MB

请提供具体的技术实现方案。"""
        
        self.cap_prompt = """请按以下逻辑层次分析基于XKongCloud Commons Nexus微内核架构的插件化API质量评估系统：

## 🔍 第一层：需求分析与技术约束
### 核心需求识别
- 分析API质量评估的核心功能需求
- 识别微内核架构的技术优势
### 技术约束梳理  
- Java 21 + Spring Boot 3.4.5技术栈分析
- Virtual Threads性能特性评估
- 内存和性能指标约束分析

## 🏗️ 第二层：架构设计与模块划分
### 微内核设计
- 核心服务最小化设计
- 插件接口标准化定义
### 服务总线设计
- 事件驱动通信机制
- 插件间解耦策略
### 插件生态设计
- 热加载机制实现
- 插件生命周期管理

## ⚙️ 第三层：技术实现与性能优化
### 核心技术实现
- Spring Boot自动配置机制
- Virtual Threads并发模型
- 插件发现与加载算法
### 性能优化策略
- 启动时间优化方案
- 内存使用优化技术
- 高并发处理优化

## 🔧 第四层：具体代码实现
### 接口定义
- 提供具体的Java接口代码
- 插件SPI定义
### 核心实现
- 提供关键类的实现代码
- 配置和启动逻辑
### 测试验证
- 单元测试设计
- 性能基准测试

## 📊 第五层：部署与监控
### 部署策略
- 生产环境部署方案
- 配置管理策略
### 监控与运维
- 性能监控指标
- 故障诊断机制

请按照这个层次结构提供详细的技术方案。"""
    
    def run_comparison_test(self) -> Dict[str, Any]:
        """运行R1 thinking vs V3+CAP的逻辑深度对比测试"""
        
        results = {
            "test_timestamp": datetime.now().isoformat(),
            "detector_info": {
                "name": self.detector.name,
                "version": self.detector.version,
                "algorithm": "结构元素计数法"
            },
            "tests": {}
        }
        
        print("🔬 逻辑层次深度探测器测试")
        print("=" * 80)
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 测试目标: 验证CAP对逻辑结构深度的提升效果")
        print(f"📊 评估算法: {self.detector.name} {self.detector.version}")
        print()
        
        # 测试1: V3 + 原始提示
        print("📋 测试1: DeepSeek-V3 + 原始提示")
        print("-" * 60)
        
        v3_result = self.api_client.call_api("deepseek-ai/DeepSeek-V3-0324", self.base_prompt)
        if v3_result["success"]:
            v3_content = v3_result.get("content", "")
            v3_depth = self.detector.detect_logic_depth(v3_content)
            
            results["tests"]["v3_base"] = {
                "model": "DeepSeek-V3-0324",
                "prompt_type": "原始提示",
                "api_response": v3_result,
                "depth_analysis": v3_depth,
                "content_length": len(v3_content)
            }
            
            print(f"📊 逻辑深度: {v3_depth['depth']}")
            print(f"📝 内容长度: {len(v3_content)}字符")
            print(f"🏗️ 结构质量: {v3_depth['analysis']['quality']} (评分: {v3_depth['analysis']['score']}/5)")
            print(f"🔍 结构元素: {v3_depth['total_elements']}个")
        
        print("\n" + "="*60 + "\n")
        
        # 测试2: V3 + CAP提示
        print("📋 测试2: DeepSeek-V3 + CAP提示")
        print("-" * 60)
        
        v3_cap_result = self.api_client.call_api("deepseek-ai/DeepSeek-V3-0324", self.cap_prompt)
        if v3_cap_result["success"]:
            v3_cap_content = v3_cap_result.get("content", "")
            v3_cap_depth = self.detector.detect_logic_depth(v3_cap_content)
            
            results["tests"]["v3_cap"] = {
                "model": "DeepSeek-V3-0324",
                "prompt_type": "CAP提示",
                "api_response": v3_cap_result,
                "depth_analysis": v3_cap_depth,
                "content_length": len(v3_cap_content)
            }
            
            print(f"📊 逻辑深度: {v3_cap_depth['depth']}")
            print(f"📝 内容长度: {len(v3_cap_content)}字符")
            print(f"🏗️ 结构质量: {v3_cap_depth['analysis']['quality']} (评分: {v3_cap_depth['analysis']['score']}/5)")
            print(f"🔍 结构元素: {v3_cap_depth['total_elements']}个")
        
        print("\n" + "="*60 + "\n")
        
        # 测试3: R1 + 原始提示 (利用thinking能力)
        print("📋 测试3: DeepSeek-R1 + 原始提示 (thinking模式)")
        print("-" * 60)
        
        r1_result = self.api_client.call_api("deepseek-ai/DeepSeek-R1-0528", self.base_prompt)
        if r1_result["success"]:
            r1_content = r1_result.get("content", "")
            r1_thinking = r1_result.get("reasoning_content", "")
            
            # 分析最终输出和thinking的深度
            r1_content_depth = self.detector.detect_logic_depth(r1_content)
            r1_thinking_depth = self.detector.detect_logic_depth(r1_thinking)
            
            results["tests"]["r1_thinking"] = {
                "model": "DeepSeek-R1-0528", 
                "prompt_type": "原始提示+thinking",
                "api_response": r1_result,
                "content_depth_analysis": r1_content_depth,
                "thinking_depth_analysis": r1_thinking_depth,
                "content_length": len(r1_content),
                "thinking_length": len(r1_thinking)
            }
            
            print(f"📊 最终输出逻辑深度: {r1_content_depth['depth']}")
            print(f"🧠 Thinking逻辑深度: {r1_thinking_depth['depth']}")
            print(f"📝 最终输出长度: {len(r1_content)}字符")
            print(f"🧠 Thinking长度: {len(r1_thinking)}字符")
            print(f"🏗️ 最终输出质量: {r1_content_depth['analysis']['quality']} (评分: {r1_content_depth['analysis']['score']}/5)")
            print(f"🧠 Thinking质量: {r1_thinking_depth['analysis']['quality']} (评分: {r1_thinking_depth['analysis']['score']}/5)")
        
        # 生成对比分析
        if all(key in results["tests"] for key in ["v3_base", "v3_cap", "r1_thinking"]):
            self._generate_comparison_analysis(results)
        
        return results
    
    def _generate_comparison_analysis(self, results: Dict[str, Any]):
        """生成对比分析报告"""
        v3_base = results["tests"]["v3_base"]["depth_analysis"]
        v3_cap = results["tests"]["v3_cap"]["depth_analysis"] 
        r1_content = results["tests"]["r1_thinking"]["content_depth_analysis"]
        r1_thinking = results["tests"]["r1_thinking"]["thinking_depth_analysis"]
        
        print("\n" + "="*80)
        print("📈 逻辑深度对比分析")
        print("="*80)
        
        # CAP效果分析
        cap_improvement = v3_cap["depth"] - v3_base["depth"]
        cap_improvement_pct = (cap_improvement / v3_base["depth"] * 100) if v3_base["depth"] > 0 else 0
        
        print(f"🎯 CAP优化效果:")
        print(f"   V3原始: {v3_base['depth']} → V3+CAP: {v3_cap['depth']}")
        print(f"   提升幅度: +{cap_improvement} ({cap_improvement_pct:+.1f}%)")
        print(f"   质量提升: {v3_base['analysis']['quality']} → {v3_cap['analysis']['quality']}")
        
        # R1 thinking分析
        print(f"\n🧠 R1 Thinking分析:")
        print(f"   R1最终输出: {r1_content['depth']}")
        print(f"   R1 Thinking: {r1_thinking['depth']}")
        print(f"   Thinking优势: +{r1_thinking['depth'] - r1_content['depth']}")
        
        # 综合对比
        print(f"\n🏆 综合对比:")
        print(f"   V3原始     : {v3_base['depth']} (基准)")
        print(f"   V3+CAP     : {v3_cap['depth']} (+{cap_improvement})")
        print(f"   R1最终输出 : {r1_content['depth']} (+{r1_content['depth'] - v3_base['depth']})")
        print(f"   R1 Thinking: {r1_thinking['depth']} (+{r1_thinking['depth'] - v3_base['depth']})")
        
        # 结论
        if cap_improvement > 0:
            print(f"\n✅ 结论: CAP显著提升了V3的逻辑结构深度")
            print(f"   这证明了结构化提示能够引导AI产生更有层次的输出")
        else:
            print(f"\n❌ 结论: CAP未显示明显的结构优化效果")
        
        # 保存分析结果
        results["comparison_analysis"] = {
            "cap_improvement": cap_improvement,
            "cap_improvement_percentage": cap_improvement_pct,
            "v3_base_depth": v3_base["depth"],
            "v3_cap_depth": v3_cap["depth"],
            "r1_content_depth": r1_content["depth"],
            "r1_thinking_depth": r1_thinking["depth"],
            "conclusion": "positive" if cap_improvement > 0 else "negative"
        }

def main():
    """主函数"""
    tester = LogicDepthTestFramework()
    results = tester.run_comparison_test()
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"logic_depth_test_report_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试报告已保存: {filename}")
    print(f"📊 逻辑深度探测器验证完成")

if __name__ == "__main__":
    main()
